#!/bin/bash

# CORDE 项目 - macOS 系统文件清理脚本
# 用于清理项目中的 .DS_Store 和其他 macOS 系统文件

echo "🧹 CORDE 项目 - macOS 系统文件清理"
echo "=================================="

# 获取项目根目录
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
cd "$PROJECT_ROOT"

echo "📁 项目目录: $PROJECT_ROOT"
echo ""

# 统计要删除的文件
echo "🔍 扫描 macOS 系统文件..."

DS_STORE_COUNT=$(find . -name ".DS_Store" -type f | wc -l | tr -d ' ')
APPLE_DOUBLE_COUNT=$(find . -name "._*" -type f | wc -l | tr -d ' ')
SPOTLIGHT_COUNT=$(find . -name ".Spotlight-V100" -type d | wc -l | tr -d ' ')
TRASHES_COUNT=$(find . -name ".Trashes" -type d | wc -l | tr -d ' ')

TOTAL_COUNT=$((DS_STORE_COUNT + APPLE_DOUBLE_COUNT + SPOTLIGHT_COUNT + TRASHES_COUNT))

echo "发现的文件："
echo "  📄 .DS_Store 文件: $DS_STORE_COUNT"
echo "  📄 ._* 文件: $APPLE_DOUBLE_COUNT"
echo "  📁 .Spotlight-V100 目录: $SPOTLIGHT_COUNT"
echo "  📁 .Trashes 目录: $TRASHES_COUNT"
echo "  📊 总计: $TOTAL_COUNT 个项目"
echo ""

if [ $TOTAL_COUNT -eq 0 ]; then
    echo "✨ 没有发现 macOS 系统文件，项目很干净！"
    exit 0
fi

# 询问是否删除
read -p "❓ 是否删除这些文件？(y/N): " -n 1 -r
echo ""

if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "🗑️  开始清理..."
    
    # 删除 .DS_Store 文件
    if [ $DS_STORE_COUNT -gt 0 ]; then
        echo "  删除 .DS_Store 文件..."
        find . -name ".DS_Store" -type f -delete
    fi
    
    # 删除 ._* 文件
    if [ $APPLE_DOUBLE_COUNT -gt 0 ]; then
        echo "  删除 ._* 文件..."
        find . -name "._*" -type f -delete
    fi
    
    # 删除 .Spotlight-V100 目录
    if [ $SPOTLIGHT_COUNT -gt 0 ]; then
        echo "  删除 .Spotlight-V100 目录..."
        find . -name ".Spotlight-V100" -type d -exec rm -rf {} +
    fi
    
    # 删除 .Trashes 目录
    if [ $TRASHES_COUNT -gt 0 ]; then
        echo "  删除 .Trashes 目录..."
        find . -name ".Trashes" -type d -exec rm -rf {} +
    fi
    
    echo ""
    echo "✅ 清理完成！删除了 $TOTAL_COUNT 个项目"
    echo ""
    echo "💡 提示："
    echo "  - 所有子项目的 .gitignore 已配置忽略这些文件"
    echo "  - 可以运行此脚本定期清理"
    echo "  - 建议配置 macOS 不在网络卷创建 .DS_Store："
    echo "    defaults write com.apple.desktopservices DSDontWriteNetworkStores true"
else
    echo "❌ 取消清理操作"
fi

echo ""
echo "🎉 脚本执行完成！"
