基于你现有的项目技术栈，我来为你提供一个完整的水表读数应用开发流程。

## 1. 项目初始化

### 创建新的React Native项目

bash

```bash
npx react-native init WaterMeterApp --template react-native-template-typescript
cd WaterMeterApp
```

### 安装核心依赖

bash

```bash
# UI组件库
npm install native-base react-native-svg react-native-safe-area-context

# 导航
npm install @react-navigation/native @react-navigation/native-stack @react-navigation/material-top-tabs react-native-screens react-native-gesture-handler react-native-tab-view react-native-pager-view

# 数据库
npm install react-native-sqlite-storage

# 网络请求
npm install axios

# 安全存储
npm install react-native-keychain @react-native-async-storage/async-storage

# 文件系统
npm install react-native-fs

# 相机和图片
npm install react-native-image-picker react-native-image-resizer

# 位置服务
npm install @react-native-community/geolocation react-native-permissions

# 网络状态
npm install @react-native-community/netinfo

# 日期处理
npm install date-fns date-fns-tz

# 其他工具
npm install lodash react-native-vector-icons

# Android相关配置
npx react-native link
```

## 2. 项目结构设计

创建以下目录结构：

```
WaterMeterApp/
├── src/
│   ├── api/                    # API调用层
│   ├── components/             # 可复用组件
│   ├── database/              # 数据库模型
│   ├── navigation/            # 导航配置
│   ├── screens/               # 页面组件
│   ├── services/              # 业务逻辑服务
│   ├── utils/                 # 工具函数
│   ├── types/                 # TypeScript类型定义
│   ├── context/               # React Context
│   └── styles/                # 样式和主题
└── assets/                    # 静态资源
```

## 3. 核心功能设计

### 3.1 数据库设计

```typescript
// src/database/DatabaseInit.ts
import SQLite from 'react-native-sqlite-storage';

const DB_NAME = 'water_meter.db';
const DB_LOCATION = 'default';

let db: SQLite.SQLiteDatabase;

const openDatabase = (): Promise<SQLite.SQLiteDatabase> => {
  return new Promise((resolve, reject) => {
    SQLite.openDatabase(
      {
        name: DB_NAME,
        location: DB_LOCATION,
      },
      dbObject => {
        resolve(dbObject);
      },
      error => {
        console.log('Error opening database: ', error);
        reject(error);
      },
    );
  });
};

const executeQuery = (sql: string, params: any[] = []): Promise<any> =>
  new Promise((resolve, reject) => {
    db.transaction(tx => {
      tx.executeSql(
        sql,
        params,
        (tx, results) => {
          resolve(results);
        },
        error => {
          console.log(`Error executing: ${sql}, Error: ${error.message}`);
          reject(error);
        },
      );
    });
  });

const createTables = async (): Promise<void> => {
  const createTablesSQL = [
    // 用户表
    `CREATE TABLE IF NOT EXISTS users (
      user_id INTEGER PRIMARY KEY AUTOINCREMENT,
      username VARCHAR(255) UNIQUE NOT NULL,
      full_name VARCHAR(255),
      email VARCHAR(255),
      phone_number VARCHAR(20),
      last_login TIMESTAMP,
      is_authenticated BOOLEAN DEFAULT FALSE
    )`,
    
    // 水表客户表
    `CREATE TABLE IF NOT EXISTS water_meter_customers (
      customer_id INTEGER PRIMARY KEY AUTOINCREMENT,
      customer_code VARCHAR(50) UNIQUE NOT NULL,
      customer_name VARCHAR(255) NOT NULL,
      address VARCHAR(500),
      phone VARCHAR(20),
      email VARCHAR(255),
      status VARCHAR(20) DEFAULT 'active',
      created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )`,
    
    // 水表设备表
    `CREATE TABLE IF NOT EXISTS water_meters (
      meter_id INTEGER PRIMARY KEY AUTOINCREMENT,
      meter_number VARCHAR(100) UNIQUE NOT NULL,
      customer_id INTEGER NOT NULL,
      meter_type VARCHAR(50),
      installation_date DATE,
      last_maintenance_date DATE,
      location_description VARCHAR(500),
      latitude REAL,
      longitude REAL,
      status VARCHAR(20) DEFAULT 'active',
      created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (customer_id) REFERENCES water_meter_customers(customer_id)
    )`,
    
    // 读数记录表
    `CREATE TABLE IF NOT EXISTS meter_readings (
      reading_id INTEGER PRIMARY KEY AUTOINCREMENT,
      meter_id INTEGER NOT NULL,
      previous_reading REAL,
      current_reading REAL NOT NULL,
      reading_date DATE NOT NULL,
      reading_time TIME NOT NULL,
      reader_user_id INTEGER,
      photo_path VARCHAR(500),
      notes TEXT,
      consumption REAL,
      latitude REAL,
      longitude REAL,
      sync_status VARCHAR(20) DEFAULT 'pending',
      created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (meter_id) REFERENCES water_meters(meter_id),
      FOREIGN KEY (reader_user_id) REFERENCES users(user_id)
    )`,
    
    // 同步日志表
    `CREATE TABLE IF NOT EXISTS sync_logs (
      sync_id INTEGER PRIMARY KEY AUTOINCREMENT,
      table_name VARCHAR(100),
      record_id INTEGER,
      action VARCHAR(20),
      sync_status VARCHAR(20) DEFAULT 'pending',
      sync_time TIMESTAMP,
      error_message TEXT,
      retry_count INTEGER DEFAULT 0,
      created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )`,
    
    // 路线表
    `CREATE TABLE IF NOT EXISTS reading_routes (
      route_id INTEGER PRIMARY KEY AUTOINCREMENT,
      route_name VARCHAR(255) NOT NULL,
      description TEXT,
      estimated_duration INTEGER,
      status VARCHAR(20) DEFAULT 'active',
      created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )`,
    
    // 路线水表关联表
    `CREATE TABLE IF NOT EXISTS route_meters (
      route_meter_id INTEGER PRIMARY KEY AUTOINCREMENT,
      route_id INTEGER NOT NULL,
      meter_id INTEGER NOT NULL,
      sequence_order INTEGER,
      FOREIGN KEY (route_id) REFERENCES reading_routes(route_id),
      FOREIGN KEY (meter_id) REFERENCES water_meters(meter_id)
    )`
  ];

  for (const sql of createTablesSQL) {
    await executeQuery(sql);
    console.log(`Table created successfully`);
  }
};

const initDB = async (): Promise<void> => {
  try {
    db = await openDatabase();
    await createTables();
    console.log('Database initialized successfully');
  } catch (error) {
    console.log('Error initializing database: ', error);
  }
};

export { initDB, executeQuery };

// src/database/WaterMeter.ts
import { executeQuery } from './DatabaseInit';

export interface WaterMeter {
  meter_id?: number;
  meter_number: string;
  customer_id: number;
  meter_type?: string;
  installation_date?: string;
  last_maintenance_date?: string;
  location_description?: string;
  latitude?: number;
  longitude?: number;
  status: string;
  created_date?: string;
  
  // 关联数据
  customer_name?: string;
  customer_address?: string;
  last_reading?: number;
  last_reading_date?: string;
}

export class WaterMeterModel {
  static async getAll(limit: number = 50): Promise<WaterMeter[]> {
    try {
      const query = `
        SELECT 
          wm.*,
          wmc.customer_name,
          wmc.address as customer_address,
          mr.current_reading as last_reading,
          mr.reading_date as last_reading_date
        FROM water_meters wm
        LEFT JOIN water_meter_customers wmc ON wm.customer_id = wmc.customer_id
        LEFT JOIN (
          SELECT meter_id, current_reading, reading_date,
                 ROW_NUMBER() OVER (PARTITION BY meter_id ORDER BY reading_date DESC, reading_time DESC) as rn
          FROM meter_readings
        ) mr ON wm.meter_id = mr.meter_id AND mr.rn = 1
        WHERE wm.status = 'active'
        ORDER BY wm.meter_number
        LIMIT ?
      `;
      
      const result = await executeQuery(query, [limit]);
      const meters: WaterMeter[] = [];
      
      for (let i = 0; i < result.rows.length; i++) {
        meters.push(result.rows.item(i));
      }
      
      return meters;
    } catch (error) {
      console.error('Error fetching water meters:', error);
      return [];
    }
  }

  static async getByRoute(routeId: number): Promise<WaterMeter[]> {
    try {
      const query = `
        SELECT 
          wm.*,
          wmc.customer_name,
          wmc.address as customer_address,
          rm.sequence_order,
          mr.current_reading as last_reading,
          mr.reading_date as last_reading_date
        FROM water_meters wm
        INNER JOIN route_meters rm ON wm.meter_id = rm.meter_id
        LEFT JOIN water_meter_customers wmc ON wm.customer_id = wmc.customer_id
        LEFT JOIN (
          SELECT meter_id, current_reading, reading_date,
                 ROW_NUMBER() OVER (PARTITION BY meter_id ORDER BY reading_date DESC, reading_time DESC) as rn
          FROM meter_readings
        ) mr ON wm.meter_id = mr.meter_id AND mr.rn = 1
        WHERE rm.route_id = ? AND wm.status = 'active'
        ORDER BY rm.sequence_order
      `;
      
      const result = await executeQuery(query, [routeId]);
      const meters: WaterMeter[] = [];
      
      for (let i = 0; i < result.rows.length; i++) {
        meters.push(result.rows.item(i));
      }
      
      return meters;
    } catch (error) {
      console.error('Error fetching route meters:', error);
      return [];
    }
  }

  static async searchByNumber(meterNumber: string): Promise<WaterMeter[]> {
    try {
      const query = `
        SELECT 
          wm.*,
          wmc.customer_name,
          wmc.address as customer_address,
          mr.current_reading as last_reading,
          mr.reading_date as last_reading_date
        FROM water_meters wm
        LEFT JOIN water_meter_customers wmc ON wm.customer_id = wmc.customer_id
        LEFT JOIN (
          SELECT meter_id, current_reading, reading_date,
                 ROW_NUMBER() OVER (PARTITION BY meter_id ORDER BY reading_date DESC, reading_time DESC) as rn
          FROM meter_readings
        ) mr ON wm.meter_id = mr.meter_id AND mr.rn = 1
        WHERE wm.meter_number LIKE ? AND wm.status = 'active'
        ORDER BY wm.meter_number
        LIMIT 20
      `;
      
      const result = await executeQuery(query, [`%${meterNumber}%`]);
      const meters: WaterMeter[] = [];
      
      for (let i = 0; i < result.rows.length; i++) {
        meters.push(result.rows.item(i));
      }
      
      return meters;
    } catch (error) {
      console.error('Error searching water meters:', error);
      return [];
    }
  }
}

// src/database/MeterReading.ts
import { executeQuery } from './DatabaseInit';

export interface MeterReading {
  reading_id?: number;
  meter_id: number;
  previous_reading?: number;
  current_reading: number;
  reading_date: string;
  reading_time: string;
  reader_user_id?: number;
  photo_path?: string;
  notes?: string;
  consumption?: number;
  latitude?: number;
  longitude?: number;
  sync_status: string;
  created_date?: string;
  
  // 关联数据
  meter_number?: string;
  customer_name?: string;
}

export class MeterReadingModel {
  static async insert(reading: MeterReading): Promise<number> {
    try {
      // 获取前一次读数
      const previousReadingQuery = `
        SELECT current_reading 
        FROM meter_readings 
        WHERE meter_id = ? 
        ORDER BY reading_date DESC, reading_time DESC 
        LIMIT 1
      `;
      const previousResult = await executeQuery(previousReadingQuery, [reading.meter_id]);
      const previousReading = previousResult.rows.length > 0 ? 
        previousResult.rows.item(0).current_reading : 0;
      
      // 计算用水量
      const consumption = reading.current_reading - previousReading;
      
      const insertQuery = `
        INSERT INTO meter_readings (
          meter_id, previous_reading, current_reading, reading_date, reading_time,
          reader_user_id, photo_path, notes, consumption, latitude, longitude, sync_status
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `;
      
      const result = await executeQuery(insertQuery, [
        reading.meter_id,
        previousReading,
        reading.current_reading,
        reading.reading_date,
        reading.reading_time,
        reading.reader_user_id,
        reading.photo_path,
        reading.notes,
        consumption,
        reading.latitude,
        reading.longitude,
        reading.sync_status || 'pending'
      ]);
      
      return result.insertId;
    } catch (error) {
      console.error('Error inserting meter reading:', error);
      throw error;
    }
  }

  static async getByMeter(meterId: number, limit: number = 10): Promise<MeterReading[]> {
    try {
      const query = `
        SELECT 
          mr.*,
          wm.meter_number,
          wmc.customer_name
        FROM meter_readings mr
        LEFT JOIN water_meters wm ON mr.meter_id = wm.meter_id
        LEFT JOIN water_meter_customers wmc ON wm.customer_id = wmc.customer_id
        WHERE mr.meter_id = ?
        ORDER BY mr.reading_date DESC, mr.reading_time DESC
        LIMIT ?
      `;
      
      const result = await executeQuery(query, [meterId, limit]);
      const readings: MeterReading[] = [];
      
      for (let i = 0; i < result.rows.length; i++) {
        readings.push(result.rows.item(i));
      }
      
      return readings;
    } catch (error) {
      console.error('Error fetching meter readings:', error);
      return [];
    }
  }

  static async getPendingSync(): Promise<MeterReading[]> {
    try {
      const query = `
        SELECT 
          mr.*,
          wm.meter_number,
          wmc.customer_name
        FROM meter_readings mr
        LEFT JOIN water_meters wm ON mr.meter_id = wm.meter_id
        LEFT JOIN water_meter_customers wmc ON wm.customer_id = wmc.customer_id
        WHERE mr.sync_status = 'pending'
        ORDER BY mr.created_date ASC
      `;
      
      const result = await executeQuery(query);
      const readings: MeterReading[] = [];
      
      for (let i = 0; i < result.rows.length; i++) {
        readings.push(result.rows.item(i));
      }
      
      return readings;
    } catch (error) {
      console.error('Error fetching pending readings:', error);
      return [];
    }
  }

  static async updateSyncStatus(readingId: number, status: string): Promise<void> {
    try {
      const query = `
        UPDATE meter_readings 
        SET sync_status = ? 
        WHERE reading_id = ?
      `;
      
      await executeQuery(query, [status, readingId]);
    } catch (error) {
      console.error('Error updating sync status:', error);
      throw error;
    }
  }
}
```



### 3.2 核心服务层

```typescript
// src/services/ReadingService.ts
import { MeterReading, MeterReadingModel } from '../database/MeterReading';
import { WaterMeter } from '../database/WaterMeter';
import { LocationService } from './LocationService';
import { PhotoService } from './PhotoService';
import { SyncService } from './SyncService';

export interface CreateReadingData {
  meter_id: number;
  current_reading: number;
  notes?: string;
  photo_uri?: string;
}

export class ReadingService {
  
  // 创建新的读数记录
  static async createReading(data: CreateReadingData): Promise<number> {
    try {
      // 获取当前位置
      const location = await LocationService.getCurrentLocation();
      
      // 处理照片
      let photoPath: string | undefined;
      if (data.photo_uri) {
        photoPath = await PhotoService.savePhoto(data.photo_uri, `meter_${data.meter_id}_${Date.now()}`);
      }
      
      // 创建读数记录
      const reading: MeterReading = {
        meter_id: data.meter_id,
        current_reading: data.current_reading,
        reading_date: new Date().toISOString().split('T')[0],
        reading_time: new Date().toTimeString().split(' ')[0],
        reader_user_id: 1, // 从当前用户获取
        photo_path: photoPath,
        notes: data.notes,
        latitude: location?.latitude,
        longitude: location?.longitude,
        sync_status: 'pending'
      };
      
      const readingId = await MeterReadingModel.insert(reading);
      
      // 触发同步
      SyncService.scheduleSyncReading(readingId);
      
      return readingId;
    } catch (error) {
      console.error('Error creating reading:', error);
      throw error;
    }
  }
  
  // 获取水表的读数历史
  static async getReadingHistory(meterId: number): Promise<MeterReading[]> {
    return await MeterReadingModel.getByMeter(meterId);
  }
  
  // 验证读数有效性
  static validateReading(currentReading: number, previousReading: number): {
    isValid: boolean;
    warnings: string[];
  } {
    const warnings: string[] = [];
    let isValid = true;
    
    // 检查读数是否小于前一次读数
    if (currentReading < previousReading) {
      warnings.push('当前读数小于前一次读数，请确认是否正确');
      isValid = false;
    }
    
    // 检查用水量是否异常（超过正常范围）
    const consumption = currentReading - previousReading;
    if (consumption > 1000) { // 假设1000为异常阈值
      warnings.push('用水量异常偏高，请确认读数是否正确');
    }
    
    if (consumption === 0) {
      warnings.push('用水量为0，请确认水表是否正常运行');
    }
    
    return { isValid, warnings };
  }
}

// src/services/LocationService.ts
import Geolocation from '@react-native-community/geolocation';
import { PermissionsAndroid, Platform } from 'react-native';

export interface Location {
  latitude: number;
  longitude: number;
  accuracy?: number;
}

export class LocationService {
  
  static async requestLocationPermission(): Promise<boolean> {
    if (Platform.OS === 'android') {
      try {
        const granted = await PermissionsAndroid.request(
          PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
          {
            title: '位置权限',
            message: '应用需要访问您的位置信息来记录读数位置',
            buttonNeutral: '稍后询问',
            buttonNegative: '取消',
            buttonPositive: '确定',
          }
        );
        return granted === PermissionsAndroid.RESULTS.GRANTED;
      } catch (err) {
        console.warn(err);
        return false;
      }
    }
    return true;
  }
  
  static async getCurrentLocation(): Promise<Location | null> {
    const hasPermission = await this.requestLocationPermission();
    if (!hasPermission) {
      throw new Error('位置权限被拒绝');
    }
    
    return new Promise((resolve, reject) => {
      Geolocation.getCurrentPosition(
        (position) => {
          resolve({
            latitude: position.coords.latitude,
            longitude: position.coords.longitude,
            accuracy: position.coords.accuracy,
          });
        },
        (error) => {
          console.error('Location error:', error);
          resolve(null); // 位置获取失败时返回null而不是抛出错误
        },
        {
          enableHighAccuracy: true,
          timeout: 15000,
          maximumAge: 10000,
        }
      );
    });
  }
}

// src/services/PhotoService.ts
import { launchCamera, launchImageLibrary, ImagePickerResponse, MediaType } from 'react-native-image-picker';
import RNFS from 'react-native-fs';
import { Platform } from 'react-native';

export class PhotoService {
  
  // 拍照
  static async takePhoto(): Promise<string | null> {
    return new Promise((resolve) => {
      launchCamera(
        {
          mediaType: 'photo' as MediaType,
          quality: 0.8,
          maxWidth: 1024,
          maxHeight: 1024,
        },
        (response: ImagePickerResponse) => {
          if (response.didCancel || response.errorMessage) {
            resolve(null);
            return;
          }
          
          if (response.assets && response.assets[0]) {
            resolve(response.assets[0].uri || null);
          } else {
            resolve(null);
          }
        }
      );
    });
  }
  
  // 从相册选择
  static async pickPhoto(): Promise<string | null> {
    return new Promise((resolve) => {
      launchImageLibrary(
        {
          mediaType: 'photo' as MediaType,
          quality: 0.8,
          maxWidth: 1024,
          maxHeight: 1024,
        },
        (response: ImagePickerResponse) => {
          if (response.didCancel || response.errorMessage) {
            resolve(null);
            return;
          }
          
          if (response.assets && response.assets[0]) {
            resolve(response.assets[0].uri || null);
          } else {
            resolve(null);
          }
        }
      );
    });
  }
  
  // 保存照片到本地
  static async savePhoto(sourceUri: string, fileName: string): Promise<string> {
    try {
      const directory = Platform.OS === 'ios' 
        ? RNFS.DocumentDirectoryPath 
        : RNFS.ExternalDirectoryPath;
      
      const destPath = `${directory}/photos/${fileName}.jpg`;
      
      // 确保目录存在
      await RNFS.mkdir(`${directory}/photos`);
      
      // 复制文件
      await RNFS.copyFile(sourceUri, destPath);
      
      return destPath;
    } catch (error) {
      console.error('Error saving photo:', error);
      throw error;
    }
  }
  
  // 删除照片
  static async deletePhoto(filePath: string): Promise<void> {
    try {
      const exists = await RNFS.exists(filePath);
      if (exists) {
        await RNFS.unlink(filePath);
      }
    } catch (error) {
      console.error('Error deleting photo:', error);
      throw error;
    }
  }
}

// src/services/SyncService.ts
import { MeterReadingModel } from '../database/MeterReading';
import { NetworkService } from './NetworkService';
import { ApiService } from './ApiService';

export class SyncService {
  private static syncQueue: number[] = [];
  private static isSyncing = false;
  
  // 添加读数到同步队列
  static scheduleSyncReading(readingId: number): void {
    if (!this.syncQueue.includes(readingId)) {
      this.syncQueue.push(readingId);
    }
    
    // 如果有网络连接，立即尝试同步
    this.processSyncQueue();
  }
  
  // 处理同步队列
  static async processSyncQueue(): Promise<void> {
    if (this.isSyncing || this.syncQueue.length === 0) {
      return;
    }
    
    const isConnected = await NetworkService.isConnected();
    if (!isConnected) {
      console.log('No network connection, sync postponed');
      return;
    }
    
    this.isSyncing = true;
    
    try {
      while (this.syncQueue.length > 0) {
        const readingId = this.syncQueue.shift()!;
        await this.syncSingleReading(readingId);
      }
    } catch (error) {
      console.error('Error processing sync queue:', error);
    } finally {
      this.isSyncing = false;
    }
  }
  
  // 同步单个读数
  private static async syncSingleReading(readingId: number): Promise<void> {
    try {
      const readings = await MeterReadingModel.getByMeter(readingId);
      if (readings.length === 0) return;
      
      const reading = readings[0];
      
      // 调用API同步
      await ApiService.uploadReading(reading);
      
      // 更新同步状态
      await MeterReadingModel.updateSyncStatus(readingId, 'synced');
      
      console.log(`Reading ${readingId} synced successfully`);
    } catch (error) {
      console.error(`Error syncing reading ${readingId}:`, error);
      
      // 标记为同步失败
      await MeterReadingModel.updateSyncStatus(readingId, 'failed');
      
      // 重新加入队列稍后重试
      this.syncQueue.push(readingId);
    }
  }
  
  // 同步所有未同步的读数
  static async syncAllPendingReadings(): Promise<void> {
    try {
      const pendingReadings = await MeterReadingModel.getPendingSync();
      
      for (const reading of pendingReadings) {
        if (reading.reading_id) {
          this.scheduleSyncReading(reading.reading_id);
        }
      }
      
      await this.processSyncQueue();
    } catch (error) {
      console.error('Error syncing all pending readings:', error);
      throw error;
    }
  }
}

// src/services/NetworkService.ts
import NetInfo from '@react-native-community/netinfo';

export class NetworkService {
  
  static async isConnected(): Promise<boolean> {
    try {
      const state = await NetInfo.fetch();
      return state.isConnected === true && state.isInternetReachable === true;
    } catch (error) {
      console.error('Error checking network status:', error);
      return false;
    }
  }
  
  static subscribeToNetworkStatus(callback: (isConnected: boolean) => void): () => void {
    return NetInfo.addEventListener(state => {
      const isConnected = state.isConnected === true && state.isInternetReachable === true;
      callback(isConnected);
    });
  }
}

// src/services/ApiService.ts
import axios from 'axios';
import { MeterReading } from '../database/MeterReading';

const API_BASE_URL = 'https://your-api-server.com/api';

const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

export class ApiService {
  
  // 上传读数数据
  static async uploadReading(reading: MeterReading): Promise<void> {
    try {
      const formData = new FormData();
      
      // 添加读数数据
      formData.append('meter_id', reading.meter_id.toString());
      formData.append('current_reading', reading.current_reading.toString());
      formData.append('reading_date', reading.reading_date);
      formData.append('reading_time', reading.reading_time);
      
      if (reading.notes) {
        formData.append('notes', reading.notes);
      }
      
      if (reading.latitude) {
        formData.append('latitude', reading.latitude.toString());
      }
      
      if (reading.longitude) {
        formData.append('longitude', reading.longitude.toString());
      }
      
      // 添加照片文件
      if (reading.photo_path) {
        formData.append('photo', {
          uri: reading.photo_path,
          type: 'image/jpeg',
          name: `reading_${reading.reading_id}_${Date.now()}.jpg`,
        } as any);
      }
      
      const response = await apiClient.post('/readings', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      
      if (response.status !== 200) {
        throw new Error(`Upload failed with status: ${response.status}`);
      }
      
      console.log('Reading uploaded successfully:', response.data);
    } catch (error) {
      console.error('Error uploading reading:', error);
      throw error;
    }
  }
  
  // 下载水表数据
  static async downloadWaterMeters(): Promise<any[]> {
    try {
      const response = await apiClient.get('/water-meters');
      return response.data;
    } catch (error) {
      console.error('Error downloading water meters:', error);
      throw error;
    }
  }
  
  // 下载路线数据
  static async downloadRoutes(): Promise<any[]> {
    try {
      const response = await apiClient.get('/routes');
      return response.data;
    } catch (error) {
      console.error('Error downloading routes:', error);
      throw error;
    }
  }
}
```



### 3.3 核心界面组件

```typescript
// src/screens/MeterListScreen.tsx
import React, { useState, useEffect, useCallback } from 'react';
import {
  VStack,
  HStack,
  Text,
  Input,
  IconButton,
  FlatList,
  Pressable,
  Box,
  Badge,
  Spinner,
  useToast,
  Select,
  Icon,
} from 'native-base';
import { RefreshControl } from 'react-native';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import { useNavigation } from '@react-navigation/native';
import { WaterMeter, WaterMeterModel } from '../database/WaterMeter';
import { ReadingRouteModel } from '../database/ReadingRoute';

interface MeterListScreenProps {}

const MeterListScreen: React.FC<MeterListScreenProps> = () => {
  const navigation = useNavigation();
  const toast = useToast();
  
  const [meters, setMeters] = useState<WaterMeter[]>([]);
  const [filteredMeters, setFilteredMeters] = useState<WaterMeter[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedRoute, setSelectedRoute] = useState<string>('all');
  const [routes, setRoutes] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);

  const loadMeters = useCallback(async () => {
    setLoading(true);
    try {
      let meterData: WaterMeter[];
      
      if (selectedRoute === 'all') {
        meterData = await WaterMeterModel.getAll(100);
      } else {
        meterData = await WaterMeterModel.getByRoute(parseInt(selectedRoute));
      }
      
      setMeters(meterData);
      setFilteredMeters(meterData);
    } catch (error) {
      console.error('Error loading meters:', error);
      toast.show({
        title: '加载失败',
        description: '无法加载水表数据',
        status: 'error',
      });
    } finally {
      setLoading(false);
    }
  }, [selectedRoute, toast]);

  const loadRoutes = useCallback(async () => {
    try {
      const routeData = await ReadingRouteModel.getAll();
      setRoutes(routeData);
    } catch (error) {
      console.error('Error loading routes:', error);
    }
  }, []);

  useEffect(() => {
    loadRoutes();
  }, [loadRoutes]);

  useEffect(() => {
    loadMeters();
  }, [loadMeters]);

  // 搜索过滤
  useEffect(() => {
    if (searchQuery.trim() === '') {
      setFilteredMeters(meters);
    } else {
      const filtered = meters.filter(meter => 
        meter.meter_number.toLowerCase().includes(searchQuery.toLowerCase()) ||
        meter.customer_name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        meter.customer_address?.toLowerCase().includes(searchQuery.toLowerCase())
      );
      setFilteredMeters(filtered);
    }
  }, [searchQuery, meters]);

  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    await loadMeters();
    setRefreshing(false);
  }, [loadMeters]);

  const handleMeterPress = useCallback((meter: WaterMeter) => {
    navigation.navigate('MeterDetail', { meter });
  }, [navigation]);

  const renderMeterItem = useCallback(({ item }: { item: WaterMeter }) => (
    <Pressable onPress={() => handleMeterPress(item)} mb={2}>
      <Box
        bg="white"
        rounded="lg"
        shadow={1}
        p={4}
        borderLeftWidth={4}
        borderLeftColor="blue.500"
      >
        <VStack space={2}>
          <HStack justifyContent="space-between" alignItems="center">
            <Text fontSize="lg" fontWeight="bold" color="gray.800">
              {item.meter_number}
            </Text>
            <Badge
              colorScheme={item.status === 'active' ? 'green' : 'gray'}
              variant="solid"
            >
              {item.status === 'active' ? '正常' : '停用'}
            </Badge>
          </HStack>
          
          <VStack space={1}>
            <Text fontSize="md" color="gray.700">
              客户: {item.customer_name}
            </Text>
            <Text fontSize="sm" color="gray.600" numberOfLines={2}>
              地址: {item.customer_address}
            </Text>
            {item.location_description && (
              <Text fontSize="sm" color="gray.600">
                位置: {item.location_description}
              </Text>
            )}
          </VStack>
          
          <HStack justifyContent="space-between" alignItems="center">
            <VStack>
              <Text fontSize="xs" color="gray.500">上次读数</Text>
              <Text fontSize="md" fontWeight="semibold" color="blue.600">
                {item.last_reading ? `${item.last_reading} m³` : '暂无'}
              </Text>
            </VStack>
            <VStack alignItems="flex-end">
              <Text fontSize="xs" color="gray.500">读数日期</Text>
              <Text fontSize="sm" color="gray.600">
                {item.last_reading_date || '暂无'}
              </Text>
            </VStack>
          </HStack>
        </VStack>
      </Box>
    </Pressable>
  ), [handleMeterPress]);

  return (
    <VStack flex={1} bg="gray.50" safeArea>
      {/* 搜索和筛选栏 */}
      <VStack space={3} p={4} bg="white" shadow={1}>
        <HStack space={2} alignItems="center">
          <Input
            flex={1}
            placeholder="搜索水表号、客户名或地址"
            value={searchQuery}
            onChangeText={setSearchQuery}
            InputLeftElement={
              <Icon
                as={MaterialCommunityIcons}
                name="magnify"
                size={5}
                ml={2}
                color="gray.400"
              />
            }
          />
          <IconButton
            icon={
              <Icon
                as={MaterialCommunityIcons}
                name="filter"
                size={6}
                color="blue.500"
              />
            }
            onPress={() => {/* 打开高级筛选 */}}
          />
        </HStack>
        
        <HStack space={2} alignItems="center">
          <Text fontSize="sm" color="gray.600">路线:</Text>
          <Select
            flex={1}
            selectedValue={selectedRoute}
            onValueChange={setSelectedRoute}
            placeholder="选择路线"
          >
            <Select.Item label="所有水表" value="all" />
            {routes.map(route => (
              <Select.Item
                key={route.route_id}
                label={route.route_name}
                value={route.route_id.toString()}
              />
            ))}
          </Select>
        </HStack>
      </VStack>

      {/* 水表列表 */}
      <VStack flex={1} px={4}>
        <HStack justifyContent="space-between" alignItems="center" py={3}>
          <Text fontSize="lg" fontWeight="bold" color="gray.700">
            水表列表 ({filteredMeters.length})
          </Text>
          <IconButton
            icon={
              <Icon
                as={MaterialCommunityIcons}
                name="refresh"
                size={6}
                color="blue.500"
              />
            }
            onPress={handleRefresh}
          />
        </HStack>

        {loading ? (
          <Box flex={1} justifyContent="center" alignItems="center">
            <Spinner size="lg" color="blue.500" />
            <Text mt={2} color="gray.600">加载中...</Text>
          </Box>
        ) : (
          <FlatList
            data={filteredMeters}
            renderItem={renderMeterItem}
            keyExtractor={(item) => item.meter_id?.toString() || ''}
            refreshControl={
              <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
            }
            ListEmptyComponent={
              <Box flex={1} justifyContent="center" alignItems="center" py={10}>
                <Icon
                  as={MaterialCommunityIcons}
                  name="water-off"
                  size={16}
                  color="gray.400"
                />
                <Text mt={2} color="gray.500">暂无水表数据</Text>
              </Box>
            }
            showsVerticalScrollIndicator={false}
          />
        )}
      </VStack>
    </VStack>
  );
};

export default MeterListScreen;

// src/screens/MeterDetailScreen.tsx
import React, { useState, useEffect, useCallback } from 'react';
import {
  VStack,
  HStack,
  Text,
  Box,
  Badge,
  Divider,
  IconButton,
  Icon,
  ScrollView,
  useToast,
  Spinner,
} from 'native-base';
import { useRoute, useNavigation } from '@react-navigation/native';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import { WaterMeter } from '../database/WaterMeter';
import { MeterReading, MeterReadingModel } from '../database/MeterReading';

interface MeterDetailScreenProps {}

const MeterDetailScreen: React.FC<MeterDetailScreenProps> = () => {
  const route = useRoute();
  const navigation = useNavigation();
  const toast = useToast();
  
  const { meter } = route.params as { meter: WaterMeter };
  
  const [readings, setReadings] = useState<MeterReading[]>([]);
  const [loading, setLoading] = useState(false);

  const loadReadings = useCallback(async () => {
    if (!meter.meter_id) return;
    
    setLoading(true);
    try {
      const readingData = await MeterReadingModel.getByMeter(meter.meter_id, 20);
      setReadings(readingData);
    } catch (error) {
      console.error('Error loading readings:', error);
      toast.show({
        title: '加载失败',
        description: '无法加载读数历史',
        status: 'error',
      });
    } finally {
      setLoading(false);
    }
  }, [meter.meter_id, toast]);

  useEffect(() => {
    loadReadings();
  }, [loadReadings]);

  const handleNewReading = useCallback(() => {
    navigation.navigate('NewReading', { meter });
  }, [navigation, meter]);

  const handleViewHistory = useCallback(() => {
    navigation.navigate('ReadingHistory', { meter });
  }, [navigation, meter]);

  return (
    <ScrollView flex={1} bg="gray.50">
      <VStack space={4} p={4}>
        {/* 水表基本信息 */}
        <Box bg="white" rounded="lg" shadow={1} p={4}>
          <VStack space={3}>
            <HStack justifyContent="space-between" alignItems="center">
              <Text fontSize="xl" fontWeight="bold" color="gray.800">
                {meter.meter_number}
              </Text>
              <Badge
                colorScheme={meter.status === 'active' ? 'green' : 'gray'}
                variant="solid"
              >
                {meter.status === 'active' ? '正常' : '停用'}
              </Badge>
            </HStack>
            
            <Divider />
            
            <VStack space={2}>
              <HStack justifyContent="space-between">
                <Text color="gray.600">客户名称:</Text>
                <Text fontWeight="medium">{meter.customer_name}</Text>
              </HStack>
              
              <HStack justifyContent="space-between">
                <Text color="gray.600">客户地址:</Text>
                <Text fontWeight="medium" flex={1} textAlign="right" numberOfLines={2}>
                  {meter.customer_address}
                </Text>
              </HStack>
              
              {meter.meter_type && (
                <HStack justifyContent="space-between">
                  <Text color="gray.600">水表类型:</Text>
                  <Text fontWeight="medium">{meter.meter_type}</Text>
                </HStack>
              )}
              
              {meter.installation_date && (
                <HStack justifyContent="space-between">
                  <Text color="gray.600">安装日期:</Text>
                  <Text fontWeight="medium">{meter.installation_date}</Text>
                </HStack>
              )}
              
              {meter.location_description && (
                <HStack justifyContent="space-between">
                  <Text color="gray.600">具体位置:</Text>
                  <Text fontWeight="medium" flex={1} textAlign="right" numberOfLines={2}>
                    {meter.location_description}
                  </Text>
                </HStack>
              )}
            </VStack>
          </VStack>
        </Box>

        {/* 最新读数信息 */}
        <Box bg="white" rounded="lg" shadow={1} p={4}>
          <VStack space={3}>
            <Text fontSize="lg" fontWeight="bold" color="gray.800">
              最新读数
            </Text>
            
            <Divider />
            
            {meter.last_reading ? (
              <VStack space={2}>
                <HStack justifyContent="space-between" alignItems="center">
                  <Text fontSize="2xl" fontWeight="bold" color="blue.600">
                    {meter.last_reading} m³
                  </Text>
                  <Text fontSize="sm" color="gray.500">
                    {meter.last_reading_date}
                  </Text>
                </HStack>
                
                {readings.length > 1 && (
                  <Text fontSize="sm" color="gray.600">
                    用水量: {(readings[0].current_reading - readings[1].current_reading).toFixed(2)} m³
                  </Text>
                )}
              </VStack>
            ) : (
              <Text color="gray.500" textAlign="center" py={4}>
                暂无读数记录
              </Text>
            )}
          </VStack>
        </Box>

        {/* 操作按钮 */}
        <HStack space={3} justifyContent="center">
          <IconButton
            bg="blue.500"
            rounded="full"
            size="lg"
            icon={
              <Icon
                as={MaterialCommunityIcons}
                name="plus"
                size={8}
                color="white"
              />
            }
            onPress={handleNewReading}
            _pressed={{ bg: 'blue.600' }}
          />
          
          <IconButton
            bg="gray.500"
            rounded="full"
            size="lg"
            icon={
              <Icon
                as={MaterialCommunityIcons}
                name="history"
                size={8}
                color="white"
              />
            }
            onPress={handleViewHistory}
            _pressed={{ bg: 'gray.600' }}
          />
        </HStack>

        {/* 最近读数历史 */}
        <Box bg="white" rounded="lg" shadow={1} p={4}>
          <VStack space={3}>
            <HStack justifyContent="space-between" alignItems="center">
              <Text fontSize="lg" fontWeight="bold" color="gray.800">
                最近读数记录
              </Text>
              {readings.length > 0 && (
                <IconButton
                  size="sm"
                  icon={
                    <Icon
                      as={MaterialCommunityIcons}
                      name="chevron-right"
                      size={5}
                      color="blue.500"
                    />
                  }
                  onPress={handleViewHistory}
                />
              )}
            </HStack>
            
            <Divider />
            
            {loading ? (
              <Box py={4} alignItems="center">
                <Spinner color="blue.500" />
              </Box>
            ) : readings.length > 0 ? (
              <VStack space={2}>
                {readings.slice(0, 5).map((reading, index) => (
                  <HStack
                    key={reading.reading_id}
                    justifyContent="space-between"
                    alignItems="center"
                    py={2}
                    px={3}
                    bg={index === 0 ? 'blue.50' : 'gray.50'}
                    rounded="md"
                  >
                    <VStack>
                      <Text fontWeight="medium">
                        {reading.current_reading} m³
                      </Text>
                      <Text fontSize="xs" color="gray.500">
                        {reading.reading_date} {reading.reading_time}
                      </Text>
                    </VStack>
                    
                    <VStack alignItems="flex-end">
                      <Badge
                        colorScheme={
                          reading.sync_status === 'synced' ? 'green' :
                          reading.sync_status === 'pending' ? 'orange' : 'red'
                        }
                        variant="subtle"
                        size="sm"
                      >
                        {reading.sync_status === 'synced' ? '已同步' :
                         reading.sync_status === 'pending' ? '待同步' : '同步失败'}
                      </Badge>
                      
                      {reading.consumption !== undefined && (
                        <Text fontSize="xs" color="gray.500">
                          用量: {reading.consumption.toFixed(2)} m³
                        </Text>
                      )}
                    </VStack>
                  </HStack>
                ))}
              </VStack>
            ) : (
              <Text color="gray.500" textAlign="center" py={4}>
                暂无读数记录
              </Text>
            )}
          </VStack>
        </Box>
      </VStack>
    </ScrollView>
  );
};

export default MeterDetailScreen;
```



### 3.4 读数录入界面

```typescript
// src/screens/NewReadingScreen.tsx
import React, { useState, useCallback, useEffect } from 'react';
import {
  VStack,
  HStack,
  Text,
  Input,
  TextArea,
  Button,
  Box,
  Image,
  IconButton,
  Icon,
  useToast,
  ScrollView,
  Alert,
  Spinner,
  Badge,
} from 'native-base';
import { useRoute, useNavigation } from '@react-navigation/native';
import { Keyboard } from 'react-native';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import { WaterMeter } from '../database/WaterMeter';
import { ReadingService, CreateReadingData } from '../services/ReadingService';
import { PhotoService } from '../services/PhotoService';
import { LocationService } from '../services/LocationService';

interface NewReadingScreenProps {}

const NewReadingScreen: React.FC<NewReadingScreenProps> = () => {
  const route = useRoute();
  const navigation = useNavigation();
  const toast = useToast();
  
  const { meter } = route.params as { meter: WaterMeter };
  
  const [currentReading, setCurrentReading] = useState('');
  const [notes, setNotes] = useState('');
  const [photoUri, setPhotoUri] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [location, setLocation] = useState<any>(null);
  const [isLoadingLocation, setIsLoadingLocation] = useState(false);
  const [validationWarnings, setValidationWarnings] = useState<string[]>([]);

  // 获取当前位置
  const getCurrentLocation = useCallback(async () => {
    setIsLoadingLocation(true);
    try {
      const loc = await LocationService.getCurrentLocation();
      setLocation(loc);
    } catch (error) {
      console.error('Error getting location:', error);
      toast.show({
        title: '位置获取失败',
        description: '无法获取当前位置，但仍可继续录入',
        status: 'warning',
      });
    } finally {
      setIsLoadingLocation(false);
    }
  }, [toast]);

  useEffect(() => {
    getCurrentLocation();
  }, [getCurrentLocation]);

  // 验证读数
  const validateReading = useCallback(() => {
    if (!currentReading.trim()) {
      return { isValid: false, warnings: ['请输入读数'] };
    }

    const reading = parseFloat(currentReading);
    if (isNaN(reading) || reading < 0) {
      return { isValid: false, warnings: ['请输入有效的读数'] };
    }

    const previousReading = meter.last_reading || 0;
    return ReadingService.validateReading(reading, previousReading);
  }, [currentReading, meter.last_reading]);

  // 读数变化时验证
  useEffect(() => {
    if (currentReading.trim()) {
      const validation = validateReading();
      setValidationWarnings(validation.warnings);
    } else {
      setValidationWarnings([]);
    }
  }, [currentReading, validateReading]);

  // 拍照
  const handleTakePhoto = useCallback(async () => {
    try {
      const uri = await PhotoService.takePhoto();
      if (uri) {
        setPhotoUri(uri);
        toast.show({
          title: '照片拍摄成功',
          status: 'success',
        });
      }
    } catch (error) {
      console.error('Error taking photo:', error);
      toast.show({
        title: '拍照失败',
        description: '无法拍摄照片，请重试',
        status: 'error',
      });
    }
  }, [toast]);

  // 选择照片
  const handlePickPhoto = useCallback(async () => {
    try {
      const uri = await PhotoService.pickPhoto();
      if (uri) {
        setPhotoUri(uri);
        toast.show({
          title: '照片选择成功',
          status: 'success',
        });
      }
    } catch (error) {
      console.error('Error picking photo:', error);
      toast.show({
        title: '选择失败',
        description: '无法选择照片，请重试',
        status: 'error',
      });
    }
  }, [toast]);

  // 删除照片
  const handleRemovePhoto = useCallback(() => {
    setPhotoUri(null);
  }, []);

  // 提交读数
  const handleSubmit = useCallback(async () => {
    Keyboard.dismiss();

    const validation = validateReading();
    if (!validation.isValid) {
      toast.show({
        title: '数据验证失败',
        description: validation.warnings.join(', '),
        status: 'error',
      });
      return;
    }

    // 如果有警告，显示确认对话框
    if (validation.warnings.length > 0) {
      // 这里应该显示确认对话框，简化处理
      const shouldContinue = await new Promise((resolve) => {
        Alert.AlertDialog({
          isOpen: true,
          leastDestructiveRef: undefined,
          onClose: () => resolve(false),
          header: '读数确认',
          body: validation.warnings.join('\n') + '\n\n是否继续提交？',
          footer: (
            <HStack space={2}>
              <Button variant="unstyled" onPress={() => resolve(false)}>
                取消
              </Button>
              <Button colorScheme="red" onPress={() => resolve(true)}>
                确认提交
              </Button>
            </HStack>
          ),
        });
      });

      if (!shouldContinue) {
        return;
      }
    }

    setIsSubmitting(true);
    try {
      const readingData: CreateReadingData = {
        meter_id: meter.meter_id!,
        current_reading: parseFloat(currentReading),
        notes: notes.trim() || undefined,
        photo_uri: photoUri || undefined,
      };

      await ReadingService.createReading(readingData);

      toast.show({
        title: '读数提交成功',
        description: '读数已保存并将自动同步到服务器',
        status: 'success',
      });

      navigation.goBack();
    } catch (error) {
      console.error('Error submitting reading:', error);
      toast.show({
        title: '提交失败',
        description: '读数提交失败，请重试',
        status: 'error',
      });
    } finally {
      setIsSubmitting(false);
    }
  }, [validateReading, toast, currentReading, notes, photoUri, meter.meter_id, navigation]);

  const handleCancel = useCallback(() => {
    navigation.goBack();
  }, [navigation]);

  return (
    <ScrollView flex={1} bg="gray.50">
      <VStack space={4} p={4}>
        {/* 水表信息 */}
        <Box bg="white" rounded="lg" shadow={1} p={4}>
          <VStack space={2}>
            <Text fontSize="lg" fontWeight="bold" color="gray.800">
              {meter.meter_number}
            </Text>
            <Text color="gray.600">{meter.customer_name}</Text>
            <Text fontSize="sm" color="gray.500" numberOfLines={2}>
              {meter.customer_address}
            </Text>
            {meter.last_reading && (
              <HStack justifyContent="space-between" mt={2}>
                <Text fontSize="sm" color="gray.600">上次读数:</Text>
                <Text fontSize="sm" fontWeight="medium" color="blue.600">
                  {meter.last_reading} m³
                </Text>
              </HStack>
            )}
          </VStack>
        </Box>

        {/* 读数输入 */}
        <Box bg="white" rounded="lg" shadow={1} p={4}>
          <VStack space={4}>
            <Text fontSize="lg" fontWeight="bold" color="gray.800">
              录入读数
            </Text>

            <VStack space={2}>
              <Text fontSize="md" color="gray.700">
                当前读数 (m³) *
              </Text>
              <Input
                value={currentReading}
                onChangeText={setCurrentReading}
                placeholder="请输入当前读数"
                keyboardType="numeric"
                size="lg"
                bg="gray.50"
                _focus={{ bg: 'white', borderColor: 'blue.500' }}
              />
              
              {/* 验证警告 */}
              {validationWarnings.length > 0 && (
                <VStack space={1}>
                  {validationWarnings.map((warning, index) => (
                    <HStack key={index} space={2} alignItems="center">
                      <Icon
                        as={MaterialCommunityIcons}
                        name="alert-circle"
                        size={4}
                        color="orange.500"
                      />
                      <Text fontSize="sm" color="orange.600">
                        {warning}
                      </Text>
                    </HStack>
                  ))}
                </VStack>
              )}

              {/* 用水量计算 */}
              {currentReading && meter.last_reading && !isNaN(parseFloat(currentReading)) && (
                <HStack justifyContent="space-between" alignItems="center" p={3} bg="blue.50" rounded="md">
                  <Text fontSize="sm" color="blue.700">本期用水量:</Text>
                  <Text fontSize="lg" fontWeight="bold" color="blue.700">
                    {(parseFloat(currentReading) - meter.last_reading).toFixed(2)} m³
                  </Text>
                </HStack>
              )}
            </VStack>
          </VStack>
        </Box>

        {/* 备注 */}
        <Box bg="white" rounded="lg" shadow={1} p={4}>
          <VStack space={3}>
            <Text fontSize="md" color="gray.700">备注</Text>
            <TextArea
              value={notes}
              onChangeText={setNotes}
              placeholder="请输入备注信息（可选）"
              h={20}
              bg="gray.50"
              _focus={{ bg: 'white', borderColor: 'blue.500' }}
            />
          </VStack>
        </Box>

        {/* 照片 */}
        <Box bg="white" rounded="lg" shadow={1} p={4}>
          <VStack space={3}>
            <Text fontSize="md" color="gray.700">水表照片</Text>
            
            {photoUri ? (
              <VStack space={3}>
                <Box position="relative">
                  <Image
                    source={{ uri: photoUri }}
                    alt="水表照片"
                    w="full"
                    h={48}
                    rounded="md"
                    resizeMode="cover"
                  />
                  <IconButton
                    position="absolute"
                    top={2}
                    right={2}
                    bg="red.500:alpha.80"
                    rounded="full"
                    size="sm"
                    icon={
                      <Icon
                        as={MaterialCommunityIcons}
                        name="close"
                        size={4}
                        color="white"
                      />
                    }
                    onPress={handleRemovePhoto}
                  />
                </Box>
                
                <HStack space={2}>
                  <Button
                    flex={1}
                    variant="outline"
                    leftIcon={
                      <Icon
                        as={MaterialCommunityIcons}
                        name="camera"
                        size={5}
                      />
                    }
                    onPress={handleTakePhoto}
                  >
                    重新拍照
                  </Button>
                  <Button
                    flex={1}
                    variant="outline"
                    leftIcon={
                      <Icon
                        as={MaterialCommunityIcons}
                        name="image"
                        size={5}
                      />
                    }
                    onPress={handlePickPhoto}
                  >
                    选择照片
                  </Button>
                </HStack>
              </VStack>
            ) : (
              <HStack space={2}>
                <Button
                  flex={1}
                  colorScheme="blue"
                  leftIcon={
                    <Icon
                      as={MaterialCommunityIcons}
                      name="camera"
                      size={5}
                    />
                  }
                  onPress={handleTakePhoto}
                >
                  拍照
                </Button>
                <Button
                  flex={1}
                  variant="outline"
                  leftIcon={
                    <Icon
                      as={MaterialCommunityIcons}
                      name="image"
                      size={5}
                    />
                  }
                  onPress={handlePickPhoto}
                >
                  选择照片
                </Button>
              </HStack>
            )}
          </VStack>
        </Box>

        {/* 位置信息 */}
        <Box bg="white" rounded="lg" shadow={1} p={4}>
          <VStack space={3}>
            <HStack justifyContent="space-between" alignItems="center">
              <Text fontSize="md" color="gray.700">位置信息</Text>
              {isLoadingLocation && <Spinner size="sm" color="blue.500" />}
            </HStack>
            
            {location ? (
              <VStack space={1}>
                <HStack justifyContent="space-between">
                  <Text fontSize="sm" color="gray.600">纬度:</Text>
                  <Text fontSize="sm" color="gray.800">
                    {location.latitude.toFixed(6)}
                  </Text>
                </HStack>
                <HStack justifyContent="space-between">
                  <Text fontSize="sm" color="gray.600">经度:</Text>
                  <Text fontSize="sm" color="gray.800">
                    {location.longitude.toFixed(6)}
                  </Text>
                </HStack>
                {location.accuracy && (
                  <HStack justifyContent="space-between">
                    <Text fontSize="sm" color="gray.600">精度:</Text>
                    <Text fontSize="sm" color="gray.800">
                      ±{location.accuracy.toFixed(0)}m
                    </Text>
                  </HStack>
                )}
                <Badge colorScheme="green" alignSelf="flex-start" mt={1}>
                  位置已获取
                </Badge>
              </VStack>
            ) : (
              <VStack space={2}>
                <Text fontSize="sm" color="gray.500">
                  位置信息获取失败或未开启定位权限
                </Text>
                <Button
                  size="sm"
                  variant="outline"
                  onPress={getCurrentLocation}
                  leftIcon={
                    <Icon
                      as={MaterialCommunityIcons}
                      name="map-marker"
                      size={4}
                    />
                  }
                >
                  重新获取位置
                </Button>
              </VStack>
            )}
          </VStack>
        </Box>

        {/* 操作按钮 */}
        <HStack space={3} mt={4}>
          <Button
            flex={1}
            variant="outline"
            onPress={handleCancel}
            leftIcon={
              <Icon
                as={MaterialCommunityIcons}
                name="close"
                size={5}
              />
            }
          >
            取消
          </Button>
          <Button
            flex={2}
            colorScheme="blue"
            onPress={handleSubmit}
            isLoading={isSubmitting}
            isLoadingText="提交中..."
            leftIcon={
              <Icon
                as={MaterialCommunityIcons}
                name="check"
                size={5}
              />
            }
          >
            提交读数
          </Button>
        </HStack>
      </VStack>
    </ScrollView>
  );
};

export default NewReadingScreen;

// src/screens/ReadingHistoryScreen.tsx
import React, { useState, useEffect, useCallback } from 'react';
import {
  VStack,
  HStack,
  Text,
  Box,
  FlatList,
  Badge,
  Image,
  IconButton,
  Icon,
  useToast,
  Spinner,
  Modal,
  Button,
} from 'native-base';
import { useRoute } from '@react-navigation/native';
import { RefreshControl } from 'react-native';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import { WaterMeter } from '../database/WaterMeter';
import { MeterReading, MeterReadingModel } from '../database/MeterReading';

interface ReadingHistoryScreenProps {}

const ReadingHistoryScreen: React.FC<ReadingHistoryScreenProps> = () => {
  const route = useRoute();
  const toast = useToast();
  
  const { meter } = route.params as { meter: WaterMeter };
  
  const [readings, setReadings] = useState<MeterReading[]>([]);
  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [selectedPhoto, setSelectedPhoto] = useState<string | null>(null);
  const [showPhotoModal, setShowPhotoModal] = useState(false);

  const loadReadings = useCallback(async () => {
    if (!meter.meter_id) return;
    
    setLoading(true);
    try {
      const readingData = await MeterReadingModel.getByMeter(meter.meter_id, 50);
      setReadings(readingData);
    } catch (error) {
      console.error('Error loading readings:', error);
      toast.show({
        title: '加载失败',
        description: '无法加载读数历史',
        status: 'error',
      });
    } finally {
      setLoading(false);
    }
  }, [meter.meter_id, toast]);

  useEffect(() => {
    loadReadings();
  }, [loadReadings]);

  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    await loadReadings();
    setRefreshing(false);
  }, [loadReadings]);

  const handlePhotoPress = useCallback((photoPath: string) => {
    setSelectedPhoto(photoPath);
    setShowPhotoModal(true);
  }, []);

  const renderReadingItem = useCallback(({ item, index }: { item: MeterReading; index: number }) => {
    const consumption = item.consumption || 0;
    const isFirst = index === 0;
    
    return (
      <Box
        bg="white"
        rounded="lg"
        shadow={1}
        p={4}
        mb={3}
        borderLeftWidth={4}
        borderLeftColor={isFirst ? 'blue.500' : 'gray.300'}
      >
        <VStack space={3}>
          <HStack justifyContent="space-between" alignItems="center">
            <VStack>
              <Text fontSize="lg" fontWeight="bold" color="gray.800">
                {item.current_reading} m³
              </Text>
              <Text fontSize="sm" color="gray.500">
                {item.reading_date} {item.reading_time}
              </Text>
            </VStack>
            
            <VStack alignItems="flex-end" space={1}>
              <Badge
                colorScheme={
                  item.sync_status === 'synced' ? 'green' :
                  item.sync_status === 'pending' ? 'orange' : 'red'
                }
                variant="solid"
              >
                {item.sync_status === 'synced' ? '已同步' :
                 item.sync_status === 'pending' ? '待同步' : '同步失败'}
              </Badge>
              
              {isFirst && (
                <Badge colorScheme="blue" variant="outline">
                  最新
                </Badge>
              )}
            </VStack>
          </HStack>
          
          {consumption > 0 && (
            <HStack justifyContent="space-between" alignItems="center" p={2} bg="blue.50" rounded="md">
              <Text fontSize="sm" color="blue.700">本期用水量:</Text>
              <Text fontSize="md" fontWeight="semibold" color="blue.700">
                {consumption.toFixed(2)} m³
              </Text>
            </HStack>
          )}
          
          {item.notes && (
            <Box p={2} bg="gray.50" rounded="md">
              <Text fontSize="sm" color="gray.700">
                备注: {item.notes}
              </Text>
            </Box>
          )}
          
          <HStack justifyContent="space-between" alignItems="center">
            <HStack space={2} alignItems="center">
              {item.photo_path && (
                <IconButton
                  size="sm"
                  bg="blue.500"
                  rounded="md"
                  icon={
                    <Icon
                      as={MaterialCommunityIcons}
                      name="camera"
                      size={4}
                      color="white"
                    />
                  }
                  onPress={() => handlePhotoPress(item.photo_path!)}
                />
              )}
              
              {item.latitude && item.longitude && (
                <IconButton
                  size="sm"
                  bg="green.500"
                  rounded="md"
                  icon={
                    <Icon
                      as={MaterialCommunityIcons}
                      name="map-marker"
                      size={4}
                      color="white"
                    />
                  }
                  onPress={() => {
                    // 打开地图或显示位置信息
                    toast.show({
                      title: '位置信息',
                      description: `纬度: ${item.latitude}, 经度: ${item.longitude}`,
                    });
                  }}
                />
              )}
            </HStack>
            
            <Text fontSize="xs" color="gray.500">
              创建于 {item.created_date?.split('T')[0]}
            </Text>
          </HStack>
        </VStack>
      </Box>
    );
  }, [handlePhotoPress, toast]);

  return (
    <VStack flex={1} bg="gray.50" safeArea>
      {/* 头部信息 */}
      <Box bg="white" shadow={1} p={4}>
        <VStack space={2}>
          <Text fontSize="lg" fontWeight="bold" color="gray.800">
            {meter.meter_number}
          </Text>
          <Text color="gray.600">{meter.customer_name}</Text>
          <HStack justifyContent="space-between" alignItems="center">
            <Text fontSize="sm" color="gray.500">
              读数历史 ({readings.length} 条记录)
            </Text>
            <IconButton
              size="sm"
              icon={
                <Icon
                  as={MaterialCommunityIcons}
                  name="refresh"
                  size={5}
                  color="blue.500"
                />
              }
              onPress={handleRefresh}
            />
          </HStack>
        </VStack>
      </Box>

      {/* 读数列表 */}
      <Box flex={1} p={4}>
        {loading ? (
          <Box flex={1} justifyContent="center" alignItems="center">
            <Spinner size="lg" color="blue.500" />
            <Text mt={2} color="gray.600">加载中...</Text>
          </Box>
        ) : (
          <FlatList
            data={readings}
            renderItem={renderReadingItem}
            keyExtractor={(item) => item.reading_id?.toString() || ''}
            refreshControl={
              <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
            }
            ListEmptyComponent={
              <Box flex={1} justifyContent="center" alignItems="center" py={10}>
                <Icon
                  as={MaterialCommunityIcons}
                  name="water-off"
                  size={16}
                  color="gray.400"
                />
                <Text mt={2} color="gray.500">暂无读数记录</Text>
              </Box>
            }
            showsVerticalScrollIndicator={false}
          />
        )}
      </Box>

      {/* 照片预览模态框 */}
      <Modal isOpen={showPhotoModal} onClose={() => setShowPhotoModal(false)} size="full">
        <Modal.Content maxWidth="90%" maxHeight="80%">
          <Modal.CloseButton />
          <Modal.Header>水表照片</Modal.Header>
          <Modal.Body>
            {selectedPhoto && (
              <Image
                source={{ uri: selectedPhoto }}
                alt="水表照片"
                w="full"
                h="full"
                resizeMode="contain"
              />
            )}
          </Modal.Body>
          <Modal.Footer>
            <Button onPress={() => setShowPhotoModal(false)}>
              关闭
            </Button>
          </Modal.Footer>
        </Modal.Content>
      </Modal>
    </VStack>
  );
};

export default ReadingHistoryScreen;
```



### 3.5 导航配置和应用入口

```typescript
// src/navigation/AppNavigator.tsx
import React from 'react';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { createMaterialTopTabNavigator } from '@react-navigation/material-top-tabs';
import { WaterMeter } from '../database/WaterMeter';

// 导入屏幕组件
import MeterListScreen from '../screens/MeterListScreen';
import MeterDetailScreen from '../screens/MeterDetailScreen';
import NewReadingScreen from '../screens/NewReadingScreen';
import ReadingHistoryScreen from '../screens/ReadingHistoryScreen';
import SyncStatusScreen from '../screens/SyncStatusScreen';
import SettingsScreen from '../screens/SettingsScreen';
import RouteManagementScreen from '../screens/RouteManagementScreen';

// 定义导航参数类型
export type RootStackParamList = {
  MainTabs: undefined;
  MeterDetail: { meter: WaterMeter };
  NewReading: { meter: WaterMeter };
  ReadingHistory: { meter: WaterMeter };
  Settings: undefined;
};

export type MainTabParamList = {
  MeterList: undefined;
  Routes: undefined;
  Sync: undefined;
  Settings: undefined;
};

const Stack = createNativeStackNavigator<RootStackParamList>();
const Tab = createMaterialTopTabNavigator<MainTabParamList>();

// 主标签页导航
const MainTabNavigator: React.FC = () => {
  return (
    <Tab.Navigator
      screenOptions={{
        tabBarActiveTintColor: '#3182CE',
        tabBarInactiveTintColor: '#A0AEC0',
        tabBarIndicatorStyle: {
          backgroundColor: '#3182CE',
          height: 3,
        },
        tabBarStyle: {
          backgroundColor: '#FFFFFF',
          elevation: 4,
          shadowOpacity: 0.1,
        },
        tabBarLabelStyle: {
          fontSize: 14,
          fontWeight: '600',
        },
      }}
    >
      <Tab.Screen
        name="MeterList"
        component={MeterListScreen}
        options={{
          title: '水表列表',
        }}
      />
      <Tab.Screen
        name="Routes"
        component={RouteManagementScreen}
        options={{
          title: '抄表路线',
        }}
      />
      <Tab.Screen
        name="Sync"
        component={SyncStatusScreen}
        options={{
          title: '同步状态',
        }}
      />
      <Tab.Screen
        name="Settings"
        component={SettingsScreen}
        options={{
          title: '设置',
        }}
      />
    </Tab.Navigator>
  );
};

// 主导航器
const AppNavigator: React.FC = () => {
  return (
    <Stack.Navigator
      screenOptions={{
        headerStyle: {
          backgroundColor: '#3182CE',
        },
        headerTintColor: '#FFFFFF',
        headerTitleStyle: {
          fontWeight: 'bold',
        },
      }}
    >
      <Stack.Screen
        name="MainTabs"
        component={MainTabNavigator}
        options={{
          title: '水表抄表系统',
        }}
      />
      <Stack.Screen
        name="MeterDetail"
        component={MeterDetailScreen}
        options={({ route }) => ({
          title: `水表详情 - ${route.params.meter.meter_number}`,
        })}
      />
      <Stack.Screen
        name="NewReading"
        component={NewReadingScreen}
        options={({ route }) => ({
          title: `录入读数 - ${route.params.meter.meter_number}`,
        })}
      />
      <Stack.Screen
        name="ReadingHistory"
        component={ReadingHistoryScreen}
        options={({ route }) => ({
          title: `读数历史 - ${route.params.meter.meter_number}`,
        })}
      />
    </Stack.Navigator>
  );
};

export default AppNavigator;

// src/App.tsx
import React, { useEffect, useState } from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { NativeBaseProvider, extendTheme } from 'native-base';
import { StatusBar } from 'react-native';
import { initDB } from './database/DatabaseInit';
import AppNavigator from './navigation/AppNavigator';
import LoadingScreen from './components/LoadingScreen';

// 自定义主题
const theme = extendTheme({
  colors: {
    primary: {
      50: '#E3F2FD',
      100: '#BBDEFB',
      200: '#90CAF9',
      300: '#64B5F6',
      400: '#42A5F5',
      500: '#2196F3',
      600: '#1E88E5',
      700: '#1976D2',
      800: '#1565C0',
      900: '#0D47A1',
    },
  },
  config: {
    initialColorMode: 'light',
  },
});

const App: React.FC = () => {
  const [isReady, setIsReady] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const initializeApp = async () => {
      try {
        // 初始化数据库
        await initDB();
        
        // 这里可以添加其他初始化操作
        // 比如检查权限、加载配置等
        
        setIsReady(true);
      } catch (err) {
        console.error('App initialization failed:', err);
        setError('应用初始化失败，请重启应用');
      }
    };

    initializeApp();
  }, []);

  if (error) {
    return (
      <NativeBaseProvider theme={theme}>
        <LoadingScreen 
          title="初始化失败" 
          message={error}
          showError={true}
        />
      </NativeBaseProvider>
    );
  }

  if (!isReady) {
    return (
      <NativeBaseProvider theme={theme}>
        <LoadingScreen 
          title="正在初始化" 
          message="请稍候..."
        />
      </NativeBaseProvider>
    );
  }

  return (
    <NativeBaseProvider theme={theme}>
      <StatusBar backgroundColor="#1976D2" barStyle="light-content" />
      <NavigationContainer>
        <AppNavigator />
      </NavigationContainer>
    </NativeBaseProvider>
  );
};

export default App;

// src/components/LoadingScreen.tsx
import React from 'react';
import {
  VStack,
  Spinner,
  Text,
  Icon,
  Box,
} from 'native-base';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';

interface LoadingScreenProps {
  title: string;
  message: string;
  showError?: boolean;
}

const LoadingScreen: React.FC<LoadingScreenProps> = ({
  title,
  message,
  showError = false,
}) => {
  return (
    <Box flex={1} bg="white" justifyContent="center" alignItems="center" safeArea>
      <VStack space={6} alignItems="center">
        {showError ? (
          <Icon
            as={MaterialCommunityIcons}
            name="alert-circle"
            size={16}
            color="red.500"
          />
        ) : (
          <Spinner size="lg" color="blue.500" />
        )}
        
        <VStack space={2} alignItems="center">
          <Text fontSize="xl" fontWeight="bold" color="gray.800">
            {title}
          </Text>
          <Text fontSize="md" color="gray.600" textAlign="center">
            {message}
          </Text>
        </VStack>
      </VStack>
    </Box>
  );
};

export default LoadingScreen;

// src/screens/SyncStatusScreen.tsx
import React, { useState, useEffect, useCallback } from 'react';
import {
  VStack,
  HStack,
  Text,
  Box,
  Badge,
  Button,
  FlatList,
  IconButton,
  Icon,
  useToast,
  Spinner,
  Progress,
} from 'native-base';
import { RefreshControl } from 'react-native';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import { MeterReading, MeterReadingModel } from '../database/MeterReading';
import { SyncService } from '../services/SyncService';
import { NetworkService } from '../services/NetworkService';

interface SyncStatusScreenProps {}

const SyncStatusScreen: React.FC<SyncStatusScreenProps> = () => {
  const toast = useToast();
  
  const [pendingReadings, setPendingReadings] = useState<MeterReading[]>([]);
  const [isConnected, setIsConnected] = useState(false);
  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [syncing, setSyncing] = useState(false);
  const [syncProgress, setSyncProgress] = useState(0);

  const loadPendingReadings = useCallback(async () => {
    setLoading(true);
    try {
      const readings = await MeterReadingModel.getPendingSync();
      setPendingReadings(readings);
    } catch (error) {
      console.error('Error loading pending readings:', error);
      toast.show({
        title: '加载失败',
        description: '无法加载待同步数据',
        status: 'error',
      });
    } finally {
      setLoading(false);
    }
  }, [toast]);

  const checkNetworkStatus = useCallback(async () => {
    const connected = await NetworkService.isConnected();
    setIsConnected(connected);
  }, []);

  useEffect(() => {
    loadPendingReadings();
    checkNetworkStatus();

    // 监听网络状态变化
    const unsubscribe = NetworkService.subscribeToNetworkStatus(setIsConnected);
    return unsubscribe;
  }, [loadPendingReadings, checkNetworkStatus]);

  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    await Promise.all([loadPendingReadings(), checkNetworkStatus()]);
    setRefreshing(false);
  }, [loadPendingReadings, checkNetworkStatus]);

  const handleSyncAll = useCallback(async () => {
    if (!isConnected) {
      toast.show({
        title: '网络未连接',
        description: '请检查网络连接后重试',
        status: 'warning',
      });
      return;
    }

    if (pendingReadings.length === 0) {
      toast.show({
        title: '没有待同步数据',
        description: '所有数据已同步完成',
        status: 'info',
      });
      return;
    }

    setSyncing(true);
    setSyncProgress(0);

    try {
      await SyncService.syncAllPendingReadings();
      
      toast.show({
        title: '同步完成',
        description: '所有待同步数据已成功同步',
        status: 'success',
      });

      // 重新加载数据
      await loadPendingReadings();
    } catch (error) {
      console.error('Error syncing readings:', error);
      toast.show({
        title: '同步失败',
        description: '部分数据同步失败，请重试',
        status: 'error',
      });
    } finally {
      setSyncing(false);
      setSyncProgress(0);
    }
  }, [isConnected, pendingReadings.length, toast, loadPendingReadings]);

  const renderPendingItem = useCallback(({ item }: { item: MeterReading }) => (
    <Box
      bg="white"
      rounded="lg"
      shadow={1}
      p={4}
      mb={3}
      borderLeftWidth={4}
      borderLeftColor={item.sync_status === 'failed' ? 'red.500' : 'orange.500'}
    >
      <VStack space={2}>
        <HStack justifyContent="space-between" alignItems="center">
          <VStack>
            <Text fontSize="md" fontWeight="bold" color="gray.800">
              {item.meter_number}
            </Text>
            <Text fontSize="sm" color="gray.600">
              {item.customer_name}
            </Text>
          </VStack>
          
          <Badge
            colorScheme={item.sync_status === 'failed' ? 'red' : 'orange'}
            variant="solid"
          >
            {item.sync_status === 'failed' ? '同步失败' : '待同步'}
          </Badge>
        </HStack>
        
        <HStack justifyContent="space-between" alignItems="center">
          <VStack>
            <Text fontSize="lg" fontWeight="semibold" color="blue.600">
              {item.current_reading} m³
            </Text>
            <Text fontSize="xs" color="gray.500">
              {item.reading_date} {item.reading_time}
            </Text>
          </VStack>
          
          <IconButton
            size="sm"
            bg="blue.500"
            rounded="md"
            icon={
              <Icon
                as={MaterialCommunityIcons}
                name="sync"
                size={4}
                color="white"
              />
            }
            onPress={() => {
              // 单独同步这一条记录
              if (item.reading_id) {
                SyncService.scheduleSyncReading(item.reading_id);
              }
            }}
          />
        </HStack>
      </VStack>
    </Box>
  ), []);

  return (
    <VStack flex={1} bg="gray.50" safeArea>
      {/* 网络状态和统计信息 */}
      <Box bg="white" shadow={1} p={4}>
        <VStack space={3}>
          <HStack justifyContent="space-between" alignItems="center">
            <Text fontSize="lg" fontWeight="bold" color="gray.800">
              同步状态
            </Text>
            <IconButton
              size="sm"
              icon={
                <Icon
                  as={MaterialCommunityIcons}
                  name="refresh"
                  size={5}
                  color="blue.500"
                />
              }
              onPress={handleRefresh}
            />
          </HStack>
          
          <HStack space={4} alignItems="center">
            <HStack space={2} alignItems="center" flex={1}>
              <Icon
                as={MaterialCommunityIcons}
                name={isConnected ? 'wifi' : 'wifi-off'}
                size={5}
                color={isConnected ? 'green.500' : 'red.500'}
              />
              <Text fontSize="sm" color="gray.600">
                {isConnected ? '网络已连接' : '网络未连接'}
              </Text>
```

## 3.5 导航配置和应用入口（续）

```typescript
// src/screens/SyncStatusScreen.tsx (续)
            </HStack>
            
            <Badge
              colorScheme={pendingReadings.length > 0 ? 'orange' : 'green'}
              variant="solid"
            >
              待同步: {pendingReadings.length}
            </Badge>
          </HStack>
          
          {syncing && (
            <VStack space={2}>
              <HStack justifyContent="space-between" alignItems="center">
                <Text fontSize="sm" color="gray.600">同步进度</Text>
                <Text fontSize="sm" color="blue.600">
                  {Math.round(syncProgress * 100)}%
                </Text>
              </HStack>
              <Progress value={syncProgress * 100} colorScheme="blue" />
            </VStack>
          )}
        </VStack>
      </Box>

      {/* 同步操作按钮 */}
      <Box bg="white" shadow={1} p={4} mt={2}>
        <HStack space={3}>
          <Button
            flex={1}
            colorScheme="blue"
            onPress={handleSyncAll}
            isLoading={syncing}
            isLoadingText="同步中..."
            isDisabled={!isConnected || pendingReadings.length === 0}
            leftIcon={
              <Icon
                as={MaterialCommunityIcons}
                name="sync"
                size={5}
              />
            }
          >
            同步所有数据
          </Button>
          
          <Button
            flex={1}
            variant="outline"
            onPress={handleRefresh}
            leftIcon={
              <Icon
                as={MaterialCommunityIcons}
                name="refresh"
                size={5}
              />
            }
          >
            刷新状态
          </Button>
        </HStack>
      </Box>

      {/* 待同步列表 */}
      <Box flex={1} p={4}>
        <Text fontSize="md" fontWeight="bold" color="gray.700" mb={3}>
          待同步数据列表
        </Text>
        
        {loading ? (
          <Box flex={1} justifyContent="center" alignItems="center">
            <Spinner size="lg" color="blue.500" />
            <Text mt={2} color="gray.600">加载中...</Text>
          </Box>
        ) : (
          <FlatList
            data={pendingReadings}
            renderItem={renderPendingItem}
            keyExtractor={(item) => item.reading_id?.toString() || ''}
            refreshControl={
              <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
            }
            ListEmptyComponent={
              <Box flex={1} justifyContent="center" alignItems="center" py={10}>
                <Icon
                  as={MaterialCommunityIcons}
                  name="check-circle"
                  size={16}
                  color="green.400"
                />
                <Text mt={2} color="gray.500">所有数据已同步完成</Text>
              </Box>
            }
            showsVerticalScrollIndicator={false}
          />
        )}
      </Box>
    </VStack>
  );
};

export default SyncStatusScreen;

// src/screens/SettingsScreen.tsx
import React, { useState, useCallback } from 'react';
import {
  VStack,
  HStack,
  Text,
  Box,
  Switch,
  Button,
  Input,
  Select,
  IconButton,
  Icon,
  useToast,
  AlertDialog,
  Divider,
} from 'native-base';
import { Alert } from 'react-native';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import AsyncStorage from '@react-native-async-storage/async-storage';

interface SettingsScreenProps {}

const SettingsScreen: React.FC<SettingsScreenProps> = () => {
  const toast = useToast();
  
  const [autoSync, setAutoSync] = useState(true);
  const [syncInterval, setSyncInterval] = useState('300'); // 5分钟
  const [apiUrl, setApiUrl] = useState('https://your-api-server.com/api');
  const [showClearDataDialog, setShowClearDataDialog] = useState(false);
  const [isClearing, setIsClearing] = useState(false);

  const handleAutoSyncToggle = useCallback(async (value: boolean) => {
    setAutoSync(value);
    try {
      await AsyncStorage.setItem('autoSync', JSON.stringify(value));
      toast.show({
        title: '设置已保存',
        status: 'success',
      });
    } catch (error) {
      console.error('Error saving auto sync setting:', error);
    }
  }, [toast]);

  const handleSyncIntervalChange = useCallback(async (value: string) => {
    setSyncInterval(value);
    try {
      await AsyncStorage.setItem('syncInterval', value);
      toast.show({
        title: '同步间隔已更新',
        status: 'success',
      });
    } catch (error) {
      console.error('Error saving sync interval:', error);
    }
  }, [toast]);

  const handleApiUrlSave = useCallback(async () => {
    try {
      await AsyncStorage.setItem('apiUrl', apiUrl);
      toast.show({
        title: 'API地址已保存',
        status: 'success',
      });
    } catch (error) {
      console.error('Error saving API URL:', error);
      toast.show({
        title: '保存失败',
        status: 'error',
      });
    }
  }, [apiUrl, toast]);

  const handleClearData = useCallback(async () => {
    setIsClearing(true);
    try {
      // 这里应该调用数据库清理服务
      // await DatabaseService.clearAllData();
      
      toast.show({
        title: '数据清理完成',
        description: '所有本地数据已清除',
        status: 'success',
      });
    } catch (error) {
      console.error('Error clearing data:', error);
      toast.show({
        title: '清理失败',
        status: 'error',
      });
    } finally {
      setIsClearing(false);
      setShowClearDataDialog(false);
    }
  }, [toast]);

  const handleExportData = useCallback(() => {
    // 实现数据导出功能
    toast.show({
      title: '功能开发中',
      description: '数据导出功能正在开发中',
      status: 'info',
    });
  }, [toast]);

  return (
    <VStack flex={1} bg="gray.50" safeArea>
      <VStack space={4} p={4}>
        
        {/* 同步设置 */}
        <Box bg="white" rounded="lg" shadow={1} p={4}>
          <VStack space={4}>
            <Text fontSize="lg" fontWeight="bold" color="gray.800">
              同步设置
            </Text>
            
            <Divider />
            
            <HStack justifyContent="space-between" alignItems="center">
              <VStack flex={1}>
                <Text fontSize="md" color="gray.700">自动同步</Text>
                <Text fontSize="sm" color="gray.500">
                  当有网络连接时自动同步数据
                </Text>
              </VStack>
              <Switch
                value={autoSync}
                onValueChange={handleAutoSyncToggle}
                colorScheme="blue"
              />
            </HStack>
            
            {autoSync && (
              <VStack space={2}>
                <Text fontSize="md" color="gray.700">同步间隔</Text>
                <Select
                  selectedValue={syncInterval}
                  onValueChange={handleSyncIntervalChange}
                  placeholder="选择同步间隔"
                >
                  <Select.Item label="1分钟" value="60" />
                  <Select.Item label="5分钟" value="300" />
                  <Select.Item label="10分钟" value="600" />
                  <Select.Item label="30分钟" value="1800" />
                  <Select.Item label="1小时" value="3600" />
                </Select>
              </VStack>
            )}
          </VStack>
        </Box>

        {/* 服务器设置 */}
        <Box bg="white" rounded="lg" shadow={1} p={4}>
          <VStack space={4}>
            <Text fontSize="lg" fontWeight="bold" color="gray.800">
              服务器设置
            </Text>
            
            <Divider />
            
            <VStack space={2}>
              <Text fontSize="md" color="gray.700">API服务器地址</Text>
              <Input
                value={apiUrl}
                onChangeText={setApiUrl}
                placeholder="输入API服务器地址"
                bg="gray.50"
                _focus={{ bg: 'white', borderColor: 'blue.500' }}
              />
              <Button
                size="sm"
                onPress={handleApiUrlSave}
                leftIcon={
                  <Icon
                    as={MaterialCommunityIcons}
                    name="content-save"
                    size={4}
                  />
                }
              >
                保存
              </Button>
            </VStack>
          </VStack>
        </Box>

        {/* 数据管理 */}
        <Box bg="white" rounded="lg" shadow={1} p={4}>
          <VStack space={4}>
            <Text fontSize="lg" fontWeight="bold" color="gray.800">
              数据管理
            </Text>
            
            <Divider />
            
            <VStack space={3}>
              <Button
                variant="outline"
                onPress={handleExportData}
                leftIcon={
                  <Icon
                    as={MaterialCommunityIcons}
                    name="export"
                    size={5}
                  />
                }
              >
                导出数据
              </Button>
              
              <Button
                colorScheme="red"
                variant="outline"
                onPress={() => setShowClearDataDialog(true)}
                leftIcon={
                  <Icon
                    as={MaterialCommunityIcons}
                    name="delete"
                    size={5}
                  />
                }
              >
                清除所有数据
              </Button>
            </VStack>
          </VStack>
        </Box>

        {/* 应用信息 */}
        <Box bg="white" rounded="lg" shadow={1} p={4}>
          <VStack space={4}>
            <Text fontSize="lg" fontWeight="bold" color="gray.800">
              应用信息
            </Text>
            
            <Divider />
            
            <VStack space={2}>
              <HStack justifyContent="space-between">
                <Text color="gray.600">版本号</Text>
                <Text fontWeight="medium">1.0.0</Text>
              </HStack>
              
              <HStack justifyContent="space-between">
                <Text color="gray.600">构建号</Text>
                <Text fontWeight="medium">20241201</Text>
              </HStack>
              
              <HStack justifyContent="space-between">
                <Text color="gray.600">最后更新</Text>
                <Text fontWeight="medium">2024年12月1日</Text>
              </HStack>
            </VStack>
          </VStack>
        </Box>
      </VStack>

      {/* 清除数据确认对话框 */}
      <AlertDialog
        isOpen={showClearDataDialog}
        onClose={() => setShowClearDataDialog(false)}
        leastDestructiveRef={undefined}
      >
        <AlertDialog.Content>
          <AlertDialog.CloseButton />
          <AlertDialog.Header>清除所有数据</AlertDialog.Header>
          <AlertDialog.Body>
            此操作将删除所有本地存储的水表数据和读数记录，且无法恢复。
            确定要继续吗？
          </AlertDialog.Body>
          <AlertDialog.Footer>
            <Button.Group space={2}>
              <Button
                variant="unstyled"
                colorScheme="coolGray"
                onPress={() => setShowClearDataDialog(false)}
              >
                取消
              </Button>
              <Button
                colorScheme="danger"
                onPress={handleClearData}
                isLoading={isClearing}
                isLoadingText="清理中..."
              >
                确认清除
              </Button>
            </Button.Group>
          </AlertDialog.Footer>
        </AlertDialog.Content>
      </AlertDialog>
    </VStack>
  );
};

export default SettingsScreen;

// src/screens/RouteManagementScreen.tsx
import React, { useState, useEffect, useCallback } from 'react';
import {
  VStack,
  HStack,
  Text,
  Box,
  FlatList,
  IconButton,
  Icon,
  useToast,
  Spinner,
  Badge,
  Progress,
  Button,
  Modal,
  Input,
} from 'native-base';
import { RefreshControl } from 'react-native';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import { useNavigation } from '@react-navigation/native';

interface Route {
  route_id: number;
  route_name: string;
  description?: string;
  estimated_duration?: number;
  status: string;
  meter_count?: number;
  completed_count?: number;
  created_date?: string;
}

interface RouteManagementScreenProps {}

const RouteManagementScreen: React.FC<RouteManagementScreenProps> = () => {
  const navigation = useNavigation();
  const toast = useToast();
  
  const [routes, setRoutes] = useState<Route[]>([]);
  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [newRouteName, setNewRouteName] = useState('');
  const [newRouteDescription, setNewRouteDescription] = useState('');

  const loadRoutes = useCallback(async () => {
    setLoading(true);
    try {
      // 这里应该调用数据库服务加载路线数据
      // const routeData = await ReadingRouteModel.getAllWithStats();
      
      // 模拟数据
      const mockRoutes: Route[] = [
        {
          route_id: 1,
          route_name: '市中心区域A',
          description: '包含商业区和住宅区水表',
          estimated_duration: 120,
          status: 'active',
          meter_count: 25,
          completed_count: 18,
        },
        {
          route_id: 2,
          route_name: '工业园区B',
          description: '工业园区内企业水表',
          estimated_duration: 90,
          status: 'active',
          meter_count: 15,
          completed_count: 15,
        },
        {
          route_id: 3,
          route_name: '老城区C',
          description: '老城区居民水表',
          estimated_duration: 150,
          status: 'active',
          meter_count: 35,
          completed_count: 12,
        },
      ];
      
      setRoutes(mockRoutes);
    } catch (error) {
      console.error('Error loading routes:', error);
      toast.show({
        title: '加载失败',
        description: '无法加载路线数据',
        status: 'error',
      });
    } finally {
      setLoading(false);
    }
  }, [toast]);

  useEffect(() => {
    loadRoutes();
  }, [loadRoutes]);

  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    await loadRoutes();
    setRefreshing(false);
  }, [loadRoutes]);

  const handleRoutePress = useCallback((route: Route) => {
    navigation.navigate('MeterList', { routeId: route.route_id });
  }, [navigation]);

  const handleCreateRoute = useCallback(async () => {
    if (!newRouteName.trim()) {
      toast.show({
        title: '请输入路线名称',
        status: 'warning',
      });
      return;
    }

    try {
      // 这里应该调用数据库服务创建新路线
      // await ReadingRouteModel.create({
      //   route_name: newRouteName,
      //   description: newRouteDescription,
      //   status: 'active',
      // });
      
      toast.show({
        title: '路线创建成功',
        status: 'success',
      });
      
      setShowCreateModal(false);
      setNewRouteName('');
      setNewRouteDescription('');
      await loadRoutes();
    } catch (error) {
      console.error('Error creating route:', error);
      toast.show({
        title: '创建失败',
        status: 'error',
      });
    }
  }, [newRouteName, newRouteDescription, toast, loadRoutes]);

  const renderRouteItem = useCallback(({ item }: { item: Route }) => {
    const completionRate = item.meter_count ? 
      (item.completed_count || 0) / item.meter_count : 0;
    
    return (
      <Box
        bg="white"
        rounded="lg"
        shadow={1}
        p={4}
        mb={3}
        borderLeftWidth={4}
        borderLeftColor="blue.500"
      >
        <VStack space={3}>
          <HStack justifyContent="space-between" alignItems="center">
            <VStack flex={1}>
              <Text fontSize="lg" fontWeight="bold" color="gray.800">
                {item.route_name}
              </Text>
              {item.description && (
                <Text fontSize="sm" color="gray.600" numberOfLines={2}>
                  {item.description}
                </Text>
              )}
            </VStack>
            
            <Badge
              colorScheme={item.status === 'active' ? 'green' : 'gray'}
              variant="solid"
            >
              {item.status === 'active' ? '启用' : '停用'}
            </Badge>
          </HStack>
          
          <HStack justifyContent="space-between" alignItems="center">
            <VStack>
              <Text fontSize="xs" color="gray.500">水表总数</Text>
              <Text fontSize="lg" fontWeight="semibold" color="blue.600">
                {item.meter_count || 0}
              </Text>
            </VStack>
            
            <VStack alignItems="center">
              <Text fontSize="xs" color="gray.500">已完成</Text>
              <Text fontSize="lg" fontWeight="semibold" color="green.600">
                {item.completed_count || 0}
              </Text>
            </VStack>
            
            {item.estimated_duration && (
              <VStack alignItems="flex-end">
                <Text fontSize="xs" color="gray.500">预计时长</Text>
                <Text fontSize="sm" color="gray.700">
                  {Math.floor(item.estimated_duration / 60)}h {item.estimated_duration % 60}m
                </Text>
              </VStack>
            )}
          </HStack>
          
          {item.meter_count && item.meter_count > 0 && (
            <VStack space={2}>
              <HStack justifyContent="space-between" alignItems="center">
                <Text fontSize="sm" color="gray.600">完成进度</Text>
                <Text fontSize="sm" color="gray.600">
                  {Math.round(completionRate * 100)}%
                </Text>
              </HStack>
              <Progress 
                value={completionRate * 100} 
                colorScheme={completionRate === 1 ? 'green' : 'blue'}
              />
            </VStack>
          )}
          
          <HStack space={2} justifyContent="flex-end">
            <Button
              size="sm"
              variant="outline"
              onPress={() => handleRoutePress(item)}
              leftIcon={
                <Icon
                  as={MaterialCommunityIcons}
                  name="eye"
                  size={4}
                />
              }
            >
              查看
            </Button>
            
            <Button
              size="sm"
              colorScheme="blue"
              onPress={() => handleRoutePress(item)}
              leftIcon={
                <Icon
                  as={MaterialCommunityIcons}
                  name="play"
                  size={4}
                />
              }
            >
              开始抄表
            </Button>
          </HStack>
        </VStack>
      </Box>
    );
  }, [handleRoutePress]);

  return (
    <VStack flex={1} bg="gray.50" safeArea>
      {/* 头部统计 */}
      <Box bg="white" shadow={1} p={4}>
        <VStack space={3}>
          <HStack justifyContent="space-between" alignItems="center">
            <Text fontSize="lg" fontWeight="bold" color="gray.800">
              抄表路线管理
            </Text>
            <IconButton
              size="sm"
              bg="blue.500"
              rounded="md"
              icon={
                <Icon
                  as={MaterialCommunityIcons}
                  name="plus"
                  size={5}
                  color="white"
                />
              }
              onPress={() => setShowCreateModal(true)}
            />
          </HStack>
          
          <HStack space={4} justifyContent="space-around">
            <VStack alignItems="center">
              <Text fontSize="lg" fontWeight="bold" color="blue.600">
                {routes.length}
              </Text>
              <Text fontSize="sm" color="gray.600">总路线</Text>
            </VStack>
            
            <VStack alignItems="center">
              <Text fontSize="lg" fontWeight="bold" color="green.600">
                {routes.filter(r => r.status === 'active').length}
              </Text>
              <Text fontSize="sm" color="gray.600">启用中</Text>
            </VStack>
            
            <VStack alignItems="center">
              <Text fontSize="lg" fontWeight="bold" color="orange.600">
                {routes.reduce((sum, r) => sum + (r.meter_count || 0), 0)}
              </Text>
              <Text fontSize="sm" color="gray.600">水表总数</Text>
            </VStack>
          </HStack>
        </VStack>
      </Box>

      {/* 路线列表 */}
      <Box flex={1} p={4}>
        {loading ? (
          <Box flex={1} justifyContent="center" alignItems="center">
            <Spinner size="lg" color="blue.500" />
            <Text mt={2} color="gray.600">加载中...</Text>
          </Box>
        ) : (
          <FlatList
            data={routes}
            renderItem={renderRouteItem}
            keyExtractor={(item) => item.route_id.toString()}
            refreshControl={
              <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
            }
            ListEmptyComponent={
              <Box flex={1} justifyContent="center" alignItems="center" py={10}>
                <Icon
                  as={MaterialCommunityIcons}
                  name="map-marker-path"
                  size={16}
                  color="gray.400"
                />
                <Text mt={2} color="gray.500">暂无路线数据</Text>
                <Button
                  mt={4}
                  onPress={() => setShowCreateModal(true)}
                  leftIcon={
                    <Icon
                      as={MaterialCommunityIcons}
                      name="plus"
                      size={4}
                    />
                  }
                >
                  创建第一条路线
                </Button>
              </Box>
            }
            showsVerticalScrollIndicator={false}
          />
        )}
      </Box>

      {/* 创建路线模态框 */}
      <Modal isOpen={showCreateModal} onClose={() => setShowCreateModal(false)}>
        <Modal.Content maxWidth="90%">
          <Modal.CloseButton />
          <Modal.Header>创建新路线</Modal.Header>
          <Modal.Body>
            <VStack space={4}>
              <VStack space={2}>
                <Text fontSize="md" color="gray.700">路线名称 *</Text>
                <Input
                  value={newRouteName}
                  onChangeText={setNewRouteName}
                  placeholder="请输入路线名称"
                  bg="gray.50"
                  _focus={{ bg: 'white', borderColor: 'blue.500' }}
                />
              </VStack>
              
              <VStack space={2}>
                <Text fontSize="md" color="gray.700">路线描述</Text>
                <Input
                  value={newRouteDescription}
                  onChangeText={setNewRouteDescription}
                  placeholder="请输入路线描述（可选）"
                  bg="gray.50"
                  _focus={{ bg: 'white', borderColor: 'blue.500' }}
                  multiline
                  numberOfLines={3}
                />
              </VStack>
            </VStack>
          </Modal.Body>
          <Modal.Footer>
            <Button.Group space={2}>
              <Button
                variant="ghost"
                colorScheme="blueGray"
                onPress={() => {
                  setShowCreateModal(false);
                  setNewRouteName('');
                  setNewRouteDescription('');
                }}
              >
                取消
              </Button>
              <Button onPress={handleCreateRoute}>
                创建
              </Button>
            </Button.Group>
          </Modal.Footer>
        </Modal.Content>
      </Modal>
    </VStack>
  );
};

export default RouteManagementScreen;
```

## 4. 项目配置和构建

### 4.1 Android配置

```xml
<!-- android/app/src/main/AndroidManifest.xml -->
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
  package="com.watermeterapp">

  <uses-permission android:name="android.permission.INTERNET" />
  <uses-permission android:name="android.permission.CAMERA" />
  <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
  <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
  <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
  <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />

  <application
    android:name=".MainApplication"
    android:label="@string/app_name"
    android:icon="@mipmap/ic_launcher"
    android:roundIcon="@mipmap/ic_launcher_round"
    android:allowBackup="false"
    android:theme="@style/AppTheme"
    android:usesCleartextTraffic="true">
    
    <activity
      android:name=".MainActivity"
      android:label="@string/app_name"
      android:configChanges="keyboard|keyboardHidden|orientation|screenSize|uiMode"
      android:launchMode="singleTask"
      android:windowSoftInputMode="adjustResize">
      <intent-filter>
        <action android:name="android.intent.action.MAIN" />
        <category android:name="android.intent.category.LAUNCHER" />
      </intent-filter>
    </activity>
    
    <provider
      android:name="androidx.core.content.FileProvider"
      android:authorities="${applicationId}.fileprovider"
      android:exported="false"
      android:grantUriPermissions="true">
      <meta-data
        android:name="android.support.FILE_PROVIDER_PATHS"
        android:resource="@xml/file_paths" />
    </provider>
  </application>
</manifest>
<!-- android/app/src/main/res/xml/file_paths.xml -->
<?xml version="1.0" encoding="utf-8"?>
<paths xmlns:android="http://schemas.android.com/apk/res/android">
    <external-path name="external_files" path="."/>
    <external-cache-path name="external_cache" path="."/>
</paths>
```

### 4.2 iOS配置

```xml
<!-- ios/WaterMeterApp/Info.plist -->
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
  <!-- 其他配置 -->
  
  <!-- 相机权限 -->
  <key>NSCameraUsageDescription</key>
  <string>应用需要使用相机拍摄水表照片</string>
  
  <!-- 照片库权限 -->
  <key>NSPhotoLibraryUsageDescription</key>
  <string>应用需要访问照片库选择水表照片</string>
  
  <!-- 位置权限 -->
  <key>NSLocationWhenInUseUsageDescription</key>
  <string>应用需要获取位置信息用于记录抄表位置</string>
  
  <!-- 文件访问权限 -->
  <key>NSAppleMusicUsageDescription</key>
  <string>应用需要访问文件系统保存数据</string>
  
</dict>
</plist>
```

### 4.3 构建脚本

```json
// package.json
{
  "name": "WaterMeterApp",
  "version": "1.0.0",
  "private": true,
  "scripts": {
    "android": "react-native run-android",
    "ios": "react-native run-ios",
    "start": "react-native start",
    "test": "jest",
    "lint": "eslint . --ext .js,.jsx,.ts,.tsx",
    "build:android": "cd android && ./gradlew assembleRelease",
    "build:android-debug": "cd android && ./gradlew assembleDebug",
    "build:ios": "cd ios && xcodebuild -workspace WaterMeterApp.xcworkspace -scheme WaterMeterApp -configuration Release -archivePath WaterMeterApp.xcarchive archive",
    "clean": "react-native clean",
    "clean:android": "cd android && ./gradlew clean",
    "clean:ios": "cd ios && xcodebuild clean",
    "postinstall": "npx react-native link"
  },
  "dependencies": {
    "react": "18.2.0",
    "react-native": "0.72.0",
    "native-base": "^3.4.0",
    "react-native-svg": "^13.9.0",
    "react-native-safe-area-context": "^4.5.0",
    "@react-navigation/native": "^6.1.0",
    "@react-navigation/native-stack": "^6.9.0",
    "@react-navigation/material-top-tabs": "^6.6.0",
    "react-native-screens": "^3.20.0",
    "react-native-gesture-handler": "^2.10.0",
    "react-native-tab-view": "^3.5.0",
    "react-native-pager-view": "^6.2.0",
    "react-native-sqlite-storage": "^6.0.1",
    "axios": "^1.4.0",
    "react-native-keychain": "^8.1.0",
    "@react-native-async-storage/async-storage": "^1.18.0",
    "react-native-fs": "^2.20.0",
    "react-native-image-picker": "^5.3.0",
    "react-native-image-resizer": "^3.0.0",
    "@react-native-community/geolocation": "^3.0.0",
    "react-native-permissions": "^3.8.0",
    "@react-native-community/netinfo": "^9.3.0",
    "date-fns": "^2.30.0",
    "date-fns-tz": "^2.0.0",
    "lodash": "^4.17.21",
    "react-native-vector-icons": "^9.2.0"
  },
  "devDependencies": {
    "@babel/core": "^7.20.0",
    "@babel/preset-env": "^7.20.0",
    "@babel/runtime": "^7.20.0",
    "@react-native/eslint-config": "^0.72.0",
    "@react-native/metro-config": "^0.72.0",
    "@tsconfig/react-native": "^3.0.0",
    "@types/react": "^18.0.24",
    "@types/react-test-renderer": "^18.0.0",
    "@types/lodash": "^4.14.195",
    "babel-jest": "^29.2.1",
    "eslint": "^8.19.0",
    "jest": "^29.2.1",
    "metro-react-native-babel-preset": "0.76.5",
    "prettier": "^2.4.1",
    "react-test-renderer": "18.2.0",
    "typescript": "4.8.4"
  },
  "engines": {
    "node": ">=16"
  }
}
```

### 4.4 TypeScript配置

```json
// tsconfig.json
{
  "extends": "@tsconfig/react-native/tsconfig.json",
  "compilerOptions": {
    "allowJs": true,
    "allowSyntheticDefaultImports": true,
    "esModuleInterop": true,
    "isolatedModules": true,
    "jsx": "react-jsx",
    "lib": ["es2017"],
    "moduleResolution": "node",
    "noEmit": true,
    "strict": true,
    "target": "esnext",
    "baseUrl": "./src",
    "paths": {
      "@/*": ["*"],
      "@/components/*": ["components/*"],
      "@/screens/*": ["screens/*"],
      "@/services/*": ["services/*"],
      "@/database/*": ["database/*"],
      "@/utils/*": ["utils/*"],
      "@/types/*": ["types/*"]
    }
  },
  "include": [
    "src/**/*",
    "index.js"
  ],
  "exclude": [
    "node_modules",
    "babel.config.js",
    "metro.config.js",
    "jest.config.js"
  ]
}
```

### 4.5 Metro配置

```javascript
// metro.config.js
const {getDefaultConfig, mergeConfig} = require('@react-native/metro-config');

const defaultConfig = getDefaultConfig(__dirname);

const config = {
  transformer: {
    babelTransformerPath: require.resolve('react-native-svg-transformer'),
  },
  resolver: {
    assetExts: defaultConfig.resolver.assetExts.filter(ext => ext !== 'svg'),
    sourceExts: [...defaultConfig.resolver.sourceExts, 'svg'],
  },
};

module.exports = mergeConfig(defaultConfig, config);
```

### 4.6 Babel配置

```javascript
// babel.config.js
module.exports = {
  presets: ['module:metro-react-native-babel-preset'],
  plugins: [
    [
      'module-resolver',
      {
        root: ['./src'],
        alias: {
          '@': './src',
          '@/components': './src/components',
          '@/screens': './src/screens',
          '@/services': './src/services',
          '@/database': './src/database',
          '@/utils': './src/utils',
          '@/types': './src/types',
        },
      },
    ],
  ],
};
```

## 5. 额外功能组件

### 5.1 数据导入导出服务

```typescript
// src/services/DataExportService.ts
import RNFS from 'react-native-fs';
import { MeterReadingModel } from '../database/MeterReading';
import { WaterMeterModel } from '../database/WaterMeter';
import { Share } from 'react-native';

export class DataExportService {
  
  // 导出读数数据为CSV
  static async exportReadingsToCSV(startDate?: string, endDate?: string): Promise<string> {
    try {
      const readings = await MeterReadingModel.getByDateRange(startDate, endDate);
      
      const csvHeader = [
        '读数ID',
        '水表编号',
        '客户名称',
        '上次读数',
        '当前读数',
        '用水量',
        '读数日期',
        '读数时间',
        '抄表员',
        '备注',
        '纬度',
        '经度',
        '同步状态',
        '创建时间'
      ].join(',');
      
      const csvRows = readings.map(reading => [
        reading.reading_id || '',
        reading.meter_number || '',
        reading.customer_name || '',
        reading.previous_reading || '',
        reading.current_reading || '',
        reading.consumption || '',
        reading.reading_date || '',
        reading.reading_time || '',
        reading.reader_user_id || '',
        `"${reading.notes || ''}"`, // 处理可能包含逗号的备注
        reading.latitude || '',
        reading.longitude || '',
        reading.sync_status || '',
        reading.created_date || ''
      ].join(','));
      
      const csvContent = [csvHeader, ...csvRows].join('\n');
      
      const fileName = `readings_${new Date().toISOString().split('T')[0]}.csv`;
      const filePath = `${RNFS.DocumentDirectoryPath}/${fileName}`;
      
      await RNFS.writeFile(filePath, csvContent, 'utf8');
      
      return filePath;
    } catch (error) {
      console.error('Error exporting readings:', error);
      throw error;
    }
  }
  
  // 导出水表数据为CSV
  static async exportWaterMetersToCSV(): Promise<string> {
    try {
      const meters = await WaterMeterModel.getAll(1000);
      
      const csvHeader = [
        '水表ID',
        '水表编号',
        '客户ID',
        '客户名称',
        '客户地址',
        '水表类型',
        '安装日期',
        '最后维护日期',
        '位置描述',
        '纬度',
        '经度',
        '状态',
        '最后读数',
        '最后读数日期',
        '创建日期'
      ].join(',');
      
      const csvRows = meters.map(meter => [
        meter.meter_id || '',
        meter.meter_number || '',
        meter.customer_id || '',
        meter.customer_name || '',
        `"${meter.customer_address || ''}"`,
        meter.meter_type || '',
        meter.installation_date || '',
        meter.last_maintenance_date || '',
        `"${meter.location_description || ''}"`,
        meter.latitude || '',
        meter.longitude || '',
        meter.status || '',
        meter.last_reading || '',
        meter.last_reading_date || '',
        meter.created_date || ''
      ].join(','));
      
      const csvContent = [csvHeader, ...csvRows].join('\n');
      
      const fileName = `water_meters_${new Date().toISOString().split('T')[0]}.csv`;
      const filePath = `${RNFS.DocumentDirectoryPath}/${fileName}`;
      
      await RNFS.writeFile(filePath, csvContent, 'utf8');
      
      return filePath;
    } catch (error) {
      console.error('Error exporting water meters:', error);
      throw error;
    }
  }
  
  // 分享导出的文件
  static async shareExportedFile(filePath: string): Promise<void> {
    try {
      const result = await Share.share({
        url: `file://${filePath}`,
        title: '导出数据',
        message: '水表读数数据导出文件',
      });
      
      if (result.action === Share.sharedAction) {
        console.log('File shared successfully');
      }
    } catch (error) {
      console.error('Error sharing file:', error);
      throw error;
    }
  }
  
  // 清理临时导出文件
  static async cleanupExportFiles(): Promise<void> {
    try {
      const files = await RNFS.readDir(RNFS.DocumentDirectoryPath);
      const csvFiles = files.filter(file => 
        file.name.endsWith('.csv') && 
        (file.name.startsWith('readings_') || file.name.startsWith('water_meters_'))
      );
      
      // 删除超过7天的导出文件
      const weekAgo = new Date();
      weekAgo.setDate(weekAgo.getDate() - 7);
      
      for (const file of csvFiles) {
        if (new Date(file.mtime) < weekAgo) {
          await RNFS.unlink(file.path);
          console.log(`Deleted old export file: ${file.name}`);
        }
      }
    } catch (error) {
      console.error('Error cleaning up export files:', error);
    }
  }
}
```

### 5.2 报表统计服务

```typescript
// src/services/ReportService.ts
import { executeQuery } from '../database/DatabaseInit';

export interface ReadingStatistics {
  totalReadings: number;
  todayReadings: number;
  weekReadings: number;
  monthReadings: number;
  averageConsumption: number;
  totalConsumption: number;
  syncedReadings: number;
  pendingReadings: number;
  failedReadings: number;
}

export interface MeterStatistics {
  totalMeters: number;
  activeMeters: number;
  inactiveMeters: number;
  metersWithReadings: number;
  metersWithoutReadings: number;
  averageReadingsPerMeter: number;
}

export class ReportService {
  
  // 获取读数统计
  static async getReadingStatistics(): Promise<ReadingStatistics> {
    try {
      const today = new Date().toISOString().split('T')[0];
      const weekAgo = new Date();
      weekAgo.setDate(weekAgo.getDate() - 7);
      const weekAgoStr = weekAgo.toISOString().split('T')[0];
      
      const monthAgo = new Date();
      monthAgo.setMonth(monthAgo.getMonth() - 1);
      const monthAgoStr = monthAgo.toISOString().split('T')[0];
      
      // 总读数
      const totalQuery = `SELECT COUNT(*) as count FROM meter_readings`;
      const totalResult = await executeQuery(totalQuery);
      const totalReadings = totalResult.rows.item(0).count;
      
      // 今日读数
      const todayQuery = `SELECT COUNT(*) as count FROM meter_readings WHERE reading_date = ?`;
      const todayResult = await executeQuery(todayQuery, [today]);
      const todayReadings = todayResult.rows.item(0).count;
      
      // 本周读数
      const weekQuery = `SELECT COUNT(*) as count FROM meter_readings WHERE reading_date >= ?`;
      const weekResult = await executeQuery(weekQuery, [weekAgoStr]);
      const weekReadings = weekResult.rows.item(0).count;
      
      // 本月读数
      const monthQuery = `SELECT COUNT(*) as count FROM meter_readings WHERE reading_date >= ?`;
      const monthResult = await executeQuery(monthQuery, [monthAgoStr]);
      const monthReadings = monthResult.rows.item(0).count;
      
      // 平均和总用水量
      const consumptionQuery = `
        SELECT 
          AVG(consumption) as avg_consumption,
          SUM(consumption) as total_consumption
        FROM meter_readings 
        WHERE consumption > 0
      `;
      const consumptionResult = await executeQuery(consumptionQuery);
      const { avg_consumption, total_consumption } = consumptionResult.rows.item(0);
      
      // 同步状态统计
      const syncStatusQuery = `
        SELECT 
          sync_status,
          COUNT(*) as count
        FROM meter_readings 
        GROUP BY sync_status
      `;
      const syncResult = await executeQuery(syncStatusQuery);
      
      let syncedReadings = 0;
      let pendingReadings = 0;
      let failedReadings = 0;
      
      for (let i = 0; i < syncResult.rows.length; i++) {
        const row = syncResult.rows.item(i);
        switch (row.sync_status) {
          case 'synced':
            syncedReadings = row.count;
            break;
          case 'pending':
            pendingReadings = row.count;
            break;
          case 'failed':
            failedReadings = row.count;
            break;
        }
      }
      
      return {
        totalReadings,
        todayReadings,
        weekReadings,
        monthReadings,
        averageConsumption: avg_consumption || 0,
        totalConsumption: total_consumption || 0,
        syncedReadings,
        pendingReadings,
        failedReadings,
      };
    } catch (error) {
      console.error('Error getting reading statistics:', error);
      throw error;
    }
  }
  
  // 获取水表统计
  static async getMeterStatistics(): Promise<MeterStatistics> {
    try {
      // 总水表数
      const totalQuery = `SELECT COUNT(*) as count FROM water_meters`;
      const totalResult = await executeQuery(totalQuery);
      const totalMeters = totalResult.rows.item(0).count;
      
      // 活跃水表数
      const activeQuery = `SELECT COUNT(*) as count FROM water_meters WHERE status = 'active'`;
      const activeResult = await executeQuery(activeQuery);
      const activeMeters = activeResult.rows.item(0).count;
      
      const inactiveMeters = totalMeters - activeMeters;
      
      // 有读数的水表数
      const withReadingsQuery = `
        SELECT COUNT(DISTINCT meter_id) as count 
        FROM meter_readings
      `;
      const withReadingsResult = await executeQuery(withReadingsQuery);
      const metersWithReadings = withReadingsResult.rows.item(0).count;
      
      const metersWithoutReadings = totalMeters - metersWithReadings;
      
      // 平均每个水表的读数数量
      const avgReadingsQuery = `
        SELECT AVG(reading_count) as avg_readings
        FROM (
          SELECT meter_id, COUNT(*) as reading_count
          FROM meter_readings
          GROUP BY meter_id
        ) as meter_reading_counts
      `;
      const avgResult = await executeQuery(avgReadingsQuery);
      const averageReadingsPerMeter = avgResult.rows.item(0).avg_readings || 0;
      
      return {
        totalMeters,
        activeMeters,
        inactiveMeters,
        metersWithReadings,
        metersWithoutReadings,
        averageReadingsPerMeter,
      };
    } catch (error) {
      console.error('Error getting meter statistics:', error);
      throw error;
    }
  }
  
  // 获取用水量趋势数据（最近30天）
  static async getConsumptionTrend(): Promise<Array<{date: string, consumption: number}>> {
    try {
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
      const startDate = thirtyDaysAgo.toISOString().split('T')[0];
      
      const query = `
        SELECT 
          reading_date as date,
          SUM(consumption) as consumption
        FROM meter_readings 
        WHERE reading_date >= ? AND consumption > 0
        GROUP BY reading_date
        ORDER BY reading_date
      `;
      
      const result = await executeQuery(query, [startDate]);
      const trendData = [];
      
      for (let i = 0; i < result.rows.length; i++) {
        trendData.push(result.rows.item(i));
      }
      
      return trendData;
    } catch (error) {
      console.error('Error getting consumption trend:', error);
      throw error;
    }
  }
  
  // 获取抄表员工作统计
  static async getReaderStatistics(): Promise<Array<{reader_id: number, reading_count: number, avg_consumption: number}>> {
    try {
      const query = `
        SELECT 
          reader_user_id as reader_id,
          COUNT(*) as reading_count,
          AVG(consumption) as avg_consumption
        FROM meter_readings 
        WHERE reader_user_id IS NOT NULL AND consumption > 0
        GROUP BY reader_user_id
        ORDER BY reading_count DESC
      `;
      
      const result = await executeQuery(query);
      const readerStats = [];
      
      for (let i = 0; i < result.rows.length; i++) {
        readerStats.push(result.rows.item(i));
      }
      
      return readerStats;
    } catch (error) {
      console.error('Error getting reader statistics:', error);
      throw error;
    }
  }
}
```

### 5.3 数据备份恢复服务

```typescript
// src/services/BackupService.ts
import RNFS from 'react-native-fs';
import { executeQuery } from '../database/DatabaseInit';

export class BackupService {
  
  // 创建完整数据备份
  static async createBackup(): Promise<string> {
    try {
      const backupData = {
        version: '1.0.0',
        timestamp: new Date().toISOString(),
        tables: {
          water_meters: await this.exportTable('water_meters'),
          water_meter_customers: await this.exportTable('water_meter_customers'),
          meter_readings: await this.exportTable('meter_readings'),
          reading_routes: await this.exportTable('reading_routes'),
          route_meters: await this.exportTable('route_meters'),
          users: await this.exportTable('users'),
        }
      };
      
      const backupJson = JSON.stringify(backupData, null, 2);
      const fileName = `backup_${new Date().toISOString().replace(/[:.]/g, '-')}.json`;
      const filePath = `${RNFS.DocumentDirectoryPath}/${fileName}`;
      
      await RNFS.writeFile(filePath, backupJson, 'utf8');
      
      return filePath;
    } catch (error) {
      console.error('Error creating backup:', error);
      throw error;
    }
  }
  
  // 导出单个表的数据
  private static async exportTable(tableName: string): Promise<any[]> {
    try {
      const query = `SELECT * FROM ${tableName}`;
      const result = await executeQuery(query);
      const data = [];
      
      for (let i = 0; i < result.rows.length; i++) {
        data.push(result.rows.item(i));
      }
      
      return data;
    } catch (error) {
      console.error(`Error exporting table ${tableName}:`, error);
      return [];
    }
  }
  
  // 从备份文件恢复数据
  static async restoreFromBackup(backupFilePath: string): Promise<void> {
    try {
      const backupContent = await RNFS.readFile(backupFilePath, 'utf8');
      const backupData = JSON.parse(backupContent);
      
      if (!backupData.version || !backupData.tables) {
        throw new Error('Invalid backup file format');
      }
      
      // 清除现有数据（注意：这会删除所有数据）
      await this.clearAllTables();
      
      // 恢复各个表的数据
      for (const [tableName, tableData] of Object.entries(backupData.tables)) {
        if (Array.isArray(tableData) && tableData.length > 0) {
          await this.restoreTableData(tableName, tableData as any[]);
        }
      }
      
      console.log('Backup restored successfully');
    } catch (error) {
      console.error('Error restoring from backup:', error);
      throw error;
    }
  }
  
  // 清除所有表数据
  private static async clearAllTables(): Promise<void> {
    const tables = [
      'meter_readings',
      'route_meters',
      'water_meters',
      'water_meter_customers',
      'reading_routes',
      'users',
      'sync_logs'
    ];
    
    for (const table of tables) {
      try {
        await executeQuery(`DELETE FROM ${table}`);
        console.log(`Cleared table: ${table}`);
      } catch (error) {
        console.error(`Error clearing table ${table}:`, error);
      }
    }
  }
  
  // 恢复单个表的数据
  private static async restoreTableData(tableName: string, data: any[]): Promise<void> {
    try {
      for (const row of data) {
        const columns = Object.keys(row);
        const values = Object.values(row);
        const placeholders = columns.map(() => '?').join(', ');
        
        const insertQuery = `
          INSERT INTO ${tableName} (${columns.join(', ')}) 
          VALUES (${placeholders})
        `;
        
        await executeQuery(insertQuery, values);
      }
      
      console.log(`Restored ${data.length} rows to table: ${tableName}`);
    } catch (error) {
      console.error(`Error restoring table ${tableName}:`, error);
      throw error;
    }
  }
  
  // 获取所有备份文件
  static async getBackupFiles(): Promise<Array<{name: string, path: string, size: number, date: Date}>> {
    try {
      const files = await RNFS.readDir(RNFS.DocumentDirectoryPath);
      const backupFiles = files
        .filter(file => file.name.startsWith('backup_') && file.name.endsWith('.json'))
        .map(file => ({
          name: file.name,
          path: file.path,
          size: file.size,
          date: new Date(file.mtime)
        }))
        .sort((a, b) => b.date.getTime() - a.date.getTime());
      
      return backupFiles;
    } catch (error) {
      console.error('Error getting backup files:', error);
      return [];
    }
  }
  
  // 删除备份文件
  static async deleteBackup(filePath: string): Promise<void> {
    try {
      await RNFS.unlink(filePath);
      console.log(`Deleted backup file: ${filePath}`);
    } catch (error) {
      console.error('Error deleting backup file:', error);
      throw error;
    }
  }
  
  // 自动清理旧备份（保留最近10个）
  static async cleanupOldBackups(): Promise<void> {
    try {
      const backupFiles = await this.getBackupFiles();
      
      if (backupFiles.length > 10) {
        const filesToDelete = backupFiles.slice(10);
        
        for (const file of filesToDelete) {
          await this.deleteBackup(file.path);
        }
        
        console.log(`Cleaned up ${filesToDelete.length} old backup files`);
      }
    } catch (error) {
      console.error('Error cleaning up old backups:', error);
    }
  }
}
```

## 6. 部署和测试

### 6.1 Jest测试配置

```javascript
// jest.config.js
module.exports = {
  preset: 'react-native',
  moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx', 'json', 'node'],
  transformIgnorePatterns: [
    'node_modules/(?!(react-native|@react-native|react-native-vector-icons|native-base)/)',
  ],
  setupFilesAfterEnv: ['<rootDir>/src/tests/setup.ts'],
  moduleNameMapping: {
    '^@/(.*)d": "cd android && ./gradlew assembleRelease",
    "build:androi: '<rootDir>/src/$1',
  },
  collectCoverageFrom: [
    'src/**/*.{ts,tsx}',
    '!src/**/*.d.ts',
    '!src/tests/**',
  ],
  coverageDirectory: 'coverage',
  coverageReporters: ['text', 'lcov', 'html'],
};
```

### 6.2 测试文件示例

```typescript
// src/tests/setup.ts
import 'react-native-gesture-handler/jestSetup';

jest.mock('react-native-reanimated', () => {
  const Reanimated = require('react-native-reanimated/mock');
  Reanimated.default.call = () => {};
  return Reanimated;
});

jest.mock('react-native/Libraries/Animated/NativeAnimatedHelper');

// src/tests/services/ReadingService.test.ts
import { ReadingService } from '../../services/ReadingService';

describe('ReadingService', () => {
  describe('validateReading', () => {
    it('should validate normal reading', () => {
      const result = ReadingService.validateReading(150, 100);
      expect(result.isValid).toBe(true);
      expect(result.warnings).toHaveLength(0);
    });
    
    it('should warn about high consumption', () => {
      const result = ReadingService.validateReading(1200, 100);
      expect(result.isValid).toBe(true);
      expect(result.warnings).toContain('用水量异常偏高，请确认读数是否正确');
    });
    
    it('should reject negative reading difference', () => {
      const result = ReadingService.validateReading(80, 100);
      expect(result.isValid).toBe(false);
      expect(result.warnings).toContain('当前读数小于前一次读数，请确认是否正确');
    });
  });
});
```

### 6.3 打包脚本

```bash
#!/bin/bash
# scripts/build-android.sh

echo "Building Android APK..."

# 清理项目
npm run clean:android

# 生成签名APK
cd android
./gradlew assembleRelease

# 复制APK到输出目录
mkdir -p ../dist
cp app/build/outputs/apk/release/app-release.apk ../dist/WaterMeterApp-v1.0.0.apk

echo "Android build completed: dist/WaterMeterApp-v1.0.0.apk"
```

## 7. 使用说明

### 7.1 快速开始

1. **安装依赖**

```bash
npm install
```

1. **初始化项目**

```bash
# Android
npx react-native run-android

# iOS
npx react-native run-ios
```

1. **数据库初始化** 应用首次启动时会自动创建数据库和表结构。

### 7.2 核心功能使用

1. **水表管理**
   - 在水表列表页面查看所有水表
   - 使用搜索功能快速查找特定水表
   - 点击水表查看详细信息和读数历史
2. **读数录入**
   - 在水表详情页点击"+"按钮开始录入
   - 输入当前读数，系统自动计算用水量
   - 可选择拍摄水表照片作为凭证
   - 系统自动记录GPS位置信息
3. **数据同步**
   - 应用自动将读数同步到服务器
   - 在同步状态页面查看同步进度
4. **数据同步**
   - 应用自动将读数同步到服务器
   - 在同步状态页面查看同步进度
   - 支持离线工作，网络恢复后自动同步
5. **路线管理**
   - 创建和管理抄表路线
   - 按路线组织水表，提高抄表效率
   - 查看路线完成进度
6. **数据导出**
   - 支持导出读数数据为CSV格式
   - 可以按日期范围筛选导出数据
   - 通过分享功能发送导出文件

### 7.3 注意事项

1. **权限设置**
   - 首次使用需要授予相机、位置、存储等权限
   - 确保网络连接正常以进行数据同步
2. **数据安全**
   - 定期使用备份功能保存数据
   - 重要数据建议及时同步到服务器
3. **性能优化**
   - 定期清理临时文件和旧照片
   - 建议每月进行一次数据备份

## 8. API接口文档

### 8.1 读数上传接口

```http
POST /api/readings
Content-Type: multipart/form-data

参数:
- meter_id: 水表ID (必填)
- current_reading: 当前读数 (必填)
- reading_date: 读数日期 (必填)
- reading_time: 读数时间 (必填)
- notes: 备注 (可选)
- latitude: 纬度 (可选)
- longitude: 经度 (可选)
- photo: 照片文件 (可选)

响应:
{
  "success": true,
  "data": {
    "reading_id": 123,
    "server_id": "SVR-456"
  }
}
```

### 8.2 水表数据下载接口

```http
GET /api/water-meters
Authorization: Bearer {token}

查询参数:
- page: 页码 (可选，默认1)
- limit: 每页数量 (可选，默认100)
- updated_after: 更新时间过滤 (可选)

响应:
{
  "success": true,
  "data": [
    {
      "meter_id": 1,
      "meter_number": "WM001",
      "customer_id": 101,
      "customer_name": "张三",
      "customer_address": "北京市朝阳区...",
      "meter_type": "智能水表",
      "installation_date": "2023-01-15",
      "location_description": "地下室",
      "latitude": 39.9042,
      "longitude": 116.4074,
      "status": "active"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 100,
    "total": 150,
    "pages": 2
  }
}
```

### 8.3 路线数据接口

```http
GET /api/routes
Authorization: Bearer {token}

响应:
{
  "success": true,
  "data": [
    {
      "route_id": 1,
      "route_name": "市中心区域A",
      "description": "包含商业区和住宅区水表",
      "estimated_duration": 120,
      "status": "active",
      "meters": [
        {
          "meter_id": 1,
          "sequence_order": 1
        }
      ]
    }
  ]
}
```

## 9. 常见问题解决

### 9.1 编译问题

**问题**: Android编译失败，提示找不到包

```bash
解决方案:
1. 清理项目缓存
   cd android && ./gradlew clean
   cd .. && npm run clean

2. 重新安装依赖
   rm -rf node_modules
   npm install

3. 重新链接原生依赖
   npx react-native unlink
   npx react-native link
```

**问题**: iOS编译失败，CocoaPods相关错误

```bash
解决方案:
1. 更新CocoaPods
   sudo gem install cocoapods

2. 重新安装pods
   cd ios
   rm -rf Pods Podfile.lock
   pod install

3. 清理Xcode缓存
   xcodebuild clean
```

### 9.2 运行时问题

**问题**: 数据库初始化失败

```javascript
解决方案:
1. 检查权限设置
2. 确保存储空间充足
3. 查看错误日志确定具体原因
4. 必要时清除应用数据重新初始化
```

**问题**: 相机功能无法使用

```javascript
解决方案:
1. 检查相机权限是否已授予
2. 确保设备有可用的相机
3. 检查AndroidManifest.xml或Info.plist中的权限配置
4. 重启应用重新请求权限
```

**问题**: GPS定位不准确

```javascript
解决方案:
1. 确保位置权限已授予
2. 在室外开阔地带使用
3. 等待GPS信号稳定
4. 检查设备定位服务是否开启
```

### 9.3 数据同步问题

**问题**: 数据无法同步到服务器

```javascript
解决方案:
1. 检查网络连接状态
2. 验证API服务器地址配置
3. 检查认证token是否有效
4. 查看服务器响应错误信息
5. 尝试手动触发同步
```

**问题**: 离线数据丢失

```javascript
解决方案:
1. 定期创建数据备份
2. 检查本地存储权限
3. 避免在应用运行时清除数据
4. 使用数据恢复功能
```

## 10. 扩展开发指南

### 10.1 添加新功能模块

1. **创建新的数据模型**

```typescript
// src/database/NewModel.ts
export interface NewEntity {
  id?: number;
  name: string;
  // 其他字段
}

export class NewModel {
  static async create(entity: NewEntity): Promise<number> {
    // 实现创建逻辑
  }
  
  static async getAll(): Promise<NewEntity[]> {
    // 实现查询逻辑
  }
}
```

1. **添加对应的服务层**

```typescript
// src/services/NewService.ts
export class NewService {
  static async processNewEntity(data: any): Promise<void> {
    // 实现业务逻辑
  }
}
```

1. **创建UI组件**

```typescript
// src/screens/NewScreen.tsx
const NewScreen: React.FC = () => {
  // 实现界面逻辑
};
```

1. **更新导航配置**

```typescript
// src/navigation/AppNavigator.tsx
// 添加新屏幕到导航器中
```

### 10.2 自定义主题

```typescript
// src/theme/CustomTheme.ts
import { extendTheme } from 'native-base';

export const customTheme = extendTheme({
  colors: {
    primary: {
      // 自定义主色调
    },
    secondary: {
      // 自定义辅助色
    }
  },
  components: {
    Button: {
      // 自定义按钮样式
    }
  }
});
```

### 10.3 集成第三方服务

```typescript
// src/services/ThirdPartyService.ts
export class ThirdPartyService {
  static async integrateWithExternalAPI(): Promise<void> {
    // 集成外部API逻辑
  }
}
```

## 11. 维护和更新

### 11.1 版本管理

1. **更新版本号**

```json
// package.json
{
  "version": "1.1.0"
}
```

1. **更新原生应用版本**

```xml
<!-- android/app/build.gradle -->
android {
    defaultConfig {
        versionCode 2
        versionName "1.1.0"
    }
}
<!-- ios/WaterMeterApp/Info.plist -->
<key>CFBundleShortVersionString</key>
<string>1.1.0</string>
<key>CFBundleVersion</key>
<string>2</string>
```

### 11.2 数据库迁移

```typescript
// src/database/Migration.ts
export class DatabaseMigration {
  static async migrateToVersion2(): Promise<void> {
    // 执行数据库结构更新
    const alterTableSQL = `
      ALTER TABLE meter_readings 
      ADD COLUMN new_field TEXT
    `;
    await executeQuery(alterTableSQL);
  }
}
```

### 11.3 性能监控

```typescript
// src/utils/PerformanceMonitor.ts
export class PerformanceMonitor {
  static logExecutionTime(functionName: string, startTime: number): void {
    const endTime = Date.now();
    const duration = endTime - startTime;
    console.log(`${functionName} executed in ${duration}ms`);
  }
}
```

## 12. 总结

这个水表读数应用提供了完整的抄表管理解决方案，包括：

1. **核心功能**
   - 水表信息管理
   - 读数录入和验证
   - 照片凭证管理
   - GPS位置记录
   - 数据同步
2. **高级功能**
   - 路线管理
   - 数据导出
   - 统计报表
   - 备份恢复
   - 离线支持
3. **技术特性**
   - TypeScript类型安全
   - SQLite本地存储
   - React Native跨平台
   - 模块化架构
   - 完整的错误处理
4. **扩展性**
   - 清晰的代码结构
   - 服务层抽象
   - 可配置的API接口
   - 主题自定义
   - 第三方集成支持

该应用可以作为水务公司、物业管理等行业的抄表工具，也可以根据具体需求进行功能扩展和定制开发。

通过合理的架构设计和模块化开发，应用具有良好的可维护性和扩展性，能够满足不同规模和复杂度的抄表管理需求。