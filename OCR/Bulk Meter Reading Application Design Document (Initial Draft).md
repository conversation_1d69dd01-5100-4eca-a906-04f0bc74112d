## Bulk Meter Reading Application Design Document (Initial Draft)

### 1. Project Background

CORDE plans to develop a mobile application for field staff to support bulk meter reading tasks, including capturing photos, OCR recognition, GPS positioning, task management, and more.
 The system must integrate with the council’s Asset Management System (AMIS) and Financial Management System (Magic), and support data visualization and file export/upload.

------

### 2. System Components

1. **Backend Services**
   - Central SQL database
   - API integration with AMIS/Magic
   - File export modules (CSV, Excel, XML)
   - Notification service (auto-email after task assignment)
2. **Admin Portal (Web)**
   - Map and list views for meter assets
   - Bulk assignment of tasks to field staff
   - Route planning and saving
   - Data review and export
3. **Mobile Application (React Native)**
   - Offline task management capability
   - Map and task list views
   - Photo capture and upload
   - OCR-based meter reading
   - GPS location recording
   - Anomaly marking (e.g., damaged meter, no-read reasons)

------

### 3. Core Functional Modules

#### 1. Task Assignment and Route Management

- Filter assets by area, urgency, and route
- Save and reuse route groupings
- Auto-email notification after task assignment

#### 2. Mobile Data Collection

- User login to receive assignments
- Capture meter photos
- Trigger OCR module for automatic reading
- Manual input allowed if OCR confidence is low (marked as manual override)
- Select anomaly reasons from a picklist
- Validate current reading against historical data

#### 3. Data Synchronization

- All data stored locally (photos, GPS, timestamps, task status)
- Auto-sync to central database when online

#### 4. Data Export and Visualization

- Power BI dashboards for task progress and anomaly visualization
- Export final data in specified formats and upload to the council’s system

------

### 4. Technology Stack Recommendations

- **Mobile framework:** React Native (reusing previous structures where possible)
- **OCR engine:** Tesseract OCR or Google ML Kit
- **Mapping:** react-native-maps + leaflet/geoserver
- **Database:** SQL Server
- **Visualization:** Power BI
- **Offline support:** Realm/SQLite + network monitoring

------

### 5. Open Items (To Be Confirmed)

1. **Whether to extend the existing app or develop a new standalone application?**
   - If rebuilding, whether existing models and structures can be reused?
2. **Who is responsible for backend API development?**
   - The requirements involve substantial backend work, including:
     - Integration with AMIS/Magic APIs
     - Task assignment and email notification logic (SQL triggers)
     - Data aggregation and storage (photos, readings, timestamps, GPS)
     - File export and upload workflows
     - Power BI reporting data structure integration
   - These are outside mobile development scope and need confirmation if I should take on full backend development, or if another backend developer will be assigned.
3. **OCR Module Selection and Integration**
   - Whether to use open-source (Tesseract) or Google ML Kit?
   - Define OCR accuracy standards; enforce manual input below confidence thresholds?
4. **Data Security and Permission Controls**
   - Whether to implement unified authentication (e.g., JWT)?
   - Whether data needs user/role/organization-level separation?
5. **Final Export Format and API Specifications**
   - Is there a defined format for council integration?
   - Whether the upload is via API push, manual upload, or third-party integration?