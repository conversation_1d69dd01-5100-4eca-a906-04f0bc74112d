# ✅ Water Meter Management System - User Story Categorization & Detailed Hour Estimates

## 📊 Project Overview

Based on the authentic 13 user stories requirements, this project adopts **Work Package + Task dual-layer architecture** with an estimated total workload of **740 hours** over a 24-26 week development cycle.

### 🎯 Core Assessment Insights:

- **Development Model**: Solo developer responsible for Web Admin Portal + Mobile App + Backend APIs + Security mechanisms + Functional testing
- **Technical Complexity**: **High-precision OCR recognition, complete offline synchronization, Work Package management, real-time monitoring & alerts** carry significant implementation complexity
- **Hardware Optimization**: Specifically optimized for **CipherLab RS38-Android 13** devices to ensure production environment stability
- **Work Rhythm**: Based on **30 hours/week** workload, deliverable within 24-26 weeks, with total time controlled at **740 hours** providing sufficient safety margin

---

## 📋 Real User Story Classification & Hour Estimates

| Story # | Feature Description | Complexity | Estimated Hours | User Role |
| ------- | ------------------- | ---------- | --------------- | --------- |
| **Story 1** | AMIS Meter Master Data Sync | 🟨 Medium | 40 | Contract Administrator |
| **Story 2** | Baseline Reading Validation System | 🟥 High | 80 | Contract Administrator + Field User |
| **Story 3** | Reusable Work Package Schedule Management | 🟥 High | 80 | Administrator |
| **Story 4** | Bulk Work Package Assignment | 🟨 Medium | 40 | Administrator |
| **Story 5** | New Asset Allocation Management | 🟨 Medium | 40 | Administrator |
| **Story 6** | Reactive Reading Management | 🟩 Simple | 25 | Field User |
| **Story 7** | Mobile Task Viewing System | 🟩 Simple | 25 | Field User |
| **Story 8** | High-Precision Photo OCR Entry | 🟥 Very High | 120 | Field User |
| **Story 9** | Route Sequence Optimization | 🟨 Medium | 35 | Field User |
| **Story 10** | Complete Offline Sync Mechanism | 🟥 Very High | 100 | Field User |
| **Story 11** | Real-time KPI Monitoring & Alerts | 🟨 Medium | 60 | Power BI Analyst |
| **Story 12** | Validated Data Export System | 🟨 Medium | 50 | Contract Administrator |
| **Story 13** | CipherLab RS38 Hardware Testing | 🟩 Simple | 25 | Business Analyst |

---

### ✅ **Total: 740 hours**

> This estimate is based on real business requirements, including Work Package management, high-precision OCR, complete offline capabilities, real-time monitoring and other core functions. Hardware adaptation, error handling, performance optimization, system integration, documentation writing and other key content have been fully considered.

---

## 🚀 Phase 1: Data Infrastructure (120 hours)

### 📡 Story 1: AMIS Meter Master Data Sync (40 hours)

**Complexity: 🟨 Medium**  
**User Role:** Contract Administrator  
**Objective:** Establish dual-guaranteed AMIS data synchronization mechanism to ensure completeness and timeliness of meter master data

------

#### 🔧 Subtask Breakdown & Estimates:

| Subtask | Description | Hours |
|---------|-------------|--------|
| AMIS REST API Integration Client | Authentication mechanism, paginated data retrieval, data mapping | 8 |
| SFTP Backup Sync Mechanism | File download, format validation, error handling | 6 |
| Nightly Automated Sync Scheduler | Hangfire scheduled tasks, status management | 4 |
| Data Validation & Error Handling | GPS coordinate validation, field completeness check | 6 |
| New Asset Marking Mechanism | "Unallocated" status automatic marking | 4 |
| Email Alert Service | Failure notification within 5 minutes, template configuration | 4 |
| Manual Trigger Sync Function | Frontend management interface integration | 4 |
| Testing & Documentation | Integration testing, operation manual writing | 4 |

**Acceptance Criteria:**
- ✅ Sync success rate ≥99%
- ✅ Send failure alerts within 5 minutes
- ✅ New assets automatically marked "Unallocated"
- ✅ Support manual sync triggering

**⏱️Subtotal: 40 hours**

------

## ✅ User Story 2: Baseline Reading Validation (80 hours)

**Complexity: 🟥 High**
 **Objective**: 
 The system should import initial baseline readings from an Excel/CSV file provided by SDC, store them, and validate subsequent user inputs on the device. Once new readings are uploaded, they become the updated reference for future checks. This includes error handling, update logic, and syncing between device and server.

------

### 🔧 Task Breakdown & Estimates (English)

| Subtask                              | Description                                                 | Hours |
| ------------------------------------ | ----------------------------------------------------------- | ----- |
| Analyze CSV/XLSX structure           | Review SDC file structure (meter ID, read value, date)      | 4     |
| Build CSV upload functionality (Web) | Allow admin to upload CSV via UI (replacing SFTP-only flow) | 6     |
| CSV validation logic                 | Check Meter ID validity, format correctness, valid dates    | 6     |
| Import logic (to SQL)                | For each meter, insert if new, update if newer date         | 6     |
| Tag baseline source                  | Mark imported records as BaselineSource = "CSV"             | 2     |
| Device job bundle download API       | Devices fetch latest SQL baseline as DeviceBaseline         | 6     |
| Reading validation logic             | Compare user input against DeviceBaseline ± tolerance       | 8     |
| UI alert for anomalies               | Display "Anomaly" banner and tag record                     | 6     |
| Handle first-read case (no baseline) | Skip validation if no previous data                         | 2     |
| Update DeviceBaseline after sync     | Uploaded reading becomes new DeviceBaseline                 | 4     |
| Unit tests + integration tests       | Simulate multiple upload/update/validation scenarios        | 8     |
| Error handling & logging             | Record skipped/failed/updated entries                       | 4     |
| Write documentation & flow diagrams  | Describe import process and on-device validation logic      | 6     |

**⏱️Subtotal: 80 hours**

---

### ✅ User Story 3: Task Scheduling via CSV (with Map Preview) (80 hours)

**Complexity: 🟥 High (includes import/export, map integration, history tracking)**

**Objective:**
 Allow administrators to export a filtered list of meters (with GPS), fill in a ScheduleName column, and upload it back to quickly create a work schedule. The system should support map preview, historical edit tracking, re-upload of updates, and basic error handling.

------

### 🔧 Subtask Breakdown & Estimates (English)

| Subtask                               | Description                                   | Hours |
| ------------------------------------- | --------------------------------------------- | ----- |
| Implement CSV export (template)       | Include MeterID, Address, GPS based on filter | 6     |
| CSV upload & parsing                  | Allow upload with ScheduleName field          | 8     |
| MeterID validation                    | Ensure IDs exist, are unique, and well-formed | 6     |
| Create schedule record with metadata  | Save creator, timestamp, and unique ID        | 6     |
| Map preview of scheduled meters       | Plot meters via GPS on Mapbox/Leaflet         | 10    |
| Plan list view + item-level preview   | Allow user to view plan and map preview       | 4     |
| Re-upload CSV to update existing plan | Compare meter lists, update accordingly       | 8     |
| Maintain change history log           | Track number of edits, uploader, changes      | 6     |
| Soft delete and recovery (30-day)     | Implement recycle bin-style soft deletion     | 4     |
| Functional testing                    | Edge cases: bad fields, unknown IDs, deletion | 6     |
| Write usage guide & instructions      | Describe CSV format, map features, editing    | 6     |

**⏱️Subtotal: 80 hours**

------

### ✅ User Story 4: Bulk Meter Assignment (40 hours)

**Complexity: 🟨 Medium**

**Objective:**
 Allow administrators to select one or more meters or routes and assign them in bulk to a specific field user. During assignment, the admin can set priority, reading frequency (e.g. monthly, annually), and optional due dates. Each assignment is stored in the database and users are notified via email. The system should auto-calculate the “Next Due Date” based on frequency.

------

### 🔧 Subtask Breakdown & Estimates (English)

| Subtask                         | Description                                       | Hours |
| ------------------------------- | ------------------------------------------------- | ----- |
| Meter or route selection UI     | Allow multi-select of meters or route group       | 4     |
| Assignment form UI              | Fields: priority, frequency, due date, notes      | 4     |
| Frequency parser & enumeration  | Handle options: monthly, annually, one-off, etc.  | 4     |
| Assignment database write logic | Save assignments with frequency/user/due date     | 6     |
| NextDueDate calculation logic   | Based on frequency + assigned date                | 4     |
| Email notification template     | Includes meter list, priority, link to mobile app | 4     |
| Configure SQL Mail or notifier  | Send email and log notification                   | 4     |
| Success confirmation UI         | Display toast/modal upon successful assignment    | 2     |
| Test multi-assignment flows     | Try combinations of meters/frequency/users        | 4     |
| Write admin usage guide         | How to assign/cancel/edit bulk jobs               | 4     |

**⏱️Subtotal: 40 hours**

------

### ✅ User Story 5: One-Off Reactive Job Assignment (25 hours)

**Complexity: 🟩 Simple (subset of bulk assignment functionality)**

**Objective:**
 The system shall enable administrators to rapidly dispatch a one-time meter reading job in response to SDC-initiated urgent requests. Upon selecting a meter, the assignment form should auto-populate with high priority, lock the frequency to “One-off,” and default the due date to 24 hours from creation. Once completed, the job should not regenerate any subsequent readings.

------

### 🔧 Subtask Breakdown & Estimates (English)

| Subtask                          | Description                                           | Hours |
| -------------------------------- | ----------------------------------------------------- | ----- |
| Reactive job entry point (UI)    | Quick-assign button from admin meter view             | 3     |
| Simplified assignment form       | Auto-fill: Priority=High, Frequency=One-off, Due=+24h | 3     |
| Assignment persistence logic     | Save record with IsReactive = true                    | 4     |
| UI state & label handling        | Tag jobs as “Reactive Read”                           | 2     |
| Reuse notification logic         | Adapt bulk assignment email/push logic                | 3     |
| Post-completion status logic     | Set status as Completed-Reactive; no recurrence       | 3     |
| Nightly overdue job checker      | Highlight past-due reactive jobs in red               | 3     |
| Test full reactive job lifecycle | Dispatch, complete, expire workflows                  | 2     |
| Write user instructions          | Documentation for emergency job assignment            | 2     |

**⏱️Subtotal: 25 hours**

------

### ✅ User Story 6: View Assigned Jobs (Map & List) (25 hours)

**Complexity: 🟩 Simple (read-only display + filters)**

**Objective:**
 Field users should be able to view their assigned jobs within the mobile app using both a map view and a sortable list. The map displays pins at meter locations, with tap-to-view info cards. The list allows sorting by route, priority, or due date. Performance should be optimized to load up to 1000 jobs within 3 seconds, ensuring navigational efficiency.

------

### 🔧 Subtask Breakdown & Estimates (English)

| Subtask                      | Description                            | Hours |
| ---------------------------- | -------------------------------------- | ----- |
| Define job data structure    | Format fields like GPS, priority, etc. | 2     |
| Implement map view component | Use Mapbox or Leaflet for job plotting | 4     |
| Info card on pin tap         | Show address, last read, priority      | 3     |
| Implement list view          | Render job list with navigation links  | 3     |
| Add list sorting/filtering   | Sort by route, date, priority          | 3     |
| Toggle between views         | Map/List switch UI and state logic     | 2     |
| Optimize performance         | Ensure ≤3s load for 1000 jobs          | 3     |
| Handle empty/error states    | Show error/help text on failure        | 2     |
| UI compatibility testing     | Validate performance on Zebra TC22     | 2     |
| Write user help doc          | Instructions for using both views      | 1     |

**⏱️Subtotal: 25 hours**

------

### ✅ User Story 7: Photo Capture + OCR + Validation + Editing (120 hours)

**Complexity: 🟥 Very High (OCR, camera integration, error handling, tagging)**

**Objective:**
 Within the mobile app, field users should be able to take a photo of the meter. The app should perform OCR to extract both the meter serial number and reading. If the OCR confidence is high and matches the assignment, the fields are auto-filled and highlighted green. If the confidence is low or the MeterID doesn’t match, the user should be prompted to confirm or manually correct the data, and the record should be flagged accordingly. The photo must be compressed and saved offline, with all data eventually uploaded to the server.

------

### 🔧 Subtask Breakdown & Estimates (English)

| Subtask                                  | Description                                         | Hours |
| ---------------------------------------- | --------------------------------------------------- | ----- |
| Camera integration + permission handling | Call camera API, configure for Zebra TC22           | 8     |
| Photo compression + local save logic     | Compress image ≤ 2MB with path/format               | 6     |
| OCR integration                          | Send image to OCR engine and receive results        | 10    |
| Confidence threshold logic               | Auto-fill fields if confidence ≥ 80%                | 6     |
| Green highlight + editable UI            | Show green filled fields with user-editable inputs  | 6     |
| ID mismatch warning                      | Show alert if OCR ID doesn’t match assigned MeterID | 8     |
| Manual override + flagging               | Flag as override or serialMismatch if edited        | 6     |
| Upload full data package                 | Include reading, MeterID, photo URL, GPS, timestamp | 8     |
| Offline OCR cache support                | Save photos and OCR results for later sync          | 8     |
| Sync baseline after upload               | Use latest reading as new DeviceBaseline            | 4     |
| Retry + retake handling                  | Allow retake or error notification on failure       | 6     |
| Multi-condition testing                  | Light/glare/angle/smudges test scenarios            | 12    |
| API integration and validation           | Backend data mapping and error handling             | 10    |
| UI/UX optimization + hints               | Show usage tips, help text, example overlay         | 6     |
| User manual writing                      | Document the entire photo + OCR flow                | 6     |

**⏱️Subtotal: 120 hours**

------

### ✅ User Story 8: Offline Mode & Sync Queue (110 hours)

**Complexity: 🟥 Very High (caching, sync performance, reliability)**

**Objective:**
 Field users must be able to operate fully offline—view tasks, input readings, take photos, and store all data locally. The system should cache all offline actions, including OCR results, GPS, and media. Upon reconnecting, the user should trigger a “Sync” action that uploads all pending items to the server, with visible progress, error states, and retry mechanisms to ensure reliable data recovery and consistency.

------

### 🔧 Subtask Breakdown & Estimates (English)

| Subtask                               | Description                                       | Hours |
| ------------------------------------- | ------------------------------------------------- | ----- |
| Offline mode login validation         | Allow if user logged in within 30 days            | 4     |
| Offline task cache structure          | Local schema for task + photo + GPS + flags       | 8     |
| Implement local storage (e.g. SQLite) | Persist data including paths and timestamps       | 10    |
| Photo & reading entry offline         | Enable inputs without connectivity                | 6     |
| OCR result caching                    | Save recognized data and image offline            | 6     |
| Sync queue logic                      | Queue all unsynced tasks with status Pending      | 6     |
| Sync button & UI entry                | Show action when connectivity returns             | 4     |
| Batch upload process                  | Support ≤10s upload of 500 readings incl. media   | 10    |
| Upload progress display               | Show uploading, success, failure feedback         | 6     |
| Network monitoring & alerts           | Detect and notify about online/offline state      | 4     |
| Error handling + retry logic          | Allow retry of failed uploads manually            | 6     |
| Write to SQL + status update          | Mark as SyncStatus = Success upon upload          | 6     |
| File cleanup after sync               | Clear local storage of synced content             | 4     |
| Stress & edge-case testing            | Drop connection mid-upload, photo too large, etc. | 10    |
| Write user offline guide              | Document offline workflow and behaviors           | 6     |

**⏱️Subtotal: 110 hours**

------

### ✅ User Story 9: Real-Time Dashboard & Alerts (60 hours)

**Complexity: 🟨 Medium (ETL process, SQL views, Power BI integration)**

**Objective:**
 The system should provide real-time KPI tracking using SQL Server reporting views (e.g., reading completion rate, route exceptions, active user ping). These views should be updated every 5 minutes by a scheduled ETL procedure and queried 8 times daily by Power BI through its on-prem gateway. When thresholds are exceeded (e.g., high exception rate or inactive users), alert emails should be sent to the admin team.

------

### 🔧 Subtask Breakdown & Estimates (English)

| Subtask                                           | Description                                              | Hours |
| ------------------------------------------------- | -------------------------------------------------------- | ----- |
| Design SQL views                                  | vw_ReadingCompletion, vw_RouteExceptions, vw_ActiveUsers | 6     |
| Implement ETL procedure (sp_UpdateReportingViews) | Run every 5 minutes to update data                       | 8     |
| Build metrics storage tables                      | Capture completion, exceptions, user activity            | 6     |
| Configure Power BI refresh schedule               | Use on-prem gateway, 8 daily refreshes                   | 4     |
| Design Power BI dashboard                         | Tiles for completion rate, exception %, avg read time    | 6     |
| Create KPI alert thresholds                       | Define limits to trigger email notifications             | 4     |
| Configure alert email service                     | Notify admins when thresholds breached                   | 4     |
| Style dashboard tiles                             | Apply color changes based on status (green/yellow/red)   | 4     |
| Test with simulated exceptions                    | Trigger alerts via fake data (missing GPS, timeout)      | 6     |
| Write documentation                               | Setup instructions for ETL, Power BI gateway, alerts     | 6     |

**⏱️Subtotal: 60 hours**

------

### ✅ User Story 10: Export Validated Reads to SDC (50 hours)

**Complexity: 🟨 Medium (API integration, validation rules, SFTP delivery)**

**Objective:**
 Contract administrators should be able to open the “Validated Reads” Power BI report, select a reading cycle, and click "Export." The backend should call the Power BI Export API to generate files in SDC-approved formats: CSV, XLSX, and XML. The XLSX file must include a validation checklist sheet that flags any missing fields or format issues. Once all rows show “✔ OK,” the admin can manually upload the files to the /incoming/reads folder via SFTP.

------

### 🔧 Subtask Breakdown & Estimates (English)

| Subtask                               | Description                                 | Hours |
| ------------------------------------- | ------------------------------------------- | ----- |
| Analyze SDC file format spec          | Review structure for CSV, XLSX, XML         | 6     |
| Configure Power BI report for export  | Define export fields and cycle slicers      | 6     |
| Integrate Power BI Export API         | Backend call to generate export files       | 6     |
| File storage & download logic         | Store/export files and generate links       | 4     |
| Generate validation sheet (XLSX only) | Compare field names, required fields, types | 6     |
| Design validation result output       | Highlight failures, mark “✔ OK” rows        | 4     |
| Export result status display          | Show status + pass/fail summary             | 4     |
| Document SFTP upload procedure        | Instructions for location, format, steps    | 4     |
| End-to-end testing                    | Export + fail cases + upload simulation     | 6     |
| Write usage documentation             | Step-by-step guide for export & issues      | 4     |

**⏱️Subtotal: 50 hours**

------

### ✅ User Story 11: Zebra TC22 Device Compatibility Testing (20 hours)

**Complexity: 🟩 Simple**

**Objective:**
 After each new APK build, compatibility testing must be conducted on the Zebra TC22 device (Android 11) to verify that the app installs, launches, and functions correctly—including camera access, offline mode, and UI performance. This ensures reliable field use and prevents performance issues or crashes on production devices.

------

### 🔧 Subtask Breakdown & Estimates (English)

| Subtask                      | Description                                         | Hours |
| ---------------------------- | --------------------------------------------------- | ----- |
| APK install & startup test   | Verify app installs and launches correctly          | 2     |
| Login page UI check          | Confirm all fields and interactions render properly | 2     |
| Camera test                  | Open, capture, compress, and store photo            | 2     |
| Map performance              | Load 1000 points on map within ≤3s                  | 2     |
| Offline mode access          | Simulate no signal, access cached tasks             | 2     |
| OCR recognition & confidence | Test local OCR accuracy and speed                   | 2     |
| Multitask switching test     | App resume, memory retention check                  | 2     |
| Error/crash logging          | Monitor logcat for system-level issues              | 2     |
| Developer collaboration      | Debug issues with dev team if needed                | 2     |
| Write final test report      | Document testing steps and results                  | 2     |

**⏱️Subtotal: 20 hours**

------

## ✅ Milestone Plan (Project Delivery Phases)

This milestone plan outlines the major delivery phases and their estimated timing based on a 670-hour workload, 30 hours per week, over approximately 22–24 weeks. It allows the client to track progress and validate key deliverables throughout the project lifecycle.

| Milestone                                         | Deliverables                                                 | Target Week | Acceptance Criteria                                          |
| ------------------------------------------------- | ------------------------------------------------------------ | ----------- | ------------------------------------------------------------ |
| **M1: Platform Setup Completed**                  | Admin login, AMIS data sync, basic job assignment UI         | Week 4      | Admin can log in, view synced meter data, and assign jobs    |
| **M2: Core Mobile Features Online**               | Photo capture, OCR integration, anomaly flagging, basic upload | Week 8      | Users can take a photo, auto-fill fields via OCR, modify and upload |
| **M3: Offline Mode Functional**                   | Offline task viewing, photo capture, reading entry, local storage | Week 14     | Users can complete jobs offline and sync successfully when reconnected |
| **M4: Task Scheduling + Map Preview + Reporting** | CSV import for scheduling, map visualization, Power BI dashboards | Week 18     | Admin can import tasks, preview routes on map, and view KPI dashboards |
| **M5: Final Delivery & Deployment**               | Full feature set complete, bug fixes, device QA, documentation | Week 22     | All mobile and admin features stable and tested on TC22, ready for handover |

------

### 💡 Notes:

- Each milestone spans approximately 3–4 weeks, allowing room for testing, bug fixing, and client feedback;
- APK demos or short video walkthroughs can be provided for milestone verification;
- Additional checkpoints (e.g., “M2.5 Mid-Project Demo”) can be added as needed without delaying the overall schedule;
- If minor scope changes arise, the timeline may shift by ±2 weeks, with buffer built into the total estimate.