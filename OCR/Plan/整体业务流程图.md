# 📊 Water Meter Management System - 整体业务流程图

## 🏗️ Work Package + Task 两层架构概览

```
[AMIS数据同步] → [Work Package管理] → [Task执行] → [数据监控] → [验证导出]
     ↓                  ↓                ↓           ↓           ↓
   主数据准备        批量工作包创建     现场任务执行   实时KPI监控   SDC数据交付
   基线验证          可重用计划管理      移动端操作    告警机制     多格式验证
```

## 🎯 基于13个用户故事的完整业务流程

### Phase 1: 数据基础设施 (Story 1-2)

#### 📡 Story 1: AMIS水表主数据同步
**Contract Administrator视角**

```
每日夜间自动运行 → AMIS REST API调用 → 数据验证 → 写入SQL → 新水表标记"Unallocated"
                        ↓ (失败时)
                  SFTP文件下载 → CSV解析 → 邮件告警(5分钟内)
```

**关键特点**：
- 双重保障机制：REST API主要，SFTP备选
- 数据完整性：ID、Asset ID、Address、GPS、Attributes
- 自动标记：新导入资产标记为"待分配到工作包"
- 错误处理：验证失败中止同步，立即发送告警

#### 📊 Story 2: 基线读数验证系统
**Contract Administrator + Field User协作**

```
SDC CSV/XLSX → SFTP投放区 → 基线导入作业 → 标记BaselineSource="CSV"
                                    ↓
设备下载作业包 → DeviceBaseline缓存 → 现场读数验证 → 异常范围显示"Anomaly"横幅
                                    ↓
成功上传读数 → 更新DeviceBaseline → 成为下次验证基准
```

**智能更新逻辑**：
- CSV日期更新：如果CSV读数日期比现有基线更新，则更新；否则忽略
- 首次读数：设备无历史基线时跳过验证
- 自动更新：每次成功上传的读数成为新的DeviceBaseline

---

### Phase 2: Work Package管理核心 (Story 3-6)

#### 🗺️ Story 3: 可重用Work Package计划管理
**Administrator视角**

```
1. 导出操作： 计划模块 → 导出CSV模板(含GPS) → 填写ScheduleName → 地图预览
                                                    ↓
2. 创建计划： CSV上传 → MeterID验证 → Work Package创建 → 地图确认 → 保存可重用计划
                                                    ↓
3. 修订管理： 计划列表 → 选择编辑 → 重新上传CSV → 变更对比 → 更新记录(30天回收站)
```

**地图预览机制**：
- 实时显示：所有水表GPS位置在地图上标记
- 地理验证：确认工作包内水表地理分布合理
- 错误提示：未知MeterID或重复ID立即显示警告

#### 👥 Story 4: 批量Work Package分配
**Administrator执行的核心功能**

```
1. 选择范围： 多选水表 → Work Package选择 → 用户选择器 → 分配参数设置
                                                    ↓
2. 参数配置： 优先级(低/中/高) → 频率(月/季/年/一次性) → 截止时间 → 预估工时
                                                    ↓
3. 自动计算： Next Due Date = 今天 + 频率周期 → Work Package Assignment创建
                                                    ↓
4. 通知发送： 2分钟内邮件通知 → Work Package详情 → 移动端链接 → 任务数量汇总
```

**支持的频率类型**：
- 定期： 每两周、每月、每季度、每年
- 一次性： 临时任务、应急响应
- 模板： 可重用Work Package模板

#### 🆕 Story 5: 新资产分配管理
**Administrator管理未分配资产**

```
1. 资产监控： AMIS同步 → 新资产标记"Unallocated" → 未分配资产视图(列表+地图)
                                                    ↓
2. 快速分配： 选择未分配资产 → 分配到现有Work Package → 或创建新Work Package
                                                    ↓
3. 实时更新： 分配后地图颜色变更 → 审计日志记录 → 阻止重复分配验证
```

**颜色标识系统**：
- 红色：未分配资产
- 蓝色：已分配到Work Package
- 绿色：任务进行中
- 灰色：任务已完成

#### ⚡ Story 6: 响应式读表管理
**Field User处理AMS一次性任务**

```
1. AMS导入： Excel文件上传 → 字段验证 → 重复任务检查 → 响应式Work Package创建
                                                    ↓
2. 快速执行： 高优先级标记 → 24小时截止期 → 现场任务执行 → 完成状态更新
                                                    ↓
3. 自动通知： 完成后2分钟内发邮件 → 不生成后续任务 → 排除重复Reactive Task ID
```

---

### Phase 3: 移动端Task执行 (Story 7-10)

#### 📱 Story 7: 移动任务查看系统
**Field User日常操作**

```
1. 双视图展示： 登录APP → 地图视图(1000+标记点) → 列表视图(Work Package分组)
                                                    ↓
2. 智能筛选： Work Package筛选 → 优先级排序 → 日期排序 → 读表顺序显示
                                                    ↓
3. 任务导航： 点击地图标记 → 详情卡片 → 导航到现场 → 任务执行入口
```

**性能要求**：
- 加载速度：1000条任务≤3秒
- 地图流畅性：标记点击响应≤1秒
- 分组显示：按Work Package显示读表顺序

#### 📸 Story 8: 高精度拍照OCR系统
**Field User核心功能**

```
1. 智能拍照： 到达现场 → 打开摄像头 → 拍摄水表 → 自动压缩≤2MB
                                                    ↓
2. OCR识别： Google ML Kit处理 → 置信度≥90%自动填充 → 绿色高亮显示
                                                    ↓
3. 智能验证： 编号匹配检查 → 不匹配显示警告横幅 → 手动修改自动标记Override
                                                    ↓
4. 异常标记： SerialMismatch标记 → Override标记 → GPS位置记录 → 离线缓存支持
```

**异常标记机制**：
- **Override**: 手动修改OCR识别结果
- **SerialMismatch**: 识别的表号与分配不符
- **Anomaly**: 读数超出历史基线范围

#### 🔄 Story 9: 路线顺序优化
**Field User实际操作优化**

```
1. 顺序记录： 实际读表顺序记录 → 时间戳+序列号跟踪 → 与计划路线对比
                                                    ↓
2. 智能对比： 计划vs实际对比显示 → 迷你地图可视化 → 偏差检测算法
                                                    ↓
3. 路线更新： 确认横幅："保持现有/采用实际" → 版本号生成 → 全局同步更新
                                                    ↓
4. 审计追踪： 路线变更记录 → 地理标记记录 → 变更审计日志
```

#### 🔌 Story 10: 完整离线同步机制
**Field User离线工作能力**

```
1. 离线条件： 30天内登录验证 → 离线模式激活 → 任务数据本地化(SQLite)
                                                    ↓
2. 离线操作： 任务查看 → 拍照OCR → GPS记录 → 本地缓存(SyncStatus=Pending)
                                                    ↓
3. 批量同步： 网络恢复检测 → 手动/自动同步触发 → 500条数据≤10秒传输
                                                    ↓
4. 可靠机制： 上传进度显示 → 失败重试支持 → 数据冲突解决 → 100%一致性保证
```

**同步队列管理**：
- 优先级队列：紧急任务优先上传
- 断点续传：支持网络中断恢复
- 数据完整性：时间戳+校验和验证

---

### Phase 4: 数据监控与导出 (Story 11-12)

#### 📊 Story 11: 实时KPI监控告警
**Power BI Analyst + Administrator**

```
1. ETL自动化： sp_UpdateReportingViews(5分钟) → 报表视图更新 → Power BI数据刷新(每日8次)
                                                    ↓
2. KPI监控： vw_ReadingCompletion(完成率) → vw_RouteExceptions(异常率) → vw_ActiveUsers(GPS追踪)
                                                    ↓
3. 智能告警： KPI阈值检查 → 异常自动检测 → 告警邮件发送 → 管理员组通知
                                                    ↓
4. 可视化： 仪表盘磁贴显示 → 颜色状态指示 → 趋势图表 → 实时GPS监控
```

**监控指标**：
- 完成率：已完成任务/总任务数
- 异常率：异常读数/总读数
- 平均时间：每个任务平均完成时间
- 活跃用户：当前在线工作人员GPS位置

#### 📤 Story 12: 验证数据导出系统
**Contract Administrator对接SDC**

```
1. 导出触发： Power BI报表 → 选择周期 → 导出按钮 → Power BI Export API调用
                                                    ↓
2. 多格式生成： CSV格式转换 → XLSX格式生成 → XML格式支持 → 自动校验页生成
                                                    ↓
3. 格式验证： 字段完整性检查 → 空值验证 → 格式规范检查 → "✔ OK"状态显示
                                                    ↓
4. SDC交付： 校验通过确认 → SFTP手动上传 → /incoming/reads目录 → 交付完成确认
```

**验证机制**：
- Excel包含校验工作表
- 所有行必须显示"✔ OK"才能上传
- 完整的导出历史追踪记录

---

### Phase 5: 质量保障 (Story 13)

#### 🧪 Story 13: CipherLab RS38-Android 13硬件测试
**Business Analyst质量保证**

```
1. 安装测试： APK自动安装 → 启动无崩溃验证 → 登录界面正常显示
                                                    ↓
2. 功能测试： 1000条任务≤3秒加载 → 拍照3秒内打开 → OCR识别准确性验证
                                                    ↓
3. 离线测试： 飞行模式功能验证 → 离线任务≤3秒显示 → 离线数据完整性检查
                                                    ↓
4. 性能测试： 长时间运行稳定性 → 内存泄漏检查 → 电池耗电量测试 → 压力测试
```

---

## 🔄 系统数据流向

### 主数据流
```
AMIS → SQL数据库 → Work Package → Task分配 → 移动设备缓存 → 现场采集 → 上传验证 → Power BI → SDC导出
```

### 基线验证流
```
SDC CSV → 基线导入 → DeviceBaseline → 现场验证 → 异常检测 → 基线更新 → 下次验证依据
```

### 离线同步流
```
在线Work Package → 设备缓存 → 离线操作 → 本地SQLite → 网络恢复 → 批量同步 → 状态更新
```

---

## 💡 Work Package vs Task 关键区别

### 🎯 Work Package (管理层面)
- **管理者**：Administrator、Contract Administrator  
- **用途**：批量管理、重复使用、模板化
- **包含内容**：一组相关水表、读表顺序、执行频率、服务区域
- **生命周期**：可重用、可修订、支持模板

### ⚡ Task (执行层面)  
- **执行者**：Field User (现场抄表员)
- **用途**：具体执行的工作项
- **包含内容**：单个水表、GPS位置、上次读数、执行状态
- **生命周期**：一次性执行、状态追踪、结果上传

---

## 🚀 关键成功因素

### 📈 业务指标
- **现场效率提升**: ≥50% (通过Work Package批量管理)
- **数据准确率**: ≥99% (通过OCR + 基线验证)
- **系统可用性**: ≥99.5% (通过离线模式支持)

### ⚡ 技术指标
- **AMIS同步成功率**: ≥99%
- **OCR识别准确率**: ≥90% (CipherLab RS38优化)
- **移动端响应时间**: 1000条任务≤3秒
- **离线同步性能**: 500条数据4G网络≤10秒

### 🎯 用户体验
- **管理端**：Work Package批量操作，CSV工作流，地图预览
- **移动端**：双视图切换，智能OCR，完整离线能力
- **监控端**：实时KPI，智能告警，多格式导出

---

*本业务流程图基于真实的13个用户故事制定，采用Work Package + Task两层架构，确保从数据同步到现场执行的完整业务闭环。*