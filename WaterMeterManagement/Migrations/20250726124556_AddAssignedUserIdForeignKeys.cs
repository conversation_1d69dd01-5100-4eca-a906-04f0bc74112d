﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace WaterMeterManagement.Migrations
{
    /// <inheritdoc />
    public partial class AddAssignedUserIdForeignKeys : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateIndex(
                name: "IX_WorkPackageItems_AssignedUserId",
                table: "WorkPackageItems",
                column: "AssignedUserId");

            migrationBuilder.CreateIndex(
                name: "IX_Routes_AssignedUserId",
                table: "Routes",
                column: "AssignedUserId");

            migrationBuilder.CreateIndex(
                name: "IX_ReadingAnomalies_AssignedUserId",
                table: "ReadingAnomalies",
                column: "AssignedUserId");

            migrationBuilder.AddForeignKey(
                name: "FK_ReadingAnomalies_Users_AssignedUserId",
                table: "ReadingAnomalies",
                column: "AssignedUserId",
                principalTable: "Users",
                principalColumn: "Id",
                onDelete: ReferentialAction.SetNull);

            migrationBuilder.AddForeignKey(
                name: "FK_Routes_Users_AssignedUserId",
                table: "Routes",
                column: "AssignedUserId",
                principalTable: "Users",
                principalColumn: "Id",
                onDelete: ReferentialAction.SetNull);

            migrationBuilder.AddForeignKey(
                name: "FK_WorkPackageItems_Users_AssignedUserId",
                table: "WorkPackageItems",
                column: "AssignedUserId",
                principalTable: "Users",
                principalColumn: "Id",
                onDelete: ReferentialAction.SetNull);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_ReadingAnomalies_Users_AssignedUserId",
                table: "ReadingAnomalies");

            migrationBuilder.DropForeignKey(
                name: "FK_Routes_Users_AssignedUserId",
                table: "Routes");

            migrationBuilder.DropForeignKey(
                name: "FK_WorkPackageItems_Users_AssignedUserId",
                table: "WorkPackageItems");

            migrationBuilder.DropIndex(
                name: "IX_WorkPackageItems_AssignedUserId",
                table: "WorkPackageItems");

            migrationBuilder.DropIndex(
                name: "IX_Routes_AssignedUserId",
                table: "Routes");

            migrationBuilder.DropIndex(
                name: "IX_ReadingAnomalies_AssignedUserId",
                table: "ReadingAnomalies");
        }
    }
}
