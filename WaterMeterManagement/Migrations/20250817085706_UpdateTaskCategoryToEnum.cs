﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace WaterMeterManagement.Migrations
{
    /// <inheritdoc />
    public partial class UpdateTaskCategoryToEnum : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // Step 1: Drop the existing TaskCategory column
            migrationBuilder.DropColumn(
                name: "TaskCategory",
                table: "Tasks");

            // Step 2: Add TaskCategory as integer (enum)
            migrationBuilder.AddColumn<int>(
                name: "TaskCategory",
                table: "Tasks",
                type: "integer",
                nullable: false,
                defaultValue: 0); // 0 = Planned (default)
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            // Step 1: Drop the integer TaskCategory column
            migrationBuilder.DropColumn(
                name: "TaskCategory",
                table: "Tasks");

            // Step 2: Add TaskCategory back as string
            migrationBuilder.AddColumn<string>(
                name: "TaskCategory",
                table: "Tasks",
                type: "character varying(20)",
                maxLength: 20,
                nullable: false,
                defaultValue: "Planned");
        }
    }
}
