﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace WaterMeterManagement.Migrations
{
    /// <inheritdoc />
    public partial class RemoveReadingModel : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "Readings");

            migrationBuilder.AddColumn<int>(
                name: "WorkPackageItemId",
                table: "MeterReadings",
                type: "integer",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "WorkTaskId",
                table: "MeterReadings",
                type: "integer",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_MeterReadings_WorkPackageItemId",
                table: "MeterReadings",
                column: "WorkPackageItemId");

            migrationBuilder.CreateIndex(
                name: "IX_MeterReadings_WorkTaskId",
                table: "MeterReadings",
                column: "WorkTaskId");

            migrationBuilder.AddForeignKey(
                name: "FK_MeterReadings_Tasks_WorkTaskId",
                table: "MeterReadings",
                column: "WorkTaskId",
                principalTable: "Tasks",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_MeterReadings_WorkPackageItems_WorkPackageItemId",
                table: "MeterReadings",
                column: "WorkPackageItemId",
                principalTable: "WorkPackageItems",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_MeterReadings_Tasks_WorkTaskId",
                table: "MeterReadings");

            migrationBuilder.DropForeignKey(
                name: "FK_MeterReadings_WorkPackageItems_WorkPackageItemId",
                table: "MeterReadings");

            migrationBuilder.DropIndex(
                name: "IX_MeterReadings_WorkPackageItemId",
                table: "MeterReadings");

            migrationBuilder.DropIndex(
                name: "IX_MeterReadings_WorkTaskId",
                table: "MeterReadings");

            migrationBuilder.DropColumn(
                name: "WorkPackageItemId",
                table: "MeterReadings");

            migrationBuilder.DropColumn(
                name: "WorkTaskId",
                table: "MeterReadings");

            migrationBuilder.CreateTable(
                name: "Readings",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    MeterId = table.Column<int>(type: "integer", nullable: false),
                    UserId = table.Column<int>(type: "integer", nullable: false),
                    AdditionalPhotos = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    AmsSyncDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    AmsSyncError = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    AmsSyncStatus = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    AnomalyReason = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    AssignmentId = table.Column<int>(type: "integer", nullable: true),
                    BaselineDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    BaselineSource = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    CantRead = table.Column<bool>(type: "boolean", nullable: false),
                    CantReadReason = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    Consumption = table.Column<decimal>(type: "numeric(12,2)", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    CreatedBy = table.Column<string>(type: "text", nullable: false),
                    DetectedMeterId = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    DeviceBaseline = table.Column<decimal>(type: "numeric(18,2)", precision: 18, scale: 2, nullable: true),
                    GpsAccuracy = table.Column<decimal>(type: "numeric(8,2)", nullable: true),
                    IsAnomalous = table.Column<bool>(type: "boolean", nullable: false),
                    IsAnomaly = table.Column<bool>(type: "boolean", nullable: false),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    IsOcrGenerated = table.Column<bool>(type: "boolean", nullable: false),
                    IsOverride = table.Column<bool>(type: "boolean", nullable: false),
                    IsSerialMismatch = table.Column<bool>(type: "boolean", nullable: false),
                    IsSyncedToAms = table.Column<bool>(type: "boolean", nullable: false),
                    IsValidated = table.Column<bool>(type: "boolean", nullable: false),
                    Latitude = table.Column<decimal>(type: "numeric(10,6)", precision: 18, scale: 6, nullable: true),
                    Longitude = table.Column<decimal>(type: "numeric(10,6)", precision: 18, scale: 6, nullable: true),
                    Notes = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    OcrConfidence = table.Column<int>(type: "integer", nullable: true),
                    PhotoFilename = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    PhotoUrl = table.Column<string>(type: "text", nullable: false),
                    PreviousReading = table.Column<decimal>(type: "numeric(12,2)", nullable: true),
                    ReadDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ReadValue = table.Column<decimal>(type: "numeric(18,2)", precision: 18, scale: 2, nullable: false),
                    ReadingDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ReadingMethod = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    ReadingType = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    ReadingValue = table.Column<decimal>(type: "numeric(12,2)", nullable: false),
                    Source = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    Status = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    SyncDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    SyncStatus = table.Column<string>(type: "text", nullable: false),
                    TaskAssignmentId = table.Column<int>(type: "integer", nullable: true),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    UpdatedBy = table.Column<string>(type: "text", nullable: false),
                    ValidatedBy = table.Column<int>(type: "integer", nullable: true),
                    ValidationComments = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    ValidationDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    ValidationNotes = table.Column<string>(type: "text", nullable: false),
                    WorkPackageItemId = table.Column<int>(type: "integer", nullable: true),
                    WorkTaskId = table.Column<int>(type: "integer", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Readings", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Readings_Tasks_WorkTaskId",
                        column: x => x.WorkTaskId,
                        principalTable: "Tasks",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_Readings_Users_UserId",
                        column: x => x.UserId,
                        principalTable: "Users",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_Readings_WaterMeters_MeterId",
                        column: x => x.MeterId,
                        principalTable: "WaterMeters",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_Readings_WorkPackageItems_WorkPackageItemId",
                        column: x => x.WorkPackageItemId,
                        principalTable: "WorkPackageItems",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateIndex(
                name: "IX_Readings_MeterId",
                table: "Readings",
                column: "MeterId");

            migrationBuilder.CreateIndex(
                name: "IX_Readings_UserId",
                table: "Readings",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_Readings_WorkPackageItemId",
                table: "Readings",
                column: "WorkPackageItemId");

            migrationBuilder.CreateIndex(
                name: "IX_Readings_WorkTaskId",
                table: "Readings",
                column: "WorkTaskId");
        }
    }
}
