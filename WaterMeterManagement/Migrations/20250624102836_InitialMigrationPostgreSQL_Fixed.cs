﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace WaterMeterManagement.Migrations
{
    /// <inheritdoc />
    public partial class InitialMigrationPostgreSQL_Fixed : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "AmisSyncs",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    SyncType = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    Status = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    StartTime = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    EndTime = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    TriggerBy = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    TotalRecords = table.Column<int>(type: "integer", nullable: false),
                    ProcessedRecords = table.Column<int>(type: "integer", nullable: false),
                    SuccessfulRecords = table.Column<int>(type: "integer", nullable: false),
                    FailedRecords = table.Column<int>(type: "integer", nullable: false),
                    SkippedRecords = table.Column<int>(type: "integer", nullable: false),
                    ProgressPercentage = table.Column<decimal>(type: "numeric(5,2)", nullable: false),
                    CurrentOperation = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    SyncConfiguration = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    ResultSummary = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    ErrorMessage = table.Column<string>(type: "character varying(2000)", maxLength: 2000, nullable: true),
                    DetailedLog = table.Column<string>(type: "text", nullable: true),
                    RecordsPerSecond = table.Column<int>(type: "integer", nullable: true),
                    DataSizeMB = table.Column<decimal>(type: "numeric(10,2)", nullable: true),
                    RetryCount = table.Column<int>(type: "integer", nullable: false),
                    MaxRetries = table.Column<int>(type: "integer", nullable: false),
                    NextRetryTime = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    AmisEndpoint = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    AmisVersion = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    LastSyncDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    SyncToken = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    CreatedBy = table.Column<string>(type: "text", nullable: false),
                    UpdatedBy = table.Column<string>(type: "text", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AmisSyncs", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "ConfigurationTemplates",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    Scope = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    Category = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    CategoryDisplayName = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    CategoryIcon = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    Key = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    DisplayName = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    Description = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: false),
                    DataType = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    DefaultValue = table.Column<string>(type: "text", nullable: false),
                    ValidationRules = table.Column<string>(type: "text", nullable: false),
                    UIComponent = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    UIProperties = table.Column<string>(type: "text", nullable: false),
                    DisplayOrder = table.Column<int>(type: "integer", nullable: false),
                    RequiresRestart = table.Column<bool>(type: "boolean", nullable: false),
                    IsSensitive = table.Column<bool>(type: "boolean", nullable: false),
                    IsReadOnly = table.Column<bool>(type: "boolean", nullable: false),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false),
                    Group = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    GroupDisplayName = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    CreatedBy = table.Column<string>(type: "text", nullable: false),
                    UpdatedBy = table.Column<string>(type: "text", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ConfigurationTemplates", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "FrequencyTemplates",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    Name = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    Description = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    FrequencyType = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    IntervalValue = table.Column<int>(type: "integer", nullable: false),
                    IntervalUnit = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: true),
                    DayOfWeek = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: true),
                    DayOfMonth = table.Column<int>(type: "integer", nullable: true),
                    MonthOfYear = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: true),
                    TimeOfDay = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    Status = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    Category = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    IsDefault = table.Column<bool>(type: "boolean", nullable: false),
                    EstimatedDuration = table.Column<int>(type: "integer", precision: 8, scale: 2, nullable: true),
                    Notes = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    UsageCount = table.Column<int>(type: "integer", nullable: false),
                    LastUsed = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    AdvancedConfiguration = table.Column<string>(type: "character varying(2000)", maxLength: 2000, nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    CreatedBy = table.Column<string>(type: "text", nullable: false),
                    UpdatedBy = table.Column<string>(type: "text", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_FrequencyTemplates", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "Permissions",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    Name = table.Column<string>(type: "text", nullable: false),
                    Code = table.Column<string>(type: "text", nullable: false),
                    Description = table.Column<string>(type: "text", nullable: false),
                    Module = table.Column<string>(type: "text", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    CreatedBy = table.Column<string>(type: "text", nullable: false),
                    UpdatedBy = table.Column<string>(type: "text", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Permissions", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "ReadingValidationRules",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    RuleName = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    RuleType = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    Description = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    MeterType = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    Zone = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    CustomerType = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false),
                    Priority = table.Column<int>(type: "integer", nullable: false),
                    MinToleranceValue = table.Column<decimal>(type: "numeric(10,2)", precision: 10, scale: 2, nullable: true),
                    MaxToleranceValue = table.Column<decimal>(type: "numeric(10,2)", precision: 10, scale: 2, nullable: true),
                    TolerancePercentage = table.Column<decimal>(type: "numeric(5,2)", precision: 5, scale: 2, nullable: true),
                    TimeWindowDays = table.Column<int>(type: "integer", nullable: true),
                    SeasonalAdjustment = table.Column<bool>(type: "boolean", nullable: false),
                    SummerMultiplier = table.Column<decimal>(type: "numeric(5,2)", precision: 5, scale: 2, nullable: true),
                    WinterMultiplier = table.Column<decimal>(type: "numeric(5,2)", precision: 5, scale: 2, nullable: true),
                    WarningThreshold = table.Column<decimal>(type: "numeric(10,2)", precision: 10, scale: 2, nullable: true),
                    ErrorThreshold = table.Column<decimal>(type: "numeric(10,2)", precision: 10, scale: 2, nullable: true),
                    CriticalThreshold = table.Column<decimal>(type: "numeric(10,2)", precision: 10, scale: 2, nullable: true),
                    ActionOnViolation = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    AutoCorrect = table.Column<bool>(type: "boolean", nullable: false),
                    NotificationRecipients = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    RuleConfiguration = table.Column<string>(type: "text", nullable: true),
                    TotalApplications = table.Column<int>(type: "integer", nullable: false),
                    TruePositives = table.Column<int>(type: "integer", nullable: false),
                    FalsePositives = table.Column<int>(type: "integer", nullable: false),
                    EffectivenessScore = table.Column<decimal>(type: "numeric(5,2)", precision: 5, scale: 2, nullable: true),
                    LastReviewDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    ReviewedBy = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    CreatedBy = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    UpdatedBy = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ReadingValidationRules", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "Roles",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    Name = table.Column<string>(type: "text", nullable: false),
                    Description = table.Column<string>(type: "text", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    CreatedBy = table.Column<string>(type: "text", nullable: false),
                    UpdatedBy = table.Column<string>(type: "text", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Roles", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "Users",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    Username = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    FullName = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    Email = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    PersonId = table.Column<int>(type: "integer", nullable: false),
                    FinCoCode = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    MobilePhone = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    ProfitCentreCode = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    EmployeeNo = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    IsAuthenticated = table.Column<bool>(type: "boolean", nullable: false),
                    LastLogin = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    CreatedDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "NOW()"),
                    UpdatedDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValueSql: "NOW()")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Users", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "AmisSyncErrors",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    AmisSyncId = table.Column<int>(type: "integer", nullable: false),
                    ErrorType = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    Severity = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    ErrorMessage = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: false),
                    DetailedError = table.Column<string>(type: "text", nullable: true),
                    StackTrace = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    RecordIdentifier = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    RecordLineNumber = table.Column<int>(type: "integer", nullable: true),
                    RecordData = table.Column<string>(type: "text", nullable: true),
                    Operation = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    Endpoint = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    HttpMethod = table.Column<string>(type: "character varying(10)", maxLength: 10, nullable: true),
                    HttpStatusCode = table.Column<int>(type: "integer", nullable: true),
                    ResolutionStatus = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    ResolvedDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    ResolvedBy = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    ResolutionNotes = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    CanRetry = table.Column<bool>(type: "boolean", nullable: false),
                    RetryCount = table.Column<int>(type: "integer", nullable: false),
                    LastRetryDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    AmisSyncId1 = table.Column<int>(type: "integer", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    CreatedBy = table.Column<string>(type: "text", nullable: false),
                    UpdatedBy = table.Column<string>(type: "text", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AmisSyncErrors", x => x.Id);
                    table.ForeignKey(
                        name: "FK_AmisSyncErrors_AmisSyncs_AmisSyncId",
                        column: x => x.AmisSyncId,
                        principalTable: "AmisSyncs",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_AmisSyncErrors_AmisSyncs_AmisSyncId1",
                        column: x => x.AmisSyncId1,
                        principalTable: "AmisSyncs",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "WorkPackages",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    Name = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    Description = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    PackageType = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    Status = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    PlannedStartDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    PlannedEndDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ActualStartDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    ActualEndDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    Frequency = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    ServiceArea = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    SubArea = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    Priority = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    TotalMeters = table.Column<int>(type: "integer", nullable: false),
                    CompletedMeters = table.Column<int>(type: "integer", nullable: false),
                    ProgressPercentage = table.Column<decimal>(type: "numeric(5,2)", precision: 5, scale: 2, nullable: false),
                    AssignedTeam = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    EstimatedHours = table.Column<decimal>(type: "numeric(8,2)", precision: 8, scale: 2, nullable: false),
                    ActualHours = table.Column<decimal>(type: "numeric(8,2)", precision: 8, scale: 2, nullable: true),
                    EstimatedCost = table.Column<decimal>(type: "numeric(10,2)", precision: 10, scale: 2, nullable: true),
                    ActualCost = table.Column<decimal>(type: "numeric(10,2)", precision: 10, scale: 2, nullable: true),
                    IsTemplate = table.Column<bool>(type: "boolean", nullable: false),
                    TemplateCategory = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    IsRecurring = table.Column<bool>(type: "boolean", nullable: false),
                    RecurrencePattern = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    RecurrenceInterval = table.Column<int>(type: "integer", nullable: true),
                    LastExecuted = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    NextExecution = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    Notes = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    Instructions = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    AmsImportBatch = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    AmsImportDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    Source = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    CreatedBy = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    UpdatedBy = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    FrequencyTemplateId = table.Column<int>(type: "integer", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_WorkPackages", x => x.Id);
                    table.ForeignKey(
                        name: "FK_WorkPackages_FrequencyTemplates_FrequencyTemplateId",
                        column: x => x.FrequencyTemplateId,
                        principalTable: "FrequencyTemplates",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "Menus",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    Name = table.Column<string>(type: "text", nullable: false),
                    Code = table.Column<string>(type: "text", nullable: false),
                    Path = table.Column<string>(type: "text", nullable: false),
                    Component = table.Column<string>(type: "text", nullable: false),
                    Icon = table.Column<string>(type: "text", nullable: false),
                    ParentId = table.Column<int>(type: "integer", nullable: true),
                    Order = table.Column<int>(type: "integer", nullable: false),
                    IsVisible = table.Column<bool>(type: "boolean", nullable: false),
                    PermissionId = table.Column<int>(type: "integer", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    CreatedBy = table.Column<string>(type: "text", nullable: false),
                    UpdatedBy = table.Column<string>(type: "text", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Menus", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Menus_Menus_ParentId",
                        column: x => x.ParentId,
                        principalTable: "Menus",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_Menus_Permissions_PermissionId",
                        column: x => x.PermissionId,
                        principalTable: "Permissions",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "RolePermissions",
                columns: table => new
                {
                    RoleId = table.Column<int>(type: "integer", nullable: false),
                    PermissionId = table.Column<int>(type: "integer", nullable: false),
                    Id = table.Column<int>(type: "integer", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    CreatedBy = table.Column<string>(type: "text", nullable: false),
                    UpdatedBy = table.Column<string>(type: "text", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_RolePermissions", x => new { x.RoleId, x.PermissionId });
                    table.ForeignKey(
                        name: "FK_RolePermissions_Permissions_PermissionId",
                        column: x => x.PermissionId,
                        principalTable: "Permissions",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_RolePermissions_Roles_RoleId",
                        column: x => x.RoleId,
                        principalTable: "Roles",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "Configurations",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    Scope = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    Category = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    Key = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    DisplayName = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    Description = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: false),
                    DataType = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    Value = table.Column<string>(type: "text", nullable: false),
                    DefaultValue = table.Column<string>(type: "text", nullable: false),
                    UserId = table.Column<int>(type: "integer", nullable: true),
                    RoleId = table.Column<int>(type: "integer", nullable: true),
                    IsSystemDefined = table.Column<bool>(type: "boolean", nullable: false),
                    IsEncrypted = table.Column<bool>(type: "boolean", nullable: false),
                    ValidationRules = table.Column<string>(type: "text", nullable: false),
                    Priority = table.Column<int>(type: "integer", nullable: false),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    CreatedBy = table.Column<string>(type: "text", nullable: false),
                    UpdatedBy = table.Column<string>(type: "text", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Configurations", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Configurations_Roles_RoleId",
                        column: x => x.RoleId,
                        principalTable: "Roles",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_Configurations_Users_UserId",
                        column: x => x.UserId,
                        principalTable: "Users",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "Routes",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    RouteName = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    Description = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    Township = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    SubArea = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    Status = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    MeterCount = table.Column<int>(type: "integer", nullable: false),
                    RouteType = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    ReadingFrequency = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: true),
                    DefaultUserId = table.Column<int>(type: "integer", nullable: true),
                    EstimatedHours = table.Column<decimal>(type: "numeric", nullable: true),
                    Name = table.Column<string>(type: "text", nullable: false),
                    Zone = table.Column<string>(type: "text", nullable: true),
                    Area = table.Column<string>(type: "text", nullable: true),
                    EstimatedDuration = table.Column<int>(type: "integer", nullable: true),
                    EstimatedDistance = table.Column<decimal>(type: "numeric(8,2)", precision: 8, scale: 2, nullable: true),
                    StartLatitude = table.Column<decimal>(type: "numeric(10,6)", precision: 10, scale: 6, nullable: true),
                    StartLongitude = table.Column<decimal>(type: "numeric(10,6)", precision: 10, scale: 6, nullable: true),
                    EndLatitude = table.Column<decimal>(type: "numeric(10,6)", precision: 10, scale: 6, nullable: true),
                    EndLongitude = table.Column<decimal>(type: "numeric(10,6)", precision: 10, scale: 6, nullable: true),
                    StartAddress = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    EndAddress = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    AssignedTo = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    BackupAssignee = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    OptimizationMethod = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: true),
                    LastOptimized = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    OptimizedBy = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    RouteGeometry = table.Column<string>(type: "text", nullable: true),
                    Notes = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    IsTemplate = table.Column<bool>(type: "boolean", nullable: false),
                    TemplateCategory = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    AverageCompletionTime = table.Column<int>(type: "integer", precision: 8, scale: 2, nullable: true),
                    DifficultyRating = table.Column<decimal>(type: "numeric(3,2)", precision: 3, scale: 1, nullable: true),
                    LastSyncDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    Source = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    TotalMeters = table.Column<int>(type: "integer", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    CreatedBy = table.Column<string>(type: "text", nullable: false),
                    UpdatedBy = table.Column<string>(type: "text", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Routes", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Routes_Users_DefaultUserId",
                        column: x => x.DefaultUserId,
                        principalTable: "Users",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "UserRoles",
                columns: table => new
                {
                    UserId = table.Column<int>(type: "integer", nullable: false),
                    RoleId = table.Column<int>(type: "integer", nullable: false),
                    Id = table.Column<int>(type: "integer", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    CreatedBy = table.Column<string>(type: "text", nullable: false),
                    UpdatedBy = table.Column<string>(type: "text", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_UserRoles", x => new { x.UserId, x.RoleId });
                    table.ForeignKey(
                        name: "FK_UserRoles_Roles_RoleId",
                        column: x => x.RoleId,
                        principalTable: "Roles",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_UserRoles_Users_UserId",
                        column: x => x.UserId,
                        principalTable: "Users",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "WorkPackageAssignments",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    WorkPackageId = table.Column<int>(type: "integer", nullable: false),
                    UserId = table.Column<int>(type: "integer", nullable: false),
                    AssignmentType = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    Status = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    AssignedDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    AssignedBy = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    AcceptedDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    RejectedDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    StartedDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    CompletedDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    Reason = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    RejectionReason = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    ExpectedStartDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    ExpectedCompletionDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    AssignedMeterCount = table.Column<int>(type: "integer", nullable: true),
                    CompletedMeterCount = table.Column<int>(type: "integer", nullable: false),
                    WorkloadWeight = table.Column<int>(type: "integer", nullable: false),
                    Priority = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: true),
                    RequiresSupervision = table.Column<bool>(type: "boolean", nullable: false),
                    SupervisorId = table.Column<int>(type: "integer", nullable: true),
                    SkillRequirements = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    EquipmentRequirements = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    SpecialInstructions = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    EmailNotificationSent = table.Column<bool>(type: "boolean", nullable: false),
                    EmailSentDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    PushNotificationSent = table.Column<bool>(type: "boolean", nullable: false),
                    PushNotificationSentDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    LastActivityDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    Rating = table.Column<int>(type: "integer", nullable: true),
                    Feedback = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    RatedBy = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    RatedDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    CreatedBy = table.Column<string>(type: "text", nullable: false),
                    UpdatedBy = table.Column<string>(type: "text", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_WorkPackageAssignments", x => x.Id);
                    table.ForeignKey(
                        name: "FK_WorkPackageAssignments_Users_SupervisorId",
                        column: x => x.SupervisorId,
                        principalTable: "Users",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.SetNull);
                    table.ForeignKey(
                        name: "FK_WorkPackageAssignments_Users_UserId",
                        column: x => x.UserId,
                        principalTable: "Users",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_WorkPackageAssignments_WorkPackages_WorkPackageId",
                        column: x => x.WorkPackageId,
                        principalTable: "WorkPackages",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "WorkPackageHistories",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    WorkPackageId = table.Column<int>(type: "integer", nullable: false),
                    Action = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    Description = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    FieldName = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    OldValue = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    NewValue = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    ChangedBy = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    ChangedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    Source = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    IpAddress = table.Column<string>(type: "character varying(45)", maxLength: 45, nullable: true),
                    UserAgent = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    AdditionalData = table.Column<string>(type: "text", nullable: true),
                    Notes = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    CreatedBy = table.Column<string>(type: "text", nullable: false),
                    UpdatedBy = table.Column<string>(type: "text", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_WorkPackageHistories", x => x.Id);
                    table.ForeignKey(
                        name: "FK_WorkPackageHistories_WorkPackages_WorkPackageId",
                        column: x => x.WorkPackageId,
                        principalTable: "WorkPackages",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "WaterMeters",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    AssetId = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: true),
                    SerialNumber = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    AccountNumber = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: true),
                    BookNumber = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: true),
                    Unit = table.Column<int>(type: "integer", nullable: false),
                    RoadNumber = table.Column<int>(type: "integer", nullable: true),
                    RoadName = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    Township = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    SubArea = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    Location = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    Address = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    LastRead = table.Column<decimal>(type: "numeric(12,2)", precision: 12, scale: 2, nullable: true),
                    RecentChange = table.Column<decimal>(type: "numeric(12,2)", precision: 12, scale: 2, nullable: true),
                    Subd = table.Column<string>(type: "character varying(10)", maxLength: 10, nullable: true),
                    DateOfRead = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    Read = table.Column<decimal>(type: "numeric(12,2)", precision: 12, scale: 2, nullable: true),
                    CantRead = table.Column<bool>(type: "boolean", nullable: false),
                    Condition = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    Comments = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    LastReading = table.Column<decimal>(type: "numeric(12,4)", precision: 12, scale: 4, nullable: true),
                    LastReadingDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    MeterType = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    Status = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    InstallDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    CustomerCode = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    CustomerName = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    Latitude = table.Column<decimal>(type: "numeric(10,6)", precision: 10, scale: 6, nullable: true),
                    Longitude = table.Column<decimal>(type: "numeric(10,6)", precision: 10, scale: 6, nullable: true),
                    Brand = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    Model = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    BatteryLevel = table.Column<int>(type: "integer", nullable: true),
                    CommunicationMethod = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: true),
                    LastMaintenanceDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    NextMaintenanceDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    Notes = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    Source = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    RouteId = table.Column<int>(type: "integer", nullable: true),
                    AssignedRoute = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    RouteSequence = table.Column<int>(type: "integer", nullable: true),
                    SyncStatus = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    LastSyncDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    MeterSize = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    MeterId = table.Column<string>(type: "text", nullable: false),
                    MeterNumber = table.Column<string>(type: "text", nullable: false),
                    PropertyId = table.Column<string>(type: "text", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    CreatedBy = table.Column<string>(type: "text", nullable: false),
                    UpdatedBy = table.Column<string>(type: "text", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_WaterMeters", x => x.Id);
                    table.ForeignKey(
                        name: "FK_WaterMeters_Routes_RouteId",
                        column: x => x.RouteId,
                        principalTable: "Routes",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.SetNull);
                });

            migrationBuilder.CreateTable(
                name: "Assignments",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    MeterId = table.Column<int>(type: "integer", nullable: false),
                    AssignedUserId = table.Column<int>(type: "integer", nullable: false),
                    Priority = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    Frequency = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    AssignmentDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    DueDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    NextDueDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    Status = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    IsReactive = table.Column<bool>(type: "boolean", nullable: false),
                    IsCompleted = table.Column<bool>(type: "boolean", nullable: false),
                    CompletedDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    Notes = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: false),
                    Instructions = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: false),
                    EmailSent = table.Column<bool>(type: "boolean", nullable: false),
                    EmailSentDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    PushNotificationSent = table.Column<bool>(type: "boolean", nullable: false),
                    PushNotificationSentDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    RouteCode = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    RouteOrder = table.Column<int>(type: "integer", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    CreatedBy = table.Column<string>(type: "text", nullable: false),
                    UpdatedBy = table.Column<string>(type: "text", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Assignments", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Assignments_Users_AssignedUserId",
                        column: x => x.AssignedUserId,
                        principalTable: "Users",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_Assignments_WaterMeters_MeterId",
                        column: x => x.MeterId,
                        principalTable: "WaterMeters",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "BaselineReadings",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    MeterId = table.Column<int>(type: "integer", nullable: false),
                    ReadValue = table.Column<decimal>(type: "numeric(18,2)", precision: 18, scale: 2, nullable: false),
                    ReadDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    BaselineSource = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    SourceFile = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: false),
                    ImportBatchId = table.Column<int>(type: "integer", nullable: true),
                    IsValidated = table.Column<bool>(type: "boolean", nullable: false),
                    ValidationErrors = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    CreatedBy = table.Column<string>(type: "text", nullable: false),
                    UpdatedBy = table.Column<string>(type: "text", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_BaselineReadings", x => x.Id);
                    table.ForeignKey(
                        name: "FK_BaselineReadings_WaterMeters_MeterId",
                        column: x => x.MeterId,
                        principalTable: "WaterMeters",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "BaselineRecords",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    MeterId = table.Column<int>(type: "integer", nullable: false),
                    BaselineDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    BaselineValue = table.Column<decimal>(type: "numeric(12,4)", precision: 12, scale: 4, nullable: false),
                    BaselineType = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    Status = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    ImportBatch = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    SourceFile = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    SourceRowNumber = table.Column<int>(type: "integer", nullable: true),
                    DataSource = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    ValidationNotes = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    IsValidated = table.Column<bool>(type: "boolean", nullable: false),
                    ValidatedDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    ValidatedBy = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    HasValidationErrors = table.Column<bool>(type: "boolean", nullable: false),
                    ValidationErrors = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    IsAnomalous = table.Column<bool>(type: "boolean", nullable: false),
                    AnomalyDescription = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    PreviousBaselineId = table.Column<int>(type: "integer", nullable: true),
                    PreviousBaselineValue = table.Column<decimal>(type: "numeric(12,4)", precision: 12, scale: 4, nullable: true),
                    VarianceFromPrevious = table.Column<decimal>(type: "numeric(12,4)", precision: 12, scale: 4, nullable: true),
                    VariancePercentage = table.Column<decimal>(type: "numeric(8,2)", precision: 8, scale: 2, nullable: true),
                    IsCorrected = table.Column<bool>(type: "boolean", nullable: false),
                    CorrectedDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    CorrectedBy = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    CorrectionReason = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    Source = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    ValidationStatus = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: true),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false),
                    ConfidenceLevel = table.Column<int>(type: "integer", nullable: false),
                    Notes = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    CreatedBy = table.Column<string>(type: "text", nullable: false),
                    UpdatedBy = table.Column<string>(type: "text", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_BaselineRecords", x => x.Id);
                    table.ForeignKey(
                        name: "FK_BaselineRecords_BaselineRecords_PreviousBaselineId",
                        column: x => x.PreviousBaselineId,
                        principalTable: "BaselineRecords",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_BaselineRecords_WaterMeters_MeterId",
                        column: x => x.MeterId,
                        principalTable: "WaterMeters",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "MeterReadings",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    MeterId = table.Column<int>(type: "integer", nullable: false),
                    MeterNumber = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    ReadingValue = table.Column<decimal>(type: "numeric(12,4)", precision: 12, scale: 4, nullable: false),
                    PreviousReading = table.Column<decimal>(type: "numeric(12,4)", precision: 12, scale: 4, nullable: true),
                    Consumption = table.Column<decimal>(type: "numeric(12,4)", precision: 12, scale: 4, nullable: true),
                    ReadingDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ReadBy = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    Status = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    ValidationStatus = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    AnomalyType = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    Notes = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    Location = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    HasPhoto = table.Column<bool>(type: "boolean", nullable: false),
                    HasOCR = table.Column<bool>(type: "boolean", nullable: false),
                    OCRStatus = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: true),
                    OCRConfidence = table.Column<decimal>(type: "numeric(8,2)", nullable: true),
                    QualityScore = table.Column<decimal>(type: "numeric(8,2)", precision: 8, scale: 2, nullable: true),
                    ProcessedBy = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    ProcessedDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    ProcessingNotes = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    ReadingType = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: true),
                    DataSource = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    CreatedBy = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    UpdatedBy = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_MeterReadings", x => x.Id);
                    table.ForeignKey(
                        name: "FK_MeterReadings_WaterMeters_MeterId",
                        column: x => x.MeterId,
                        principalTable: "WaterMeters",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "RouteWaypoints",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    RouteId = table.Column<int>(type: "integer", nullable: false),
                    WaterMeterId = table.Column<int>(type: "integer", nullable: false),
                    SequenceOrder = table.Column<int>(type: "integer", nullable: false),
                    Latitude = table.Column<decimal>(type: "numeric(10,6)", precision: 10, scale: 6, nullable: true),
                    Longitude = table.Column<decimal>(type: "numeric(10,6)", precision: 10, scale: 6, nullable: true),
                    Address = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    EstimatedDuration = table.Column<int>(type: "integer", nullable: true),
                    DistanceFromPrevious = table.Column<decimal>(type: "numeric(8,2)", precision: 8, scale: 2, nullable: true),
                    TravelTimeFromPrevious = table.Column<int>(type: "integer", nullable: true),
                    Notes = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    AccessDifficulty = table.Column<string>(type: "character varying(20)", maxLength: 20, precision: 3, scale: 1, nullable: true),
                    RequiresSpecialEquipment = table.Column<bool>(type: "boolean", nullable: false),
                    SpecialInstructions = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    CreatedBy = table.Column<string>(type: "text", nullable: false),
                    UpdatedBy = table.Column<string>(type: "text", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_RouteWaypoints", x => x.Id);
                    table.ForeignKey(
                        name: "FK_RouteWaypoints_Routes_RouteId",
                        column: x => x.RouteId,
                        principalTable: "Routes",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_RouteWaypoints_WaterMeters_WaterMeterId",
                        column: x => x.WaterMeterId,
                        principalTable: "WaterMeters",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "WorkPackageItems",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    WorkPackageId = table.Column<int>(type: "integer", nullable: false),
                    MeterId = table.Column<int>(type: "integer", nullable: false),
                    SequenceOrder = table.Column<int>(type: "integer", nullable: false),
                    Status = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    ScheduledDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    ActualDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    AssignedTo = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    Priority = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: true),
                    EstimatedMinutes = table.Column<int>(type: "integer", nullable: true),
                    ActualMinutes = table.Column<int>(type: "integer", nullable: true),
                    SpecialInstructions = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    Notes = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    LastReading = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: true),
                    RequiresSpecialHandling = table.Column<bool>(type: "boolean", nullable: false),
                    SpecialHandlingReason = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    DifficultyRating = table.Column<decimal>(type: "numeric(2,1)", precision: 2, scale: 1, nullable: true),
                    Latitude = table.Column<decimal>(type: "numeric(10,6)", precision: 10, scale: 6, nullable: true),
                    Longitude = table.Column<decimal>(type: "numeric(10,6)", precision: 10, scale: 6, nullable: true),
                    ServiceAddress = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    SkipReason = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    FailureReason = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    RetryCount = table.Column<int>(type: "integer", nullable: false),
                    MaxRetries = table.Column<int>(type: "integer", nullable: false),
                    CompletedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    CompletedBy = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    CreatedBy = table.Column<string>(type: "text", nullable: false),
                    UpdatedBy = table.Column<string>(type: "text", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_WorkPackageItems", x => x.Id);
                    table.ForeignKey(
                        name: "FK_WorkPackageItems_WaterMeters_MeterId",
                        column: x => x.MeterId,
                        principalTable: "WaterMeters",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_WorkPackageItems_WorkPackages_WorkPackageId",
                        column: x => x.WorkPackageId,
                        principalTable: "WorkPackages",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "ReadingAnomalies",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    MeterReadingId = table.Column<int>(type: "integer", nullable: false),
                    AnomalyType = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    Severity = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    Description = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    ExpectedValue = table.Column<decimal>(type: "numeric(18,2)", precision: 12, scale: 4, nullable: true),
                    ActualValue = table.Column<decimal>(type: "numeric(18,2)", precision: 12, scale: 4, nullable: true),
                    Variance = table.Column<decimal>(type: "numeric(18,2)", precision: 12, scale: 4, nullable: true),
                    ConfidenceScore = table.Column<decimal>(type: "numeric(5,2)", precision: 5, scale: 2, nullable: true),
                    Status = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    AssignedTo = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    AssignedDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    ResolvedBy = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    ResolvedDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    Resolution = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    ResolutionType = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    Notes = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    RequiresFieldVisit = table.Column<bool>(type: "boolean", nullable: false),
                    IsRecurring = table.Column<bool>(type: "boolean", nullable: false),
                    RelatedAnomalyId = table.Column<int>(type: "integer", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    CreatedBy = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    UpdatedBy = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ReadingAnomalies", x => x.Id);
                    table.ForeignKey(
                        name: "FK_ReadingAnomalies_MeterReadings_MeterReadingId",
                        column: x => x.MeterReadingId,
                        principalTable: "MeterReadings",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_ReadingAnomalies_ReadingAnomalies_RelatedAnomalyId",
                        column: x => x.RelatedAnomalyId,
                        principalTable: "ReadingAnomalies",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "ReadingPhotos",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    MeterReadingId = table.Column<int>(type: "integer", nullable: false),
                    FileName = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: false),
                    FilePath = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: false),
                    FileSize = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    FileType = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: true),
                    QualityScore = table.Column<decimal>(type: "numeric(3,2)", precision: 3, scale: 2, nullable: true),
                    QualityStatus = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: true),
                    IsProcessed = table.Column<bool>(type: "boolean", nullable: false),
                    HasOCR = table.Column<bool>(type: "boolean", nullable: false),
                    OCRResult = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    OCRConfidence = table.Column<decimal>(type: "numeric(5,2)", precision: 5, scale: 2, nullable: true),
                    OCRStatus = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: true),
                    IsOverridden = table.Column<bool>(type: "boolean", nullable: false),
                    OverriddenBy = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    OverriddenDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    OverrideReason = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    ProcessingNotes = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    CreatedBy = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    UpdatedBy = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ReadingPhotos", x => x.Id);
                    table.ForeignKey(
                        name: "FK_ReadingPhotos_MeterReadings_MeterReadingId",
                        column: x => x.MeterReadingId,
                        principalTable: "MeterReadings",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "Tasks",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    Name = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    Description = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    Status = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    Priority = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    Type = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    AssignedTo = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    CreatedBy = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    WorkPackageId = table.Column<int>(type: "integer", nullable: true),
                    WorkPackageItemId = table.Column<int>(type: "integer", nullable: true),
                    MeterId = table.Column<int>(type: "integer", nullable: true),
                    RouteId = table.Column<int>(type: "integer", nullable: true),
                    DueDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    ScheduledDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    StartDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    CompletedDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    EstimatedHours = table.Column<decimal>(type: "numeric(5,2)", precision: 5, scale: 2, nullable: true),
                    ActualHours = table.Column<decimal>(type: "numeric(5,2)", precision: 5, scale: 2, nullable: true),
                    ProgressPercentage = table.Column<int>(type: "integer", nullable: false),
                    Location = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    ServiceAddress = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    Latitude = table.Column<decimal>(type: "numeric(10,6)", precision: 10, scale: 6, nullable: true),
                    Longitude = table.Column<decimal>(type: "numeric(10,6)", precision: 10, scale: 6, nullable: true),
                    LastReading = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: true),
                    CurrentReading = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: true),
                    ReadingPhotoUrl = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    Instructions = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    Notes = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    FailureReason = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    SkipReason = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Tasks", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Tasks_Routes_RouteId",
                        column: x => x.RouteId,
                        principalTable: "Routes",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_Tasks_WaterMeters_MeterId",
                        column: x => x.MeterId,
                        principalTable: "WaterMeters",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_Tasks_WorkPackageItems_WorkPackageItemId",
                        column: x => x.WorkPackageItemId,
                        principalTable: "WorkPackageItems",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_Tasks_WorkPackages_WorkPackageId",
                        column: x => x.WorkPackageId,
                        principalTable: "WorkPackages",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "OCRRecords",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    MeterReadingId = table.Column<int>(type: "integer", nullable: false),
                    ReadingPhotoId = table.Column<int>(type: "integer", nullable: true),
                    ProcessingEngine = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    ProcessingVersion = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    RecognizedValue = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    ConfidenceScore = table.Column<decimal>(type: "numeric(5,2)", precision: 5, scale: 2, nullable: false),
                    Status = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    RawData = table.Column<string>(type: "text", nullable: true),
                    ErrorMessage = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    ProcessingTimeMs = table.Column<int>(type: "integer", nullable: false),
                    BoundingBox = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    QualityScore = table.Column<decimal>(type: "numeric(5,2)", precision: 5, scale: 2, nullable: true),
                    IsManuallyVerified = table.Column<bool>(type: "boolean", nullable: false),
                    VerifiedBy = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    VerifiedDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    CorrectedValue = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    CorrectionReason = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    IsTrainingData = table.Column<bool>(type: "boolean", nullable: false),
                    TrainingLabel = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    ProcessingMetadata = table.Column<string>(type: "text", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    CreatedBy = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    UpdatedBy = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_OCRRecords", x => x.Id);
                    table.ForeignKey(
                        name: "FK_OCRRecords_MeterReadings_MeterReadingId",
                        column: x => x.MeterReadingId,
                        principalTable: "MeterReadings",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_OCRRecords_ReadingPhotos_ReadingPhotoId",
                        column: x => x.ReadingPhotoId,
                        principalTable: "ReadingPhotos",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "Readings",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    MeterId = table.Column<int>(type: "integer", nullable: false),
                    UserId = table.Column<int>(type: "integer", nullable: false),
                    TaskAssignmentId = table.Column<int>(type: "integer", nullable: true),
                    ReadingValue = table.Column<decimal>(type: "numeric(12,2)", nullable: false),
                    ReadingDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    PreviousReading = table.Column<decimal>(type: "numeric(12,2)", nullable: true),
                    Consumption = table.Column<decimal>(type: "numeric(12,2)", nullable: true),
                    ReadingMethod = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    OcrConfidence = table.Column<int>(type: "integer", nullable: true),
                    IsAnomalous = table.Column<bool>(type: "boolean", nullable: false),
                    AnomalyReason = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    CantRead = table.Column<bool>(type: "boolean", nullable: false),
                    CantReadReason = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    Latitude = table.Column<decimal>(type: "numeric(10,6)", precision: 18, scale: 6, nullable: true),
                    Longitude = table.Column<decimal>(type: "numeric(10,6)", precision: 18, scale: 6, nullable: true),
                    GpsAccuracy = table.Column<decimal>(type: "numeric(8,2)", nullable: true),
                    PhotoFilename = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    AdditionalPhotos = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    Notes = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    Status = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    IsValidated = table.Column<bool>(type: "boolean", nullable: false),
                    ValidatedBy = table.Column<int>(type: "integer", nullable: true),
                    ValidationDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    ValidationComments = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    IsSyncedToAms = table.Column<bool>(type: "boolean", nullable: false),
                    AmsSyncDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    AmsSyncStatus = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    AmsSyncError = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    ReadValue = table.Column<decimal>(type: "numeric(18,2)", precision: 18, scale: 2, nullable: false),
                    ReadDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    PhotoUrl = table.Column<string>(type: "text", nullable: false),
                    IsOcrGenerated = table.Column<bool>(type: "boolean", nullable: false),
                    IsOverride = table.Column<bool>(type: "boolean", nullable: false),
                    IsSerialMismatch = table.Column<bool>(type: "boolean", nullable: false),
                    DetectedMeterId = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    IsAnomaly = table.Column<bool>(type: "boolean", nullable: false),
                    ValidationNotes = table.Column<string>(type: "text", nullable: false),
                    SyncStatus = table.Column<string>(type: "text", nullable: false),
                    SyncDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    Source = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    ReadingType = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    DeviceBaseline = table.Column<decimal>(type: "numeric(18,2)", precision: 18, scale: 2, nullable: true),
                    BaselineDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    BaselineSource = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    AssignmentId = table.Column<int>(type: "integer", nullable: true),
                    WorkPackageItemId = table.Column<int>(type: "integer", nullable: true),
                    WorkTaskId = table.Column<int>(type: "integer", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    CreatedBy = table.Column<string>(type: "text", nullable: false),
                    UpdatedBy = table.Column<string>(type: "text", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Readings", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Readings_Assignments_AssignmentId",
                        column: x => x.AssignmentId,
                        principalTable: "Assignments",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.SetNull);
                    table.ForeignKey(
                        name: "FK_Readings_Tasks_WorkTaskId",
                        column: x => x.WorkTaskId,
                        principalTable: "Tasks",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_Readings_Users_UserId",
                        column: x => x.UserId,
                        principalTable: "Users",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_Readings_WaterMeters_MeterId",
                        column: x => x.MeterId,
                        principalTable: "WaterMeters",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_Readings_WorkPackageItems_WorkPackageItemId",
                        column: x => x.WorkPackageItemId,
                        principalTable: "WorkPackageItems",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "TaskAssignments",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    TaskId = table.Column<int>(type: "integer", nullable: false),
                    UserId = table.Column<int>(type: "integer", nullable: false),
                    AssignedDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    AssignedBy = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    AssignmentType = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    Status = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    AcceptedDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    RejectedDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    Reason = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    RouteId = table.Column<int>(type: "integer", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_TaskAssignments", x => x.Id);
                    table.ForeignKey(
                        name: "FK_TaskAssignments_Routes_RouteId",
                        column: x => x.RouteId,
                        principalTable: "Routes",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_TaskAssignments_Tasks_TaskId",
                        column: x => x.TaskId,
                        principalTable: "Tasks",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "TaskHistories",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    TaskId = table.Column<int>(type: "integer", nullable: false),
                    Action = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    OldValue = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    NewValue = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    ChangedBy = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    ChangedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    Reason = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_TaskHistories", x => x.Id);
                    table.ForeignKey(
                        name: "FK_TaskHistories_Tasks_TaskId",
                        column: x => x.TaskId,
                        principalTable: "Tasks",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_AmisSyncErrors_AmisSyncId_ErrorType",
                table: "AmisSyncErrors",
                columns: new[] { "AmisSyncId", "ErrorType" });

            migrationBuilder.CreateIndex(
                name: "IX_AmisSyncErrors_AmisSyncId1",
                table: "AmisSyncErrors",
                column: "AmisSyncId1");

            migrationBuilder.CreateIndex(
                name: "IX_AmisSyncErrors_CreatedAt",
                table: "AmisSyncErrors",
                column: "CreatedAt");

            migrationBuilder.CreateIndex(
                name: "IX_AmisSyncErrors_ResolutionStatus",
                table: "AmisSyncErrors",
                column: "ResolutionStatus");

            migrationBuilder.CreateIndex(
                name: "IX_Assignments_AssignedUserId",
                table: "Assignments",
                column: "AssignedUserId");

            migrationBuilder.CreateIndex(
                name: "IX_Assignments_MeterId",
                table: "Assignments",
                column: "MeterId");

            migrationBuilder.CreateIndex(
                name: "IX_BaselineReadings_MeterId",
                table: "BaselineReadings",
                column: "MeterId");

            migrationBuilder.CreateIndex(
                name: "IX_BaselineRecords_ImportBatch",
                table: "BaselineRecords",
                column: "ImportBatch");

            migrationBuilder.CreateIndex(
                name: "IX_BaselineRecords_MeterId_BaselineDate",
                table: "BaselineRecords",
                columns: new[] { "MeterId", "BaselineDate" });

            migrationBuilder.CreateIndex(
                name: "IX_BaselineRecords_PreviousBaselineId",
                table: "BaselineRecords",
                column: "PreviousBaselineId");

            migrationBuilder.CreateIndex(
                name: "IX_Configurations_RoleId",
                table: "Configurations",
                column: "RoleId");

            migrationBuilder.CreateIndex(
                name: "IX_Configurations_Scope_Category",
                table: "Configurations",
                columns: new[] { "Scope", "Category" });

            migrationBuilder.CreateIndex(
                name: "IX_Configurations_Scope_Category_Key_UserId_RoleId",
                table: "Configurations",
                columns: new[] { "Scope", "Category", "Key", "UserId", "RoleId" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Configurations_Scope_RoleId",
                table: "Configurations",
                columns: new[] { "Scope", "RoleId" });

            migrationBuilder.CreateIndex(
                name: "IX_Configurations_Scope_UserId",
                table: "Configurations",
                columns: new[] { "Scope", "UserId" });

            migrationBuilder.CreateIndex(
                name: "IX_Configurations_UserId",
                table: "Configurations",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_ConfigurationTemplates_Category_DisplayOrder",
                table: "ConfigurationTemplates",
                columns: new[] { "Category", "DisplayOrder" });

            migrationBuilder.CreateIndex(
                name: "IX_ConfigurationTemplates_Scope_Category",
                table: "ConfigurationTemplates",
                columns: new[] { "Scope", "Category" });

            migrationBuilder.CreateIndex(
                name: "IX_ConfigurationTemplates_Scope_Category_Key",
                table: "ConfigurationTemplates",
                columns: new[] { "Scope", "Category", "Key" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_FrequencyTemplates_Category",
                table: "FrequencyTemplates",
                column: "Category");

            migrationBuilder.CreateIndex(
                name: "IX_FrequencyTemplates_FrequencyType",
                table: "FrequencyTemplates",
                column: "FrequencyType");

            migrationBuilder.CreateIndex(
                name: "IX_FrequencyTemplates_IsDefault",
                table: "FrequencyTemplates",
                column: "IsDefault");

            migrationBuilder.CreateIndex(
                name: "IX_FrequencyTemplates_Name",
                table: "FrequencyTemplates",
                column: "Name",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_FrequencyTemplates_Status",
                table: "FrequencyTemplates",
                column: "Status");

            migrationBuilder.CreateIndex(
                name: "IX_Menus_Code",
                table: "Menus",
                column: "Code",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Menus_ParentId",
                table: "Menus",
                column: "ParentId");

            migrationBuilder.CreateIndex(
                name: "IX_Menus_PermissionId",
                table: "Menus",
                column: "PermissionId");

            migrationBuilder.CreateIndex(
                name: "IX_MeterReadings_MeterId_ReadingDate",
                table: "MeterReadings",
                columns: new[] { "MeterId", "ReadingDate" });

            migrationBuilder.CreateIndex(
                name: "IX_MeterReadings_ReadingDate",
                table: "MeterReadings",
                column: "ReadingDate");

            migrationBuilder.CreateIndex(
                name: "IX_OCRRecords_ConfidenceScore",
                table: "OCRRecords",
                column: "ConfidenceScore");

            migrationBuilder.CreateIndex(
                name: "IX_OCRRecords_CreatedAt",
                table: "OCRRecords",
                column: "CreatedAt");

            migrationBuilder.CreateIndex(
                name: "IX_OCRRecords_IsDeleted",
                table: "OCRRecords",
                column: "IsDeleted");

            migrationBuilder.CreateIndex(
                name: "IX_OCRRecords_IsManuallyVerified",
                table: "OCRRecords",
                column: "IsManuallyVerified");

            migrationBuilder.CreateIndex(
                name: "IX_OCRRecords_IsTrainingData",
                table: "OCRRecords",
                column: "IsTrainingData");

            migrationBuilder.CreateIndex(
                name: "IX_OCRRecords_MeterReadingId",
                table: "OCRRecords",
                column: "MeterReadingId");

            migrationBuilder.CreateIndex(
                name: "IX_OCRRecords_ProcessingEngine",
                table: "OCRRecords",
                column: "ProcessingEngine");

            migrationBuilder.CreateIndex(
                name: "IX_OCRRecords_ReadingPhotoId",
                table: "OCRRecords",
                column: "ReadingPhotoId");

            migrationBuilder.CreateIndex(
                name: "IX_OCRRecords_Status",
                table: "OCRRecords",
                column: "Status");

            migrationBuilder.CreateIndex(
                name: "IX_Permissions_Code",
                table: "Permissions",
                column: "Code",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_ReadingAnomalies_AnomalyType",
                table: "ReadingAnomalies",
                column: "AnomalyType");

            migrationBuilder.CreateIndex(
                name: "IX_ReadingAnomalies_AssignedTo",
                table: "ReadingAnomalies",
                column: "AssignedTo");

            migrationBuilder.CreateIndex(
                name: "IX_ReadingAnomalies_CreatedAt",
                table: "ReadingAnomalies",
                column: "CreatedAt");

            migrationBuilder.CreateIndex(
                name: "IX_ReadingAnomalies_IsDeleted",
                table: "ReadingAnomalies",
                column: "IsDeleted");

            migrationBuilder.CreateIndex(
                name: "IX_ReadingAnomalies_IsRecurring",
                table: "ReadingAnomalies",
                column: "IsRecurring");

            migrationBuilder.CreateIndex(
                name: "IX_ReadingAnomalies_MeterReadingId",
                table: "ReadingAnomalies",
                column: "MeterReadingId");

            migrationBuilder.CreateIndex(
                name: "IX_ReadingAnomalies_RelatedAnomalyId",
                table: "ReadingAnomalies",
                column: "RelatedAnomalyId");

            migrationBuilder.CreateIndex(
                name: "IX_ReadingAnomalies_ResolutionType",
                table: "ReadingAnomalies",
                column: "ResolutionType");

            migrationBuilder.CreateIndex(
                name: "IX_ReadingAnomalies_ResolvedBy",
                table: "ReadingAnomalies",
                column: "ResolvedBy");

            migrationBuilder.CreateIndex(
                name: "IX_ReadingAnomalies_Severity",
                table: "ReadingAnomalies",
                column: "Severity");

            migrationBuilder.CreateIndex(
                name: "IX_ReadingAnomalies_Status",
                table: "ReadingAnomalies",
                column: "Status");

            migrationBuilder.CreateIndex(
                name: "IX_ReadingPhotos_CreatedAt",
                table: "ReadingPhotos",
                column: "CreatedAt");

            migrationBuilder.CreateIndex(
                name: "IX_ReadingPhotos_HasOCR",
                table: "ReadingPhotos",
                column: "HasOCR");

            migrationBuilder.CreateIndex(
                name: "IX_ReadingPhotos_IsDeleted",
                table: "ReadingPhotos",
                column: "IsDeleted");

            migrationBuilder.CreateIndex(
                name: "IX_ReadingPhotos_IsOverridden",
                table: "ReadingPhotos",
                column: "IsOverridden");

            migrationBuilder.CreateIndex(
                name: "IX_ReadingPhotos_IsProcessed",
                table: "ReadingPhotos",
                column: "IsProcessed");

            migrationBuilder.CreateIndex(
                name: "IX_ReadingPhotos_MeterReadingId",
                table: "ReadingPhotos",
                column: "MeterReadingId");

            migrationBuilder.CreateIndex(
                name: "IX_ReadingPhotos_OCRStatus",
                table: "ReadingPhotos",
                column: "OCRStatus");

            migrationBuilder.CreateIndex(
                name: "IX_ReadingPhotos_QualityStatus",
                table: "ReadingPhotos",
                column: "QualityStatus");

            migrationBuilder.CreateIndex(
                name: "IX_Readings_AssignmentId",
                table: "Readings",
                column: "AssignmentId");

            migrationBuilder.CreateIndex(
                name: "IX_Readings_MeterId",
                table: "Readings",
                column: "MeterId");

            migrationBuilder.CreateIndex(
                name: "IX_Readings_UserId",
                table: "Readings",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_Readings_WorkPackageItemId",
                table: "Readings",
                column: "WorkPackageItemId");

            migrationBuilder.CreateIndex(
                name: "IX_Readings_WorkTaskId",
                table: "Readings",
                column: "WorkTaskId");

            migrationBuilder.CreateIndex(
                name: "IX_ReadingValidationRules_CreatedAt",
                table: "ReadingValidationRules",
                column: "CreatedAt");

            migrationBuilder.CreateIndex(
                name: "IX_ReadingValidationRules_CustomerType",
                table: "ReadingValidationRules",
                column: "CustomerType");

            migrationBuilder.CreateIndex(
                name: "IX_ReadingValidationRules_IsActive",
                table: "ReadingValidationRules",
                column: "IsActive");

            migrationBuilder.CreateIndex(
                name: "IX_ReadingValidationRules_IsDeleted",
                table: "ReadingValidationRules",
                column: "IsDeleted");

            migrationBuilder.CreateIndex(
                name: "IX_ReadingValidationRules_MeterType",
                table: "ReadingValidationRules",
                column: "MeterType");

            migrationBuilder.CreateIndex(
                name: "IX_ReadingValidationRules_Priority",
                table: "ReadingValidationRules",
                column: "Priority");

            migrationBuilder.CreateIndex(
                name: "IX_ReadingValidationRules_RuleName",
                table: "ReadingValidationRules",
                column: "RuleName");

            migrationBuilder.CreateIndex(
                name: "IX_ReadingValidationRules_RuleType",
                table: "ReadingValidationRules",
                column: "RuleType");

            migrationBuilder.CreateIndex(
                name: "IX_ReadingValidationRules_Zone",
                table: "ReadingValidationRules",
                column: "Zone");

            migrationBuilder.CreateIndex(
                name: "IX_RolePermissions_PermissionId",
                table: "RolePermissions",
                column: "PermissionId");

            migrationBuilder.CreateIndex(
                name: "IX_Roles_Name",
                table: "Roles",
                column: "Name",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Routes_AssignedTo",
                table: "Routes",
                column: "AssignedTo");

            migrationBuilder.CreateIndex(
                name: "IX_Routes_DefaultUserId",
                table: "Routes",
                column: "DefaultUserId");

            migrationBuilder.CreateIndex(
                name: "IX_Routes_IsTemplate",
                table: "Routes",
                column: "IsTemplate");

            migrationBuilder.CreateIndex(
                name: "IX_Routes_Name",
                table: "Routes",
                column: "Name");

            migrationBuilder.CreateIndex(
                name: "IX_Routes_Status",
                table: "Routes",
                column: "Status");

            migrationBuilder.CreateIndex(
                name: "IX_Routes_TemplateCategory",
                table: "Routes",
                column: "TemplateCategory");

            migrationBuilder.CreateIndex(
                name: "IX_Routes_Zone",
                table: "Routes",
                column: "Zone");

            migrationBuilder.CreateIndex(
                name: "IX_RouteWaypoints_RouteId_SequenceOrder",
                table: "RouteWaypoints",
                columns: new[] { "RouteId", "SequenceOrder" });

            migrationBuilder.CreateIndex(
                name: "IX_RouteWaypoints_WaterMeterId",
                table: "RouteWaypoints",
                column: "WaterMeterId");

            migrationBuilder.CreateIndex(
                name: "IX_TaskAssignments_AssignedBy",
                table: "TaskAssignments",
                column: "AssignedBy");

            migrationBuilder.CreateIndex(
                name: "IX_TaskAssignments_AssignedDate",
                table: "TaskAssignments",
                column: "AssignedDate");

            migrationBuilder.CreateIndex(
                name: "IX_TaskAssignments_AssignmentType",
                table: "TaskAssignments",
                column: "AssignmentType");

            migrationBuilder.CreateIndex(
                name: "IX_TaskAssignments_IsDeleted",
                table: "TaskAssignments",
                column: "IsDeleted");

            migrationBuilder.CreateIndex(
                name: "IX_TaskAssignments_RouteId",
                table: "TaskAssignments",
                column: "RouteId");

            migrationBuilder.CreateIndex(
                name: "IX_TaskAssignments_Status",
                table: "TaskAssignments",
                column: "Status");

            migrationBuilder.CreateIndex(
                name: "IX_TaskAssignments_TaskId_UserId",
                table: "TaskAssignments",
                columns: new[] { "TaskId", "UserId" });

            migrationBuilder.CreateIndex(
                name: "IX_TaskAssignments_UserId",
                table: "TaskAssignments",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_TaskHistories_Action",
                table: "TaskHistories",
                column: "Action");

            migrationBuilder.CreateIndex(
                name: "IX_TaskHistories_ChangedAt",
                table: "TaskHistories",
                column: "ChangedAt");

            migrationBuilder.CreateIndex(
                name: "IX_TaskHistories_ChangedBy",
                table: "TaskHistories",
                column: "ChangedBy");

            migrationBuilder.CreateIndex(
                name: "IX_TaskHistories_IsDeleted",
                table: "TaskHistories",
                column: "IsDeleted");

            migrationBuilder.CreateIndex(
                name: "IX_TaskHistories_TaskId_ChangedAt",
                table: "TaskHistories",
                columns: new[] { "TaskId", "ChangedAt" });

            migrationBuilder.CreateIndex(
                name: "IX_Tasks_AssignedTo",
                table: "Tasks",
                column: "AssignedTo");

            migrationBuilder.CreateIndex(
                name: "IX_Tasks_CreatedAt",
                table: "Tasks",
                column: "CreatedAt");

            migrationBuilder.CreateIndex(
                name: "IX_Tasks_CreatedBy",
                table: "Tasks",
                column: "CreatedBy");

            migrationBuilder.CreateIndex(
                name: "IX_Tasks_DueDate",
                table: "Tasks",
                column: "DueDate");

            migrationBuilder.CreateIndex(
                name: "IX_Tasks_IsDeleted",
                table: "Tasks",
                column: "IsDeleted");

            migrationBuilder.CreateIndex(
                name: "IX_Tasks_Latitude_Longitude",
                table: "Tasks",
                columns: new[] { "Latitude", "Longitude" });

            migrationBuilder.CreateIndex(
                name: "IX_Tasks_MeterId",
                table: "Tasks",
                column: "MeterId");

            migrationBuilder.CreateIndex(
                name: "IX_Tasks_Name",
                table: "Tasks",
                column: "Name");

            migrationBuilder.CreateIndex(
                name: "IX_Tasks_Priority",
                table: "Tasks",
                column: "Priority");

            migrationBuilder.CreateIndex(
                name: "IX_Tasks_RouteId",
                table: "Tasks",
                column: "RouteId");

            migrationBuilder.CreateIndex(
                name: "IX_Tasks_ScheduledDate",
                table: "Tasks",
                column: "ScheduledDate");

            migrationBuilder.CreateIndex(
                name: "IX_Tasks_ServiceAddress",
                table: "Tasks",
                column: "ServiceAddress");

            migrationBuilder.CreateIndex(
                name: "IX_Tasks_Status",
                table: "Tasks",
                column: "Status");

            migrationBuilder.CreateIndex(
                name: "IX_Tasks_Type",
                table: "Tasks",
                column: "Type");

            migrationBuilder.CreateIndex(
                name: "IX_Tasks_WorkPackageId",
                table: "Tasks",
                column: "WorkPackageId");

            migrationBuilder.CreateIndex(
                name: "IX_Tasks_WorkPackageItemId",
                table: "Tasks",
                column: "WorkPackageItemId");

            migrationBuilder.CreateIndex(
                name: "IX_UserRoles_RoleId",
                table: "UserRoles",
                column: "RoleId");

            migrationBuilder.CreateIndex(
                name: "IX_Users_Username",
                table: "Users",
                column: "Username",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_WaterMeters_AccountNumber",
                table: "WaterMeters",
                column: "AccountNumber");

            migrationBuilder.CreateIndex(
                name: "IX_WaterMeters_AssetId",
                table: "WaterMeters",
                column: "AssetId");

            migrationBuilder.CreateIndex(
                name: "IX_WaterMeters_CustomerCode",
                table: "WaterMeters",
                column: "CustomerCode");

            migrationBuilder.CreateIndex(
                name: "IX_WaterMeters_RouteId",
                table: "WaterMeters",
                column: "RouteId");

            migrationBuilder.CreateIndex(
                name: "IX_WaterMeters_SerialNumber",
                table: "WaterMeters",
                column: "SerialNumber",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_WaterMeters_Status",
                table: "WaterMeters",
                column: "Status");

            migrationBuilder.CreateIndex(
                name: "IX_WaterMeters_SyncStatus",
                table: "WaterMeters",
                column: "SyncStatus");

            migrationBuilder.CreateIndex(
                name: "IX_WaterMeters_Township",
                table: "WaterMeters",
                column: "Township");

            migrationBuilder.CreateIndex(
                name: "IX_WorkPackageAssignments_AssignedBy",
                table: "WorkPackageAssignments",
                column: "AssignedBy");

            migrationBuilder.CreateIndex(
                name: "IX_WorkPackageAssignments_AssignedDate",
                table: "WorkPackageAssignments",
                column: "AssignedDate");

            migrationBuilder.CreateIndex(
                name: "IX_WorkPackageAssignments_AssignmentType",
                table: "WorkPackageAssignments",
                column: "AssignmentType");

            migrationBuilder.CreateIndex(
                name: "IX_WorkPackageAssignments_CreatedAt",
                table: "WorkPackageAssignments",
                column: "CreatedAt");

            migrationBuilder.CreateIndex(
                name: "IX_WorkPackageAssignments_IsDeleted",
                table: "WorkPackageAssignments",
                column: "IsDeleted");

            migrationBuilder.CreateIndex(
                name: "IX_WorkPackageAssignments_Priority",
                table: "WorkPackageAssignments",
                column: "Priority");

            migrationBuilder.CreateIndex(
                name: "IX_WorkPackageAssignments_RequiresSupervision",
                table: "WorkPackageAssignments",
                column: "RequiresSupervision");

            migrationBuilder.CreateIndex(
                name: "IX_WorkPackageAssignments_Status",
                table: "WorkPackageAssignments",
                column: "Status");

            migrationBuilder.CreateIndex(
                name: "IX_WorkPackageAssignments_SupervisorId",
                table: "WorkPackageAssignments",
                column: "SupervisorId");

            migrationBuilder.CreateIndex(
                name: "IX_WorkPackageAssignments_UserId",
                table: "WorkPackageAssignments",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_WorkPackageAssignments_WorkPackageId_UserId",
                table: "WorkPackageAssignments",
                columns: new[] { "WorkPackageId", "UserId" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_WorkPackageHistories_Action",
                table: "WorkPackageHistories",
                column: "Action");

            migrationBuilder.CreateIndex(
                name: "IX_WorkPackageHistories_ChangedAt",
                table: "WorkPackageHistories",
                column: "ChangedAt");

            migrationBuilder.CreateIndex(
                name: "IX_WorkPackageHistories_ChangedBy",
                table: "WorkPackageHistories",
                column: "ChangedBy");

            migrationBuilder.CreateIndex(
                name: "IX_WorkPackageHistories_IsDeleted",
                table: "WorkPackageHistories",
                column: "IsDeleted");

            migrationBuilder.CreateIndex(
                name: "IX_WorkPackageHistories_Source",
                table: "WorkPackageHistories",
                column: "Source");

            migrationBuilder.CreateIndex(
                name: "IX_WorkPackageHistories_WorkPackageId_ChangedAt",
                table: "WorkPackageHistories",
                columns: new[] { "WorkPackageId", "ChangedAt" });

            migrationBuilder.CreateIndex(
                name: "IX_WorkPackageItems_AssignedTo",
                table: "WorkPackageItems",
                column: "AssignedTo");

            migrationBuilder.CreateIndex(
                name: "IX_WorkPackageItems_CreatedAt",
                table: "WorkPackageItems",
                column: "CreatedAt");

            migrationBuilder.CreateIndex(
                name: "IX_WorkPackageItems_IsDeleted",
                table: "WorkPackageItems",
                column: "IsDeleted");

            migrationBuilder.CreateIndex(
                name: "IX_WorkPackageItems_MeterId",
                table: "WorkPackageItems",
                column: "MeterId");

            migrationBuilder.CreateIndex(
                name: "IX_WorkPackageItems_Priority",
                table: "WorkPackageItems",
                column: "Priority");

            migrationBuilder.CreateIndex(
                name: "IX_WorkPackageItems_RequiresSpecialHandling",
                table: "WorkPackageItems",
                column: "RequiresSpecialHandling");

            migrationBuilder.CreateIndex(
                name: "IX_WorkPackageItems_ScheduledDate",
                table: "WorkPackageItems",
                column: "ScheduledDate");

            migrationBuilder.CreateIndex(
                name: "IX_WorkPackageItems_Status",
                table: "WorkPackageItems",
                column: "Status");

            migrationBuilder.CreateIndex(
                name: "IX_WorkPackageItems_WorkPackageId_MeterId",
                table: "WorkPackageItems",
                columns: new[] { "WorkPackageId", "MeterId" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_WorkPackageItems_WorkPackageId_SequenceOrder",
                table: "WorkPackageItems",
                columns: new[] { "WorkPackageId", "SequenceOrder" });

            migrationBuilder.CreateIndex(
                name: "IX_WorkPackages_CreatedAt",
                table: "WorkPackages",
                column: "CreatedAt");

            migrationBuilder.CreateIndex(
                name: "IX_WorkPackages_CreatedBy",
                table: "WorkPackages",
                column: "CreatedBy");

            migrationBuilder.CreateIndex(
                name: "IX_WorkPackages_Frequency",
                table: "WorkPackages",
                column: "Frequency");

            migrationBuilder.CreateIndex(
                name: "IX_WorkPackages_FrequencyTemplateId",
                table: "WorkPackages",
                column: "FrequencyTemplateId");

            migrationBuilder.CreateIndex(
                name: "IX_WorkPackages_IsDeleted",
                table: "WorkPackages",
                column: "IsDeleted");

            migrationBuilder.CreateIndex(
                name: "IX_WorkPackages_IsRecurring",
                table: "WorkPackages",
                column: "IsRecurring");

            migrationBuilder.CreateIndex(
                name: "IX_WorkPackages_IsTemplate",
                table: "WorkPackages",
                column: "IsTemplate");

            migrationBuilder.CreateIndex(
                name: "IX_WorkPackages_Name",
                table: "WorkPackages",
                column: "Name");

            migrationBuilder.CreateIndex(
                name: "IX_WorkPackages_PackageType",
                table: "WorkPackages",
                column: "PackageType");

            migrationBuilder.CreateIndex(
                name: "IX_WorkPackages_PlannedEndDate",
                table: "WorkPackages",
                column: "PlannedEndDate");

            migrationBuilder.CreateIndex(
                name: "IX_WorkPackages_PlannedStartDate",
                table: "WorkPackages",
                column: "PlannedStartDate");

            migrationBuilder.CreateIndex(
                name: "IX_WorkPackages_Priority",
                table: "WorkPackages",
                column: "Priority");

            migrationBuilder.CreateIndex(
                name: "IX_WorkPackages_ServiceArea",
                table: "WorkPackages",
                column: "ServiceArea");

            migrationBuilder.CreateIndex(
                name: "IX_WorkPackages_Source",
                table: "WorkPackages",
                column: "Source");

            migrationBuilder.CreateIndex(
                name: "IX_WorkPackages_Status",
                table: "WorkPackages",
                column: "Status");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "AmisSyncErrors");

            migrationBuilder.DropTable(
                name: "BaselineReadings");

            migrationBuilder.DropTable(
                name: "BaselineRecords");

            migrationBuilder.DropTable(
                name: "Configurations");

            migrationBuilder.DropTable(
                name: "ConfigurationTemplates");

            migrationBuilder.DropTable(
                name: "Menus");

            migrationBuilder.DropTable(
                name: "OCRRecords");

            migrationBuilder.DropTable(
                name: "ReadingAnomalies");

            migrationBuilder.DropTable(
                name: "Readings");

            migrationBuilder.DropTable(
                name: "ReadingValidationRules");

            migrationBuilder.DropTable(
                name: "RolePermissions");

            migrationBuilder.DropTable(
                name: "RouteWaypoints");

            migrationBuilder.DropTable(
                name: "TaskAssignments");

            migrationBuilder.DropTable(
                name: "TaskHistories");

            migrationBuilder.DropTable(
                name: "UserRoles");

            migrationBuilder.DropTable(
                name: "WorkPackageAssignments");

            migrationBuilder.DropTable(
                name: "WorkPackageHistories");

            migrationBuilder.DropTable(
                name: "AmisSyncs");

            migrationBuilder.DropTable(
                name: "ReadingPhotos");

            migrationBuilder.DropTable(
                name: "Assignments");

            migrationBuilder.DropTable(
                name: "Permissions");

            migrationBuilder.DropTable(
                name: "Tasks");

            migrationBuilder.DropTable(
                name: "Roles");

            migrationBuilder.DropTable(
                name: "MeterReadings");

            migrationBuilder.DropTable(
                name: "WorkPackageItems");

            migrationBuilder.DropTable(
                name: "WaterMeters");

            migrationBuilder.DropTable(
                name: "WorkPackages");

            migrationBuilder.DropTable(
                name: "Routes");

            migrationBuilder.DropTable(
                name: "FrequencyTemplates");

            migrationBuilder.DropTable(
                name: "Users");
        }
    }
}
