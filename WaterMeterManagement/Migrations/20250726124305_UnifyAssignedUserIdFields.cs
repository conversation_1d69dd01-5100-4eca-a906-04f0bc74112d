﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace WaterMeterManagement.Migrations
{
    /// <inheritdoc />
    public partial class UnifyAssignedUserIdFields : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // Step 1: Add AssignedUserId columns
            migrationBuilder.AddColumn<int>(
                name: "AssignedUserId",
                table: "WorkPackageItems",
                type: "integer",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "AssignedUserId",
                table: "Routes",
                type: "integer",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "AssignedUserId",
                table: "ReadingAnomalies",
                type: "integer",
                nullable: true);

            // Step 2: Migrate data from AssignedTo (string) to AssignedUserId (int)
            // Convert valid numeric AssignedTo values to AssignedUserId
            
            // Migrate WorkPackageItems
            migrationBuilder.Sql(@"
                UPDATE ""WorkPackageItems""
                SET ""AssignedUserId"" = CASE 
                    WHEN ""AssignedTo"" ~ '^[0-9]+$' THEN CAST(""AssignedTo"" AS INTEGER)
                    ELSE NULL 
                END
                WHERE ""AssignedTo"" IS NOT NULL AND ""AssignedTo"" != '';
            ");

            // Migrate Routes
            migrationBuilder.Sql(@"
                UPDATE ""Routes""
                SET ""AssignedUserId"" = CASE 
                    WHEN ""AssignedTo"" ~ '^[0-9]+$' THEN CAST(""AssignedTo"" AS INTEGER)
                    ELSE NULL 
                END
                WHERE ""AssignedTo"" IS NOT NULL AND ""AssignedTo"" != '';
            ");

            // Migrate ReadingAnomalies
            migrationBuilder.Sql(@"
                UPDATE ""ReadingAnomalies""
                SET ""AssignedUserId"" = CASE 
                    WHEN ""AssignedTo"" ~ '^[0-9]+$' THEN CAST(""AssignedTo"" AS INTEGER)
                    ELSE NULL 
                END
                WHERE ""AssignedTo"" IS NOT NULL AND ""AssignedTo"" != '';
            ");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "AssignedUserId",
                table: "WorkPackageItems");

            migrationBuilder.DropColumn(
                name: "AssignedUserId",
                table: "Routes");

            migrationBuilder.DropColumn(
                name: "AssignedUserId",
                table: "ReadingAnomalies");
        }
    }
}
