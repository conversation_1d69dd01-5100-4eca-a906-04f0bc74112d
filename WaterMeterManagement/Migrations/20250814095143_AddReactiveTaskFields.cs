﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace WaterMeterManagement.Migrations
{
    /// <inheritdoc />
    public partial class AddReactiveTaskFields : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "AmsTaskNumber",
                table: "Tasks",
                type: "character varying(50)",
                maxLength: 50,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "AssetId",
                table: "Tasks",
                type: "character varying(50)",
                maxLength: 50,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "TaskCategory",
                table: "Tasks",
                type: "character varying(20)",
                maxLength: 20,
                nullable: false,
                defaultValue: "");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "AmsTaskNumber",
                table: "Tasks");

            migrationBuilder.DropColumn(
                name: "AssetId",
                table: "Tasks");

            migrationBuilder.DropColumn(
                name: "TaskCategory",
                table: "Tasks");
        }
    }
}
