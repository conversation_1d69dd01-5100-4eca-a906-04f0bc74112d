﻿// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;
using WaterMeterManagement.Data;

#nullable disable

namespace WaterMeterManagement.Migrations
{
    [DbContext(typeof(ApplicationDbContext))]
    partial class ApplicationDbContextModelSnapshot : ModelSnapshot
    {
        protected override void BuildModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "8.0.11")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("WaterMeterManagement.Models.AmisSync", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("AmisEndpoint")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("AmisVersion")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("CurrentOperation")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<decimal?>("DataSizeMB")
                        .HasColumnType("decimal(10,2)");

                    b.Property<string>("DetailedLog")
                        .HasColumnType("text");

                    b.Property<DateTime?>("EndTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ErrorMessage")
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<int>("FailedRecords")
                        .HasColumnType("integer");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastSyncDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("MaxRetries")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("NextRetryTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("ProcessedRecords")
                        .HasColumnType("integer");

                    b.Property<decimal>("ProgressPercentage")
                        .HasColumnType("decimal(5,2)");

                    b.Property<int?>("RecordsPerSecond")
                        .HasColumnType("integer");

                    b.Property<string>("ResultSummary")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<int>("RetryCount")
                        .HasColumnType("integer");

                    b.Property<int>("SkippedRecords")
                        .HasColumnType("integer");

                    b.Property<DateTime>("StartTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<int>("SuccessfulRecords")
                        .HasColumnType("integer");

                    b.Property<string>("SyncConfiguration")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("SyncToken")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("SyncType")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<int>("TotalRecords")
                        .HasColumnType("integer");

                    b.Property<string>("TriggerBy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("AmisSyncs");
                });

            modelBuilder.Entity("WaterMeterManagement.Models.AmisSyncError", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int>("AmisSyncId")
                        .HasColumnType("integer");

                    b.Property<int?>("AmisSyncId1")
                        .HasColumnType("integer");

                    b.Property<bool>("CanRetry")
                        .HasColumnType("boolean");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("DetailedError")
                        .HasColumnType("text");

                    b.Property<string>("Endpoint")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("ErrorMessage")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("ErrorType")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("HttpMethod")
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)");

                    b.Property<int?>("HttpStatusCode")
                        .HasColumnType("integer");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastRetryDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Operation")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("RecordData")
                        .HasColumnType("text");

                    b.Property<string>("RecordIdentifier")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<int?>("RecordLineNumber")
                        .HasColumnType("integer");

                    b.Property<string>("ResolutionNotes")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("ResolutionStatus")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<string>("ResolvedBy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime?>("ResolvedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("RetryCount")
                        .HasColumnType("integer");

                    b.Property<string>("Severity")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<string>("StackTrace")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("AmisSyncId1");

                    b.HasIndex("CreatedAt");

                    b.HasIndex("ResolutionStatus");

                    b.HasIndex("AmisSyncId", "ErrorType");

                    b.ToTable("AmisSyncErrors");
                });

            modelBuilder.Entity("WaterMeterManagement.Models.BaselineRecord", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("AnomalyDescription")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<DateTime>("BaselineDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("BaselineType")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<decimal>("BaselineValue")
                        .HasPrecision(12, 4)
                        .HasColumnType("decimal(12,4)");

                    b.Property<int>("ConfidenceLevel")
                        .HasColumnType("integer");

                    b.Property<string>("CorrectedBy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime?>("CorrectedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CorrectionReason")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("DataSource")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<bool>("HasValidationErrors")
                        .HasColumnType("boolean");

                    b.Property<string>("ImportBatch")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsAnomalous")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsCorrected")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsValidated")
                        .HasColumnType("boolean");

                    b.Property<int>("MeterId")
                        .HasColumnType("integer");

                    b.Property<string>("Notes")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<int?>("PreviousBaselineId")
                        .HasColumnType("integer");

                    b.Property<decimal?>("PreviousBaselineValue")
                        .HasPrecision(12, 4)
                        .HasColumnType("decimal(12,4)");

                    b.Property<string>("Source")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("SourceFile")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<int?>("SourceRowNumber")
                        .HasColumnType("integer");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("ValidatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime?>("ValidatedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ValidationErrors")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<string>("ValidationNotes")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("ValidationStatus")
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<decimal?>("VarianceFromPrevious")
                        .HasPrecision(12, 4)
                        .HasColumnType("decimal(12,4)");

                    b.Property<decimal?>("VariancePercentage")
                        .HasPrecision(8, 2)
                        .HasColumnType("decimal(8,2)");

                    b.HasKey("Id");

                    b.HasIndex("ImportBatch");

                    b.HasIndex("PreviousBaselineId");

                    b.HasIndex("MeterId", "BaselineDate");

                    b.ToTable("BaselineRecords");
                });

            modelBuilder.Entity("WaterMeterManagement.Models.Configuration", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Category")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("DataType")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<string>("DefaultValue")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("DisplayName")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsEncrypted")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsSystemDefined")
                        .HasColumnType("boolean");

                    b.Property<string>("Key")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<int>("Priority")
                        .HasColumnType("integer");

                    b.Property<int?>("RoleId")
                        .HasColumnType("integer");

                    b.Property<string>("Scope")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int?>("UserId")
                        .HasColumnType("integer");

                    b.Property<string>("ValidationRules")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Value")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("RoleId");

                    b.HasIndex("UserId");

                    b.HasIndex("Scope", "Category");

                    b.HasIndex("Scope", "RoleId");

                    b.HasIndex("Scope", "UserId");

                    b.HasIndex("Scope", "Category", "Key", "UserId", "RoleId")
                        .IsUnique();

                    b.ToTable("Configurations");
                });

            modelBuilder.Entity("WaterMeterManagement.Models.ConfigurationTemplate", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Category")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("CategoryDisplayName")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("CategoryIcon")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("DataType")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<string>("DefaultValue")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("DisplayName")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<int>("DisplayOrder")
                        .HasColumnType("integer");

                    b.Property<string>("Group")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("GroupDisplayName")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsReadOnly")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsSensitive")
                        .HasColumnType("boolean");

                    b.Property<string>("Key")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<bool>("RequiresRestart")
                        .HasColumnType("boolean");

                    b.Property<string>("Scope")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("UIComponent")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("UIProperties")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("ValidationRules")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("Category", "DisplayOrder");

                    b.HasIndex("Scope", "Category");

                    b.HasIndex("Scope", "Category", "Key")
                        .IsUnique();

                    b.ToTable("ConfigurationTemplates");
                });

            modelBuilder.Entity("WaterMeterManagement.Models.FrequencyTemplate", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("AdvancedConfiguration")
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<string>("Category")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int?>("DayOfMonth")
                        .HasColumnType("integer");

                    b.Property<string>("DayOfWeek")
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<int?>("EstimatedDuration")
                        .HasPrecision(8, 2)
                        .HasColumnType("integer");

                    b.Property<string>("FrequencyType")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<string>("IntervalUnit")
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<int>("IntervalValue")
                        .HasColumnType("integer");

                    b.Property<bool>("IsDefault")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastUsed")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("MonthOfYear")
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Notes")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<string>("TimeOfDay")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("UsageCount")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("Category");

                    b.HasIndex("FrequencyType");

                    b.HasIndex("IsDefault");

                    b.HasIndex("Name")
                        .IsUnique();

                    b.HasIndex("Status");

                    b.ToTable("FrequencyTemplates");
                });

            modelBuilder.Entity("WaterMeterManagement.Models.Menu", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Component")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Icon")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsVisible")
                        .HasColumnType("boolean");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("Order")
                        .HasColumnType("integer");

                    b.Property<int?>("ParentId")
                        .HasColumnType("integer");

                    b.Property<string>("Path")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int?>("PermissionId")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("Code")
                        .IsUnique();

                    b.HasIndex("ParentId");

                    b.HasIndex("PermissionId");

                    b.ToTable("Menus");
                });

            modelBuilder.Entity("WaterMeterManagement.Models.MeterReading", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("AnomalyReason")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("AnomalyType")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<bool>("CantRead")
                        .HasColumnType("boolean");

                    b.Property<string>("CantReadReason")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("DataSource")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<decimal?>("GpsAccuracy")
                        .HasPrecision(8, 2)
                        .HasColumnType("decimal(8,2)");

                    b.Property<bool>("HasOCR")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsAnomalous")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsValidated")
                        .HasColumnType("boolean");

                    b.Property<decimal?>("Latitude")
                        .HasPrecision(10, 6)
                        .HasColumnType("decimal(10,6)");

                    b.Property<string>("Location")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<decimal?>("Longitude")
                        .HasPrecision(10, 6)
                        .HasColumnType("decimal(10,6)");

                    b.Property<int>("MeterId")
                        .HasColumnType("integer");

                    b.Property<string>("Notes")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<decimal?>("OCRConfidence")
                        .HasPrecision(5, 2)
                        .HasColumnType("decimal(5,2)");

                    b.Property<string>("OCRStatus")
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<DateTime>("ReadingDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ReadingMethod")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<string>("ReadingType")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<decimal>("ReadingValue")
                        .HasPrecision(12, 4)
                        .HasColumnType("decimal(12,4)");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<int>("TaskId")
                        .HasColumnType("integer");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<int>("UserId")
                        .HasColumnType("integer");

                    b.Property<int?>("ValidatedBy")
                        .HasColumnType("integer");

                    b.Property<string>("ValidationComments")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<DateTime?>("ValidationDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ValidationStatus")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<int?>("WorkPackageItemId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("CantRead");

                    b.HasIndex("CreatedAt");

                    b.HasIndex("DataSource");

                    b.HasIndex("IsAnomalous");

                    b.HasIndex("IsDeleted");

                    b.HasIndex("IsValidated");

                    b.HasIndex("MeterId");

                    b.HasIndex("ReadingDate");

                    b.HasIndex("ReadingMethod");

                    b.HasIndex("ReadingType");

                    b.HasIndex("Status");

                    b.HasIndex("TaskId");

                    b.HasIndex("UserId");

                    b.HasIndex("ValidatedBy");

                    b.HasIndex("ValidationStatus");

                    b.HasIndex("WorkPackageItemId");

                    b.ToTable("MeterReadings");
                });

            modelBuilder.Entity("WaterMeterManagement.Models.OCRRecord", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("BoundingBox")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<decimal>("ConfidenceScore")
                        .HasPrecision(5, 2)
                        .HasColumnType("decimal(5,2)");

                    b.Property<string>("CorrectedValue")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("CorrectionReason")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("ErrorMessage")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsManuallyVerified")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsTrainingData")
                        .HasColumnType("boolean");

                    b.Property<int>("MeterReadingId")
                        .HasColumnType("integer");

                    b.Property<string>("ProcessingEngine")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("ProcessingMetadata")
                        .HasColumnType("text");

                    b.Property<int>("ProcessingTimeMs")
                        .HasColumnType("integer");

                    b.Property<string>("ProcessingVersion")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<decimal?>("QualityScore")
                        .HasPrecision(5, 2)
                        .HasColumnType("decimal(5,2)");

                    b.Property<string>("RawData")
                        .HasColumnType("text");

                    b.Property<int?>("ReadingPhotoId")
                        .HasColumnType("integer");

                    b.Property<string>("RecognizedValue")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<string>("TrainingLabel")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("VerifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime?>("VerifiedDate")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.HasIndex("ConfidenceScore");

                    b.HasIndex("CreatedAt");

                    b.HasIndex("IsDeleted");

                    b.HasIndex("IsManuallyVerified");

                    b.HasIndex("IsTrainingData");

                    b.HasIndex("MeterReadingId");

                    b.HasIndex("ProcessingEngine");

                    b.HasIndex("ReadingPhotoId");

                    b.HasIndex("Status");

                    b.ToTable("OCRRecords");
                });

            modelBuilder.Entity("WaterMeterManagement.Models.Permission", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("Module")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("Code")
                        .IsUnique();

                    b.ToTable("Permissions");
                });

            modelBuilder.Entity("WaterMeterManagement.Models.ReadingAnomaly", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<decimal?>("ActualValue")
                        .HasPrecision(12, 4)
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("AnomalyType")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime?>("AssignedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("AssignedTo")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<int?>("AssignedUserId")
                        .HasColumnType("integer");

                    b.Property<decimal?>("ConfidenceScore")
                        .HasPrecision(5, 2)
                        .HasColumnType("decimal(5,2)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("Description")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<decimal?>("ExpectedValue")
                        .HasPrecision(12, 4)
                        .HasColumnType("decimal(18,2)");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsRecurring")
                        .HasColumnType("boolean");

                    b.Property<int>("MeterReadingId")
                        .HasColumnType("integer");

                    b.Property<string>("Notes")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<int?>("RelatedAnomalyId")
                        .HasColumnType("integer");

                    b.Property<bool>("RequiresFieldVisit")
                        .HasColumnType("boolean");

                    b.Property<string>("Resolution")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<string>("ResolutionType")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("ResolvedBy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime?>("ResolvedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Severity")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<decimal?>("Variance")
                        .HasPrecision(12, 4)
                        .HasColumnType("decimal(18,2)");

                    b.HasKey("Id");

                    b.HasIndex("AnomalyType");

                    b.HasIndex("AssignedTo");

                    b.HasIndex("AssignedUserId");

                    b.HasIndex("CreatedAt");

                    b.HasIndex("IsDeleted");

                    b.HasIndex("IsRecurring");

                    b.HasIndex("MeterReadingId");

                    b.HasIndex("RelatedAnomalyId");

                    b.HasIndex("ResolutionType");

                    b.HasIndex("ResolvedBy");

                    b.HasIndex("Severity");

                    b.HasIndex("Status");

                    b.ToTable("ReadingAnomalies");
                });

            modelBuilder.Entity("WaterMeterManagement.Models.ReadingPhoto", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CapturedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CloudflareUrl")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("FileName")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("FilePath")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<long>("FileSizeBytes")
                        .HasColumnType("bigint");

                    b.Property<bool>("HasOCR")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsOverridden")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsProcessed")
                        .HasColumnType("boolean");

                    b.Property<decimal?>("Latitude")
                        .HasColumnType("decimal(10,8)");

                    b.Property<decimal?>("Longitude")
                        .HasColumnType("decimal(11,8)");

                    b.Property<string>("MimeType")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("OCRStatus")
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<decimal?>("OcrConfidence")
                        .HasPrecision(5, 2)
                        .HasColumnType("decimal(5,2)");

                    b.Property<string>("OcrResult")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("OriginalFileName")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("OverriddenBy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime?>("OverriddenDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("OverrideReason")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("PhotoType")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<string>("ProcessingNotes")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<decimal?>("QualityScore")
                        .HasPrecision(3, 2)
                        .HasColumnType("decimal(3,2)");

                    b.Property<string>("QualityStatus")
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<int>("ReadingId")
                        .HasColumnType("integer");

                    b.Property<string>("SyncError")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("SyncStatus")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<string>("ThumbnailUrl")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime>("UploadTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Uuid")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.HasKey("Id");

                    b.HasIndex("CreatedAt");

                    b.HasIndex("HasOCR");

                    b.HasIndex("IsDeleted");

                    b.HasIndex("IsOverridden");

                    b.HasIndex("IsProcessed");

                    b.HasIndex("OCRStatus");

                    b.HasIndex("QualityStatus");

                    b.HasIndex("ReadingId");

                    b.ToTable("ReadingPhotos");
                });

            modelBuilder.Entity("WaterMeterManagement.Models.ReadingValidationRule", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("ActionOnViolation")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<bool>("AutoCorrect")
                        .HasColumnType("boolean");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<decimal?>("CriticalThreshold")
                        .HasPrecision(10, 2)
                        .HasColumnType("decimal(10,2)");

                    b.Property<string>("CustomerType")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<decimal?>("EffectivenessScore")
                        .HasPrecision(5, 2)
                        .HasColumnType("decimal(5,2)");

                    b.Property<decimal?>("ErrorThreshold")
                        .HasPrecision(10, 2)
                        .HasColumnType("decimal(10,2)");

                    b.Property<int>("FalsePositives")
                        .HasColumnType("integer");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastReviewDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<decimal?>("MaxToleranceValue")
                        .HasPrecision(10, 2)
                        .HasColumnType("decimal(10,2)");

                    b.Property<string>("MeterType")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<decimal?>("MinToleranceValue")
                        .HasPrecision(10, 2)
                        .HasColumnType("decimal(10,2)");

                    b.Property<string>("NotificationRecipients")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<int>("Priority")
                        .HasColumnType("integer");

                    b.Property<string>("ReviewedBy")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("RuleConfiguration")
                        .HasColumnType("text");

                    b.Property<string>("RuleName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("RuleType")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<bool>("SeasonalAdjustment")
                        .HasColumnType("boolean");

                    b.Property<decimal?>("SummerMultiplier")
                        .HasPrecision(5, 2)
                        .HasColumnType("decimal(5,2)");

                    b.Property<int?>("TimeWindowDays")
                        .HasColumnType("integer");

                    b.Property<decimal?>("TolerancePercentage")
                        .HasPrecision(5, 2)
                        .HasColumnType("decimal(5,2)");

                    b.Property<int>("TotalApplications")
                        .HasColumnType("integer");

                    b.Property<int>("TruePositives")
                        .HasColumnType("integer");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<decimal?>("WarningThreshold")
                        .HasPrecision(10, 2)
                        .HasColumnType("decimal(10,2)");

                    b.Property<decimal?>("WinterMultiplier")
                        .HasPrecision(5, 2)
                        .HasColumnType("decimal(5,2)");

                    b.Property<string>("Zone")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.HasKey("Id");

                    b.HasIndex("CreatedAt");

                    b.HasIndex("CustomerType");

                    b.HasIndex("IsActive");

                    b.HasIndex("IsDeleted");

                    b.HasIndex("MeterType");

                    b.HasIndex("Priority");

                    b.HasIndex("RuleName");

                    b.HasIndex("RuleType");

                    b.HasIndex("Zone");

                    b.ToTable("ReadingValidationRules");
                });

            modelBuilder.Entity("WaterMeterManagement.Models.Role", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("Name")
                        .IsUnique();

                    b.ToTable("Roles");
                });

            modelBuilder.Entity("WaterMeterManagement.Models.RolePermission", b =>
                {
                    b.Property<int>("RoleId")
                        .HasColumnType("integer");

                    b.Property<int>("PermissionId")
                        .HasColumnType("integer");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("Id")
                        .HasColumnType("integer");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("RoleId", "PermissionId");

                    b.HasIndex("PermissionId");

                    b.ToTable("RolePermissions");
                });

            modelBuilder.Entity("WaterMeterManagement.Models.Route", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Area")
                        .HasColumnType("text");

                    b.Property<string>("AssignedTo")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<int?>("AssignedUserId")
                        .HasColumnType("integer");

                    b.Property<int?>("AverageCompletionTime")
                        .HasPrecision(8, 2)
                        .HasColumnType("integer");

                    b.Property<string>("BackupAssignee")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int?>("DefaultUserId")
                        .HasColumnType("integer");

                    b.Property<string>("Description")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<decimal?>("DifficultyRating")
                        .HasPrecision(3, 1)
                        .HasColumnType("decimal(3,2)");

                    b.Property<string>("EndAddress")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<decimal?>("EndLatitude")
                        .HasPrecision(10, 6)
                        .HasColumnType("decimal(10,6)");

                    b.Property<decimal?>("EndLongitude")
                        .HasPrecision(10, 6)
                        .HasColumnType("decimal(10,6)");

                    b.Property<decimal?>("EstimatedDistance")
                        .HasPrecision(8, 2)
                        .HasColumnType("decimal(8,2)");

                    b.Property<int?>("EstimatedDuration")
                        .HasColumnType("integer");

                    b.Property<decimal?>("EstimatedHours")
                        .HasColumnType("numeric");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsTemplate")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastOptimized")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("LastSyncDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("MeterCount")
                        .HasColumnType("integer");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Notes")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<string>("OptimizationMethod")
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<string>("OptimizedBy")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("ReadingFrequency")
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<string>("RouteGeometry")
                        .HasColumnType("text");

                    b.Property<string>("RouteName")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("RouteType")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<string>("Source")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<string>("StartAddress")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<decimal?>("StartLatitude")
                        .HasPrecision(10, 6)
                        .HasColumnType("decimal(10,6)");

                    b.Property<decimal?>("StartLongitude")
                        .HasPrecision(10, 6)
                        .HasColumnType("decimal(10,6)");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<string>("SubArea")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("TemplateCategory")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<int?>("TotalMeters")
                        .HasColumnType("integer");

                    b.Property<string>("Township")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Zone")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("AssignedTo");

                    b.HasIndex("AssignedUserId");

                    b.HasIndex("DefaultUserId");

                    b.HasIndex("IsTemplate");

                    b.HasIndex("Name");

                    b.HasIndex("Status");

                    b.HasIndex("TemplateCategory");

                    b.HasIndex("Zone");

                    b.ToTable("Routes");
                });

            modelBuilder.Entity("WaterMeterManagement.Models.RouteWaypoint", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("AccessDifficulty")
                        .HasMaxLength(20)
                        .HasPrecision(3, 1)
                        .HasColumnType("character varying(20)");

                    b.Property<string>("Address")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<decimal?>("DistanceFromPrevious")
                        .HasPrecision(8, 2)
                        .HasColumnType("decimal(8,2)");

                    b.Property<int?>("EstimatedDuration")
                        .HasColumnType("integer");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<decimal?>("Latitude")
                        .HasPrecision(10, 6)
                        .HasColumnType("decimal(10,6)");

                    b.Property<decimal?>("Longitude")
                        .HasPrecision(10, 6)
                        .HasColumnType("decimal(10,6)");

                    b.Property<string>("Notes")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<bool>("RequiresSpecialEquipment")
                        .HasColumnType("boolean");

                    b.Property<int>("RouteId")
                        .HasColumnType("integer");

                    b.Property<int>("SequenceOrder")
                        .HasColumnType("integer");

                    b.Property<string>("SpecialInstructions")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<int?>("TravelTimeFromPrevious")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("WaterMeterId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("WaterMeterId");

                    b.HasIndex("RouteId", "SequenceOrder");

                    b.ToTable("RouteWaypoints");
                });

            modelBuilder.Entity("WaterMeterManagement.Models.TaskAssignment", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime?>("AcceptedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("AssignedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime>("AssignedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("AssignmentType")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("Reason")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<DateTime?>("RejectedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int?>("RouteId")
                        .HasColumnType("integer");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<int>("TaskId")
                        .HasColumnType("integer");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("UserId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("AssignedBy");

                    b.HasIndex("AssignedDate");

                    b.HasIndex("AssignmentType");

                    b.HasIndex("IsDeleted");

                    b.HasIndex("RouteId");

                    b.HasIndex("Status");

                    b.HasIndex("UserId");

                    b.HasIndex("TaskId", "UserId");

                    b.ToTable("TaskAssignments");
                });

            modelBuilder.Entity("WaterMeterManagement.Models.TaskHistory", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Action")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime>("ChangedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ChangedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("NewValue")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("OldValue")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("Reason")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<int>("TaskId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("Action");

                    b.HasIndex("ChangedAt");

                    b.HasIndex("ChangedBy");

                    b.HasIndex("IsDeleted");

                    b.HasIndex("TaskId", "ChangedAt");

                    b.ToTable("TaskHistories");
                });

            modelBuilder.Entity("WaterMeterManagement.Models.User", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreatedDate")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasDefaultValueSql("NOW()");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("EmployeeNo")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("FinCoCode")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("FullName")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<bool>("IsAuthenticated")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastLogin")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("MobilePhone")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<int>("PersonId")
                        .HasColumnType("integer");

                    b.Property<string>("ProfitCentreCode")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime>("UpdatedDate")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasDefaultValueSql("NOW()");

                    b.Property<string>("Username")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.HasKey("Id");

                    b.HasIndex("Username")
                        .IsUnique();

                    b.ToTable("Users");
                });

            modelBuilder.Entity("WaterMeterManagement.Models.UserRole", b =>
                {
                    b.Property<int>("UserId")
                        .HasColumnType("integer");

                    b.Property<int>("RoleId")
                        .HasColumnType("integer");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("Id")
                        .HasColumnType("integer");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("UserId", "RoleId");

                    b.HasIndex("RoleId");

                    b.ToTable("UserRoles");
                });

            modelBuilder.Entity("WaterMeterManagement.Models.WaterMeter", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("AccountNumber")
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<string>("Address")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("AssetId")
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<string>("AssignedRoute")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<int?>("BatteryLevel")
                        .HasColumnType("integer");

                    b.Property<string>("BookNumber")
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<string>("Brand")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<bool>("CantRead")
                        .HasColumnType("boolean");

                    b.Property<string>("Comments")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<string>("CommunicationMethod")
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<string>("Condition")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("CustomerCode")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("CustomerName")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime?>("DateOfRead")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("InstallDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastMaintenanceDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<decimal?>("LastRead")
                        .HasPrecision(12, 2)
                        .HasColumnType("decimal(12,2)");

                    b.Property<decimal?>("LastReading")
                        .HasPrecision(12, 4)
                        .HasColumnType("decimal(12,4)");

                    b.Property<DateTime?>("LastReadingDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("LastSyncDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<decimal?>("Latitude")
                        .HasPrecision(10, 6)
                        .HasColumnType("decimal(10,6)");

                    b.Property<string>("Location")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<decimal?>("Longitude")
                        .HasPrecision(10, 6)
                        .HasColumnType("decimal(10,6)");

                    b.Property<string>("MeterId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("MeterNumber")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("MeterSize")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("MeterType")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("Model")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime?>("NextMaintenanceDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Notes")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("PropertyId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<decimal?>("Read")
                        .HasPrecision(12, 2)
                        .HasColumnType("decimal(12,2)");

                    b.Property<decimal?>("RecentChange")
                        .HasPrecision(12, 2)
                        .HasColumnType("decimal(12,2)");

                    b.Property<string>("RoadName")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<int?>("RoadNumber")
                        .HasColumnType("integer");

                    b.Property<int?>("RouteId")
                        .HasColumnType("integer");

                    b.Property<int?>("RouteSequence")
                        .HasColumnType("integer");

                    b.Property<string>("SerialNumber")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("Source")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<string>("SubArea")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Subd")
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)");

                    b.Property<string>("SyncStatus")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<string>("Township")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<int>("Unit")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("AccountNumber");

                    b.HasIndex("AssetId");

                    b.HasIndex("CustomerCode");

                    b.HasIndex("RouteId");

                    b.HasIndex("SerialNumber")
                        .IsUnique();

                    b.HasIndex("Status");

                    b.HasIndex("SyncStatus");

                    b.HasIndex("Township");

                    b.ToTable("WaterMeters");
                });

            modelBuilder.Entity("WaterMeterManagement.Models.WorkPackage", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<decimal?>("ActualCost")
                        .HasPrecision(10, 2)
                        .HasColumnType("decimal(10,2)");

                    b.Property<DateTime?>("ActualEndDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<decimal?>("ActualHours")
                        .HasPrecision(8, 2)
                        .HasColumnType("decimal(8,2)");

                    b.Property<DateTime?>("ActualStartDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("AmsImportBatch")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime?>("AmsImportDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("AssignedTeam")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<int>("CompletedMeters")
                        .HasColumnType("integer");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<decimal?>("EstimatedCost")
                        .HasPrecision(10, 2)
                        .HasColumnType("decimal(10,2)");

                    b.Property<decimal>("EstimatedHours")
                        .HasPrecision(8, 2)
                        .HasColumnType("decimal(8,2)");

                    b.Property<string>("Frequency")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<int?>("FrequencyTemplateId")
                        .HasColumnType("integer");

                    b.Property<string>("Instructions")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsRecurring")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsTemplate")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastExecuted")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime?>("NextExecution")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Notes")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<string>("PackageType")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<DateTime>("PlannedEndDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime>("PlannedStartDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Priority")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<decimal>("ProgressPercentage")
                        .HasPrecision(5, 2)
                        .HasColumnType("decimal(5,2)");

                    b.Property<int?>("RecurrenceInterval")
                        .HasColumnType("integer");

                    b.Property<string>("RecurrencePattern")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("ServiceArea")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Source")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<string>("SubArea")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("TemplateCategory")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<int>("TotalMeters")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.HasKey("Id");

                    b.HasIndex("CreatedAt");

                    b.HasIndex("CreatedBy");

                    b.HasIndex("Frequency");

                    b.HasIndex("FrequencyTemplateId");

                    b.HasIndex("IsDeleted");

                    b.HasIndex("IsRecurring");

                    b.HasIndex("IsTemplate");

                    b.HasIndex("Name");

                    b.HasIndex("PackageType");

                    b.HasIndex("PlannedEndDate");

                    b.HasIndex("PlannedStartDate");

                    b.HasIndex("Priority");

                    b.HasIndex("ServiceArea");

                    b.HasIndex("Source");

                    b.HasIndex("Status");

                    b.ToTable("WorkPackages");
                });

            modelBuilder.Entity("WaterMeterManagement.Models.WorkPackageAssignment", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime?>("AcceptedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("AssignedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime>("AssignedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int?>("AssignedMeterCount")
                        .HasColumnType("integer");

                    b.Property<string>("AssignmentType")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<DateTime?>("CompletedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("CompletedMeterCount")
                        .HasColumnType("integer");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<bool>("EmailNotificationSent")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("EmailSentDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("EquipmentRequirements")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<DateTime?>("ExpectedCompletionDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("ExpectedStartDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Feedback")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastActivityDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Priority")
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<bool>("PushNotificationSent")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("PushNotificationSentDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("RatedBy")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime?>("RatedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int?>("Rating")
                        .HasColumnType("integer");

                    b.Property<string>("Reason")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<DateTime?>("RejectedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("RejectionReason")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<bool>("RequiresSupervision")
                        .HasColumnType("boolean");

                    b.Property<string>("SkillRequirements")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("SpecialInstructions")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<DateTime?>("StartedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<int?>("SupervisorId")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("UserId")
                        .HasColumnType("integer");

                    b.Property<int>("WorkPackageId")
                        .HasColumnType("integer");

                    b.Property<int>("WorkloadWeight")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("AssignedBy");

                    b.HasIndex("AssignedDate");

                    b.HasIndex("AssignmentType");

                    b.HasIndex("CreatedAt");

                    b.HasIndex("IsDeleted");

                    b.HasIndex("Priority");

                    b.HasIndex("RequiresSupervision");

                    b.HasIndex("Status");

                    b.HasIndex("SupervisorId");

                    b.HasIndex("UserId");

                    b.HasIndex("WorkPackageId", "UserId")
                        .IsUnique();

                    b.ToTable("WorkPackageAssignments");
                });

            modelBuilder.Entity("WaterMeterManagement.Models.WorkPackageHistory", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Action")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("AdditionalData")
                        .HasColumnType("text");

                    b.Property<DateTime>("ChangedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ChangedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Description")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("FieldName")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("IpAddress")
                        .HasMaxLength(45)
                        .HasColumnType("character varying(45)");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("NewValue")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("Notes")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<string>("OldValue")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("Source")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("UserAgent")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<int>("WorkPackageId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("Action");

                    b.HasIndex("ChangedAt");

                    b.HasIndex("ChangedBy");

                    b.HasIndex("IsDeleted");

                    b.HasIndex("Source");

                    b.HasIndex("WorkPackageId", "ChangedAt");

                    b.ToTable("WorkPackageHistories");
                });

            modelBuilder.Entity("WaterMeterManagement.Models.WorkPackageItem", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime?>("ActualDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int?>("ActualMinutes")
                        .HasColumnType("integer");

                    b.Property<string>("AssignedTo")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<int?>("AssignedUserId")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("CompletedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CompletedBy")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<decimal?>("DifficultyRating")
                        .HasPrecision(2, 1)
                        .HasColumnType("decimal(2,1)");

                    b.Property<int?>("EstimatedMinutes")
                        .HasColumnType("integer");

                    b.Property<string>("FailureReason")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("LastReading")
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<decimal?>("Latitude")
                        .HasPrecision(10, 6)
                        .HasColumnType("decimal(10,6)");

                    b.Property<decimal?>("Longitude")
                        .HasPrecision(10, 6)
                        .HasColumnType("decimal(10,6)");

                    b.Property<int>("MaxRetries")
                        .HasColumnType("integer");

                    b.Property<int>("MeterId")
                        .HasColumnType("integer");

                    b.Property<string>("Notes")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("Priority")
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<bool>("RequiresSpecialHandling")
                        .HasColumnType("boolean");

                    b.Property<int>("RetryCount")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("ScheduledDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("SequenceOrder")
                        .HasColumnType("integer");

                    b.Property<string>("ServiceAddress")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("SkipReason")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("SpecialHandlingReason")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("SpecialInstructions")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("WorkPackageId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("AssignedTo");

                    b.HasIndex("AssignedUserId");

                    b.HasIndex("CreatedAt");

                    b.HasIndex("IsDeleted");

                    b.HasIndex("MeterId");

                    b.HasIndex("Priority");

                    b.HasIndex("RequiresSpecialHandling");

                    b.HasIndex("ScheduledDate");

                    b.HasIndex("Status");

                    b.HasIndex("WorkPackageId", "MeterId")
                        .IsUnique();

                    b.HasIndex("WorkPackageId", "SequenceOrder");

                    b.ToTable("WorkPackageItems");
                });

            modelBuilder.Entity("WaterMeterManagement.Models.WorkTask", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<decimal?>("ActualHours")
                        .HasPrecision(5, 2)
                        .HasColumnType("decimal(5,2)");

                    b.Property<string>("AssignedTo")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<int?>("AssignedUserId")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("CompletedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("CurrentReading")
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<string>("Description")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<DateTime?>("DueDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<decimal?>("EstimatedHours")
                        .HasPrecision(5, 2)
                        .HasColumnType("decimal(5,2)");

                    b.Property<string>("FailureReason")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("Instructions")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("LastReading")
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<decimal?>("Latitude")
                        .HasPrecision(10, 6)
                        .HasColumnType("decimal(10,6)");

                    b.Property<string>("Location")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<decimal?>("Longitude")
                        .HasPrecision(10, 6)
                        .HasColumnType("decimal(10,6)");

                    b.Property<int?>("MeterId")
                        .HasColumnType("integer");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("Notes")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<string>("Priority")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<int>("ProgressPercentage")
                        .HasColumnType("integer");

                    b.Property<string>("ReadingPhotoUrl")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<int?>("RouteId")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("ScheduledDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ServiceAddress")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("SkipReason")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<DateTime?>("StartDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("Type")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int?>("WorkPackageId")
                        .HasColumnType("integer");

                    b.Property<int?>("WorkPackageItemId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("AssignedTo");

                    b.HasIndex("AssignedUserId");

                    b.HasIndex("CreatedAt");

                    b.HasIndex("CreatedBy");

                    b.HasIndex("DueDate");

                    b.HasIndex("IsDeleted");

                    b.HasIndex("MeterId");

                    b.HasIndex("Name");

                    b.HasIndex("Priority");

                    b.HasIndex("RouteId");

                    b.HasIndex("ScheduledDate");

                    b.HasIndex("ServiceAddress");

                    b.HasIndex("Status");

                    b.HasIndex("Type");

                    b.HasIndex("WorkPackageId");

                    b.HasIndex("WorkPackageItemId");

                    b.HasIndex("Latitude", "Longitude");

                    b.ToTable("Tasks");
                });

            modelBuilder.Entity("WaterMeterManagement.Models.AmisSyncError", b =>
                {
                    b.HasOne("WaterMeterManagement.Models.AmisSync", "AmisSync")
                        .WithMany()
                        .HasForeignKey("AmisSyncId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("WaterMeterManagement.Models.AmisSync", null)
                        .WithMany("SyncErrors")
                        .HasForeignKey("AmisSyncId1");

                    b.Navigation("AmisSync");
                });

            modelBuilder.Entity("WaterMeterManagement.Models.BaselineRecord", b =>
                {
                    b.HasOne("WaterMeterManagement.Models.WaterMeter", "WaterMeter")
                        .WithMany("BaselineRecords")
                        .HasForeignKey("MeterId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("WaterMeterManagement.Models.BaselineRecord", "PreviousBaseline")
                        .WithMany("SubsequentBaselines")
                        .HasForeignKey("PreviousBaselineId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("PreviousBaseline");

                    b.Navigation("WaterMeter");
                });

            modelBuilder.Entity("WaterMeterManagement.Models.Configuration", b =>
                {
                    b.HasOne("WaterMeterManagement.Models.Role", "Role")
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("WaterMeterManagement.Models.User", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("Role");

                    b.Navigation("User");
                });

            modelBuilder.Entity("WaterMeterManagement.Models.Menu", b =>
                {
                    b.HasOne("WaterMeterManagement.Models.Menu", "Parent")
                        .WithMany("Children")
                        .HasForeignKey("ParentId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("WaterMeterManagement.Models.Permission", "Permission")
                        .WithMany()
                        .HasForeignKey("PermissionId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("Parent");

                    b.Navigation("Permission");
                });

            modelBuilder.Entity("WaterMeterManagement.Models.MeterReading", b =>
                {
                    b.HasOne("WaterMeterManagement.Models.WaterMeter", "WaterMeter")
                        .WithMany("MeterReadings")
                        .HasForeignKey("MeterId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("WaterMeterManagement.Models.WorkTask", "Task")
                        .WithMany("Readings")
                        .HasForeignKey("TaskId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("WaterMeterManagement.Models.User", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("WaterMeterManagement.Models.User", "Validator")
                        .WithMany()
                        .HasForeignKey("ValidatedBy")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("WaterMeterManagement.Models.WorkPackageItem", null)
                        .WithMany("Readings")
                        .HasForeignKey("WorkPackageItemId");

                    b.Navigation("Task");

                    b.Navigation("User");

                    b.Navigation("Validator");

                    b.Navigation("WaterMeter");
                });

            modelBuilder.Entity("WaterMeterManagement.Models.OCRRecord", b =>
                {
                    b.HasOne("WaterMeterManagement.Models.MeterReading", "MeterReading")
                        .WithMany("OCRRecords")
                        .HasForeignKey("MeterReadingId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("WaterMeterManagement.Models.ReadingPhoto", "ReadingPhoto")
                        .WithMany("OCRRecords")
                        .HasForeignKey("ReadingPhotoId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.Navigation("MeterReading");

                    b.Navigation("ReadingPhoto");
                });

            modelBuilder.Entity("WaterMeterManagement.Models.ReadingAnomaly", b =>
                {
                    b.HasOne("WaterMeterManagement.Models.User", null)
                        .WithMany()
                        .HasForeignKey("AssignedUserId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("WaterMeterManagement.Models.MeterReading", "MeterReading")
                        .WithMany("Anomalies")
                        .HasForeignKey("MeterReadingId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("WaterMeterManagement.Models.ReadingAnomaly", "RelatedAnomaly")
                        .WithMany("ChildAnomalies")
                        .HasForeignKey("RelatedAnomalyId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("MeterReading");

                    b.Navigation("RelatedAnomaly");
                });

            modelBuilder.Entity("WaterMeterManagement.Models.ReadingPhoto", b =>
                {
                    b.HasOne("WaterMeterManagement.Models.MeterReading", "MeterReading")
                        .WithMany("Photos")
                        .HasForeignKey("ReadingId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("MeterReading");
                });

            modelBuilder.Entity("WaterMeterManagement.Models.RolePermission", b =>
                {
                    b.HasOne("WaterMeterManagement.Models.Permission", "Permission")
                        .WithMany("RolePermissions")
                        .HasForeignKey("PermissionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("WaterMeterManagement.Models.Role", "Role")
                        .WithMany("RolePermissions")
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Permission");

                    b.Navigation("Role");
                });

            modelBuilder.Entity("WaterMeterManagement.Models.Route", b =>
                {
                    b.HasOne("WaterMeterManagement.Models.User", null)
                        .WithMany()
                        .HasForeignKey("AssignedUserId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("WaterMeterManagement.Models.User", "DefaultUser")
                        .WithMany()
                        .HasForeignKey("DefaultUserId");

                    b.Navigation("DefaultUser");
                });

            modelBuilder.Entity("WaterMeterManagement.Models.RouteWaypoint", b =>
                {
                    b.HasOne("WaterMeterManagement.Models.Route", "Route")
                        .WithMany("RouteWaypoints")
                        .HasForeignKey("RouteId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("WaterMeterManagement.Models.WaterMeter", "WaterMeter")
                        .WithMany()
                        .HasForeignKey("WaterMeterId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Route");

                    b.Navigation("WaterMeter");
                });

            modelBuilder.Entity("WaterMeterManagement.Models.TaskAssignment", b =>
                {
                    b.HasOne("WaterMeterManagement.Models.Route", null)
                        .WithMany("TaskAssignments")
                        .HasForeignKey("RouteId");

                    b.HasOne("WaterMeterManagement.Models.WorkTask", "Task")
                        .WithMany("TaskAssignments")
                        .HasForeignKey("TaskId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("WaterMeterManagement.Models.User", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Task");

                    b.Navigation("User");
                });

            modelBuilder.Entity("WaterMeterManagement.Models.TaskHistory", b =>
                {
                    b.HasOne("WaterMeterManagement.Models.WorkTask", "Task")
                        .WithMany("TaskHistories")
                        .HasForeignKey("TaskId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Task");
                });

            modelBuilder.Entity("WaterMeterManagement.Models.UserRole", b =>
                {
                    b.HasOne("WaterMeterManagement.Models.Role", "Role")
                        .WithMany("UserRoles")
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("WaterMeterManagement.Models.User", "User")
                        .WithMany("UserRoles")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Role");

                    b.Navigation("User");
                });

            modelBuilder.Entity("WaterMeterManagement.Models.WaterMeter", b =>
                {
                    b.HasOne("WaterMeterManagement.Models.Route", "Route")
                        .WithMany("Meters")
                        .HasForeignKey("RouteId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.Navigation("Route");
                });

            modelBuilder.Entity("WaterMeterManagement.Models.WorkPackage", b =>
                {
                    b.HasOne("WaterMeterManagement.Models.FrequencyTemplate", null)
                        .WithMany("WorkPackages")
                        .HasForeignKey("FrequencyTemplateId");
                });

            modelBuilder.Entity("WaterMeterManagement.Models.WorkPackageAssignment", b =>
                {
                    b.HasOne("WaterMeterManagement.Models.User", "Supervisor")
                        .WithMany()
                        .HasForeignKey("SupervisorId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("WaterMeterManagement.Models.User", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("WaterMeterManagement.Models.WorkPackage", "WorkPackage")
                        .WithMany("Assignments")
                        .HasForeignKey("WorkPackageId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Supervisor");

                    b.Navigation("User");

                    b.Navigation("WorkPackage");
                });

            modelBuilder.Entity("WaterMeterManagement.Models.WorkPackageHistory", b =>
                {
                    b.HasOne("WaterMeterManagement.Models.WorkPackage", "WorkPackage")
                        .WithMany("Histories")
                        .HasForeignKey("WorkPackageId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("WorkPackage");
                });

            modelBuilder.Entity("WaterMeterManagement.Models.WorkPackageItem", b =>
                {
                    b.HasOne("WaterMeterManagement.Models.User", null)
                        .WithMany()
                        .HasForeignKey("AssignedUserId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("WaterMeterManagement.Models.WaterMeter", "Meter")
                        .WithMany()
                        .HasForeignKey("MeterId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("WaterMeterManagement.Models.WorkPackage", "WorkPackage")
                        .WithMany("Items")
                        .HasForeignKey("WorkPackageId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Meter");

                    b.Navigation("WorkPackage");
                });

            modelBuilder.Entity("WaterMeterManagement.Models.WorkTask", b =>
                {
                    b.HasOne("WaterMeterManagement.Models.User", "AssignedUser")
                        .WithMany()
                        .HasForeignKey("AssignedUserId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("WaterMeterManagement.Models.WaterMeter", "Meter")
                        .WithMany()
                        .HasForeignKey("MeterId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("WaterMeterManagement.Models.Route", "Route")
                        .WithMany()
                        .HasForeignKey("RouteId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("WaterMeterManagement.Models.WorkPackage", "WorkPackage")
                        .WithMany("Tasks")
                        .HasForeignKey("WorkPackageId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("WaterMeterManagement.Models.WorkPackageItem", "WorkPackageItem")
                        .WithMany("Tasks")
                        .HasForeignKey("WorkPackageItemId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.Navigation("AssignedUser");

                    b.Navigation("Meter");

                    b.Navigation("Route");

                    b.Navigation("WorkPackage");

                    b.Navigation("WorkPackageItem");
                });

            modelBuilder.Entity("WaterMeterManagement.Models.AmisSync", b =>
                {
                    b.Navigation("SyncErrors");
                });

            modelBuilder.Entity("WaterMeterManagement.Models.BaselineRecord", b =>
                {
                    b.Navigation("SubsequentBaselines");
                });

            modelBuilder.Entity("WaterMeterManagement.Models.FrequencyTemplate", b =>
                {
                    b.Navigation("WorkPackages");
                });

            modelBuilder.Entity("WaterMeterManagement.Models.Menu", b =>
                {
                    b.Navigation("Children");
                });

            modelBuilder.Entity("WaterMeterManagement.Models.MeterReading", b =>
                {
                    b.Navigation("Anomalies");

                    b.Navigation("OCRRecords");

                    b.Navigation("Photos");
                });

            modelBuilder.Entity("WaterMeterManagement.Models.Permission", b =>
                {
                    b.Navigation("RolePermissions");
                });

            modelBuilder.Entity("WaterMeterManagement.Models.ReadingAnomaly", b =>
                {
                    b.Navigation("ChildAnomalies");
                });

            modelBuilder.Entity("WaterMeterManagement.Models.ReadingPhoto", b =>
                {
                    b.Navigation("OCRRecords");
                });

            modelBuilder.Entity("WaterMeterManagement.Models.Role", b =>
                {
                    b.Navigation("RolePermissions");

                    b.Navigation("UserRoles");
                });

            modelBuilder.Entity("WaterMeterManagement.Models.Route", b =>
                {
                    b.Navigation("Meters");

                    b.Navigation("RouteWaypoints");

                    b.Navigation("TaskAssignments");
                });

            modelBuilder.Entity("WaterMeterManagement.Models.User", b =>
                {
                    b.Navigation("UserRoles");
                });

            modelBuilder.Entity("WaterMeterManagement.Models.WaterMeter", b =>
                {
                    b.Navigation("BaselineRecords");

                    b.Navigation("MeterReadings");
                });

            modelBuilder.Entity("WaterMeterManagement.Models.WorkPackage", b =>
                {
                    b.Navigation("Assignments");

                    b.Navigation("Histories");

                    b.Navigation("Items");

                    b.Navigation("Tasks");
                });

            modelBuilder.Entity("WaterMeterManagement.Models.WorkPackageItem", b =>
                {
                    b.Navigation("Readings");

                    b.Navigation("Tasks");
                });

            modelBuilder.Entity("WaterMeterManagement.Models.WorkTask", b =>
                {
                    b.Navigation("Readings");

                    b.Navigation("TaskAssignments");

                    b.Navigation("TaskHistories");
                });
#pragma warning restore 612, 618
        }
    }
}
