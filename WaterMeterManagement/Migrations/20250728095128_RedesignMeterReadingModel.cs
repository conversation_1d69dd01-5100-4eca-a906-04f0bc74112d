﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace WaterMeterManagement.Migrations
{
    /// <inheritdoc />
    public partial class RedesignMeterReadingModel : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_MeterReadings_Tasks_WorkTaskId",
                table: "MeterReadings");

            migrationBuilder.DropIndex(
                name: "IX_MeterReadings_MeterId_ReadingDate",
                table: "MeterReadings");

            migrationBuilder.DropColumn(
                name: "Consumption",
                table: "MeterReadings");

            migrationBuilder.DropColumn(
                name: "MeterNumber",
                table: "MeterReadings");

            migrationBuilder.DropColumn(
                name: "PreviousReading",
                table: "MeterReadings");

            migrationBuilder.DropColumn(
                name: "ProcessedBy",
                table: "MeterReadings");

            migrationBuilder.DropColumn(
                name: "ReadBy",
                table: "MeterReadings");

            migrationBuilder.RenameColumn(
                name: "WorkTaskId",
                table: "MeterReadings",
                newName: "ValidatedBy");

            migrationBuilder.RenameColumn(
                name: "QualityScore",
                table: "MeterReadings",
                newName: "GpsAccuracy");

            migrationBuilder.RenameColumn(
                name: "ProcessingNotes",
                table: "MeterReadings",
                newName: "ValidationComments");

            migrationBuilder.RenameColumn(
                name: "ProcessedDate",
                table: "MeterReadings",
                newName: "ValidationDate");

            migrationBuilder.RenameColumn(
                name: "HasPhoto",
                table: "MeterReadings",
                newName: "IsValidated");

            migrationBuilder.RenameIndex(
                name: "IX_MeterReadings_WorkTaskId",
                table: "MeterReadings",
                newName: "IX_MeterReadings_ValidatedBy");

            migrationBuilder.AlterColumn<string>(
                name: "ReadingType",
                table: "MeterReadings",
                type: "character varying(20)",
                maxLength: 20,
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "character varying(20)",
                oldMaxLength: 20,
                oldNullable: true);

            migrationBuilder.AlterColumn<decimal>(
                name: "OCRConfidence",
                table: "MeterReadings",
                type: "numeric(5,2)",
                precision: 5,
                scale: 2,
                nullable: true,
                oldClrType: typeof(decimal),
                oldType: "numeric(8,2)",
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "DataSource",
                table: "MeterReadings",
                type: "character varying(20)",
                maxLength: 20,
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "character varying(20)",
                oldMaxLength: 20,
                oldNullable: true);

            migrationBuilder.AddColumn<string>(
                name: "AnomalyReason",
                table: "MeterReadings",
                type: "character varying(200)",
                maxLength: 200,
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "CantRead",
                table: "MeterReadings",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<string>(
                name: "CantReadReason",
                table: "MeterReadings",
                type: "character varying(200)",
                maxLength: 200,
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "IsAnomalous",
                table: "MeterReadings",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<decimal>(
                name: "Latitude",
                table: "MeterReadings",
                type: "numeric(10,6)",
                precision: 10,
                scale: 6,
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "Longitude",
                table: "MeterReadings",
                type: "numeric(10,6)",
                precision: 10,
                scale: 6,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ReadingMethod",
                table: "MeterReadings",
                type: "character varying(20)",
                maxLength: 20,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<int>(
                name: "TaskId",
                table: "MeterReadings",
                type: "integer",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "UserId",
                table: "MeterReadings",
                type: "integer",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.CreateIndex(
                name: "IX_MeterReadings_CantRead",
                table: "MeterReadings",
                column: "CantRead");

            migrationBuilder.CreateIndex(
                name: "IX_MeterReadings_CreatedAt",
                table: "MeterReadings",
                column: "CreatedAt");

            migrationBuilder.CreateIndex(
                name: "IX_MeterReadings_DataSource",
                table: "MeterReadings",
                column: "DataSource");

            migrationBuilder.CreateIndex(
                name: "IX_MeterReadings_IsAnomalous",
                table: "MeterReadings",
                column: "IsAnomalous");

            migrationBuilder.CreateIndex(
                name: "IX_MeterReadings_IsDeleted",
                table: "MeterReadings",
                column: "IsDeleted");

            migrationBuilder.CreateIndex(
                name: "IX_MeterReadings_IsValidated",
                table: "MeterReadings",
                column: "IsValidated");

            migrationBuilder.CreateIndex(
                name: "IX_MeterReadings_MeterId",
                table: "MeterReadings",
                column: "MeterId");

            migrationBuilder.CreateIndex(
                name: "IX_MeterReadings_ReadingMethod",
                table: "MeterReadings",
                column: "ReadingMethod");

            migrationBuilder.CreateIndex(
                name: "IX_MeterReadings_ReadingType",
                table: "MeterReadings",
                column: "ReadingType");

            migrationBuilder.CreateIndex(
                name: "IX_MeterReadings_Status",
                table: "MeterReadings",
                column: "Status");

            migrationBuilder.CreateIndex(
                name: "IX_MeterReadings_TaskId",
                table: "MeterReadings",
                column: "TaskId");

            migrationBuilder.CreateIndex(
                name: "IX_MeterReadings_UserId",
                table: "MeterReadings",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_MeterReadings_ValidationStatus",
                table: "MeterReadings",
                column: "ValidationStatus");

            migrationBuilder.AddForeignKey(
                name: "FK_MeterReadings_Tasks_TaskId",
                table: "MeterReadings",
                column: "TaskId",
                principalTable: "Tasks",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_MeterReadings_Users_UserId",
                table: "MeterReadings",
                column: "UserId",
                principalTable: "Users",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_MeterReadings_Users_ValidatedBy",
                table: "MeterReadings",
                column: "ValidatedBy",
                principalTable: "Users",
                principalColumn: "Id",
                onDelete: ReferentialAction.SetNull);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_MeterReadings_Tasks_TaskId",
                table: "MeterReadings");

            migrationBuilder.DropForeignKey(
                name: "FK_MeterReadings_Users_UserId",
                table: "MeterReadings");

            migrationBuilder.DropForeignKey(
                name: "FK_MeterReadings_Users_ValidatedBy",
                table: "MeterReadings");

            migrationBuilder.DropIndex(
                name: "IX_MeterReadings_CantRead",
                table: "MeterReadings");

            migrationBuilder.DropIndex(
                name: "IX_MeterReadings_CreatedAt",
                table: "MeterReadings");

            migrationBuilder.DropIndex(
                name: "IX_MeterReadings_DataSource",
                table: "MeterReadings");

            migrationBuilder.DropIndex(
                name: "IX_MeterReadings_IsAnomalous",
                table: "MeterReadings");

            migrationBuilder.DropIndex(
                name: "IX_MeterReadings_IsDeleted",
                table: "MeterReadings");

            migrationBuilder.DropIndex(
                name: "IX_MeterReadings_IsValidated",
                table: "MeterReadings");

            migrationBuilder.DropIndex(
                name: "IX_MeterReadings_MeterId",
                table: "MeterReadings");

            migrationBuilder.DropIndex(
                name: "IX_MeterReadings_ReadingMethod",
                table: "MeterReadings");

            migrationBuilder.DropIndex(
                name: "IX_MeterReadings_ReadingType",
                table: "MeterReadings");

            migrationBuilder.DropIndex(
                name: "IX_MeterReadings_Status",
                table: "MeterReadings");

            migrationBuilder.DropIndex(
                name: "IX_MeterReadings_TaskId",
                table: "MeterReadings");

            migrationBuilder.DropIndex(
                name: "IX_MeterReadings_UserId",
                table: "MeterReadings");

            migrationBuilder.DropIndex(
                name: "IX_MeterReadings_ValidationStatus",
                table: "MeterReadings");

            migrationBuilder.DropColumn(
                name: "AnomalyReason",
                table: "MeterReadings");

            migrationBuilder.DropColumn(
                name: "CantRead",
                table: "MeterReadings");

            migrationBuilder.DropColumn(
                name: "CantReadReason",
                table: "MeterReadings");

            migrationBuilder.DropColumn(
                name: "IsAnomalous",
                table: "MeterReadings");

            migrationBuilder.DropColumn(
                name: "Latitude",
                table: "MeterReadings");

            migrationBuilder.DropColumn(
                name: "Longitude",
                table: "MeterReadings");

            migrationBuilder.DropColumn(
                name: "ReadingMethod",
                table: "MeterReadings");

            migrationBuilder.DropColumn(
                name: "TaskId",
                table: "MeterReadings");

            migrationBuilder.DropColumn(
                name: "UserId",
                table: "MeterReadings");

            migrationBuilder.RenameColumn(
                name: "ValidationDate",
                table: "MeterReadings",
                newName: "ProcessedDate");

            migrationBuilder.RenameColumn(
                name: "ValidationComments",
                table: "MeterReadings",
                newName: "ProcessingNotes");

            migrationBuilder.RenameColumn(
                name: "ValidatedBy",
                table: "MeterReadings",
                newName: "WorkTaskId");

            migrationBuilder.RenameColumn(
                name: "IsValidated",
                table: "MeterReadings",
                newName: "HasPhoto");

            migrationBuilder.RenameColumn(
                name: "GpsAccuracy",
                table: "MeterReadings",
                newName: "QualityScore");

            migrationBuilder.RenameIndex(
                name: "IX_MeterReadings_ValidatedBy",
                table: "MeterReadings",
                newName: "IX_MeterReadings_WorkTaskId");

            migrationBuilder.AlterColumn<string>(
                name: "ReadingType",
                table: "MeterReadings",
                type: "character varying(20)",
                maxLength: 20,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "character varying(20)",
                oldMaxLength: 20);

            migrationBuilder.AlterColumn<decimal>(
                name: "OCRConfidence",
                table: "MeterReadings",
                type: "numeric(8,2)",
                nullable: true,
                oldClrType: typeof(decimal),
                oldType: "numeric(5,2)",
                oldPrecision: 5,
                oldScale: 2,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "DataSource",
                table: "MeterReadings",
                type: "character varying(20)",
                maxLength: 20,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "character varying(20)",
                oldMaxLength: 20);

            migrationBuilder.AddColumn<decimal>(
                name: "Consumption",
                table: "MeterReadings",
                type: "numeric(12,4)",
                precision: 12,
                scale: 4,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "MeterNumber",
                table: "MeterReadings",
                type: "character varying(50)",
                maxLength: 50,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<decimal>(
                name: "PreviousReading",
                table: "MeterReadings",
                type: "numeric(12,4)",
                precision: 12,
                scale: 4,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ProcessedBy",
                table: "MeterReadings",
                type: "character varying(50)",
                maxLength: 50,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ReadBy",
                table: "MeterReadings",
                type: "character varying(50)",
                maxLength: 50,
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_MeterReadings_MeterId_ReadingDate",
                table: "MeterReadings",
                columns: new[] { "MeterId", "ReadingDate" });

            migrationBuilder.AddForeignKey(
                name: "FK_MeterReadings_Tasks_WorkTaskId",
                table: "MeterReadings",
                column: "WorkTaskId",
                principalTable: "Tasks",
                principalColumn: "Id");
        }
    }
}
