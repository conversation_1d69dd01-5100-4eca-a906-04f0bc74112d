﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace WaterMeterManagement.Migrations
{
    /// <inheritdoc />
    public partial class UpdateReadingPhotoFields : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_ReadingPhotos_MeterReadings_MeterReadingId",
                table: "ReadingPhotos");

            migrationBuilder.DropColumn(
                name: "FileSize",
                table: "ReadingPhotos");

            migrationBuilder.DropColumn(
                name: "FileType",
                table: "ReadingPhotos");

            migrationBuilder.RenameColumn(
                name: "OCRResult",
                table: "ReadingPhotos",
                newName: "OcrResult");

            migrationBuilder.RenameColumn(
                name: "OCRConfidence",
                table: "ReadingPhotos",
                newName: "OcrConfidence");

            migrationBuilder.RenameColumn(
                name: "MeterReadingId",
                table: "ReadingPhotos",
                newName: "ReadingId");

            migrationBuilder.RenameIndex(
                name: "IX_ReadingPhotos_MeterReadingId",
                table: "ReadingPhotos",
                newName: "IX_ReadingPhotos_ReadingId");

            migrationBuilder.AlterColumn<string>(
                name: "FilePath",
                table: "ReadingPhotos",
                type: "character varying(1000)",
                maxLength: 1000,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "character varying(1000)",
                oldMaxLength: 1000);

            migrationBuilder.AddColumn<DateTime>(
                name: "CapturedAt",
                table: "ReadingPhotos",
                type: "timestamp with time zone",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AddColumn<string>(
                name: "CloudflareUrl",
                table: "ReadingPhotos",
                type: "character varying(1000)",
                maxLength: 1000,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<long>(
                name: "FileSizeBytes",
                table: "ReadingPhotos",
                type: "bigint",
                nullable: false,
                defaultValue: 0L);

            migrationBuilder.AddColumn<decimal>(
                name: "Latitude",
                table: "ReadingPhotos",
                type: "numeric(10,8)",
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "Longitude",
                table: "ReadingPhotos",
                type: "numeric(11,8)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "MimeType",
                table: "ReadingPhotos",
                type: "character varying(50)",
                maxLength: 50,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "OriginalFileName",
                table: "ReadingPhotos",
                type: "character varying(500)",
                maxLength: 500,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "PhotoType",
                table: "ReadingPhotos",
                type: "character varying(20)",
                maxLength: 20,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "SyncError",
                table: "ReadingPhotos",
                type: "character varying(500)",
                maxLength: 500,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "SyncStatus",
                table: "ReadingPhotos",
                type: "character varying(20)",
                maxLength: 20,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "ThumbnailUrl",
                table: "ReadingPhotos",
                type: "character varying(1000)",
                maxLength: 1000,
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "UploadTime",
                table: "ReadingPhotos",
                type: "timestamp with time zone",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AddColumn<string>(
                name: "Uuid",
                table: "ReadingPhotos",
                type: "character varying(50)",
                maxLength: 50,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddForeignKey(
                name: "FK_ReadingPhotos_MeterReadings_ReadingId",
                table: "ReadingPhotos",
                column: "ReadingId",
                principalTable: "MeterReadings",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_ReadingPhotos_MeterReadings_ReadingId",
                table: "ReadingPhotos");

            migrationBuilder.DropColumn(
                name: "CapturedAt",
                table: "ReadingPhotos");

            migrationBuilder.DropColumn(
                name: "CloudflareUrl",
                table: "ReadingPhotos");

            migrationBuilder.DropColumn(
                name: "FileSizeBytes",
                table: "ReadingPhotos");

            migrationBuilder.DropColumn(
                name: "Latitude",
                table: "ReadingPhotos");

            migrationBuilder.DropColumn(
                name: "Longitude",
                table: "ReadingPhotos");

            migrationBuilder.DropColumn(
                name: "MimeType",
                table: "ReadingPhotos");

            migrationBuilder.DropColumn(
                name: "OriginalFileName",
                table: "ReadingPhotos");

            migrationBuilder.DropColumn(
                name: "PhotoType",
                table: "ReadingPhotos");

            migrationBuilder.DropColumn(
                name: "SyncError",
                table: "ReadingPhotos");

            migrationBuilder.DropColumn(
                name: "SyncStatus",
                table: "ReadingPhotos");

            migrationBuilder.DropColumn(
                name: "ThumbnailUrl",
                table: "ReadingPhotos");

            migrationBuilder.DropColumn(
                name: "UploadTime",
                table: "ReadingPhotos");

            migrationBuilder.DropColumn(
                name: "Uuid",
                table: "ReadingPhotos");

            migrationBuilder.RenameColumn(
                name: "OcrResult",
                table: "ReadingPhotos",
                newName: "OCRResult");

            migrationBuilder.RenameColumn(
                name: "OcrConfidence",
                table: "ReadingPhotos",
                newName: "OCRConfidence");

            migrationBuilder.RenameColumn(
                name: "ReadingId",
                table: "ReadingPhotos",
                newName: "MeterReadingId");

            migrationBuilder.RenameIndex(
                name: "IX_ReadingPhotos_ReadingId",
                table: "ReadingPhotos",
                newName: "IX_ReadingPhotos_MeterReadingId");

            migrationBuilder.AlterColumn<string>(
                name: "FilePath",
                table: "ReadingPhotos",
                type: "character varying(1000)",
                maxLength: 1000,
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "character varying(1000)",
                oldMaxLength: 1000,
                oldNullable: true);

            migrationBuilder.AddColumn<string>(
                name: "FileSize",
                table: "ReadingPhotos",
                type: "character varying(50)",
                maxLength: 50,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "FileType",
                table: "ReadingPhotos",
                type: "character varying(20)",
                maxLength: 20,
                nullable: true);

            migrationBuilder.AddForeignKey(
                name: "FK_ReadingPhotos_MeterReadings_MeterReadingId",
                table: "ReadingPhotos",
                column: "MeterReadingId",
                principalTable: "MeterReadings",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
