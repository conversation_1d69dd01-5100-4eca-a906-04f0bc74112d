# Water Meter Management System - Backend API

## 🚀 项目简介

这是水表读数管理系统的后端API，使用ASP.NET Core 8.0开发，提供与移动端App的数据交互接口。

## 🏗️ 技术栈

- **.NET 8.0** - 应用框架
- **ASP.NET Core Web API** - Web API框架
- **Entity Framework Core** - ORM数据访问
- **SQL Server** - 数据库
- **JWT Bearer Authentication** - 身份验证
- **AutoMapper** - 对象映射
- **Serilog** - 结构化日志
- **Swagger/OpenAPI** - API文档

## 📋 主要功能

### 当前版本 (v1.0)
- ✅ **用户认证**: 集成Workbench API进行用户登录验证
- ✅ **JWT Token**: 生成和验证JWT访问令牌
- ✅ **用户管理**: 本地用户信息存储和管理
- ✅ **API文档**: 完整的Swagger文档支持

### 计划功能
- 🔄 **水表管理**: 水表设备信息管理
- 🔄 **读数记录**: 水表读数数据接收和存储
- 🔄 **任务分配**: 现场任务分配和管理
- 🔄 **数据同步**: 与AMIS/Magic系统数据同步
- 🔄 **报表导出**: Power BI集成和数据导出

## 🛠️ 开发环境设置

### 前置要求
- .NET 8.0 SDK
- SQL Server (LocalDB 或完整版)
- Visual Studio 2022 或 VS Code

### 安装和运行
```bash
# 克隆项目到本地
cd WaterMeterManagement

# 恢复NuGet包
dotnet restore

# 运行数据库迁移（首次运行会自动创建）
dotnet ef database update

# 启动开发服务器
dotnet run
```

### 访问地址
- **API基础地址**: `https://localhost:7000`
- **Swagger文档**: `https://localhost:7000` (开发环境默认首页)

## 📁 项目结构

```
WaterMeterManagement/
├── Controllers/          # API控制器
│   └── AuthController.cs
├── Models/              # 数据模型
│   └── User.cs
├── Services/            # 业务逻辑服务
│   ├── Interfaces/      # 服务接口
│   │   ├── IAuthService.cs
│   │   ├── IWorkbenchService.cs
│   │   ├── IMenuService.cs
│   │   └── IRoleService.cs
│   ├── AuthService.cs
│   ├── WorkbenchService.cs
│   ├── MenuService.cs
│   └── RoleService.cs
├── DTOs/               # 数据传输对象
│   ├── LoginRequestDto.cs
│   ├── LoginResponseDto.cs
│   └── WorkbenchLoginResponseDto.cs
├── Data/               # 数据访问层
│   └── ApplicationDbContext.cs
├── appsettings.json    # 应用配置
└── Program.cs          # 应用启动配置
```

## 🔐 API 接口

### 认证接口

#### 1. 用户登录
- **POST** `/api/auth/login`
- **说明**: 通过Workbench API验证用户凭证并返回JWT Token

**请求体**:
```json
{
  "username": "firstname.lastname",
  "password": "your_password",
  "rememberMe": false
}
```

**响应**:
```json
{
  "success": true,
  "message": "登录成功",
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "user": {
    "id": 1,
    "username": "firstname.lastname",
    "fullName": "Full Name",
    "personId": 12345,
    "finCoCode": "ABC",
    "isAuthenticated": true,
    "lastLogin": "2024-01-01T00:00:00Z"
  }
}
```

#### 2. 获取当前用户信息
- **GET** `/api/auth/me`
- **说明**: 获取当前登录用户的信息
- **认证**: 需要Bearer Token

#### 3. 测试受保护接口
- **GET** `/api/auth/test-protected`
- **说明**: 测试JWT认证是否正常工作
- **认证**: 需要Bearer Token

## ⚙️ 配置说明

### appsettings.json
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "数据库连接字符串"
  },
  "Jwt": {
    "SecretKey": "JWT密钥",
    "Issuer": "发行者",
    "Audience": "受众",
    "ExpirationDays": 30
  },
  "Workbench": {
    "BaseUrl": "Workbench API基础地址",
    "TimeoutSeconds": 10
  }
}
```

## 🔍 日志

项目使用Serilog进行结构化日志记录：
- **控制台输出**: 开发环境实时日志
- **文件输出**: `logs/watermeter-{date}.log`

## 🧪 测试

使用Swagger UI进行API测试：
1. 启动应用后访问 `https://localhost:7000`
2. 使用有效的Workbench凭证进行登录
3. 复制返回的token
4. 点击"Authorize"按钮，输入`Bearer {token}`
5. 测试受保护的API接口

## 🚧 开发状态

**当前版本**: v1.0.0 (基础架构)
**开发状态**: 🟢 基础功能完成

### 已完成
- [x] 项目基础架构搭建
- [x] Workbench API集成
- [x] JWT认证系统
- [x] 用户管理基础功能
- [x] API文档集成

### 进行中
- [ ] 水表设备管理模块
- [ ] 读数记录处理模块

### 计划中
- [ ] 任务分配系统
- [ ] 数据同步模块
- [ ] 报表系统集成

## 📞 联系方式

如有问题或建议，请联系开发团队。

---
**构建日期**: 2024年1月  
**维护**: 开发团队 