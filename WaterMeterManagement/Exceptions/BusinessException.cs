using WaterMeterManagement.Enums;

namespace WaterMeterManagement.Exceptions
{
    public class BusinessException : Exception
    {
        public ErrorCode ErrorCode { get; }
        public int HttpStatusCode { get; }

        public BusinessException(ErrorCode errorCode, int httpStatusCode = 400) 
            : base(GetErrorMessage(errorCode))
        {
            ErrorCode = errorCode;
            HttpStatusCode = httpStatusCode;
        }

        public BusinessException(ErrorCode errorCode, string customMessage, int httpStatusCode = 400) 
            : base(customMessage)
        {
            ErrorCode = errorCode;
            HttpStatusCode = httpStatusCode;
        }

        public BusinessException(ErrorCode errorCode, Exception innerException, int httpStatusCode = 400) 
            : base(GetErrorMessage(errorCode), innerException)
        {
            ErrorCode = errorCode;
            HttpStatusCode = httpStatusCode;
        }

        private static string GetErrorMessage(ErrorCode errorCode)
        {
            return Constants.ErrorMessages.GetMessage(errorCode);
        }
    }
}
