using Microsoft.EntityFrameworkCore;
using WaterMeterManagement.Models;

namespace WaterMeterManagement.Data
{
    public static class ConfigurationSeeder
    {
        public static async Task SeedConfigurationTemplatesAsync(ApplicationDbContext context)
        {
            Console.WriteLine("🔍 ConfigurationSeeder: Starting SeedConfigurationTemplatesAsync");
            
            // Notice: This will clear all existing templates
            var existingTemplates = await context.ConfigurationTemplates.ToListAsync();
            Console.WriteLine($"🔍 ConfigurationSeeder: Found {existingTemplates.Count} existing templates");
            
            if (existingTemplates.Any())
            {
                context.ConfigurationTemplates.RemoveRange(existingTemplates);
                await context.SaveChangesAsync();
                Console.WriteLine($"🔍 ConfigurationSeeder: Cleared {existingTemplates.Count} existing templates");
            }

            var templates = new List<ConfigurationTemplate>();
            
            var systemTemplates = CreateSystemSettingsTemplates();
            Console.WriteLine($"🔍 ConfigurationSeeder: Created {systemTemplates.Count} system templates");
            templates.AddRange(systemTemplates);
            
            var userTemplates = CreateUserSettingsTemplates();
            Console.WriteLine($"🔍 ConfigurationSeeder: Created {userTemplates.Count} user templates");
            templates.AddRange(userTemplates);

            Console.WriteLine($"🔍 ConfigurationSeeder: Total templates to add: {templates.Count}");
            
            context.ConfigurationTemplates.AddRange(templates);
            await context.SaveChangesAsync();
            
            // validate the result
            var finalCount = await context.ConfigurationTemplates.CountAsync();
            Console.WriteLine($"🔍 ConfigurationSeeder: Final template count in database: {finalCount}");
        }

        private static List<ConfigurationTemplate> CreateSystemSettingsTemplates()
        {
            var templates = new List<ConfigurationTemplate>();

            // =============================================================================
            // General Settings Category
            // =============================================================================
            templates.AddRange(new[]
            {
                new ConfigurationTemplate
                {
                    Scope = "System", Category = "General", CategoryDisplayName = "General Settings",
                    CategoryIcon = "SettingOutlined", Key = "appName", DisplayName = "Application Name",
                    Description = "Name of the application", DataType = "string",
                    DefaultValue = "\"Water Meter Management System\"", UIComponent = "input",
                    DisplayOrder = 1, Group = "Basic", GroupDisplayName = "Basic Information",
                    IsActive = true, CreatedAt = DateTime.UtcNow, CreatedBy = "System",
                    UpdatedAt = DateTime.UtcNow, UpdatedBy = "System"
                },
                new ConfigurationTemplate
                {
                    Scope = "System", Category = "General", CategoryDisplayName = "General Settings",
                    CategoryIcon = "SettingOutlined", Key = "appVersion", DisplayName = "Application Version",
                    Description = "Current version of the application", DataType = "string",
                    DefaultValue = "\"1.0.0\"", UIComponent = "input",
                    DisplayOrder = 2, Group = "Basic", GroupDisplayName = "Basic Information",
                    IsActive = true, CreatedAt = DateTime.UtcNow, CreatedBy = "System",
                    UpdatedAt = DateTime.UtcNow, UpdatedBy = "System"
                },
                new ConfigurationTemplate
                {
                    Scope = "System", Category = "General", CategoryDisplayName = "General Settings",
                    CategoryIcon = "SettingOutlined", Key = "workbenchApiUrl", DisplayName = "Workbench API URL",
                    Description = "URL for Workbench API integration", DataType = "string",
                    DefaultValue = "\"https://sicon-mnlweb.sicon.co.nz/WorkbenchLogTest/api\"", UIComponent = "input",
                    DisplayOrder = 3, Group = "API", GroupDisplayName = "API Configuration",
                    IsActive = true, CreatedAt = DateTime.UtcNow, CreatedBy = "System",
                    UpdatedAt = DateTime.UtcNow, UpdatedBy = "System"
                },
                new ConfigurationTemplate
                {
                    Scope = "System", Category = "General", CategoryDisplayName = "General Settings",
                    CategoryIcon = "SettingOutlined", Key = "apiTimeout", DisplayName = "API Timeout (ms)",
                    Description = "Default timeout for API requests in milliseconds", DataType = "number",
                    DefaultValue = "10000", UIComponent = "number",
                    UIProperties = "{\"min\":1000,\"max\":300000,\"step\":1000,\"formatter\":\"ms\"}",
                    DisplayOrder = 4, Group = "API", GroupDisplayName = "API Configuration",
                    IsActive = true, CreatedAt = DateTime.UtcNow, CreatedBy = "System",
                    UpdatedAt = DateTime.UtcNow, UpdatedBy = "System"
                },
                new ConfigurationTemplate
                {
                    Scope = "System", Category = "General", CategoryDisplayName = "General Settings",
                    CategoryIcon = "SettingOutlined", Key = "logLevel", DisplayName = "Log Level",
                    Description = "System logging level", DataType = "string",
                    DefaultValue = "\"Information\"", UIComponent = "select",
                    UIProperties = "{\"options\":[{\"label\":\"Trace\",\"value\":\"Trace\"},{\"label\":\"Debug\",\"value\":\"Debug\"},{\"label\":\"Information\",\"value\":\"Information\"},{\"label\":\"Warning\",\"value\":\"Warning\"},{\"label\":\"Error\",\"value\":\"Error\"},{\"label\":\"Critical\",\"value\":\"Critical\"}]}",
                    DisplayOrder = 5, Group = "Logging", GroupDisplayName = "Logging Configuration",
                    IsActive = true, CreatedAt = DateTime.UtcNow, CreatedBy = "System",
                    UpdatedAt = DateTime.UtcNow, UpdatedBy = "System"
                },
                new ConfigurationTemplate
                {
                    Scope = "System", Category = "General", CategoryDisplayName = "General Settings",
                    CategoryIcon = "SettingOutlined", Key = "maxLogFiles", DisplayName = "Max Log Files",
                    Description = "Maximum number of log files to retain", DataType = "number",
                    DefaultValue = "30", UIComponent = "number",
                    UIProperties = "{\"min\":1,\"max\":365}",
                    DisplayOrder = 6, Group = "Logging", GroupDisplayName = "Logging Configuration",
                    IsActive = true, CreatedAt = DateTime.UtcNow, CreatedBy = "System",
                    UpdatedAt = DateTime.UtcNow, UpdatedBy = "System"
                }
            });

            // =============================================================================
            // Security Settings Category
            // =============================================================================
            templates.AddRange(new[]
            {
                new ConfigurationTemplate
                {
                    Scope = "System", Category = "Security", CategoryDisplayName = "Security Settings",
                    CategoryIcon = "SecurityScanOutlined", Key = "jwtExpirationDays", DisplayName = "JWT Token Expiration (Days)",
                    Description = "Number of days before JWT tokens expire", DataType = "number",
                    DefaultValue = "30", UIComponent = "number",
                    UIProperties = "{\"min\":1,\"max\":365}", RequiresRestart = true,
                    DisplayOrder = 1, Group = "Authentication", GroupDisplayName = "Authentication Settings",
                    IsActive = true, CreatedAt = DateTime.UtcNow, CreatedBy = "System",
                    UpdatedAt = DateTime.UtcNow, UpdatedBy = "System"
                },
                new ConfigurationTemplate
                {
                    Scope = "System", Category = "Security", CategoryDisplayName = "Security Settings",
                    CategoryIcon = "SecurityScanOutlined", Key = "passwordMinLength", DisplayName = "Minimum Password Length",
                    Description = "Minimum required password length", DataType = "number",
                    DefaultValue = "8", UIComponent = "number",
                    UIProperties = "{\"min\":6,\"max\":32}",
                    DisplayOrder = 2, Group = "Password", GroupDisplayName = "Password Policy",
                    IsActive = true, CreatedAt = DateTime.UtcNow, CreatedBy = "System",
                    UpdatedAt = DateTime.UtcNow, UpdatedBy = "System"
                },
                new ConfigurationTemplate
                {
                    Scope = "System", Category = "Security", CategoryDisplayName = "Security Settings",
                    CategoryIcon = "SecurityScanOutlined", Key = "passwordRequireNumbers", DisplayName = "Require Numbers",
                    Description = "Require numbers in passwords", DataType = "boolean",
                    DefaultValue = "true", UIComponent = "switch",
                    UIProperties = "{\"checkedChildren\":\"Required\",\"unCheckedChildren\":\"Optional\"}",
                    DisplayOrder = 3, Group = "Password", GroupDisplayName = "Password Policy",
                    IsActive = true, CreatedAt = DateTime.UtcNow, CreatedBy = "System",
                    UpdatedAt = DateTime.UtcNow, UpdatedBy = "System"
                },
                new ConfigurationTemplate
                {
                    Scope = "System", Category = "Security", CategoryDisplayName = "Security Settings",
                    CategoryIcon = "SecurityScanOutlined", Key = "passwordRequireSymbols", DisplayName = "Require Special Characters",
                    Description = "Require special characters in passwords", DataType = "boolean",
                    DefaultValue = "true", UIComponent = "switch",
                    UIProperties = "{\"checkedChildren\":\"Required\",\"unCheckedChildren\":\"Optional\"}",
                    DisplayOrder = 4, Group = "Password", GroupDisplayName = "Password Policy",
                    IsActive = true, CreatedAt = DateTime.UtcNow, CreatedBy = "System",
                    UpdatedAt = DateTime.UtcNow, UpdatedBy = "System"
                },
                new ConfigurationTemplate
                {
                    Scope = "System", Category = "Security", CategoryDisplayName = "Security Settings",
                    CategoryIcon = "SecurityScanOutlined", Key = "sessionTimeoutMinutes", DisplayName = "Session Timeout (Minutes)",
                    Description = "User session timeout in minutes", DataType = "number",
                    DefaultValue = "120", UIComponent = "number",
                    UIProperties = "{\"min\":5,\"max\":1440}",
                    DisplayOrder = 5, Group = "Session", GroupDisplayName = "Session Management",
                    IsActive = true, CreatedAt = DateTime.UtcNow, CreatedBy = "System",
                    UpdatedAt = DateTime.UtcNow, UpdatedBy = "System"
                },
                new ConfigurationTemplate
                {
                    Scope = "System", Category = "Security", CategoryDisplayName = "Security Settings",
                    CategoryIcon = "SecurityScanOutlined", Key = "enableAuditLog", DisplayName = "Enable Audit Logging",
                    Description = "Enable detailed audit logging for security events", DataType = "boolean",
                    DefaultValue = "true", UIComponent = "switch",
                    DisplayOrder = 6, Group = "Audit", GroupDisplayName = "Audit & Monitoring",
                    IsActive = true, CreatedAt = DateTime.UtcNow, CreatedBy = "System",
                    UpdatedAt = DateTime.UtcNow, UpdatedBy = "System"
                }
            });

            // =============================================================================
            // Notification Settings Category
            // =============================================================================
            templates.AddRange(new[]
            {
                new ConfigurationTemplate
                {
                    Scope = "System", Category = "Notification", CategoryDisplayName = "Notification Settings",
                    CategoryIcon = "BellOutlined", Key = "enableEmailNotifications", DisplayName = "Enable Email Notifications",
                    Description = "Enable system email notifications", DataType = "boolean",
                    DefaultValue = "false", UIComponent = "switch",
                    DisplayOrder = 1, Group = "Email", GroupDisplayName = "Email Configuration",
                    IsActive = true, CreatedAt = DateTime.UtcNow, CreatedBy = "System",
                    UpdatedAt = DateTime.UtcNow, UpdatedBy = "System"
                },
                new ConfigurationTemplate
                {
                    Scope = "System", Category = "Notification", CategoryDisplayName = "Notification Settings",
                    CategoryIcon = "BellOutlined", Key = "smtpServer", DisplayName = "SMTP Server",
                    Description = "SMTP server for sending emails", DataType = "string",
                    DefaultValue = "\"\"", UIComponent = "input",
                    UIProperties = "{\"placeholder\":\"smtp.gmail.com\"}",
                    DisplayOrder = 2, Group = "Email", GroupDisplayName = "Email Configuration",
                    IsActive = true, CreatedAt = DateTime.UtcNow, CreatedBy = "System",
                    UpdatedAt = DateTime.UtcNow, UpdatedBy = "System"
                },
                new ConfigurationTemplate
                {
                    Scope = "System", Category = "Notification", CategoryDisplayName = "Notification Settings",
                    CategoryIcon = "BellOutlined", Key = "smtpPort", DisplayName = "SMTP Port",
                    Description = "SMTP server port", DataType = "number",
                    DefaultValue = "587", UIComponent = "number",
                    UIProperties = "{\"min\":1,\"max\":65535}",
                    DisplayOrder = 3, Group = "Email", GroupDisplayName = "Email Configuration",
                    IsActive = true, CreatedAt = DateTime.UtcNow, CreatedBy = "System",
                    UpdatedAt = DateTime.UtcNow, UpdatedBy = "System"
                },
                new ConfigurationTemplate
                {
                    Scope = "System", Category = "Notification", CategoryDisplayName = "Notification Settings",
                    CategoryIcon = "BellOutlined", Key = "smtpUsername", DisplayName = "SMTP Username",
                    Description = "SMTP authentication username", DataType = "string",
                    DefaultValue = "\"\"", UIComponent = "input",
                    DisplayOrder = 4, Group = "Email", GroupDisplayName = "Email Configuration",
                    IsActive = true, CreatedAt = DateTime.UtcNow, CreatedBy = "System",
                    UpdatedAt = DateTime.UtcNow, UpdatedBy = "System"
                },
                new ConfigurationTemplate
                {
                    Scope = "System", Category = "Notification", CategoryDisplayName = "Notification Settings",
                    CategoryIcon = "BellOutlined", Key = "smtpPassword", DisplayName = "SMTP Password",
                    Description = "SMTP authentication password", DataType = "string",
                    DefaultValue = "\"\"", UIComponent = "input",
                    IsSensitive = true,
                    DisplayOrder = 5, Group = "Email", GroupDisplayName = "Email Configuration",
                    IsActive = true, CreatedAt = DateTime.UtcNow, CreatedBy = "System",
                    UpdatedAt = DateTime.UtcNow, UpdatedBy = "System"
                }
            });

            // =============================================================================
            // API Settings Category
            // =============================================================================
            templates.AddRange(new[]
            {
                new ConfigurationTemplate
                {
                    Scope = "System", Category = "API", CategoryDisplayName = "API Configuration",
                    CategoryIcon = "ApiOutlined", Key = "amisApiUrl", DisplayName = "AMIS API URL",
                    Description = "URL for AMIS API integration", DataType = "string",
                    DefaultValue = "\"https://api.amis.com/v1\"", UIComponent = "input",
                    DisplayOrder = 1, Group = "External", GroupDisplayName = "External APIs",
                    IsActive = true, CreatedAt = DateTime.UtcNow, CreatedBy = "System",
                    UpdatedAt = DateTime.UtcNow, UpdatedBy = "System"
                },
                new ConfigurationTemplate
                {
                    Scope = "System", Category = "API", CategoryDisplayName = "API Configuration",
                    CategoryIcon = "ApiOutlined", Key = "enablePowerBiIntegration", DisplayName = "Enable Power BI Integration",
                    Description = "Enable Power BI dashboard integration", DataType = "boolean",
                    DefaultValue = "false", UIComponent = "switch",
                    DisplayOrder = 2, Group = "PowerBI", GroupDisplayName = "Power BI Integration",
                    IsActive = true, CreatedAt = DateTime.UtcNow, CreatedBy = "System",
                    UpdatedAt = DateTime.UtcNow, UpdatedBy = "System"
                }
            });

            // =============================================================================
            // Alert Settings Category
            // =============================================================================
            templates.AddRange(new[]
            {
                new ConfigurationTemplate
                {
                    Scope = "System", Category = "Alert", CategoryDisplayName = "Alert Settings",
                    CategoryIcon = "AlertOutlined", Key = "enableSystemAlerts", DisplayName = "Enable System Alerts",
                    Description = "Enable system monitoring alerts", DataType = "boolean",
                    DefaultValue = "true", UIComponent = "switch",
                    DisplayOrder = 1, Group = "General", GroupDisplayName = "Alert Configuration",
                    IsActive = true, CreatedAt = DateTime.UtcNow, CreatedBy = "System",
                    UpdatedAt = DateTime.UtcNow, UpdatedBy = "System"
                },
                new ConfigurationTemplate
                {
                    Scope = "System", Category = "Alert", CategoryDisplayName = "Alert Settings",
                    CategoryIcon = "AlertOutlined", Key = "systemCpuThreshold", DisplayName = "CPU Usage Threshold (%)",
                    Description = "CPU usage threshold for alerts", DataType = "number",
                    DefaultValue = "80", UIComponent = "slider",
                    UIProperties = "{\"min\":1,\"max\":100,\"marks\":{\"50\":\"50%\",\"80\":\"80%\",\"95\":\"95%\"}}",
                    DisplayOrder = 2, Group = "Thresholds", GroupDisplayName = "System Thresholds",
                    IsActive = true, CreatedAt = DateTime.UtcNow, CreatedBy = "System",
                    UpdatedAt = DateTime.UtcNow, UpdatedBy = "System"
                }
            });

            // =============================================================================
            // UI Settings Category
            // =============================================================================
            templates.AddRange(new[]
            {
                new ConfigurationTemplate
                {
                    Scope = "System", Category = "UI", CategoryDisplayName = "UI Settings",
                    CategoryIcon = "BgColorsOutlined", Key = "defaultTheme", DisplayName = "Default Theme",
                    Description = "Default theme for new users", DataType = "string",
                    DefaultValue = "\"light\"", UIComponent = "radio",
                    UIProperties = "{\"options\":[{\"label\":\"Light\",\"value\":\"light\"},{\"label\":\"Dark\",\"value\":\"dark\"},{\"label\":\"Auto\",\"value\":\"auto\"}]}",
                    DisplayOrder = 1, Group = "Theme", GroupDisplayName = "Theme Settings",
                    IsActive = true, CreatedAt = DateTime.UtcNow, CreatedBy = "System",
                    UpdatedAt = DateTime.UtcNow, UpdatedBy = "System"
                },
                new ConfigurationTemplate
                {
                    Scope = "System", Category = "UI", CategoryDisplayName = "UI Settings",
                    CategoryIcon = "BgColorsOutlined", Key = "defaultLanguage", DisplayName = "Default Language",
                    Description = "Default language for the system", DataType = "string",
                    DefaultValue = "\"en-US\"", UIComponent = "select",
                    UIProperties = "{\"options\":[{\"label\":\"English (US)\",\"value\":\"en-US\"},{\"label\":\"中文 (简体)\",\"value\":\"zh-CN\"},{\"label\":\"English (NZ)\",\"value\":\"en-NZ\"}]}",
                    DisplayOrder = 2, Group = "Localization", GroupDisplayName = "Language Settings",
                    IsActive = true, CreatedAt = DateTime.UtcNow, CreatedBy = "System",
                    UpdatedAt = DateTime.UtcNow, UpdatedBy = "System"
                },
                new ConfigurationTemplate
                {
                    Scope = "System", Category = "UI", CategoryDisplayName = "UI Settings",
                    CategoryIcon = "BgColorsOutlined", Key = "dateTimeFormat", DisplayName = "Date/Time Format",
                    Description = "Default date and time display format", DataType = "string",
                    DefaultValue = "\"DD/MM/YYYY HH:mm\"", UIComponent = "select",
                    UIProperties = "{\"options\":[{\"label\":\"DD/MM/YYYY HH:mm\",\"value\":\"DD/MM/YYYY HH:mm\"},{\"label\":\"MM/DD/YYYY hh:mm A\",\"value\":\"MM/DD/YYYY hh:mm A\"},{\"label\":\"YYYY-MM-DD HH:mm\",\"value\":\"YYYY-MM-DD HH:mm\"}]}",
                    DisplayOrder = 3, Group = "Format", GroupDisplayName = "Date/Time Format",
                    IsActive = true, CreatedAt = DateTime.UtcNow, CreatedBy = "System",
                    UpdatedAt = DateTime.UtcNow, UpdatedBy = "System"
                },
                new ConfigurationTemplate
                {
                    Scope = "System", Category = "UI", CategoryDisplayName = "UI Settings",
                    CategoryIcon = "BgColorsOutlined", Key = "mapProvider", DisplayName = "Map Provider",
                    Description = "Default map provider for location displays", DataType = "string",
                    DefaultValue = "\"OpenStreetMap\"", UIComponent = "radio",
                    UIProperties = "{\"options\":[{\"label\":\"OpenStreetMap\",\"value\":\"OpenStreetMap\"},{\"label\":\"Google Maps\",\"value\":\"GoogleMaps\"},{\"label\":\"Bing Maps\",\"value\":\"BingMaps\"}]}",
                    DisplayOrder = 4, Group = "Maps", GroupDisplayName = "Map Preferences",
                    IsActive = true, CreatedAt = DateTime.UtcNow, CreatedBy = "System",
                    UpdatedAt = DateTime.UtcNow, UpdatedBy = "System"
                }
            });

            // =============================================================================
            // Alert Thresholds Category
            // =============================================================================
            templates.AddRange(new[]
            {
                new ConfigurationTemplate
                {
                    Scope = "System", Category = "Thresholds", CategoryDisplayName = "Alert Thresholds",
                    CategoryIcon = "DashboardOutlined", Key = "completionRateThreshold", DisplayName = "Completion Rate Threshold (%)",
                    Description = "Minimum completion rate before triggering alerts", DataType = "number",
                    DefaultValue = "85", UIComponent = "slider",
                    UIProperties = "{\"min\":1,\"max\":100,\"marks\":{\"70\":\"70%\",\"85\":\"85%\",\"95\":\"95%\"}}",
                    DisplayOrder = 1, Group = "KPI", GroupDisplayName = "KPI Thresholds",
                    IsActive = true, CreatedAt = DateTime.UtcNow, CreatedBy = "System",
                    UpdatedAt = DateTime.UtcNow, UpdatedBy = "System"
                },
                new ConfigurationTemplate
                {
                    Scope = "System", Category = "Thresholds", CategoryDisplayName = "Alert Thresholds",
                    CategoryIcon = "DashboardOutlined", Key = "exceptionRateThreshold", DisplayName = "Exception Rate Threshold (%)",
                    Description = "Maximum exception rate before triggering alerts", DataType = "number",
                    DefaultValue = "5", UIComponent = "slider",
                    UIProperties = "{\"min\":1,\"max\":20,\"marks\":{\"2\":\"2%\",\"5\":\"5%\",\"10\":\"10%\"}}",
                    DisplayOrder = 2, Group = "Performance", GroupDisplayName = "Performance Metrics",
                    IsActive = true, CreatedAt = DateTime.UtcNow, CreatedBy = "System",
                    UpdatedAt = DateTime.UtcNow, UpdatedBy = "System"
                },
                new ConfigurationTemplate
                {
                    Scope = "System", Category = "Thresholds", CategoryDisplayName = "Alert Thresholds",
                    CategoryIcon = "DashboardOutlined", Key = "dataQualityThreshold", DisplayName = "Data Quality Score",
                    Description = "Minimum data quality score before triggering alerts", DataType = "number",
                    DefaultValue = "90", UIComponent = "slider",
                    UIProperties = "{\"min\":50,\"max\":100,\"marks\":{\"70\":\"70\",\"85\":\"85\",\"90\":\"90\",\"95\":\"95\"}}",
                    DisplayOrder = 3, Group = "Quality", GroupDisplayName = "Data Quality Rules",
                    IsActive = true, CreatedAt = DateTime.UtcNow, CreatedBy = "System",
                    UpdatedAt = DateTime.UtcNow, UpdatedBy = "System"
                },
                new ConfigurationTemplate
                {
                    Scope = "System", Category = "Thresholds", CategoryDisplayName = "Alert Thresholds",
                    CategoryIcon = "DashboardOutlined", Key = "responseTimeLimit", DisplayName = "Response Time Limit (ms)",
                    Description = "Maximum acceptable response time before triggering alerts", DataType = "number",
                    DefaultValue = "5000", UIComponent = "number",
                    UIProperties = "{\"min\":100,\"max\":30000,\"step\":100,\"formatter\":\"ms\"}",
                    DisplayOrder = 4, Group = "Performance", GroupDisplayName = "Response Time Limits",
                    IsActive = true, CreatedAt = DateTime.UtcNow, CreatedBy = "System",
                    UpdatedAt = DateTime.UtcNow, UpdatedBy = "System"
                }
            });

            return templates;
        }

        private static List<ConfigurationTemplate> CreateUserSettingsTemplates()
        {
            var templates = new List<ConfigurationTemplate>();

            // =============================================================================
            // Personal Preferences Category
            // =============================================================================
            templates.AddRange(new[]
            {
                new ConfigurationTemplate
                {
                    Scope = "User", Category = "Preferences", CategoryDisplayName = "Personal Preferences",
                    CategoryIcon = "UserOutlined", Key = "theme", DisplayName = "Theme Mode",
                    Description = "Preferred UI theme", DataType = "string",
                    DefaultValue = "\"light\"", UIComponent = "radio",
                    UIProperties = "{\"options\":[{\"label\":\"Light\",\"value\":\"light\"},{\"label\":\"Dark\",\"value\":\"dark\"},{\"label\":\"Auto\",\"value\":\"auto\"}]}",
                    DisplayOrder = 1, Group = "Appearance", GroupDisplayName = "Appearance Settings",
                    IsActive = true, CreatedAt = DateTime.UtcNow, CreatedBy = "System",
                    UpdatedAt = DateTime.UtcNow, UpdatedBy = "System"
                },
                new ConfigurationTemplate
                {
                    Scope = "User", Category = "Preferences", CategoryDisplayName = "Personal Preferences",
                    CategoryIcon = "UserOutlined", Key = "language", DisplayName = "Language",
                    Description = "Preferred interface language", DataType = "string",
                    DefaultValue = "\"en-US\"", UIComponent = "select",
                    UIProperties = "{\"options\":[{\"label\":\"English (US)\",\"value\":\"en-US\"},{\"label\":\"中文 (简体)\",\"value\":\"zh-CN\"}]}",
                    DisplayOrder = 2, Group = "Localization", GroupDisplayName = "Localization",
                    IsActive = true, CreatedAt = DateTime.UtcNow, CreatedBy = "System",
                    UpdatedAt = DateTime.UtcNow, UpdatedBy = "System"
                },
                new ConfigurationTemplate
                {
                    Scope = "User", Category = "Preferences", CategoryDisplayName = "Personal Preferences",
                    CategoryIcon = "UserOutlined", Key = "timezone", DisplayName = "Timezone",
                    Description = "Preferred timezone", DataType = "string",
                    DefaultValue = "\"Pacific/Auckland\"", UIComponent = "select",
                    UIProperties = "{\"options\":[{\"label\":\"Pacific/Auckland\",\"value\":\"Pacific/Auckland\"},{\"label\":\"America/New_York\",\"value\":\"America/New_York\"},{\"label\":\"Europe/London\",\"value\":\"Europe/London\"},{\"label\":\"Asia/Shanghai\",\"value\":\"Asia/Shanghai\"}]}",
                    DisplayOrder = 3, Group = "Localization", GroupDisplayName = "Localization",
                    IsActive = true, CreatedAt = DateTime.UtcNow, CreatedBy = "System",
                    UpdatedAt = DateTime.UtcNow, UpdatedBy = "System"
                },
                new ConfigurationTemplate
                {
                    Scope = "User", Category = "Preferences", CategoryDisplayName = "Personal Preferences",
                    CategoryIcon = "UserOutlined", Key = "pageSize", DisplayName = "Default Page Size",
                    Description = "Default number of items per page", DataType = "number",
                    DefaultValue = "10", UIComponent = "select",
                    UIProperties = "{\"options\":[{\"label\":\"10 per page\",\"value\":10},{\"label\":\"20 per page\",\"value\":20},{\"label\":\"50 per page\",\"value\":50}]}",
                    DisplayOrder = 4, Group = "Interface", GroupDisplayName = "Interface Settings",
                    IsActive = true, CreatedAt = DateTime.UtcNow, CreatedBy = "System",
                    UpdatedAt = DateTime.UtcNow, UpdatedBy = "System"
                },
                new ConfigurationTemplate
                {
                    Scope = "User", Category = "Preferences", CategoryDisplayName = "Personal Preferences",
                    CategoryIcon = "UserOutlined", Key = "autoSave", DisplayName = "Auto Save",
                    Description = "Automatically save form changes", DataType = "boolean",
                    DefaultValue = "true", UIComponent = "switch",
                    DisplayOrder = 5, Group = "Interface", GroupDisplayName = "Interface Settings",
                    IsActive = true, CreatedAt = DateTime.UtcNow, CreatedBy = "System",
                    UpdatedAt = DateTime.UtcNow, UpdatedBy = "System"
                }
            });

            // =============================================================================
            // Notification Preferences Category
            // =============================================================================
            templates.AddRange(new[]
            {
                new ConfigurationTemplate
                {
                    Scope = "User", Category = "Notifications", CategoryDisplayName = "Notification Preferences",
                    CategoryIcon = "BellOutlined", Key = "emailNotifications", DisplayName = "Email Notifications",
                    Description = "Receive email notifications", DataType = "boolean",
                    DefaultValue = "true", UIComponent = "switch",
                    DisplayOrder = 1, Group = "Email", GroupDisplayName = "Email Notifications",
                    IsActive = true, CreatedAt = DateTime.UtcNow, CreatedBy = "System",
                    UpdatedAt = DateTime.UtcNow, UpdatedBy = "System"
                },
                new ConfigurationTemplate
                {
                    Scope = "User", Category = "Notifications", CategoryDisplayName = "Notification Preferences",
                    CategoryIcon = "BellOutlined", Key = "browserNotifications", DisplayName = "Browser Notifications",
                    Description = "Receive browser push notifications", DataType = "boolean",
                    DefaultValue = "true", UIComponent = "switch",
                    DisplayOrder = 2, Group = "Browser", GroupDisplayName = "Browser Notifications",
                    IsActive = true, CreatedAt = DateTime.UtcNow, CreatedBy = "System",
                    UpdatedAt = DateTime.UtcNow, UpdatedBy = "System"
                },
                new ConfigurationTemplate
                {
                    Scope = "User", Category = "Notifications", CategoryDisplayName = "Notification Preferences",
                    CategoryIcon = "BellOutlined", Key = "soundNotifications", DisplayName = "Sound Notifications",
                    Description = "Play sound for notifications", DataType = "boolean",
                    DefaultValue = "true", UIComponent = "switch",
                    DisplayOrder = 3, Group = "Sound", GroupDisplayName = "Sound Settings",
                    IsActive = true, CreatedAt = DateTime.UtcNow, CreatedBy = "System",
                    UpdatedAt = DateTime.UtcNow, UpdatedBy = "System"
                }
            });

            // =============================================================================
            // Privacy Settings Category
            // =============================================================================
            templates.AddRange(new[]
            {
                new ConfigurationTemplate
                {
                    Scope = "User", Category = "Privacy", CategoryDisplayName = "Privacy Settings",
                    CategoryIcon = "LockOutlined", Key = "showOnlineStatus", DisplayName = "Show Online Status",
                    Description = "Show your online status to other users", DataType = "boolean",
                    DefaultValue = "true", UIComponent = "switch",
                    DisplayOrder = 1, Group = "Visibility", GroupDisplayName = "Profile Visibility",
                    IsActive = true, CreatedAt = DateTime.UtcNow, CreatedBy = "System",
                    UpdatedAt = DateTime.UtcNow, UpdatedBy = "System"
                },
                new ConfigurationTemplate
                {
                    Scope = "User", Category = "Privacy", CategoryDisplayName = "Privacy Settings",
                    CategoryIcon = "LockOutlined", Key = "allowProfileView", DisplayName = "Allow Profile View",
                    Description = "Allow other users to view your profile", DataType = "boolean",
                    DefaultValue = "true", UIComponent = "switch",
                    DisplayOrder = 2, Group = "Visibility", GroupDisplayName = "Profile Visibility",
                    IsActive = true, CreatedAt = DateTime.UtcNow, CreatedBy = "System",
                    UpdatedAt = DateTime.UtcNow, UpdatedBy = "System"
                },
                new ConfigurationTemplate
                {
                    Scope = "User", Category = "Privacy", CategoryDisplayName = "Privacy Settings",
                    CategoryIcon = "LockOutlined", Key = "sessionTimeout", DisplayName = "Session Timeout (Minutes)",
                    Description = "Personal session timeout override", DataType = "number",
                    DefaultValue = "120", UIComponent = "number",
                    UIProperties = "{\"min\":5,\"max\":480}",
                    DisplayOrder = 3, Group = "Security", GroupDisplayName = "Security Settings",
                    IsActive = true, CreatedAt = DateTime.UtcNow, CreatedBy = "System",
                    UpdatedAt = DateTime.UtcNow, UpdatedBy = "System"
                }
            });

            return templates;
        }
    }
} 