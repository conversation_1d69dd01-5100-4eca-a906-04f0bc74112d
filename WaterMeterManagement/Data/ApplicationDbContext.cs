using Microsoft.EntityFrameworkCore;
using WaterMeterManagement.Models;
using System.Security.Claims;

namespace WaterMeterManagement.Data
{
    public class ApplicationDbContext : DbContext
    {
        private readonly IHttpContextAccessor _httpContextAccessor;

        public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options, IHttpContextAccessor httpContextAccessor)
            : base(options)
        {
            _httpContextAccessor = httpContextAccessor;
        }

        // RBAC Tables
        public DbSet<User> Users { get; set; }
        public DbSet<Role> Roles { get; set; }
        public DbSet<Permission> Permissions { get; set; }
        public DbSet<UserRole> UserRoles { get; set; }
        public DbSet<RolePermission> RolePermissions { get; set; }
        public DbSet<Menu> Menus { get; set; }
        
        // Business Tables


        public DbSet<AmisSync> AmisSyncs { get; set; }
        
        // Configuration Management Tables
        public DbSet<Configuration> Configurations { get; set; }
        public DbSet<ConfigurationTemplate> ConfigurationTemplates { get; set; }
        
        // AMIS Sync & Water Meter Management
        public DbSet<WaterMeter> WaterMeters { get; set; }
        public DbSet<BaselineRecord> BaselineRecords { get; set; }
        public DbSet<AmisSyncError> AmisSyncErrors { get; set; }
        
        // Planning & Scheduling Tables
        public DbSet<Models.Route> Routes { get; set; }
        public DbSet<RouteWaypoint> RouteWaypoints { get; set; }
        public DbSet<FrequencyTemplate> FrequencyTemplates { get; set; }
        
        // Task Management Tables
        public DbSet<Models.WorkTask> Tasks { get; set; }
        public DbSet<TaskAssignment> TaskAssignments { get; set; }
        public DbSet<TaskHistory> TaskHistories { get; set; }
        
        // Work Package Management Tables
        public DbSet<WorkPackage> WorkPackages { get; set; }
        public DbSet<WorkPackageItem> WorkPackageItems { get; set; }
        public DbSet<WorkPackageAssignment> WorkPackageAssignments { get; set; }
        public DbSet<WorkPackageHistory> WorkPackageHistories { get; set; }
        
        // Meter Reading Management Tables
        public DbSet<Models.MeterReading> MeterReadings { get; set; }
        public DbSet<ReadingPhoto> ReadingPhotos { get; set; }
        public DbSet<ReadingAnomaly> ReadingAnomalies { get; set; }
        public DbSet<OCRRecord> OCRRecords { get; set; }
        public DbSet<ReadingValidationRule> ReadingValidationRules { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // Configure User entity
            modelBuilder.Entity<User>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Username).IsRequired().HasMaxLength(255);
                entity.Property(e => e.FullName).HasMaxLength(255);
                entity.Property(e => e.FinCoCode).HasMaxLength(50);
                entity.Property(e => e.CreatedDate).HasDefaultValueSql("NOW()");
                entity.Property(e => e.UpdatedDate).HasDefaultValueSql("NOW()");
                
                // Create unique index for username
                entity.HasIndex(e => e.Username).IsUnique();
            });

            // Configure Role entity
            modelBuilder.Entity<Role>(entity =>
            {
                entity.HasIndex(e => e.Name).IsUnique();
            });

            // Configure Permission entity
            modelBuilder.Entity<Permission>(entity =>
            {
                entity.HasIndex(e => e.Code).IsUnique();
            });

            // Configure Menu entity
            modelBuilder.Entity<Menu>(entity =>
            {
                entity.HasIndex(e => e.Code).IsUnique();
                entity.HasOne(m => m.Parent)
                    .WithMany(m => m.Children)
                    .HasForeignKey(m => m.ParentId)
                    .OnDelete(DeleteBehavior.Restrict);
                
                entity.HasOne(m => m.Permission)
                    .WithMany()
                    .HasForeignKey(m => m.PermissionId)
                    .OnDelete(DeleteBehavior.Restrict);
            });

            // Configure UserRole relationship
            modelBuilder.Entity<UserRole>(entity =>
            {
                entity.HasKey(ur => new { ur.UserId, ur.RoleId });
                entity.HasOne(ur => ur.User)
                    .WithMany(u => u.UserRoles)
                    .HasForeignKey(ur => ur.UserId);
                entity.HasOne(ur => ur.Role)
                    .WithMany(r => r.UserRoles)
                    .HasForeignKey(ur => ur.RoleId);
            });

            // Configure RolePermission relationship
            modelBuilder.Entity<RolePermission>(entity =>
            {
                entity.HasKey(rp => new { rp.RoleId, rp.PermissionId });
                entity.HasOne(rp => rp.Role)
                    .WithMany(r => r.RolePermissions)
                    .HasForeignKey(rp => rp.RoleId);
                entity.HasOne(rp => rp.Permission)
                    .WithMany(p => p.RolePermissions)
                    .HasForeignKey(rp => rp.PermissionId);
            });

            // Configure Configuration entity
            modelBuilder.Entity<Configuration>(entity =>
            {
                entity.HasKey(c => c.Id);
                
                // Create composite index to ensure uniqueness: Scope + Category + Key + UserId + RoleId
                entity.HasIndex(c => new { c.Scope, c.Category, c.Key, c.UserId, c.RoleId }).IsUnique();
                
                // Regular indexes for query optimization
                entity.HasIndex(c => new { c.Scope, c.Category });
                entity.HasIndex(c => new { c.Scope, c.UserId });
                entity.HasIndex(c => new { c.Scope, c.RoleId });
                
                // Foreign key relationships
                entity.HasOne(c => c.User)
                    .WithMany()
                    .HasForeignKey(c => c.UserId)
                    .OnDelete(DeleteBehavior.Cascade);
                    
                entity.HasOne(c => c.Role)
                    .WithMany()
                    .HasForeignKey(c => c.RoleId)
                    .OnDelete(DeleteBehavior.Cascade);
            });

            // Configure ConfigurationTemplate entity
            modelBuilder.Entity<ConfigurationTemplate>(entity =>
            {
                entity.HasKey(ct => ct.Id);
                
                // Create composite index to ensure uniqueness: Scope + Category + Key
                entity.HasIndex(ct => new { ct.Scope, ct.Category, ct.Key }).IsUnique();
                
                // Regular indexes for query optimization
                entity.HasIndex(ct => new { ct.Scope, ct.Category });
                entity.HasIndex(ct => new { ct.Category, ct.DisplayOrder });
            });

            // Configure WaterMeter entity
            modelBuilder.Entity<WaterMeter>(entity =>
            {
                // Indexes for unique constraints and performance
                entity.HasIndex(e => e.SerialNumber).IsUnique();
                entity.HasIndex(e => e.AssetId);
                entity.HasIndex(e => e.AccountNumber);
                entity.HasIndex(e => e.CustomerCode);
                entity.HasIndex(e => e.Township);
                entity.HasIndex(e => e.Status);
                entity.HasIndex(e => e.SyncStatus);
                
                // Precision for decimal properties
                entity.Property(e => e.Latitude).HasPrecision(10, 6);
                entity.Property(e => e.Longitude).HasPrecision(10, 6);
                entity.Property(e => e.LastReading).HasPrecision(12, 4);
                entity.Property(e => e.LastRead).HasPrecision(12, 2);
                entity.Property(e => e.RecentChange).HasPrecision(12, 2);
                entity.Property(e => e.Read).HasPrecision(12, 2);
                
                // Foreign key relationship to Route
                entity.HasOne(wm => wm.Route)
                    .WithMany(r => r.Meters)
                    .HasForeignKey(wm => wm.RouteId)
                    .OnDelete(DeleteBehavior.SetNull);
            });

            // Configure MeterReading entity
            modelBuilder.Entity<MeterReading>(entity =>
            {
                entity.HasOne(mr => mr.WaterMeter)
                    .WithMany(wm => wm.MeterReadings)
                    .HasForeignKey(mr => mr.MeterId)
                    .OnDelete(DeleteBehavior.Restrict);

                entity.HasOne(mr => mr.User)
                    .WithMany()
                    .HasForeignKey(mr => mr.UserId)
                    .OnDelete(DeleteBehavior.Restrict);

                entity.HasOne(mr => mr.Task)
                    .WithMany(t => t.Readings)
                    .HasForeignKey(mr => mr.TaskId)
                    .OnDelete(DeleteBehavior.Restrict);

                entity.HasOne(mr => mr.Validator)
                    .WithMany()
                    .HasForeignKey(mr => mr.ValidatedBy)
                    .OnDelete(DeleteBehavior.SetNull);

                entity.Property(e => e.ReadingValue).HasPrecision(12, 4);
                entity.Property(e => e.OCRConfidence).HasPrecision(5, 2);
                entity.Property(e => e.Latitude).HasPrecision(10, 6);
                entity.Property(e => e.Longitude).HasPrecision(10, 6);
                entity.Property(e => e.GpsAccuracy).HasPrecision(8, 2);

                entity.HasIndex(e => e.MeterId);
                entity.HasIndex(e => e.UserId);
                entity.HasIndex(e => e.TaskId);
                entity.HasIndex(e => e.ReadingDate);
                entity.HasIndex(e => e.Status);
                entity.HasIndex(e => e.ValidationStatus);
                entity.HasIndex(e => e.ReadingMethod);
                entity.HasIndex(e => e.ReadingType);
                entity.HasIndex(e => e.DataSource);
                entity.HasIndex(e => e.IsAnomalous);
                entity.HasIndex(e => e.CantRead);
                entity.HasIndex(e => e.IsValidated);
                entity.HasIndex(e => e.CreatedAt);
                entity.HasIndex(e => e.IsDeleted);
            });

            // Configure BaselineRecord entity
            modelBuilder.Entity<BaselineRecord>(entity =>
            {
                entity.HasOne(br => br.WaterMeter)
                    .WithMany(wm => wm.BaselineRecords)
                    .HasForeignKey(br => br.MeterId)
                    .OnDelete(DeleteBehavior.Restrict);

                entity.HasOne(br => br.PreviousBaseline)
                    .WithMany(pb => pb.SubsequentBaselines)
                    .HasForeignKey(br => br.PreviousBaselineId)
                    .OnDelete(DeleteBehavior.Restrict);

                entity.Property(e => e.BaselineValue).HasPrecision(12, 4);
                entity.Property(e => e.PreviousBaselineValue).HasPrecision(12, 4);
                entity.Property(e => e.VarianceFromPrevious).HasPrecision(12, 4);
                entity.Property(e => e.VariancePercentage).HasPrecision(8, 2);
                
                entity.HasIndex(e => new { e.MeterId, e.BaselineDate });
                entity.HasIndex(e => e.ImportBatch);
            });

            // Configure AmisSyncError entity
            modelBuilder.Entity<AmisSyncError>(entity =>
            {
                entity.HasOne(ase => ase.AmisSync)
                    .WithMany()
                    .HasForeignKey(ase => ase.AmisSyncId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasIndex(e => new { e.AmisSyncId, e.ErrorType });
                entity.HasIndex(e => e.ResolutionStatus);
                entity.HasIndex(e => e.CreatedAt);
            });



            // Configure Route entity
            modelBuilder.Entity<Models.Route>(entity =>
            {
                entity.HasIndex(e => e.Name);
                entity.HasIndex(e => e.Status);
                entity.HasIndex(e => e.Zone);
                entity.HasIndex(e => e.AssignedTo);
                entity.HasIndex(e => e.IsTemplate);
                entity.HasIndex(e => e.TemplateCategory);

                // Configure AssignedUserId foreign key
                entity.HasOne<User>()
                    .WithMany()
                    .HasForeignKey(r => r.AssignedUserId)
                    .OnDelete(DeleteBehavior.SetNull);

                entity.Property(e => e.EstimatedDistance).HasPrecision(8, 2);
                entity.Property(e => e.StartLatitude).HasPrecision(10, 6);
                entity.Property(e => e.StartLongitude).HasPrecision(10, 6);
                entity.Property(e => e.EndLatitude).HasPrecision(10, 6);
                entity.Property(e => e.EndLongitude).HasPrecision(10, 6);
                entity.Property(e => e.AverageCompletionTime).HasPrecision(8, 2);
                entity.Property(e => e.DifficultyRating).HasPrecision(3, 1);
            });

            // Configure RouteWaypoint entity
            modelBuilder.Entity<RouteWaypoint>(entity =>
            {
                entity.HasOne(rw => rw.Route)
                    .WithMany(r => r.RouteWaypoints)
                    .HasForeignKey(rw => rw.RouteId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(rw => rw.WaterMeter)
                    .WithMany()
                    .HasForeignKey(rw => rw.WaterMeterId)
                    .OnDelete(DeleteBehavior.Restrict);

                entity.Property(e => e.Latitude).HasPrecision(10, 6);
                entity.Property(e => e.Longitude).HasPrecision(10, 6);
                entity.Property(e => e.DistanceFromPrevious).HasPrecision(8, 2);
                entity.Property(e => e.AccessDifficulty).HasPrecision(3, 1);

                entity.HasIndex(e => new { e.RouteId, e.SequenceOrder });
                entity.HasIndex(e => e.WaterMeterId);
            });



            // Configure FrequencyTemplate entity
            modelBuilder.Entity<FrequencyTemplate>(entity =>
            {
                entity.HasIndex(e => e.Name).IsUnique();
                entity.HasIndex(e => e.FrequencyType);
                entity.HasIndex(e => e.Category);
                entity.HasIndex(e => e.IsDefault);
                entity.HasIndex(e => e.Status);

                entity.Property(e => e.EstimatedDuration).HasPrecision(8, 2);
            });

            // Configure Task entity
            modelBuilder.Entity<Models.WorkTask>(entity =>
            {
                entity.HasIndex(e => e.Name);
                entity.HasIndex(e => e.Status);
                entity.HasIndex(e => e.Priority);
                entity.HasIndex(e => e.Type);
                entity.HasIndex(e => e.AssignedTo);
                entity.HasIndex(e => e.AssignedUserId);
                entity.HasIndex(e => e.CreatedBy);
                entity.HasIndex(e => e.DueDate);
                entity.HasIndex(e => e.CreatedAt);
                entity.HasIndex(e => e.IsDeleted);
                entity.HasIndex(e => e.WorkPackageId);
                entity.HasIndex(e => e.WorkPackageItemId);
                entity.HasIndex(e => e.MeterId);
                entity.HasIndex(e => e.ScheduledDate);
                entity.HasIndex(e => e.ServiceAddress);
                entity.HasIndex(e => new { e.Latitude, e.Longitude });

                entity.Property(e => e.EstimatedHours).HasPrecision(5, 2);
                entity.Property(e => e.ActualHours).HasPrecision(5, 2);
                entity.Property(e => e.Latitude).HasPrecision(10, 6);
                entity.Property(e => e.Longitude).HasPrecision(10, 6);

                // Configure foreign key relationships
                entity.HasOne(t => t.WorkPackage)
                    .WithMany(wp => wp.Tasks)
                    .HasForeignKey(t => t.WorkPackageId)
                    .OnDelete(DeleteBehavior.NoAction);

                entity.HasOne(t => t.WorkPackageItem)
                    .WithMany(wpi => wpi.Tasks)
                    .HasForeignKey(t => t.WorkPackageItemId)
                    .OnDelete(DeleteBehavior.NoAction);

                entity.HasOne(t => t.Meter)
                    .WithMany()
                    .HasForeignKey(t => t.MeterId)
                    .OnDelete(DeleteBehavior.NoAction);

                entity.HasOne(t => t.Route)
                    .WithMany()
                    .HasForeignKey(t => t.RouteId)
                    .OnDelete(DeleteBehavior.NoAction);

                entity.HasOne(t => t.AssignedUser)
                    .WithMany()
                    .HasForeignKey(t => t.AssignedUserId)
                    .OnDelete(DeleteBehavior.SetNull);

                // Global filter for soft deletes
                entity.HasQueryFilter(t => !t.IsDeleted);
            });

            // Configure TaskAssignment entity
            modelBuilder.Entity<TaskAssignment>(entity =>
            {
                entity.HasOne(ta => ta.Task)
                    .WithMany(t => t.TaskAssignments)
                    .HasForeignKey(ta => ta.TaskId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasIndex(e => new { e.TaskId, e.UserId });
                entity.HasIndex(e => e.UserId);
                entity.HasIndex(e => e.AssignedDate);
                entity.HasIndex(e => e.AssignmentType);
                entity.HasIndex(e => e.Status);
                entity.HasIndex(e => e.AssignedBy);
                entity.HasIndex(e => e.IsDeleted);

                // Global filter for soft deletes
                entity.HasQueryFilter(ta => !ta.IsDeleted);
            });

            // Configure TaskHistory entity
            modelBuilder.Entity<TaskHistory>(entity =>
            {
                entity.HasOne(th => th.Task)
                    .WithMany(t => t.TaskHistories)
                    .HasForeignKey(th => th.TaskId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasIndex(e => new { e.TaskId, e.ChangedAt });
                entity.HasIndex(e => e.Action);
                entity.HasIndex(e => e.ChangedBy);
                entity.HasIndex(e => e.ChangedAt);
                entity.HasIndex(e => e.IsDeleted);

                // Global filter for soft deletes
                entity.HasQueryFilter(th => !th.IsDeleted);
            });

            // Configure ReadingPhoto entity
            modelBuilder.Entity<ReadingPhoto>(entity =>
            {
                entity.HasOne(rp => rp.MeterReading)
                    .WithMany(mr => mr.Photos)
                    .HasForeignKey(rp => rp.ReadingId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasIndex(e => e.ReadingId);
                entity.HasIndex(e => e.QualityStatus);
                entity.HasIndex(e => e.OCRStatus);
                entity.HasIndex(e => e.IsProcessed);
                entity.HasIndex(e => e.HasOCR);
                entity.HasIndex(e => e.IsOverridden);
                entity.HasIndex(e => e.CreatedAt);
                entity.HasIndex(e => e.IsDeleted);

                entity.Property(e => e.QualityScore).HasPrecision(3, 2);
                entity.Property(e => e.OcrConfidence).HasPrecision(5, 2);

                // Global filter for soft deletes
                entity.HasQueryFilter(rp => !rp.IsDeleted);
            });

            // Configure ReadingAnomaly entity
            modelBuilder.Entity<ReadingAnomaly>(entity =>
            {
                entity.HasOne(ra => ra.MeterReading)
                    .WithMany(mr => mr.Anomalies)
                    .HasForeignKey(ra => ra.MeterReadingId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(ra => ra.RelatedAnomaly)
                    .WithMany(ra => ra.ChildAnomalies)
                    .HasForeignKey(ra => ra.RelatedAnomalyId)
                    .OnDelete(DeleteBehavior.Restrict);

                // Configure AssignedUserId foreign key
                entity.HasOne<User>()
                    .WithMany()
                    .HasForeignKey(ra => ra.AssignedUserId)
                    .OnDelete(DeleteBehavior.SetNull);

                entity.HasIndex(e => e.MeterReadingId);
                entity.HasIndex(e => e.AnomalyType);
                entity.HasIndex(e => e.Severity);
                entity.HasIndex(e => e.Status);
                entity.HasIndex(e => e.AssignedTo);
                entity.HasIndex(e => e.ResolvedBy);
                entity.HasIndex(e => e.ResolutionType);
                entity.HasIndex(e => e.IsRecurring);
                entity.HasIndex(e => e.CreatedAt);
                entity.HasIndex(e => e.IsDeleted);

                entity.Property(e => e.ExpectedValue).HasPrecision(12, 4);
                entity.Property(e => e.ActualValue).HasPrecision(12, 4);
                entity.Property(e => e.Variance).HasPrecision(12, 4);
                entity.Property(e => e.ConfidenceScore).HasPrecision(5, 2);

                // Global filter for soft deletes
                entity.HasQueryFilter(ra => !ra.IsDeleted);
            });

            // Configure OCRRecord entity
            modelBuilder.Entity<OCRRecord>(entity =>
            {
                entity.HasOne(ocr => ocr.MeterReading)
                    .WithMany(mr => mr.OCRRecords)
                    .HasForeignKey(ocr => ocr.MeterReadingId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(ocr => ocr.ReadingPhoto)
                    .WithMany(rp => rp.OCRRecords)
                    .HasForeignKey(ocr => ocr.ReadingPhotoId)
                    .OnDelete(DeleteBehavior.NoAction);

                entity.HasIndex(e => e.MeterReadingId);
                entity.HasIndex(e => e.ReadingPhotoId);
                entity.HasIndex(e => e.ProcessingEngine);
                entity.HasIndex(e => e.Status);
                entity.HasIndex(e => e.ConfidenceScore);
                entity.HasIndex(e => e.IsManuallyVerified);
                entity.HasIndex(e => e.IsTrainingData);
                entity.HasIndex(e => e.CreatedAt);
                entity.HasIndex(e => e.IsDeleted);

                entity.Property(e => e.ConfidenceScore).HasPrecision(5, 2);
                entity.Property(e => e.QualityScore).HasPrecision(5, 2);

                // Global filter for soft deletes
                entity.HasQueryFilter(ocr => !ocr.IsDeleted);
            });

            // Configure ReadingValidationRule entity
            modelBuilder.Entity<ReadingValidationRule>(entity =>
            {
                entity.HasIndex(e => e.RuleName);
                entity.HasIndex(e => e.RuleType);
                entity.HasIndex(e => e.MeterType);
                entity.HasIndex(e => e.Zone);
                entity.HasIndex(e => e.CustomerType);
                entity.HasIndex(e => e.IsActive);
                entity.HasIndex(e => e.Priority);
                entity.HasIndex(e => e.CreatedAt);
                entity.HasIndex(e => e.IsDeleted);

                entity.Property(e => e.MinToleranceValue).HasPrecision(10, 2);
                entity.Property(e => e.MaxToleranceValue).HasPrecision(10, 2);
                entity.Property(e => e.TolerancePercentage).HasPrecision(5, 2);
                entity.Property(e => e.SummerMultiplier).HasPrecision(5, 2);
                entity.Property(e => e.WinterMultiplier).HasPrecision(5, 2);
                entity.Property(e => e.WarningThreshold).HasPrecision(10, 2);
                entity.Property(e => e.ErrorThreshold).HasPrecision(10, 2);
                entity.Property(e => e.CriticalThreshold).HasPrecision(10, 2);
                entity.Property(e => e.EffectivenessScore).HasPrecision(5, 2);

                // Global filter for soft deletes
                entity.HasQueryFilter(rvr => !rvr.IsDeleted);
            });

            // ============ Work Package Configuration ============

            // Configure WorkPackage entity
            modelBuilder.Entity<WorkPackage>(entity =>
            {
                entity.HasIndex(e => e.Name);
                entity.HasIndex(e => e.PackageType);
                entity.HasIndex(e => e.Status);
                entity.HasIndex(e => e.Priority);
                entity.HasIndex(e => e.ServiceArea);
                entity.HasIndex(e => e.Frequency);
                entity.HasIndex(e => e.PlannedStartDate);
                entity.HasIndex(e => e.PlannedEndDate);
                entity.HasIndex(e => e.CreatedBy);
                entity.HasIndex(e => e.Source);
                entity.HasIndex(e => e.IsTemplate);
                entity.HasIndex(e => e.IsRecurring);
                entity.HasIndex(e => e.CreatedAt);
                entity.HasIndex(e => e.IsDeleted);

                entity.Property(e => e.ProgressPercentage).HasPrecision(5, 2);
                entity.Property(e => e.EstimatedHours).HasPrecision(8, 2);
                entity.Property(e => e.ActualHours).HasPrecision(8, 2);
                entity.Property(e => e.EstimatedCost).HasPrecision(10, 2);
                entity.Property(e => e.ActualCost).HasPrecision(10, 2);

                // Global filter for soft deletes
                entity.HasQueryFilter(wp => !wp.IsDeleted);
            });

            // Configure WorkPackageItem entity
            modelBuilder.Entity<WorkPackageItem>(entity =>
            {
                entity.HasOne(wpi => wpi.WorkPackage)
                    .WithMany(wp => wp.Items)
                    .HasForeignKey(wpi => wpi.WorkPackageId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(wpi => wpi.Meter)
                    .WithMany()
                    .HasForeignKey(wpi => wpi.MeterId)
                    .OnDelete(DeleteBehavior.Restrict);

                // Configure AssignedUserId foreign key
                entity.HasOne<User>()
                    .WithMany()
                    .HasForeignKey(wpi => wpi.AssignedUserId)
                    .OnDelete(DeleteBehavior.SetNull);

                entity.HasIndex(e => new { e.WorkPackageId, e.MeterId }).IsUnique();
                entity.HasIndex(e => new { e.WorkPackageId, e.SequenceOrder });
                entity.HasIndex(e => e.MeterId);
                entity.HasIndex(e => e.Status);
                entity.HasIndex(e => e.AssignedTo);
                entity.HasIndex(e => e.ScheduledDate);
                entity.HasIndex(e => e.Priority);
                entity.HasIndex(e => e.RequiresSpecialHandling);
                entity.HasIndex(e => e.CreatedAt);
                entity.HasIndex(e => e.IsDeleted);

                entity.Property(e => e.DifficultyRating).HasPrecision(2, 1);
                entity.Property(e => e.Latitude).HasPrecision(10, 6);
                entity.Property(e => e.Longitude).HasPrecision(10, 6);

                // Global filter for soft deletes
                entity.HasQueryFilter(wpi => !wpi.IsDeleted);
            });

            // Configure WorkPackageAssignment entity
            modelBuilder.Entity<WorkPackageAssignment>(entity =>
            {
                entity.HasOne(wpa => wpa.WorkPackage)
                    .WithMany(wp => wp.Assignments)
                    .HasForeignKey(wpa => wpa.WorkPackageId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(wpa => wpa.User)
                    .WithMany()
                    .HasForeignKey(wpa => wpa.UserId)
                    .OnDelete(DeleteBehavior.Restrict);

                entity.HasOne(wpa => wpa.Supervisor)
                    .WithMany()
                    .HasForeignKey(wpa => wpa.SupervisorId)
                    .OnDelete(DeleteBehavior.SetNull);

                entity.HasIndex(e => new { e.WorkPackageId, e.UserId }).IsUnique();
                entity.HasIndex(e => e.UserId);
                entity.HasIndex(e => e.AssignmentType);
                entity.HasIndex(e => e.Status);
                entity.HasIndex(e => e.AssignedDate);
                entity.HasIndex(e => e.AssignedBy);
                entity.HasIndex(e => e.SupervisorId);
                entity.HasIndex(e => e.Priority);
                entity.HasIndex(e => e.RequiresSupervision);
                entity.HasIndex(e => e.CreatedAt);
                entity.HasIndex(e => e.IsDeleted);

                // Global filter for soft deletes
                entity.HasQueryFilter(wpa => !wpa.IsDeleted);
            });

            // Configure WorkPackageHistory entity
            modelBuilder.Entity<WorkPackageHistory>(entity =>
            {
                entity.HasOne(wph => wph.WorkPackage)
                    .WithMany(wp => wp.Histories)
                    .HasForeignKey(wph => wph.WorkPackageId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasIndex(e => new { e.WorkPackageId, e.ChangedAt });
                entity.HasIndex(e => e.Action);
                entity.HasIndex(e => e.ChangedBy);
                entity.HasIndex(e => e.ChangedAt);
                entity.HasIndex(e => e.Source);
                entity.HasIndex(e => e.IsDeleted);

                // Global filter for soft deletes
                entity.HasQueryFilter(wph => !wph.IsDeleted);
            });
        }

        /// <summary>
        /// Override SaveChangesAsync to automatically manage audit fields
        /// This is the core of the BaseEntity audit field management architecture
        /// </summary>
        public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
        {
            var currentUser = GetCurrentUser();
            var now = DateTime.UtcNow;

            foreach (var entry in ChangeTracker.Entries<BaseEntity>())
            {
                switch (entry.State)
                {
                    case EntityState.Added:
                        entry.Entity.CreatedAt = now;
                        entry.Entity.CreatedBy = currentUser;
                        entry.Entity.UpdatedAt = now;
                        entry.Entity.UpdatedBy = currentUser;
                        entry.Entity.IsDeleted = false;
                        break;
                        
                    case EntityState.Modified:
                        // Preserve original CreatedAt and CreatedBy
                        entry.Property(e => e.CreatedAt).IsModified = false;
                        entry.Property(e => e.CreatedBy).IsModified = false;
                        
                        // Update modification fields
                        entry.Entity.UpdatedAt = now;
                        entry.Entity.UpdatedBy = currentUser;
                        break;
                }
            }

            return await base.SaveChangesAsync(cancellationToken);
        }

        /// <summary>
        /// Override SaveChanges to automatically manage audit fields
        /// </summary>
        public override int SaveChanges()
        {
            var currentUser = GetCurrentUser();
            var now = DateTime.UtcNow;

            foreach (var entry in ChangeTracker.Entries<BaseEntity>())
            {
                switch (entry.State)
                {
                    case EntityState.Added:
                        entry.Entity.CreatedAt = now;
                        entry.Entity.CreatedBy = currentUser;
                        entry.Entity.UpdatedAt = now;
                        entry.Entity.UpdatedBy = currentUser;
                        entry.Entity.IsDeleted = false;
                        break;
                        
                    case EntityState.Modified:
                        // Preserve original CreatedAt and CreatedBy
                        entry.Property(e => e.CreatedAt).IsModified = false;
                        entry.Property(e => e.CreatedBy).IsModified = false;
                        
                        // Update modification fields
                        entry.Entity.UpdatedAt = now;
                        entry.Entity.UpdatedBy = currentUser;
                        break;
                }
            }

            return base.SaveChanges();
        }

        /// <summary>
        /// Get current user from HTTP context
        /// </summary>
        private string GetCurrentUser()
        {
            var user = _httpContextAccessor?.HttpContext?.User;
            
            if (user?.Identity?.IsAuthenticated == true)
            {
                // Try different claim types in order of preference
                return user.FindFirst(ClaimTypes.Name)?.Value ??
                       user.FindFirst(ClaimTypes.NameIdentifier)?.Value ??
                       user.FindFirst("username")?.Value ??
                       user.FindFirst("sub")?.Value ??
                       user.Identity.Name ??
                       "System";
            }
            
            return "System";
        }
    }
} 