using Microsoft.EntityFrameworkCore;
using WaterMeterManagement.Models;

namespace WaterMeterManagement.Data
{
    public static class DbInitializer
    {
        public static async Task InitializeAsync(ApplicationDbContext context)
        {
            // Ensure database is created
            await context.Database.EnsureCreatedAsync();

            // Check if we need to seed data
            if (await context.Permissions.AnyAsync())
            {
                // Data exists, but check if we need to update menus
                await UpdateMenusAsync(context);
                return; // DB has been seeded
            }

            try
            {
                // 1. Create Permissions
                var permissions = new List<Permission>
                {
                    // Dashboard Module
                    new Permission { Name = "Dashboard View", Code = "dashboard.view", Module = "Dashboard", Description = "View dashboard", CreatedAt = DateTime.UtcNow, UpdatedAt = DateTime.UtcNow, CreatedBy = "System", UpdatedBy = "System", IsDeleted = false },
                    new Permission { Name = "Dashboard Analytics", Code = "dashboard.analytics", Module = "Dashboard", Description = "View dashboard analytics", CreatedAt = DateTime.UtcNow, UpdatedAt = DateTime.UtcNow, CreatedBy = "System", UpdatedBy = "System", IsDeleted = false },
                    
                    // System Management Module
                    new Permission { Name = "Menu Management", Code = "system.menu.manage", Module = "System", Description = "Manage system menus", CreatedAt = DateTime.UtcNow, UpdatedAt = DateTime.UtcNow, CreatedBy = "System", UpdatedBy = "System", IsDeleted = false },
                    new Permission { Name = "Role Management", Code = "system.role.manage", Module = "System", Description = "Manage user roles", CreatedAt = DateTime.UtcNow, UpdatedAt = DateTime.UtcNow, CreatedBy = "System", UpdatedBy = "System", IsDeleted = false },
                    new Permission { Name = "Permission Management", Code = "system.permission.manage", Module = "System", Description = "Manage system permissions", CreatedAt = DateTime.UtcNow, UpdatedAt = DateTime.UtcNow, CreatedBy = "System", UpdatedBy = "System", IsDeleted = false },
                    new Permission { Name = "User Management", Code = "system.user.manage", Module = "System", Description = "Manage system users", CreatedAt = DateTime.UtcNow, UpdatedAt = DateTime.UtcNow, CreatedBy = "System", UpdatedBy = "System", IsDeleted = false },
                    
                    // Business Module
                    new Permission { Name = "Water Meter View", Code = "business.watermeter.view", Module = "Business", Description = "View water meters", CreatedAt = DateTime.UtcNow, UpdatedAt = DateTime.UtcNow, CreatedBy = "System", UpdatedBy = "System", IsDeleted = false },
                    new Permission { Name = "Water Meter Manage", Code = "business.watermeter.manage", Module = "Business", Description = "Manage water meters", CreatedAt = DateTime.UtcNow, UpdatedAt = DateTime.UtcNow, CreatedBy = "System", UpdatedBy = "System", IsDeleted = false },
                    new Permission { Name = "Data Analysis", Code = "business.analysis.view", Module = "Business", Description = "View data analysis", CreatedAt = DateTime.UtcNow, UpdatedAt = DateTime.UtcNow, CreatedBy = "System", UpdatedBy = "System", IsDeleted = false }
                };

                await context.Permissions.AddRangeAsync(permissions);
                await context.SaveChangesAsync();

                // 2. Create Roles
                var roles = new List<Role>
                {
                    new Role { Name = "Administrator", Description = "System administrator with full access", CreatedAt = DateTime.UtcNow, UpdatedAt = DateTime.UtcNow, CreatedBy = "System", UpdatedBy = "System", IsDeleted = false },
                    new Role { Name = "Manager", Description = "Manager with limited administrative access", CreatedAt = DateTime.UtcNow, UpdatedAt = DateTime.UtcNow, CreatedBy = "System", UpdatedBy = "System", IsDeleted = false },
                    new Role { Name = "Operator", Description = "Regular user with basic access", CreatedAt = DateTime.UtcNow, UpdatedAt = DateTime.UtcNow, CreatedBy = "System", UpdatedBy = "System", IsDeleted = false }
                };

                await context.Roles.AddRangeAsync(roles);
                await context.SaveChangesAsync();

                // 3. Create default Administrator user
                var adminUser = new User
                {
                    Username = "admin",
                    FullName = "System Administrator",
                    PersonId = 999999, // Special ID for system admin
                    FinCoCode = "SYSTEM",
                    IsAuthenticated = true,
                    LastLogin = DateTime.UtcNow,
                    CreatedDate = DateTime.UtcNow,
                    UpdatedDate = DateTime.UtcNow
                };

                await context.Users.AddAsync(adminUser);
                await context.SaveChangesAsync();

                // 4. Assign Administrator role to admin user
                var adminRole = await context.Roles.FirstAsync(r => r.Name == "Administrator");
                var adminUserRole = new UserRole
                {
                    UserId = adminUser.Id,
                    RoleId = adminRole.Id,
                    CreatedAt = DateTime.UtcNow,
                    CreatedBy = "System",
                    IsDeleted = false
                };

                await context.UserRoles.AddAsync(adminUserRole);
                await context.SaveChangesAsync();

                // 5. Create Role-Permission assignments
                var adminRoleId = adminRole.Id;
                var managerRole = await context.Roles.FirstAsync(r => r.Name == "Manager");
                var operatorRole = await context.Roles.FirstAsync(r => r.Name == "Operator");

                var rolePermissions = new List<RolePermission>();

                // Administrator gets all permissions
                foreach (var permission in permissions)
                {
                    rolePermissions.Add(new RolePermission
                    {
                        RoleId = adminRoleId,
                        PermissionId = permission.Id,
                        CreatedAt = DateTime.UtcNow,
                        CreatedBy = "System",
                        IsDeleted = false
                    });
                }

                // Manager gets business and limited system permissions
                var managerPermissions = permissions.Where(p => 
                    p.Code.StartsWith("dashboard.") || 
                    p.Code.StartsWith("business.") ||
                    p.Code == "system.user.manage").ToList();

                foreach (var permission in managerPermissions)
                {
                    rolePermissions.Add(new RolePermission
                    {
                        RoleId = managerRole.Id,
                        PermissionId = permission.Id,
                        CreatedAt = DateTime.UtcNow,
                        CreatedBy = "System",
                        IsDeleted = false
                    });
                }

                // Operator gets basic permissions
                var operatorPermissions = permissions.Where(p => 
                    p.Code.StartsWith("dashboard.") || 
                    p.Code == "business.watermeter.view" ||
                    p.Code == "business.analysis.view").ToList();

                foreach (var permission in operatorPermissions)
                {
                    rolePermissions.Add(new RolePermission
                    {
                        RoleId = operatorRole.Id,
                        PermissionId = permission.Id,
                        CreatedAt = DateTime.UtcNow,
                        CreatedBy = "System",
                        IsDeleted = false
                    });
                }

                await context.RolePermissions.AddRangeAsync(rolePermissions);
                await context.SaveChangesAsync();

                // 6. Get permission IDs for menu association
                var dashboardPermission = permissions.FirstOrDefault(p => p.Code == "dashboard.view");
                var businessViewPermission = permissions.FirstOrDefault(p => p.Code == "business.watermeter.view");
                var businessManagePermission = permissions.FirstOrDefault(p => p.Code == "business.watermeter.manage");
                var analysisPermission = permissions.FirstOrDefault(p => p.Code == "business.analysis.view");
                var menuManagePermission = permissions.FirstOrDefault(p => p.Code == "system.menu.manage");
                var roleManagePermission = permissions.FirstOrDefault(p => p.Code == "system.role.manage");
                var permissionManagePermission = permissions.FirstOrDefault(p => p.Code == "system.permission.manage");
                var userManagePermission = permissions.FirstOrDefault(p => p.Code == "system.user.manage");

                // 7. Create Menus - Top Level (no permission required for parent menus)
                var menus = new List<Menu>
                {
                    // Main Dashboard - requires dashboard.view permission
                    new Menu { Name = "Dashboard", Code = "dashboard", Path = "/dashboard", Component = "Dashboard", Icon = "DashboardOutlined", ParentId = null, Order = 1, IsVisible = true, PermissionId = dashboardPermission?.Id, CreatedAt = DateTime.UtcNow, UpdatedAt = DateTime.UtcNow, CreatedBy = "System", UpdatedBy = "System", IsDeleted = false },
                    
                    // Parent menus - no permission required (visibility controlled by children)
                    new Menu { Name = "Master Data", Code = "master-data", Path = "", Component = "", Icon = "DatabaseOutlined", ParentId = null, Order = 2, IsVisible = true, PermissionId = null, CreatedAt = DateTime.UtcNow, UpdatedAt = DateTime.UtcNow, CreatedBy = "System", UpdatedBy = "System", IsDeleted = false },
                    new Menu { Name = "Planning", Code = "planning", Path = "", Component = "", Icon = "CalendarOutlined", ParentId = null, Order = 3, IsVisible = true, PermissionId = null, CreatedAt = DateTime.UtcNow, UpdatedAt = DateTime.UtcNow, CreatedBy = "System", UpdatedBy = "System", IsDeleted = false },
                    new Menu { Name = "Task Management", Code = "task-management", Path = "", Component = "", Icon = "CheckSquareOutlined", ParentId = null, Order = 4, IsVisible = true, PermissionId = null, CreatedAt = DateTime.UtcNow, UpdatedAt = DateTime.UtcNow, CreatedBy = "System", UpdatedBy = "System", IsDeleted = false },
                    new Menu { Name = "Meter Reading", Code = "meter-reading", Path = "", Component = "", Icon = "ReadOutlined", ParentId = null, Order = 5, IsVisible = true, PermissionId = null, CreatedAt = DateTime.UtcNow, UpdatedAt = DateTime.UtcNow, CreatedBy = "System", UpdatedBy = "System", IsDeleted = false },
                    new Menu { Name = "Sync Center", Code = "sync-center", Path = "", Component = "", Icon = "SyncOutlined", ParentId = null, Order = 6, IsVisible = true, PermissionId = null, CreatedAt = DateTime.UtcNow, UpdatedAt = DateTime.UtcNow, CreatedBy = "System", UpdatedBy = "System", IsDeleted = false },
                    new Menu { Name = "Reports", Code = "reports", Path = "", Component = "", Icon = "BarChartOutlined", ParentId = null, Order = 7, IsVisible = true, PermissionId = null, CreatedAt = DateTime.UtcNow, UpdatedAt = DateTime.UtcNow, CreatedBy = "System", UpdatedBy = "System", IsDeleted = false },
                    new Menu { Name = "Mobile Management", Code = "mobile-management", Path = "", Component = "", Icon = "MobileOutlined", ParentId = null, Order = 8, IsVisible = true, PermissionId = null, CreatedAt = DateTime.UtcNow, UpdatedAt = DateTime.UtcNow, CreatedBy = "System", UpdatedBy = "System", IsDeleted = false },
                    new Menu { Name = "Quick Access", Code = "quick-access", Path = "", Component = "", Icon = "ThunderboltOutlined", ParentId = null, Order = 9, IsVisible = true, PermissionId = null, CreatedAt = DateTime.UtcNow, UpdatedAt = DateTime.UtcNow, CreatedBy = "System", UpdatedBy = "System", IsDeleted = false },
                    new Menu { Name = "System Management", Code = "system", Path = "", Component = "", Icon = "SettingOutlined", ParentId = null, Order = 10, IsVisible = true, PermissionId = null, CreatedAt = DateTime.UtcNow, UpdatedAt = DateTime.UtcNow, CreatedBy = "System", UpdatedBy = "System", IsDeleted = false }
                };

                await context.Menus.AddRangeAsync(menus);
                await context.SaveChangesAsync();

                // Get parent menu IDs for child menus
                var masterDataParentId = (await context.Menus.FirstAsync(m => m.Code == "master-data")).Id;
                var planningParentId = (await context.Menus.FirstAsync(m => m.Code == "planning")).Id;
                var taskMgmtParentId = (await context.Menus.FirstAsync(m => m.Code == "task-management")).Id;
                var meterReadingParentId = (await context.Menus.FirstAsync(m => m.Code == "meter-reading")).Id;
                var syncCenterParentId = (await context.Menus.FirstAsync(m => m.Code == "sync-center")).Id;
                var reportsParentId = (await context.Menus.FirstAsync(m => m.Code == "reports")).Id;
                var mobileMgmtParentId = (await context.Menus.FirstAsync(m => m.Code == "mobile-management")).Id;
                var quickAccessParentId = (await context.Menus.FirstAsync(m => m.Code == "quick-access")).Id;
                var systemParentId = (await context.Menus.FirstAsync(m => m.Code == "system")).Id;

                // 8. Create child menus with proper permission assignments
                var subMenus = new List<Menu>
                {
                    // Master Data submenus
                    new Menu { Name = "AMIS Sync", Code = "amis-sync", Path = "/master-data/amis-sync", Component = "AmisSync", Icon = "SyncOutlined", ParentId = masterDataParentId, Order = 1, IsVisible = true, PermissionId = businessManagePermission?.Id, CreatedAt = DateTime.UtcNow, UpdatedAt = DateTime.UtcNow, CreatedBy = "System", UpdatedBy = "System", IsDeleted = false },
                    new Menu { Name = "Meter Management", Code = "meter-mgmt", Path = "/master-data/meters", Component = "MeterManagement", Icon = "DatabaseOutlined", ParentId = masterDataParentId, Order = 2, IsVisible = true, PermissionId = businessViewPermission?.Id, CreatedAt = DateTime.UtcNow, UpdatedAt = DateTime.UtcNow, CreatedBy = "System", UpdatedBy = "System", IsDeleted = false },
                    new Menu { Name = "Baseline Management", Code = "baseline-mgmt", Path = "/master-data/baseline", Component = "BaselineManagement", Icon = "LineChartOutlined", ParentId = masterDataParentId, Order = 3, IsVisible = true, PermissionId = businessManagePermission?.Id, CreatedAt = DateTime.UtcNow, UpdatedAt = DateTime.UtcNow, CreatedBy = "System", UpdatedBy = "System", IsDeleted = false },
                    
                    // Planning submenus  
                    new Menu { Name = "Work Package Management", Code = "workpackage-mgmt", Path = "/planning/work-packages", Component = "WorkPackageManagement", Icon = "ProjectOutlined", ParentId = planningParentId, Order = 1, IsVisible = true, PermissionId = businessManagePermission?.Id, CreatedAt = DateTime.UtcNow, UpdatedAt = DateTime.UtcNow, CreatedBy = "System", UpdatedBy = "System", IsDeleted = false },
                    new Menu { Name = "Route Management", Code = "route-mgmt", Path = "/planning/routes", Component = "RouteManagement", Icon = "AimOutlined", ParentId = planningParentId, Order = 2, IsVisible = true, PermissionId = businessManagePermission?.Id, CreatedAt = DateTime.UtcNow, UpdatedAt = DateTime.UtcNow, CreatedBy = "System", UpdatedBy = "System", IsDeleted = false },
                    new Menu { Name = "Frequency Templates", Code = "frequency-templates", Path = "/planning/frequency-templates", Component = "FrequencyTemplates", Icon = "ClockCircleOutlined", ParentId = planningParentId, Order = 3, IsVisible = true, PermissionId = businessManagePermission?.Id, CreatedAt = DateTime.UtcNow, UpdatedAt = DateTime.UtcNow, CreatedBy = "System", UpdatedBy = "System", IsDeleted = false },
                    
                    // Task Management submenus
                    new Menu { Name = "Task Management", Code = "task-mgmt", Path = "/task-management/tasks", Component = "TaskManagement", Icon = "CheckSquareOutlined", ParentId = taskMgmtParentId, Order = 1, IsVisible = true, PermissionId = businessManagePermission?.Id, CreatedAt = DateTime.UtcNow, UpdatedAt = DateTime.UtcNow, CreatedBy = "System", UpdatedBy = "System", IsDeleted = false },
                    new Menu { Name = "Task Assignment", Code = "task-assignment", Path = "/task-management/assignment", Component = "TaskAssignment", Icon = "UserSwitchOutlined", ParentId = taskMgmtParentId, Order = 2, IsVisible = true, PermissionId = businessManagePermission?.Id, CreatedAt = DateTime.UtcNow, UpdatedAt = DateTime.UtcNow, CreatedBy = "System", UpdatedBy = "System", IsDeleted = false },
                    new Menu { Name = "Task Monitoring", Code = "task-monitor", Path = "/task-management/monitoring", Component = "TaskMonitoring", Icon = "MonitorOutlined", ParentId = taskMgmtParentId, Order = 3, IsVisible = true, PermissionId = analysisPermission?.Id, CreatedAt = DateTime.UtcNow, UpdatedAt = DateTime.UtcNow, CreatedBy = "System", UpdatedBy = "System", IsDeleted = false },
                    new Menu { Name = "Overdue Management", Code = "overdue-mgmt", Path = "/task-management/overdue", Component = "OverdueManagement", Icon = "ExclamationCircleOutlined", ParentId = taskMgmtParentId, Order = 4, IsVisible = true, PermissionId = analysisPermission?.Id, CreatedAt = DateTime.UtcNow, UpdatedAt = DateTime.UtcNow, CreatedBy = "System", UpdatedBy = "System", IsDeleted = false },
                    
                    // Meter Reading submenus
                    new Menu { Name = "Reading Records", Code = "reading-records", Path = "/reading-records", Component = "ReadingRecords", Icon = "ReadOutlined", ParentId = meterReadingParentId, Order = 1, IsVisible = true, PermissionId = businessViewPermission?.Id, CreatedAt = DateTime.UtcNow, UpdatedAt = DateTime.UtcNow, CreatedBy = "System", UpdatedBy = "System", IsDeleted = false },
                    new Menu { Name = "Photo Management", Code = "photo-management", Path = "/photo-management", Component = "PhotoManagement", Icon = "PictureOutlined", ParentId = meterReadingParentId, Order = 2, IsVisible = true, PermissionId = businessViewPermission?.Id, CreatedAt = DateTime.UtcNow, UpdatedAt = DateTime.UtcNow, CreatedBy = "System", UpdatedBy = "System", IsDeleted = false },
                    new Menu { Name = "Validation Rules", Code = "validation-rules", Path = "/validation-rules", Component = "ValidationRules", Icon = "CheckCircleOutlined", ParentId = meterReadingParentId, Order = 3, IsVisible = true, PermissionId = businessManagePermission?.Id, CreatedAt = DateTime.UtcNow, UpdatedAt = DateTime.UtcNow, CreatedBy = "System", UpdatedBy = "System", IsDeleted = false },
                    new Menu { Name = "Anomaly Management", Code = "anomaly-mgmt", Path = "/anomaly-management", Component = "AnomalyManagement", Icon = "AlertOutlined", ParentId = meterReadingParentId, Order = 4, IsVisible = true, PermissionId = analysisPermission?.Id, CreatedAt = DateTime.UtcNow, UpdatedAt = DateTime.UtcNow, CreatedBy = "System", UpdatedBy = "System", IsDeleted = false },
                    
                    // Sync Center submenus
                    new Menu { Name = "Device Sync Status", Code = "device-sync-status", Path = "/sync-center/device-status", Component = "DeviceSyncStatus", Icon = "TabletOutlined", ParentId = syncCenterParentId, Order = 1, IsVisible = true, PermissionId = analysisPermission?.Id, CreatedAt = DateTime.UtcNow, UpdatedAt = DateTime.UtcNow, CreatedBy = "System", UpdatedBy = "System", IsDeleted = false },
                    new Menu { Name = "Sync Queue", Code = "sync-queue", Path = "/sync-center/queue", Component = "SyncQueue", Icon = "UnorderedListOutlined", ParentId = syncCenterParentId, Order = 2, IsVisible = true, PermissionId = analysisPermission?.Id, CreatedAt = DateTime.UtcNow, UpdatedAt = DateTime.UtcNow, CreatedBy = "System", UpdatedBy = "System", IsDeleted = false },
                    new Menu { Name = "Offline Data", Code = "offline-data", Path = "/sync-center/offline", Component = "OfflineData", Icon = "DisconnectOutlined", ParentId = syncCenterParentId, Order = 3, IsVisible = true, PermissionId = analysisPermission?.Id, CreatedAt = DateTime.UtcNow, UpdatedAt = DateTime.UtcNow, CreatedBy = "System", UpdatedBy = "System", IsDeleted = false },
                    new Menu { Name = "Sync Logs", Code = "sync-logs", Path = "/sync-center/logs", Component = "SyncLogs", Icon = "FileTextOutlined", ParentId = syncCenterParentId, Order = 4, IsVisible = true, PermissionId = analysisPermission?.Id, CreatedAt = DateTime.UtcNow, UpdatedAt = DateTime.UtcNow, CreatedBy = "System", UpdatedBy = "System", IsDeleted = false },
                    
                    // Reports submenus
                    new Menu { Name = "Operational Dashboard", Code = "operational-dashboard", Path = "/reports/operational", Component = "OperationalDashboard", Icon = "DashboardOutlined", ParentId = reportsParentId, Order = 1, IsVisible = true, PermissionId = analysisPermission?.Id, CreatedAt = DateTime.UtcNow, UpdatedAt = DateTime.UtcNow, CreatedBy = "System", UpdatedBy = "System", IsDeleted = false },
                    new Menu { Name = "Completion Reports", Code = "completion-reports", Path = "/reports/completion", Component = "CompletionReports", Icon = "PieChartOutlined", ParentId = reportsParentId, Order = 2, IsVisible = true, PermissionId = analysisPermission?.Id, CreatedAt = DateTime.UtcNow, UpdatedAt = DateTime.UtcNow, CreatedBy = "System", UpdatedBy = "System", IsDeleted = false },
                    new Menu { Name = "Exception Analysis", Code = "exception-analysis", Path = "/reports/exceptions", Component = "ExceptionAnalysis", Icon = "WarningOutlined", ParentId = reportsParentId, Order = 3, IsVisible = true, PermissionId = analysisPermission?.Id, CreatedAt = DateTime.UtcNow, UpdatedAt = DateTime.UtcNow, CreatedBy = "System", UpdatedBy = "System", IsDeleted = false },
                    new Menu { Name = "User Performance", Code = "user-performance", Path = "/reports/performance", Component = "UserPerformance", Icon = "TrophyOutlined", ParentId = reportsParentId, Order = 4, IsVisible = true, PermissionId = analysisPermission?.Id, CreatedAt = DateTime.UtcNow, UpdatedAt = DateTime.UtcNow, CreatedBy = "System", UpdatedBy = "System", IsDeleted = false },
                    new Menu { Name = "Export Center", Code = "export-center", Path = "", Component = "", Icon = "ExportOutlined", ParentId = reportsParentId, Order = 5, IsVisible = true, PermissionId = null, CreatedAt = DateTime.UtcNow, UpdatedAt = DateTime.UtcNow, CreatedBy = "System", UpdatedBy = "System", IsDeleted = false },
                    
                    // Mobile Management submenus
                    new Menu { Name = "Device Registry", Code = "device-registry", Path = "/mobile/devices", Component = "DeviceRegistry", Icon = "TabletOutlined", ParentId = mobileMgmtParentId, Order = 1, IsVisible = true, PermissionId = userManagePermission?.Id, CreatedAt = DateTime.UtcNow, UpdatedAt = DateTime.UtcNow, CreatedBy = "System", UpdatedBy = "System", IsDeleted = false },
                    new Menu { Name = "App Versions", Code = "app-versions", Path = "/mobile/versions", Component = "AppVersions", Icon = "AppstoreOutlined", ParentId = mobileMgmtParentId, Order = 2, IsVisible = true, PermissionId = userManagePermission?.Id, CreatedAt = DateTime.UtcNow, UpdatedAt = DateTime.UtcNow, CreatedBy = "System", UpdatedBy = "System", IsDeleted = false },
                    new Menu { Name = "Compatibility Tests", Code = "compatibility-tests", Path = "/mobile/compatibility", Component = "CompatibilityTests", Icon = "ExperimentOutlined", ParentId = mobileMgmtParentId, Order = 3, IsVisible = true, PermissionId = userManagePermission?.Id, CreatedAt = DateTime.UtcNow, UpdatedAt = DateTime.UtcNow, CreatedBy = "System", UpdatedBy = "System", IsDeleted = false },
                    
                    // Quick Access submenus
                    new Menu { Name = "Create Reactive Task", Code = "create-reactive-task", Path = "/quick-access/create-task", Component = "CreateReactiveTask", Icon = "PlusCircleOutlined", ParentId = quickAccessParentId, Order = 1, IsVisible = true, PermissionId = businessManagePermission?.Id, CreatedAt = DateTime.UtcNow, UpdatedAt = DateTime.UtcNow, CreatedBy = "System", UpdatedBy = "System", IsDeleted = false },
                    new Menu { Name = "Today's Assignments", Code = "todays-assignments", Path = "/quick-access/today", Component = "TodaysAssignments", Icon = "ClockCircleOutlined", ParentId = quickAccessParentId, Order = 2, IsVisible = true, PermissionId = analysisPermission?.Id, CreatedAt = DateTime.UtcNow, UpdatedAt = DateTime.UtcNow, CreatedBy = "System", UpdatedBy = "System", IsDeleted = false },
                    new Menu { Name = "Pending Sync", Code = "pending-sync", Path = "/quick-access/pending-sync", Component = "PendingSync", Icon = "HourglassOutlined", ParentId = quickAccessParentId, Order = 3, IsVisible = true, PermissionId = analysisPermission?.Id, CreatedAt = DateTime.UtcNow, UpdatedAt = DateTime.UtcNow, CreatedBy = "System", UpdatedBy = "System", IsDeleted = false },
                    new Menu { Name = "Recent Alerts", Code = "recent-alerts", Path = "/quick-access/alerts", Component = "RecentAlerts", Icon = "BellOutlined", ParentId = quickAccessParentId, Order = 4, IsVisible = true, PermissionId = analysisPermission?.Id, CreatedAt = DateTime.UtcNow, UpdatedAt = DateTime.UtcNow, CreatedBy = "System", UpdatedBy = "System", IsDeleted = false },
                    
                    // System Management submenus
                    new Menu { Name = "Menu Management", Code = "menu-mgmt", Path = "/system/menu", Component = "MenuManagement", Icon = "MenuOutlined", ParentId = systemParentId, Order = 1, IsVisible = true, PermissionId = menuManagePermission?.Id, CreatedAt = DateTime.UtcNow, UpdatedAt = DateTime.UtcNow, CreatedBy = "System", UpdatedBy = "System", IsDeleted = false },
                    new Menu { Name = "Role Management", Code = "role-mgmt", Path = "/system/role", Component = "RoleManagement", Icon = "TeamOutlined", ParentId = systemParentId, Order = 2, IsVisible = true, PermissionId = roleManagePermission?.Id, CreatedAt = DateTime.UtcNow, UpdatedAt = DateTime.UtcNow, CreatedBy = "System", UpdatedBy = "System", IsDeleted = false },
                    new Menu { Name = "Permission Management", Code = "perm-mgmt", Path = "/system/permission", Component = "PermissionManagement", Icon = "SafetyOutlined", ParentId = systemParentId, Order = 3, IsVisible = true, PermissionId = permissionManagePermission?.Id, CreatedAt = DateTime.UtcNow, UpdatedAt = DateTime.UtcNow, CreatedBy = "System", UpdatedBy = "System", IsDeleted = false },
                    new Menu { Name = "User Management", Code = "user-mgmt", Path = "/system/user", Component = "UserManagement", Icon = "UserOutlined", ParentId = systemParentId, Order = 4, IsVisible = true, PermissionId = userManagePermission?.Id, CreatedAt = DateTime.UtcNow, UpdatedAt = DateTime.UtcNow, CreatedBy = "System", UpdatedBy = "System", IsDeleted = false },
                    new Menu { Name = "Settings", Code = "settings", Path = "/system/settings", Component = "Settings", Icon = "SettingOutlined", ParentId = systemParentId, Order = 5, IsVisible = true, PermissionId = userManagePermission?.Id, CreatedAt = DateTime.UtcNow, UpdatedAt = DateTime.UtcNow, CreatedBy = "System", UpdatedBy = "System", IsDeleted = false }
                };

                await context.Menus.AddRangeAsync(subMenus);
                await context.SaveChangesAsync();

                // 9. Add third-level menus
                var taskAssignmentParentId = (await context.Menus.FirstAsync(m => m.Code == "task-assignment")).Id;
                var exportCenterParentId = (await context.Menus.FirstAsync(m => m.Code == "export-center")).Id;

                var thirdLevelMenus = new List<Menu>
                {
                    // Task Assignment submenus
                    new Menu { Name = "Bulk Assignment", Code = "bulk-assignment", Path = "/task-management/assignment/bulk", Component = "BulkAssignment", Icon = "UsergroupAddOutlined", ParentId = taskAssignmentParentId, Order = 1, IsVisible = true, PermissionId = businessManagePermission?.Id, CreatedAt = DateTime.UtcNow, UpdatedAt = DateTime.UtcNow, CreatedBy = "System", UpdatedBy = "System", IsDeleted = false },
                    new Menu { Name = "Reactive Assignment", Code = "reactive-assignment", Path = "/task-management/assignment/reactive", Component = "ReactiveAssignment", Icon = "ThunderboltOutlined", ParentId = taskAssignmentParentId, Order = 2, IsVisible = true, PermissionId = businessManagePermission?.Id, CreatedAt = DateTime.UtcNow, UpdatedAt = DateTime.UtcNow, CreatedBy = "System", UpdatedBy = "System", IsDeleted = false },
                    
                    // Export Center submenus
                    new Menu { Name = "Generate Reports", Code = "generate-reports", Path = "/reports/export/generate", Component = "GenerateReports", Icon = "FileExcelOutlined", ParentId = exportCenterParentId, Order = 1, IsVisible = true, PermissionId = analysisPermission?.Id, CreatedAt = DateTime.UtcNow, UpdatedAt = DateTime.UtcNow, CreatedBy = "System", UpdatedBy = "System", IsDeleted = false },
                    new Menu { Name = "Export History", Code = "export-history", Path = "/reports/export/history", Component = "ExportHistory", Icon = "HistoryOutlined", ParentId = exportCenterParentId, Order = 2, IsVisible = true, PermissionId = analysisPermission?.Id, CreatedAt = DateTime.UtcNow, UpdatedAt = DateTime.UtcNow, CreatedBy = "System", UpdatedBy = "System", IsDeleted = false },
                    new Menu { Name = "Template Management", Code = "template-mgmt", Path = "/reports/export/templates", Component = "TemplateManagement", Icon = "LayoutOutlined", ParentId = exportCenterParentId, Order = 3, IsVisible = true, PermissionId = businessManagePermission?.Id, CreatedAt = DateTime.UtcNow, UpdatedAt = DateTime.UtcNow, CreatedBy = "System", UpdatedBy = "System", IsDeleted = false }
                };

                await context.Menus.AddRangeAsync(thirdLevelMenus);
                await context.SaveChangesAsync();

                // 10. Seed Configuration Templates
                await ConfigurationSeeder.SeedConfigurationTemplatesAsync(context);

                Console.WriteLine("✅ Database seeded successfully with default Administrator user (username: admin)");
                Console.WriteLine("✅ Admin user has been granted ALL permissions and should see full menu hierarchy");
                Console.WriteLine("✅ Admin user can authenticate through Workbench API with valid credentials");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Error seeding database: {ex.Message}");
                throw;
            }
        }

        private static async Task UpdateMenusAsync(ApplicationDbContext context)
        {
            // This method can be used for future menu updates if needed
            Console.WriteLine("ℹ️ Menu structure is up to date");
        }
    }
} 