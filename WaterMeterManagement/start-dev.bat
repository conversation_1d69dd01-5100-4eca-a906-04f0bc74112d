@echo off
echo 🚀 启动 Water Meter Management 后端API开发服务器...
echo.

REM 检查.NET是否安装
dotnet --version >nul 2>&1
if errorlevel 1 (
    echo ❌ .NET SDK未安装或未正确配置PATH
    echo 请先安装.NET 8.0 SDK
    pause
    exit /b 1
)

echo ✅ .NET SDK版本:
dotnet --version

echo.
echo 📦 恢复NuGet包...
dotnet restore

if errorlevel 1 (
    echo ❌ NuGet包恢复失败
    pause
    exit /b 1
)

echo.
echo 🏗️ 构建项目...
dotnet build

if errorlevel 1 (
    echo ❌ 项目构建失败
    pause
    exit /b 1
)

echo.
echo 🎯 启动开发服务器...
echo 📖 Swagger文档将在 https://localhost:7000 打开
echo 🛑 按 Ctrl+C 停止服务器
echo.

dotnet run

pause 