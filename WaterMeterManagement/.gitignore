# Build results
[Dd]ebug/
[Dd]ebugPublic/
[Rr]elease/
[Rr]eleases/
x64/
x86/
[Aa][Rr][Mm]/
[Aa][Rr][Mm]64/
bld/
[Bb]in/
[Oo]bj/
[Ll]og/
[Ll]ogs/

# Visual Studio files
.vs/
*.suo
*.user
*.userosscache
*.sln.docstates

# .NET Core
project.lock.json
project.fragment.lock.json
artifacts/

# NuGet
*.nupkg
*.snupkg
.nuget/

# Entity Framework
*.mdf
*.ldf
*.ndf

# Local database files
*.db
*.db-shm
*.db-wal

# Logs
logs/
*.log

# Environment variables
.env
appsettings.Development.json

# JetBrains Rider
/.idea/.gitignore
/.idea/misc.xml
/.idea/modules.xml
/.idea/vcs.xml
/.idea/WaterMeterManagement.iml
/WaterMeterManagement.sln
.idea/.idea.WaterMeterManagement/.idea/encodings.xml
.idea/.idea.WaterMeterManagement/.idea/indexLayout.xml
.idea/.idea.WaterMeterManagement/.idea/projectSettingsUpdater.xml
.idea/.idea.WaterMeterManagement/.idea/vcs.xml
.idea/.idea.WaterMeterManagement/.idea/workspace.xml

# macOS system files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Windows system files
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/

# Linux system files
*~
.fuse_hidden*
.directory
.Trash-*
