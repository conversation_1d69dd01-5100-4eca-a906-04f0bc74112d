using System.ComponentModel.DataAnnotations;

namespace WaterMeterManagement.DTOs
{
    public class PhotoUploadRequestDto
    {
        [Required]
        public IFormFile Photo { get; set; } = null!;

        [Required]
        [StringLength(255)]
        public string OriginalFileName { get; set; } = string.Empty;

        [Range(1, long.MaxValue)]
        public long FileSize { get; set; }

        [Required]
        [StringLength(50)]
        public string MimeType { get; set; } = string.Empty;
    }

    public class PhotoUploadResponseDto
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public int? PhotoId { get; set; }
        public string? CloudflareUrl { get; set; }
        public string? ThumbnailUrl { get; set; }
        public string? OriginalFileName { get; set; }
        public long? FileSize { get; set; }
        public DateTime? UploadTime { get; set; }
    }
}
