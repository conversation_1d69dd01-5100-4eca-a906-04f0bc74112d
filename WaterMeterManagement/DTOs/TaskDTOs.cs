using System.ComponentModel.DataAnnotations;

namespace WaterMeterManagement.DTOs
{
    public class TaskListDto
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string? Description { get; set; }
        public string Status { get; set; } = string.Empty;
        public string Priority { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty;
        public string TaskCategory { get; set; } = string.Empty;
        public string? AmsTaskNumber { get; set; }
        public string? AssetId { get; set; }
        public int? MeterId { get; set; }
        public int? AssignedUserId { get; set; }
        public string? AssignedUserName { get; set; }
        public string CreatedBy { get; set; } = string.Empty;
        public int? RouteId { get; set; }
        public DateTime? DueDate { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? CompletedDate { get; set; }
        public decimal? EstimatedHours { get; set; }
        public decimal? ActualHours { get; set; }
        public int ProgressPercentage { get; set; }
        public string? Location { get; set; }
        public string? Instructions { get; set; }
        public string? Notes { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }

        // GPS coordinates
        public decimal? Latitude { get; set; }
        public decimal? Longitude { get; set; }

        // Meter GPS coordinates (from associated water meter)
        public decimal? MeterLatitude { get; set; }
        public decimal? MeterLongitude { get; set; }

        // Related entity names
        public string? RouteName { get; set; }
        public string? WorkPackageName { get; set; }
        public string? MeterSerialNumber { get; set; }

        // Water meter details
        public string? MeterAssetId { get; set; }
        public string? MeterAccountNumber { get; set; }
        public string? MeterType { get; set; }
    }

    public class CreateTaskDto
    {
        [Required]
        [StringLength(200)]
        public string Name { get; set; } = string.Empty;

        [StringLength(1000)]
        public string? Description { get; set; }

        [Required]
        [StringLength(50)]
        public string Status { get; set; } = "Pending";

        [Required]
        [StringLength(50)]
        public string Priority { get; set; } = "Medium";

        [Required]
        [StringLength(50)]
        public string Type { get; set; } = "Reading";

        [Required]
        [StringLength(20)]
        public string TaskCategory { get; set; } = "Reactive";

        [StringLength(50)]
        public string? AmsTaskNumber { get; set; }

        [StringLength(50)]
        public string? AssetId { get; set; }

        public int? MeterId { get; set; }

        public int? AssignedUserId { get; set; }

        public int? RouteId { get; set; }

        public DateTime? DueDate { get; set; }
        public DateTime? StartDate { get; set; }

        public decimal? EstimatedHours { get; set; }

        [StringLength(200)]
        public string? Location { get; set; }

        [StringLength(1000)]
        public string? Instructions { get; set; }

        [StringLength(1000)]
        public string? Notes { get; set; }
    }

    public class UpdateTaskDto : CreateTaskDto
    {
        [Required]
        public int Id { get; set; }

        public DateTime? CompletedDate { get; set; }
        public decimal? ActualHours { get; set; }
        
        [Range(0, 100)]
        public int ProgressPercentage { get; set; } = 0;
    }

    public class TaskSearchDto
    {
        public int Page { get; set; } = 1;
        public int PageSize { get; set; } = 10;
        public string? SortBy { get; set; }
        public string? SortDirection { get; set; }
        public string? Name { get; set; }
        public string? Status { get; set; }
        public string? Priority { get; set; }
        public string? Type { get; set; }
        public int? AssignedUserId { get; set; }
        public int? RouteId { get; set; }
        public string? WorkPackageName { get; set; }
        public DateTime? DueDateFrom { get; set; }
        public DateTime? DueDateTo { get; set; }
        public DateTime? CreatedDateFrom { get; set; }
        public DateTime? CreatedDateTo { get; set; }
        public bool? IsOverdue { get; set; }
    }

    public class TaskSearchResultDto
    {
        public List<TaskListDto> Tasks { get; set; } = new();
        public int TotalCount { get; set; }
        public int PageNumber { get; set; }
        public int PageSize { get; set; }
        public int TotalPages { get; set; }
    }

    public class BulkAssignmentDto
    {
        [Required]
        public List<int> TaskIds { get; set; } = new();

        [Required]
        public int UserId { get; set; }

        [StringLength(500)]
        public string? Reason { get; set; }
    }

    public class ReactiveAssignmentDto
    {
        [Required]
        public List<int> MeterIds { get; set; } = new();

        [StringLength(500)]
        public string? Description { get; set; }

        [StringLength(50)]
        public string? Priority { get; set; }

        public int? AssignedUserId { get; set; }

        public DateTime? DueDate { get; set; }

        [StringLength(1000)]
        public string? Instructions { get; set; }
    }

    public class TaskStatisticsDto
    {
        public int TotalTasks { get; set; }
        public int PendingTasks { get; set; }
        public int InProgressTasks { get; set; }
        public int CompletedTasks { get; set; }
        public int CancelledTasks { get; set; }
        public int OverdueTasks { get; set; }
        public decimal AverageCompletionTime { get; set; }
        public decimal TaskCompletionRate { get; set; }
        public List<TaskStatusCountDto> StatusCounts { get; set; } = new();
        public List<TaskPriorityCountDto> PriorityCounts { get; set; } = new();
    }

    public class TaskStatusCountDto
    {
        public string Status { get; set; } = string.Empty;
        public int Count { get; set; }
        public decimal Percentage { get; set; }
    }

    public class TaskPriorityCountDto
    {
        public string Priority { get; set; } = string.Empty;
        public int Count { get; set; }
        public decimal Percentage { get; set; }
    }

    public class OverdueTaskDto
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Priority { get; set; } = string.Empty;
        public int? AssignedUserId { get; set; }
        public DateTime DueDate { get; set; }
        public int DaysOverdue { get; set; }
        public string? Location { get; set; }
        public DateTime CreatedAt { get; set; }
    }

    public class TaskPerformanceMetricsDto
    {
        public string Period { get; set; } = string.Empty;
        public int CompletedTasks { get; set; }
        public double AverageCompletionTimeHours { get; set; }
        public decimal OnTimeCompletionRate { get; set; }
    }

    // Task Assignment DTOs
    public class TaskAssignmentDto
    {
        public int Id { get; set; }
        public int TaskId { get; set; }
        public int UserId { get; set; }
        public DateTime AssignedDate { get; set; }
        public string AssignedBy { get; set; } = string.Empty;
        public string AssignmentType { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
        public DateTime? AcceptedDate { get; set; }
        public DateTime? RejectedDate { get; set; }
        public string? Reason { get; set; }
        
        // Related entity names
        public string? TaskName { get; set; }
        public string? UserName { get; set; }
        public string? UserEmail { get; set; }
        public string? TaskStatus { get; set; }
        public string? TaskPriority { get; set; }
    }

    public class CreateTaskAssignmentDto
    {
        [Required]
        public int TaskId { get; set; }
        
        [Required]
        public int UserId { get; set; }
        
        [StringLength(50)]
        public string AssignmentType { get; set; } = "Manual";
        
        [StringLength(500)]
        public string? Reason { get; set; }
    }

    public class UserWorkloadSummaryDto
    {
        public int UserId { get; set; }
        public string UserName { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public string? Department { get; set; }
        public string? Zone { get; set; }
        public int ActiveTaskCount { get; set; }
        public int CompletedTaskCount { get; set; }
        public int OverdueTaskCount { get; set; }
        public decimal WorkloadPercentage { get; set; }
        public decimal EfficiencyScore { get; set; }
        public string AvailabilityStatus { get; set; } = "Available"; // Available, Busy, Offline
        public DateTime? LastActivity { get; set; }
        public List<string> Skills { get; set; } = new();
        public int MaxCapacity { get; set; } = 10;
    }

    public class TaskAssignmentValidationDto
    {
        public bool CanAssign { get; set; }
        public string? Reason { get; set; }
        public List<string> Warnings { get; set; } = new();
        public UserWorkloadSummaryDto? UserWorkload { get; set; }
        public int RecommendedTaskCount { get; set; }
        public decimal PriorityScore { get; set; }
    }

    public class AssignmentResultDto
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public List<TaskAssignmentDto> CreatedAssignments { get; set; } = new();
        public List<string> Warnings { get; set; } = new();
        public List<string> Errors { get; set; } = new();
        public int TotalAssigned { get; set; }
        public int TotalFailed { get; set; }
    }

    // Work Package Grouped Search DTO
    public class TaskWorkPackageSearchDto
    {
        public int Page { get; set; } = 1;
        public int PageSize { get; set; } = 3; // Number of work packages per page
        public int? AssignedUserId { get; set; }
        public string? WorkPackageFilter { get; set; }
        public string? Status { get; set; }
        public string? Priority { get; set; }
    }

    // Work Package Group DTO
    public class WorkPackageGroupDto
    {
        public string WorkPackageName { get; set; } = string.Empty;
        public int TaskCount { get; set; }
        public List<TaskListDto> Tasks { get; set; } = new();
    }

    // Work Package Grouped Result DTO
    public class WorkPackageGroupedTasksDto
    {
        public List<WorkPackageGroupDto> WorkPackages { get; set; } = new();
        public int TotalWorkPackages { get; set; }
        public int TotalTasks { get; set; }
        public int PageNumber { get; set; }
        public int PageSize { get; set; }
        public int TotalPages { get; set; }
    }
} 