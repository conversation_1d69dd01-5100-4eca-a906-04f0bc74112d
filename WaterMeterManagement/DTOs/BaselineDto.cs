using WaterMeterManagement.DTOs.Common;

namespace WaterMeterManagement.DTOs
{
    public class BaselineRecordDto
    {
        public int Id { get; set; }
        public int MeterId { get; set; }
        public string? MeterSerialNumber { get; set; }
        public string? MeterLocation { get; set; }
        public DateTime BaselineDate { get; set; }
        public decimal BaselineValue { get; set; }
        public string BaselineType { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
        public string? ImportBatch { get; set; }
        public string? SourceFile { get; set; }
        public int? SourceRowNumber { get; set; }
        public string DataSource { get; set; } = string.Empty;
        public string? ValidationNotes { get; set; }
        public bool IsValidated { get; set; }
        public DateTime? ValidatedDate { get; set; }
        public string? ValidatedBy { get; set; }
        public bool HasValidationErrors { get; set; }
        public string? ValidationErrors { get; set; }
        public bool IsAnomalous { get; set; }
        public string? AnomalyDescription { get; set; }
        public int? PreviousBaselineId { get; set; }
        public decimal? PreviousBaselineValue { get; set; }
        public decimal? VarianceFromPrevious { get; set; }
        public decimal? VariancePercentage { get; set; }
        public bool IsCorrected { get; set; }
        public DateTime? CorrectedDate { get; set; }
        public string? CorrectedBy { get; set; }
        public string? CorrectionReason { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
    }

    public class BaselineRecordListDto
    {
        public int Id { get; set; }
        public int MeterId { get; set; }
        public string? MeterSerialNumber { get; set; }
        public string? MeterLocation { get; set; }
        public DateTime BaselineDate { get; set; }
        public decimal BaselineValue { get; set; }
        public string BaselineType { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
        public string DataSource { get; set; } = string.Empty;
        public bool IsValidated { get; set; }
        public bool HasValidationErrors { get; set; }
        public bool IsAnomalous { get; set; }
        public decimal? VariancePercentage { get; set; }
    }

    public class CreateBaselineRecordDto
    {
        public int MeterId { get; set; }
        public DateTime BaselineDate { get; set; }
        public decimal BaselineValue { get; set; }
        public string BaselineType { get; set; } = string.Empty;
        public string Status { get; set; } = "Active";
        public string DataSource { get; set; } = "Manual";
        public string? ValidationNotes { get; set; }
    }

    public class UpdateBaselineRecordDto
    {
        public DateTime BaselineDate { get; set; }
        public decimal BaselineValue { get; set; }
        public string BaselineType { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
        public string? ValidationNotes { get; set; }
        public bool IsValidated { get; set; }
        public string? CorrectionReason { get; set; }
    }

    public class BaselineImportDto
    {
        public string SerialNumber { get; set; } = string.Empty;
        public DateTime BaselineDate { get; set; }
        public decimal BaselineValue { get; set; }
        public string BaselineType { get; set; } = "Periodic";
        public string Status { get; set; } = "Active";
        public string? ValidationNotes { get; set; }
        public int RowNumber { get; set; }
        public List<string> ValidationErrors { get; set; } = new List<string>();
        public int? MeterId { get; set; }
    }

    public class BaselineImportResultDto
    {
        public int TotalRows { get; set; }
        public int SuccessfulRows { get; set; }
        public int FailedRows { get; set; }
        public int UpdatedRows { get; set; }
        public int NewRows { get; set; }
        public string? ImportBatch { get; set; }
        public List<BaselineImportDto> FailedRecords { get; set; } = new List<BaselineImportDto>();
        public List<string> GeneralErrors { get; set; } = new List<string>();
        public bool Success => FailedRows == 0 && GeneralErrors.Count == 0;
        public string? FileName { get; set; }
        public DateTime ImportDate { get; set; }
    }

    public class BaselineSearchDto : BasePaginationDto
    {
        public string? SerialNumber { get; set; }
        public string? Location { get; set; }
        public string? BaselineType { get; set; }
        public string? Status { get; set; }
        public string? DataSource { get; set; }
        public DateTime? BaselineDateFrom { get; set; }
        public DateTime? BaselineDateTo { get; set; }
        public DateTime? UpdatedFrom { get; set; } // For incremental sync
        public bool? IsValidated { get; set; }
        public bool? HasValidationErrors { get; set; }
        public bool? IsAnomalous { get; set; }
        public string? ImportBatch { get; set; }

        public BaselineSearchDto()
        {
            // Set default values for baseline management
            PageSize = PaginationDefaults.DataManagementPageSize;
            SortBy = "baselineDate";
            SortDirection = "desc";
        }
    }

    public class BaselineValidationDto
    {
        public int BaselineId { get; set; }
        public bool IsValid { get; set; }
        public string? ValidationNotes { get; set; }
        public string? ValidatedBy { get; set; }
        public List<string> ValidationErrors { get; set; } = new List<string>();
    }

    public class BaselineAnomalyDto
    {
        public int Id { get; set; }
        public int MeterId { get; set; }
        public string? MeterSerialNumber { get; set; }
        public DateTime BaselineDate { get; set; }
        public decimal BaselineValue { get; set; }
        public decimal? PreviousBaselineValue { get; set; }
        public decimal? VarianceFromPrevious { get; set; }
        public decimal? VariancePercentage { get; set; }
        public string? AnomalyDescription { get; set; }
        public string Status { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }
    }

    public class BaselineHistoryDto
    {
        public int MeterId { get; set; }
        public string? MeterSerialNumber { get; set; }
        public string? MeterLocation { get; set; }
        public List<BaselineRecordDto> BaselineRecords { get; set; } = new List<BaselineRecordDto>();
        public int TotalRecords { get; set; }
        public DateTime? FirstBaselineDate { get; set; }
        public DateTime? LastBaselineDate { get; set; }
        public decimal? CurrentBaseline { get; set; }
        public decimal? PreviousBaseline { get; set; }
        public decimal? TotalChange { get; set; }
        public decimal? AverageMonthlyChange { get; set; }
    }

    public class BaselineCorrectionDto
    {
        public int BaselineId { get; set; }
        public decimal NewBaselineValue { get; set; }
        public string CorrectionReason { get; set; } = string.Empty;
        public string? Notes { get; set; }
    }

    public class BatchValidationDto
    {
        public List<int> BaselineIds { get; set; } = new();
        public bool IsValid { get; set; }
        public string? ValidationNotes { get; set; }
        public string ValidatedBy { get; set; } = "System";
    }

    public class BatchValidationResultDto
    {
        public int TotalRequested { get; set; }
        public int Successful { get; set; }
        public int Failed { get; set; }
        public List<string> Errors { get; set; } = new();
        public DateTime ProcessedAt { get; set; } = DateTime.UtcNow;
        public string ProcessedBy { get; set; } = string.Empty;
    }
} 