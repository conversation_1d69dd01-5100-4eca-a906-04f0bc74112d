using System.ComponentModel.DataAnnotations;

namespace WaterMeterManagement.DTOs
{
    /// <summary>
    /// Dynamic select option DTO
    /// </summary>
    public class DynamicOptionDto
    {
        public string Value { get; set; } = string.Empty;
        public string Label { get; set; } = string.Empty;
        public string? Description { get; set; }
        public bool Disabled { get; set; } = false;
        public Dictionary<string, object>? Metadata { get; set; }
    }

    /// <summary>
    /// Dynamic select request DTO
    /// </summary>
    public class DynamicSelectRequestDto
    {
        [Required]
        public string Strategy { get; set; } = string.Empty;
        
        [Required]
        public string DataSource { get; set; } = string.Empty;
        
        public Dictionary<string, object> Params { get; set; } = new Dictionary<string, object>();
        
        public string ValueField { get; set; } = "id";
        
        public string LabelField { get; set; } = "name";
    }

    /// <summary>
    /// Dynamic select response DTO
    /// </summary>
    public class DynamicSelectResponseDto
    {
        public List<DynamicOptionDto> Options { get; set; } = new List<DynamicOptionDto>();
        public int TotalCount { get; set; }
        public bool Success { get; set; } = true;
        public string? ErrorMessage { get; set; }
        public Dictionary<string, object>? Metadata { get; set; }
    }
}
