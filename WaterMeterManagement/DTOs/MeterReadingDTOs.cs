using System.ComponentModel.DataAnnotations;

namespace WaterMeterManagement.DTOs
{
    // Search and List DTOs
    public class MeterReadingSearchDto
    {
        public string? MeterNumber { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public string? Status { get; set; }
        public string? ValidationStatus { get; set; }
        public string? AnomalyType { get; set; }
        public int? UserId { get; set; }  // 替代ReadBy
        public int? ValidatedBy { get; set; }  // 替代ProcessedBy
        public bool? HasPhoto { get; set; }
        public bool? HasOCR { get; set; }
        public bool? IsAnomalous { get; set; }
        public bool? CantRead { get; set; }
        public string? ReadingMethod { get; set; }
        public string? Zone { get; set; }
        public int Page { get; set; } = 1;
        public int PageSize { get; set; } = 10;
        public string? SortBy { get; set; }
        public string? SortDirection { get; set; } = "desc";
    }

    public class MeterReadingListDto
    {
        public int Id { get; set; }
        public int MeterId { get; set; }
        public int UserId { get; set; }
        public int TaskId { get; set; }
        public string MeterNumber { get; set; } = string.Empty;  // 从WaterMeter获取
        public decimal ReadingValue { get; set; }
        public decimal? PreviousReading { get; set; }  // 计算得出
        public decimal? Consumption { get; set; }  // 计算得出
        public DateTime ReadingDate { get; set; }
        public string? ReadBy { get; set; }  // 从User获取
        public string Status { get; set; } = string.Empty;
        public string? ValidationStatus { get; set; }
        public string? AnomalyType { get; set; }
        public string? Notes { get; set; }
        public string? Location { get; set; }
        public bool HasPhoto { get; set; }
        public bool HasOCR { get; set; }
        public string? OCRStatus { get; set; }
        public decimal? OCRConfidence { get; set; }
        public string? ReadingMethod { get; set; }
        public string? ReadingType { get; set; }
        public string? DataSource { get; set; }
        public bool IsAnomalous { get; set; }
        public bool CantRead { get; set; }
        public bool IsValidated { get; set; }
        public decimal? Latitude { get; set; }
        public decimal? Longitude { get; set; }
        public decimal? GpsAccuracy { get; set; }
        public int? ValidatedBy { get; set; }
        public DateTime? ValidationDate { get; set; }
        public string? ValidationComments { get; set; }
        public string? AnomalyReason { get; set; }
        public string? CantReadReason { get; set; }
        public string CreatedBy { get; set; } = string.Empty;
        public string UpdatedBy { get; set; } = string.Empty;
        public bool IsDeleted { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
    }

    public class MeterReadingSearchResultDto
    {
        public List<MeterReadingListDto> Readings { get; set; } = new();
        public int TotalCount { get; set; }
        public int Page { get; set; }
        public int PageSize { get; set; }
        public int TotalPages { get; set; }
    }

    // Create and Update DTOs
    public class CreateMeterReadingDto
    {
        [Required]
        public int MeterId { get; set; }

        [Required]
        public int UserId { get; set; }

        [Required]
        public int TaskId { get; set; }

        [Required]
        public decimal ReadingValue { get; set; }

        [Required]
        public DateTime ReadingDate { get; set; }

        [StringLength(20)]
        public string ReadingMethod { get; set; } = "Manual";

        [StringLength(20)]
        public string ReadingType { get; set; } = "Regular";

        [StringLength(20)]
        public string DataSource { get; set; } = "Web";

        [StringLength(500)]
        public string? Notes { get; set; }

        [StringLength(200)]
        public string? Location { get; set; }

        public bool HasOCR { get; set; } = false;

        [StringLength(20)]
        public string? OCRStatus { get; set; }

        public decimal? OCRConfidence { get; set; }

        public decimal? Latitude { get; set; }
        public decimal? Longitude { get; set; }
        public decimal? GpsAccuracy { get; set; }

        public bool CantRead { get; set; } = false;
        [StringLength(200)]
        public string? CantReadReason { get; set; }
    }

    public class UpdateMeterReadingDto
    {
        [Required]
        public decimal ReadingValue { get; set; }

        [Required]
        [StringLength(20)]
        public string Status { get; set; } = string.Empty;

        [StringLength(50)]
        public string? ValidationStatus { get; set; }

        [StringLength(100)]
        public string? AnomalyType { get; set; }

        [StringLength(500)]
        public string? Notes { get; set; }

        [StringLength(200)]
        public string? Location { get; set; }

        public bool IsAnomalous { get; set; } = false;
        [StringLength(200)]
        public string? AnomalyReason { get; set; }

        public bool CantRead { get; set; } = false;
        [StringLength(200)]
        public string? CantReadReason { get; set; }

        [StringLength(500)]
        public string? ValidationComments { get; set; }
    }

    // Photo Management DTOs - moved to separate file ReadingPhotoDto.cs

    public class PhotoSearchDto
    {
        public string? MeterNumber { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public string? QualityStatus { get; set; }
        public string? OCRStatus { get; set; }
        public bool? IsOverridden { get; set; }
        public int Page { get; set; } = 1;
        public int PageSize { get; set; } = 10;
    }

    public class UpdatePhotoQualityDto
    {
        public decimal QualityScore { get; set; }
        public string QualityStatus { get; set; } = string.Empty;
    }

    // Anomaly Management DTOs
    public class ReadingAnomalyDto
    {
        public int Id { get; set; }
        public int MeterReadingId { get; set; }
        public string MeterNumber { get; set; } = string.Empty;
        public string AnomalyType { get; set; } = string.Empty;
        public string Severity { get; set; } = string.Empty;
        public string? Description { get; set; }
        public decimal? ExpectedValue { get; set; }
        public decimal? ActualValue { get; set; }
        public decimal? Variance { get; set; }
        public decimal? ConfidenceScore { get; set; }
        public string Status { get; set; } = string.Empty;
        public string? AssignedTo { get; set; }
        public DateTime? AssignedDate { get; set; }
        public string? ResolvedBy { get; set; }
        public DateTime? ResolvedDate { get; set; }
        public string? Resolution { get; set; }
        public string? ResolutionType { get; set; }
        public bool RequiresFieldVisit { get; set; }
        public bool IsRecurring { get; set; }
        public DateTime CreatedAt { get; set; }
    }

    public class AnomalySearchDto
    {
        public string? MeterNumber { get; set; }
        public string? AnomalyType { get; set; }
        public string? Severity { get; set; }
        public string? Status { get; set; }
        public string? AssignedTo { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public bool? RequiresFieldVisit { get; set; }
        public bool? IsRecurring { get; set; }
        public int Page { get; set; } = 1;
        public int PageSize { get; set; } = 10;
    }

    public class CreateAnomalyDto
    {
        [Required]
        public int MeterReadingId { get; set; }

        [Required]
        [StringLength(100)]
        public string AnomalyType { get; set; } = string.Empty;

        [Required]
        [StringLength(20)]
        public string Severity { get; set; } = string.Empty;

        [StringLength(1000)]
        public string? Description { get; set; }

        public decimal? ExpectedValue { get; set; }
        public decimal? ActualValue { get; set; }
        public decimal? Variance { get; set; }
        public decimal? ConfidenceScore { get; set; }
        public bool RequiresFieldVisit { get; set; } = false;
        public string? AssignedTo { get; set; }
    }

    public class UpdateAnomalyDto
    {
        [Required]
        [StringLength(20)]
        public string Status { get; set; } = string.Empty;

        [StringLength(1000)]
        public string? Resolution { get; set; }

        [StringLength(100)]
        public string? ResolutionType { get; set; }

        [StringLength(1000)]
        public string? Notes { get; set; }
    }

    // Validation Rules DTOs
    public class ValidationRuleDto
    {
        public int Id { get; set; }
        public string RuleName { get; set; } = string.Empty;
        public string RuleType { get; set; } = string.Empty;
        public string? Description { get; set; }
        public string? MeterType { get; set; }
        public string? Zone { get; set; }
        public string? CustomerType { get; set; }
        public bool IsActive { get; set; }
        public int Priority { get; set; }
        public decimal? MinToleranceValue { get; set; }
        public decimal? MaxToleranceValue { get; set; }
        public decimal? TolerancePercentage { get; set; }
        public int? TimeWindowDays { get; set; }
        public bool SeasonalAdjustment { get; set; }
        public decimal? SummerMultiplier { get; set; }
        public decimal? WinterMultiplier { get; set; }
        public decimal? WarningThreshold { get; set; }
        public decimal? ErrorThreshold { get; set; }
        public decimal? CriticalThreshold { get; set; }
        public string? ActionOnViolation { get; set; }
        public bool AutoCorrect { get; set; }
        public string? NotificationRecipients { get; set; }
        public string? RuleConfiguration { get; set; }
        public decimal? EffectivenessScore { get; set; }
        public DateTime CreatedAt { get; set; }
    }

    public class CreateValidationRuleDto
    {
        [Required]
        [StringLength(100)]
        public string RuleName { get; set; } = string.Empty;

        [Required]
        [StringLength(50)]
        public string RuleType { get; set; } = string.Empty;

        [StringLength(500)]
        public string? Description { get; set; }

        [StringLength(50)]
        public string? MeterType { get; set; }

        [StringLength(50)]
        public string? Zone { get; set; }

        [StringLength(50)]
        public string? CustomerType { get; set; }

        public bool IsActive { get; set; } = true;
        public int Priority { get; set; } = 5;
        public decimal? MinToleranceValue { get; set; }
        public decimal? MaxToleranceValue { get; set; }
        public decimal? TolerancePercentage { get; set; }
        public int? TimeWindowDays { get; set; }
        public bool SeasonalAdjustment { get; set; } = false;
        public decimal? SummerMultiplier { get; set; }
        public decimal? WinterMultiplier { get; set; }
        public decimal? WarningThreshold { get; set; }
        public decimal? ErrorThreshold { get; set; }
        public decimal? CriticalThreshold { get; set; }

        [StringLength(50)]
        public string? ActionOnViolation { get; set; } = "Flag";

        public bool AutoCorrect { get; set; } = false;

        [StringLength(100)]
        public string? NotificationRecipients { get; set; }

        public string? RuleConfiguration { get; set; }
    }

    // Statistics DTOs
    public class MeterReadingStatisticsDto
    {
        public int TotalReadings { get; set; }
        public int PendingReadings { get; set; }
        public int ConfirmedReadings { get; set; }
        public int AnomalyReadings { get; set; }
        public int CorrectedReadings { get; set; }
        public int ReadingsWithPhoto { get; set; }
        public int ReadingsWithOCR { get; set; }
        public decimal OCRSuccessRate { get; set; }
        public decimal AnomalyDetectionRate { get; set; }
        public decimal DataQualityScore { get; set; }
        public List<ReadingStatusCountDto> StatusCounts { get; set; } = new();
        public List<AnomalyTypeCountDto> AnomalyTypeCounts { get; set; } = new();
    }

    public class ReadingStatusCountDto
    {
        public string Status { get; set; } = string.Empty;
        public int Count { get; set; }
        public decimal Percentage { get; set; }
    }

    public class AnomalyTypeCountDto
    {
        public string AnomalyType { get; set; } = string.Empty;
        public int Count { get; set; }
        public decimal Percentage { get; set; }
    }

    // OCR Management DTOs
    public class OCRRecordDto
    {
        public int Id { get; set; }
        public int MeterReadingId { get; set; }
        public int? ReadingPhotoId { get; set; }
        public string ProcessingEngine { get; set; } = string.Empty;
        public string ProcessingVersion { get; set; } = string.Empty;
        public string RecognizedValue { get; set; } = string.Empty;
        public decimal ConfidenceScore { get; set; }
        public string Status { get; set; } = string.Empty;
        public string? ErrorMessage { get; set; }
        public int ProcessingTimeMs { get; set; }
        public string? BoundingBox { get; set; }
        public decimal? QualityScore { get; set; }
        public bool IsManuallyVerified { get; set; }
        public string? VerifiedBy { get; set; }
        public DateTime? VerifiedDate { get; set; }
        public string? CorrectedValue { get; set; }
        public string? CorrectionReason { get; set; }
        public bool IsTrainingData { get; set; }
        public DateTime CreatedAt { get; set; }
    }

    /// <summary>
    /// Web Admin Reading Response DTO (for API consistency)
    /// </summary>
    public class ReadingResponseDto
    {
        public bool Success { get; set; }
        public string? Message { get; set; }
        public int? ReadingId { get; set; }
        public List<string> Errors { get; set; } = new List<string>();
        public List<string> Warnings { get; set; } = new List<string>();
        public bool ValidationPassed { get; set; }
        public bool AnomalyDetected { get; set; }
        public string? AnomalyType { get; set; }
        public decimal? SuggestedValue { get; set; }
    }

    /// <summary>
    /// Enhanced Reading Response DTO (for advanced web features)
    /// </summary>
    public class EnhancedReadingResponseDto : ReadingResponseDto
    {
        public bool LocationVerified { get; set; }
        public decimal? LocationAccuracy { get; set; }
        public bool OCRProcessed { get; set; }
        public decimal? OCRConfidence { get; set; }
        public string? OCRStatus { get; set; }
        public string? RecognizedValue { get; set; }
        
        // 兼容性属性
        public decimal? OcrConfidence { get => OCRConfidence; set => OCRConfidence = value; }
        public int DataQualityScore { get; set; }
        public List<string> QualityFactors { get; set; } = new List<string>();

        // Additional properties needed by controllers/services
        public int ValidationScore { get; set; }
        public bool RequiresReview { get; set; }
        public string? ReviewReason { get; set; }
        public bool IsAnomaly { get; set; }
        public bool IsGpsAccuracyAcceptable { get; set; }
        public decimal? GpsAccuracy { get; set; }
        public string? GpsValidationMessage { get; set; }
        public bool IsOcrConfidenceAcceptable { get; set; }
        public string? OcrValidationMessage { get; set; }
        public decimal? CalculatedConsumption { get; set; }
        public bool IsConsumptionNormal { get; set; }
        public string? ConsumptionAnalysis { get; set; }
        public DateTime ProcessedAt { get; set; }
        public string? ProcessedBy { get; set; }
        public string? ValidationStatus { get; set; }
        public List<string> NextActions { get; set; } = new List<string>();
    }

    /// <summary>
    /// Batch Reading Request DTO (unified with Mobile DTOs)
    /// </summary>
    public class BatchReadingRequest
    {
        [Required]
        public List<WaterMeterManagement.DTOs.Mobile.MobileReadingDto> Readings { get; set; } = new List<WaterMeterManagement.DTOs.Mobile.MobileReadingDto>();
        
        [StringLength(200)]
        public string? DeviceInfo { get; set; }
        
        public DateTime SubmissionTime { get; set; } = DateTime.UtcNow;
        
        [StringLength(500)]
        public string? BatchNotes { get; set; }
    }

    /// <summary>
    /// Batch Reading Response DTO (for web admin batch operations)
    /// </summary>
    public class BatchReadingResponseDto
    {
        public bool Success { get; set; }
        public string? Message { get; set; }
        public int SuccessCount { get; set; }
        public int FailureCount { get; set; }
        public List<ReadingResponseDto> Results { get; set; } = new List<ReadingResponseDto>();
        public List<string> GeneralErrors { get; set; } = new List<string>();
        public DateTime ProcessedAt { get; set; } = DateTime.UtcNow;

        // Additional properties needed by controllers/services
        public int TotalSubmitted { get; set; }
        public int SuccessfullyProcessed { get; set; }
        public int Failed { get; set; }
        public List<string> FailedReadings { get; set; } = new List<string>();
        
        // 兼容性属性 (别名)
        public int FailedCount { get => FailureCount; set => FailureCount = value; }
    }

    /// <summary>
    /// Enhanced Reading Validation DTO (for web admin validation)
    /// </summary>
    public class EnhancedReadingValidationDto
    {
        public bool IsValid { get; set; }
        public List<string> Errors { get; set; } = new List<string>();
        public List<string> Warnings { get; set; } = new List<string>();
        
        // Anomaly detection
        public bool AnomalyDetected { get; set; }
        public string? AnomalyType { get; set; }
        public string? AnomalyReason { get; set; }
        
        // Advanced validation features for web
        public bool LocationValidated { get; set; }
        public bool OCRValidated { get; set; }
        public bool PhotoQualityValidated { get; set; }
        public bool TimeValidated { get; set; }
        
        public decimal? SuggestedValue { get; set; }
        public int ConfidenceScore { get; set; }
        public int OverallQualityScore { get; set; }
        
        public List<string> QualityRecommendations { get; set; } = new List<string>();

        // Additional properties needed by controllers/services
        public int ValidationScore { get; set; }
        public string? ValidationStatus { get; set; }
        public bool RequiresReview { get; set; }
        public string? ReviewReason { get; set; }
        public bool IsAnomaly { get; set; }
        public bool IsGpsDataValid { get; set; }
        public decimal? GpsAccuracy { get; set; }
        public string? GpsValidationMessage { get; set; }
        public bool IsOcrDataValid { get; set; }
        public decimal? OcrConfidence { get; set; }
        public string? OcrValidationMessage { get; set; }
        public List<string> AnomalyReasons { get; set; } = new List<string>();
        public bool IsConsumptionNormal { get; set; }
        public decimal? DailyConsumption { get; set; }
    }
} 