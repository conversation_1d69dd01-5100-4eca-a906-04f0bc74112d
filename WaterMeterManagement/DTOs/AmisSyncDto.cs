namespace WaterMeterManagement.DTOs
{
    public class AmisSyncDto
    {
        public int Id { get; set; }
        public string SyncType { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
        public DateTime StartTime { get; set; }
        public DateTime? EndTime { get; set; }
        public TimeSpan? Duration { get; set; }
        public string? TriggerBy { get; set; }
        public int TotalRecords { get; set; }
        public int ProcessedRecords { get; set; }
        public int SuccessfulRecords { get; set; }
        public int FailedRecords { get; set; }
        public int SkippedRecords { get; set; }
        public decimal ProgressPercentage { get; set; }
        public string? CurrentOperation { get; set; }
        public string? ResultSummary { get; set; }
        public string? ErrorMessage { get; set; }
        public int? RecordsPerSecond { get; set; }
        public decimal? DataSizeMB { get; set; }
        public int RetryCount { get; set; }
        public int MaxRetries { get; set; }
        public DateTime? NextRetryTime { get; set; }
        public string? AmisEndpoint { get; set; }
        public string? AmisVersion { get; set; }
        public DateTime? LastSyncDate { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
    }

    public class AmisSyncListDto
    {
        public int Id { get; set; }
        public string SyncType { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
        public DateTime StartTime { get; set; }
        public DateTime? EndTime { get; set; }
        public TimeSpan? Duration { get; set; }
        public int TotalRecords { get; set; }
        public int SuccessfulRecords { get; set; }
        public int FailedRecords { get; set; }
        public decimal ProgressPercentage { get; set; }
        public string? ErrorMessage { get; set; }
    }

    public class CreateAmisSyncDto
    {
        public string SyncType { get; set; } = string.Empty;
        public string? TriggerBy { get; set; }
        public string? SyncConfiguration { get; set; }
    }

    public class AmisSyncConfigurationDto
    {
        public string? AmisEndpoint { get; set; }
        public string? ApiKey { get; set; }
        public string? ApiSecret { get; set; }
        public int TimeoutSeconds { get; set; } = 30;
        public int BatchSize { get; set; } = 100;
        public bool EnableRetry { get; set; } = true;
        public int MaxRetries { get; set; } = 3;
        public int RetryDelaySeconds { get; set; } = 60;
        public DateTime? SyncFromDate { get; set; }
        public DateTime? SyncToDate { get; set; }
        public List<string> SyncTypes { get; set; } = new List<string>();
    }

    public class AmisSyncErrorDto
    {
        public int Id { get; set; }
        public int AmisSyncId { get; set; }
        public string ErrorType { get; set; } = string.Empty;
        public string Severity { get; set; } = string.Empty;
        public string ErrorMessage { get; set; } = string.Empty;
        public string? DetailedError { get; set; }
        public string? RecordIdentifier { get; set; }
        public int? RecordLineNumber { get; set; }
        public string? Operation { get; set; }
        public string? Endpoint { get; set; }
        public int? HttpStatusCode { get; set; }
        public string ResolutionStatus { get; set; } = string.Empty;
        public DateTime? ResolvedDate { get; set; }
        public string? ResolvedBy { get; set; }
        public string? ResolutionNotes { get; set; }
        public bool CanRetry { get; set; }
        public int RetryCount { get; set; }
        public DateTime? LastRetryDate { get; set; }
        public DateTime CreatedAt { get; set; }
    }

    public class AmisSyncSearchDto
    {
        public string? SyncType { get; set; }
        public string? Status { get; set; }
        public DateTime? StartTimeFrom { get; set; }
        public DateTime? StartTimeTo { get; set; }
        public string? TriggerBy { get; set; }
        public int Page { get; set; } = 1;
        public int PageSize { get; set; } = 10;
        public string? SortBy { get; set; }
        public string? SortDirection { get; set; } = "desc";
    }

    public class ManualSyncRequestDto
    {
        public string SyncType { get; set; } = string.Empty;
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public bool ForceFullSync { get; set; } = false;
        public string? Notes { get; set; }
    }

    public class SyncProgressDto
    {
        public int Id { get; set; }
        public string Status { get; set; } = string.Empty;
        public decimal ProgressPercentage { get; set; }
        public string? CurrentOperation { get; set; }
        public int ProcessedRecords { get; set; }
        public int TotalRecords { get; set; }
        public string? ErrorMessage { get; set; }
        public DateTime LastUpdate { get; set; }
    }
} 