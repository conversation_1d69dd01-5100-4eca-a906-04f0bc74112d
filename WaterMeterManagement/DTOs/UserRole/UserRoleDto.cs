namespace WaterMeterManagement.DTOs.UserRole
{
    public class UserRoleDto
    {
        public int UserId { get; set; }
        public int RoleId { get; set; }
        public string UserName { get; set; } = string.Empty;
        public string RoleName { get; set; } = string.Empty;
        public string RoleDescription { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }
        public string CreatedBy { get; set; } = string.Empty;
    }

    public class AssignRolesDto
    {
        public List<int> RoleIds { get; set; } = new();
    }

    public class UserRoleAssignmentResultDto
    {
        public int UserId { get; set; }
        public string UserName { get; set; } = string.Empty;
        public List<RoleAssignmentDetailDto> Results { get; set; } = new();
        public int SuccessCount { get; set; }
        public int ErrorCount { get; set; }
    }

    public class RoleAssignmentDetailDto
    {
        public int RoleId { get; set; }
        public string RoleName { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
    }
}
