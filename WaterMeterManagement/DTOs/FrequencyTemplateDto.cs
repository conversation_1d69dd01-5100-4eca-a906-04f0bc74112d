namespace WaterMeterManagement.DTOs
{
    public class FrequencyTemplateDto
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string? Description { get; set; }
        public string FrequencyType { get; set; } = string.Empty;
        public int IntervalValue { get; set; }
        public string? IntervalUnit { get; set; }
        public string? DayOfWeek { get; set; }
        public int? DayOfMonth { get; set; }
        public string? MonthOfYear { get; set; }
        public string? TimeOfDay { get; set; }
        public string Status { get; set; } = string.Empty;
        public string? Category { get; set; }
        public bool IsDefault { get; set; }
        public decimal? EstimatedDuration { get; set; }
        public string? Notes { get; set; }
        public int UsageCount { get; set; }
        public DateTime? LastUsed { get; set; }
        public string? AdvancedConfiguration { get; set; }
        
        public DateTime CreatedAt { get; set; }
        public string CreatedBy { get; set; } = string.Empty;
        public DateTime? UpdatedAt { get; set; }
        public string? UpdatedBy { get; set; }
    }

    public class FrequencyTemplateListDto
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string? Description { get; set; }
        public string FrequencyType { get; set; } = string.Empty;
        public int IntervalValue { get; set; }
        public string? IntervalUnit { get; set; }
        public string Status { get; set; } = string.Empty;
        public bool IsActive => Status == "Active";
        public string? Category { get; set; }
        public bool IsDefault { get; set; }
        public decimal? EstimatedDuration { get; set; }
        public int UsageCount { get; set; }
        public DateTime? LastUsed { get; set; }
        public DateTime CreatedAt { get; set; }
        public string CreatedBy { get; set; } = string.Empty;
    }

    public class CreateFrequencyTemplateDto
    {
        public string Name { get; set; } = string.Empty;
        public string? Description { get; set; }
        public string FrequencyType { get; set; } = string.Empty;
        public int IntervalValue { get; set; } = 1;
        public string? IntervalUnit { get; set; }
        public string? DayOfWeek { get; set; }
        public int? DayOfMonth { get; set; }
        public string? MonthOfYear { get; set; }
        public string? TimeOfDay { get; set; }
        public string? Category { get; set; }
        public bool IsDefault { get; set; } = false;
        public decimal? EstimatedDuration { get; set; }
        public string? Notes { get; set; }
        public string? AdvancedConfiguration { get; set; }
    }

    public class UpdateFrequencyTemplateDto
    {
        public string Name { get; set; } = string.Empty;
        public string? Description { get; set; }
        public string FrequencyType { get; set; } = string.Empty;
        public int IntervalValue { get; set; } = 1;
        public string? IntervalUnit { get; set; }
        public string? DayOfWeek { get; set; }
        public int? DayOfMonth { get; set; }
        public string? MonthOfYear { get; set; }
        public string? TimeOfDay { get; set; }
        public string Status { get; set; } = string.Empty;
        public string? Category { get; set; }
        public bool IsDefault { get; set; } = false;
        public decimal? EstimatedDuration { get; set; }
        public string? Notes { get; set; }
        public string? AdvancedConfiguration { get; set; }
    }

    public class FrequencyTemplateSearchDto
    {
        public int Page { get; set; } = 1;
        public int PageSize { get; set; } = 10;
        public string? SortBy { get; set; } = "CreatedAt";
        public string? SortDirection { get; set; } = "desc";
        public string? Name { get; set; }
        public string? FrequencyType { get; set; }
        public string? Status { get; set; }
        public string? Category { get; set; }
        public bool? IsDefault { get; set; }
        public int? MinUsageCount { get; set; }
        public int? MaxUsageCount { get; set; }
    }

    public class FrequencyCalculationDto
    {
        public DateTime StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public string FrequencyType { get; set; } = string.Empty;
        public int IntervalValue { get; set; } = 1;
        public string? IntervalUnit { get; set; }
        public string? DayOfWeek { get; set; }
        public int? DayOfMonth { get; set; }
        public string? MonthOfYear { get; set; }
        public string? TimeOfDay { get; set; }
        public int MaxOccurrences { get; set; } = 100;
    }

    public class FrequencyCalculationResultDto
    {
        public List<DateTime> ScheduledDates { get; set; } = new();
        public int TotalOccurrences { get; set; }
        public DateTime? FirstOccurrence { get; set; }
        public DateTime? LastOccurrence { get; set; }
        public string FrequencyDescription { get; set; } = string.Empty;
    }

    public class FrequencyTemplateStatisticsDto
    {
        public int TotalTemplates { get; set; }
        public int ActiveTemplates { get; set; }
        public int DefaultTemplates { get; set; }
        public int TotalUsages { get; set; }

        public Dictionary<string, int> TypeBreakdown { get; set; } = new();
        public Dictionary<string, int> CategoryBreakdown { get; set; } = new();
        public Dictionary<string, int> StatusBreakdown { get; set; } = new();
        public List<FrequencyTemplateListDto> MostUsedTemplates { get; set; } = new();
    }

    public class DuplicateFrequencyTemplateDto
    {
        public string Name { get; set; } = string.Empty;
    }

    public class CalculateFrequencyRequestDto
    {
        public string StartDate { get; set; } = string.Empty;
        public string EndDate { get; set; } = string.Empty;
    }
}