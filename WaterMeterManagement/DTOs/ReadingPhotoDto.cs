namespace WaterMeterManagement.DTOs
{
    public class ReadingPhotoDto
    {
        public int Id { get; set; }
        public int ReadingId { get; set; }
        public int MeterReadingId { get; set; }
        public string FileName { get; set; } = string.Empty;
        public string OriginalFileName { get; set; } = string.Empty;
        public string CloudflareUrl { get; set; } = string.Empty;
        public string? ThumbnailUrl { get; set; }
        public int FileSizeBytes { get; set; }
        public long FileSize { get; set; }
        public string MimeType { get; set; } = string.Empty;
        public DateTime UploadTime { get; set; }
        public decimal? QualityScore { get; set; }
        public string? QualityStatus { get; set; }
        public bool IsProcessed { get; set; }
        public bool HasOCR { get; set; }
        public string? OcrResult { get; set; }
        public decimal? OcrConfidence { get; set; }
        public string? OcrStatus { get; set; }
        public bool IsOverridden { get; set; }
        public string? OverriddenBy { get; set; }
        public string? OverriddenDate { get; set; }
        public string? OverrideReason { get; set; }
        public string CreatedAt { get; set; } = string.Empty;
    }
}
