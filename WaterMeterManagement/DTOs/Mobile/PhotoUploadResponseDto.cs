namespace WaterMeterManagement.DTOs.Mobile
{
    /// <summary>
    /// Response DTO for photo upload operations
    /// </summary>
    public class PhotoUploadResponseDto
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public int? PhotoId { get; set; }
        public string? FilePath { get; set; }
        public string? FileName { get; set; }
        public long? FileSize { get; set; }
        public string? ContentType { get; set; }
        public DateTime? UploadTime { get; set; }
        public string? Error { get; set; }
    }

    /// <summary>
    /// DTO for photo information
    /// </summary>
    public class PhotoDto
    {
        public int Id { get; set; }
        public string FileName { get; set; } = string.Empty;
        public string FilePath { get; set; } = string.Empty;
        public long FileSize { get; set; }
        public string ContentType { get; set; } = string.Empty;
        public int? TaskId { get; set; }
        public int? MeterId { get; set; }
        public int UploadedByUserId { get; set; }
        public DateTime UploadTime { get; set; }
        public string? Description { get; set; }
    }
} 