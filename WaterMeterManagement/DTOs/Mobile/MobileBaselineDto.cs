namespace WaterMeterManagement.DTOs.Mobile
{
    /// <summary>
    /// Mobile baseline data transfer object for synchronization
    /// 移动端基线数据传输对象，用于同步
    /// </summary>
    public class MobileBaselineDto
    {
        public int Id { get; set; }
        public int MeterId { get; set; }
        public string MeterSerialNumber { get; set; } = string.Empty;
        public DateTime BaselineDate { get; set; }
        public decimal BaselineValue { get; set; }
        public string BaselineType { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
        public string? ImportBatch { get; set; }
        public string? SourceFile { get; set; }
        public string DataSource { get; set; } = string.Empty;
        public string? ValidationNotes { get; set; }
        public bool IsValidated { get; set; }
        public DateTime? ValidatedDate { get; set; }
        public string? ValidatedBy { get; set; }
        public bool HasValidationErrors { get; set; }
        public string? ValidationErrors { get; set; }
        public bool IsAnomalous { get; set; }
        public string? AnomalyDescription { get; set; }
        public int? PreviousBaselineId { get; set; }
        public decimal? PreviousBaselineValue { get; set; }
        public decimal? VarianceFromPrevious { get; set; }
        public decimal? VariancePercentage { get; set; }
        public bool IsCorrected { get; set; }
        public DateTime? CorrectedDate { get; set; }
        public string? CorrectedBy { get; set; }
        public string? CorrectionReason { get; set; }
        public int ConfidenceLevel { get; set; }
        public string? Notes { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public string CreatedBy { get; set; } = string.Empty;
        public string UpdatedBy { get; set; } = string.Empty;
    }
} 