namespace WaterMeterManagement.DTOs.Mobile
{
    /// <summary>
    /// DTO for syncing tasks to mobile devices
    /// Simplified version focused on sync requirements
    /// </summary>
    public class MobileTaskSyncDto
    {
        public int Id { get; set; }
        public int AssignedUserId { get; set; }
        public int MeterId { get; set; }
        public string MeterNumber { get; set; } = string.Empty;
        public string TaskType { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
        public string Priority { get; set; } = string.Empty;
        public DateTime? ScheduledDate { get; set; }
        public DateTime? DueDate { get; set; }
        public string? Location { get; set; }
        public string? Instructions { get; set; }
        public string? Notes { get; set; }
        public string? WorkPackageName { get; set; }
        public string? RouteName { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
    }
} 