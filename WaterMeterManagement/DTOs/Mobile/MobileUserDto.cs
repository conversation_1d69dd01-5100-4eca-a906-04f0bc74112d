using System.ComponentModel.DataAnnotations;

namespace WaterMeterManagement.DTOs.Mobile
{
    /// <summary>
    /// Mobile user workload information
    /// </summary>
    public class MobileUserWorkloadDto
    {
        public int UserId { get; set; }
        public string UserName { get; set; } = string.Empty;
        public string FullName { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public string? Department { get; set; }
        public string? Zone { get; set; }
        
        // Workload statistics
        public int ActiveTaskCount { get; set; }
        public int CompletedTaskCount { get; set; }
        public int OverdueTaskCount { get; set; }
        public int TotalAssignedToday { get; set; }
        public double WorkloadPercentage { get; set; }
        public double EfficiencyScore { get; set; }
        
        // Today's statistics
        public int TodayCompletedCount { get; set; }
        public int TodayPendingCount { get; set; }
        public double TodayProgress { get; set; }
        
        // This week's statistics
        public int WeekCompletedCount { get; set; }
        public int WeekAssignedCount { get; set; }
        
        // Status information
        public string AvailabilityStatus { get; set; } = "Available"; // Available, Busy, Offline
        public DateTime? LastActivity { get; set; }
        public DateTime? LastLocationUpdate { get; set; }
        public double? CurrentLatitude { get; set; }
        public double? CurrentLongitude { get; set; }
        
        // Capacity and skills
        public int MaxCapacity { get; set; } = 10;
        public List<string> Skills { get; set; } = new();
        
        // Mobile-specific calculations
        public bool IsAtCapacity => ActiveTaskCount >= MaxCapacity;
        public bool IsOverloaded => WorkloadPercentage > 100;
        public string WorkloadStatus => WorkloadPercentage switch
        {
            <= 50 => "Light",
            <= 75 => "Moderate", 
            <= 90 => "Heavy",
            _ => "At Capacity"
        };
    }

    /// <summary>
    /// Detailed user statistics for mobile dashboard
    /// </summary>
    public class MobileUserStatsDto
    {
        // User identification
        public int UserId { get; set; }
        
        // Reading statistics
        public int TotalReadings { get; set; }
        public int SuccessfulReadings { get; set; }
        public int PendingReadings { get; set; }
        public double SuccessRate { get; set; }
        
        // Overall statistics
        public int TotalTasksAssigned { get; set; }
        public int TotalTasksCompleted { get; set; }
        public int TotalReadingsSubmitted { get; set; }
        public double AverageCompletionTime { get; set; } // in hours
        public double CompletionRate { get; set; } // percentage
        public int CurrentStreak { get; set; } // consecutive completion days
        
        // This week
        public int WeekTasksCompleted { get; set; }
        public int WeekReadingsSubmitted { get; set; }
        public double WeekAverageTime { get; set; }
        
        // This month
        public int MonthTasksCompleted { get; set; }
        public int MonthReadingsSubmitted { get; set; }
        public double MonthAverageTime { get; set; }
        
        // Quality metrics
        public double AverageQualityScore { get; set; }
        public int PhotosSubmitted { get; set; }
        public int IssuesReported { get; set; }
        public int CustomersContacted { get; set; }
        
        // Performance trends
        public List<DailyPerformanceDto> Last7Days { get; set; } = new();
        public List<MonthlyPerformanceDto> Last6Months { get; set; } = new();
        
        // Recognition and achievements
        public List<string> Achievements { get; set; } = new();
        public int Rank { get; set; } // among all users
        public int TotalUsers { get; set; }
        
        // Recent activity
        public DateTime? LastTaskCompleted { get; set; }
        public DateTime? LastReadingSubmitted { get; set; }
        public string? LastTaskLocation { get; set; }
    }

    /// <summary>
    /// Daily performance data point
    /// </summary>
    public class DailyPerformanceDto
    {
        public DateTime Date { get; set; }
        public int TasksCompleted { get; set; }
        public int ReadingsSubmitted { get; set; }
        public double HoursWorked { get; set; }
        public double AverageQuality { get; set; }
    }

    /// <summary>
    /// Monthly performance data point
    /// </summary>
    public class MonthlyPerformanceDto
    {
        public int Year { get; set; }
        public int Month { get; set; }
        public string MonthName { get; set; } = string.Empty;
        public int TasksCompleted { get; set; }
        public int ReadingsSubmitted { get; set; }
        public double TotalHours { get; set; }
        public double CompletionRate { get; set; }
    }

    /// <summary>
    /// User location update request
    /// </summary>
    public class LocationUpdateDto
    {
        [Required]
        [Range(-90, 90, ErrorMessage = "Latitude must be between -90 and 90")]
        public double Latitude { get; set; }
        
        [Required] 
        [Range(-180, 180, ErrorMessage = "Longitude must be between -180 and 180")]
        public double Longitude { get; set; }
        
        public double? Accuracy { get; set; }
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
        public string ActivityType { get; set; } = "general"; // reading, travel, break, general
        public string? Notes { get; set; }
    }

    /// <summary>
    /// Mobile user profile information
    /// </summary>
    public class MobileUserProfileDto
    {
        public int UserId { get; set; }
        public string UserName { get; set; } = string.Empty;
        public string FullName { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public string? Phone { get; set; }
        public string? Department { get; set; }
        public string? Zone { get; set; }
        public string Role { get; set; } = string.Empty;
        
        // Employment info
        public DateTime? StartDate { get; set; }
        public string? EmployeeId { get; set; }
        public string? Supervisor { get; set; }
        
        // App preferences
        public MobileAppPreferencesDto Preferences { get; set; } = new();
        
        // Device information
        public List<MobileDeviceDto> RegisteredDevices { get; set; } = new();
        
        // Last login info
        public DateTime? LastLoginDate { get; set; }
        public string? LastLoginDevice { get; set; }
        public string? LastLoginLocation { get; set; }
    }

    /// <summary>
    /// Mobile app preferences
    /// </summary>
    public class MobileAppPreferencesDto
    {
        public bool EnableNotifications { get; set; } = true;
        public bool EnableLocationTracking { get; set; } = true;
        public bool EnableOfflineMode { get; set; } = true;
        public bool EnableAutoSync { get; set; } = true;
        public string Theme { get; set; } = "auto"; // light, dark, auto
        public string Language { get; set; } = "en";
        public int SyncIntervalMinutes { get; set; } = 30;
        public bool RequirePhotoForReading { get; set; } = true;
        public bool EnableGPSValidation { get; set; } = true;
    }

    /// <summary>
    /// Mobile device information
    /// </summary>
    public class MobileDeviceDto
    {
        public string DeviceId { get; set; } = string.Empty;
        public string DeviceName { get; set; } = string.Empty;
        public string Platform { get; set; } = string.Empty; // iOS, Android
        public string OSVersion { get; set; } = string.Empty;
        public string AppVersion { get; set; } = string.Empty;
        public DateTime LastUsed { get; set; }
        public bool IsActive { get; set; } = true;
        public DateTime RegisteredDate { get; set; }
    }

    /// <summary>
    /// User status update request
    /// </summary>
    public class UserStatusUpdateDto
    {
        [Required]
        public string Status { get; set; } = string.Empty; // Available, Busy, Break, Offline
        public string? Notes { get; set; }
        public DateTime? StatusUntil { get; set; }
        public double? Latitude { get; set; }
        public double? Longitude { get; set; }
    }
} 