using System.ComponentModel.DataAnnotations;

namespace WaterMeterManagement.DTOs.Mobile
{
    /// <summary>
    /// 移动端读数DTO - 与MeterReading模型对齐
    /// Mobile Reading DTO - Aligned with MeterReading Model
    /// </summary>
    public class MobileReadingDto
    {
        [Required]
        public int MeterId { get; set; }

        [Required]
        public int UserId { get; set; }

        [Required]
        public int TaskId { get; set; }

        public string? MeterNumber { get; set; }

        [Required]
        public decimal ReadingValue { get; set; }

        [Required]
        public DateTime ReadingDate { get; set; }

        [Required]
        [StringLength(20)]
        public string ReadingMethod { get; set; } = "Manual";

        [StringLength(20)]
        public string ReadingType { get; set; } = "Regular";

        [StringLength(20)]
        public string DataSource { get; set; } = "Mobile";

        public bool HasOCR { get; set; } = false;

        [StringLength(20)]
        public string? OCRStatus { get; set; }

        public decimal? OcrConfidence { get; set; }

        public decimal? Latitude { get; set; }
        public decimal? Longitude { get; set; }
        public decimal? GpsAccuracy { get; set; }

        [StringLength(200)]
        public string? Location { get; set; }

        [Required]
        [StringLength(20)]
        public string Status { get; set; } = "Completed";

        [StringLength(50)]
        public string? ValidationStatus { get; set; }

        public bool IsValidated { get; set; } = false;
        public int? ValidatedBy { get; set; }
        public DateTime? ValidationDate { get; set; }

        [StringLength(500)]
        public string? ValidationComments { get; set; }

        public bool IsAnomalous { get; set; } = false;

        [StringLength(200)]
        public string? AnomalyReason { get; set; }

        [StringLength(100)]
        public string? AnomalyType { get; set; }

        public bool CantRead { get; set; } = false;

        [StringLength(200)]
        public string? CantReadReason { get; set; }

        [StringLength(500)]
        public string? Notes { get; set; }

        // ============ 移动端专用字段 ============
        public List<MobilePhotoDto> Photos { get; set; } = new List<MobilePhotoDto>(); // 照片数组

        [StringLength(200)]
        public string? DeviceInfo { get; set; }               // 设备信息

        [StringLength(20)]
        public string? AppVersion { get; set; }               // 应用版本

        public bool IsOfflineReading { get; set; } = false;   // 是否离线读数

        [StringLength(50)]
        public string? OfflineId { get; set; }                // 离线唯一标识

        // ============ 同步状态 (仅用于响应) ============
        public string? SyncStatus { get; set; }               // 同步状态
        public string? SyncError { get; set; }                // 同步错误信息
        public int SyncAttempts { get; set; } = 0;            // 同步尝试次数

        // ============ 其他兼容性属性 ============
        public decimal? PreviousReading { get; set; }         // 上次读数
        public bool RequiresReview { get; set; } = false;     // 需要审核
        public string? ReviewReason { get; set; }             // 审核原因
    }

    /// <summary>
    /// 移动端照片DTO
    /// Mobile Photo DTO
    /// </summary>
    public class MobilePhotoDto
    {
        public int? Id { get; set; }                          // 服务器端ID

        [Required]
        [StringLength(50)]
        public string Uuid { get; set; } = string.Empty;     // 本地唯一标识符

        // 照片信息
        [Required]
        [StringLength(255)]
        public string FileName { get; set; } = string.Empty; // 文件名

        [StringLength(500)]
        public string? FilePath { get; set; }                // 本地文件路径

        public string? Base64Data { get; set; }              // Base64数据 (用于上传)

        [Required]
        public long FileSizeBytes { get; set; }              // 文件大小

        [Required]
        [StringLength(50)]
        public string MimeType { get; set; } = string.Empty; // MIME类型

        // 拍摄信息
        [Required]
        public DateTime CapturedAt { get; set; }             // 拍摄时间

        public decimal? Latitude { get; set; }               // 拍摄位置纬度
        public decimal? Longitude { get; set; }              // 拍摄位置经度

        [Required]
        [StringLength(20)]
        public string PhotoType { get; set; } = "meter";     // 照片类型

        // 同步状态 (仅用于响应)
        public string? SyncStatus { get; set; }              // 同步状态
        public string? SyncError { get; set; }               // 同步错误信息
    }

    /// <summary>
    /// 批量读数提交请求
    /// Batch Reading Submission Request
    /// </summary>
    public class BatchMobileReadingDto
    {
        [Required]
        public List<MobileReadingDto> Readings { get; set; } = new List<MobileReadingDto>();

        [StringLength(200)]
        public string? DeviceInfo { get; set; }              // 设备信息

        [StringLength(20)]
        public string? AppVersion { get; set; }              // 应用版本

        public DateTime SubmissionTime { get; set; } = DateTime.UtcNow; // 提交时间
    }

    /// <summary>
    /// 读数提交响应
    /// Reading Submission Response
    /// </summary>
    public class MobileReadingResponseDto
    {
        public bool Success { get; set; }
        public string? Message { get; set; }
        public int? ReadingId { get; set; }                  // 服务器端读数ID
        public List<string> Errors { get; set; } = new List<string>();
        public List<string> Warnings { get; set; } = new List<string>();

        // 验证结果
        public bool ValidationPassed { get; set; }
        public bool AnomalyDetected { get; set; }
        public string? AnomalyType { get; set; }
        public decimal? SuggestedValue { get; set; }

        // 照片上传结果
        public List<PhotoUploadResult> PhotoResults { get; set; } = new List<PhotoUploadResult>();
    }

    /// <summary>
    /// 照片上传结果
    /// Photo Upload Result
    /// </summary>
    public class PhotoUploadResult
    {
        public string Uuid { get; set; } = string.Empty;     // 本地UUID
        public int? PhotoId { get; set; }                    // 服务器端照片ID
        public bool Success { get; set; }
        public string? Error { get; set; }
        public string? Url { get; set; }                     // 照片URL (如果成功)

        // Additional properties needed by services
        public string? OriginalFileName { get; set; }        // 原始文件名
        public string? UploadedFileName { get; set; }        // 上传后文件名
        public string? FileName { get; set; }               // 文件名
        public string? CloudflareUrl { get; set; }          // Cloudflare R2 URL
        public string? ThumbnailUrl { get; set; }           // 缩略图URL
        public int FileSizeBytes { get; set; }              // 文件大小（字节）
        public string? MimeType { get; set; }               // MIME类型
    }

    /// <summary>
    /// 批量读数提交响应
    /// Batch Reading Submission Response
    /// </summary>
    public class BatchMobileReadingResponseDto
    {
        public bool Success { get; set; }
        public string? Message { get; set; }
        public int SuccessCount { get; set; }
        public int FailureCount { get; set; }
        public List<MobileReadingResponseDto> Results { get; set; } = new List<MobileReadingResponseDto>();
        public List<string> GeneralErrors { get; set; } = new List<string>();
    }

    /// <summary>
    /// 完成任务请求
    /// Complete Task Request
    /// </summary>
    public class CompleteTaskRequest
    {
        [Required]
        public MobileReadingDto Reading { get; set; } = new MobileReadingDto();

        [StringLength(500)]
        public string? CompletionNotes { get; set; }

        public DateTime TaskStartTime { get; set; }
        public DateTime TaskCompletionTime { get; set; }

        // 位置验证
        public decimal? CompletionLatitude { get; set; }
        public decimal? CompletionLongitude { get; set; }
    }

    /// <summary>
    /// 读数验证结果
    /// Reading Validation Result
    /// </summary>
    public class ReadingValidationDto
    {
        public bool IsValid { get; set; }
        public List<string> Errors { get; set; } = new List<string>();
        public List<string> Warnings { get; set; } = new List<string>();

        // 异常检测
        public bool AnomalyDetected { get; set; }
        public string? AnomalyType { get; set; }
        public string? AnomalyReason { get; set; }

        // 建议值
        public decimal? SuggestedValue { get; set; }
        public int ConfidenceScore { get; set; } // 0-100

        // 消费分析
        public decimal? CalculatedConsumption { get; set; }
        public bool IsConsumptionNormal { get; set; } = true;
        public string? ConsumptionAnalysis { get; set; }
    }








}