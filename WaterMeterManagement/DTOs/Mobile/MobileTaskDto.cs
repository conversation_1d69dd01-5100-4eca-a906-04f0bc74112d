using System.ComponentModel.DataAnnotations;

namespace WaterMeterManagement.DTOs.Mobile
{
    /// <summary>
    /// Mobile-optimized Task DTO for mobile app consumption
    /// </summary>
    public class MobileTaskDto
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string? Description { get; set; }
        public string Status { get; set; } = string.Empty;
        public string Priority { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty;
        public DateTime? DueDate { get; set; }
        public DateTime? StartDate { get; set; }
        public string? Location { get; set; }
        public string? Instructions { get; set; }
        public string? Notes { get; set; }
        
        // Water meter information
        public int MeterId { get; set; }
        public string MeterNumber { get; set; } = string.Empty;
        public string? MeterType { get; set; }
        public string? AssetId { get; set; }
        public string? AccountNumber { get; set; }
        public double? LastReading { get; set; }
        public DateTime? LastReadingDate { get; set; }
        
        // Customer information
        public string? CustomerName { get; set; }
        public string? CustomerPhone { get; set; }
        public string? CustomerEmail { get; set; }
        public string? Address { get; set; }
        
        // Location information
        public double? Latitude { get; set; }
        public double? Longitude { get; set; }
        
        // Assignment information - 统一使用数据库字段名
        public int? AssignedUserId { get; set; }
        public DateTime AssignedDate { get; set; }
        public string AssignedBy { get; set; } = string.Empty;
        public string AssignmentType { get; set; } = string.Empty;
        
        // Progress information
        public int? EstimatedHours { get; set; }
        public int ProgressPercentage { get; set; } = 0;
        
        // Additional required properties
        public int? EstimatedDuration { get; set; }
        public TaskLocationDto? TaskLocation { get; set; }
        public MeterInfoDto? MeterInfo { get; set; }
        public TaskAssignmentInfoDto? AssignmentInfo { get; set; }
        public TaskProgressDto? ProgressInfo { get; set; }
        
        // Additional properties for task management
        public TaskAssignmentInfoDto? TaskAssignment { get; set; }
        public TaskProgressDto? TaskProgress { get; set; }
        public RouteInfoDto? RouteInfo { get; set; }
        public WorkPackageInfoDto? WorkPackageInfo { get; set; }
        public List<string> SpecialInstructions { get; set; } = new();
        public List<string> SafetyNotes { get; set; } = new();
        public List<string> RequiredPhotos { get; set; } = new();
        
        // Computed properties - made settable for API serialization
        public bool IsOverdue { get; set; }
        public bool IsToday { get; set; }
        public int? DaysUntilDue { get; set; }
        
        // Mobile-specific fields - made settable for API serialization
        public bool IsUrgent { get; set; }
        
        // Audit fields for mobile display
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
    }

    /// <summary>
    /// Detailed task information for mobile task detail view
    /// </summary>
    public class MobileTaskDetailDto : MobileTaskDto
    {
        // Additional detailed information
        public string? WorkPackageName { get; set; }
        public string? RouteName { get; set; }
        public DateTime? CompletedDate { get; set; }
        public double? ActualHours { get; set; }
        
        // Mobile detail collections
        public List<MobileReadingHistoryDto> ReadingHistory { get; set; } = new List<MobileReadingHistoryDto>();
        public List<MobileTaskAssignmentHistoryDto> AssignmentHistory { get; set; } = new List<MobileTaskAssignmentHistoryDto>();
        public List<string> AttachedPhotos { get; set; } = new List<string>();
        public List<string> Alerts { get; set; } = new List<string>();
        
        // 不重复定义基类已有的属性：RouteInfo, WorkPackageInfo, SpecialInstructions, SafetyNotes, RequiredPhotos
        // 这些属性已经从 MobileTaskDto 基类继承
        
        public bool RequiresCustomerContact { get; set; } = false;
        public string? AccessInstructions { get; set; }
        public List<string> RequiredEquipment { get; set; } = new();
        public string? CompletionNotes { get; set; }
        public bool HasPhotos { get; set; } = false;
        public int PhotoCount { get; set; } = 0;
    }

    /// <summary>
    /// Reading history item for mobile display
    /// </summary>
    public class MobileReadingHistoryDto
    {
        public int Id { get; set; }
        public double ReadingValue { get; set; }
        public DateTime ReadingDate { get; set; }
        public string ReadingMethod { get; set; } = string.Empty;
        public string? Notes { get; set; }
        public string ReadByUser { get; set; } = string.Empty;
        public int? QualityScore { get; set; }
    }

    /// <summary>
    /// Task assignment history for mobile display
    /// </summary>
    public class MobileTaskAssignmentHistoryDto
    {
        public int Id { get; set; }
        public DateTime AssignedDate { get; set; }
        public string AssignedBy { get; set; } = string.Empty;
        public string AssignmentType { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
        public DateTime? AcceptedDate { get; set; }
        public DateTime? RejectedDate { get; set; }
        public string? Reason { get; set; }
    }

    /// <summary>
    /// Request model for starting a task
    /// </summary>
    public class StartTaskRequest
    {
        public double? StartLatitude { get; set; }
        public double? StartLongitude { get; set; }
        public string? Notes { get; set; }
        public string DeviceInfo { get; set; } = string.Empty;
    }

    /// <summary>
    /// Request model for updating task status
    /// </summary>
    public class UpdateTaskStatusRequest
    {
        [Required]
        public string Status { get; set; } = string.Empty;
        public string? Notes { get; set; }
        public double? Latitude { get; set; }
        public double? Longitude { get; set; }
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    }

    #region Additional Mobile DTOs

    /// <summary>
    /// Generic service result class
    /// </summary>
    public class ServiceResult
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
    }

    /// <summary>
    /// Generic service result with data
    /// </summary>
    public class ServiceResult<T>
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public T? Data { get; set; }
    }

    /// <summary>
    /// Mobile task summary DTO for dashboard
    /// </summary>
    public class MobileTaskSummaryDto
    {
        public int TotalAssigned { get; set; }
        public int InProgress { get; set; }
        public int Completed { get; set; }
        public int Overdue { get; set; }
        public int DueToday { get; set; }
        public int DueTomorrow { get; set; }
        public decimal CompletionRate { get; set; }
        public int ConsecutiveDaysActive { get; set; }
        public List<UrgentTaskDto> UrgentTasks { get; set; } = new List<UrgentTaskDto>();
    }

    /// <summary>
    /// Urgent task summary DTO
    /// </summary>
    public class UrgentTaskDto
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public DateTime DueDate { get; set; }
        public int HoursOverdue { get; set; }
        public string Priority { get; set; } = string.Empty;
    }

    /// <summary>
    /// Task status sync request for offline support
    /// </summary>
    public class TaskStatusSyncRequest
    {
        public List<TaskStatusUpdate> StatusUpdates { get; set; } = new();
        public DateTime SyncTimestamp { get; set; } = DateTime.UtcNow;
    }

    /// <summary>
    /// Individual task status update
    /// </summary>
    public class TaskStatusUpdate
    {
        public int TaskId { get; set; }
        public string Status { get; set; } = string.Empty;
        public DateTime UpdateTime { get; set; }
        public string? Notes { get; set; }
        public double? Latitude { get; set; }
        public double? Longitude { get; set; }
        public string? OfflineId { get; set; } // For offline conflict resolution
    }

    /// <summary>
    /// Task sync response
    /// </summary>
    public class TaskSyncResponseDto
    {
        public int TotalUpdates { get; set; }
        public int SuccessfulUpdates { get; set; }
        public int FailedUpdates { get; set; }
        public List<TaskSyncResult> Results { get; set; } = new();
        public List<string> GeneralErrors { get; set; } = new();
    }

    /// <summary>
    /// Individual task sync result
    /// </summary>
    public class TaskSyncResult
    {
        public int TaskId { get; set; }
        public string? OfflineId { get; set; }
        public bool Success { get; set; }
        public string? ErrorMessage { get; set; }
        public DateTime? ServerTimestamp { get; set; }
    }

    /// <summary>
    /// Update reading request
    /// </summary>
    public class UpdateReadingRequest
    {
        public double ReadingValue { get; set; }
        public string? Notes { get; set; }
        public DateTime ReadingDate { get; set; }
        public string? ValidationStatus { get; set; }
    }

    /// <summary>
    /// User leaderboard DTO
    /// </summary>
    public class UserLeaderboardDto
    {
        public int UserId { get; set; }
        public string UserName { get; set; } = string.Empty;
        public string FullName { get; set; } = string.Empty;
        public int TasksCompleted { get; set; }
        public double CompletionRate { get; set; }
        public double AverageQuality { get; set; }
        public int Rank { get; set; }
        public int Points { get; set; }
    }

    /// <summary>
    /// User achievement DTO
    /// </summary>
    public class UserAchievementDto
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Category { get; set; } = string.Empty;
        public int Points { get; set; }
        public DateTime AchievedDate { get; set; }
        public string IconUrl { get; set; } = string.Empty;
    }

    /// <summary>
    /// Mobile reading stats DTO
    /// </summary>
    public class MobileReadingStatsDto
    {
        public int TotalReadings { get; set; }
        public int ReadingsThisWeek { get; set; }
        public int ReadingsThisMonth { get; set; }
        public double AverageQualityScore { get; set; }
        public int PhotosUploaded { get; set; }
        public int IssuesReported { get; set; }
        public double AverageReadingTime { get; set; }
        public DateTime? LastReadingDate { get; set; }
        public int ConsecutiveDaysWithReadings { get; set; }
        public List<DailyReadingCount> Last7Days { get; set; } = new();
    }

    /// <summary>
    /// Daily reading count
    /// </summary>
    public class DailyReadingCount
    {
        public DateTime Date { get; set; }
        public int Count { get; set; }
        public double AverageTime { get; set; }
    }

    /// <summary>
    /// Nearby user DTO
    /// </summary>
    public class NearbyUserDto
    {
        public int UserId { get; set; }
        public string UserName { get; set; } = string.Empty;
        public string FullName { get; set; } = string.Empty;
        public double DistanceKm { get; set; }
        public double? Latitude { get; set; }
        public double? Longitude { get; set; }
        public string Status { get; set; } = string.Empty;
        public DateTime LastSeen { get; set; }
    }

    /// <summary>
    /// Offline reading sync request
    /// </summary>
    public class OfflineReadingSyncRequest
    {
        public List<OfflineReadingDto> OfflineReadings { get; set; } = new();
        public DateTime SyncTimestamp { get; set; } = DateTime.UtcNow;
    }

    /// <summary>
    /// Offline reading DTO
    /// </summary>
    public class OfflineReadingDto
    {
        public string OfflineId { get; set; } = string.Empty;
        public int TaskId { get; set; }
        public int MeterId { get; set; }
        public double ReadingValue { get; set; }
        public DateTime ReadingDate { get; set; }
        public string? Notes { get; set; }
        public List<MobilePhotoDto> Photos { get; set; } = new();
    }

    /// <summary>
    /// Offline reading sync response
    /// </summary>
    public class OfflineReadingSyncResponse
    {
        public int TotalOfflineReadings { get; set; }
        public int SuccessfullySynced { get; set; }
        public int Failed { get; set; }
        public List<OfflineReadingSyncResult> Results { get; set; } = new();
        public List<string> GeneralErrors { get; set; } = new();
    }

    /// <summary>
    /// Offline reading sync result
    /// </summary>
    public class OfflineReadingSyncResult
    {
        public string OfflineId { get; set; } = string.Empty;
        public int? ServerReadingId { get; set; }
        public bool Success { get; set; }
        public string? ErrorMessage { get; set; }
        public DateTime? ServerTimestamp { get; set; }
    }

    /// <summary>
    /// Mobile sync request DTO
    /// </summary>
    public class MobileSyncRequestDto
    {
        public DateTime LastSyncTime { get; set; }
        public string AppVersion { get; set; } = string.Empty;
        public string DeviceId { get; set; } = string.Empty;
        public int PendingTaskCount { get; set; }
        public int OfflineReadingCount { get; set; }
    }

    /// <summary>
    /// Mobile sync response DTO
    /// </summary>
    public class MobileSyncResponseDto
    {
        public DateTime ServerTime { get; set; }
        public bool HasNewAssignments { get; set; }
        public int NewAssignmentCount { get; set; }
        public bool HasUpdatedTasks { get; set; }
        public int UpdatedTaskCount { get; set; }
        public bool RequiresFullSync { get; set; }
        
        // Additional properties needed by MeterReadingService
        public bool SyncSuccess { get; set; }
        public int ProcessedCount { get; set; }
        public int FailedCount { get; set; }
        public List<string> SyncErrors { get; set; } = new();
        
        public List<string> SystemMessages { get; set; } = new();
        public List<string> Notifications { get; set; } = new();
        public bool IsAppVersionSupported { get; set; }
    }

    #endregion
    
    #region Supporting DTO Classes

    /// <summary>
    /// Task location information DTO
    /// </summary>
    public class TaskLocationDto
    {
        public double? Latitude { get; set; }
        public double? Longitude { get; set; }
        public string? Address { get; set; }
        public string? Instructions { get; set; }
        public double? Accuracy { get; set; }
    }

    /// <summary>
    /// Meter information DTO
    /// </summary>
    public class MeterInfoDto
    {
        public int Id { get; set; }
        public int MeterId { get; set; }
        public string MeterNumber { get; set; } = string.Empty;
        public string SerialNumber { get; set; } = string.Empty;
        public string? MeterType { get; set; }
        public string? Brand { get; set; }
        public string? Model { get; set; }
        public string? Size { get; set; }
        public string? Type { get; set; }
        public string? AssetId { get; set; }
        public string? AccountNumber { get; set; }
        public double? LastReading { get; set; }
        public DateTime? LastReadingDate { get; set; }
        public DateTime? InstallDate { get; set; }
        public string? ManufacturerName { get; set; }
        public string? Status { get; set; }
        public string? Location { get; set; }
    }

    /// <summary>
    /// Task assignment information DTO
    /// </summary>
    public class TaskAssignmentInfoDto
    {
        public int? AssignedUserId { get; set; }
        public string? AssignedUserName { get; set; }
        public DateTime? AssignedDate { get; set; }
        public DateTime? AcceptedDate { get; set; }
        public bool CanReject { get; set; } = true;
        public bool CanAccept { get; set; } = true;
        public string Notes { get; set; } = string.Empty;
        public string? AssignmentType { get; set; }
        public string? AssignmentPriority { get; set; }
    }

    /// <summary>
    /// Task progress information DTO
    /// </summary>
    public class TaskProgressDto
    {
        public string Status { get; set; } = string.Empty;
        public double Progress { get; set; } = 0.0;
        public DateTime? StartedAt { get; set; }
        public DateTime? CompletedAt { get; set; }
        public int? TimeSpent { get; set; } // in minutes
        public string? CurrentStep { get; set; }
        public int? TotalSteps { get; set; }
        public int? CompletedSteps { get; set; }
    }

    /// <summary>
    /// Route information DTO
    /// </summary>
    public class RouteInfoDto
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string? Description { get; set; }
        public string? Area { get; set; }
        public int? EstimatedDuration { get; set; }
    }

    /// <summary>
    /// Work package information DTO
    /// </summary>
    public class WorkPackageInfoDto
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string? Description { get; set; }
        public string? Status { get; set; }
        public DateTime? CreatedDate { get; set; }
    }

    #endregion

} 