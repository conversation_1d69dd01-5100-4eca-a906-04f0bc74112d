using System.ComponentModel.DataAnnotations;

namespace WaterMeterManagement.DTOs
{
    /// <summary>
    /// Configuration item DTO for API responses
    /// </summary>
    public class ConfigurationDto
    {
        public int Id { get; set; }
        public string Scope { get; set; } = string.Empty;
        public string Category { get; set; } = string.Empty;
        public string Key { get; set; } = string.Empty;
        public string DisplayName { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string DataType { get; set; } = string.Empty;
        public object? Value { get; set; }
        public object? DefaultValue { get; set; }
        public int? UserId { get; set; }
        public int? RoleId { get; set; }
        public bool IsSystemDefined { get; set; }
        public bool IsEncrypted { get; set; }
        public object? ValidationRules { get; set; }
        public int Priority { get; set; }
        public bool IsActive { get; set; }
    }

    /// <summary>
    /// Configuration template DTO for API responses
    /// </summary>
    public class ConfigurationTemplateDto
    {
        public int Id { get; set; }
        public string Scope { get; set; } = string.Empty;
        public string Category { get; set; } = string.Empty;
        public string CategoryDisplayName { get; set; } = string.Empty;
        public string CategoryIcon { get; set; } = string.Empty;
        public string Key { get; set; } = string.Empty;
        public string DisplayName { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string DataType { get; set; } = string.Empty;
        public object? DefaultValue { get; set; }
        public object? ValidationRules { get; set; }
        public string UIComponent { get; set; } = string.Empty;
        public object? UIProperties { get; set; }
        public int DisplayOrder { get; set; }
        public bool RequiresRestart { get; set; }
        public bool IsSensitive { get; set; }
        public bool IsReadOnly { get; set; }
        public bool IsActive { get; set; }
        public string Group { get; set; } = string.Empty;
        public string GroupDisplayName { get; set; } = string.Empty;
    }

    /// <summary>
    /// Request DTO for updating configurations
    /// </summary>
    public class UpdateConfigurationRequest
    {
        [Required]
        public string Scope { get; set; } = string.Empty;

        [Required]
        public string Category { get; set; } = string.Empty;

        [Required]
        public List<ConfigurationUpdateItem> Configurations { get; set; } = new();
    }

    /// <summary>
    /// Individual configuration update item
    /// </summary>
    public class ConfigurationUpdateItem
    {
        [Required]
        public string Key { get; set; } = string.Empty;

        public object? Value { get; set; }
    }

    /// <summary>
    /// Request DTO for getting configurations
    /// </summary>
    public class GetConfigurationRequest
    {
        [Required]
        public string Scope { get; set; } = string.Empty;

        public string? Category { get; set; }
        public int? UserId { get; set; }
        public int? RoleId { get; set; }
        public bool IncludeInherited { get; set; } = true;
    }

    /// <summary>
    /// Configuration category grouping
    /// </summary>
    public class ConfigurationCategoryDto
    {
        public string Category { get; set; } = string.Empty;
        public string CategoryDisplayName { get; set; } = string.Empty;
        public string CategoryIcon { get; set; } = string.Empty;
        public List<ConfigurationGroupDto> Groups { get; set; } = new();
    }

    /// <summary>
    /// Configuration group within a category
    /// </summary>
    public class ConfigurationGroupDto
    {
        public string Group { get; set; } = string.Empty;
        public string GroupDisplayName { get; set; } = string.Empty;
        public List<ConfigurationItemDto> Items { get; set; } = new();
    }

    /// <summary>
    /// Combined configuration item with template information
    /// </summary>
    public class ConfigurationItemDto
    {
        public string Key { get; set; } = string.Empty;
        public string DisplayName { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string DataType { get; set; } = string.Empty;
        public object? Value { get; set; }
        public object? DefaultValue { get; set; }
        public object? ValidationRules { get; set; }
        public string UIComponent { get; set; } = string.Empty;
        public object? UIProperties { get; set; }
        public int DisplayOrder { get; set; }
        public bool RequiresRestart { get; set; }
        public bool IsSensitive { get; set; }
        public bool IsReadOnly { get; set; }
        public bool IsInherited { get; set; }
        public string InheritedFrom { get; set; } = string.Empty; // 'System', 'Role', 'User'
    }

    /// <summary>
    /// Bulk configuration update response
    /// </summary>
    public class ConfigurationUpdateResponse
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public int UpdatedCount { get; set; }
        public int FailedCount { get; set; }
        public List<string> Errors { get; set; } = new();
        public List<string> RequiresRestart { get; set; } = new();
    }
} 