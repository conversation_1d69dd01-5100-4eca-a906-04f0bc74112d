namespace WaterMeterManagement.DTOs
{
    // ============ Bulk Sync DTOs ============
    
    public class BulkSyncResultDto
    {
        public bool Success { get; set; }
        public int TotalMeters { get; set; }
        public int SuccessfulSyncs { get; set; }
        public int FailedSyncs { get; set; }
        public List<string> Errors { get; set; } = new();
        public List<string> Warnings { get; set; } = new();
        public DateTime SyncStarted { get; set; }
        public DateTime SyncCompleted { get; set; }
        public string Summary => $"Synced {SuccessfulSyncs}/{TotalMeters} meters successfully";
    }

    // ============ Consumption Analysis DTOs ============
    
    public class ConsumptionAnalysisDto
    {
        public string AssetId { get; set; } = string.Empty;
        public string MeterNumber { get; set; } = string.Empty;
        public string AccountNumber { get; set; } = string.Empty;
        public ConsumptionSummaryDto Summary { get; set; } = new();
        public List<MonthlyConsumptionDto> MonthlyData { get; set; } = new();
        public ConsumptionTrendsDto Trends { get; set; } = new();
        public List<AnomalyDto> Anomalies { get; set; } = new();
        public ConsumptionForecastDto Forecast { get; set; } = new();
    }

    public class ConsumptionSummaryDto
    {
        public decimal TotalConsumption { get; set; }
        public decimal AverageMonthlyConsumption { get; set; }
        public decimal MinMonthlyConsumption { get; set; }
        public decimal MaxMonthlyConsumption { get; set; }
        public decimal CurrentMonthConsumption { get; set; }
        public decimal LastMonthConsumption { get; set; }
        public decimal YearToDateConsumption { get; set; }
        public string ConsumptionPattern { get; set; } = string.Empty; // "Stable", "Increasing", "Decreasing", "Irregular"
    }

    public class MonthlyConsumptionDto
    {
        public int Year { get; set; }
        public int Month { get; set; }
        public string MonthName { get; set; } = string.Empty;
        public decimal Consumption { get; set; }
        public decimal PreviousReading { get; set; }
        public decimal CurrentReading { get; set; }
        public DateTime ReadingDate { get; set; }
        public bool HasAnomaly { get; set; }
        public string? AnomalyReason { get; set; }
    }

    public class ConsumptionTrendsDto
    {
        public decimal TrendSlope { get; set; } // Positive = increasing, Negative = decreasing
        public string TrendDirection { get; set; } = string.Empty; // "Increasing", "Decreasing", "Stable"
        public decimal TrendConfidence { get; set; } // 0-1 confidence score
        public decimal SeasonalVariation { get; set; }
        public decimal AverageGrowthRate { get; set; }
        public bool IsUsageStable { get; set; }
    }

    public class AnomalyDto
    {
        public DateTime Date { get; set; }
        public decimal ReadingValue { get; set; }
        public decimal ExpectedValue { get; set; }
        public decimal Deviation { get; set; }
        public string AnomalyType { get; set; } = string.Empty; // "High", "Low", "Missing", "Invalid"
        public string Reason { get; set; } = string.Empty;
        public string Severity { get; set; } = string.Empty; // "Low", "Medium", "High", "Critical"
    }

    public class ConsumptionForecastDto
    {
        public List<ForecastPointDto> Forecast { get; set; } = new();
        public decimal ConfidenceInterval { get; set; }
        public string ForecastMethod { get; set; } = string.Empty;
        public DateTime ForecastGeneratedAt { get; set; }
    }

    public class ForecastPointDto
    {
        public DateTime Period { get; set; }
        public decimal PredictedConsumption { get; set; }
        public decimal LowerBound { get; set; }
        public decimal UpperBound { get; set; }
        public decimal Confidence { get; set; }
    }

    // ============ Anomaly Flag DTOs ============
    
    public class AnomalyFlagDto
    {
        public string AnomalyType { get; set; } = string.Empty; // "Reading", "Consumption", "Meter", "Location"
        public string Reason { get; set; } = string.Empty;
        public string AnomalyReason { get; set; } = string.Empty;
        public string Severity { get; set; } = "Medium"; // "Low", "Medium", "High", "Critical"
        public string Description { get; set; } = string.Empty;
        public bool RequiresInvestigation { get; set; } = true;
        public string? RecommendedAction { get; set; }
        public DateTime? TargetResolutionDate { get; set; }
    }

    // ============ Route Assignment DTOs ============
    
    public class RouteAssignmentDto
    {
        public List<string> AccountNumbers { get; set; } = new();
        public List<string> MeterAssetIds { get; set; } = new();
        public string AssignmentMethod { get; set; } = "Manual"; // "Manual", "Geographic", "Township", "RoadName"
        public bool OverrideExisting { get; set; } = false;
        public string? Reason { get; set; }
    }

    public class RouteAssignmentResultDto
    {
        public bool Success { get; set; }
        public int MetersAssigned { get; set; }
        public int MetersSkipped { get; set; }
        public int MetersErrored { get; set; }
        public List<string> AssignedAccountNumbers { get; set; } = new();
        public List<string> SkippedAccountNumbers { get; set; } = new();
        public List<string> Errors { get; set; } = new();
        public List<string> Warnings { get; set; } = new();
        public string Summary => $"Assigned {MetersAssigned}/{AccountNumbers.Count} meters to route";
        
        // This property is for internal use
        internal List<string> AccountNumbers { get; set; } = new();
    }

    public class AutoAssignmentCriteriaDto
    {
        public string AssignmentMethod { get; set; } = "Geographic"; // "Geographic", "Township", "RoadName", "Proximity"
        public string? Township { get; set; }
        public string? RoadName { get; set; }
        public decimal? MaxDistanceKm { get; set; }
        public int? MaxMeters { get; set; }
        public int? MaxMetersPerRoute { get; set; }
        public bool BalanceLoad { get; set; } = true;
        public bool PreferContiguousAreas { get; set; } = true;
        public List<string>? ExcludeAccountNumbers { get; set; }
    }

    // ============ Route Sync DTOs ============
    
    public class AmsSyncResultDto
    {
        public bool Success { get; set; }
        public int RoutesProcessed { get; set; }
        public int RoutesCreated { get; set; }
        public int RoutesUpdated { get; set; }
        public int RoutesSkipped { get; set; }
        public List<string> Errors { get; set; } = new();
        public List<string> Warnings { get; set; } = new();
        public DateTime SyncStarted { get; set; }
        public DateTime SyncCompleted { get; set; }
        public string Summary => $"Processed {RoutesProcessed} routes: {RoutesCreated} created, {RoutesUpdated} updated";
    }

    // ============ Route Schedule DTOs ============
    
    public class RouteReadingScheduleDto
    {
        public int RouteId { get; set; }
        public string RouteName { get; set; } = string.Empty;
        public string Township { get; set; } = string.Empty;
        public int MeterCount { get; set; }
        public List<ScheduledReadingDto> ScheduledReadings { get; set; } = new();
        public ReadingFrequencyDto Frequency { get; set; } = new();
        public RouteScheduleStatusDto Status { get; set; } = new();
    }

    public class ScheduledReadingDto
    {
        public DateTime ScheduledDate { get; set; }
        public string ReadingType { get; set; } = string.Empty; // "Regular", "Meter Exchange", "Final", "Special"
        public int EstimatedDurationMinutes { get; set; }
        public string? AssignedTo { get; set; }
        public string Status { get; set; } = "Pending"; // "Pending", "In Progress", "Completed", "Overdue"
        public int MetersToRead { get; set; }
        public int MetersCompleted { get; set; }
    }

    public class ReadingFrequencyDto
    {
        public string FrequencyType { get; set; } = string.Empty; // "Monthly", "Quarterly", "Semi-Annual", "Annual"
        public int IntervalDays { get; set; }
        public List<int> PreferredDaysOfMonth { get; set; } = new();
        public string? TimeSlot { get; set; } // "Morning", "Afternoon", "Evening"
    }

    public class RouteScheduleStatusDto
    {
        public string CurrentStatus { get; set; } = string.Empty; // "On Schedule", "Behind", "Ahead", "Critical"
        public DateTime? LastCompletedReading { get; set; }
        public DateTime? NextScheduledReading { get; set; }
        public int DaysUntilNext { get; set; }
        public decimal CompletionRate { get; set; } // Percentage of on-time completions
        public bool RequiresAttention { get; set; }
    }

    // ============ Route Coverage Analysis DTOs ============
    
    public class RouteCoverageRequestDto
    {
        public string? Township { get; set; }
        public List<string>? RoadNames { get; set; }
        public decimal? MaxRouteDistanceKm { get; set; }
        public int? MaxMetersPerRoute { get; set; }
        public bool AnalyzeGaps { get; set; } = true;
        public bool SuggestOptimizations { get; set; } = true;
    }

    public class RouteCoverageAnalysisDto
    {
        public CoverageStatisticsDto Statistics { get; set; } = new();
        public List<CoverageGapDto> CoverageGaps { get; set; } = new();
        public List<RouteOptimizationSuggestionDto> OptimizationSuggestions { get; set; } = new();
        public List<UnassignedAreaDto> UnassignedAreas { get; set; } = new();
        public EfficiencyMetricsDto EfficiencyMetrics { get; set; } = new();
    }

    public class CoverageStatisticsDto
    {
        public int TotalMeters { get; set; }
        public int AssignedMeters { get; set; }
        public int UnassignedMeters { get; set; }
        public decimal CoveragePercentage { get; set; }
        public int TotalRoutes { get; set; }
        public decimal AverageMetersPerRoute { get; set; }
        public decimal AverageRouteDistanceKm { get; set; }
    }

    public class CoverageGapDto
    {
        public string Description { get; set; } = string.Empty;
        public string? Township { get; set; }
        public string? RoadName { get; set; }
        public int AffectedMeters { get; set; }
        public string Severity { get; set; } = string.Empty; // "Low", "Medium", "High"
        public string? SuggestedAction { get; set; }
    }

    public class RouteOptimizationSuggestionDto
    {
        public int RouteId { get; set; }
        public string RouteName { get; set; } = string.Empty;
        public string SuggestionType { get; set; } = string.Empty; // "Split", "Merge", "Rebalance", "Realign"
        public string Description { get; set; } = string.Empty;
        public decimal EstimatedImprovementPercent { get; set; }
        public string Priority { get; set; } = string.Empty; // "Low", "Medium", "High"
    }

    public class UnassignedAreaDto
    {
        public string Township { get; set; } = string.Empty;
        public string? RoadName { get; set; }
        public int MeterCount { get; set; }
        public List<string> AccountNumbers { get; set; } = new();
        public string? NearestRoute { get; set; }
        public decimal? DistanceToNearestRouteKm { get; set; }
    }

    public class EfficiencyMetricsDto
    {
        public decimal AverageReadingTimeMinutes { get; set; }
        public decimal AverageTravelTimeMinutes { get; set; }
        public decimal RouteEfficiencyScore { get; set; } // 0-100 score
        public decimal OptimalRouteCount { get; set; }
        public decimal CurrentUtilizationPercent { get; set; }
        public List<string> EfficiencyRecommendations { get; set; } = new();
    }

} 