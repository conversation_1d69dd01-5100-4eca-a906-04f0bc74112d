using System;
using System.ComponentModel.DataAnnotations;

namespace WaterMeterManagement.DTOs
{
    public class AmisMeterDto
    {
        [Required]
        public string MeterId { get; set; } = string.Empty;
        
        public string SerialNumber { get; set; } = string.Empty;
        
        [Required]
        public string Address { get; set; } = string.Empty;
        
        public double? Latitude { get; set; }
        public double? Longitude { get; set; }
        
        public string MeterType { get; set; } = string.Empty;
        public string Brand { get; set; } = string.Empty;
        public string Model { get; set; } = string.Empty;
        public string MeterSize { get; set; } = string.Empty;
        
        public DateTime? InstallDate { get; set; }
        public string Status { get; set; } = "Active";
        
        public string PropertyId { get; set; } = string.Empty;
        public string CustomerName { get; set; } = string.Empty;
        
        // Additional AMIS-specific fields
        public string Zone { get; set; } = string.Empty;
        public string Route { get; set; } = string.Empty;
        public string ReadingFrequency { get; set; } = string.Empty;
        
        public DateTime LastModified { get; set; }
    }
} 