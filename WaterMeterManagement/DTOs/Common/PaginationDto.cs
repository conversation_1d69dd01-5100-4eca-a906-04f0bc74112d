using System.ComponentModel.DataAnnotations;

namespace WaterMeterManagement.DTOs.Common
{
    /// <summary>
    /// Base pagination DTO for all search operations
    /// </summary>
    public abstract class BasePaginationDto
    {
        private int _page = 1;
        private int _pageSize = 10;

        /// <summary>
        /// Page number (1-based)
        /// </summary>
        [Range(1, int.MaxValue, ErrorMessage = "Page must be greater than 0")]
        public int Page
        {
            get => _page;
            set => _page = Math.Max(1, value);
        }

        /// <summary>
        /// Number of items per page (max 1000 for data management interfaces)
        /// </summary>
        [Range(1, 1000, ErrorMessage = "PageSize must be between 1 and 1000")]
        public int PageSize
        {
            get => _pageSize;
            set => _pageSize = Math.Max(1, Math.Min(1000, value));
        }

        /// <summary>
        /// Sort field name
        /// </summary>
        public string? SortBy { get; set; }

        /// <summary>
        /// Sort direction (asc/desc)
        /// </summary>
        public string? SortDirection { get; set; } = "asc";

        /// <summary>
        /// Calculate skip count for database queries
        /// </summary>
        public int Skip => (Page - 1) * PageSize;

        /// <summary>
        /// Get take count for database queries
        /// </summary>
        public int Take => PageSize;

        /// <summary>
        /// Validate sort direction
        /// </summary>
        public bool IsValidSortDirection =>
            string.IsNullOrEmpty(SortDirection) ||
            SortDirection.Equals("asc", StringComparison.OrdinalIgnoreCase) ||
            SortDirection.Equals("desc", StringComparison.OrdinalIgnoreCase);
    }

    /// <summary>
    /// Generic pagination response wrapper
    /// </summary>
    /// <typeparam name="T">Data type</typeparam>
    public class PaginatedResponse<T>
    {
        /// <summary>
        /// Data items for current page
        /// </summary>
        public List<T> Data { get; set; } = new();

        /// <summary>
        /// Total count of all items
        /// </summary>
        public int TotalCount { get; set; }

        /// <summary>
        /// Current page number
        /// </summary>
        public int Page { get; set; }

        /// <summary>
        /// Items per page
        /// </summary>
        public int PageSize { get; set; }

        /// <summary>
        /// Total number of pages
        /// </summary>
        public int TotalPages => (int)Math.Ceiling((double)TotalCount / PageSize);

        /// <summary>
        /// Whether there is a next page
        /// </summary>
        public bool HasNextPage => Page < TotalPages;

        /// <summary>
        /// Whether there is a previous page
        /// </summary>
        public bool HasPreviousPage => Page > 1;

        /// <summary>
        /// Create a paginated response
        /// </summary>
        public static PaginatedResponse<T> Create(List<T> data, int totalCount, int page, int pageSize)
        {
            return new PaginatedResponse<T>
            {
                Data = data,
                TotalCount = totalCount,
                Page = page,
                PageSize = pageSize
            };
        }
    }

    /// <summary>
    /// Standard pagination configuration for different contexts
    /// </summary>
    public static class PaginationDefaults
    {
        /// <summary>
        /// Default page size for general listing
        /// </summary>
        public const int DefaultPageSize = 10;

        /// <summary>
        /// Page size for data management interfaces (higher limit for admin users)
        /// </summary>
        public const int DataManagementPageSize = 50;

        /// <summary>
        /// Maximum page size for data management interfaces
        /// </summary>
        public const int MaxDataManagementPageSize = 1000;

        /// <summary>
        /// Page size for mobile synchronization
        /// </summary>
        public const int MobileSyncPageSize = 1000;

        /// <summary>
        /// Standard page size options for UI dropdowns
        /// </summary>
        public static readonly string[] StandardPageSizeOptions = { "10", "20", "50", "100" };

        /// <summary>
        /// Extended page size options for data management interfaces
        /// </summary>
        public static readonly string[] DataManagementPageSizeOptions = { "10", "20", "50", "100", "500", "1000" };
    }
}
