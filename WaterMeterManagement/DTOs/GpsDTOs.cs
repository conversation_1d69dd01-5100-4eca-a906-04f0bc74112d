namespace WaterMeterManagement.DTOs
{
    public class GpsCoordinateResultDto
    {
        public bool Success { get; set; }
        public decimal? Latitude { get; set; }
        public decimal? Longitude { get; set; }
        public string? FormattedAddress { get; set; }
        public string? ErrorMessage { get; set; }
        public string? PlaceId { get; set; }
        public List<string> AddressComponents { get; set; } = new();
        public string? LocationType { get; set; }
        public decimal? Accuracy { get; set; }
    }

    public class BatchGpsResultDto
    {
        public int TotalRequests { get; set; }
        public int SuccessCount { get; set; }
        public int FailureCount { get; set; }
        public List<GpsCoordinateResultDto> Results { get; set; } = new();
        public List<string> Errors { get; set; } = new();
        public TimeSpan ProcessingTime { get; set; }
    }

    public class GpsUpdateResultDto
    {
        public bool Success { get; set; }
        public int WaterMeterId { get; set; }
        public string? WaterMeterSerial { get; set; }
        public string? Address { get; set; }
        public decimal? OldLatitude { get; set; }
        public decimal? OldLongitude { get; set; }
        public decimal? NewLatitude { get; set; }
        public decimal? NewLongitude { get; set; }
        public string? ErrorMessage { get; set; }
        public DateTime UpdatedAt { get; set; }
    }

    public class BatchGpsUpdateResultDto
    {
        public int TotalMeters { get; set; }
        public int SuccessCount { get; set; }
        public int FailureCount { get; set; }
        public int SkippedCount { get; set; }
        public List<GpsUpdateResultDto> Results { get; set; } = new();
        public List<string> Errors { get; set; } = new();
        public TimeSpan ProcessingTime { get; set; }
        public DateTime StartedAt { get; set; }
        public DateTime CompletedAt { get; set; }
    }

    public class GpsServiceStatsDto
    {
        public int TotalWaterMeters { get; set; }
        public int MetersWithGps { get; set; }
        public int MetersWithoutGps { get; set; }
        public decimal GpsCompletionPercentage { get; set; }
        public int TodayGpsRequests { get; set; }
        public int TodaySuccessfulRequests { get; set; }
        public int TodayFailedRequests { get; set; }
        public decimal TodaySuccessRate { get; set; }
        public DateTime LastGpsUpdate { get; set; }
    }

    public class GoogleGeocodingResponseDto
    {
        public List<GoogleGeocodingResultDto> Results { get; set; } = new();
        public string Status { get; set; } = string.Empty;
        public string? ErrorMessage { get; set; }
    }

    public class GoogleGeocodingResultDto
    {
        public List<GoogleAddressComponentDto> AddressComponents { get; set; } = new();
        public string FormattedAddress { get; set; } = string.Empty;
        public GoogleGeometryDto Geometry { get; set; } = new();
        public string PlaceId { get; set; } = string.Empty;
        public List<string> Types { get; set; } = new();
    }

    public class GoogleAddressComponentDto
    {
        public string LongName { get; set; } = string.Empty;
        public string ShortName { get; set; } = string.Empty;
        public List<string> Types { get; set; } = new();
    }

    public class GoogleGeometryDto
    {
        public GoogleLocationDto Location { get; set; } = new();
        public string LocationType { get; set; } = string.Empty;
        public GoogleViewportDto Viewport { get; set; } = new();
    }

    public class GoogleLocationDto
    {
        public decimal Lat { get; set; }
        public decimal Lng { get; set; }
    }

    public class GoogleViewportDto
    {
        public GoogleLocationDto Northeast { get; set; } = new();
        public GoogleLocationDto Southwest { get; set; } = new();
    }
}
