namespace WaterMeterManagement.DTOs
{
    /// <summary>
    /// Staff sync response DTO
    /// </summary>
    public class StaffSyncResponseDto
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public int TotalProcessed { get; set; }
        public int SuccessCount { get; set; }
        public int FailureCount { get; set; }
        public List<string> Errors { get; set; } = new List<string>();
        public DateTime SyncTime { get; set; } = DateTime.UtcNow;
    }
    
    /// <summary>
    /// Workbench staff data DTO
    /// </summary>
    public class WorkbenchStaffDto
    {
        public int PersonId { get; set; }
        public string FullName { get; set; } = string.Empty;
        public string EmailAddress { get; set; } = string.Empty;
        public string MobilePhone { get; set; } = string.Empty;
        public string ProfitCentreCode { get; set; } = string.Empty;
        public string EmployeeNo { get; set; } = string.Empty;
    }
    
    /// <summary>
    /// Workbench API response for staff data
    /// </summary>
    public class WorkbenchStaffApiResponse
    {
        public int Records { get; set; }
        public List<WorkbenchStaffRow> Rows { get; set; } = new List<WorkbenchStaffRow>();
    }
    
    /// <summary>
    /// Workbench staff row data structure
    /// </summary>
    public class WorkbenchStaffRow
    {
        public string Col01 { get; set; } = string.Empty; // PersonID
        public string Col02 { get; set; } = string.Empty; // FullName
        public string Col03 { get; set; } = string.Empty; // EmailAddress
        public string Col04 { get; set; } = string.Empty; // MobilePhone
        public string Col05 { get; set; } = string.Empty; // ProfitCentreCode
        public string Col06 { get; set; } = string.Empty; // EmployeeNo
    }
} 