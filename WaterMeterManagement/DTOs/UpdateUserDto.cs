using System.ComponentModel.DataAnnotations;

namespace WaterMeterManagement.DTOs
{
    public class UpdateUserDto
    {
        [StringLength(255)]
        public string? FullName { get; set; }
        
        [StringLength(50)]
        public string? FinCoCode { get; set; }
        
        [StringLength(255)]
        [EmailAddress]
        public string? Email { get; set; }
        
        [StringLength(50)]
        public string? MobilePhone { get; set; }
        
        [StringLength(50)]
        public string? ProfitCentreCode { get; set; }
        
        [StringLength(50)]
        public string? EmployeeNo { get; set; }
    }
} 