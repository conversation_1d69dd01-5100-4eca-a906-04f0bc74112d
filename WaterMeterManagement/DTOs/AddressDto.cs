namespace WaterMeterManagement.DTOs
{
    /// <summary>
    /// Result of address parsing operation
    /// </summary>
    public class AddressParseResultDto
    {
        /// <summary>
        /// Whether the address was successfully parsed
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// Error message if parsing failed
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// Formatted address for display (Location field)
        /// </summary>
        public string FormattedAddress { get; set; } = string.Empty;

        /// <summary>
        /// Structured address components
        /// </summary>
        public AddressComponentsDto? Components { get; set; }

        /// <summary>
        /// GPS coordinates if available
        /// </summary>
        public decimal? Latitude { get; set; }
        public decimal? Longitude { get; set; }

        /// <summary>
        /// Google Place ID for reference
        /// </summary>
        public string? PlaceId { get; set; }
    }

    /// <summary>
    /// Structured address components matching WaterMeter fields
    /// </summary>
    public class AddressComponentsDto
    {
        /// <summary>
        /// Street number (Road Number)
        /// </summary>
        public int? RoadNumber { get; set; }

        /// <summary>
        /// Street name (Road Name)
        /// </summary>
        public string? RoadName { get; set; }

        /// <summary>
        /// Suburb/locality (Township)
        /// </summary>
        public string? Township { get; set; }

        /// <summary>
        /// Administrative area (Sub Area)
        /// </summary>
        public string? SubArea { get; set; }

        /// <summary>
        /// Postal code
        /// </summary>
        public string? PostalCode { get; set; }

        /// <summary>
        /// Country
        /// </summary>
        public string? Country { get; set; }
    }

    /// <summary>
    /// Address suggestion for autocomplete
    /// </summary>
    public class AddressSuggestionDto
    {
        /// <summary>
        /// Display text for the suggestion
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// Google Place ID
        /// </summary>
        public string PlaceId { get; set; } = string.Empty;

        /// <summary>
        /// Main text (usually street address)
        /// </summary>
        public string MainText { get; set; } = string.Empty;

        /// <summary>
        /// Secondary text (usually suburb, city)
        /// </summary>
        public string SecondaryText { get; set; } = string.Empty;
    }

    /// <summary>
    /// Request DTO for address parsing
    /// </summary>
    public class AddressParseRequestDto
    {
        /// <summary>
        /// Raw address input from user
        /// </summary>
        public string Address { get; set; } = string.Empty;

        /// <summary>
        /// Country code to limit search (default: NZ)
        /// </summary>
        public string CountryCode { get; set; } = "NZ";
    }
}
