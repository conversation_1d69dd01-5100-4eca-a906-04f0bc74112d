namespace WaterMeterManagement.DTOs
{
    public class LoginResponseDto
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public string Token { get; set; } = string.Empty;
        public UserDto? User { get; set; }
    }

    public class UserDto
    {
        public int Id { get; set; }
        public string Username { get; set; } = string.Empty;
        public string FullName { get; set; } = string.Empty;
        public int PersonId { get; set; }
        public string FinCoCode { get; set; } = string.Empty;
        public bool IsAuthenticated { get; set; }
        public DateTime? LastLogin { get; set; }
    }
} 