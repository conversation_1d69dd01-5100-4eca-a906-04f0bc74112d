using WaterMeterManagement.DTOs.Common;

namespace WaterMeterManagement.DTOs
{
    public class WaterMeterDto
    {
        public int Id { get; set; }
        public string SerialNumber { get; set; } = string.Empty;
        public string Location { get; set; } = string.Empty;
        public string Address { get; set; } = string.Empty;
        public string MeterType { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
        public DateTime? InstallDate { get; set; }
        public DateTime? LastReadingDate { get; set; }
        public decimal? LastReading { get; set; }
        public decimal? LastRead { get; set; }
        public string? CustomerCode { get; set; }
        public string? CustomerName { get; set; }
        public decimal? Latitude { get; set; }
        public decimal? Longitude { get; set; }
        public string? Brand { get; set; }
        public string? Model { get; set; }
        public int? BatteryLevel { get; set; }
        public string? CommunicationMethod { get; set; }
        public DateTime? LastMaintenanceDate { get; set; }
        public DateTime? NextMaintenanceDate { get; set; }
        public string? Notes { get; set; }
        
        // AMS Integration Fields
        public string? AssetId { get; set; }
        public string? MeterNumber { get; set; }
        public string? AccountNumber { get; set; }
        public string? BookNumber { get; set; }
        public int? Unit { get; set; }
        public int? RoadNumber { get; set; }
        public string? RoadName { get; set; }
        public string? Township { get; set; }
        public string? SubArea { get; set; }
        public decimal? RecentChange { get; set; }
        public string? Subd { get; set; }
        public DateTime? DateOfRead { get; set; }
        public decimal? Read { get; set; }
        public bool CantRead { get; set; }
        public string? Condition { get; set; }
        public string? Comments { get; set; }
        public string? SyncStatus { get; set; }
        public DateTime? LastSyncDate { get; set; }
        public string? Source { get; set; }
        
        public DateTime? CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
    }

    public class WaterMeterListDto
    {
        public int Id { get; set; }
        public string SerialNumber { get; set; } = string.Empty;
        public string Location { get; set; } = string.Empty;
        public string MeterType { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
        public DateTime? InstallDate { get; set; }
        public DateTime? LastReadingDate { get; set; }
        public decimal? LastRead { get; set; }
        public string? CustomerName { get; set; }
        public int? BatteryLevel { get; set; }

        // GPS Coordinates
        public decimal? Latitude { get; set; }
        public decimal? Longitude { get; set; }

        // AMS Integration Fields
        public string? AssetId { get; set; }
        public string? MeterNumber { get; set; }
        public string? AccountNumber { get; set; }
        public string? RoadName { get; set; }
        public string? Township { get; set; }
        public string? AssignedRoute { get; set; }
        public DateTime? DateOfRead { get; set; }
        public bool CantRead { get; set; }
        public string? Condition { get; set; }
        public string? SyncStatus { get; set; }
        public DateTime? LastSyncDate { get; set; }
    }

    public class CreateWaterMeterDto
    {
        public string SerialNumber { get; set; } = string.Empty;
        public string Location { get; set; } = string.Empty;
        public string Address { get; set; } = string.Empty;
        public string MeterType { get; set; } = string.Empty;
        public string Status { get; set; } = "Active";
        public DateTime InstallDate { get; set; }
        public string? CustomerCode { get; set; }
        public string? CustomerName { get; set; }
        public decimal? Latitude { get; set; }
        public decimal? Longitude { get; set; }
        public string? Brand { get; set; }
        public string? Model { get; set; }
        public string? CommunicationMethod { get; set; }
        public string? Notes { get; set; }

        // AMS Integration fields
        public string? AccountNumber { get; set; }
        public string? AssetId { get; set; }

        // Structured address fields (from address parsing)
        public int? RoadNumber { get; set; }
        public string? RoadName { get; set; }
        public string? Township { get; set; }
        public string? SubArea { get; set; }
    }

    public class UpdateWaterMeterDto
    {
        public string? Location { get; set; }
        public string? Address { get; set; }
        public string? MeterType { get; set; }
        public string? Status { get; set; }
        public DateTime? InstallDate { get; set; }
        public string? AccountNumber { get; set; }
        public string? CustomerCode { get; set; }
        public string? CustomerName { get; set; }
        public decimal? Latitude { get; set; }
        public decimal? Longitude { get; set; }
        public string? Brand { get; set; }
        public string? Model { get; set; }
        public string? CommunicationMethod { get; set; }
        public DateTime? LastMaintenanceDate { get; set; }
        public DateTime? NextMaintenanceDate { get; set; }
        public string? Notes { get; set; }
    }

    public class WaterMeterImportDto
    {
        public string SerialNumber { get; set; } = string.Empty;
        public string Location { get; set; } = string.Empty;
        public string Address { get; set; } = string.Empty;
        public string MeterType { get; set; } = string.Empty;
        public string Status { get; set; } = "Active";
        public DateTime InstallDate { get; set; }
        public string? CustomerCode { get; set; }
        public string? CustomerName { get; set; }
        public decimal? Latitude { get; set; }
        public decimal? Longitude { get; set; }
        public string? Brand { get; set; }
        public string? Model { get; set; }
        public string? CommunicationMethod { get; set; }
        public string? Notes { get; set; }
        public int RowNumber { get; set; }
        public List<string> ValidationErrors { get; set; } = new List<string>();
    }

    public class WaterMeterSearchDto : BasePaginationDto
    {
        public string? WorkPackageAssignment { get; set; } // "unassigned", "assigned", "all"
        public string? SerialNumber { get; set; }
        public string? Location { get; set; }
        public string? MeterType { get; set; }
        public string? Status { get; set; }
        public string? CustomerName { get; set; }
        public DateTime? InstallDateFrom { get; set; }
        public DateTime? InstallDateTo { get; set; }

        public WaterMeterSearchDto()
        {
            // Set default values for water meter management
            PageSize = PaginationDefaults.DataManagementPageSize;
            SortBy = "SerialNumber";
            SortDirection = "asc";
        }
    }

    public class WaterMeterImportResultDto
    {
        public int TotalRows { get; set; }
        public int SuccessfulRows { get; set; }
        public int FailedRows { get; set; }
        public List<WaterMeterImportDto> FailedRecords { get; set; } = new List<WaterMeterImportDto>();
        public List<string> GeneralErrors { get; set; } = new List<string>();
        public bool Success => FailedRows == 0 && GeneralErrors.Count == 0;
    }
} 