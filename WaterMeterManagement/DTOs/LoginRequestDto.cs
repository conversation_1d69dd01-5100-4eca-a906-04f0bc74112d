using System.ComponentModel.DataAnnotations;

namespace WaterMeterManagement.DTOs
{
    public class LoginRequestDto
    {
        [Required(ErrorMessage = "用户名不能为空")]
        [StringLength(255, ErrorMessage = "用户名长度不能超过255个字符")]
        public string Username { get; set; } = string.Empty;

        [Required(ErrorMessage = "密码不能为空")]
        [StringLength(255, ErrorMessage = "密码长度不能超过255个字符")]
        public string Password { get; set; } = string.Empty;

        public bool RememberMe { get; set; } = false;
    }
} 