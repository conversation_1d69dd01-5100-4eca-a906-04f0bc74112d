namespace WaterMeterManagement.DTOs
{
    public class RouteDto
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string? Description { get; set; }
        public string Status { get; set; } = string.Empty;
        public string? Zone { get; set; }
        public string? Area { get; set; }
        public int? EstimatedDuration { get; set; }
        public decimal? EstimatedDistance { get; set; }
        public int TotalMeters { get; set; }
        public decimal? StartLatitude { get; set; }
        public decimal? StartLongitude { get; set; }
        public decimal? EndLatitude { get; set; }
        public decimal? EndLongitude { get; set; }
        public string? StartAddress { get; set; }
        public string? EndAddress { get; set; }
        public int? AssignedUserId { get; set; }
        public string? BackupAssignee { get; set; }
        public string? OptimizationMethod { get; set; }
        public DateTime? LastOptimized { get; set; }
        public string? OptimizedBy { get; set; }
        public string? RouteGeometry { get; set; }
        public string? Notes { get; set; }
        public bool IsTemplate { get; set; }
        public string? TemplateCategory { get; set; }
        public decimal? AverageCompletionTime { get; set; }
        public decimal? DifficultyRating { get; set; }
        
        public List<RouteWaypointDto> Waypoints { get; set; } = new();
        
        public DateTime CreatedAt { get; set; }
        public string CreatedBy { get; set; } = string.Empty;
        public DateTime? UpdatedAt { get; set; }
        public string? UpdatedBy { get; set; }
    }

    public class RouteListDto
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string? Description { get; set; }
        public string Status { get; set; } = string.Empty;
        public string? Zone { get; set; }
        public string? Area { get; set; }
        public int? EstimatedDuration { get; set; }
        public decimal? EstimatedDistance { get; set; }
        public int TotalMeters { get; set; }
        public int? AssignedUserId { get; set; }
        public string? OptimizationMethod { get; set; }
        public DateTime? LastOptimized { get; set; }
        public bool IsTemplate { get; set; }
        public DateTime CreatedAt { get; set; }
        public string CreatedBy { get; set; } = string.Empty;
    }

    public class CreateRouteDto
    {
        public string Name { get; set; } = string.Empty;
        public string? Description { get; set; }
        public string? Zone { get; set; }
        public string? Area { get; set; }
        public int? EstimatedDuration { get; set; }
        public decimal? EstimatedDistance { get; set; }
        public decimal? StartLatitude { get; set; }
        public decimal? StartLongitude { get; set; }
        public decimal? EndLatitude { get; set; }
        public decimal? EndLongitude { get; set; }
        public string? StartAddress { get; set; }
        public string? EndAddress { get; set; }
        public int? AssignedUserId { get; set; }
        public string? BackupAssignee { get; set; }
        public string? Notes { get; set; }
        public bool IsTemplate { get; set; } = false;
        public string? TemplateCategory { get; set; }
        public decimal? DifficultyRating { get; set; }
        
        public List<CreateRouteWaypointDto>? Waypoints { get; set; }
    }

    public class UpdateRouteDto
    {
        public string Name { get; set; } = string.Empty;
        public string? Description { get; set; }
        public string Status { get; set; } = string.Empty;
        public string? Zone { get; set; }
        public string? Area { get; set; }
        public int? EstimatedDuration { get; set; }
        public decimal? EstimatedDistance { get; set; }
        public decimal? StartLatitude { get; set; }
        public decimal? StartLongitude { get; set; }
        public decimal? EndLatitude { get; set; }
        public decimal? EndLongitude { get; set; }
        public string? StartAddress { get; set; }
        public string? EndAddress { get; set; }
        public int? AssignedUserId { get; set; }
        public string? BackupAssignee { get; set; }
        public string? Notes { get; set; }
        public bool IsTemplate { get; set; } = false;
        public string? TemplateCategory { get; set; }
        public decimal? DifficultyRating { get; set; }
    }

    public class RouteSearchDto
    {
        public int Page { get; set; } = 1;
        public int PageSize { get; set; } = 10;
        public string? SortBy { get; set; } = "CreatedAt";
        public string? SortDirection { get; set; } = "desc";
        public string? Name { get; set; }
        public string? Status { get; set; }
        public string? Zone { get; set; }
        public string? Area { get; set; }
        public int? AssignedUserId { get; set; }
        public bool? IsTemplate { get; set; }
        public string? TemplateCategory { get; set; }
        public int? MinDuration { get; set; }
        public int? MaxDuration { get; set; }
        public decimal? MinDistance { get; set; }
        public decimal? MaxDistance { get; set; }
    }

    public class RouteOptimizationDto
    {
        public int RouteId { get; set; }
        public string OptimizationMethod { get; set; } = "DistanceOptimal";
        public bool PreserveStartEnd { get; set; } = true;
        public List<int>? FixedWaypointIds { get; set; }
        public bool ConsiderTrafficConditions { get; set; } = false;
        public bool ConsiderTimeWindows { get; set; } = false;
    }

    public class RouteOptimizationResultDto
    {
        public int RouteId { get; set; }
        public string OptimizationMethod { get; set; } = string.Empty;
        public decimal OriginalDistance { get; set; }
        public decimal OptimizedDistance { get; set; }
        public decimal DistanceSaved { get; set; }
        public int OriginalDuration { get; set; }
        public int OptimizedDuration { get; set; }
        public int TimeSaved { get; set; }
        public List<RouteWaypointDto> OptimizedWaypoints { get; set; } = new();
        public DateTime OptimizedAt { get; set; }
        public string OptimizedBy { get; set; } = string.Empty;
    }

    public class RouteWaypointDto
    {
        public int Id { get; set; }
        public int RouteId { get; set; }
        public int WaterMeterId { get; set; }
        public int SequenceOrder { get; set; }
        public decimal? Latitude { get; set; }
        public decimal? Longitude { get; set; }
        public string? Address { get; set; }
        public int? EstimatedDuration { get; set; }
        public decimal? DistanceFromPrevious { get; set; }
        public int? TravelTimeFromPrevious { get; set; }
        public string? Notes { get; set; }
        public decimal? AccessDifficulty { get; set; }
        public bool RequiresSpecialEquipment { get; set; }
        public string? SpecialInstructions { get; set; }
        
        // Related data
        public string? WaterMeterSerial { get; set; }
        public string? CustomerName { get; set; }
    }

    public class CreateRouteWaypointDto
    {
        public int WaterMeterId { get; set; }
        public int SequenceOrder { get; set; }
        public int? EstimatedDuration { get; set; }
        public string? Notes { get; set; }
        public decimal? AccessDifficulty { get; set; }
        public bool RequiresSpecialEquipment { get; set; } = false;
        public string? SpecialInstructions { get; set; }
    }

    public class RouteStatisticsDto
    {
        public int TotalRoutes { get; set; }
        public int ActiveRoutes { get; set; }
        public int TemplateRoutes { get; set; }
        public int TotalWaypoints { get; set; }
        public decimal? AverageDistance { get; set; }
        public int? AverageDuration { get; set; }
        
        public Dictionary<string, int> StatusBreakdown { get; set; } = new();
        public Dictionary<string, int> ZoneBreakdown { get; set; } = new();
        public Dictionary<string, int> DifficultyBreakdown { get; set; } = new();
    }

    public class UpdateWaypointCoordinatesDto
    {
        public decimal Latitude { get; set; }
        public decimal Longitude { get; set; }
        public string? Address { get; set; }
    }

    public class BatchUpdateCoordinatesDto
    {
        public List<WaypointCoordinateUpdate> Updates { get; set; } = new();
    }

    public class WaypointCoordinateUpdate
    {
        public int WaypointId { get; set; }
        public decimal Latitude { get; set; }
        public decimal Longitude { get; set; }
        public string? Address { get; set; }
    }

    public class BatchUpdateResult
    {
        public int SuccessCount { get; set; }
        public int FailedCount { get; set; }
        public List<string> Failures { get; set; } = new();
    }

    public class DuplicateRouteDto
    {
        public string Name { get; set; } = string.Empty;
        public string? Description { get; set; }
        public bool IncludeWaypoints { get; set; } = true;
        public int? AssignedUserId { get; set; }
    }
} 