namespace WaterMeterManagement.DTOs
{
    public class ApiResponse<T>
    {
        public int Code { get; set; }
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public T? Data { get; set; }

        public static ApiResponse<T> SuccessResult(T? data = default, string message = "Success")
        {
            return new ApiResponse<T>
            {
                Code = 200,
                Success = true,
                Message = message,
                Data = data
            };
        }

        public static ApiResponse<T> ErrorResult(string message, int code = 500)
        {
            return new ApiResponse<T>
            {
                Code = code,
                Success = false,
                Message = message,
                Data = default
            };
        }
    }

    public class ApiResponse : ApiResponse<object>
    {
        public static new ApiResponse Success(string message = "Success")
        {
            var response = new ApiResponse
            {
                Code = 200,
                Message = message
            };
            return response;
        }

        public static ApiResponse Error(string message, int code = 500)
        {
            var response = new ApiResponse
            {
                Code = code,
                Message = message
            };
            return response;
        }
    }
}
