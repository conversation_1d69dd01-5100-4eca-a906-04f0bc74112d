using System.ComponentModel.DataAnnotations;

namespace WaterMeterManagement.DTOs
{
    public class WorkPackageDto
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string? Description { get; set; }
        public string PackageType { get; set; } = "Scheduled";
        public string Status { get; set; } = "Draft";
        public DateTime PlannedStartDate { get; set; }
        public DateTime PlannedEndDate { get; set; }
        public DateTime? ActualStartDate { get; set; }
        public DateTime? ActualEndDate { get; set; }
        public string Frequency { get; set; } = "Monthly";
        public string ServiceArea { get; set; } = string.Empty;
        public string? SubArea { get; set; }
        public string Priority { get; set; } = "Medium";
        public int TotalMeters { get; set; }
        public int CompletedMeters { get; set; }
        public decimal ProgressPercentage { get; set; }
        public string? AssignedTeam { get; set; }
        public decimal EstimatedHours { get; set; }
        public decimal? ActualHours { get; set; }
        public decimal? EstimatedCost { get; set; }
        public decimal? ActualCost { get; set; }
        public bool IsTemplate { get; set; }
        public string? TemplateCategory { get; set; }
        public bool IsRecurring { get; set; }
        public string? RecurrencePattern { get; set; }
        public int? RecurrenceInterval { get; set; }
        public DateTime? LastExecuted { get; set; }
        public DateTime? NextExecution { get; set; }
        public string? Notes { get; set; }
        public string? Instructions { get; set; }
        public string? AmsImportBatch { get; set; }
        public DateTime? AmsImportDate { get; set; }
        public string Source { get; set; } = "Manual";
        public string CreatedBy { get; set; } = string.Empty;
        public string? UpdatedBy { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
        
        // Computed Properties
        public bool IsStarted { get; set; }
        public bool IsCompleted { get; set; }
        public bool IsOverdue { get; set; }
        public int? RemainingDays { get; set; }
        public decimal CompletionRate { get; set; }
        
        // Navigation Properties
        public List<WorkPackageItemDto> Items { get; set; } = new();
        public List<WorkPackageAssignmentDto> Assignments { get; set; } = new();
    }

    public class WorkPackageListDto
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string? Description { get; set; }
        public string PackageType { get; set; } = "Scheduled";
        public string Status { get; set; } = "Draft";
        public DateTime PlannedStartDate { get; set; }
        public DateTime PlannedEndDate { get; set; }
        public string Frequency { get; set; } = "Monthly";
        public string ServiceArea { get; set; } = string.Empty;
        public string? SubArea { get; set; }
        public string Priority { get; set; } = "Medium";
        public int TotalMeters { get; set; }
        public int CompletedMeters { get; set; }
        public decimal ProgressPercentage { get; set; }
        public string? AssignedTeam { get; set; }
        public decimal EstimatedHours { get; set; }
        public bool IsTemplate { get; set; }
        public bool IsRecurring { get; set; }
        public string CreatedBy { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }
        
        // Computed Properties
        public bool IsOverdue { get; set; }
        public int? RemainingDays { get; set; }
        public decimal CompletionRate { get; set; }
    }

    public class CreateWorkPackageDto
    {
        [Required]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;

        [StringLength(500)]
        public string? Description { get; set; }

        [Required]
        [StringLength(20)]
        public string PackageType { get; set; } = "Scheduled";

        [StringLength(20)]
        public string Status { get; set; } = "Draft";

        [Required]
        public DateTime PlannedStartDate { get; set; }

        [Required]
        public DateTime PlannedEndDate { get; set; }

        [Required]
        [StringLength(20)]
        public string Frequency { get; set; } = "Monthly";

        [Required]
        [StringLength(100)]
        public string ServiceArea { get; set; } = string.Empty;

        [StringLength(100)]
        public string? SubArea { get; set; }

        [Required]
        [StringLength(20)]
        public string Priority { get; set; } = "Medium";

        [StringLength(200)]
        public string? AssignedTeam { get; set; }

        public decimal EstimatedHours { get; set; } = 0;

        public decimal? EstimatedCost { get; set; }

        public bool IsTemplate { get; set; } = false;

        [StringLength(50)]
        public string? TemplateCategory { get; set; }

        public bool IsRecurring { get; set; } = false;

        [StringLength(50)]
        public string? RecurrencePattern { get; set; }

        public int? RecurrenceInterval { get; set; }

        [StringLength(1000)]
        public string? Notes { get; set; }

        [StringLength(1000)]
        public string? Instructions { get; set; }

        // Meter selection
        public List<int> MeterIds { get; set; } = new();
        
        // User assignments
        public List<CreateWorkPackageAssignmentDto> Assignments { get; set; } = new();
        
        // Audit fields are automatically managed by BaseEntity + DbContext
        // No CreatedBy field needed - will be set automatically
    }

    public class UpdateWorkPackageDto
    {
        [Required]
        public int Id { get; set; }

        [Required]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;

        [StringLength(500)]
        public string? Description { get; set; }

        [Required]
        [StringLength(20)]
        public string PackageType { get; set; } = "Scheduled";

        [Required]
        [StringLength(20)]
        public string Status { get; set; } = "Draft";

        [Required]
        public DateTime PlannedStartDate { get; set; }

        [Required]
        public DateTime PlannedEndDate { get; set; }

        public DateTime? ActualStartDate { get; set; }

        public DateTime? ActualEndDate { get; set; }

        [Required]
        [StringLength(20)]
        public string Frequency { get; set; } = "Monthly";

        [Required]
        [StringLength(100)]
        public string ServiceArea { get; set; } = string.Empty;

        [StringLength(100)]
        public string? SubArea { get; set; }

        [Required]
        [StringLength(20)]
        public string Priority { get; set; } = "Medium";

        [StringLength(200)]
        public string? AssignedTeam { get; set; }

        public decimal EstimatedHours { get; set; } = 0;

        public decimal? ActualHours { get; set; }

        public decimal? EstimatedCost { get; set; }

        public decimal? ActualCost { get; set; }

        public bool IsTemplate { get; set; } = false;

        [StringLength(50)]
        public string? TemplateCategory { get; set; }

        public bool IsRecurring { get; set; } = false;

        [StringLength(50)]
        public string? RecurrencePattern { get; set; }

        public int? RecurrenceInterval { get; set; }

        public DateTime? NextExecution { get; set; }

        [StringLength(1000)]
        public string? Notes { get; set; }

        [StringLength(1000)]
        public string? Instructions { get; set; }

        // Audit fields are automatically managed by BaseEntity + DbContext
        // No UpdatedBy field needed - will be set automatically
    }

    public class WorkPackageItemDto
    {
        public int Id { get; set; }
        public int WorkPackageId { get; set; }
        public int MeterId { get; set; }
        public int SequenceOrder { get; set; }
        public string Status { get; set; } = "Pending";
        public DateTime? ScheduledDate { get; set; }
        public DateTime? ActualDate { get; set; }
        public string? AssignedTo { get; set; }
        public string? Priority { get; set; }
        public int? EstimatedMinutes { get; set; }
        public int? ActualMinutes { get; set; }
        public string? SpecialInstructions { get; set; }
        public string? Notes { get; set; }
        public string? LastReading { get; set; }
        public bool RequiresSpecialHandling { get; set; }
        public string? SpecialHandlingReason { get; set; }
        public decimal? DifficultyRating { get; set; }
        public decimal? Latitude { get; set; }
        public decimal? Longitude { get; set; }
        public string? ServiceAddress { get; set; }
        public string? PropertyDetails { get; set; }
        public string? AccessNotes { get; set; }
        public bool HasPhoto { get; set; }
        public string? PhotoUrl { get; set; }
        public string? QrCode { get; set; }
        public string? MeterSerialNumber { get; set; }
        public string? MeterType { get; set; }
        public string? MeterSize { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
        
        // Computed Properties
        public bool IsCompleted { get; set; }
        public bool CanRetry { get; set; }
        public bool IsOverdue { get; set; }
        public decimal? EfficiencyScore { get; set; }
        
        // Meter details
        public string? MeterLocation { get; set; }
        public string? MeterDescription { get; set; }
    }

    public class WorkPackageAssignmentDto
    {
        public int Id { get; set; }
        public int WorkPackageId { get; set; }
        public int UserId { get; set; }
        public string AssignmentType { get; set; } = "Primary";
        public string Status { get; set; } = "Assigned";
        public DateTime AssignedDate { get; set; }
        public string AssignedBy { get; set; } = string.Empty;
        public DateTime? AcceptedDate { get; set; }
        public DateTime? RejectedDate { get; set; }
        public DateTime? StartedDate { get; set; }
        public DateTime? CompletedDate { get; set; }
        public string? Reason { get; set; }
        public string? RejectionReason { get; set; }
        public DateTime? ExpectedStartDate { get; set; }
        public DateTime? ExpectedCompletionDate { get; set; }
        public int? AssignedMeterCount { get; set; }
        public int CompletedMeterCount { get; set; }
        public int WorkloadWeight { get; set; } = 100;
        public string? Priority { get; set; }
        public bool RequiresSupervision { get; set; }
        public int? SupervisorId { get; set; }
        public string? SkillRequirements { get; set; }
        public string? EquipmentRequirements { get; set; }
        public string? SpecialInstructions { get; set; }
        public bool EmailNotificationSent { get; set; }
        public DateTime? EmailSentDate { get; set; }
        public bool PushNotificationSent { get; set; }
        public DateTime? PushNotificationSentDate { get; set; }
        public DateTime? LastActivityDate { get; set; }
        public int? Rating { get; set; }
        public string? Feedback { get; set; }
        public string? RatedBy { get; set; }
        public DateTime? RatedDate { get; set; }
        
        // Computed Properties
        public bool IsAccepted { get; set; }
        public bool IsCompleted { get; set; }
        public bool IsRejected { get; set; }
        public bool IsInProgress { get; set; }
        public decimal CompletionRate { get; set; }
        public bool IsOverdue { get; set; }
        
        // User details
        public string? UserName { get; set; }
        public string? UserEmail { get; set; }
        public string? SupervisorName { get; set; }
    }

    public class CreateWorkPackageAssignmentDto
    {
        [Required]
        public int UserId { get; set; }

        [StringLength(20)]
        public string AssignmentType { get; set; } = "Primary";

        [StringLength(500)]
        public string? Reason { get; set; }

        public DateTime? ExpectedStartDate { get; set; }

        public DateTime? ExpectedCompletionDate { get; set; }

        public int? AssignedMeterCount { get; set; }

        [Range(0, 100)]
        public int WorkloadWeight { get; set; } = 100;

        [StringLength(20)]
        public string? Priority { get; set; }

        public bool RequiresSupervision { get; set; }

        public int? SupervisorId { get; set; }

        [StringLength(200)]
        public string? SkillRequirements { get; set; }

        [StringLength(200)]
        public string? EquipmentRequirements { get; set; }

        [StringLength(1000)]
        public string? SpecialInstructions { get; set; }

        [Required]
        [StringLength(100)]
        public string AssignedBy { get; set; } = string.Empty;
    }

    public class WorkPackageSearchDto
    {
        public string? Name { get; set; }
        public string? PackageType { get; set; }
        public string? Status { get; set; }
        public string? Priority { get; set; }
        public string? ServiceArea { get; set; }
        public string? SubArea { get; set; }
        public string? CreatedBy { get; set; }
        public int? AssignedUserId { get; set; }
        public DateTime? PlannedStartDateFrom { get; set; }
        public DateTime? PlannedStartDateTo { get; set; }
        public DateTime? PlannedEndDateFrom { get; set; }
        public DateTime? PlannedEndDateTo { get; set; }
        public bool? IsTemplate { get; set; }
        public bool? IsRecurring { get; set; }
        public bool? IsOverdue { get; set; }
        public string? Source { get; set; }
        public decimal? MinProgress { get; set; }
        public decimal? MaxProgress { get; set; }
    }

    public class WorkPackageStatisticsDto
    {
        public int TotalWorkPackages { get; set; }
        public int DraftWorkPackages { get; set; }
        public int ActiveWorkPackages { get; set; }
        public int InProgressWorkPackages { get; set; }
        public int CompletedWorkPackages { get; set; }
        public int CancelledWorkPackages { get; set; }
        public int OverdueWorkPackages { get; set; }
        public int TemplateWorkPackages { get; set; }
        public int RecurringWorkPackages { get; set; }
        
        public int TotalMeters { get; set; }
        public int CompletedMeters { get; set; }
        public int PendingMeters { get; set; }
        public int InProgressMeters { get; set; }
        
        public decimal OverallProgress { get; set; }
        public decimal AverageCompletionTime { get; set; }
        public decimal TotalEstimatedHours { get; set; }
        public decimal TotalActualHours { get; set; }
        public decimal EfficiencyRatio { get; set; }
    }

    public class WorkPackageImportResultDto
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public int TotalRows { get; set; }
        public int SuccessCount { get; set; }
        public int FailureCount { get; set; }
        public List<string> Errors { get; set; } = new();
        public List<WorkPackageListDto> ImportedWorkPackages { get; set; } = new();
    }

    public class TaskGenerationResultDto
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public int GeneratedTaskCount { get; set; }
        public List<int> TaskIds { get; set; } = new();
        public List<string> Warnings { get; set; } = new();
    }

    public class WorkPackageActivationResultDto
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public bool TasksGenerated { get; set; }
        public int GeneratedTaskCount { get; set; }
        public List<string> ValidationResults { get; set; } = new();
        public List<string> Warnings { get; set; } = new();
        public DateTime ActivatedAt { get; set; }
    }

    public class UserWorkloadDto
    {
        public int UserId { get; set; }
        public string UserName { get; set; } = string.Empty;
        public string? Email { get; set; }
        public int ActiveWorkPackages { get; set; }
        public int TotalAssignedMeters { get; set; }
        public int CompletedMeters { get; set; }
        public decimal CompletionRate { get; set; }
        public decimal TotalEstimatedHours { get; set; }
        public decimal TotalActualHours { get; set; }
        public decimal EfficiencyRatio { get; set; }
        public int OverdueWorkPackages { get; set; }
        public DateTime? LastActivity { get; set; }
        public string? CurrentStatus { get; set; }
        public List<WorkPackageAssignmentDto> ActiveAssignments { get; set; } = new();
    }

    public class WorkPackagePerformanceDto
    {
        public int WorkPackageId { get; set; }
        public string WorkPackageName { get; set; } = string.Empty;
        public int UserId { get; set; }
        public string UserName { get; set; } = string.Empty;
        public DateTime StartDate { get; set; }
        public DateTime? CompletionDate { get; set; }
        public decimal EstimatedHours { get; set; }
        public decimal ActualHours { get; set; }
        public decimal EfficiencyRatio { get; set; }
        public int TotalMeters { get; set; }
        public int CompletedMeters { get; set; }
        public decimal CompletionRate { get; set; }
        public decimal AverageTimePerMeter { get; set; }
        public int QualityScore { get; set; }
        public int RetryCount { get; set; }
        public decimal SuccessRate { get; set; }
        public string? Notes { get; set; }
    }

    public class WorkPackageHistoryDto
    {
        public int Id { get; set; }
        public int WorkPackageId { get; set; }
        public string Action { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string ActionBy { get; set; } = string.Empty;
        public DateTime ActionDate { get; set; }
        public string? AdditionalData { get; set; }
        public string? UserName { get; set; }
        public string ActionDisplayName => Action switch
        {
            "Created" => "Created",
            "Updated" => "Updated",
            "StatusChanged" => "Status Changed",
            "Started" => "Started",
            "Completed" => "Completed",
            "Cancelled" => "Cancelled",
            "Assigned" => "Assigned",
            "MeterAdded" => "Meter Added",
            "MeterRemoved" => "Meter Removed",
            "TaskGenerated" => "Tasks Generated",
            _ => Action
        };
    }

    public class ImportResultDto
    {
        public int TotalRows { get; set; }
        public int SuccessCount { get; set; }
        public int FailureCount { get; set; }
        public List<string> Errors { get; set; } = new();
    }

    public class BatchOperationResultDto
    {
        public int SuccessCount { get; set; }
        public int FailureCount { get; set; }
        public List<string> Errors { get; set; } = new();
        public List<int> ProcessedIds { get; set; } = new();
    }
}
