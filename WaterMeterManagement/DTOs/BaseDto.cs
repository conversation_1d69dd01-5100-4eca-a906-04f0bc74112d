using System.Text.Json.Serialization;

namespace WaterMeterManagement.DTOs
{
    public class BaseDto
    {
        private readonly Dictionary<string, object> _translations = new Dictionary<string, object>();

        [JsonExtensionData]
        public Dictionary<string, object> AdditionalData
        {
            get => _translations;
            set
            {
                _translations.Clear();
                if (value != null)
                {
                    foreach (var kvp in value)
                    {
                        _translations[kvp.Key] = kvp.Value;
                    }
                }
            }
        }

        public T GetTranslation<T>(string key)
        {
            if (_translations.TryGetValue(key, out var value) && value is T typedValue)
            {
                return typedValue;
            }
            return default(T);
        }

        public void SetTranslation(string key, object value)
        {
            _translations[key] = value;
        }

        public bool HasTranslation(string key)
        {
            return _translations.ContainsKey(key);
        }

        public bool RemoveTranslation(string key)
        {
            return _translations.Remove(key);
        }

        public IEnumerable<string> GetTranslationKeys()
        {
            return _translations.Keys;
        }

        public void ClearTranslations()
        {
            _translations.Clear();
        }
    }
}
