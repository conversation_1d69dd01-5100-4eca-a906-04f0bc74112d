using WaterMeterManagement.Enums;

namespace WaterMeterManagement.Constants
{
    public static class ErrorMessages
    {
        public static readonly Dictionary<ErrorCode, string> Messages = new()
        {
            // General errors
            [ErrorCode.UnknownError] = "An unexpected error occurred. Please try again later.",
            [ErrorCode.ValidationError] = "The provided data is invalid. Please check your input.",
            [ErrorCode.DatabaseError] = "A database error occurred. Please contact support if the problem persists.",
            [ErrorCode.UnauthorizedAccess] = "You are not authorized to perform this action.",
            
            // User related errors
            [ErrorCode.UserNotFound] = "The specified user was not found.",
            [ErrorCode.UserAlreadyExists] = "A user with this information already exists.",
            [ErrorCode.InvalidUserCredentials] = "Invalid username or password.",
            [ErrorCode.UserInactive] = "This user account is inactive.",
            
            // Role related errors
            [ErrorCode.RoleNotFound] = "The specified role was not found.",
            [ErrorCode.RoleAlreadyExists] = "A role with this name already exists.",
            [ErrorCode.RoleInUse] = "This role cannot be deleted because it is currently assigned to users.",
            [ErrorCode.InvalidRoleAssignment] = "The role assignment is invalid.",
            
            // Permission related errors
            [ErrorCode.PermissionNotFound] = "The specified permission was not found.",
            [ErrorCode.PermissionAlreadyExists] = "A permission with this code already exists.",
            [ErrorCode.InsufficientPermissions] = "You do not have sufficient permissions to perform this action.",
            
            // Menu related errors
            [ErrorCode.MenuNotFound] = "The specified menu item was not found.",
            [ErrorCode.MenuCodeAlreadyExists] = "A menu with this code already exists.",
            [ErrorCode.InvalidMenuStructure] = "The menu structure is invalid.",
            
            // Water meter related errors
            [ErrorCode.WaterMeterNotFound] = "The specified water meter was not found.",
            [ErrorCode.WaterMeterAlreadyExists] = "A water meter with this number already exists.",
            [ErrorCode.InvalidMeterReading] = "The meter reading is invalid or out of range.",
            [ErrorCode.MeterOffline] = "The water meter is currently offline.",
            
            // Task related errors
            [ErrorCode.TaskNotFound] = "The specified task was not found.",
            [ErrorCode.TaskAlreadyCompleted] = "This task has already been completed.",
            [ErrorCode.TaskAssignmentFailed] = "Failed to assign the task.",
            [ErrorCode.InvalidTaskStatus] = "The task status is invalid.",
            
            // Route related errors
            [ErrorCode.RouteNotFound] = "The specified route was not found.",
            [ErrorCode.RouteOptimizationFailed] = "Route optimization failed. Please try again.",
            [ErrorCode.InvalidRouteData] = "The route data is invalid."
        };

        public static string GetMessage(ErrorCode errorCode)
        {
            return Messages.TryGetValue(errorCode, out var message) 
                ? message 
                : Messages[ErrorCode.UnknownError];
        }
    }
}
