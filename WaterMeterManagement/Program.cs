using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.EntityFrameworkCore;
using Microsoft.IdentityModel.Tokens;
using Microsoft.OpenApi.Models;
using Serilog;
using System.Text;
using WaterMeterManagement.Data;
using WaterMeterManagement.Repositories;
using WaterMeterManagement.Repositories.Interfaces;
using WaterMeterManagement.Services;
using WaterMeterManagement.Services.Interfaces;
using WaterMeterManagement.Middlewares;
using OfficeOpenXml;

// Configure EPPlus license for non-commercial use (EPPlus 8+)
ExcelPackage.License.SetNonCommercialOrganization("Water Meter Management System");

var builder = WebApplication.CreateBuilder(args);

// Configure Serilog
builder.Host.UseSerilog((context, configuration) =>
{
    configuration.ReadFrom.Configuration(context.Configuration);
});

// Add services to the container
builder.Services.AddControllers()
    .AddJsonOptions(options =>
    {
        // Handle circular references in JSON serialization
        options.JsonSerializerOptions.ReferenceHandler = System.Text.Json.Serialization.ReferenceHandler.IgnoreCycles;
        options.JsonSerializerOptions.WriteIndented = true;
        
        // Use camelCase property names for frontend compatibility
        options.JsonSerializerOptions.PropertyNamingPolicy = System.Text.Json.JsonNamingPolicy.CamelCase;
    });
builder.Services.AddEndpointsApiExplorer();

// Configure Swagger
builder.Services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("v1", new OpenApiInfo 
    { 
        Title = "Water Meter Management API", 
        Version = "v1",
        Description = "Water Meter Reading Management System Backend API - Protected by Authentication",
        Contact = new OpenApiContact
        {
            Name = "Water Meter Management Team",
            Email = "<EMAIL>"
        }
    });
    
    // Add JWT authentication configuration to Swagger
    c.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
    {
        Description = @"JWT Authorization header using the Bearer scheme. 
                      Enter 'Bearer' [space] and then your token in the text input below.
                      Example: 'Bearer 12345abcdef'
                      
                      To get a token:
                      1. Use POST /api/auth/login with your credentials
                      2. Copy the 'token' value from the response
                      3. Click 'Authorize' button above and enter 'Bearer {your-token}'",
        Name = "Authorization",
        In = ParameterLocation.Header,
        Type = SecuritySchemeType.ApiKey,
        Scheme = "Bearer",
        BearerFormat = "JWT"
    });
    
    c.AddSecurityRequirement(new OpenApiSecurityRequirement()
    {
        {
            new OpenApiSecurityScheme
            {
                Reference = new OpenApiReference
                {
                    Type = ReferenceType.SecurityScheme,
                    Id = "Bearer"
                },
                Scheme = "oauth2",
                Name = "Bearer",
                In = ParameterLocation.Header,
            },
            new List<string>()
        }
    });

    // Enable XML comments if available
    var xmlFile = $"{System.Reflection.Assembly.GetExecutingAssembly().GetName().Name}.xml";
    var xmlPath = Path.Combine(AppContext.BaseDirectory, xmlFile);
    if (File.Exists(xmlPath))
    {
        c.IncludeXmlComments(xmlPath);
    }
});

// Configure Entity Framework
builder.Services.AddDbContext<ApplicationDbContext>(options =>
    options.UseNpgsql(builder.Configuration.GetConnectionString("DefaultConnection")));

// Register HttpContextAccessor for audit field management
builder.Services.AddHttpContextAccessor();

// Register repositories
builder.Services.AddScoped<IMenuRepository, MenuRepository>();
builder.Services.AddScoped<IUserRepository, UserRepository>();
builder.Services.AddScoped<IUserRoleRepository, UserRoleRepository>();
builder.Services.AddScoped<IRolePermissionRepository, RolePermissionRepository>();
builder.Services.AddScoped<IRoleRepository, RoleRepository>();
builder.Services.AddScoped<IPermissionRepository, PermissionRepository>();

// Register services
builder.Services.AddScoped<IMenuService, MenuService>();
builder.Services.AddScoped<IUserRoleService, UserRoleService>();
builder.Services.AddScoped<IRoleService, RoleService>();

// Configure JWT authentication
var jwtSettings = builder.Configuration.GetSection("Jwt");
var secretKey = jwtSettings["SecretKey"] ?? "DefaultSecretKey123456789";
var key = Encoding.ASCII.GetBytes(secretKey);

builder.Services.AddAuthentication(options =>
{
    options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
    options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
})
.AddJwtBearer(options =>
{
    options.RequireHttpsMetadata = false; // Development environment setting
    options.SaveToken = true;
    options.TokenValidationParameters = new TokenValidationParameters
    {
        ValidateIssuerSigningKey = true,
        IssuerSigningKey = new SymmetricSecurityKey(key),
        ValidateIssuer = true,
        ValidIssuer = jwtSettings["Issuer"],
        ValidateAudience = true,
        ValidAudience = jwtSettings["Audience"],
        ValidateLifetime = true,
        ClockSkew = TimeSpan.Zero
    };
});

builder.Services.AddAuthorization();

// Configure CORS
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowAll", builder =>
    {
        builder
            .AllowAnyOrigin()
            .AllowAnyMethod()
            .AllowAnyHeader();
    });
});

// Register HTTP client
builder.Services.AddHttpClient<IWorkbenchService, WorkbenchService>();
builder.Services.AddHttpClient(); // Add HttpClientFactory for AmisSyncService

// Register custom services
builder.Services.AddScoped<IAuthService, AuthService>();
builder.Services.AddScoped<IStaffSyncService, StaffSyncService>();
builder.Services.AddScoped<IConfigurationService, ConfigurationService>();
builder.Services.AddScoped<IWaterMeterService, WaterMeterService>();
builder.Services.AddScoped<IAmisSyncService, AmisSyncService>();
builder.Services.AddScoped<IBaselineService, BaselineService>();

// Register Planning & Scheduling services

builder.Services.AddScoped<IRouteService, RouteService>();
builder.Services.AddScoped<IFrequencyTemplateService, FrequencyTemplateService>();

// Register Task Management services
builder.Services.AddScoped<ITaskService, TaskService>();

// Register Work Package Management services
builder.Services.AddScoped<IWorkPackageService, WorkPackageService>();

// Register Meter Reading Management services
builder.Services.AddScoped<IMeterReadingService, MeterReadingService>();

// Register AMS Import services
builder.Services.AddScoped<AmsExcelImportService>();
builder.Services.AddScoped<AmsDataMappingService>();
builder.Services.AddScoped<AmsValidationService>();

// Register Mobile-specific services
builder.Services.AddScoped<IPhotoService, PhotoService>();
builder.Services.AddScoped<ICloudflareR2Service, CloudflareR2Service>();
builder.Services.AddScoped<ILocationService, LocationService>();
builder.Services.AddScoped<IAssignmentService, AssignmentService>();
builder.Services.AddScoped<IMeterService, MeterService>();
builder.Services.AddScoped<IUserService, UserService>();

// Register GPS services
builder.Services.AddScoped<IGpsService, GpsService>();

// Register Address services
builder.Services.AddScoped<IAddressService, AddressService>();

// Register Dynamic Select services
builder.Services.AddScoped<IDynamicDataService, DynamicDataService>();
builder.Services.AddScoped<WaterMeterManagement.Services.Strategies.DatabaseStrategy>();
builder.Services.AddScoped<WaterMeterManagement.Services.Strategies.EnumStrategy>();

// Configure AutoMapper
builder.Services.AddAutoMapper(typeof(Program));

// Add Health Checks
builder.Services.AddHealthChecks()
    .AddNpgSql(builder.Configuration.GetConnectionString("DefaultConnection")!);

var app = builder.Build();

// Enable static files for login page
app.UseStaticFiles();

// Enable Serilog request logging
app.UseSerilogRequestLogging();

// Add global exception handling middleware - MUST be early in pipeline
app.UseMiddleware<WaterMeterManagement.Middlewares.GlobalExceptionMiddleware>();

app.UseHttpsRedirection();

app.UseCors("AllowAll");

// Add Swagger authentication middleware - MUST be BEFORE JWT authentication middleware
app.UseMiddleware<SwaggerAuthMiddleware>();

app.UseAuthentication();
app.UseAuthorization();

// Configure HTTP request pipeline
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI(c =>
    {
        c.SwaggerEndpoint("/swagger/v1/swagger.json", "Water Meter Management API v1");
        c.RoutePrefix = "swagger"; // Set Swagger UI at /swagger path
        c.DocumentTitle = "Water Meter Management API - Swagger UI";
        c.DisplayRequestDuration();
        c.EnableDeepLinking();
        c.EnableFilter();
        c.ShowExtensions();
        c.EnableValidator();
        c.SupportedSubmitMethods(new[] { 
            Swashbuckle.AspNetCore.SwaggerUI.SubmitMethod.Get, 
            Swashbuckle.AspNetCore.SwaggerUI.SubmitMethod.Post, 
            Swashbuckle.AspNetCore.SwaggerUI.SubmitMethod.Put, 
            Swashbuckle.AspNetCore.SwaggerUI.SubmitMethod.Delete, 
            Swashbuckle.AspNetCore.SwaggerUI.SubmitMethod.Patch 
        });
    });
}

app.MapControllers();

// Map Health Check endpoint
app.MapHealthChecks("/health");

// Ensure database is created
using (var scope = app.Services.CreateScope())
{
    var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
    try
    {
        // Apply any pending migrations
        context.Database.Migrate();
        Log.Information("Database migrations applied successfully");
        
        // Seed initial data
        await DbInitializer.InitializeAsync(context);
        Log.Information("Database seeding completed successfully");
    }
    catch (Exception ex)
    {
        Log.Error(ex, "Database migration or seeding failed");
    }
}

Log.Information("🚀 Water Meter Management System Backend API started successfully");
Log.Information("📖 Swagger documentation available at: http://localhost:5000/swagger");
Log.Information("🔐 Login page available at: http://localhost:5000/login.html");

app.Run();
