using System.IdentityModel.Tokens.Jwt;
using Microsoft.IdentityModel.Tokens;
using System.Text;

namespace WaterMeterManagement.Middlewares
{
    /// <summary>
    /// Middleware to protect Swagger UI with authentication
    /// </summary>
    public class SwaggerAuthMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly ILogger<SwaggerAuthMiddleware> _logger;
        private readonly IConfiguration _configuration;

        public SwaggerAuthMiddleware(RequestDelegate next, ILogger<SwaggerAuthMiddleware> logger, IConfiguration configuration)
        {
            _next = next;
            _logger = logger;
            _configuration = configuration;
        }

        public async Task InvokeAsync(HttpContext context)
        {
            var path = context.Request.Path.Value?.ToLower();
            
            // For API requests, automatically add JWT token from cookie to Authorization header
            if (path?.StartsWith("/api/") == true && !path.Contains("/api/auth/login"))
            {
                // If no Authorization header but we have JWT cookie, add it
                if (!context.Request.Headers.ContainsKey("Authorization"))
                {
                    if (context.Request.Cookies.TryGetValue("jwt_token", out string? tokenFromCookie))
                    {
                        if (!string.IsNullOrEmpty(tokenFromCookie))
                        {
                            // Remove existing Authorization header if any
                            context.Request.Headers.Remove("Authorization");
                            // Add new Authorization header
                            context.Request.Headers.Add("Authorization", $"Bearer {tokenFromCookie}");
                            _logger.LogInformation("Auto-added JWT token from cookie to API request: {Path}", path);
                        }
                    }
                }
                else
                {
                    _logger.LogInformation("Authorization header already exists for API request: {Path}", path);
                }
            }
            
            // Check if this is a Swagger request
            if (path?.StartsWith("/swagger") == true)
            {
                // Check if user has valid JWT token
                if (!IsValidToken(context))
                {
                    _logger.LogWarning("Unauthorized access to Swagger: {Path}", path);
                    context.Response.Redirect("/login.html");
                    return;
                }
                
                _logger.LogInformation("Authenticated user accessing Swagger");
            }
            
            // Handle root path redirection
            if (path == "/" || path == "/index.html")
            {
                if (IsValidToken(context))
                {
                    context.Response.Redirect("/swagger");
                    return;
                }
                else
                {
                    context.Response.Redirect("/login.html");
                    return;
                }
            }

            await _next(context);
        }
        
        private bool IsValidToken(HttpContext context)
        {
            // Check cookie first
            if (context.Request.Cookies.TryGetValue("jwt_token", out string? tokenFromCookie))
            {
                _logger.LogInformation("Found JWT token in cookie, validating...");
                if (ValidateJwtToken(tokenFromCookie))
                {
                    _logger.LogInformation("Cookie token is valid");
                    return true;
                }
                else
                {
                    _logger.LogWarning("Cookie token is invalid");
                }
            }
            else
            {
                _logger.LogInformation("No JWT token found in cookies");
            }
            
            // Check Authorization header
            var authHeader = context.Request.Headers["Authorization"].FirstOrDefault();
            if (authHeader != null && authHeader.StartsWith("Bearer "))
            {
                var token = authHeader.Substring("Bearer ".Length).Trim();
                _logger.LogInformation("Found JWT token in Authorization header, validating...");
                if (ValidateJwtToken(token))
                {
                    _logger.LogInformation("Authorization header token is valid");
                    return true;
                }
                else
                {
                    _logger.LogWarning("Authorization header token is invalid");
                }
            }
            
            return false;
        }
        
        private bool ValidateJwtToken(string? token)
        {
            if (string.IsNullOrEmpty(token))
            {
                _logger.LogWarning("Token is null or empty");
                return false;
            }
                
            try
            {
                var jwtSettings = _configuration.GetSection("Jwt");
                var secretKey = jwtSettings["SecretKey"] ?? "WaterMeterManagement_SuperSecretKey_2024_ForDevelopmentOnly";
                
                _logger.LogInformation("Validating token...");
                
                var key = Encoding.ASCII.GetBytes(secretKey);

                var tokenHandler = new JwtSecurityTokenHandler();
                var validationParameters = new TokenValidationParameters
                {
                    ValidateIssuerSigningKey = true,
                    IssuerSigningKey = new SymmetricSecurityKey(key),
                    ValidateIssuer = false,  // 先关掉这些验证
                    ValidateAudience = false, // 先关掉这些验证
                    ValidateLifetime = true,
                    ClockSkew = TimeSpan.FromMinutes(5) // 给一些时间容差
                };

                tokenHandler.ValidateToken(token, validationParameters, out SecurityToken validatedToken);
                _logger.LogInformation("Token validation successful");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Token validation failed: {Message}", ex.Message);
                return false;
            }
        }


    }
} 