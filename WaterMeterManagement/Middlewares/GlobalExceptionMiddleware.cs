using System.Net;
using System.Text.Json;
using Microsoft.EntityFrameworkCore;
using WaterMeterManagement.DTOs;
using WaterMeterManagement.Enums;
using WaterMeterManagement.Exceptions;

namespace WaterMeterManagement.Middlewares
{
    public class GlobalExceptionMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly ILogger<GlobalExceptionMiddleware> _logger;

        public GlobalExceptionMiddleware(RequestDelegate next, ILogger<GlobalExceptionMiddleware> logger)
        {
            _next = next;
            _logger = logger;
        }

        public async Task InvokeAsync(HttpContext context)
        {
            try
            {
                await _next(context);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "An unhandled exception occurred");
                await HandleExceptionAsync(context, ex);
            }
        }

        private async Task HandleExceptionAsync(HttpContext context, Exception exception)
        {
            context.Response.ContentType = "application/json";

            var response = exception switch
            {
                BusinessException businessEx => ApiResponse.Error(businessEx.Message, (int)businessEx.ErrorCode),

                UnauthorizedAccessException => ApiResponse.Error(
                    Constants.ErrorMessages.GetMessage(ErrorCode.UnauthorizedAccess),
                    (int)ErrorCode.UnauthorizedAccess),

                ArgumentNullException => ApiResponse.Error(
                    Constants.ErrorMessages.GetMessage(ErrorCode.ValidationError),
                    (int)ErrorCode.ValidationError),

                ArgumentException argEx => ApiResponse.Error(argEx.Message, (int)ErrorCode.ValidationError),

                DbUpdateException dbEx => ApiResponse.Error(
                    GetDatabaseErrorMessage(dbEx),
                    (int)ErrorCode.DatabaseError),

                InvalidOperationException invalidOpEx => ApiResponse.Error(
                    invalidOpEx.Message,
                    (int)ErrorCode.ValidationError),

                _ => ApiResponse.Error(exception.Message, (int)ErrorCode.UnknownError)
            };

            // Set HTTP status code
            context.Response.StatusCode = exception switch
            {
                BusinessException businessEx => businessEx.HttpStatusCode,
                UnauthorizedAccessException => (int)HttpStatusCode.Unauthorized,
                ArgumentNullException => (int)HttpStatusCode.BadRequest,
                ArgumentException => (int)HttpStatusCode.BadRequest,
                DbUpdateException => (int)HttpStatusCode.InternalServerError,
                InvalidOperationException => (int)HttpStatusCode.BadRequest,
                _ => (int)HttpStatusCode.InternalServerError
            };

            var jsonResponse = JsonSerializer.Serialize(response, new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            });

            await context.Response.WriteAsync(jsonResponse);
        }

        private string GetDatabaseErrorMessage(DbUpdateException dbEx)
        {
            // Handle specific database errors
            if (dbEx.InnerException?.Message.Contains("duplicate key") == true)
            {
                return "A record with this information already exists.";
            }

            if (dbEx.InnerException?.Message.Contains("foreign key") == true)
            {
                return "This operation would violate data integrity constraints.";
            }

            if (dbEx.InnerException?.Message.Contains("unique constraint") == true)
            {
                return "A record with this unique identifier already exists.";
            }

            return Constants.ErrorMessages.GetMessage(ErrorCode.DatabaseError);
        }
    }
}
