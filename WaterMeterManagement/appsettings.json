{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore": "Information"}}, "AllowedHosts": "*", "ConnectionStrings": {"DefaultConnection": "Host=localhost;Port=5432;Database=WaterMeterManagementDb;Username=postgres;Password=******"}, "Jwt": {"SecretKey": "WaterMeterManagement_SuperSecretKey_2024_ForDevelopmentOnly", "Issuer": "WaterMeterManagement", "Audience": "WaterMeterManagement", "ExpirationDays": 30}, "Workbench": {"BaseUrl": "https://sicon-mnlweb.sicon.co.nz/WorkbenchLogTest/api", "TimeoutSeconds": 10}, "CloudflareR2": {"Endpoint": "https://13b75f12df74cacdb8cbc18db2cf27a8.r2.cloudflarestorage.com", "BucketName": "water-meter-photos", "AccessKey": "47fa93b83106267a3b11eda5471ec389", "SecretKey": "7769bc2a5080f793866a495c60de62092f17bc2edd8d089c712c9692ec4de0a5", "CustomDomain": "https://pub-00d931e8b01046cdab19093c76aba40b.r2.dev"}, "GoogleMaps": {"ApiKey": "AIzaSyA5SvS5evWbn2_ory2v1H_Q4dB9qsGQjWE"}, "Serilog": {"Using": ["Serilog.Sinks.Console", "Serilog.Sinks.File"], "MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "System": "Warning"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss} {Level:u3}] {Message:lj} {Properties:j}{NewLine}{Exception}"}}, {"Name": "File", "Args": {"path": "logs/watermeter-.log", "rollingInterval": "Day", "outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss} {Level:u3}] {Message:lj} {Properties:j}{NewLine}{Exception}"}}]}}