using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace WaterMeterManagement.Models
{
    /// <summary>
    /// Water Meter Model - Unified model supporting both legacy and AMS integration
    /// Combines original WaterMeter fields with AMS export file structure
    /// Maps to "Rolleston Area 1 MASTER" table in AMS Excel file
    /// </summary>
    public class WaterMeter : BaseEntity
    {
        // ============ AMS Core Fields ============
        
        /// <summary>
        /// Asset ID - Unique identifier in AMS system
        /// Maps to Excel field: Asset ID
        /// </summary>
        [StringLength(20)]
        public string? AssetId { get; set; }

        /// <summary>
        /// Meter Number - Physical meter serial number
        /// Maps to Excel field: Meter Number (also used as SerialNumber)
        /// </summary>
        [Required]
        [StringLength(50)]
        public string SerialNumber { get; set; } = string.Empty;

        /// <summary>
        /// Account Number - Customer account association
        /// Maps to Excel field: Account Number
        /// </summary>
        [StringLength(20)]
        public string? AccountNumber { get; set; }

        /// <summary>
        /// Book Number - AMS internal management reference
        /// Maps to Excel field: Book Number
        /// </summary>
        [StringLength(20)]
        public string? BookNumber { get; set; }

        /// <summary>
        /// Unit identifier - typically 10
        /// Maps to Excel field: Unit
        /// </summary>
        public int Unit { get; set; } = 10;

        // ============ Geographic Location ============

        /// <summary>
        /// Road Number
        /// Maps to Excel field: Road Number
        /// </summary>
        public int? RoadNumber { get; set; }

        /// <summary>
        /// Road Name
        /// Maps to Excel field: Road Name
        /// </summary>
        [StringLength(200)]
        public string? RoadName { get; set; }

        /// <summary>
        /// Township area
        /// Maps to Excel field: Township
        /// </summary>
        [StringLength(100)]
        public string? Township { get; set; }

        /// <summary>
        /// Sub area designation
        /// Maps to Excel field: Sub Area
        /// </summary>
        [StringLength(100)]
        public string? SubArea { get; set; }

        /// <summary>
        /// Legacy Location field - maps to combination of address fields
        /// </summary>
        [Required]
        [StringLength(100)]
        public string Location { get; set; } = string.Empty;

        /// <summary>
        /// Full address
        /// </summary>
        [StringLength(200)]
        public string Address { get; set; } = string.Empty;

        /// <summary>
        /// Full combined address from AMS fields
        /// </summary>
        public string FullAddress => !string.IsNullOrEmpty(RoadName) || !string.IsNullOrEmpty(Township) 
            ? $"{RoadNumber} {RoadName}, {SubArea}, {Township}".Trim()
            : Address;

        // ============ Reading Information ============

        /// <summary>
        /// Last reading value from AMS
        /// Maps to Excel field: Last Read
        /// </summary>
        [Column(TypeName = "decimal(12,2)")]
        public decimal? LastRead { get; set; }

        /// <summary>
        /// Recent change amount
        /// Maps to Excel field: Recent Change
        /// </summary>
        [Column(TypeName = "decimal(12,2)")]
        public decimal? RecentChange { get; set; }

        /// <summary>
        /// Subdivision flag
        /// Maps to Excel field: Subd
        /// </summary>
        [StringLength(10)]
        public string? Subd { get; set; }

        /// <summary>
        /// Date of reading from AMS
        /// Maps to Excel field: Date Of Read
        /// </summary>
        public DateTime? DateOfRead { get; set; }

        /// <summary>
        /// Current reading value from AMS
        /// Maps to Excel field: Read
        /// </summary>
        [Column(TypeName = "decimal(12,2)")]
        public decimal? Read { get; set; }

        /// <summary>
        /// Unable to read flag
        /// Maps to Excel field: Can't Read
        /// </summary>
        public bool CantRead { get; set; } = false;

        /// <summary>
        /// Reading condition or status
        /// Maps to Excel field: Condition
        /// </summary>
        [StringLength(50)]
        public string? Condition { get; set; }

        /// <summary>
        /// Detailed comments and notes from AMS
        /// Maps to Excel field: Comments
        /// </summary>
        [StringLength(1000)]
        public string? Comments { get; set; }

        // ============ Legacy Reading Fields ============

        /// <summary>
        /// Legacy LastReading field - maps to LastRead
        /// </summary>
        [Column(TypeName = "decimal(12,4)")]
        public decimal? LastReading 
        { 
            get => LastRead; 
            set => LastRead = value; 
        }

        private DateTime? _lastReadingDate;
        /// <summary>
        /// Last reading date (legacy field)
        /// </summary>
        public DateTime? LastReadingDate 
        { 
            get => _lastReadingDate ?? DateOfRead; 
            set => _lastReadingDate = value; 
        }

        // ============ Meter Information ============

        [Required]
        [StringLength(50)]
        public string MeterType { get; set; } = string.Empty; // e.g., "Residential", "Commercial", "Industrial"

        [Required]
        [StringLength(20)]
        public string Status { get; set; } = "Active"; // Active, Inactive, Maintenance, Replaced

        public DateTime? InstallDate { get; set; }

        [StringLength(50)]
        public string? CustomerCode { get; set; }

        [StringLength(100)]
        public string? CustomerName { get; set; }

        [Column(TypeName = "decimal(10,6)")]
        public decimal? Latitude { get; set; }

        [Column(TypeName = "decimal(10,6)")]
        public decimal? Longitude { get; set; }

        [StringLength(100)]
        public string? Brand { get; set; }

        [StringLength(50)]
        public string? Model { get; set; }

        public int? BatteryLevel { get; set; } // for smart meters

        [StringLength(20)]
        public string? CommunicationMethod { get; set; } // LoRaWAN, NB-IoT, etc.

        public DateTime? LastMaintenanceDate { get; set; }

        public DateTime? NextMaintenanceDate { get; set; }

        [StringLength(500)]
        public string? Notes { get; set; }

        // ============ AMS System Fields ============
        
        [StringLength(20)]
        public string Source { get; set; } = "Manual"; // AMS, Manual

        /// <summary>
        /// Assigned route ID - Foreign key to Route table
        /// </summary>
        public int? RouteId { get; set; }

        /// <summary>
        /// Assigned route name (from Data Check table)
        /// </summary>
        [StringLength(50)]
        public string? AssignedRoute { get; set; }

        /// <summary>
        /// Sequence number within route
        /// </summary>
        public int? RouteSequence { get; set; }

        /// <summary>
        /// AMS synchronization status
        /// </summary>
        [StringLength(20)]
        public string SyncStatus { get; set; } = "Synced"; // Synced, Pending, Error

        public DateTime? LastSyncDate { get; set; }

        [StringLength(50)]
        public string MeterSize { get; set; } = string.Empty;

        // ============ Computed Properties ============

        /// <summary>
        /// Has geographic coordinates
        /// </summary>
        public bool HasCoordinates => Latitude.HasValue && Longitude.HasValue;

        /// <summary>
        /// Has recent reading data
        /// </summary>
        public bool HasRecentReading => (LastRead.HasValue || Read.HasValue) && (DateOfRead.HasValue || LastReadingDate.HasValue);

        /// <summary>
        /// Reading is anomalous
        /// </summary>
        public bool IsReadingAnomalous => CantRead || !string.IsNullOrEmpty(Condition);

        // ============ Legacy Compatibility Properties ============
        
        /// <summary>
        /// Legacy MeterId field - maps to AssetId or SerialNumber
        /// </summary>
        public string MeterId 
        { 
            get => AssetId ?? SerialNumber; 
            set => AssetId = value; 
        }
        
        /// <summary>
        /// Legacy MeterNumber field - maps to SerialNumber
        /// </summary>
        public string MeterNumber 
        { 
            get => SerialNumber; 
            set => SerialNumber = value; 
        }
        
        /// <summary>
        /// Legacy PropertyId field - maps to AccountNumber or CustomerCode
        /// </summary>
        public string PropertyId 
        { 
            get => AccountNumber ?? CustomerCode ?? ""; 
            set 
            { 
                if (string.IsNullOrEmpty(AccountNumber)) 
                    AccountNumber = value; 
                if (string.IsNullOrEmpty(CustomerCode)) 
                    CustomerCode = value; 
            } 
        }

        // ============ Navigation Properties ============

        public virtual Route? Route { get; set; }
        public virtual ICollection<MeterReading> MeterReadings { get; set; } = new List<MeterReading>();
        public virtual ICollection<BaselineRecord> BaselineRecords { get; set; } = new List<BaselineRecord>();


    }
} 