using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace WaterMeterManagement.Models
{
    /// <summary>
    /// Configuration template for defining available configuration options
    /// Serves as metadata for configuration items
    /// </summary>
    [Table("ConfigurationTemplates")]
    public class ConfigurationTemplate : BaseEntity
    {
        // Id is inherited from BaseEntity

        /// <summary>
        /// Configuration scope: 'System', 'User', 'Role', 'Department', etc.
        /// </summary>
        [Required]
        [StringLength(50)]
        public string Scope { get; set; } = string.Empty;

        /// <summary>
        /// Configuration category: 'General', 'Security', 'Notifications', 'Appearance', etc.
        /// </summary>
        [Required]
        [StringLength(100)]
        public string Category { get; set; } = string.Empty;

        /// <summary>
        /// Category display name
        /// </summary>
        [StringLength(200)]
        public string CategoryDisplayName { get; set; } = string.Empty;

        /// <summary>
        /// Category icon (Ant Design icon name)
        /// </summary>
        [StringLength(50)]
        public string CategoryIcon { get; set; } = string.Empty;

        /// <summary>
        /// Configuration key/name
        /// </summary>
        [Required]
        [StringLength(200)]
        public string Key { get; set; } = string.Empty;

        /// <summary>
        /// Human-readable display name
        /// </summary>
        [Required]
        [StringLength(200)]
        public string DisplayName { get; set; } = string.Empty;

        /// <summary>
        /// Configuration description/help text
        /// </summary>
        [StringLength(500)]
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// Data type: 'string', 'number', 'boolean', 'array', 'object', 'time', 'date', 'email', 'password'
        /// </summary>
        [Required]
        [StringLength(20)]
        public string DataType { get; set; } = string.Empty;

        /// <summary>
        /// Default value (JSON serialized)
        /// </summary>
        public string DefaultValue { get; set; } = string.Empty;

        /// <summary>
        /// Validation rules (JSON format)
        /// e.g., {"min": 1, "max": 100, "required": true, "options": [{"label": "Option 1", "value": "opt1"}]}
        /// </summary>
        public string ValidationRules { get; set; } = string.Empty;

        /// <summary>
        /// UI component type: 'input', 'number', 'switch', 'select', 'radio', 'checkbox', 'slider', 'timepicker'
        /// </summary>
        [Required]
        [StringLength(50)]
        public string UIComponent { get; set; } = string.Empty;

        /// <summary>
        /// UI component properties (JSON format)
        /// e.g., {"placeholder": "Enter value", "min": 0, "max": 100}
        /// </summary>
        public string UIProperties { get; set; } = string.Empty;

        /// <summary>
        /// Display order within category
        /// </summary>
        public int DisplayOrder { get; set; } = 0;

        /// <summary>
        /// Whether this configuration requires system restart to take effect
        /// </summary>
        public bool RequiresRestart { get; set; } = false;

        /// <summary>
        /// Whether this configuration is sensitive (password, API key, etc.)
        /// </summary>
        public bool IsSensitive { get; set; } = false;

        /// <summary>
        /// Whether this configuration is read-only
        /// </summary>
        public bool IsReadOnly { get; set; } = false;

        /// <summary>
        /// Whether this template is active
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// Group within category (for organizing related settings)
        /// </summary>
        [StringLength(100)]
        public string Group { get; set; } = string.Empty;

        /// <summary>
        /// Group display name
        /// </summary>
        [StringLength(200)]
        public string GroupDisplayName { get; set; } = string.Empty;
    }
} 