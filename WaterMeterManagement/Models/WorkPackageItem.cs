using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace WaterMeterManagement.Models
{
    /// <summary>
    /// Work Package Item Model - Work Package Item
    /// Represents specific meters contained in a work package, defining reading order and status
    /// </summary>
    [Table("WorkPackageItems")]
    public class WorkPackageItem : BaseEntity
    {
        /// <summary>
        /// Parent work package ID
        /// </summary>
        [Required]
        public int WorkPackageId { get; set; }

        /// <summary>
        /// Water meter ID
        /// </summary>
        [Required]
        public int MeterId { get; set; }

        /// <summary>
        /// Execution order within the work package
        /// </summary>
        [Required]
        public int SequenceOrder { get; set; }

        /// <summary>
        /// Item status
        /// </summary>
        [Required]
        [StringLength(20)]
        public string Status { get; set; } = "Pending"; // Pending, InProgress, Completed, Skipped, Failed

        /// <summary>
        /// Scheduled execution date
        /// </summary>
        public DateTime? ScheduledDate { get; set; }

        /// <summary>
        /// Actual execution date
        /// </summary>
        public DateTime? ActualDate { get; set; }

        /// <summary>
        /// Assigned user (legacy string field - to be deprecated)
        /// </summary>
        [StringLength(100)]
        public string? AssignedTo { get; set; }

        /// <summary>
        /// ID of the assigned user (for referential integrity)
        /// </summary>
        public int? AssignedUserId { get; set; }

        /// <summary>
        /// Priority (can override work package priority)
        /// </summary>
        [StringLength(20)]
        public string? Priority { get; set; } // Low, Medium, High, Critical

        /// <summary>
        /// Estimated reading time in minutes
        /// </summary>
        public int? EstimatedMinutes { get; set; }

        /// <summary>
        /// Actual reading time in minutes
        /// </summary>
        public int? ActualMinutes { get; set; }

        /// <summary>
        /// Special instructions for this specific meter
        /// </summary>
        [StringLength(500)]
        public string? SpecialInstructions { get; set; }

        /// <summary>
        /// Notes
        /// </summary>
        [StringLength(500)]
        public string? Notes { get; set; }

        /// <summary>
        /// Last reading for validation
        /// </summary>
        [StringLength(20)]
        public string? LastReading { get; set; }

        /// <summary>
        /// Whether special handling is required
        /// </summary>
        public bool RequiresSpecialHandling { get; set; } = false;

        /// <summary>
        /// Reason for special handling
        /// </summary>
        [StringLength(200)]
        public string? SpecialHandlingReason { get; set; }

        /// <summary>
        /// Access difficulty rating
        /// </summary>
        [Column(TypeName = "decimal(2,1)")]
        public decimal? DifficultyRating { get; set; } // 1.0 to 5.0

        /// <summary>
        /// GPS coordinates - Latitude
        /// </summary>
        [Column(TypeName = "decimal(10,6)")]
        public decimal? Latitude { get; set; }

        /// <summary>
        /// GPS coordinates - Longitude
        /// </summary>
        [Column(TypeName = "decimal(10,6)")]
        public decimal? Longitude { get; set; }

        /// <summary>
        /// Service address
        /// </summary>
        [StringLength(200)]
        public string? ServiceAddress { get; set; }

        /// <summary>
        /// Skip reason (if status is Skipped)
        /// </summary>
        [StringLength(200)]
        public string? SkipReason { get; set; }

        /// <summary>
        /// Failure reason (if status is Failed)
        /// </summary>
        [StringLength(200)]
        public string? FailureReason { get; set; }

        /// <summary>
        /// Number of retries attempted
        /// </summary>
        public int RetryCount { get; set; } = 0;

        /// <summary>
        /// Maximum number of retries allowed
        /// </summary>
        public int MaxRetries { get; set; } = 3;

        /// <summary>
        /// Completion timestamp
        /// </summary>
        public DateTime? CompletedAt { get; set; }

        /// <summary>
        /// User who completed the item
        /// </summary>
        [StringLength(100)]
        public string? CompletedBy { get; set; }

        // ============ Computed Properties ============

        /// <summary>
        /// Whether the item is completed
        /// </summary>
        public bool IsCompleted => Status == "Completed";

        /// <summary>
        /// Whether the item can be retried
        /// </summary>
        public bool CanRetry => Status == "Failed" && RetryCount < MaxRetries;

        /// <summary>
        /// Whether the item is overdue
        /// </summary>
        public bool IsOverdue => ScheduledDate.HasValue && !IsCompleted && DateTime.UtcNow > ScheduledDate.Value;

        /// <summary>
        /// Efficiency score (actual time vs estimated time)
        /// </summary>
        public decimal? EfficiencyScore => 
            EstimatedMinutes.HasValue && ActualMinutes.HasValue && EstimatedMinutes > 0
                ? (decimal)EstimatedMinutes.Value / ActualMinutes.Value * 100
                : null;

        // ============ Navigation Properties ============

        /// <summary>
        /// Parent work package
        /// </summary>
        [ForeignKey("WorkPackageId")]
        public virtual WorkPackage WorkPackage { get; set; } = null!;

        /// <summary>
        /// Associated water meter
        /// </summary>
        [ForeignKey("MeterId")]
        public virtual WaterMeter Meter { get; set; } = null!;

        /// <summary>
        /// Generated tasks (a work package item may generate one or more tasks)
        /// </summary>
        public virtual ICollection<WorkTask> Tasks { get; set; } = new List<WorkTask>();

        /// <summary>
        /// Meter reading records
        /// </summary>
        public virtual ICollection<MeterReading> Readings { get; set; } = new List<MeterReading>();
    }
} 