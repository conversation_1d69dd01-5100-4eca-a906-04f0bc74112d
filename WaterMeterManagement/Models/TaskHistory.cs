using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace WaterMeterManagement.Models
{
    [Table("TaskHistories")]
    public class TaskHistory
    {
        [Key]
        public int Id { get; set; }

        [Required]
        public int TaskId { get; set; }

        [Required]
        [StringLength(100)]
        public string Action { get; set; } = string.Empty; // Created, Updated, Assigned, Started, Completed, Cancelled

        [StringLength(500)]
        public string? OldValue { get; set; }

        [StringLength(500)]
        public string? NewValue { get; set; }

        [Required]
        [StringLength(100)]
        public string ChangedBy { get; set; } = string.Empty;

        [Required]
        public DateTime ChangedAt { get; set; } = DateTime.UtcNow;

        [StringLength(500)]
        public string? Reason { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public bool IsDeleted { get; set; } = false;

        // Navigation properties
        [ForeignKey("TaskId")]
        public virtual WorkTask Task { get; set; } = null!;
    }
} 