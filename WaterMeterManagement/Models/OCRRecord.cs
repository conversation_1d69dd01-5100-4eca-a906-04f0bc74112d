using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace WaterMeterManagement.Models
{
    public class OCRRecord
    {
        [Key]
        public int Id { get; set; }

        [Required]
        public int MeterReadingId { get; set; }

        public int? ReadingPhotoId { get; set; }

        [Required]
        [StringLength(50)]
        public string ProcessingEngine { get; set; } = string.Empty; // TesseractOCR, GoogleVision, AzureCognitive, etc.

        [StringLength(20)]
        public string ProcessingVersion { get; set; } = string.Empty;

        [Required]
        [StringLength(50)]
        public string RecognizedValue { get; set; } = string.Empty;

        [Column(TypeName = "decimal(5,2)")]
        public decimal ConfidenceScore { get; set; }

        [Required]
        [StringLength(20)]
        public string Status { get; set; } = "Success"; // Success, Failed, LowConfidence, RequiresReview

        [Column(TypeName = "text")]
        public string? RawData { get; set; } // Full OCR response data in JSON format

        [StringLength(500)]
        public string? ErrorMessage { get; set; }

        public int ProcessingTimeMs { get; set; }

        [StringLength(200)]
        public string? BoundingBox { get; set; } // Coordinates of recognized text

        [Column(TypeName = "decimal(5,2)")]
        public decimal? QualityScore { get; set; }

        public bool IsManuallyVerified { get; set; } = false;

        [StringLength(50)]
        public string? VerifiedBy { get; set; }

        public DateTime? VerifiedDate { get; set; }

        [StringLength(50)]
        public string? CorrectedValue { get; set; }

        [StringLength(500)]
        public string? CorrectionReason { get; set; }

        public bool IsTrainingData { get; set; } = false;

        [StringLength(100)]
        public string? TrainingLabel { get; set; }

        // Processing metadata
        [Column(TypeName = "text")]
        public string? ProcessingMetadata { get; set; } // Additional processing info in JSON format

        // Audit fields
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
        
        [StringLength(50)]
        public string CreatedBy { get; set; } = string.Empty;
        
        [StringLength(50)]
        public string UpdatedBy { get; set; } = string.Empty;
        
        public bool IsDeleted { get; set; } = false;

        // Navigation properties
        public virtual MeterReading MeterReading { get; set; } = null!;
        public virtual ReadingPhoto? ReadingPhoto { get; set; }
    }
} 