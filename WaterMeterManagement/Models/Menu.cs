using System.Collections.Generic;

namespace WaterMeterManagement.Models
{
    public class Menu : BaseEntity
    {
        public string Name { get; set; }
        public string Code { get; set; }
        public string Path { get; set; }
        public string Component { get; set; }
        public string Icon { get; set; }
        public int? ParentId { get; set; }
        public int Order { get; set; }
        public bool IsVisible { get; set; }
        public int? PermissionId { get; set; }  // 外键到Permission表
        
        // 导航属性
        public virtual Menu? Parent { get; set; }
        public virtual ICollection<Menu> Children { get; set; } = new List<Menu>();
        public virtual Permission? Permission { get; set; }
    }
} 