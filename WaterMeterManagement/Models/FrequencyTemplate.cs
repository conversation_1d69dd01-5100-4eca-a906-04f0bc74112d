using System.ComponentModel.DataAnnotations;

namespace WaterMeterManagement.Models
{
    public class FrequencyTemplate : BaseEntity
    {
        [Required]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;

        [StringLength(500)]
        public string? Description { get; set; }

        [Required]
        [StringLength(20)]
        public string FrequencyType { get; set; } = string.Empty; // Daily, Weekly, Monthly, Quarterly, Annual, Custom

        [Required]
        public int IntervalValue { get; set; } = 1;

        [StringLength(20)]
        public string? IntervalUnit { get; set; } // Days, Weeks, Months, Years

        [StringLength(20)]
        public string? DayOfWeek { get; set; } // For weekly frequencies

        public int? DayOfMonth { get; set; } // For monthly frequencies

        [StringLength(20)]
        public string? MonthOfYear { get; set; } // For annual frequencies

        [StringLength(100)]
        public string? TimeOfDay { get; set; } // HH:mm format

        [Required]
        [StringLength(20)]
        public string Status { get; set; } = "Active"; // Active, Inactive, Archived

        [StringLength(50)]
        public string? Category { get; set; } // Residential, Commercial, Industrial

        public bool IsDefault { get; set; } = false;

        public int? EstimatedDuration { get; set; } // in minutes per meter

        [StringLength(500)]
        public string? Notes { get; set; }

        public int UsageCount { get; set; } = 0;

        public DateTime? LastUsed { get; set; }

        // JSON configuration for complex scheduling rules
        [StringLength(2000)]
        public string? AdvancedConfiguration { get; set; }

        // Navigation properties
        public virtual ICollection<WorkPackage> WorkPackages { get; set; } = new List<WorkPackage>();
    }
} 