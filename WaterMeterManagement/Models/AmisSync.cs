using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace WaterMeterManagement.Models
{
    public class AmisSync : BaseEntity
    {
        [Required]
        [StringLength(50)]
        public string SyncType { get; set; } = string.Empty; // FullSync, IncrementalSync, MeterData, BaselineData

        [Required]
        [StringLength(20)]
        public string Status { get; set; } = "Pending"; // Pending, Running, Completed, Failed, Cancelled

        [Required]
        public DateTime StartTime { get; set; }

        public DateTime? EndTime { get; set; }

        public TimeSpan? Duration => EndTime.HasValue ? EndTime.Value - StartTime : null;

        [StringLength(50)]
        public string? TriggerBy { get; set; } // System, Manual, Scheduled

        // Sync Statistics
        public int TotalRecords { get; set; } = 0;
        public int ProcessedRecords { get; set; } = 0;
        public int SuccessfulRecords { get; set; } = 0;
        public int FailedRecords { get; set; } = 0;
        public int SkippedRecords { get; set; } = 0;

        // Progress tracking
        [Column(TypeName = "decimal(5,2)")]
        public decimal ProgressPercentage { get; set; } = 0;

        [StringLength(100)]
        public string? CurrentOperation { get; set; }

        // Configuration used for this sync
        [StringLength(500)]
        public string? SyncConfiguration { get; set; } // JSON config

        // Result summary
        [StringLength(1000)]
        public string? ResultSummary { get; set; }

        [StringLength(2000)]
        public string? ErrorMessage { get; set; }

        [Column(TypeName = "text")]
        public string? DetailedLog { get; set; }

        // Performance metrics
        public int? RecordsPerSecond { get; set; }

        [Column(TypeName = "decimal(10,2)")]
        public decimal? DataSizeMB { get; set; }

        // Retry information
        public int RetryCount { get; set; } = 0;
        public int MaxRetries { get; set; } = 3;
        public DateTime? NextRetryTime { get; set; }

        // AMIS specific fields
        [StringLength(100)]
        public string? AmisEndpoint { get; set; }

        [StringLength(50)]
        public string? AmisVersion { get; set; }

        public DateTime? LastSyncDate { get; set; }

        [StringLength(100)]
        public string? SyncToken { get; set; }

        // Navigation properties
        public virtual ICollection<AmisSyncError> SyncErrors { get; set; } = new List<AmisSyncError>();
    }
} 