using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace WaterMeterManagement.Models
{
    public class MeterReading
    {
        // ============ 基础信息 ============
        [Key]
        public int Id { get; set; }

        [Required]
        public int MeterId { get; set; }

        [Required]
        public int UserId { get; set; }                    // 执行读数的用户

        [Required]
        public int TaskId { get; set; }                    // 任务ID（核心字段）

        // ============ 读数数据 ============
        [Required]
        [Column(TypeName = "decimal(12,4)")]
        public decimal ReadingValue { get; set; }

        [Required]
        public DateTime ReadingDate { get; set; }

        // ============ 读数方法 ============
        [Required]
        [StringLength(20)]
        public string ReadingMethod { get; set; } = "Manual";        // Manual, OCR, Estimated

        [StringLength(20)]
        public string ReadingType { get; set; } = "Regular";         // Regular, Reactive, Emergency

        [StringLength(20)]
        public string DataSource { get; set; } = "Mobile";           // Mobile, Web, Import

        // ============ OCR相关 ============
        public bool HasOCR { get; set; } = false;
        [StringLength(20)] public string? OCRStatus { get; set; }    // Success, Failed, LowConfidence
        [Column(TypeName = "decimal(5,2)")] public decimal? OCRConfidence { get; set; }  // 0-100

        // ============ GPS位置信息 ============
        [Column(TypeName = "decimal(10,6)")] public decimal? Latitude { get; set; }
        [Column(TypeName = "decimal(10,6)")] public decimal? Longitude { get; set; }
        [Column(TypeName = "decimal(8,2)")] public decimal? GpsAccuracy { get; set; }   // 米
        [StringLength(200)] public string? Location { get; set; }    // 文本位置描述

        // ============ 状态管理 ============
        [Required]
        [StringLength(20)]
        public string Status { get; set; } = "Completed";            // Completed, Pending, Validated, Rejected

        [StringLength(50)]
        public string? ValidationStatus { get; set; }                // Valid, Invalid, RequiresReview

        // ============ 验证信息 ============
        public bool IsValidated { get; set; } = false;
        public int? ValidatedBy { get; set; }                        // 验证人用户ID
        public DateTime? ValidationDate { get; set; }
        [StringLength(500)] public string? ValidationComments { get; set; }

        // ============ 异常检测 ============
        public bool IsAnomalous { get; set; } = false;
        [StringLength(200)] public string? AnomalyReason { get; set; }
        [StringLength(100)] public string? AnomalyType { get; set; }

        public bool CantRead { get; set; } = false;                  // 无法读取标志
        [StringLength(200)] public string? CantReadReason { get; set; }

        // ============ 备注 ============
        [StringLength(500)] public string? Notes { get; set; }

        // ============ 审计字段 ============
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
        [StringLength(50)] public string CreatedBy { get; set; } = string.Empty;
        [StringLength(50)] public string UpdatedBy { get; set; } = string.Empty;
        public bool IsDeleted { get; set; } = false;

        // ============ 导航属性 ============
        public virtual WaterMeter WaterMeter { get; set; } = null!;
        public virtual User User { get; set; } = null!;
        public virtual WorkTask Task { get; set; } = null!;          // 关联任务
        public virtual User? Validator { get; set; }                 // 验证人

        // 关联的子表
        public virtual ICollection<ReadingPhoto> Photos { get; set; } = new List<ReadingPhoto>();
        public virtual ICollection<ReadingAnomaly> Anomalies { get; set; } = new List<ReadingAnomaly>();
        public virtual ICollection<OCRRecord> OCRRecords { get; set; } = new List<OCRRecord>();
    }
}