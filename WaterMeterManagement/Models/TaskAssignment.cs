using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace WaterMeterManagement.Models
{
    [Table("TaskAssignments")]
    public class TaskAssignment
    {
        [Key]
        public int Id { get; set; }

        [Required]
        public int TaskId { get; set; }

        [Required]
        public int UserId { get; set; }

        [Required]
        public DateTime AssignedDate { get; set; } = DateTime.UtcNow;

        [Required]
        [StringLength(100)]
        public string AssignedBy { get; set; } = string.Empty;

        [Required]
        [StringLength(50)]
        public string AssignmentType { get; set; } = "Manual"; // Manual, Bulk, Reactive

        [Required]
        [StringLength(50)]
        public string Status { get; set; } = "Assigned"; // Assigned, Accepted, Rejected, Completed

        public DateTime? AcceptedDate { get; set; }
        public DateTime? RejectedDate { get; set; }

        [StringLength(500)]
        public string? Reason { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
        public bool IsDeleted { get; set; } = false;

        // Navigation properties
        [ForeignKey("TaskId")]
        public virtual WorkTask Task { get; set; } = null!;

        [ForeignKey("UserId")]
        public virtual User User { get; set; } = null!;
    }
} 