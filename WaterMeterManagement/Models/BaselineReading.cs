using System;
using System.ComponentModel.DataAnnotations;

namespace WaterMeterManagement.Models
{
    public class BaselineReading : BaseEntity
    {
        [Required]
        public int MeterId { get; set; }
        
        [Required]
        public decimal ReadValue { get; set; }
        
        [Required]
        public DateTime ReadDate { get; set; }
        
        [StringLength(20)]
        public string BaselineSource { get; set; } = "CSV"; // CSV, Import
        
        [StringLength(500)]
        public string SourceFile { get; set; } = string.Empty;
        
        public int? ImportBatchId { get; set; }
        
        // Import validation
        public bool IsValidated { get; set; } = false;
        
        [StringLength(500)]
        public string ValidationErrors { get; set; } = string.Empty;
        
        // Navigation properties
        public virtual WaterMeter Meter { get; set; }
    }
} 