using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace WaterMeterManagement.Models
{
    public class ReadingPhoto
    {
        [Key]
        public int Id { get; set; }

        [Required]
        public int ReadingId { get; set; }  // 统一命名：与移动端 reading_id 对应

        [Required]
        [StringLength(50)]
        public string Uuid { get; set; } = string.Empty;  // 添加：移动端唯一标识符

        [Required]
        [StringLength(500)]
        public string FileName { get; set; } = string.Empty;

        [Required]
        [StringLength(500)]
        public string OriginalFileName { get; set; } = string.Empty;

        [StringLength(1000)]
        public string? FilePath { get; set; }

        [Required]
        [StringLength(1000)]
        public string CloudflareUrl { get; set; } = string.Empty;

        [StringLength(1000)]
        public string? ThumbnailUrl { get; set; }

        public long FileSizeBytes { get; set; }  // 统一命名：与移动端 file_size 对应

        [Required]
        [StringLength(50)]
        public string MimeType { get; set; } = string.Empty;  // 统一命名：与移动端 mime_type 对应

        // 添加：拍摄信息字段（移动端需要）
        [Required]
        public DateTime CapturedAt { get; set; } = DateTime.UtcNow;  // 对应移动端 captured_at

        [Column(TypeName = "decimal(10,8)")]
        public decimal? Latitude { get; set; }  // 对应移动端 latitude

        [Column(TypeName = "decimal(11,8)")]
        public decimal? Longitude { get; set; }  // 对应移动端 longitude

        [Required]
        [StringLength(20)]
        public string PhotoType { get; set; } = "meter";  // 对应移动端 photo_type

        // 添加：同步状态字段（移动端需要）
        [Required]
        [StringLength(20)]
        public string SyncStatus { get; set; } = "pending";  // 对应移动端 sync_status

        [StringLength(500)]
        public string? SyncError { get; set; }  // 对应移动端 sync_error

        [Column(TypeName = "decimal(3,2)")]
        public decimal? QualityScore { get; set; }

        [StringLength(20)]
        public string? QualityStatus { get; set; } // Good, Fair, Poor, Blurred, DarkImage

        public bool IsProcessed { get; set; } = false;

        public bool HasOCR { get; set; } = false;

        [StringLength(50)]
        public string? OcrResult { get; set; }

        [Column(TypeName = "decimal(5,2)")]
        public decimal? OcrConfidence { get; set; }

        public DateTime UploadTime { get; set; } = DateTime.UtcNow;

        [StringLength(20)]
        public string? OCRStatus { get; set; } // Success, Failed, LowConfidence

        public bool IsOverridden { get; set; } = false;

        [StringLength(50)]
        public string? OverriddenBy { get; set; }

        public DateTime? OverriddenDate { get; set; }

        [StringLength(500)]
        public string? OverrideReason { get; set; }

        [StringLength(500)]
        public string? ProcessingNotes { get; set; }

        // Audit fields
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
        
        [StringLength(50)]
        public string CreatedBy { get; set; } = string.Empty;
        
        [StringLength(50)]
        public string UpdatedBy { get; set; } = string.Empty;
        
        public bool IsDeleted { get; set; } = false;

        // Navigation properties
        [ForeignKey("ReadingId")]
        public virtual MeterReading MeterReading { get; set; } = null!;
        public virtual ICollection<OCRRecord> OCRRecords { get; set; } = new List<OCRRecord>();
    }
} 