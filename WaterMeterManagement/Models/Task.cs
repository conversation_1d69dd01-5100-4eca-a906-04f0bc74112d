using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace WaterMeterManagement.Models
{
    /// <summary>
    /// Work Task Model - Task (Execution Level)
    /// Represents specific execution tasks, typically generated from Work Packages
    /// Upgraded version with Work Package support
    /// </summary>
    [Table("Tasks")]
    public class WorkTask
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(200)]
        public string Name { get; set; } = string.Empty;

        [StringLength(1000)]
        public string? Description { get; set; }

        [Required]
        [StringLength(50)]
        public string Status { get; set; } = "Pending"; // Pending, InProgress, Completed, Cancelled, Failed, Skipped

        [Required]
        [StringLength(50)]
        public string Priority { get; set; } = "Medium"; // Low, Medium, High, Critical

        [Required]
        [StringLength(50)]
        public string Type { get; set; } = "MeterReading"; // MeterReading, Inspection, Maintenance, Installation

        /// <summary>
        /// Task category - distinguishes between scheduled and reactive tasks
        /// </summary>
        [Required]
        [StringLength(20)]
        public string TaskCategory { get; set; } = "Scheduled"; // Scheduled, Reactive

        /// <summary>
        /// AMS Task Number for reactive tasks
        /// </summary>
        [StringLength(50)]
        public string? AmsTaskNumber { get; set; }

        /// <summary>
        /// Asset ID for direct asset reference
        /// </summary>
        [StringLength(50)]
        public string? AssetId { get; set; }



        [StringLength(100)]
        public string? AssignedTo { get; set; }

        /// <summary>
        /// ID of the assigned user (for referential integrity)
        /// </summary>
        public int? AssignedUserId { get; set; }

        [Required]
        [StringLength(100)]
        public string CreatedBy { get; set; } = string.Empty;

        // ============ Work Package Relations (New) ============
        
        /// <summary>
        /// Associated work package ID
        /// </summary>
        public int? WorkPackageId { get; set; }

        /// <summary>
        /// Associated work package item ID
        /// </summary>
        public int? WorkPackageItemId { get; set; }

        /// <summary>
        /// Associated water meter ID (for meter reading tasks)
        /// </summary>
        public int? MeterId { get; set; }

        // ============ Legacy Fields (Backward Compatibility) ============

        public int? RouteId { get; set; }

        // ============ Time Management ============

        public DateTime? DueDate { get; set; }
        
        /// <summary>
        /// Scheduled execution date (new)
        /// </summary>
        public DateTime? ScheduledDate { get; set; }
        
        public DateTime? StartDate { get; set; }
        public DateTime? CompletedDate { get; set; }

        [Column(TypeName = "decimal(5,2)")]
        public decimal? EstimatedHours { get; set; }

        [Column(TypeName = "decimal(5,2)")]
        public decimal? ActualHours { get; set; }

        [Range(0, 100)]
        public int ProgressPercentage { get; set; } = 0;

        // ============ Location and Address Information (New) ============

        [StringLength(200)]
        public string? Location { get; set; }

        /// <summary>
        /// Service address (new)
        /// </summary>
        [StringLength(200)]
        public string? ServiceAddress { get; set; }

        /// <summary>
        /// GPS coordinates - Latitude (new)
        /// </summary>
        [Column(TypeName = "decimal(10,6)")]
        public decimal? Latitude { get; set; }

        /// <summary>
        /// GPS coordinates - Longitude (new)
        /// </summary>
        [Column(TypeName = "decimal(10,6)")]
        public decimal? Longitude { get; set; }

        // ============ Meter Reading Related Fields (New) ============

        /// <summary>
        /// Last reading for validation
        /// </summary>
        [StringLength(20)]
        public string? LastReading { get; set; }

        /// <summary>
        /// Current reading
        /// </summary>
        [StringLength(20)]
        public string? CurrentReading { get; set; }

        /// <summary>
        /// Reading photo URL
        /// </summary>
        [StringLength(500)]
        public string? ReadingPhotoUrl { get; set; }

        // ============ Other Information ============

        [StringLength(1000)]
        public string? Instructions { get; set; }

        [StringLength(1000)]
        public string? Notes { get; set; }

        /// <summary>
        /// Failure reason (new)
        /// </summary>
        [StringLength(200)]
        public string? FailureReason { get; set; }

        /// <summary>
        /// Skip reason (new)
        /// </summary>
        [StringLength(200)]
        public string? SkipReason { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
        public bool IsDeleted { get; set; } = false;

        // ============ Computed Properties ============

        /// <summary>
        /// Whether the task is completed
        /// </summary>
        public bool IsCompleted => Status == "Completed";

        /// <summary>
        /// Whether the task failed
        /// </summary>
        public bool IsFailed => Status == "Failed";

        /// <summary>
        /// Whether the task was skipped
        /// </summary>
        public bool IsSkipped => Status == "Skipped";

        /// <summary>
        /// Whether the task is overdue
        /// </summary>
        public bool IsOverdue => DueDate.HasValue && !IsCompleted && DateTime.UtcNow > DueDate.Value;

        /// <summary>
        /// Whether the task is from a work package
        /// </summary>
        public bool IsFromWorkPackage => WorkPackageId.HasValue;

        // ============ Navigation Properties ============

        /// <summary>
        /// Associated work package (new)
        /// </summary>
        [ForeignKey("WorkPackageId")]
        public virtual WorkPackage? WorkPackage { get; set; }

        /// <summary>
        /// Associated work package item (new)
        /// </summary>
        [ForeignKey("WorkPackageItemId")]
        public virtual WorkPackageItem? WorkPackageItem { get; set; }

        /// <summary>
        /// Associated water meter (new)
        /// </summary>
        [ForeignKey("MeterId")]
        public virtual WaterMeter? Meter { get; set; }

        /// <summary>
        /// Assigned user navigation property
        /// </summary>
        [ForeignKey("AssignedUserId")]
        public virtual User? AssignedUser { get; set; }

        // ============ Legacy Navigation Properties (Backward Compatibility) ============

        [ForeignKey("RouteId")]
        public virtual Route? Route { get; set; }

        public virtual ICollection<TaskAssignment> TaskAssignments { get; set; } = new List<TaskAssignment>();
        public virtual ICollection<TaskHistory> TaskHistories { get; set; } = new List<TaskHistory>();

        /// <summary>
        /// 读表记录（新增）
        /// </summary>
        public virtual ICollection<MeterReading> Readings { get; set; } = new List<MeterReading>();
    }
} 