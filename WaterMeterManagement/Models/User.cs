using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace WaterMeterManagement.Models
{
    public class User
    {
        public int Id { get; set; }
        
        [Required]
        [StringLength(255)]
        public string Username { get; set; } = string.Empty;
        
        [StringLength(255)]
        public string FullName { get; set; } = string.Empty;
        
        [StringLength(255)]
        [EmailAddress]
        public string Email { get; set; } = string.Empty;
        
        public int PersonId { get; set; }
        
        [StringLength(50)]
        public string FinCoCode { get; set; } = string.Empty;
        
        [StringLength(50)]
        public string MobilePhone { get; set; } = string.Empty;
        
        [StringLength(50)]
        public string ProfitCentreCode { get; set; } = string.Empty;
        
        [StringLength(50)]
        public string EmployeeNo { get; set; } = string.Empty;
        
        public bool IsAuthenticated { get; set; }
        
        public DateTime? LastLogin { get; set; }
        
        public DateTime CreatedDate { get; set; } = DateTime.UtcNow;

        public DateTime UpdatedDate { get; set; } = DateTime.UtcNow;

        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        public bool IsDeleted { get; set; } = false;

        public virtual ICollection<UserRole> UserRoles { get; set; } = new List<UserRole>();
    }
} 