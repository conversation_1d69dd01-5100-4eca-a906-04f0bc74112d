using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace WaterMeterManagement.Models
{
    public class RouteWaypoint : BaseEntity
    {
        [Required]
        public int RouteId { get; set; }

        [Required]
        public int WaterMeterId { get; set; }

        [Required]
        public int SequenceOrder { get; set; }

        [Column(TypeName = "decimal(10,6)")]
        public decimal? Latitude { get; set; }

        [Column(TypeName = "decimal(10,6)")]
        public decimal? Longitude { get; set; }

        [StringLength(200)]
        public string? Address { get; set; }

        public int? EstimatedDuration { get; set; } // in minutes

        [Column(TypeName = "decimal(8,2)")]
        public decimal? DistanceFromPrevious { get; set; } // in kilometers

        public int? TravelTimeFromPrevious { get; set; } // in minutes

        [StringLength(500)]
        public string? Notes { get; set; }

        [StringLength(20)]
        public string? AccessDifficulty { get; set; } // Easy, Medium, Hard

        public bool RequiresSpecialEquipment { get; set; } = false;

        [StringLength(200)]
        public string? SpecialInstructions { get; set; }

        // Navigation properties
        public virtual Route Route { get; set; } = null!;
        public virtual WaterMeter WaterMeter { get; set; } = null!;
    }
} 