using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace WaterMeterManagement.Models
{
    /// <summary>
    /// Work Package Model - Management Level
    /// Contains a collection of water meters for batch management, reuse, and templating
    /// Replaces the original Schedule concept
    /// </summary>
    [Table("WorkPackages")]
    public class WorkPackage : BaseEntity
    {
        /// <summary>
        /// Work package name
        /// </summary>
        [Required]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Work package description
        /// </summary>
        [StringLength(500)]
        public string? Description { get; set; }

        /// <summary>
        /// Work package type
        /// </summary>
        [Required]
        [StringLength(20)]
        public string PackageType { get; set; } = "Scheduled"; // Scheduled, Reactive, Emergency

        /// <summary>
        /// Work package status
        /// </summary>
        [Required]
        [StringLength(20)]
        public string Status { get; set; } = "Draft"; // Draft, Active, InProgress, Completed, Cancelled

        /// <summary>
        /// Planned start date
        /// </summary>
        [Required]
        public DateTime PlannedStartDate { get; set; }

        /// <summary>
        /// Planned end date
        /// </summary>
        [Required]
        public DateTime PlannedEndDate { get; set; }

        /// <summary>
        /// Actual start date
        /// </summary>
        public DateTime? ActualStartDate { get; set; }

        /// <summary>
        /// Actual end date
        /// </summary>
        public DateTime? ActualEndDate { get; set; }

        /// <summary>
        /// Execution frequency
        /// </summary>
        [Required]
        [StringLength(20)]
        public string Frequency { get; set; } = "Monthly"; // Monthly, Quarterly, Biannually, Annually, OneTime

        /// <summary>
        /// Service area
        /// </summary>
        [Required]
        [StringLength(100)]
        public string ServiceArea { get; set; } = string.Empty;

        /// <summary>
        /// Sub area
        /// </summary>
        [StringLength(100)]
        public string? SubArea { get; set; }

        /// <summary>
        /// Priority level
        /// </summary>
        [Required]
        [StringLength(20)]
        public string Priority { get; set; } = "Medium"; // Low, Medium, High, Critical

        /// <summary>
        /// Total number of meters
        /// </summary>
        public int TotalMeters { get; set; } = 0;

        /// <summary>
        /// Number of completed meters
        /// </summary>
        public int CompletedMeters { get; set; } = 0;

        /// <summary>
        /// Progress percentage
        /// </summary>
        [Column(TypeName = "decimal(5,2)")]
        public decimal ProgressPercentage { get; set; } = 0;

        /// <summary>
        /// Assigned team or users
        /// </summary>
        [StringLength(200)]
        public string? AssignedTeam { get; set; }

        /// <summary>
        /// Estimated hours
        /// </summary>
        [Column(TypeName = "decimal(8,2)")]
        public decimal EstimatedHours { get; set; } = 0;

        /// <summary>
        /// Actual hours
        /// </summary>
        [Column(TypeName = "decimal(8,2)")]
        public decimal? ActualHours { get; set; }

        /// <summary>
        /// Estimated cost
        /// </summary>
        [Column(TypeName = "decimal(10,2)")]
        public decimal? EstimatedCost { get; set; }

        /// <summary>
        /// Actual cost
        /// </summary>
        [Column(TypeName = "decimal(10,2)")]
        public decimal? ActualCost { get; set; }

        /// <summary>
        /// Whether this is a template
        /// </summary>
        public bool IsTemplate { get; set; } = false;

        /// <summary>
        /// Template category
        /// </summary>
        [StringLength(50)]
        public string? TemplateCategory { get; set; }

        /// <summary>
        /// Whether this is a recurring work package
        /// </summary>
        public bool IsRecurring { get; set; } = false;

        /// <summary>
        /// Recurrence pattern
        /// </summary>
        [StringLength(50)]
        public string? RecurrencePattern { get; set; } // Weekly, Monthly, Quarterly, etc.

        /// <summary>
        /// Recurrence interval
        /// </summary>
        public int? RecurrenceInterval { get; set; } // Every X weeks/months

        /// <summary>
        /// Last execution time
        /// </summary>
        public DateTime? LastExecuted { get; set; }

        /// <summary>
        /// Next execution time
        /// </summary>
        public DateTime? NextExecution { get; set; }

        /// <summary>
        /// Work package notes
        /// </summary>
        [StringLength(1000)]
        public string? Notes { get; set; }

        /// <summary>
        /// Special instructions
        /// </summary>
        [StringLength(1000)]
        public string? Instructions { get; set; }

        /// <summary>
        /// AMS import batch identifier
        /// </summary>
        [StringLength(100)]
        public string? AmsImportBatch { get; set; }

        /// <summary>
        /// AMS import date
        /// </summary>
        public DateTime? AmsImportDate { get; set; }

        /// <summary>
        /// Data source
        /// </summary>
        [StringLength(20)]
        public string Source { get; set; } = "Manual"; // Manual, AMS, CSV, Template

        /// <summary>
        /// Creator of the work package
        /// </summary>
        [Required]
        [StringLength(100)]
        public new string CreatedBy { get; set; } = string.Empty;

        /// <summary>
        /// Last updater
        /// </summary>
        [StringLength(100)]
        public new string? UpdatedBy { get; set; }

        // ============ Computed Properties ============

        /// <summary>
        /// Whether the work package has started
        /// </summary>
        public bool IsStarted => ActualStartDate.HasValue;

        /// <summary>
        /// Whether the work package is completed
        /// </summary>
        public bool IsCompleted => Status == "Completed";

        /// <summary>
        /// Whether the work package is overdue
        /// </summary>
        public bool IsOverdue => !IsCompleted && DateTime.UtcNow > PlannedEndDate;

        /// <summary>
        /// Remaining days until planned end date
        /// </summary>
        public int? RemainingDays => IsCompleted ? (int?)0 : (int?)(PlannedEndDate - DateTime.UtcNow).Days;

        /// <summary>
        /// Completion rate percentage
        /// </summary>
        public decimal CompletionRate => TotalMeters > 0 ? (decimal)CompletedMeters / TotalMeters * 100 : 0;

        // ============ Navigation Properties ============

        /// <summary>
        /// Work package items (contained meters)
        /// </summary>
        public virtual ICollection<WorkPackageItem> Items { get; set; } = new List<WorkPackageItem>();

        /// <summary>
        /// Work package assignments (assigned users)
        /// </summary>
        public virtual ICollection<WorkPackageAssignment> Assignments { get; set; } = new List<WorkPackageAssignment>();

        /// <summary>
        /// Generated tasks
        /// </summary>
        public virtual ICollection<WorkTask> Tasks { get; set; } = new List<WorkTask>();

        /// <summary>
        /// Work package history records
        /// </summary>
        public virtual ICollection<WorkPackageHistory> Histories { get; set; } = new List<WorkPackageHistory>();
    }
} 