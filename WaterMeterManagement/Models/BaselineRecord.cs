using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace WaterMeterManagement.Models
{
    public class BaselineRecord : BaseEntity
    {
        [Required]
        public int MeterId { get; set; }

        [Required]
        public DateTime BaselineDate { get; set; }

        [Required]
        [Column(TypeName = "decimal(12,4)")]
        public decimal BaselineValue { get; set; }

        [Required]
        [StringLength(20)]
        public string BaselineType { get; set; } = string.Empty; // Initial, Periodic, Correction, Migration

        [Required]
        [StringLength(20)]
        public string Status { get; set; } = "Active"; // Active, Superseded, Invalid

        [StringLength(50)]
        public string? ImportBatch { get; set; }

        [StringLength(100)]
        public string? SourceFile { get; set; }

        public int? SourceRowNumber { get; set; }

        [StringLength(20)]
        public string DataSource { get; set; } = "Manual"; // CSV, Excel, Manual, AMIS

        [StringLength(500)]
        public string? ValidationNotes { get; set; }

        public bool IsValidated { get; set; } = false;

        public DateTime? ValidatedDate { get; set; }

        [StringLength(50)]
        public string? ValidatedBy { get; set; }

        // Quality and error tracking
        public bool HasValidationErrors { get; set; } = false;

        [StringLength(1000)]
        public string? ValidationErrors { get; set; }

        public bool IsAnomalous { get; set; } = false;

        [StringLength(500)]
        public string? AnomalyDescription { get; set; }

        // Previous baseline reference
        public int? PreviousBaselineId { get; set; }

        [Column(TypeName = "decimal(12,4)")]
        public decimal? PreviousBaselineValue { get; set; }

        [Column(TypeName = "decimal(12,4)")]
        public decimal? VarianceFromPrevious { get; set; }

        [Column(TypeName = "decimal(8,2)")]
        public decimal? VariancePercentage { get; set; }

        // Correction information
        public bool IsCorrected { get; set; } = false;

        public DateTime? CorrectedDate { get; set; }

        [StringLength(50)]
        public string? CorrectedBy { get; set; }

        [StringLength(500)]
        public string? CorrectionReason { get; set; }

        // AMS Integration Fields
        [StringLength(50)]
        public string? Source { get; set; }

        [StringLength(20)]
        public string? ValidationStatus { get; set; }

        public bool IsActive { get; set; } = true;

        [Range(0, 100)]
        public int ConfidenceLevel { get; set; } = 100;

        [StringLength(1000)]
        public string? Notes { get; set; }

        // Navigation properties
        public virtual WaterMeter WaterMeter { get; set; } = null!;
        public virtual BaselineRecord? PreviousBaseline { get; set; }
        public virtual ICollection<BaselineRecord> SubsequentBaselines { get; set; } = new List<BaselineRecord>();
    }
} 