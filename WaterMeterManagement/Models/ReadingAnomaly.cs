using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace WaterMeterManagement.Models
{
    public class ReadingAnomaly
    {
        [Key]
        public int Id { get; set; }

        [Required]
        public int MeterReadingId { get; set; }

        [Required]
        [StringLength(100)]
        public string AnomalyType { get; set; } = string.Empty; // NegativeUsage, ExcessiveUsage, ZeroUsage, SuddenSpike, LongTermZero

        [Required]
        [StringLength(20)]
        public string Severity { get; set; } = "Medium"; // Low, Medium, High, Critical

        [StringLength(1000)]
        public string? Description { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal? ExpectedValue { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal? ActualValue { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal? Variance { get; set; }

        [Column(TypeName = "decimal(5,2)")]
        public decimal? ConfidenceScore { get; set; }

        [Required]
        [StringLength(20)]
        public string Status { get; set; } = "Open"; // Open, InProgress, Resolved, Dismissed

        [StringLength(50)]
        public string? AssignedTo { get; set; }

        /// <summary>
        /// ID of the assigned user (for referential integrity)
        /// </summary>
        public int? AssignedUserId { get; set; }

        public DateTime? AssignedDate { get; set; }

        [StringLength(50)]
        public string? ResolvedBy { get; set; }

        public DateTime? ResolvedDate { get; set; }

        [StringLength(1000)]
        public string? Resolution { get; set; }

        [StringLength(100)]
        public string? ResolutionType { get; set; } // DataCorrection, MeterReplacement, CustomerNotification, False Positive

        [StringLength(1000)]
        public string? Notes { get; set; }

        public bool RequiresFieldVisit { get; set; } = false;

        public bool IsRecurring { get; set; } = false;

        public int? RelatedAnomalyId { get; set; }

        // Audit fields
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
        
        [StringLength(50)]
        public string CreatedBy { get; set; } = string.Empty;
        
        [StringLength(50)]
        public string UpdatedBy { get; set; } = string.Empty;
        
        public bool IsDeleted { get; set; } = false;

        // Navigation properties
        public virtual MeterReading MeterReading { get; set; } = null!;
        public virtual ReadingAnomaly? RelatedAnomaly { get; set; }
        public virtual ICollection<ReadingAnomaly> ChildAnomalies { get; set; } = new List<ReadingAnomaly>();
    }
} 