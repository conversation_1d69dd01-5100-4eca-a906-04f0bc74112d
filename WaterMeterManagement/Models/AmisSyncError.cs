using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace WaterMeterManagement.Models
{
    public class AmisSyncError : BaseEntity
    {
        [Required]
        public int AmisSyncId { get; set; }

        [Required]
        [StringLength(50)]
        public string ErrorType { get; set; } = string.Empty; // Connection, Authentication, Validation, Processing, Timeout

        [Required]
        [StringLength(20)]
        public string Severity { get; set; } = "Error"; // Info, Warning, Error, Critical

        [Required]
        [StringLength(500)]
        public string ErrorMessage { get; set; } = string.Empty;

        [Column(TypeName = "text")]
        public string? DetailedError { get; set; }

        [StringLength(1000)]
        public string? StackTrace { get; set; }

        // Record specific information
        [StringLength(100)]
        public string? RecordIdentifier { get; set; } // Meter ID, Record ID, etc.

        public int? RecordLineNumber { get; set; }

        [Column(TypeName = "text")]
        public string? RecordData { get; set; } // JSON of the problematic record

        // Error context
        [StringLength(100)]
        public string? Operation { get; set; } // GetMeters, PostReadings, etc.

        [StringLength(200)]
        public string? Endpoint { get; set; }

        [StringLength(10)]
        public string? HttpMethod { get; set; }

        public int? HttpStatusCode { get; set; }

        // Resolution tracking
        [StringLength(20)]
        public string ResolutionStatus { get; set; } = "Unresolved"; // Unresolved, Resolved, Ignored

        public DateTime? ResolvedDate { get; set; }

        [StringLength(50)]
        public string? ResolvedBy { get; set; }

        [StringLength(500)]
        public string? ResolutionNotes { get; set; }

        // Retry information
        public bool CanRetry { get; set; } = true;
        public int RetryCount { get; set; } = 0;
        public DateTime? LastRetryDate { get; set; }

        // Navigation properties
        public virtual AmisSync AmisSync { get; set; } = null!;
    }
} 