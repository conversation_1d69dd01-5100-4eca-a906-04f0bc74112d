using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace WaterMeterManagement.Models
{
    /// <summary>
    /// Configuration entity for storing system and user settings
    /// Supports single values, arrays, and complex objects
    /// </summary>
    [Table("Configurations")]
    public class Configuration : BaseEntity
    {
        // Id is inherited from BaseEntity

        /// <summary>
        /// Configuration scope: 'System', 'User', 'Role', 'Department', etc.
        /// </summary>
        [Required]
        [StringLength(50)]
        public string Scope { get; set; } = string.Empty;

        /// <summary>
        /// Configuration category: 'General', 'Security', 'Notifications', 'Appearance', etc.
        /// </summary>
        [Required]
        [StringLength(100)]
        public string Category { get; set; } = string.Empty;

        /// <summary>
        /// Configuration key/name
        /// </summary>
        [Required]
        [StringLength(200)]
        public string Key { get; set; } = string.Empty;

        /// <summary>
        /// Human-readable display name
        /// </summary>
        [StringLength(200)]
        public string DisplayName { get; set; } = string.Empty;

        /// <summary>
        /// Configuration description
        /// </summary>
        [StringLength(500)]
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// Data type: 'string', 'number', 'boolean', 'array', 'object', 'time', 'date'
        /// </summary>
        [Required]
        [StringLength(20)]
        public string DataType { get; set; } = string.Empty;

        /// <summary>
        /// JSON serialized configuration value
        /// Supports single values, arrays, and complex objects
        /// </summary>
        public string Value { get; set; } = string.Empty;

        /// <summary>
        /// Default value (JSON serialized)
        /// </summary>
        public string DefaultValue { get; set; } = string.Empty;

        /// <summary>
        /// User ID for user-specific settings (null for system settings)
        /// </summary>
        public int? UserId { get; set; }

        /// <summary>
        /// Role ID for role-specific settings (null for system/user settings)
        /// </summary>
        public int? RoleId { get; set; }

        /// <summary>
        /// Whether this configuration is system-defined (cannot be deleted)
        /// </summary>
        public bool IsSystemDefined { get; set; } = false;

        /// <summary>
        /// Whether this configuration is encrypted
        /// </summary>
        public bool IsEncrypted { get; set; } = false;

        /// <summary>
        /// Validation rules (JSON format)
        /// e.g., {"min": 1, "max": 100, "required": true, "options": ["option1", "option2"]}
        /// </summary>
        public string ValidationRules { get; set; } = string.Empty;

        /// <summary>
        /// Configuration priority (higher number = higher priority)
        /// Used for inheritance: System < Role < User
        /// </summary>
        public int Priority { get; set; } = 0;

        /// <summary>
        /// Whether this configuration is active
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// Navigation property to User
        /// </summary>
        [ForeignKey("UserId")]
        public virtual User? User { get; set; }

        /// <summary>
        /// Navigation property to Role
        /// </summary>
        [ForeignKey("RoleId")]
        public virtual Role? Role { get; set; }
    }
} 