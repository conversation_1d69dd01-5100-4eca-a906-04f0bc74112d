using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace WaterMeterManagement.Models
{
    /// <summary>
    /// Route Model - Based on "Data Check" sheet in AMS Excel file
    /// Manages meter routes for organized reading schedules
    /// </summary>
    public class Route : BaseEntity
    {
        /// <summary>
        /// Route name/identifier (e.g., "Route18", "Route 10A", "Route17")
        /// Maps to Excel sheet: Data Check tab names
        /// </summary>
        [Required]
        [StringLength(50)]
        public string RouteName { get; set; } = string.Empty;

        /// <summary>
        /// Route description
        /// </summary>
        [StringLength(200)]
        public string? Description { get; set; }

        /// <summary>
        /// Township or area this route covers
        /// </summary>
        [Required]
        [StringLength(100)]
        public string Township { get; set; } = string.Empty;

        /// <summary>
        /// Sub area within township
        /// </summary>
        [StringLength(100)]
        public string? SubArea { get; set; }

        /// <summary>
        /// Route status
        /// </summary>
        [Required]
        [StringLength(20)]
        public string Status { get; set; } = "Active"; // Active, Inactive, Planned

        /// <summary>
        /// Number of meters in this route
        /// </summary>
        public int MeterCount { get; set; } = 0;

        /// <summary>
        /// Route type
        /// </summary>
        [Required]
        [StringLength(20)]
        public string RouteType { get; set; } = "Scheduled"; // Scheduled, Reactive, Mixed

        /// <summary>
        /// Reading frequency for this route
        /// </summary>
        [StringLength(20)]
        public string? ReadingFrequency { get; set; } // Monthly, Quarterly, Biannual, Annual

        /// <summary>
        /// Default assigned user ID for this route
        /// </summary>
        public int? DefaultUserId { get; set; }

        /// <summary>
        /// Estimated time to complete route (in hours)
        /// </summary>
        public decimal? EstimatedHours { get; set; }

        // ============ Legacy Fields (for backward compatibility) ============
        
        /// <summary>
        /// Legacy Name field - maps to RouteName
        /// </summary>
        public string Name 
        { 
            get => RouteName; 
            set => RouteName = value; 
        }

        /// <summary>
        /// Legacy Zone field - maps to Township
        /// </summary>
        public string? Zone 
        { 
            get => Township; 
            set => Township = value ?? string.Empty; 
        }

        /// <summary>
        /// Legacy Area field - maps to SubArea
        /// </summary>
        public string? Area 
        { 
            get => SubArea; 
            set => SubArea = value; 
        }

        public int? EstimatedDuration { get; set; } // in minutes

        [Column(TypeName = "decimal(8,2)")]
        public decimal? EstimatedDistance { get; set; } // in kilometers

        [Column(TypeName = "decimal(10,6)")]
        public decimal? StartLatitude { get; set; }

        [Column(TypeName = "decimal(10,6)")]
        public decimal? StartLongitude { get; set; }

        [Column(TypeName = "decimal(10,6)")]
        public decimal? EndLatitude { get; set; }

        [Column(TypeName = "decimal(10,6)")]
        public decimal? EndLongitude { get; set; }

        [StringLength(200)]
        public string? StartAddress { get; set; }

        [StringLength(200)]
        public string? EndAddress { get; set; }

        [StringLength(100)]
        public string? AssignedTo { get; set; }

        /// <summary>
        /// ID of the assigned user (for referential integrity)
        /// </summary>
        public int? AssignedUserId { get; set; }

        [StringLength(100)]
        public string? BackupAssignee { get; set; }

        [StringLength(20)]
        public string? OptimizationMethod { get; set; } // Manual, TimeOptimal, DistanceOptimal, Traffic

        public DateTime? LastOptimized { get; set; }

        [StringLength(100)]
        public string? OptimizedBy { get; set; }

        [Column(TypeName = "text")]
        public string? RouteGeometry { get; set; } // GeoJSON or similar

        /// <summary>
        /// Route notes or special instructions
        /// </summary>
        [StringLength(1000)]
        public string? Notes { get; set; }

        public bool IsTemplate { get; set; } = false;

        [StringLength(50)]
        public string? TemplateCategory { get; set; }

        public int? AverageCompletionTime { get; set; } // in minutes

        [Column(TypeName = "decimal(3,2)")]
        public decimal? DifficultyRating { get; set; } // 1.0 to 5.0

        /// <summary>
        /// Last sync date with AMS
        /// </summary>
        public DateTime? LastSyncDate { get; set; }

        /// <summary>
        /// Data source
        /// </summary>
        [StringLength(20)]
        public string Source { get; set; } = "Manual"; // AMS, Manual, Import

        // ============ Computed Properties ============

        /// <summary>
        /// Whether route has assigned meters
        /// </summary>
        public bool HasMeters => MeterCount > 0;

        /// <summary>
        /// Estimated days to complete based on hours (assuming 8-hour workday)
        /// </summary>
        public decimal? EstimatedDays => EstimatedHours.HasValue ? Math.Ceiling(EstimatedHours.Value / 8) : null;

        /// <summary>
        /// Legacy TotalMeters field - maps to MeterCount
        /// </summary>
        public int? TotalMeters 
        { 
            get => MeterCount; 
            set => MeterCount = value ?? 0; 
        }

        // Navigation properties
        public virtual ICollection<RouteWaypoint> RouteWaypoints { get; set; } = new List<RouteWaypoint>();
        
        /// <summary>
        /// Meters assigned to this route
        /// </summary>
        public virtual ICollection<WaterMeter> Meters { get; set; } = new List<WaterMeter>();

        /// <summary>
        /// Task assignments for this route
        /// </summary>
        public virtual ICollection<TaskAssignment> TaskAssignments { get; set; } = new List<TaskAssignment>();

        /// <summary>
        /// Default assigned user
        /// </summary>
        public virtual User? DefaultUser { get; set; }
    }
} 