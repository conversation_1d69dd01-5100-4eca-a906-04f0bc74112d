using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace WaterMeterManagement.Models
{
    /// <summary>
    /// Work Package Assignment Model
    /// Manages the relationship and status of work packages assigned to users
    /// </summary>
    [Table("WorkPackageAssignments")]
    public class WorkPackageAssignment : BaseEntity
    {
        /// <summary>
        /// Work package ID
        /// </summary>
        [Required]
        public int WorkPackageId { get; set; }

        /// <summary>
        /// Assigned user ID
        /// </summary>
        [Required]
        public int UserId { get; set; }

        /// <summary>
        /// Assignment type
        /// </summary>
        [Required]
        [StringLength(20)]
        public string AssignmentType { get; set; } = "Primary"; // Primary, Backup, Shared, Observer

        /// <summary>
        /// Assignment status
        /// </summary>
        [Required]
        [StringLength(20)]
        public string Status { get; set; } = "Assigned"; // Assigned, Accepted, Rejected, InProgress, Completed, Cancelled

        /// <summary>
        /// Assignment date
        /// </summary>
        [Required]
        public DateTime AssignedDate { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// User who made the assignment
        /// </summary>
        [Required]
        [StringLength(100)]
        public string AssignedBy { get; set; } = string.Empty;

        /// <summary>
        /// Date when assignment was accepted
        /// </summary>
        public DateTime? AcceptedDate { get; set; }

        /// <summary>
        /// Date when assignment was rejected
        /// </summary>
        public DateTime? RejectedDate { get; set; }

        /// <summary>
        /// Date when work started
        /// </summary>
        public DateTime? StartedDate { get; set; }

        /// <summary>
        /// Date when work was completed
        /// </summary>
        public DateTime? CompletedDate { get; set; }

        /// <summary>
        /// Assignment reason or notes
        /// </summary>
        [StringLength(500)]
        public string? Reason { get; set; }

        /// <summary>
        /// Rejection reason
        /// </summary>
        [StringLength(500)]
        public string? RejectionReason { get; set; }

        /// <summary>
        /// Expected start date
        /// </summary>
        public DateTime? ExpectedStartDate { get; set; }

        /// <summary>
        /// Expected completion date
        /// </summary>
        public DateTime? ExpectedCompletionDate { get; set; }

        /// <summary>
        /// Number of assigned meters (if partial assignment)
        /// </summary>
        public int? AssignedMeterCount { get; set; }

        /// <summary>
        /// Number of completed meters
        /// </summary>
        public int CompletedMeterCount { get; set; } = 0;

        /// <summary>
        /// Workload weight (0-100)
        /// </summary>
        [Range(0, 100)]
        public int WorkloadWeight { get; set; } = 100;

        /// <summary>
        /// Priority (can override work package priority)
        /// </summary>
        [StringLength(20)]
        public string? Priority { get; set; } // Low, Medium, High, Critical

        /// <summary>
        /// Whether supervision is required
        /// </summary>
        public bool RequiresSupervision { get; set; } = false;

        /// <summary>
        /// Supervisor user ID
        /// </summary>
        public int? SupervisorId { get; set; }

        /// <summary>
        /// Required skills
        /// </summary>
        [StringLength(200)]
        public string? SkillRequirements { get; set; }

        /// <summary>
        /// Required equipment
        /// </summary>
        [StringLength(200)]
        public string? EquipmentRequirements { get; set; }

        /// <summary>
        /// Special instructions
        /// </summary>
        [StringLength(1000)]
        public string? SpecialInstructions { get; set; }

        /// <summary>
        /// Email notification sent flag
        /// </summary>
        public bool EmailNotificationSent { get; set; } = false;

        /// <summary>
        /// Email sent date
        /// </summary>
        public DateTime? EmailSentDate { get; set; }

        /// <summary>
        /// Push notification sent flag
        /// </summary>
        public bool PushNotificationSent { get; set; } = false;

        /// <summary>
        /// Push notification sent date
        /// </summary>
        public DateTime? PushNotificationSentDate { get; set; }

        /// <summary>
        /// Last activity date
        /// </summary>
        public DateTime? LastActivityDate { get; set; }

        /// <summary>
        /// Rating (1-5 stars)
        /// </summary>
        [Range(1, 5)]
        public int? Rating { get; set; }

        /// <summary>
        /// Feedback comments
        /// </summary>
        [StringLength(1000)]
        public string? Feedback { get; set; }

        /// <summary>
        /// User who provided the rating
        /// </summary>
        [StringLength(100)]
        public string? RatedBy { get; set; }

        /// <summary>
        /// Rating date
        /// </summary>
        public DateTime? RatedDate { get; set; }

        // ============ Computed Properties ============

        /// <summary>
        /// Whether the assignment has been accepted
        /// </summary>
        public bool IsAccepted => Status == "Accepted" || Status == "InProgress" || Status == "Completed";

        /// <summary>
        /// Whether the assignment is completed
        /// </summary>
        public bool IsCompleted => Status == "Completed";

        /// <summary>
        /// Whether the assignment was rejected
        /// </summary>
        public bool IsRejected => Status == "Rejected";

        /// <summary>
        /// Whether the assignment is in progress
        /// </summary>
        public bool IsInProgress => Status == "InProgress";

        /// <summary>
        /// Completion rate percentage
        /// </summary>
        public decimal CompletionRate => 
            AssignedMeterCount.HasValue && AssignedMeterCount > 0
                ? (decimal)CompletedMeterCount / AssignedMeterCount.Value * 100
                : 0;

        /// <summary>
        /// Whether the assignment is overdue
        /// </summary>
        public bool IsOverdue => 
            ExpectedCompletionDate.HasValue && !IsCompleted && DateTime.UtcNow > ExpectedCompletionDate.Value;

        // ============ Navigation Properties ============

        /// <summary>
        /// Associated work package
        /// </summary>
        [ForeignKey("WorkPackageId")]
        public virtual WorkPackage WorkPackage { get; set; } = null!;

        /// <summary>
        /// Assigned user
        /// </summary>
        [ForeignKey("UserId")]
        public virtual User User { get; set; } = null!;

        /// <summary>
        /// Supervisor user
        /// </summary>
        [ForeignKey("SupervisorId")]
        public virtual User? Supervisor { get; set; }
    }
} 