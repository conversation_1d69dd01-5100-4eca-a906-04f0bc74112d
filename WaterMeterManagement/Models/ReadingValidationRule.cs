using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace WaterMeterManagement.Models
{
    public class ReadingValidationRule
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(100)]
        public string RuleName { get; set; } = string.Empty;

        [Required]
        [StringLength(50)]
        public string RuleType { get; set; } = string.Empty; // Tolerance, ZeroUsage, NegativeUsage, ExcessiveUsage, Trend

        [StringLength(500)]
        public string? Description { get; set; }

        [StringLength(50)]
        public string? MeterType { get; set; } // Specific meter type or "All"

        [StringLength(50)]
        public string? Zone { get; set; } // Specific zone or "All"

        [StringLength(50)]
        public string? CustomerType { get; set; } // Residential, Commercial, Industrial, or "All"

        public bool IsActive { get; set; } = true;

        public int Priority { get; set; } = 5; // 1-10, higher number = higher priority

        // Tolerance settings
        [Column(TypeName = "decimal(10,2)")]
        public decimal? MinToleranceValue { get; set; }

        [Column(TypeName = "decimal(10,2)")]
        public decimal? MaxToleranceValue { get; set; }

        [Column(TypeName = "decimal(5,2)")]
        public decimal? TolerancePercentage { get; set; }

        // Time-based settings
        public int? TimeWindowDays { get; set; }

        public bool SeasonalAdjustment { get; set; } = false;

        [Column(TypeName = "decimal(5,2)")]
        public decimal? SummerMultiplier { get; set; }

        [Column(TypeName = "decimal(5,2)")]
        public decimal? WinterMultiplier { get; set; }

        // Threshold settings
        [Column(TypeName = "decimal(10,2)")]
        public decimal? WarningThreshold { get; set; }

        [Column(TypeName = "decimal(10,2)")]
        public decimal? ErrorThreshold { get; set; }

        [Column(TypeName = "decimal(10,2)")]
        public decimal? CriticalThreshold { get; set; }

        // Action settings
        [StringLength(50)]
        public string? ActionOnViolation { get; set; } = "Flag"; // Flag, Reject, RequireApproval

        public bool AutoCorrect { get; set; } = false;

        [StringLength(100)]
        public string? NotificationRecipients { get; set; }

        // Rule configuration (JSON format for complex rules)
        [Column(TypeName = "text")]
        public string? RuleConfiguration { get; set; }

        // Effectiveness tracking
        public int TotalApplications { get; set; } = 0;

        public int TruePositives { get; set; } = 0;

        public int FalsePositives { get; set; } = 0;

        [Column(TypeName = "decimal(5,2)")]
        public decimal? EffectivenessScore { get; set; }

        public DateTime? LastReviewDate { get; set; }

        [StringLength(50)]
        public string? ReviewedBy { get; set; }

        // Audit fields
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
        
        [StringLength(50)]
        public string CreatedBy { get; set; } = string.Empty;
        
        [StringLength(50)]
        public string UpdatedBy { get; set; } = string.Empty;
        
        public bool IsDeleted { get; set; } = false;
    }
} 