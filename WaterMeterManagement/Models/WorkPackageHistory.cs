using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace WaterMeterManagement.Models
{
    /// <summary>
    /// Work Package History Model
    /// Records all changes to work packages for audit and tracking purposes
    /// </summary>
    [Table("WorkPackageHistories")]
    public class WorkPackageHistory : BaseEntity
    {
        /// <summary>
        /// Work package ID
        /// </summary>
        [Required]
        public int WorkPackageId { get; set; }

        /// <summary>
        /// Action type
        /// </summary>
        [Required]
        [StringLength(50)]
        public string Action { get; set; } = string.Empty; // Created, Updated, Assigned, Started, Completed, Cancelled, etc.

        /// <summary>
        /// Action description
        /// </summary>
        [StringLength(200)]
        public string? Description { get; set; }

        /// <summary>
        /// Changed field name
        /// </summary>
        [StringLength(100)]
        public string? FieldName { get; set; }

        /// <summary>
        /// Old value
        /// </summary>
        [StringLength(500)]
        public string? OldValue { get; set; }

        /// <summary>
        /// New value
        /// </summary>
        [StringLength(500)]
        public string? NewValue { get; set; }

        /// <summary>
        /// User who made the change
        /// </summary>
        [Required]
        [StringLength(100)]
        public string ChangedBy { get; set; } = string.Empty;

        /// <summary>
        /// When the change occurred
        /// </summary>
        [Required]
        public DateTime ChangedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// Source of the change
        /// </summary>
        [StringLength(50)]
        public string? Source { get; set; } // Web, Mobile, API, System

        /// <summary>
        /// IP address
        /// </summary>
        [StringLength(45)]
        public string? IpAddress { get; set; }

        /// <summary>
        /// User agent
        /// </summary>
        [StringLength(500)]
        public string? UserAgent { get; set; }

        /// <summary>
        /// Additional data in JSON format
        /// </summary>
        [Column(TypeName = "text")]
        public string? AdditionalData { get; set; }

        /// <summary>
        /// Additional notes
        /// </summary>
        [StringLength(1000)]
        public string? Notes { get; set; }

        // ============ Navigation Properties ============

        /// <summary>
        /// Associated work package
        /// </summary>
        [ForeignKey("WorkPackageId")]
        public virtual WorkPackage WorkPackage { get; set; } = null!;
    }
} 