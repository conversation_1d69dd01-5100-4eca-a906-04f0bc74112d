# Cloudflare R2 集成规划文档

## 📋 项目概述

本文档详细规划了将 Cloudflare R2 对象存储集成到水表读数管理系统中，用于存储和管理照片文件。

**当前状态**：
- ✅ R2 服务类已实现 (`CloudflareR2Service.cs`)
- ✅ 数据库模型已完善 (`ReadingPhoto.cs`)
- ✅ 照片上传流程已建立 (`PhotoService.cs`)
- ⚠️ **需要配置真实的 R2 凭据**

## 🎯 集成目标

- ✅ 将移动端上传的照片存储到 Cloudflare R2
- ✅ 提供安全的照片访问和下载功能
- ✅ 实现照片的生命周期管理
- ✅ 降低存储成本（R2 无出站费用）

## 🔧 技术架构

### 当前实现的数据流程
1. **移动端**：拍摄照片 → Base64编码 → 发送到后端API
2. **后端**：接收照片 → 保存到数据库 → **调用R2Service上传** → 更新CloudflareUrl
3. **访问**：生成预签名URL → 安全访问照片

### 核心组件（已实现）
- `ICloudflareR2Service` - R2服务接口
- `CloudflareR2Service` - R2服务实现
- `PhotoService` - 照片处理服务
- `ReadingPhoto` - 照片数据模型

## 🔑 Cloudflare R2 配置

### 1. 账户设置

#### 获取账户信息
- 登录 [Cloudflare Dashboard](https://dash.cloudflare.com/)
- 右侧边栏查看 **Account ID**
- 记录账户ID：`<ACCOUNT_ID>`

#### 创建 R2 存储桶
```bash
# 使用 Wrangler CLI
npx wrangler r2 bucket create water-meter-photos

# 或通过 Dashboard 创建
# 1. 进入 R2 Object Storage
# 2. 点击 "Create bucket"
# 3. 输入名称：water-meter-photos
# 4. 选择位置：Auto (推荐)
```

### 2. API 令牌配置

#### 创建 API 令牌步骤
1. **进入令牌管理**
   - Dashboard → 右上角头像 → My Profile
   - API Tokens → Create Token

2. **选择令牌类型**
   - 选择 "Custom token"
   - 或使用 "R2 Token" 模板

3. **配置权限**
   ```json
   {
     "permissions": [
       {
         "effect": "allow",
         "resources": {
           "com.cloudflare.edge.r2.bucket.<ACCOUNT_ID>_default_water-meter-photos": "*"
         },
         "permission_groups": [
           {
             "id": "2efd5506f9c8494dacb1fa10a3e7d5b6",
             "name": "Workers R2 Storage Bucket Item Write"
           },
           {
             "id": "6a018a9f2fc74eb6b293b0c548f38b39", 
             "name": "Workers R2 Storage Bucket Item Read"
           }
         ]
       }
     ]
   }
   ```

4. **获取凭据**
   - **Access Key ID**: `<ACCESS_KEY_ID>`
   - **Secret Access Key**: `<SECRET_ACCESS_KEY>` (仅显示一次，务必保存)

### 3. 环境变量配置

#### 后端配置 (appsettings.json)
```json
{
  "CloudflareR2": {
    "AccountId": "<ACCOUNT_ID>",
    "AccessKeyId": "<ACCESS_KEY_ID>",
    "SecretAccessKey": "<SECRET_ACCESS_KEY>",
    "BucketName": "water-meter-photos",
    "Endpoint": "https://<ACCOUNT_ID>.r2.cloudflarestorage.com",
    "Region": "auto"
  }
}
```

#### 开发环境变量
```bash
# .env 文件
R2_ACCOUNT_ID=<ACCOUNT_ID>
R2_ACCESS_KEY_ID=<ACCESS_KEY_ID>
R2_SECRET_ACCESS_KEY=<SECRET_ACCESS_KEY>
R2_BUCKET_NAME=water-meter-photos
R2_ENDPOINT=https://<ACCOUNT_ID>.r2.cloudflarestorage.com
```

## 🏗️ 实施计划

### ✅ 已完成的工作

#### 1. R2 服务集成（已完成）
- ✅ `ICloudflareR2Service` 接口定义
- ✅ `CloudflareR2Service` 服务实现
- ✅ 依赖注入配置 (`Program.cs`)


#### 2. 数据库模型（已完成）
- ✅ `ReadingPhoto` 模型包含所有必要字段：
  - `CloudflareUrl` - R2对象URL
  - `ThumbnailUrl` - 缩略图URL
  - `UploadTime` - 上传时间
  - `SyncStatus` - 同步状态
  - `SyncError` - 同步错误
  - `CapturedAt`, `Latitude`, `Longitude` - 拍摄信息
  - `FileSizeBytes`, `MimeType` - 文件信息

#### 3. 照片处理服务（已完成）
- ✅ `PhotoService` 集成R2上传
- ✅ 移动端照片接收和处理
- ✅ 数据库记录更新

### 🔧 待完成的工作

#### Phase 1: 配置真实的 R2 凭据

**当前状态**：使用占位符配置，需要替换为真实凭据

#### Phase 2: 添加独立的 R2 上传 API 端点

为将来的需求提供直接上传接口：
```csharp
[HttpPost("api/photos/upload-to-r2")]
public async Task<IActionResult> UploadToR2([FromForm] IFormFile file)
{
    // 直接上传到R2的独立端点
}
```

#### Phase 3: 现有照片的 R2 迁移

为已存在的照片提供批量上传到R2的功能

## 📊 成本估算

### R2 定价 (2024年)
- **存储**: $0.015/GB/月
- **Class A 操作** (写入): $4.50/百万次
- **Class B 操作** (读取): $0.36/百万次
- **出站流量**: 免费 🎉

### 预估成本 (月)
```
假设：1000个读数/月，每个2张照片，每张1MB

存储成本：
- 照片数量：2000张/月
- 存储空间：2GB
- 成本：2GB × $0.015 = $0.03/月

操作成本：
- 上传操作：2000次 × $4.50/1M = $0.009/月
- 读取操作：4000次 × $0.36/1M = $0.0014/月

总计：约 $0.04/月 (极低成本)
```

## 🔒 安全考虑

### 1. 访问控制
- 使用预签名URL限制访问时间
- 实现基于角色的权限控制
- 定期轮换API密钥

### 2. 数据保护
- 照片上传前验证文件类型
- 实施文件大小限制
- 考虑启用服务端加密

### 3. 网络安全
- 所有API调用使用HTTPS
- 实施请求频率限制
- 记录访问日志

## 📈 监控和维护

### 1. 监控指标
- 上传成功率
- 平均上传时间
- 存储使用量
- API调用频率

### 2. 日志记录
- 上传/下载操作日志
- 错误和异常记录
- 性能指标追踪

### 3. 备份策略
- 定期备份重要照片
- 实施数据保留政策
- 考虑跨区域复制

## 🚀 实施清单

### 立即需要完成的工作

#### 1. 配置真实的 R2 凭据
- [ ] 创建 Cloudflare 账户（如果没有）
- [ ] 创建 R2 存储桶
- [ ] 生成 API 令牌
- [ ] 更新 `appsettings.json` 中的真实凭据

#### 2. 测试 R2 集成
- [ ] 测试照片上传功能
- [ ] 验证数据库记录更新
- [ ] 测试预签名URL生成
- [ ] 验证缩略图创建

#### 3. 添加独立上传端点（可选）
- [ ] 创建 `/api/photos/upload-to-r2` 端点
- [ ] 实现直接文件上传功能

### 可选的增强功能

#### 1. 现有照片迁移
- [ ] 创建批量迁移脚本
- [ ] 迁移现有本地照片到R2

#### 2. 监控和日志
- [ ] 添加R2操作监控
- [ ] 实施错误报告
- [ ] 性能指标收集

## 📚 参考资源

- [Cloudflare R2 官方文档](https://developers.cloudflare.com/r2/)
- [R2 API 参考](https://developers.cloudflare.com/r2/api/)
- [AWS S3 SDK for .NET](https://docs.aws.amazon.com/sdk-for-net/v3/developer-guide/s3.html)
- [R2 定价信息](https://developers.cloudflare.com/r2/pricing/)

## 💻 当前实现分析

### 1. 现有的 CloudflareR2Service（已实现）
```csharp
// WaterMeterManagement/Services/CloudflareR2Service.cs
public class CloudflareR2Service : ICloudflareR2Service
{
    // ✅ 已实现的核心方法：
    // - UploadPhotoAsync(Stream, fileName, contentType)
    // - UploadPhotoAsync(IFormFile)
    // - DeletePhotoAsync(photoKey)
    // - GeneratePresignedUrlAsync(photoKey, expiration)
    // - CreateThumbnailAsync(originalStream, fileName)
    // - PhotoExistsAsync(photoKey)
    // - GetPhotoSizeAsync(photoKey)
    // - GetPublicUrl(photoKey)
}
```

### 2. 现有的 PhotoService 集成（已实现）
```csharp
// WaterMeterManagement/Services/PhotoService.cs
public async Task<PhotoUploadResult> UploadSinglePhotoAsync(int readingId, MobilePhotoDto photo, int userId)
{
    // 1. 解码Base64照片数据
    // 2. 调用 _r2Service.UploadPhotoAsync() 上传到R2
    // 3. 创建缩略图并上传
    // 4. 保存到数据库，包含CloudflareUrl
    // 5. 返回上传结果
}
```

### 3. 当前配置状态（需要更新）
```json
// appsettings.json - 当前使用占位符
{
  "CloudflareR2": {
    "Endpoint": "https://your-account-id.r2.cloudflarestorage.com",
    "BucketName": "water-meter-photos-test",
    "AccessKey": "your-r2-access-key-placeholder",
    "SecretKey": "your-r2-secret-key-placeholder",
    "CustomDomain": "https://mock-r2-domain.com"
  }
}
```

## 🔄 迁移策略

### 现有照片迁移
1. **批量上传脚本**
   ```bash
   # 创建迁移脚本
   dotnet run --project MigrationTool -- migrate-photos
   ```

2. **渐进式迁移**
   - 新照片直接上传到R2
   - 现有照片按需迁移
   - 保持向后兼容

### 回滚计划
- 保留本地文件备份
- 实施双写策略（本地+R2）
- 快速切换机制

## ⚡ 性能优化

### 1. 上传优化
- 实施并行上传
- 图片压缩和缩略图生成
- 断点续传支持

### 2. 访问优化
- CDN 集成
- 预签名URL缓存
- 图片懒加载

### 3. 成本优化
- 生命周期策略
- 智能分层存储
- 定期清理无用文件

## 🎯 总结

### 当前状态
- ✅ **R2 服务完全实现** - 所有必要的方法都已就绪
- ✅ **数据库模型完善** - 包含所有R2相关字段
- ✅ **照片上传流程建立** - 移动端到R2的完整链路
- ⚠️ **仅需配置真实凭据** - 替换占位符配置

### 下一步行动
1. **立即执行**：按照文档配置真实的 Cloudflare R2 凭据
2. **测试验证**：确保照片能成功上传到R2并更新数据库
3. **可选增强**：添加独立上传端点和现有照片迁移

### 预期效果
配置完成后，系统将：
- 自动将移动端照片上传到 Cloudflare R2
- 在数据库中记录 R2 URL
- 支持安全的照片访问和缩略图生成
- 享受 R2 的零出站费用优势

---

**关键提醒**：R2 集成的核心代码已经完成，只需要配置真实的 API 凭据即可启用！
