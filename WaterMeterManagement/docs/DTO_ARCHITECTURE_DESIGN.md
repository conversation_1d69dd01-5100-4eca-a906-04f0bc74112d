# DTO Architecture Design Document

## Overview
This document defines the Data Transfer Object (DTO) architecture for the Water Meter Management system, establishing clear boundaries and responsibilities for different types of DTOs.

## Architecture Principles

### 1. **Separation by Client Type**
- **Web Admin DTOs** (`WaterMeterManagement.DTOs`): For web management interface
- **Mobile App DTOs** (`WaterMeterManagement.DTOs.Mobile`): Optimized for mobile applications

### 2. **Domain-Driven Organization**
DTOs are organized by business domain within each client type:

```
DTOs/
├── {Domain}DTOs.cs           # Web admin DTOs
├── Mobile/
│   └── Mobile{Domain}Dto.cs  # Mobile-optimized DTOs
```

## DTO Categories and Responsibilities

### **Web Admin DTOs** (Root Namespace)
Located in: `WaterMeterManagement.DTOs`

#### **1. Reading Management**
- **File**: `MeterReadingDTOs.cs`
- **Purpose**: Comprehensive reading data for web admin interface
- **Key DTOs**:
  - `MeterReadingSearchDto` - Search/filter parameters
  - `MeterReadingListDto` - List view with full details
  - `CreateMeterReadingDto` - Creating new readings
  - `UpdateMeterReadingDto` - Updating existing readings
  - `MeterReadingStatisticsDto` - Analytics and reporting

#### **2. Task Management**
- **File**: `TaskDTOs.cs`
- **Purpose**: Task management for administrators
- **Key DTOs**:
  - `TaskListDto` - Administrative task view
  - `CreateTaskDto` - Task creation
  - `UpdateTaskDto` - Task updates

#### **3. Water Meter Management**
- **File**: `WaterMeterDto.cs`
- **Purpose**: Comprehensive meter data management
- **Key DTOs**:
  - `WaterMeterDto` - Full meter information
  - `MeterSearchDto` - Search and filtering

#### **4. Baseline Management**
- **File**: `BaselineDto.cs`
- **Purpose**: Baseline data management
- **Key DTOs**:
  - `BaselineDto` - Baseline record management
  - `BaselineSearchDto` - Search and filtering

### **Mobile App DTOs** (Mobile Namespace)
Located in: `WaterMeterManagement.DTOs.Mobile`

#### **1. Mobile Reading Operations**
- **File**: `MobileReadingDto.cs`
- **Purpose**: Optimized for mobile reading workflows
- **Key DTOs**:
  - `MobileReadingDto` - Core mobile reading data
  - `MobilePhotoDto` - Photo management
  - `ReadingResponseDto` - API response for readings
  - `BatchReadingRequest` - Batch operations
  - `ReadingValidationDto` - Field validation

#### **2. Mobile Task Operations**
- **File**: `MobileTaskDto.cs`
- **Purpose**: Task management for field workers
- **Key DTOs**:
  - `MobileTaskDto` - Task information for mobile
  - `MobileTaskDetailDto` - Detailed task view
  - `TaskCompletionDto` - Task completion data

#### **3. Mobile Baseline Operations**
- **File**: `MobileBaselineDto.cs`
- **Purpose**: Baseline data for mobile sync
- **Key DTOs**:
  - `MobileBaselineDto` - Mobile-optimized baseline data

## Design Guidelines

### **1. Namespace Organization**
```csharp
// Web Admin DTOs
namespace WaterMeterManagement.DTOs
{
    public class MeterReadingListDto { }
}

// Mobile DTOs
namespace WaterMeterManagement.DTOs.Mobile
{
    public class MobileReadingDto { }
}
```

### **2. DTO Naming Conventions**
- **Web Admin**: `{Entity}Dto`, `{Entity}SearchDto`, `Create{Entity}Dto`
- **Mobile**: `Mobile{Entity}Dto`, `{Entity}ResponseDto`, `{Entity}RequestDto`

### **3. Field Optimization**
- **Web Admin**: Complete data sets for comprehensive management
- **Mobile**: Minimal required fields for performance and bandwidth optimization

### **4. Response/Request Patterns**
- **Request DTOs**: Input validation, required fields
- **Response DTOs**: Result data, status information, error handling
- **Search DTOs**: Filtering, pagination, sorting parameters

## Cross-Cutting Concerns

### **1. Common Response Patterns**
All response DTOs should include:
```csharp
public class BaseResponseDto
{
    public bool Success { get; set; }
    public string? Message { get; set; }
    public List<string> Errors { get; set; } = new();
    public List<string> Warnings { get; set; } = new();
}
```

### **2. Synchronization DTOs**
For offline/online sync operations:
- Located in: `DTOs.Mobile`
- Prefix: `Sync`, `Offline`
- Include conflict resolution fields

### **3. Validation DTOs**
For business rule validation:
- Include validation results
- Provide suggestions and warnings
- Support progressive validation

## Implementation Rules

### **1. No Duplicate Definitions**
- Each DTO should have a single, clear purpose
- Avoid duplicating DTOs across namespaces
- Use inheritance for shared functionality

### **2. Proper Using Statements**
Controllers and Services must include appropriate using statements:
```csharp
// For mobile controllers
using WaterMeterManagement.DTOs.Mobile;

// For web controllers
using WaterMeterManagement.DTOs;
```

### **3. Interface Consistency**
Service interfaces should use DTOs from the appropriate namespace based on the consuming client.

## Migration Strategy

### **Phase 1: Cleanup Existing DTOs**
1. Remove duplicate definitions
2. Ensure proper namespace organization
3. Fix using statements

### **Phase 2: Optimize Mobile DTOs**
1. Review mobile DTOs for size optimization
2. Add mobile-specific validation
3. Implement proper response patterns

### **Phase 3: Enhance Web DTOs**
1. Add comprehensive search capabilities
2. Implement advanced filtering
3. Add analytical DTOs

## Conclusion

This architecture provides:
- **Clear separation** between web and mobile concerns
- **Domain-driven organization** for maintainability
- **Performance optimization** for different client types
- **Scalable structure** for future enhancements

Following this architecture will ensure consistent, maintainable, and efficient DTO management across the entire system. 