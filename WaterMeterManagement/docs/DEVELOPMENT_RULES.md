# Development Rules & Guidelines

## 📋 Project Management Rules

### Documentation Management
- **Single Source of Truth**: All documentation must be placed in the corresponding project's `docs` folder
- **Iterative Updates**: For the same issue or requirement, do NOT create multiple MD documents. Update the same document iteratively
- **Structured Documentation**: Maintain clear, comprehensive documentation with proper sections and examples

### Project Structure
- **Frontend Projects**: Always use `yarn` to start frontend projects
- **Backend Projects**: Use `dotnet build && dotnet run` for .NET projects
- **Working Directory**: Operations are based from `/d/LU/693/CORED`

## 🏗️ Architecture & Design Principles

### Design Philosophy
- **First Principles Thinking**: When planning code design, break down problems to fundamental truths and build solutions from the ground up
- **KISS Principle**: Keep It Simple, Stupid - maintain simplicity in implementation
- **SOLID Principles**: Follow all five SOLID principles:
  - **S**ingle Responsibility Principle
  - **O**pen/Closed Principle
  - **L**iskov Substitution Principle
  - **I**nterface Segregation Principle
  - **D**ependency Inversion Principle

### Code Design Standards
- **Single Responsibility**: Each code modification should follow the single responsibility principle
- **No Mixed Changes**: Do not mix multiple types of changes in a single modification
- **Consistent Architecture**: Maintain architectural consistency across the entire project
- **Code Reuse**: Maximize reuse of existing code, avoid duplication

## 💻 Code Implementation Rules

### Language & Framework Standards
- **Multi-Language Expertise**: Proficient in mainstream programming languages and frameworks
- **High-Quality Code**: Generate production-ready, optimized code
- **Performance Focus**: Always consider performance optimization
- **Problem Solving**: Proactively identify and resolve technical issues

### Code Style & Standards
- **English Only**: ALL code and comments must be in English (this is an English system developed in New Zealand)
- **No Chinese**: Absolutely no Chinese characters in any part of the code
- **Consistent Style**: Maintain consistent code style throughout the project
- **Clear Naming**: Use descriptive, meaningful names for variables, methods, and classes

### Code Quality Requirements
- **Clean Code**: Write clean, readable, and maintainable code
- **Error Handling**: Implement comprehensive error handling and logging
- **Testing**: Consider testability in code design
- **Documentation**: Include clear inline documentation for complex logic

### Audit Field Management Architecture (CRITICAL)
- **BaseEntity Inheritance**: ALL entities MUST inherit from BaseEntity to automatically get audit fields
- **Automatic Management**: Audit fields (CreatedAt, UpdatedAt, CreatedBy, UpdatedBy, IsDeleted) are managed automatically by DbContext
- **No Manual Setting**: NEVER manually set audit fields in Services or Controllers
- **DTO Design**: DTOs MUST NOT include audit fields - only business data
- **Frontend Responsibility**: Frontend only handles business data, never audit information
- **DbContext Interception**: All audit logic is centralized in ApplicationDbContext.SaveChangesAsync()
- **User Context**: Current user is extracted from HttpContext automatically
- **Zero Configuration**: New entities get audit capabilities by simply inheriting BaseEntity

#### Entity Design Rule
```csharp
// ✅ CORRECT: Inherit BaseEntity
public class YourNewEntity : BaseEntity
{
    public string BusinessField { get; set; } = string.Empty;
    // Audit fields inherited automatically
}

// ❌ WRONG: Manual audit fields
public class YourNewEntity
{
    public string BusinessField { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; } // DON'T DO THIS
}
```

#### DTO Design Rule
```csharp
// ✅ CORRECT: Business fields only
public class CreateYourEntityDto
{
    public string BusinessField { get; set; } = string.Empty;
    // No audit fields
}

// ❌ WRONG: Including audit fields
public class CreateYourEntityDto
{
    public string BusinessField { get; set; } = string.Empty;
    public string CreatedBy { get; set; } = string.Empty; // DON'T DO THIS
}
```

#### Service Implementation Rule
```csharp
// ✅ CORRECT: Only business logic
public async Task<EntityDto> CreateAsync(CreateEntityDto dto)
{
    var entity = new YourEntity { BusinessField = dto.BusinessField };
    await _context.SaveChangesAsync(); // Audit fields set automatically
    return MapToDto(entity);
}

// ❌ WRONG: Manual audit field setting
public async Task<EntityDto> CreateAsync(CreateEntityDto dto)
{
    var entity = new YourEntity 
    { 
        BusinessField = dto.BusinessField,
        CreatedBy = GetCurrentUser(), // DON'T DO THIS
        CreatedAt = DateTime.UtcNow   // DON'T DO THIS
    };
}
```

## 🔧 Technical Implementation Guidelines

### Backend (.NET) Standards
- **Entity Framework**: Use EF Core for data access with proper migration strategies
- **Dependency Injection**: Properly register services in Program.cs
- **API Design**: Follow RESTful API conventions
- **Validation**: Implement comprehensive data validation
- **Logging**: Use structured logging (Serilog)
- **Security**: Implement proper authentication and authorization

### Frontend Standards
- **Package Management**: Use `yarn` for all frontend projects
- **Component Design**: Create reusable, maintainable components
- **State Management**: Implement proper state management patterns
- **TypeScript**: Use TypeScript for type safety
- **Responsive Design**: Ensure mobile-friendly interfaces

### Database Standards
- **Migration Strategy**: Use Entity Framework migrations for schema changes
- **Data Integrity**: Maintain referential integrity and constraints
- **Performance**: Optimize queries and indexing
- **Backup Compatibility**: Ensure backward compatibility in schema changes

## 🚀 Development Workflow

### Code Review Process
- **Self Review**: Always review your own code before submission
- **Testing**: Test functionality thoroughly before delivery
- **Performance Check**: Verify performance impact of changes
- **Documentation Update**: Update relevant documentation with changes

### Deployment Standards
- **Build Verification**: Ensure code compiles without errors
- **Runtime Testing**: Verify application starts and runs correctly
- **Configuration**: Check all configuration settings
- **Environment Variables**: Properly manage environment-specific settings

## 📊 Project-Specific Rules

### Water Meter Management System
- **AMS Integration**: Follow established AMS integration patterns
- **Backward Compatibility**: Maintain compatibility with existing data and APIs
- **Data Validation**: Implement multi-tier validation (required fields → business rules → data integrity)
- **Excel Processing**: Use EPPlus library for Excel file operations
- **Quality Scoring**: Implement comprehensive data quality assessment
- **Audit Architecture**: MANDATORY use of BaseEntity inheritance + DbContext automatic audit field management
- **Data Layer Consistency**: All models must follow the established audit field pattern
- **API Design**: Controllers should never handle audit fields - focus on business logic only

### Mobile Applications
- **React Native**: Use React Native for cross-platform mobile development
- **State Management**: Implement proper context and state management
- **Offline Capability**: Consider offline functionality where applicable
- **Performance**: Optimize for mobile device constraints

### Frontend Applications
- **Next.js**: Use Next.js for React-based frontend applications
- **Component Libraries**: Utilize established UI component libraries
- **API Integration**: Implement proper API client patterns
- **Error Handling**: Provide user-friendly error messages

## ✅ Quality Assurance

### Code Quality Metrics
- **Complexity**: Keep cyclomatic complexity low
- **Coverage**: Aim for high test coverage
- **Performance**: Monitor and optimize performance metrics
- **Security**: Follow security best practices

### Validation Checklist
- [ ] Code compiles without errors or warnings
- [ ] All tests pass
- [ ] Documentation is updated
- [ ] Performance impact is acceptable
- [ ] Security implications are considered
- [ ] Backward compatibility is maintained
- [ ] **Audit Fields**: All new entities inherit BaseEntity (NO manual audit fields)
- [ ] **DTO Design**: DTOs contain ONLY business fields (NO audit fields)
- [ ] **Service Layer**: No manual setting of CreatedBy/UpdatedBy/CreatedAt/UpdatedAt
- [ ] **Frontend**: TypeScript interfaces match backend DTOs (business fields only)

## 🎯 User Interaction Protocol

### Response Standards
- **Language**: Always respond in Chinese to the user
- **Technical Content**: All code and technical documentation in English
- **Clarity**: Provide clear explanations and examples
- **Proactive Help**: Anticipate and address potential issues

### Command Execution
- **Final Step**: Every response must end with asking the user to execute:
  ```bash
  cd /d/LU/693/WaterMeterManagement && dotnet build && dotnet run
  ```
- **Frontend Projects**: Use `yarn start` or `yarn dev` for frontend projects
- **Verification**: Always verify that applications start correctly

## 🔄 Continuous Improvement

### Learning & Adaptation
- **Stay Updated**: Keep up with latest technologies and best practices
- **Feedback Integration**: Incorporate user feedback into development approach
- **Process Refinement**: Continuously improve development processes
- **Knowledge Sharing**: Document lessons learned and best practices

### Innovation Guidelines
- **Technology Evaluation**: Evaluate new technologies against project needs
- **Risk Assessment**: Consider risks and benefits of new approaches
- **Gradual Adoption**: Implement changes incrementally when possible
- **Rollback Plans**: Always have rollback strategies for major changes

---

## 📞 Support & Maintenance

### Issue Resolution
- **Systematic Approach**: Use systematic debugging approaches
- **Root Cause Analysis**: Identify and address root causes, not just symptoms
- **Documentation**: Document solutions for future reference
- **Prevention**: Implement measures to prevent similar issues

### Maintenance Standards
- **Regular Updates**: Keep dependencies and frameworks updated
- **Security Patches**: Apply security updates promptly
- **Performance Monitoring**: Monitor and optimize system performance
- **Backup Strategies**: Implement and test backup and recovery procedures

---

*These rules ensure consistent, high-quality development while maintaining system reliability and user satisfaction.* 