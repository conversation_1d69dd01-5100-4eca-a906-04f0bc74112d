# 水表管理系统 - 更新后的菜单结构

## 概览
根据业务需求，已将原来的"Business Management"层级移除，业务功能模块现在与Dashboard和System Management处于同一级别。

## 菜单层级结构

### 一级菜单 (Top Level)
```
1. 📊 Dashboard                    (Order: 1)
2. 🗃️ Master Data                 (Order: 2)
3. 📅 Planning                     (Order: 3)
4. ☑️ Task Management             (Order: 4)
5. 📖 Meter Reading               (Order: 5)
6. 📡 Sync Center                 (Order: 6)
7. 📊 Reports                     (Order: 7)
8. 📱 Mobile Management           (Order: 8)
9. 🔍 Quick Access               (Order: 9)
10. ⚙️ System Management          (Order: 10)
```

### 二级菜单详细结构

#### 🗃️ Master Data（主数据）
- **AMIS Sync** (AMIS同步)
- **Meter Management** (水表管理)
- **Baseline Management** (基准管理)

#### 📅 Planning（计划调度）
- **Schedule Management** (计划管理)
- **Route Management** (路线管理)
- **Frequency Templates** (频率模板)

#### ☑️ Task Management（任务管理）
- **Task Assignment** (任务分配)
  - Bulk Assignment (批量分配)
  - Reactive Assignment (响应式分配)
- **Task Monitoring** (任务监控)
- **Overdue Management** (逾期管理)

#### 📖 Meter Reading（抄表管理）
- **Reading Records** (读数记录)
- **Photo Gallery** (照片库)
- **Anomaly Management** (异常管理)
- **Validation Rules** (验证规则)

#### 📡 Sync Center（同步中心）
- **Device Sync Status** (设备同步状态)
- **Sync Queue** (同步队列)
- **Offline Data** (离线数据)
- **Sync Logs** (同步日志)

#### 📊 Reports（报表中心）
- **Operational Dashboard** (运营仪表板)
- **Completion Reports** (完成率报表)
- **Exception Analysis** (异常分析)
- **User Performance** (用户绩效)
- **Export Center** (导出中心)
  - Generate Reports (生成报表)
  - Export History (导出历史)
  - Template Management (模板管理)

#### 📱 Mobile Management（移动端管理）
- **Device Registry** (设备注册)
- **App Versions** (应用版本)
- **Compatibility Tests** (兼容性测试)

#### 🔍 Quick Access（快捷入口）
- **Create Reactive Task** (创建响应任务)
- **Today's Assignments** (今日分配)
- **Pending Sync** (待同步数据)
- **Recent Alerts** (最近告警)

#### ⚙️ System Management（系统管理）
- **Menu Management** (菜单管理)
- **Role Management** (角色管理)
- **Permission Management** (权限管理)
- **User Management** (用户管理)

## 技术实现细节

### 图标映射
使用Ant Design图标系统：
- 📊 Dashboard: `DashboardOutlined`
- 🗃️ Master Data: `DatabaseOutlined`
- 📅 Planning: `CalendarOutlined`
- ☑️ Task Management: `CheckSquareOutlined`
- 📖 Meter Reading: `ReadOutlined`
- 📡 Sync Center: `SyncOutlined`
- 📊 Reports: `BarChartOutlined`
- 📱 Mobile Management: `MobileOutlined`
- 🔍 Quick Access: `ThunderboltOutlined`
- ⚙️ System Management: `SettingOutlined`

### 权限控制
各菜单项根据功能性质分配相应权限：
- **查看权限**: `business.watermeter.view`, `business.analysis.view`
- **管理权限**: `business.watermeter.manage`
- **系统权限**: `system.*.manage`

### 路由结构
- Master Data: `/master-data/*`
- Planning: `/planning/*`
- Task Management: `/task-management/*`
- Meter Reading: `/meter-reading/*`
- Sync Center: `/sync-center/*`
- Reports: `/reports/*`
- Mobile Management: `/mobile/*`
- Quick Access: `/quick-access/*`
- System Management: `/system/*`

## 数据库更新
菜单结构已在`DbInitializer.cs`中完成配置，包括：
- 移除了原有的"Business Management"父级菜单
- 添加了8个新的业务功能一级菜单
- 配置了完整的二级和三级菜单结构
- 设置了适当的权限约束和图标

## 下一步操作
1. 运行数据库迁移以应用新的菜单结构
2. 更新前端路由配置以匹配新的菜单结构
3. 开始实现具体的业务功能模块 