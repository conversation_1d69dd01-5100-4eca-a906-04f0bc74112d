# User Role Assignment UPSERT Fix

## Problem Fixed
Fixed the "duplicate key value violates unique constraint" error when assigning roles to users who previously had roles that were soft-deleted.

## Root Cause
The original implementation only used INSERT operations when assigning roles, which caused primary key conflicts when trying to assign roles that existed as soft-deleted records.

## Solution Implemented

### 1. Created UpsertResult DTO
```csharp
public class UpsertResult
{
    public int RoleId { get; set; }
    public string Operation { get; set; } = string.Empty; // "Created", "Restored", "Skipped"
    public bool Success { get; set; }
    public string? ErrorMessage { get; set; }
}
```

### 2. Added UpsertUserRole Method
- **Interface**: `IUserRoleRepository.UpsertUserRole(int userId, int roleId, string updatedBy)`
- **Implementation**: `UserRoleRepository.UpsertUserRole`

### 3. UPSERT Logic
For each role assignment:
1. **Check if record exists** (including soft-deleted)
2. **If exists and soft-deleted**: UPDATE to restore (set IsDeleted=false)
3. **If exists and active**: SKIP (already assigned)
4. **If doesn't exist**: INSERT new record

### 4. Enhanced Controller
- **Transaction support**: All role assignments in single transaction
- **Detailed logging**: Operation summary and individual results
- **Error handling**: Rollback on any failure
- **Result reporting**: Returns operation summary to client

## Key Benefits

### 1. Handles Soft Delete Properly
- Restores previously deleted role assignments
- No more primary key conflicts
- Maintains audit trail

### 2. Atomic Operations
- All role assignments succeed or fail together
- Database consistency guaranteed
- Proper error handling

### 3. Detailed Feedback
- Operation summary (Created/Restored/Skipped counts)
- Individual operation results
- Better debugging information

## API Response Example

### Success Response
```json
{
  "message": "Roles assigned successfully",
  "summary": {
    "Restored": 2,
    "Created": 1,
    "Skipped": 0
  },
  "details": [
    {
      "roleId": 1,
      "operation": "Restored",
      "success": true,
      "errorMessage": null
    },
    {
      "roleId": 2,
      "operation": "Restored", 
      "success": true,
      "errorMessage": null
    },
    {
      "roleId": 3,
      "operation": "Created",
      "success": true,
      "errorMessage": null
    }
  ]
}
```

## Testing Steps

### 1. Test Scenario: User with Soft-Deleted Roles
```sql
-- Check current state
SELECT * FROM "UserRoles" WHERE "UserId" = 1;

-- Should show records with IsDeleted = true
```

### 2. Assign Roles via API
```bash
POST /api/user/1/roles
Content-Type: application/json

[1, 2, 3]
```

### 3. Verify Results
```sql
-- Check after assignment
SELECT * FROM "UserRoles" WHERE "UserId" = 1;

-- Should show:
-- - Previously soft-deleted records now have IsDeleted = false
-- - New records created for roles that didn't exist
-- - UpdatedAt timestamps updated for restored records
```

## Backward Compatibility

### CreateAsync Method Preserved
- Still used by `AuthService.AssignDefaultRoleAsync`
- No breaking changes to existing functionality
- Only `UserController.AssignRolesToUser` uses new UPSERT logic

### Database Schema
- No database changes required
- Uses existing soft delete pattern
- Maintains all audit fields

## Future Improvements

### 1. Bulk Operations
Could be optimized for large role assignments using bulk SQL operations.

### 2. Permission Validation
Could add validation to ensure assigned roles are valid and user has permission to assign them.

### 3. Audit Logging
Could enhance with detailed audit logging for compliance requirements.

## Error Handling

### Transaction Rollback
- Any failure in role assignment rolls back entire operation
- Database remains in consistent state
- Detailed error messages for debugging

### Validation
- Checks user existence before processing
- Validates role existence before assignment
- Graceful handling of invalid roles

## Performance Considerations

### Database Queries
- One query per role to check existence
- Efficient use of Entity Framework
- Transaction overhead minimal for typical role counts

### Memory Usage
- Results collected in memory for reporting
- Acceptable for typical role assignment scenarios
- Could be optimized for very large role sets if needed

## Conclusion

This fix resolves the primary key constraint issue while maintaining:
- ✅ Data integrity
- ✅ Audit trail
- ✅ Backward compatibility  
- ✅ Proper error handling
- ✅ Detailed operation feedback

The UPSERT pattern is now properly implemented for user role assignments, eliminating the soft delete conflict issue.
