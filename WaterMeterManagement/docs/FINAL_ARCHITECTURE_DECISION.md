# Final Architecture & Tech Stack Decision

## 🎯 Confirmed Architecture: Complete Frontend-Backend Separation

```
CORED Ecosystem:
├── WaterMeterManagement/          # .NET 8 Web API (Port 5000)
│   ├── Controllers/               # REST API endpoints
│   │   ├── AuthController.cs     # JWT authentication
│   │   ├── UsersController.cs    # User management
│   │   └── MetersController.cs   # Water meter management
│   ├── Services/                 # Business logic
│   ├── Models/                   # Data models
│   └── Database/                 # SQL Server
│
├── water-meter-admin/            # Ant Design Pro Next.js (Port 3000)
│   ├── src/
│   │   ├── pages/               # Page components
│   │   ├── components/          # Reusable UI components
│   │   ├── services/            # API calls to .NET backend
│   │   ├── models/              # TypeScript interfaces
│   │   └── locales/             # i18n support
│   └── config/                  # Project configuration
│
└── MeterReadingApp/             # Mobile app (existing)
    └── (calls WaterMeterManagement API)
```

## 🚀 Why Ant Design Pro Next.js is Perfect

### ✅ **Advantages You Mentioned:**
- **Frequent Updates**: Very active development
- **Excellent i18n Support**: Built-in internationalization
- **Professional Grade**: Used by <PERSON><PERSON><PERSON>, Ant Financial
- **Rich Ecosystem**: Tons of plugins and extensions

### ✅ **Additional Benefits:**
- **TypeScript First**: Full type safety
- **Next.js 13+ App Router**: Latest React features
- **Built-in State Management**: Zustand/Redux Toolkit
- **Enterprise Features**: RBAC, layouts, themes
- **Mobile Responsive**: Perfect for your admin needs

---

## 🛠️ Implementation Plan

### Step 1: Setup Ant Design Pro Next.js Project

```bash
# Navigate to CORED directory
cd /d/LU/693/CORED

# Create Ant Design Pro project
npx create-next-app@latest water-meter-admin --typescript --eslint --app
cd water-meter-admin

# Install Ant Design Pro dependencies
npm install antd @ant-design/pro-components @ant-design/pro-layout @ant-design/pro-table
npm install @ant-design/pro-form @ant-design/pro-card @ant-design/pro-descriptions
npm install @ant-design/icons

# Install additional utilities
npm install axios swr dayjs
npm install @types/node

# Install internationalization
npm install react-intl @formatjs/intl-localematcher @formatjs/intl-locale
```

### Step 2: Update .NET API for CORS

```csharp
// Program.cs - Add CORS configuration
var builder = WebApplication.CreateBuilder(args);

// Add services
builder.Services.AddControllers();
builder.Services.AddDbContext<ApplicationDbContext>(options =>
    options.UseSqlServer(builder.Configuration.GetConnectionString("DefaultConnection")));

// Add CORS for frontend separation
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowAntDesignPro", builder =>
    {
        builder.WithOrigins(
            "http://localhost:3000",      // Next.js dev server
            "https://localhost:3000"      // HTTPS dev server
        )
        .AllowAnyMethod()
        .AllowAnyHeader()
        .AllowCredentials();
    });
});

// JWT Authentication
builder.Services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
    .AddJwtBearer(options =>
    {
        options.TokenValidationParameters = new TokenValidationParameters
        {
            ValidateIssuer = true,
            ValidateAudience = true,
            ValidateLifetime = true,
            ValidateIssuerSigningKey = true,
            ValidIssuer = builder.Configuration["Jwt:Issuer"],
            ValidAudience = builder.Configuration["Jwt:Audience"],
            IssuerSigningKey = new SymmetricSecurityKey(
                Encoding.UTF8.GetBytes(builder.Configuration["Jwt:Key"]))
        };
    });

var app = builder.Build();

// Configure pipeline
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

app.UseHttpsRedirection();
app.UseCors("AllowAntDesignPro");  // Enable CORS
app.UseAuthentication();
app.UseAuthorization();
app.MapControllers();

app.Run();
```

### Step 3: Ant Design Pro Project Structure

```
water-meter-admin/
├── src/
│   ├── app/                      # Next.js 13 App Router
│   │   ├── layout.tsx           # Root layout
│   │   ├── page.tsx             # Home page
│   │   ├── login/
│   │   │   └── page.tsx         # Login page
│   │   ├── dashboard/
│   │   │   └── page.tsx         # Dashboard
│   │   ├── users/
│   │   │   ├── page.tsx         # User list
│   │   │   └── [id]/
│   │   │       └── page.tsx     # User details
│   │   └── meters/
│   │       ├── page.tsx         # Meter list
│   │       └── [id]/
│   │           └── page.tsx     # Meter details
│   │
│   ├── components/              # Reusable components
│   │   ├── GlobalHeader/
│   │   ├── GlobalSider/
│   │   ├── UserTable/
│   │   └── MeterTable/
│   │
│   ├── services/                # API services
│   │   ├── api.ts              # Axios configuration
│   │   ├── auth.ts             # Authentication APIs
│   │   ├── user.ts             # User management APIs
│   │   └── meter.ts            # Meter management APIs
│   │
│   ├── types/                   # TypeScript definitions
│   │   ├── auth.ts
│   │   ├── user.ts
│   │   └── meter.ts
│   │
│   ├── utils/                   # Utility functions
│   │   ├── request.ts          # HTTP request wrapper
│   │   └── storage.ts          # Local storage helpers
│   │
│   └── locales/                 # Internationalization
│       ├── en-US.json
│       ├── zh-CN.json
│       └── index.ts
│
├── public/                      # Static assets
├── next.config.js              # Next.js configuration
├── tailwind.config.js          # Tailwind CSS
├── tsconfig.json               # TypeScript configuration
└── package.json
```

### Step 4: API Service Layer Setup

```typescript
// src/services/api.ts
import axios from 'axios';
import { message } from 'antd';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000/api';

export const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor - add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor - handle errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      message.error('Authentication failed, please login again');
      localStorage.removeItem('token');
      window.location.href = '/login';
    } else if (error.response?.status >= 500) {
      message.error('Server error, please try again later');
    }
    return Promise.reject(error);
  }
);

// src/services/auth.ts
export interface LoginRequest {
  username: string;
  password: string;
}

export interface LoginResponse {
  token: string;
  refreshToken: string;
  user: {
    id: string;
    username: string;
    fullName: string;
    email: string;
  };
}

export const authService = {
  async login(data: LoginRequest): Promise<LoginResponse> {
    const response = await api.post('/auth/login', data);
    return response.data;
  },

  async getProfile() {
    const response = await api.get('/auth/profile');
    return response.data;
  },

  async logout() {
    localStorage.removeItem('token');
    localStorage.removeItem('refreshToken');
  }
};

// src/services/user.ts
export interface User {
  id: string;
  username: string;
  fullName: string;
  email: string;
  lastLogin?: string;
  isActive: boolean;
}

export const userService = {
  async getUsers(params?: { page?: number; size?: number; search?: string }) {
    const response = await api.get('/users', { params });
    return response.data;
  },

  async getUserById(id: string) {
    const response = await api.get(`/users/${id}`);
    return response.data;
  },

  async createUser(user: Omit<User, 'id'>) {
    const response = await api.post('/users', user);
    return response.data;
  },

  async updateUser(id: string, user: Partial<User>) {
    const response = await api.put(`/users/${id}`, user);
    return response.data;
  },

  async deleteUser(id: string) {
    const response = await api.delete(`/users/${id}`);
    return response.data;
  }
};
```

### Step 5: Ant Design Pro Components Example

```typescript
// src/components/UserTable/index.tsx
import { ProTable, ProColumns } from '@ant-design/pro-table';
import { Button, Space, Popconfirm } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined } from '@ant-design/icons';
import { User, userService } from '@/services/user';

export default function UserTable() {
  const columns: ProColumns<User>[] = [
    {
      title: 'Username',
      dataIndex: 'username',
      key: 'username',
      copyable: true,
    },
    {
      title: 'Full Name',
      dataIndex: 'fullName',
      key: 'fullName',
    },
    {
      title: 'Email',
      dataIndex: 'email',
      key: 'email',
      copyable: true,
    },
    {
      title: 'Last Login',
      dataIndex: 'lastLogin',
      key: 'lastLogin',
      valueType: 'dateTime',
    },
    {
      title: 'Status',
      dataIndex: 'isActive',
      key: 'isActive',
      valueType: 'select',
      valueEnum: {
        true: { text: 'Active', status: 'Success' },
        false: { text: 'Inactive', status: 'Error' },
      },
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record) => (
        <Space>
          <Button 
            type="link" 
            icon={<EditOutlined />}
            onClick={() => handleEdit(record.id)}
          >
            Edit
          </Button>
          <Popconfirm
            title="Are you sure to delete this user?"
            onConfirm={() => handleDelete(record.id)}
          >
            <Button type="link" danger icon={<DeleteOutlined />}>
              Delete
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  const handleEdit = (id: string) => {
    // Navigate to edit page
    window.location.href = `/users/${id}`;
  };

  const handleDelete = async (id: string) => {
    try {
      await userService.deleteUser(id);
      // Refresh table data
    } catch (error) {
      console.error('Delete failed:', error);
    }
  };

  return (
    <ProTable<User>
      columns={columns}
      request={async (params) => {
        const response = await userService.getUsers(params);
        return {
          data: response.data,
          success: true,
          total: response.total,
        };
      }}
      rowKey="id"
      pagination={{
        showSizeChanger: true,
        pageSizeOptions: ['10', '20', '50'],
      }}
      search={{
        labelWidth: 'auto',
      }}
      toolBarRender={() => [
        <Button key="add" type="primary" icon={<PlusOutlined />}>
          Add User
        </Button>,
      ]}
    />
  );
}
```

---

## 🎨 Development Workflow

### Daily Development:
```bash
# Terminal 1: Start .NET API
cd WaterMeterManagement
dotnet run                    # http://localhost:5000

# Terminal 2: Start Ant Design Pro frontend
cd water-meter-admin
npm run dev                   # http://localhost:3000
```

### API Integration:
```bash
# Environment variables (.env.local)
NEXT_PUBLIC_API_URL=http://localhost:5000/api
NEXT_PUBLIC_APP_NAME="Water Meter Management"
```

---

## ✅ Next Steps

1. **Create the Ant Design Pro project**
2. **Update .NET API CORS settings**
3. **Build core components**: Login, Dashboard, User Management
4. **Setup internationalization** (English/Chinese)
5. **Connect to your existing .NET API**

Would you like me to start setting this up for you right now? 