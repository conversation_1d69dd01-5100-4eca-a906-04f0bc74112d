FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS base
WORKDIR /app
EXPOSE 5000

FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /src
COPY ["WaterMeterManagement.csproj", "."]
RUN dotnet restore "WaterMeterManagement.csproj"
COPY . .
WORKDIR "/src"
RUN dotnet build "WaterMeterManagement.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "WaterMeterManagement.csproj" -c Release -o /app/publish

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .

# Create logs directory
RUN mkdir -p /app/logs

# Install curl for health checks
RUN apt-get update && apt-get install -y curl && rm -rf /var/lib/apt/lists/*

ENTRYPOINT ["dotnet", "WaterMeterManagement.dll"] 