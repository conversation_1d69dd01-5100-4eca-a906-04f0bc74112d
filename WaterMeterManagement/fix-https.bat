@echo off
echo 🔧 Fixing HTTPS certificate for development...
echo.

echo 1. Trust the ASP.NET Core HTTPS development certificate
dotnet dev-certs https --trust

echo.
echo 2. Clean and regenerate certificate
dotnet dev-certs https --clean
dotnet dev-certs https --trust

echo.
echo 3. Restart the application
echo Please press Ctrl+C to stop the current application and run 'dotnet run' again

echo.
echo ✅ HTTPS should now work at: https://localhost:7000
pause 