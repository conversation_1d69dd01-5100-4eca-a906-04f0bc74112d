using AutoMapper;
using WaterMeterManagement.DTOs;
using WaterMeterManagement.Models;

namespace WaterMeterManagement.Mappings
{
    public class WaterMeterMappingProfile : Profile
    {
        public WaterMeterMappingProfile()
        {
            // 创建到实体的映射，忽略null值
            CreateMap<UpdateWaterMeterDto, WaterMeter>()
                .ForAllMembers(opt => opt.Condition((src, dest, srcMember) => srcMember != null));
            
            // 从实体到DTO的映射
            CreateMap<WaterMeter, WaterMeterDto>();
            CreateMap<WaterMeter, WaterMeterListDto>();
            
            // 创建DTO的映射
            CreateMap<CreateWaterMeterDto, WaterMeter>();
        }
    }
}
