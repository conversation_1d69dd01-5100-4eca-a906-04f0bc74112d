<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Water Meter Management - Login to Access Swagger</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .login-container {
            background: white;
            border-radius: 10px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            width: 100%;
            max-width: 400px;
        }
        
        .login-header {
            background: linear-gradient(135deg, #2196F3, #1976D2);
            color: white;
            padding: 30px 20px;
            text-align: center;
        }
        
        .login-header h1 {
            font-size: 24px;
            margin-bottom: 10px;
        }
        
        .login-header p {
            opacity: 0.9;
            font-size: 14px;
        }
        
        .login-form {
            padding: 30px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }
        
        .form-group input {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e1e5e9;
            border-radius: 6px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }
        
        .form-group input:focus {
            outline: none;
            border-color: #2196F3;
        }
        
        .login-btn {
            width: 100%;
            background: linear-gradient(135deg, #2196F3, #1976D2);
            color: white;
            border: none;
            padding: 12px;
            font-size: 16px;
            font-weight: 600;
            border-radius: 6px;
            cursor: pointer;
            transition: transform 0.2s ease;
        }
        
        .login-btn:hover {
            transform: translateY(-2px);
        }
        
        .login-btn:disabled {
            opacity: 0.6;
            transform: none;
            cursor: not-allowed;
        }
        
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 6px;
            display: none;
        }
        
        .result.success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        
        .result.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        
        .token-display {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 15px;
            margin-top: 15px;
            word-break: break-all;
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }
        
        .copy-btn {
            background: #28a745;
            color: white;
            border: none;
            padding: 8px 16px;
            font-size: 12px;
            border-radius: 4px;
            cursor: pointer;
            margin-top: 10px;
        }
        
        .swagger-btn {
            background: #17a2b8;
            color: white;
            border: none;
            padding: 12px;
            font-size: 14px;
            font-weight: 600;
            border-radius: 6px;
            cursor: pointer;
            width: 100%;
            margin-top: 15px;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }
        
        .demo-credentials {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
            font-size: 14px;
        }
        
        .demo-credentials h4 {
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <h1>💧 Water Meter Management</h1>
            <p>Login with your Workbench credentials to access Swagger API Documentation</p>
        </div>
        
        <div class="login-form">
            <form id="loginForm">
                <div class="form-group">
                    <label for="username">Username:</label>
                    <input type="text" id="username" name="username" required placeholder="Enter your workbench username">
                </div>
                
                <div class="form-group">
                    <label for="password">Password:</label>
                    <input type="password" id="password" name="password" required placeholder="Enter your workbench password">
                </div>
                
                <button type="submit" class="login-btn" id="loginBtn">
                    🔐 Login with Workbench
                </button>
            </form>
            
            <div id="result" class="result">
                <div id="resultMessage"></div>
            </div>
        </div>
    </div>

    <script>
        document.getElementById('loginForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const loginBtn = document.getElementById('loginBtn');
            const result = document.getElementById('result');
            const resultMessage = document.getElementById('resultMessage');
            
            // Show loading state
            loginBtn.disabled = true;
            loginBtn.textContent = '🔄 Authenticating with Workbench...';
            result.style.display = 'none';
            
            try {
                const username = document.getElementById('username').value;
                const password = document.getElementById('password').value;
                
                const response = await fetch('/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: username,
                        password: password
                    })
                });
                
                const data = await response.json();
                
                if (response.ok && data.success) {
                    // Store token in cookie for middleware
                    document.cookie = `jwt_token=${data.token}; path=/; max-age=2592000`; // 30 days
                    
                    // Store in localStorage as backup
                    localStorage.setItem('jwtToken', data.token);
                    
                    // Show success message briefly then redirect
                    result.className = 'result success';
                    result.style.display = 'block';
                    resultMessage.innerHTML = `
                        ✅ <strong>Login Successful!</strong><br>
                        Redirecting to Swagger...
                    `;
                    
                    // Redirect to swagger after 1 second
                    setTimeout(() => {
                        window.location.href = '/swagger';
                    }, 1000);
                    
                } else {
                    // Error
                    result.className = 'result error';
                    result.style.display = 'block';
                    resultMessage.innerHTML = `
                        ❌ <strong>Login Failed</strong><br>
                        ${data.message || 'Invalid credentials'}
                    `;
                }
                
            } catch (error) {
                // Network error
                result.className = 'result error';
                result.style.display = 'block';
                resultMessage.innerHTML = `
                    ❌ <strong>Network Error</strong><br>
                    Unable to connect to the server. Please try again.
                `;
                console.error('Login error:', error);
            }
            
            // Reset button
            loginBtn.disabled = false;
            loginBtn.textContent = '🔐 Login with Workbench';
        });
        
        // Check if user already has a valid token
        window.addEventListener('load', function() {
            // Check if there's a valid token cookie
            const cookies = document.cookie.split(';');
            const jwtCookie = cookies.find(cookie => cookie.trim().startsWith('jwt_token='));
            
            if (jwtCookie) {
                // User might already be logged in, redirect to swagger
                window.location.href = '/swagger';
            }
        });
    </script>
</body>
</html> 