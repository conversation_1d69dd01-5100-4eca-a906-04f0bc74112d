using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using WaterMeterManagement.Data;
using WaterMeterManagement.Models;
using WaterMeterManagement.Repositories.Interfaces;

namespace WaterMeterManagement.Repositories
{
    public class MenuRepository : IMenuRepository
    {
        private readonly ApplicationDbContext _context;

        public MenuRepository(ApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<IEnumerable<Menu>> GetAllAsync()
        {
            return await _context.Menus
                .Include(m => m.Parent)
                .Include(m => m.Permission)
                .Where(m => !m.IsDeleted)
                .OrderBy(m => m.Order)
                .ToListAsync();
        }

        public async Task<Menu> GetByIdAsync(int id)
        {
            return await _context.Menus
                .Include(m => m.Parent)
                .Include(m => m.Permission)
                .FirstOrDefaultAsync(m => m.Id == id && !m.IsDeleted);
        }

        public async Task<Menu> GetByCodeAsync(string code)
        {
            return await _context.Menus
                .Include(m => m.Parent)
                .Include(m => m.Permission)
                .FirstOrDefaultAsync(m => m.Code == code && !m.IsDeleted);
        }

        public async Task<IEnumerable<Menu>> GetRootMenusAsync()
        {
            return await _context.Menus
                .Include(m => m.Permission)
                .Where(m => m.ParentId == null && !m.IsDeleted)
                .OrderBy(m => m.Order)
                .ToListAsync();
        }

        public async Task<IEnumerable<Menu>> GetChildrenAsync(int parentId)
        {
            return await _context.Menus
                .Include(m => m.Permission)
                .Where(m => m.ParentId == parentId && !m.IsDeleted)
                .OrderBy(m => m.Order)
                .ToListAsync();
        }

        public async Task<Menu> CreateAsync(Menu menu)
        {
            await _context.Menus.AddAsync(menu);
            await _context.SaveChangesAsync();
            return menu;
        }

        public async Task<Menu> UpdateAsync(Menu menu)
        {
            // Detach any existing tracked entity with the same ID
            var existingEntity = _context.ChangeTracker.Entries<Menu>()
                .FirstOrDefault(e => e.Entity.Id == menu.Id);
            if (existingEntity != null)
            {
                existingEntity.State = Microsoft.EntityFrameworkCore.EntityState.Detached;
            }

            _context.Menus.Update(menu);
            await _context.SaveChangesAsync();
            return menu;
        }

        public async Task DeleteAsync(int id)
        {
            var menu = await _context.Menus.FindAsync(id);
            if (menu != null)
            {
                menu.IsDeleted = true;
                await _context.SaveChangesAsync();
            }
        }

        public async Task<bool> ExistsAsync(int id)
        {
            return await _context.Menus.AnyAsync(m => m.Id == id && !m.IsDeleted);
        }

        public async Task<bool> ExistsByCodeAsync(string code)
        {
            return await _context.Menus.AnyAsync(m => m.Code == code && !m.IsDeleted);
        }
    }
} 