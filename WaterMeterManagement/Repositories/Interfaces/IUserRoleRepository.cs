using System.Collections.Generic;
using System.Threading.Tasks;
using WaterMeterManagement.Models;

namespace WaterMeterManagement.Repositories.Interfaces
{
    public interface IUserRoleRepository
    {
        Task<IEnumerable<UserRole>> GetByUserIdAsync(int userId);
        Task<IEnumerable<UserRole>> GetByRoleIdAsync(int roleId);
        Task<UserRole> CreateAsync(UserRole userRole);
        Task<string> UpsertUserRole(int userId, int roleId, string updatedBy);
        Task DeleteAsync(int userId, int roleId);
        Task<bool> ExistsAsync(int userId, int roleId);
    }
} 