using System.Collections.Generic;
using System.Threading.Tasks;
using WaterMeterManagement.Models;

namespace WaterMeterManagement.Repositories.Interfaces
{
    public interface IRolePermissionRepository
    {
        Task<IEnumerable<RolePermission>> GetByRoleIdsAsync(IEnumerable<int> roleIds);
        Task<IEnumerable<RolePermission>> GetByRoleIdAsync(int roleId);
        Task<RolePermission> CreateAsync(RolePermission rolePermission);
        Task DeleteAsync(int roleId, int permissionId);
        Task<bool> ExistsAsync(int roleId, int permissionId);
    }
} 