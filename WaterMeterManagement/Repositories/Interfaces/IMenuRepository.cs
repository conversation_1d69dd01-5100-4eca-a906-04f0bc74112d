using System.Collections.Generic;
using System.Threading.Tasks;
using WaterMeterManagement.Models;

namespace WaterMeterManagement.Repositories.Interfaces
{
    public interface IMenuRepository
    {
        Task<IEnumerable<Menu>> GetAllAsync();
        Task<Menu> GetByIdAsync(int id);
        Task<Menu> GetByCodeAsync(string code);
        Task<IEnumerable<Menu>> GetRootMenusAsync();
        Task<IEnumerable<Menu>> GetChildrenAsync(int parentId);
        Task<Menu> CreateAsync(Menu menu);
        Task<Menu> UpdateAsync(Menu menu);
        Task DeleteAsync(int id);
        Task<bool> ExistsAsync(int id);
        Task<bool> ExistsByCodeAsync(string code);
    }
} 