using WaterMeterManagement.Models;

namespace WaterMeterManagement.Repositories.Interfaces
{
    public interface IRoleRepository
    {
        Task<IEnumerable<Role>> GetAllAsync();
        Task<Role?> GetByIdAsync(int id);
        Task<Role> AddAsync(Role role);
        Task<Role> UpdateAsync(Role role);
        Task DeleteAsync(int id);
        Task<IEnumerable<Role>> GetRolesWithPermissionsAsync();
        Task<Role?> GetRoleWithPermissionsAsync(int id);
        Task<bool> RoleExistsAsync(string name);
        Task<Role?> GetByNameAsync(string name);
        Task<bool> ExistsAsync(int id);
        Task AssignPermissionsToRoleAsync(int roleId, List<int> permissionIds);
        Task RemovePermissionsFromRoleAsync(int roleId);
        Task<IEnumerable<Permission>> GetRolePermissionsAsync(int roleId);
    }
} 