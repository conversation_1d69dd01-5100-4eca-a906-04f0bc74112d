using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using WaterMeterManagement.Data;
using WaterMeterManagement.Models;
using WaterMeterManagement.Repositories.Interfaces;


namespace WaterMeterManagement.Repositories
{
    public class UserRoleRepository : IUserRoleRepository
    {
        private readonly ApplicationDbContext _context;

        public UserRoleRepository(ApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<IEnumerable<UserRole>> GetByUserIdAsync(int userId)
        {
            return await _context.UserRoles
                .Include(ur => ur.Role)
                .Where(ur => ur.UserId == userId && !ur.IsDeleted)
                .ToListAsync();
        }

        public async Task<IEnumerable<UserRole>> GetByRoleIdAsync(int roleId)
        {
            return await _context.UserRoles
                .Include(ur => ur.User)
                .Where(ur => ur.RoleId == roleId && !ur.IsDeleted)
                .ToListAsync();
        }

        public async Task<UserRole> CreateAsync(UserRole userRole)
        {
            await _context.UserRoles.AddAsync(userRole);
            await _context.SaveChangesAsync();
            return userRole;
        }

        public async Task<string> UpsertUserRole(int userId, int roleId, string updatedBy)
        {
            // Check if UserRole exists (including soft deleted ones)
            var existingUserRole = await _context.UserRoles
                .Include(ur => ur.Role)
                .FirstOrDefaultAsync(ur => ur.UserId == userId && ur.RoleId == roleId);

            if (existingUserRole != null)
            {
                if (existingUserRole.IsDeleted)
                {
                    // Restore soft deleted record
                    existingUserRole.IsDeleted = false;
                    existingUserRole.UpdatedAt = DateTime.UtcNow;
                    existingUserRole.UpdatedBy = updatedBy;
                    await _context.SaveChangesAsync();
                    return $"Role '{existingUserRole.Role.Name}' has been restored and assigned successfully";
                }
                else
                {
                    // Already exists and not deleted
                    return $"Role '{existingUserRole.Role.Name}' was already assigned";
                }
            }
            else
            {
                // Get role name for friendly message
                var role = await _context.Roles.FindAsync(roleId);
                var roleName = role?.Name ?? $"Role ID {roleId}";

                // Create new record
                var newUserRole = new UserRole
                {
                    UserId = userId,
                    RoleId = roleId,
                    CreatedAt = DateTime.UtcNow,
                    CreatedBy = updatedBy,
                    UpdatedAt = DateTime.UtcNow,
                    UpdatedBy = updatedBy,
                    IsDeleted = false
                };

                await _context.UserRoles.AddAsync(newUserRole);
                await _context.SaveChangesAsync();
                return $"Role '{roleName}' has been assigned successfully";
            }
        }

        public async Task DeleteAsync(int userId, int roleId)
        {
            var userRole = await _context.UserRoles
                .FirstOrDefaultAsync(ur => ur.UserId == userId && ur.RoleId == roleId && !ur.IsDeleted);
            
            if (userRole != null)
            {
                userRole.IsDeleted = true;
                await _context.SaveChangesAsync();
            }
        }

        public async Task<bool> ExistsAsync(int userId, int roleId)
        {
            return await _context.UserRoles
                .AnyAsync(ur => ur.UserId == userId && ur.RoleId == roleId && !ur.IsDeleted);
        }
    }
} 