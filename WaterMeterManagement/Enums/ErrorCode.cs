namespace WaterMeterManagement.Enums
{
    public enum ErrorCode
    {
        // General errors (1000-1999)
        UnknownError = 1000,
        ValidationError = 1001,
        DatabaseError = 1002,
        UnauthorizedAccess = 1003,
        
        // User related errors (2000-2999)
        UserNotFound = 2000,
        UserAlreadyExists = 2001,
        InvalidUserCredentials = 2002,
        UserInactive = 2003,
        
        // Role related errors (3000-3999)
        RoleNotFound = 3000,
        RoleAlreadyExists = 3001,
        RoleInUse = 3002,
        InvalidRoleAssignment = 3003,
        
        // Permission related errors (4000-4999)
        PermissionNotFound = 4000,
        PermissionAlreadyExists = 4001,
        InsufficientPermissions = 4002,
        
        // Menu related errors (5000-5999)
        MenuNotFound = 5000,
        MenuCodeAlreadyExists = 5001,
        InvalidMenuStructure = 5002,
        
        // Water meter related errors (6000-6999)
        WaterMeterNotFound = 6000,
        WaterMeterAlreadyExists = 6001,
        InvalidMeterReading = 6002,
        MeterOffline = 6003,
        
        // Task related errors (7000-7999)
        TaskNotFound = 7000,
        TaskAlreadyCompleted = 7001,
        TaskAssignmentFailed = 7002,
        InvalidTaskStatus = 7003,
        
        // Route related errors (8000-8999)
        RouteNotFound = 8000,
        RouteOptimizationFailed = 8001,
        InvalidRouteData = 8002
    }
}
