#!/usr/bin/env pwsh

Write-Host "🧪 Testing Water Meter Management API..." -ForegroundColor Green
Write-Host ""

# Test endpoints
$endpoints = @(
    @{
        Name = "HTTP Swagger (Port 5000)"
        Url = "http://localhost:5000"
        Method = "GET"
    },
    @{
        Name = "HTTPS Swagger (Port 7000)"
        Url = "https://localhost:7000"
        Method = "GET"
    },
    @{
        Name = "API Health Check"
        Url = "http://localhost:5000/api/auth/test-protected"
        Method = "GET"
    }
)

foreach ($endpoint in $endpoints) {
    Write-Host "Testing: $($endpoint.Name)" -ForegroundColor Yellow
    Write-Host "URL: $($endpoint.Url)" -ForegroundColor Gray
    
    try {
        if ($endpoint.Url -like "https://*") {
            # Skip certificate validation for localhost HTTPS
            $response = Invoke-WebRequest -Uri $endpoint.Url -Method $endpoint.Method -SkipCertificateCheck -ErrorAction Stop
        } else {
            $response = Invoke-WebRequest -Uri $endpoint.Url -Method $endpoint.Method -ErrorAction Stop
        }
        
        Write-Host "✅ SUCCESS - Status: $($response.StatusCode)" -ForegroundColor Green
        if ($response.StatusCode -eq 200) {
            Write-Host "📄 Content preview: $($response.Content.Substring(0, [Math]::Min(100, $response.Content.Length)))..." -ForegroundColor Cyan
        }
    }
    catch {
        Write-Host "❌ FAILED - Error: $($_.Exception.Message)" -ForegroundColor Red
    }
    
    Write-Host ""
}

Write-Host "🔗 Try these URLs in your browser:" -ForegroundColor Magenta
Write-Host "HTTP:  http://localhost:5000" -ForegroundColor White
Write-Host "HTTPS: https://localhost:7000" -ForegroundColor White
Write-Host ""
Write-Host "📚 Note: If HTTPS doesn't work, use HTTP for development" -ForegroundColor Yellow 