using Microsoft.AspNetCore.Mvc;
using WaterMeterManagement.DTOs;
using WaterMeterManagement.Services;

namespace WaterMeterManagement.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class TaskController : ControllerBase
    {
        private readonly ITaskService _taskService;
        private readonly ILogger<TaskController> _logger;
        private readonly TransCode _transCode;

        public TaskController(ITaskService taskService, ILogger<TaskController> logger, TransCode transCode)
        {
            _taskService = taskService;
            _logger = logger;
            _transCode = transCode;
        }

        [HttpGet]
        public async Task<ActionResult<TaskSearchResultDto>> GetTasks([FromQuery] TaskSearchDto searchDto)
        {
            try
            {
                var result = await _taskService.GetTasksAsync(searchDto);
                await _transCode.TransAsync(result.Tasks, "taskCategory", "enum", "TaskCategory", "taskCategoryName");
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting tasks");
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<TaskListDto>> GetTask(int id)
        {
            try
            {
                var task = await _taskService.GetTaskByIdAsync(id);
                if (task == null)
                {
                    return NotFound($"Task with ID {id} not found");
                }
                return Ok(task);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting task {Id}", id);
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpPost]
        public async Task<ActionResult<TaskListDto>> CreateTask([FromBody] CreateTaskDto createDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var createdBy = User.Identity?.Name ?? "System";
                var task = await _taskService.CreateTaskAsync(createDto, createdBy);
                return CreatedAtAction(nameof(GetTask), new { id = task.Id }, task);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating task");
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpPut("{id}")]
        public async Task<ActionResult<TaskListDto>> UpdateTask(int id, [FromBody] UpdateTaskDto updateDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                if (id != updateDto.Id)
                {
                    return BadRequest("ID mismatch");
                }

                var updatedBy = User.Identity?.Name ?? "System";
                var task = await _taskService.UpdateTaskAsync(id, updateDto, updatedBy);
                return Ok(task);
            }
            catch (ArgumentException ex)
            {
                return NotFound(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating task {Id}", id);
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpDelete("{id}")]
        public async Task<ActionResult> DeleteTask(int id)
        {
            try
            {
                var deletedBy = User.Identity?.Name ?? "System";
                var result = await _taskService.DeleteTaskAsync(id, deletedBy);
                if (!result)
                {
                    return NotFound($"Task with ID {id} not found");
                }
                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting task {Id}", id);
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpPost("{id}/assign")]
        public async Task<ActionResult> AssignTask(int id, [FromBody] int userId)
        {
            try
            {
                var assignedBy = User.Identity?.Name ?? "System";
                var result = await _taskService.AssignTaskAsync(id, userId, assignedBy);
                if (!result)
                {
                    return NotFound($"Task with ID {id} not found");
                }
                return Ok(new { message = "Task assigned successfully" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error assigning task {Id} to {UserId}", id, userId);
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpPost("bulk-assign")]
        public async Task<ActionResult> BulkAssignTasks([FromBody] BulkAssignmentDto bulkAssignmentDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var assignedBy = User.Identity?.Name ?? "System";
                var result = await _taskService.BulkAssignTasksAsync(bulkAssignmentDto, assignedBy);
                if (!result)
                {
                    return BadRequest("Failed to assign tasks");
                }
                return Ok(new { message = "Tasks assigned successfully" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error bulk assigning tasks");
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpPost("reactive-assign")]
        public async Task<ActionResult<List<TaskListDto>>> ReactiveAssignTasks([FromBody] ReactiveAssignmentDto reactiveDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var assignedBy = User.Identity?.Name ?? "System";
                var assignedTasks = await _taskService.ReactiveAssignmentAsync(reactiveDto, assignedBy);
                return Ok(assignedTasks);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in reactive assignment");
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpPatch("{id}/status")]
        public async Task<ActionResult> UpdateTaskStatus(int id, [FromBody] string status)
        {
            try
            {
                var updatedBy = User.Identity?.Name ?? "System";
                var result = await _taskService.UpdateTaskStatusAsync(id, status, updatedBy);
                if (!result)
                {
                    return NotFound($"Task with ID {id} not found");
                }
                return Ok(new { message = "Task status updated successfully" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating task status {Id}", id);
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpPatch("{id}/progress")]
        public async Task<ActionResult> UpdateTaskProgress(int id, [FromBody] int progressPercentage)
        {
            try
            {
                var updatedBy = User.Identity?.Name ?? "System";
                var result = await _taskService.UpdateTaskProgressAsync(id, progressPercentage, updatedBy);
                if (!result)
                {
                    return NotFound($"Task with ID {id} not found");
                }
                return Ok(new { message = "Task progress updated successfully" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating task progress {Id}", id);
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpGet("{id}/history")]
        public async Task<ActionResult> GetTaskHistory(int id)
        {
            try
            {
                var history = await _taskService.GetTaskHistoryAsync(id);
                return Ok(history);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting task history {Id}", id);
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpGet("export")]
        public async Task<ActionResult> ExportTasks([FromQuery] TaskSearchDto? searchDto = null)
        {
            try
            {
                var fileBytes = await _taskService.ExportTasksAsync(searchDto);
                var fileName = $"tasks_export_{DateTime.UtcNow:yyyyMMdd_HHmmss}.xlsx";
                
                return File(fileBytes, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", fileName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error exporting tasks");
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpPost("import")]
        public async Task<ActionResult> ImportTasks(IFormFile file)
        {
            try
            {
                if (file == null || file.Length == 0)
                {
                    return BadRequest("No file uploaded");
                }

                if (!file.FileName.EndsWith(".xlsx") && !file.FileName.EndsWith(".xls"))
                {
                    return BadRequest("Only Excel files are supported");
                }

                var importedBy = User.Identity?.Name ?? "System";
                using var stream = file.OpenReadStream();
                var result = await _taskService.ImportTasksAsync(stream, importedBy);

                if (result.Success)
                {
                    return Ok(new { message = result.Message, importedCount = result.ImportedCount });
                }
                else
                {
                    return BadRequest(new { message = result.Message });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error importing tasks");
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpGet("statistics")]
        public async Task<ActionResult<TaskStatisticsDto>> GetTaskStatistics()
        {
            try
            {
                var statistics = await _taskService.GetTaskStatisticsAsync();
                return Ok(statistics);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting task statistics");
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpGet("overdue")]
        public async Task<ActionResult<List<OverdueTaskDto>>> GetOverdueTasks()
        {
            try
            {
                var overdueTasks = await _taskService.GetOverdueTasksAsync();
                return Ok(overdueTasks);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting overdue tasks");
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpGet("performance-metrics")]
        public async Task<ActionResult<List<TaskPerformanceMetricsDto>>> GetTaskPerformanceMetrics()
        {
            try
            {
                var metrics = await _taskService.GetTaskPerformanceMetricsAsync();
                return Ok(metrics);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting task performance metrics");
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpGet("work-packages")]
        public async Task<ActionResult<WorkPackageGroupedTasksDto>> GetTasksGroupedByWorkPackage(
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 3,
            [FromQuery] int? assignedUserId = null,
            [FromQuery] string? workPackageFilter = null,
            [FromQuery] string? status = null,
            [FromQuery] string? priority = null)
        {
            try
            {
                // Fix for assignedUserId parameter handling
                // If assignedUserId parameter exists in query but is 0, treat it as 0 for unassigned filter
                if (Request.Query.ContainsKey("assignedUserId") && assignedUserId == null)
                {
                    assignedUserId = 0; // Convert null to 0 to trigger unassigned filter
                }

                var result = await _taskService.GetTasksGroupedByWorkPackageAsync(new TaskWorkPackageSearchDto
                {
                    Page = page,
                    PageSize = pageSize,
                    AssignedUserId = assignedUserId,
                    WorkPackageFilter = workPackageFilter,
                    Status = status,
                    Priority = priority
                });
                
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting tasks grouped by work package");
                return StatusCode(500, "Internal server error");
            }
        }

        #region Enhanced Task Assignment Endpoints

        /// <summary>
        /// Create a task assignment with validation
        /// </summary>
        [HttpPost("assignments")]
        public async Task<ActionResult<AssignmentResultDto>> CreateTaskAssignment([FromBody] CreateTaskAssignmentDto assignmentDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var assignedBy = User.Identity?.Name ?? "System";
                var result = await _taskService.CreateTaskAssignmentAsync(assignmentDto, assignedBy);
                
                if (!result.Success)
                {
                    return BadRequest(result);
                }

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating task assignment");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Get task assignments (filtered by task or user)
        /// </summary>
        [HttpGet("assignments")]
        public async Task<ActionResult<List<TaskAssignmentDto>>> GetTaskAssignments([FromQuery] int? taskId = null, [FromQuery] int? userId = null)
        {
            try
            {
                var assignments = await _taskService.GetTaskAssignmentsAsync(taskId, userId);
                return Ok(assignments);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting task assignments");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Validate task assignment before creating it
        /// </summary>
        [HttpPost("{taskId}/validate-assignment")]
        public async Task<ActionResult<TaskAssignmentValidationDto>> ValidateTaskAssignment(int taskId, [FromBody] int userId)
        {
            try
            {
                var validation = await _taskService.ValidateTaskAssignmentAsync(taskId, userId);
                return Ok(validation);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating task assignment");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Get workload summary for all users
        /// </summary>
        [HttpGet("users/workload")]
        public async Task<ActionResult<List<UserWorkloadSummaryDto>>> GetUserWorkloadSummary()
        {
            try
            {
                var workloadSummary = await _taskService.GetUserWorkloadSummaryAsync();
                return Ok(workloadSummary);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting user workload summary");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Get workload details for a specific user
        /// </summary>
        [HttpGet("users/{userId}/workload")]
        public async Task<ActionResult<UserWorkloadSummaryDto>> GetUserWorkload(int userId)
        {
            try
            {
                var workload = await _taskService.GetUserWorkloadAsync(userId);
                if (workload == null)
                {
                    return NotFound($"User with ID {userId} not found");
                }
                return Ok(workload);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting user workload for user {UserId}", userId);
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Accept a task assignment
        /// </summary>
        [HttpPost("assignments/{assignmentId}/accept")]
        public async Task<ActionResult> AcceptTaskAssignment(int assignmentId)
        {
            try
            {
                var acceptedBy = User.Identity?.Name ?? "System";
                var result = await _taskService.AcceptTaskAssignmentAsync(assignmentId, acceptedBy);
                
                if (!result)
                {
                    return NotFound($"Assignment with ID {assignmentId} not found");
                }

                return Ok(new { message = "Task assignment accepted successfully" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error accepting task assignment {AssignmentId}", assignmentId);
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Reject a task assignment
        /// </summary>
        [HttpPost("assignments/{assignmentId}/reject")]
        public async Task<ActionResult> RejectTaskAssignment(int assignmentId, [FromBody] RejectAssignmentRequest request)
        {
            try
            {
                if (string.IsNullOrEmpty(request.Reason))
                {
                    return BadRequest("Rejection reason is required");
                }

                var rejectedBy = User.Identity?.Name ?? "System";
                var result = await _taskService.RejectTaskAssignmentAsync(assignmentId, rejectedBy, request.Reason);
                
                if (!result)
                {
                    return NotFound($"Assignment with ID {assignmentId} not found");
                }

                return Ok(new { message = "Task assignment rejected successfully" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error rejecting task assignment {AssignmentId}", assignmentId);
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Enhanced bulk assignment with validation and detailed results
        /// </summary>
        [HttpPost("bulk-assign-enhanced")]
        public async Task<ActionResult<AssignmentResultDto>> BulkAssignTasksEnhanced([FromBody] BulkAssignmentEnhancedRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                if (request.TaskIds == null || !request.TaskIds.Any())
                {
                    return BadRequest("At least one task ID is required");
                }

                var assignedBy = User.Identity?.Name ?? "System";
                var result = await _taskService.BulkAssignTasksEnhancedAsync(
                    request.TaskIds, 
                    request.UserId, 
                    request.AssignmentType ?? "Bulk", 
                    assignedBy, 
                    request.Reason
                );

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in enhanced bulk assignment");
                return StatusCode(500, "Internal server error");
            }
        }

        #endregion
    }

    #region Request Models

    public class RejectAssignmentRequest
    {
        public string Reason { get; set; } = string.Empty;
    }

    public class BulkAssignmentEnhancedRequest
    {
        public List<int> TaskIds { get; set; } = new();
        public int UserId { get; set; }
        public string? AssignmentType { get; set; }
        public string? Reason { get; set; }
    }

    #endregion
} 