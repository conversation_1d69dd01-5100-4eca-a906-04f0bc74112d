using Microsoft.AspNetCore.Mvc;
using WaterMeterManagement.DTOs;
using WaterMeterManagement.Services.Interfaces;

namespace WaterMeterManagement.Controllers
{
    [ApiController]
    [Route("api/frequency-templates")]
    public class FrequencyTemplateController : ControllerBase
    {
        private readonly IFrequencyTemplateService _frequencyTemplateService;
        private readonly ILogger<FrequencyTemplateController> _logger;

        public FrequencyTemplateController(
            IFrequencyTemplateService frequencyTemplateService,
            ILogger<FrequencyTemplateController> logger)
        {
            _frequencyTemplateService = frequencyTemplateService;
            _logger = logger;
        }

        [HttpGet]
        public async Task<ActionResult<object>> GetFrequencyTemplates([FromQuery] FrequencyTemplateSearchDto searchDto)
        {
            try
            {
                var (templates, totalCount) = await _frequencyTemplateService.GetFrequencyTemplatesAsync(searchDto);

                return Ok(new
                {
                    templates = templates,
                    totalCount = totalCount,
                    pageNumber = searchDto.Page,
                    pageSize = searchDto.PageSize,
                    totalPages = (int)Math.Ceiling((double)totalCount / searchDto.PageSize)
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving frequency templates");
                return StatusCode(500, new { Message = "Internal server error" });
            }
        }

        [HttpGet("{id:int}")]
        public async Task<ActionResult<FrequencyTemplateDto>> GetFrequencyTemplate(int id)
        {
            try
            {
                var template = await _frequencyTemplateService.GetFrequencyTemplateByIdAsync(id);
                if (template == null)
                {
                    return NotFound(new { Message = "Frequency template not found" });
                }

                return Ok(template);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving frequency template {TemplateId}", id);
                return StatusCode(500, new { Message = "Internal server error" });
            }
        }

        [HttpPost]
        public async Task<ActionResult<FrequencyTemplateDto>> CreateFrequencyTemplate([FromBody] CreateFrequencyTemplateDto createDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                // Validate template before creation
                var validationErrors = await _frequencyTemplateService.ValidateTemplateAsync(createDto);
                if (validationErrors.Any())
                {
                    return BadRequest(new { Message = "Validation failed", Errors = validationErrors });
                }

                var template = await _frequencyTemplateService.CreateFrequencyTemplateAsync(createDto);
                return CreatedAtAction(nameof(GetFrequencyTemplate), new { id = template.Id }, template);
            }
            catch (Microsoft.EntityFrameworkCore.DbUpdateException ex) when (ex.InnerException?.Message.Contains("duplicate key") == true)
            {
                _logger.LogWarning(ex, "Duplicate frequency template name: {Name}", createDto.Name);
                return BadRequest(new { Message = "A template with this name already exists" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating frequency template");
                return StatusCode(500, new { Message = "Internal server error" });
            }
        }

        [HttpPut("{id:int}")]
        public async Task<ActionResult<FrequencyTemplateDto>> UpdateFrequencyTemplate(int id, [FromBody] UpdateFrequencyTemplateDto updateDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var template = await _frequencyTemplateService.UpdateFrequencyTemplateAsync(id, updateDto);
                if (template == null)
                {
                    return NotFound(new { Message = "Frequency template not found" });
                }

                return Ok(template);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating frequency template {TemplateId}", id);
                return StatusCode(500, new { Message = "Internal server error" });
            }
        }

        [HttpDelete("{id:int}")]
        public async Task<ActionResult> DeleteFrequencyTemplate(int id)
        {
            try
            {
                var success = await _frequencyTemplateService.DeleteFrequencyTemplateAsync(id);
                if (!success)
                {
                    return NotFound(new { Message = "Frequency template not found" });
                }

                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting frequency template {TemplateId}", id);
                return StatusCode(500, new { Message = "Internal server error" });
            }
        }

        [HttpPost("{id:int}/duplicate")]
        public async Task<ActionResult<FrequencyTemplateDto>> DuplicateFrequencyTemplate(int id, [FromBody] DuplicateFrequencyTemplateDto duplicateDto)
        {
            try
            {
                var duplicatedTemplate = await _frequencyTemplateService.DuplicateFrequencyTemplateAsync(id, duplicateDto.Name);
                if (duplicatedTemplate == null)
                {
                    return NotFound(new { Message = "Source template not found" });
                }

                return Ok(duplicatedTemplate);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error duplicating frequency template {TemplateId}", id);
                return StatusCode(500, new { Message = "Internal server error" });
            }
        }

        [HttpPost("{id:int}/calculate")]
        public async Task<ActionResult<FrequencyCalculationResultDto>> CalculateFrequency(int id, [FromBody] CalculateFrequencyRequestDto requestDto)
        {
            try
            {
                var template = await _frequencyTemplateService.GetFrequencyTemplateByIdAsync(id);
                if (template == null)
                {
                    return NotFound(new { Message = "Frequency template not found" });
                }

                var calculationDto = new FrequencyCalculationDto
                {
                    StartDate = DateTime.Parse(requestDto.StartDate),
                    EndDate = DateTime.Parse(requestDto.EndDate),
                    FrequencyType = template.FrequencyType,
                    IntervalValue = template.IntervalValue,
                    IntervalUnit = template.IntervalUnit,
                    DayOfWeek = template.DayOfWeek,
                    DayOfMonth = template.DayOfMonth,
                    MonthOfYear = template.MonthOfYear,
                    TimeOfDay = template.TimeOfDay,
                    MaxOccurrences = 100
                };

                var result = await _frequencyTemplateService.CalculateFrequencyAsync(calculationDto);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calculating frequency for template {TemplateId}", id);
                return StatusCode(500, new { Message = "Internal server error" });
            }
        }

        [HttpGet("statistics")]
        public async Task<ActionResult<FrequencyTemplateStatisticsDto>> GetStatistics()
        {
            try
            {
                var statistics = await _frequencyTemplateService.GetFrequencyTemplateStatisticsAsync();
                return Ok(statistics);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving frequency template statistics");
                return StatusCode(500, new { Message = "Internal server error" });
            }
        }
    }
} 