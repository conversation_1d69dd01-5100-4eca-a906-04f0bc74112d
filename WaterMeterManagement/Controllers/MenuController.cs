using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using WaterMeterManagement.Models;
using WaterMeterManagement.Services.Interfaces;

namespace WaterMeterManagement.Controllers
{
    [Authorize]
    [ApiController]
    [Route("api/[controller]")]
    public class MenuController : ControllerBase
    {
        private readonly IMenuService _menuService;
        private readonly ILogger<MenuController> _logger;

        public MenuController(IMenuService menuService, ILogger<MenuController> logger)
        {
            _menuService = menuService;
            _logger = logger;
        }

        [HttpGet]
        public async Task<ActionResult<IEnumerable<Menu>>> GetAllMenus()
        {
            var menus = await _menuService.GetAllMenusAsync();
            return Ok(menus);
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<Menu>> GetMenuById(int id)
        {
            var menu = await _menuService.GetMenuByIdAsync(id);
            if (menu == null)
            {
                return NotFound();
            }
            return Ok(menu);
        }

        [HttpGet("code/{code}")]
        public async Task<ActionResult<Menu>> GetMenuByCode(string code)
        {
            var menu = await _menuService.GetMenuByCodeAsync(code);
            if (menu == null)
            {
                return NotFound();
            }
            return Ok(menu);
        }

        [HttpGet("user/{userId}")]
        public async Task<ActionResult<IEnumerable<Menu>>> GetUserMenus(int userId)
        {
            var menus = await _menuService.GetUserMenusAsync(userId);
            return Ok(menus);
        }

        [HttpPost]
        [Authorize(Roles = "Administrator")]
        public async Task<ActionResult<Menu>> CreateMenu(Menu menu)
        {
            try
            {
                var createdMenu = await _menuService.CreateMenuAsync(menu);
                return CreatedAtAction(nameof(GetMenuById), new { id = createdMenu.Id }, createdMenu);
            }
            catch (System.Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpPut("{id}")]
        [Authorize(Roles = "Administrator")]
        public async Task<IActionResult> UpdateMenu(int id, Menu menu)
        {
            // Ensure the menu ID matches the URL parameter
            menu.Id = id;

            try
            {
                await _menuService.UpdateMenuAsync(menu);
                return NoContent();
            }
            catch (System.Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpDelete("{id}")]
        [Authorize(Roles = "Administrator")]
        public async Task<IActionResult> DeleteMenu(int id)
        {
            try
            {
                await _menuService.DeleteMenuAsync(id);
                return NoContent();
            }
            catch (System.Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpGet("validate-code/{code}")]
        public async Task<ActionResult<bool>> ValidateMenuCode(string code, [FromQuery] int? excludeId)
        {
            var isValid = await _menuService.ValidateMenuCodeAsync(code, excludeId);
            return Ok(isValid);
        }
    }
} 