using Microsoft.AspNetCore.Mvc;
using WaterMeterManagement.Services;
using WaterMeterManagement.Services.Interfaces;

namespace WaterMeterManagement.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class R2TestController : ControllerBase
    {
        private readonly ICloudflareR2Service _r2Service;
        private readonly ILogger<R2TestController> _logger;

        public R2TestController(ICloudflareR2Service r2Service, ILogger<R2TestController> logger)
        {
            _r2Service = r2Service;
            _logger = logger;
        }

        [HttpGet("connection")]
        public async Task<IActionResult> TestConnection()
        {
            try
            {
                _logger.LogInformation("Testing R2 connection...");

                // Test by trying to list objects (this will fail if connection is bad)
                var testKey = $"test/connection-test-{DateTime.UtcNow:yyyyMMdd-HHmmss}.txt";

                // Create a small test file
                var testContent = $"R2 Connection Test - {DateTime.UtcNow}";
                var testBytes = System.Text.Encoding.UTF8.GetBytes(testContent);
                using var testStream = new MemoryStream(testBytes);

                // Try to upload test file
                var uploadedUrl = await _r2Service.UploadPhotoAsync(testStream, testKey, "text/plain");

                if (!string.IsNullOrEmpty(uploadedUrl))
                {
                    _logger.LogInformation("R2 connection test successful");

                    // Extract key from URL for further operations
                    var uploadedKey = _r2Service.ExtractKeyFromUrl(uploadedUrl);

                    // Try to generate a presigned URL
                    var presignedUrl = await _r2Service.GeneratePresignedUrlAsync(uploadedKey, TimeSpan.FromMinutes(5));

                    // Try to delete the test file
                    await _r2Service.DeletePhotoAsync(uploadedKey);

                    return Ok(new
                    {
                        success = true,
                        message = "R2 connection successful",
                        uploadedUrl = uploadedUrl,
                        uploadedKey = uploadedKey,
                        presignedUrl = presignedUrl,
                        timestamp = DateTime.UtcNow
                    });
                }
                else
                {
                    _logger.LogError("R2 connection test failed: Upload returned empty URL");
                    return BadRequest(new
                    {
                        success = false,
                        message = "R2 connection failed",
                        error = "Upload returned empty URL"
                    });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error testing R2 connection");
                return StatusCode(500, new
                {
                    success = false,
                    message = "R2 connection test error",
                    error = ex.Message,
                    stackTrace = ex.StackTrace
                });
            }
        }

        [HttpGet("bucket-info")]
        public async Task<IActionResult> GetBucketInfo()
        {
            try
            {
                _logger.LogInformation("Getting R2 bucket information...");

                // Simple test without actual operations
                return Ok(new
                {
                    success = true,
                    message = "R2 service configured",
                    endpoint = "https://13b75f12df74cacdb8cbc18db2cf27a8.r2.cloudflarestorage.com",
                    bucket = "water-meter-photos",
                    testPerformed = DateTime.UtcNow
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting bucket info");
                return StatusCode(500, new
                {
                    success = false,
                    message = "Error accessing bucket",
                    error = ex.Message
                });
            }
        }

        [HttpGet("simple-test")]
        public async Task<IActionResult> SimpleTest()
        {
            try
            {
                _logger.LogInformation("Performing simple R2 test...");

                // Test with a very simple operation
                using var httpClient = new HttpClient();
                var endpoint = "https://13b75f12df74cacdb8cbc18db2cf27a8.r2.cloudflarestorage.com";

                // Try to access the bucket root (this should return 403 or similar, but not connection error)
                var response = await httpClient.GetAsync($"{endpoint}/water-meter-photos/");

                return Ok(new
                {
                    success = true,
                    message = "HTTP connection test completed",
                    statusCode = (int)response.StatusCode,
                    statusDescription = response.StatusCode.ToString(),
                    endpoint = endpoint,
                    timestamp = DateTime.UtcNow
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in simple test");
                return StatusCode(500, new
                {
                    success = false,
                    message = "Simple test error",
                    error = ex.Message
                });
            }
        }

        [HttpPost("upload-test")]
        public async Task<IActionResult> TestUpload([FromForm] IFormFile file)
        {
            try
            {
                if (file == null || file.Length == 0)
                {
                    return BadRequest(new { success = false, message = "No file provided" });
                }

                _logger.LogInformation("Testing file upload to R2...");

                var uploadedUrl = await _r2Service.UploadPhotoAsync(file);

                if (!string.IsNullOrEmpty(uploadedUrl))
                {
                    var uploadedKey = _r2Service.ExtractKeyFromUrl(uploadedUrl);
                    var presignedUrl = await _r2Service.GeneratePresignedUrlAsync(uploadedKey, TimeSpan.FromHours(1));

                    return Ok(new
                    {
                        success = true,
                        message = "File uploaded successfully",
                        key = uploadedKey,
                        url = uploadedUrl,
                        presignedUrl = presignedUrl,
                        size = file.Length,
                        contentType = file.ContentType
                    });
                }
                else
                {
                    return BadRequest(new
                    {
                        success = false,
                        message = "Upload failed",
                        error = "Upload returned empty URL"
                    });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error testing file upload");
                return StatusCode(500, new
                {
                    success = false,
                    message = "Upload test error",
                    error = ex.Message
                });
            }
        }
    }
}
