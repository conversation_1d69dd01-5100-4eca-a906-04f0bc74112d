using Microsoft.AspNetCore.Mvc;
using WaterMeterManagement.DTOs;
using WaterMeterManagement.DTOs.Common;
using WaterMeterManagement.Services.Interfaces;

namespace WaterMeterManagement.Controllers
{
    [ApiController]
    [Route("api/baseline")]
    public class BaselineController : ControllerBase
    {
        private readonly IBaselineService _baselineService;
        private readonly ILogger<BaselineController> _logger;

        public BaselineController(
            IBaselineService baselineService,
            ILogger<BaselineController> logger)
        {
            _baselineService = baselineService;
            _logger = logger;
        }

        [HttpGet]
        public async Task<ActionResult<PaginatedResponse<BaselineRecordListDto>>> GetBaselineRecords([FromQuery] BaselineSearchDto searchDto)
        {
            try
            {
                var (records, totalCount) = await _baselineService.GetBaselineRecordsAsync(searchDto);
                var response = PaginatedResponse<BaselineRecordListDto>.Create(records, totalCount, searchDto.Page, searchDto.PageSize);

                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving baseline records");
                return StatusCode(500, new { Message = "Internal server error" });
            }
        }

        [HttpGet("{id:int}")]
        public async Task<ActionResult<BaselineRecordDto>> GetBaselineRecord(int id)
        {
            try
            {
                var record = await _baselineService.GetBaselineRecordByIdAsync(id);
                if (record == null)
                {
                    return NotFound(new { Message = "Baseline record not found" });
                }

                return Ok(record);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving baseline record {BaselineId}", id);
                return StatusCode(500, new { Message = "Internal server error" });
            }
        }

        [HttpPost]
        public async Task<ActionResult<BaselineRecordDto>> CreateBaselineRecord([FromBody] CreateBaselineRecordDto createDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var record = await _baselineService.CreateBaselineRecordAsync(createDto);
                return CreatedAtAction(nameof(GetBaselineRecord), new { id = record.Id }, record);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating baseline record");
                return StatusCode(500, new { Message = "Internal server error" });
            }
        }

        [HttpPut("{id:int}")]
        public async Task<ActionResult<BaselineRecordDto>> UpdateBaselineRecord(int id, [FromBody] UpdateBaselineRecordDto updateDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var record = await _baselineService.UpdateBaselineRecordAsync(id, updateDto);
                if (record == null)
                {
                    return NotFound(new { Message = "Baseline record not found" });
                }

                return Ok(record);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating baseline record {BaselineId}", id);
                return StatusCode(500, new { Message = "Internal server error" });
            }
        }

        [HttpDelete("{id:int}")]
        public async Task<ActionResult> DeleteBaselineRecord(int id)
        {
            try
            {
                var success = await _baselineService.DeleteBaselineRecordAsync(id);
                if (!success)
                {
                    return NotFound(new { Message = "Baseline record not found" });
                }

                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting baseline record {BaselineId}", id);
                return StatusCode(500, new { Message = "Internal server error" });
            }
        }

        [HttpPost("import/csv")]
        public async Task<ActionResult<BaselineImportResultDto>> ImportFromCsv(IFormFile file)
        {
            try
            {
                if (file == null || file.Length == 0)
                {
                    return BadRequest(new { Message = "No file provided" });
                }

                if (!file.FileName.EndsWith(".csv", StringComparison.OrdinalIgnoreCase))
                {
                    return BadRequest(new { Message = "File must be a CSV file" });
                }

                using var stream = file.OpenReadStream();
                var result = await _baselineService.ImportFromCsvAsync(stream, file.FileName);
                
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error importing baseline records from CSV");
                return StatusCode(500, new { Message = "Internal server error" });
            }
        }

        [HttpPost("import/excel")]
        public async Task<ActionResult<BaselineImportResultDto>> ImportFromExcel(IFormFile file)
        {
            try
            {
                if (file == null || file.Length == 0)
                {
                    return BadRequest(new { Message = "No file provided" });
                }

                if (!file.FileName.EndsWith(".xlsx", StringComparison.OrdinalIgnoreCase) &&
                    !file.FileName.EndsWith(".xls", StringComparison.OrdinalIgnoreCase))
                {
                    return BadRequest(new { Message = "File must be an Excel file" });
                }

                using var stream = file.OpenReadStream();
                var result = await _baselineService.ImportFromExcelAsync(stream, file.FileName);
                
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error importing baseline records from Excel");
                return StatusCode(500, new { Message = "Internal server error" });
            }
        }

        [HttpPost("import/sdc")]
        public async Task<ActionResult<BaselineImportResultDto>> ImportFromSdcFile(IFormFile file)
        {
            try
            {
                if (file == null || file.Length == 0)
                {
                    return BadRequest(new { Message = "No file provided" });
                }

                var allowedExtensions = new[] { ".xlsx", ".xls", ".csv" };
                var fileExtension = Path.GetExtension(file.FileName).ToLowerInvariant();
                
                if (!allowedExtensions.Contains(fileExtension))
                {
                    return BadRequest(new { Message = "File must be an Excel (.xlsx, .xls) or CSV (.csv) file" });
                }

                using var stream = file.OpenReadStream();
                var result = await _baselineService.ImportFromSdcFileAsync(stream, file.FileName);
                
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error importing baseline records from SDC file");
                return StatusCode(500, new { Message = "Internal server error" });
            }
        }

        [HttpPost("validate-import")]
        public async Task<ActionResult<List<BaselineImportDto>>> ValidateImportData([FromBody] List<BaselineImportDto> importData)
        {
            try
            {
                var validatedData = await _baselineService.ValidateImportDataAsync(importData);
                return Ok(validatedData);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating import data");
                return StatusCode(500, new { Message = "Internal server error" });
            }
        }

        [HttpGet("meter/{meterId:int}/history")]
        public async Task<ActionResult<BaselineHistoryDto>> GetMeterBaselineHistory(int meterId)
        {
            try
            {
                var history = await _baselineService.GetMeterBaselineHistoryAsync(meterId);
                return Ok(history);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving baseline history for meter {MeterId}", meterId);
                return StatusCode(500, new { Message = "Internal server error" });
            }
        }

        [HttpGet("anomalous")]
        public async Task<ActionResult<List<BaselineAnomalyDto>>> GetAnomalousBaselines()
        {
            try
            {
                var anomalies = await _baselineService.GetAnomalousBaselinesAsync();
                return Ok(anomalies);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving anomalous baselines");
                return StatusCode(500, new { Message = "Internal server error" });
            }
        }

        [HttpGet("unvalidated")]
        public async Task<ActionResult<List<BaselineRecordListDto>>> GetUnvalidatedBaselines()
        {
            try
            {
                var records = await _baselineService.GetUnvalidatedBaselinesAsync();
                return Ok(records);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving unvalidated baselines");
                return StatusCode(500, new { Message = "Internal server error" });
            }
        }

        [HttpPost("{id:int}/validate")]
        public async Task<ActionResult> ValidateBaseline(int id, [FromBody] BaselineValidationDto validationDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                validationDto.BaselineId = id;
                var success = await _baselineService.ValidateBaselineAsync(validationDto);
                if (!success)
                {
                    return NotFound(new { Message = "Baseline record not found" });
                }

                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating baseline {BaselineId}", id);
                return StatusCode(500, new { Message = "Internal server error" });
            }
        }

        [HttpPost("{id:int}/correct")]
        public async Task<ActionResult> CorrectBaseline(int id, [FromBody] BaselineCorrectionDto correctionDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                correctionDto.BaselineId = id;
                var success = await _baselineService.CorrectBaselineAsync(correctionDto, "Manual");
                if (!success)
                {
                    return NotFound(new { Message = "Baseline record not found" });
                }

                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error correcting baseline {BaselineId}", id);
                return StatusCode(500, new { Message = "Internal server error" });
            }
        }

        [HttpGet("import-batch/{importBatch}")]
        public async Task<ActionResult<List<BaselineRecordListDto>>> GetBaselinesByImportBatch(string importBatch)
        {
            try
            {
                var records = await _baselineService.GetBaselinesByImportBatchAsync(importBatch);
                return Ok(records);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving baselines by import batch {ImportBatch}", importBatch);
                return StatusCode(500, new { Message = "Internal server error" });
            }
        }

        [HttpGet("statistics")]
        public async Task<ActionResult<Dictionary<string, object>>> GetBaselineStatistics()
        {
            try
            {
                var statistics = await _baselineService.GetBaselineStatisticsAsync();
                return Ok(statistics);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving baseline statistics");
                return StatusCode(500, new { Message = "Internal server error" });
            }
        }

        [HttpGet("recent")]
        public async Task<ActionResult<List<BaselineRecordListDto>>> GetRecentBaselines([FromQuery] int days = 7)
        {
            try
            {
                var records = await _baselineService.GetRecentBaselinesAsync(days);
                return Ok(records);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving recent baselines");
                return StatusCode(500, new { Message = "Internal server error" });
            }
        }

        [HttpGet("export")]
        public async Task<ActionResult> ExportBaselines([FromQuery] BaselineSearchDto searchDto)
        {
            try
            {
                var (records, _) = await _baselineService.GetBaselineRecordsAsync(searchDto);
                var excelData = await _baselineService.ExportBaselinesAsync(records);
                
                var fileName = $"baseline_export_{DateTime.Now:yyyyMMdd_HHmmss}.xlsx";
                return File(excelData, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", fileName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error exporting baseline records");
                return StatusCode(500, new { Message = "Internal server error" });
            }
        }

        [HttpGet("template/excel")]
        public async Task<ActionResult> DownloadExcelTemplate()
        {
            try
            {
                var templateData = await _baselineService.GenerateExcelTemplateAsync();
                var fileName = $"baseline_import_template_{DateTime.Now:yyyyMMdd}.xlsx";
                
                return File(templateData, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", fileName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating Excel template");
                return StatusCode(500, new { Message = "Internal server error" });
            }
        }

        [HttpGet("templates/sdc-sample")]
        public async Task<ActionResult> DownloadSdcImportTemplate()
        {
            try
            {
                var templateData = await _baselineService.GenerateSdcImportTemplateAsync();
                var fileName = $"SDC_Baseline_Import_Template_{DateTime.Now:yyyyMMdd}.xlsx";
                
                return File(templateData, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", fileName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating SDC template");
                return StatusCode(500, new { Message = "Internal server error" });
            }
        }

        [HttpGet("import/sdc/history")]
        public async Task<ActionResult<object>> GetSdcImportHistory([FromQuery] int page = 1, [FromQuery] int pageSize = 10)
        {
            try
            {
                var (imports, totalCount) = await _baselineService.GetSdcImportHistoryAsync(page, pageSize);
                
                return Ok(new
                {
                    Data = imports,
                    TotalCount = totalCount,
                    Page = page,
                    PageSize = pageSize,
                    TotalPages = (int)Math.Ceiling((double)totalCount / pageSize)
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving SDC import history");
                return StatusCode(500, new { Message = "Internal server error" });
            }
        }

        [HttpPost("import/sdc/validate")]
        public async Task<ActionResult<object>> ValidateSdcFile(IFormFile file)
        {
            try
            {
                if (file == null || file.Length == 0)
                {
                    return BadRequest(new { Message = "No file provided" });
                }

                var allowedExtensions = new[] { ".xlsx", ".xls", ".csv" };
                var fileExtension = Path.GetExtension(file.FileName).ToLowerInvariant();
                
                if (!allowedExtensions.Contains(fileExtension))
                {
                    return BadRequest(new { Message = "File must be an Excel (.xlsx, .xls) or CSV (.csv) file" });
                }

                using var stream = file.OpenReadStream();
                var validation = await _baselineService.ValidateSdcFileAsync(stream, file.FileName);
                
                return Ok(validation);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating SDC file");
                return StatusCode(500, new { Message = "Internal server error" });
            }
        }

        [HttpPost("batch-validate")]
        public async Task<ActionResult> BatchValidateBaselines([FromBody] BatchValidationDto batchDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var result = await _baselineService.BatchValidateBaselinesAsync(batchDto);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error batch validating baselines");
                return StatusCode(500, new { Message = "Internal server error" });
            }
        }

        [HttpPost("{id:int}/mark-anomalous")]
        public async Task<ActionResult> MarkBaselineAsAnomalous(int id, [FromBody] MarkAnomalousDto markDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var success = await _baselineService.MarkBaselineAsAnomalousAsync(id, markDto.Description, "Manual");
                if (!success)
                {
                    return NotFound(new { Message = "Baseline record not found" });
                }

                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error marking baseline as anomalous {BaselineId}", id);
                return StatusCode(500, new { Message = "Internal server error" });
            }
        }

        [HttpPost("meter/{meterId:int}/calculate-variances")]
        public async Task<ActionResult> CalculateVariances(int meterId)
        {
            try
            {
                await _baselineService.CalculateVariancesAsync(meterId);
                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calculating variances for meter {MeterId}", meterId);
                return StatusCode(500, new { Message = "Internal server error" });
            }
        }

        [HttpGet("meter/{meterId:int}/superseded")]
        public async Task<ActionResult<List<BaselineRecordDto>>> GetSupersededBaselines(int meterId)
        {
            try
            {
                var records = await _baselineService.GetSupersededBaselinesAsync(meterId);
                return Ok(records);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving superseded baselines for meter {MeterId}", meterId);
                return StatusCode(500, new { Message = "Internal server error" });
            }
        }
    }

    public class MarkAnomalousDto
    {
        public string Description { get; set; } = string.Empty;
    }
} 