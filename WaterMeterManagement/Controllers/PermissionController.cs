using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using WaterMeterManagement.Models;
using WaterMeterManagement.Repositories.Interfaces;

namespace WaterMeterManagement.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class PermissionController : ControllerBase
    {
        private readonly IPermissionRepository _permissionRepository;

        public PermissionController(IPermissionRepository permissionRepository)
        {
            _permissionRepository = permissionRepository;
        }

        [HttpGet]
        public async Task<ActionResult<IEnumerable<Permission>>> GetAllPermissions()
        {
            try
            {
                var permissions = await _permissionRepository.GetAllPermissionsAsync();
                return Ok(permissions);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Internal server error: {ex.Message}");
            }
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<Permission>> GetPermission(int id)
        {
            try
            {
                var permission = await _permissionRepository.GetByIdAsync(id);
                if (permission == null)
                {
                    return NotFound($"Permission with ID {id} not found.");
                }
                return Ok(permission);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Internal server error: {ex.Message}");
            }
        }

        [HttpPost]
        public async Task<ActionResult<Permission>> CreatePermission([FromBody] Permission permission)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                // Check if permission code already exists
                if (await _permissionRepository.PermissionExistsAsync(permission.Code))
                {
                    return Conflict($"Permission with code '{permission.Code}' already exists.");
                }

                permission.CreatedAt = DateTime.UtcNow;
                permission.UpdatedAt = DateTime.UtcNow;
                permission.CreatedBy = "System"; // TODO: Get from current user context
                permission.UpdatedBy = "System";

                var createdPermission = await _permissionRepository.AddAsync(permission);
                return CreatedAtAction(nameof(GetPermission), new { id = createdPermission.Id }, createdPermission);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Internal server error: {ex.Message}");
            }
        }

        [HttpPut("{id}")]
        public async Task<ActionResult<Permission>> UpdatePermission(int id, [FromBody] Permission permission)
        {
            try
            {
                if (id != permission.Id)
                {
                    return BadRequest("Permission ID mismatch.");
                }

                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var existingPermission = await _permissionRepository.GetByIdAsync(id);
                if (existingPermission == null)
                {
                    return NotFound($"Permission with ID {id} not found.");
                }

                // Check if the new code conflicts with another permission
                var allPermissions = await _permissionRepository.GetAllAsync();
                if (allPermissions.Any(p => p.Code.ToLower() == permission.Code.ToLower() && p.Id != permission.Id))
                {
                    return Conflict($"Permission with code '{permission.Code}' already exists.");
                }

                existingPermission.Name = permission.Name;
                existingPermission.Code = permission.Code;
                existingPermission.Description = permission.Description;
                existingPermission.Module = permission.Module;
                existingPermission.UpdatedAt = DateTime.UtcNow;
                existingPermission.UpdatedBy = "System"; // TODO: Get from current user context

                var updatedPermission = await _permissionRepository.UpdateAsync(existingPermission);
                return Ok(updatedPermission);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Internal server error: {ex.Message}");
            }
        }

        [HttpDelete("{id}")]
        public async Task<ActionResult> DeletePermission(int id)
        {
            try
            {
                var permission = await _permissionRepository.GetByIdAsync(id);
                if (permission == null)
                {
                    return NotFound($"Permission with ID {id} not found.");
                }

                // TODO: Check if permission is assigned to any roles before deletion
                await _permissionRepository.DeleteAsync(id);
                return NoContent();
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Internal server error: {ex.Message}");
            }
        }

        [HttpGet("modules")]
        public async Task<ActionResult<IEnumerable<string>>> GetModules()
        {
            try
            {
                var permissions = await _permissionRepository.GetAllPermissionsAsync();
                var modules = permissions
                    .Select(p => p.Module)
                    .Where(m => !string.IsNullOrEmpty(m))
                    .Distinct()
                    .OrderBy(m => m)
                    .ToList();
                
                return Ok(modules);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Internal server error: {ex.Message}");
            }
        }

        [HttpGet("by-module/{module}")]
        public async Task<ActionResult<IEnumerable<Permission>>> GetPermissionsByModule(string module)
        {
            try
            {
                var permissions = await _permissionRepository.GetPermissionsByModuleAsync(module);
                return Ok(permissions);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Internal server error: {ex.Message}");
            }
        }
    }
} 