using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using WaterMeterManagement.DTOs;
using WaterMeterManagement.Services.Interfaces;

namespace WaterMeterManagement.Controllers
{
    /// <summary>
    /// Staff synchronization controller
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class StaffSyncController : ControllerBase
    {
        private readonly IStaffSyncService _staffSyncService;
        private readonly ILogger<StaffSyncController> _logger;

        public StaffSyncController(
            IStaffSyncService staffSyncService,
            ILogger<StaffSyncController> logger)
        {
            _staffSyncService = staffSyncService;
            _logger = logger;
        }

        /// <summary>
        /// Synchronize staff data from Workbench
        /// </summary>
        /// <returns>Synchronization result</returns>
        [HttpPost("sync")]
        public async Task<ActionResult<StaffSyncResponseDto>> SyncStaff()
        {
            try
            {
                _logger.LogInformation("Staff synchronization requested");

                var result = await _staffSyncService.SyncStaffDataAsync();

                if (result.Success)
                {
                    _logger.LogInformation("Staff synchronization completed successfully: {Message}", result.Message);
                    return Ok(result);
                }
                else
                {
                    _logger.LogWarning("Staff synchronization completed with errors: {Message}", result.Message);
                    return BadRequest(result);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during staff synchronization request: {Message}", ex.Message);
                
                var errorResponse = new StaffSyncResponseDto
                {
                    Success = false,
                    Message = $"Staff synchronization failed: {ex.Message}",
                    Errors = new List<string> { ex.Message },
                    SyncTime = DateTime.UtcNow
                };

                return StatusCode(500, errorResponse);
            }
        }

        /// <summary>
        /// Get staff synchronization status (placeholder for future enhancement)
        /// </summary>
        /// <returns>Sync status information</returns>
        [HttpGet("status")]
        public async Task<ActionResult> GetSyncStatus()
        {
            // Placeholder for future enhancement - could track last sync time, etc.
            return Ok(new { 
                message = "Staff sync status endpoint - to be implemented",
                timestamp = DateTime.UtcNow 
            });
        }
    }
} 