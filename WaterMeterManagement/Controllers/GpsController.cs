using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using WaterMeterManagement.Data;
using WaterMeterManagement.DTOs;
using WaterMeterManagement.Services.Interfaces;

namespace WaterMeterManagement.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class GpsController : ControllerBase
    {
        private readonly IGpsService _gpsService;
        private readonly ILogger<GpsController> _logger;
        private readonly ApplicationDbContext _context;

        public GpsController(IGpsService gpsService, ILogger<GpsController> logger, ApplicationDbContext context)
        {
            _gpsService = gpsService;
            _logger = logger;
            _context = context;
        }

        [HttpPost("geocode")]
        public async Task<ActionResult<GpsCoordinateResultDto>> GeocodeAddress([FromBody] string address)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(address))
                {
                    return BadRequest(new { message = "Address is required" });
                }

                var result = await _gpsService.GetCoordinatesFromAddressAsync(address);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error geocoding address: {Address}", address);
                return StatusCode(500, new { message = "Internal server error" });
            }
        }

        [HttpPost("geocode/batch")]
        public async Task<ActionResult<BatchGpsResultDto>> GeocodeAddressesBatch([FromBody] List<string> addresses)
        {
            try
            {
                if (addresses == null || addresses.Count == 0)
                {
                    return BadRequest(new { message = "Addresses list is required" });
                }

                var result = await _gpsService.GetCoordinatesFromAddressesBatchAsync(addresses);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error batch geocoding addresses");
                return StatusCode(500, new { message = "Internal server error" });
            }
        }

        [HttpPost("water-meter/{id}/update-gps")]
        public async Task<ActionResult<GpsUpdateResultDto>> UpdateWaterMeterGps(int id)
        {
            try
            {
                var result = await _gpsService.UpdateWaterMeterGpsAsync(id);
                
                if (!result.Success)
                {
                    return BadRequest(result);
                }

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating GPS for water meter {Id}", id);
                return StatusCode(500, new { message = "Internal server error" });
            }
        }

        [HttpPost("water-meters/update-gps/batch")]
        public async Task<ActionResult<BatchGpsUpdateResultDto>> UpdateWaterMetersGpsBatch([FromBody] List<int> waterMeterIds)
        {
            try
            {
                if (waterMeterIds == null || waterMeterIds.Count == 0)
                {
                    return BadRequest(new { message = "Water meter IDs list is required" });
                }

                var result = await _gpsService.UpdateWaterMetersGpsBatchAsync(waterMeterIds);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error batch updating GPS for water meters");
                return StatusCode(500, new { message = "Internal server error" });
            }
        }

        [HttpPost("water-meters/update-missing-gps")]
        public async Task<ActionResult<BatchGpsUpdateResultDto>> UpdateAllMissingGps()
        {
            try
            {
                var result = await _gpsService.UpdateAllMissingGpsAsync();
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating all missing GPS coordinates");
                return StatusCode(500, new { message = "Internal server error" });
            }
        }



        [HttpGet("validate")]
        public ActionResult<bool> ValidateGpsCoordinates([FromQuery] decimal latitude, [FromQuery] decimal longitude)
        {
            try
            {
                var isValid = _gpsService.ValidateGpsCoordinates(latitude, longitude);
                return Ok(new { isValid, latitude, longitude });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating GPS coordinates");
                return StatusCode(500, new { message = "Internal server error" });
            }
        }

        [HttpGet("stats")]
        public async Task<ActionResult<GpsServiceStatsDto>> GetGpsServiceStats()
        {
            try
            {
                var stats = await _gpsService.GetGpsServiceStatsAsync();
                return Ok(stats);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting GPS service stats");
                return StatusCode(500, new { message = "Internal server error" });
            }
        }

        [HttpGet("debug/water-meters-status")]
        public async Task<ActionResult> GetWaterMetersGpsStatus()
        {
            try
            {
                var allMeters = await _context.WaterMeters
                    .Select(wm => new {
                        wm.Id,
                        wm.SerialNumber,
                        wm.Address,
                        wm.Latitude,
                        wm.Longitude,
                        HasGps = wm.Latitude != null && wm.Longitude != null,
                        HasAddress = !string.IsNullOrEmpty(wm.Address)
                    })
                    .Take(10)
                    .ToListAsync();

                var summary = new {
                    TotalMeters = await _context.WaterMeters.CountAsync(),
                    MetersWithGps = await _context.WaterMeters.CountAsync(wm => wm.Latitude != null && wm.Longitude != null),
                    MetersWithAddress = await _context.WaterMeters.CountAsync(wm => !string.IsNullOrEmpty(wm.Address)),
                    MetersNeedingGps = await _context.WaterMeters.CountAsync(wm =>
                        (wm.Latitude == null || wm.Longitude == null) && !string.IsNullOrEmpty(wm.Address)),
                    SampleMeters = allMeters
                };

                return Ok(summary);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting water meters GPS status");
                return StatusCode(500, new { message = "Internal server error" });
            }
        }
    }
}
