using Microsoft.AspNetCore.Mvc;
using WaterMeterManagement.DTOs;
using WaterMeterManagement.DTOs.Common;
using WaterMeterManagement.Services.Interfaces;

namespace WaterMeterManagement.Controllers
{
    [ApiController]
    [Route("api/water-meters")]
    public class WaterMeterController : ControllerBase
    {
        private readonly IWaterMeterService _waterMeterService;
        private readonly ILogger<WaterMeterController> _logger;

        public WaterMeterController(
            IWaterMeterService waterMeterService,
            ILogger<WaterMeterController> logger)
        {
            _waterMeterService = waterMeterService;
            _logger = logger;
        }

        [HttpGet]
        public async Task<ActionResult<PaginatedResponse<WaterMeterListDto>>> GetWaterMeters([FromQuery] WaterMeterSearchDto searchDto)
        {
            try
            {
                var (meters, totalCount) = await _waterMeterService.GetWaterMetersAsync(searchDto);
                var response = PaginatedResponse<WaterMeterListDto>.Create(meters, totalCount, searchDto.Page, searchDto.PageSize);

                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving water meters");
                return StatusCode(500, new { Message = "Internal server error" });
            }
        }

        [HttpGet("{id:int}")]
        public async Task<ActionResult<WaterMeterDto>> GetWaterMeter(int id)
        {
            try
            {
                var meter = await _waterMeterService.GetWaterMeterByIdAsync(id);
                if (meter == null)
                {
                    return NotFound(new { Message = "Water meter not found" });
                }

                return Ok(meter);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving water meter {MeterId}", id);
                return StatusCode(500, new { Message = "Internal server error" });
            }
        }

        [HttpGet("serial/{serialNumber}")]
        public async Task<ActionResult<WaterMeterDto>> GetWaterMeterBySerial(string serialNumber)
        {
            try
            {
                var meter = await _waterMeterService.GetWaterMeterBySerialNumberAsync(serialNumber);
                if (meter == null)
                {
                    return NotFound(new { Message = "Water meter not found" });
                }

                return Ok(meter);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving water meter by serial {SerialNumber}", serialNumber);
                return StatusCode(500, new { Message = "Internal server error" });
            }
        }

        [HttpPost]
        public async Task<ActionResult<WaterMeterDto>> CreateWaterMeter([FromBody] CreateWaterMeterDto createDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var meter = await _waterMeterService.CreateWaterMeterAsync(createDto);
                return CreatedAtAction(nameof(GetWaterMeter), new { id = meter.Id }, meter);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating water meter");
                return StatusCode(500, new { Message = "Internal server error" });
            }
        }

        [HttpPut("{id:int}")]
        public async Task<ActionResult<WaterMeterDto>> UpdateWaterMeter(int id, [FromBody] UpdateWaterMeterDto updateDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var meter = await _waterMeterService.UpdateWaterMeterAsync(id, updateDto);
                if (meter == null)
                {
                    return NotFound(new { Message = "Water meter not found" });
                }

                return Ok(meter);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating water meter {MeterId}", id);
                return StatusCode(500, new { Message = "Internal server error" });
            }
        }

        [HttpDelete("{id:int}")]
        public async Task<ActionResult> DeleteWaterMeter(int id)
        {
            try
            {
                var success = await _waterMeterService.DeleteWaterMeterAsync(id);
                if (!success)
                {
                    return NotFound(new { Message = "Water meter not found" });
                }

                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting water meter {MeterId}", id);
                return StatusCode(500, new { Message = "Internal server error" });
            }
        }

        [HttpPost("import/csv")]
        public async Task<ActionResult<WaterMeterImportResultDto>> ImportFromCsv(IFormFile file)
        {
            try
            {
                if (file == null || file.Length == 0)
                {
                    return BadRequest(new { Message = "No file provided" });
                }

                if (!file.FileName.EndsWith(".csv", StringComparison.OrdinalIgnoreCase))
                {
                    return BadRequest(new { Message = "File must be a CSV file" });
                }

                using var stream = file.OpenReadStream();
                var result = await _waterMeterService.ImportFromCsvAsync(stream, file.FileName);
                
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error importing water meters from CSV");
                return StatusCode(500, new { Message = "Internal server error" });
            }
        }

        [HttpPost("import/excel")]
        public async Task<ActionResult<WaterMeterImportResultDto>> ImportFromExcel(IFormFile file)
        {
            try
            {
                if (file == null || file.Length == 0)
                {
                    return BadRequest(new { Message = "No file provided" });
                }

                if (!file.FileName.EndsWith(".xlsx", StringComparison.OrdinalIgnoreCase) &&
                    !file.FileName.EndsWith(".xls", StringComparison.OrdinalIgnoreCase))
                {
                    return BadRequest(new { Message = "File must be an Excel file" });
                }

                using var stream = file.OpenReadStream();
                var result = await _waterMeterService.ImportFromExcelAsync(stream, file.FileName);
                
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error importing water meters from Excel");
                return StatusCode(500, new { Message = "Internal server error" });
            }
        }

        [HttpPost("validate-import")]
        public async Task<ActionResult<List<WaterMeterImportDto>>> ValidateImportData([FromBody] List<WaterMeterImportDto> importData)
        {
            try
            {
                var validatedData = await _waterMeterService.ValidateImportDataAsync(importData);
                return Ok(validatedData);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating import data");
                return StatusCode(500, new { Message = "Internal server error" });
            }
        }

        [HttpGet("validate-serial/{serialNumber}")]
        public async Task<ActionResult<bool>> ValidateSerialNumber(string serialNumber, [FromQuery] int? excludeId = null)
        {
            try
            {
                var isValid = await _waterMeterService.ValidateSerialNumberAsync(serialNumber, excludeId);
                return Ok(new { IsValid = isValid });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating serial number {SerialNumber}", serialNumber);
                return StatusCode(500, new { Message = "Internal server error" });
            }
        }

        [HttpPost("{id:int}/reading")]
        public async Task<ActionResult> UpdateMeterReading(int id, [FromBody] SimpleMeterReadingDto readingDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                await _waterMeterService.UpdateMeterReadingAsync(id, readingDto.ReadingValue, readingDto.ReadingDate, readingDto.DataSource ?? "Manual");
                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating meter reading for {MeterId}", id);
                return StatusCode(500, new { Message = "Internal server error" });
            }
        }

        [HttpGet("location/{location}")]
        public async Task<ActionResult<List<WaterMeterListDto>>> GetMetersByLocation(string location)
        {
            try
            {
                var meters = await _waterMeterService.GetMetersByLocationAsync(location);
                return Ok(meters);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving meters by location {Location}", location);
                return StatusCode(500, new { Message = "Internal server error" });
            }
        }

        [HttpGet("status/{status}")]
        public async Task<ActionResult<List<WaterMeterListDto>>> GetMetersByStatus(string status)
        {
            try
            {
                var meters = await _waterMeterService.GetMetersByStatusAsync(status);
                return Ok(meters);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving meters by status {Status}", status);
                return StatusCode(500, new { Message = "Internal server error" });
            }
        }

        [HttpGet("maintenance/due")]
        public async Task<ActionResult<List<WaterMeterListDto>>> GetMetersDueForMaintenance()
        {
            try
            {
                var meters = await _waterMeterService.GetMetersDueForMaintenanceAsync();
                return Ok(meters);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving meters due for maintenance");
                return StatusCode(500, new { Message = "Internal server error" });
            }
        }

        [HttpGet("battery/low")]
        public async Task<ActionResult<List<WaterMeterListDto>>> GetMetersWithLowBattery([FromQuery] int? threshold = 20)
        {
            try
            {
                var meters = await _waterMeterService.GetMetersWithLowBatteryAsync(threshold);
                return Ok(meters);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving meters with low battery");
                return StatusCode(500, new { Message = "Internal server error" });
            }
        }

        // ============ AMS Integration Endpoints ============

        [HttpGet("asset/{assetId}")]
        public async Task<ActionResult<WaterMeterDto>> GetWaterMeterByAssetId(string assetId)
        {
            try
            {
                var meter = await _waterMeterService.GetWaterMeterByAssetIdAsync(assetId);
                if (meter == null)
                {
                    return NotFound(new { Message = "Water meter not found" });
                }

                return Ok(meter);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving water meter by Asset ID {AssetId}", assetId);
                return StatusCode(500, new { Message = "Internal server error" });
            }
        }

        [HttpGet("account/{accountNumber}")]
        public async Task<ActionResult<WaterMeterDto>> GetWaterMeterByAccountNumber(string accountNumber)
        {
            try
            {
                var meter = await _waterMeterService.GetWaterMeterByAccountNumberAsync(accountNumber);
                if (meter == null)
                {
                    return NotFound(new { Message = "Water meter not found" });
                }

                return Ok(meter);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving water meter by Account Number {AccountNumber}", accountNumber);
                return StatusCode(500, new { Message = "Internal server error" });
            }
        }

        [HttpGet("township/{township}")]
        public async Task<ActionResult<List<WaterMeterListDto>>> GetMetersByTownship(string township, [FromQuery] int page = 1, [FromQuery] int pageSize = 50)
        {
            try
            {
                var meters = await _waterMeterService.GetMetersByTownshipAsync(township, page, pageSize);
                return Ok(meters);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving meters by township {Township}", township);
                return StatusCode(500, new { Message = "Internal server error" });
            }
        }

        [HttpGet("road/{roadName}")]
        public async Task<ActionResult<List<WaterMeterListDto>>> GetMetersByRoad(string roadName, [FromQuery] int page = 1, [FromQuery] int pageSize = 50)
        {
            try
            {
                var meters = await _waterMeterService.GetMetersByRoadAsync(roadName, page, pageSize);
                return Ok(meters);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving meters by road {RoadName}", roadName);
                return StatusCode(500, new { Message = "Internal server error" });
            }
        }

        [HttpGet("route/{routeName}")]
        public async Task<ActionResult<List<WaterMeterListDto>>> GetMetersByRoute(string routeName, [FromQuery] int page = 1, [FromQuery] int pageSize = 50)
        {
            try
            {
                var meters = await _waterMeterService.GetMetersByRouteAsync(routeName, page, pageSize);
                return Ok(meters);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving meters by route {RouteName}", routeName);
                return StatusCode(500, new { Message = "Internal server error" });
            }
        }

        [HttpGet("cant-read")]
        public async Task<ActionResult<List<WaterMeterListDto>>> GetCantReadMeters([FromQuery] int page = 1, [FromQuery] int pageSize = 50)
        {
            try
            {
                var meters = await _waterMeterService.GetCantReadMetersAsync(page, pageSize);
                return Ok(meters);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving can't read meters");
                return StatusCode(500, new { Message = "Internal server error" });
            }
        }

        [HttpPost("{id:int}/ams-sync")]
        public async Task<ActionResult> SyncMeterWithAms(int id)
        {
            try
            {
                var success = await _waterMeterService.SyncMeterWithAmsAsync(id);
                if (!success)
                {
                    return NotFound(new { Message = "Water meter not found" });
                }

                return Ok(new { Message = "Meter synced with AMS successfully" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error syncing meter {MeterId} with AMS", id);
                return StatusCode(500, new { Message = "Internal server error" });
            }
        }

        [HttpPost("bulk-ams-sync")]
        public async Task<ActionResult<BulkSyncResultDto>> BulkSyncWithAms([FromBody] List<int> meterIds)
        {
            try
            {
                var result = await _waterMeterService.BulkSyncWithAmsAsync(meterIds);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error bulk syncing meters with AMS");
                return StatusCode(500, new { Message = "Internal server error" });
            }
        }

        [HttpGet("ams-anomalies")]
        public async Task<ActionResult<List<WaterMeterListDto>>> GetAmsAnomalies([FromQuery] int page = 1, [FromQuery] int pageSize = 50)
        {
            try
            {
                var meters = await _waterMeterService.GetAmsAnomaliesAsync(page, pageSize);
                return Ok(meters);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving AMS anomalies");
                return StatusCode(500, new { Message = "Internal server error" });
            }
        }

        [HttpGet("consumption-analysis/{assetId}")]
        public async Task<ActionResult<ConsumptionAnalysisDto>> GetConsumptionAnalysis(string assetId, [FromQuery] int months = 12)
        {
            try
            {
                var endDate = DateTime.UtcNow;
                var startDate = endDate.AddMonths(-months);
                var analysis = await _waterMeterService.GetConsumptionAnalysisAsync(assetId, startDate, endDate);
                if (analysis == null)
                {
                    return NotFound(new { Message = "Water meter not found" });
                }

                return Ok(analysis);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving consumption analysis for meter {AssetId}", assetId);
                return StatusCode(500, new { Message = "Internal server error" });
            }
        }

        [HttpPost("{id:int}/flag-anomaly")]
        public async Task<ActionResult> FlagMeterAnomaly(int id, [FromBody] AnomalyFlagDto flagDto)
        {
            try
            {
                var success = await _waterMeterService.FlagMeterAnomalyAsync(id, flagDto);
                if (!success)
                {
                    return NotFound(new { Message = "Water meter not found" });
                }

                return Ok(new { Message = "Meter anomaly flagged successfully" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error flagging anomaly for meter {MeterId}", id);
                return StatusCode(500, new { Message = "Internal server error" });
            }
        }

        [HttpPost("work-package-template")]
        public async Task<ActionResult> GenerateWorkPackageTemplate([FromBody] WorkPackageTemplateRequestDto request)
        {
            try
            {
                if (request.MeterIds == null || !request.MeterIds.Any())
                {
                    return BadRequest(new { Message = "Meter IDs are required" });
                }

                var excelBytes = await _waterMeterService.GenerateWorkPackageTemplateAsync(request.MeterIds);
                
                var fileName = $"water-meter-template-{DateTime.UtcNow:yyyy-MM-dd-HHmm}.xlsx";
                
                return File(excelBytes, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", fileName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating work package template");
                return StatusCode(500, new { Message = "Internal server error" });
            }
        }

        [HttpPost("import-work-packages")]
        public async Task<ActionResult<WorkPackageImportResultDto>> ImportWorkPackages(IFormFile file)
        {
            try
            {
                if (file == null || file.Length == 0)
                {
                    return BadRequest(new { Message = "No file provided" });
                }

                if (!file.FileName.EndsWith(".xlsx", StringComparison.OrdinalIgnoreCase) &&
                    !file.FileName.EndsWith(".xls", StringComparison.OrdinalIgnoreCase))
                {
                    return BadRequest(new { Message = "File must be an Excel file" });
                }

                using var stream = file.OpenReadStream();
                var result = await _waterMeterService.ImportWorkPackagesFromExcelAsync(stream, file.FileName);

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error importing work packages from Excel");
                return StatusCode(500, new { Message = "Internal server error" });
            }
        }

        [HttpGet("export/excel")]
        public async Task<ActionResult> ExportWaterMetersExcel([FromQuery] WaterMeterSearchDto? searchDto = null)
        {
            try
            {
                var excelData = await _waterMeterService.ExportWaterMetersAsync(searchDto);
                var fileName = $"water_meters_export_{DateTime.Now:yyyyMMdd_HHmmss}.xlsx";

                return File(excelData, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", fileName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error exporting water meters to Excel");
                return StatusCode(500, new { Message = "Internal server error" });
            }
        }

        [HttpGet("export/csv")]
        public async Task<ActionResult> ExportWaterMetersCsv([FromQuery] WaterMeterSearchDto? searchDto = null)
        {
            try
            {
                var csvData = await _waterMeterService.ExportWaterMetersCsvAsync(searchDto);
                var fileName = $"water_meters_export_{DateTime.Now:yyyyMMdd_HHmmss}.csv";

                return File(csvData, "text/csv", fileName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error exporting water meters to CSV");
                return StatusCode(500, new { Message = "Internal server error" });
            }
        }
    }

    public class SimpleMeterReadingDto
    {
        public decimal ReadingValue { get; set; }
        public DateTime ReadingDate { get; set; }
        public string? DataSource { get; set; }
    }

    public class WorkPackageTemplateRequestDto
    {
        public List<string> MeterIds { get; set; } = new List<string>();
    }
} 