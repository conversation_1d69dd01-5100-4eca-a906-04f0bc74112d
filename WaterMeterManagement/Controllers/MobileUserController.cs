using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using WaterMeterManagement.DTOs.Mobile;
using WaterMeterManagement.Services;
using WaterMeterManagement.Services.Interfaces;
using System.Security.Claims;

namespace WaterMeterManagement.Controllers
{
    /// <summary>
    /// Mobile User Controller - Handles user profile, workload, and mobile app settings
    /// </summary>
    [ApiController]
    [Route("api/mobile/users")]
    [Authorize]
    public class MobileUserController : ControllerBase
    {
        private readonly IUserService _userService;
        private readonly ITaskService _taskService;
        private readonly ILocationService _locationService;
        private readonly ILogger<MobileUserController> _logger;

        public MobileUserController(
            IUserService userService,
            ITaskService taskService,
            ILocationService locationService,
            ILogger<MobileUserController> logger)
        {
            _userService = userService;
            _taskService = taskService;
            _locationService = locationService;
            _logger = logger;
        }

        /// <summary>
        /// Get current user's workload information
        /// </summary>
        [HttpGet("my-workload")]
        public async Task<ActionResult<MobileUserWorkloadDto>> GetMyWorkload()
        {
            try
            {
                var userId = GetCurrentUserId();
                if (userId == 0)
                {
                    return Unauthorized("User not authenticated");
                }

                _logger.LogInformation("Getting workload information for user {UserId}", userId);

                var workload = await _userService.GetMobileUserWorkloadAsync(userId);
                
                if (workload == null)
                {
                    return NotFound("User workload information not found");
                }

                return Ok(workload);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting workload for user {UserId}", GetCurrentUserId());
                return StatusCode(500, "An error occurred while retrieving workload information");
            }
        }

        /// <summary>
        /// Get current user's detailed statistics
        /// </summary>
        [HttpGet("my-stats")]
        public async Task<ActionResult<MobileUserStatsDto>> GetMyStats()
        {
            try
            {
                var userId = GetCurrentUserId();
                if (userId == 0)
                {
                    return Unauthorized("User not authenticated");
                }

                _logger.LogInformation("Getting detailed statistics for user {UserId}", userId);

                var stats = await _userService.GetMobileUserStatsAsync(userId);
                
                return Ok(stats);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting statistics for user {UserId}", GetCurrentUserId());
                return StatusCode(500, "An error occurred while retrieving user statistics");
            }
        }

        /// <summary>
        /// Get current user's profile information
        /// </summary>
        [HttpGet("my-profile")]
        public async Task<ActionResult<MobileUserProfileDto>> GetMyProfile()
        {
            try
            {
                var userId = GetCurrentUserId();
                if (userId == 0)
                {
                    return Unauthorized("User not authenticated");
                }

                _logger.LogInformation("Getting profile information for user {UserId}", userId);

                var profile = await _userService.GetMobileUserProfileAsync(userId);
                
                if (profile == null)
                {
                    return NotFound("User profile not found");
                }

                return Ok(profile);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting profile for user {UserId}", GetCurrentUserId());
                return StatusCode(500, "An error occurred while retrieving user profile");
            }
        }

        /// <summary>
        /// Update user's location
        /// </summary>
        [HttpPost("location")]
        public async Task<ActionResult> UpdateLocation([FromBody] LocationUpdateDto locationUpdate)
        {
            try
            {
                var userId = GetCurrentUserId();
                if (userId == 0)
                {
                    return Unauthorized("User not authenticated");
                }

                _logger.LogInformation("Updating location for user {UserId} to ({Lat}, {Lng})", 
                    userId, locationUpdate.Latitude, locationUpdate.Longitude);

                var result = await _locationService.UpdateUserLocationAsync(userId, locationUpdate);
                
                if (!result.Success)
                {
                    return BadRequest(result.Message);
                }

                return Ok(new { message = "Location updated successfully" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating location for user {UserId}", GetCurrentUserId());
                return StatusCode(500, "An error occurred while updating location");
            }
        }

        /// <summary>
        /// Update user's availability status
        /// </summary>
        [HttpPost("status")]
        public async Task<ActionResult> UpdateStatus([FromBody] UserStatusUpdateDto statusUpdate)
        {
            try
            {
                var userId = GetCurrentUserId();
                if (userId == 0)
                {
                    return Unauthorized("User not authenticated");
                }

                _logger.LogInformation("Updating status for user {UserId} to {Status}", 
                    userId, statusUpdate.Status);

                var result = await _userService.UpdateUserStatusAsync(userId, statusUpdate);
                
                if (!result.Success)
                {
                    return BadRequest(result.Message);
                }

                return Ok(new { 
                    message = "Status updated successfully", 
                    status = statusUpdate.Status,
                    statusUntil = statusUpdate.StatusUntil
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating status for user {UserId}", GetCurrentUserId());
                return StatusCode(500, "An error occurred while updating status");
            }
        }

        /// <summary>
        /// Update mobile app preferences
        /// </summary>
        [HttpPut("preferences")]
        public async Task<ActionResult> UpdatePreferences([FromBody] MobileAppPreferencesDto preferences)
        {
            try
            {
                var userId = GetCurrentUserId();
                if (userId == 0)
                {
                    return Unauthorized("User not authenticated");
                }

                _logger.LogInformation("Updating app preferences for user {UserId}", userId);

                var result = await _userService.UpdateMobilePreferencesAsync(userId, preferences);
                
                if (!result.Success)
                {
                    return BadRequest(result.Message);
                }

                return Ok(new { message = "Preferences updated successfully" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating preferences for user {UserId}", GetCurrentUserId());
                return StatusCode(500, "An error occurred while updating preferences");
            }
        }

        /// <summary>
        /// Register a mobile device
        /// </summary>
        [HttpPost("register-device")]
        public async Task<ActionResult> RegisterDevice([FromBody] MobileDeviceDto device)
        {
            try
            {
                var userId = GetCurrentUserId();
                if (userId == 0)
                {
                    return Unauthorized("User not authenticated");
                }

                _logger.LogInformation("Registering device {DeviceId} for user {UserId}", 
                    device.DeviceId, userId);

                var result = await _userService.RegisterMobileDeviceAsync(userId, device);
                
                if (!result.Success)
                {
                    return BadRequest(result.Message);
                }

                return Ok(new { 
                    message = "Device registered successfully", 
                    deviceId = device.DeviceId
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error registering device for user {UserId}", GetCurrentUserId());
                return StatusCode(500, "An error occurred while registering device");
            }
        }

        /// <summary>
        /// Get user's registered devices
        /// </summary>
        [HttpGet("devices")]
        public async Task<ActionResult<List<MobileDeviceDto>>> GetRegisteredDevices()
        {
            try
            {
                var userId = GetCurrentUserId();
                if (userId == 0)
                {
                    return Unauthorized("User not authenticated");
                }

                _logger.LogInformation("Getting registered devices for user {UserId}", userId);

                var devices = await _userService.GetUserDevicesAsync(userId);
                
                return Ok(devices);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting devices for user {UserId}", GetCurrentUserId());
                return StatusCode(500, "An error occurred while retrieving devices");
            }
        }

        /// <summary>
        /// Deactivate a mobile device
        /// </summary>
        [HttpDelete("devices/{deviceId}")]
        public async Task<ActionResult> DeactivateDevice(string deviceId)
        {
            try
            {
                var userId = GetCurrentUserId();
                if (userId == 0)
                {
                    return Unauthorized("User not authenticated");
                }

                _logger.LogInformation("Deactivating device {DeviceId} for user {UserId}", deviceId, userId);

                var result = await _userService.DeactivateDeviceAsync(userId, deviceId);
                
                if (!result.Success)
                {
                    return BadRequest(result.Message);
                }

                return Ok(new { message = "Device deactivated successfully", deviceId });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deactivating device {DeviceId} for user {UserId}", deviceId, GetCurrentUserId());
                return StatusCode(500, "An error occurred while deactivating device");
            }
        }

        /// <summary>
        /// Get performance leaderboard
        /// </summary>
        [HttpGet("leaderboard")]
        public async Task<ActionResult<List<UserLeaderboardDto>>> GetLeaderboard(
            [FromQuery] string period = "week", // week, month, quarter
            [FromQuery] int limit = 10)
        {
            try
            {
                var userId = GetCurrentUserId();
                if (userId == 0)
                {
                    return Unauthorized("User not authenticated");
                }

                _logger.LogInformation("Getting leaderboard for period {Period}, limit {Limit}", period, limit);

                var leaderboard = await _userService.GetPerformanceLeaderboardAsync(period, limit);
                
                return Ok(leaderboard);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting leaderboard");
                return StatusCode(500, "An error occurred while retrieving leaderboard");
            }
        }

        /// <summary>
        /// Get user achievements
        /// </summary>
        [HttpGet("my-achievements")]
        public async Task<ActionResult<List<UserAchievementDto>>> GetMyAchievements()
        {
            try
            {
                var userId = GetCurrentUserId();
                if (userId == 0)
                {
                    return Unauthorized("User not authenticated");
                }

                _logger.LogInformation("Getting achievements for user {UserId}", userId);

                var achievements = await _userService.GetUserAchievementsAsync(userId);
                
                return Ok(achievements);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting achievements for user {UserId}", GetCurrentUserId());
                return StatusCode(500, "An error occurred while retrieving achievements");
            }
        }

        /// <summary>
        /// Sync mobile app data (heartbeat)
        /// </summary>
        [HttpPost("sync-heartbeat")]
        public async Task<ActionResult<MobileSyncResponseDto>> SyncHeartbeat([FromBody] MobileSyncRequestDto syncRequest)
        {
            try
            {
                var userId = GetCurrentUserId();
                if (userId == 0)
                {
                    return Unauthorized("User not authenticated");
                }

                _logger.LogInformation("Processing sync heartbeat for user {UserId}, device {DeviceId}", 
                    userId, syncRequest.DeviceId);

                var response = await _userService.ProcessMobileSyncAsync(userId, syncRequest);
                
                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing sync heartbeat for user {UserId}", GetCurrentUserId());
                return StatusCode(500, "An error occurred while processing sync");
            }
        }

        /// <summary>
        /// Get nearby team members
        /// </summary>
        [HttpGet("nearby-team")]
        public async Task<ActionResult<List<NearbyUserDto>>> GetNearbyTeamMembers(
            [FromQuery] double latitude,
            [FromQuery] double longitude,
            [FromQuery] double radiusKm = 10.0)
        {
            try
            {
                var userId = GetCurrentUserId();
                if (userId == 0)
                {
                    return Unauthorized("User not authenticated");
                }

                _logger.LogInformation("Getting nearby team members for user {UserId} at ({Lat}, {Lng}), radius {Radius}km", 
                    userId, latitude, longitude, radiusKm);

                var nearbyUsers = await _userService.GetNearbyTeamMembersAsync(userId, latitude, longitude, radiusKm);
                
                return Ok(nearbyUsers);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting nearby team members for user {UserId}", GetCurrentUserId());
                return StatusCode(500, "An error occurred while retrieving nearby team members");
            }
        }

        #region Helper Methods

        private int GetCurrentUserId()
        {
            var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            return int.TryParse(userIdClaim, out var userId) ? userId : 0;
        }

        #endregion
    }
} 