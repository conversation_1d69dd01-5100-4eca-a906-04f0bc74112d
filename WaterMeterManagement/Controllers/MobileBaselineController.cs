using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using WaterMeterManagement.Data;
using WaterMeterManagement.DTOs;
using WaterMeterManagement.DTOs.Mobile;
using WaterMeterManagement.Services.Interfaces;

namespace WaterMeterManagement.Controllers
{
    /// <summary>
    /// Mobile Baseline Controller - Provides mobile app optimized endpoints for baseline data
    /// 移动端基线控制器 - 为移动应用提供优化的基线数据接口
    /// </summary>
    [ApiController]
    [Route("api/mobile/baselines")]
    [Authorize]
    public class MobileBaselineController : ControllerBase
    {
        private readonly ApplicationDbContext _context;
        private readonly IBaselineService _baselineService;
        private readonly ILogger<MobileBaselineController> _logger;

        public MobileBaselineController(
            ApplicationDbContext context,
            IBaselineService baselineService,
            ILogger<MobileBaselineController> logger)
        {
            _context = context;
            _baselineService = baselineService;
            _logger = logger;
        }

        /// <summary>
        /// Get all baselines for mobile synchronization with pagination
        /// 获取所有基线数据用于移动端同步，支持分页
        /// </summary>
        [HttpGet("sync/all")]
        public async Task<ActionResult<object>> SyncAllBaselines(
            [FromQuery] int page = 1, 
            [FromQuery] int pageSize = 1000,
            [FromQuery] DateTime? lastSyncDate = null)
        {
            try
            {
                _logger.LogInformation("Mobile baseline sync requested - Page: {Page}, PageSize: {PageSize}", page, pageSize);

                // 🎯 UPDATED: Use BaselineService instead of direct context queries
                // Create search criteria for the service
                var searchDto = new BaselineSearchDto
                {
                    Page = page,
                    PageSize = pageSize,
                    SortBy = "baselinedate",
                    SortDirection = "desc"
                };

                // Add incremental sync filter if provided
                if (lastSyncDate.HasValue)
                {
                    searchDto.UpdatedFrom = lastSyncDate.Value;
                    _logger.LogInformation("Incremental sync from {LastSyncDate}", lastSyncDate.Value.ToString("yyyy-MM-dd HH:mm:ss"));
                }

                var (records, totalCount) = await _baselineService.GetBaselineRecordsAsync(searchDto);

                // Convert to mobile DTOs
                var baselines = records.Select(b => new MobileBaselineDto
                {
                    Id = b.Id,
                    MeterId = b.MeterId,
                    MeterSerialNumber = b.MeterSerialNumber,
                    BaselineDate = b.BaselineDate,
                    BaselineValue = b.BaselineValue,
                    BaselineType = b.BaselineType,
                    Status = b.Status,
                    ImportBatch = "", // Will be filled from full record if needed
                    SourceFile = "", // Will be filled from full record if needed
                    DataSource = b.DataSource,
                    ValidationNotes = "", // Will be filled from full record if needed
                    IsValidated = b.IsValidated,
                    ValidatedDate = null, // Will be filled from full record if needed
                    ValidatedBy = "", // Will be filled from full record if needed
                    HasValidationErrors = b.HasValidationErrors,
                    ValidationErrors = "", // Will be filled from full record if needed
                    IsAnomalous = b.IsAnomalous,
                    AnomalyDescription = "", // Will be filled from full record if needed
                    PreviousBaselineId = null, // Will be filled from full record if needed
                    PreviousBaselineValue = null, // Will be filled from full record if needed
                    VarianceFromPrevious = null, // Will be filled from full record if needed
                    VariancePercentage = b.VariancePercentage,
                    IsCorrected = false, // Will be filled from full record if needed
                    CorrectedDate = null, // Will be filled from full record if needed
                    CorrectedBy = "", // Will be filled from full record if needed
                    CorrectionReason = "", // Will be filled from full record if needed
                    ConfidenceLevel = 100, // Default value, will be filled from full record if needed
                    Notes = "", // Will be filled from full record if needed
                    CreatedAt = DateTime.UtcNow, // Placeholder
                    UpdatedAt = DateTime.UtcNow, // Placeholder
                    CreatedBy = "System",
                    UpdatedBy = "System"
                }).ToList();

                _logger.LogInformation("Retrieved {Count} baselines out of {Total} total", baselines.Count, totalCount);

                return Ok(new
                {
                    data = baselines,
                    pagination = new
                    {
                        page,
                        pageSize,
                        totalCount,
                        totalPages = (int)Math.Ceiling((double)totalCount / pageSize),
                        hasMore = page * pageSize < totalCount
                    },
                    metadata = new
                    {
                        syncTimestamp = DateTime.UtcNow,
                        apiVersion = "1.0"
                    }
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving baselines for mobile sync");
                return StatusCode(500, new { Message = "Internal server error while syncing baselines" });
            }
        }

        /// <summary>
        /// Get baseline statistics for mobile dashboard
        /// 获取基线统计信息用于移动端仪表板
        /// </summary>
        [HttpGet("statistics")]
        public async Task<ActionResult<object>> GetBaselineStatistics()
        {
            try
            {
                var stats = await _context.BaselineRecords
                    .Where(b => !b.IsDeleted)
                    .GroupBy(b => 1)
                    .Select(g => new
                    {
                        TotalCount = g.Count(),
                        ActiveCount = g.Count(b => b.Status == "Active"),
                        ValidatedCount = g.Count(b => b.IsValidated),
                        AnomalousCount = g.Count(b => b.IsAnomalous),
                        CorrectedCount = g.Count(b => b.IsCorrected),
                        RecentCount = g.Count(b => b.CreatedAt >= DateTime.Today.AddDays(-30))
                    })
                    .FirstOrDefaultAsync();

                return Ok(stats ?? new
                {
                    TotalCount = 0,
                    ActiveCount = 0,
                    ValidatedCount = 0,
                    AnomalousCount = 0,
                    CorrectedCount = 0,
                    RecentCount = 0
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving baseline statistics");
                return StatusCode(500, new { Message = "Internal server error while getting statistics" });
            }
        }
    }
} 