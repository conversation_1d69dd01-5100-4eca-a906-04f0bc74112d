using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using WaterMeterManagement.DTOs;
using WaterMeterManagement.Services.Interfaces;
using System.Security.Claims;

namespace WaterMeterManagement.Controllers
{
    /// <summary>
    /// Work Package Management Controller
    /// Handles work package operations including CRUD, assignments, and task generation
    /// </summary>
    [Authorize]
    [ApiController]
    [Route("api/[controller]")]
    public class WorkPackageController : ControllerBase
    {
        private readonly IWorkPackageService _workPackageService;
        private readonly ILogger<WorkPackageController> _logger;

        public WorkPackageController(
            IWorkPackageService workPackageService,
            ILogger<WorkPackageController> logger)
        {
            _workPackageService = workPackageService;
            _logger = logger;
        }

        #region Basic CRUD Operations

        /// <summary>
        /// Get all work packages with pagination and filtering
        /// </summary>
        [HttpGet]
        public async Task<ActionResult<object>> GetWorkPackages(
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 10,
            [FromQuery] string? name = null,
            [FromQuery] string? packageType = null,
            [FromQuery] string? status = null,
            [FromQuery] string? priority = null,
            [FromQuery] string? serviceArea = null,
            [FromQuery] string? subArea = null,
            [FromQuery] string? createdBy = null,
            [FromQuery] int? assignedUserId = null,
            [FromQuery] DateTime? plannedStartDateFrom = null,
            [FromQuery] DateTime? plannedStartDateTo = null,
            [FromQuery] DateTime? plannedEndDateFrom = null,
            [FromQuery] DateTime? plannedEndDateTo = null,
            [FromQuery] bool? isTemplate = null,
            [FromQuery] bool? isRecurring = null,
            [FromQuery] bool? isOverdue = null,
            [FromQuery] string? source = null,
            [FromQuery] decimal? minProgress = null,
            [FromQuery] decimal? maxProgress = null)
        {
            try
            {
                var searchDto = new WorkPackageSearchDto
                {
                    Name = name,
                    PackageType = packageType,
                    Status = status,
                    Priority = priority,
                    ServiceArea = serviceArea,
                    SubArea = subArea,
                    CreatedBy = createdBy,
                    AssignedUserId = assignedUserId,
                    PlannedStartDateFrom = plannedStartDateFrom,
                    PlannedStartDateTo = plannedStartDateTo,
                    PlannedEndDateFrom = plannedEndDateFrom,
                    PlannedEndDateTo = plannedEndDateTo,
                    IsTemplate = isTemplate,
                    IsRecurring = isRecurring,
                    IsOverdue = isOverdue,
                    Source = source,
                    MinProgress = minProgress,
                    MaxProgress = maxProgress
                };

                var (items, totalCount) = await _workPackageService.GetWorkPackagesAsync(page, pageSize, searchDto);

                return Ok(new
                {
                    Items = items,
                    TotalCount = totalCount,
                    Page = page,
                    PageSize = pageSize,
                    TotalPages = (int)Math.Ceiling((double)totalCount / pageSize)
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting work packages");
                return StatusCode(500, new { message = "An error occurred while retrieving work packages" });
            }
        }

        /// <summary>
        /// Get work package by ID
        /// </summary>
        [HttpGet("{id}")]
        public async Task<ActionResult<WorkPackageDto>> GetWorkPackage(int id)
        {
            try
            {
                var workPackage = await _workPackageService.GetWorkPackageByIdAsync(id);
                if (workPackage == null)
                {
                    return NotFound(new { message = $"Work package with ID {id} not found" });
                }

                return Ok(workPackage);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting work package {Id}", id);
                return StatusCode(500, new { message = "An error occurred while retrieving the work package" });
            }
        }

        /// <summary>
        /// Create a new work package
        /// </summary>
        [HttpPost]
        public async Task<ActionResult<WorkPackageDto>> CreateWorkPackage([FromBody] CreateWorkPackageDto createDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                // Validate work package data
                var validationErrors = await _workPackageService.ValidateWorkPackageAsync(createDto);
                if (validationErrors.Any())
                {
                    return BadRequest(new { message = "Validation failed", errors = validationErrors });
                }

                var workPackage = await _workPackageService.CreateWorkPackageAsync(createDto);
                return CreatedAtAction(nameof(GetWorkPackage), new { id = workPackage.Id }, workPackage);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating work package");
                return StatusCode(500, new { message = "An error occurred while creating the work package" });
            }
        }

        /// <summary>
        /// Update an existing work package
        /// </summary>
        [HttpPut("{id}")]
        public async Task<ActionResult<WorkPackageDto>> UpdateWorkPackage(int id, [FromBody] UpdateWorkPackageDto updateDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                if (id != updateDto.Id)
                {
                    return BadRequest(new { message = "ID in URL does not match ID in request body" });
                }

                var workPackage = await _workPackageService.UpdateWorkPackageAsync(updateDto);
                return Ok(workPackage);
            }
            catch (ArgumentException ex)
            {
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating work package {Id}", id);
                return StatusCode(500, new { message = "An error occurred while updating the work package" });
            }
        }

        /// <summary>
        /// Delete a work package
        /// </summary>
        [HttpDelete("{id}")]
        public async Task<ActionResult> DeleteWorkPackage(int id)
        {
            try
            {
                var currentUser = GetCurrentUser();
                var result = await _workPackageService.DeleteWorkPackageAsync(id, currentUser);
                
                if (!result)
                {
                    return NotFound(new { message = $"Work package with ID {id} not found" });
                }

                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting work package {Id}", id);
                return StatusCode(500, new { message = "An error occurred while deleting the work package" });
            }
        }

        #endregion

        #region Status Management

        /// <summary>
        /// Update work package status
        /// </summary>
        [HttpPatch("{id}/status")]
        public async Task<ActionResult> UpdateStatus(int id, [FromBody] UpdateStatusRequest request)
        {
            try
            {
                if (string.IsNullOrEmpty(request.Status))
                {
                    return BadRequest(new { message = "Status is required" });
                }

                var currentUser = GetCurrentUser();
                var result = await _workPackageService.UpdateWorkPackageStatusAsync(id, request.Status, currentUser);
                
                if (!result)
                {
                    return NotFound(new { message = $"Work package with ID {id} not found" });
                }

                return Ok(new { message = "Status updated successfully" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating work package status {Id}", id);
                return StatusCode(500, new { message = "An error occurred while updating the status" });
            }
        }

        /// <summary>
        /// Start work package execution
        /// </summary>
        [HttpPost("{id}/start")]
        public async Task<ActionResult> StartWorkPackage(int id)
        {
            try
            {
                var currentUser = GetCurrentUser();
                var result = await _workPackageService.StartWorkPackageAsync(id, currentUser);
                
                if (!result)
                {
                    return NotFound(new { message = $"Work package with ID {id} not found or cannot be started" });
                }

                return Ok(new { message = "Work package started successfully" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error starting work package {Id}", id);
                return StatusCode(500, new { message = "An error occurred while starting the work package" });
            }
        }

        /// <summary>
        /// Activate work package with validation and optional task generation
        /// </summary>
        [HttpPost("{id}/activate")]
        public async Task<ActionResult<WorkPackageActivationResultDto>> ActivateWorkPackage(int id, [FromQuery] bool generateTasks = true)
        {
            try
            {
                var currentUser = GetCurrentUser();
                var result = await _workPackageService.ActivateWorkPackageAsync(id, currentUser, generateTasks);
                
                if (!result.Success)
                {
                    return BadRequest(result);
                }

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error activating work package {Id}", id);
                return StatusCode(500, new { 
                    success = false,
                    message = "An error occurred while activating the work package",
                    activatedAt = DateTime.UtcNow
                });
            }
        }

        /// <summary>
        /// Complete work package
        /// </summary>
        [HttpPost("{id}/complete")]
        public async Task<ActionResult> CompleteWorkPackage(int id)
        {
            try
            {
                var currentUser = GetCurrentUser();
                var result = await _workPackageService.CompleteWorkPackageAsync(id, currentUser);
                
                if (!result)
                {
                    return NotFound(new { message = $"Work package with ID {id} not found or cannot be completed" });
                }

                return Ok(new { message = "Work package completed successfully" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error completing work package {Id}", id);
                return StatusCode(500, new { message = "An error occurred while completing the work package" });
            }
        }

        /// <summary>
        /// Cancel work package
        /// </summary>
        [HttpPost("{id}/cancel")]
        public async Task<ActionResult> CancelWorkPackage(int id, [FromBody] CancelRequest request)
        {
            try
            {
                var currentUser = GetCurrentUser();
                var result = await _workPackageService.CancelWorkPackageAsync(id, currentUser, request.Reason ?? "");
                
                if (!result)
                {
                    return NotFound(new { message = $"Work package with ID {id} not found or cannot be cancelled" });
                }

                return Ok(new { message = "Work package cancelled successfully" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error cancelling work package {Id}", id);
                return StatusCode(500, new { message = "An error occurred while cancelling the work package" });
            }
        }

        #endregion

        #region Work Package Items

        /// <summary>
        /// Get work package items
        /// </summary>
        [HttpGet("{id}/items")]
        public async Task<ActionResult<List<WorkPackageItemDto>>> GetWorkPackageItems(int id)
        {
            try
            {
                var items = await _workPackageService.GetWorkPackageItemsAsync(id);
                return Ok(items);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting work package items for {Id}", id);
                return StatusCode(500, new { message = "An error occurred while retrieving work package items" });
            }
        }

        /// <summary>
        /// Add meters to work package
        /// </summary>
        [HttpPost("{id}/items")]
        public async Task<ActionResult> AddMeters(int id, [FromBody] AddMetersRequest request)
        {
            try
            {
                if (request.MeterIds == null || !request.MeterIds.Any())
                {
                    return BadRequest(new { message = "At least one meter ID is required" });
                }

                var currentUser = GetCurrentUser();
                var result = await _workPackageService.AddMetersToWorkPackageAsync(id, request.MeterIds, currentUser);
                
                if (!result)
                {
                    return NotFound(new { message = $"Work package with ID {id} not found" });
                }

                return Ok(new { message = "Meters added successfully" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error adding meters to work package {Id}", id);
                return StatusCode(500, new { message = "An error occurred while adding meters" });
            }
        }

        #endregion

        #region Assignments

        /// <summary>
        /// Get work package assignments
        /// </summary>
        [HttpGet("{id}/assignments")]
        public async Task<ActionResult<List<WorkPackageAssignmentDto>>> GetAssignments(int id)
        {
            try
            {
                var assignments = await _workPackageService.GetWorkPackageAssignmentsAsync(id);
                return Ok(assignments);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting assignments for work package {Id}", id);
                return StatusCode(500, new { message = "An error occurred while retrieving assignments" });
            }
        }

        /// <summary>
        /// Assign work package to users
        /// </summary>
        [HttpPost("{id}/assignments")]
        public async Task<ActionResult> AssignWorkPackage(int id, [FromBody] List<CreateWorkPackageAssignmentDto> assignments)
        {
            try
            {
                if (assignments == null || !assignments.Any())
                {
                    return BadRequest(new { message = "At least one assignment is required" });
                }

                // Set assigned by for all assignments
                var currentUser = GetCurrentUser();
                foreach (var assignment in assignments)
                {
                    assignment.AssignedBy = currentUser;
                }

                var result = await _workPackageService.AssignWorkPackageAsync(id, assignments);
                
                if (!result)
                {
                    return NotFound(new { message = $"Work package with ID {id} not found" });
                }

                return Ok(new { message = "Work package assigned successfully" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error assigning work package {Id}", id);
                return StatusCode(500, new { message = "An error occurred while assigning the work package" });
            }
        }

        #endregion

        #region Task Generation

        /// <summary>
        /// Generate tasks from work package
        /// </summary>
        [HttpPost("{id}/generate-tasks")]
        public async Task<ActionResult<TaskGenerationResultDto>> GenerateTasks(int id)
        {
            try
            {
                var currentUser = GetCurrentUser();
                var result = await _workPackageService.GenerateTasksAsync(id, currentUser);
                
                if (!result.Success)
                {
                    return BadRequest(new { message = result.Message });
                }

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating tasks for work package {Id}", id);
                return StatusCode(500, new { message = "An error occurred while generating tasks" });
            }
        }

        #endregion

        #region Templates

        /// <summary>
        /// Get work package templates
        /// </summary>
        [HttpGet("templates")]
        public async Task<ActionResult<List<WorkPackageListDto>>> GetTemplates([FromQuery] string? category = null)
        {
            try
            {
                var templates = await _workPackageService.GetTemplatesAsync(category);
                return Ok(templates);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting work package templates");
                return StatusCode(500, new { message = "An error occurred while retrieving templates" });
            }
        }

        #endregion

        #region Statistics

        /// <summary>
        /// Get work package statistics
        /// </summary>
        [HttpGet("statistics")]
        public async Task<ActionResult<WorkPackageStatisticsDto>> GetStatistics([FromQuery] WorkPackageSearchDto? filter = null)
        {
            try
            {
                var statistics = await _workPackageService.GetStatisticsAsync(filter);
                return Ok(statistics);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting work package statistics");
                return StatusCode(500, new { message = "An error occurred while retrieving statistics" });
            }
        }

        #endregion

        #region Import/Export

        /// <summary>
        /// Download work package template
        /// </summary>
        [HttpGet("template/download")]
        public async Task<ActionResult> DownloadTemplate()
        {
            try
            {
                var templateData = await _workPackageService.GenerateTemplateAsync();
                return File(templateData, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", 
                    $"work-package-template-{DateTime.Now:yyyy-MM-dd}.xlsx");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error downloading work package template");
                return StatusCode(500, new { message = "An error occurred while generating template" });
            }
        }

        /// <summary>
        /// Export filtered template
        /// </summary>
        [HttpPost("template/filtered-export")]
        public async Task<ActionResult> ExportFilteredTemplate([FromBody] FilteredExportRequest request)
        {
            try
            {
                var templateData = await _workPackageService.ExportFilteredTemplateAsync(request);
                return File(templateData, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", 
                    $"filtered-template-{DateTime.Now:yyyy-MM-dd-HHmm}.xlsx");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error exporting filtered template");
                return StatusCode(500, new { message = "An error occurred while exporting filtered template" });
            }
        }

        /// <summary>
        /// Import work packages from Excel
        /// </summary>
        [HttpPost("import/excel")]
        public async Task<ActionResult<ImportResultDto>> ImportExcel(IFormFile file)
        {
            try
            {
                if (file == null || file.Length == 0)
                {
                    return BadRequest(new { message = "No file uploaded" });
                }

                if (!file.FileName.EndsWith(".xlsx") && !file.FileName.EndsWith(".xls"))
                {
                    return BadRequest(new { message = "Only Excel files are supported" });
                }

                var currentUser = GetCurrentUser();
                var result = await _workPackageService.ImportWorkPackagesAsync(file, currentUser);
                
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error importing work packages from Excel");
                return StatusCode(500, new { message = "An error occurred while importing work packages" });
            }
        }

        /// <summary>
        /// Export work packages to Excel
        /// </summary>
        [HttpGet("export/excel")]
        public async Task<ActionResult> ExportExcel([FromQuery] WorkPackageSearchDto? filter = null)
        {
            try
            {
                var excelData = await _workPackageService.ExportWorkPackagesAsync(filter);
                return File(excelData, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", 
                    $"work-packages-export-{DateTime.Now:yyyy-MM-dd}.xlsx");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error exporting work packages to Excel");
                return StatusCode(500, new { message = "An error occurred while exporting work packages" });
            }
        }

        #endregion

        #region Batch Operations

        /// <summary>
        /// Batch activate work packages
        /// </summary>
        [HttpPost("batch/activate")]
        public async Task<ActionResult> BatchActivate([FromBody] BatchOperationRequest request)
        {
            try
            {
                if (request.Ids == null || !request.Ids.Any())
                {
                    return BadRequest(new { message = "At least one work package ID is required" });
                }

                var currentUser = GetCurrentUser();
                var result = await _workPackageService.BatchActivateAsync(request.Ids, currentUser);
                
                return Ok(new { 
                    message = $"Successfully activated {result.SuccessCount} out of {request.Ids.Count} work packages",
                    successCount = result.SuccessCount,
                    failureCount = result.FailureCount,
                    errors = result.Errors
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error batch activating work packages");
                return StatusCode(500, new { message = "An error occurred while activating work packages" });
            }
        }

        /// <summary>
        /// Batch pause work packages
        /// </summary>
        [HttpPost("batch/pause")]
        public async Task<ActionResult> BatchPause([FromBody] BatchOperationRequest request)
        {
            try
            {
                if (request.Ids == null || !request.Ids.Any())
                {
                    return BadRequest(new { message = "At least one work package ID is required" });
                }

                var currentUser = GetCurrentUser();
                var result = await _workPackageService.BatchPauseAsync(request.Ids, currentUser);
                
                return Ok(new { 
                    message = $"Successfully paused {result.SuccessCount} out of {request.Ids.Count} work packages",
                    successCount = result.SuccessCount,
                    failureCount = result.FailureCount,
                    errors = result.Errors
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error batch pausing work packages");
                return StatusCode(500, new { message = "An error occurred while pausing work packages" });
            }
        }

        /// <summary>
        /// Batch cancel work packages
        /// </summary>
        [HttpPost("batch/cancel")]
        public async Task<ActionResult> BatchCancel([FromBody] BatchCancelRequest request)
        {
            try
            {
                if (request.Ids == null || !request.Ids.Any())
                {
                    return BadRequest(new { message = "At least one work package ID is required" });
                }

                var currentUser = GetCurrentUser();
                var result = await _workPackageService.BatchCancelAsync(request.Ids, request.Reason, currentUser);
                
                return Ok(new { 
                    message = $"Successfully cancelled {result.SuccessCount} out of {request.Ids.Count} work packages",
                    successCount = result.SuccessCount,
                    failureCount = result.FailureCount,
                    errors = result.Errors
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error batch cancelling work packages");
                return StatusCode(500, new { message = "An error occurred while cancelling work packages" });
            }
        }

        /// <summary>
        /// Batch delete work packages
        /// </summary>
        [HttpPost("batch/delete")]
        public async Task<ActionResult> BatchDelete([FromBody] BatchOperationRequest request)
        {
            try
            {
                if (request.Ids == null || !request.Ids.Any())
                {
                    return BadRequest(new { message = "At least one work package ID is required" });
                }

                var currentUser = GetCurrentUser();
                var result = await _workPackageService.BatchDeleteAsync(request.Ids, currentUser);
                
                return Ok(new { 
                    message = $"Successfully deleted {result.SuccessCount} out of {request.Ids.Count} work packages",
                    successCount = result.SuccessCount,
                    failureCount = result.FailureCount,
                    errors = result.Errors
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error batch deleting work packages");
                return StatusCode(500, new { message = "An error occurred while deleting work packages" });
            }
        }

        #endregion

        #region Helper Methods

        /// <summary>
        /// Get current user from JWT claims
        /// </summary>
        private string GetCurrentUser()
        {
            return User.FindFirst(ClaimTypes.Name)?.Value ?? "system";
        }

        #endregion
    }

    #region Request DTOs

    public class UpdateStatusRequest
    {
        public string Status { get; set; } = string.Empty;
    }

    public class CancelRequest
    {
        public string? Reason { get; set; }
    }

    public class AddMetersRequest
    {
        public List<int> MeterIds { get; set; } = new();
    }

    public class FilteredExportRequest
    {
        public List<string> ServiceAreas { get; set; } = new();
        public List<string> SubAreas { get; set; } = new();
        public List<string> MeterTypes { get; set; } = new();
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public bool IncludeCompleted { get; set; }
    }

    public class BatchOperationRequest
    {
        public List<int> Ids { get; set; } = new();
    }

    public class BatchCancelRequest
    {
        public List<int> Ids { get; set; } = new();
        public string? Reason { get; set; }
    }

    #endregion
}
