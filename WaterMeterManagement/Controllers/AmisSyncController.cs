using Microsoft.AspNetCore.Mvc;
using WaterMeterManagement.DTOs;
using WaterMeterManagement.Services.Interfaces;

namespace WaterMeterManagement.Controllers
{
    [ApiController]
    [Route("api/amis-sync")]
    public class AmisSyncController : ControllerBase
    {
        private readonly IAmisSyncService _amisSyncService;
        private readonly ILogger<AmisSyncController> _logger;

        public AmisSyncController(
            IAmisSyncService amisSyncService,
            ILogger<AmisSyncController> logger)
        {
            _amisSyncService = amisSyncService;
            _logger = logger;
        }

        // Base GET endpoint for frontend compatibility
        [HttpGet]
        public async Task<ActionResult<object>> GetSyncs([FromQuery] AmisSyncSearchDto searchDto)
        {
            try
            {
                var (syncs, totalCount) = await _amisSyncService.GetSyncHistoryAsync(searchDto);
                
                return Ok(new
                {
                    Data = syncs,
                    TotalCount = totalCount,
                    Page = searchDto.Page,
                    PageSize = searchDto.PageSize,
                    TotalPages = (int)Math.Ceiling((double)totalCount / searchDto.PageSize)
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving syncs");
                return StatusCode(500, new { Message = "Internal server error" });
            }
        }

        [HttpGet("history")]
        public async Task<ActionResult<object>> GetSyncHistory([FromQuery] AmisSyncSearchDto searchDto)
        {
            try
            {
                var (syncs, totalCount) = await _amisSyncService.GetSyncHistoryAsync(searchDto);
                
                return Ok(new
                {
                    Data = syncs,
                    TotalCount = totalCount,
                    Page = searchDto.Page,
                    PageSize = searchDto.PageSize,
                    TotalPages = (int)Math.Ceiling((double)totalCount / searchDto.PageSize)
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving sync history");
                return StatusCode(500, new { Message = "Internal server error" });
            }
        }

        [HttpGet("{id:int}")]
        public async Task<ActionResult<AmisSyncDto>> GetSync(int id)
        {
            try
            {
                var sync = await _amisSyncService.GetSyncByIdAsync(id);
                if (sync == null)
                {
                    return NotFound(new { Message = "Sync record not found" });
                }

                return Ok(sync);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving sync {SyncId}", id);
                return StatusCode(500, new { Message = "Internal server error" });
            }
        }

        [HttpPost("manual")]
        public async Task<ActionResult<AmisSyncDto>> StartManualSync([FromBody] ManualSyncRequestDto request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var sync = await _amisSyncService.StartManualSyncAsync(request, "Manual");
                return Ok(sync);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error starting manual sync");
                return StatusCode(500, new { Message = "Internal server error" });
            }
        }

        [HttpPost("{id:int}/cancel")]
        public async Task<ActionResult> CancelSync(int id)
        {
            try
            {
                var success = await _amisSyncService.CancelSyncAsync(id, "Manual");
                if (!success)
                {
                    return NotFound(new { Message = "Sync record not found or cannot be cancelled" });
                }

                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error cancelling sync {SyncId}", id);
                return StatusCode(500, new { Message = "Internal server error" });
            }
        }

        [HttpGet("{id:int}/progress")]
        public async Task<ActionResult<SyncProgressDto>> GetSyncProgress(int id)
        {
            try
            {
                var progress = await _amisSyncService.GetSyncProgressAsync(id);
                if (progress == null)
                {
                    return NotFound(new { Message = "Sync record not found" });
                }

                return Ok(progress);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving sync progress {SyncId}", id);
                return StatusCode(500, new { Message = "Internal server error" });
            }
        }

        [HttpGet("{id:int}/errors")]
        public async Task<ActionResult<List<AmisSyncErrorDto>>> GetSyncErrors(int id)
        {
            try
            {
                var errors = await _amisSyncService.GetSyncErrorsAsync(id);
                return Ok(errors);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving sync errors {SyncId}", id);
                return StatusCode(500, new { Message = "Internal server error" });
            }
        }

        [HttpPost("{id:int}/retry")]
        public async Task<ActionResult> RetrySync(int id)
        {
            try
            {
                var success = await _amisSyncService.RetrySyncAsync(id, "Manual");
                if (!success)
                {
                    return NotFound(new { Message = "Sync record not found or cannot be retried" });
                }

                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrying sync {SyncId}", id);
                return StatusCode(500, new { Message = "Internal server error" });
            }
        }

        [HttpPost("errors/{errorId:int}/resolve")]
        public async Task<ActionResult> ResolveSyncError(int errorId, [FromBody] ResolveSyncErrorDto resolveDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var success = await _amisSyncService.ResolveSyncErrorAsync(errorId, resolveDto.ResolutionNotes, "Manual");
                if (!success)
                {
                    return NotFound(new { Message = "Sync error not found" });
                }

                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error resolving sync error {ErrorId}", errorId);
                return StatusCode(500, new { Message = "Internal server error" });
            }
        }

        [HttpGet("configuration")]
        public async Task<ActionResult<AmisSyncConfigurationDto>> GetSyncConfiguration()
        {
            try
            {
                var configuration = await _amisSyncService.GetSyncConfigurationAsync();
                return Ok(configuration);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving sync configuration");
                return StatusCode(500, new { Message = "Internal server error" });
            }
        }

        [HttpPut("configuration")]
        public async Task<ActionResult> UpdateSyncConfiguration([FromBody] AmisSyncConfigurationDto configuration)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var success = await _amisSyncService.UpdateSyncConfigurationAsync(configuration);
                if (!success)
                {
                    return BadRequest(new { Message = "Failed to update configuration" });
                }

                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating sync configuration");
                return StatusCode(500, new { Message = "Internal server error" });
            }
        }

        [HttpPost("test-connection")]
        public async Task<ActionResult> TestAmisConnection()
        {
            try
            {
                var success = await _amisSyncService.TestAmisConnectionAsync();
                return Ok(new { Success = success, Message = success ? "Connection successful" : "Connection failed" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error testing AMIS connection");
                return StatusCode(500, new { Message = "Internal server error" });
            }
        }

        [HttpGet("active")]
        public async Task<ActionResult<List<AmisSyncDto>>> GetActiveSyncs()
        {
            try
            {
                var syncs = await _amisSyncService.GetActiveSyncsAsync();
                return Ok(syncs);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving active syncs");
                return StatusCode(500, new { Message = "Internal server error" });
            }
        }

        [HttpGet("errors/unresolved")]
        public async Task<ActionResult<List<AmisSyncErrorDto>>> GetUnresolvedErrors()
        {
            try
            {
                var errors = await _amisSyncService.GetUnresolvedErrorsAsync();
                return Ok(errors);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving unresolved errors");
                return StatusCode(500, new { Message = "Internal server error" });
            }
        }

        [HttpGet("statistics")]
        public async Task<ActionResult<Dictionary<string, object>>> GetSyncStatistics()
        {
            try
            {
                var statistics = await _amisSyncService.GetSyncStatisticsAsync();
                return Ok(statistics);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving sync statistics");
                return StatusCode(500, new { Message = "Internal server error" });
            }
        }

        [HttpPost("cleanup")]
        public async Task<ActionResult> CleanupOldSyncLogs([FromQuery] int retentionDays = 30)
        {
            try
            {
                await _amisSyncService.CleanupOldSyncLogsAsync(retentionDays);
                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error cleaning up old sync logs");
                return StatusCode(500, new { Message = "Internal server error" });
            }
        }
    }

    public class ResolveSyncErrorDto
    {
        public string ResolutionNotes { get; set; } = string.Empty;
    }
} 