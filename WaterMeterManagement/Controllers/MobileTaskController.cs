using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Security.Claims;
using WaterMeterManagement.Data;
using WaterMeterManagement.DTOs.Mobile;
using WaterMeterManagement.Models;
using WaterMeterManagement.Services.Interfaces;

namespace WaterMeterManagement.Controllers
{
    /// <summary>
    /// Mobile Task Controller - Provides mobile app optimized endpoints
    /// Filters tasks by current authenticated user
    /// </summary>
    [ApiController]
    [Route("api/mobile/tasks")]
    [Authorize] // Require JWT authentication
    public class MobileTaskController : ControllerBase
    {
        private readonly ApplicationDbContext _context;
        private readonly IMeterService _meterService;
        private readonly ILogger<MobileTaskController> _logger;

        public MobileTaskController(ApplicationDbContext context, IMeterService meterService, ILogger<MobileTaskController> logger)
        {
            _context = context;
            _meterService = meterService;
            _logger = logger;
        }

        /// <summary>
        /// Get current user's assigned tasks
        /// </summary>
        [HttpGet("my-assignments")]
        public async Task<ActionResult<List<MobileTaskDto>>> GetMyAssignedTasks(
            [FromQuery] string? status = null,
            [FromQuery] bool includeCompleted = false,
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 50)
        {
            try
            {
                // Get current user ID from JWT token
                var currentUserId = GetCurrentUserId();
                if (currentUserId == null)
                {
                    _logger.LogWarning("Unable to get current user ID from JWT token");
                    return Unauthorized("Invalid user context");
                }

                _logger.LogInformation("Getting assigned tasks for user {UserId}", currentUserId);

                // Build query for user's assigned tasks through TaskAssignments table
                var assignmentQuery = _context.TaskAssignments
                    .Include(ta => ta.Task)
                        .ThenInclude(t => t.Meter)
                    .Include(ta => ta.Task)
                        .ThenInclude(t => t.WorkPackage)
                    .Include(ta => ta.Task)
                        .ThenInclude(t => t.Route)
                    .Where(ta => !ta.IsDeleted && ta.UserId == currentUserId);

                // Apply status filter to task assignment with support for multiple status formats
                if (!string.IsNullOrEmpty(status))
                {
                    var statusLower = status.ToLower();
                    
                    // Handle multiple status formats for "In Progress" status
                    if (statusLower == "inprogress" || statusLower == "in_progress")
                    {
                        assignmentQuery = assignmentQuery.Where(ta => 
                            ta.Task.Status.ToLower() == "inprogress" || 
                            ta.Task.Status.ToLower() == "in progress" ||
                            ta.Task.Status.ToLower() == "in_progress");
                    }
                    else
                    {
                        assignmentQuery = assignmentQuery.Where(ta => ta.Task.Status.ToLower() == statusLower);
                    }
                }

                // Exclude completed tasks unless specifically requested
                if (!includeCompleted)
                {
                    assignmentQuery = assignmentQuery.Where(ta => ta.Task.Status.ToLower() != "completed");
                }

                // Apply sorting - prioritize overdue and high priority tasks
                assignmentQuery = assignmentQuery.OrderBy(ta => ta.Task.DueDate.HasValue && ta.Task.DueDate < DateTime.UtcNow ? 0 : 1) // Overdue first
                            .ThenBy(ta => ta.Task.Priority == "High" ? 0 : ta.Task.Priority == "Medium" ? 1 : 2) // Priority order
                            .ThenBy(ta => ta.Task.DueDate ?? DateTime.MaxValue) // Due date ascending
                            .ThenBy(ta => ta.Task.CreatedAt); // Creation date ascending

                // Apply pagination
                var totalTasks = await assignmentQuery.CountAsync();
                var taskAssignments = await assignmentQuery
                    .Skip((page - 1) * pageSize)
                    .Take(pageSize)
                    .ToListAsync();

                // Extract tasks from assignments
                var tasks = taskAssignments.Select(ta => ta.Task).ToList();

                _logger.LogInformation("Found {Count} tasks for user {UserId} (page {Page}/{PageSize})", 
                    tasks.Count, currentUserId, page, pageSize);

                // Convert to mobile DTOs
                var mobileTasks = new List<MobileTaskDto>();
                foreach (var task in tasks)
                {
                    var mobileTask = await ConvertToMobileTaskDto(task);
                    mobileTasks.Add(mobileTask);
                }

                // 🐛 详细日志：打印后端返回给移动端的数据
                _logger.LogInformation("=== 后端接口返回数据 ===");
                _logger.LogInformation("总任务数: {Count}", mobileTasks.Count);
                
                for (int i = 0; i < Math.Min(mobileTasks.Count, 3); i++) // 只打印前3条
                {
                    var task = mobileTasks[i];
                    var originalTask = tasks[i]; // 获取原始数据库任务
                    _logger.LogInformation("原始DB任务 {Index}: AssignedUserId={DbAssignedUserId}, AssignedTo={DbAssignedTo}",
                        i + 1, originalTask.AssignedUserId, originalTask.AssignedTo);
                    _logger.LogInformation("转换后DTO {Index}: ID={TaskId}, Name={Name}, AssignedUserId={AssignedUserId}, MeterId={MeterId}, MeterNumber={MeterNumber}",
                        i + 1, task.Id, task.Name, task.AssignedUserId, task.MeterId, task.MeterNumber);
                }
                _logger.LogInformation("=== 后端接口返回数据结束 ===");

                // Add pagination headers
                Response.Headers.Add("X-Total-Count", totalTasks.ToString());
                Response.Headers.Add("X-Page", page.ToString());
                Response.Headers.Add("X-Page-Size", pageSize.ToString());

                return Ok(mobileTasks);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting assigned tasks for user");
                return StatusCode(500, "Internal server error occurred while retrieving tasks");
            }
        }

        /// <summary>
        /// Get task detail for mobile app
        /// </summary>
        [HttpGet("{taskId}")]
        public async Task<ActionResult<MobileTaskDetailDto>> GetTaskDetail(int taskId)
        {
            try
            {
                var currentUserId = GetCurrentUserId();
                if (currentUserId == null)
                {
                    return Unauthorized("Invalid user context");
                }

                // Find task through TaskAssignment to ensure user has access
                var taskAssignment = await _context.TaskAssignments
                    .Include(ta => ta.Task)
                        .ThenInclude(t => t.Meter)
                    .Include(ta => ta.Task)
                        .ThenInclude(t => t.WorkPackage)
                    .Include(ta => ta.Task)
                        .ThenInclude(t => t.Route)
                    .Include(ta => ta.Task)
                        .ThenInclude(t => t.TaskAssignments)
                    .Where(ta => ta.TaskId == taskId && !ta.IsDeleted && ta.UserId == currentUserId)
                    .FirstOrDefaultAsync();

                var task = taskAssignment?.Task;

                if (task == null)
                {
                    _logger.LogWarning("Task {TaskId} not found or not assigned to user {UserId}", taskId, currentUserId);
                    return NotFound($"Task {taskId} not found or not accessible");
                }

                var taskDetail = await ConvertToMobileTaskDetailDto(task);
                return Ok(taskDetail);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting task detail for taskId {TaskId}", taskId);
                return StatusCode(500, "Internal server error occurred while retrieving task detail");
            }
        }

        /// <summary>
        /// Update task status (start, complete, pause, etc.)
        /// </summary>
        [HttpPut("{taskId}/status")]
        public async Task<ActionResult> UpdateTaskStatus(int taskId, [FromBody] UpdateTaskStatusRequest request)
        {
            try
            {
                var currentUserId = GetCurrentUserId();
                if (currentUserId == null)
                {
                    return Unauthorized("Invalid user context");
                }

                // Find task through TaskAssignment to ensure user has access
                var taskAssignment = await _context.TaskAssignments
                    .Include(ta => ta.Task)
                    .Where(ta => ta.TaskId == taskId && !ta.IsDeleted && ta.UserId == currentUserId)
                    .FirstOrDefaultAsync();

                var task = taskAssignment?.Task;

                if (task == null)
                {
                    return NotFound($"Task {taskId} not found or not accessible");
                }

                // Update task status and related fields
                var oldStatus = task.Status;
                task.Status = request.Status;
                task.UpdatedAt = DateTime.UtcNow;

                // Handle status-specific updates
                switch (request.Status.ToLower())
                {
                    case "in_progress":
                    case "inprogress":
                        task.StartDate ??= DateTime.UtcNow;
                        break;
                    case "completed":
                        task.CompletedDate = DateTime.UtcNow;
                        task.ProgressPercentage = 100;
                        break;
                    case "paused":
                        // Keep existing dates
                        break;
                }

                // Add optional notes
                if (!string.IsNullOrWhiteSpace(request.Notes))
                {
                    task.Notes = string.IsNullOrWhiteSpace(task.Notes) 
                        ? request.Notes 
                        : $"{task.Notes}\n[{DateTime.UtcNow:yyyy-MM-dd HH:mm}] {request.Notes}";
                }

                await _context.SaveChangesAsync();

                _logger.LogInformation("Task {TaskId} status updated from {OldStatus} to {NewStatus} by user {UserId}", 
                    taskId, oldStatus, request.Status, currentUserId);

                return Ok(new { success = true, message = "Task status updated successfully" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating task status for taskId {TaskId}", taskId);
                return StatusCode(500, "Internal server error occurred while updating task status");
            }
        }

        /// <summary>
        /// Get user's task statistics for mobile dashboard
        /// </summary>
        [HttpGet("summary")]
        public async Task<ActionResult<MobileTaskSummaryDto>> GetTaskSummary()
        {
            try
            {
                var currentUserId = GetCurrentUserId();
                if (currentUserId == null)
                {
                    return Unauthorized("Invalid user context");
                }

                // Get user's tasks through TaskAssignments
                var userTaskAssignments = await _context.TaskAssignments
                    .Include(ta => ta.Task)
                    .Where(ta => !ta.IsDeleted && ta.UserId == currentUserId)
                    .ToListAsync();

                var userTasks = userTaskAssignments.Select(ta => ta.Task).ToList();

                var pendingCount = userTasks.Count(t => t.Status.ToLower() == "pending" || t.Status.ToLower() == "assigned");
                var inProgressCount = userTasks.Count(t => 
                    t.Status.ToLower() == "inprogress" || 
                    t.Status.ToLower() == "in progress" ||
                    t.Status.ToLower() == "in_progress");
                var completedCount = userTasks.Count(t => t.Status.ToLower() == "completed");
                var overdueCount = userTasks.Count(t => t.IsOverdue);
                var today = DateTime.UtcNow.Date;
                var dueTodayCount = userTasks.Count(t => t.DueDate.HasValue && t.DueDate.Value.Date == today);
                var dueTomorrowCount = userTasks.Count(t => t.DueDate.HasValue && t.DueDate.Value.Date == today.AddDays(1));

                // Get urgent/overdue tasks for the summary
                var urgentTasks = userTasks
                    .Where(t => t.IsOverdue || (t.Priority.ToLower() == "high" || t.Priority.ToLower() == "critical"))
                    .Take(5) // Limit to top 5 urgent tasks
                    .Select(t => new UrgentTaskDto
                    {
                        Id = t.Id,
                        Name = t.Name,
                        DueDate = t.DueDate ?? DateTime.MaxValue,
                        HoursOverdue = t.DueDate.HasValue && t.DueDate < DateTime.UtcNow 
                            ? (int)(DateTime.UtcNow - t.DueDate.Value).TotalHours 
                            : 0,
                        Priority = t.Priority
                    })
                    .ToList();

                var summary = new MobileTaskSummaryDto
                {
                    TotalAssigned = userTasks.Count,
                    InProgress = inProgressCount,
                    Completed = completedCount,
                    Overdue = overdueCount,
                    DueToday = dueTodayCount,
                    DueTomorrow = dueTomorrowCount,
                    CompletionRate = userTasks.Count > 0 ? Math.Round((decimal)completedCount / userTasks.Count * 100, 1) : 0,
                    ConsecutiveDaysActive = 1, // Placeholder - would calculate from actual activity data
                    UrgentTasks = urgentTasks
                };

                return Ok(summary);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting task summary for user");
                return StatusCode(500, "Internal server error occurred while retrieving task summary");
            }
        }

        /// <summary>
        /// Get all tasks for mobile sync (not filtered by user)
        /// This endpoint is used by mobile sync services to download all tasks to device
        /// </summary>
        [HttpGet("sync/all")]
        public async Task<ActionResult<List<MobileTaskSyncDto>>> GetAllTasksForSync(
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 100,
            [FromQuery] string? startDate = null,
            [FromQuery] string? endDate = null)
        {
            try
            {
                var currentUserId = GetCurrentUserId();
                if (currentUserId == null)
                {
                    return Unauthorized("Invalid user context");
                }

                _logger.LogInformation("Getting all tasks for sync - Page: {Page}, PageSize: {PageSize}", page, pageSize);

                // Build query for all tasks (not filtered by user for sync purposes)
                var taskQuery = _context.Tasks
                    .Include(t => t.Meter)
                    .Include(t => t.WorkPackage)
                    .Include(t => t.Route)
                    .Include(t => t.TaskAssignments)
                    .Where(t => !t.IsDeleted);

                // Apply date range filter if provided
                if (!string.IsNullOrEmpty(startDate) && DateTime.TryParse(startDate, out var start))
                {
                    taskQuery = taskQuery.Where(t => t.CreatedAt >= start);
                }

                if (!string.IsNullOrEmpty(endDate) && DateTime.TryParse(endDate, out var end))
                {
                    taskQuery = taskQuery.Where(t => t.CreatedAt <= end.AddDays(1));
                }

                // Apply sorting
                taskQuery = taskQuery.OrderByDescending(t => t.CreatedAt)
                    .ThenBy(t => t.DueDate ?? DateTime.MaxValue);

                // Apply pagination
                var totalTasks = await taskQuery.CountAsync();
                var tasks = await taskQuery
                    .Skip((page - 1) * pageSize)
                    .Take(pageSize)
                    .ToListAsync();

                _logger.LogInformation("Found {Count} tasks for sync (total: {Total}, page {Page}/{PageSize})", 
                    tasks.Count, totalTasks, page, pageSize);

                // Convert to sync DTOs
                var syncTasks = tasks.Select(ConvertToMobileTaskSyncDto).ToList();

                // 🐛 详细日志：打印后端Sync接口返回给移动端的数据
                _logger.LogInformation("=== 后端Sync接口返回数据 ===");
                _logger.LogInformation("Sync总任务数: {Count}", syncTasks.Count);
                
                for (int i = 0; i < Math.Min(syncTasks.Count, 3); i++) // 只打印前3条
                {
                    var task = syncTasks[i];
                    var originalTask = tasks[i];
                    _logger.LogInformation("Sync原始DB任务 {Index}: AssignedUserId={DbAssignedUserId}, AssignedTo={DbAssignedTo}",
                        i + 1, originalTask.AssignedUserId, originalTask.AssignedTo);
                    _logger.LogInformation("Sync转换后DTO {Index}: ID={TaskId}, AssignedUserId={AssignedUserId}, MeterId={MeterId}, MeterNumber={MeterNumber}",
                        i + 1, task.Id, task.AssignedUserId, task.MeterId, task.MeterNumber);
                }
                _logger.LogInformation("=== 后端Sync接口返回数据结束 ===");

                // Add pagination headers
                Response.Headers.Add("X-Total-Count", totalTasks.ToString());
                Response.Headers.Add("X-Page", page.ToString());
                Response.Headers.Add("X-Page-Size", pageSize.ToString());
                Response.Headers.Add("X-Total-Pages", ((int)Math.Ceiling((double)totalTasks / pageSize)).ToString());

                return Ok(syncTasks);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting all tasks for sync");
                return StatusCode(500, "Internal server error occurred while retrieving tasks for sync");
            }
        }

        #region Private Helper Methods

        /// <summary>
        /// Get current user ID from JWT token
        /// </summary>
        private int? GetCurrentUserId()
        {
            var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier) ?? User.FindFirst("sub") ?? User.FindFirst("user_id");
            if (userIdClaim != null && int.TryParse(userIdClaim.Value, out var userId))
            {
                return userId;
            }

            // Fallback: try to get from username claim and lookup in database
            var usernameClaim = User.FindFirst(ClaimTypes.Name) ?? User.FindFirst("username");
            if (usernameClaim != null)
            {
                // In this case, we might need to lookup user by username
                // This is a fallback for cases where the mobile app uses username in JWT
                _logger.LogWarning("Could not find user ID in JWT claims, found username: {Username}", usernameClaim.Value);
                // You might want to implement username lookup here if needed
            }

            return null;
        }

        /// <summary>
        /// Convert WorkTask to MobileTaskDto
        /// </summary>
        private async Task<MobileTaskDto> ConvertToMobileTaskDto(WorkTask task)
        {
            return new MobileTaskDto
            {
                Id = task.Id,
                Name = task.Name,
                Description = task.Description,
                Status = task.Status,
                Priority = task.Priority,
                Type = task.Type,
                DueDate = task.DueDate,
                StartDate = task.StartDate,
                Location = task.ServiceAddress ?? task.Location,
                Instructions = task.Instructions,
                Notes = task.Notes,

                // Water meter information
                MeterId = task.MeterId ?? 0,
                MeterNumber = task.Meter?.SerialNumber ?? $"M{task.Id:000000}",
                MeterType = task.Meter?.MeterType ?? "Standard",
                AssetId = task.Meter?.AssetId,
                AccountNumber = task.Meter?.AccountNumber,
                LastReading = task.MeterId.HasValue ? await _meterService.GetEffectiveLastReadingAsync(task.MeterId.Value) : null,
                LastReadingDate = task.Meter?.LastReadingDate,

                // Customer information
                CustomerName = task.Meter?.CustomerName ?? "Unknown Customer",
                CustomerPhone = "+64 21 123 4567", // Placeholder
                CustomerEmail = $"customer{task.Id}@waterservices.co.nz", // Placeholder
                Address = task.Location ?? task.ServiceAddress ?? task.Meter?.FullAddress ?? task.Meter?.Address ?? "Address not available",

                // Location information
                Latitude = task.Latitude.HasValue ? (double?)Convert.ToDouble(task.Latitude.Value) : null,
                Longitude = task.Longitude.HasValue ? (double?)Convert.ToDouble(task.Longitude.Value) : null,

                // Assignment information - 直接使用数据库字段名，无需映射
                AssignedUserId = task.AssignedUserId,
                AssignedDate = task.CreatedAt,
                AssignedBy = task.CreatedBy,
                AssignmentType = "Regular",

                // Progress information
                EstimatedHours = task.EstimatedHours.HasValue ? (int?)Math.Round((double)task.EstimatedHours.Value) : null,
                ProgressPercentage = task.ProgressPercentage,

                // Mobile-specific computed fields
                IsUrgent = task.Priority.ToLower() == "high" || task.Priority.ToLower() == "critical",
                IsOverdue = task.IsOverdue,
                DaysUntilDue = task.DueDate.HasValue ? (int?)(task.DueDate.Value - DateTime.UtcNow).TotalDays : null,

                // Audit fields
                CreatedAt = task.CreatedAt,
                UpdatedAt = task.UpdatedAt
            };
        }

        /// <summary>
        /// Convert WorkTask to MobileTaskDetailDto
        /// </summary>
        private async Task<MobileTaskDetailDto> ConvertToMobileTaskDetailDto(WorkTask task)
        {
            return new MobileTaskDetailDto
            {
                // Base task fields
                Id = task.Id,
                Name = task.Name,
                Description = task.Description,
                Status = task.Status,
                Priority = task.Priority,
                Type = task.Type,
                DueDate = task.DueDate,
                StartDate = task.StartDate,
                Location = task.Location ?? task.ServiceAddress ?? "Unknown Location",
                Instructions = task.Instructions,
                Notes = task.Notes,

                // Water meter information
                MeterId = task.MeterId ?? 0,
                MeterNumber = task.Meter?.SerialNumber ?? $"M{task.Id:000000}",
                MeterType = task.Meter?.MeterType ?? "Standard",
                AssetId = task.Meter?.AssetId,
                AccountNumber = task.Meter?.AccountNumber,
                LastReading = task.MeterId.HasValue ? await _meterService.GetEffectiveLastReadingAsync(task.MeterId.Value) : null,
                LastReadingDate = task.Meter?.LastReadingDate,

                // Customer information
                CustomerName = task.Meter?.CustomerName ?? "Unknown Customer",
                CustomerPhone = "+64 21 123 4567", // Placeholder
                CustomerEmail = $"customer{task.Id}@waterservices.co.nz", // Placeholder
                Address = task.Location ?? task.ServiceAddress ?? task.Meter?.FullAddress ?? task.Meter?.Address ?? "Address not available",

                // Location information
                Latitude = task.Latitude.HasValue ? (double?)Convert.ToDouble(task.Latitude.Value) : null,
                Longitude = task.Longitude.HasValue ? (double?)Convert.ToDouble(task.Longitude.Value) : null,

                // Assignment information - 统一使用数据库字段名
                AssignedUserId = task.AssignedUserId,
                AssignedDate = task.CreatedAt,
                AssignedBy = task.CreatedBy,
                AssignmentType = "Regular",

                // Progress information
                EstimatedHours = task.EstimatedHours.HasValue ? (int?)Math.Round((double)task.EstimatedHours.Value) : null,
                ProgressPercentage = task.ProgressPercentage,

                // Mobile-specific computed fields
                IsUrgent = task.Priority.ToLower() == "high" || task.Priority.ToLower() == "critical",
                IsOverdue = task.IsOverdue,
                DaysUntilDue = task.DueDate.HasValue ? (int?)(task.DueDate.Value - DateTime.UtcNow).TotalDays : null,

                // Audit fields
                CreatedAt = task.CreatedAt,
                UpdatedAt = task.UpdatedAt,

                // Additional detail fields
                WorkPackageName = task.WorkPackage?.Name,
                RouteName = task.Route?.Name,
                CompletedDate = task.CompletedDate,
                ActualHours = task.ActualHours.HasValue ? (double?)Convert.ToDouble(task.ActualHours.Value) : null,

                // Extended mobile detail fields (will be enhanced later with real data)
                MeterInfo = task.Meter != null ? new MeterInfoDto
                {
                    Id = task.Meter.Id,
                    MeterId = task.Meter.Id,
                    MeterNumber = task.Meter.SerialNumber,
                    SerialNumber = task.Meter.SerialNumber,
                    MeterType = task.Meter.MeterType,
                    Brand = task.Meter.Brand,
                    Model = task.Meter.Model,
                    Size = task.Meter.MeterType,
                    Type = task.Meter.MeterType,
                    AssetId = task.Meter.AssetId,
                    AccountNumber = task.Meter.AccountNumber,
                    LastReading = task.MeterId.HasValue ? await _meterService.GetEffectiveLastReadingAsync(task.MeterId.Value) : null,
                    LastReadingDate = task.Meter.LastReadingDate,
                    InstallDate = task.Meter.InstallDate,
                    ManufacturerName = task.Meter.Brand,
                    Status = "Active", // Placeholder
                    Location = task.ServiceAddress ?? task.Location
                } : null,

                // Assignment information
                AssignmentInfo = new TaskAssignmentInfoDto
                {
                    AssignedUserId = task.AssignedUserId,
                    AssignedUserName = "Current User", // Placeholder
                    AssignedDate = task.CreatedAt,
                    AcceptedDate = task.StartDate,
                    CanReject = task.Status.ToLower() == "assigned" || task.Status.ToLower() == "pending",
                    CanAccept = task.Status.ToLower() == "assigned" || task.Status.ToLower() == "pending",
                    Notes = task.Notes ?? "",
                    AssignmentType = "Regular",
                    AssignmentPriority = task.Priority
                },


            };
        }

        /// <summary>
        /// Convert WorkTask to MobileTaskSyncDto
        /// </summary>
        private MobileTaskSyncDto ConvertToMobileTaskSyncDto(WorkTask task)
        {
            return new MobileTaskSyncDto
            {
                Id = task.Id,
                AssignedUserId = task.AssignedUserId ?? 0,
                MeterId = task.MeterId ?? 0,
                MeterNumber = task.Meter?.SerialNumber ?? $"M{task.Id:000000}",
                TaskType = task.Type,
                Status = task.Status,
                Priority = task.Priority,
                ScheduledDate = task.StartDate,
                DueDate = task.DueDate,
                Location = task.Location ?? task.ServiceAddress,
                Instructions = task.Instructions,
                Notes = task.Notes,
                WorkPackageName = task.WorkPackage?.Name,
                RouteName = task.Route?.Name,
                CreatedAt = task.CreatedAt,
                UpdatedAt = task.UpdatedAt
            };
        }

        #endregion
    }

    #region Request DTOs

    /// <summary>
    /// Request DTO for updating task status
    /// </summary>
    public class UpdateTaskStatusRequest
    {
        public string Status { get; set; } = string.Empty;
        public string? Notes { get; set; }
    }

    #endregion
} 