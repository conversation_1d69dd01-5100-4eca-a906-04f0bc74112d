using Microsoft.AspNetCore.Mvc;
using System.Text.Json;
using WaterMeterManagement.DTOs;
using WaterMeterManagement.Services.Interfaces;

namespace WaterMeterManagement.Controllers
{
    [ApiController]
    [Route("api/dynamic-select")]
    public class DynamicSelectController : ControllerBase
    {
        private readonly IDynamicDataService _dynamicDataService;
        private readonly ILogger<DynamicSelectController> _logger;

        public DynamicSelectController(IDynamicDataService dynamicDataService, ILogger<DynamicSelectController> logger)
        {
            _dynamicDataService = dynamicDataService;
            _logger = logger;
        }

        /// <summary>
        /// Get dynamic select options
        /// </summary>
        /// <param name="strategy">Strategy type (database, enum, api, custom)</param>
        /// <param name="dataSource">Data source identifier</param>
        /// <param name="params">JSON parameters</param>
        /// <returns>Dynamic select options</returns>
        [HttpGet("options")]
        public async Task<ActionResult<DynamicSelectResponseDto>> GetOptions(
            [FromQuery] string strategy,
            [FromQuery] string dataSource,
            [FromQuery] string? @params = "{}")
        {
            try
            {
                if (string.IsNullOrEmpty(strategy))
                {
                    return BadRequest(new DynamicSelectResponseDto
                    {
                        Success = false,
                        ErrorMessage = "Strategy parameter is required"
                    });
                }

                if (string.IsNullOrEmpty(dataSource))
                {
                    return BadRequest(new DynamicSelectResponseDto
                    {
                        Success = false,
                        ErrorMessage = "DataSource parameter is required"
                    });
                }

                // Parse parameters
                Dictionary<string, object> parameters;
                try
                {
                    parameters = string.IsNullOrEmpty(@params) || @params == "{}" 
                        ? new Dictionary<string, object>()
                        : JsonSerializer.Deserialize<Dictionary<string, object>>(@params) ?? new Dictionary<string, object>();
                }
                catch (JsonException ex)
                {
                    _logger.LogWarning(ex, "Invalid JSON parameters: {Params}", @params);
                    return BadRequest(new DynamicSelectResponseDto
                    {
                        Success = false,
                        ErrorMessage = "Invalid JSON format in params parameter"
                    });
                }

                var result = await _dynamicDataService.GetOptionsAsync(strategy, dataSource, parameters);
                
                if (!result.Success)
                {
                    return BadRequest(result);
                }

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting dynamic select options");
                return StatusCode(500, new DynamicSelectResponseDto
                {
                    Success = false,
                    ErrorMessage = "Internal server error"
                });
            }
        }

        /// <summary>
        /// Get available strategies
        /// </summary>
        /// <returns>List of available strategies</returns>
        [HttpGet("strategies")]
        public async Task<ActionResult<List<string>>> GetAvailableStrategies()
        {
            try
            {
                var strategies = await _dynamicDataService.GetAvailableStrategiesAsync();
                return Ok(strategies);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting available strategies");
                return StatusCode(500, new { Message = "Internal server error" });
            }
        }

        /// <summary>
        /// Get available data sources for a strategy
        /// </summary>
        /// <param name="strategy">Strategy name</param>
        /// <returns>List of available data sources</returns>
        [HttpGet("strategies/{strategy}/datasources")]
        public async Task<ActionResult<List<string>>> GetAvailableDataSources(string strategy)
        {
            try
            {
                if (string.IsNullOrEmpty(strategy))
                {
                    return BadRequest(new { Message = "Strategy parameter is required" });
                }

                var dataSources = await _dynamicDataService.GetAvailableDataSourcesAsync(strategy);
                return Ok(dataSources);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting available data sources for strategy: {Strategy}", strategy);
                return StatusCode(500, new { Message = "Internal server error" });
            }
        }

        /// <summary>
        /// Get dynamic select options via POST (for complex parameters)
        /// </summary>
        /// <param name="request">Dynamic select request</param>
        /// <returns>Dynamic select options</returns>
        [HttpPost("options")]
        public async Task<ActionResult<DynamicSelectResponseDto>> GetOptionsPost([FromBody] DynamicSelectRequestDto request)
        {
            try
            {
                if (string.IsNullOrEmpty(request.Strategy))
                {
                    return BadRequest(new DynamicSelectResponseDto
                    {
                        Success = false,
                        ErrorMessage = "Strategy is required"
                    });
                }

                if (string.IsNullOrEmpty(request.DataSource))
                {
                    return BadRequest(new DynamicSelectResponseDto
                    {
                        Success = false,
                        ErrorMessage = "DataSource is required"
                    });
                }

                var result = await _dynamicDataService.GetOptionsAsync(request.Strategy, request.DataSource, request.Params);
                
                if (!result.Success)
                {
                    return BadRequest(result);
                }

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting dynamic select options via POST");
                return StatusCode(500, new DynamicSelectResponseDto
                {
                    Success = false,
                    ErrorMessage = "Internal server error"
                });
            }
        }
    }
}
