using Microsoft.AspNetCore.Mvc;
using WaterMeterManagement.DTOs;
using WaterMeterManagement.Services.Interfaces;

namespace WaterMeterManagement.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class AddressController : ControllerBase
    {
        private readonly IAddressService _addressService;
        private readonly ILogger<AddressController> _logger;

        public AddressController(
            IAddressService addressService,
            ILogger<AddressController> logger)
        {
            _addressService = addressService;
            _logger = logger;
        }

        /// <summary>
        /// Parse address and return structured components
        /// </summary>
        /// <param name="request">Address parse request</param>
        /// <returns>Parsed address data</returns>
        [HttpPost("parse")]
        public async Task<ActionResult<AddressParseResultDto>> ParseAddress([FromBody] AddressParseRequestDto request)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(request.Address))
                {
                    return BadRequest(new { Message = "Address is required" });
                }

                var result = await _addressService.ParseAddressAsync(request.Address);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error parsing address: {Address}", request.Address);
                return StatusCode(500, new { Message = "Internal server error" });
            }
        }

        /// <summary>
        /// Get address suggestions for autocomplete
        /// </summary>
        /// <param name="input">Partial address input</param>
        /// <param name="countryCode">Country code (default: NZ)</param>
        /// <returns>List of address suggestions</returns>
        [HttpGet("suggestions")]
        public async Task<ActionResult<List<AddressSuggestionDto>>> GetAddressSuggestions(
            [FromQuery] string input,
            [FromQuery] string countryCode = "NZ")
        {
            try
            {
                if (string.IsNullOrWhiteSpace(input) || input.Length < 3)
                {
                    return Ok(new List<AddressSuggestionDto>());
                }

                var suggestions = await _addressService.GetAddressSuggestionsAsync(input, countryCode);
                return Ok(suggestions);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting address suggestions for: {Input}", input);
                return StatusCode(500, new { Message = "Internal server error" });
            }
        }

        /// <summary>
        /// Parse address by Place ID (for when user selects from suggestions)
        /// </summary>
        /// <param name="placeId">Google Place ID</param>
        /// <returns>Parsed address data</returns>
        [HttpGet("parse-by-place-id/{placeId}")]
        public async Task<ActionResult<AddressParseResultDto>> ParseAddressByPlaceId(string placeId)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(placeId))
                {
                    return BadRequest(new { Message = "Place ID is required" });
                }

                // For now, we'll implement this later if needed
                // This would use Google Places Details API
                return BadRequest(new { Message = "Not implemented yet" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error parsing address by place ID: {PlaceId}", placeId);
                return StatusCode(500, new { Message = "Internal server error" });
            }
        }
    }
}
