using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using WaterMeterManagement.DTOs;
using WaterMeterManagement.DTOs.UserRole;
using WaterMeterManagement.Services.Interfaces;

namespace WaterMeterManagement.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class UserRoleController : ControllerBase
    {
        private readonly IUserRoleService _userRoleService;

        public UserRoleController(IUserRoleService userRoleService)
        {
            _userRoleService = userRoleService;
        }

        [HttpGet("user/{userId}/roles")]
        public async Task<ActionResult<ApiResponse<List<UserRoleDto>>>> GetUserRoles(int userId)
        {
            var roles = await _userRoleService.GetUserRolesAsync(userId);
            return Ok(ApiResponse<List<UserRoleDto>>.SuccessResult(roles, "User roles retrieved successfully"));
        }

        [HttpGet("role/{roleId}/users")]
        public async Task<ActionResult<ApiResponse<List<UserRoleDto>>>> GetRoleUsers(int roleId)
        {
            var users = await _userRoleService.GetRoleUsersAsync(roleId);
            return Ok(ApiResponse<List<UserRoleDto>>.SuccessResult(users, "Role users retrieved successfully"));
        }

        [HttpPost("user/{userId}/roles")]
        public async Task<ActionResult<ApiResponse<UserRoleAssignmentResultDto>>> AssignRolesToUser(
            int userId,
            [FromBody] AssignRolesDto assignRolesDto)
        {
            var currentUser = User.Identity?.Name ?? "System";
            var result = await _userRoleService.AssignRolesToUserAsync(userId, assignRolesDto.RoleIds, currentUser);
            
            var message = result.ErrorCount == 0 
                ? "All roles assigned successfully"
                : $"Partial success: {result.SuccessCount} succeeded, {result.ErrorCount} failed";

            return Ok(ApiResponse<UserRoleAssignmentResultDto>.SuccessResult(result, message));
        }

        [HttpPost("user/{userId}/role/{roleId}")]
        public async Task<ActionResult<ApiResponse<RoleAssignmentDetailDto>>> AddRoleToUser(int userId, int roleId)
        {
            var currentUser = User.Identity?.Name ?? "System";
            var result = await _userRoleService.AddRoleToUserAsync(userId, roleId, currentUser);

            return Ok(ApiResponse<RoleAssignmentDetailDto>.SuccessResult(result, result.Message));
        }

        [HttpDelete("user/{userId}/role/{roleId}")]
        public async Task<ActionResult<ApiResponse>> RemoveRoleFromUser(int userId, int roleId)
        {
            await _userRoleService.RemoveRoleFromUserAsync(userId, roleId);
            return Ok(ApiResponse.Success("Role removed successfully"));
        }

        [HttpGet("user/{userId}/role/{roleId}/exists")]
        public async Task<ActionResult<ApiResponse<bool>>> UserHasRole(int userId, int roleId)
        {
            var hasRole = await _userRoleService.UserHasRoleAsync(userId, roleId);
            return Ok(ApiResponse<bool>.SuccessResult(hasRole, "Role check completed"));
        }

        [HttpGet("available-roles")]
        public async Task<ActionResult<ApiResponse<List<Models.Role>>>> GetAvailableRoles()
        {
            var roles = await _userRoleService.GetAvailableRolesAsync();
            return Ok(ApiResponse<List<Models.Role>>.SuccessResult(roles, "Available roles retrieved successfully"));
        }
    }
}
