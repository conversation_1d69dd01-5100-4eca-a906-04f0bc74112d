using Microsoft.AspNetCore.Mvc;
using WaterMeterManagement.DTOs;
using WaterMeterManagement.Services.Interfaces;
using Microsoft.EntityFrameworkCore;
using WaterMeterManagement.Data;

namespace WaterMeterManagement.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class ConfigurationController : ControllerBase
    {
        private readonly IConfigurationService _configurationService;
        private readonly ILogger<ConfigurationController> _logger;
        private readonly ApplicationDbContext _context;

        public ConfigurationController(
            IConfigurationService configurationService,
            ILogger<ConfigurationController> logger,
            ApplicationDbContext context)
        {
            _configurationService = configurationService;
            _logger = logger;
            _context = context;
        }

        /// <summary>
        /// Get configurations by scope and category
        /// </summary>
        [HttpGet]
        public async Task<IActionResult> GetConfigurations([FromQuery] GetConfigurationRequest request)
        {
            try
            {
                var configurations = await _configurationService.GetConfigurationsAsync(request);
                return Ok(configurations);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting configurations for scope: {Scope}", request.Scope);
                return StatusCode(500, new { message = "Error retrieving configurations", error = ex.Message });
            }
        }

        /// <summary>
        /// Update multiple configurations in bulk
        /// </summary>
        [HttpPost("update")]
        public async Task<IActionResult> UpdateConfigurations([FromBody] UpdateConfigurationRequest request)
        {
            try
            {
                // TODO: Get current user ID from authentication context
                var currentUserId = 1; // Placeholder

                var result = await _configurationService.UpdateConfigurationsAsync(request, currentUserId);
                
                if (result.Success)
                {
                    return Ok(result);
                }
                
                return BadRequest(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating configurations");
                return StatusCode(500, new { message = "Error updating configurations", error = ex.Message });
            }
        }

        /// <summary>
        /// Get configuration templates for a scope
        /// </summary>
        [HttpGet("templates/{scope}")]
        public async Task<IActionResult> GetConfigurationTemplates(string scope)
        {
            try
            {
                var templates = await _configurationService.GetConfigurationTemplatesAsync(scope);
                return Ok(templates);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting configuration templates for scope: {Scope}", scope);
                return StatusCode(500, new { message = "Error retrieving configuration templates", error = ex.Message });
            }
        }

        /// <summary>
        /// Get a specific configuration value
        /// </summary>
        [HttpGet("{scope}/{category}/{key}")]
        public async Task<IActionResult> GetConfigurationValue(string scope, string category, string key, [FromQuery] int? userId = null, [FromQuery] int? roleId = null)
        {
            try
            {
                var value = await _configurationService.GetConfigurationValueAsync<object>(scope, category, key, userId, roleId);
                return Ok(new { key, value });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting configuration value: {Scope}.{Category}.{Key}", scope, category, key);
                return StatusCode(500, new { message = "Error retrieving configuration value", error = ex.Message });
            }
        }

        /// <summary>
        /// Set a specific configuration value
        /// </summary>
        [HttpPost("{scope}/{category}/{key}")]
        public async Task<IActionResult> SetConfigurationValue(string scope, string category, string key, [FromBody] object value, [FromQuery] int? userId = null, [FromQuery] int? roleId = null)
        {
            try
            {
                var success = await _configurationService.SetConfigurationValueAsync(scope, category, key, value, userId, roleId);
                
                if (success)
                {
                    return Ok(new { message = "Configuration value updated successfully" });
                }
                
                return BadRequest(new { message = "Failed to update configuration value" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error setting configuration value: {Scope}.{Category}.{Key}", scope, category, key);
                return StatusCode(500, new { message = "Error setting configuration value", error = ex.Message });
            }
        }

        /// <summary>
        /// Reset configurations to defaults
        /// </summary>
        [HttpPost("reset")]
        public async Task<IActionResult> ResetToDefaults([FromBody] ResetConfigurationRequest request)
        {
            try
            {
                var result = await _configurationService.ResetToDefaultsAsync(request.Scope, request.Category, request.UserId, request.RoleId);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error resetting configurations to defaults");
                return StatusCode(500, new { message = "Error resetting configurations", error = ex.Message });
            }
        }

        /// <summary>
        /// Initialize default configuration templates (development/admin use)
        /// </summary>
        [HttpPost("initialize")]
        public async Task<IActionResult> InitializeDefaults()
        {
            try
            {
                await _configurationService.InitializeDefaultConfigurationsAsync();
                return Ok(new { success = true, message = "Default configurations initialized successfully" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error initializing default configurations");
                return StatusCode(500, new { success = false, message = "Failed to initialize default configurations" });
            }
        }

        [HttpPost("clear-and-reinitialize")]
        public async Task<IActionResult> ClearAndReinitialize()
        {
            try
            {
                _logger.LogInformation("🔍 Starting clear-and-reinitialize process");
                
                // Clear existing configuration templates
                var existingTemplates = await _context.ConfigurationTemplates.ToListAsync();
                _logger.LogInformation($"🔍 Found {existingTemplates.Count} existing configuration templates");
                
                if (existingTemplates.Any())
                {
                    _context.ConfigurationTemplates.RemoveRange(existingTemplates);
                    await _context.SaveChangesAsync();
                    _logger.LogInformation($"Cleared {existingTemplates.Count} existing configuration templates");
                }

                // Clear existing configurations
                var existingConfigs = await _context.Configurations.ToListAsync();
                _logger.LogInformation($"🔍 Found {existingConfigs.Count} existing configurations");
                
                if (existingConfigs.Any())
                {
                    _context.Configurations.RemoveRange(existingConfigs);
                    await _context.SaveChangesAsync();
                    _logger.LogInformation($"Cleared {existingConfigs.Count} existing configurations");
                }

                // Reinitialize
                _logger.LogInformation("🔍 Starting InitializeDefaultConfigurationsAsync");
                await _configurationService.InitializeDefaultConfigurationsAsync();
                
                // Verify the result
                var finalTemplateCount = await _context.ConfigurationTemplates.CountAsync();
                var finalConfigCount = await _context.Configurations.CountAsync();
                
                _logger.LogInformation($"🔍 After reinitialization: {finalTemplateCount} templates, {finalConfigCount} configurations");
                
                return Ok(new { 
                    success = true, 
                    message = "Configurations cleared and reinitialized successfully",
                    clearedTemplates = existingTemplates.Count,
                    clearedConfigurations = existingConfigs.Count,
                    finalTemplateCount = finalTemplateCount,
                    finalConfigCount = finalConfigCount
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error clearing and reinitializing configurations");
                return StatusCode(500, new { success = false, message = "Failed to clear and reinitialize configurations" });
            }
        }

        [HttpPost("force-reinitialize")]
        public async Task<IActionResult> ForceReinitialize()
        {
            try
            {
                // Just reinitialize without clearing (will update existing templates)
                await _configurationService.InitializeDefaultConfigurationsAsync();
                
                return Ok(new { 
                    success = true, 
                    message = "Configuration templates force reinitialized successfully"
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error force reinitializing configurations");
                return StatusCode(500, new { success = false, message = "Failed to force reinitialize configurations" });
            }
        }

        /// <summary>
        /// Validate a configuration value
        /// </summary>
        [HttpPost("validate")]
        public async Task<IActionResult> ValidateConfiguration([FromBody] ValidateConfigurationRequest request)
        {
            try
            {
                var (isValid, errorMessage) = await _configurationService.ValidateConfigurationAsync(request.TemplateKey, request.Value);
                
                return Ok(new 
                { 
                    isValid, 
                    errorMessage,
                    message = isValid ? "Validation passed" : "Validation failed"
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating configuration");
                return StatusCode(500, new { message = "Error validating configuration", error = ex.Message });
            }
        }
    }

    /// <summary>
    /// Reset configuration request DTO
    /// </summary>
    public class ResetConfigurationRequest
    {
        public string Scope { get; set; } = string.Empty;
        public string Category { get; set; } = string.Empty;
        public int? UserId { get; set; }
        public int? RoleId { get; set; }
    }

    /// <summary>
    /// Validate configuration request DTO
    /// </summary>
    public class ValidateConfigurationRequest
    {
        public string TemplateKey { get; set; } = string.Empty;
        public object Value { get; set; } = new();
    }
} 