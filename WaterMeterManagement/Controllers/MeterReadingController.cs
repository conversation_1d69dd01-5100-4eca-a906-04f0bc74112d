using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using WaterMeterManagement.DTOs;
using WaterMeterManagement.Services;
using WaterMeterManagement.Services.Interfaces;

namespace WaterMeterManagement.Controllers
{
    [ApiController]
    [Route("api/meter-reading")]
    public class MeterReadingController : ControllerBase
    {
        private readonly IMeterReadingService _meterReadingService;
        private readonly IPhotoService _photoService;
        private readonly ILogger<MeterReadingController> _logger;

        public MeterReadingController(
            IMeterReadingService meterReadingService,
            IPhotoService photoService,
            ILogger<MeterReadingController> logger)
        {
            _meterReadingService = meterReadingService;
            _photoService = photoService;
            _logger = logger;
        }

        #region Reading Management

        /// <summary>
        /// Get meter readings with search and pagination
        /// </summary>
        [HttpGet]
        public async Task<ActionResult<MeterReadingSearchResultDto>> GetReadings([FromQuery] MeterReadingSearchDto searchDto)
        {
            try
            {
                var result = await _meterReadingService.GetReadingsAsync(searchDto);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting meter readings");
                return StatusCode(500, new { message = "Internal server error" });
            }
        }

        /// <summary>
        /// Get meter reading by ID
        /// </summary>
        [HttpGet("{id}")]
        public async Task<ActionResult<MeterReadingListDto>> GetReading(int id)
        {
            try
            {
                var reading = await _meterReadingService.GetReadingByIdAsync(id);
                if (reading == null)
                {
                    return NotFound(new { message = $"Reading with id {id} not found" });
                }
                return Ok(reading);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting meter reading {Id}", id);
                return StatusCode(500, new { message = "Internal server error" });
            }
        }

        /// <summary>
        /// Create new meter reading
        /// </summary>
        [HttpPost]
        public async Task<ActionResult<MeterReadingListDto>> CreateReading([FromBody] CreateMeterReadingDto createDto)
        {
            try
            {
                var username = User.Identity?.Name ?? "System";
                var reading = await _meterReadingService.CreateReadingAsync(createDto, username);
                return CreatedAtAction(nameof(GetReading), new { id = reading.Id }, reading);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating meter reading");
                return StatusCode(500, new { message = "Internal server error" });
            }
        }

        /// <summary>
        /// Update meter reading
        /// </summary>
        [HttpPut("{id}")]
        public async Task<ActionResult<MeterReadingListDto>> UpdateReading(int id, [FromBody] UpdateMeterReadingDto updateDto)
        {
            try
            {
                var username = User.Identity?.Name ?? "System";
                var reading = await _meterReadingService.UpdateReadingAsync(id, updateDto, username);
                return Ok(reading);
            }
            catch (ArgumentException ex)
            {
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating meter reading {Id}", id);
                return StatusCode(500, new { message = "Internal server error" });
            }
        }

        /// <summary>
        /// Delete meter reading
        /// </summary>
        [HttpDelete("{id}")]
        public async Task<ActionResult> DeleteReading(int id)
        {
            try
            {
                var username = User.Identity?.Name ?? "System";
                var success = await _meterReadingService.DeleteReadingAsync(id, username);
                if (!success)
                {
                    return NotFound(new { message = $"Reading with id {id} not found" });
                }
                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting meter reading {Id}", id);
                return StatusCode(500, new { message = "Internal server error" });
            }
        }

        /// <summary>
        /// Confirm meter reading
        /// </summary>
        [HttpPost("{id}/confirm")]
        public async Task<ActionResult> ConfirmReading(int id)
        {
            try
            {
                var username = User.Identity?.Name ?? "System";
                var success = await _meterReadingService.ConfirmReadingAsync(id, username);
                if (!success)
                {
                    return NotFound(new { message = $"Reading with id {id} not found" });
                }
                return Ok(new { message = "Reading confirmed successfully" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error confirming meter reading {Id}", id);
                return StatusCode(500, new { message = "Internal server error" });
            }
        }

        /// <summary>
        /// Correct meter reading
        /// </summary>
        [HttpPost("{id}/correct")]
        public async Task<ActionResult> CorrectReading(int id, [FromBody] CorrectReadingDto correctDto)
        {
            try
            {
                var username = User.Identity?.Name ?? "System";
                var success = await _meterReadingService.CorrectReadingAsync(id, correctDto.CorrectedValue, correctDto.Reason, username);
                if (!success)
                {
                    return NotFound(new { message = $"Reading with id {id} not found" });
                }
                return Ok(new { message = "Reading corrected successfully" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error correcting meter reading {Id}", id);
                return StatusCode(500, new { message = "Internal server error" });
            }
        }

        /// <summary>
        /// Upload photo for meter reading
        /// </summary>
        [HttpPost("{readingId}/photos")]
        public async Task<ActionResult<PhotoUploadResponseDto>> UploadPhoto(
            int readingId,
            [FromForm] PhotoUploadRequestDto request)
        {
            try
            {
                _logger.LogInformation("=== PHOTO UPLOAD START === Reading ID: {ReadingId}, File: {FileName}, Size: {FileSize}",
                    readingId, request.OriginalFileName, request.FileSize);

                var username = User.Identity?.Name ?? "System";
                var userId = GetCurrentUserId();

                var validation = await _photoService.ValidatePhotoSizeAsync((int)request.FileSize);
                if (!validation)
                {
                    return BadRequest(new PhotoUploadResponseDto
                    {
                        Success = false,
                        Message = "File size exceeds maximum limit"
                    });
                }

                using var stream = request.Photo.OpenReadStream();
                var photoBytes = new byte[stream.Length];
                await stream.ReadAsync(photoBytes, 0, (int)stream.Length);

                var mobilePhotoDto = new DTOs.Mobile.MobilePhotoDto
                {
                    FileName = request.OriginalFileName,
                    Base64Data = Convert.ToBase64String(photoBytes),
                    FileSizeBytes = (int)request.FileSize,
                    MimeType = request.MimeType
                };

                _logger.LogInformation("Calling PhotoService.UploadSinglePhotoAsync for reading {ReadingId}", readingId);
                var result = await _photoService.UploadSinglePhotoAsync(readingId, mobilePhotoDto, userId);
                _logger.LogInformation("PhotoService result: Success={Success}, Error={Error}", result.Success, result.Error);

                return Ok(new PhotoUploadResponseDto
                {
                    Success = result.Success,
                    Message = result.Success ? "Photo uploaded successfully" : result.Error,
                    PhotoId = result.PhotoId,
                    CloudflareUrl = result.Url,
                    ThumbnailUrl = result.Url,
                    OriginalFileName = result.OriginalFileName,
                    FileSize = request.FileSize,
                    UploadTime = DateTime.UtcNow
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error uploading photo for reading {ReadingId}", readingId);
                return StatusCode(500, new PhotoUploadResponseDto
                {
                    Success = false,
                    Message = "Internal server error"
                });
            }
        }

        /// <summary>
        /// Get photos for meter reading
        /// </summary>
        [HttpGet("{readingId}/photos")]
        public async Task<ActionResult<List<ReadingPhotoDto>>> GetReadingPhotos(int readingId)
        {
            try
            {
                var photos = await _photoService.GetReadingPhotosAsync(readingId);
                return Ok(photos);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting photos for reading {ReadingId}", readingId);
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Delete photo
        /// </summary>
        [HttpDelete("photos/{photoId}")]
        public async Task<ActionResult> DeletePhoto(int photoId)
        {
            try
            {
                var (success, message) = await _photoService.DeletePhotoAsync(photoId, 1); // TODO: Get actual user ID
                if (!success)
                {
                    return NotFound("Photo not found");
                }
                return Ok();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting photo {PhotoId}", photoId);
                return StatusCode(500, "Internal server error");
            }
        }

        #endregion

        #region Photo Management



        /// <summary>
        /// Search photos
        /// </summary>
        [HttpGet("photos/search")]
        public async Task<ActionResult<List<ReadingPhotoDto>>> SearchPhotos([FromQuery] PhotoSearchDto searchDto)
        {
            try
            {
                var photos = await _meterReadingService.SearchPhotosAsync(searchDto);
                return Ok(photos);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error searching photos");
                return StatusCode(500, new { message = "Internal server error" });
            }
        }

        /// <summary>
        /// Override OCR result for a photo
        /// </summary>
        [HttpPost("photos/{photoId}/override-ocr")]
        public async Task<ActionResult> OverrideOCRResult(int photoId, [FromBody] OverrideOCRDto overrideDto)
        {
            try
            {
                var username = User.Identity?.Name ?? "System";
                var success = await _meterReadingService.OverrideOCRResultAsync(photoId, overrideDto.CorrectedValue, overrideDto.Reason, username);
                if (!success)
                {
                    return NotFound(new { message = $"Photo with id {photoId} not found" });
                }
                return Ok(new { message = "OCR result overridden successfully" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error overriding OCR result for photo {PhotoId}", photoId);
                return StatusCode(500, new { message = "Internal server error" });
            }
        }

        /// <summary>
        /// Update photo quality
        /// </summary>
        [HttpPut("photos/{photoId}/quality")]
        public async Task<ActionResult> UpdatePhotoQuality(int photoId, [FromBody] UpdatePhotoQualityDto qualityDto)
        {
            try
            {
                var username = User.Identity?.Name ?? "System";
                var success = await _meterReadingService.UpdatePhotoQualityAsync(photoId, qualityDto.QualityScore, qualityDto.QualityStatus, username);
                if (!success)
                {
                    return NotFound("Photo not found");
                }
                return Ok(new { message = "Photo quality updated successfully" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating photo quality for photo {PhotoId}", photoId);
                return StatusCode(500, new { message = "Internal server error" });
            }
        }

        /// <summary>
        /// Get photo by ID
        /// </summary>
        [HttpGet("photos/{photoId}")]
        public async Task<ActionResult<ReadingPhotoDto>> GetPhotoById(int photoId)
        {
            try
            {
                var photo = await _meterReadingService.GetPhotoByIdAsync(photoId);
                if (photo == null)
                {
                    return NotFound("Photo not found");
                }
                return Ok(photo);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting photo {PhotoId}", photoId);
                return StatusCode(500, new { message = "Internal server error" });
            }
        }

        #endregion

        #region Anomaly Management

        /// <summary>
        /// Get anomalies with search
        /// </summary>
        [HttpGet("anomalies")]
        public async Task<ActionResult<List<ReadingAnomalyDto>>> GetAnomalies([FromQuery] AnomalySearchDto searchDto)
        {
            try
            {
                var anomalies = await _meterReadingService.GetAnomaliesAsync(searchDto);
                return Ok(anomalies);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting anomalies");
                return StatusCode(500, new { message = "Internal server error" });
            }
        }

        /// <summary>
        /// Get unresolved anomalies
        /// </summary>
        [HttpGet("anomalies/unresolved")]
        public async Task<ActionResult<List<ReadingAnomalyDto>>> GetUnresolvedAnomalies()
        {
            try
            {
                var anomalies = await _meterReadingService.GetUnresolvedAnomaliesAsync();
                return Ok(anomalies);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting unresolved anomalies");
                return StatusCode(500, new { message = "Internal server error" });
            }
        }

        /// <summary>
        /// Assign anomaly to user
        /// </summary>
        [HttpPost("anomalies/{anomalyId}/assign")]
        public async Task<ActionResult> AssignAnomaly(int anomalyId, [FromBody] AssignAnomalyDto assignDto)
        {
            try
            {
                var username = User.Identity?.Name ?? "System";
                var success = await _meterReadingService.AssignAnomalyAsync(anomalyId, assignDto.AssignedUserId, username);
                if (!success)
                {
                    return NotFound(new { message = $"Anomaly with id {anomalyId} not found" });
                }
                return Ok(new { message = "Anomaly assigned successfully" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error assigning anomaly {AnomalyId}", anomalyId);
                return StatusCode(500, new { message = "Internal server error" });
            }
        }

        /// <summary>
        /// Resolve anomaly
        /// </summary>
        [HttpPost("anomalies/{anomalyId}/resolve")]
        public async Task<ActionResult> ResolveAnomaly(int anomalyId, [FromBody] ResolveAnomalyDto resolveDto)
        {
            try
            {
                var username = User.Identity?.Name ?? "System";
                var success = await _meterReadingService.ResolveAnomalyAsync(anomalyId, resolveDto.Resolution, resolveDto.ResolutionType, username);
                if (!success)
                {
                    return NotFound(new { message = $"Anomaly with id {anomalyId} not found" });
                }
                return Ok(new { message = "Anomaly resolved successfully" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error resolving anomaly {AnomalyId}", anomalyId);
                return StatusCode(500, new { message = "Internal server error" });
            }
        }

        #endregion

        #region Validation Rules

        /// <summary>
        /// Get validation rules
        /// </summary>
        [HttpGet("validation-rules")]
        public async Task<ActionResult<List<ValidationRuleDto>>> GetValidationRules()
        {
            try
            {
                var rules = await _meterReadingService.GetValidationRulesAsync();
                return Ok(rules);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting validation rules");
                return StatusCode(500, new { message = "Internal server error" });
            }
        }

        /// <summary>
        /// Create validation rule
        /// </summary>
        [HttpPost("validation-rules")]
        public async Task<ActionResult<ValidationRuleDto>> CreateValidationRule([FromBody] CreateValidationRuleDto createDto)
        {
            try
            {
                var username = User.Identity?.Name ?? "System";
                var rule = await _meterReadingService.CreateValidationRuleAsync(createDto, username);
                return CreatedAtAction(nameof(GetValidationRule), new { id = rule.Id }, rule);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating validation rule");
                return StatusCode(500, new { message = "Internal server error" });
            }
        }

        /// <summary>
        /// Get validation rule by ID
        /// </summary>
        [HttpGet("validation-rules/{id}")]
        public async Task<ActionResult<ValidationRuleDto>> GetValidationRule(int id)
        {
            try
            {
                var rule = await _meterReadingService.GetValidationRuleByIdAsync(id);
                if (rule == null)
                {
                    return NotFound(new { message = $"Validation rule with id {id} not found" });
                }
                return Ok(rule);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting validation rule {Id}", id);
                return StatusCode(500, new { message = "Internal server error" });
            }
        }

        #endregion

        #region Statistics

        /// <summary>
        /// Get reading statistics
        /// </summary>
        [HttpGet("statistics")]
        public async Task<ActionResult<MeterReadingStatisticsDto>> GetStatistics()
        {
            try
            {
                var statistics = await _meterReadingService.GetReadingStatisticsAsync();
                return Ok(statistics);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting reading statistics");
                return StatusCode(500, new { message = "Internal server error" });
            }
        }

        /// <summary>
        /// Get OCR accuracy rate
        /// </summary>
        [HttpGet("ocr-accuracy")]
        public async Task<ActionResult<decimal>> GetOCRAccuracyRate()
        {
            try
            {
                var accuracy = await _meterReadingService.GetOCRAccuracyRateAsync();
                return Ok(new { accuracy });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting OCR accuracy rate");
                return StatusCode(500, new { message = "Internal server error" });
            }
        }

        #endregion

        #region Utility Methods

        /// <summary>
        /// Get meter numbers for dropdown
        /// </summary>
        [HttpGet("meter-numbers")]
        public async Task<ActionResult<List<string>>> GetMeterNumbers()
        {
            try
            {
                var meterNumbers = await _meterReadingService.GetMeterNumbersAsync();
                return Ok(meterNumbers);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting meter numbers");
                return StatusCode(500, new { message = "Internal server error" });
            }
        }

        /// <summary>
        /// Get reading history for a meter
        /// </summary>
        [HttpGet("meters/{meterNumber}/history")]
        public async Task<ActionResult<List<MeterReadingListDto>>> GetReadingHistory(string meterNumber, [FromQuery] int? months = null)
        {
            try
            {
                var history = await _meterReadingService.GetReadingHistoryAsync(meterNumber, months);
                return Ok(history);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting reading history for meter {MeterNumber}", meterNumber);
                return StatusCode(500, new { message = "Internal server error" });
            }
        }

        #endregion

        #region Helper Methods

        private int GetCurrentUserId()
        {
            var userIdClaim = User.FindFirst("UserId")?.Value ?? User.FindFirst("sub")?.Value;
            return int.TryParse(userIdClaim, out var userId) ? userId : 1;
        }

        #endregion
    }

    // Supporting DTOs for controller actions
    public class CorrectReadingDto
    {
        public decimal CorrectedValue { get; set; }
        public string Reason { get; set; } = string.Empty;
    }

    public class OverrideOCRDto
    {
        public string CorrectedValue { get; set; } = string.Empty;
        public string Reason { get; set; } = string.Empty;
    }

    public class AssignAnomalyDto
    {
        public int AssignedUserId { get; set; }
    }

    public class ResolveAnomalyDto
    {
        public string Resolution { get; set; } = string.Empty;
        public string ResolutionType { get; set; } = string.Empty;
    }
} 