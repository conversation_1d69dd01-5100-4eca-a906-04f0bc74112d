using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using WaterMeterManagement.DTOs;
using WaterMeterManagement.DTOs.Mobile;
using WaterMeterManagement.Models;
using WaterMeterManagement.Services;
using WaterMeterManagement.Services.Interfaces;
using System.Security.Claims;

namespace WaterMeterManagement.Controllers
{
    /// <summary>
    /// Mobile Reading Controller - Handles meter reading submissions from mobile devices
    /// </summary>
    [ApiController]
    [Route("api/mobile/readings")]
    [Authorize]
    public class MobileReadingController : ControllerBase
    {
        private readonly IMeterReadingService _meterReadingService;
        private readonly ITaskService _taskService;
        private readonly IPhotoService _photoService;
        private readonly ILogger<MobileReadingController> _logger;

        public MobileReadingController(
            IMeterReadingService meterReadingService,
            ITaskService taskService,
            IPhotoService photoService,
            ILogger<MobileReadingController> logger)
        {
            _meterReadingService = meterReadingService;
            _taskService = taskService;
            _photoService = photoService;
            _logger = logger;
        }

        /// <summary>
        /// Submit enhanced meter reading with GPS, validation, and OCR data
        /// </summary>
        [HttpPost("submit")]
        public async Task<ActionResult<EnhancedReadingResponseDto>> SubmitEnhancedReading([FromBody] MobileReadingDto readingDto)
        {
            try
            {
                var userId = GetCurrentUserId();
                if (userId == 0)
                {
                    return Unauthorized(new EnhancedReadingResponseDto
                    {
                        Success = false,
                        Message = "User not authenticated"
                    });
                }

                _logger.LogInformation(
                    "Submitting enhanced reading - Task: {TaskId}, Meter: {MeterId}, Value: {Value}, " +
                    "GPS: ({Lat}, {Lng}, {Accuracy}m), OCR: {OcrConfidence}, Validation: {ValidationStatus}, User: {UserId}",
                    readingDto.TaskId, readingDto.MeterId, readingDto.ReadingValue,
                    readingDto.Latitude, readingDto.Longitude, readingDto.GpsAccuracy,
                    readingDto.OcrConfidence, readingDto.ValidationStatus, userId);

                // Check if reading already exists for this task
                var existingReading = await _meterReadingService.GetMobileReadingByTaskIdAsync(readingDto.TaskId);
                
                if (existingReading != null)
                {
                    // Update existing reading
                    _logger.LogInformation("Updating existing enhanced reading for task {TaskId}", readingDto.TaskId);
                    var updatedReading = await _meterReadingService.UpdateMobileReadingByTaskIdAsync(readingDto.TaskId, readingDto, userId);
                    
                    // Validate the updated reading
                    var validationResult = await _meterReadingService.ValidateEnhancedMobileReadingAsync(readingDto, userId);
                    
                    return Ok(new EnhancedReadingResponseDto
                    {
                        Success = true,
                        Message = "Enhanced reading updated successfully",
                        ReadingId = updatedReading?.Id ?? 0,
                        ValidationPassed = validationResult.IsValid,
                        ValidationScore = validationResult.ValidationScore,
                        RequiresReview = validationResult.RequiresReview,
                        AnomalyDetected = validationResult.AnomalyDetected,
                        AnomalyType = validationResult.AnomalyType,
                        SuggestedValue = validationResult.SuggestedValue,
                        Warnings = validationResult.Warnings
                    });
                }

                // Enhanced validation with GPS and OCR data
                var validationResultNew = await _meterReadingService.ValidateEnhancedMobileReadingAsync(readingDto, userId);
                if (!validationResultNew.IsValid)
                {
                    return BadRequest(new EnhancedReadingResponseDto
                    {
                        Success = false,
                        Message = "Enhanced reading validation failed",
                        Errors = validationResultNew.Errors,
                        Warnings = validationResultNew.Warnings,
                        ValidationScore = validationResultNew.ValidationScore,
                        RequiresReview = validationResultNew.RequiresReview
                    });
                }

                // Submit the enhanced reading
                var result = await _meterReadingService.SubmitEnhancedMobileReadingAsync(readingDto, userId);
                
                if (!result.Success)
                {
                    return BadRequest(result);
                }

                _logger.LogInformation("Enhanced reading submitted successfully - ReadingId: {ReadingId}, TaskId: {TaskId}", 
                    result.ReadingId, readingDto.TaskId);

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error submitting enhanced reading - TaskId: {TaskId}, MeterId: {MeterId}", 
                    readingDto.TaskId, readingDto.MeterId);
                return StatusCode(500, new EnhancedReadingResponseDto
                {
                    Success = false,
                    Message = "An error occurred while submitting the enhanced reading",
                    Errors = new List<string> { ex.Message }
                });
            }
        }

        /// <summary>
        /// Submit a single meter reading with validation (Legacy endpoint)
        /// </summary>
        [HttpPost]
        public async Task<ActionResult<ReadingResponseDto>> SubmitReading([FromBody] MobileReadingDto readingDto)
        {
            try
            {
                var userId = GetCurrentUserId();
                if (userId == 0)
                {
                    return Unauthorized("User not authenticated");
                }

                _logger.LogInformation("Submitting reading for task {TaskId}, meter {MeterId}, value {Value}, user {UserId}, photos count: {PhotoCount}",
                    readingDto.TaskId, readingDto.MeterId, readingDto.ReadingValue, userId, readingDto.Photos?.Count ?? 0);

                // Check if reading already exists for this task
                var existingReading = await _meterReadingService.GetMobileReadingByTaskIdAsync(readingDto.TaskId);
                
                if (existingReading != null)
                {
                    // Update existing reading
                    _logger.LogInformation("Updating existing reading for task {TaskId}", readingDto.TaskId);
                    var updatedReading = await _meterReadingService.UpdateMobileReadingByTaskIdAsync(readingDto.TaskId, readingDto, userId);
                    
                    return Ok(new ReadingResponseDto
                    {
                        Success = true,
                        Message = "Reading updated successfully",
                        ReadingId = updatedReading?.Id ?? 0
                    });
                }

                // Validate reading before submission
                var validationResult = await _meterReadingService.ValidateMobileReadingAsync(readingDto, userId);
                if (!validationResult.IsValid)
                {
                    return BadRequest(new ReadingResponseDto
                    {
                        Success = false,
                        Message = "Reading validation failed",
                        Errors = validationResult.Errors,
                        Warnings = validationResult.Warnings
                    });
                }

                // Submit the reading
                var result = await _meterReadingService.SubmitMobileReadingAsync(readingDto, userId);
                
                if (!result.Success)
                {
                    return BadRequest(result);
                }

                _logger.LogInformation("Reading submitted successfully with ID {ReadingId}", result.ReadingId);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error submitting reading for task {TaskId}", readingDto.TaskId);
                return StatusCode(500, "An error occurred while submitting the reading");
            }
        }

        /// <summary>
        /// Submit multiple readings in batch (for offline sync)
        /// </summary>
        [HttpPost("batch")]
        public async Task<ActionResult<BatchReadingResponseDto>> SubmitBatchReadings([FromBody] BatchReadingRequest request)
        {
            try
            {
                var userId = GetCurrentUserId();
                if (userId == 0)
                {
                    return Unauthorized("User not authenticated");
                }

                _logger.LogInformation("Submitting batch of {Count} readings for user {UserId}", 
                    request.Readings.Count, userId);

                var result = await _meterReadingService.SubmitBatchMobileReadingsAsync(request, userId);
                
                _logger.LogInformation("Batch submission completed: {Successful}/{Total} successful", 
                    result.SuccessfullyProcessed, result.TotalSubmitted);

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error submitting batch readings for user {UserId}", GetCurrentUserId());
                return StatusCode(500, "An error occurred while submitting batch readings");
            }
        }

        /// <summary>
        /// Validate a reading before submission
        /// </summary>
        [HttpPost("validate")]
        public async Task<ActionResult<ReadingValidationDto>> ValidateReading([FromBody] MobileReadingDto readingDto)
        {
            try
            {
                var userId = GetCurrentUserId();
                if (userId == 0)
                {
                    return Unauthorized("User not authenticated");
                }

                _logger.LogInformation("Validating reading for task {TaskId}, meter {MeterId}, value {Value}", 
                    readingDto.TaskId, readingDto.MeterId, readingDto.ReadingValue);

                var result = await _meterReadingService.ValidateMobileReadingAsync(readingDto, userId);
                
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating reading for task {TaskId}", readingDto.TaskId);
                return StatusCode(500, "An error occurred while validating the reading");
            }
        }

        /// <summary>
        /// Submit reading with task-based duplicate detection
        /// </summary>
        [HttpPost("submit-with-task-check")]
        public async Task<ActionResult<MobileReadingResponseDto>> SubmitReadingWithTaskCheck([FromBody] MobileReadingDto readingDto)
        {
            try
            {
                var userId = GetCurrentUserId();
                if (userId == 0)
                {
                    return Unauthorized("User not authenticated");
                }

                _logger.LogInformation(
                    "Submitting reading with task check - Task: {TaskId}, Meter: {MeterId}, Value: {Value}, User: {UserId}",
                    readingDto.TaskId, readingDto.MeterId, readingDto.ReadingValue, userId);

                // Check if reading already exists for this task
                var existingReading = await _meterReadingService.GetMobileReadingByTaskIdAsync(readingDto.TaskId);
                
                MeterReading? resultReading;
                string action;
                
                if (existingReading != null)
                {
                    // Update existing reading
                    _logger.LogInformation("Updating existing reading for task {TaskId}", readingDto.TaskId);
                    resultReading = await _meterReadingService.UpdateMobileReadingByTaskIdAsync(readingDto.TaskId, readingDto, userId);
                    action = "updated";
                }
                else
                {
                    // Create new reading
                    _logger.LogInformation("Creating new reading for task {TaskId}", readingDto.TaskId);
                    var response = await _meterReadingService.SubmitEnhancedMobileReadingAsync(readingDto, userId);
                    return Ok(new MobileReadingResponseDto
                    {
                        Success = response.Success,
                        Message = response.Message,
                        ReadingId = response.ReadingId,
                        ValidationPassed = response.ValidationPassed,
                        AnomalyDetected = response.AnomalyDetected,
                        AnomalyType = response.AnomalyType,
                        SuggestedValue = response.SuggestedValue,
                        Warnings = response.Warnings
                    });
                }

                // Validate the reading
                var validationResult = await _meterReadingService.ValidateEnhancedMobileReadingAsync(readingDto, userId);
                
                return Ok(new MobileReadingResponseDto
                {
                    Success = true,
                    Message = $"Reading {action} successfully",
                    ReadingId = resultReading?.Id ?? 0,
                    ValidationPassed = validationResult.IsValid,
                    AnomalyDetected = validationResult.AnomalyDetected,
                    AnomalyType = validationResult.AnomalyType,
                    SuggestedValue = validationResult.SuggestedValue,
                    Warnings = validationResult.Warnings
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error submitting reading for task {TaskId}", readingDto.TaskId);
                return StatusCode(500, new MobileReadingResponseDto
                {
                    Success = false,
                    Message = "An error occurred while processing the reading",
                    Errors = new List<string> { ex.Message }
                });
            }
        }

        /// <summary>
        /// Upload photos for a reading
        /// </summary>
        [HttpPost("{readingId}/photos")]
        public async Task<ActionResult<List<PhotoUploadResult>>> UploadPhotos(
            int readingId, 
            [FromBody] List<MobilePhotoDto> photos)
        {
            try
            {
                var userId = GetCurrentUserId();
                if (userId == 0)
                {
                    return Unauthorized("User not authenticated");
                }

                _logger.LogInformation("Uploading {Count} photos for reading {ReadingId}", 
                    photos.Count, readingId);

                var results = await _photoService.UploadMobilePhotosAsync(readingId, photos, userId);
                
                return Ok(results);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error uploading photos for reading {ReadingId}", readingId);
                return StatusCode(500, "An error occurred while uploading photos");
            }
        }

        /// <summary>
        /// Upload photo for meter reading
        /// </summary>
        [HttpPost("photos/upload")]
        public async Task<ActionResult<DTOs.Mobile.PhotoUploadResponseDto>> UploadPhoto(IFormFile photo, [FromForm] int? taskId = null, [FromForm] int? meterId = null)
        {
            try
            {
                var userId = GetCurrentUserId();
                if (userId == 0)
                {
                    return Unauthorized(new DTOs.Mobile.PhotoUploadResponseDto
                    {
                        Success = false,
                        Message = "User not authenticated"
                    });
                }

                if (photo == null || photo.Length == 0)
                {
                    return BadRequest(new DTOs.Mobile.PhotoUploadResponseDto
                    {
                        Success = false,
                        Message = "No photo file provided"
                    });
                }

                // Validate file type
                var allowedTypes = new[] { "image/jpeg", "image/jpg", "image/png", "image/bmp" };
                if (!allowedTypes.Contains(photo.ContentType.ToLower()))
                {
                    return BadRequest(new DTOs.Mobile.PhotoUploadResponseDto
                    {
                        Success = false,
                        Message = "Invalid file type. Only JPEG, PNG, and BMP files are allowed."
                    });
                }

                // Validate file size (max 10MB)
                const int maxSize = 10 * 1024 * 1024;
                if (photo.Length > maxSize)
                {
                    return BadRequest(new DTOs.Mobile.PhotoUploadResponseDto
                    {
                        Success = false,
                        Message = "File size exceeds 10MB limit"
                    });
                }

                _logger.LogInformation("Uploading photo - Size: {Size} bytes, Type: {Type}, Task: {TaskId}, Meter: {MeterId}, User: {UserId}",
                    photo.Length, photo.ContentType, taskId, meterId, userId);

                // 使用PhotoService上传到R2
                var uploadResult = await _photoService.UploadFormFileAsync(photo, userId);

                if (uploadResult.Success)
                {
                    _logger.LogInformation("Photo uploaded successfully to R2 - URL: {Url}", uploadResult.CloudflareUrl);

                    return Ok(new DTOs.Mobile.PhotoUploadResponseDto
                    {
                        Success = true,
                        Message = "Photo uploaded successfully",
                        PhotoId = uploadResult.PhotoId,
                        FilePath = uploadResult.CloudflareUrl,
                        FileName = uploadResult.FileName,
                        FileSize = photo.Length,
                        ContentType = photo.ContentType,
                        UploadTime = DateTime.UtcNow
                    });
                }
                else
                {
                    _logger.LogError("Photo upload failed: {Error}", uploadResult.Error);
                    return StatusCode(500, new DTOs.Mobile.PhotoUploadResponseDto
                    {
                        Success = false,
                        Message = $"Photo upload failed: {uploadResult.Error}"
                    });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error uploading photo - TaskId: {TaskId}, MeterId: {MeterId}", taskId, meterId);
                return StatusCode(500, new DTOs.Mobile.PhotoUploadResponseDto
                {
                    Success = false,
                    Message = "An error occurred while uploading the photo",
                    Error = ex.Message
                });
            }
        }

        /// <summary>
        /// Get uploaded photos for a task or meter
        /// </summary>
        [HttpGet("photos")]
        public async Task<ActionResult<List<PhotoDto>>> GetPhotos([FromQuery] int? taskId = null, [FromQuery] int? meterId = null)
        {
            try
            {
                var userId = GetCurrentUserId();
                if (userId == 0)
                {
                    return Unauthorized("User not authenticated");
                }

                _logger.LogInformation("Getting photos - TaskId: {TaskId}, MeterId: {MeterId}, User: {UserId}",
                    taskId, meterId, userId);

                // For now, return empty list as the photo service method doesn't exist yet
                var photos = new List<PhotoDto>();

                return Ok(photos);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting photos - TaskId: {TaskId}, MeterId: {MeterId}", taskId, meterId);
                return StatusCode(500, "An error occurred while retrieving photos");
            }
        }

        /// <summary>
        /// Get reading history for a meter
        /// </summary>
        [HttpGet("history/{meterId}")]
        public async Task<ActionResult<List<MobileReadingHistoryDto>>> GetReadingHistory(
            int meterId,
            [FromQuery] int limit = 10)
        {
            try
            {
                var userId = GetCurrentUserId();
                if (userId == 0)
                {
                    return Unauthorized("User not authenticated");
                }

                _logger.LogInformation("Getting reading history for meter {MeterId}, limit {Limit}", 
                    meterId, limit);

                var history = await _meterReadingService.GetMobileReadingHistoryAsync(meterId, limit);
                
                return Ok(history);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting reading history for meter {MeterId}", meterId);
                return StatusCode(500, "An error occurred while retrieving reading history");
            }
        }

        /// <summary>
        /// Get recent readings by current user
        /// </summary>
        [HttpGet("my-recent")]
        public async Task<ActionResult<List<MobileReadingHistoryDto>>> GetMyRecentReadings(
            [FromQuery] int limit = 20,
            [FromQuery] int page = 1)
        {
            try
            {
                var userId = GetCurrentUserId();
                if (userId == 0)
                {
                    return Unauthorized("User not authenticated");
                }

                _logger.LogInformation("Getting recent readings for user {UserId}, page {Page}, limit {Limit}", 
                    userId, page, limit);

                var readings = await _meterReadingService.GetUserRecentReadingsAsync(userId, page, limit);
                
                return Ok(readings);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting recent readings for user {UserId}", GetCurrentUserId());
                return StatusCode(500, "An error occurred while retrieving recent readings");
            }
        }

        /// <summary>
        /// Update reading with additional information
        /// </summary>
        [HttpPut("{readingId}")]
        public async Task<ActionResult> UpdateReading(int readingId, [FromBody] UpdateReadingRequest request)
        {
            try
            {
                var userId = GetCurrentUserId();
                if (userId == 0)
                {
                    return Unauthorized("User not authenticated");
                }

                _logger.LogInformation("Updating reading {ReadingId} for user {UserId}", readingId, userId);

                var result = await _meterReadingService.UpdateMobileReadingAsync(readingId, request, userId);
                
                if (!result.Success)
                {
                    return BadRequest(result.Message);
                }

                return Ok(new { message = "Reading updated successfully", readingId });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating reading {ReadingId}", readingId);
                return StatusCode(500, "An error occurred while updating the reading");
            }
        }

        /// <summary>
        /// Delete a reading (soft delete)
        /// </summary>
        [HttpDelete("{readingId}")]
        public async Task<ActionResult> DeleteReading(int readingId)
        {
            try
            {
                var userId = GetCurrentUserId();
                if (userId == 0)
                {
                    return Unauthorized("User not authenticated");
                }

                _logger.LogInformation("Deleting reading {ReadingId} for user {UserId}", readingId, userId);

                var result = await _meterReadingService.DeleteMobileReadingAsync(readingId, userId);
                
                /* if (!result.Success)
                {
                    return BadRequest(result.Message);
                } */

                return Ok(new { message = "Reading deleted successfully", readingId });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting reading {ReadingId}", readingId);
                return StatusCode(500, "An error occurred while deleting the reading");
            }
        }

        /// <summary>
        /// Submit reading and complete task in one operation
        /// </summary>
        [HttpPost("submit-and-complete")]
        public async Task<ActionResult<ReadingResponseDto>> SubmitReadingAndCompleteTask([FromBody] CompleteTaskRequest request)
        {
            try
            {
                var userId = GetCurrentUserId();
                if (userId == 0)
                {
                    return Unauthorized("User not authenticated");
                }

                _logger.LogInformation("Submitting reading and completing task {TaskId} for user {UserId}", 
                    request.Reading.TaskId, userId);

                // First validate the reading
                var validationResult = await _meterReadingService.ValidateMobileReadingAsync(request.Reading, userId);
                if (!validationResult.IsValid)
                {
                    return BadRequest(new ReadingResponseDto
                    {
                        Success = false,
                        Message = "Reading validation failed",
                        Errors = validationResult.Errors,
                        Warnings = validationResult.Warnings
                    });
                }

                // Submit reading and complete task atomically
                var result = await _meterReadingService.SubmitReadingAndCompleteTaskAsync(request, userId);
                
                if (!result.Success)
                {
                    return BadRequest(result);
                }

                _logger.LogInformation("Reading submitted and task completed successfully. ReadingId: {ReadingId}, TaskId: {TaskId}", 
                    result.ReadingId, request.Reading.TaskId);

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error submitting reading and completing task {TaskId}", request.Reading.TaskId);
                return StatusCode(500, "An error occurred while submitting reading and completing task");
            }
        }

        /// <summary>
        /// Get reading statistics for current user
        /// </summary>
        [HttpGet("my-stats")]
        public async Task<ActionResult<MobileReadingStatsDto>> GetMyReadingStats()
        {
            try
            {
                var userId = GetCurrentUserId();
                if (userId == 0)
                {
                    return Unauthorized("User not authenticated");
                }

                _logger.LogInformation("Getting reading statistics for user {UserId}", userId);

                var stats = await _meterReadingService.GetUserReadingStatsAsync(userId);
                
                return Ok(stats);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting reading statistics for user {UserId}", GetCurrentUserId());
                return StatusCode(500, "An error occurred while retrieving reading statistics");
            }
        }

        /// <summary>
        /// Sync offline readings
        /// </summary>
        [HttpPost("sync-offline")]
        public async Task<ActionResult<OfflineReadingSyncResponse>> SyncOfflineReadings([FromBody] OfflineReadingSyncRequest request)
        {
            try
            {
                var userId = GetCurrentUserId();
                if (userId == 0)
                {
                    return Unauthorized("User not authenticated");
                }

                _logger.LogInformation("Syncing {Count} offline readings for user {UserId}", 
                    request.OfflineReadings.Count, userId);

                var result = await _meterReadingService.SyncOfflineReadingsAsync(request, userId);
                
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error syncing offline readings for user {UserId}", GetCurrentUserId());
                return StatusCode(500, "An error occurred while syncing offline readings");
            }
        }

        #region Helper Methods

        private int GetCurrentUserId()
        {
            var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            return int.TryParse(userIdClaim, out var userId) ? userId : 0;
        }

        #endregion
    }
} 