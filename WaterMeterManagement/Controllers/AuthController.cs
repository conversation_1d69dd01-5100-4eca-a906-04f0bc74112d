using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using WaterMeterManagement.DTOs;
using WaterMeterManagement.Services.Interfaces;

namespace WaterMeterManagement.Controllers
{
    /// <summary>
    /// authentication controller
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class AuthController : ControllerBase
    {
        private readonly IAuthService _authService;
        private readonly ILogger<AuthController> _logger;

        public AuthController(IAuthService authService, ILogger<AuthController> logger)
        {
            _authService = authService;
            _logger = logger;
        }

        /// <summary>
        /// </summary>
        [HttpPost("login")]
        [AllowAnonymous]
        public async Task<ActionResult<LoginResponseDto>> Login([FromBody] LoginRequestDto loginRequest)
        {
            try
            {
                _logger.LogInformation("received login request: {Username}", loginRequest.Username);

                if (!ModelState.IsValid)
                {
                    _logger.LogWarning("Login request validation failed: {Username}", loginRequest.Username);
                    return BadRequest(new LoginResponseDto
                    {
                        Success = false,
                        Message = "request parameter validation failed"
                    });
                }

                var result = await _authService.LoginAsync(loginRequest);

                if (result.Success)
                {
                    _logger.LogInformation("User login successful: {Username}", loginRequest.Username);
                    return Ok(result);
                }
                else
                {
                    _logger.LogWarning("user login failed: {Username}, reason: {Message}", 
                        loginRequest.Username, result.Message);
                    return Unauthorized(result);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "login API unexpected error: {Username}", loginRequest.Username);
                return StatusCode(500, new LoginResponseDto
                {
                    Success = false,
                    Message = "server internal error, please try again later"
                });
            }
        }

        /// <summary>
        /// get current user information
        /// </summary>
        /// <returns>user information</returns>
        [HttpGet("me")]
        [Authorize]
        public ActionResult<UserDto> GetCurrentUser()
        {
            try
            {
                var userId = User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value;
                var username = User.FindFirst(System.Security.Claims.ClaimTypes.Name)?.Value;
                var fullName = User.FindFirst("FullName")?.Value;
                var personId = User.FindFirst("PersonId")?.Value;
                var finCoCode = User.FindFirst("FinCoCode")?.Value;

                if (userId == null || username == null)
                {
                    _logger.LogWarning("Can't get user information from JWT Token");
                    return Unauthorized(new { message = "Invalid authentication information" });
                }

                var userDto = new UserDto
                {
                    Id = int.Parse(userId),
                    Username = username,
                    FullName = fullName ?? "",
                    PersonId = int.Parse(personId ?? "0"),
                    FinCoCode = finCoCode ?? "",
                    IsAuthenticated = true
                };

                _logger.LogInformation("return current user information: {Username}", username);
                return Ok(userDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "get current user information error");
                return StatusCode(500, new { message = "server internal error" });
            }
        }

        /// <summary>
        /// test protected API
        /// </summary>
        /// <returns>test message</returns>
        [HttpGet("test-protected")]
        [Authorize]
        public ActionResult<object> TestProtected()
        {
            var username = User.FindFirst(System.Security.Claims.ClaimTypes.Name)?.Value;
            _logger.LogInformation("protected API accessed: {Username}", username);
            
            return Ok(new
            {
                message = "this is a protected API endpoint",
                user = username,
                timestamp = DateTime.UtcNow
            });
        }
    }
} 