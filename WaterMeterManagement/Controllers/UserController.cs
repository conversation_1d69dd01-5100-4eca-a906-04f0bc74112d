using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Linq;
using WaterMeterManagement.Data;
using WaterMeterManagement.DTOs;
using WaterMeterManagement.Models;
using WaterMeterManagement.Repositories.Interfaces;
using WaterMeterManagement.Enums;
using WaterMeterManagement.Exceptions;

namespace WaterMeterManagement.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class UserController : ControllerBase
    {
        private readonly ApplicationDbContext _context;
        private readonly IUserRoleRepository _userRoleRepository;
        private readonly IRoleRepository _roleRepository;
        private readonly ILogger<UserController> _logger;

        public UserController(
            ApplicationDbContext context,
            IUserRoleRepository userRoleRepository,
            IRoleRepository roleRepository,
            ILogger<UserController> logger)
        {
            _context = context;
            _userRoleRepository = userRoleRepository;
            _roleRepository = roleRepository;
            _logger = logger;
        }

        [HttpGet]
        public async Task<ActionResult<IEnumerable<object>>> GetAllUsers()
        {
            try
            {
                var users = await _context.Users
                    .Select(u => new
                    {
                        u.Id,
                        u.Username,
                        u.FullName,
                        u.PersonId,
                        u.FinCoCode,
                        u.Email,
                        u.MobilePhone,
                        u.ProfitCentreCode,
                        u.EmployeeNo,
                        u.IsAuthenticated,
                        u.LastLogin,
                        u.CreatedDate,
                        u.UpdatedDate,
                        Roles = u.UserRoles
                            .Where(ur => !ur.IsDeleted)
                            .Select(ur => new { ur.Role.Id, ur.Role.Name })
                            .ToList()
                    })
                    .ToListAsync();

                return Ok(users);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving users");
                return StatusCode(500, $"Internal server error: {ex.Message}");
            }
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<object>> GetUser(int id)
        {
            try
            {
                var user = await _context.Users
                    .Where(u => u.Id == id)
                    .Select(u => new
                    {
                        u.Id,
                        u.Username,
                        u.FullName,
                        u.PersonId,
                        u.FinCoCode,
                        u.Email,
                        u.MobilePhone,
                        u.ProfitCentreCode,
                        u.EmployeeNo,
                        u.IsAuthenticated,
                        u.LastLogin,
                        u.CreatedDate,
                        u.UpdatedDate,
                        Roles = u.UserRoles
                            .Where(ur => !ur.IsDeleted)
                            .Select(ur => new { ur.Role.Id, ur.Role.Name })
                            .ToList()
                    })
                    .FirstOrDefaultAsync();

                if (user == null)
                {
                    return NotFound($"User with ID {id} not found.");
                }

                return Ok(user);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving user {UserId}", id);
                return StatusCode(500, $"Internal server error: {ex.Message}");
            }
        }





        [HttpPut("{id}")]
        public async Task<ActionResult<User>> UpdateUser(int id, [FromBody] UpdateUserDto updateUserDto)
        {
            try
            {
                var user = await _context.Users.FindAsync(id);
                if (user == null)
                {
                    return NotFound($"User with ID {id} not found.");
                }

                // Update user properties
                if (!string.IsNullOrEmpty(updateUserDto.FullName))
                {
                    user.FullName = updateUserDto.FullName;
                }
                
                if (!string.IsNullOrEmpty(updateUserDto.FinCoCode))
                {
                    user.FinCoCode = updateUserDto.FinCoCode;
                }
                
                if (!string.IsNullOrEmpty(updateUserDto.Email))
                {
                    user.Email = updateUserDto.Email;
                }
                
                if (!string.IsNullOrEmpty(updateUserDto.MobilePhone))
                {
                    user.MobilePhone = updateUserDto.MobilePhone;
                }
                
                if (!string.IsNullOrEmpty(updateUserDto.ProfitCentreCode))
                {
                    user.ProfitCentreCode = updateUserDto.ProfitCentreCode;
                }
                
                if (!string.IsNullOrEmpty(updateUserDto.EmployeeNo))
                {
                    user.EmployeeNo = updateUserDto.EmployeeNo;
                }

                user.UpdatedDate = DateTime.UtcNow;

                _context.Users.Update(user);
                await _context.SaveChangesAsync();

                _logger.LogInformation("User {UserId} updated successfully", id);
                return Ok(user);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating user {UserId}", id);
                return StatusCode(500, $"Internal server error: {ex.Message}");
            }
        }

        [HttpDelete("{id}")]
        public async Task<ActionResult> DeleteUser(int id)
        {
            try
            {
                var user = await _context.Users.FindAsync(id);
                if (user == null)
                {
                    return NotFound($"User with ID {id} not found.");
                }

                // Check if user is admin (prevent deletion)
                if (user.Username.Equals("admin", StringComparison.OrdinalIgnoreCase))
                {
                    return BadRequest("Cannot delete the system administrator account.");
                }

                // Remove user role assignments first
                var userRoles = await _userRoleRepository.GetByUserIdAsync(id);
                foreach (var userRole in userRoles)
                {
                    await _userRoleRepository.DeleteAsync(id, userRole.RoleId);
                }

                // Remove the user
                _context.Users.Remove(user);
                await _context.SaveChangesAsync();

                _logger.LogInformation("User {UserId} deleted successfully", id);
                return Ok(new { message = "User deleted successfully" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting user {UserId}", id);
                return StatusCode(500, $"Internal server error: {ex.Message}");
            }
        }

        [HttpGet("statistics")]
        public async Task<ActionResult<object>> GetUserStatistics()
        {
            try
            {
                var totalUsers = await _context.Users.CountAsync();
                var authenticatedUsers = await _context.Users.CountAsync(u => u.IsAuthenticated);
                var recentLogins = await _context.Users
                    .Where(u => u.LastLogin.HasValue && u.LastLogin > DateTime.UtcNow.AddDays(-30))
                    .CountAsync();

                var statistics = new
                {
                    TotalUsers = totalUsers,
                    AuthenticatedUsers = authenticatedUsers,
                    RecentLogins = recentLogins,
                    InactiveUsers = totalUsers - authenticatedUsers
                };

                return Ok(statistics);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving user statistics");
                return StatusCode(500, $"Internal server error: {ex.Message}");
            }
        }
    }
} 