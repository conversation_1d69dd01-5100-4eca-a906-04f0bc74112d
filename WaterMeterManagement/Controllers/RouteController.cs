using Microsoft.AspNetCore.Mvc;
using WaterMeterManagement.DTOs;
using WaterMeterManagement.Services.Interfaces;

namespace WaterMeterManagement.Controllers
{
    [ApiController]
    [Route("api/routes")]
    public class RouteController : ControllerBase
    {
        private readonly IRouteService _routeService;
        private readonly ILogger<RouteController> _logger;

        public RouteController(
            IRouteService routeService,
            ILogger<RouteController> logger)
        {
            _routeService = routeService;
            _logger = logger;
        }

        [HttpGet]
        public async Task<ActionResult<object>> GetRoutes([FromQuery] RouteSearchDto searchDto)
        {
            try
            {
                var (routes, totalCount) = await _routeService.GetRoutesAsync(searchDto);
                
                return Ok(new
                {
                    Data = routes,
                    TotalCount = totalCount,
                    Page = searchDto.Page,
                    PageSize = searchDto.PageSize,
                    TotalPages = (int)Math.Ceiling((double)totalCount / searchDto.PageSize)
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving routes");
                return StatusCode(500, new { Message = "Internal server error" });
            }
        }

        [HttpGet("{id:int}")]
        public async Task<ActionResult<RouteDto>> GetRoute(int id)
        {
            try
            {
                var route = await _routeService.GetRouteByIdAsync(id);
                if (route == null)
                {
                    return NotFound(new { Message = "Route not found" });
                }

                return Ok(route);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving route {RouteId}", id);
                return StatusCode(500, new { Message = "Internal server error" });
            }
        }

        [HttpPost]
        public async Task<ActionResult<RouteDto>> CreateRoute([FromBody] CreateRouteDto createDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var route = await _routeService.CreateRouteAsync(createDto);
                return CreatedAtAction(nameof(GetRoute), new { id = route.Id }, route);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating route");
                return StatusCode(500, new { Message = "Internal server error" });
            }
        }

        [HttpPut("{id:int}")]
        public async Task<ActionResult<RouteDto>> UpdateRoute(int id, [FromBody] UpdateRouteDto updateDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var route = await _routeService.UpdateRouteAsync(id, updateDto);
                if (route == null)
                {
                    return NotFound(new { Message = "Route not found" });
                }

                return Ok(route);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating route {RouteId}", id);
                return StatusCode(500, new { Message = "Internal server error" });
            }
        }

        [HttpDelete("{id:int}")]
        public async Task<ActionResult> DeleteRoute(int id)
        {
            try
            {
                var success = await _routeService.DeleteRouteAsync(id);
                if (!success)
                {
                    return NotFound(new { Message = "Route not found" });
                }

                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting route {RouteId}", id);
                return StatusCode(500, new { Message = "Internal server error" });
            }
        }

        [HttpPost("{id:int}/optimize")]
        public async Task<ActionResult<RouteOptimizationResultDto>> OptimizeRoute(int id, [FromBody] RouteOptimizationDto optimizationDto)
        {
            try
            {
                optimizationDto.RouteId = id;
                var result = await _routeService.OptimizeRouteAsync(optimizationDto);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error optimizing route {RouteId}", id);
                return StatusCode(500, new { Message = "Internal server error" });
            }
        }

        [HttpPost("{id:int}/apply-optimization")]
        public async Task<ActionResult> ApplyOptimization(int id, [FromBody] List<RouteWaypointDto> optimizedWaypoints)
        {
            try
            {
                var success = await _routeService.ApplyOptimizationAsync(id, optimizedWaypoints);
                if (!success)
                {
                    return NotFound(new { Message = "Route not found" });
                }

                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error applying optimization to route {RouteId}", id);
                return StatusCode(500, new { Message = "Internal server error" });
            }
        }

        [HttpPost("{id:int}/duplicate")]
        public async Task<ActionResult<RouteDto>> DuplicateRoute(int id, [FromBody] DuplicateRouteDto duplicateDto)
        {
            try
            {
                var duplicatedRoute = await _routeService.DuplicateRouteAsync(id, duplicateDto);
                if (duplicatedRoute == null)
                {
                    return NotFound(new { Message = "Source route not found" });
                }

                return Ok(duplicatedRoute);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error duplicating route {RouteId}", id);
                return StatusCode(500, new { Message = "Internal server error" });
            }
        }

        [HttpGet("{id:int}/waypoints")]
        public async Task<ActionResult<List<RouteWaypointDto>>> GetRouteWaypoints(int id)
        {
            try
            {
                var waypoints = await _routeService.GetRouteWaypointsAsync(id);
                return Ok(waypoints);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving waypoints for route {RouteId}", id);
                return StatusCode(500, new { Message = "Internal server error" });
            }
        }

        [HttpPost("{id:int}/waypoints")]
        public async Task<ActionResult<RouteWaypointDto>> AddWaypoint(int id, [FromBody] CreateRouteWaypointDto waypointDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var waypoint = await _routeService.AddWaypointAsync(id, waypointDto);
                return Ok(waypoint);
            }
            catch (ArgumentException ex)
            {
                return BadRequest(new { Message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error adding waypoint to route {RouteId}", id);
                return StatusCode(500, new { Message = "Internal server error" });
            }
        }

        [HttpPut("waypoints/{waypointId:int}")]
        public async Task<ActionResult> UpdateWaypoint(int waypointId, [FromBody] CreateRouteWaypointDto waypointDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var success = await _routeService.UpdateWaypointAsync(waypointId, waypointDto);
                if (!success)
                {
                    return NotFound(new { Message = "Waypoint not found" });
                }

                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating waypoint {WaypointId}", waypointId);
                return StatusCode(500, new { Message = "Internal server error" });
            }
        }

        [HttpDelete("waypoints/{waypointId:int}")]
        public async Task<ActionResult> RemoveWaypoint(int waypointId)
        {
            try
            {
                var success = await _routeService.RemoveWaypointAsync(waypointId);
                if (!success)
                {
                    return NotFound(new { Message = "Waypoint not found" });
                }

                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error removing waypoint {WaypointId}", waypointId);
                return StatusCode(500, new { Message = "Internal server error" });
            }
        }

        [HttpPut("{id:int}/waypoints/reorder")]
        public async Task<ActionResult> ReorderWaypoints(int id, [FromBody] List<int> waypointIds)
        {
            try
            {
                var success = await _routeService.ReorderWaypointsAsync(id, waypointIds);
                if (!success)
                {
                    return NotFound(new { Message = "Route not found" });
                }

                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error reordering waypoints for route {RouteId}", id);
                return StatusCode(500, new { Message = "Internal server error" });
            }
        }

        [HttpGet("templates")]
        public async Task<ActionResult<List<RouteListDto>>> GetRouteTemplates([FromQuery] string? category = null)
        {
            try
            {
                var templates = await _routeService.GetRouteTemplatesAsync(category);
                return Ok(templates);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving route templates");
                return StatusCode(500, new { Message = "Internal server error" });
            }
        }

        [HttpPost("templates/{templateId:int}/create-route")]
        public async Task<ActionResult<RouteDto>> CreateRouteFromTemplate(int templateId, [FromBody] CreateRouteDto routeDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var route = await _routeService.CreateRouteFromTemplateAsync(templateId, routeDto);
                return Ok(route);
            }
            catch (ArgumentException ex)
            {
                return BadRequest(new { Message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating route from template {TemplateId}", templateId);
                return StatusCode(500, new { Message = "Internal server error" });
            }
        }

        [HttpPost("{id:int}/save-as-template")]
        public async Task<ActionResult> SaveAsTemplate(int id, [FromBody] SaveAsTemplateRequestDto request)
        {
            try
            {
                var success = await _routeService.SaveAsTemplateAsync(id, request.TemplateName, request.Category);
                if (!success)
                {
                    return NotFound(new { Message = "Route not found" });
                }

                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving route {RouteId} as template", id);
                return StatusCode(500, new { Message = "Internal server error" });
            }
        }

        [HttpGet("{id:int}/distance")]
        public async Task<ActionResult<decimal?>> CalculateRouteDistance(int id)
        {
            try
            {
                var distance = await _routeService.CalculateRouteDistanceAsync(id);
                return Ok(new { Distance = distance });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calculating distance for route {RouteId}", id);
                return StatusCode(500, new { Message = "Internal server error" });
            }
        }

        [HttpGet("{id:int}/time")]
        public async Task<ActionResult<int?>> CalculateRouteTime(int id)
        {
            try
            {
                var time = await _routeService.CalculateRouteTimeAsync(id);
                return Ok(new { Time = time });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calculating time for route {RouteId}", id);
                return StatusCode(500, new { Message = "Internal server error" });
            }
        }

        [HttpPost("calculate-distance")]
        public async Task<ActionResult<decimal?>> CalculateDistanceBetweenPoints([FromBody] DistanceCalculationRequestDto request)
        {
            try
            {
                var distance = await _routeService.CalculateDistanceBetweenPointsAsync(
                    request.Latitude1, request.Longitude1, 
                    request.Latitude2, request.Longitude2);
                return Ok(new { Distance = distance });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calculating distance between points");
                return StatusCode(500, new { Message = "Internal server error" });
            }
        }

        [HttpGet("{id:int}/validate")]
        public async Task<ActionResult<List<string>>> ValidateRoute(int id)
        {
            try
            {
                var errors = await _routeService.ValidateRouteAsync(id);
                return Ok(new { Errors = errors, IsValid = !errors.Any() });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating route {RouteId}", id);
                return StatusCode(500, new { Message = "Internal server error" });
            }
        }

        [HttpGet("{id:int}/is-optimal")]
        public async Task<ActionResult<bool>> IsRouteOptimal(int id)
        {
            try
            {
                var isOptimal = await _routeService.IsRouteOptimalAsync(id);
                return Ok(new { IsOptimal = isOptimal });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking if route {RouteId} is optimal", id);
                return StatusCode(500, new { Message = "Internal server error" });
            }
        }

        [HttpGet("statistics")]
        public async Task<ActionResult<RouteStatisticsDto>> GetStatistics()
        {
            try
            {
                var statistics = await _routeService.GetRouteStatisticsAsync();
                return Ok(statistics);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving route statistics");
                return StatusCode(500, new { Message = "Internal server error" });
            }
        }

        [HttpGet("dashboard")]
        public async Task<ActionResult<Dictionary<string, object>>> GetDashboardData()
        {
            try
            {
                var dashboardData = await _routeService.GetRouteDashboardDataAsync();
                return Ok(dashboardData);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving route dashboard data");
                return StatusCode(500, new { Message = "Internal server error" });
            }
        }

        [HttpGet("by-assignee/{assigneeUserId}")]
        public async Task<ActionResult<List<RouteListDto>>> GetRoutesByAssignee(int assigneeUserId)
        {
            try
            {
                var routes = await _routeService.GetRoutesByAssigneeAsync(assigneeUserId);
                return Ok(routes);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving routes for assignee {AssigneeUserId}", assigneeUserId);
                return StatusCode(500, new { Message = "Internal server error" });
            }
        }

        [HttpGet("{id:int}/geometry")]
        public async Task<ActionResult<string>> GenerateRouteGeometry(int id)
        {
            try
            {
                var geometry = await _routeService.GenerateRouteGeometryAsync(id);
                return Ok(new { Geometry = geometry });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating geometry for route {RouteId}", id);
                return StatusCode(500, new { Message = "Internal server error" });
            }
        }

        [HttpGet("{id:int}/map/export")]
        public async Task<ActionResult> ExportRouteMap(int id)
        {
            try
            {
                var mapData = await _routeService.ExportRouteMapAsync(id);
                return File(mapData, "application/pdf", $"route_map_{id}_{DateTime.UtcNow:yyyyMMdd}.pdf");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error exporting map for route {RouteId}", id);
                return StatusCode(500, new { Message = "Internal server error" });
            }
        }

        [HttpGet("nearby-waypoints")]
        public async Task<ActionResult<List<RouteWaypointDto>>> GetNearbyWaypoints([FromQuery] NearbyWaypointsRequestDto request)
        {
            try
            {
                var waypoints = await _routeService.GetNearbyWaypointsAsync(
                    request.Latitude, request.Longitude, request.RadiusKm);
                return Ok(waypoints);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving nearby waypoints");
                return StatusCode(500, new { Message = "Internal server error" });
            }
        }

        // ============ AMS Integration Endpoints ============

        [HttpGet("ams-routes")]
        public async Task<ActionResult<List<RouteListDto>>> GetAmsRoutes([FromQuery] int page = 1, [FromQuery] int pageSize = 50)
        {
            try
            {
                var routes = await _routeService.GetAmsRoutesAsync(page, pageSize);
                return Ok(routes);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving AMS routes");
                return StatusCode(500, new { Message = "Internal server error" });
            }
        }

        [HttpGet("township/{township}/routes")]
        public async Task<ActionResult<List<RouteListDto>>> GetRoutesByTownship(string township)
        {
            try
            {
                var routes = await _routeService.GetRoutesByTownshipAsync(township);
                return Ok(routes);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving routes for township {Township}", township);
                return StatusCode(500, new { Message = "Internal server error" });
            }
        }

        [HttpPost("sync-with-ams")]
        public async Task<ActionResult<AmsSyncResultDto>> SyncRoutesWithAms()
        {
            try
            {
                var result = await _routeService.SyncRoutesWithAmsAsync();
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error syncing routes with AMS");
                return StatusCode(500, new { Message = "Internal server error" });
            }
        }

        [HttpPost("{id:int}/assign-meters")]
        public async Task<ActionResult<RouteAssignmentResultDto>> AssignMetersToRoute(int id, [FromBody] RouteAssignmentDto assignmentDto)
        {
            try
            {
                var result = await _routeService.AssignMetersToRouteAsync(id, assignmentDto);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error assigning meters to route {RouteId}", id);
                return StatusCode(500, new { Message = "Internal server error" });
            }
        }

        [HttpPost("{id:int}/remove-meters")]
        public async Task<ActionResult> RemoveMetersFromRoute(int id, [FromBody] List<string> accountNumbers)
        {
            try
            {
                var success = await _routeService.RemoveMetersFromRouteAsync(id, accountNumbers);
                if (!success)
                {
                    return NotFound(new { Message = "Route not found" });
                }

                return Ok(new { Message = $"Removed {accountNumbers.Count} meters from route" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error removing meters from route {RouteId}", id);
                return StatusCode(500, new { Message = "Internal server error" });
            }
        }

        [HttpGet("{id:int}/meters")]
        public async Task<ActionResult<List<WaterMeterListDto>>> GetRouteMeters(int id, [FromQuery] int page = 1, [FromQuery] int pageSize = 50)
        {
            try
            {
                var meters = await _routeService.GetRouteMetersAsync(id, page, pageSize);
                return Ok(meters);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving meters for route {RouteId}", id);
                return StatusCode(500, new { Message = "Internal server error" });
            }
        }

        [HttpGet("{id:int}/meter-count")]
        public async Task<ActionResult<int>> GetRouteMeterCount(int id)
        {
            try
            {
                var count = await _routeService.GetRouteMeterCountAsync(id);
                return Ok(new { MeterCount = count });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving meter count for route {RouteId}", id);
                return StatusCode(500, new { Message = "Internal server error" });
            }
        }

        [HttpPost("{id:int}/auto-assign")]
        public async Task<ActionResult<RouteAssignmentResultDto>> AutoAssignMeters(int id, [FromBody] AutoAssignmentCriteriaDto criteria)
        {
            try
            {
                var result = await _routeService.AutoAssignMetersAsync(id, criteria);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error auto-assigning meters to route {RouteId}", id);
                return StatusCode(500, new { Message = "Internal server error" });
            }
        }

        [HttpGet("{id:int}/reading-schedule")]
        public async Task<ActionResult<RouteReadingScheduleDto>> GetRouteReadingSchedule(int id, [FromQuery] DateTime? startDate = null, [FromQuery] DateTime? endDate = null)
        {
            try
            {
                var start = startDate ?? DateTime.UtcNow;
                var end = endDate ?? DateTime.UtcNow.AddMonths(3);
                var schedule = await _routeService.GetRouteReadingScheduleAsync(id, start, end);
                if (schedule == null)
                {
                    return NotFound(new { Message = "Route not found" });
                }

                return Ok(schedule);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving reading schedule for route {RouteId}", id);
                return StatusCode(500, new { Message = "Internal server error" });
            }
        }

        [HttpPost("analyze-coverage")]
        public async Task<ActionResult<RouteCoverageAnalysisDto>> AnalyzeRouteCoverage([FromBody] RouteCoverageRequestDto request)
        {
            try
            {
                var analysis = await _routeService.AnalyzeRouteCoverageAsync(request);
                return Ok(analysis);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error analyzing route coverage");
                return StatusCode(500, new { Message = "Internal server error" });
            }
        }

        [HttpGet("unassigned-meters")]
        public async Task<ActionResult<List<WaterMeterListDto>>> GetUnassignedMeters([FromQuery] int page = 1, [FromQuery] int pageSize = 50)
        {
            try
            {
                var meters = await _routeService.GetUnassignedMetersAsync(page, pageSize);
                return Ok(meters);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving unassigned meters");
                return StatusCode(500, new { Message = "Internal server error" });
            }
        }

        // PUT: api/routes/waypoints/{id}/coordinates
        [HttpPut("waypoints/{id}/coordinates")]
        public async Task<ActionResult> UpdateWaypointCoordinates(int id, [FromBody] UpdateWaypointCoordinatesDto coordinatesDto)
        {
            try
            {
                var success = await _routeService.UpdateWaypointCoordinatesAsync(id, coordinatesDto);
                if (!success)
                {
                    return NotFound($"Waypoint with ID {id} not found");
                }

                return Ok(new { message = "Waypoint coordinates updated successfully" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating waypoint coordinates for waypoint {WaypointId}", id);
                return StatusCode(500, new { error = "Failed to update waypoint coordinates" });
            }
        }

        // PUT: api/routes/{id}/waypoints/coordinates/batch
        [HttpPut("{id}/waypoints/coordinates/batch")]
        public async Task<ActionResult> BatchUpdateWaypointCoordinates(int id, [FromBody] BatchUpdateCoordinatesDto batchDto)
        {
            try
            {
                var result = await _routeService.BatchUpdateWaypointCoordinatesAsync(id, batchDto);
                return Ok(new { 
                    message = $"Updated {result.SuccessCount} waypoints successfully", 
                    successCount = result.SuccessCount,
                    failedCount = result.FailedCount,
                    failures = result.Failures
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error batch updating waypoint coordinates for route {RouteId}", id);
                return StatusCode(500, new { error = "Failed to batch update waypoint coordinates" });
            }
        }
    }

    // Request DTOs
    public class SaveAsTemplateRequestDto
    {
        public string TemplateName { get; set; } = string.Empty;
        public string? Category { get; set; }
    }

    public class DistanceCalculationRequestDto
    {
        public decimal Latitude1 { get; set; }
        public decimal Longitude1 { get; set; }
        public decimal Latitude2 { get; set; }
        public decimal Longitude2 { get; set; }
    }

    public class NearbyWaypointsRequestDto
    {
        public decimal Latitude { get; set; }
        public decimal Longitude { get; set; }
        public decimal RadiusKm { get; set; } = 5;
    }
} 