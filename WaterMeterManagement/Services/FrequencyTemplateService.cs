using Microsoft.EntityFrameworkCore;
using WaterMeterManagement.Data;
using WaterMeterManagement.DTOs;
using WaterMeterManagement.Models;
using WaterMeterManagement.Services.Interfaces;
using System.Text.Json;

namespace WaterMeterManagement.Services
{
    public class FrequencyTemplateService : IFrequencyTemplateService
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<FrequencyTemplateService> _logger;

        public FrequencyTemplateService(ApplicationDbContext context, ILogger<FrequencyTemplateService> logger)
        {
            _context = context;
            _logger = logger;
        }

        public async Task<(List<FrequencyTemplateListDto> templates, int totalCount)> GetFrequencyTemplatesAsync(FrequencyTemplateSearchDto searchDto)
        {
            var query = _context.FrequencyTemplates.AsQueryable();

            // Apply filters
            if (!string.IsNullOrEmpty(searchDto.Name))
            {
                query = query.Where(ft => ft.Name.Contains(searchDto.Name));
            }

            if (!string.IsNullOrEmpty(searchDto.FrequencyType))
            {
                query = query.Where(ft => ft.FrequencyType == searchDto.FrequencyType);
            }

            if (!string.IsNullOrEmpty(searchDto.Status))
            {
                query = query.Where(ft => ft.Status == searchDto.Status);
            }

            if (!string.IsNullOrEmpty(searchDto.Category))
            {
                query = query.Where(ft => ft.Category == searchDto.Category);
            }

            if (searchDto.IsDefault.HasValue)
            {
                query = query.Where(ft => ft.IsDefault == searchDto.IsDefault.Value);
            }

            if (searchDto.MinUsageCount.HasValue)
            {
                query = query.Where(ft => ft.UsageCount >= searchDto.MinUsageCount.Value);
            }

            if (searchDto.MaxUsageCount.HasValue)
            {
                query = query.Where(ft => ft.UsageCount <= searchDto.MaxUsageCount.Value);
            }

            var totalCount = await query.CountAsync();

            // Apply sorting
            switch (searchDto.SortBy?.ToLower())
            {
                case "name":
                    query = searchDto.SortDirection?.ToLower() == "desc" 
                        ? query.OrderByDescending(ft => ft.Name)
                        : query.OrderBy(ft => ft.Name);
                    break;
                case "frequencytype":
                    query = searchDto.SortDirection?.ToLower() == "desc"
                        ? query.OrderByDescending(ft => ft.FrequencyType)
                        : query.OrderBy(ft => ft.FrequencyType);
                    break;
                case "category":
                    query = searchDto.SortDirection?.ToLower() == "desc"
                        ? query.OrderByDescending(ft => ft.Category)
                        : query.OrderBy(ft => ft.Category);
                    break;
                case "usagecount":
                    query = searchDto.SortDirection?.ToLower() == "desc"
                        ? query.OrderByDescending(ft => ft.UsageCount)
                        : query.OrderBy(ft => ft.UsageCount);
                    break;
                case "lastused":
                    query = searchDto.SortDirection?.ToLower() == "desc"
                        ? query.OrderByDescending(ft => ft.LastUsed)
                        : query.OrderBy(ft => ft.LastUsed);
                    break;
                default:
                    query = searchDto.SortDirection?.ToLower() == "desc"
                        ? query.OrderByDescending(ft => ft.CreatedAt)
                        : query.OrderBy(ft => ft.CreatedAt);
                    break;
            }

            var templates = await query
                .Skip((searchDto.Page - 1) * searchDto.PageSize)
                .Take(searchDto.PageSize)
                .Select(ft => new FrequencyTemplateListDto
                {
                    Id = ft.Id,
                    Name = ft.Name,
                    Description = ft.Description,
                    FrequencyType = ft.FrequencyType,
                    IntervalValue = ft.IntervalValue,
                    IntervalUnit = ft.IntervalUnit,
                    Status = ft.Status,
                    Category = ft.Category,
                    IsDefault = ft.IsDefault,
                    EstimatedDuration = ft.EstimatedDuration,
                    UsageCount = ft.UsageCount,
                    LastUsed = ft.LastUsed,
                    CreatedAt = ft.CreatedAt,
                    CreatedBy = ft.CreatedBy
                })
                .ToListAsync();

            return (templates, totalCount);
        }

        public async Task<FrequencyTemplateDto?> GetFrequencyTemplateByIdAsync(int id)
        {
            var template = await _context.FrequencyTemplates
                .Where(ft => ft.Id == id)
                .Select(ft => new FrequencyTemplateDto
                {
                    Id = ft.Id,
                    Name = ft.Name,
                    Description = ft.Description,
                    FrequencyType = ft.FrequencyType,
                    IntervalValue = ft.IntervalValue,
                    IntervalUnit = ft.IntervalUnit,
                    DayOfWeek = ft.DayOfWeek,
                    DayOfMonth = ft.DayOfMonth,
                    MonthOfYear = ft.MonthOfYear,
                    TimeOfDay = ft.TimeOfDay,
                    Status = ft.Status,
                    Category = ft.Category,
                    IsDefault = ft.IsDefault,
                    EstimatedDuration = ft.EstimatedDuration,
                    Notes = ft.Notes,
                    UsageCount = ft.UsageCount,
                    LastUsed = ft.LastUsed,
                    AdvancedConfiguration = ft.AdvancedConfiguration,
                    CreatedAt = ft.CreatedAt,
                    CreatedBy = ft.CreatedBy,
                    UpdatedAt = ft.UpdatedAt,
                    UpdatedBy = ft.UpdatedBy
                })
                .FirstOrDefaultAsync();

            return template;
        }

        public async Task<FrequencyTemplateDto> CreateFrequencyTemplateAsync(CreateFrequencyTemplateDto createDto)
        {
            var template = new FrequencyTemplate
            {
                Name = createDto.Name,
                Description = createDto.Description,
                FrequencyType = createDto.FrequencyType,
                IntervalValue = createDto.IntervalValue,
                IntervalUnit = createDto.IntervalUnit,
                DayOfWeek = createDto.DayOfWeek,
                DayOfMonth = createDto.DayOfMonth,
                MonthOfYear = createDto.MonthOfYear,
                TimeOfDay = createDto.TimeOfDay,
                Category = createDto.Category,
                IsDefault = createDto.IsDefault,
                EstimatedDuration = createDto.EstimatedDuration.HasValue ? (int?)createDto.EstimatedDuration.Value : null,
                Notes = createDto.Notes,
                AdvancedConfiguration = createDto.AdvancedConfiguration,
                Status = "Active",
                UsageCount = 0,
                CreatedAt = DateTime.UtcNow,
                CreatedBy = "System" // TODO: Get from current user context
            };

            // If setting as default, unset other defaults of the same type
            if (createDto.IsDefault)
            {
                await UnsetDefaultsForTypeAsync(createDto.FrequencyType);
            }

            _context.FrequencyTemplates.Add(template);
            await _context.SaveChangesAsync();

            return await GetFrequencyTemplateByIdAsync(template.Id) ?? 
                   throw new InvalidOperationException("Failed to retrieve created template");
        }

        public async Task<FrequencyTemplateDto?> UpdateFrequencyTemplateAsync(int id, UpdateFrequencyTemplateDto updateDto)
        {
            var template = await _context.FrequencyTemplates.FindAsync(id);
            if (template == null)
            {
                return null;
            }

            // If setting as default, unset other defaults of the same type
            if (updateDto.IsDefault && !template.IsDefault)
            {
                await UnsetDefaultsForTypeAsync(updateDto.FrequencyType);
            }

            template.Name = updateDto.Name;
            template.Description = updateDto.Description;
            template.FrequencyType = updateDto.FrequencyType;
            template.IntervalValue = updateDto.IntervalValue;
            template.IntervalUnit = updateDto.IntervalUnit;
            template.DayOfWeek = updateDto.DayOfWeek;
            template.DayOfMonth = updateDto.DayOfMonth;
            template.MonthOfYear = updateDto.MonthOfYear;
            template.TimeOfDay = updateDto.TimeOfDay;
            template.Status = updateDto.Status;
            template.Category = updateDto.Category;
            template.IsDefault = updateDto.IsDefault;
            template.EstimatedDuration = updateDto.EstimatedDuration.HasValue ? (int?)updateDto.EstimatedDuration.Value : null;
            template.Notes = updateDto.Notes;
            template.AdvancedConfiguration = updateDto.AdvancedConfiguration;
            template.UpdatedAt = DateTime.UtcNow;
            template.UpdatedBy = "System"; // TODO: Get from current user context

            await _context.SaveChangesAsync();
            return await GetFrequencyTemplateByIdAsync(id);
        }

        public async Task<bool> DeleteFrequencyTemplateAsync(int id)
        {
            var template = await _context.FrequencyTemplates.FindAsync(id);
            if (template == null)
            {
                return false;
            }

            // Check if template is being used
            var isUsed = await IsTemplateUsedAsync(id);
            if (isUsed)
            {
                throw new InvalidOperationException("Cannot delete template that is currently in use");
            }

            _context.FrequencyTemplates.Remove(template);
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> UseTemplateAsync(int templateId)
        {
            var template = await _context.FrequencyTemplates.FindAsync(templateId);
            if (template == null)
            {
                return false;
            }

            template.UsageCount++;
            template.LastUsed = DateTime.UtcNow;
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<FrequencyTemplateDto?> DuplicateFrequencyTemplateAsync(int templateId, string newName)
        {
            var sourceTemplate = await _context.FrequencyTemplates.FindAsync(templateId);
            if (sourceTemplate == null)
            {
                return null;
            }

            var duplicatedTemplate = new FrequencyTemplate
            {
                Name = newName,
                Description = sourceTemplate.Description,
                FrequencyType = sourceTemplate.FrequencyType,
                IntervalValue = sourceTemplate.IntervalValue,
                IntervalUnit = sourceTemplate.IntervalUnit,
                DayOfWeek = sourceTemplate.DayOfWeek,
                DayOfMonth = sourceTemplate.DayOfMonth,
                MonthOfYear = sourceTemplate.MonthOfYear,
                TimeOfDay = sourceTemplate.TimeOfDay,
                Category = sourceTemplate.Category,
                IsDefault = false,
                EstimatedDuration = sourceTemplate.EstimatedDuration,
                Notes = sourceTemplate.Notes,
                AdvancedConfiguration = sourceTemplate.AdvancedConfiguration,
                Status = "Active",
                UsageCount = 0,
                CreatedAt = DateTime.UtcNow,
                CreatedBy = "System"
            };

            _context.FrequencyTemplates.Add(duplicatedTemplate);
            await _context.SaveChangesAsync();

            return await GetFrequencyTemplateByIdAsync(duplicatedTemplate.Id);
        }



        public async Task<FrequencyCalculationResultDto> CalculateFrequencyAsync(FrequencyCalculationDto calculationDto)
        {
            var scheduledDates = new List<DateTime>();
            var currentDate = calculationDto.StartDate.Date;
            var endDate = calculationDto.EndDate?.Date ?? DateTime.MaxValue.Date;
            var maxOccurrences = Math.Min(calculationDto.MaxOccurrences, 1000); // Limit to prevent infinite loops

            var iterations = 0;
            const int maxIterations = 10000; // Safety limit

            while (currentDate <= endDate && scheduledDates.Count < maxOccurrences && iterations < maxIterations)
            {
                iterations++;

                switch (calculationDto.FrequencyType.ToLower())
                {
                    case "daily":
                        scheduledDates.Add(currentDate);
                        currentDate = currentDate.AddDays(calculationDto.IntervalValue);
                        break;

                    case "weekly":
                        scheduledDates.Add(currentDate);
                        currentDate = currentDate.AddDays(7 * calculationDto.IntervalValue);
                        break;

                    case "monthly":
                        scheduledDates.Add(currentDate);
                        currentDate = currentDate.AddMonths(calculationDto.IntervalValue);
                        break;

                    case "quarterly":
                        scheduledDates.Add(currentDate);
                        currentDate = currentDate.AddMonths(3 * calculationDto.IntervalValue);
                        break;

                    case "annual":
                        scheduledDates.Add(currentDate);
                        currentDate = currentDate.AddYears(calculationDto.IntervalValue);
                        break;

                    default:
                        // For unknown frequency types, add current date and increment by 1 day to avoid infinite loop
                        scheduledDates.Add(currentDate);
                        currentDate = currentDate.AddDays(1);
                        break;
                }
            }

            // Apply time of day if specified
            if (!string.IsNullOrEmpty(calculationDto.TimeOfDay) && 
                TimeSpan.TryParse(calculationDto.TimeOfDay, out var timeOfDay))
            {
                scheduledDates = scheduledDates.Select(d => d.Add(timeOfDay)).ToList();
            }

            var result = new FrequencyCalculationResultDto
            {
                ScheduledDates = scheduledDates,
                TotalOccurrences = scheduledDates.Count,
                FirstOccurrence = scheduledDates.FirstOrDefault(),
                LastOccurrence = scheduledDates.LastOrDefault(),
                FrequencyDescription = GenerateFrequencyDescriptionSync(calculationDto)
            };

            return result;
        }

        public async Task<List<DateTime>> GetNextOccurrencesAsync(int templateId, DateTime startDate, int count = 10)
        {
            var template = await GetFrequencyTemplateByIdAsync(templateId);
            if (template == null)
            {
                return new List<DateTime>();
            }

            var calculationDto = new FrequencyCalculationDto
            {
                StartDate = startDate,
                FrequencyType = template.FrequencyType,
                IntervalValue = template.IntervalValue,
                IntervalUnit = template.IntervalUnit,
                DayOfWeek = template.DayOfWeek,
                DayOfMonth = template.DayOfMonth,
                MonthOfYear = template.MonthOfYear,
                TimeOfDay = template.TimeOfDay,
                MaxOccurrences = count
            };

            var result = await CalculateFrequencyAsync(calculationDto);
            return result.ScheduledDates;
        }

        public async Task<List<FrequencyTemplateListDto>> GetDefaultTemplatesAsync()
        {
            var (templates, _) = await GetFrequencyTemplatesAsync(new FrequencyTemplateSearchDto
            {
                IsDefault = true,
                Status = "Active",
                PageSize = 100,
                SortBy = "FrequencyType"
            });

            return templates;
        }

        public async Task<bool> SetAsDefaultAsync(int templateId, bool isDefault = true)
        {
            var template = await _context.FrequencyTemplates.FindAsync(templateId);
            if (template == null)
            {
                return false;
            }

            if (isDefault)
            {
                // Unset other defaults of the same type
                await UnsetDefaultsForTypeAsync(template.FrequencyType);
            }

            template.IsDefault = isDefault;
            template.UpdatedAt = DateTime.UtcNow;
            template.UpdatedBy = "System";

            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<FrequencyTemplateDto?> GetDefaultTemplateByTypeAsync(string frequencyType)
        {
            var template = await _context.FrequencyTemplates
                .Where(ft => ft.FrequencyType == frequencyType && ft.IsDefault && ft.Status == "Active")
                .Select(ft => new FrequencyTemplateDto
                {
                    Id = ft.Id,
                    Name = ft.Name,
                    Description = ft.Description,
                    FrequencyType = ft.FrequencyType,
                    IntervalValue = ft.IntervalValue,
                    IntervalUnit = ft.IntervalUnit,
                    DayOfWeek = ft.DayOfWeek,
                    DayOfMonth = ft.DayOfMonth,
                    MonthOfYear = ft.MonthOfYear,
                    TimeOfDay = ft.TimeOfDay,
                    Status = ft.Status,
                    Category = ft.Category,
                    IsDefault = ft.IsDefault,
                    EstimatedDuration = ft.EstimatedDuration,
                    Notes = ft.Notes,
                    UsageCount = ft.UsageCount,
                    LastUsed = ft.LastUsed,
                    AdvancedConfiguration = ft.AdvancedConfiguration,
                    CreatedAt = ft.CreatedAt,
                    CreatedBy = ft.CreatedBy,
                    UpdatedAt = ft.UpdatedAt,
                    UpdatedBy = ft.UpdatedBy
                })
                .FirstOrDefaultAsync();

            return template;
        }

        public async Task<List<string>> GetCategoriesAsync()
        {
            var categories = await _context.FrequencyTemplates
                .Where(ft => !string.IsNullOrEmpty(ft.Category))
                .Select(ft => ft.Category!)
                .Distinct()
                .OrderBy(c => c)
                .ToListAsync();

            return categories;
        }

        public async Task<List<FrequencyTemplateListDto>> GetTemplatesByCategoryAsync(string category)
        {
            var (templates, _) = await GetFrequencyTemplatesAsync(new FrequencyTemplateSearchDto
            {
                Category = category,
                Status = "Active",
                PageSize = 100,
                SortBy = "Name"
            });

            return templates;
        }

        public async Task<List<string>> ValidateTemplateAsync(CreateFrequencyTemplateDto templateDto)
        {
            var errors = new List<string>();

            if (string.IsNullOrWhiteSpace(templateDto.Name))
            {
                errors.Add("Template name is required");
            }

            if (string.IsNullOrWhiteSpace(templateDto.FrequencyType))
            {
                errors.Add("Frequency type is required");
            }

            if (templateDto.IntervalValue <= 0)
            {
                errors.Add("Interval value must be greater than 0");
            }

            // Validate frequency-specific fields
            switch (templateDto.FrequencyType?.ToLower())
            {
                case "weekly":
                    if (!string.IsNullOrEmpty(templateDto.DayOfWeek) && 
                        !Enum.TryParse<DayOfWeek>(templateDto.DayOfWeek, out _))
                    {
                        errors.Add("Invalid day of week");
                    }
                    break;

                case "monthly":
                    if (templateDto.DayOfMonth.HasValue && 
                        (templateDto.DayOfMonth < 1 || templateDto.DayOfMonth > 31))
                    {
                        errors.Add("Day of month must be between 1 and 31");
                    }
                    break;
            }

            // Validate time of day format
            if (!string.IsNullOrEmpty(templateDto.TimeOfDay) && 
                !TimeSpan.TryParse(templateDto.TimeOfDay, out _))
            {
                errors.Add("Invalid time of day format (use HH:mm)");
            }

            // Check for duplicate names
            var existingTemplate = await _context.FrequencyTemplates
                .AnyAsync(ft => ft.Name == templateDto.Name);
            if (existingTemplate)
            {
                errors.Add("A template with this name already exists");
            }

            return errors;
        }

        public async Task<bool> IsTemplateUsedAsync(int templateId)
        {
            // Check if any schedules are using this template
            // This would require a relationship between Schedule and FrequencyTemplate
            // For now, return false as we haven't implemented the relationship yet
            return false;
        }

        public async Task<FrequencyTemplateStatisticsDto> GetFrequencyTemplateStatisticsAsync()
        {
            var statistics = new FrequencyTemplateStatisticsDto();

            statistics.TotalTemplates = await _context.FrequencyTemplates.CountAsync();
            statistics.ActiveTemplates = await _context.FrequencyTemplates.CountAsync(ft => ft.Status == "Active");
            statistics.DefaultTemplates = await _context.FrequencyTemplates.CountAsync(ft => ft.IsDefault);
            statistics.TotalUsages = await _context.FrequencyTemplates.SumAsync(ft => ft.UsageCount);

            // Type breakdown
            statistics.TypeBreakdown = await _context.FrequencyTemplates
                .GroupBy(ft => ft.FrequencyType)
                .ToDictionaryAsync(g => g.Key, g => g.Count());

            // Category breakdown
            statistics.CategoryBreakdown = await _context.FrequencyTemplates
                .Where(ft => !string.IsNullOrEmpty(ft.Category))
                .GroupBy(ft => ft.Category!)
                .ToDictionaryAsync(g => g.Key, g => g.Count());

            // Status breakdown
            statistics.StatusBreakdown = await _context.FrequencyTemplates
                .GroupBy(ft => ft.Status)
                .ToDictionaryAsync(g => g.Key, g => g.Count());

            // Most used templates
            var (mostUsed, _) = await GetFrequencyTemplatesAsync(new FrequencyTemplateSearchDto
            {
                PageSize = 10,
                SortBy = "UsageCount",
                SortDirection = "desc"
            });
            statistics.MostUsedTemplates = mostUsed;

            return statistics;
        }

        public async Task<List<FrequencyTemplateListDto>> GetMostUsedTemplatesAsync(int count = 10)
        {
            var (templates, _) = await GetFrequencyTemplatesAsync(new FrequencyTemplateSearchDto
            {
                PageSize = count,
                SortBy = "UsageCount",
                SortDirection = "desc"
            });

            return templates;
        }

        public async Task<Dictionary<string, int>> GetUsageStatsByTypeAsync()
        {
            var stats = await _context.FrequencyTemplates
                .GroupBy(ft => ft.FrequencyType)
                .ToDictionaryAsync(g => g.Key, g => g.Sum(ft => ft.UsageCount));

            return stats;
        }

        public async Task<byte[]> ExportTemplatesToCsvAsync()
        {
            // Implementation for CSV export
            throw new NotImplementedException("CSV export will be implemented");
        }

        public async Task<byte[]> ExportTemplatesToExcelAsync()
        {
            // Implementation for Excel export
            throw new NotImplementedException("Excel export will be implemented");
        }

        public async Task<int> ImportTemplatesFromCsvAsync(Stream csvStream)
        {
            // Implementation for CSV import
            throw new NotImplementedException("CSV import will be implemented");
        }

        public async Task<int> ImportTemplatesFromExcelAsync(Stream excelStream)
        {
            // Implementation for Excel import
            throw new NotImplementedException("Excel import will be implemented");
        }

        public async Task<string> GenerateFrequencyDescriptionAsync(FrequencyTemplateDto template)
        {
            return GenerateFrequencyDescriptionSync(new FrequencyCalculationDto
            {
                FrequencyType = template.FrequencyType,
                IntervalValue = template.IntervalValue,
                IntervalUnit = template.IntervalUnit,
                DayOfWeek = template.DayOfWeek,
                DayOfMonth = template.DayOfMonth,
                MonthOfYear = template.MonthOfYear,
                TimeOfDay = template.TimeOfDay
            });
        }

        public async Task<bool> IsValidFrequencyConfigurationAsync(string frequencyType, int intervalValue, string? intervalUnit)
        {
            if (intervalValue <= 0)
                return false;

            var validTypes = new[] { "Daily", "Weekly", "Monthly", "Quarterly", "Annual", "Custom" };
            return validTypes.Contains(frequencyType, StringComparer.OrdinalIgnoreCase);
        }

        public async Task<List<DateTime>> CalculateDateRangeAsync(DateTime startDate, DateTime endDate, FrequencyTemplateDto template)
        {
            var calculationDto = new FrequencyCalculationDto
            {
                StartDate = startDate,
                EndDate = endDate,
                FrequencyType = template.FrequencyType,
                IntervalValue = template.IntervalValue,
                IntervalUnit = template.IntervalUnit,
                DayOfWeek = template.DayOfWeek,
                DayOfMonth = template.DayOfMonth,
                MonthOfYear = template.MonthOfYear,
                TimeOfDay = template.TimeOfDay,
                MaxOccurrences = 1000 // Large number to get all dates in range
            };

            var result = await CalculateFrequencyAsync(calculationDto);
            return result.ScheduledDates.Where(d => d >= startDate && d <= endDate).ToList();
        }

        #region Private Helper Methods

        private async Task UnsetDefaultsForTypeAsync(string frequencyType)
        {
            var existingDefaults = await _context.FrequencyTemplates
                .Where(ft => ft.FrequencyType == frequencyType && ft.IsDefault)
                .ToListAsync();

            foreach (var template in existingDefaults)
            {
                template.IsDefault = false;
                template.UpdatedAt = DateTime.UtcNow;
                template.UpdatedBy = "System";
            }

            await _context.SaveChangesAsync();
        }

        private static DateTime GetNextDayOfWeek(DateTime startDate, DayOfWeek targetDayOfWeek)
        {
            var daysAhead = targetDayOfWeek - startDate.DayOfWeek;
            if (daysAhead <= 0)
                daysAhead += 7;
            return startDate.AddDays(daysAhead);
        }

        private static string GenerateFrequencyDescriptionSync(FrequencyCalculationDto calculationDto)
        {
            var description = calculationDto.FrequencyType.ToLower() switch
            {
                "daily" => calculationDto.IntervalValue == 1 ? "Daily" : $"Every {calculationDto.IntervalValue} days",
                "weekly" => calculationDto.IntervalValue == 1 ? 
                    (string.IsNullOrEmpty(calculationDto.DayOfWeek) ? "Weekly" : $"Every {calculationDto.DayOfWeek}") :
                    $"Every {calculationDto.IntervalValue} weeks" + 
                    (string.IsNullOrEmpty(calculationDto.DayOfWeek) ? "" : $" on {calculationDto.DayOfWeek}"),
                "monthly" => calculationDto.IntervalValue == 1 ? 
                    (calculationDto.DayOfMonth.HasValue ? $"Monthly on day {calculationDto.DayOfMonth}" : "Monthly") :
                    $"Every {calculationDto.IntervalValue} months" +
                    (calculationDto.DayOfMonth.HasValue ? $" on day {calculationDto.DayOfMonth}" : ""),
                "quarterly" => calculationDto.IntervalValue == 1 ? "Quarterly" : $"Every {calculationDto.IntervalValue} quarters",
                "annual" => calculationDto.IntervalValue == 1 ? "Annually" : $"Every {calculationDto.IntervalValue} years",
                _ => "Custom frequency"
            };

            if (!string.IsNullOrEmpty(calculationDto.TimeOfDay))
            {
                description += $" at {calculationDto.TimeOfDay}";
            }

            return description;
        }

        #endregion
    }
} 