using Microsoft.EntityFrameworkCore;
using System.Text.Json;
using WaterMeterManagement.Data;
using WaterMeterManagement.DTOs;
using WaterMeterManagement.Models;
using WaterMeterManagement.Services.Interfaces;

namespace WaterMeterManagement.Services
{
    public class AmisSyncService : IAmisSyncService
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<AmisSyncService> _logger;
        private readonly IHttpClientFactory _httpClientFactory;
        private readonly IConfiguration _configuration;

        public AmisSyncService(
            ApplicationDbContext context, 
            ILogger<AmisSyncService> logger,
            IHttpClientFactory httpClientFactory,
            IConfiguration configuration)
        {
            _context = context;
            _logger = logger;
            _httpClientFactory = httpClientFactory;
            _configuration = configuration;
        }

        public async Task<(List<AmisSyncListDto> Syncs, int TotalCount)> GetSyncHistoryAsync(AmisSyncSearchDto searchDto)
        {
            var query = _context.AmisSyncs.AsQueryable();

            // Apply filters
            if (!string.IsNullOrEmpty(searchDto.SyncType))
            {
                query = query.Where(s => s.SyncType == searchDto.SyncType);
            }

            if (!string.IsNullOrEmpty(searchDto.Status))
            {
                query = query.Where(s => s.Status == searchDto.Status);
            }

            if (searchDto.StartTimeFrom.HasValue)
            {
                query = query.Where(s => s.StartTime >= searchDto.StartTimeFrom.Value);
            }

            if (searchDto.StartTimeTo.HasValue)
            {
                query = query.Where(s => s.StartTime <= searchDto.StartTimeTo.Value);
            }

            if (!string.IsNullOrEmpty(searchDto.TriggerBy))
            {
                query = query.Where(s => s.TriggerBy == searchDto.TriggerBy);
            }

            var totalCount = await query.CountAsync();

            // Apply sorting
            if (!string.IsNullOrEmpty(searchDto.SortBy))
            {
                switch (searchDto.SortBy.ToLower())
                {
                    case "starttime":
                        query = searchDto.SortDirection?.ToLower() == "asc"
                            ? query.OrderBy(s => s.StartTime)
                            : query.OrderByDescending(s => s.StartTime);
                        break;
                    case "synctype":
                        query = searchDto.SortDirection?.ToLower() == "desc"
                            ? query.OrderByDescending(s => s.SyncType)
                            : query.OrderBy(s => s.SyncType);
                        break;
                    case "status":
                        query = searchDto.SortDirection?.ToLower() == "desc"
                            ? query.OrderByDescending(s => s.Status)
                            : query.OrderBy(s => s.Status);
                        break;
                    default:
                        query = query.OrderByDescending(s => s.StartTime);
                        break;
                }
            }
            else
            {
                query = query.OrderByDescending(s => s.StartTime);
            }

            // Apply pagination
            var syncs = await query
                .Skip((searchDto.Page - 1) * searchDto.PageSize)
                .Take(searchDto.PageSize)
                .Select(s => new AmisSyncListDto
                {
                    Id = s.Id,
                    SyncType = s.SyncType,
                    Status = s.Status,
                    StartTime = s.StartTime,
                    EndTime = s.EndTime,
                    Duration = s.Duration,
                    TotalRecords = s.TotalRecords,
                    SuccessfulRecords = s.SuccessfulRecords,
                    FailedRecords = s.FailedRecords,
                    ProgressPercentage = s.ProgressPercentage,
                    ErrorMessage = s.ErrorMessage
                })
                .ToListAsync();

            return (syncs, totalCount);
        }

        public async Task<AmisSyncDto?> GetSyncByIdAsync(int id)
        {
            var sync = await _context.AmisSyncs
                .Include(s => s.SyncErrors)
                .FirstOrDefaultAsync(s => s.Id == id);

            if (sync == null) return null;

            return MapToDto(sync);
        }

        public async Task<AmisSyncDto> StartManualSyncAsync(ManualSyncRequestDto request, string triggerBy)
        {
            var sync = new AmisSync
            {
                SyncType = request.SyncType,
                Status = "Pending",
                StartTime = DateTime.UtcNow,
                TriggerBy = triggerBy,
                SyncConfiguration = JsonSerializer.Serialize(new
                {
                    FromDate = request.FromDate,
                    ToDate = request.ToDate,
                    ForceFullSync = request.ForceFullSync,
                    Notes = request.Notes
                }),
                CreatedAt = DateTime.UtcNow,
                CreatedBy = triggerBy,
                UpdatedAt = DateTime.UtcNow,
                UpdatedBy = triggerBy
            };

            _context.AmisSyncs.Add(sync);
            await _context.SaveChangesAsync();

            // Start sync process in background
            _ = Task.Run(async () => await ExecuteSyncAsync(sync.Id));

            return MapToDto(sync);
        }

        public async Task<bool> CancelSyncAsync(int syncId, string cancelledBy)
        {
            var sync = await _context.AmisSyncs.FirstOrDefaultAsync(s => s.Id == syncId);
            if (sync == null || !CanCancelSync(sync.Status))
                return false;

            sync.Status = "Cancelled";
            sync.EndTime = DateTime.UtcNow;
            sync.ErrorMessage = $"Cancelled by {cancelledBy}";
            sync.UpdatedAt = DateTime.UtcNow;
            sync.UpdatedBy = cancelledBy;

            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<SyncProgressDto?> GetSyncProgressAsync(int syncId)
        {
            var sync = await _context.AmisSyncs.FirstOrDefaultAsync(s => s.Id == syncId);
            if (sync == null) return null;

            return new SyncProgressDto
            {
                Id = sync.Id,
                Status = sync.Status,
                ProgressPercentage = sync.ProgressPercentage,
                CurrentOperation = sync.CurrentOperation,
                ProcessedRecords = sync.ProcessedRecords,
                TotalRecords = sync.TotalRecords,
                ErrorMessage = sync.ErrorMessage,
                LastUpdate = sync.UpdatedAt ?? sync.CreatedAt
            };
        }

        public async Task<List<AmisSyncErrorDto>> GetSyncErrorsAsync(int syncId)
        {
            return await _context.AmisSyncErrors
                .Where(e => e.AmisSyncId == syncId)
                .Select(e => new AmisSyncErrorDto
                {
                    Id = e.Id,
                    AmisSyncId = e.AmisSyncId,
                    ErrorType = e.ErrorType,
                    Severity = e.Severity,
                    ErrorMessage = e.ErrorMessage,
                    DetailedError = e.DetailedError,
                    RecordIdentifier = e.RecordIdentifier,
                    RecordLineNumber = e.RecordLineNumber,
                    Operation = e.Operation,
                    Endpoint = e.Endpoint,
                    HttpStatusCode = e.HttpStatusCode,
                    ResolutionStatus = e.ResolutionStatus,
                    ResolvedDate = e.ResolvedDate,
                    ResolvedBy = e.ResolvedBy,
                    ResolutionNotes = e.ResolutionNotes,
                    CanRetry = e.CanRetry,
                    RetryCount = e.RetryCount,
                    LastRetryDate = e.LastRetryDate,
                    CreatedAt = e.CreatedAt
                })
                .OrderByDescending(e => e.CreatedAt)
                .ToListAsync();
        }

        public async Task<bool> RetrySyncAsync(int syncId, string retryBy)
        {
            var sync = await _context.AmisSyncs.FirstOrDefaultAsync(s => s.Id == syncId);
            if (sync == null || !CanRetrySync(sync.Status))
                return false;

            sync.Status = "Pending";
            sync.RetryCount++;
            sync.NextRetryTime = null;
            sync.ErrorMessage = null;
            sync.UpdatedAt = DateTime.UtcNow;
            sync.UpdatedBy = retryBy;

            await _context.SaveChangesAsync();

            // Start sync process in background
            _ = Task.Run(async () => await ExecuteSyncAsync(sync.Id));

            return true;
        }

        public async Task<bool> ResolveSyncErrorAsync(int errorId, string resolutionNotes, string resolvedBy)
        {
            var error = await _context.AmisSyncErrors.FirstOrDefaultAsync(e => e.Id == errorId);
            if (error == null) return false;

            error.ResolutionStatus = "Resolved";
            error.ResolvedDate = DateTime.UtcNow;
            error.ResolvedBy = resolvedBy;
            error.ResolutionNotes = resolutionNotes;
            error.UpdatedAt = DateTime.UtcNow;
            error.UpdatedBy = resolvedBy;

            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<AmisSyncConfigurationDto> GetSyncConfigurationAsync()
        {
            // This would typically come from configuration or database
            return new AmisSyncConfigurationDto
            {
                AmisEndpoint = _configuration["AmisSync:Endpoint"],
                TimeoutSeconds = int.Parse(_configuration["AmisSync:TimeoutSeconds"] ?? "30"),
                BatchSize = int.Parse(_configuration["AmisSync:BatchSize"] ?? "100"),
                EnableRetry = bool.Parse(_configuration["AmisSync:EnableRetry"] ?? "true"),
                MaxRetries = int.Parse(_configuration["AmisSync:MaxRetries"] ?? "3"),
                RetryDelaySeconds = int.Parse(_configuration["AmisSync:RetryDelaySeconds"] ?? "60"),
                SyncTypes = new List<string> { "FullSync", "IncrementalSync", "MeterData", "BaselineData" }
            };
        }

        public async Task<bool> UpdateSyncConfigurationAsync(AmisSyncConfigurationDto configuration)
        {
            // This would typically update configuration in database or config file
            // For now, just return true as a placeholder
            return await Task.FromResult(true);
        }

        public async Task<bool> TestAmisConnectionAsync()
        {
            try
            {
                var config = await GetSyncConfigurationAsync();
                if (string.IsNullOrEmpty(config.AmisEndpoint))
                    return false;

                using var httpClient = _httpClientFactory.CreateClient();
                httpClient.Timeout = TimeSpan.FromSeconds(config.TimeoutSeconds);

                var response = await httpClient.GetAsync($"{config.AmisEndpoint}/health");
                return response.IsSuccessStatusCode;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error testing AMIS connection");
                return false;
            }
        }

        public async Task<List<AmisSyncDto>> GetActiveSyncsAsync()
        {
            return await _context.AmisSyncs
                .Where(s => s.Status == "Running" || s.Status == "Pending")
                .Select(s => MapToDto(s))
                .ToListAsync();
        }

        public async Task<List<AmisSyncErrorDto>> GetUnresolvedErrorsAsync()
        {
            return await _context.AmisSyncErrors
                .Where(e => e.ResolutionStatus == "Unresolved")
                .Select(e => new AmisSyncErrorDto
                {
                    Id = e.Id,
                    AmisSyncId = e.AmisSyncId,
                    ErrorType = e.ErrorType,
                    Severity = e.Severity,
                    ErrorMessage = e.ErrorMessage,
                    RecordIdentifier = e.RecordIdentifier,
                    Operation = e.Operation,
                    CreatedAt = e.CreatedAt
                })
                .OrderByDescending(e => e.CreatedAt)
                .ToListAsync();
        }

        public async Task<Dictionary<string, object>> GetSyncStatisticsAsync()
        {
            var today = DateTime.UtcNow.Date;
            var thisWeek = today.AddDays(-7);
            var thisMonth = today.AddDays(-30);

            var stats = new Dictionary<string, object>
            {
                ["TotalSyncs"] = await _context.AmisSyncs.CountAsync(),
                ["TodaySyncs"] = await _context.AmisSyncs.CountAsync(s => s.StartTime >= today),
                ["WeeklySyncs"] = await _context.AmisSyncs.CountAsync(s => s.StartTime >= thisWeek),
                ["MonthlySyncs"] = await _context.AmisSyncs.CountAsync(s => s.StartTime >= thisMonth),
                ["SuccessfulSyncs"] = await _context.AmisSyncs.CountAsync(s => s.Status == "Completed"),
                ["FailedSyncs"] = await _context.AmisSyncs.CountAsync(s => s.Status == "Failed"),
                ["UnresolvedErrors"] = await _context.AmisSyncErrors.CountAsync(e => e.ResolutionStatus == "Unresolved"),
                ["ActiveSyncs"] = await _context.AmisSyncs.CountAsync(s => s.Status == "Running" || s.Status == "Pending")
            };

            return stats;
        }

        public async Task CleanupOldSyncLogsAsync(int retentionDays = 30)
        {
            var cutoffDate = DateTime.UtcNow.AddDays(-retentionDays);
            
            var oldSyncs = await _context.AmisSyncs
                .Where(s => s.CreatedAt < cutoffDate && s.Status != "Running" && s.Status != "Pending")
                .ToListAsync();

            if (oldSyncs.Any())
            {
                _context.AmisSyncs.RemoveRange(oldSyncs);
                await _context.SaveChangesAsync();
                
                _logger.LogInformation("Cleaned up {Count} old sync logs older than {Days} days", 
                    oldSyncs.Count, retentionDays);
            }
        }

        private async Task ExecuteSyncAsync(int syncId)
        {
            var sync = await _context.AmisSyncs.FirstOrDefaultAsync(s => s.Id == syncId);
            if (sync == null) return;

            try
            {
                sync.Status = "Running";
                sync.StartTime = DateTime.UtcNow;
                sync.UpdatedAt = DateTime.UtcNow;
                await _context.SaveChangesAsync();

                // Simulate sync process - in real implementation, this would call AMIS APIs
                await SimulateSyncProcess(sync);

                sync.Status = "Completed";
                sync.EndTime = DateTime.UtcNow;
                sync.ProgressPercentage = 100;
                sync.ResultSummary = $"Successfully synced {sync.SuccessfulRecords} records";
                sync.UpdatedAt = DateTime.UtcNow;
                await _context.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                sync.Status = "Failed";
                sync.EndTime = DateTime.UtcNow;
                sync.ErrorMessage = ex.Message;
                sync.UpdatedAt = DateTime.UtcNow;
                await _context.SaveChangesAsync();

                _logger.LogError(ex, "Sync {SyncId} failed", syncId);
            }
        }

        private async Task SimulateSyncProcess(AmisSync sync)
        {
            // Simulate a sync process with progress updates
            var totalRecords = new Random().Next(50, 200);
            sync.TotalRecords = totalRecords;
            
            for (int i = 0; i < totalRecords; i++)
            {
                await Task.Delay(100); // Simulate processing time
                
                sync.ProcessedRecords = i + 1;
                sync.ProgressPercentage = (decimal)(i + 1) / totalRecords * 100;
                sync.CurrentOperation = $"Processing record {i + 1} of {totalRecords}";
                
                // Simulate occasional errors
                if (new Random().Next(0, 100) < 5) // 5% error rate
                {
                    sync.FailedRecords++;
                    await CreateSyncError(sync.Id, "Processing", "Error", $"Failed to process record {i + 1}", $"Record{i + 1}");
                }
                else
                {
                    sync.SuccessfulRecords++;
                }
                
                sync.UpdatedAt = DateTime.UtcNow;
                
                if (i % 10 == 0) // Update progress every 10 records
                {
                    await _context.SaveChangesAsync();
                }
            }
        }

        private async Task CreateSyncError(int syncId, string errorType, string severity, string errorMessage, string recordIdentifier)
        {
            var error = new AmisSyncError
            {
                AmisSyncId = syncId,
                ErrorType = errorType,
                Severity = severity,
                ErrorMessage = errorMessage,
                RecordIdentifier = recordIdentifier,
                ResolutionStatus = "Unresolved",
                CanRetry = true,
                CreatedAt = DateTime.UtcNow,
                CreatedBy = "System",
                UpdatedAt = DateTime.UtcNow,
                UpdatedBy = "System"
            };

            _context.AmisSyncErrors.Add(error);
        }

        private static bool CanCancelSync(string status)
        {
            return status == "Pending" || status == "Running";
        }

        private static bool CanRetrySync(string status)
        {
            return status == "Failed" || status == "Cancelled";
        }

        private static AmisSyncDto MapToDto(AmisSync sync)
        {
            return new AmisSyncDto
            {
                Id = sync.Id,
                SyncType = sync.SyncType,
                Status = sync.Status,
                StartTime = sync.StartTime,
                EndTime = sync.EndTime,
                Duration = sync.Duration,
                TriggerBy = sync.TriggerBy,
                TotalRecords = sync.TotalRecords,
                ProcessedRecords = sync.ProcessedRecords,
                SuccessfulRecords = sync.SuccessfulRecords,
                FailedRecords = sync.FailedRecords,
                SkippedRecords = sync.SkippedRecords,
                ProgressPercentage = sync.ProgressPercentage,
                CurrentOperation = sync.CurrentOperation,
                ResultSummary = sync.ResultSummary,
                ErrorMessage = sync.ErrorMessage,
                RecordsPerSecond = sync.RecordsPerSecond,
                DataSizeMB = sync.DataSizeMB,
                RetryCount = sync.RetryCount,
                MaxRetries = sync.MaxRetries,
                NextRetryTime = sync.NextRetryTime,
                AmisEndpoint = sync.AmisEndpoint,
                AmisVersion = sync.AmisVersion,
                LastSyncDate = sync.LastSyncDate,
                CreatedAt = sync.CreatedAt,
                UpdatedAt = sync.UpdatedAt ?? sync.CreatedAt
            };
        }
    }
} 