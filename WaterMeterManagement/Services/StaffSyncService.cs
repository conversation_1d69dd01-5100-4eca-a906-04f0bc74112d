using Microsoft.EntityFrameworkCore;
using WaterMeterManagement.Data;
using WaterMeterManagement.DTOs;
using WaterMeterManagement.Models;
using WaterMeterManagement.Services.Interfaces;

namespace WaterMeterManagement.Services
{
    /// <summary>
    /// Staff synchronization service implementation
    /// </summary>
    public class StaffSyncService : IStaffSyncService
    {
        private readonly ApplicationDbContext _context;
        private readonly IWorkbenchService _workbenchService;
        private readonly ILogger<StaffSyncService> _logger;

        public StaffSyncService(
            ApplicationDbContext context,
            IWorkbenchService workbenchService,
            ILogger<StaffSyncService> logger)
        {
            _context = context;
            _workbenchService = workbenchService;
            _logger = logger;
        }

        public async Task<StaffSyncResponseDto> SyncStaffDataAsync()
        {
            var response = new StaffSyncResponseDto
            {
                Success = false,
                Message = "Staff synchronization failed",
                SyncTime = DateTime.UtcNow
            };

            try
            {
                _logger.LogInformation("Starting staff data synchronization from Workbench");

                // Get the first page of staff data to determine total pages
                var initialData = await _workbenchService.FetchStaffDataAsync(1, 100);
                if (initialData == null || initialData.Rows.Count == 0)
                {
                    response.Message = "No staff data found in Workbench API";
                    _logger.LogWarning("No staff data returned from Workbench API");
                    return response;
                }

                // Calculate total pages
                var totalPages = Math.Ceiling((double)initialData.Records / 100);
                _logger.LogInformation("Total staff records: {Records}, Total pages: {Pages}", 
                    initialData.Records, totalPages);

                var totalProcessed = 0;
                var successCount = 0;
                var failureCount = 0;
                var errors = new List<string>();

                // Process the first page
                var firstPageResult = await ProcessStaffRows(initialData.Rows);
                totalProcessed += firstPageResult.TotalProcessed;
                successCount += firstPageResult.SuccessCount;
                failureCount += firstPageResult.FailureCount;
                errors.AddRange(firstPageResult.Errors);

                // Process remaining pages
                for (int page = 2; page <= totalPages; page++)
                {
                    _logger.LogInformation("Processing page {Page} of {TotalPages}", page, totalPages);
                    
                    var staffData = await _workbenchService.FetchStaffDataAsync(page, 100);
                    if (staffData != null && staffData.Rows.Count > 0)
                    {
                        var pageResult = await ProcessStaffRows(staffData.Rows);
                        totalProcessed += pageResult.TotalProcessed;
                        successCount += pageResult.SuccessCount;
                        failureCount += pageResult.FailureCount;
                        errors.AddRange(pageResult.Errors);
                    }
                }

                // Update response
                response.Success = failureCount == 0;
                response.TotalProcessed = totalProcessed;
                response.SuccessCount = successCount;
                response.FailureCount = failureCount;
                response.Errors = errors;
                response.Message = response.Success 
                    ? $"Staff synchronization completed successfully. {successCount} records processed."
                    : $"Staff synchronization completed with errors. {successCount} successful, {failureCount} failed.";

                _logger.LogInformation("Staff synchronization completed: {Success} successful, {Failed} failed", 
                    successCount, failureCount);

                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during staff synchronization: {Message}", ex.Message);
                response.Message = $"Staff synchronization failed: {ex.Message}";
                response.Errors.Add(ex.Message);
                return response;
            }
        }

        private async Task<StaffSyncResponseDto> ProcessStaffRows(List<WorkbenchStaffRow> rows)
        {
            var result = new StaffSyncResponseDto();
            
            foreach (var row in rows)
            {
                try
                {
                    result.TotalProcessed++;

                    // Log raw row data for debugging
                    _logger.LogInformation("Processing raw staff row: Col01={Col01}, Col02={Col02}, Col03={Col03}, Col04={Col04}, Col05={Col05}, Col06={Col06}", 
                        row.Col01 ?? "NULL", row.Col02 ?? "NULL", row.Col03 ?? "NULL", 
                        row.Col04 ?? "NULL", row.Col05 ?? "NULL", row.Col06 ?? "NULL");

                    // Parse and validate data
                    if (!int.TryParse(row.Col01, out int personId))
                    {
                        var error = $"Invalid PersonID: {row.Col01}";
                        result.Errors.Add(error);
                        result.FailureCount++;
                        _logger.LogWarning(error);
                        continue;
                    }

                    // Create staff DTO
                    var staffDto = new WorkbenchStaffDto
                    {
                        PersonId = personId,
                        FullName = row.Col02 ?? string.Empty,
                        EmailAddress = row.Col03 ?? string.Empty,
                        MobilePhone = row.Col04 ?? string.Empty,
                        ProfitCentreCode = row.Col05 ?? string.Empty,
                        EmployeeNo = row.Col06 ?? string.Empty
                    };

                    // Log mapped DTO for debugging
                    _logger.LogInformation("Mapped staff DTO: PersonId={PersonId}, FullName='{FullName}', Email='{Email}', Mobile='{Mobile}', ProfitCentre='{ProfitCentre}', EmployeeNo='{EmployeeNo}'", 
                        staffDto.PersonId, staffDto.FullName, staffDto.EmailAddress, 
                        staffDto.MobilePhone, staffDto.ProfitCentreCode, staffDto.EmployeeNo);

                    // Upsert user data
                    await UpsertUserAsync(staffDto);
                    result.SuccessCount++;

                    _logger.LogDebug("Successfully processed staff: {FullName} (ID: {PersonId})", 
                        staffDto.FullName, staffDto.PersonId);
                }
                catch (Exception ex)
                {
                    var error = $"Error processing staff {row.Col02} (ID: {row.Col01}): {ex.Message}";
                    result.Errors.Add(error);
                    result.FailureCount++;
                    _logger.LogError(ex, error);
                }
            }

            return result;
        }

        private async Task UpsertUserAsync(WorkbenchStaffDto staffDto)
        {
            // Find existing user by PersonId
            var existingUser = await _context.Users
                .FirstOrDefaultAsync(u => u.PersonId == staffDto.PersonId);

            if (existingUser != null)
            {
                // Update existing user (keep original username and FinCoCode unchanged)
                existingUser.FullName = staffDto.FullName;
                existingUser.Email = staffDto.EmailAddress;
                existingUser.MobilePhone = staffDto.MobilePhone;
                existingUser.ProfitCentreCode = staffDto.ProfitCentreCode;
                existingUser.EmployeeNo = staffDto.EmployeeNo;
                existingUser.UpdatedDate = DateTime.UtcNow;
                // NOTE: Username and FinCoCode are NOT updated to preserve login credentials

                _context.Users.Update(existingUser);
                _logger.LogInformation("Updated existing user: {FullName} (ID: {PersonId}), username preserved: {Username}, FinCoCode preserved: {FinCoCode}", 
                    staffDto.FullName, staffDto.PersonId, existingUser.Username, existingUser.FinCoCode);
                
                // Log the updated values for debugging
                _logger.LogInformation("Updated user values: Email='{Email}', Mobile='{Mobile}', ProfitCentre='{ProfitCentre}', EmployeeNo='{EmployeeNo}'",
                    existingUser.Email, existingUser.MobilePhone, existingUser.ProfitCentreCode, existingUser.EmployeeNo);
            }
            else
            {
                // Generate username: default to FullName, fallback to staff_PersonId if name exists
                var username = staffDto.FullName;
                
                // Check if FullName username already exists
                var usernameExists = await _context.Users
                    .AnyAsync(u => u.Username == username);
                
                if (usernameExists)
                {
                    // If FullName exists, use staff_PersonId pattern
                    username = $"staff_{staffDto.PersonId}";
                    
                    // Double check this doesn't exist either
                    var fallbackExists = await _context.Users
                        .AnyAsync(u => u.Username == username);
                        
                    if (fallbackExists)
                    {
                        // Last resort: append timestamp
                        username = $"staff_{staffDto.PersonId}_{DateTime.UtcNow:yyyyMMddHHmmss}";
                    }
                }

                // Create new user with username defaulting to FullName
                var newUser = new User
                {
                    Username = username, // Default to FullName, fallback to staff_PersonId
                    FullName = staffDto.FullName,
                    Email = staffDto.EmailAddress,
                    PersonId = staffDto.PersonId,
                    FinCoCode = string.Empty, // Will be set during authentication
                    MobilePhone = staffDto.MobilePhone,
                    ProfitCentreCode = staffDto.ProfitCentreCode,
                    EmployeeNo = staffDto.EmployeeNo,
                    IsAuthenticated = false,
                    CreatedDate = DateTime.UtcNow,
                    UpdatedDate = DateTime.UtcNow
                };

                _context.Users.Add(newUser);
                _logger.LogInformation("Created new user: {FullName} (ID: {PersonId}) with username: {Username}",
                    staffDto.FullName, staffDto.PersonId, username);
                
                // Log the new user values for debugging
                _logger.LogInformation("New user values: Email='{Email}', Mobile='{Mobile}', ProfitCentre='{ProfitCentre}', EmployeeNo='{EmployeeNo}'",
                    newUser.Email, newUser.MobilePhone, newUser.ProfitCentreCode, newUser.EmployeeNo);
            }

            await _context.SaveChangesAsync();
            _logger.LogInformation("Database save completed for PersonId: {PersonId}", staffDto.PersonId);
        }
    }
} 