using WaterMeterManagement.DTOs;
using WaterMeterManagement.Services.Interfaces;

namespace WaterMeterManagement.Services
{
    /// <summary>
    /// Dynamic data service implementation
    /// </summary>
    public class DynamicDataService : IDynamicDataService
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly ILogger<DynamicDataService> _logger;
        private readonly Dictionary<string, Type> _strategies;

        public DynamicDataService(IServiceProvider serviceProvider, ILogger<DynamicDataService> logger)
        {
            _serviceProvider = serviceProvider;
            _logger = logger;
            
            // Register available strategies
            _strategies = new Dictionary<string, Type>
            {
                { "database", typeof(Strategies.DatabaseStrategy) },
                { "enum", typeof(Strategies.EnumStrategy) }
                // Add more strategies here as needed
            };
        }

        public async Task<DynamicSelectResponseDto> GetOptionsAsync(string strategy, string dataSource, Dictionary<string, object> parameters)
        {
            try
            {
                _logger.LogInformation("Getting options for strategy: {Strategy}, dataSource: {DataSource}, params: {@Parameters}", 
                    strategy, dataSource, parameters);

                // Validate strategy
                if (!_strategies.ContainsKey(strategy.ToLower()))
                {
                    return new DynamicSelectResponseDto
                    {
                        Success = false,
                        ErrorMessage = $"Unknown strategy: {strategy}. Available strategies: {string.Join(", ", _strategies.Keys)}"
                    };
                }

                // Get strategy instance
                var strategyType = _strategies[strategy.ToLower()];
                var strategyInstance = _serviceProvider.GetService(strategyType) as IDataSourceStrategy;
                
                if (strategyInstance == null)
                {
                    return new DynamicSelectResponseDto
                    {
                        Success = false,
                        ErrorMessage = $"Failed to create strategy instance for: {strategy}"
                    };
                }

                // Get options from strategy
                var options = await strategyInstance.GetOptionsAsync(dataSource, parameters);

                return new DynamicSelectResponseDto
                {
                    Options = options,
                    TotalCount = options.Count,
                    Success = true,
                    Metadata = new Dictionary<string, object>
                    {
                        { "strategy", strategy },
                        { "dataSource", dataSource },
                        { "parameters", parameters }
                    }
                };
            }
            catch (ArgumentException ex)
            {
                _logger.LogWarning(ex, "Invalid argument for strategy: {Strategy}, dataSource: {DataSource}", strategy, dataSource);
                return new DynamicSelectResponseDto
                {
                    Success = false,
                    ErrorMessage = ex.Message
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting options for strategy: {Strategy}, dataSource: {DataSource}", strategy, dataSource);
                return new DynamicSelectResponseDto
                {
                    Success = false,
                    ErrorMessage = "An error occurred while retrieving options"
                };
            }
        }

        public async Task<List<string>> GetAvailableStrategiesAsync()
        {
            return await Task.FromResult(_strategies.Keys.ToList());
        }

        public async Task<List<string>> GetAvailableDataSourcesAsync(string strategy)
        {
            try
            {
                if (!_strategies.ContainsKey(strategy.ToLower()))
                {
                    return new List<string>();
                }

                var strategyType = _strategies[strategy.ToLower()];
                var strategyInstance = _serviceProvider.GetService(strategyType) as IDataSourceStrategy;
                
                if (strategyInstance == null)
                {
                    return new List<string>();
                }

                return await strategyInstance.GetAvailableDataSourcesAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting available data sources for strategy: {Strategy}", strategy);
                return new List<string>();
            }
        }
    }
}
