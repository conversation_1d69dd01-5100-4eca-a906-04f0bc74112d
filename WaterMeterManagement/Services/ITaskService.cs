using WaterMeterManagement.DTOs;
using WaterMeterManagement.DTOs.Mobile;
using WaterMeterManagement.Models;

namespace WaterMeterManagement.Services
{
    public interface ITaskService
    {
        System.Threading.Tasks.Task<TaskSearchResultDto> GetTasksAsync(TaskSearchDto searchDto);
        System.Threading.Tasks.Task<WorkPackageGroupedTasksDto> GetTasksGroupedByWorkPackageAsync(TaskWorkPackageSearchDto searchDto);
        System.Threading.Tasks.Task<TaskListDto?> GetTaskByIdAsync(int id);
        System.Threading.Tasks.Task<TaskListDto> CreateTaskAsync(CreateTaskDto createDto, string createdBy);
        System.Threading.Tasks.Task<TaskListDto> UpdateTaskAsync(int id, UpdateTaskDto updateDto, string updatedBy);
        System.Threading.Tasks.Task<bool> DeleteTaskAsync(int id, string deletedBy);
        System.Threading.Tasks.Task<bool> AssignTaskAsync(int taskId, int userId, string assignedBy);
        System.Threading.Tasks.Task<bool> BulkAssignTasksAsync(BulkAssignmentDto bulkAssignmentDto, string assignedBy);
        System.Threading.Tasks.Task<List<TaskListDto>> ReactiveAssignmentAsync(ReactiveAssignmentDto reactiveDto, string assignedBy);
        System.Threading.Tasks.Task<bool> UpdateTaskStatusAsync(int taskId, string status, string updatedBy);
        System.Threading.Tasks.Task<bool> UpdateTaskProgressAsync(int taskId, int progressPercentage, string updatedBy);
        System.Threading.Tasks.Task<List<TaskHistory>> GetTaskHistoryAsync(int taskId);
        System.Threading.Tasks.Task<byte[]> ExportTasksAsync(TaskSearchDto? searchDto = null);
        System.Threading.Tasks.Task<(bool Success, string Message, int ImportedCount)> ImportTasksAsync(Stream fileStream, string importedBy);
        System.Threading.Tasks.Task<TaskStatisticsDto> GetTaskStatisticsAsync();
        System.Threading.Tasks.Task<List<OverdueTaskDto>> GetOverdueTasksAsync();
        System.Threading.Tasks.Task<List<TaskPerformanceMetricsDto>> GetTaskPerformanceMetricsAsync();

        // Enhanced Task Assignment Methods
        System.Threading.Tasks.Task<AssignmentResultDto> CreateTaskAssignmentAsync(CreateTaskAssignmentDto assignmentDto, string assignedBy);
        System.Threading.Tasks.Task<List<TaskAssignmentDto>> GetTaskAssignmentsAsync(int? taskId = null, int? userId = null);
        System.Threading.Tasks.Task<TaskAssignmentValidationDto> ValidateTaskAssignmentAsync(int taskId, int userId);
        System.Threading.Tasks.Task<List<UserWorkloadSummaryDto>> GetUserWorkloadSummaryAsync();
        System.Threading.Tasks.Task<UserWorkloadSummaryDto?> GetUserWorkloadAsync(int userId);
        System.Threading.Tasks.Task<bool> AcceptTaskAssignmentAsync(int assignmentId, string acceptedBy);
        System.Threading.Tasks.Task<bool> RejectTaskAssignmentAsync(int assignmentId, string rejectedBy, string reason);
        System.Threading.Tasks.Task<AssignmentResultDto> BulkAssignTasksEnhancedAsync(List<int> taskIds, int userId, string assignmentType, string assignedBy, string? reason = null);

        // Mobile-specific Task Methods
        System.Threading.Tasks.Task<List<MobileTaskDto>> GetUserMobileTasksAsync(int userId, string? status = null, bool includeCompleted = false, int page = 1, int pageSize = 50);
        System.Threading.Tasks.Task<MobileTaskDetailDto?> GetMobileTaskDetailAsync(int taskId, int userId);
        System.Threading.Tasks.Task<ServiceResult> StartMobileTaskAsync(int taskId, int userId, StartTaskRequest request);
        System.Threading.Tasks.Task<ServiceResult> UpdateMobileTaskStatusAsync(int taskId, int userId, UpdateTaskStatusRequest request);
        System.Threading.Tasks.Task<ServiceResult<int>> CompleteMobileTaskAsync(int taskId, int userId, CompleteTaskRequest request);
        System.Threading.Tasks.Task<List<MobileTaskDto>> GetNearbyMobileTasksAsync(int userId, double latitude, double longitude, double radiusKm);
        System.Threading.Tasks.Task<MobileTaskSummaryDto> GetMobileTaskSummaryAsync(int userId);
        System.Threading.Tasks.Task<TaskSyncResponseDto> SyncMobileTaskStatusAsync(int userId, TaskStatusSyncRequest request);
    }
} 