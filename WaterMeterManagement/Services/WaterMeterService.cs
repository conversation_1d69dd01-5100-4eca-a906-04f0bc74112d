using Microsoft.EntityFrameworkCore;
using System.Globalization;
using WaterMeterManagement.Data;
using WaterMeterManagement.DTOs;
using WaterMeterManagement.Models;
using WaterMeterManagement.Services.Interfaces;
using CsvHelper;
using OfficeOpenXml;
using AutoMapper;

namespace WaterMeterManagement.Services
{
    public class WaterMeterService : IWaterMeterService
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<WaterMeterService> _logger;
        private readonly IWorkPackageService _workPackageService;
        private readonly IMapper _mapper;

        public WaterMeterService(ApplicationDbContext context, ILogger<WaterMeterService> logger, IWorkPackageService workPackageService, IMapper mapper)
        {
            _context = context;
            _logger = logger;
            _workPackageService = workPackageService;
            _mapper = mapper;
        }

        public async Task<(List<WaterMeterListDto> Meters, int TotalCount)> GetWaterMetersAsync(WaterMeterSearchDto searchDto)
        {
            var query = _context.WaterMeters.AsQueryable();

            // Apply work package assignment filter first
            if (!string.IsNullOrEmpty(searchDto.WorkPackageAssignment))
            {
                switch (searchDto.WorkPackageAssignment.ToLower())
                {
                    case "unassigned":
                        // Only meters not assigned to any work package (方案1: 简单版本)
                        query = query.Where(m => !_context.WorkPackageItems
                            .Any(wpi => wpi.MeterId == m.Id && !wpi.IsDeleted));
                        break;
                    case "assigned":
                        // Only meters assigned to work packages
                        query = query.Where(m => _context.WorkPackageItems
                            .Any(wpi => wpi.MeterId == m.Id && !wpi.IsDeleted));
                        break;
                    case "all":
                    default:
                        // No filter - show all meters
                        break;
                }
            }

            // Apply other filters
            if (!string.IsNullOrEmpty(searchDto.SerialNumber))
            {
                query = query.Where(m => m.SerialNumber.Contains(searchDto.SerialNumber));
            }

            if (!string.IsNullOrEmpty(searchDto.Location))
            {
                query = query.Where(m => m.Location.Contains(searchDto.Location) ||
                                        (!string.IsNullOrEmpty(m.Address) && m.Address.Contains(searchDto.Location)) ||
                                        (!string.IsNullOrEmpty(m.RoadName) && m.RoadName.Contains(searchDto.Location)));
            }

            if (!string.IsNullOrEmpty(searchDto.MeterType))
            {
                query = query.Where(m => m.MeterType == searchDto.MeterType);
            }

            if (!string.IsNullOrEmpty(searchDto.Status))
            {
                query = query.Where(m => m.Status == searchDto.Status);
            }

            if (!string.IsNullOrEmpty(searchDto.CustomerName))
            {
                query = query.Where(m => !string.IsNullOrEmpty(m.CustomerName) && m.CustomerName.Contains(searchDto.CustomerName));
            }

            if (searchDto.InstallDateFrom.HasValue)
            {
                query = query.Where(m => m.InstallDate >= searchDto.InstallDateFrom.Value);
            }

            if (searchDto.InstallDateTo.HasValue)
            {
                query = query.Where(m => m.InstallDate <= searchDto.InstallDateTo.Value);
            }

            var totalCount = await query.CountAsync();

            // Apply sorting
            if (!string.IsNullOrEmpty(searchDto.SortBy))
            {
                switch (searchDto.SortBy.ToLower())
                {
                    case "serialnumber":
                        query = searchDto.SortDirection?.ToLower() == "desc" 
                            ? query.OrderByDescending(m => m.SerialNumber)
                            : query.OrderBy(m => m.SerialNumber);
                        break;
                    case "location":
                        query = searchDto.SortDirection?.ToLower() == "desc"
                            ? query.OrderByDescending(m => m.Location)
                            : query.OrderBy(m => m.Location);
                        break;
                    case "installdate":
                        query = searchDto.SortDirection?.ToLower() == "desc"
                            ? query.OrderByDescending(m => m.InstallDate)
                            : query.OrderBy(m => m.InstallDate);
                        break;
                    default:
                        query = query.OrderBy(m => m.SerialNumber);
                        break;
                }
            }
            else
            {
                query = query.OrderBy(m => m.SerialNumber);
            }

            // Apply pagination using base class properties
            var meters = await query
                .Skip(searchDto.Skip)
                .Take(searchDto.Take)
                .Select(m => new WaterMeterListDto
                {
                    Id = m.Id,
                    SerialNumber = m.SerialNumber,
                    Location = m.Location,
                    MeterType = m.MeterType,
                    Status = m.Status,
                    LastReadingDate = m.DateOfRead,
                    LastRead = m.LastRead,
                    CustomerName = m.CustomerName,
                    BatteryLevel = m.BatteryLevel,
                    InstallDate = m.InstallDate,
                    // GPS Coordinates
                    Latitude = m.Latitude,
                    Longitude = m.Longitude,
                    // AMS Integration fields
                    AssetId = m.AssetId,
                    MeterNumber = m.SerialNumber,
                    AccountNumber = m.AccountNumber,
                    Township = m.Township,
                    RoadName = m.RoadName,
                    SyncStatus = m.SyncStatus,
                    LastSyncDate = m.LastSyncDate,
                    DateOfRead = m.DateOfRead,
                    CantRead = m.CantRead,
                    Condition = m.Condition
                })
                .ToListAsync();

            return (meters, totalCount);
        }

        public async Task<WaterMeterDto?> GetWaterMeterByIdAsync(int id)
        {
            var meter = await _context.WaterMeters
                .FirstOrDefaultAsync(m => m.Id == id);

            if (meter == null) return null;

            return MapToDto(meter);
        }

        public async Task<WaterMeterDto?> GetWaterMeterBySerialNumberAsync(string serialNumber)
        {
            var meter = await _context.WaterMeters
                .FirstOrDefaultAsync(m => m.SerialNumber == serialNumber);

            if (meter == null) return null;

            return MapToDto(meter);
        }

        public async Task<WaterMeterDto> CreateWaterMeterAsync(CreateWaterMeterDto createDto)
        {
            // Check if serial number already exists
            var existingMeter = await _context.WaterMeters
                .FirstOrDefaultAsync(m => m.SerialNumber == createDto.SerialNumber);

            if (existingMeter != null)
            {
                throw new InvalidOperationException($"Water meter with serial number '{createDto.SerialNumber}' already exists");
            }

            var meter = new WaterMeter
            {
                SerialNumber = createDto.SerialNumber,
                Location = createDto.Location,
                Address = createDto.Address,
                MeterType = createDto.MeterType,
                Status = createDto.Status,
                InstallDate = createDto.InstallDate,
                CustomerCode = createDto.CustomerCode,
                CustomerName = createDto.CustomerName,
                Latitude = createDto.Latitude,
                Longitude = createDto.Longitude,
                Brand = createDto.Brand,
                Model = createDto.Model,
                CommunicationMethod = createDto.CommunicationMethod,
                Notes = createDto.Notes,
                // AMS Integration fields
                AccountNumber = createDto.AccountNumber,
                AssetId = createDto.AssetId,
                // Structured address fields
                RoadNumber = createDto.RoadNumber,
                RoadName = createDto.RoadName,
                Township = createDto.Township,
                SubArea = createDto.SubArea,
                CreatedAt = DateTime.UtcNow,
                CreatedBy = "System",
                UpdatedAt = DateTime.UtcNow,
                UpdatedBy = "System"
            };

            _context.WaterMeters.Add(meter);
            await _context.SaveChangesAsync();

            return MapToDto(meter);
        }



        public async Task<WaterMeterDto?> UpdateWaterMeterAsync(int id, UpdateWaterMeterDto updateDto)
        {
            var meter = await _context.WaterMeters.FirstOrDefaultAsync(m => m.Id == id);
            if (meter == null) return null;

            // 使用 AutoMapper 映射，自动忽略 null 值
            _mapper.Map(updateDto, meter);

            // Always update metadata
            meter.UpdatedAt = DateTime.UtcNow;
            meter.UpdatedBy = "System";

            await _context.SaveChangesAsync();
            return MapToDto(meter);
        }

        public async Task<bool> DeleteWaterMeterAsync(int id)
        {
            var meter = await _context.WaterMeters.FirstOrDefaultAsync(m => m.Id == id);
            if (meter == null) return false;

            _context.WaterMeters.Remove(meter);
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<WaterMeterImportResultDto> ImportWaterMetersAsync(List<WaterMeterImportDto> importData, string fileName)
        {
            var result = new WaterMeterImportResultDto
            {
                TotalRows = importData.Count
            };

            var successfulMeters = new List<WaterMeter>();

            foreach (var importDto in importData)
            {
                try
                {
                    // Check if serial number already exists
                    var existingMeter = await _context.WaterMeters
                        .FirstOrDefaultAsync(m => m.SerialNumber == importDto.SerialNumber);

                    if (existingMeter != null)
                    {
                        importDto.ValidationErrors.Add($"Serial number '{importDto.SerialNumber}' already exists");
                        result.FailedRecords.Add(importDto);
                        result.FailedRows++;
                        continue;
                    }

                    var meter = new WaterMeter
                    {
                        SerialNumber = importDto.SerialNumber,
                        Location = importDto.Location,
                        Address = importDto.Address,
                        MeterType = importDto.MeterType,
                        Status = importDto.Status,
                        InstallDate = importDto.InstallDate,
                        CustomerCode = importDto.CustomerCode,
                        CustomerName = importDto.CustomerName,
                        Latitude = importDto.Latitude,
                        Longitude = importDto.Longitude,
                        Brand = importDto.Brand,
                        Model = importDto.Model,
                        CommunicationMethod = importDto.CommunicationMethod,
                        Notes = importDto.Notes,
                        CreatedAt = DateTime.UtcNow,
                        CreatedBy = "Import",
                        UpdatedAt = DateTime.UtcNow,
                        UpdatedBy = "Import"
                    };

                    successfulMeters.Add(meter);
                    result.SuccessfulRows++;
                }
                catch (Exception ex)
                {
                    importDto.ValidationErrors.Add($"Error processing row: {ex.Message}");
                    result.FailedRecords.Add(importDto);
                    result.FailedRows++;
                }
            }

            if (successfulMeters.Any())
            {
                _context.WaterMeters.AddRange(successfulMeters);
                await _context.SaveChangesAsync();
            }

            return result;
        }

        public async Task<WaterMeterImportResultDto> ImportFromCsvAsync(Stream csvStream, string fileName)
        {
            var importData = new List<WaterMeterImportDto>();

            using (var reader = new StreamReader(csvStream))
            using (var csv = new CsvReader(reader, CultureInfo.InvariantCulture))
            {
                var records = csv.GetRecords<WaterMeterImportDto>().ToList();
                for (int i = 0; i < records.Count; i++)
                {
                    records[i].RowNumber = i + 2; // +2 because CSV header is row 1
                    importData.Add(records[i]);
                }
            }

            var validatedData = await ValidateImportDataAsync(importData);
            return await ImportWaterMetersAsync(validatedData, fileName);
        }

        public async Task<WaterMeterImportResultDto> ImportFromExcelAsync(Stream excelStream, string fileName)
        {
            var importData = new List<WaterMeterImportDto>();

            using (var package = new ExcelPackage(excelStream))
            {
                var worksheet = package.Workbook.Worksheets[0];
                var rowCount = worksheet.Dimension.Rows;

                for (int row = 2; row <= rowCount; row++) // Skip header row
                {
                    var importDto = new WaterMeterImportDto
                    {
                        RowNumber = row,
                        SerialNumber = worksheet.Cells[row, 1].Value?.ToString() ?? "",
                        Location = worksheet.Cells[row, 2].Value?.ToString() ?? "",
                        Address = worksheet.Cells[row, 3].Value?.ToString() ?? "",
                        MeterType = worksheet.Cells[row, 4].Value?.ToString() ?? "",
                        Status = worksheet.Cells[row, 5].Value?.ToString() ?? "Active",
                        CustomerCode = worksheet.Cells[row, 6].Value?.ToString(),
                        CustomerName = worksheet.Cells[row, 7].Value?.ToString(),
                        Brand = worksheet.Cells[row, 8].Value?.ToString(),
                        Model = worksheet.Cells[row, 9].Value?.ToString(),
                        CommunicationMethod = worksheet.Cells[row, 10].Value?.ToString(),
                        Notes = worksheet.Cells[row, 11].Value?.ToString()
                    };

                    // Parse InstallDate
                    if (worksheet.Cells[row, 12].Value != null)
                    {
                        if (DateTime.TryParse(worksheet.Cells[row, 12].Value.ToString(), out var installDate))
                        {
                            importDto.InstallDate = installDate;
                        }
                    }

                    // Parse coordinates
                    if (decimal.TryParse(worksheet.Cells[row, 13].Value?.ToString(), out var lat))
                        importDto.Latitude = lat;

                    if (decimal.TryParse(worksheet.Cells[row, 14].Value?.ToString(), out var lng))
                        importDto.Longitude = lng;

                    importData.Add(importDto);
                }
            }

            var validatedData = await ValidateImportDataAsync(importData);
            return await ImportWaterMetersAsync(validatedData, fileName);
        }

        public async Task<List<WaterMeterImportDto>> ValidateImportDataAsync(List<WaterMeterImportDto> importData)
        {
            foreach (var item in importData)
            {
                item.ValidationErrors.Clear();

                // Required field validations
                if (string.IsNullOrWhiteSpace(item.SerialNumber))
                    item.ValidationErrors.Add("Serial Number is required");

                if (string.IsNullOrWhiteSpace(item.Location))
                    item.ValidationErrors.Add("Location is required");

                if (string.IsNullOrWhiteSpace(item.MeterType))
                    item.ValidationErrors.Add("Meter Type is required");

                // Business rule validations
                if (!string.IsNullOrEmpty(item.SerialNumber))
                {
                    var exists = await _context.WaterMeters
                        .AnyAsync(m => m.SerialNumber == item.SerialNumber);
                    
                    if (exists)
                        item.ValidationErrors.Add($"Serial Number '{item.SerialNumber}' already exists");
                }

                // Validate meter type
                var validMeterTypes = new[] { "Residential", "Commercial", "Industrial" };
                if (!string.IsNullOrEmpty(item.MeterType) && !validMeterTypes.Contains(item.MeterType))
                {
                    item.ValidationErrors.Add($"Invalid Meter Type. Valid types: {string.Join(", ", validMeterTypes)}");
                }

                // Validate status
                var validStatuses = new[] { "Active", "Inactive", "Maintenance", "Replaced" };
                if (!string.IsNullOrEmpty(item.Status) && !validStatuses.Contains(item.Status))
                {
                    item.ValidationErrors.Add($"Invalid Status. Valid statuses: {string.Join(", ", validStatuses)}");
                }
            }

            return importData;
        }

        public async Task<bool> ValidateSerialNumberAsync(string serialNumber, int? excludeId = null)
        {
            var query = _context.WaterMeters.Where(m => m.SerialNumber == serialNumber);
            
            if (excludeId.HasValue)
            {
                query = query.Where(m => m.Id != excludeId.Value);
            }

            return !await query.AnyAsync();
        }

        public async Task UpdateMeterReadingAsync(int meterId, decimal readingValue, DateTime readingDate, string dataSource = "Manual")
        {
            var meter = await _context.WaterMeters.FirstOrDefaultAsync(m => m.Id == meterId);
            if (meter == null)
                throw new ArgumentException("Water meter not found", nameof(meterId));

            // Create new reading record
            var reading = new MeterReading
            {
                MeterId = meterId,
                UserId = 1, // System user
                TaskId = 1, // TODO: 需要创建系统任务
                ReadingDate = readingDate,
                ReadingValue = readingValue,
                ReadingMethod = "Manual",
                ReadingType = "Regular",
                Status = "Completed",
                DataSource = dataSource,
                CreatedAt = DateTime.UtcNow,
                CreatedBy = "System",
                UpdatedAt = DateTime.UtcNow,
                UpdatedBy = "System"
            };

            // 注意：不再在MeterReading中存储consumption，而是通过计算获得

            _context.MeterReadings.Add(reading);

                            // Update meter's last reading
                var previousReading = meter.LastRead;
                meter.LastRead = readingValue;
                meter.DateOfRead = readingDate;
            meter.UpdatedAt = DateTime.UtcNow;
            meter.UpdatedBy = "System";

            await _context.SaveChangesAsync();
        }

        public async Task<List<WaterMeterListDto>> GetMetersByLocationAsync(string location)
        {
            return await _context.WaterMeters
                .Where(m => m.Location.Contains(location))
                .Select(m => new WaterMeterListDto
                {
                    Id = m.Id,
                    SerialNumber = m.SerialNumber,
                    Location = m.Location,
                    MeterType = m.MeterType,
                    Status = m.Status,
                    LastReadingDate = m.DateOfRead,
                    LastRead = m.LastRead,
                    CustomerName = m.CustomerName,
                    BatteryLevel = m.BatteryLevel
                })
                .ToListAsync();
        }

        public async Task<List<WaterMeterListDto>> GetMetersByStatusAsync(string status)
        {
            return await _context.WaterMeters
                .Where(m => m.Status == status)
                .Select(m => new WaterMeterListDto
                {
                    Id = m.Id,
                    SerialNumber = m.SerialNumber,
                    Location = m.Location,
                    MeterType = m.MeterType,
                    Status = m.Status,
                    LastReadingDate = m.DateOfRead,
                    LastRead = m.LastRead,
                    CustomerName = m.CustomerName,
                    BatteryLevel = m.BatteryLevel
                })
                .ToListAsync();
        }

        public async Task<List<WaterMeterListDto>> GetMetersDueForMaintenanceAsync()
        {
            var today = DateTime.UtcNow.Date;
            return await _context.WaterMeters
                .Where(m => m.NextMaintenanceDate.HasValue && m.NextMaintenanceDate.Value <= today)
                .Select(m => new WaterMeterListDto
                {
                    Id = m.Id,
                    SerialNumber = m.SerialNumber,
                    Location = m.Location,
                    MeterType = m.MeterType,
                    Status = m.Status,
                    LastReadingDate = m.DateOfRead,
                    LastRead = m.LastRead,
                    CustomerName = m.CustomerName,
                    BatteryLevel = m.BatteryLevel
                })
                .ToListAsync();
        }

        public async Task<List<WaterMeterListDto>> GetMetersWithLowBatteryAsync(int? threshold = 20)
        {
            return await _context.WaterMeters
                .Where(m => m.BatteryLevel.HasValue && m.BatteryLevel.Value <= (threshold ?? 20))
                .Select(m => new WaterMeterListDto
                {
                    Id = m.Id,
                    SerialNumber = m.SerialNumber,
                    Location = m.Location,
                    MeterType = m.MeterType,
                    Status = m.Status,
                    LastReadingDate = m.DateOfRead,
                    LastRead = m.LastRead,
                    CustomerName = m.CustomerName,
                    BatteryLevel = m.BatteryLevel
                })
                .ToListAsync();
        }



        // AMS Integration methods
        public async Task<WaterMeterDto?> GetWaterMeterByAssetIdAsync(string assetId)
        {
            var meter = await _context.WaterMeters
                .FirstOrDefaultAsync(m => m.AssetId == assetId);

            return meter != null ? MapToDto(meter) : null;
        }

        public async Task<WaterMeterDto?> GetWaterMeterByAccountNumberAsync(string accountNumber)
        {
            var meter = await _context.WaterMeters
                .FirstOrDefaultAsync(m => m.AccountNumber == accountNumber);

            return meter != null ? MapToDto(meter) : null;
        }

        public async Task<List<WaterMeterListDto>> GetMetersByTownshipAsync(string township, int page = 1, int pageSize = 50)
        {
            return await _context.WaterMeters
                .Where(m => m.Township == township)
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .Select(m => new WaterMeterListDto
                {
                    Id = m.Id,
                    SerialNumber = m.SerialNumber,
                    Location = m.Location,
                    MeterType = m.MeterType,
                    Status = m.Status,
                    LastReadingDate = m.DateOfRead,
                    LastRead = m.LastRead,
                    CustomerName = m.CustomerName,
                    AssetId = m.AssetId,
                    AccountNumber = m.AccountNumber,
                    RoadName = m.RoadName,
                    Township = m.Township
                })
                .ToListAsync();
        }

        public async Task<List<WaterMeterListDto>> GetMetersByRoadAsync(string roadName, int page = 1, int pageSize = 50)
        {
            return await _context.WaterMeters
                .Where(m => m.RoadName == roadName)
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .Select(m => new WaterMeterListDto
                {
                    Id = m.Id,
                    SerialNumber = m.SerialNumber,
                    Location = m.Location,
                    MeterType = m.MeterType,
                    Status = m.Status,
                    LastReadingDate = m.DateOfRead,
                    LastRead = m.LastRead,
                    CustomerName = m.CustomerName,
                    AssetId = m.AssetId,
                    AccountNumber = m.AccountNumber,
                    RoadName = m.RoadName,
                    Township = m.Township
                })
                .ToListAsync();
        }

        public async Task<List<WaterMeterListDto>> GetMetersByRouteAsync(string routeName, int page = 1, int pageSize = 50)
        {
            return await _context.WaterMeters
                .Where(m => m.AssignedRoute == routeName)
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .Select(m => new WaterMeterListDto
                {
                    Id = m.Id,
                    SerialNumber = m.SerialNumber,
                    Location = m.Location,
                    MeterType = m.MeterType,
                    Status = m.Status,
                    LastReadingDate = m.DateOfRead,
                    LastRead = m.LastRead,
                    CustomerName = m.CustomerName,
                    AssetId = m.AssetId,
                    AccountNumber = m.AccountNumber,
                    RoadName = m.RoadName,
                    Township = m.Township
                })
                .ToListAsync();
        }

        public async Task<List<WaterMeterListDto>> GetCantReadMetersAsync(int page = 1, int pageSize = 50)
        {
            return await _context.WaterMeters
                .Where(m => m.CantRead || !string.IsNullOrEmpty(m.Condition))
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .Select(m => new WaterMeterListDto
                {
                    Id = m.Id,
                    SerialNumber = m.SerialNumber,
                    Location = m.Location,
                    MeterType = m.MeterType,
                    Status = m.Status,
                    LastReadingDate = m.DateOfRead,
                    LastRead = m.LastRead,
                    CustomerName = m.CustomerName,
                    AssetId = m.AssetId,
                    AccountNumber = m.AccountNumber,
                    RoadName = m.RoadName,
                    Township = m.Township
                })
                .ToListAsync();
        }

        public async Task<bool> SyncMeterWithAmsAsync(int meterId)
        {
            try
            {
                var meter = await _context.WaterMeters.FindAsync(meterId);
                if (meter == null) return false;

                // Update sync status
                meter.SyncStatus = "Synced";
                meter.LastSyncDate = DateTime.UtcNow;

                await _context.SaveChangesAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> BulkSyncWithAmsAsync(List<int> meterIds)
        {
            try
            {
                var meters = await _context.WaterMeters
                    .Where(m => meterIds.Contains(m.Id))
                    .ToListAsync();

                foreach (var meter in meters)
                {
                    meter.SyncStatus = "Synced";
                    meter.LastSyncDate = DateTime.UtcNow;
                }

                await _context.SaveChangesAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<List<WaterMeterListDto>> GetAmsAnomaliesAsync(int page = 1, int pageSize = 50)
        {
            return await _context.WaterMeters
                .Where(m => m.CantRead || !string.IsNullOrEmpty(m.Condition) || m.SyncStatus == "Error")
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .Select(m => new WaterMeterListDto
                {
                    Id = m.Id,
                    SerialNumber = m.SerialNumber,
                    Location = m.Location,
                    MeterType = m.MeterType,
                    Status = m.Status,
                    LastReadingDate = m.DateOfRead,
                    LastRead = m.LastRead,
                    CustomerName = m.CustomerName,
                    AssetId = m.AssetId,
                    AccountNumber = m.AccountNumber,
                    RoadName = m.RoadName,
                    Township = m.Township
                })
                .ToListAsync();
        }

        public async Task<object> GetConsumptionAnalysisAsync(string assetId, DateTime startDate, DateTime endDate)
        {
            var meter = await _context.WaterMeters
                .FirstOrDefaultAsync(m => m.AssetId == assetId);

            if (meter == null) return new { error = "Meter not found" };

            // Basic consumption analysis
            return new
            {
                AssetId = assetId,
                Period = new { StartDate = startDate, EndDate = endDate },
                CurrentReading = meter.LastRead,
                PreviousReading = (meter.LastRead ?? 0) - (meter.RecentChange ?? 0),
                Consumption = meter.RecentChange,
                LastReadDate = meter.DateOfRead,
                Status = meter.Status,
                IsAnomalous = meter.CantRead || !string.IsNullOrEmpty(meter.Condition)
            };
        }

        public async Task<bool> FlagMeterAnomalyAsync(int meterId, AnomalyFlagDto flagDto)
        {
            try
            {
                var meter = await _context.WaterMeters.FindAsync(meterId);
                if (meter == null) return false;

                meter.Condition = flagDto.AnomalyReason;
                meter.CantRead = true;
                meter.SyncStatus = "Error";

                await _context.SaveChangesAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<byte[]> GenerateWorkPackageTemplateAsync(List<string> meterIds)
        {
            try
            {
                // Get meters by serial numbers
                var meters = await _context.WaterMeters
                    .Where(m => meterIds.Contains(m.SerialNumber))
                    .OrderBy(m => m.SerialNumber)
                    .ToListAsync();

                using var package = new ExcelPackage();
                var worksheet = package.Workbook.Worksheets.Add("Water Meter Template");

                // Define headers
                var headers = new[]
                {
                    "Work Package Name", // Empty column for user to fill
                    "Serial Number",
                    "Location",
                    "Address",
                    "Meter Type",
                    "Status",
                    "Install Date",
                    "Customer Name",
                    "Township",
                    "Road Name",
                    "Account Number",
                    "Asset ID",
                    "Last Read",
                    "Date of Read",
                    "Latitude",
                    "Longitude",
                    "Notes"
                };

                // Add headers
                for (int i = 0; i < headers.Length; i++)
                {
                    worksheet.Cells[1, i + 1].Value = headers[i];
                    worksheet.Cells[1, i + 1].Style.Font.Bold = true;
                    worksheet.Cells[1, i + 1].Style.Fill.PatternType = OfficeOpenXml.Style.ExcelFillStyle.Solid;
                    worksheet.Cells[1, i + 1].Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.LightBlue);
                }

                // Make Work Package Name column header stand out
                worksheet.Cells[1, 1].Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.Yellow);
                worksheet.Cells[1, 1].Style.Font.Color.SetColor(System.Drawing.Color.Red);

                // Add data rows
                for (int row = 0; row < meters.Count; row++)
                {
                    var meter = meters[row];
                    var excelRow = row + 2; // Start from row 2 (after header)

                    worksheet.Cells[excelRow, 1].Value = ""; // Work Package Name - empty for user to fill
                    worksheet.Cells[excelRow, 2].Value = meter.SerialNumber;
                    worksheet.Cells[excelRow, 3].Value = meter.Location;
                    worksheet.Cells[excelRow, 4].Value = meter.Address;
                    worksheet.Cells[excelRow, 5].Value = meter.MeterType;
                    worksheet.Cells[excelRow, 6].Value = meter.Status;
                    worksheet.Cells[excelRow, 7].Value = meter.InstallDate?.ToString("yyyy-MM-dd");
                    worksheet.Cells[excelRow, 8].Value = meter.CustomerName;
                    worksheet.Cells[excelRow, 9].Value = meter.Township;
                    worksheet.Cells[excelRow, 10].Value = meter.RoadName;
                    worksheet.Cells[excelRow, 11].Value = meter.AccountNumber;
                    worksheet.Cells[excelRow, 12].Value = meter.AssetId;
                    worksheet.Cells[excelRow, 13].Value = meter.LastRead;
                    worksheet.Cells[excelRow, 14].Value = meter.DateOfRead?.ToString("yyyy-MM-dd");
                    worksheet.Cells[excelRow, 15].Value = meter.Latitude;
                    worksheet.Cells[excelRow, 16].Value = meter.Longitude;
                    worksheet.Cells[excelRow, 17].Value = meter.Notes;
                }

                // Auto-fit columns
                worksheet.Cells.AutoFitColumns();

                // Make Work Package Name column wider for better visibility
                worksheet.Column(1).Width = 25;

                // Add instruction comment to the Work Package Name header
                worksheet.Cells[1, 1].AddComment("Fill in the Work Package Name for each meter. Meters with the same Work Package Name will be grouped together.", "System");

                // Add instruction note for Work Package Name column
                // Data validation will be added in future version

                return package.GetAsByteArray();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating work package template");
                throw;
            }
        }

        public async Task<WorkPackageImportResultDto> ImportWorkPackagesFromExcelAsync(Stream excelStream, string fileName)
        {
            var result = new WorkPackageImportResultDto
            {
                Success = false,
                Message = "Import failed",
                Errors = new List<string>(),
                ImportedWorkPackages = new List<WorkPackageListDto>()
            };

            try
            {
                using var package = new ExcelPackage(excelStream);
                var worksheet = package.Workbook.Worksheets[0];

                if (worksheet == null)
                {
                    result.Errors.Add("No worksheet found in Excel file");
                    return result;
                }

                var rowCount = worksheet.Dimension?.Rows ?? 0;
                if (rowCount <= 1)
                {
                    result.Errors.Add("No data rows found in Excel file");
                    return result;
                }

                result.TotalRows = rowCount - 1; // Exclude header row

                // Parse the Excel data
                var workPackageGroups = new Dictionary<string, List<WorkPackageItemData>>();

                for (int row = 2; row <= rowCount; row++)
                {
                    try
                    {
                        var workPackageName = worksheet.Cells[row, 1].Value?.ToString()?.Trim();
                        var serialNumber = worksheet.Cells[row, 2].Value?.ToString()?.Trim();

                        if (string.IsNullOrEmpty(workPackageName))
                        {
                            result.Errors.Add($"Row {row}: Work Package Name is required");
                            continue;
                        }

                        if (string.IsNullOrEmpty(serialNumber))
                        {
                            result.Errors.Add($"Row {row}: Serial Number is required");
                            continue;
                        }

                        // Find the water meter
                        var meter = await _context.WaterMeters
                            .FirstOrDefaultAsync(m => m.SerialNumber == serialNumber);

                        if (meter == null)
                        {
                            result.Errors.Add($"Row {row}: Water meter with serial number '{serialNumber}' not found");
                            continue;
                        }

                        // Add to group
                        if (!workPackageGroups.ContainsKey(workPackageName))
                        {
                            workPackageGroups[workPackageName] = new List<WorkPackageItemData>();
                        }

                        workPackageGroups[workPackageName].Add(new WorkPackageItemData
                        {
                            MeterId = meter.Id,
                            SerialNumber = meter.SerialNumber,
                            Location = meter.Location,
                            Row = row
                        });
                    }
                    catch (Exception ex)
                    {
                        result.Errors.Add($"Row {row}: Error processing - {ex.Message}");
                    }
                }

                // Create Work Packages for each group
                foreach (var group in workPackageGroups)
                {
                    try
                    {
                        var workPackageName = group.Key;
                        var meters = group.Value;

                        var createWorkPackageDto = new CreateWorkPackageDto
                        {
                            Name = workPackageName,
                            Description = $"Work package created from Excel import - {fileName}",
                            PackageType = "Scheduled",
                            Status = "Draft",
                            PlannedStartDate = DateTime.UtcNow.Date,
                            PlannedEndDate = DateTime.UtcNow.Date.AddDays(7),
                            Frequency = "Once",
                            ServiceArea = "General",
                            Priority = "Medium",
                            EstimatedHours = meters.Count * 0.5m, // Estimate 30 minutes per meter
                            MeterIds = meters.Select(m => m.MeterId).ToList()
                        };

                        var createdWorkPackage = await _workPackageService.CreateWorkPackageAsync(createWorkPackageDto);
                        
                        result.ImportedWorkPackages.Add(new WorkPackageListDto
                        {
                            Id = createdWorkPackage.Id,
                            Name = createdWorkPackage.Name,
                            Description = createdWorkPackage.Description,
                            PackageType = createdWorkPackage.PackageType,
                            Status = createdWorkPackage.Status,
                            PlannedStartDate = createdWorkPackage.PlannedStartDate,
                            PlannedEndDate = createdWorkPackage.PlannedEndDate,
                            TotalMeters = meters.Count,
                            CreatedBy = createdWorkPackage.CreatedBy,
                            CreatedAt = createdWorkPackage.CreatedAt
                        });

                        result.SuccessCount++;
                    }
                    catch (Exception ex)
                    {
                        result.Errors.Add($"Error creating work package '{group.Key}': {ex.Message}");
                        result.FailureCount++;
                    }
                }

                result.Success = result.SuccessCount > 0;
                result.Message = result.Success 
                    ? $"Successfully imported {result.SuccessCount} work packages"
                    : "No work packages were imported";

                _logger.LogInformation("Work package import completed: {SuccessCount} success, {FailureCount} failures", 
                    result.SuccessCount, result.FailureCount);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error importing work packages from Excel file: {FileName}", fileName);
                result.Errors.Add($"Import failed: {ex.Message}");
                return result;
            }
        }

        public async Task<byte[]> ExportWaterMetersAsync(WaterMeterSearchDto? searchDto = null)
        {
            try
            {
                // Get all meters matching search criteria (no pagination for export)
                var query = _context.WaterMeters.AsQueryable();

                if (searchDto != null)
                {
                    if (!string.IsNullOrEmpty(searchDto.SerialNumber))
                        query = query.Where(m => m.SerialNumber.Contains(searchDto.SerialNumber));

                    if (!string.IsNullOrEmpty(searchDto.Location))
                        query = query.Where(m => m.Location.Contains(searchDto.Location));

                    if (!string.IsNullOrEmpty(searchDto.MeterType))
                        query = query.Where(m => m.MeterType == searchDto.MeterType);

                    if (!string.IsNullOrEmpty(searchDto.Status))
                        query = query.Where(m => m.Status == searchDto.Status);

                    if (!string.IsNullOrEmpty(searchDto.CustomerName))
                        query = query.Where(m => m.CustomerName != null && m.CustomerName.Contains(searchDto.CustomerName));

                    if (searchDto.InstallDateFrom.HasValue)
                        query = query.Where(m => m.InstallDate >= searchDto.InstallDateFrom.Value);

                    if (searchDto.InstallDateTo.HasValue)
                        query = query.Where(m => m.InstallDate <= searchDto.InstallDateTo.Value);
                }

                // Apply sorting
                if (!string.IsNullOrEmpty(searchDto?.SortBy))
                {
                    var isDescending = searchDto.SortDirection?.ToLower() == "desc";
                    query = searchDto.SortBy.ToLower() switch
                    {
                        "serialnumber" => isDescending ? query.OrderByDescending(m => m.SerialNumber) : query.OrderBy(m => m.SerialNumber),
                        "location" => isDescending ? query.OrderByDescending(m => m.Location) : query.OrderBy(m => m.Location),
                        "metertype" => isDescending ? query.OrderByDescending(m => m.MeterType) : query.OrderBy(m => m.MeterType),
                        "status" => isDescending ? query.OrderByDescending(m => m.Status) : query.OrderBy(m => m.Status),
                        "customername" => isDescending ? query.OrderByDescending(m => m.CustomerName) : query.OrderBy(m => m.CustomerName),
                        "installdate" => isDescending ? query.OrderByDescending(m => m.InstallDate) : query.OrderBy(m => m.InstallDate),
                        _ => query.OrderBy(m => m.SerialNumber)
                    };
                }
                else
                {
                    query = query.OrderBy(m => m.SerialNumber);
                }

                var meters = await query.ToListAsync();

                using var package = new ExcelPackage();
                var worksheet = package.Workbook.Worksheets.Add("Water Meters");

                // Define headers
                var headers = new[]
                {
                    "Serial Number", "Asset ID", "Account Number", "Location", "Address",
                    "Meter Type", "Status", "Customer Name", "Install Date", "Last Read",
                    "Date of Read", "Battery Level", "Township", "Road Name", "Latitude",
                    "Longitude", "Sync Status", "Last Sync Date", "Can't Read", "Condition", "Notes"
                };

                // Add headers
                for (int i = 0; i < headers.Length; i++)
                {
                    worksheet.Cells[1, i + 1].Value = headers[i];
                    worksheet.Cells[1, i + 1].Style.Font.Bold = true;
                    worksheet.Cells[1, i + 1].Style.Fill.PatternType = OfficeOpenXml.Style.ExcelFillStyle.Solid;
                    worksheet.Cells[1, i + 1].Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.LightBlue);
                }

                // Add data rows
                for (int row = 0; row < meters.Count; row++)
                {
                    var meter = meters[row];
                    var excelRow = row + 2; // Start from row 2 (after header)

                    worksheet.Cells[excelRow, 1].Value = meter.SerialNumber;
                    worksheet.Cells[excelRow, 2].Value = meter.AssetId;
                    worksheet.Cells[excelRow, 3].Value = meter.AccountNumber;
                    worksheet.Cells[excelRow, 4].Value = meter.Location;
                    worksheet.Cells[excelRow, 5].Value = meter.Address;
                    worksheet.Cells[excelRow, 6].Value = meter.MeterType;
                    worksheet.Cells[excelRow, 7].Value = meter.Status;
                    worksheet.Cells[excelRow, 8].Value = meter.CustomerName;
                    worksheet.Cells[excelRow, 9].Value = meter.InstallDate?.ToString("yyyy-MM-dd");
                    worksheet.Cells[excelRow, 10].Value = meter.LastRead;
                    worksheet.Cells[excelRow, 11].Value = meter.DateOfRead?.ToString("yyyy-MM-dd");
                    worksheet.Cells[excelRow, 12].Value = meter.BatteryLevel;
                    worksheet.Cells[excelRow, 13].Value = meter.Township;
                    worksheet.Cells[excelRow, 14].Value = meter.RoadName;
                    worksheet.Cells[excelRow, 15].Value = meter.Latitude;
                    worksheet.Cells[excelRow, 16].Value = meter.Longitude;
                    worksheet.Cells[excelRow, 17].Value = meter.SyncStatus;
                    worksheet.Cells[excelRow, 18].Value = meter.LastSyncDate?.ToString("yyyy-MM-dd HH:mm");
                    worksheet.Cells[excelRow, 19].Value = meter.CantRead;
                    worksheet.Cells[excelRow, 20].Value = meter.Condition;
                    worksheet.Cells[excelRow, 21].Value = meter.Notes;
                }

                // Auto-fit columns
                worksheet.Cells.AutoFitColumns();

                return package.GetAsByteArray();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error exporting water meters to Excel");
                throw;
            }
        }

        public async Task<byte[]> ExportWaterMetersCsvAsync(WaterMeterSearchDto? searchDto = null)
        {
            try
            {
                // Get all meters matching search criteria (no pagination for export)
                var query = _context.WaterMeters.AsQueryable();

                if (searchDto != null)
                {
                    if (!string.IsNullOrEmpty(searchDto.SerialNumber))
                        query = query.Where(m => m.SerialNumber.Contains(searchDto.SerialNumber));

                    if (!string.IsNullOrEmpty(searchDto.Location))
                        query = query.Where(m => m.Location.Contains(searchDto.Location));

                    if (!string.IsNullOrEmpty(searchDto.MeterType))
                        query = query.Where(m => m.MeterType == searchDto.MeterType);

                    if (!string.IsNullOrEmpty(searchDto.Status))
                        query = query.Where(m => m.Status == searchDto.Status);

                    if (!string.IsNullOrEmpty(searchDto.CustomerName))
                        query = query.Where(m => m.CustomerName != null && m.CustomerName.Contains(searchDto.CustomerName));

                    if (searchDto.InstallDateFrom.HasValue)
                        query = query.Where(m => m.InstallDate >= searchDto.InstallDateFrom.Value);

                    if (searchDto.InstallDateTo.HasValue)
                        query = query.Where(m => m.InstallDate <= searchDto.InstallDateTo.Value);
                }

                // Apply sorting
                if (!string.IsNullOrEmpty(searchDto?.SortBy))
                {
                    var isDescending = searchDto.SortDirection?.ToLower() == "desc";
                    query = searchDto.SortBy.ToLower() switch
                    {
                        "serialnumber" => isDescending ? query.OrderByDescending(m => m.SerialNumber) : query.OrderBy(m => m.SerialNumber),
                        "location" => isDescending ? query.OrderByDescending(m => m.Location) : query.OrderBy(m => m.Location),
                        "metertype" => isDescending ? query.OrderByDescending(m => m.MeterType) : query.OrderBy(m => m.MeterType),
                        "status" => isDescending ? query.OrderByDescending(m => m.Status) : query.OrderBy(m => m.Status),
                        "customername" => isDescending ? query.OrderByDescending(m => m.CustomerName) : query.OrderBy(m => m.CustomerName),
                        "installdate" => isDescending ? query.OrderByDescending(m => m.InstallDate) : query.OrderBy(m => m.InstallDate),
                        _ => query.OrderBy(m => m.SerialNumber)
                    };
                }
                else
                {
                    query = query.OrderBy(m => m.SerialNumber);
                }

                var meters = await query.ToListAsync();

                using var memoryStream = new MemoryStream();
                using var writer = new StreamWriter(memoryStream);
                using var csv = new CsvWriter(writer, CultureInfo.InvariantCulture);

                // Write headers
                csv.WriteField("Serial Number");
                csv.WriteField("Asset ID");
                csv.WriteField("Account Number");
                csv.WriteField("Location");
                csv.WriteField("Address");
                csv.WriteField("Meter Type");
                csv.WriteField("Status");
                csv.WriteField("Customer Name");
                csv.WriteField("Install Date");
                csv.WriteField("Last Read");
                csv.WriteField("Date of Read");
                csv.WriteField("Battery Level");
                csv.WriteField("Township");
                csv.WriteField("Road Name");
                csv.WriteField("Latitude");
                csv.WriteField("Longitude");
                csv.WriteField("Sync Status");
                csv.WriteField("Last Sync Date");
                csv.WriteField("Can't Read");
                csv.WriteField("Condition");
                csv.WriteField("Notes");
                csv.NextRecord();

                // Write data
                foreach (var meter in meters)
                {
                    csv.WriteField(meter.SerialNumber);
                    csv.WriteField(meter.AssetId);
                    csv.WriteField(meter.AccountNumber);
                    csv.WriteField(meter.Location);
                    csv.WriteField(meter.Address);
                    csv.WriteField(meter.MeterType);
                    csv.WriteField(meter.Status);
                    csv.WriteField(meter.CustomerName);
                    csv.WriteField(meter.InstallDate?.ToString("yyyy-MM-dd"));
                    csv.WriteField(meter.LastRead);
                    csv.WriteField(meter.DateOfRead?.ToString("yyyy-MM-dd"));
                    csv.WriteField(meter.BatteryLevel);
                    csv.WriteField(meter.Township);
                    csv.WriteField(meter.RoadName);
                    csv.WriteField(meter.Latitude);
                    csv.WriteField(meter.Longitude);
                    csv.WriteField(meter.SyncStatus);
                    csv.WriteField(meter.LastSyncDate?.ToString("yyyy-MM-dd HH:mm"));
                    csv.WriteField(meter.CantRead);
                    csv.WriteField(meter.Condition);
                    csv.WriteField(meter.Notes);
                    csv.NextRecord();
                }

                writer.Flush();
                return memoryStream.ToArray();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error exporting water meters to CSV");
                throw;
            }
        }

        private class WorkPackageItemData
        {
            public int MeterId { get; set; }
            public string SerialNumber { get; set; } = string.Empty;
            public string? Location { get; set; }
            public int Row { get; set; }
        }

        private static WaterMeterDto MapToDto(WaterMeter meter)
        {
            return new WaterMeterDto
            {
                Id = meter.Id,
                SerialNumber = meter.SerialNumber,
                Location = meter.Location,
                Address = meter.Address,
                MeterType = meter.MeterType,
                Status = meter.Status,
                InstallDate = meter.InstallDate,
                LastReadingDate = meter.DateOfRead,
                LastRead = meter.LastRead,
                CustomerCode = meter.CustomerCode,
                CustomerName = meter.CustomerName,
                Latitude = meter.Latitude,
                Longitude = meter.Longitude,
                Brand = meter.Brand,
                Model = meter.Model,
                BatteryLevel = meter.BatteryLevel,
                CommunicationMethod = meter.CommunicationMethod,
                LastMaintenanceDate = meter.LastMaintenanceDate,
                NextMaintenanceDate = meter.NextMaintenanceDate,
                Notes = meter.Notes,
                CreatedAt = meter.CreatedAt,
                UpdatedAt = meter.UpdatedAt,
                // AMS Integration fields
                AssetId = meter.AssetId,
                MeterNumber = meter.SerialNumber,
                AccountNumber = meter.AccountNumber,
                BookNumber = meter.BookNumber,
                Unit = meter.Unit,
                RoadNumber = meter.RoadNumber,
                RoadName = meter.RoadName,
                Township = meter.Township,
                SubArea = meter.SubArea,
                RecentChange = meter.RecentChange,
                Subd = meter.Subd,
                DateOfRead = meter.DateOfRead,
                Read = meter.Read,
                CantRead = meter.CantRead,
                Condition = meter.Condition,
                Comments = meter.Comments,
                SyncStatus = meter.SyncStatus,
                LastSyncDate = meter.LastSyncDate,
                Source = meter.Source
            };
        }
    }
} 