using Microsoft.EntityFrameworkCore;
using System.Globalization;
using WaterMeterManagement.Data;
using WaterMeterManagement.DTOs;
using WaterMeterManagement.Models;
using WaterMeterManagement.Services.Interfaces;
using CsvHelper;
using OfficeOpenXml;
using OfficeOpenXml.Style;
using System.Drawing;

namespace WaterMeterManagement.Services
{
    public class BaselineService : IBaselineService
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<BaselineService> _logger;

        public BaselineService(ApplicationDbContext context, ILogger<BaselineService> logger)
        {
            _context = context;
            _logger = logger;
        }

        public async Task<(List<BaselineRecordListDto> Records, int TotalCount)> GetBaselineRecordsAsync(BaselineSearchDto searchDto)
        {
            // 🎯 UPDATED: Always query from BaselineRecords table directly
            // 直接从BaselineRecords表查询，不再使用fallback逻辑
            var query = _context.BaselineRecords
                .Include(b => b.WaterMeter)
                .Where(b => !b.IsDeleted) // Only non-deleted records
                .AsQueryable();

            // Apply filters
            if (!string.IsNullOrEmpty(searchDto.SerialNumber))
            {
                query = query.Where(b => b.WaterMeter.SerialNumber.Contains(searchDto.SerialNumber));
            }

            if (!string.IsNullOrEmpty(searchDto.Location))
            {
                query = query.Where(b => b.WaterMeter.Location.Contains(searchDto.Location));
            }

            if (!string.IsNullOrEmpty(searchDto.BaselineType))
            {
                query = query.Where(b => b.BaselineType == searchDto.BaselineType);
            }

            if (!string.IsNullOrEmpty(searchDto.Status))
            {
                query = query.Where(b => b.Status == searchDto.Status);
            }

            if (!string.IsNullOrEmpty(searchDto.DataSource))
            {
                query = query.Where(b => b.DataSource == searchDto.DataSource);
            }

            if (searchDto.BaselineDateFrom.HasValue)
            {
                query = query.Where(b => b.BaselineDate >= searchDto.BaselineDateFrom.Value);
            }

            if (searchDto.BaselineDateTo.HasValue)
            {
                query = query.Where(b => b.BaselineDate <= searchDto.BaselineDateTo.Value);
            }

            if (searchDto.UpdatedFrom.HasValue)
            {
                query = query.Where(b => b.UpdatedAt >= searchDto.UpdatedFrom.Value);
            }

            if (searchDto.IsValidated.HasValue)
            {
                query = query.Where(b => b.IsValidated == searchDto.IsValidated.Value);
            }

            if (searchDto.HasValidationErrors.HasValue)
            {
                query = query.Where(b => b.HasValidationErrors == searchDto.HasValidationErrors.Value);
            }

            if (searchDto.IsAnomalous.HasValue)
            {
                query = query.Where(b => b.IsAnomalous == searchDto.IsAnomalous.Value);
            }

            if (!string.IsNullOrEmpty(searchDto.ImportBatch))
            {
                query = query.Where(b => b.ImportBatch == searchDto.ImportBatch);
            }

            var totalCount = await query.CountAsync();

            // Apply sorting
            if (!string.IsNullOrEmpty(searchDto.SortBy))
            {
                switch (searchDto.SortBy.ToLower())
                {
                    case "baselinedate":
                        query = searchDto.SortDirection?.ToLower() == "asc"
                            ? query.OrderBy(b => b.BaselineDate)
                            : query.OrderByDescending(b => b.BaselineDate);
                        break;
                    case "serialnumber":
                        query = searchDto.SortDirection?.ToLower() == "desc"
                            ? query.OrderByDescending(b => b.WaterMeter.SerialNumber)
                            : query.OrderBy(b => b.WaterMeter.SerialNumber);
                        break;
                    case "baselinevalue":
                        query = searchDto.SortDirection?.ToLower() == "desc"
                            ? query.OrderByDescending(b => b.BaselineValue)
                            : query.OrderBy(b => b.BaselineValue);
                        break;
                    default:
                        query = query.OrderByDescending(b => b.BaselineDate);
                        break;
                }
            }
            else
            {
                query = query.OrderByDescending(b => b.BaselineDate);
            }

            // Apply pagination using base class properties
            var records = await query
                .Skip(searchDto.Skip)
                .Take(searchDto.Take)
                .Select(b => new BaselineRecordListDto
                {
                    Id = b.Id,
                    MeterId = b.MeterId,
                    MeterSerialNumber = b.WaterMeter.SerialNumber,
                    MeterLocation = b.WaterMeter.Location,
                    BaselineDate = b.BaselineDate,
                    BaselineValue = b.BaselineValue,
                    BaselineType = b.BaselineType,
                    Status = b.Status,
                    DataSource = b.DataSource,
                    IsValidated = b.IsValidated,
                    HasValidationErrors = b.HasValidationErrors,
                    IsAnomalous = b.IsAnomalous,
                    VariancePercentage = b.VariancePercentage
                })
                .ToListAsync();

            return (records, totalCount);
        }

        public async Task<BaselineRecordDto?> GetBaselineRecordByIdAsync(int id)
        {
            var record = await _context.BaselineRecords
                .Include(b => b.WaterMeter)
                .FirstOrDefaultAsync(b => b.Id == id);

            if (record == null) return null;

            return MapToDto(record);
        }

        public async Task<BaselineRecordDto> CreateBaselineRecordAsync(CreateBaselineRecordDto createDto)
        {
            // Verify meter exists
            var meter = await _context.WaterMeters.FirstOrDefaultAsync(m => m.Id == createDto.MeterId);
            if (meter == null)
                throw new ArgumentException("Water meter not found", nameof(createDto.MeterId));

            // Get previous baseline for variance calculation
            var previousBaseline = await _context.BaselineRecords
                .Where(b => b.MeterId == createDto.MeterId && b.Status == "Active")
                .OrderByDescending(b => b.BaselineDate)
                .FirstOrDefaultAsync();

            var record = new BaselineRecord
            {
                MeterId = createDto.MeterId,
                BaselineDate = createDto.BaselineDate,
                BaselineValue = createDto.BaselineValue,
                BaselineType = createDto.BaselineType,
                Status = createDto.Status,
                DataSource = createDto.DataSource,
                ValidationNotes = createDto.ValidationNotes,
                CreatedAt = DateTime.UtcNow,
                CreatedBy = "System",
                UpdatedAt = DateTime.UtcNow,
                UpdatedBy = "System"
            };

            // Calculate variance if previous baseline exists
            if (previousBaseline != null)
            {
                record.PreviousBaselineId = previousBaseline.Id;
                record.PreviousBaselineValue = previousBaseline.BaselineValue;
                record.VarianceFromPrevious = record.BaselineValue - previousBaseline.BaselineValue;
                record.VariancePercentage = previousBaseline.BaselineValue != 0 
                    ? (record.VarianceFromPrevious / previousBaseline.BaselineValue) * 100 
                    : null;

                // Check for anomalies (variance > 20%)
                if (record.VariancePercentage.HasValue && Math.Abs(record.VariancePercentage.Value) > 20)
                {
                    record.IsAnomalous = true;
                    record.AnomalyDescription = $"Large variance of {record.VariancePercentage:F2}% from previous baseline";
                }

                // Mark previous baseline as superseded if this is replacing it
                if (createDto.BaselineType == "Correction" || createDto.BaselineType == "Migration")
                {
                    previousBaseline.Status = "Superseded";
                    previousBaseline.UpdatedAt = DateTime.UtcNow;
                    previousBaseline.UpdatedBy = "System";
                }
            }

            _context.BaselineRecords.Add(record);
            await _context.SaveChangesAsync();

            return await GetBaselineRecordByIdAsync(record.Id) ?? throw new InvalidOperationException("Failed to retrieve created record");
        }

        public async Task<BaselineRecordDto?> UpdateBaselineRecordAsync(int id, UpdateBaselineRecordDto updateDto)
        {
            var record = await _context.BaselineRecords.FirstOrDefaultAsync(b => b.Id == id);
            if (record == null) return null;

            var oldValue = record.BaselineValue;
            
            record.BaselineDate = updateDto.BaselineDate;
            record.BaselineValue = updateDto.BaselineValue;
            record.BaselineType = updateDto.BaselineType;
            record.Status = updateDto.Status;
            record.ValidationNotes = updateDto.ValidationNotes;
            record.IsValidated = updateDto.IsValidated;
            record.UpdatedAt = DateTime.UtcNow;
            record.UpdatedBy = "System";

            // If value changed, recalculate variances
            if (oldValue != record.BaselineValue)
            {
                await CalculateVariancesAsync(record.MeterId);
            }

            await _context.SaveChangesAsync();
            return await GetBaselineRecordByIdAsync(record.Id);
        }

        public async Task<bool> DeleteBaselineRecordAsync(int id)
        {
            var record = await _context.BaselineRecords.FirstOrDefaultAsync(b => b.Id == id);
            if (record == null) return false;

            _context.BaselineRecords.Remove(record);
            await _context.SaveChangesAsync();

            // Recalculate variances for the meter
            await CalculateVariancesAsync(record.MeterId);

            return true;
        }

        public async Task<BaselineImportResultDto> ImportBaselineRecordsAsync(List<BaselineImportDto> importData, string fileName)
        {
            var importBatch = Guid.NewGuid().ToString();
            var result = new BaselineImportResultDto
            {
                TotalRows = importData.Count,
                ImportBatch = importBatch,
                FileName = fileName,
                ImportDate = DateTime.UtcNow
            };

            var successfulRecords = new List<BaselineRecord>();

            foreach (var importDto in importData)
            {
                try
                {
                    // Find meter by serial number
                    var meter = await _context.WaterMeters
                        .FirstOrDefaultAsync(m => m.SerialNumber == importDto.SerialNumber);

                    if (meter == null)
                    {
                        importDto.ValidationErrors.Add($"Water meter with serial number '{importDto.SerialNumber}' not found");
                        result.FailedRecords.Add(importDto);
                        result.FailedRows++;
                        continue;
                    }

                    importDto.MeterId = meter.Id;

                    // Check for existing baseline on the same date
                    var existingBaseline = await _context.BaselineRecords
                        .FirstOrDefaultAsync(b => b.MeterId == meter.Id && 
                                           b.BaselineDate.Date == importDto.BaselineDate.Date);

                    BaselineRecord record;
                    
                    if (existingBaseline != null)
                    {
                        // Update existing record
                        existingBaseline.BaselineValue = importDto.BaselineValue;
                        existingBaseline.BaselineType = importDto.BaselineType;
                        existingBaseline.Status = importDto.Status;
                        existingBaseline.ValidationNotes = importDto.ValidationNotes;
                        existingBaseline.ImportBatch = importBatch;
                        existingBaseline.SourceFile = fileName;
                        existingBaseline.SourceRowNumber = importDto.RowNumber;
                        existingBaseline.DataSource = "CSV";
                        existingBaseline.UpdatedAt = DateTime.UtcNow;
                        existingBaseline.UpdatedBy = "Import";
                        
                        record = existingBaseline;
                        result.UpdatedRows++;
                    }
                    else
                    {
                        // Create new record
                        record = new BaselineRecord
                        {
                            MeterId = meter.Id,
                            BaselineDate = importDto.BaselineDate,
                            BaselineValue = importDto.BaselineValue,
                            BaselineType = importDto.BaselineType,
                            Status = importDto.Status,
                            ImportBatch = importBatch,
                            SourceFile = fileName,
                            SourceRowNumber = importDto.RowNumber,
                            DataSource = "CSV",
                            ValidationNotes = importDto.ValidationNotes,
                            CreatedAt = DateTime.UtcNow,
                            CreatedBy = "Import",
                            UpdatedAt = DateTime.UtcNow,
                            UpdatedBy = "Import"
                        };

                        successfulRecords.Add(record);
                        result.NewRows++;
                    }

                    result.SuccessfulRows++;
                }
                catch (Exception ex)
                {
                    importDto.ValidationErrors.Add($"Error processing row: {ex.Message}");
                    result.FailedRecords.Add(importDto);
                    result.FailedRows++;
                }
            }

            if (successfulRecords.Any())
            {
                _context.BaselineRecords.AddRange(successfulRecords);
                await _context.SaveChangesAsync();

                // Calculate variances for all affected meters
                var meterIds = successfulRecords.Select(r => r.MeterId).Distinct();
                foreach (var meterId in meterIds)
                {
                    await CalculateVariancesAsync(meterId);
                }
            }

            return result;
        }

        public async Task<BaselineImportResultDto> ImportFromCsvAsync(Stream csvStream, string fileName)
        {
            var importData = new List<BaselineImportDto>();

            using (var reader = new StreamReader(csvStream))
            using (var csv = new CsvReader(reader, CultureInfo.InvariantCulture))
            {
                var records = csv.GetRecords<BaselineImportDto>().ToList();
                for (int i = 0; i < records.Count; i++)
                {
                    records[i].RowNumber = i + 2; // +2 because CSV header is row 1
                    importData.Add(records[i]);
                }
            }

            var validatedData = await ValidateImportDataAsync(importData);
            return await ImportBaselineRecordsAsync(validatedData, fileName);
        }

        public async Task<BaselineImportResultDto> ImportFromExcelAsync(Stream excelStream, string fileName)
        {
            var importData = new List<BaselineImportDto>();

            using (var package = new ExcelPackage(excelStream))
            {
                var worksheet = package.Workbook.Worksheets[0];
                var rowCount = worksheet.Dimension.Rows;

                for (int row = 2; row <= rowCount; row++) // Skip header row
                {
                    var importDto = new BaselineImportDto
                    {
                        RowNumber = row,
                        SerialNumber = worksheet.Cells[row, 1].Value?.ToString() ?? "",
                        BaselineType = worksheet.Cells[row, 3].Value?.ToString() ?? "Periodic",
                        Status = worksheet.Cells[row, 4].Value?.ToString() ?? "Active",
                        ValidationNotes = worksheet.Cells[row, 5].Value?.ToString()
                    };

                    // Parse BaselineDate
                    if (worksheet.Cells[row, 2].Value != null)
                    {
                        if (DateTime.TryParse(worksheet.Cells[row, 2].Value.ToString(), out var baselineDate))
                        {
                            importDto.BaselineDate = baselineDate;
                        }
                    }

                    // Parse BaselineValue
                    if (decimal.TryParse(worksheet.Cells[row, 6].Value?.ToString(), out var baselineValue))
                    {
                        importDto.BaselineValue = baselineValue;
                    }

                    if (!string.IsNullOrEmpty(importDto.SerialNumber))
                    {
                        importData.Add(importDto);
                    }
                }
            }

            var validatedData = await ValidateImportDataAsync(importData);
            return await ImportBaselineRecordsAsync(validatedData, fileName);
        }

        public async Task<List<BaselineImportDto>> ValidateImportDataAsync(List<BaselineImportDto> importData)
        {
            foreach (var item in importData)
            {
                item.ValidationErrors.Clear();

                // Required field validations
                if (string.IsNullOrWhiteSpace(item.SerialNumber))
                    item.ValidationErrors.Add("Serial Number is required");

                if (item.BaselineDate == default)
                    item.ValidationErrors.Add("Baseline Date is required");

                if (item.BaselineValue <= 0)
                    item.ValidationErrors.Add("Baseline Value must be greater than 0");

                // Business rule validations
                if (!string.IsNullOrEmpty(item.SerialNumber))
                {
                    var meter = await _context.WaterMeters
                        .FirstOrDefaultAsync(m => m.SerialNumber == item.SerialNumber);
                    
                    if (meter == null)
                        item.ValidationErrors.Add($"Water meter with serial number '{item.SerialNumber}' not found");
                    else
                        item.MeterId = meter.Id;
                }

                // Validate baseline type
                var validBaselineTypes = new[] { "Initial", "Periodic", "Correction", "Migration" };
                if (!string.IsNullOrEmpty(item.BaselineType) && !validBaselineTypes.Contains(item.BaselineType))
                {
                    item.ValidationErrors.Add($"Invalid Baseline Type. Valid types: {string.Join(", ", validBaselineTypes)}");
                }

                // Validate status
                var validStatuses = new[] { "Active", "Superseded", "Invalid" };
                if (!string.IsNullOrEmpty(item.Status) && !validStatuses.Contains(item.Status))
                {
                    item.ValidationErrors.Add($"Invalid Status. Valid statuses: {string.Join(", ", validStatuses)}");
                }
            }

            return importData;
        }

        public async Task<BaselineHistoryDto> GetMeterBaselineHistoryAsync(int meterId)
        {
            var meter = await _context.WaterMeters.FirstOrDefaultAsync(m => m.Id == meterId);
            if (meter == null)
                throw new ArgumentException("Water meter not found", nameof(meterId));

            var baselines = await _context.BaselineRecords
                .Where(b => b.MeterId == meterId)
                .OrderBy(b => b.BaselineDate)
                .ToListAsync();

            var history = new BaselineHistoryDto
            {
                MeterId = meterId,
                MeterSerialNumber = meter.SerialNumber,
                MeterLocation = meter.Location,
                BaselineRecords = baselines.Select(MapToDto).ToList(),
                TotalRecords = baselines.Count
            };

            if (baselines.Any())
            {
                history.FirstBaselineDate = baselines.First().BaselineDate;
                history.LastBaselineDate = baselines.Last().BaselineDate;
                history.CurrentBaseline = baselines.Last().BaselineValue;
                
                if (baselines.Count > 1)
                {
                    history.PreviousBaseline = baselines[^2].BaselineValue;
                    history.TotalChange = history.CurrentBaseline - baselines.First().BaselineValue;
                    
                    var monthsDiff = (decimal)((history.LastBaselineDate - history.FirstBaselineDate)?.TotalDays ?? 0) / 30;
                    if (monthsDiff > 0)
                    {
                        history.AverageMonthlyChange = history.TotalChange / monthsDiff;
                    }
                }
            }

            return history;
        }

        public async Task<List<BaselineAnomalyDto>> GetAnomalousBaselinesAsync()
        {
            return await _context.BaselineRecords
                .Include(b => b.WaterMeter)
                .Where(b => b.IsAnomalous)
                .Select(b => new BaselineAnomalyDto
                {
                    Id = b.Id,
                    MeterId = b.MeterId,
                    MeterSerialNumber = b.WaterMeter.SerialNumber,
                    BaselineDate = b.BaselineDate,
                    BaselineValue = b.BaselineValue,
                    PreviousBaselineValue = b.PreviousBaselineValue,
                    VarianceFromPrevious = b.VarianceFromPrevious,
                    VariancePercentage = b.VariancePercentage,
                    AnomalyDescription = b.AnomalyDescription,
                    Status = b.Status,
                    CreatedAt = b.CreatedAt
                })
                .OrderByDescending(b => b.CreatedAt)
                .ToListAsync();
        }

        public async Task<List<BaselineRecordListDto>> GetUnvalidatedBaselinesAsync()
        {
            return await _context.BaselineRecords
                .Include(b => b.WaterMeter)
                .Where(b => !b.IsValidated)
                .Select(b => new BaselineRecordListDto
                {
                    Id = b.Id,
                    MeterId = b.MeterId,
                    MeterSerialNumber = b.WaterMeter.SerialNumber,
                    MeterLocation = b.WaterMeter.Location,
                    BaselineDate = b.BaselineDate,
                    BaselineValue = b.BaselineValue,
                    BaselineType = b.BaselineType,
                    Status = b.Status,
                    DataSource = b.DataSource,
                    IsValidated = b.IsValidated,
                    HasValidationErrors = b.HasValidationErrors,
                    IsAnomalous = b.IsAnomalous,
                    VariancePercentage = b.VariancePercentage
                })
                .OrderByDescending(b => b.BaselineDate)
                .ToListAsync();
        }

        public async Task<bool> ValidateBaselineAsync(BaselineValidationDto validationDto)
        {
            var record = await _context.BaselineRecords.FirstOrDefaultAsync(b => b.Id == validationDto.BaselineId);
            if (record == null) return false;

            record.IsValidated = validationDto.IsValid;
            record.ValidatedDate = DateTime.UtcNow;
            record.ValidatedBy = validationDto.ValidatedBy;
            record.ValidationNotes = validationDto.ValidationNotes;
            record.HasValidationErrors = !validationDto.IsValid || validationDto.ValidationErrors.Any();
            
            if (validationDto.ValidationErrors.Any())
            {
                record.ValidationErrors = string.Join("; ", validationDto.ValidationErrors);
            }

            record.UpdatedAt = DateTime.UtcNow;
            record.UpdatedBy = validationDto.ValidatedBy ?? "System";

            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> CorrectBaselineAsync(BaselineCorrectionDto correctionDto, string correctedBy)
        {
            var record = await _context.BaselineRecords.FirstOrDefaultAsync(b => b.Id == correctionDto.BaselineId);
            if (record == null) return false;

            record.BaselineValue = correctionDto.NewBaselineValue;
            record.IsCorrected = true;
            record.CorrectedDate = DateTime.UtcNow;
            record.CorrectedBy = correctedBy;
            record.CorrectionReason = correctionDto.CorrectionReason;
            record.ValidationNotes = correctionDto.Notes;
            record.UpdatedAt = DateTime.UtcNow;
            record.UpdatedBy = correctedBy;

            await _context.SaveChangesAsync();

            // Recalculate variances for the meter
            await CalculateVariancesAsync(record.MeterId);

            return true;
        }

        public async Task<List<BaselineRecordListDto>> GetBaselinesByImportBatchAsync(string importBatch)
        {
            return await _context.BaselineRecords
                .Include(b => b.WaterMeter)
                .Where(b => b.ImportBatch == importBatch)
                .Select(b => new BaselineRecordListDto
                {
                    Id = b.Id,
                    MeterId = b.MeterId,
                    MeterSerialNumber = b.WaterMeter.SerialNumber,
                    MeterLocation = b.WaterMeter.Location,
                    BaselineDate = b.BaselineDate,
                    BaselineValue = b.BaselineValue,
                    BaselineType = b.BaselineType,
                    Status = b.Status,
                    DataSource = b.DataSource,
                    IsValidated = b.IsValidated,
                    HasValidationErrors = b.HasValidationErrors,
                    IsAnomalous = b.IsAnomalous,
                    VariancePercentage = b.VariancePercentage
                })
                .OrderBy(b => b.MeterSerialNumber)
                .ThenBy(b => b.BaselineDate)
                .ToListAsync();
        }

        public async Task<Dictionary<string, object>> GetBaselineStatisticsAsync()
        {
            var today = DateTime.UtcNow.Date;
            var thisWeek = today.AddDays(-7);
            var thisMonth = today.AddDays(-30);

            // Check if BaselineRecords table has data
            var hasBaselineRecords = await _context.BaselineRecords.AnyAsync();
            
            Dictionary<string, object> stats;
            
            if (!hasBaselineRecords)
            {
                // Use WaterMeter data for statistics
                stats = new Dictionary<string, object>
                {
                    ["totalBaselines"] = await _context.WaterMeters.CountAsync(m => m.LastRead.HasValue || m.Read.HasValue),
                    ["activeBaselines"] = await _context.WaterMeters.CountAsync(m => m.Status == "Active" && (m.LastRead.HasValue || m.Read.HasValue)),
                    ["todayBaselines"] = await _context.WaterMeters.CountAsync(m => m.UpdatedAt >= today && (m.LastRead.HasValue || m.Read.HasValue)),
                    ["weeklyBaselines"] = await _context.WaterMeters.CountAsync(m => m.UpdatedAt >= thisWeek && (m.LastRead.HasValue || m.Read.HasValue)),
                    ["monthlyBaselines"] = await _context.WaterMeters.CountAsync(m => m.UpdatedAt >= thisMonth && (m.LastRead.HasValue || m.Read.HasValue)),
                    ["unvalidatedBaselines"] = await _context.WaterMeters.CountAsync(m => m.CantRead && (m.LastRead.HasValue || m.Read.HasValue)),
                    ["anomalousBaselines"] = await _context.WaterMeters.CountAsync(m => (m.CantRead || !string.IsNullOrEmpty(m.Condition)) && (m.LastRead.HasValue || m.Read.HasValue)),
                    ["errorBaselines"] = await _context.WaterMeters.CountAsync(m => m.CantRead && (m.LastRead.HasValue || m.Read.HasValue)),
                    ["importedBaselines"] = await _context.WaterMeters.CountAsync(m => m.Source == "AMS" && (m.LastRead.HasValue || m.Read.HasValue))
                };
            }
            else
            {
                // Use BaselineRecords data for statistics
                stats = new Dictionary<string, object>
                {
                    ["totalBaselines"] = await _context.BaselineRecords.CountAsync(),
                    ["activeBaselines"] = await _context.BaselineRecords.CountAsync(b => b.Status == "Active"),
                    ["todayBaselines"] = await _context.BaselineRecords.CountAsync(b => b.CreatedAt >= today),
                    ["weeklyBaselines"] = await _context.BaselineRecords.CountAsync(b => b.CreatedAt >= thisWeek),
                    ["monthlyBaselines"] = await _context.BaselineRecords.CountAsync(b => b.CreatedAt >= thisMonth),
                    ["unvalidatedBaselines"] = await _context.BaselineRecords.CountAsync(b => !b.IsValidated),
                    ["anomalousBaselines"] = await _context.BaselineRecords.CountAsync(b => b.IsAnomalous),
                    ["errorBaselines"] = await _context.BaselineRecords.CountAsync(b => b.HasValidationErrors),
                    ["importedBaselines"] = await _context.BaselineRecords.CountAsync(b => b.DataSource == "CSV" || b.DataSource == "Excel")
                };
            }

            return stats;
        }

        public async Task<List<BaselineRecordListDto>> GetRecentBaselinesAsync(int days = 7)
        {
            var cutoffDate = DateTime.UtcNow.Date.AddDays(-days);
            
            return await _context.BaselineRecords
                .Include(b => b.WaterMeter)
                .Where(b => b.CreatedAt >= cutoffDate)
                .Select(b => new BaselineRecordListDto
                {
                    Id = b.Id,
                    MeterId = b.MeterId,
                    MeterSerialNumber = b.WaterMeter.SerialNumber,
                    MeterLocation = b.WaterMeter.Location,
                    BaselineDate = b.BaselineDate,
                    BaselineValue = b.BaselineValue,
                    BaselineType = b.BaselineType,
                    Status = b.Status,
                    DataSource = b.DataSource,
                    IsValidated = b.IsValidated,
                    HasValidationErrors = b.HasValidationErrors,
                    IsAnomalous = b.IsAnomalous,
                    VariancePercentage = b.VariancePercentage
                })
                .OrderByDescending(b => b.BaselineDate)
                .ToListAsync();
        }

        public async Task<bool> MarkBaselineAsAnomalousAsync(int baselineId, string description, string markedBy)
        {
            var record = await _context.BaselineRecords.FirstOrDefaultAsync(b => b.Id == baselineId);
            if (record == null) return false;

            record.IsAnomalous = true;
            record.AnomalyDescription = description;
            record.UpdatedAt = DateTime.UtcNow;
            record.UpdatedBy = markedBy;

            await _context.SaveChangesAsync();
            return true;
        }

        public async Task CalculateVariancesAsync(int meterId)
        {
            var baselines = await _context.BaselineRecords
                .Where(b => b.MeterId == meterId)
                .OrderBy(b => b.BaselineDate)
                .ToListAsync();

            for (int i = 1; i < baselines.Count; i++)
            {
                var current = baselines[i];
                var previous = baselines[i - 1];

                current.PreviousBaselineId = previous.Id;
                current.PreviousBaselineValue = previous.BaselineValue;
                current.VarianceFromPrevious = current.BaselineValue - previous.BaselineValue;
                current.VariancePercentage = previous.BaselineValue != 0 
                    ? (current.VarianceFromPrevious / previous.BaselineValue) * 100 
                    : null;

                // Check for anomalies (variance > 20%)
                if (current.VariancePercentage.HasValue && Math.Abs(current.VariancePercentage.Value) > 20)
                {
                    current.IsAnomalous = true;
                    current.AnomalyDescription = $"Large variance of {current.VariancePercentage:F2}% from previous baseline";
                }
                else if (current.IsAnomalous && current.AnomalyDescription?.StartsWith("Large variance") == true)
                {
                    // Reset anomaly if variance is now acceptable
                    current.IsAnomalous = false;
                    current.AnomalyDescription = null;
                }
            }

            await _context.SaveChangesAsync();
        }

        public async Task<List<BaselineRecordDto>> GetSupersededBaselinesAsync(int meterId)
        {
            var records = await _context.BaselineRecords
                .Where(b => b.MeterId == meterId && b.Status == "Superseded")
                .OrderByDescending(b => b.BaselineDate)
                .ToListAsync();

            return records.Select(MapToDto).ToList();
        }

        public async Task<byte[]> ExportBaselinesAsync(List<BaselineRecordListDto> baselines)
        {
            using var package = new ExcelPackage();
            var worksheet = package.Workbook.Worksheets.Add("Baseline Export");

            // Set headers
            var headers = new[]
            {
                "Meter Serial Number", "Location", "Baseline Date", "Baseline Value", 
                "Baseline Type", "Status", "Data Source", "Validated", 
                "Has Errors", "Anomalous", "Variance %", "Import Batch"
            };

            for (int i = 0; i < headers.Length; i++)
            {
                worksheet.Cells[1, i + 1].Value = headers[i];
                worksheet.Cells[1, i + 1].Style.Font.Bold = true;
                worksheet.Cells[1, i + 1].Style.Fill.PatternType = ExcelFillStyle.Solid;
                worksheet.Cells[1, i + 1].Style.Fill.BackgroundColor.SetColor(Color.LightGray);
            }

            // Fill data
            for (int i = 0; i < baselines.Count; i++)
            {
                var baseline = baselines[i];
                var row = i + 2;

                worksheet.Cells[row, 1].Value = baseline.MeterSerialNumber;
                worksheet.Cells[row, 2].Value = baseline.MeterLocation;
                worksheet.Cells[row, 3].Value = baseline.BaselineDate.ToString("yyyy-MM-dd");
                worksheet.Cells[row, 4].Value = baseline.BaselineValue;
                worksheet.Cells[row, 5].Value = baseline.BaselineType;
                worksheet.Cells[row, 6].Value = baseline.Status;
                worksheet.Cells[row, 7].Value = baseline.DataSource;
                worksheet.Cells[row, 8].Value = baseline.IsValidated ? "Yes" : "No";
                worksheet.Cells[row, 9].Value = baseline.HasValidationErrors ? "Yes" : "No";
                worksheet.Cells[row, 10].Value = baseline.IsAnomalous ? "Yes" : "No";
                worksheet.Cells[row, 11].Value = baseline.VariancePercentage?.ToString("F2") ?? "N/A";
            }

            worksheet.Cells.AutoFitColumns();
            return package.GetAsByteArray();
        }

        public async Task<byte[]> GenerateExcelTemplateAsync()
        {
            using var package = new ExcelPackage();
            var worksheet = package.Workbook.Worksheets.Add("Baseline Import Template");

            // Set template headers
            var headers = new[]
            {
                "SerialNumber", "BaselineDate", "BaselineValue", "BaselineType", 
                "Status", "ValidationNotes"
            };

            for (int i = 0; i < headers.Length; i++)
            {
                worksheet.Cells[1, i + 1].Value = headers[i];
                worksheet.Cells[1, i + 1].Style.Font.Bold = true;
                worksheet.Cells[1, i + 1].Style.Fill.PatternType = ExcelFillStyle.Solid;
                worksheet.Cells[1, i + 1].Style.Fill.BackgroundColor.SetColor(Color.LightBlue);
            }

            // Add sample data rows
            worksheet.Cells[2, 1].Value = "19M1567747";
            worksheet.Cells[2, 2].Value = DateTime.UtcNow.ToString("yyyy-MM-dd");
            worksheet.Cells[2, 3].Value = 125.45;
            worksheet.Cells[2, 4].Value = "Initial";
            worksheet.Cells[2, 5].Value = "Active";
            worksheet.Cells[2, 6].Value = "Sample baseline reading";

            worksheet.Cells[3, 1].Value = "17M245630";
            worksheet.Cells[3, 2].Value = DateTime.UtcNow.AddDays(-30).ToString("yyyy-MM-dd");
            worksheet.Cells[3, 3].Value = 89.12;
            worksheet.Cells[3, 4].Value = "Periodic";
            worksheet.Cells[3, 5].Value = "Active";
            worksheet.Cells[3, 6].Value = "Monthly reading";

            // Add instructions worksheet
            var instructionSheet = package.Workbook.Worksheets.Add("Instructions");
            instructionSheet.Cells[1, 1].Value = "Baseline Import Template Instructions";
            instructionSheet.Cells[1, 1].Style.Font.Size = 16;
            instructionSheet.Cells[1, 1].Style.Font.Bold = true;

            var instructions = new[]
            {
                "",
                "Required Fields:",
                "• SerialNumber: Water meter serial number (must exist in system)",
                "• BaselineDate: Date in YYYY-MM-DD format",
                "• BaselineValue: Numeric value (positive number)",
                "",
                "Optional Fields:",
                "• BaselineType: Initial, Periodic, Correction, or Migration (default: Periodic)",
                "• Status: Active, Superseded, or Invalid (default: Active)",
                "• ValidationNotes: Additional notes for validation",
                "",
                "Important Notes:",
                "• Remove the sample data rows before importing",
                "• Ensure water meter serial numbers exist in the system",
                "• Date format must be YYYY-MM-DD",
                "• Baseline values must be positive numbers",
                "• Duplicate baseline dates for the same meter will be rejected"
            };

            for (int i = 0; i < instructions.Length; i++)
            {
                instructionSheet.Cells[i + 2, 1].Value = instructions[i];
                if (instructions[i].StartsWith("•"))
                {
                    instructionSheet.Cells[i + 2, 1].Style.Indent = 1;
                }
            }

            worksheet.Cells.AutoFitColumns();
            instructionSheet.Cells.AutoFitColumns();
            
            return await Task.FromResult(package.GetAsByteArray());
        }

        public async Task<BatchValidationResultDto> BatchValidateBaselinesAsync(BatchValidationDto batchDto)
        {
            var result = new BatchValidationResultDto
            {
                TotalRequested = batchDto.BaselineIds.Count,
                ProcessedBy = batchDto.ValidatedBy
            };

            foreach (var baselineId in batchDto.BaselineIds)
            {
                try
                {
                    var validationDto = new BaselineValidationDto
                    {
                        BaselineId = baselineId,
                        IsValid = batchDto.IsValid,
                        ValidationNotes = batchDto.ValidationNotes,
                        ValidatedBy = batchDto.ValidatedBy
                    };

                    var success = await ValidateBaselineAsync(validationDto);
                    if (success)
                    {
                        result.Successful++;
                    }
                    else
                    {
                        result.Failed++;
                        result.Errors.Add($"Baseline {baselineId}: Record not found");
                    }
                }
                catch (Exception ex)
                {
                    result.Failed++;
                    result.Errors.Add($"Baseline {baselineId}: {ex.Message}");
                    _logger.LogError(ex, "Error validating baseline {BaselineId} in batch", baselineId);
                }
            }

            return result;
        }

        // SDC import related methods
        public async Task<BaselineImportResultDto> ImportFromSdcFileAsync(Stream fileStream, string fileName)
        {
            try
            {
                var fileExtension = Path.GetExtension(fileName).ToLowerInvariant();
                
                if (fileExtension == ".csv")
                {
                    return await ImportFromCsvAsync(fileStream, fileName);
                }
                else if (fileExtension == ".xlsx" || fileExtension == ".xls")
                {
                    return await ImportFromSdcExcelAsync(fileStream, fileName);
                }
                else
                {
                    throw new ArgumentException("Unsupported file format. Only CSV and Excel files are supported.");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error importing SDC file {FileName}", fileName);
                throw;
            }
        }

        private async Task<BaselineImportResultDto> ImportFromSdcExcelAsync(Stream excelStream, string fileName)
        {
            var importData = new List<BaselineImportDto>();

            using var package = new ExcelPackage(excelStream);
            var worksheet = package.Workbook.Worksheets.FirstOrDefault();
            
            if (worksheet == null)
                throw new ArgumentException("Excel file must contain at least one worksheet");

            var rowCount = worksheet.Dimension?.Rows ?? 0;
            
            if (rowCount < 2)
                throw new ArgumentException("Excel file must contain at least one data row");

            // Parse header row to find column indices
            var headers = new Dictionary<string, int>();
            for (int col = 1; col <= worksheet.Dimension.Columns; col++)
            {
                var header = worksheet.Cells[1, col].Value?.ToString()?.Trim().ToLowerInvariant();
                if (!string.IsNullOrEmpty(header))
                {
                    headers[header] = col;
                }
            }

            // Required columns mapping
            var requiredColumns = new Dictionary<string, string[]>
            {
                ["serialnumber"] = new[] { "serialnumber", "serial number", "meter serial", "serial" },
                ["baselinedate"] = new[] { "baselinedate", "baseline date", "date", "reading date" },
                ["baselinevalue"] = new[] { "baselinevalue", "baseline value", "value", "reading", "reading value" }
            };

            var columnMap = new Dictionary<string, int>();
            foreach (var required in requiredColumns)
            {
                var found = false;
                foreach (var variant in required.Value)
                {
                    if (headers.ContainsKey(variant))
                    {
                        columnMap[required.Key] = headers[variant];
                        found = true;
                        break;
                    }
                }
                if (!found)
                {
                    throw new ArgumentException($"Required column not found: {string.Join(" or ", required.Value)}");
                }
            }

            // Optional columns
            var optionalColumns = new Dictionary<string, string[]>
            {
                ["baselinetype"] = new[] { "baselinetype", "baseline type", "type" },
                ["status"] = new[] { "status" },
                ["location"] = new[] { "location", "address" },
                ["validationnotes"] = new[] { "validationnotes", "validation notes", "notes", "comments" }
            };

            foreach (var optional in optionalColumns)
            {
                foreach (var variant in optional.Value)
                {
                    if (headers.ContainsKey(variant))
                    {
                        columnMap[optional.Key] = headers[variant];
                        break;
                    }
                }
            }

            // Parse data rows
            for (int row = 2; row <= rowCount; row++)
            {
                var importDto = new BaselineImportDto
                {
                    RowNumber = row
                };

                try
                {
                    // Required fields
                    importDto.SerialNumber = worksheet.Cells[row, columnMap["serialnumber"]].Value?.ToString()?.Trim() ?? "";
                    
                    var dateStr = worksheet.Cells[row, columnMap["baselinedate"]].Value?.ToString()?.Trim();
                    if (DateTime.TryParse(dateStr, out var baselineDate))
                    {
                        importDto.BaselineDate = baselineDate;
                    }
                    else
                    {
                        importDto.ValidationErrors.Add("Invalid baseline date format");
                    }

                                         var valueStr = worksheet.Cells[row, columnMap["baselinevalue"]].Value?.ToString()?.Trim();
                     if (decimal.TryParse(valueStr, out var baselineValue))
                     {
                         importDto.BaselineValue = baselineValue;
                     }
                    else
                    {
                        importDto.ValidationErrors.Add("Invalid baseline value");
                    }

                    // Optional fields with defaults
                    importDto.BaselineType = columnMap.ContainsKey("baselinetype") 
                        ? worksheet.Cells[row, columnMap["baselinetype"]].Value?.ToString()?.Trim() ?? "Periodic"
                        : "Periodic";
                        
                    importDto.Status = columnMap.ContainsKey("status")
                        ? worksheet.Cells[row, columnMap["status"]].Value?.ToString()?.Trim() ?? "Active"
                        : "Active";
                        
                    importDto.ValidationNotes = columnMap.ContainsKey("validationnotes")
                        ? worksheet.Cells[row, columnMap["validationnotes"]].Value?.ToString()?.Trim()
                        : null;

                    // Basic validation
                    if (string.IsNullOrEmpty(importDto.SerialNumber))
                        importDto.ValidationErrors.Add("Serial number is required");
                    
                    if (importDto.BaselineValue <= 0)
                        importDto.ValidationErrors.Add("Baseline value must be positive");

                    importData.Add(importDto);
                }
                catch (Exception ex)
                {
                    importDto.ValidationErrors.Add($"Error parsing row: {ex.Message}");
                    importData.Add(importDto);
                }
            }

            return await ImportBaselineRecordsAsync(importData, fileName);
        }

        public async Task<byte[]> GenerateSdcImportTemplateAsync()
        {
            using var package = new ExcelPackage();
            var worksheet = package.Workbook.Worksheets.Add("SDC Baseline Import");

            // Set headers
            var headers = new[]
            {
                "SerialNumber", "BaselineDate", "BaselineValue", "BaselineType", 
                "Status", "Location", "ValidationNotes"
            };

            for (int i = 0; i < headers.Length; i++)
            {
                worksheet.Cells[1, i + 1].Value = headers[i];
                worksheet.Cells[1, i + 1].Style.Font.Bold = true;
                worksheet.Cells[1, i + 1].Style.Fill.PatternType = ExcelFillStyle.Solid;
                worksheet.Cells[1, i + 1].Style.Fill.BackgroundColor.SetColor(Color.LightGreen);
            }

            // Add sample data
            worksheet.Cells[2, 1].Value = "19M1567747";
            worksheet.Cells[2, 2].Value = DateTime.UtcNow.ToString("yyyy-MM-dd");
            worksheet.Cells[2, 3].Value = 125.45;
            worksheet.Cells[2, 4].Value = "Initial";
            worksheet.Cells[2, 5].Value = "Active";
            worksheet.Cells[2, 6].Value = "Rolleston Area 1";
            worksheet.Cells[2, 7].Value = "SDC baseline reading";

            // Add instructions
            var instructionSheet = package.Workbook.Worksheets.Add("SDC Import Instructions");
            instructionSheet.Cells[1, 1].Value = "SDC Baseline Import Instructions";
            instructionSheet.Cells[1, 1].Style.Font.Size = 16;
            instructionSheet.Cells[1, 1].Style.Font.Bold = true;

            var instructions = new[]
            {
                "",
                "This template is specifically designed for SDC baseline data import.",
                "",
                "Required Columns:",
                "• SerialNumber: Water meter serial number (must exist in system)",
                "• BaselineDate: Date in YYYY-MM-DD format",
                "• BaselineValue: Numeric baseline reading value",
                "",
                "Optional Columns:",
                "• BaselineType: Initial, Periodic, Correction, Migration (default: Periodic)",
                "• Status: Active, Superseded, Invalid (default: Active)",
                "• Location: Meter location/address",
                "• ValidationNotes: Additional validation notes",
                "",
                "Important Notes:",
                "• Remove sample data before importing real data",
                "• Serial numbers must match existing meters in the system",
                "• Baseline values must be positive numbers",
                "• Date format: YYYY-MM-DD (e.g., 2024-01-15)",
                "• File supports both .xlsx and .csv formats"
            };

            for (int i = 0; i < instructions.Length; i++)
            {
                instructionSheet.Cells[i + 2, 1].Value = instructions[i];
                if (instructions[i].StartsWith("•"))
                {
                    instructionSheet.Cells[i + 2, 1].Style.Indent = 1;
                }
            }

            worksheet.Cells.AutoFitColumns();
            instructionSheet.Cells.AutoFitColumns();
            
            return await Task.FromResult(package.GetAsByteArray());
        }

        public async Task<(List<object> Imports, int TotalCount)> GetSdcImportHistoryAsync(int page, int pageSize)
        {
            // This would typically query an ImportHistory table
            // For now, returning sample data
            var sampleHistory = new List<object>
            {
                new {
                    Id = 1,
                    FileName = "sdc_baselines_2024_01.xlsx",
                    ImportDate = DateTime.UtcNow.AddDays(-7),
                    TotalRows = 150,
                    SuccessfulRows = 145,
                    FailedRows = 5,
                    ImportedBy = "Admin"
                },
                new {
                    Id = 2,
                    FileName = "sdc_monthly_baseline.csv",
                    ImportDate = DateTime.UtcNow.AddDays(-14),
                    TotalRows = 89,
                    SuccessfulRows = 89,
                    FailedRows = 0,
                    ImportedBy = "System"
                }
            };

            var totalCount = sampleHistory.Count;
            var pagedData = sampleHistory
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .ToList();

            return await Task.FromResult((pagedData.Cast<object>().ToList(), totalCount));
        }

        public async Task<object> ValidateSdcFileAsync(Stream fileStream, string fileName)
        {
            try
            {
                var fileExtension = Path.GetExtension(fileName).ToLowerInvariant();
                var validationResult = new
                {
                    IsValid = true,
                    FileName = fileName,
                    FileSize = fileStream.Length,
                    FileType = fileExtension,
                    EstimatedRows = 0,
                    ValidationMessages = new List<string>(),
                    RequiredColumns = new[] { "SerialNumber", "BaselineDate", "BaselineValue" },
                    OptionalColumns = new[] { "BaselineType", "Status", "Location", "ValidationNotes" }
                };

                if (fileExtension == ".csv")
                {
                    // Basic CSV validation
                    fileStream.Position = 0;
                    using var reader = new StreamReader(fileStream);
                    var firstLine = await reader.ReadLineAsync();
                    
                    if (string.IsNullOrEmpty(firstLine))
                    {
                        return new
                        {
                            IsValid = false,
                            Message = "CSV file appears to be empty",
                            ValidationMessages = new[] { "File contains no data" }
                        };
                    }

                    var headers = firstLine.Split(',').Select(h => h.Trim().ToLowerInvariant()).ToArray();
                    var missingColumns = new List<string>();
                    
                    if (!headers.Any(h => h.Contains("serial")))
                        missingColumns.Add("SerialNumber");
                    if (!headers.Any(h => h.Contains("date")))
                        missingColumns.Add("BaselineDate");
                    if (!headers.Any(h => h.Contains("value") || h.Contains("reading")))
                        missingColumns.Add("BaselineValue");

                    if (missingColumns.Any())
                    {
                        return new
                        {
                            IsValid = false,
                            Message = $"Missing required columns: {string.Join(", ", missingColumns)}",
                            ValidationMessages = missingColumns.Select(c => $"Missing required column: {c}").ToArray()
                        };
                    }
                }
                else if (fileExtension == ".xlsx" || fileExtension == ".xls")
                {
                    // Basic Excel validation
                    using var package = new ExcelPackage(fileStream);
                    var worksheet = package.Workbook.Worksheets.FirstOrDefault();
                    
                    if (worksheet == null)
                    {
                        return new
                        {
                            IsValid = false,
                            Message = "Excel file contains no worksheets",
                            ValidationMessages = new[] { "No worksheets found in file" }
                        };
                    }

                    var rowCount = worksheet.Dimension?.Rows ?? 0;
                    if (rowCount < 2)
                    {
                        return new
                        {
                            IsValid = false,
                            Message = "Excel file contains no data rows",
                            ValidationMessages = new[] { "File must contain at least one data row" }
                        };
                    }

                    return new
                    {
                        IsValid = true,
                        FileName = fileName,
                        FileSize = fileStream.Length,
                        FileType = fileExtension,
                        EstimatedRows = rowCount - 1,
                        ValidationMessages = new[] { $"File validation successful. Found {rowCount - 1} data rows." },
                        RequiredColumns = new[] { "SerialNumber", "BaselineDate", "BaselineValue" },
                        OptionalColumns = new[] { "BaselineType", "Status", "Location", "ValidationNotes" }
                    };
                }

                return validationResult;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating SDC file {FileName}", fileName);
                return new
                {
                    IsValid = false,
                    Message = $"File validation failed: {ex.Message}",
                    ValidationMessages = new[] { ex.Message }
                };
            }
        }

        private static BaselineRecordDto MapToDto(BaselineRecord record)
        {
            return new BaselineRecordDto
            {
                Id = record.Id,
                MeterId = record.MeterId,
                MeterSerialNumber = record.WaterMeter?.SerialNumber,
                MeterLocation = record.WaterMeter?.Location,
                BaselineDate = record.BaselineDate,
                BaselineValue = record.BaselineValue,
                BaselineType = record.BaselineType,
                Status = record.Status,
                ImportBatch = record.ImportBatch,
                SourceFile = record.SourceFile,
                SourceRowNumber = record.SourceRowNumber,
                DataSource = record.DataSource,
                ValidationNotes = record.ValidationNotes,
                IsValidated = record.IsValidated,
                ValidatedDate = record.ValidatedDate,
                ValidatedBy = record.ValidatedBy,
                HasValidationErrors = record.HasValidationErrors,
                ValidationErrors = record.ValidationErrors,
                IsAnomalous = record.IsAnomalous,
                AnomalyDescription = record.AnomalyDescription,
                PreviousBaselineId = record.PreviousBaselineId,
                PreviousBaselineValue = record.PreviousBaselineValue,
                VarianceFromPrevious = record.VarianceFromPrevious,
                VariancePercentage = record.VariancePercentage,
                IsCorrected = record.IsCorrected,
                CorrectedDate = record.CorrectedDate,
                CorrectedBy = record.CorrectedBy,
                CorrectionReason = record.CorrectionReason,
                CreatedAt = record.CreatedAt,
                UpdatedAt = record.UpdatedAt ?? record.CreatedAt
            };
        }

        /// <summary>
        /// Fallback method to get baseline data from WaterMeter table when BaselineRecords is empty
        /// </summary>
        private async Task<(List<BaselineRecordListDto> Records, int TotalCount)> GetBaselineRecordsFromWaterMetersAsync(BaselineSearchDto searchDto)
        {
            var query = _context.WaterMeters.AsQueryable();

            // Apply filters (map to WaterMeter fields)
            if (!string.IsNullOrEmpty(searchDto.SerialNumber))
            {
                query = query.Where(m => m.SerialNumber.Contains(searchDto.SerialNumber));
            }

            if (!string.IsNullOrEmpty(searchDto.Location))
            {
                query = query.Where(m => m.Location.Contains(searchDto.Location));
            }

            if (searchDto.BaselineDateFrom.HasValue)
            {
                query = query.Where(m => m.DateOfRead >= searchDto.BaselineDateFrom.Value);
            }

            if (searchDto.BaselineDateTo.HasValue)
            {
                query = query.Where(m => m.DateOfRead <= searchDto.BaselineDateTo.Value);
            }

            // Only show meters with actual reading data
            query = query.Where(m => m.LastRead.HasValue || m.Read.HasValue);

            var totalCount = await query.CountAsync();

            // Apply sorting
            if (!string.IsNullOrEmpty(searchDto.SortBy))
            {
                switch (searchDto.SortBy.ToLower())
                {
                    case "baselinedate":
                        query = searchDto.SortDirection?.ToLower() == "asc"
                            ? query.OrderBy(m => m.DateOfRead ?? m.LastReadingDate)
                            : query.OrderByDescending(m => m.DateOfRead ?? m.LastReadingDate);
                        break;
                    case "serialnumber":
                        query = searchDto.SortDirection?.ToLower() == "desc"
                            ? query.OrderByDescending(m => m.SerialNumber)
                            : query.OrderBy(m => m.SerialNumber);
                        break;
                    case "baselinevalue":
                        query = searchDto.SortDirection?.ToLower() == "desc"
                            ? query.OrderByDescending(m => m.LastRead ?? m.Read)
                            : query.OrderBy(m => m.LastRead ?? m.Read);
                        break;
                    default:
                        query = query.OrderByDescending(m => m.DateOfRead ?? m.LastReadingDate ?? m.UpdatedAt);
                        break;
                }
            }
            else
            {
                query = query.OrderByDescending(m => m.DateOfRead ?? m.LastReadingDate ?? m.UpdatedAt);
            }

            // Apply pagination using base class properties
            var meters = await query
                .Skip(searchDto.Skip)
                .Take(searchDto.Take)
                .ToListAsync();

            // Map WaterMeter data to BaselineRecordListDto
            var records = meters.Select(m => new BaselineRecordListDto
            {
                Id = m.Id, // Using meter ID as record ID
                MeterId = m.Id,
                MeterSerialNumber = m.SerialNumber,
                MeterLocation = m.Location,
                BaselineDate = m.DateOfRead ?? m.LastReadingDate ?? m.UpdatedAt ?? DateTime.UtcNow,
                BaselineValue = m.LastRead ?? m.Read ?? 0,
                BaselineType = "Current", // Default type for water meter data
                Status = m.Status == "Active" ? "Active" : "Inactive",
                DataSource = m.Source ?? "WaterMeter",
                IsValidated = true, // Assume water meter data is validated
                HasValidationErrors = m.CantRead,
                IsAnomalous = m.CantRead || !string.IsNullOrEmpty(m.Condition),
                VariancePercentage = null // No historical data to calculate variance
            }).ToList();

            return (records, totalCount);
        }
    }
} 