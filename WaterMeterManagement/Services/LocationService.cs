using WaterMeterManagement.Services.Interfaces;
using WaterMeterManagement.DTOs.Mobile;

namespace WaterMeterManagement.Services
{
    public class LocationService : ILocationService
    {
        private readonly ILogger<LocationService> _logger;

        public LocationService(ILogger<LocationService> logger)
        {
            _logger = logger;
        }

        public async Task<(bool Success, string Message)> UpdateUserLocationAsync(int userId, LocationUpdateDto locationUpdate)
        {
            _logger.LogInformation("Updating location for user {UserId} to ({Lat}, {Lng})", 
                userId, locationUpdate.Latitude, locationUpdate.Longitude);
            
            // Basic implementation - store to database in real implementation
            return await Task.FromResult((true, "Location updated successfully"));
        }

        public async Task<(double? Latitude, double? Longitude)> GetUserLastLocationAsync(int userId)
        {
            _logger.LogInformation("Getting last location for user {UserId}", userId);
            // Return null for now - implement database lookup in real implementation
            return await Task.FromResult<(double?, double?)>((null, null));
        }

        public async Task<bool> ValidateLocationAccuracyAsync(double? accuracy)
        {
            // Accept accuracy within 100 meters
            return await Task.FromResult(!accuracy.HasValue || accuracy.Value <= 100);
        }

        public async Task<bool> IsLocationWithinServiceAreaAsync(double latitude, double longitude)
        {
            _logger.LogInformation("Validating location ({Lat}, {Lng}) is within service area", latitude, longitude);
            // Always return true for now - implement real geofencing logic
            return await Task.FromResult(true);
        }

        public async Task<double> CalculateDistanceKmAsync(double lat1, double lng1, double lat2, double lng2)
        {
            const double R = 6371; // Radius of the Earth in km
            var dLat = DegreesToRadians(lat2 - lat1);
            var dLng = DegreesToRadians(lng2 - lng1);
            var a = Math.Sin(dLat / 2) * Math.Sin(dLat / 2) +
                    Math.Cos(DegreesToRadians(lat1)) * Math.Cos(DegreesToRadians(lat2)) *
                    Math.Sin(dLng / 2) * Math.Sin(dLng / 2);
            var c = 2 * Math.Atan2(Math.Sqrt(a), Math.Sqrt(1 - a));
            var distance = R * c;

            return await Task.FromResult(distance);
        }

        public async Task<List<T>> FilterByDistanceAsync<T>(List<T> items, double userLat, double userLng, double maxDistanceKm, Func<T, (double Lat, double Lng)> locationSelector)
        {
            var filteredItems = new List<T>();
            
            foreach (var item in items)
            {
                var (lat, lng) = locationSelector(item);
                var distance = await CalculateDistanceKmAsync(userLat, userLng, lat, lng);
                
                if (distance <= maxDistanceKm)
                {
                    filteredItems.Add(item);
                }
            }
            
            return filteredItems;
        }

        public async Task<List<LocationUpdateDto>> GetUserLocationHistoryAsync(int userId, DateTime? fromDate = null, int limit = 100)
        {
            _logger.LogInformation("Getting location history for user {UserId}", userId);
            // Return empty list for now - implement database lookup in real implementation
            return await Task.FromResult(new List<LocationUpdateDto>());
        }

        public async Task<bool> IsUserInTaskAreaAsync(int userId, int taskId)
        {
            _logger.LogInformation("Checking if user {UserId} is in task {TaskId} area", userId, taskId);
            // Return true for now - implement real logic
            return await Task.FromResult(true);
        }

        public async Task<List<int>> GetTasksInUserAreaAsync(int userId, double radiusKm)
        {
            _logger.LogInformation("Getting tasks in user {UserId} area (radius: {Radius}km)", userId, radiusKm);
            // Return empty list for now - implement real logic
            return await Task.FromResult(new List<int>());
        }

        private static double DegreesToRadians(double degrees)
        {
            return degrees * (Math.PI / 180);
        }
    }
} 