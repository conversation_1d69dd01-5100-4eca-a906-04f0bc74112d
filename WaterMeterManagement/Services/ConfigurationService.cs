using Microsoft.EntityFrameworkCore;
using System.Text.Json;
using WaterMeterManagement.Data;
using WaterMeterManagement.DTOs;
using WaterMeterManagement.Models;
using WaterMeterManagement.Services.Interfaces;

namespace WaterMeterManagement.Services
{
    public class ConfigurationService : IConfigurationService
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<ConfigurationService> _logger;

        public ConfigurationService(ApplicationDbContext context, ILogger<ConfigurationService> logger)
        {
            _context = context;
            _logger = logger;
        }

        public async Task<List<ConfigurationCategoryDto>> GetConfigurationsAsync(GetConfigurationRequest request)
        {
            _logger.LogInformation($"🔍 ConfigurationService: GetConfigurationsAsync called with Scope={request.Scope}");
            
            var templates = await _context.ConfigurationTemplates
                .Where(t => t.Scope == request.Scope && t.IsActive)
                .OrderBy(t => t.Category).ThenBy(t => t.DisplayOrder)
                .ToListAsync();

            _logger.LogInformation($"🔍 ConfigurationService: Found {templates.Count} templates for scope {request.Scope}");

            var configurations = await _context.Configurations
                .Where(c => c.Scope == request.Scope && c.IsActive)
                .ToListAsync();

            _logger.LogInformation($"🔍 ConfigurationService: Found {configurations.Count} configurations for scope {request.Scope}");

            var result = BuildConfigurationStructure(templates, configurations);
            _logger.LogInformation($"🔍 ConfigurationService: Built {result.Count} categories");
            
            return result;
        }

        public async Task<ConfigurationUpdateResponse> UpdateConfigurationsAsync(UpdateConfigurationRequest request, int? currentUserId = null)
        {
            var response = new ConfigurationUpdateResponse();
            
            foreach (var config in request.Configurations)
            {
                try
                {
                    var template = await _context.ConfigurationTemplates
                        .FirstOrDefaultAsync(t => t.Scope == request.Scope && 
                                                t.Category == request.Category && 
                                                t.Key == config.Key);

                    if (template == null)
                    {
                        response.Errors.Add($"Template not found: {config.Key}");
                        response.FailedCount++;
                        continue;
                    }

                    int? userId = request.Scope == "User" ? currentUserId : null;
                    var existing = await _context.Configurations
                        .FirstOrDefaultAsync(c => c.Scope == request.Scope && 
                                                c.Category == request.Category && 
                                                c.Key == config.Key &&
                                                c.UserId == userId);

                    var jsonValue = JsonSerializer.Serialize(config.Value);

                    if (existing != null)
                    {
                        existing.Value = jsonValue;
                        existing.UpdatedAt = DateTime.UtcNow;
                        existing.UpdatedBy = "System";
                        _context.Configurations.Update(existing);
                    }
                    else
                    {
                        var newConfig = new Configuration
                        {
                            Scope = request.Scope,
                            Category = request.Category,
                            Key = config.Key,
                            DisplayName = template.DisplayName,
                            DataType = template.DataType,
                            Value = jsonValue,
                            DefaultValue = template.DefaultValue,
                            UserId = userId,
                            Priority = request.Scope == "System" ? 1 : 3,
                            IsActive = true,
                            CreatedAt = DateTime.UtcNow,
                            CreatedBy = "System",
                            UpdatedAt = DateTime.UtcNow,
                            UpdatedBy = "System"
                        };
                        _context.Configurations.Add(newConfig);
                    }

                    response.UpdatedCount++;
                }
                catch (Exception ex)
                {
                    response.Errors.Add($"Error updating {config.Key}: {ex.Message}");
                    response.FailedCount++;
                }
            }

            await _context.SaveChangesAsync();
            response.Success = response.FailedCount == 0;
            response.Message = $"Updated {response.UpdatedCount} configurations";
            return response;
        }

        public async Task<List<ConfigurationCategoryDto>> GetConfigurationTemplatesAsync(string scope)
        {
            var templates = await _context.ConfigurationTemplates
                .Where(t => t.Scope == scope && t.IsActive)
                .OrderBy(t => t.Category).ThenBy(t => t.DisplayOrder)
                .ToListAsync();

            return BuildConfigurationStructure(templates, new List<Configuration>());
        }

        public async Task<T?> GetConfigurationValueAsync<T>(string scope, string category, string key, int? userId = null, int? roleId = null)
        {
            var config = await _context.Configurations
                .FirstOrDefaultAsync(c => c.Scope == scope && c.Category == category && c.Key == key);

            if (config == null) return default(T);
            return JsonSerializer.Deserialize<T>(config.Value);
        }

        public async Task<bool> SetConfigurationValueAsync<T>(string scope, string category, string key, T value, int? userId = null, int? roleId = null)
        {
            try
            {
                var jsonValue = JsonSerializer.Serialize(value);
                var existing = await _context.Configurations
                    .FirstOrDefaultAsync(c => c.Scope == scope && c.Category == category && c.Key == key);

                if (existing != null)
                {
                    existing.Value = jsonValue;
                    existing.UpdatedAt = DateTime.UtcNow;
                    existing.UpdatedBy = "System";
                    _context.Configurations.Update(existing);
                }
                else
                {
                    var template = await _context.ConfigurationTemplates
                        .FirstOrDefaultAsync(t => t.Scope == scope && t.Category == category && t.Key == key);
                    if (template == null) return false;

                    var newConfig = new Configuration
                    {
                        Scope = scope, Category = category, Key = key,
                        DisplayName = template.DisplayName, DataType = template.DataType,
                        Value = jsonValue, UserId = userId, Priority = 1,
                        IsActive = true, CreatedAt = DateTime.UtcNow, CreatedBy = "System",
                        UpdatedAt = DateTime.UtcNow, UpdatedBy = "System"
                    };
                    _context.Configurations.Add(newConfig);
                }
                await _context.SaveChangesAsync();
                return true;
            }
            catch { return false; }
        }

        public async Task<ConfigurationUpdateResponse> ResetToDefaultsAsync(string scope, string category, int? userId = null, int? roleId = null)
        {
            var configs = await _context.Configurations
                .Where(c => c.Scope == scope && c.Category == category)
                .ToListAsync();

            _context.Configurations.RemoveRange(configs);
            await _context.SaveChangesAsync();

            return new ConfigurationUpdateResponse
            {
                Success = true,
                UpdatedCount = configs.Count,
                Message = $"Reset {configs.Count} configurations"
            };
        }

        public async Task InitializeDefaultConfigurationsAsync()
        {
            _logger.LogInformation("🔍 ConfigurationService: InitializeDefaultConfigurationsAsync called");
            
            await ConfigurationSeeder.SeedConfigurationTemplatesAsync(_context);
            
            _logger.LogInformation("🔍 ConfigurationService: ConfigurationSeeder completed");
        }

        public Task<(bool IsValid, string ErrorMessage)> ValidateConfigurationAsync(string templateKey, object value)
        {
            return Task.FromResult((true, string.Empty));
        }

        private List<ConfigurationCategoryDto> BuildConfigurationStructure(
            List<ConfigurationTemplate> templates, List<Configuration> configurations)
        {
            return templates.GroupBy(t => t.Category).Select(g => new ConfigurationCategoryDto
            {
                Category = g.Key,
                CategoryDisplayName = g.First().CategoryDisplayName,
                Groups = new List<ConfigurationGroupDto>
                {
                    new ConfigurationGroupDto
                    {
                        Group = "Default",
                        GroupDisplayName = "Settings",
                        Items = g.Select(t => new ConfigurationItemDto
                        {
                            Key = t.Key,
                            DisplayName = t.DisplayName,
                            DataType = t.DataType,
                            DefaultValue = DeserializeValue(t.DefaultValue),
                            Value = configurations.FirstOrDefault(c => c.Key == t.Key)?.Value != null 
                                ? DeserializeValue(configurations.First(c => c.Key == t.Key).Value)
                                : DeserializeValue(t.DefaultValue),
                            UIComponent = t.UIComponent,
                            DisplayOrder = t.DisplayOrder
                        }).ToList()
                    }
                }
            }).ToList();
        }

        private object? DeserializeValue(string? jsonValue)
        {
            if (string.IsNullOrEmpty(jsonValue)) return null;
            try { return JsonSerializer.Deserialize<object>(jsonValue); }
            catch { return jsonValue; }
        }
    }
} 