using WaterMeterManagement.DTOs;

namespace WaterMeterManagement.Services.Interfaces
{
    public interface IAddressService
    {
        /// <summary>
        /// Parse address using Google Places API and return structured address components
        /// </summary>
        /// <param name="address">Raw address input from user</param>
        /// <returns>Parsed address data with structured components</returns>
        Task<AddressParseResultDto> ParseAddressAsync(string address);

        /// <summary>
        /// Get address suggestions for autocomplete
        /// </summary>
        /// <param name="input">Partial address input</param>
        /// <param name="countryCode">Country code to limit search (default: NZ)</param>
        /// <returns>List of address suggestions</returns>
        Task<List<AddressSuggestionDto>> GetAddressSuggestionsAsync(string input, string countryCode = "NZ");
    }
}
