using WaterMeterManagement.DTOs;
using WaterMeterManagement.DTOs.Mobile;

namespace WaterMeterManagement.Services.Interfaces
{
    /// <summary>
    /// Simplified interface for meter reading operations - using existing DTOs only
    /// </summary>
    public interface IMeterReadingService
    {
        // Basic CRUD operations (existing methods)
        Task<MeterReadingSearchResultDto> GetReadingsAsync(MeterReadingSearchDto searchDto);
        Task<MeterReadingListDto?> GetReadingByIdAsync(int id);
        Task<MeterReadingListDto> CreateReadingAsync(CreateMeterReadingDto createDto, string createdBy);
        Task<MeterReadingListDto> UpdateReadingAsync(int id, UpdateMeterReadingDto updateDto, string updatedBy);
        Task<bool> DeleteReadingAsync(int id, string deletedBy);
        
        // Reading confirmation and correction (existing methods)
        Task<bool> ConfirmReadingAsync(int id, string confirmedBy);
        Task<bool> CorrectReadingAsync(int id, decimal correctedValue, string reason, string correctedBy);
        
        // Photo management (existing methods)  
        Task<List<ReadingPhotoDto>> GetReadingPhotosAsync(int readingId);
        Task<ReadingPhotoDto?> GetPhotoByIdAsync(int id);
        Task<List<ReadingPhotoDto>> SearchPhotosAsync(PhotoSearchDto searchDto);
        Task<ReadingPhotoDto> UploadPhotoAsync(int readingId, string fileName, string filePath, string uploadedBy);
        Task<bool> UpdatePhotoQualityAsync(int photoId, decimal qualityScore, string qualityStatus, string updatedBy);
        Task<bool> OverrideOCRResultAsync(int photoId, string correctedValue, string reason, string overriddenBy);
        Task<bool> DeletePhotoAsync(int photoId, string deletedBy);
        
        // Anomaly management (existing methods)
        Task<List<ReadingAnomalyDto>> GetAnomaliesAsync(AnomalySearchDto searchDto);
        Task<ReadingAnomalyDto?> GetAnomalyByIdAsync(int id);
        Task<ReadingAnomalyDto> CreateAnomalyAsync(CreateAnomalyDto createDto, string createdBy);
        Task<ReadingAnomalyDto> UpdateAnomalyAsync(int id, UpdateAnomalyDto updateDto, string updatedBy);
        Task<bool> AssignAnomalyAsync(int anomalyId, int assignedUserId, string assignedBy);
        Task<bool> ResolveAnomalyAsync(int anomalyId, string resolution, string resolutionType, string resolvedBy);
        Task<bool> DeleteAnomalyAsync(int id, string deletedBy);
        Task<List<ReadingAnomalyDto>> GetUnresolvedAnomaliesAsync();
        Task<List<ReadingAnomalyDto>> GetRecurringAnomaliesAsync();
        
        // Validation rules (existing methods)
        Task<List<ValidationRuleDto>> GetValidationRulesAsync();
        Task<ValidationRuleDto?> GetValidationRuleByIdAsync(int id);
        Task<ValidationRuleDto> CreateValidationRuleAsync(CreateValidationRuleDto createDto, string createdBy);
        Task<ValidationRuleDto> UpdateValidationRuleAsync(int id, CreateValidationRuleDto updateDto, string updatedBy);
        Task<bool> DeleteValidationRuleAsync(int id, string deletedBy);
        Task<bool> ActivateValidationRuleAsync(int id, bool isActive, string updatedBy);
        Task<List<ValidationRuleDto>> GetActiveValidationRulesAsync();
        
        // OCR management (existing methods)
        Task<List<OCRRecordDto>> GetOCRRecordsAsync(int readingId);
        Task<OCRRecordDto?> GetOCRRecordByIdAsync(int id);
        Task<OCRRecordDto> ProcessOCRAsync(int photoId, string processingEngine, string processedBy);
        Task<bool> VerifyOCRResultAsync(int ocrId, string correctedValue, string reason, string verifiedBy);
        Task<bool> MarkAsTrainingDataAsync(int ocrId, string trainingLabel, string updatedBy);
        
        // Workflow and validation (existing methods)
        Task<bool> ValidateReadingAsync(int readingId, string validatedBy);
        Task<List<ReadingAnomalyDto>> DetectAnomaliesAsync(int readingId);
        Task<bool> ProcessReadingWorkflowAsync(int readingId, string processedBy);
        Task<decimal> CalculateUsageAsync(int readingId);
        Task<bool> ApplyValidationRulesAsync(int readingId);
        
        // Statistics and reporting (existing methods)
        Task<MeterReadingStatisticsDto> GetReadingStatisticsAsync();
        Task<List<ReadingStatusCountDto>> GetReadingStatusCountsAsync();
        Task<List<AnomalyTypeCountDto>> GetAnomalyTypeCountsAsync();
        Task<decimal> GetOCRAccuracyRateAsync();
        Task<decimal> GetDataQualityScoreAsync();
        
        // Import/Export (existing methods)
        Task<byte[]> ExportReadingsAsync(MeterReadingSearchDto? searchDto = null);
        Task<(bool Success, string Message, int ImportedCount)> ImportReadingsAsync(Stream fileStream, string importedBy);
        Task<byte[]> ExportAnomaliesAsync(AnomalySearchDto? searchDto = null);
        Task<byte[]> ExportValidationRulesAsync();
        
        // Utility methods (existing methods)
        Task<List<string>> GetMeterNumbersAsync();
        Task<List<string>> GetAnomalyTypesAsync();
        Task<List<string>> GetValidationRuleTypesAsync();
        Task<List<MeterReadingListDto>> GetReadingHistoryAsync(string meterNumber, int? months = null);
        
        // Mobile reading operations - 修正为与控制器匹配的签名
        Task<ReadingValidationDto> ValidateMobileReadingAsync(MobileReadingDto reading, int userId);
        Task<ReadingResponseDto> SubmitMobileReadingAsync(MobileReadingDto request, int userId);
        Task<BatchReadingResponseDto> SubmitBatchMobileReadingsAsync(BatchReadingRequest request, int userId);
        Task<List<MobileReadingHistoryDto>> GetMobileReadingHistoryAsync(int meterId, int limit);
        Task<List<MobileReadingHistoryDto>> GetUserRecentReadingsAsync(int userId, int page, int limit);
        Task<ReadingResponseDto> UpdateMobileReadingAsync(int id, UpdateReadingRequest request, int userId);
        Task<bool> DeleteMobileReadingAsync(int id, int userId);
        Task<ReadingResponseDto> SubmitReadingAndCompleteTaskAsync(CompleteTaskRequest request, int userId);
        Task<MobileReadingStatsDto> GetUserReadingStatsAsync(int userId, DateTime? fromDate = null, DateTime? toDate = null);
        Task<OfflineReadingSyncResponse> SyncOfflineReadingsAsync(OfflineReadingSyncRequest request, int userId);
        
        // Enhanced mobile reading operations for GPS, OCR, and validation data
        Task<EnhancedReadingValidationDto> ValidateEnhancedMobileReadingAsync(MobileReadingDto reading, int userId);
        Task<EnhancedReadingResponseDto> SubmitEnhancedMobileReadingAsync(MobileReadingDto reading, int userId);
        
        // New methods for task-based operations
        Task<MobileReadingDto?> GetMobileReadingByTaskIdAsync(int taskId);
        Task<Models.MeterReading?> UpdateMobileReadingByTaskIdAsync(int taskId, MobileReadingDto readingDto, int userId);
    }
} 