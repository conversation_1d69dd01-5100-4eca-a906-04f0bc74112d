using WaterMeterManagement.DTOs;

namespace WaterMeterManagement.Services.Interfaces
{
    public interface IGpsService
    {
        Task<GpsCoordinateResultDto> GetCoordinatesFromAddressAsync(string address);
        Task<BatchGpsResultDto> GetCoordinatesFromAddressesBatchAsync(List<string> addresses);
        Task<GpsUpdateResultDto> UpdateWaterMeterGpsAsync(int waterMeterId);
        Task<BatchGpsUpdateResultDto> UpdateWaterMetersGpsBatchAsync(List<int> waterMeterIds);
        Task<BatchGpsUpdateResultDto> UpdateAllMissingGpsAsync();
        bool ValidateGpsCoordinates(decimal latitude, decimal longitude);
        Task<GpsServiceStatsDto> GetGpsServiceStatsAsync();
    }
}
