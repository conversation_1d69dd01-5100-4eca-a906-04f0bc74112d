using WaterMeterManagement.DTOs.Mobile;

namespace WaterMeterManagement.Services.Interfaces
{
    /// <summary>
    /// Interface for mobile location operations
    /// </summary>
    public interface ILocationService
    {
        // Location updates
        Task<(bool Success, string Message)> UpdateUserLocationAsync(int userId, LocationUpdateDto locationUpdate);
        Task<(double? Latitude, double? Longitude)> GetUserLastLocationAsync(int userId);
        
        // Location validation
        Task<bool> ValidateLocationAccuracyAsync(double? accuracy);
        Task<bool> IsLocationWithinServiceAreaAsync(double latitude, double longitude);
        
        // Distance calculations
        Task<double> CalculateDistanceKmAsync(double lat1, double lng1, double lat2, double lng2);
        Task<List<T>> FilterByDistanceAsync<T>(List<T> items, double userLat, double userLng, double maxDistanceKm, Func<T, (double Lat, double Lng)> locationSelector);
        
        // Location history
        Task<List<LocationUpdateDto>> GetUserLocationHistoryAsync(int userId, DateTime? fromDate = null, int limit = 100);
        
        // Geofencing
        Task<bool> IsUserInTaskAreaAsync(int userId, int taskId);
        Task<List<int>> GetTasksInUserAreaAsync(int userId, double radiusKm);
    }
} 