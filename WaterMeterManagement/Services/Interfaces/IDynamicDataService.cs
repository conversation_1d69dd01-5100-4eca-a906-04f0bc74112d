using WaterMeterManagement.DTOs;

namespace WaterMeterManagement.Services.Interfaces
{
    /// <summary>
    /// Dynamic data service interface
    /// </summary>
    public interface IDynamicDataService
    {
        Task<DynamicSelectResponseDto> GetOptionsAsync(string strategy, string dataSource, Dictionary<string, object> parameters);
        Task<List<string>> GetAvailableStrategiesAsync();
        Task<List<string>> GetAvailableDataSourcesAsync(string strategy);
    }

    /// <summary>
    /// Data source strategy interface
    /// </summary>
    public interface IDataSourceStrategy
    {
        string StrategyName { get; }
        Task<List<DynamicOptionDto>> GetOptionsAsync(string dataSource, Dictionary<string, object> parameters);
        Task<List<string>> GetAvailableDataSourcesAsync();
    }
}
