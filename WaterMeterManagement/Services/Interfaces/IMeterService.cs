using WaterMeterManagement.Models;

namespace WaterMeterManagement.Services.Interfaces
{
    /// <summary>
    /// Interface for meter operations
    /// </summary>
    public interface IMeterService
    {
        Task<WaterMeter?> GetMeterByIdAsync(int meterId);
        Task<List<WaterMeter>> GetMetersByUserAreaAsync(int userId, double latitude, double longitude, double radiusKm);
        Task<double?> GetLastReadingValueAsync(int meterId);
        Task<DateTime?> GetLastReadingDateAsync(int meterId);
        Task<bool> ValidateMeterAccessAsync(int meterId, int userId);
        Task<List<WaterMeter>> GetMetersInRouteAsync(int routeId);
        Task<double?> GetEffectiveLastReadingAsync(int meterId);
    }
} 