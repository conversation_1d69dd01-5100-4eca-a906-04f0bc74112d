namespace WaterMeterManagement.Services.Interfaces
{
    public interface ICloudflareR2Service
    {
        Task<string> UploadPhotoAsync(Stream photoStream, string fileName, string contentType);
        Task<string> UploadPhotoAsync(IFormFile photoFile);
        Task<bool> DeletePhotoAsync(string photoKey);
        Task<string> GeneratePresignedUrlAsync(string photoKey, TimeSpan expiration);
        Task<string> CreateThumbnailAsync(Stream originalStream, string fileName);
        Task<bool> PhotoExistsAsync(string photoKey);
        Task<long> GetPhotoSizeAsync(string photoKey);
        string GetPublicUrl(string photoKey);
        string ExtractKeyFromUrl(string photoUrl);
    }
}
