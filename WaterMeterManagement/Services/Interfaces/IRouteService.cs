using WaterMeterManagement.DTOs;

namespace WaterMeterManagement.Services.Interfaces
{
    public interface IRouteService
    {
        // CRUD operations
        Task<(List<RouteListDto> routes, int totalCount)> GetRoutesAsync(RouteSearchDto searchDto);
        Task<RouteDto?> GetRouteByIdAsync(int id);
        Task<RouteDto> CreateRouteAsync(CreateRouteDto createDto);
        Task<RouteDto?> UpdateRouteAsync(int id, UpdateRouteDto updateDto);
        Task<bool> DeleteRouteAsync(int id);
        Task<RouteDto?> DuplicateRouteAsync(int sourceRouteId, DuplicateRouteDto duplicateDto);

        // Route optimization
        Task<RouteOptimizationResultDto> OptimizeRouteAsync(RouteOptimizationDto optimizationDto);
        Task<bool> ApplyOptimizationAsync(int routeId, List<RouteWaypointDto> optimizedWaypoints);

        // Waypoint management
        Task<List<RouteWaypointDto>> GetRouteWaypointsAsync(int routeId);
        Task<RouteWaypointDto> AddWaypointAsync(int routeId, CreateRouteWaypointDto waypointDto);
        Task<bool> UpdateWaypointAsync(int waypointId, CreateRouteWaypointDto waypointDto);
        Task<bool> RemoveWaypointAsync(int waypointId);
        Task<bool> ReorderWaypointsAsync(int routeId, List<int> waypointIds);

        // Template management
        Task<List<RouteListDto>> GetRouteTemplatesAsync(string? category = null);
        Task<RouteDto> CreateRouteFromTemplateAsync(int templateId, CreateRouteDto routeDto);
        Task<bool> SaveAsTemplateAsync(int routeId, string templateName, string? category = null);

        // Distance and time calculations
        Task<decimal?> CalculateRouteDistanceAsync(int routeId);
        Task<int?> CalculateRouteTimeAsync(int routeId);
        Task<decimal?> CalculateDistanceBetweenPointsAsync(decimal lat1, decimal lon1, decimal lat2, decimal lon2);

        // Route validation
        Task<List<string>> ValidateRouteAsync(int routeId);
        Task<bool> IsRouteOptimalAsync(int routeId);

        // Statistics and reporting
        Task<RouteStatisticsDto> GetRouteStatisticsAsync();
        Task<Dictionary<string, object>> GetRouteDashboardDataAsync();
        Task<List<RouteListDto>> GetRoutesByAssigneeAsync(int assigneeUserId);

        // Map and location services
        Task<string> GenerateRouteGeometryAsync(int routeId);
        Task<byte[]> ExportRouteMapAsync(int routeId);
        Task<List<RouteWaypointDto>> GetNearbyWaypointsAsync(decimal latitude, decimal longitude, decimal radiusKm);
        
        // AMS Integration methods
        Task<List<RouteListDto>> GetAmsRoutesAsync(int page = 1, int pageSize = 50);
        Task<List<RouteListDto>> GetRoutesByTownshipAsync(string township);
        Task<bool> SyncRoutesWithAmsAsync();
        Task<bool> AssignMetersToRouteAsync(int routeId, RouteAssignmentDto assignmentDto);
        Task<bool> RemoveMetersFromRouteAsync(int routeId, List<string> meterAssetIds);
        Task<List<object>> GetRouteMetersAsync(int routeId, int page = 1, int pageSize = 50);
        Task<int> GetRouteMeterCountAsync(int routeId);
        Task<bool> AutoAssignMetersAsync(int routeId, AutoAssignmentCriteriaDto criteria);
        Task<object> GetRouteReadingScheduleAsync(int routeId, DateTime startDate, DateTime endDate);
        Task<object> AnalyzeRouteCoverageAsync(RouteCoverageRequestDto request);
        Task<List<object>> GetUnassignedMetersAsync(int page = 1, int pageSize = 50);
        Task<bool> UpdateWaypointCoordinatesAsync(int waypointId, UpdateWaypointCoordinatesDto coordinatesDto);
        Task<BatchUpdateResult> BatchUpdateWaypointCoordinatesAsync(int routeId, BatchUpdateCoordinatesDto batchDto);
    }
} 