using WaterMeterManagement.DTOs;

namespace WaterMeterManagement.Services.Interfaces
{
    /// <summary>
    /// Staff synchronization service interface
    /// </summary>
    public interface IStaffSyncService
    {
        /// <summary>
        /// Synchronize staff data from Workbench to local database
        /// </summary>
        /// <returns>Synchronization result</returns>
        Task<StaffSyncResponseDto> SyncStaffDataAsync();
    }
} 