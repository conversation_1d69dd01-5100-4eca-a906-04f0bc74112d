using WaterMeterManagement.DTOs.UserRole;

namespace WaterMeterManagement.Services.Interfaces
{
    public interface IUserRoleService
    {
        /// <summary>
        /// Get all roles assigned to a user
        /// </summary>
        Task<List<UserRoleDto>> GetUserRolesAsync(int userId);

        /// <summary>
        /// Get all users assigned to a role
        /// </summary>
        Task<List<UserRoleDto>> GetRoleUsersAsync(int roleId);

        /// <summary>
        /// Assign roles to a user (replaces existing assignments)
        /// </summary>
        Task<UserRoleAssignmentResultDto> AssignRolesToUserAsync(int userId, List<int> roleIds, string assignedBy);

        /// <summary>
        /// Add a single role to a user (without removing existing roles)
        /// </summary>
        Task<RoleAssignmentDetailDto> AddRoleToUserAsync(int userId, int roleId, string assignedBy);

        /// <summary>
        /// Remove a role from a user
        /// </summary>
        Task RemoveRoleFromUserAsync(int userId, int roleId);

        /// <summary>
        /// Check if a user has a specific role
        /// </summary>
        Task<bool> UserHasRoleAsync(int userId, int roleId);

        /// <summary>
        /// Get all available roles that can be assigned
        /// </summary>
        Task<List<Models.Role>> GetAvailableRolesAsync();
    }
}
