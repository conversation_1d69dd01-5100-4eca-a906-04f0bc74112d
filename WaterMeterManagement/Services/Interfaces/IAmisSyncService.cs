using WaterMeterManagement.DTOs;

namespace WaterMeterManagement.Services.Interfaces
{
    public interface IAmisSyncService
    {
        Task<(List<AmisSyncListDto> Syncs, int TotalCount)> GetSyncHistoryAsync(AmisSyncSearchDto searchDto);
        Task<AmisSyncDto?> GetSyncByIdAsync(int id);
        Task<AmisSyncDto> StartManualSyncAsync(ManualSyncRequestDto request, string triggerBy);
        Task<bool> CancelSyncAsync(int syncId, string cancelledBy);
        Task<SyncProgressDto?> GetSyncProgressAsync(int syncId);
        Task<List<AmisSyncErrorDto>> GetSyncErrorsAsync(int syncId);
        Task<bool> RetrySyncAsync(int syncId, string retryBy);
        Task<bool> ResolveSyncErrorAsync(int errorId, string resolutionNotes, string resolvedBy);
        Task<AmisSyncConfigurationDto> GetSyncConfigurationAsync();
        Task<bool> UpdateSyncConfigurationAsync(AmisSyncConfigurationDto configuration);
        Task<bool> TestAmisConnectionAsync();
        Task<List<AmisSyncDto>> GetActiveSyncsAsync();
        Task<List<AmisSyncErrorDto>> GetUnresolvedErrorsAsync();
        Task<Dictionary<string, object>> GetSyncStatisticsAsync();
        Task CleanupOldSyncLogsAsync(int retentionDays = 30);
    }
} 