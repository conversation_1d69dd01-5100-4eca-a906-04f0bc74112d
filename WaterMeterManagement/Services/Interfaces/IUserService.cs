using WaterMeterManagement.DTOs.Mobile;

namespace WaterMeterManagement.Services.Interfaces
{
    /// <summary>
    /// Interface for mobile user operations
    /// </summary>
    public interface IUserService
    {
        // User profile and workload
        Task<MobileUserWorkloadDto?> GetMobileUserWorkloadAsync(int userId);
        Task<MobileUserStatsDto> GetMobileUserStatsAsync(int userId);
        Task<MobileUserProfileDto?> GetMobileUserProfileAsync(int userId);
        
        // User status management
        Task<(bool Success, string Message)> UpdateUserStatusAsync(int userId, UserStatusUpdateDto statusUpdate);
        
        // App preferences
        Task<(bool Success, string Message)> UpdateMobilePreferencesAsync(int userId, MobileAppPreferencesDto preferences);
        
        // Device management
        Task<(bool Success, string Message)> RegisterMobileDeviceAsync(int userId, MobileDeviceDto device);
        Task<List<MobileDeviceDto>> GetUserDevicesAsync(int userId);
        Task<(bool Success, string Message)> DeactivateDeviceAsync(int userId, string deviceId);
        
        // Performance and achievements
        Task<List<UserLeaderboardDto>> GetPerformanceLeaderboardAsync(string period, int limit);
        Task<List<UserAchievementDto>> GetUserAchievementsAsync(int userId);
        
        // Location and team
        Task<List<NearbyUserDto>> GetNearbyTeamMembersAsync(int userId, double latitude, double longitude, double radiusKm);
        
        // Sync operations
        Task<MobileSyncResponseDto> ProcessMobileSyncAsync(int userId, MobileSyncRequestDto syncRequest);
    }
} 