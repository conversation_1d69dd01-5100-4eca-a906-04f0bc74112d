using WaterMeterManagement.DTOs;

namespace WaterMeterManagement.Services.Interfaces
{
    public interface IWaterMeterService
    {
        Task<(List<WaterMeterListDto> Meters, int TotalCount)> GetWaterMetersAsync(WaterMeterSearchDto searchDto);
        Task<WaterMeterDto?> GetWaterMeterByIdAsync(int id);
        Task<WaterMeterDto?> GetWaterMeterBySerialNumberAsync(string serialNumber);
        Task<WaterMeterDto> CreateWaterMeterAsync(CreateWaterMeterDto createDto);
        Task<WaterMeterDto?> UpdateWaterMeterAsync(int id, UpdateWaterMeterDto updateDto);
        Task<bool> DeleteWaterMeterAsync(int id);
        Task<WaterMeterImportResultDto> ImportWaterMetersAsync(List<WaterMeterImportDto> importData, string fileName);
        Task<WaterMeterImportResultDto> ImportFromCsvAsync(Stream csvStream, string fileName);
        Task<WaterMeterImportResultDto> ImportFromExcelAsync(Stream excelStream, string fileName);
        Task<List<WaterMeterImportDto>> ValidateImportDataAsync(List<WaterMeterImportDto> importData);
        Task<bool> ValidateSerialNumberAsync(string serialNumber, int? excludeId = null);
        Task UpdateMeterReadingAsync(int meterId, decimal readingValue, DateTime readingDate, string dataSource = "Manual");
        Task<List<WaterMeterListDto>> GetMetersByLocationAsync(string location);
        Task<List<WaterMeterListDto>> GetMetersByStatusAsync(string status);
        Task<List<WaterMeterListDto>> GetMetersDueForMaintenanceAsync();
        Task<List<WaterMeterListDto>> GetMetersWithLowBatteryAsync(int? threshold = 20);
        
        // AMS Integration methods
        Task<WaterMeterDto?> GetWaterMeterByAssetIdAsync(string assetId);
        Task<WaterMeterDto?> GetWaterMeterByAccountNumberAsync(string accountNumber);
        Task<List<WaterMeterListDto>> GetMetersByTownshipAsync(string township, int page = 1, int pageSize = 50);
        Task<List<WaterMeterListDto>> GetMetersByRoadAsync(string roadName, int page = 1, int pageSize = 50);
        Task<List<WaterMeterListDto>> GetMetersByRouteAsync(string routeName, int page = 1, int pageSize = 50);
        Task<List<WaterMeterListDto>> GetCantReadMetersAsync(int page = 1, int pageSize = 50);
        Task<bool> SyncMeterWithAmsAsync(int meterId);
        Task<bool> BulkSyncWithAmsAsync(List<int> meterIds);
        Task<List<WaterMeterListDto>> GetAmsAnomaliesAsync(int page = 1, int pageSize = 50);
        Task<object> GetConsumptionAnalysisAsync(string assetId, DateTime startDate, DateTime endDate);
        Task<bool> FlagMeterAnomalyAsync(int meterId, AnomalyFlagDto flagDto);
        Task<byte[]> GenerateWorkPackageTemplateAsync(List<string> meterIds);
        Task<WorkPackageImportResultDto> ImportWorkPackagesFromExcelAsync(Stream excelStream, string fileName);
        Task<byte[]> ExportWaterMetersAsync(WaterMeterSearchDto? searchDto = null);
        Task<byte[]> ExportWaterMetersCsvAsync(WaterMeterSearchDto? searchDto = null);
    }
} 