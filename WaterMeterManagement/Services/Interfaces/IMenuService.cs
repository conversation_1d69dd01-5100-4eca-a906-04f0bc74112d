using System.Collections.Generic;
using System.Threading.Tasks;
using WaterMeterManagement.Models;

namespace WaterMeterManagement.Services.Interfaces
{
    public interface IMenuService
    {
        Task<IEnumerable<Menu>> GetAllMenusAsync();
        Task<Menu> GetMenuByIdAsync(int id);
        Task<Menu> GetMenuByCodeAsync(string code);
        Task<IEnumerable<Menu>> GetUserMenusAsync(int userId);
        Task<Menu> CreateMenuAsync(Menu menu);
        Task<Menu> UpdateMenuAsync(Menu menu);
        Task DeleteMenuAsync(int id);
        Task<bool> ValidateMenuCodeAsync(string code, int? excludeId = null);
    }
} 