using WaterMeterManagement.Models;

namespace WaterMeterManagement.Services.Interfaces
{
    public interface IRoleService
    {
        Task<IEnumerable<Role>> GetAllRolesAsync();
        Task<Role?> GetRoleByIdAsync(int id);
        Task<Role> CreateRoleAsync(Role role);
        Task<Role> UpdateRoleAsync(Role role);
        Task<bool> DeleteRoleAsync(int id);
        Task<bool> RoleExistsAsync(string name);
        Task AssignPermissionsToRoleAsync(int roleId, List<int> permissionIds);
        Task<IEnumerable<Permission>> GetRolePermissionsAsync(int roleId);
        Task<IEnumerable<Permission>> GetAllPermissionsAsync();
    }
} 