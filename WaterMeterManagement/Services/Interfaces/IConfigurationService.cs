using WaterMeterManagement.DTOs;

namespace WaterMeterManagement.Services.Interfaces
{
    /// <summary>
    /// Configuration management service interface
    /// </summary>
    public interface IConfigurationService
    {
        /// <summary>
        /// Get configurations by scope and category with inheritance support
        /// </summary>
        Task<List<ConfigurationCategoryDto>> GetConfigurationsAsync(GetConfigurationRequest request);

        /// <summary>
        /// Update multiple configurations in bulk
        /// </summary>
        Task<ConfigurationUpdateResponse> UpdateConfigurationsAsync(UpdateConfigurationRequest request, int? currentUserId = null);

        /// <summary>
        /// Get configuration templates for a scope
        /// </summary>
        Task<List<ConfigurationCategoryDto>> GetConfigurationTemplatesAsync(string scope);

        /// <summary>
        /// Get a single configuration value
        /// </summary>
        Task<T?> GetConfigurationValueAsync<T>(string scope, string category, string key, int? userId = null, int? roleId = null);

        /// <summary>
        /// Set a single configuration value
        /// </summary>
        Task<bool> SetConfigurationValueAsync<T>(string scope, string category, string key, T value, int? userId = null, int? roleId = null);

        /// <summary>
        /// Reset configurations to default values
        /// </summary>
        Task<ConfigurationUpdateResponse> ResetToDefaultsAsync(string scope, string category, int? userId = null, int? roleId = null);

        /// <summary>
        /// Initialize system default configurations
        /// </summary>
        Task InitializeDefaultConfigurationsAsync();

        /// <summary>
        /// Validate configuration value against rules
        /// </summary>
        Task<(bool IsValid, string ErrorMessage)> ValidateConfigurationAsync(string templateKey, object value);
    }
} 