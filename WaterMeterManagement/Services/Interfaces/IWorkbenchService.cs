using WaterMeterManagement.DTOs;

namespace WaterMeterManagement.Services.Interfaces
{
    /// <summary>
    /// Workbench API service interface
    /// </summary>
    public interface IWorkbenchService
    {
        /// <summary>
        /// Authenticate user credentials through Workbench API
        /// </summary>
        /// <param name="username">Username</param>
        /// <param name="password">Password</param>
        /// <returns>Workbench user information</returns>
        Task<WorkbenchLoginResponseDto?> AuthenticateAsync(string username, string password);
        
        /// <summary>
        /// Fetch staff data from Workbench API
        /// </summary>
        /// <param name="page">Page number (starting from 1)</param>
        /// <param name="pageSize">Number of records per page</param>
        /// <returns>Workbench staff data response</returns>
        Task<WorkbenchStaffApiResponse?> FetchStaffDataAsync(int page = 1, int pageSize = 100);
    }
} 