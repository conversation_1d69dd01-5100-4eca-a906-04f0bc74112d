using Microsoft.AspNetCore.Http;
using WaterMeterManagement.DTOs.Mobile;
using WaterMeterManagement.DTOs;

namespace WaterMeterManagement.Services.Interfaces
{
    /// <summary>
    /// Interface for mobile photo/attachment operations
    /// </summary>
    public interface IPhotoService
    {
        // Photo upload operations
        Task<List<PhotoUploadResult>> UploadMobilePhotosAsync(int readingId, List<MobilePhotoDto> photos, int userId);
        Task<PhotoUploadResult> UploadSinglePhotoAsync(int readingId, MobilePhotoDto photo, int userId);
        Task<PhotoUploadResult> UploadFormFileAsync(IFormFile photo, int userId);
        
        // Photo management
        Task<(bool Success, string Message)> DeletePhotoAsync(int photoId, int userId);
        Task<List<string>> GetPhotoUrlsAsync(int readingId);
        Task<List<ReadingPhotoDto>> GetReadingPhotosAsync(int readingId);
        
        // Photo validation
        Task<(bool IsValid, List<string> Errors)> ValidatePhotoAsync(MobilePhotoDto photo);
        Task<bool> ValidatePhotoSizeAsync(int fileSizeBytes);
        
        // Photo processing
        Task<string?> ProcessPhotoAsync(MobilePhotoDto photo, string uploadPath);
        Task<string?> CreateThumbnailAsync(string originalPath);
        
        // Batch operations
        Task<List<PhotoUploadResult>> ProcessBatchPhotosAsync(List<MobilePhotoDto> photos, int readingId, int userId);
    }
} 