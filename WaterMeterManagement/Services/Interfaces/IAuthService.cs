using WaterMeterManagement.DTOs;

namespace WaterMeterManagement.Services.Interfaces
{
    /// <summary>
    /// Authentication service interface
    /// </summary>
    public interface IAuthService
    {
        /// <summary>
        /// User login
        /// </summary>
        /// <param name="loginRequest">Login request</param>
        /// <returns>Login response</returns>
        Task<LoginResponseDto> LoginAsync(LoginRequestDto loginRequest);

        /// <summary>
        /// Generate JWT Token
        /// </summary>
        /// <param name="user">User information</param>
        /// <returns>JWT Token</returns>
        string GenerateJwtToken(UserDto user);

        /// <summary>
        /// Validate JWT Token
        /// </summary>
        /// <param name="token">JWT <PERSON></param>
        /// <returns>User information</returns>
        UserDto? ValidateJwtToken(string token);
    }
} 