using WaterMeterManagement.DTOs;

namespace WaterMeterManagement.Services.Interfaces
{
    /// <summary>
    /// Work Package Service Interface
    /// Defines contracts for work package management operations
    /// </summary>
    public interface IWorkPackageService
    {
        #region Basic CRUD Operations

        /// <summary>
        /// Get work packages with pagination and filtering
        /// </summary>
        Task<(List<WorkPackageListDto> Items, int TotalCount)> GetWorkPackagesAsync(
            int page = 1, 
            int pageSize = 10, 
            WorkPackageSearchDto? search = null);

        /// <summary>
        /// Get work package by ID with full details
        /// </summary>
        Task<WorkPackageDto?> GetWorkPackageByIdAsync(int id);

        /// <summary>
        /// Create a new work package
        /// </summary>
        Task<WorkPackageDto> CreateWorkPackageAsync(CreateWorkPackageDto createDto);

        /// <summary>
        /// Update an existing work package
        /// </summary>
        Task<WorkPackageDto> UpdateWorkPackageAsync(UpdateWorkPackageDto updateDto);

        /// <summary>
        /// Delete a work package
        /// </summary>
        Task<bool> DeleteWorkPackageAsync(int id, string deletedBy);

        /// <summary>
        /// Validate work package data before creation or update
        /// </summary>
        Task<List<string>> ValidateWorkPackageAsync(CreateWorkPackageDto workPackage);

        #endregion

        #region Status Management

        /// <summary>
        /// Update work package status
        /// </summary>
        Task<bool> UpdateWorkPackageStatusAsync(int id, string status, string updatedBy);

        /// <summary>
        /// Start work package execution
        /// </summary>
        Task<bool> StartWorkPackageAsync(int id, string startedBy);

        /// <summary>
        /// Complete work package
        /// </summary>
        Task<bool> CompleteWorkPackageAsync(int id, string completedBy);

        /// <summary>
        /// Cancel work package
        /// </summary>
        Task<bool> CancelWorkPackageAsync(int id, string cancelledBy, string reason);

        /// <summary>
        /// Check if work package can be started
        /// </summary>
        Task<(bool CanStart, List<string> Reasons)> CanStartWorkPackageAsync(int workPackageId);

        /// <summary>
        /// Check if work package can be completed
        /// </summary>
        Task<(bool CanComplete, List<string> Reasons)> CanCompleteWorkPackageAsync(int workPackageId);

        #endregion

        #region Work Package Items Management

        /// <summary>
        /// Get work package items with meter details
        /// </summary>
        Task<List<WorkPackageItemDto>> GetWorkPackageItemsAsync(int workPackageId);

        /// <summary>
        /// Add meters to work package
        /// </summary>
        Task<bool> AddMetersToWorkPackageAsync(int workPackageId, List<int> meterIds, string addedBy);

        /// <summary>
        /// Remove meters from work package
        /// </summary>
        Task<bool> RemoveMetersFromWorkPackageAsync(int workPackageId, List<int> meterIds, string removedBy);

        /// <summary>
        /// Update item execution order
        /// </summary>
        Task<bool> UpdateItemOrderAsync(int workPackageId, List<int> itemIds, string updatedBy);

        /// <summary>
        /// Update individual item status
        /// </summary>
        Task<bool> UpdateItemStatusAsync(int itemId, string status, string updatedBy);

        /// <summary>
        /// Get suggested meters for work package based on area and criteria
        /// </summary>
        Task<List<WaterMeterDto>> GetSuggestedMetersAsync(string serviceArea, string? subArea = null, int maxCount = 100);

        #endregion

        #region Assignment Management

        /// <summary>
        /// Get work package assignments
        /// </summary>
        Task<List<WorkPackageAssignmentDto>> GetWorkPackageAssignmentsAsync(int workPackageId);

        /// <summary>
        /// Assign work package to users
        /// </summary>
        Task<bool> AssignWorkPackageAsync(int workPackageId, List<CreateWorkPackageAssignmentDto> assignments);

        /// <summary>
        /// Remove assignment from work package
        /// </summary>
        Task<bool> RemoveAssignmentAsync(int assignmentId, string removedBy, string reason);

        /// <summary>
        /// Accept assignment by user
        /// </summary>
        Task<bool> AcceptAssignmentAsync(int assignmentId, string acceptedBy);

        /// <summary>
        /// Reject assignment by user
        /// </summary>
        Task<bool> RejectAssignmentAsync(int assignmentId, string rejectedBy, string reason);

        /// <summary>
        /// Update assignment progress
        /// </summary>
        Task<bool> UpdateAssignmentProgressAsync(int assignmentId, int completedMeterCount, string updatedBy);

        /// <summary>
        /// Send assignment notifications to users
        /// </summary>
        Task<bool> SendAssignmentNotificationsAsync(int workPackageId);

        #endregion

        #region Activation Management

        /// <summary>
        /// Activate work package with validation and optional task generation
        /// </summary>
        Task<WorkPackageActivationResultDto> ActivateWorkPackageAsync(int workPackageId, string activatedBy, bool generateTasks = true);

        #endregion

        #region Task Generation

        /// <summary>
        /// Generate tasks from work package items
        /// </summary>
        Task<TaskGenerationResultDto> GenerateTasksAsync(int workPackageId, string createdBy);

        /// <summary>
        /// Regenerate tasks from work package items
        /// </summary>
        Task<TaskGenerationResultDto> RegenerateTasksAsync(int workPackageId, string createdBy, bool deleteExisting = true);

        #endregion

        #region Template Management

        /// <summary>
        /// Get available work package templates
        /// </summary>
        Task<List<WorkPackageListDto>> GetTemplatesAsync(string? category = null);

        /// <summary>
        /// Create work package from template
        /// </summary>
        Task<WorkPackageDto> CreateFromTemplateAsync(int templateId, CreateWorkPackageDto createDto);

        /// <summary>
        /// Save work package as template
        /// </summary>
        Task<WorkPackageDto> SaveAsTemplateAsync(int workPackageId, string templateName, string category, string createdBy);

        #endregion

        #region Recurring Work Packages

        /// <summary>
        /// Get recurring work packages that need processing
        /// </summary>
        Task<List<WorkPackageListDto>> GetRecurringWorkPackagesAsync();

        /// <summary>
        /// Process recurring work packages and create new instances
        /// </summary>
        Task<List<WorkPackageDto>> ProcessRecurringWorkPackagesAsync(string createdBy);

        /// <summary>
        /// Update recurrence settings
        /// </summary>
        Task<bool> UpdateRecurrenceAsync(int workPackageId, string pattern, int interval, DateTime nextExecution, string updatedBy);

        #endregion

        #region Import/Export

        /// <summary>
        /// Import work packages from CSV file
        /// </summary>
        Task<WorkPackageImportResultDto> ImportFromCsvAsync(Stream csvStream, string importedBy);

        /// <summary>
        /// Export work packages to CSV
        /// </summary>
        Task<byte[]> ExportToCsvAsync(WorkPackageSearchDto? search = null);

        /// <summary>
        /// Import work packages from AMS Excel file
        /// </summary>
        Task<WorkPackageImportResultDto> ImportFromAmsExcelAsync(Stream excelStream, string importedBy);

        /// <summary>
        /// Generate blank work package template
        /// </summary>
        Task<byte[]> GenerateTemplateAsync();

        /// <summary>
        /// Export filtered template based on criteria
        /// </summary>
        Task<byte[]> ExportFilteredTemplateAsync(object filterRequest);

        /// <summary>
        /// Import work packages from Excel file
        /// </summary>
        Task<ImportResultDto> ImportWorkPackagesAsync(IFormFile file, string importedBy);

        /// <summary>
        /// Export work packages to Excel
        /// </summary>
        Task<byte[]> ExportWorkPackagesAsync(WorkPackageSearchDto? search = null);

        #endregion

        #region Batch Operations

        /// <summary>
        /// Batch activate work packages
        /// </summary>
        Task<BatchOperationResultDto> BatchActivateAsync(List<int> ids, string activatedBy);

        /// <summary>
        /// Batch pause work packages
        /// </summary>
        Task<BatchOperationResultDto> BatchPauseAsync(List<int> ids, string pausedBy);

        /// <summary>
        /// Batch cancel work packages
        /// </summary>
        Task<BatchOperationResultDto> BatchCancelAsync(List<int> ids, string? reason, string cancelledBy);

        /// <summary>
        /// Batch delete work packages
        /// </summary>
        Task<BatchOperationResultDto> BatchDeleteAsync(List<int> ids, string deletedBy);

        #endregion

        #region Analytics & Reporting

        /// <summary>
        /// Get work package statistics
        /// </summary>
        Task<WorkPackageStatisticsDto> GetStatisticsAsync(WorkPackageSearchDto? filter = null);

        /// <summary>
        /// Get user workload analysis
        /// </summary>
        Task<List<UserWorkloadDto>> GetUserWorkloadAsync(DateTime? dateFrom = null, DateTime? dateTo = null);

        /// <summary>
        /// Get performance metrics
        /// </summary>
        Task<List<WorkPackagePerformanceDto>> GetPerformanceMetricsAsync(int? workPackageId = null, DateTime? dateFrom = null, DateTime? dateTo = null);

        #endregion

        #region Notifications

        /// <summary>
        /// Send overdue reminders for work packages
        /// </summary>
        Task<bool> SendOverdueRemindersAsync();

        #endregion

        #region History & Audit

        /// <summary>
        /// Get work package history
        /// </summary>
        Task<List<WorkPackageHistoryDto>> GetHistoryAsync(int workPackageId);

        /// <summary>
        /// Add history record
        /// </summary>
        Task AddHistoryAsync(int workPackageId, string action, string description, string actionBy, string? additionalData = null);

        #endregion
    }
}
