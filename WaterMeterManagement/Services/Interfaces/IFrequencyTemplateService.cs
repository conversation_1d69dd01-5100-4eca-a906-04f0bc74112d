using WaterMeterManagement.DTOs;

namespace WaterMeterManagement.Services.Interfaces
{
    public interface IFrequencyTemplateService
    {
        // CRUD operations
        Task<(List<FrequencyTemplateListDto> templates, int totalCount)> GetFrequencyTemplatesAsync(FrequencyTemplateSearchDto searchDto);
        Task<FrequencyTemplateDto?> GetFrequencyTemplateByIdAsync(int id);
        Task<FrequencyTemplateDto> CreateFrequencyTemplateAsync(CreateFrequencyTemplateDto createDto);
        Task<FrequencyTemplateDto?> UpdateFrequencyTemplateAsync(int id, UpdateFrequencyTemplateDto updateDto);
        Task<bool> DeleteFrequencyTemplateAsync(int id);
        Task<FrequencyTemplateDto?> DuplicateFrequencyTemplateAsync(int templateId, string newName);

        // Template usage
        Task<bool> UseTemplateAsync(int templateId);
        Task<FrequencyCalculationResultDto> CalculateFrequencyAsync(FrequencyCalculationDto calculationDto);
        Task<List<DateTime>> GetNextOccurrencesAsync(int templateId, DateTime startDate, int count = 10);

        // Default templates
        Task<List<FrequencyTemplateListDto>> GetDefaultTemplatesAsync();
        Task<bool> SetAsDefaultAsync(int templateId, bool isDefault = true);
        Task<FrequencyTemplateDto?> GetDefaultTemplateByTypeAsync(string frequencyType);

        // Template categories
        Task<List<string>> GetCategoriesAsync();
        Task<List<FrequencyTemplateListDto>> GetTemplatesByCategoryAsync(string category);

        // Template validation
        Task<List<string>> ValidateTemplateAsync(CreateFrequencyTemplateDto templateDto);
        Task<bool> IsTemplateUsedAsync(int templateId);

        // Statistics and reporting
        Task<FrequencyTemplateStatisticsDto> GetFrequencyTemplateStatisticsAsync();
        Task<List<FrequencyTemplateListDto>> GetMostUsedTemplatesAsync(int count = 10);
        Task<Dictionary<string, int>> GetUsageStatsByTypeAsync();

        // Import/Export
        Task<byte[]> ExportTemplatesToCsvAsync();
        Task<byte[]> ExportTemplatesToExcelAsync();
        Task<int> ImportTemplatesFromCsvAsync(Stream csvStream);
        Task<int> ImportTemplatesFromExcelAsync(Stream excelStream);

        // Frequency calculations and utilities
        Task<string> GenerateFrequencyDescriptionAsync(FrequencyTemplateDto template);
        Task<bool> IsValidFrequencyConfigurationAsync(string frequencyType, int intervalValue, string? intervalUnit);
        Task<List<DateTime>> CalculateDateRangeAsync(DateTime startDate, DateTime endDate, FrequencyTemplateDto template);
    }
} 