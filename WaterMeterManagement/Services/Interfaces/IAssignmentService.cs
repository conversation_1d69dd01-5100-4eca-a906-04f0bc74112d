using WaterMeterManagement.DTOs.Mobile;

namespace WaterMeterManagement.Services.Interfaces
{
    /// <summary>
    /// Interface for mobile task assignment operations
    /// </summary>
    public interface IAssignmentService
    {
        // Assignment acceptance/rejection
        Task<(bool Success, string Message)> AcceptMobileAssignmentAsync(int taskId, int userId);
        Task<(bool Success, string Message)> RejectMobileAssignmentAsync(int taskId, int userId, string reason);
        
        // Assignment validation
        Task<bool> CanUserAcceptAssignmentAsync(int taskId, int userId);
        Task<List<string>> ValidateAssignmentConstraintsAsync(int taskId, int userId);
        
        // Assignment notifications
        Task<bool> SendAssignmentNotificationAsync(int taskId, int userId, string notificationType);
        Task<List<int>> GetPendingAssignmentNotificationsAsync(int userId);
        
        // Assignment history
        Task<List<MobileTaskAssignmentHistoryDto>> GetUserAssignmentHistoryAsync(int userId, int page = 1, int limit = 20);
        
        // Assignment analytics
        Task<(int Accepted, int Rejected, double AcceptanceRate)> GetUserAssignmentStatsAsync(int userId, DateTime? fromDate = null);
    }
} 