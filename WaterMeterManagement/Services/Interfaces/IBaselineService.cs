using WaterMeterManagement.DTOs;

namespace WaterMeterManagement.Services.Interfaces
{
    public interface IBaselineService
    {
        Task<(List<BaselineRecordListDto> Records, int TotalCount)> GetBaselineRecordsAsync(BaselineSearchDto searchDto);
        Task<BaselineRecordDto?> GetBaselineRecordByIdAsync(int id);
        Task<BaselineRecordDto> CreateBaselineRecordAsync(CreateBaselineRecordDto createDto);
        Task<BaselineRecordDto?> UpdateBaselineRecordAsync(int id, UpdateBaselineRecordDto updateDto);
        Task<bool> DeleteBaselineRecordAsync(int id);
        Task<BaselineImportResultDto> ImportBaselineRecordsAsync(List<BaselineImportDto> importData, string fileName);
        Task<BaselineImportResultDto> ImportFromCsvAsync(Stream csvStream, string fileName);
        Task<BaselineImportResultDto> ImportFromExcelAsync(Stream excelStream, string fileName);
        Task<List<BaselineImportDto>> ValidateImportDataAsync(List<BaselineImportDto> importData);
        Task<BaselineHistoryDto> GetMeterBaselineHistoryAsync(int meterId);
        Task<List<BaselineAnomalyDto>> GetAnomalousBaselinesAsync();
        Task<List<BaselineRecordListDto>> GetUnvalidatedBaselinesAsync();
        Task<bool> ValidateBaselineAsync(BaselineValidationDto validationDto);
        Task<bool> CorrectBaselineAsync(BaselineCorrectionDto correctionDto, string correctedBy);
        Task<List<BaselineRecordListDto>> GetBaselinesByImportBatchAsync(string importBatch);
        Task<Dictionary<string, object>> GetBaselineStatisticsAsync();
        Task<List<BaselineRecordListDto>> GetRecentBaselinesAsync(int days = 7);
        Task<bool> MarkBaselineAsAnomalousAsync(int baselineId, string description, string markedBy);
        Task CalculateVariancesAsync(int meterId);
        Task<List<BaselineRecordDto>> GetSupersededBaselinesAsync(int meterId);
        
        // New methods for export, template and batch operations
        Task<byte[]> ExportBaselinesAsync(List<BaselineRecordListDto> baselines);
        Task<byte[]> GenerateExcelTemplateAsync();
        Task<BatchValidationResultDto> BatchValidateBaselinesAsync(BatchValidationDto batchDto);
        
        // SDC import related methods
        Task<BaselineImportResultDto> ImportFromSdcFileAsync(Stream fileStream, string fileName);
        Task<byte[]> GenerateSdcImportTemplateAsync();
        Task<(List<object> Imports, int TotalCount)> GetSdcImportHistoryAsync(int page, int pageSize);
        Task<object> ValidateSdcFileAsync(Stream fileStream, string fileName);
    }
} 