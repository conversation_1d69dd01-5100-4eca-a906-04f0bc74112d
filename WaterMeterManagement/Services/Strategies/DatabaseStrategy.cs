using Microsoft.EntityFrameworkCore;
using WaterMeterManagement.Data;
using WaterMeterManagement.DTOs;
using WaterMeterManagement.Services.Interfaces;

namespace WaterMeterManagement.Services.Strategies
{
    /// <summary>
    /// Database strategy for dynamic select options
    /// </summary>
    public class DatabaseStrategy : IDataSourceStrategy
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<DatabaseStrategy> _logger;

        public string StrategyName => "database";

        public DatabaseStrategy(ApplicationDbContext context, ILogger<DatabaseStrategy> logger)
        {
            _context = context;
            _logger = logger;
        }

        public async Task<List<DynamicOptionDto>> GetOptionsAsync(string dataSource, Dictionary<string, object> parameters)
        {
            try
            {
                _logger.LogInformation("Getting database options for dataSource: {DataSource}, params: {@Parameters}", 
                    dataSource, parameters);

                return dataSource.ToLower() switch
                {
                    "watermeter.township" => await GetWaterMeterTownshipsAsync(parameters),
                    "watermeter.subarea" => await GetWaterMeterSubAreasAsync(parameters),
                    "watermeter.metertype" => await GetWaterMeterTypesAsync(parameters),
                    "watermeter.status" => await GetWaterMeterStatusesAsync(parameters),
                    "watermeter.brand" => await GetWaterMeterBrandsAsync(parameters),
                    "watermeter.communicationmethod" => await GetWaterMeterCommunicationMethodsAsync(parameters),
                    "watermeter.roadname" => await GetWaterMeterRoadNamesAsync(parameters),
                    "route.name" => await GetRouteNamesAsync(parameters),
                    "user.role" => await GetUserRolesAsync(parameters),
                    "workpackage.servicarea" => await GetWorkPackageServiceAreasAsync(parameters),
                    "workpackage.priority" => await GetWorkPackagePrioritiesAsync(parameters),
                    "workpackage.status" => await GetWorkPackageStatusesAsync(parameters),
                    _ => throw new ArgumentException($"Unknown database data source: {dataSource}")
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting database options for dataSource: {DataSource}", dataSource);
                throw;
            }
        }

        public async Task<List<string>> GetAvailableDataSourcesAsync()
        {
            return new List<string>
            {
                "watermeter.township",
                "watermeter.subarea", 
                "watermeter.metertype",
                "watermeter.status",
                "watermeter.brand",
                "watermeter.communicationmethod",
                "watermeter.roadname",
                "route.name",
                "user.role",
                "workpackage.servicarea",
                "workpackage.priority",
                "workpackage.status"
            };
        }

        #region Private Methods

        private async Task<List<DynamicOptionDto>> GetWaterMeterTownshipsAsync(Dictionary<string, object> parameters)
        {
            var query = _context.WaterMeters.Where(m => !string.IsNullOrEmpty(m.Township));

            // Apply filters based on parameters
            if (parameters.ContainsKey("status"))
            {
                var status = parameters["status"].ToString();
                if (!string.IsNullOrEmpty(status))
                    query = query.Where(m => m.Status == status);
            }

            if (parameters.ContainsKey("meterType"))
            {
                var meterType = parameters["meterType"].ToString();
                if (!string.IsNullOrEmpty(meterType))
                    query = query.Where(m => m.MeterType == meterType);
            }

            var townships = await query
                .Select(m => m.Township!)
                .Distinct()
                .OrderBy(t => t)
                .ToListAsync();

            return townships.Select(t => new DynamicOptionDto
            {
                Value = t,
                Label = t,
                Description = $"Township: {t}"
            }).ToList();
        }

        private async Task<List<DynamicOptionDto>> GetWaterMeterSubAreasAsync(Dictionary<string, object> parameters)
        {
            var query = _context.WaterMeters.Where(m => !string.IsNullOrEmpty(m.SubArea));

            // Apply township filter if provided
            if (parameters.ContainsKey("township"))
            {
                var township = parameters["township"].ToString();
                if (!string.IsNullOrEmpty(township))
                    query = query.Where(m => m.Township == township);
            }

            var subAreas = await query
                .Select(m => m.SubArea!)
                .Distinct()
                .OrderBy(s => s)
                .ToListAsync();

            return subAreas.Select(s => new DynamicOptionDto
            {
                Value = s,
                Label = s,
                Description = $"Sub Area: {s}"
            }).ToList();
        }

        private async Task<List<DynamicOptionDto>> GetWaterMeterTypesAsync(Dictionary<string, object> parameters)
        {
            var meterTypes = await _context.WaterMeters
                .Where(m => !string.IsNullOrEmpty(m.MeterType))
                .Select(m => m.MeterType)
                .Distinct()
                .OrderBy(t => t)
                .ToListAsync();

            return meterTypes.Select(t => new DynamicOptionDto
            {
                Value = t,
                Label = t,
                Description = $"Meter Type: {t}"
            }).ToList();
        }

        private async Task<List<DynamicOptionDto>> GetWaterMeterStatusesAsync(Dictionary<string, object> parameters)
        {
            var statuses = await _context.WaterMeters
                .Where(m => !string.IsNullOrEmpty(m.Status))
                .Select(m => m.Status)
                .Distinct()
                .OrderBy(s => s)
                .ToListAsync();

            return statuses.Select(s => new DynamicOptionDto
            {
                Value = s,
                Label = s,
                Description = $"Status: {s}"
            }).ToList();
        }

        private async Task<List<DynamicOptionDto>> GetWaterMeterBrandsAsync(Dictionary<string, object> parameters)
        {
            var brands = await _context.WaterMeters
                .Where(m => !string.IsNullOrEmpty(m.Brand))
                .Select(m => m.Brand!)
                .Distinct()
                .OrderBy(b => b)
                .ToListAsync();

            return brands.Select(b => new DynamicOptionDto
            {
                Value = b,
                Label = b,
                Description = $"Brand: {b}"
            }).ToList();
        }

        private async Task<List<DynamicOptionDto>> GetWaterMeterCommunicationMethodsAsync(Dictionary<string, object> parameters)
        {
            var methods = await _context.WaterMeters
                .Where(m => !string.IsNullOrEmpty(m.CommunicationMethod))
                .Select(m => m.CommunicationMethod!)
                .Distinct()
                .OrderBy(m => m)
                .ToListAsync();

            return methods.Select(m => new DynamicOptionDto
            {
                Value = m,
                Label = m,
                Description = $"Communication Method: {m}"
            }).ToList();
        }

        private async Task<List<DynamicOptionDto>> GetWaterMeterRoadNamesAsync(Dictionary<string, object> parameters)
        {
            var query = _context.WaterMeters.Where(m => !string.IsNullOrEmpty(m.RoadName));

            // Apply township filter if provided
            if (parameters.ContainsKey("township"))
            {
                var township = parameters["township"].ToString();
                if (!string.IsNullOrEmpty(township))
                    query = query.Where(m => m.Township == township);
            }

            var roadNames = await query
                .Select(m => m.RoadName!)
                .Distinct()
                .OrderBy(r => r)
                .ToListAsync();

            return roadNames.Select(r => new DynamicOptionDto
            {
                Value = r,
                Label = r,
                Description = $"Road: {r}"
            }).ToList();
        }

        private async Task<List<DynamicOptionDto>> GetRouteNamesAsync(Dictionary<string, object> parameters)
        {
            var routes = await _context.Routes
                .Where(r => !string.IsNullOrEmpty(r.Name))
                .Select(r => new { r.Id, r.Name, r.Description })
                .OrderBy(r => r.Name)
                .ToListAsync();

            return routes.Select(r => new DynamicOptionDto
            {
                Value = r.Id.ToString(),
                Label = r.Name,
                Description = r.Description ?? $"Route: {r.Name}"
            }).ToList();
        }

        private async Task<List<DynamicOptionDto>> GetUserRolesAsync(Dictionary<string, object> parameters)
        {
            var roles = await _context.Roles
                .Select(r => new { r.Id, r.Name, r.Description })
                .OrderBy(r => r.Name)
                .ToListAsync();

            return roles.Select(r => new DynamicOptionDto
            {
                Value = r.Id.ToString(),
                Label = r.Name,
                Description = r.Description
            }).ToList();
        }

        private async Task<List<DynamicOptionDto>> GetWorkPackageServiceAreasAsync(Dictionary<string, object> parameters)
        {
            var serviceAreas = await _context.WorkPackages
                .Where(w => !string.IsNullOrEmpty(w.ServiceArea))
                .Select(w => w.ServiceArea)
                .Distinct()
                .OrderBy(s => s)
                .ToListAsync();

            return serviceAreas.Select(s => new DynamicOptionDto
            {
                Value = s,
                Label = s,
                Description = $"Service Area: {s}"
            }).ToList();
        }

        private async Task<List<DynamicOptionDto>> GetWorkPackagePrioritiesAsync(Dictionary<string, object> parameters)
        {
            var priorities = await _context.WorkPackages
                .Where(w => !string.IsNullOrEmpty(w.Priority))
                .Select(w => w.Priority)
                .Distinct()
                .OrderBy(p => p)
                .ToListAsync();

            return priorities.Select(p => new DynamicOptionDto
            {
                Value = p,
                Label = p,
                Description = $"Priority: {p}"
            }).ToList();
        }

        private async Task<List<DynamicOptionDto>> GetWorkPackageStatusesAsync(Dictionary<string, object> parameters)
        {
            var statuses = await _context.WorkPackages
                .Where(w => !string.IsNullOrEmpty(w.Status))
                .Select(w => w.Status)
                .Distinct()
                .OrderBy(s => s)
                .ToListAsync();

            return statuses.Select(s => new DynamicOptionDto
            {
                Value = s,
                Label = s,
                Description = $"Status: {s}"
            }).ToList();
        }

        #endregion
    }
}
