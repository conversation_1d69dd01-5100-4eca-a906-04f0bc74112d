using WaterMeterManagement.DTOs;
using WaterMeterManagement.Services.Interfaces;
using System.ComponentModel;
using System.Reflection;

namespace WaterMeterManagement.Services.Strategies
{
    /// <summary>
    /// Enum strategy for dynamic select options
    /// </summary>
    public class EnumStrategy : IDataSourceStrategy
    {
        private readonly ILogger<EnumStrategy> _logger;

        public string StrategyName => "enum";

        public EnumStrategy(ILogger<EnumStrategy> logger)
        {
            _logger = logger;
        }

        public async Task<List<DynamicOptionDto>> GetOptionsAsync(string dataSource, Dictionary<string, object> parameters)
        {
            try
            {
                _logger.LogInformation("Getting enum options for dataSource: {DataSource}", dataSource);

                return dataSource.ToLower() switch
                {
                    "meterstatus" => await GetMeterStatusEnumAsync(),
                    "metertype" => await GetMeterTypeEnumAsync(),
                    "priority" => await GetPriorityEnumAsync(),
                    "workpackagestatus" => await GetWorkPackageStatusEnumAsync(),
                    "frequency" => await GetFrequencyEnumAsync(),
                    "communicationmethod" => await GetCommunicationMethodEnumAsync(),
                    "syncstatus" => await GetSyncStatusEnumAsync(),
                    "source" => await GetSourceEnumAsync(),
                    _ => throw new ArgumentException($"Unknown enum data source: {dataSource}")
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting enum options for dataSource: {DataSource}", dataSource);
                throw;
            }
        }

        public async Task<List<string>> GetAvailableDataSourcesAsync()
        {
            return await Task.FromResult(new List<string>
            {
                "meterstatus",
                "metertype",
                "priority",
                "workpackagestatus",
                "frequency",
                "communicationmethod",
                "syncstatus",
                "source"
            });
        }

        #region Private Methods

        private async Task<List<DynamicOptionDto>> GetMeterStatusEnumAsync()
        {
            var options = new List<DynamicOptionDto>
            {
                new() { Value = "Active", Label = "Active", Description = "Meter is active and operational" },
                new() { Value = "Inactive", Label = "Inactive", Description = "Meter is inactive" },
                new() { Value = "Maintenance", Label = "Maintenance", Description = "Meter is under maintenance" },
                new() { Value = "Removed", Label = "Removed", Description = "Meter has been removed" },
                new() { Value = "Replaced", Label = "Replaced", Description = "Meter has been replaced" }
            };

            return await Task.FromResult(options);
        }

        private async Task<List<DynamicOptionDto>> GetMeterTypeEnumAsync()
        {
            var options = new List<DynamicOptionDto>
            {
                new() { Value = "Residential", Label = "Residential", Description = "Residential water meter" },
                new() { Value = "Commercial", Label = "Commercial", Description = "Commercial water meter" },
                new() { Value = "Industrial", Label = "Industrial", Description = "Industrial water meter" },
                new() { Value = "Municipal", Label = "Municipal", Description = "Municipal water meter" }
            };

            return await Task.FromResult(options);
        }

        private async Task<List<DynamicOptionDto>> GetPriorityEnumAsync()
        {
            var options = new List<DynamicOptionDto>
            {
                new() { Value = "Low", Label = "Low", Description = "Low priority" },
                new() { Value = "Medium", Label = "Medium", Description = "Medium priority" },
                new() { Value = "High", Label = "High", Description = "High priority" },
                new() { Value = "Critical", Label = "Critical", Description = "Critical priority" }
            };

            return await Task.FromResult(options);
        }

        private async Task<List<DynamicOptionDto>> GetWorkPackageStatusEnumAsync()
        {
            var options = new List<DynamicOptionDto>
            {
                new() { Value = "Draft", Label = "Draft", Description = "Work package is in draft state" },
                new() { Value = "Planned", Label = "Planned", Description = "Work package is planned" },
                new() { Value = "InProgress", Label = "In Progress", Description = "Work package is in progress" },
                new() { Value = "Completed", Label = "Completed", Description = "Work package is completed" },
                new() { Value = "Cancelled", Label = "Cancelled", Description = "Work package is cancelled" },
                new() { Value = "OnHold", Label = "On Hold", Description = "Work package is on hold" }
            };

            return await Task.FromResult(options);
        }

        private async Task<List<DynamicOptionDto>> GetFrequencyEnumAsync()
        {
            var options = new List<DynamicOptionDto>
            {
                new() { Value = "OneTime", Label = "One Time", Description = "Execute once" },
                new() { Value = "Daily", Label = "Daily", Description = "Execute daily" },
                new() { Value = "Weekly", Label = "Weekly", Description = "Execute weekly" },
                new() { Value = "Monthly", Label = "Monthly", Description = "Execute monthly" },
                new() { Value = "Quarterly", Label = "Quarterly", Description = "Execute quarterly" },
                new() { Value = "Biannually", Label = "Biannually", Description = "Execute twice a year" },
                new() { Value = "Annually", Label = "Annually", Description = "Execute annually" }
            };

            return await Task.FromResult(options);
        }

        private async Task<List<DynamicOptionDto>> GetCommunicationMethodEnumAsync()
        {
            var options = new List<DynamicOptionDto>
            {
                new() { Value = "Manual", Label = "Manual", Description = "Manual reading" },
                new() { Value = "LoRaWAN", Label = "LoRaWAN", Description = "LoRaWAN communication" },
                new() { Value = "NB-IoT", Label = "NB-IoT", Description = "NB-IoT communication" },
                new() { Value = "WiFi", Label = "WiFi", Description = "WiFi communication" },
                new() { Value = "Cellular", Label = "Cellular", Description = "Cellular communication" },
                new() { Value = "Zigbee", Label = "Zigbee", Description = "Zigbee communication" }
            };

            return await Task.FromResult(options);
        }

        private async Task<List<DynamicOptionDto>> GetSyncStatusEnumAsync()
        {
            var options = new List<DynamicOptionDto>
            {
                new() { Value = "Synced", Label = "Synced", Description = "Data is synchronized" },
                new() { Value = "Pending", Label = "Pending", Description = "Sync is pending" },
                new() { Value = "Failed", Label = "Failed", Description = "Sync failed" },
                new() { Value = "InProgress", Label = "In Progress", Description = "Sync in progress" }
            };

            return await Task.FromResult(options);
        }

        private async Task<List<DynamicOptionDto>> GetSourceEnumAsync()
        {
            var options = new List<DynamicOptionDto>
            {
                new() { Value = "Manual", Label = "Manual", Description = "Manually created" },
                new() { Value = "AMS", Label = "AMS", Description = "Imported from AMS" },
                new() { Value = "API", Label = "API", Description = "Created via API" },
                new() { Value = "Import", Label = "Import", Description = "Imported from file" }
            };

            return await Task.FromResult(options);
        }

        #endregion
    }
}
