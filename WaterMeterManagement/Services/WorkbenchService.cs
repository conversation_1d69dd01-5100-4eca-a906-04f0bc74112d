using System.Text;
using System.Text.Json;
using WaterMeterManagement.DTOs;
using WaterMeterManagement.Services.Interfaces;

namespace WaterMeterManagement.Services
{
    /// <summary>
    /// Workbench API service implementation with consistent API call logic as mobile app
    /// </summary>
    public class WorkbenchService : IWorkbenchService
    {
        private readonly HttpClient _httpClient;
        private readonly ILogger<WorkbenchService> _logger;

        public WorkbenchService(HttpClient httpClient, ILogger<WorkbenchService> logger)
        {
            _httpClient = httpClient;
            _logger = logger;
            _httpClient.Timeout = TimeSpan.FromSeconds(30); // Increase timeout
        }

        public async Task<WorkbenchLoginResponseDto?> AuthenticateAsync(string username, string password)
        {
            try
            {
                _logger.LogInformation("Attempting Workbench API authentication for user: {Username}", username);

                // Create exact same request as Postman - use absolute URL
                var url = "https://sicon-mnlweb.sicon.co.nz/WorkbenchTest/api/AccountApi";
                var request = new HttpRequestMessage(HttpMethod.Get, url);
                
                // Set Basic Auth exactly like Postman
                var credentials = Convert.ToBase64String(Encoding.UTF8.GetBytes($"{username}:{password}"));
                request.Headers.Add("Authorization", $"Basic {credentials}");

                _logger.LogInformation("Making GET request to: {Url}", url);
                _logger.LogInformation("Authorization: Basic {Credentials}", credentials);

                var response = await _httpClient.SendAsync(request);

                _logger.LogInformation("Response status: {StatusCode}", response.StatusCode);

                if (response.IsSuccessStatusCode)
                {
                    var jsonContent = await response.Content.ReadAsStringAsync();
                    _logger.LogInformation("Success response: {Response}", jsonContent);

                    var options = new JsonSerializerOptions { PropertyNameCaseInsensitive = true };
                    var workbenchUser = JsonSerializer.Deserialize<WorkbenchLoginResponseDto>(jsonContent, options);
                    
                    if (workbenchUser != null)
                    {
                        _logger.LogInformation("Authentication successful: {FullName} (ID: {PersonID})", 
                            workbenchUser.FullName, workbenchUser.PersonID);
                        return workbenchUser;
                    }
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogError("Request failed: {StatusCode} - {Content}", response.StatusCode, errorContent);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Request error: {Message}", ex.Message);
            }

            return null;
        }

        public async Task<WorkbenchStaffApiResponse?> FetchStaffDataAsync(int page = 1, int pageSize = 100)
        {
            try
            {
                _logger.LogInformation("Fetching staff data from Workbench API - Page: {Page}, PageSize: {PageSize}", page, pageSize);

                // Use the exact same URL as mobile app with TableApi endpoint
                var url = "https://sicon-mnlweb.sicon.co.nz/WorkbenchTest/api/TableApi";
                
                // Create the same request payload as mobile app (exactly matching StaffApi.ts)
                var requestPayload = new
                {
                    TableName = "People",
                    ColumnNames = "PersonID, FullName, EmailAddress, MobilePhone, ProfitCentreCode, EmployeeNo",
                    PredicateRows = new object[]
                    {
                        new
                        {
                            LeftOperand = "ProfitCentreCode",
                            Operator = "Like",
                            RightOperand = new string[] { "C003", "N003", "N002" }
                        },
                        new
                        {
                            LeftOperand = "IsEmployee",
                            Operator = "Eq",
                            RightOperand = new int[] { -1 }
                        },
                        new
                        {
                            LeftOperand = "HasLogin",
                            Operator = "Eq",
                            RightOperand = new int[] { -1 }
                        }
                    },
                    Page = page,
                    Rows = pageSize
                };

                var json = JsonSerializer.Serialize(requestPayload);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                // Add Basic Authentication using default credentials (same as mobile app)
                var defaultUsername = "Lindaj";
                var defaultPassword = "CORDEwinter24!";
                var credentials = Convert.ToBase64String(Encoding.UTF8.GetBytes($"{defaultUsername}:{defaultPassword}"));
                
                var request = new HttpRequestMessage(HttpMethod.Post, url)
                {
                    Content = content
                };
                request.Headers.Add("Authorization", $"Basic {credentials}");

                _logger.LogInformation("Making POST request to: {Url}", url);
                _logger.LogInformation("Authorization: Basic {Credentials}", credentials);
                _logger.LogInformation("Request payload: {Payload}", json);

                var response = await _httpClient.SendAsync(request);

                _logger.LogInformation("Response status: {StatusCode}", response.StatusCode);

                if (response.IsSuccessStatusCode)
                {
                    var jsonContent = await response.Content.ReadAsStringAsync();
                    _logger.LogInformation("Success response: {Response}", jsonContent);

                    var options = new JsonSerializerOptions { PropertyNameCaseInsensitive = true };
                    var staffResponse = JsonSerializer.Deserialize<WorkbenchStaffApiResponse>(jsonContent, options);
                    
                    if (staffResponse != null)
                    {
                        _logger.LogInformation("Staff data fetched successfully: {Records} records, {RowCount} rows", 
                            staffResponse.Records, staffResponse.Rows.Count);
                        return staffResponse;
                    }
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogError("Staff data fetch failed: {StatusCode} - {Content}", response.StatusCode, errorContent);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error fetching staff data: {Message}", ex.Message);
            }

            return null;
        }
    }
} 