using WaterMeterManagement.Models;
using WaterMeterManagement.Repositories.Interfaces;
using WaterMeterManagement.Services.Interfaces;

namespace WaterMeterManagement.Services
{
    public class RoleService : IRoleService
    {
        private readonly IRoleRepository _roleRepository;
        private readonly IPermissionRepository _permissionRepository;

        public RoleService(IRoleRepository roleRepository, IPermissionRepository permissionRepository)
        {
            _roleRepository = roleRepository;
            _permissionRepository = permissionRepository;
        }

        public async Task<IEnumerable<Role>> GetAllRolesAsync()
        {
            return await _roleRepository.GetRolesWithPermissionsAsync();
        }

        public async Task<Role?> GetRoleByIdAsync(int id)
        {
            return await _roleRepository.GetRoleWithPermissionsAsync(id);
        }

        public async Task<Role> CreateRoleAsync(Role role)
        {
            // Check if role name already exists
            if (await _roleRepository.RoleExistsAsync(role.Name))
            {
                throw new InvalidOperationException($"Role with name '{role.Name}' already exists.");
            }

            role.CreatedAt = DateTime.UtcNow;
            role.UpdatedAt = DateTime.UtcNow;
            role.CreatedBy = "System"; // TODO: Get from current user context
            role.UpdatedBy = "System";

            return await _roleRepository.AddAsync(role);
        }

        public async Task<Role> UpdateRoleAsync(Role role)
        {
            var existingRole = await _roleRepository.GetByIdAsync(role.Id);
            if (existingRole == null)
            {
                throw new ArgumentException($"Role with ID {role.Id} not found.");
            }

            // Check if the new name conflicts with another role
            var allRoles = await _roleRepository.GetAllAsync();
            if (allRoles.Any(r => r.Name.ToLower() == role.Name.ToLower() && r.Id != role.Id))
            {
                throw new InvalidOperationException($"Role with name '{role.Name}' already exists.");
            }

            existingRole.Name = role.Name;
            existingRole.Description = role.Description;
            existingRole.UpdatedAt = DateTime.UtcNow;
            existingRole.UpdatedBy = "System"; // TODO: Get from current user context

            return await _roleRepository.UpdateAsync(existingRole);
        }

        public async Task<bool> DeleteRoleAsync(int id)
        {
            var role = await _roleRepository.GetByIdAsync(id);
            if (role == null)
            {
                return false;
            }

            // TODO: Check if role is assigned to any users before deletion
            await _roleRepository.DeleteAsync(id);
            return true;
        }

        public async Task<bool> RoleExistsAsync(string name)
        {
            return await _roleRepository.RoleExistsAsync(name);
        }

        public async Task AssignPermissionsToRoleAsync(int roleId, List<int> permissionIds)
        {
            var role = await _roleRepository.GetByIdAsync(roleId);
            if (role == null)
            {
                throw new ArgumentException($"Role with ID {roleId} not found.");
            }

            await _roleRepository.AssignPermissionsToRoleAsync(roleId, permissionIds);
        }

        public async Task<IEnumerable<Permission>> GetRolePermissionsAsync(int roleId)
        {
            return await _roleRepository.GetRolePermissionsAsync(roleId);
        }

        public async Task<IEnumerable<Permission>> GetAllPermissionsAsync()
        {
            return await _permissionRepository.GetAllPermissionsAsync();
        }
    }
} 