using Microsoft.EntityFrameworkCore;
using WaterMeterManagement.Data;
using WaterMeterManagement.DTOs.UserRole;
using WaterMeterManagement.Enums;
using WaterMeterManagement.Exceptions;
using WaterMeterManagement.Models;
using WaterMeterManagement.Repositories.Interfaces;
using WaterMeterManagement.Services.Interfaces;

namespace WaterMeterManagement.Services
{
    public class UserRoleService : IUserRoleService
    {
        private readonly IUserRoleRepository _userRoleRepository;
        private readonly IRoleRepository _roleRepository;
        private readonly ILogger<UserRoleService> _logger;

        public UserRoleService(
            IUserRoleRepository userRoleRepository,
            IRoleRepository roleRepository,
            ILogger<UserRoleService> logger)
        {
            _userRoleRepository = userRoleRepository;
            _roleRepository = roleRepository;
            _logger = logger;
        }

        public async Task<List<UserRoleDto>> GetUserRolesAsync(int userId)
        {
            var userRoles = await _userRoleRepository.GetByUserIdAsync(userId);

            return userRoles.Select(ur => new UserRoleDto
            {
                UserId = ur.UserId,
                RoleId = ur.RoleId,
                UserName = ur.User?.Username ?? "",
                RoleName = ur.Role?.Name ?? "",
                RoleDescription = ur.Role?.Description ?? "",
                CreatedAt = ur.CreatedAt,
                CreatedBy = ur.CreatedBy
            }).ToList();
        }

        public async Task<List<UserRoleDto>> GetRoleUsersAsync(int roleId)
        {
            var roleExists = await _roleRepository.ExistsAsync(roleId);
            if (!roleExists)
            {
                throw new BusinessException(ErrorCode.RoleNotFound, 404);
            }

            var userRoles = await _userRoleRepository.GetByRoleIdAsync(roleId);
            
            return userRoles.Select(ur => new UserRoleDto
            {
                UserId = ur.UserId,
                RoleId = ur.RoleId,
                UserName = ur.User?.Username ?? "",
                RoleName = ur.Role?.Name ?? "",
                RoleDescription = ur.Role?.Description ?? "",
                CreatedAt = ur.CreatedAt,
                CreatedBy = ur.CreatedBy
            }).ToList();
        }

        public async Task<UserRoleAssignmentResultDto> AssignRolesToUserAsync(int userId, List<int> roleIds, string assignedBy)
        {
            var existingUserRoles = await _userRoleRepository.GetByUserIdAsync(userId);
            foreach (var existingUserRole in existingUserRoles)
            {
                await _userRoleRepository.DeleteAsync(userId, existingUserRole.RoleId);
            }

            var results = new List<RoleAssignmentDetailDto>();
            var successCount = 0;

            foreach (var roleId in roleIds)
            {
                var result = await AddRoleToUserAsync(userId, roleId, assignedBy);
                results.Add(result);
                
                if (result.Status != "Error")
                {
                    successCount++;
                }
            }

            _logger.LogInformation("Role assignment completed for user {UserId}. Success: {SuccessCount}, Total: {Total}", 
                userId, successCount, roleIds.Count);

            return new UserRoleAssignmentResultDto
            {
                UserId = userId,
                UserName = $"User{userId}",
                Results = results,
                SuccessCount = successCount,
                ErrorCount = roleIds.Count - successCount
            };
        }

        public async Task<RoleAssignmentDetailDto> AddRoleToUserAsync(int userId, int roleId, string assignedBy)
        {
            var role = await _roleRepository.GetByIdAsync(roleId);
            if (role == null)
            {
                return new RoleAssignmentDetailDto
                {
                    RoleId = roleId,
                    RoleName = $"Role ID {roleId}",
                    Status = "Error",
                    Message = "Role not found"
                };
            }

            var message = await _userRoleRepository.UpsertUserRole(userId, roleId, assignedBy);

            var status = message.Contains("restored") ? "Restored" :
                        message.Contains("already assigned") ? "Skipped" : "Created";

            return new RoleAssignmentDetailDto
            {
                RoleId = roleId,
                RoleName = role.Name,
                Status = status,
                Message = message
            };
        }

        public async Task RemoveRoleFromUserAsync(int userId, int roleId)
        {
            var hasRole = await _userRoleRepository.ExistsAsync(userId, roleId);
            if (!hasRole)
            {
                throw new BusinessException(ErrorCode.InvalidRoleAssignment, "User does not have this role", 400);
            }

            await _userRoleRepository.DeleteAsync(userId, roleId);
            
            _logger.LogInformation("Role {RoleId} removed from user {UserId}", roleId, userId);
        }

        public async Task<bool> UserHasRoleAsync(int userId, int roleId)
        {
            return await _userRoleRepository.ExistsAsync(userId, roleId);
        }

        public async Task<List<Role>> GetAvailableRolesAsync()
        {
            return (await _roleRepository.GetAllAsync()).ToList();
        }
    }
}
