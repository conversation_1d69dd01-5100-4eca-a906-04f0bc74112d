using Microsoft.EntityFrameworkCore;
using WaterMeterManagement.Data;
using WaterMeterManagement.DTOs;
using WaterMeterManagement.DTOs.Mobile;
using WaterMeterManagement.Models;
using WaterMeterManagement.Services.Interfaces;

namespace WaterMeterManagement.Services
{
    public class MeterReadingService : IMeterReadingService
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<MeterReadingService> _logger;
        private readonly ICloudflareR2Service _r2Service;

        public MeterReadingService(ApplicationDbContext context, ILogger<MeterReadingService> logger, ICloudflareR2Service r2Service)
        {
            _context = context;
            _logger = logger;
            _r2Service = r2Service;
        }

        #region Reading Management

        public async Task<MeterReadingSearchResultDto> GetReadingsAsync(MeterReadingSearchDto searchDto)
        {
            try
            {
                var query = _context.MeterReadings.AsQueryable();

                // Apply filters
                if (!string.IsNullOrWhiteSpace(searchDto.MeterNumber))
                {
                    query = query.Where(r => r.WaterMeter.SerialNumber.Contains(searchDto.MeterNumber));
                }

                if (searchDto.StartDate.HasValue)
                {
                    query = query.Where(r => r.ReadingDate >= searchDto.StartDate.Value);
                }

                if (searchDto.EndDate.HasValue)
                {
                    query = query.Where(r => r.ReadingDate <= searchDto.EndDate.Value);
                }

                if (!string.IsNullOrWhiteSpace(searchDto.Status))
                {
                    query = query.Where(r => r.Status == searchDto.Status);
                }

                if (!string.IsNullOrWhiteSpace(searchDto.ValidationStatus))
                {
                    query = query.Where(r => r.ValidationStatus == searchDto.ValidationStatus);
                }

                // Get total count
                var totalCount = await query.CountAsync();

                // Apply sorting
                if (!string.IsNullOrWhiteSpace(searchDto.SortBy))
                {
                    var isDescending = searchDto.SortDirection?.ToLower() == "desc";
                    query = searchDto.SortBy.ToLower() switch
                    {
                        "meternumber" => isDescending ? query.OrderByDescending(r => r.WaterMeter.SerialNumber) : query.OrderBy(r => r.WaterMeter.SerialNumber),
                        "readingdate" => isDescending ? query.OrderByDescending(r => r.ReadingDate) : query.OrderBy(r => r.ReadingDate),
                        "readingvalue" => isDescending ? query.OrderByDescending(r => r.ReadingValue) : query.OrderBy(r => r.ReadingValue),
                        "status" => isDescending ? query.OrderByDescending(r => r.Status) : query.OrderBy(r => r.Status),
                        "createdat" => isDescending ? query.OrderByDescending(r => r.CreatedAt) : query.OrderBy(r => r.CreatedAt),
                        _ => isDescending ? query.OrderByDescending(r => r.CreatedAt) : query.OrderBy(r => r.CreatedAt)
                    };
                }
                else
                {
                    query = query.OrderByDescending(r => r.CreatedAt);
                }

                // Apply pagination
                var readings = await query
                    .Skip((searchDto.Page - 1) * searchDto.PageSize)
                    .Take(searchDto.PageSize)
                    .Select(r => new MeterReadingListDto
                    {
                        Id = r.Id,
                        MeterId = r.MeterId,
                        UserId = r.UserId,
                        TaskId = r.TaskId,
                        MeterNumber = r.WaterMeter.SerialNumber,
                        ReadingValue = r.ReadingValue,
                        PreviousReading = null, // 删除的字段
                        Consumption = null, // 删除的字段
                        ReadingDate = r.ReadingDate,
                        ReadBy = r.User.Username ?? r.CreatedBy,
                        Status = r.Status,
                        ValidationStatus = r.ValidationStatus,
                        AnomalyType = r.AnomalyType,
                        Notes = r.Notes,
                        Location = r.Location,
                        HasPhoto = r.Photos.Any(),
                        HasOCR = r.HasOCR,
                        OCRStatus = r.OCRStatus,
                        OCRConfidence = r.OCRConfidence,
                        ReadingMethod = r.ReadingMethod,
                        ReadingType = r.ReadingType,
                        DataSource = r.DataSource,
                        IsAnomalous = r.IsAnomalous,
                        CantRead = r.CantRead,
                        IsValidated = r.IsValidated,
                        Latitude = r.Latitude,
                        Longitude = r.Longitude,
                        GpsAccuracy = r.GpsAccuracy,
                        ValidatedBy = r.ValidatedBy,
                        ValidationDate = r.ValidationDate,
                        ValidationComments = r.ValidationComments,
                        AnomalyReason = r.AnomalyReason,
                        CantReadReason = r.CantReadReason,
                        CreatedBy = r.CreatedBy,
                        UpdatedBy = r.UpdatedBy,
                        IsDeleted = r.IsDeleted,
                        CreatedAt = r.CreatedAt,
                        UpdatedAt = r.UpdatedAt
                    })
                    .ToListAsync();

                return new MeterReadingSearchResultDto
                {
                    Readings = readings,
                    TotalCount = totalCount,
                    Page = searchDto.Page,
                    PageSize = searchDto.PageSize,
                    TotalPages = (int)Math.Ceiling((double)totalCount / searchDto.PageSize)
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting meter readings");
                throw;
            }
        }

        public async Task<MeterReadingListDto?> GetReadingByIdAsync(int id)
        {
            try
            {
                var reading = await _context.MeterReadings
                    .Where(r => r.Id == id)
                    .Select(r => new MeterReadingListDto
                    {
                        Id = r.Id,
                        MeterId = r.MeterId,
                        UserId = r.UserId,
                        TaskId = r.TaskId,
                        MeterNumber = r.WaterMeter.SerialNumber,
                        ReadingValue = r.ReadingValue,
                        PreviousReading = null, // 删除的字段
                        Consumption = null, // 删除的字段
                        ReadingDate = r.ReadingDate,
                        ReadBy = r.User.Username ?? r.CreatedBy,
                        Status = r.Status,
                        ValidationStatus = r.ValidationStatus,
                        AnomalyType = r.AnomalyType,
                        Notes = r.Notes,
                        Location = r.Location,
                        HasPhoto = r.Photos.Any(),
                        HasOCR = r.HasOCR,
                        OCRStatus = r.OCRStatus,
                        OCRConfidence = r.OCRConfidence,
                        ReadingMethod = r.ReadingMethod,
                        ReadingType = r.ReadingType,
                        DataSource = r.DataSource,
                        IsAnomalous = r.IsAnomalous,
                        CantRead = r.CantRead,
                        IsValidated = r.IsValidated,
                        Latitude = r.Latitude,
                        Longitude = r.Longitude,
                        GpsAccuracy = r.GpsAccuracy,
                        ValidatedBy = r.ValidatedBy,
                        ValidationDate = r.ValidationDate,
                        ValidationComments = r.ValidationComments,
                        AnomalyReason = r.AnomalyReason,
                        CantReadReason = r.CantReadReason,
                        CreatedBy = r.CreatedBy,
                        UpdatedBy = r.UpdatedBy,
                        IsDeleted = r.IsDeleted,
                        CreatedAt = r.CreatedAt,
                        UpdatedAt = r.UpdatedAt
                    })
                    .FirstOrDefaultAsync();

                return reading;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting meter reading by id {Id}", id);
                throw;
            }
        }

        public async Task<MeterReadingListDto> CreateReadingAsync(CreateMeterReadingDto createDto, string createdBy)
        {
            try
            {
                // 注意：Consumption现在通过计算获得，不再存储在数据库中

                var reading = new MeterReading
                {
                    MeterId = createDto.MeterId,
                    UserId = createDto.UserId,
                    TaskId = createDto.TaskId,
                    ReadingValue = createDto.ReadingValue,
                    ReadingDate = createDto.ReadingDate,
                    ReadingMethod = createDto.ReadingMethod,
                    ReadingType = createDto.ReadingType,
                    DataSource = createDto.DataSource,
                    Status = "Completed",
                    ValidationStatus = "Pending",
                    Notes = createDto.Notes,
                    Location = createDto.Location,
                    HasOCR = createDto.HasOCR,
                    OCRStatus = createDto.OCRStatus,
                    OCRConfidence = createDto.OCRConfidence,
                    Latitude = createDto.Latitude,
                    Longitude = createDto.Longitude,
                    GpsAccuracy = createDto.GpsAccuracy,
                    CantRead = createDto.CantRead,
                    CantReadReason = createDto.CantReadReason,
                    CreatedBy = createdBy,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                };

                _context.MeterReadings.Add(reading);
                await _context.SaveChangesAsync();

                return await GetReadingByIdAsync(reading.Id) ?? throw new Exception("Failed to retrieve created reading");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating meter reading");
                throw;
            }
        }

        public async Task<MeterReadingListDto> UpdateReadingAsync(int id, UpdateMeterReadingDto updateDto, string updatedBy)
        {
            try
            {
                var reading = await _context.MeterReadings.FindAsync(id);
                if (reading == null)
                {
                    throw new ArgumentException($"Reading with id {id} not found");
                }

                // Update fields
                reading.ReadingValue = updateDto.ReadingValue;
                reading.Status = updateDto.Status;
                reading.ValidationStatus = updateDto.ValidationStatus;
                reading.AnomalyType = updateDto.AnomalyType;
                reading.Notes = updateDto.Notes;
                reading.Location = updateDto.Location;
                reading.UpdatedBy = updatedBy;
                reading.UpdatedAt = DateTime.UtcNow;

                // 注意：PreviousReading和Consumption字段已删除，现在通过计算获得

                await _context.SaveChangesAsync();

                return await GetReadingByIdAsync(id) ?? throw new Exception("Failed to retrieve updated reading");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating meter reading {Id}", id);
                throw;
            }
        }

        public async Task<bool> DeleteReadingAsync(int id, string deletedBy)
        {
            try
            {
                var reading = await _context.MeterReadings.FindAsync(id);
                if (reading == null)
                {
                    return false;
                }

                _context.MeterReadings.Remove(reading);
                await _context.SaveChangesAsync();

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting meter reading {Id}", id);
                throw;
            }
        }

        public async Task<bool> ConfirmReadingAsync(int id, string confirmedBy)
        {
            try
            {
                var reading = await _context.MeterReadings.FindAsync(id);
                if (reading == null)
                {
                    return false;
                }

                reading.Status = "Confirmed";
                reading.ValidationStatus = "Valid";
                reading.IsValidated = true;
                reading.ValidatedBy = 1; // TODO: 从confirmedBy参数获取实际用户ID
                reading.ValidationDate = DateTime.UtcNow;
                reading.UpdatedBy = confirmedBy;
                reading.UpdatedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error confirming meter reading {Id}", id);
                throw;
            }
        }

        public async Task<bool> CorrectReadingAsync(int id, decimal correctedValue, string reason, string correctedBy)
        {
            try
            {
                var reading = await _context.MeterReadings.FindAsync(id);
                if (reading == null)
                {
                    return false;
                }

                // Store original value in notes
                var originalValue = reading.ReadingValue;
                reading.ReadingValue = correctedValue;
                reading.Status = "Corrected";
                reading.ValidationStatus = "Corrected";
                reading.Notes = $"Original: {originalValue}, Corrected: {correctedValue}. Reason: {reason}";
                reading.IsValidated = true;
                reading.ValidatedBy = 1; // TODO: 从correctedBy参数获取实际用户ID
                reading.ValidationDate = DateTime.UtcNow;
                reading.ValidationComments = reason;
                reading.UpdatedBy = correctedBy;
                reading.UpdatedAt = DateTime.UtcNow;

                // 注意：Consumption字段已删除，现在通过计算获得

                await _context.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error correcting meter reading {Id}", id);
                throw;
            }
        }

        #endregion

        #region Photo Management

        public async Task<List<ReadingPhotoDto>> GetReadingPhotosAsync(int readingId)
        {
            try
            {
                return new List<ReadingPhotoDto>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting photos for reading {ReadingId}", readingId);
                throw;
            }
        }

        public async Task<ReadingPhotoDto?> GetPhotoByIdAsync(int id)
        {
            try
            {
                var photo = await _context.ReadingPhotos
                    .Include(p => p.MeterReading)
                    .ThenInclude(r => r.WaterMeter)
                    .Where(p => p.Id == id && !p.IsDeleted)
                    .FirstOrDefaultAsync();

                if (photo == null)
                {
                    return null;
                }

                return new ReadingPhotoDto
                {
                    Id = photo.Id,
                    ReadingId = photo.ReadingId,
                    MeterReadingId = photo.ReadingId,
                    FileName = photo.FileName,
                    OriginalFileName = photo.OriginalFileName,
                    CloudflareUrl = photo.CloudflareUrl,
                    ThumbnailUrl = photo.ThumbnailUrl,
                    FileSizeBytes = (int)photo.FileSizeBytes,
                    FileSize = photo.FileSizeBytes,
                    MimeType = photo.MimeType,
                    UploadTime = photo.UploadTime,
                    QualityScore = photo.QualityScore,
                    QualityStatus = photo.QualityStatus,
                    IsProcessed = photo.IsProcessed,
                    HasOCR = !string.IsNullOrEmpty(photo.OcrResult),
                    OcrResult = photo.OcrResult,
                    OcrConfidence = photo.OcrConfidence,
                    OcrStatus = photo.OCRStatus,
                    IsOverridden = photo.IsOverridden,
                    OverriddenBy = photo.OverriddenBy,
                    OverriddenDate = photo.OverriddenDate.HasValue ? photo.OverriddenDate.Value.ToString("O") : null,
                    OverrideReason = photo.OverrideReason,
                    CreatedAt = photo.CreatedAt.ToString("O")
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting photo by id {Id}", id);
                throw;
            }
        }

        public async Task<List<ReadingPhotoDto>> SearchPhotosAsync(PhotoSearchDto searchDto)
        {
            try
            {
                var query = _context.ReadingPhotos
                    .Include(p => p.MeterReading)
                    .ThenInclude(r => r.WaterMeter)
                    .Where(p => !p.IsDeleted)
                    .AsQueryable();

                if (!string.IsNullOrWhiteSpace(searchDto.MeterNumber))
                {
                    query = query.Where(p => p.MeterReading.WaterMeter.SerialNumber.Contains(searchDto.MeterNumber));
                }

                if (searchDto.StartDate.HasValue)
                {
                    query = query.Where(p => p.UploadTime >= searchDto.StartDate.Value);
                }

                if (searchDto.EndDate.HasValue)
                {
                    query = query.Where(p => p.UploadTime <= searchDto.EndDate.Value);
                }

                if (!string.IsNullOrWhiteSpace(searchDto.QualityStatus))
                {
                    query = query.Where(p => p.QualityStatus == searchDto.QualityStatus);
                }

                if (!string.IsNullOrWhiteSpace(searchDto.OCRStatus))
                {
                    query = query.Where(p => p.OCRStatus == searchDto.OCRStatus);
                }

                if (searchDto.IsOverridden.HasValue)
                {
                    query = query.Where(p => p.IsOverridden == searchDto.IsOverridden.Value);
                }

                var totalCount = await query.CountAsync();

                var photos = await query
                    .OrderByDescending(p => p.UploadTime)
                    .Skip((searchDto.Page - 1) * searchDto.PageSize)
                    .Take(searchDto.PageSize)
                    .Select(p => new ReadingPhotoDto
                    {
                        Id = p.Id,
                        ReadingId = p.ReadingId,
                        MeterReadingId = p.ReadingId,
                        FileName = p.FileName,
                        OriginalFileName = p.OriginalFileName,
                        CloudflareUrl = p.CloudflareUrl,
                        ThumbnailUrl = p.ThumbnailUrl,
                        FileSizeBytes = (int)p.FileSizeBytes,
                        FileSize = p.FileSizeBytes,
                        MimeType = p.MimeType,
                        UploadTime = p.UploadTime,
                        QualityScore = p.QualityScore,
                        QualityStatus = p.QualityStatus,
                        IsProcessed = p.IsProcessed,
                        HasOCR = !string.IsNullOrEmpty(p.OcrResult),
                        OcrResult = p.OcrResult,
                        OcrConfidence = p.OcrConfidence,
                        OcrStatus = p.OCRStatus,
                        IsOverridden = p.IsOverridden,
                        OverriddenBy = p.OverriddenBy,
                        OverriddenDate = p.OverriddenDate.HasValue ? p.OverriddenDate.Value.ToString("O") : null,
                        OverrideReason = p.OverrideReason,
                        CreatedAt = p.CreatedAt.ToString("O")
                    })
                    .ToListAsync();

                _logger.LogInformation("Found {Count} photos matching search criteria", photos.Count);
                return photos;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error searching photos");
                throw;
            }
        }

        public async Task<ReadingPhotoDto> UploadPhotoAsync(int readingId, string fileName, string filePath, string uploadedBy)
        {
            try
            {
                _logger.LogInformation("Uploading photo for reading {ReadingId}: {FileName}", readingId, fileName);

                // 检查reading是否存在
                var reading = await _context.MeterReadings.FindAsync(readingId);
                if (reading == null)
                {
                    throw new ArgumentException($"Reading with ID {readingId} not found");
                }

                // 检查文件是否存在
                if (!File.Exists(filePath))
                {
                    throw new FileNotFoundException($"File not found: {filePath}");
                }

                // 读取文件并上传到R2
                using var fileStream = File.OpenRead(filePath);
                var contentType = GetContentType(fileName);
                var cloudflareUrl = await _r2Service.UploadPhotoAsync(fileStream, fileName, contentType);

                // 创建缩略图
                fileStream.Position = 0;
                var thumbnailUrl = await _r2Service.CreateThumbnailAsync(fileStream, fileName);

                // 保存到数据库
                var readingPhoto = new ReadingPhoto
                {
                    ReadingId = readingId,
                    OriginalFileName = fileName,
                    CloudflareUrl = cloudflareUrl,
                    ThumbnailUrl = thumbnailUrl,
                    FileSizeBytes = (int)new FileInfo(filePath).Length,
                    MimeType = contentType,
                    UploadTime = DateTime.UtcNow,
                    IsProcessed = true,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow,
                    CreatedBy = uploadedBy,
                    UpdatedBy = uploadedBy
                };

                _context.ReadingPhotos.Add(readingPhoto);
                await _context.SaveChangesAsync();

                _logger.LogInformation("Photo uploaded successfully: {PhotoId} -> {Url}", readingPhoto.Id, cloudflareUrl);

                return new ReadingPhotoDto
                {
                    Id = readingPhoto.Id,
                    ReadingId = readingId,
                    FileName = fileName,
                    CloudflareUrl = cloudflareUrl,
                    ThumbnailUrl = thumbnailUrl,
                    FileSizeBytes = (int)readingPhoto.FileSizeBytes,
                    FileSize = readingPhoto.FileSizeBytes,
                    MimeType = contentType,
                    UploadTime = readingPhoto.UploadTime,
                    IsProcessed = readingPhoto.IsProcessed
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error uploading photo for reading {ReadingId}: {FileName}", readingId, fileName);
                throw;
            }
        }

        private string GetContentType(string fileName)
        {
            var extension = Path.GetExtension(fileName).ToLowerInvariant();
            return extension switch
            {
                ".jpg" or ".jpeg" => "image/jpeg",
                ".png" => "image/png",
                ".bmp" => "image/bmp",
                ".gif" => "image/gif",
                _ => "application/octet-stream"
            };
        }

        public async Task<bool> UpdatePhotoQualityAsync(int photoId, decimal qualityScore, string qualityStatus, string updatedBy)
        {
            try
            {
                var photo = await _context.ReadingPhotos.FindAsync(photoId);
                if (photo == null)
                {
                    _logger.LogWarning("Photo with ID {PhotoId} not found", photoId);
                    return false;
                }

                photo.QualityScore = qualityScore;
                photo.QualityStatus = qualityStatus;
                photo.UpdatedAt = DateTime.UtcNow;
                photo.UpdatedBy = updatedBy;

                await _context.SaveChangesAsync();

                _logger.LogInformation("Photo {PhotoId} quality updated to {QualityScore}/{QualityStatus} by {UpdatedBy}",
                    photoId, qualityScore, qualityStatus, updatedBy);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating photo quality for photo {PhotoId}", photoId);
                throw;
            }
        }

        public async Task<bool> OverrideOCRResultAsync(int photoId, string correctedValue, string reason, string overriddenBy)
        {
            try
            {
                var photo = await _context.ReadingPhotos.FindAsync(photoId);
                if (photo == null)
                {
                    _logger.LogWarning("Photo with ID {PhotoId} not found", photoId);
                    return false;
                }

                photo.OcrResult = correctedValue;
                photo.IsOverridden = true;
                photo.OverriddenBy = overriddenBy;
                photo.OverriddenDate = DateTime.UtcNow;
                photo.OverrideReason = reason;
                photo.UpdatedAt = DateTime.UtcNow;
                photo.UpdatedBy = overriddenBy;

                await _context.SaveChangesAsync();

                _logger.LogInformation("OCR result for photo {PhotoId} overridden to '{CorrectedValue}' by {OverriddenBy}, reason: {Reason}",
                    photoId, correctedValue, overriddenBy, reason);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error overriding OCR result for photo {PhotoId}", photoId);
                throw;
            }
        }

        public async Task<bool> DeletePhotoAsync(int photoId, string deletedBy)
        {
            try
            {
                var photo = await _context.ReadingPhotos.FindAsync(photoId);
                if (photo == null)
                {
                    _logger.LogWarning("Photo with ID {PhotoId} not found", photoId);
                    return false;
                }

                photo.IsDeleted = true;
                photo.UpdatedAt = DateTime.UtcNow;
                photo.UpdatedBy = deletedBy;

                await _context.SaveChangesAsync();

                // Note: R2 file deletion is commented out to preserve files in storage
                // Uncomment below if you want to physically delete files from R2
                /*
                try
                {
                    var photoKey = _r2Service.ExtractKeyFromUrl(photo.CloudflareUrl);
                    await _r2Service.DeletePhotoAsync(photoKey);

                    if (!string.IsNullOrEmpty(photo.ThumbnailUrl))
                    {
                        var thumbnailKey = _r2Service.ExtractKeyFromUrl(photo.ThumbnailUrl);
                        await _r2Service.DeletePhotoAsync(thumbnailKey);
                    }
                }
                catch (Exception r2Ex)
                {
                    _logger.LogWarning(r2Ex, "Failed to delete photo from R2 storage, but database record marked as deleted");
                }
                */

                _logger.LogInformation("Photo {PhotoId} deleted by {DeletedBy}", photoId, deletedBy);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting photo {PhotoId}", photoId);
                throw;
            }
        }

        #endregion

        #region Anomaly Management

        public async Task<List<ReadingAnomalyDto>> GetAnomaliesAsync(AnomalySearchDto searchDto)
        {
            try
            {
                var mockAnomalies = new List<ReadingAnomalyDto>
                {
                    new ReadingAnomalyDto
                    {
                        Id = 1,
                        MeterReadingId = 1,
                        MeterNumber = "WM001",
                        AnomalyType = "High Usage",
                        Severity = "Medium",
                        Description = "Usage 200% higher than average",
                        ExpectedValue = 100,
                        ActualValue = 300,
                        Variance = 200,
                        ConfidenceScore = 0.85m,
                        Status = "Open",
                        RequiresFieldVisit = true,
                        IsRecurring = false,
                        CreatedAt = DateTime.UtcNow
                    }
                };

                return await Task.FromResult(mockAnomalies);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting anomalies");
                throw;
            }
        }

        public async Task<ReadingAnomalyDto?> GetAnomalyByIdAsync(int id)
        {
            try
            {
                return await Task.FromResult<ReadingAnomalyDto?>(null);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting anomaly by id {Id}", id);
                throw;
            }
        }

        public async Task<ReadingAnomalyDto> CreateAnomalyAsync(CreateAnomalyDto createDto, string createdBy)
        {
            try
            {
                throw new NotImplementedException("Create anomaly not implemented yet");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating anomaly");
                throw;
            }
        }

        public async Task<ReadingAnomalyDto> UpdateAnomalyAsync(int id, UpdateAnomalyDto updateDto, string updatedBy)
        {
            try
            {
                throw new NotImplementedException("Update anomaly not implemented yet");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating anomaly");
                throw;
            }
        }

        public async Task<bool> AssignAnomalyAsync(int anomalyId, int assignedUserId, string assignedBy)
        {
            try
            {
                var anomaly = await _context.ReadingAnomalies
                    .FirstOrDefaultAsync(a => a.Id == anomalyId && !a.IsDeleted);

                if (anomaly == null)
                    return false;

                anomaly.AssignedUserId = assignedUserId;
                anomaly.AssignedDate = DateTime.UtcNow;
                anomaly.UpdatedAt = DateTime.UtcNow;
                anomaly.UpdatedBy = assignedBy;

                await _context.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error assigning anomaly {AnomalyId} to user {UserId}", anomalyId, assignedUserId);
                throw;
            }
        }

        public async Task<bool> ResolveAnomalyAsync(int anomalyId, string resolution, string resolutionType, string resolvedBy)
        {
            try
            {
                return await Task.FromResult(true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error resolving anomaly");
                throw;
            }
        }

        public async Task<bool> DeleteAnomalyAsync(int id, string deletedBy)
        {
            try
            {
                return await Task.FromResult(true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting anomaly");
                throw;
            }
        }

        public async Task<List<ReadingAnomalyDto>> GetUnresolvedAnomaliesAsync()
        {
            try
            {
                return await Task.FromResult(new List<ReadingAnomalyDto>());
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting unresolved anomalies");
                throw;
            }
        }

        public async Task<List<ReadingAnomalyDto>> GetRecurringAnomaliesAsync()
        {
            try
            {
                return await Task.FromResult(new List<ReadingAnomalyDto>());
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting recurring anomalies");
                throw;
            }
        }

        #endregion

        #region Validation Rules Management

        public async Task<List<ValidationRuleDto>> GetValidationRulesAsync()
        {
            try
            {
                var mockRules = new List<ValidationRuleDto>
                {
                    new ValidationRuleDto
                    {
                        Id = 1,
                        RuleName = "Maximum Daily Usage",
                        RuleType = "Usage Validation",
                        Description = "Check if daily usage exceeds maximum threshold",
                        IsActive = true,
                        Priority = 1,
                        MaxToleranceValue = 1000,
                        WarningThreshold = 800,
                        ErrorThreshold = 1000,
                        CriticalThreshold = 1200,
                        ActionOnViolation = "Flag for Review",
                        AutoCorrect = false,
                        CreatedAt = DateTime.UtcNow
                    }
                };

                return await Task.FromResult(mockRules);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting validation rules");
                throw;
            }
        }

        public async Task<ValidationRuleDto?> GetValidationRuleByIdAsync(int id)
        {
            try
            {
                return await Task.FromResult<ValidationRuleDto?>(null);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting validation rule by id {Id}", id);
                throw;
            }
        }

        public async Task<ValidationRuleDto> CreateValidationRuleAsync(CreateValidationRuleDto createDto, string createdBy)
        {
            try
            {
                throw new NotImplementedException("Create validation rule not implemented yet");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating validation rule");
                throw;
            }
        }

        public async Task<ValidationRuleDto> UpdateValidationRuleAsync(int id, CreateValidationRuleDto updateDto, string updatedBy)
        {
            try
            {
                throw new NotImplementedException("Update validation rule not implemented yet");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating validation rule");
                throw;
            }
        }

        public async Task<bool> DeleteValidationRuleAsync(int id, string deletedBy)
        {
            try
            {
                return await Task.FromResult(true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting validation rule");
                throw;
            }
        }

        public async Task<bool> ActivateValidationRuleAsync(int id, bool isActive, string updatedBy)
        {
            try
            {
                return await Task.FromResult(true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error activating validation rule");
                throw;
            }
        }

        public async Task<List<ValidationRuleDto>> GetActiveValidationRulesAsync()
        {
            try
            {
                return await Task.FromResult(new List<ValidationRuleDto>());
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting active validation rules");
                throw;
            }
        }

        #endregion

        #region OCR Management

        public async Task<List<OCRRecordDto>> GetOCRRecordsAsync(int readingId)
        {
            try
            {
                return await Task.FromResult(new List<OCRRecordDto>());
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting OCR records");
                throw;
            }
        }

        public async Task<OCRRecordDto?> GetOCRRecordByIdAsync(int id)
        {
            try
            {
                return await Task.FromResult<OCRRecordDto?>(null);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting OCR record by id {Id}", id);
                throw;
            }
        }

        public async Task<OCRRecordDto> ProcessOCRAsync(int photoId, string processingEngine, string processedBy)
        {
            try
            {
                throw new NotImplementedException("OCR processing not implemented yet");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing OCR");
                throw;
            }
        }

        public async Task<bool> VerifyOCRResultAsync(int ocrId, string correctedValue, string reason, string verifiedBy)
        {
            try
            {
                return await Task.FromResult(true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error verifying OCR result");
                throw;
            }
        }

        public async Task<bool> MarkAsTrainingDataAsync(int ocrId, string trainingLabel, string updatedBy)
        {
            try
            {
                return await Task.FromResult(true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error marking as training data");
                throw;
            }
        }

        #endregion

        #region Business Logic Operations

        public async Task<bool> ValidateReadingAsync(int readingId, string validatedBy)
        {
            try
            {
                return await Task.FromResult(true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating reading");
                throw;
            }
        }

        public async Task<List<ReadingAnomalyDto>> DetectAnomaliesAsync(int readingId)
        {
            try
            {
                return await Task.FromResult(new List<ReadingAnomalyDto>());
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error detecting anomalies");
                throw;
            }
        }

        public async Task<bool> ProcessReadingWorkflowAsync(int readingId, string processedBy)
        {
            try
            {
                return await Task.FromResult(true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing reading workflow");
                throw;
            }
        }

        public async Task<decimal> CalculateUsageAsync(int readingId)
        {
            try
            {
                return await Task.FromResult(0m);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calculating usage");
                throw;
            }
        }

        public async Task<bool> ApplyValidationRulesAsync(int readingId)
        {
            try
            {
                return await Task.FromResult(true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error applying validation rules");
                throw;
            }
        }

        #endregion

        #region Statistics and Reporting

        public async Task<MeterReadingStatisticsDto> GetReadingStatisticsAsync()
        {
            try
            {
                var totalReadings = await _context.MeterReadings.CountAsync();
                var pendingReadings = await _context.MeterReadings.CountAsync(r => r.Status == "Pending");
                var confirmedReadings = await _context.MeterReadings.CountAsync(r => r.Status == "Confirmed");
                var anomalyReadings = await _context.MeterReadings.CountAsync(r => r.Status == "Anomaly");
                var correctedReadings = await _context.MeterReadings.CountAsync(r => r.Status == "Corrected");
                var readingsWithPhoto = await _context.MeterReadings.CountAsync(r => r.Photos.Any());
                var readingsWithOCR = await _context.MeterReadings.CountAsync(r => r.HasOCR == true);

                var statusCounts = await _context.MeterReadings
                    .GroupBy(r => r.Status)
                    .Select(g => new { Status = g.Key, Count = g.Count() })
                    .ToListAsync();

                return new MeterReadingStatisticsDto
                {
                    TotalReadings = totalReadings,
                    PendingReadings = pendingReadings,
                    ConfirmedReadings = confirmedReadings,
                    AnomalyReadings = anomalyReadings,
                    CorrectedReadings = correctedReadings,
                    ReadingsWithPhoto = readingsWithPhoto,
                    ReadingsWithOCR = readingsWithOCR,
                    OCRSuccessRate = readingsWithOCR > 0 ? (decimal)readingsWithOCR / totalReadings * 100 : 0,
                    AnomalyDetectionRate = totalReadings > 0 ? (decimal)anomalyReadings / totalReadings * 100 : 0,
                    DataQualityScore = 85.5m,
                    StatusCounts = statusCounts.Select(sc => new ReadingStatusCountDto 
                    { 
                        Status = sc.Status, 
                        Count = sc.Count, 
                        Percentage = totalReadings > 0 ? (decimal)sc.Count / totalReadings * 100 : 0 
                    }).ToList(),
                    AnomalyTypeCounts = new List<AnomalyTypeCountDto>()
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting reading statistics");
                throw;
            }
        }

        public async Task<List<ReadingStatusCountDto>> GetReadingStatusCountsAsync()
        {
            try
            {
                return await Task.FromResult(new List<ReadingStatusCountDto>());
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting reading status counts");
                throw;
            }
        }

        public async Task<List<AnomalyTypeCountDto>> GetAnomalyTypeCountsAsync()
        {
            try
            {
                return await Task.FromResult(new List<AnomalyTypeCountDto>());
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting anomaly type counts");
                throw;
            }
        }

        public async Task<decimal> GetOCRAccuracyRateAsync()
        {
            try
            {
                return await Task.FromResult(95.5m);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting OCR accuracy rate");
                throw;
            }
        }

        public async Task<decimal> GetDataQualityScoreAsync()
        {
            try
            {
                return await Task.FromResult(87.2m);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting data quality score");
                throw;
            }
        }

        #endregion

        #region Export and Import

        public async Task<byte[]> ExportReadingsAsync(MeterReadingSearchDto? searchDto = null)
        {
            try
            {
                throw new NotImplementedException("Export not implemented yet");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error exporting readings");
                throw;
            }
        }

        public async Task<(bool Success, string Message, int ImportedCount)> ImportReadingsAsync(Stream fileStream, string importedBy)
        {
            try
            {
                throw new NotImplementedException("Import not implemented yet");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error importing readings");
                throw;
            }
        }

        public async Task<byte[]> ExportAnomaliesAsync(AnomalySearchDto? searchDto = null)
        {
            try
            {
                throw new NotImplementedException("Export anomalies not implemented yet");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error exporting anomalies");
                throw;
            }
        }

        public async Task<byte[]> ExportValidationRulesAsync()
        {
            try
            {
                throw new NotImplementedException("Export validation rules not implemented yet");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error exporting validation rules");
                throw;
            }
        }

        #endregion

        #region Utility Methods

        public async Task<List<string>> GetMeterNumbersAsync()
        {
            try
            {
                return await _context.MeterReadings
                    .Select(r => r.WaterMeter.SerialNumber)
                    .Distinct()
                    .OrderBy(mn => mn)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting meter numbers");
                throw;
            }
        }

        public async Task<List<string>> GetAnomalyTypesAsync()
        {
            try
            {
                return await Task.FromResult(new List<string> { "High Usage", "Zero Usage", "Negative Reading", "Data Quality" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting anomaly types");
                throw;
            }
        }

        public async Task<List<string>> GetValidationRuleTypesAsync()
        {
            try
            {
                return await Task.FromResult(new List<string> { "Usage Validation", "Data Integrity", "Threshold Check", "Seasonal Validation" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting validation rule types");
                throw;
            }
        }

        public async Task<List<MeterReadingListDto>> GetReadingHistoryAsync(string meterNumber, int? months = null)
        {
            try
            {
                var query = _context.MeterReadings.AsQueryable();
                
                if (!string.IsNullOrWhiteSpace(meterNumber))
                {
                    query = query.Where(r => r.WaterMeter.SerialNumber.Contains(meterNumber));
                }
                
                if (months.HasValue)
                {
                    var cutoffDate = DateTime.UtcNow.AddMonths(-months.Value);
                    query = query.Where(r => r.ReadingDate >= cutoffDate);
                }
                
                var readings = await query
                    .OrderByDescending(r => r.ReadingDate)
                    .Select(r => new MeterReadingListDto
                    {
                        Id = r.Id,
                        MeterId = r.MeterId,
                        UserId = r.UserId,
                        TaskId = r.TaskId,
                        MeterNumber = r.WaterMeter.SerialNumber,
                        ReadingValue = r.ReadingValue,
                        PreviousReading = null, // 删除的字段
                        Consumption = null, // 删除的字段
                        ReadingDate = r.ReadingDate,
                        ReadBy = r.User.Username ?? r.CreatedBy,
                        Status = r.Status,
                        ValidationStatus = r.ValidationStatus,
                        AnomalyType = r.AnomalyType,
                        Notes = r.Notes,
                        Location = r.Location,
                        HasPhoto = r.Photos.Any(),
                        HasOCR = r.HasOCR,
                        OCRStatus = r.OCRStatus,
                        OCRConfidence = r.OCRConfidence,
                        ReadingMethod = r.ReadingMethod,
                        ReadingType = r.ReadingType,
                        DataSource = r.DataSource,
                        IsAnomalous = r.IsAnomalous,
                        CantRead = r.CantRead,
                        IsValidated = r.IsValidated,
                        Latitude = r.Latitude,
                        Longitude = r.Longitude,
                        GpsAccuracy = r.GpsAccuracy,
                        ValidatedBy = r.ValidatedBy,
                        ValidationDate = r.ValidationDate,
                        ValidationComments = r.ValidationComments,
                        AnomalyReason = r.AnomalyReason,
                        CantReadReason = r.CantReadReason,
                        CreatedBy = r.CreatedBy,
                        UpdatedBy = r.UpdatedBy,
                        IsDeleted = r.IsDeleted,
                        CreatedAt = r.CreatedAt,
                        UpdatedAt = r.UpdatedAt
                    })
                    .ToListAsync();
                
                return readings;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting reading history for meter {MeterNumber}", meterNumber);
                throw;
            }
        }

        #endregion

        #region Mobile-specific methods

        public async Task<ReadingValidationDto> ValidateMobileReadingAsync(MobileReadingDto reading, int userId)
        {
            try
            {
                var validation = new ReadingValidationDto
                {
                    IsValid = true,
                    Errors = new List<string>(),
                    Warnings = new List<string>()
                };

                // Basic validation
                if (reading == null)
                {
                    validation.IsValid = false;
                    validation.Errors.Add("Reading data is required");
                    return validation;
                }

                if (string.IsNullOrEmpty(reading.MeterNumber))
                {
                    validation.IsValid = false;
                    validation.Errors.Add("Meter number is required");
                }

                if (reading.ReadingValue < 0)
                {
                    validation.IsValid = false;
                    validation.Errors.Add("Reading value must be non-negative");
                }

                return validation;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating mobile reading");
                return new ReadingValidationDto
                {
                    IsValid = false,
                    Errors = new List<string> { "Validation failed due to system error" },
                    Warnings = new List<string>()
                };
            }
        }

        public async Task<ReadingResponseDto> SubmitMobileReadingAsync(MobileReadingDto request, int userId)
        {
            try
            {
                var createDto = new CreateMeterReadingDto
                {
                    MeterId = request.MeterId,
                    UserId = userId,
                    TaskId = request.TaskId,
                    ReadingValue = (decimal)request.ReadingValue,
                    ReadingDate = request.ReadingDate,
                    Notes = request.Notes,
                    Location = request.Location,
                    ReadingMethod = request.ReadingMethod,
                    ReadingType = request.ReadingType,
                    DataSource = request.DataSource
                };

                var result = await CreateReadingAsync(createDto, userId.ToString());

                // Process photos if any
                if (request.Photos != null && request.Photos.Count > 0 && result != null)
                {
                    _logger.LogInformation("Processing {PhotoCount} photos for new reading {ReadingId}", request.Photos.Count, result.Id);
                    await ProcessReadingPhotosAsync(result.Id, request.Photos, userId);
                }

                return new ReadingResponseDto
                {
                    Success = true,
                    Message = "Reading submitted successfully",
                    ReadingId = result?.Id ?? 0 // ReadingId 是 int 类型，可以直接赋值 0
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error submitting mobile reading");
                return new ReadingResponseDto
                {
                    Success = false,
                    Message = ex.Message,
                    ReadingId = 0 // ReadingId 是 int 类型，不能为 null，失败时设为 0
                };
            }
        }

        public async Task<BatchReadingResponseDto> SubmitBatchMobileReadingsAsync(BatchReadingRequest request, int userId)
        {
            var successCount = 0;
            var failedReadings = new List<string>();

            foreach (var reading in request.Readings)
            {
                try
                {
                    await SubmitMobileReadingAsync(reading, userId);
                    successCount++;
                }
                catch (Exception ex)
                {
                    failedReadings.Add($"Meter {reading.MeterNumber}: {ex.Message}");
                    _logger.LogError(ex, "Failed to submit reading for meter {MeterNumber}", reading.MeterNumber);
                }
            }

            return new BatchReadingResponseDto
            {
                TotalSubmitted = request.Readings.Count,
                SuccessfullyProcessed = successCount,
                Failed = failedReadings.Count,
                SuccessCount = successCount,
                FailedCount = failedReadings.Count,
                FailedReadings = failedReadings,
                Results = new List<ReadingResponseDto>(),
                GeneralErrors = new List<string>()
            };
        }

        public async Task<List<MobileReadingHistoryDto>> GetMobileReadingHistoryAsync(int meterId, int limit)
        {
            try
            {
                var readings = await _context.MeterReadings
                    .Where(r => r.MeterId == meterId)
                    .OrderByDescending(r => r.ReadingDate)
                    .Take(limit)
                    .Select(r => new MobileReadingHistoryDto
                    {
                        Id = r.Id,
                        ReadingValue = (double)r.ReadingValue,
                        ReadingDate = r.ReadingDate,
                        ReadingMethod = "Manual", // 默认值
                        Notes = r.Notes,
                        ReadByUser = r.User.Username ?? r.CreatedBy ?? "Unknown",
                        QualityScore = null // 暂时设为 null
                    })
                    .ToListAsync();

                return readings;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting reading history for meter {MeterId}", meterId);
                return new List<MobileReadingHistoryDto>();
            }
        }

        public async Task<List<MobileReadingHistoryDto>> GetUserRecentReadingsAsync(int userId, int page, int limit)
        {
            try
            {
                var skip = (page - 1) * limit;
                var readings = await _context.MeterReadings
                    .Where(r => r.UserId == userId)
                    .OrderByDescending(r => r.ReadingDate)
                    .Skip(skip)
                    .Take(limit)
                    .Select(r => new MobileReadingHistoryDto
                    {
                        Id = r.Id,
                        ReadingValue = (double)r.ReadingValue,
                        ReadingDate = r.ReadingDate,
                        ReadingMethod = "Manual", // 默认值
                        Notes = r.Notes,
                        ReadByUser = r.User.Username ?? r.CreatedBy ?? "Unknown",
                        QualityScore = null // 暂时设为 null
                    })
                    .ToListAsync();

                return readings;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting recent readings for user {UserId}", userId);
                return new List<MobileReadingHistoryDto>();
            }
        }

        public async Task<ReadingResponseDto> UpdateMobileReadingAsync(int id, UpdateReadingRequest request, int userId)
        {
            try
            {
                var updateDto = new UpdateMeterReadingDto
                {
                    ReadingValue = (decimal)request.ReadingValue, // 修正属性名和类型转换
                    Notes = request.Notes
                    // Location 属性在 UpdateReadingRequest 中不存在，移除
                };

                await UpdateReadingAsync(id, updateDto, userId.ToString());
                
                return new ReadingResponseDto
                {
                    Success = true,
                    Message = "Reading updated successfully",
                    ReadingId = id
                };
            }
            catch (Exception ex)
            {
                return new ReadingResponseDto
                {
                    Success = false,
                    Message = ex.Message,
                    ReadingId = id
                };
            }
        }

        public async Task<bool> DeleteMobileReadingAsync(int id, int userId)
        {
            return await DeleteReadingAsync(id, userId.ToString());
        }

        public async Task<ReadingResponseDto> SubmitReadingAndCompleteTaskAsync(CompleteTaskRequest request, int userId)
        {
            try
            {
                // CompleteTaskRequest.Reading.TaskId 才有 TaskId
                var mobileReading = new MobileReadingDto
                {
                    TaskId = request.Reading.TaskId,
                    MeterId = request.Reading.MeterId,
                    MeterNumber = request.Reading.MeterNumber,
                    ReadingValue = request.Reading.ReadingValue,
                    ReadingDate = request.Reading.ReadingDate,
                    Notes = request.Reading.Notes,
                    Location = request.Reading.Location
                };

                var response = await SubmitMobileReadingAsync(mobileReading, userId);
                
                if (response.Success)
                {
                    response.Message += " and task completed";
                }
                
                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error submitting reading and completing task");
                return new ReadingResponseDto
                {
                    Success = false,
                    Message = ex.Message
                };
            }
        }

        public async Task<MobileReadingStatsDto> GetUserReadingStatsAsync(int userId, DateTime? fromDate = null, DateTime? toDate = null)
        {
            try
            {
                var query = _context.MeterReadings.Where(r => r.UserId == userId);
                
                if (fromDate.HasValue)
                    query = query.Where(r => r.ReadingDate >= fromDate.Value);
                
                if (toDate.HasValue)
                    query = query.Where(r => r.ReadingDate <= toDate.Value);

                var totalReadings = await query.CountAsync();
                var lastReadingDate = await query.OrderByDescending(r => r.ReadingDate).Select(r => r.ReadingDate).FirstOrDefaultAsync();

                return new MobileReadingStatsDto
                {
                    TotalReadings = totalReadings,
                    ReadingsThisWeek = 0, // TODO: Calculate
                    ReadingsThisMonth = 0, // TODO: Calculate
                    AverageQualityScore = 0, // TODO: Calculate from quality scores
                    PhotosUploaded = 0, // TODO: Count photos
                    IssuesReported = 0, // TODO: Count issues
                    AverageReadingTime = 0, // TODO: Calculate average time
                    LastReadingDate = lastReadingDate,
                    ConsecutiveDaysWithReadings = 0, // TODO: Calculate
                    Last7Days = new List<DailyReadingCount>() // TODO: Calculate last 7 days
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting user reading stats for user {UserId}", userId);
                return new MobileReadingStatsDto
                {
                    TotalReadings = 0,
                    ReadingsThisWeek = 0,
                    ReadingsThisMonth = 0,
                    AverageQualityScore = 0,
                    PhotosUploaded = 0,
                    IssuesReported = 0,
                    AverageReadingTime = 0,
                    LastReadingDate = null,
                    ConsecutiveDaysWithReadings = 0,
                    Last7Days = new List<DailyReadingCount>()
                };
            }
        }

        public async Task<OfflineReadingSyncResponse> SyncOfflineReadingsAsync(OfflineReadingSyncRequest request, int userId)
        {
            try
            {
                var batchRequest = new BatchReadingRequest
                {
                    Readings = request.OfflineReadings.Select(or => new MobileReadingDto
                    {
                        TaskId = or.TaskId,
                        MeterId = or.MeterId,
                        ReadingValue = (decimal)or.ReadingValue,
                        ReadingDate = or.ReadingDate,
                        Notes = or.Notes,
                        UserId = userId
                    }).ToList()
                };

                var batchResponse = await SubmitBatchMobileReadingsAsync(batchRequest, userId);
                
                return new OfflineReadingSyncResponse
                {
                    TotalOfflineReadings = request.OfflineReadings.Count,
                    SuccessfullySynced = batchResponse.SuccessCount,
                    Failed = batchResponse.FailedCount,
                    Results = new List<OfflineReadingSyncResult>(),
                    GeneralErrors = batchResponse.GeneralErrors
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error syncing offline readings for user {UserId}", userId);
                return new OfflineReadingSyncResponse
                {
                    TotalOfflineReadings = request.OfflineReadings.Count,
                    SuccessfullySynced = 0,
                    Failed = request.OfflineReadings.Count,
                    Results = new List<OfflineReadingSyncResult>(),
                    GeneralErrors = new List<string> { "Sync failed due to system error" }
                };
            }
        }

        // Enhanced mobile reading operations for GPS, OCR, and validation data
        public async Task<EnhancedReadingValidationDto> ValidateEnhancedMobileReadingAsync(MobileReadingDto reading, int userId)
        {
            try
            {
                var validation = new EnhancedReadingValidationDto
                {
                    IsValid = true,
                    Errors = new List<string>(),
                    Warnings = new List<string>(),
                    ValidationScore = 100,
                    ValidationStatus = "passed"
                };

                // Basic validation
                if (reading == null)
                {
                    validation.IsValid = false;
                    validation.ValidationScore = 0;
                    validation.ValidationStatus = "failed";
                    validation.Errors.Add("Reading data is required");
                    return validation;
                }

                // Validate required fields
                if (string.IsNullOrEmpty(reading.MeterNumber))
                {
                    validation.IsValid = false;
                    validation.Errors.Add("Meter number is required");
                    validation.ValidationScore -= 30;
                }

                if (reading.ReadingValue < 0)
                {
                    validation.IsValid = false;
                    validation.Errors.Add("Reading value must be non-negative");
                    validation.ValidationScore -= 40;
                }

                // Enhanced GPS validation
                if (reading.Latitude.HasValue && reading.Longitude.HasValue && reading.GpsAccuracy.HasValue)
                {
                    validation.IsGpsDataValid = true;
                    validation.GpsAccuracy = reading.GpsAccuracy.Value;

                    if (reading.GpsAccuracy.Value > 50)
                    {
                        validation.Warnings.Add("GPS accuracy is poor (>50m)");
                        validation.GpsValidationMessage = $"Poor GPS accuracy: {reading.GpsAccuracy.Value:F1}m";
                        validation.ValidationScore -= 20;
                        validation.RequiresReview = true;
                        validation.ReviewReason = "Poor GPS accuracy requires location verification";
                    }
                    else if (reading.GpsAccuracy.Value > 20)
                    {
                        validation.Warnings.Add("GPS accuracy is moderate");
                        validation.GpsValidationMessage = $"Moderate GPS accuracy: {reading.GpsAccuracy.Value:F1}m";
                        validation.ValidationScore -= 10;
                    }
                    else
                    {
                        validation.GpsValidationMessage = $"Good GPS accuracy: {reading.GpsAccuracy.Value:F1}m";
                    }
                }
                else
                {
                    validation.IsGpsDataValid = false;
                    validation.Warnings.Add("GPS data is missing");
                    validation.GpsValidationMessage = "No GPS coordinates provided";
                    validation.ValidationScore -= 15;
                }

                // Enhanced OCR validation
                if (reading.OcrConfidence.HasValue)
                {
                    validation.IsOcrDataValid = true;
                    validation.OcrConfidence = reading.OcrConfidence.Value;

                    if (reading.OcrConfidence.Value < 0.6m)
                    {
                        validation.Warnings.Add("OCR confidence is low");
                        validation.OcrValidationMessage = $"Low OCR confidence: {reading.OcrConfidence.Value * 100:F0}%";
                        validation.ValidationScore -= 30;
                        validation.RequiresReview = true;
                        validation.ReviewReason = (validation.ReviewReason ?? "") + "; Low OCR confidence requires manual verification";
                    }
                    else if (reading.OcrConfidence.Value < 0.8m)
                    {
                        validation.Warnings.Add("OCR confidence is moderate");
                        validation.OcrValidationMessage = $"Moderate OCR confidence: {reading.OcrConfidence.Value * 100:F0}%";
                        validation.ValidationScore -= 15;
                    }
                    else
                    {
                        validation.OcrValidationMessage = $"Good OCR confidence: {reading.OcrConfidence.Value * 100:F0}%";
                    }
                }

                // Business rule validation for consumption
                if (reading.ReadingValue > 0)
                {
                    // Check for extremely high consumption (simplified business rule)
                    if (reading.ReadingValue > 100000)
                    {
                        validation.IsAnomaly = true;
                        validation.AnomalyType = "extremely_high_reading";
                        validation.AnomalyReasons.Add("Reading value is extremely high");
                        validation.ValidationScore -= 25;
                        validation.RequiresReview = true;
                        validation.ReviewReason = (validation.ReviewReason ?? "") + "; Extremely high reading detected";
                    }

                    // Normal consumption validation
                    validation.IsConsumptionNormal = true;
                    validation.DailyConsumption = reading.ReadingValue; // Simplified - would calculate based on previous reading
                }

                // Determine final validation status
                if (!validation.IsValid)
                {
                    validation.ValidationStatus = "failed";
                }
                else if (validation.RequiresReview)
                {
                    validation.ValidationStatus = "requires_review";
                }
                else if (validation.ValidationScore < 80)
                {
                    validation.ValidationStatus = "warning";
                }
                else
                {
                    validation.ValidationStatus = "passed";
                }

                // Ensure validation score is within bounds
                validation.ValidationScore = Math.Max(0, Math.Min(100, validation.ValidationScore));

                _logger.LogInformation("Enhanced validation completed for meter {MeterNumber}: Score={Score}, Status={Status}, Anomaly={IsAnomaly}",
                    reading.MeterNumber, validation.ValidationScore, validation.ValidationStatus, validation.IsAnomaly);

                return validation;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error performing enhanced validation for mobile reading");
                return new EnhancedReadingValidationDto
                {
                    IsValid = false,
                    Errors = new List<string> { "Enhanced validation failed due to system error" },
                    ValidationScore = 0,
                    ValidationStatus = "failed"
                };
            }
        }

        public async Task<EnhancedReadingResponseDto> SubmitEnhancedMobileReadingAsync(MobileReadingDto reading, int userId)
        {
            try
            {
                _logger.LogInformation("Processing enhanced mobile reading submission - Meter: {MeterNumber}, Value: {Value}",
                    reading.MeterNumber, reading.ReadingValue);

                // Calculate consumption if previous reading exists
                decimal? consumption = null;
                if (reading.PreviousReading.HasValue)
                {
                    consumption = (decimal)reading.ReadingValue - (decimal)reading.PreviousReading.Value;
                }

                // Create enhanced meter reading record
                var meterReading = new MeterReading
                {
                    MeterId = reading.MeterId,
                    UserId = userId,
                    TaskId = reading.TaskId,
                    ReadingValue = (decimal)reading.ReadingValue,
                    ReadingDate = reading.ReadingDate,
                    ReadingMethod = reading.ReadingMethod ?? "Manual",
                    ReadingType = "Regular",
                    DataSource = "Mobile",
                    Status = "Completed", // Mark as completed since this is a mobile submission
                    ValidationStatus = reading.ValidationStatus ?? "Pending",
                    Notes = reading.Notes,
                    Location = reading.Location,
                    
                    // Enhanced OCR data
                    HasOCR = reading.OcrConfidence.HasValue,
                    OCRConfidence = reading.OcrConfidence.HasValue ? (decimal)reading.OcrConfidence.Value : null,
                    OCRStatus = reading.OcrConfidence.HasValue ? "Processed" : null,
                    
                    // GPS位置信息
                    Latitude = reading.Latitude.HasValue ? (decimal?)reading.Latitude.Value : null,
                    Longitude = reading.Longitude.HasValue ? (decimal?)reading.Longitude.Value : null,
                    GpsAccuracy = reading.GpsAccuracy.HasValue ? (decimal?)reading.GpsAccuracy.Value : null,
                    
                    // Validation and anomaly data
                    AnomalyType = reading.AnomalyType,
                    
                    // Audit fields
                    CreatedBy = userId.ToString(),
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow,
                    UpdatedBy = userId.ToString()
                };

                _context.MeterReadings.Add(meterReading);

                // Update task status to completed if task ID is provided
                if (reading.TaskId > 0)
                {
                    var task = await _context.Tasks.FindAsync(reading.TaskId);
                    if (task != null)
                    {
                        task.Status = "Completed";
                        task.CompletedDate = DateTime.UtcNow;
                        task.UpdatedAt = DateTime.UtcNow;
                        
                        _logger.LogInformation("Task {TaskId} marked as completed", reading.TaskId);
                    }
                }

                await _context.SaveChangesAsync();

                // Create enhanced response
                var response = new EnhancedReadingResponseDto
                {
                    ReadingId = meterReading.Id,
                    Success = true,
                    Message = "Enhanced reading submitted successfully",
                    ValidationScore = 100, // Would come from validation result
                    ValidationStatus = reading.ValidationStatus ?? "passed",
                    RequiresReview = reading.RequiresReview,
                    ReviewReason = reading.ReviewReason,
                    IsAnomaly = reading.IsAnomalous,
                    AnomalyType = reading.AnomalyType,
                    
                    // GPS validation results
                    IsGpsAccuracyAcceptable = reading.GpsAccuracy <= 20,
                    GpsAccuracy = reading.GpsAccuracy,
                    GpsValidationMessage = reading.GpsAccuracy.HasValue ? 
                        $"GPS accuracy: {reading.GpsAccuracy.Value:F1}m" : "No GPS data",
                    
                    // OCR validation results  
                    IsOcrConfidenceAcceptable = reading.OcrConfidence >= 0.8m,
                    OcrConfidence = reading.OcrConfidence,
                    OcrValidationMessage = reading.OcrConfidence.HasValue ?
                        $"OCR confidence: {reading.OcrConfidence.Value * 100:F0}%" : "No OCR data",
                    
                    // Consumption analysis
                    CalculatedConsumption = consumption,
                    IsConsumptionNormal = consumption == null || consumption.Value < 5000,
                    ConsumptionAnalysis = consumption.HasValue ? 
                        $"Consumption: {consumption.Value:N0} L" : "No previous reading for comparison",
                    
                    ProcessedAt = DateTime.UtcNow,
                    ProcessedBy = userId.ToString()
                };

                // Add next actions based on validation
                if (response.RequiresReview)
                {
                    response.NextActions.Add("Manual review required");
                }
                if (response.IsAnomaly)
                {
                    response.NextActions.Add("Investigate consumption anomaly");
                }
                if (!response.IsGpsAccuracyAcceptable)
                {
                    response.NextActions.Add("Verify location accuracy");
                }
                if (!response.IsOcrConfidenceAcceptable)
                {
                    response.NextActions.Add("Verify reading value");
                }

                _logger.LogInformation("Enhanced reading submitted successfully - ReadingId: {ReadingId}, TaskId: {TaskId}",
                    meterReading.Id, reading.TaskId);

                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error submitting enhanced mobile reading - TaskId: {TaskId}", reading.TaskId);
                return new EnhancedReadingResponseDto
                {
                    Success = false,
                    Message = "Failed to submit enhanced reading",
                    Errors = new List<string> { ex.Message },
                    ValidationStatus = "failed"
                };
            }
        }

        /// <summary>
        /// Get mobile reading by task ID
        /// </summary>
        public async Task<MobileReadingDto?> GetMobileReadingByTaskIdAsync(int taskId)
        {
            try
            {
                var reading = await _context.MeterReadings
                    .Include(r => r.WaterMeter)
                    .FirstOrDefaultAsync(r => r.TaskId == taskId && !r.IsDeleted);

                if (reading == null)
                    return null;

                return new MobileReadingDto
                {
                    TaskId = reading.TaskId,
                    MeterId = reading.MeterId,
                    UserId = reading.UserId,
                    MeterNumber = reading.WaterMeter?.SerialNumber,
                    ReadingValue = reading.ReadingValue,
                    ReadingDate = reading.ReadingDate,
                    ReadingMethod = reading.ReadingMethod,
                    ReadingType = reading.ReadingType ?? "Regular",
                    DataSource = reading.DataSource ?? "Mobile",
                    HasOCR = reading.HasOCR,
                    OcrConfidence = reading.OCRConfidence,
                    Latitude = reading.Latitude,
                    Longitude = reading.Longitude,
                    GpsAccuracy = reading.GpsAccuracy,
                    Location = reading.Location,
                    Status = reading.Status,
                    ValidationStatus = reading.ValidationStatus,
                    IsValidated = reading.IsValidated,
                    IsAnomalous = reading.IsAnomalous,
                    CantRead = reading.CantRead,
                    Notes = reading.Notes,
                    IsOfflineReading = false // MeterReading 模型中没有这个字段
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting mobile reading by task ID: {TaskId}", taskId);
                return null;
            }
        }

        /// <summary>
        /// Update mobile reading by task ID
        /// </summary>
        public async Task<MeterReading?> UpdateMobileReadingByTaskIdAsync(int taskId, MobileReadingDto readingDto, int userId)
        {
            try
            {
                var existingReading = await _context.MeterReadings
                    .FirstOrDefaultAsync(r => r.TaskId == taskId && !r.IsDeleted);

                if (existingReading == null)
                    return null;

                // Update reading properties
                existingReading.ReadingValue = readingDto.ReadingValue;
                existingReading.ReadingDate = readingDto.ReadingDate;
                existingReading.ReadingMethod = readingDto.ReadingMethod;
                existingReading.ReadingType = readingDto.ReadingType;
                existingReading.HasOCR = readingDto.HasOCR;
                existingReading.OCRConfidence = readingDto.OcrConfidence;
                existingReading.Latitude = readingDto.Latitude;
                existingReading.Longitude = readingDto.Longitude;
                existingReading.GpsAccuracy = readingDto.GpsAccuracy;
                existingReading.Location = readingDto.Location;
                existingReading.Status = readingDto.Status;
                existingReading.ValidationStatus = readingDto.ValidationStatus;
                existingReading.IsValidated = readingDto.IsValidated;
                existingReading.IsAnomalous = readingDto.IsAnomalous;
                existingReading.CantRead = readingDto.CantRead;
                existingReading.Notes = readingDto.Notes;
                existingReading.UpdatedAt = DateTime.UtcNow;
                existingReading.UpdatedBy = userId.ToString();

                await _context.SaveChangesAsync();

                // Process photos if any
                if (readingDto.Photos != null && readingDto.Photos.Count > 0)
                {
                    _logger.LogInformation("Processing {PhotoCount} photos for reading {ReadingId}", readingDto.Photos.Count, existingReading.Id);
                    await ProcessReadingPhotosAsync(existingReading.Id, readingDto.Photos, userId);
                }

                _logger.LogInformation("Updated mobile reading for task {TaskId} successfully", taskId);
                return existingReading;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating mobile reading for task {TaskId}", taskId);
                throw;
            }
        }

        /// <summary>
        /// Process photos for a reading
        /// </summary>
        private async Task ProcessReadingPhotosAsync(int readingId, List<MobilePhotoDto> photos, int userId)
        {
            try
            {
                foreach (var photoDto in photos)
                {
                    try
                    {
                        string cloudflareUrl = "";
                        string thumbnailUrl = "";
                        string syncStatus = "pending";

                        // 如果有Base64数据，直接上传到R2
                        if (!string.IsNullOrEmpty(photoDto.Base64Data))
                        {
                            try
                            {
                                var photoBytes = Convert.FromBase64String(photoDto.Base64Data);
                                using var photoStream = new MemoryStream(photoBytes);

                                cloudflareUrl = await _r2Service.UploadPhotoAsync(photoStream, photoDto.FileName, photoDto.MimeType);

                                // 创建缩略图
                                photoStream.Position = 0;
                                thumbnailUrl = await _r2Service.CreateThumbnailAsync(photoStream, photoDto.FileName);

                                syncStatus = "synced";
                                _logger.LogInformation("Uploaded photo {FileName} to R2: {Url}", photoDto.FileName, cloudflareUrl);
                            }
                            catch (Exception uploadEx)
                            {
                                _logger.LogError(uploadEx, "Failed to upload photo {FileName} to R2", photoDto.FileName);
                                syncStatus = "failed";
                            }
                        }

                        // Create ReadingPhoto entity
                        var readingPhoto = new ReadingPhoto
                        {
                            ReadingId = readingId,
                            Uuid = photoDto.Uuid,
                            FileName = photoDto.FileName,
                            OriginalFileName = photoDto.FileName,
                            FilePath = photoDto.FilePath,
                            FileSizeBytes = photoDto.FileSizeBytes,
                            MimeType = photoDto.MimeType,
                            CapturedAt = photoDto.CapturedAt,
                            Latitude = photoDto.Latitude,
                            Longitude = photoDto.Longitude,
                            PhotoType = photoDto.PhotoType,
                            SyncStatus = syncStatus,
                            CloudflareUrl = cloudflareUrl,
                            ThumbnailUrl = thumbnailUrl,
                            UploadTime = !string.IsNullOrEmpty(cloudflareUrl) ? DateTime.UtcNow : DateTime.MinValue,
                            IsProcessed = !string.IsNullOrEmpty(cloudflareUrl),
                            CreatedAt = DateTime.UtcNow,
                            UpdatedAt = DateTime.UtcNow,
                            CreatedBy = userId.ToString(),
                            UpdatedBy = userId.ToString(),
                            IsDeleted = false
                        };

                        _context.ReadingPhotos.Add(readingPhoto);

                        _logger.LogInformation("Added photo {FileName} for reading {ReadingId} with sync status: {SyncStatus}",
                            photoDto.FileName, readingId, syncStatus);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error processing photo {FileName} for reading {ReadingId}", photoDto.FileName, readingId);
                    }
                }

                await _context.SaveChangesAsync();
                _logger.LogInformation("Successfully processed {PhotoCount} photos for reading {ReadingId}", photos.Count, readingId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing photos for reading {ReadingId}", readingId);
                throw;
            }
        }

        #endregion
    }
}