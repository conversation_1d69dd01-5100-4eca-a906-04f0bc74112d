using WaterMeterManagement.Data;
using Microsoft.EntityFrameworkCore;
using static WaterMeterManagement.Services.AmsExcelImportService;

namespace WaterMeterManagement.Services
{
    /// <summary>
    /// AMS Validation Service
    /// Handles data validation and error detection for AMS imports
    /// </summary>
    public class AmsValidationService
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<AmsValidationService> _logger;

        public AmsValidationService(ApplicationDbContext context, ILogger<AmsValidationService> logger)
        {
            _context = context;
            _logger = logger;
        }

        /// <summary>
        /// Validation result
        /// </summary>
        public class ValidationResult
        {
            public bool IsValid { get; set; }
            public bool HasCriticalErrors { get; set; }
            public List<string> Errors { get; set; } = new();
            public List<string> Warnings { get; set; } = new();
            public int QualityScore { get; set; }
        }

        /// <summary>
        /// Validate meter data from AMS import
        /// </summary>
        public async Task<ValidationResult> ValidateMeterDataAsync(MeterData meterData)
        {
            var result = new ValidationResult();

            // Required field validation
            await ValidateRequiredFields(meterData, result);

            // Business logic validation
            await ValidateBusinessRules(meterData, result);

            // Data integrity validation
            await ValidateDataIntegrity(meterData, result);

            // Reading validation
            await ValidateReadingData(meterData, result);

            // Set overall validation status
            result.HasCriticalErrors = result.Errors.Any(e => IsCriticalError(e));
            result.IsValid = !result.HasCriticalErrors;
            result.QualityScore = CalculateQualityScore(meterData, result);

            return result;
        }

        /// <summary>
        /// Validate required fields
        /// </summary>
        private async Task ValidateRequiredFields(MeterData meterData, ValidationResult result)
        {
            if (string.IsNullOrWhiteSpace(meterData.AssetId))
            {
                result.Errors.Add("Asset ID is required");
            }

            if (string.IsNullOrWhiteSpace(meterData.MeterNumber))
            {
                result.Errors.Add("Meter Number is required");
            }

            if (string.IsNullOrWhiteSpace(meterData.AccountNumber))
            {
                result.Errors.Add("Account Number is required");
            }

            if (string.IsNullOrWhiteSpace(meterData.RoadName))
            {
                result.Errors.Add("Road Name is required");
            }

            if (string.IsNullOrWhiteSpace(meterData.Township))
            {
                result.Errors.Add("Township is required");
            }

            // Check for duplicates
            await ValidateDuplicates(meterData, result);
        }

        /// <summary>
        /// Validate for duplicate records
        /// </summary>
        private async Task ValidateDuplicates(MeterData meterData, ValidationResult result)
        {
            // Check for duplicate Asset ID
            var existingAssetId = await _context.WaterMeters
                .AnyAsync(m => m.AssetId == meterData.AssetId);

            // Check for duplicate Meter Number
            var existingMeterNumber = await _context.WaterMeters
                .AnyAsync(m => m.SerialNumber == meterData.MeterNumber && m.AssetId != meterData.AssetId);

            // Check for duplicate Account Number
            var existingAccountNumber = await _context.WaterMeters
                .AnyAsync(m => m.AccountNumber == meterData.AccountNumber && m.AssetId != meterData.AssetId);

            if (existingMeterNumber)
            {
                result.Warnings.Add($"Meter Number {meterData.MeterNumber} already exists for different Asset ID");
            }

            if (existingAccountNumber)
            {
                result.Warnings.Add($"Account Number {meterData.AccountNumber} already exists for different Asset ID");
            }
        }

        /// <summary>
        /// Validate business rules
        /// </summary>
        private async Task ValidateBusinessRules(MeterData meterData, ValidationResult result)
        {
            // Validate Asset ID format (should be numeric)
            if (!string.IsNullOrEmpty(meterData.AssetId) && !long.TryParse(meterData.AssetId, out _))
            {
                result.Warnings.Add("Asset ID should be numeric");
            }

            // Validate Account Number format
            if (!string.IsNullOrEmpty(meterData.AccountNumber) && !long.TryParse(meterData.AccountNumber, out _))
            {
                result.Warnings.Add("Account Number should be numeric");
            }

            // Validate Unit value
            if (meterData.Unit <= 0)
            {
                result.Warnings.Add("Unit value should be positive");
            }

            // Validate Road Number
            if (meterData.RoadNumber.HasValue && meterData.RoadNumber <= 0)
            {
                result.Warnings.Add("Road Number should be positive");
            }

            // Validate Township values
            await ValidateTownshipData(meterData, result);
        }

        /// <summary>
        /// Validate township and area data
        /// </summary>
        private async Task ValidateTownshipData(MeterData meterData, ValidationResult result)
        {
            // Check if township exists in system
            var validTownships = await _context.WaterMeters
                .Where(m => !string.IsNullOrEmpty(m.Township))
                .Select(m => m.Township)
                .Distinct()
                .ToListAsync();

            if (validTownships.Any() && !validTownships.Contains(meterData.Township, StringComparer.OrdinalIgnoreCase))
            {
                result.Warnings.Add($"Township '{meterData.Township}' is new. Existing townships: {string.Join(", ", validTownships.Take(5))}");
            }

            // Validate road name format
            if (!string.IsNullOrEmpty(meterData.RoadName))
            {
                if (meterData.RoadName.Length > 200)
                {
                    result.Errors.Add("Road Name exceeds maximum length (200 characters)");
                }

                // Check for suspicious characters
                if (meterData.RoadName.Contains('\t') || meterData.RoadName.Contains('\n'))
                {
                    result.Warnings.Add("Road Name contains unusual characters");
                }
            }
        }

        /// <summary>
        /// Validate data integrity
        /// </summary>
        private async Task ValidateDataIntegrity(MeterData meterData, ValidationResult result)
        {
            // Validate string lengths
            if (meterData.AssetId?.Length > 20)
            {
                result.Errors.Add("Asset ID exceeds maximum length (20 characters)");
            }

            if (meterData.MeterNumber?.Length > 50)
            {
                result.Errors.Add("Meter Number exceeds maximum length (50 characters)");
            }

            if (meterData.AccountNumber?.Length > 20)
            {
                result.Errors.Add("Account Number exceeds maximum length (20 characters)");
            }

            if (meterData.BookNumber?.Length > 20)
            {
                result.Warnings.Add("Book Number exceeds recommended length (20 characters)");
            }

            if (meterData.Township?.Length > 100)
            {
                result.Errors.Add("Township exceeds maximum length (100 characters)");
            }

            if (meterData.SubArea?.Length > 100)
            {
                result.Warnings.Add("Sub Area exceeds recommended length (100 characters)");
            }

            if (meterData.Condition?.Length > 50)
            {
                result.Warnings.Add("Condition exceeds recommended length (50 characters)");
            }

            if (meterData.Comments?.Length > 1000)
            {
                result.Warnings.Add("Comments exceed recommended length (1000 characters)");
            }
        }

        /// <summary>
        /// Validate reading data
        /// </summary>
        private async Task ValidateReadingData(MeterData meterData, ValidationResult result)
        {
            // Validate reading values
            if (meterData.LastRead.HasValue)
            {
                if (meterData.LastRead < 0)
                {
                    result.Errors.Add("Last Read value cannot be negative");
                }

                if (meterData.LastRead > 999999999.99m)
                {
                    result.Warnings.Add("Last Read value seems extremely high");
                }
            }

            if (meterData.Read.HasValue)
            {
                if (meterData.Read < 0)
                {
                    result.Errors.Add("Read value cannot be negative");
                }

                if (meterData.Read > 999999999.99m)
                {
                    result.Warnings.Add("Read value seems extremely high");
                }
            }

            // Validate reading consistency
            if (meterData.LastRead.HasValue && meterData.Read.HasValue)
            {
                var difference = meterData.Read.Value - meterData.LastRead.Value;
                
                if (difference < 0)
                {
                    result.Warnings.Add("Current reading is less than last reading (possible meter reset)");
                }

                if (Math.Abs(difference) > 50000) // Large consumption change
                {
                    result.Warnings.Add($"Large consumption change detected: {difference:N2}");
                }
            }

            // Validate recent change consistency
            if (meterData.RecentChange.HasValue && meterData.LastRead.HasValue && meterData.Read.HasValue)
            {
                var calculatedChange = meterData.Read.Value - meterData.LastRead.Value;
                var reportedChange = meterData.RecentChange.Value;

                if (Math.Abs(calculatedChange - reportedChange) > 0.01m)
                {
                    result.Warnings.Add($"Recent Change value ({reportedChange}) doesn't match calculated difference ({calculatedChange})");
                }
            }

            // Validate date consistency
            if (meterData.DateOfRead.HasValue)
            {
                if (meterData.DateOfRead > DateTime.UtcNow.AddDays(1))
                {
                    result.Warnings.Add("Reading date is in the future");
                }

                if (meterData.DateOfRead < DateTime.UtcNow.AddYears(-5))
                {
                    result.Warnings.Add("Reading date is more than 5 years old");
                }
            }

            // Validate reading status
            if (meterData.CantRead && meterData.Read.HasValue)
            {
                result.Warnings.Add("Meter marked as 'Can't Read' but has reading value");
            }

            if (!meterData.CantRead && !meterData.Read.HasValue && meterData.LastRead.HasValue)
            {
                result.Warnings.Add("No current reading provided for readable meter");
            }
        }

        /// <summary>
        /// Calculate data quality score
        /// </summary>
        private int CalculateQualityScore(MeterData meterData, ValidationResult result)
        {
            int score = 100;

            // Deduct points for errors and warnings
            score -= result.Errors.Count * 10;
            score -= result.Warnings.Count * 5;

            // Deduct for missing optional data
            if (string.IsNullOrEmpty(meterData.BookNumber)) score -= 2;
            if (!meterData.RoadNumber.HasValue) score -= 3;
            if (string.IsNullOrEmpty(meterData.SubArea)) score -= 3;
            if (!meterData.LastRead.HasValue) score -= 5;
            if (!meterData.DateOfRead.HasValue) score -= 5;
            if (!meterData.Read.HasValue) score -= 5;
            if (string.IsNullOrEmpty(meterData.Comments)) score -= 2;

            // Bonus for good data
            if (meterData.LastRead.HasValue && meterData.Read.HasValue && meterData.DateOfRead.HasValue)
            {
                score += 5; // Complete reading data
            }

            if (!meterData.CantRead && string.IsNullOrEmpty(meterData.Condition))
            {
                score += 3; // Clean reading
            }

            return Math.Max(Math.Min(score, 100), 0);
        }

        /// <summary>
        /// Determine if error is critical
        /// </summary>
        private bool IsCriticalError(string error)
        {
            var criticalKeywords = new[] { "required", "exceeds maximum length", "cannot be negative" };
            return criticalKeywords.Any(keyword => error.Contains(keyword, StringComparison.OrdinalIgnoreCase));
        }

        /// <summary>
        /// Validate route assignment data
        /// </summary>
        public ValidationResult ValidateRouteData(string routeName, List<string> accountNumbers)
        {
            var result = new ValidationResult();

            if (string.IsNullOrWhiteSpace(routeName))
            {
                result.Errors.Add("Route name is required");
            }

            if (routeName?.Length > 50)
            {
                result.Errors.Add("Route name exceeds maximum length (50 characters)");
            }

            if (!accountNumbers.Any())
            {
                result.Warnings.Add("Route has no assigned account numbers");
            }

            if (accountNumbers.Count > 1000)
            {
                result.Warnings.Add($"Route has {accountNumbers.Count} meters, which is unusually high");
            }

            // Check for duplicate account numbers in route
            var duplicates = accountNumbers.GroupBy(x => x).Where(g => g.Count() > 1).ToList();
            if (duplicates.Any())
            {
                result.Warnings.Add($"Route contains duplicate account numbers: {string.Join(", ", duplicates.Select(d => d.Key))}");
            }

            result.IsValid = !result.Errors.Any();
            result.HasCriticalErrors = result.Errors.Any();
            result.QualityScore = Math.Max(100 - (result.Errors.Count * 10) - (result.Warnings.Count * 5), 0);

            return result;
        }

        /// <summary>
        /// Validate anomaly data
        /// </summary>
        public ValidationResult ValidateAnomalyData(MeterData meterData)
        {
            var result = new ValidationResult();

            if (!meterData.CantRead && string.IsNullOrEmpty(meterData.Condition))
            {
                result.Warnings.Add("Anomaly record should have either 'Can't Read' flag or Condition specified");
            }

            if (meterData.Read.HasValue && meterData.CantRead)
            {
                result.Warnings.Add("Anomaly record has reading value but is marked as 'Can't Read'");
            }

            result.IsValid = !result.Errors.Any();
            result.HasCriticalErrors = result.Errors.Any();

            return result;
        }
    }
} 