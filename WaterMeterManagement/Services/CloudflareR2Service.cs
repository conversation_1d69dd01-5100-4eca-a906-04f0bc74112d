using Amazon.S3;
using Amazon.S3.Model;
using WaterMeterManagement.Services.Interfaces;
using SixLabors.ImageSharp;
using SixLabors.ImageSharp.Processing;
using SixLabors.ImageSharp.Formats.Jpeg;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace WaterMeterManagement.Services
{
    public class CloudflareR2Service : ICloudflareR2Service
    {
        private readonly AmazonS3Client _s3Client;
        private readonly IConfiguration _configuration;
        private readonly ILogger<CloudflareR2Service> _logger;
        private readonly string _bucketName;
        private readonly string _customDomain;

        public CloudflareR2Service(IConfiguration configuration, ILogger<CloudflareR2Service> logger)
        {
            _configuration = configuration;
            _logger = logger;
            _bucketName = _configuration["CloudflareR2:BucketName"] ?? throw new ArgumentNullException("CloudflareR2:BucketName");
            _customDomain = _configuration["CloudflareR2:CustomDomain"] ?? throw new ArgumentNullException("CloudflareR2:CustomDomain");

            // 按照官方文档示例配置
            var credentials = new Amazon.Runtime.BasicAWSCredentials(
                _configuration["CloudflareR2:AccessKey"],
                _configuration["CloudflareR2:SecretKey"]
            );

            var config = new AmazonS3Config
            {
                ServiceURL = _configuration["CloudflareR2:Endpoint"]
            };

            _s3Client = new AmazonS3Client(credentials, config);

            _logger.LogInformation("R2 Service initialized with endpoint: {Endpoint}, bucket: {Bucket}",
                _configuration["CloudflareR2:Endpoint"], _bucketName);
        }

        public async Task<string> UploadPhotoAsync(Stream photoStream, string fileName, string contentType)
        {
            try
            {
                _logger.LogInformation("=== R2 UPLOAD START === File: {FileName}, ContentType: {ContentType}, Size: {Size}",
                    fileName, contentType, photoStream.Length);

                // 检查R2配置是否为占位符
                if (_bucketName.Contains("your-") || _configuration["CloudflareR2:AccessKey"]?.Contains("your-") == true)
                {
                    _logger.LogWarning("R2 credentials not configured, using mock upload");
                    var mockUrl = $"https://mock-r2-domain.com/photos/{DateTime.UtcNow:yyyy/MM/dd}/{Guid.NewGuid()}-{fileName}";
                    _logger.LogInformation("Mock photo upload successful: {Url}", mockUrl);
                    return mockUrl;
                }

                var key = GeneratePhotoKey(fileName);
                
                var request = new PutObjectRequest
                {
                    BucketName = _bucketName,
                    Key = key,
                    InputStream = photoStream,
                    ContentType = contentType,
                    Metadata = {
                        ["upload-time"] = DateTime.UtcNow.ToString("O"),
                        ["original-name"] = fileName
                    },
                    // 按照官方文档要求的R2兼容性设置
                    DisablePayloadSigning = true,
                    DisableDefaultChecksumValidation = true
                };

                var response = await _s3Client.PutObjectAsync(request);

                var publicUrl = GetPublicUrl(key);
                _logger.LogInformation("=== R2 UPLOAD SUCCESS === Key: {Key}, URL: {Url}, ETag: {ETag}",
                    key, publicUrl, response.ETag);

                return publicUrl;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to upload photo {FileName}", fileName);
                throw;
            }
        }

        public async Task<string> UploadPhotoAsync(IFormFile photoFile)
        {
            using var stream = photoFile.OpenReadStream();
            return await UploadPhotoAsync(stream, photoFile.FileName, photoFile.ContentType);
        }

        public async Task<bool> DeletePhotoAsync(string photoKey)
        {
            try
            {
                var key = ExtractKeyFromUrl(photoKey);
                
                var request = new DeleteObjectRequest
                {
                    BucketName = _bucketName,
                    Key = key
                };

                await _s3Client.DeleteObjectAsync(request);
                _logger.LogInformation("Photo deleted successfully: {Key}", key);
                
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to delete photo {PhotoKey}", photoKey);
                return false;
            }
        }

        public async Task<string> GeneratePresignedUrlAsync(string photoKey, TimeSpan expiration)
        {
            try
            {
                var key = ExtractKeyFromUrl(photoKey);
                
                var request = new GetPreSignedUrlRequest
                {
                    BucketName = _bucketName,
                    Key = key,
                    Expires = DateTime.UtcNow.Add(expiration),
                    Verb = HttpVerb.GET
                };

                return await _s3Client.GetPreSignedURLAsync(request);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to generate presigned URL for {PhotoKey}", photoKey);
                throw;
            }
        }

        public async Task<string> CreateThumbnailAsync(Stream originalStream, string fileName)
        {
            try
            {
                using var image = await Image.LoadAsync(originalStream);
                
                image.Mutate(x => x.Resize(new ResizeOptions
                {
                    Size = new Size(300, 300),
                    Mode = ResizeMode.Max
                }));

                using var thumbnailStream = new MemoryStream();
                await image.SaveAsync(thumbnailStream, new JpegEncoder { Quality = 80 });
                thumbnailStream.Position = 0;

                var thumbnailKey = GenerateThumbnailKey(fileName);
                
                var request = new PutObjectRequest
                {
                    BucketName = _bucketName,
                    Key = thumbnailKey,
                    InputStream = thumbnailStream,
                    ContentType = "image/jpeg",
                    ServerSideEncryptionMethod = ServerSideEncryptionMethod.AES256,
                    Metadata = {
                        ["upload-time"] = DateTime.UtcNow.ToString("O"),
                        ["original-name"] = fileName,
                        ["is-thumbnail"] = "true"
                    }
                };

                await _s3Client.PutObjectAsync(request);
                
                return GetPublicUrl(thumbnailKey);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to create thumbnail for {FileName}", fileName);
                throw;
            }
        }

        public async Task<bool> PhotoExistsAsync(string photoKey)
        {
            try
            {
                var key = ExtractKeyFromUrl(photoKey);
                
                var request = new GetObjectMetadataRequest
                {
                    BucketName = _bucketName,
                    Key = key
                };

                await _s3Client.GetObjectMetadataAsync(request);
                return true;
            }
            catch (AmazonS3Exception ex) when (ex.StatusCode == System.Net.HttpStatusCode.NotFound)
            {
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to check if photo exists {PhotoKey}", photoKey);
                throw;
            }
        }

        public async Task<long> GetPhotoSizeAsync(string photoKey)
        {
            try
            {
                var key = ExtractKeyFromUrl(photoKey);
                
                var request = new GetObjectMetadataRequest
                {
                    BucketName = _bucketName,
                    Key = key
                };

                var response = await _s3Client.GetObjectMetadataAsync(request);
                return response.ContentLength;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get photo size {PhotoKey}", photoKey);
                throw;
            }
        }

        public string GetPublicUrl(string photoKey)
        {
            var key = ExtractKeyFromUrl(photoKey);
            return $"{_customDomain.TrimEnd('/')}/{key}";
        }

        public string ExtractKeyFromUrl(string photoUrl)
        {
            if (string.IsNullOrEmpty(photoUrl))
                return photoUrl;

            if (photoUrl.StartsWith("https://") || photoUrl.StartsWith("http://"))
            {
                var uri = new Uri(photoUrl);
                return uri.AbsolutePath.TrimStart('/');
            }

            return photoUrl;
        }

        private string GeneratePhotoKey(string originalFileName)
        {
            var now = DateTime.UtcNow;
            var extension = Path.GetExtension(originalFileName);
            var uniqueId = Guid.NewGuid().ToString("N")[..8];
            
            return $"photos/{now:yyyy}/{now:MM}/{now:dd}/{uniqueId}_{now:HHmmss}{extension}";
        }

        private string GenerateThumbnailKey(string originalFileName)
        {
            var now = DateTime.UtcNow;
            var extension = Path.GetExtension(originalFileName);
            var uniqueId = Guid.NewGuid().ToString("N")[..8];
            
            return $"thumbnails/{now:yyyy}/{now:MM}/{now:dd}/{uniqueId}_{now:HHmmss}_thumb{extension}";
        }

        public void Dispose()
        {
            _s3Client?.Dispose();
        }
    }
}
