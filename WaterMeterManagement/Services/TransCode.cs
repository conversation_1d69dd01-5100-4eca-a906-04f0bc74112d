using System.Reflection;
using Microsoft.EntityFrameworkCore;
using WaterMeterManagement.Data;
using WaterMeterManagement.DTOs;

namespace WaterMeterManagement.Services
{
    public class TransCode
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<TransCode> _logger;

        public TransCode(ApplicationDbContext context, ILogger<TransCode> logger)
        {
            _context = context;
            _logger = logger;
        }

        public async Task<IEnumerable<T>> TransAsync<T>(IEnumerable<T> data, string sourceField,
            string translationType, string config, string targetField = null)
            where T : BaseDto
        {
            if (data == null || !data.Any())
                return data ?? new List<T>();

            var targetFieldName = targetField ?? sourceField + "Name";

            try
            {
                var sourceValues = GetUniqueSourceValues(data, sourceField);
                if (!sourceValues.Any())
                    return data;

                Dictionary<object, string> translations = translationType.ToLower() switch
                {
                    "enum" => await GetEnumTranslationsAsync(sourceValues, config),
                    "table" => await GetTableTranslationsAsync(sourceValues, config),
                    _ => new Dictionary<object, string>()
                };

                ApplyTranslations(data, sourceField, targetFieldName, translations);
                return data;
            }
            catch (Exception ex)
            {
                _logger.LogError($"TransCode error: {ex.Message}");
                return data;
            }
        }

        public IEnumerable<T> Trans<T>(IEnumerable<T> data, string sourceField,
            string translationType, string config, string targetField = null)
            where T : BaseDto
        {
            return TransAsync(data, sourceField, translationType, config, targetField).Result;
        }

        private HashSet<object> GetUniqueSourceValues<T>(IEnumerable<T> data, string sourceField)
            where T : BaseDto
        {
            var sourceValues = new HashSet<object>();

            // Try exact match first, then case-insensitive match
            var propertyInfo = typeof(T).GetProperty(sourceField) ??
                              typeof(T).GetProperty(sourceField, BindingFlags.IgnoreCase | BindingFlags.Public | BindingFlags.Instance);

            if (propertyInfo == null)
                return sourceValues;

            foreach (var item in data)
            {
                var value = propertyInfo.GetValue(item);
                if (value != null)
                {
                    sourceValues.Add(value);
                }
            }

            return sourceValues;
        }

        private async Task<Dictionary<object, string>> GetEnumTranslationsAsync(HashSet<object> sourceValues, string enumTypeName)
        {
            var translations = new Dictionary<object, string>();

            try
            {
                var enumType = AppDomain.CurrentDomain.GetAssemblies()
                    .SelectMany(a => a.GetTypes())
                    .FirstOrDefault(t => t.IsEnum && t.Name == enumTypeName);

                if (enumType == null)
                    return translations;

                foreach (var value in sourceValues)
                {
                    if (Enum.IsDefined(enumType, value))
                    {
                        var enumValue = Enum.ToObject(enumType, value);
                        translations[value] = enumValue.ToString();
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"Enum translation error: {ex.Message}");
            }

            return await Task.FromResult(translations);
        }

        private async Task<Dictionary<object, string>> GetTableTranslationsAsync(HashSet<object> sourceValues, string config)
        {
            var translations = new Dictionary<object, string>();

            try
            {
                var configParts = config.Split('|');
                if (configParts.Length != 3)
                    return translations;

                var tableName = configParts[0];
                var keyField = configParts[1];
                var valueField = configParts[2];

                var sql = $"SELECT [{keyField}], [{valueField}] FROM [{tableName}] WHERE [{keyField}] IN ({string.Join(",", sourceValues.Select(v => $"'{v}'"))})";

                using var command = _context.Database.GetDbConnection().CreateCommand();
                command.CommandText = sql;

                if (_context.Database.GetDbConnection().State != System.Data.ConnectionState.Open)
                    await _context.Database.GetDbConnection().OpenAsync();

                using var reader = await command.ExecuteReaderAsync();
                while (await reader.ReadAsync())
                {
                    var key = reader[keyField];
                    var value = reader[valueField]?.ToString();

                    if (key != null && !string.IsNullOrEmpty(value))
                    {
                        translations[key] = value;
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"Table translation error: {ex.Message}");
            }

            return translations;
        }

        private void ApplyTranslations<T>(IEnumerable<T> data, string sourceField,
            string targetField, Dictionary<object, string> translations)
            where T : BaseDto
        {
            // Try exact match first, then case-insensitive match
            var propertyInfo = typeof(T).GetProperty(sourceField) ??
                              typeof(T).GetProperty(sourceField, BindingFlags.IgnoreCase | BindingFlags.Public | BindingFlags.Instance);

            if (propertyInfo == null) return;

            foreach (var item in data)
            {
                var sourceValue = propertyInfo.GetValue(item);
                if (sourceValue != null && translations.TryGetValue(sourceValue, out var translatedValue))
                {
                    item.SetTranslation(targetField, translatedValue);
                }
            }
        }
    }

    public static class TransCodeExtensions
    {
        public static async Task<IEnumerable<T>> TransAsync<T>(this IEnumerable<T> data,
            TransCode transCode, string sourceField, string translationType, string config, string targetField = null)
            where T : BaseDto
        {
            return await transCode.TransAsync(data, sourceField, translationType, config, targetField);
        }
    }
}
