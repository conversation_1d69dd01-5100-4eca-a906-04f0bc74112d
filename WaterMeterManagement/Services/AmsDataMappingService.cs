using WaterMeterManagement.Models;
using static WaterMeterManagement.Services.AmsExcelImportService;

namespace WaterMeterManagement.Services
{
    /// <summary>
    /// AMS Data Mapping Service
    /// Handles transformation between AMS data and internal models
    /// </summary>
    public class AmsDataMappingService
    {
        private readonly ILogger<AmsDataMappingService> _logger;

        public AmsDataMappingService(ILogger<AmsDataMappingService> logger)
        {
            _logger = logger;
        }

        /// <summary>
        /// Create a new WaterMeter from AMS data
        /// </summary>
        public WaterMeter CreateMeterFromAmsData(MeterData amsData)
        {
            return new WaterMeter
            {
                // AMS Core Fields
                AssetId = amsData.AssetId,
                SerialNumber = amsData.MeterNumber,
                AccountNumber = amsData.AccountNumber,
                BookNumber = amsData.BookNumber,
                Unit = amsData.Unit,

                // Geographic Location
                RoadNumber = amsData.RoadNumber,
                RoadName = amsData.RoadName,
                Township = amsData.Township,
                SubArea = amsData.SubArea,
                Location = !string.IsNullOrEmpty(amsData.RoadName) 
                    ? $"{amsData.RoadNumber} {amsData.RoadName}, {amsData.Township}".Trim()
                    : "Unknown",

                // Reading Information
                LastRead = amsData.LastRead,
                RecentChange = amsData.RecentChange,
                Subd = amsData.Subd,
                DateOfRead = amsData.DateOfRead,
                Read = amsData.Read,
                CantRead = amsData.CantRead,
                Condition = amsData.Condition,
                Comments = amsData.Comments,

                // System Fields
                Status = "Active",
                Source = "AMS",
                SyncStatus = "Synced",
                
                // Set default values for required fields
                MeterType = "Water",
                Brand = "Unknown",
                Model = "Unknown",
                CustomerName = "Unknown"
            };
        }

        /// <summary>
        /// Update an existing WaterMeter with AMS data
        /// </summary>
        public void UpdateMeterFromAmsData(WaterMeter existingMeter, MeterData amsData)
        {
            // Update AMS fields
            existingMeter.SerialNumber = amsData.MeterNumber;
            existingMeter.AccountNumber = amsData.AccountNumber;
            existingMeter.BookNumber = amsData.BookNumber;
            existingMeter.Unit = amsData.Unit;

            // Update geographic location
            existingMeter.RoadNumber = amsData.RoadNumber;
            existingMeter.RoadName = amsData.RoadName;
            existingMeter.Township = amsData.Township;
            existingMeter.SubArea = amsData.SubArea;
            
            // Update location based on AMS data
            if (!string.IsNullOrEmpty(amsData.RoadName))
            {
                existingMeter.Location = $"{amsData.RoadNumber} {amsData.RoadName}, {amsData.Township}".Trim();
            }

            // Update reading information
            existingMeter.LastRead = amsData.LastRead;
            existingMeter.RecentChange = amsData.RecentChange;
            existingMeter.Subd = amsData.Subd;
            existingMeter.DateOfRead = amsData.DateOfRead;
            existingMeter.Read = amsData.Read;
            existingMeter.CantRead = amsData.CantRead;
            existingMeter.Condition = amsData.Condition;
            existingMeter.Comments = amsData.Comments;

            // Update system fields
            existingMeter.Source = "AMS";
            existingMeter.UpdatedAt = DateTime.UtcNow;
            existingMeter.UpdatedBy = "AMS Import";
        }

        /// <summary>
        /// Create baseline record from AMS meter data
        /// </summary>
        public BaselineRecord? CreateBaselineFromAmsData(WaterMeter meter, MeterData amsData)
        {
            if (!amsData.LastRead.HasValue) return null;

            return new BaselineRecord
            {
                MeterId = meter.Id,
                BaselineValue = amsData.LastRead.Value,
                BaselineDate = amsData.DateOfRead ?? DateTime.UtcNow,
                Source = "AMS_Import",
                ValidationStatus = "Pending",
                IsActive = true,
                ConfidenceLevel = DetermineConfidenceLevel(amsData),
                BaselineType = "Standard",
                Notes = $"Imported from AMS on {DateTime.UtcNow:yyyy-MM-dd}"
            };
        }

        /// <summary>
        /// Create reading record from AMS data
        /// </summary>
        public MeterReading? CreateReadingFromAmsData(WaterMeter meter, MeterData amsData)
        {
            if (!amsData.Read.HasValue) return null;

            return new MeterReading
            {
                MeterId = meter.Id,
                UserId = 1, // System user for AMS imports
                TaskId = 0, // TODO: 需要创建系统任务或传入TaskId
                ReadingValue = amsData.Read.Value,
                ReadingDate = amsData.DateOfRead ?? DateTime.UtcNow,
                ReadingMethod = "Import",
                ReadingType = "Regular",
                DataSource = "Import",
                Status = "Completed",
                ValidationStatus = DetermineIfAnomalous(amsData) ? "RequiresReview" : "Valid",
                IsAnomalous = DetermineIfAnomalous(amsData),
                AnomalyType = amsData.CantRead ? "CantRead" : (DetermineIfAnomalous(amsData) ? "DataAnomaly" : null),
                AnomalyReason = amsData.CantRead ? "Unable to read" : amsData.Condition,
                CantRead = amsData.CantRead,
                CantReadReason = amsData.Condition,
                Notes = $"Imported from AMS: {amsData.Comments}. Condition: {amsData.Condition}",
                CreatedBy = "AMS_Import",
                UpdatedBy = "AMS_Import"
            };
        }

        /// <summary>
        /// Map AMS meter data to existing reading for validation
        /// </summary>
        public void UpdateReadingFromAmsData(MeterReading existingReading, MeterData amsData)
        {
            if (!amsData.Read.HasValue) return;

            existingReading.ReadingValue = amsData.Read.Value;
            existingReading.ReadingDate = amsData.DateOfRead ?? existingReading.ReadingDate;
            existingReading.ValidationStatus = DetermineIfAnomalous(amsData) ? "RequiresReview" : "Valid";
            existingReading.IsAnomalous = DetermineIfAnomalous(amsData);
            existingReading.AnomalyType = amsData.CantRead ? "CantRead" : (DetermineIfAnomalous(amsData) ? "DataAnomaly" : null);
            existingReading.AnomalyReason = amsData.CantRead ? "Unable to read" : amsData.Condition;
            existingReading.CantRead = amsData.CantRead;
            existingReading.CantReadReason = amsData.Condition;
            existingReading.UpdatedBy = "AMS_Import";
            existingReading.UpdatedAt = DateTime.UtcNow;

            // Merge notes
            var amsNotes = $"AMS update: {amsData.Comments}";
            if (string.IsNullOrEmpty(existingReading.Notes))
            {
                existingReading.Notes = amsNotes;
            }
            else if (!existingReading.Notes.Contains(amsNotes))
            {
                existingReading.Notes = $"{existingReading.Notes}; {amsNotes}";
            }
        }

        /// <summary>
        /// Export meter data to AMS format
        /// </summary>
        public MeterData ExportMeterToAmsFormat(WaterMeter meter)
        {
            return new MeterData
            {
                AssetId = meter.AssetId,
                MeterNumber = meter.SerialNumber,
                AccountNumber = meter.AccountNumber,
                BookNumber = meter.BookNumber,
                Unit = meter.Unit,
                RoadNumber = meter.RoadNumber,
                RoadName = meter.RoadName,
                Township = meter.Township,
                SubArea = meter.SubArea,
                LastRead = meter.LastRead,
                RecentChange = meter.RecentChange,
                Subd = meter.Subd,
                DateOfRead = meter.DateOfRead,
                Read = meter.Read,
                CantRead = meter.CantRead,
                Condition = meter.Condition,
                Comments = meter.Comments
            };
        }

        /// <summary>
        /// Determine confidence level based on AMS data quality
        /// </summary>
        private int DetermineConfidenceLevel(MeterData amsData)
        {
            int confidence = 100;

            // Reduce confidence for missing data
            if (!amsData.LastRead.HasValue) confidence -= 20;
            if (!amsData.DateOfRead.HasValue) confidence -= 10;
            if (string.IsNullOrEmpty(amsData.RoadName)) confidence -= 10;
            if (amsData.CantRead) confidence -= 30;
            if (!string.IsNullOrEmpty(amsData.Condition)) confidence -= 15;

            return Math.Max(confidence, 10); // Minimum 10% confidence
        }

        /// <summary>
        /// Determine if reading is anomalous based on AMS data
        /// </summary>
        private bool DetermineIfAnomalous(MeterData amsData)
        {
            return amsData.CantRead || 
                   !string.IsNullOrEmpty(amsData.Condition) ||
                   (amsData.RecentChange.HasValue && Math.Abs(amsData.RecentChange.Value) > 10000); // Large consumption change
        }

        /// <summary>
        /// Validate AMS data completeness
        /// </summary>
        public bool IsAmsDataComplete(MeterData amsData)
        {
            return !string.IsNullOrEmpty(amsData.AssetId) &&
                   !string.IsNullOrEmpty(amsData.MeterNumber) &&
                   !string.IsNullOrEmpty(amsData.AccountNumber) &&
                   !string.IsNullOrEmpty(amsData.RoadName) &&
                   !string.IsNullOrEmpty(amsData.Township);
        }

        /// <summary>
        /// Get AMS data quality score (0-100)
        /// </summary>
        public int GetAmsDataQualityScore(MeterData amsData)
        {
            int score = 0;

            // Core fields (40 points)
            if (!string.IsNullOrEmpty(amsData.AssetId)) score += 10;
            if (!string.IsNullOrEmpty(amsData.MeterNumber)) score += 10;
            if (!string.IsNullOrEmpty(amsData.AccountNumber)) score += 10;
            if (!string.IsNullOrEmpty(amsData.RoadName)) score += 10;

            // Location fields (20 points)
            if (!string.IsNullOrEmpty(amsData.Township)) score += 10;
            if (amsData.RoadNumber.HasValue) score += 5;
            if (!string.IsNullOrEmpty(amsData.SubArea)) score += 5;

            // Reading fields (30 points)
            if (amsData.LastRead.HasValue) score += 15;
            if (amsData.DateOfRead.HasValue) score += 10;
            if (!amsData.CantRead) score += 5;

            // Additional data (10 points)
            if (!string.IsNullOrEmpty(amsData.BookNumber)) score += 3;
            if (amsData.RecentChange.HasValue) score += 3;
            if (!string.IsNullOrEmpty(amsData.Comments)) score += 2;
            if (!string.IsNullOrEmpty(amsData.Subd)) score += 2;

            return Math.Min(score, 100);
        }
    }
} 