using Microsoft.EntityFrameworkCore;
using System.Text.Json;
using WaterMeterManagement.Data;
using WaterMeterManagement.DTOs;
using WaterMeterManagement.Services.Interfaces;

namespace WaterMeterManagement.Services
{
    public class GpsService : IGpsService
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<GpsService> _logger;
        private readonly IConfiguration _configuration;
        private readonly HttpClient _httpClient;

        private readonly string _googleApiKey;
        private readonly string _geocodingApiUrl = "https://maps.googleapis.com/maps/api/geocode/json";
        private readonly int _requestDelayMs = 50; // Reduced delay
        private readonly int _maxConcurrentRequests = 10; // Allow parallel processing

        public GpsService(
            ApplicationDbContext context,
            ILogger<GpsService> logger,
            IConfiguration configuration,
            HttpClient httpClient)
        {
            _context = context;
            _logger = logger;
            _configuration = configuration;
            _httpClient = httpClient;

            _googleApiKey = _configuration["GoogleMaps:ApiKey"] ??
                           throw new InvalidOperationException("Google Maps API Key not configured");
        }

        public async Task<GpsCoordinateResultDto> GetCoordinatesFromAddressAsync(string address)
        {
            if (string.IsNullOrWhiteSpace(address))
            {
                return new GpsCoordinateResultDto
                {
                    Success = false,
                    ErrorMessage = "Address cannot be empty"
                };
            }

            try
            {
                _logger.LogInformation("Getting GPS coordinates for address: {Address}", address);

                var encodedAddress = Uri.EscapeDataString(address);
                var requestUrl = $"{_geocodingApiUrl}?address={encodedAddress}&key={_googleApiKey}";

                var response = await _httpClient.GetAsync(requestUrl);
                response.EnsureSuccessStatusCode();

                var jsonContent = await response.Content.ReadAsStringAsync();
                var geocodingResponse = JsonSerializer.Deserialize<GoogleGeocodingResponseDto>(jsonContent, new JsonSerializerOptions
                {
                    PropertyNamingPolicy = JsonNamingPolicy.SnakeCaseLower
                });

                if (geocodingResponse?.Status == "OK" && geocodingResponse.Results.Count > 0)
                {
                    var result = geocodingResponse.Results[0];
                    var location = result.Geometry.Location;

                    _logger.LogInformation("Successfully geocoded address: {Address} -> ({Lat}, {Lng})",
                        address, location.Lat, location.Lng);

                    return new GpsCoordinateResultDto
                    {
                        Success = true,
                        Latitude = location.Lat,
                        Longitude = location.Lng,
                        FormattedAddress = result.FormattedAddress,
                        PlaceId = result.PlaceId,
                        LocationType = result.Geometry.LocationType,
                        AddressComponents = result.AddressComponents.Select(ac => ac.LongName).ToList()
                    };
                }
                else
                {
                    var errorMessage = geocodingResponse?.Status == "ZERO_RESULTS"
                        ? "No results found for the given address"
                        : $"Geocoding failed with status: {geocodingResponse?.Status}";

                    _logger.LogWarning("Geocoding failed for address: {Address}, Status: {Status}",
                        address, geocodingResponse?.Status);

                    return new GpsCoordinateResultDto
                    {
                        Success = false,
                        ErrorMessage = errorMessage
                    };
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error geocoding address: {Address}", address);
                return new GpsCoordinateResultDto
                {
                    Success = false,
                    ErrorMessage = $"Geocoding error: {ex.Message}"
                };
            }
        }

        public async Task<BatchGpsResultDto> GetCoordinatesFromAddressesBatchAsync(List<string> addresses)
        {
            var startTime = DateTime.UtcNow;
            var results = new List<GpsCoordinateResultDto>();
            var errors = new List<string>();

            _logger.LogInformation("Starting batch geocoding for {Count} addresses", addresses.Count);

            foreach (var address in addresses)
            {
                try
                {
                    var result = await GetCoordinatesFromAddressAsync(address);
                    results.Add(result);

                    if (!result.Success)
                    {
                        errors.Add($"Address '{address}': {result.ErrorMessage}");
                    }

                    await Task.Delay(_requestDelayMs);
                }
                catch (Exception ex)
                {
                    var errorResult = new GpsCoordinateResultDto
                    {
                        Success = false,
                        ErrorMessage = ex.Message
                    };
                    results.Add(errorResult);
                    errors.Add($"Address '{address}': {ex.Message}");

                    _logger.LogError(ex, "Error processing address in batch: {Address}", address);
                }
            }

            var endTime = DateTime.UtcNow;
            var successCount = results.Count(r => r.Success);

            _logger.LogInformation("Batch geocoding completed: {Success}/{Total} successful in {Duration}ms",
                successCount, addresses.Count, (endTime - startTime).TotalMilliseconds);

            return new BatchGpsResultDto
            {
                TotalRequests = addresses.Count,
                SuccessCount = successCount,
                FailureCount = addresses.Count - successCount,
                Results = results,
                Errors = errors,
                ProcessingTime = endTime - startTime
            };
        }

        public async Task<GpsUpdateResultDto> UpdateWaterMeterGpsAsync(int waterMeterId)
        {
            try
            {
                _logger.LogInformation("Processing GPS update for water meter ID: {WaterMeterId}", waterMeterId);

                var waterMeter = await _context.WaterMeters.FindAsync(waterMeterId);
                if (waterMeter == null)
                {
                    _logger.LogWarning("Water meter not found: {WaterMeterId}", waterMeterId);
                    return new GpsUpdateResultDto
                    {
                        Success = false,
                        WaterMeterId = waterMeterId,
                        ErrorMessage = "Water meter not found"
                    };
                }

                var addressToGeocode = !string.IsNullOrWhiteSpace(waterMeter.Address) ? waterMeter.Address :
                                       (!string.IsNullOrWhiteSpace(waterMeter.RoadName) || !string.IsNullOrWhiteSpace(waterMeter.Township)) ?
                                       $"{waterMeter.RoadNumber} {waterMeter.RoadName}, {waterMeter.SubArea}, {waterMeter.Township}".Trim() :
                                       waterMeter.Location;

                _logger.LogInformation("Processing meter {Serial}, using address: '{Address}'",
                    waterMeter.SerialNumber, addressToGeocode);

                if (string.IsNullOrWhiteSpace(addressToGeocode))
                {
                    _logger.LogWarning("Water meter {Serial} has no valid address", waterMeter.SerialNumber);
                    return new GpsUpdateResultDto
                    {
                        Success = false,
                        WaterMeterId = waterMeterId,
                        WaterMeterSerial = waterMeter.SerialNumber,
                        ErrorMessage = "Water meter has no valid address"
                    };
                }

                var oldLatitude = waterMeter.Latitude;
                var oldLongitude = waterMeter.Longitude;

                _logger.LogInformation("Calling Google Geocoding API for address: '{Address}'", addressToGeocode);
                var gpsResult = await GetCoordinatesFromAddressAsync(addressToGeocode);

                _logger.LogInformation("Google API Response - Success: {Success}, Lat: {Lat}, Lng: {Lng}, FormattedAddress: '{FormattedAddress}', Error: '{Error}'",
                    gpsResult.Success, gpsResult.Latitude, gpsResult.Longitude, gpsResult.FormattedAddress, gpsResult.ErrorMessage);

                if (!gpsResult.Success)
                {
                    return new GpsUpdateResultDto
                    {
                        Success = false,
                        WaterMeterId = waterMeterId,
                        WaterMeterSerial = waterMeter.SerialNumber,
                        Address = waterMeter.Address,
                        OldLatitude = oldLatitude,
                        OldLongitude = oldLongitude,
                        ErrorMessage = gpsResult.ErrorMessage
                    };
                }

                waterMeter.Latitude = gpsResult.Latitude;
                waterMeter.Longitude = gpsResult.Longitude;
                waterMeter.UpdatedAt = DateTime.UtcNow;
                waterMeter.UpdatedBy = "GPS_SERVICE";

                await _context.SaveChangesAsync();

                _logger.LogInformation("Updated GPS coordinates for water meter {Serial}: ({OldLat}, {OldLng}) -> ({NewLat}, {NewLng})",
                    waterMeter.SerialNumber, oldLatitude, oldLongitude, gpsResult.Latitude, gpsResult.Longitude);

                return new GpsUpdateResultDto
                {
                    Success = true,
                    WaterMeterId = waterMeterId,
                    WaterMeterSerial = waterMeter.SerialNumber,
                    Address = waterMeter.Address,
                    OldLatitude = oldLatitude,
                    OldLongitude = oldLongitude,
                    NewLatitude = gpsResult.Latitude,
                    NewLongitude = gpsResult.Longitude,
                    UpdatedAt = DateTime.UtcNow
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating GPS coordinates for water meter {WaterMeterId}", waterMeterId);
                return new GpsUpdateResultDto
                {
                    Success = false,
                    WaterMeterId = waterMeterId,
                    ErrorMessage = $"Update error: {ex.Message}"
                };
            }
        }

        public async Task<BatchGpsUpdateResultDto> UpdateWaterMetersGpsBatchAsync(List<int> waterMeterIds)
        {
            var startTime = DateTime.UtcNow;
            var results = new List<GpsUpdateResultDto>();
            var errors = new List<string>();

            _logger.LogInformation("Starting batch GPS update for {Count} water meters", waterMeterIds.Count);

            foreach (var waterMeterId in waterMeterIds)
            {
                try
                {
                    var result = await UpdateWaterMeterGpsAsync(waterMeterId);
                    results.Add(result);

                    if (!result.Success)
                    {
                        errors.Add($"Water meter {waterMeterId}: {result.ErrorMessage}");
                    }

                    await Task.Delay(_requestDelayMs);
                }
                catch (Exception ex)
                {
                    var errorResult = new GpsUpdateResultDto
                    {
                        Success = false,
                        WaterMeterId = waterMeterId,
                        ErrorMessage = ex.Message
                    };
                    results.Add(errorResult);
                    errors.Add($"Water meter {waterMeterId}: {ex.Message}");

                    _logger.LogError(ex, "Error processing water meter in batch: {WaterMeterId}", waterMeterId);
                }
            }

            var endTime = DateTime.UtcNow;
            var successCount = results.Count(r => r.Success);

            _logger.LogInformation("Batch GPS update completed: {Success}/{Total} successful in {Duration}ms",
                successCount, waterMeterIds.Count, (endTime - startTime).TotalMilliseconds);

            return new BatchGpsUpdateResultDto
            {
                TotalMeters = waterMeterIds.Count,
                SuccessCount = successCount,
                FailureCount = waterMeterIds.Count - successCount,
                SkippedCount = 0,
                Results = results,
                Errors = errors,
                ProcessingTime = endTime - startTime,
                StartedAt = startTime,
                CompletedAt = endTime
            };
        }

        public async Task<BatchGpsUpdateResultDto> UpdateAllMissingGpsAsync()
        {
            _logger.LogInformation("Starting UpdateAllMissingGpsAsync - querying water meters with addresses");

            var totalMeters = await _context.WaterMeters.CountAsync();
            _logger.LogInformation("Total water meters in database: {TotalCount}", totalMeters);

            var waterMetersWithAddress = await _context.WaterMeters
                .Where(wm => (!string.IsNullOrWhiteSpace(wm.RoadName) || !string.IsNullOrWhiteSpace(wm.Township)) ||
                            !string.IsNullOrWhiteSpace(wm.Address) ||
                            !string.IsNullOrWhiteSpace(wm.Location))
                .Select(wm => new {
                    wm.Id,
                    wm.SerialNumber,
                    wm.Address,
                    wm.Location,
                    wm.RoadNumber,
                    wm.RoadName,
                    wm.Township,
                    wm.SubArea,
                    FullAddress = (!string.IsNullOrEmpty(wm.RoadName) || !string.IsNullOrEmpty(wm.Township))
                        ? (wm.RoadNumber != null ? wm.RoadNumber.ToString() + " " : "") + wm.RoadName + ", " + wm.SubArea + ", " + wm.Township
                        : (!string.IsNullOrWhiteSpace(wm.Address) ? wm.Address : wm.Location)
                })
                .ToListAsync();

            _logger.LogInformation("Found {Count} water meters with addresses", waterMetersWithAddress.Count);

            _logger.LogInformation("Will process {Count} meters for GPS updates", waterMetersWithAddress.Count);

            var meterIds = waterMetersWithAddress.Select(wm => wm.Id).ToList();
            return await UpdateWaterMetersGpsBatchAsync(meterIds);
        }

        public bool ValidateGpsCoordinates(decimal latitude, decimal longitude)
        {
            return latitude >= -90 && latitude <= 90 && longitude >= -180 && longitude <= 180;
        }

        public async Task<GpsServiceStatsDto> GetGpsServiceStatsAsync()
        {
            var totalMeters = await _context.WaterMeters.CountAsync();
            var metersWithGps = await _context.WaterMeters
                .CountAsync(wm => wm.Latitude != null && wm.Longitude != null);
            var metersWithoutGps = totalMeters - metersWithGps;

            var completionPercentage = totalMeters > 0 ? (decimal)metersWithGps / totalMeters * 100 : 0;

            var lastGpsUpdate = await _context.WaterMeters
                .Where(wm => wm.Latitude != null && wm.Longitude != null)
                .MaxAsync(wm => (DateTime?)wm.UpdatedAt) ?? DateTime.MinValue;

            return new GpsServiceStatsDto
            {
                TotalWaterMeters = totalMeters,
                MetersWithGps = metersWithGps,
                MetersWithoutGps = metersWithoutGps,
                GpsCompletionPercentage = completionPercentage,
                TodayGpsRequests = 0,
                TodaySuccessfulRequests = 0,
                TodayFailedRequests = 0,
                TodaySuccessRate = 0,
                LastGpsUpdate = lastGpsUpdate
            };
        }
    }
}
