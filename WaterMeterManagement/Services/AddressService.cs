using System.Text.Json;
using WaterMeterManagement.DTOs;
using WaterMeterManagement.Services.Interfaces;

namespace WaterMeterManagement.Services
{
    public class AddressService : IAddressService
    {
        private readonly HttpClient _httpClient;
        private readonly ILogger<AddressService> _logger;
        private readonly IConfiguration _configuration;
        private readonly string _googleApiKey;

        // Google Places API endpoints
        private readonly string _placesAutocompleteUrl = "https://maps.googleapis.com/maps/api/place/autocomplete/json";
        private readonly string _placesDetailsUrl = "https://maps.googleapis.com/maps/api/place/details/json";
        private readonly string _geocodingUrl = "https://maps.googleapis.com/maps/api/geocode/json";

        public AddressService(
            HttpClient httpClient,
            ILogger<AddressService> logger,
            IConfiguration configuration)
        {
            _httpClient = httpClient;
            _logger = logger;
            _configuration = configuration;
            _googleApiKey = _configuration["GoogleMaps:ApiKey"] ??
                           throw new InvalidOperationException("Google Maps API Key not configured");
        }

        public async Task<AddressParseResultDto> ParseAddressAsync(string address)
        {
            if (string.IsNullOrWhiteSpace(address))
            {
                return new AddressParseResultDto
                {
                    Success = false,
                    ErrorMessage = "Address cannot be empty"
                };
            }

            try
            {
                _logger.LogInformation("Parsing address: {Address}", address);

                // Use Geocoding API to get detailed address information
                var encodedAddress = Uri.EscapeDataString(address);
                var requestUrl = $"{_geocodingUrl}?address={encodedAddress}&components=country:NZ&key={_googleApiKey}";

                var response = await _httpClient.GetAsync(requestUrl);
                response.EnsureSuccessStatusCode();

                var jsonContent = await response.Content.ReadAsStringAsync();
                var geocodingResponse = JsonSerializer.Deserialize<GoogleGeocodingResponseDto>(jsonContent, new JsonSerializerOptions
                {
                    PropertyNamingPolicy = JsonNamingPolicy.SnakeCaseLower
                });

                if (geocodingResponse?.Status == "OK" && geocodingResponse.Results.Count > 0)
                {
                    var result = geocodingResponse.Results[0];
                    var components = ParseAddressComponents(result.AddressComponents);

                    return new AddressParseResultDto
                    {
                        Success = true,
                        FormattedAddress = result.FormattedAddress,
                        Components = components,
                        Latitude = result.Geometry.Location.Lat,
                        Longitude = result.Geometry.Location.Lng,
                        PlaceId = result.PlaceId
                    };
                }
                else
                {
                    _logger.LogWarning("Address parsing failed: {Status}", geocodingResponse?.Status);
                    return new AddressParseResultDto
                    {
                        Success = false,
                        ErrorMessage = "Could not parse the provided address",
                        FormattedAddress = address // Fallback to original input
                    };
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error parsing address: {Address}", address);
                return new AddressParseResultDto
                {
                    Success = false,
                    ErrorMessage = "Address parsing service unavailable",
                    FormattedAddress = address // Fallback to original input
                };
            }
        }

        public async Task<List<AddressSuggestionDto>> GetAddressSuggestionsAsync(string input, string countryCode = "NZ")
        {
            if (string.IsNullOrWhiteSpace(input) || input.Length < 3)
            {
                return new List<AddressSuggestionDto>();
            }

            try
            {
                _logger.LogInformation("Getting address suggestions for: {Input}", input);

                var encodedInput = Uri.EscapeDataString(input);
                var requestUrl = $"{_placesAutocompleteUrl}?input={encodedInput}&components=country:{countryCode}&types=address&key={_googleApiKey}";

                var response = await _httpClient.GetAsync(requestUrl);
                response.EnsureSuccessStatusCode();

                var jsonContent = await response.Content.ReadAsStringAsync();
                var autocompleteResponse = JsonSerializer.Deserialize<GooglePlacesAutocompleteResponseDto>(jsonContent, new JsonSerializerOptions
                {
                    PropertyNamingPolicy = JsonNamingPolicy.SnakeCaseLower
                });

                if (autocompleteResponse?.Status == "OK" && autocompleteResponse.Predictions != null)
                {
                    return autocompleteResponse.Predictions.Select(p => new AddressSuggestionDto
                    {
                        Description = p.Description,
                        PlaceId = p.PlaceId,
                        MainText = p.StructuredFormatting?.MainText ?? p.Description,
                        SecondaryText = p.StructuredFormatting?.SecondaryText ?? ""
                    }).ToList();
                }

                return new List<AddressSuggestionDto>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting address suggestions for: {Input}", input);
                return new List<AddressSuggestionDto>();
            }
        }

        private AddressComponentsDto ParseAddressComponents(List<GoogleAddressComponentDto> components)
        {
            var result = new AddressComponentsDto();

            foreach (var component in components)
            {
                var types = component.Types;

                if (types.Contains("street_number"))
                {
                    if (int.TryParse(component.LongName, out int streetNumber))
                    {
                        result.RoadNumber = streetNumber;
                    }
                }
                else if (types.Contains("route"))
                {
                    result.RoadName = component.LongName;
                }
                else if (types.Contains("locality") || types.Contains("sublocality"))
                {
                    result.Township = component.LongName;
                }
                else if (types.Contains("administrative_area_level_1"))
                {
                    result.SubArea = component.LongName;
                }
                else if (types.Contains("postal_code"))
                {
                    result.PostalCode = component.LongName;
                }
                else if (types.Contains("country"))
                {
                    result.Country = component.LongName;
                }
            }

            return result;
        }
    }

    // Google API Response DTOs (reusing existing ones from GpsService)
    public class GooglePlacesAutocompleteResponseDto
    {
        public string Status { get; set; } = string.Empty;
        public List<GooglePlacePredictionDto> Predictions { get; set; } = new();
    }

    public class GooglePlacePredictionDto
    {
        public string Description { get; set; } = string.Empty;
        public string PlaceId { get; set; } = string.Empty;
        public GooglePlaceStructuredFormattingDto? StructuredFormatting { get; set; }
    }

    public class GooglePlaceStructuredFormattingDto
    {
        public string MainText { get; set; } = string.Empty;
        public string SecondaryText { get; set; } = string.Empty;
    }
}
