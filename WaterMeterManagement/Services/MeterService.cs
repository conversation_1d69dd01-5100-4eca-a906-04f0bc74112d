using WaterMeterManagement.Services.Interfaces;
using WaterMeterManagement.Models;
using WaterMeterManagement.Data;
using Microsoft.EntityFrameworkCore;

namespace WaterMeterManagement.Services
{
    public class MeterService : IMeterService
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<MeterService> _logger;

        public MeterService(ApplicationDbContext context, ILogger<MeterService> logger)
        {
            _context = context;
            _logger = logger;
        }

        public async Task<WaterMeter?> GetMeterByIdAsync(int meterId)
        {
            try
            {
                return await _context.WaterMeters
                    .FirstOrDefaultAsync(m => m.Id == meterId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting meter by id {MeterId}", meterId);
                return null;
            }
        }

        public async Task<List<WaterMeter>> GetMetersByUserAreaAsync(int userId, double latitude, double longitude, double radiusKm)
        {
            try
            {
                // Simplified implementation
                return await _context.WaterMeters
                    .Where(m => m.Latitude.HasValue && m.Longitude.HasValue)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting meters by user area");
                return new List<WaterMeter>();
            }
        }

        public async Task<double?> GetLastReadingValueAsync(int meterId)
        {
            try
            {
                var meter = await _context.WaterMeters
                    .FirstOrDefaultAsync(m => m.Id == meterId);
                return meter?.LastReading.HasValue == true ? (double?)meter.LastReading.Value : null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting last reading value for meter {MeterId}", meterId);
                return null;
            }
        }

        public async Task<DateTime?> GetLastReadingDateAsync(int meterId)
        {
            try
            {
                var meter = await _context.WaterMeters
                    .FirstOrDefaultAsync(m => m.Id == meterId);
                return meter?.LastReadingDate;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting last reading date for meter {MeterId}", meterId);
                return null;
            }
        }

        public async Task<bool> ValidateMeterAccessAsync(int meterId, int userId)
        {
            try
            {
                var meter = await _context.WaterMeters
                    .FirstOrDefaultAsync(m => m.Id == meterId);
                return meter != null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating meter access for meter {MeterId}, user {UserId}", meterId, userId);
                return false;
            }
        }

        public async Task<List<WaterMeter>> GetMetersInRouteAsync(int routeId)
        {
            try
            {
                return await _context.WaterMeters.ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting meters in route {RouteId}", routeId);
                return new List<WaterMeter>();
            }
        }

        public async Task<bool> GetMeterStatusAsync(int meterId)
        {
            try
            {
                var meter = await _context.WaterMeters
                    .FirstOrDefaultAsync(m => m.Id == meterId);
                return meter != null && meter.Status == "Active";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting meter status for meter {MeterId}", meterId);
                return false;
            }
        }

        /// <summary>
        /// Get effective last reading value for a meter, considering baseline data if last reading is empty
        /// 获取水表的有效最后读数，如果last reading为空则考虑基线数据
        /// </summary>
        public async Task<double?> GetEffectiveLastReadingAsync(int meterId)
        {
            try
            {
                // First, get the water meter
                var meter = await _context.WaterMeters
                    .FirstOrDefaultAsync(m => m.Id == meterId);

                if (meter == null)
                {
                    _logger.LogWarning("Meter with ID {MeterId} not found", meterId);
                    return null;
                }

                // First, try to get the actual last reading from water meter
                if (meter.LastRead.HasValue && meter.LastRead.Value > 0)
                {
                    _logger.LogInformation("Using LastRead value {LastRead} for meter {MeterId}", meter.LastRead.Value, meterId);
                    return (double)meter.LastRead.Value;
                }

                // If LastRead is empty, try Read field (AMS data)
                if (meter.Read.HasValue && meter.Read.Value > 0)
                {
                    _logger.LogInformation("Using Read value {Read} for meter {MeterId}", meter.Read.Value, meterId);
                    return (double)meter.Read.Value;
                }

                // If no readings in WaterMeter, try to get from the most recent baseline data
                try
                {
                    var latestBaseline = await _context.BaselineRecords
                        .Where(b => b.MeterId == meterId && b.Status == "Active" && !b.IsDeleted)
                        .OrderByDescending(b => b.BaselineDate)
                        .FirstOrDefaultAsync();

                    if (latestBaseline != null)
                    {
                        _logger.LogInformation("Using baseline value {BaselineValue} for meter {MeterId}", 
                            latestBaseline.BaselineValue, meterId);
                        return (double)latestBaseline.BaselineValue;
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Error retrieving baseline data for meter {MeterId}", meterId);
                }

                // If no baseline data found, check if there's a most recent meter reading
                try
                {
                    var latestReading = await _context.MeterReadings
                        .Where(r => r.MeterId == meterId && !r.IsDeleted)
                        .OrderByDescending(r => r.ReadingDate)
                        .FirstOrDefaultAsync();

                    if (latestReading != null)
                    {
                        _logger.LogInformation("Using latest MeterReading value {ReadingValue} for meter {MeterId}", 
                            latestReading.ReadingValue, meterId);
                        return (double)latestReading.ReadingValue;
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Error retrieving latest MeterReading for meter {MeterId}", meterId);
                }

                // No data found
                _logger.LogInformation("No reading or baseline data found for meter {MeterId}, returning null", meterId);
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting effective last reading for meter {MeterId}", meterId);
                return null;
            }
        }
    }
} 