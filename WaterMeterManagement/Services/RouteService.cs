using Microsoft.EntityFrameworkCore;
using WaterMeterManagement.Data;
using WaterMeterManagement.DTOs;
using WaterMeterManagement.Models;
using WaterMeterManagement.Services.Interfaces;

namespace WaterMeterManagement.Services
{
    public class RouteService : IRouteService
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<RouteService> _logger;

        public RouteService(ApplicationDbContext context, ILogger<RouteService> logger)
        {
            _context = context;
            _logger = logger;
        }

        public async Task<(List<RouteListDto> routes, int totalCount)> GetRoutesAsync(RouteSearchDto searchDto)
        {
            var query = _context.Routes.AsQueryable();

            // Apply filters
            if (!string.IsNullOrEmpty(searchDto.Name))
            {
                query = query.Where(r => r.Name.Contains(searchDto.Name));
            }

            if (!string.IsNullOrEmpty(searchDto.Status))
            {
                query = query.Where(r => r.Status == searchDto.Status);
            }

            if (!string.IsNullOrEmpty(searchDto.Zone))
            {
                query = query.Where(r => r.Zone == searchDto.Zone);
            }

            if (!string.IsNullOrEmpty(searchDto.Area))
            {
                query = query.Where(r => r.Area == searchDto.Area);
            }

            if (searchDto.AssignedUserId.HasValue)
            {
                query = query.Where(r => r.AssignedUserId == searchDto.AssignedUserId.Value);
            }

            if (searchDto.IsTemplate.HasValue)
            {
                query = query.Where(r => r.IsTemplate == searchDto.IsTemplate.Value);
            }

            if (!string.IsNullOrEmpty(searchDto.TemplateCategory))
            {
                query = query.Where(r => r.TemplateCategory == searchDto.TemplateCategory);
            }

            // Get total count
            var totalCount = await query.CountAsync();

            // Apply pagination
            var routes = await query
                .OrderBy(r => r.CreatedAt)
                .Skip((searchDto.Page - 1) * searchDto.PageSize)
                .Take(searchDto.PageSize)
                .Select(r => new RouteListDto
                {
                    Id = r.Id,
                    Name = r.Name,
                    Description = r.Description,
                    Status = r.Status,
                    Zone = r.Zone,
                    Area = r.Area,
                    EstimatedDuration = r.EstimatedDuration,
                    EstimatedDistance = r.EstimatedDistance,
                    TotalMeters = _context.RouteWaypoints.Count(rw => rw.RouteId == r.Id),
                    AssignedUserId = r.AssignedUserId,
                    OptimizationMethod = r.OptimizationMethod,
                    LastOptimized = r.LastOptimized,
                    IsTemplate = r.IsTemplate,
                    CreatedAt = r.CreatedAt,
                    CreatedBy = r.CreatedBy
                })
                .ToListAsync();

            return (routes, totalCount);
        }

        public async Task<RouteDto?> GetRouteByIdAsync(int id)
        {
            var route = await _context.Routes
                .Include(r => r.RouteWaypoints)
                .FirstOrDefaultAsync(r => r.Id == id);

            if (route == null)
                return null;

            return new RouteDto
            {
                Id = route.Id,
                Name = route.Name,
                Description = route.Description,
                Status = route.Status,
                Zone = route.Zone,
                Area = route.Area,
                EstimatedDuration = route.EstimatedDuration,
                EstimatedDistance = route.EstimatedDistance,
                TotalMeters = route.RouteWaypoints.Count,
                StartLatitude = route.StartLatitude,
                StartLongitude = route.StartLongitude,
                EndLatitude = route.EndLatitude,
                EndLongitude = route.EndLongitude,
                StartAddress = route.StartAddress,
                EndAddress = route.EndAddress,
                AssignedUserId = route.AssignedUserId,
                BackupAssignee = route.BackupAssignee,
                OptimizationMethod = route.OptimizationMethod,
                LastOptimized = route.LastOptimized,
                OptimizedBy = route.OptimizedBy,
                RouteGeometry = route.RouteGeometry,
                Notes = route.Notes,
                IsTemplate = route.IsTemplate,
                TemplateCategory = route.TemplateCategory,
                AverageCompletionTime = route.AverageCompletionTime,
                DifficultyRating = route.DifficultyRating,
                CreatedAt = route.CreatedAt,
                CreatedBy = route.CreatedBy,
                UpdatedAt = route.UpdatedAt,
                UpdatedBy = route.UpdatedBy,
                Waypoints = route.RouteWaypoints.Select(rw => new RouteWaypointDto
                {
                    Id = rw.Id,
                    RouteId = rw.RouteId,
                    WaterMeterId = rw.WaterMeterId,
                    SequenceOrder = rw.SequenceOrder,
                    Latitude = rw.Latitude,
                    Longitude = rw.Longitude,
                    Address = rw.Address,
                    EstimatedDuration = rw.EstimatedDuration,
                    Notes = rw.Notes,
                    AccessDifficulty = string.IsNullOrEmpty(rw.AccessDifficulty) ? null : 
                        decimal.TryParse(rw.AccessDifficulty, out var difficulty) ? difficulty : null,
                    RequiresSpecialEquipment = rw.RequiresSpecialEquipment,
                    SpecialInstructions = rw.SpecialInstructions
                }).OrderBy(rw => rw.SequenceOrder).ToList()
            };
        }

        public async Task<RouteDto> CreateRouteAsync(CreateRouteDto createDto)
        {
            var route = new Models.Route
            {
                Name = createDto.Name,
                Description = createDto.Description,
                Zone = createDto.Zone,
                Area = createDto.Area,
                EstimatedDuration = createDto.EstimatedDuration,
                EstimatedDistance = createDto.EstimatedDistance,
                StartLatitude = createDto.StartLatitude,
                StartLongitude = createDto.StartLongitude,
                EndLatitude = createDto.EndLatitude,
                EndLongitude = createDto.EndLongitude,
                StartAddress = createDto.StartAddress,
                EndAddress = createDto.EndAddress,
                AssignedUserId = createDto.AssignedUserId,
                BackupAssignee = createDto.BackupAssignee,
                Notes = createDto.Notes,
                IsTemplate = createDto.IsTemplate,
                TemplateCategory = createDto.TemplateCategory,
                DifficultyRating = createDto.DifficultyRating,
                Status = "Active",
                CreatedAt = DateTime.UtcNow,
                CreatedBy = "System"
            };

            _context.Routes.Add(route);
            await _context.SaveChangesAsync();

            return await GetRouteByIdAsync(route.Id) ?? throw new InvalidOperationException("Failed to retrieve created route");
        }

        public async Task<RouteDto?> UpdateRouteAsync(int id, UpdateRouteDto updateDto)
        {
            var route = await _context.Routes.FindAsync(id);
            if (route == null)
                return null;

            route.Name = updateDto.Name;
            route.Description = updateDto.Description;
            route.Status = updateDto.Status;
            route.Zone = updateDto.Zone;
            route.Area = updateDto.Area;
            route.EstimatedDuration = updateDto.EstimatedDuration;
            route.EstimatedDistance = updateDto.EstimatedDistance;
            route.StartLatitude = updateDto.StartLatitude;
            route.StartLongitude = updateDto.StartLongitude;
            route.EndLatitude = updateDto.EndLatitude;
            route.EndLongitude = updateDto.EndLongitude;
            route.StartAddress = updateDto.StartAddress;
            route.EndAddress = updateDto.EndAddress;
            route.AssignedUserId = updateDto.AssignedUserId;
            route.BackupAssignee = updateDto.BackupAssignee;
            route.Notes = updateDto.Notes;
            route.IsTemplate = updateDto.IsTemplate;
            route.TemplateCategory = updateDto.TemplateCategory;
            route.DifficultyRating = updateDto.DifficultyRating;
            route.UpdatedAt = DateTime.UtcNow;
            route.UpdatedBy = "System";

            await _context.SaveChangesAsync();
            return await GetRouteByIdAsync(id);
        }

        public async Task<bool> DeleteRouteAsync(int id)
        {
            var route = await _context.Routes.FindAsync(id);
            if (route == null)
                return false;

            _context.Routes.Remove(route);
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<RouteDto?> DuplicateRouteAsync(int sourceRouteId, DuplicateRouteDto duplicateDto)
        {
            var sourceRoute = await _context.Routes
                .Include(r => r.RouteWaypoints)
                .FirstOrDefaultAsync(r => r.Id == sourceRouteId);

            if (sourceRoute == null)
                return null;

            // Create new route
            var newRoute = new Models.Route
            {
                Name = duplicateDto.Name,
                Description = duplicateDto.Description ?? $"Copied from: {sourceRoute.Name}",
                Status = "Active",
                Zone = sourceRoute.Zone,
                Area = sourceRoute.Area,
                EstimatedDuration = sourceRoute.EstimatedDuration,
                EstimatedDistance = sourceRoute.EstimatedDistance,
                StartLatitude = sourceRoute.StartLatitude,
                StartLongitude = sourceRoute.StartLongitude,
                EndLatitude = sourceRoute.EndLatitude,
                EndLongitude = sourceRoute.EndLongitude,
                StartAddress = sourceRoute.StartAddress,
                EndAddress = sourceRoute.EndAddress,
                AssignedUserId = duplicateDto.AssignedUserId,
                BackupAssignee = sourceRoute.BackupAssignee,
                Notes = sourceRoute.Notes,
                IsTemplate = false, // New routes are not templates by default
                TemplateCategory = null,
                DifficultyRating = sourceRoute.DifficultyRating,
                CreatedAt = DateTime.UtcNow,
                CreatedBy = "System"
            };

            _context.Routes.Add(newRoute);
            await _context.SaveChangesAsync();

            // Copy waypoints if requested
            if (duplicateDto.IncludeWaypoints && sourceRoute.RouteWaypoints.Any())
            {
                var newWaypoints = sourceRoute.RouteWaypoints.Select(wp => new RouteWaypoint
                {
                    RouteId = newRoute.Id,
                    WaterMeterId = wp.WaterMeterId,
                    SequenceOrder = wp.SequenceOrder,
                    Latitude = wp.Latitude,
                    Longitude = wp.Longitude,
                    Address = wp.Address,
                    EstimatedDuration = wp.EstimatedDuration,
                    Notes = wp.Notes,
                    AccessDifficulty = wp.AccessDifficulty,
                    RequiresSpecialEquipment = wp.RequiresSpecialEquipment,
                    SpecialInstructions = wp.SpecialInstructions,
                    CreatedAt = DateTime.UtcNow,
                    CreatedBy = "System"
                }).ToList();

                _context.RouteWaypoints.AddRange(newWaypoints);
                await _context.SaveChangesAsync();
            }

            return await GetRouteByIdAsync(newRoute.Id);
        }

        public async Task<RouteOptimizationResultDto> OptimizeRouteAsync(RouteOptimizationDto optimizationDto)
        {
            // Get existing waypoints for the route
            var waypoints = await GetRouteWaypointsAsync(optimizationDto.RouteId);
            
            if (waypoints.Count < 2)
            {
                return new RouteOptimizationResultDto
                {
                    RouteId = optimizationDto.RouteId,
                    OptimizationMethod = optimizationDto.OptimizationMethod,
                    OriginalDistance = 0,
                    OptimizedDistance = 0,
                    DistanceSaved = 0,
                    OriginalDuration = 0,
                    OptimizedDuration = 0,
                    TimeSaved = 0,
                    OptimizedWaypoints = waypoints,
                    OptimizedAt = DateTime.UtcNow,
                    OptimizedBy = "System"
                };
            }

            // Simple optimization: sort by coordinates (basic nearest neighbor approach)
            var optimizedWaypoints = waypoints.ToList();
            
            // If preserve start/end is true, keep first and last waypoints in place
            if (optimizationDto.PreserveStartEnd && waypoints.Count > 2)
            {
                var first = waypoints[0];
                var last = waypoints[waypoints.Count - 1];
                var middle = waypoints.Skip(1).Take(waypoints.Count - 2).ToList();
                
                // Simple optimization: sort middle waypoints by latitude then longitude
                var sortedMiddle = middle.OrderBy(w => w.Latitude).ThenBy(w => w.Longitude).ToList();
                
                optimizedWaypoints = new List<RouteWaypointDto> { first }
                    .Concat(sortedMiddle)
                    .Concat(new[] { last })
                    .ToList();
            }
            else
            {
                // Sort all waypoints by coordinates
                optimizedWaypoints = waypoints.OrderBy(w => w.Latitude).ThenBy(w => w.Longitude).ToList();
            }

            // Update sequence order
            for (int i = 0; i < optimizedWaypoints.Count; i++)
            {
                optimizedWaypoints[i].SequenceOrder = i + 1;
            }

            // Calculate basic distance estimates
            var originalDistance = CalculateBasicDistance(waypoints);
            var optimizedDistance = CalculateBasicDistance(optimizedWaypoints);
            var distanceSaved = Math.Max(0, originalDistance - optimizedDistance);
            
            // Calculate basic time estimates (assume 5km/h walking speed + 5 min per waypoint)
            var originalDuration = waypoints.Count * 5 + (int)(originalDistance * 12); // 12 min per km at 5km/h
            var optimizedDuration = optimizedWaypoints.Count * 5 + (int)(optimizedDistance * 12);
            var timeSaved = Math.Max(0, originalDuration - optimizedDuration);

            return new RouteOptimizationResultDto
            {
                RouteId = optimizationDto.RouteId,
                OptimizationMethod = optimizationDto.OptimizationMethod,
                OriginalDistance = originalDistance,
                OptimizedDistance = optimizedDistance,
                DistanceSaved = distanceSaved,
                OriginalDuration = originalDuration,
                OptimizedDuration = optimizedDuration,
                TimeSaved = timeSaved,
                OptimizedWaypoints = optimizedWaypoints,
                OptimizedAt = DateTime.UtcNow,
                OptimizedBy = "System"
            };
        }

        private decimal CalculateBasicDistance(List<RouteWaypointDto> waypoints)
        {
            if (waypoints.Count < 2) return 0;
            
            decimal totalDistance = 0;
            for (int i = 0; i < waypoints.Count - 1; i++)
            {
                var current = waypoints[i];
                var next = waypoints[i + 1];
                
                if (current.Latitude.HasValue && current.Longitude.HasValue && 
                    next.Latitude.HasValue && next.Longitude.HasValue)
                {
                    var distance = CalculateDistanceBetweenPointsAsync(
                        current.Latitude.Value, current.Longitude.Value,
                        next.Latitude.Value, next.Longitude.Value).Result;
                    
                    totalDistance += distance ?? 0;
                }
            }
            
            return totalDistance;
        }

        public async Task<bool> ApplyOptimizationAsync(int routeId, List<RouteWaypointDto> optimizedWaypoints)
        {
            return true;
        }

        public async Task<List<RouteWaypointDto>> GetRouteWaypointsAsync(int routeId)
        {
            var waypoints = await _context.RouteWaypoints
                .Where(rw => rw.RouteId == routeId)
                .Include(rw => rw.WaterMeter)
                .OrderBy(rw => rw.SequenceOrder)
                .ToListAsync();

            return waypoints.Select(rw => new RouteWaypointDto
            {
                Id = rw.Id,
                RouteId = rw.RouteId,
                WaterMeterId = rw.WaterMeterId,
                SequenceOrder = rw.SequenceOrder,
                Latitude = rw.Latitude,
                Longitude = rw.Longitude,
                Address = rw.Address,
                EstimatedDuration = rw.EstimatedDuration,
                Notes = rw.Notes,
                AccessDifficulty = string.IsNullOrEmpty(rw.AccessDifficulty) ? null : 
                    decimal.TryParse(rw.AccessDifficulty, out var difficulty) ? difficulty : null,
                RequiresSpecialEquipment = rw.RequiresSpecialEquipment,
                SpecialInstructions = rw.SpecialInstructions,
                WaterMeterSerial = rw.WaterMeter?.SerialNumber,
                CustomerName = rw.WaterMeter?.CustomerName
            }).ToList();
        }

        public async Task<RouteWaypointDto> AddWaypointAsync(int routeId, CreateRouteWaypointDto waypointDto)
        {
            // Get the water meter to extract location info
            var waterMeter = await _context.WaterMeters.FindAsync(waypointDto.WaterMeterId);
            if (waterMeter == null)
                throw new ArgumentException("Water meter not found");

            var waypoint = new RouteWaypoint
            {
                RouteId = routeId,
                WaterMeterId = waypointDto.WaterMeterId,
                SequenceOrder = waypointDto.SequenceOrder,
                Latitude = waterMeter.Latitude,
                Longitude = waterMeter.Longitude,
                Address = waterMeter.FullAddress ?? waterMeter.Address,
                EstimatedDuration = waypointDto.EstimatedDuration,
                Notes = waypointDto.Notes,
                AccessDifficulty = waypointDto.AccessDifficulty?.ToString(),
                RequiresSpecialEquipment = waypointDto.RequiresSpecialEquipment,
                SpecialInstructions = waypointDto.SpecialInstructions
            };

            _context.RouteWaypoints.Add(waypoint);
            await _context.SaveChangesAsync();

            return new RouteWaypointDto
            {
                Id = waypoint.Id,
                RouteId = waypoint.RouteId,
                WaterMeterId = waypoint.WaterMeterId,
                SequenceOrder = waypoint.SequenceOrder,
                Latitude = waypoint.Latitude,
                Longitude = waypoint.Longitude,
                Address = waypoint.Address,
                EstimatedDuration = waypoint.EstimatedDuration,
                Notes = waypoint.Notes,
                AccessDifficulty = string.IsNullOrEmpty(waypoint.AccessDifficulty) ? null : 
                    decimal.TryParse(waypoint.AccessDifficulty, out var difficulty) ? difficulty : null,
                RequiresSpecialEquipment = waypoint.RequiresSpecialEquipment,
                SpecialInstructions = waypoint.SpecialInstructions,
                WaterMeterSerial = waterMeter.SerialNumber,
                CustomerName = waterMeter.CustomerName
            };
        }

        public async Task<bool> UpdateWaypointAsync(int waypointId, CreateRouteWaypointDto waypointDto)
        {
            var waypoint = await _context.RouteWaypoints.FindAsync(waypointId);
            if (waypoint == null)
                return false;

            waypoint.SequenceOrder = waypointDto.SequenceOrder;
            waypoint.EstimatedDuration = waypointDto.EstimatedDuration;
            waypoint.Notes = waypointDto.Notes;
            waypoint.AccessDifficulty = waypointDto.AccessDifficulty?.ToString();
            waypoint.RequiresSpecialEquipment = waypointDto.RequiresSpecialEquipment;
            waypoint.SpecialInstructions = waypointDto.SpecialInstructions;
            waypoint.UpdatedAt = DateTime.UtcNow;
            waypoint.UpdatedBy = "System";

            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> RemoveWaypointAsync(int waypointId)
        {
            var waypoint = await _context.RouteWaypoints.FindAsync(waypointId);
            if (waypoint == null)
                return false;

            _context.RouteWaypoints.Remove(waypoint);
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> ReorderWaypointsAsync(int routeId, List<int> waypointIds)
        {
            var waypoints = await _context.RouteWaypoints
                .Where(rw => rw.RouteId == routeId && waypointIds.Contains(rw.Id))
                .ToListAsync();

            for (int i = 0; i < waypointIds.Count; i++)
            {
                var waypoint = waypoints.FirstOrDefault(w => w.Id == waypointIds[i]);
                if (waypoint != null)
                {
                    waypoint.SequenceOrder = i + 1;
                    waypoint.UpdatedAt = DateTime.UtcNow;
                    waypoint.UpdatedBy = "System";
                }
            }

            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<List<RouteListDto>> GetRouteTemplatesAsync(string? category = null)
        {
            var query = _context.Routes.Where(r => r.IsTemplate);

            if (!string.IsNullOrEmpty(category))
            {
                query = query.Where(r => r.TemplateCategory == category);
            }

            return await query
                .Select(r => new RouteListDto
                {
                    Id = r.Id,
                    Name = r.Name,
                    Description = r.Description,
                    Status = r.Status,
                    Zone = r.Zone,
                    Area = r.Area,
                    EstimatedDuration = r.EstimatedDuration,
                    EstimatedDistance = r.EstimatedDistance,
                    TotalMeters = _context.RouteWaypoints.Count(rw => rw.RouteId == r.Id),
                    AssignedUserId = r.AssignedUserId,
                    OptimizationMethod = r.OptimizationMethod,
                    LastOptimized = r.LastOptimized,
                    IsTemplate = r.IsTemplate,
                    CreatedAt = r.CreatedAt,
                    CreatedBy = r.CreatedBy
                })
                .ToListAsync();
        }

        public async Task<RouteDto> CreateRouteFromTemplateAsync(int templateId, CreateRouteDto routeDto)
        {
            var template = await _context.Routes
                .Include(r => r.RouteWaypoints)
                .FirstOrDefaultAsync(r => r.Id == templateId && r.IsTemplate);

            if (template == null)
                throw new ArgumentException("Template not found");

            var newRoute = new Models.Route
            {
                Name = routeDto.Name,
                Description = routeDto.Description,
                Zone = routeDto.Zone ?? template.Zone,
                Area = routeDto.Area ?? template.Area,
                EstimatedDuration = routeDto.EstimatedDuration ?? template.EstimatedDuration,
                EstimatedDistance = routeDto.EstimatedDistance ?? template.EstimatedDistance,
                StartLatitude = routeDto.StartLatitude ?? template.StartLatitude,
                StartLongitude = routeDto.StartLongitude ?? template.StartLongitude,
                EndLatitude = routeDto.EndLatitude ?? template.EndLatitude,
                EndLongitude = routeDto.EndLongitude ?? template.EndLongitude,
                StartAddress = routeDto.StartAddress ?? template.StartAddress,
                EndAddress = routeDto.EndAddress ?? template.EndAddress,
                AssignedUserId = routeDto.AssignedUserId,
                BackupAssignee = routeDto.BackupAssignee,
                Notes = routeDto.Notes ?? template.Notes,
                IsTemplate = false,
                DifficultyRating = routeDto.DifficultyRating ?? template.DifficultyRating,
                Status = "Active",
                CreatedAt = DateTime.UtcNow,
                CreatedBy = "System"
            };

            _context.Routes.Add(newRoute);
            await _context.SaveChangesAsync();

            return await GetRouteByIdAsync(newRoute.Id) ?? throw new InvalidOperationException("Failed to retrieve created route");
        }

        public async Task<bool> SaveAsTemplateAsync(int routeId, string templateName, string? category = null)
        {
            var route = await _context.Routes
                .Include(r => r.RouteWaypoints)
                .FirstOrDefaultAsync(r => r.Id == routeId);

            if (route == null)
                return false;

            var template = new Models.Route
            {
                Name = templateName,
                Description = $"Template based on route: {route.Name}",
                Zone = route.Zone,
                Area = route.Area,
                EstimatedDuration = route.EstimatedDuration,
                EstimatedDistance = route.EstimatedDistance,
                StartLatitude = route.StartLatitude,
                StartLongitude = route.StartLongitude,
                EndLatitude = route.EndLatitude,
                EndLongitude = route.EndLongitude,
                StartAddress = route.StartAddress,
                EndAddress = route.EndAddress,
                Notes = route.Notes,
                IsTemplate = true,
                TemplateCategory = category,
                DifficultyRating = route.DifficultyRating,
                Status = "Active",
                CreatedAt = DateTime.UtcNow,
                CreatedBy = "System"
            };

            _context.Routes.Add(template);
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<decimal?> CalculateRouteDistanceAsync(int routeId)
        {
            return null;
        }

        public async Task<int?> CalculateRouteTimeAsync(int routeId)
        {
            return null;
        }

        public async Task<decimal?> CalculateDistanceBetweenPointsAsync(decimal lat1, decimal lon1, decimal lat2, decimal lon2)
        {
            // Haversine formula implementation
            const double earthRadiusKm = 6371.0;

            var dLat = ToRadians((double)(lat2 - lat1));
            var dLon = ToRadians((double)(lon2 - lon1));

            var a = Math.Sin(dLat / 2) * Math.Sin(dLat / 2) +
                    Math.Cos(ToRadians((double)lat1)) * Math.Cos(ToRadians((double)lat2)) *
                    Math.Sin(dLon / 2) * Math.Sin(dLon / 2);
            var c = 2 * Math.Atan2(Math.Sqrt(a), Math.Sqrt(1 - a));

            return (decimal)(earthRadiusKm * c);
        }

        public async Task<List<string>> ValidateRouteAsync(int routeId)
        {
            var errors = new List<string>();
            return errors;
        }

        public async Task<bool> IsRouteOptimalAsync(int routeId)
        {
            return true;
        }

        public async Task<RouteStatisticsDto> GetRouteStatisticsAsync()
        {
            var totalRoutes = await _context.Routes.CountAsync();
            var activeRoutes = await _context.Routes.CountAsync(r => r.Status == "Active");
            var templateRoutes = await _context.Routes.CountAsync(r => r.IsTemplate);
            var totalWaypoints = await _context.RouteWaypoints.CountAsync();

            return new RouteStatisticsDto
            {
                TotalRoutes = totalRoutes,
                ActiveRoutes = activeRoutes,
                TemplateRoutes = templateRoutes,
                TotalWaypoints = totalWaypoints
            };
        }

        public async Task<Dictionary<string, object>> GetRouteDashboardDataAsync()
        {
            var stats = await GetRouteStatisticsAsync();
            return new Dictionary<string, object>
            {
                ["totalRoutes"] = stats.TotalRoutes,
                ["activeRoutes"] = stats.ActiveRoutes,
                ["templateRoutes"] = stats.TemplateRoutes,
                ["totalWaypoints"] = stats.TotalWaypoints
            };
        }

        public async Task<List<RouteListDto>> GetRoutesByAssigneeAsync(int assigneeUserId)
        {
            return await _context.Routes
                .Where(r => r.AssignedUserId == assigneeUserId)
                .Select(r => new RouteListDto
                {
                    Id = r.Id,
                    Name = r.Name,
                    Description = r.Description,
                    Status = r.Status,
                    Zone = r.Zone,
                    Area = r.Area,
                    EstimatedDuration = r.EstimatedDuration,
                    EstimatedDistance = r.EstimatedDistance,
                    TotalMeters = _context.RouteWaypoints.Count(rw => rw.RouteId == r.Id),
                    AssignedUserId = r.AssignedUserId,
                    OptimizationMethod = r.OptimizationMethod,
                    LastOptimized = r.LastOptimized,
                    IsTemplate = r.IsTemplate,
                    CreatedAt = r.CreatedAt,
                    CreatedBy = r.CreatedBy
                })
                .ToListAsync();
        }

        public async Task<string> GenerateRouteGeometryAsync(int routeId)
        {
            return string.Empty;
        }

        public async Task<byte[]> ExportRouteMapAsync(int routeId)
        {
            return Array.Empty<byte>();
        }

        public async Task<List<RouteWaypointDto>> GetNearbyWaypointsAsync(decimal latitude, decimal longitude, decimal radiusKm)
        {
            return new List<RouteWaypointDto>();
        }

        public async Task<List<RouteListDto>> GetAmsRoutesAsync(int page = 1, int pageSize = 50)
        {
            return await _context.Routes
                .Where(r => !string.IsNullOrEmpty(r.Source) && r.Source == "AMS")
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .Select(r => new RouteListDto
                {
                    Id = r.Id,
                    Name = r.RouteName ?? r.Name,
                    Description = r.Description,
                    Status = r.Status,
                    Zone = r.Township ?? r.Zone,
                    Area = r.Area,
                    EstimatedDuration = r.EstimatedDuration,
                    EstimatedDistance = r.EstimatedDistance,
                    TotalMeters = r.MeterCount,
                    AssignedUserId = r.AssignedUserId,
                    CreatedAt = r.CreatedAt,
                    CreatedBy = r.CreatedBy
                })
                .ToListAsync();
        }

        public async Task<List<RouteListDto>> GetRoutesByTownshipAsync(string township)
        {
            return await _context.Routes
                .Where(r => r.Township == township)
                .Select(r => new RouteListDto
                {
                    Id = r.Id,
                    Name = r.RouteName,
                    Description = r.Description,
                    Status = r.Status,
                    Zone = r.Township,
                    Area = r.SubArea,
                    EstimatedDuration = r.EstimatedDuration,
                    EstimatedDistance = r.EstimatedDistance,
                    TotalMeters = r.MeterCount,
                    AssignedUserId = r.AssignedUserId,
                    OptimizationMethod = r.OptimizationMethod,
                    LastOptimized = r.LastOptimized,
                    IsTemplate = r.IsTemplate,
                    CreatedAt = r.CreatedAt,
                    CreatedBy = r.CreatedBy
                })
                .ToListAsync();
        }

        public async Task<bool> SyncRoutesWithAmsAsync()
        {
            try
            {
                var routes = await _context.Routes.Where(r => r.Source == "AMS").ToListAsync();
                foreach (var route in routes)
                {
                    route.LastSyncDate = DateTime.UtcNow;
                }
                await _context.SaveChangesAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> AssignMetersToRouteAsync(int routeId, RouteAssignmentDto assignmentDto)
        {
            try
            {
                var route = await _context.Routes.FindAsync(routeId);
                if (route == null) return false;

                var meters = await _context.WaterMeters
                    .Where(m => assignmentDto.MeterAssetIds.Contains(m.AssetId))
                    .ToListAsync();

                foreach (var meter in meters)
                {
                    meter.RouteId = routeId;
                    meter.AssignedRoute = route.RouteName ?? route.Name;
                }

                await _context.SaveChangesAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> RemoveMetersFromRouteAsync(int routeId, List<string> meterAssetIds)
        {
            try
            {
                var meters = await _context.WaterMeters
                    .Where(m => m.RouteId == routeId && meterAssetIds.Contains(m.AssetId))
                    .ToListAsync();

                foreach (var meter in meters)
                {
                    meter.RouteId = null;
                    meter.AssignedRoute = null;
                }

                await _context.SaveChangesAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<List<object>> GetRouteMetersAsync(int routeId, int page = 1, int pageSize = 50)
        {
            return await _context.RouteWaypoints
                .Where(rw => rw.RouteId == routeId)
                .Include(rw => rw.WaterMeter)
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .Select(rw => new
                {
                    Id = rw.WaterMeter.Id,
                    AssetId = rw.WaterMeter.AssetId,
                    MeterNumber = rw.WaterMeter.SerialNumber,
                    AccountNumber = rw.WaterMeter.AccountNumber,
                    Address = rw.WaterMeter.FullAddress,
                    Township = rw.WaterMeter.Township,
                    RoadName = rw.WaterMeter.RoadName,
                    Status = rw.WaterMeter.Status,
                    LastRead = rw.WaterMeter.LastRead,
                    DateOfRead = rw.WaterMeter.DateOfRead,
                    SequenceOrder = rw.SequenceOrder
                })
                .Cast<object>()
                .ToListAsync();
        }

        public async Task<int> GetRouteMeterCountAsync(int routeId)
        {
            return await _context.RouteWaypoints.CountAsync(rw => rw.RouteId == routeId);
        }

        public async Task<bool> AutoAssignMetersAsync(int routeId, AutoAssignmentCriteriaDto criteria)
        {
            try
            {
                var route = await _context.Routes.FindAsync(routeId);
                if (route == null) return false;

                var query = _context.WaterMeters.Where(m => m.RouteId == null);

                if (!string.IsNullOrEmpty(criteria.Township))
                {
                    query = query.Where(m => m.Township == criteria.Township);
                }

                if (!string.IsNullOrEmpty(criteria.RoadName))
                {
                    query = query.Where(m => m.RoadName == criteria.RoadName);
                }

                if (criteria.MaxMeters.HasValue)
                {
                    query = query.Take(criteria.MaxMeters.Value);
                }

                var meters = await query.ToListAsync();

                foreach (var meter in meters)
                {
                    meter.RouteId = routeId;
                    meter.AssignedRoute = route.RouteName ?? route.Name;
                }

                await _context.SaveChangesAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<object> GetRouteReadingScheduleAsync(int routeId, DateTime startDate, DateTime endDate)
        {
            var route = await _context.Routes.FindAsync(routeId);
            if (route == null) return new { error = "Route not found" };

            var meterCount = await GetRouteMeterCountAsync(routeId);
            
            return new
            {
                RouteId = routeId,
                RouteName = route.RouteName,
                Township = route.Township,
                MeterCount = meterCount,
                ReadingFrequency = route.ReadingFrequency,
                EstimatedHours = route.EstimatedHours,
                Period = new { StartDate = startDate, EndDate = endDate },
                Status = route.Status
            };
        }

        public async Task<object> AnalyzeRouteCoverageAsync(RouteCoverageRequestDto request)
        {
            var totalMeters = await _context.WaterMeters
                .Where(m => m.Township == request.Township)
                .CountAsync();

            // 获取已经在RouteWaypoint表中的水表IDs（针对特定Township）
            var assignedMeterIdsInTownship = await _context.RouteWaypoints
                .Join(_context.WaterMeters,
                    rw => rw.WaterMeterId,
                    m => m.Id,
                    (rw, m) => new { rw, m })
                .Where(joined => joined.m.Township == request.Township)
                .Select(joined => joined.rw.WaterMeterId)
                .Distinct()
                .CountAsync();

            var unassignedMeters = totalMeters - assignedMeterIdsInTownship;

            return new
            {
                Township = request.Township,
                TotalMeters = totalMeters,
                AssignedMeters = assignedMeterIdsInTownship,
                UnassignedMeters = unassignedMeters,
                CoveragePercentage = totalMeters > 0 ? (double)assignedMeterIdsInTownship / totalMeters * 100 : 0,
                AnalysisDate = DateTime.UtcNow
            };
        }

        public async Task<List<object>> GetUnassignedMetersAsync(int page = 1, int pageSize = 50)
        {
            // 获取已经在RouteWaypoint表中的水表IDs
            var assignedMeterIds = await _context.RouteWaypoints
                .Select(rw => rw.WaterMeterId)
                .Distinct()
                .ToListAsync();

            // 返回不在RouteWaypoint表中的水表
            return await _context.WaterMeters
                .Where(m => !assignedMeterIds.Contains(m.Id))
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .Select(m => new
                {
                    Id = m.Id,
                    AssetId = m.AssetId,
                    SerialNumber = m.SerialNumber,
                    MeterNumber = m.SerialNumber, // For compatibility
                    AccountNumber = m.AccountNumber,
                    CustomerName = m.CustomerName,
                    Address = m.FullAddress ?? m.Address,
                    Location = m.Location,
                    FullAddress = m.FullAddress,
                    Township = m.Township,
                    RoadName = m.RoadName,
                    Status = m.Status
                })
                .Cast<object>()
                .ToListAsync();
        }

        public async Task<bool> UpdateWaypointCoordinatesAsync(int waypointId, UpdateWaypointCoordinatesDto coordinatesDto)
        {
            try
            {
                var waypoint = await _context.RouteWaypoints.FindAsync(waypointId);
                if (waypoint == null)
                    return false;

                waypoint.Latitude = coordinatesDto.Latitude;
                waypoint.Longitude = coordinatesDto.Longitude;
                if (!string.IsNullOrEmpty(coordinatesDto.Address))
                {
                    waypoint.Address = coordinatesDto.Address;
                }
                waypoint.UpdatedAt = DateTime.UtcNow;
                waypoint.UpdatedBy = "System";

                await _context.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating waypoint coordinates for waypoint {WaypointId}", waypointId);
                return false;
            }
        }

        public async Task<BatchUpdateResult> BatchUpdateWaypointCoordinatesAsync(int routeId, BatchUpdateCoordinatesDto batchDto)
        {
            var result = new BatchUpdateResult();
            
            try
            {
                var waypointIds = batchDto.Updates.Select(u => u.WaypointId).ToList();
                var waypoints = await _context.RouteWaypoints
                    .Where(rw => rw.RouteId == routeId && waypointIds.Contains(rw.Id))
                    .ToListAsync();

                foreach (var update in batchDto.Updates)
                {
                    try
                    {
                        var waypoint = waypoints.FirstOrDefault(w => w.Id == update.WaypointId);
                        if (waypoint != null)
                        {
                            waypoint.Latitude = update.Latitude;
                            waypoint.Longitude = update.Longitude;
                            if (!string.IsNullOrEmpty(update.Address))
                            {
                                waypoint.Address = update.Address;
                            }
                            waypoint.UpdatedAt = DateTime.UtcNow;
                            waypoint.UpdatedBy = "System";
                            
                            result.SuccessCount++;
                        }
                        else
                        {
                            result.FailedCount++;
                            result.Failures.Add($"Waypoint {update.WaypointId} not found in route {routeId}");
                        }
                    }
                    catch (Exception ex)
                    {
                        result.FailedCount++;
                        result.Failures.Add($"Failed to update waypoint {update.WaypointId}: {ex.Message}");
                        _logger.LogError(ex, "Error updating waypoint {WaypointId} in batch update", update.WaypointId);
                    }
                }

                if (result.SuccessCount > 0)
                {
                    await _context.SaveChangesAsync();
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in batch update waypoint coordinates for route {RouteId}", routeId);
                result.FailedCount = batchDto.Updates.Count;
                result.Failures.Add($"Batch update failed: {ex.Message}");
                return result;
            }
        }

        private static double ToRadians(double degrees)
        {
            return degrees * Math.PI / 180.0;
        }
    }
} 