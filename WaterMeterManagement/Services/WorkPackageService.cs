using Microsoft.EntityFrameworkCore;
using Microsoft.AspNetCore.Http;
using OfficeOpenXml;
using WaterMeterManagement.Data;
using WaterMeterManagement.DTOs;
using WaterMeterManagement.Models;
using WaterMeterManagement.Services.Interfaces;

namespace WaterMeterManagement.Services
{
    /// <summary>
    /// Work Package Service Implementation
    /// Provides business logic for work package management
    /// </summary>
    public class WorkPackageService : IWorkPackageService
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<WorkPackageService> _logger;

        public WorkPackageService(
            ApplicationDbContext context,
            ILogger<WorkPackageService> logger)
        {
            _context = context;
            _logger = logger;
        }

        #region Basic CRUD Operations

        public async Task<(List<WorkPackageListDto> Items, int TotalCount)> GetWorkPackagesAsync(
            int page = 1, 
            int pageSize = 10, 
            WorkPackageSearchDto? search = null)
        {
            try
            {
                var query = _context.WorkPackages.AsQueryable();

                // Apply filters
                if (search != null)
                {
                    if (!string.IsNullOrEmpty(search.Name))
                        query = query.Where(wp => wp.Name.Contains(search.Name));

                    if (!string.IsNullOrEmpty(search.PackageType))
                        query = query.Where(wp => wp.PackageType == search.PackageType);

                    if (!string.IsNullOrEmpty(search.Status))
                        query = query.Where(wp => wp.Status == search.Status);

                    if (!string.IsNullOrEmpty(search.Priority))
                        query = query.Where(wp => wp.Priority == search.Priority);

                    if (!string.IsNullOrEmpty(search.ServiceArea))
                        query = query.Where(wp => wp.ServiceArea.Contains(search.ServiceArea));

                    if (!string.IsNullOrEmpty(search.SubArea))
                        query = query.Where(wp => wp.SubArea != null && wp.SubArea.Contains(search.SubArea));

                    if (!string.IsNullOrEmpty(search.CreatedBy))
                        query = query.Where(wp => wp.CreatedBy.Contains(search.CreatedBy));

                    if (search.PlannedStartDateFrom.HasValue)
                        query = query.Where(wp => wp.PlannedStartDate >= search.PlannedStartDateFrom.Value);

                    if (search.PlannedStartDateTo.HasValue)
                        query = query.Where(wp => wp.PlannedStartDate <= search.PlannedStartDateTo.Value);

                    if (search.PlannedEndDateFrom.HasValue)
                        query = query.Where(wp => wp.PlannedEndDate >= search.PlannedEndDateFrom.Value);

                    if (search.PlannedEndDateTo.HasValue)
                        query = query.Where(wp => wp.PlannedEndDate <= search.PlannedEndDateTo.Value);

                    if (search.IsTemplate.HasValue)
                        query = query.Where(wp => wp.IsTemplate == search.IsTemplate.Value);

                    if (search.IsRecurring.HasValue)
                        query = query.Where(wp => wp.IsRecurring == search.IsRecurring.Value);

                    if (search.IsOverdue.HasValue && search.IsOverdue.Value)
                        query = query.Where(wp => wp.Status != "Completed" && wp.PlannedEndDate < DateTime.UtcNow);

                    if (!string.IsNullOrEmpty(search.Source))
                        query = query.Where(wp => wp.Source == search.Source);

                    if (search.MinProgress.HasValue)
                        query = query.Where(wp => wp.ProgressPercentage >= search.MinProgress.Value);

                    if (search.MaxProgress.HasValue)
                        query = query.Where(wp => wp.ProgressPercentage <= search.MaxProgress.Value);
                }

                var totalCount = await query.CountAsync();

                var items = await query
                    .OrderByDescending(wp => wp.CreatedAt)
                    .Skip((page - 1) * pageSize)
                    .Take(pageSize)
                    .Select(wp => new WorkPackageListDto
                    {
                        Id = wp.Id,
                        Name = wp.Name,
                        Description = wp.Description,
                        PackageType = wp.PackageType,
                        Status = wp.Status,
                        PlannedStartDate = wp.PlannedStartDate,
                        PlannedEndDate = wp.PlannedEndDate,
                        Frequency = wp.Frequency,
                        ServiceArea = wp.ServiceArea,
                        Priority = wp.Priority,
                        TotalMeters = wp.TotalMeters,
                        CompletedMeters = wp.CompletedMeters,
                        ProgressPercentage = wp.ProgressPercentage,
                        AssignedTeam = wp.AssignedTeam,
                        EstimatedHours = wp.EstimatedHours,
                        IsTemplate = wp.IsTemplate,
                        IsRecurring = wp.IsRecurring,
                        CreatedBy = wp.CreatedBy,
                        CreatedAt = wp.CreatedAt,
                        IsOverdue = wp.Status != "Completed" && wp.PlannedEndDate < DateTime.UtcNow,
                        RemainingDays = wp.Status == "Completed" ? 0 : (int)(wp.PlannedEndDate - DateTime.UtcNow).TotalDays,
                        CompletionRate = wp.TotalMeters > 0 ? (decimal)wp.CompletedMeters / wp.TotalMeters * 100 : 0
                    })
                    .ToListAsync();

                return (items, totalCount);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting work packages");
                throw;
            }
        }

        public async Task<WorkPackageDto?> GetWorkPackageByIdAsync(int id)
        {
            try
            {
                var workPackage = await _context.WorkPackages
                    .FirstOrDefaultAsync(wp => wp.Id == id);

                if (workPackage == null)
                    return null;

                // Get work package items
                var items = await GetWorkPackageItemsAsync(id) ?? new List<WorkPackageItemDto>();

                // Get work package assignments (if needed)
                var assignments = new List<WorkPackageAssignmentDto>(); // TODO: Implement GetWorkPackageAssignmentsAsync if needed

                return new WorkPackageDto
                {
                    Id = workPackage.Id,
                    Name = workPackage.Name,
                    Description = workPackage.Description,
                    PackageType = workPackage.PackageType,
                    Status = workPackage.Status,
                    PlannedStartDate = workPackage.PlannedStartDate,
                    PlannedEndDate = workPackage.PlannedEndDate,
                    ActualStartDate = workPackage.ActualStartDate,
                    ActualEndDate = workPackage.ActualEndDate,
                    Frequency = workPackage.Frequency,
                    ServiceArea = workPackage.ServiceArea,
                    SubArea = workPackage.SubArea,
                    Priority = workPackage.Priority,
                    TotalMeters = workPackage.TotalMeters,
                    CompletedMeters = workPackage.CompletedMeters,
                    ProgressPercentage = workPackage.ProgressPercentage,
                    AssignedTeam = workPackage.AssignedTeam,
                    EstimatedHours = workPackage.EstimatedHours,
                    ActualHours = workPackage.ActualHours,
                    EstimatedCost = workPackage.EstimatedCost,
                    ActualCost = workPackage.ActualCost,
                    IsTemplate = workPackage.IsTemplate,
                    TemplateCategory = workPackage.TemplateCategory,
                    IsRecurring = workPackage.IsRecurring,
                    RecurrencePattern = workPackage.RecurrencePattern,
                    RecurrenceInterval = workPackage.RecurrenceInterval,
                    LastExecuted = workPackage.LastExecuted,
                    NextExecution = workPackage.NextExecution,
                    Notes = workPackage.Notes,
                    Instructions = workPackage.Instructions,
                    AmsImportBatch = workPackage.AmsImportBatch,
                    AmsImportDate = workPackage.AmsImportDate,
                    Source = workPackage.Source,
                    CreatedBy = workPackage.CreatedBy,
                    UpdatedBy = workPackage.UpdatedBy,
                    CreatedAt = workPackage.CreatedAt,
                    UpdatedAt = workPackage.UpdatedAt,
                    IsStarted = workPackage.ActualStartDate.HasValue,
                    IsCompleted = workPackage.Status == "Completed",
                    IsOverdue = workPackage.Status != "Completed" && DateTime.UtcNow > workPackage.PlannedEndDate,
                    RemainingDays = workPackage.Status == "Completed" ? (int?)0 : (int?)Math.Ceiling((workPackage.PlannedEndDate - DateTime.UtcNow).TotalDays),
                    CompletionRate = workPackage.TotalMeters > 0 ? (decimal)workPackage.CompletedMeters / workPackage.TotalMeters * 100 : 0,
                    Items = items,
                    Assignments = assignments
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting work package {Id}", id);
                throw;
            }
        }

        public async Task<WorkPackageDto> CreateWorkPackageAsync(CreateWorkPackageDto createDto)
        {
            try
            {
                var workPackage = new WorkPackage
                {
                    Name = createDto.Name,
                    Description = createDto.Description,
                    PackageType = createDto.PackageType,
                    Status = "Draft",
                    PlannedStartDate = createDto.PlannedStartDate,
                    PlannedEndDate = createDto.PlannedEndDate,
                    Frequency = createDto.Frequency,
                    ServiceArea = createDto.ServiceArea,
                    SubArea = createDto.SubArea,
                    Priority = createDto.Priority,
                    AssignedTeam = createDto.AssignedTeam,
                    EstimatedHours = createDto.EstimatedHours,
                    EstimatedCost = createDto.EstimatedCost,
                    IsTemplate = createDto.IsTemplate,
                    TemplateCategory = createDto.TemplateCategory,
                    IsRecurring = createDto.IsRecurring,
                    RecurrencePattern = createDto.RecurrencePattern,
                    RecurrenceInterval = createDto.RecurrenceInterval,
                    Notes = createDto.Notes,
                    Instructions = createDto.Instructions,
                    Source = "Manual"
                };

                _context.WorkPackages.Add(workPackage);
                await _context.SaveChangesAsync();

                // Add meters if provided
                if (createDto.MeterIds.Any())
                {
                    var items = createDto.MeterIds.Select((meterId, index) => new WorkPackageItem
                    {
                        WorkPackageId = workPackage.Id,
                        MeterId = meterId,
                        SequenceOrder = index + 1,
                        Status = "Pending"
                    }).ToList();

                    _context.WorkPackageItems.AddRange(items);
                    workPackage.TotalMeters = items.Count;
                }

                // Add assignments if provided
                if (createDto.Assignments.Any())
                {
                    var assignments = createDto.Assignments.Select(a => new WorkPackageAssignment
                    {
                        WorkPackageId = workPackage.Id,
                        UserId = a.UserId,
                        AssignmentType = a.AssignmentType,
                        Status = "Assigned",
                        AssignedDate = DateTime.UtcNow,
                        AssignedBy = a.AssignedBy,
                        Reason = a.Reason,
                        ExpectedStartDate = a.ExpectedStartDate,
                        ExpectedCompletionDate = a.ExpectedCompletionDate,
                        AssignedMeterCount = a.AssignedMeterCount,
                        WorkloadWeight = a.WorkloadWeight,
                        Priority = a.Priority,
                        RequiresSupervision = a.RequiresSupervision,
                        SupervisorId = a.SupervisorId,
                        SkillRequirements = a.SkillRequirements,
                        EquipmentRequirements = a.EquipmentRequirements,
                        SpecialInstructions = a.SpecialInstructions
                    }).ToList();

                    _context.WorkPackageAssignments.AddRange(assignments);
                }

                await _context.SaveChangesAsync();

                // Add history record
                await AddHistoryAsync(workPackage.Id, "Created", $"Work package '{workPackage.Name}' created", workPackage.CreatedBy);

                return await GetWorkPackageByIdAsync(workPackage.Id) ?? throw new InvalidOperationException("Failed to retrieve created work package");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating work package");
                throw;
            }
        }

        /// <summary>
        /// Update an existing work package
        /// </summary>
        public async Task<WorkPackageDto> UpdateWorkPackageAsync(UpdateWorkPackageDto updateDto)
        {
            try
            {
                var existingWorkPackage = await _context.WorkPackages
                    .Include(wp => wp.Items)
                    .Include(wp => wp.Assignments)
                    .FirstOrDefaultAsync(wp => wp.Id == updateDto.Id);

                if (existingWorkPackage == null)
                {
                    throw new ArgumentException($"Work package with ID {updateDto.Id} not found");
                }

                // Store original values for history tracking
                var originalName = existingWorkPackage.Name;
                var originalStatus = existingWorkPackage.Status;

                // Update business fields only - audit fields are managed by DbContext
                existingWorkPackage.Name = updateDto.Name;
                existingWorkPackage.Description = updateDto.Description;
                existingWorkPackage.PackageType = updateDto.PackageType;
                existingWorkPackage.Status = updateDto.Status;
                existingWorkPackage.PlannedStartDate = updateDto.PlannedStartDate;
                existingWorkPackage.PlannedEndDate = updateDto.PlannedEndDate;
                existingWorkPackage.ActualStartDate = updateDto.ActualStartDate;
                existingWorkPackage.ActualEndDate = updateDto.ActualEndDate;
                existingWorkPackage.Frequency = updateDto.Frequency;
                existingWorkPackage.ServiceArea = updateDto.ServiceArea;
                existingWorkPackage.SubArea = updateDto.SubArea;
                existingWorkPackage.Priority = updateDto.Priority;
                existingWorkPackage.AssignedTeam = updateDto.AssignedTeam;
                existingWorkPackage.EstimatedHours = updateDto.EstimatedHours;
                existingWorkPackage.ActualHours = updateDto.ActualHours;
                existingWorkPackage.EstimatedCost = updateDto.EstimatedCost;
                existingWorkPackage.ActualCost = updateDto.ActualCost;
                existingWorkPackage.IsTemplate = updateDto.IsTemplate;
                existingWorkPackage.TemplateCategory = updateDto.TemplateCategory;
                existingWorkPackage.IsRecurring = updateDto.IsRecurring;
                existingWorkPackage.RecurrencePattern = updateDto.RecurrencePattern;
                existingWorkPackage.RecurrenceInterval = updateDto.RecurrenceInterval;
                existingWorkPackage.NextExecution = updateDto.NextExecution;
                existingWorkPackage.Notes = updateDto.Notes;
                existingWorkPackage.Instructions = updateDto.Instructions;

                // ============ AUTOMATIC AUDIT FIELD MANAGEMENT ============
                // UpdatedBy and UpdatedAt are set automatically by DbContext.SaveChangesAsync()
                // No manual intervention required - this is the beauty of the BaseEntity approach!

                // Save changes - audit fields will be set automatically
                await _context.SaveChangesAsync();

                // Add history records for significant changes
                var changedFields = new List<string>();
                if (originalName != existingWorkPackage.Name)
                    changedFields.Add($"Name: '{originalName}' → '{existingWorkPackage.Name}'");
                if (originalStatus != existingWorkPackage.Status)
                    changedFields.Add($"Status: '{originalStatus}' → '{existingWorkPackage.Status}'");

                var historyDescription = changedFields.Any() 
                    ? $"Work package updated: {string.Join(", ", changedFields)}"
                    : "Work package updated";

                await AddHistoryAsync(updateDto.Id, "Updated", historyDescription, existingWorkPackage.UpdatedBy);

                _logger.LogInformation("Work package {Id} updated by {UpdatedBy}", updateDto.Id, existingWorkPackage.UpdatedBy);

                // Return updated work package with full details
                return await GetWorkPackageByIdAsync(updateDto.Id) ?? throw new InvalidOperationException("Failed to retrieve updated work package");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating work package {Id}", updateDto.Id);
                throw;
            }
        }

        public async Task<bool> DeleteWorkPackageAsync(int id, string deletedBy)
        {
            try
            {
                var workPackage = await _context.WorkPackages.FindAsync(id);
                if (workPackage == null)
                {
                    _logger.LogWarning("Work package {Id} not found for deletion", id);
                    return false;
                }

                // Check if work package can be deleted (only allow deletion of Draft, Cancelled, or Completed packages)
                if (workPackage.Status == "Active" || workPackage.Status == "InProgress")
                {
                    _logger.LogWarning("Cannot delete active work package {Id}", id);
                    return false;
                }

                // Add deletion history before actually deleting
                await AddHistoryAsync(id, "Deleted", $"Work package deleted by {deletedBy}", deletedBy);

                // Delete related data in proper order
                // 1. Delete work package items
                var workPackageItems = await _context.WorkPackageItems
                    .Where(wpi => wpi.WorkPackageId == id)
                    .ToListAsync();
                _context.WorkPackageItems.RemoveRange(workPackageItems);

                // 2. Delete work package assignments (if any)
                var assignments = await _context.WorkPackageAssignments
                    .Where(wpa => wpa.WorkPackageId == id)
                    .ToListAsync();
                _context.WorkPackageAssignments.RemoveRange(assignments);

                // 3. Delete work package histories
                var histories = await _context.WorkPackageHistories
                    .Where(wph => wph.WorkPackageId == id)
                    .ToListAsync();
                _context.WorkPackageHistories.RemoveRange(histories);

                // 4. Finally delete the work package itself
                _context.WorkPackages.Remove(workPackage);

                await _context.SaveChangesAsync();

                _logger.LogInformation("Work package {Id} deleted successfully by {DeletedBy}", id, deletedBy);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting work package {Id}", id);
                throw;
            }
        }

        // Status management stubs
        public async Task<bool> UpdateWorkPackageStatusAsync(int id, string status, string updatedBy)
        {
            try
            {
                var workPackage = await _context.WorkPackages.FindAsync(id);
                if (workPackage == null)
                {
                    _logger.LogWarning("Work package {Id} not found for status update", id);
                    return false;
                }

                // Update status and related fields
                workPackage.Status = status;
                workPackage.UpdatedBy = updatedBy;
                workPackage.UpdatedAt = DateTime.UtcNow;

                // Set date fields based on status
                switch (status.ToLower())
                {
                    case "active":
                        if (workPackage.ActualStartDate == null)
                        {
                            workPackage.ActualStartDate = DateTime.UtcNow;
                        }
                        break;
                    case "completed":
                        if (workPackage.ActualEndDate == null)
                        {
                            workPackage.ActualEndDate = DateTime.UtcNow;
                        }
                        break;
                    case "cancelled":
                        if (workPackage.ActualEndDate == null)
                        {
                            workPackage.ActualEndDate = DateTime.UtcNow;
                        }
                        break;
                }

                await _context.SaveChangesAsync();

                // Add history record
                await AddHistoryAsync(id, "Status Updated", $"Status changed to {status} by {updatedBy}", updatedBy);

                _logger.LogInformation("Work package {Id} status updated to {Status} by {UpdatedBy}", id, status, updatedBy);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating work package {Id} status to {Status}", id, status);
                throw;
            }
        }

        public async Task<bool> StartWorkPackageAsync(int id, string startedBy)
        {
            try
            {
                var success = await UpdateWorkPackageStatusAsync(id, "Active", startedBy);
                if (success)
                {
                    await AddHistoryAsync(id, "Started", $"Work package started by {startedBy}", startedBy);
                }
                return success;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error starting work package {Id}", id);
                throw;
            }
        }

        public async Task<WorkPackageActivationResultDto> ActivateWorkPackageAsync(int workPackageId, string activatedBy, bool generateTasks = true)
        {
            try
            {
                _logger.LogInformation("Starting activation process for work package {WorkPackageId}", workPackageId);

                // 1. Get work package and validate existence
                var workPackage = await _context.WorkPackages.FindAsync(workPackageId);
                if (workPackage == null)
                {
                    return new WorkPackageActivationResultDto
                    {
                        Success = false,
                        Message = "Work package not found",
                        ActivatedAt = DateTime.UtcNow
                    };
                }

                // 2. Validate current status - can only activate Draft packages
                if (workPackage.Status != "Draft")
                {
                    return new WorkPackageActivationResultDto
                    {
                        Success = false,
                        Message = $"Cannot activate work package in {workPackage.Status} status. Only Draft work packages can be activated.",
                        ActivatedAt = DateTime.UtcNow
                    };
                }

                // 3. Validate work package has items (water meters)
                var itemCount = await _context.WorkPackageItems
                    .CountAsync(wpi => wpi.WorkPackageId == workPackageId);

                if (itemCount == 0)
                {
                    return new WorkPackageActivationResultDto
                    {
                        Success = false,
                        Message = "Cannot activate work package: No water meters assigned. Please add meters before activation.",
                        ValidationResults = { "No water meters found in work package" },
                        ActivatedAt = DateTime.UtcNow
                    };
                }

                // 4. Validate planned dates
                var validationResults = new List<string>();
                var warnings = new List<string>();

                if (workPackage.PlannedStartDate < DateTime.UtcNow.Date)
                {
                    warnings.Add($"Planned start date ({workPackage.PlannedStartDate:yyyy-MM-dd}) is in the past");
                }

                if (workPackage.PlannedEndDate < workPackage.PlannedStartDate)
                {
                    validationResults.Add("Planned end date cannot be earlier than planned start date");
                }

                if (workPackage.PlannedEndDate < DateTime.UtcNow.Date)
                {
                    warnings.Add($"Planned end date ({workPackage.PlannedEndDate:yyyy-MM-dd}) is in the past");
                }

                // 5. Check if tasks already exist
                var existingTaskCount = await _context.Tasks
                    .CountAsync(t => t.WorkPackageId == workPackageId);

                if (existingTaskCount > 0 && generateTasks)
                {
                    warnings.Add($"{existingTaskCount} tasks already exist for this work package");
                }

                // 6. Stop if validation failed
                if (validationResults.Any())
                {
                    return new WorkPackageActivationResultDto
                    {
                        Success = false,
                        Message = "Validation failed: " + string.Join(", ", validationResults),
                        ValidationResults = validationResults,
                        Warnings = warnings,
                        ActivatedAt = DateTime.UtcNow
                    };
                }

                // 7. Update work package status to Active
                workPackage.Status = "Active";
                workPackage.ActualStartDate = DateTime.UtcNow;
                workPackage.UpdatedAt = DateTime.UtcNow;
                workPackage.UpdatedBy = activatedBy;

                await _context.SaveChangesAsync();

                // 8. Add activation history
                await AddHistoryAsync(workPackageId, "Activated", 
                    $"Work package activated by {activatedBy}. {itemCount} meters included.", activatedBy);

                // 9. Generate tasks if requested and none exist
                var taskGenerationResult = new TaskGenerationResultDto { Success = true };
                if (generateTasks && existingTaskCount == 0)
                {
                    try
                    {
                        taskGenerationResult = await GenerateTasksAsync(workPackageId, activatedBy);
                        if (!taskGenerationResult.Success)
                        {
                            warnings.Add($"Task generation failed: {taskGenerationResult.Message}");
                        }
                        else
                        {
                            warnings.AddRange(taskGenerationResult.Warnings);
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error generating tasks during activation for work package {WorkPackageId}", workPackageId);
                        warnings.Add("Task generation failed due to system error");
                    }
                }

                _logger.LogInformation("Work package {WorkPackageId} activated successfully", workPackageId);

                return new WorkPackageActivationResultDto
                {
                    Success = true,
                    Message = $"Work package activated successfully with {itemCount} meters",
                    TasksGenerated = generateTasks && taskGenerationResult.Success,
                    GeneratedTaskCount = taskGenerationResult.GeneratedTaskCount,
                    ValidationResults = validationResults,
                    Warnings = warnings,
                    ActivatedAt = DateTime.UtcNow
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error activating work package {WorkPackageId}", workPackageId);
                return new WorkPackageActivationResultDto
                {
                    Success = false,
                    Message = "System error during activation: " + ex.Message,
                    ActivatedAt = DateTime.UtcNow
                };
            }
        }

        public async Task<bool> CompleteWorkPackageAsync(int id, string completedBy)
        {
            try
            {
                var success = await UpdateWorkPackageStatusAsync(id, "Completed", completedBy);
                if (success)
                {
                    await AddHistoryAsync(id, "Completed", $"Work package completed by {completedBy}", completedBy);
                }
                return success;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error completing work package {Id}", id);
                throw;
            }
        }

        public async Task<bool> CancelWorkPackageAsync(int id, string cancelledBy, string reason)
        {
            try
            {
                var success = await UpdateWorkPackageStatusAsync(id, "Cancelled", cancelledBy);
                if (success)
                {
                    var reasonText = string.IsNullOrEmpty(reason) ? "No reason provided" : reason;
                    await AddHistoryAsync(id, "Cancelled", $"Work package cancelled by {cancelledBy}. Reason: {reasonText}", cancelledBy, reason);
                }
                return success;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error cancelling work package {Id}", id);
                throw;
            }
        }

        // Work package items stubs
        public async Task<List<WorkPackageItemDto>> GetWorkPackageItemsAsync(int workPackageId)
        {
            try
            {
                var items = await _context.WorkPackageItems
                    .Include(wpi => wpi.Meter)
                    .Where(wpi => wpi.WorkPackageId == workPackageId)
                    .OrderBy(wpi => wpi.SequenceOrder)
                    .Select(wpi => new WorkPackageItemDto
                    {
                        Id = wpi.Id,
                        WorkPackageId = wpi.WorkPackageId,
                        MeterId = wpi.MeterId,
                        SequenceOrder = wpi.SequenceOrder,
                        Status = wpi.Status,
                        ScheduledDate = wpi.ScheduledDate,
                        ActualDate = wpi.ActualDate,
                        AssignedTo = wpi.AssignedTo,
                        Priority = wpi.Priority,
                        EstimatedMinutes = wpi.EstimatedMinutes,
                        ActualMinutes = wpi.ActualMinutes,
                        SpecialInstructions = wpi.SpecialInstructions,
                        Notes = wpi.Notes,
                        LastReading = wpi.LastReading,
                        RequiresSpecialHandling = wpi.RequiresSpecialHandling,
                        SpecialHandlingReason = wpi.SpecialHandlingReason,
                        DifficultyRating = wpi.DifficultyRating,
                        // Use WorkPackageItem coordinates first, fall back to WaterMeter coordinates
                        Latitude = wpi.Latitude ?? wpi.Meter.Latitude,
                        Longitude = wpi.Longitude ?? wpi.Meter.Longitude,
                        // Use ServiceAddress from WorkPackageItem if available, otherwise use WaterMeter address information
                        ServiceAddress = !string.IsNullOrEmpty(wpi.ServiceAddress) 
                            ? wpi.ServiceAddress 
                            : (wpi.Meter != null 
                                ? (!string.IsNullOrEmpty(wpi.Meter.Address) 
                                    ? wpi.Meter.Address 
                                    : (!string.IsNullOrEmpty(wpi.Meter.Location) 
                                        ? wpi.Meter.Location 
                                        : wpi.Meter.FullAddress))
                                : null),
                        PropertyDetails = wpi.Meter != null ? wpi.Meter.Comments : null, // Use WaterMeter Comments as PropertyDetails
                        AccessNotes = wpi.Meter != null ? wpi.Meter.Notes : null, // Use WaterMeter Notes as AccessNotes
                        HasPhoto = false, // This field doesn't exist in WorkPackageItem
                        PhotoUrl = null, // This field doesn't exist in WorkPackageItem
                        QrCode = null, // This field doesn't exist in WorkPackageItem
                        CreatedAt = wpi.CreatedAt,
                        UpdatedAt = wpi.UpdatedAt,
                        IsCompleted = wpi.Status == "Completed",
                        CanRetry = wpi.Status == "Failed" || wpi.Status == "Cancelled",
                        IsOverdue = wpi.ScheduledDate.HasValue && DateTime.UtcNow > wpi.ScheduledDate.Value && wpi.Status != "Completed",
                        EfficiencyScore = wpi.ActualMinutes.HasValue && wpi.EstimatedMinutes.HasValue && wpi.EstimatedMinutes.Value > 0 
                            ? (decimal)wpi.EstimatedMinutes.Value / wpi.ActualMinutes.Value * 100 : null,
                        
                        // Water meter details
                        MeterSerialNumber = wpi.Meter != null ? wpi.Meter.SerialNumber : null,
                        MeterType = wpi.Meter != null ? wpi.Meter.MeterType : null,
                        MeterSize = null, // MeterSize field doesn't exist in WaterMeter model
                        MeterLocation = wpi.Meter != null ? wpi.Meter.Location : null,
                        MeterDescription = wpi.Meter != null ? wpi.Meter.Comments : null // Use Comments field instead of Description
                    })
                    .ToListAsync();

                return items;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting work package items for work package {WorkPackageId}", workPackageId);
                throw;
            }
        }

        public async Task<bool> AddMetersToWorkPackageAsync(int workPackageId, List<int> meterIds, string addedBy)
        {
            throw new NotImplementedException("AddMetersToWorkPackageAsync not yet implemented");
        }

        public async Task<bool> RemoveMetersFromWorkPackageAsync(int workPackageId, List<int> meterIds, string removedBy)
        {
            throw new NotImplementedException("RemoveMetersFromWorkPackageAsync not yet implemented");
        }

        public async Task<bool> UpdateItemOrderAsync(int workPackageId, List<int> itemIds, string updatedBy)
        {
            throw new NotImplementedException("UpdateItemOrderAsync not yet implemented");
        }

        public async Task<bool> UpdateItemStatusAsync(int itemId, string status, string updatedBy)
        {
            throw new NotImplementedException("UpdateItemStatusAsync not yet implemented");
        }

        // Assignment stubs
        public async Task<List<WorkPackageAssignmentDto>> GetWorkPackageAssignmentsAsync(int workPackageId)
        {
            throw new NotImplementedException("GetWorkPackageAssignmentsAsync not yet implemented");
        }

        public async Task<bool> AssignWorkPackageAsync(int workPackageId, List<CreateWorkPackageAssignmentDto> assignments)
        {
            throw new NotImplementedException("AssignWorkPackageAsync not yet implemented");
        }

        public async Task<bool> RemoveAssignmentAsync(int assignmentId, string removedBy, string reason)
        {
            throw new NotImplementedException("RemoveAssignmentAsync not yet implemented");
        }

        public async Task<bool> AcceptAssignmentAsync(int assignmentId, string acceptedBy)
        {
            throw new NotImplementedException("AcceptAssignmentAsync not yet implemented");
        }

        public async Task<bool> RejectAssignmentAsync(int assignmentId, string rejectedBy, string reason)
        {
            throw new NotImplementedException("RejectAssignmentAsync not yet implemented");
        }

        public async Task<bool> UpdateAssignmentProgressAsync(int assignmentId, int completedMeterCount, string updatedBy)
        {
            throw new NotImplementedException("UpdateAssignmentProgressAsync not yet implemented");
        }

        // Task generation stubs
        public async Task<TaskGenerationResultDto> GenerateTasksAsync(int workPackageId, string createdBy)
        {
            try
            {
                _logger.LogInformation("Starting task generation for work package {WorkPackageId}", workPackageId);

                // 1. Get work package and validate
                var workPackage = await _context.WorkPackages.FindAsync(workPackageId);
                if (workPackage == null)
        {
            return new TaskGenerationResultDto 
            { 
                Success = false, 
                        Message = "Work package not found"
                    };
                }

                // 2. Check if work package is in correct status
                if (workPackage.Status != "Draft" && workPackage.Status != "Active")
                {
                    return new TaskGenerationResultDto
                    {
                        Success = false,
                        Message = $"Cannot generate tasks for work package in {workPackage.Status} status"
                    };
                }

                // 3. Get work package items
                var workPackageItems = await _context.WorkPackageItems
                    .Include(wpi => wpi.Meter)
                    .Where(wpi => wpi.WorkPackageId == workPackageId && !wpi.IsDeleted)
                    .ToListAsync();

                if (!workPackageItems.Any())
                {
                    return new TaskGenerationResultDto
                    {
                        Success = false,
                        Message = "No meters found in work package"
                    };
                }

                // 4. Check if tasks already exist for this work package
                var existingTasks = await _context.Tasks
                    .Where(t => t.WorkPackageId == workPackageId && !t.IsDeleted)
                    .CountAsync();

                if (existingTasks > 0)
                {
                    return new TaskGenerationResultDto
                    {
                        Success = false,
                        Message = $"Tasks already exist for this work package ({existingTasks} tasks found)"
                    };
                }

                // 5. Generate tasks for each work package item
                var generatedTasks = new List<Models.WorkTask>();
                var warnings = new List<string>();

                foreach (var item in workPackageItems)
                {
                    try
                    {
                        var task = new Models.WorkTask
                        {
                            Name = $"{workPackage.Name} - {item.Meter?.SerialNumber ?? $"Meter {item.MeterId}"}",
                            Description = $"Meter reading task for {item.Meter?.SerialNumber}",
                            Type = "MeterReading",
                            Status = "Pending",
                            Priority = item.Priority ?? workPackage.Priority,
                            WorkPackageId = workPackageId,
                            WorkPackageItemId = item.Id,
                            MeterId = item.MeterId,
                            CreatedBy = createdBy,
                            ScheduledDate = item.ScheduledDate ?? workPackage.PlannedStartDate,
                            DueDate = workPackage.PlannedEndDate,
                            EstimatedHours = item.EstimatedMinutes.HasValue ? item.EstimatedMinutes.Value / 60.0m : null,
                            Location = item.ServiceAddress ?? item.Meter?.Location,
                            ServiceAddress = item.ServiceAddress ?? item.Meter?.Address,
                            Latitude = item.Latitude ?? item.Meter?.Latitude,
                            Longitude = item.Longitude ?? item.Meter?.Longitude,
                            Instructions = string.Join(". ", new[] 
                            { 
                                workPackage.Instructions, 
                                item.SpecialInstructions 
                            }.Where(s => !string.IsNullOrEmpty(s))),
                            Notes = item.Notes,
                            LastReading = item.LastReading,
                            CreatedAt = DateTime.UtcNow,
                            UpdatedAt = DateTime.UtcNow
                        };

                        _context.Tasks.Add(task);
                        generatedTasks.Add(task);

                        _logger.LogDebug("Generated task for meter {MeterId} in work package {WorkPackageId}", 
                            item.MeterId, workPackageId);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "Failed to generate task for work package item {ItemId}", item.Id);
                        warnings.Add($"Failed to generate task for meter {item.Meter?.SerialNumber ?? item.MeterId.ToString()}: {ex.Message}");
                    }
                }

                // 6. Save all tasks to database
                await _context.SaveChangesAsync();

                // 7. Update work package status to Active if it was Draft
                if (workPackage.Status == "Draft")
                {
                    workPackage.Status = "Active";
                    workPackage.ActualStartDate = DateTime.UtcNow;
                    workPackage.UpdatedAt = DateTime.UtcNow;
                    await _context.SaveChangesAsync();
                }

                // 8. Add history record
                await AddHistoryAsync(workPackageId, "TaskGenerated", 
                    $"Generated {generatedTasks.Count} tasks from work package items", createdBy);

                _logger.LogInformation("Successfully generated {TaskCount} tasks for work package {WorkPackageId}", 
                    generatedTasks.Count, workPackageId);

                return new TaskGenerationResultDto
                {
                    Success = true,
                    Message = $"Successfully generated {generatedTasks.Count} tasks",
                    GeneratedTaskCount = generatedTasks.Count,
                    TaskIds = generatedTasks.Select(t => t.Id).ToList(),
                    Warnings = warnings
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating tasks for work package {WorkPackageId}", workPackageId);
                return new TaskGenerationResultDto
                {
                    Success = false,
                    Message = $"Failed to generate tasks: {ex.Message}"
                };
            }
        }

        public async Task<TaskGenerationResultDto> RegenerateTasksAsync(int workPackageId, string createdBy, bool deleteExisting = true)
        {
            return new TaskGenerationResultDto 
            { 
                Success = false, 
                Message = "Task regeneration not yet implemented" 
            };
        }

        // Template stubs
        public async Task<List<WorkPackageListDto>> GetTemplatesAsync(string? category = null)
        {
            throw new NotImplementedException("GetTemplatesAsync not yet implemented");
        }

        public async Task<WorkPackageDto> CreateFromTemplateAsync(int templateId, CreateWorkPackageDto createDto)
        {
            throw new NotImplementedException("CreateFromTemplateAsync not yet implemented");
        }

        public async Task<WorkPackageDto> SaveAsTemplateAsync(int workPackageId, string templateName, string category, string createdBy)
        {
            throw new NotImplementedException("SaveAsTemplateAsync not yet implemented");
        }

        // Validation
        public async Task<List<string>> ValidateWorkPackageAsync(CreateWorkPackageDto workPackage)
        {
            var errors = new List<string>();

            if (workPackage.PlannedStartDate >= workPackage.PlannedEndDate)
            {
                errors.Add("Planned start date must be before planned end date");
            }

            if (workPackage.PlannedStartDate < DateTime.UtcNow.Date.AddDays(-1))
            {
                errors.Add("Planned start date cannot be in the past");
            }

            return errors;
        }

        // Statistics
        public async Task<WorkPackageStatisticsDto> GetStatisticsAsync(WorkPackageSearchDto? filter = null)
        {
            var query = _context.WorkPackages.AsQueryable();

            // Apply filters if provided
            if (filter != null)
            {
                // Apply same filters as in GetWorkPackagesAsync
            }

            var stats = new WorkPackageStatisticsDto
            {
                TotalWorkPackages = await query.CountAsync(),
                DraftWorkPackages = await query.CountAsync(wp => wp.Status == "Draft"),
                ActiveWorkPackages = await query.CountAsync(wp => wp.Status == "Active"),
                InProgressWorkPackages = await query.CountAsync(wp => wp.Status == "InProgress"),
                CompletedWorkPackages = await query.CountAsync(wp => wp.Status == "Completed"),
                CancelledWorkPackages = await query.CountAsync(wp => wp.Status == "Cancelled"),
                OverdueWorkPackages = await query.CountAsync(wp => wp.Status != "Completed" && wp.PlannedEndDate < DateTime.UtcNow),
                TemplateWorkPackages = await query.CountAsync(wp => wp.IsTemplate),
                RecurringWorkPackages = await query.CountAsync(wp => wp.IsRecurring),
                TotalMeters = await query.SumAsync(wp => wp.TotalMeters),
                CompletedMeters = await query.SumAsync(wp => wp.CompletedMeters),
                TotalEstimatedHours = await query.SumAsync(wp => wp.EstimatedHours),
                TotalActualHours = await query.SumAsync(wp => wp.ActualHours ?? 0)
            };

            stats.PendingMeters = stats.TotalMeters - stats.CompletedMeters;
            stats.OverallProgress = stats.TotalMeters > 0 ? (decimal)stats.CompletedMeters / stats.TotalMeters * 100 : 0;
            stats.EfficiencyRatio = stats.TotalEstimatedHours > 0 && stats.TotalActualHours > 0 
                ? stats.TotalEstimatedHours / stats.TotalActualHours * 100 
                : 0;

            return stats;
        }

        // History
        public async Task AddHistoryAsync(int workPackageId, string action, string description, string actionBy, string? additionalData = null)
        {
            var history = new WorkPackageHistory
            {
                WorkPackageId = workPackageId,
                Action = action,
                Description = description,
                ChangedBy = actionBy,
                ChangedAt = DateTime.UtcNow,
                AdditionalData = additionalData,
                Source = "Web"
            };

            _context.WorkPackageHistories.Add(history);
            await _context.SaveChangesAsync();
        }

        #endregion

        // Placeholder implementations for all other required interface methods
        // These will be implemented incrementally

        public async Task<List<WorkPackageListDto>> GetRecurringWorkPackagesAsync()
        {
            throw new NotImplementedException();
        }

        public async Task<List<WorkPackageDto>> ProcessRecurringWorkPackagesAsync(string createdBy)
        {
            throw new NotImplementedException();
        }

        public async Task<bool> UpdateRecurrenceAsync(int workPackageId, string pattern, int interval, DateTime nextExecution, string updatedBy)
        {
            throw new NotImplementedException();
        }

        public async Task<WorkPackageImportResultDto> ImportFromCsvAsync(Stream csvStream, string importedBy)
        {
            throw new NotImplementedException();
        }

        public async Task<byte[]> ExportToCsvAsync(WorkPackageSearchDto? search = null)
        {
            throw new NotImplementedException();
        }

        public async Task<WorkPackageImportResultDto> ImportFromAmsExcelAsync(Stream excelStream, string importedBy)
        {
            throw new NotImplementedException();
        }

        public async Task<List<UserWorkloadDto>> GetUserWorkloadAsync(DateTime? dateFrom = null, DateTime? dateTo = null)
        {
            throw new NotImplementedException();
        }

        public async Task<List<WorkPackagePerformanceDto>> GetPerformanceMetricsAsync(int? workPackageId = null, DateTime? dateFrom = null, DateTime? dateTo = null)
        {
            throw new NotImplementedException();
        }

        public async Task<(bool CanStart, List<string> Reasons)> CanStartWorkPackageAsync(int workPackageId)
        {
            throw new NotImplementedException();
        }

        public async Task<(bool CanComplete, List<string> Reasons)> CanCompleteWorkPackageAsync(int workPackageId)
        {
            throw new NotImplementedException();
        }

        public async Task<List<WaterMeterDto>> GetSuggestedMetersAsync(string serviceArea, string? subArea = null, int maxCount = 100)
        {
            throw new NotImplementedException();
        }

        public async Task<bool> SendAssignmentNotificationsAsync(int workPackageId)
        {
            throw new NotImplementedException();
        }

        public async Task<bool> SendOverdueRemindersAsync()
        {
            throw new NotImplementedException();
        }

        public async Task<List<WorkPackageHistoryDto>> GetHistoryAsync(int workPackageId)
        {
            throw new NotImplementedException();
        }

        // New methods for enhanced Work Package Management UI

        public async Task<byte[]> GenerateTemplateAsync()
        {
            // Create a simple Excel template with headers
            using var package = new OfficeOpenXml.ExcelPackage();
            var worksheet = package.Workbook.Worksheets.Add("Work Package Template");

            // Add headers
            var headers = new string[]
            {
                "Name", "Description", "PackageType", "PlannedStartDate", "PlannedEndDate",
                "ServiceArea", "SubArea", "Priority", "EstimatedHours", "AssignedTeam",
                "Notes", "Instructions"
            };

            for (int i = 0; i < headers.Length; i++)
            {
                worksheet.Cells[1, i + 1].Value = headers[i];
                worksheet.Cells[1, i + 1].Style.Font.Bold = true;
            }

            // Auto-fit columns
            worksheet.Cells.AutoFitColumns();

            return await Task.FromResult(package.GetAsByteArray());
        }

        public async Task<byte[]> ExportFilteredTemplateAsync(object filterRequest)
        {
            // For now, return the same template - this can be enhanced later
            // with actual filtering based on the filterRequest
            return await GenerateTemplateAsync();
        }

        public async Task<ImportResultDto> ImportWorkPackagesAsync(IFormFile file, string importedBy)
        {
            var result = new ImportResultDto();
            
            try
            {
                using var stream = file.OpenReadStream();
                using var package = new OfficeOpenXml.ExcelPackage(stream);
                var worksheet = package.Workbook.Worksheets.FirstOrDefault();
                
                if (worksheet == null)
                {
                    result.Errors.Add("No worksheet found in the Excel file");
                    return result;
                }

                var rowCount = worksheet.Dimension?.Rows ?? 0;
                result.TotalRows = Math.Max(0, rowCount - 1); // Exclude header row

                // Process each row (starting from row 2 to skip headers)
                for (int row = 2; row <= rowCount; row++)
                {
                    try
                    {
                        var workPackage = new CreateWorkPackageDto
                        {
                            Name = worksheet.Cells[row, 1].Value?.ToString() ?? "",
                            Description = worksheet.Cells[row, 2].Value?.ToString(),
                            PackageType = worksheet.Cells[row, 3].Value?.ToString() ?? "Scheduled",
                            PlannedStartDate = DateTime.TryParse(worksheet.Cells[row, 4].Value?.ToString(), out var startDate) ? startDate : DateTime.UtcNow,
                            PlannedEndDate = DateTime.TryParse(worksheet.Cells[row, 5].Value?.ToString(), out var endDate) ? endDate : DateTime.UtcNow.AddDays(30),
                            ServiceArea = worksheet.Cells[row, 6].Value?.ToString() ?? "",
                            SubArea = worksheet.Cells[row, 7].Value?.ToString(),
                            Priority = worksheet.Cells[row, 8].Value?.ToString() ?? "Medium",
                            EstimatedHours = decimal.TryParse(worksheet.Cells[row, 9].Value?.ToString(), out var hours) ? hours : 0,
                            AssignedTeam = worksheet.Cells[row, 10].Value?.ToString(),
                            Notes = worksheet.Cells[row, 11].Value?.ToString(),
                            Instructions = worksheet.Cells[row, 12].Value?.ToString(),
                            Frequency = "Monthly"
                        };

                        // Validate and create work package
                        var validationErrors = await ValidateWorkPackageAsync(workPackage);
                        if (validationErrors.Any())
                        {
                            result.Errors.Add($"Row {row}: {string.Join(", ", validationErrors)}");
                            result.FailureCount++;
                        }
                        else
                        {
                            await CreateWorkPackageAsync(workPackage);
                            result.SuccessCount++;
                        }
                    }
                    catch (Exception ex)
                    {
                        result.Errors.Add($"Row {row}: {ex.Message}");
                        result.FailureCount++;
                    }
                }
            }
            catch (Exception ex)
            {
                result.Errors.Add($"Error processing file: {ex.Message}");
            }

            return result;
        }

        public async Task<byte[]> ExportWorkPackagesAsync(WorkPackageSearchDto? search = null)
        {
            var (workPackages, _) = await GetWorkPackagesAsync(1, int.MaxValue, search);

            using var package = new OfficeOpenXml.ExcelPackage();
            var worksheet = package.Workbook.Worksheets.Add("Work Packages");

            // Add headers
            var headers = new string[]
            {
                "ID", "Name", "Description", "Status", "PackageType", "Priority",
                "ServiceArea", "SubArea", "PlannedStartDate", "PlannedEndDate",
                "TotalMeters", "CompletedMeters", "ProgressPercentage", "AssignedTeam",
                "EstimatedHours", "ActualHours", "CreatedBy", "CreatedAt"
            };

            for (int i = 0; i < headers.Length; i++)
            {
                worksheet.Cells[1, i + 1].Value = headers[i];
                worksheet.Cells[1, i + 1].Style.Font.Bold = true;
            }

            // Add data
            for (int i = 0; i < workPackages.Count; i++)
            {
                var wp = workPackages[i];
                var row = i + 2;

                worksheet.Cells[row, 1].Value = wp.Id;
                worksheet.Cells[row, 2].Value = wp.Name;
                worksheet.Cells[row, 3].Value = wp.Description;
                worksheet.Cells[row, 4].Value = wp.Status;
                worksheet.Cells[row, 5].Value = wp.PackageType;
                worksheet.Cells[row, 6].Value = wp.Priority;
                worksheet.Cells[row, 7].Value = wp.ServiceArea;
                worksheet.Cells[row, 8].Value = wp.SubArea;
                worksheet.Cells[row, 9].Value = wp.PlannedStartDate.ToString("yyyy-MM-dd");
                worksheet.Cells[row, 10].Value = wp.PlannedEndDate.ToString("yyyy-MM-dd");
                worksheet.Cells[row, 11].Value = wp.TotalMeters;
                worksheet.Cells[row, 12].Value = wp.CompletedMeters;
                worksheet.Cells[row, 13].Value = wp.ProgressPercentage;
                worksheet.Cells[row, 14].Value = wp.AssignedTeam;
                worksheet.Cells[row, 15].Value = wp.EstimatedHours;
                worksheet.Cells[row, 16].Value = ""; // ActualHours - not in list DTO
                worksheet.Cells[row, 17].Value = wp.CreatedBy;
                worksheet.Cells[row, 18].Value = wp.CreatedAt.ToString("yyyy-MM-dd HH:mm");
            }

            // Auto-fit columns
            worksheet.Cells.AutoFitColumns();

            return package.GetAsByteArray();
        }

        public async Task<BatchOperationResultDto> BatchActivateAsync(List<int> ids, string activatedBy)
        {
            var result = new BatchOperationResultDto();

            foreach (var id in ids)
            {
                try
                {
                    var success = await UpdateWorkPackageStatusAsync(id, "Active", activatedBy);
                    if (success)
                    {
                        result.SuccessCount++;
                        result.ProcessedIds.Add(id);
                    }
                    else
                    {
                        result.FailureCount++;
                        result.Errors.Add($"Failed to activate work package {id}");
                    }
                }
                catch (Exception ex)
                {
                    result.FailureCount++;
                    result.Errors.Add($"Error activating work package {id}: {ex.Message}");
                }
            }

            return result;
        }

        public async Task<BatchOperationResultDto> BatchPauseAsync(List<int> ids, string pausedBy)
        {
            var result = new BatchOperationResultDto();

            foreach (var id in ids)
            {
                try
                {
                    var success = await UpdateWorkPackageStatusAsync(id, "Paused", pausedBy);
                    if (success)
                    {
                        result.SuccessCount++;
                        result.ProcessedIds.Add(id);
                    }
                    else
                    {
                        result.FailureCount++;
                        result.Errors.Add($"Failed to pause work package {id}");
                    }
                }
                catch (Exception ex)
                {
                    result.FailureCount++;
                    result.Errors.Add($"Error pausing work package {id}: {ex.Message}");
                }
            }

            return result;
        }

        public async Task<BatchOperationResultDto> BatchCancelAsync(List<int> ids, string? reason, string cancelledBy)
        {
            var result = new BatchOperationResultDto();

            foreach (var id in ids)
            {
                try
                {
                    var success = await CancelWorkPackageAsync(id, cancelledBy, reason ?? "Batch cancellation");
                    if (success)
                    {
                        result.SuccessCount++;
                        result.ProcessedIds.Add(id);
                    }
                    else
                    {
                        result.FailureCount++;
                        result.Errors.Add($"Failed to cancel work package {id}");
                    }
                }
                catch (Exception ex)
                {
                    result.FailureCount++;
                    result.Errors.Add($"Error cancelling work package {id}: {ex.Message}");
                }
            }

            return result;
        }

        public async Task<BatchOperationResultDto> BatchDeleteAsync(List<int> ids, string deletedBy)
        {
            var result = new BatchOperationResultDto();

            foreach (var id in ids)
            {
                try
                {
                    var success = await DeleteWorkPackageAsync(id, deletedBy);
                    if (success)
                    {
                        result.SuccessCount++;
                        result.ProcessedIds.Add(id);
                    }
                    else
                    {
                        result.FailureCount++;
                        result.Errors.Add($"Failed to delete work package {id}");
                    }
                }
                catch (Exception ex)
                {
                    result.FailureCount++;
                    result.Errors.Add($"Error deleting work package {id}: {ex.Message}");
                }
            }

            return result;
        }
    }
}
