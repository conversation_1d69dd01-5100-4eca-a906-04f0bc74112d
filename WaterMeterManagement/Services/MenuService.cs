using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using WaterMeterManagement.Models;
using WaterMeterManagement.Repositories.Interfaces;
using WaterMeterManagement.Services.Interfaces;

namespace WaterMeterManagement.Services
{
    public class MenuService : IMenuService
    {
        private readonly IMenuRepository _menuRepository;
        private readonly IUserRoleRepository _userRoleRepository;
        private readonly IRolePermissionRepository _rolePermissionRepository;

        public MenuService(
            IMenuRepository menuRepository,
            IUserRoleRepository userRoleRepository,
            IRolePermissionRepository rolePermissionRepository)
        {
            _menuRepository = menuRepository;
            _userRoleRepository = userRoleRepository;
            _rolePermissionRepository = rolePermissionRepository;
        }

        public async Task<IEnumerable<Menu>> GetAllMenusAsync()
        {
            return await _menuRepository.GetAllAsync();
        }

        public async Task<Menu> GetMenuByIdAsync(int id)
        {
            return await _menuRepository.GetByIdAsync(id);
        }

        public async Task<Menu> GetMenuByCodeAsync(string code)
        {
            return await _menuRepository.GetByCodeAsync(code);
        }

        public async Task<IEnumerable<Menu>> GetUserMenusAsync(int userId)
        {
            Console.WriteLine($"🔍 GetUserMenusAsync called for userId: {userId}");
            
            // Get user roles
            var userRoles = await _userRoleRepository.GetByUserIdAsync(userId);
            var roleIds = userRoles.Select(ur => ur.RoleId).ToList();
            Console.WriteLine($"📋 User roles found: {string.Join(", ", roleIds)}");

            // Get role permissions
            var rolePermissions = await _rolePermissionRepository.GetByRoleIdsAsync(roleIds);
            var permissionIds = rolePermissions.Select(rp => rp.PermissionId).ToList();
            Console.WriteLine($"🔐 Permission IDs found: {string.Join(", ", permissionIds)}");

            // Get all menus
            var allMenus = await _menuRepository.GetAllAsync();
            Console.WriteLine($"📝 Total menus in database: {allMenus.Count()}");
            
            // Debug: Print all menus with their parent info
            foreach (var menu in allMenus)
            {
                var permissionInfo = menu.Permission != null ? $"PermissionId: {menu.Permission.Id}" : "No Permission";
                var parentInfo = menu.ParentId.HasValue ? $"ParentId: {menu.ParentId}" : "Root";
                Console.WriteLine($"  📄 Menu: {menu.Name} (Code: {menu.Code}, {parentInfo}, {permissionInfo})");
            }

            // Filter menus based on permissions
            var filteredMenus = allMenus.Where(m => 
                m.Permission == null || 
                permissionIds.Contains(m.Permission.Id))
                .OrderBy(m => m.Order)
                .ToList();
                
            Console.WriteLine($"✅ Filtered menus count: {filteredMenus.Count}");
            Console.WriteLine("🎯 Filtered menus:");
            foreach (var menu in filteredMenus)
            {
                var parentInfo = menu.ParentId.HasValue ? $"ParentId: {menu.ParentId}" : "Root";
                Console.WriteLine($"  ✓ {menu.Name} (Code: {menu.Code}, {parentInfo})");
            }

            return filteredMenus;
        }

        public async Task<Menu> CreateMenuAsync(Menu menu)
        {
            if (await _menuRepository.ExistsByCodeAsync(menu.Code))
            {
                throw new System.Exception("Menu code already exists");
            }

            return await _menuRepository.CreateAsync(menu);
        }

        public async Task<Menu> UpdateMenuAsync(Menu menu)
        {
            if (!await _menuRepository.ExistsAsync(menu.Id))
            {
                throw new System.Exception("Menu not found");
            }

            // Check if code exists for a different menu
            var existingMenuWithCode = await _menuRepository.GetByCodeAsync(menu.Code);
            if (existingMenuWithCode != null && existingMenuWithCode.Id != menu.Id)
            {
                throw new System.Exception("Menu code already exists");
            }

            return await _menuRepository.UpdateAsync(menu);
        }

        public async Task DeleteMenuAsync(int id)
        {
            if (!await _menuRepository.ExistsAsync(id))
            {
                throw new System.Exception("Menu not found");
            }

            await _menuRepository.DeleteAsync(id);
        }

        public async Task<bool> ValidateMenuCodeAsync(string code, int? excludeId = null)
        {
            var menu = await _menuRepository.GetByCodeAsync(code);
            return menu == null || (excludeId.HasValue && menu.Id == excludeId.Value);
        }
    }
} 