using Microsoft.EntityFrameworkCore;
using Microsoft.IdentityModel.Tokens;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;
using WaterMeterManagement.Data;
using WaterMeterManagement.DTOs;
using WaterMeterManagement.Models;
using WaterMeterManagement.Repositories.Interfaces;
using WaterMeterManagement.Services.Interfaces;

namespace WaterMeterManagement.Services
{
    /// <summary>
    /// Authentication service implementation
    /// </summary>
    public class AuthService : IAuthService
    {
        private readonly ApplicationDbContext _context;
        private readonly IWorkbenchService _workbenchService;
        private readonly IConfiguration _configuration;
        private readonly ILogger<AuthService> _logger;
        private readonly IUserRoleRepository _userRoleRepository;
        private readonly IRoleRepository _roleRepository;

        public AuthService(
            ApplicationDbContext context,
            IWorkbenchService workbenchService,
            IConfiguration configuration,
            ILogger<AuthService> logger,
            IUserRoleRepository userRoleRepository,
            IRoleRepository roleRepository)
        {
            _context = context;
            _workbenchService = workbenchService;
            _configuration = configuration;
            _logger = logger;
            _userRoleRepository = userRoleRepository;
            _roleRepository = roleRepository;
        }

        public async Task<LoginResponseDto> LoginAsync(LoginRequestDto loginRequest)
        {
            try
            {
                _logger.LogInformation("Attempting user login: {Username}", loginRequest.Username);

                // Special handling for admin user
                if (loginRequest.Username.Equals("admin", StringComparison.OrdinalIgnoreCase))
                {
                    // Check if admin password is correct (use a simple password for demo)
                    if (loginRequest.Password != "admin123")
                    {
                        _logger.LogWarning("Admin authentication failed: {Username}", loginRequest.Username);
                        return new LoginResponseDto
                        {
                            Success = false,
                            Message = "Invalid admin credentials."
                        };
                    }

                    // For admin, create a mock Workbench user
                    var adminWorkbenchUser = new WorkbenchLoginResponseDto
                    {
                        PersonID = 999999,
                        FullName = "System Administrator",
                        FinCoCode = "SYSTEM"
                    };

                    return await ProcessUserLoginAsync(loginRequest.Username, adminWorkbenchUser, true);
                }

                // Authenticate regular users through Workbench API
                var workbenchUser = await _workbenchService.AuthenticateAsync(
                    loginRequest.Username, 
                    loginRequest.Password);

                if (workbenchUser == null)
                {
                    _logger.LogWarning("Workbench authentication failed: {Username}", loginRequest.Username);
                    return new LoginResponseDto
                    {
                        Success = false,
                        Message = "Invalid username or password. Please check your Workbench credentials."
                    };
                }

                return await ProcessUserLoginAsync(loginRequest.Username, workbenchUser, false);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred during login process: {Username}", loginRequest.Username);
                return new LoginResponseDto
                {
                    Success = false,
                    Message = "An error occurred during login. Please try again later."
                };
            }
        }

        private async Task<LoginResponseDto> ProcessUserLoginAsync(string username, WorkbenchLoginResponseDto workbenchUser, bool isAdmin)
        {
            // Find or create user in local database
            var user = await _context.Users
                .FirstOrDefaultAsync(u => u.Username == username);

            bool isNewUser = false;
            if (user == null)
            {
                // Create new user
                user = new User
                {
                    Username = username,
                    FullName = workbenchUser.FullName,
                    PersonId = workbenchUser.PersonID,
                    FinCoCode = workbenchUser.FinCoCode,
                    IsAuthenticated = true,
                    LastLogin = DateTime.UtcNow,
                    CreatedDate = DateTime.UtcNow,
                    UpdatedDate = DateTime.UtcNow
                };

                _context.Users.Add(user);
                await _context.SaveChangesAsync();
                isNewUser = true;
                _logger.LogInformation("Creating new user: {Username}", username);
            }
            else
            {
                // Update existing user information
                user.FullName = workbenchUser.FullName;
                user.PersonId = workbenchUser.PersonID;
                user.FinCoCode = workbenchUser.FinCoCode;
                user.IsAuthenticated = true;
                user.LastLogin = DateTime.UtcNow;
                user.UpdatedDate = DateTime.UtcNow;

                _context.Users.Update(user);
                await _context.SaveChangesAsync();
                _logger.LogInformation("Updating existing user: {Username}", username);
            }

            // For new non-admin users, assign default Operator role
            if (isNewUser && !isAdmin)
            {
                await AssignDefaultRoleAsync(user.Id);
            }

            // Get user roles and permissions for JWT
            var userRoles = await GetUserRolesAsync(user.Id);
            var userPermissions = await GetUserPermissionsAsync(user.Id);

            // Generate user DTO
            var userDto = new UserDto
            {
                Id = user.Id,
                Username = user.Username,
                FullName = user.FullName,
                PersonId = user.PersonId,
                FinCoCode = user.FinCoCode,
                IsAuthenticated = user.IsAuthenticated,
                LastLogin = user.LastLogin
            };

            // Generate JWT Token with roles and permissions
            var token = GenerateJwtToken(userDto, userRoles, userPermissions);

            _logger.LogInformation("User login successful: {Username} ({FullName}) with {RoleCount} roles", 
                user.Username, user.FullName, userRoles.Count);

            return new LoginResponseDto
            {
                Success = true,
                Message = "Login successful",
                Token = token,
                User = userDto
            };
        }

        private async Task AssignDefaultRoleAsync(int userId)
        {
            try
            {
                // Find Operator role
                var operatorRole = await _roleRepository.GetByNameAsync("Operator");
                if (operatorRole != null)
                {
                    // Check if user already has this role
                    var exists = await _userRoleRepository.ExistsAsync(userId, operatorRole.Id);
                    if (!exists)
                    {
                        var userRole = new UserRole
                        {
                            UserId = userId,
                            RoleId = operatorRole.Id,
                            CreatedAt = DateTime.UtcNow,
                            CreatedBy = "System",
                            IsDeleted = false
                        };

                        await _userRoleRepository.CreateAsync(userRole);
                        _logger.LogInformation("Assigned default Operator role to user: {UserId}", userId);
                    }
                }
                else
                {
                    _logger.LogWarning("Operator role not found in database");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error assigning default role to user: {UserId}", userId);
            }
        }

        private async Task<List<string>> GetUserRolesAsync(int userId)
        {
            var userRoles = await _userRoleRepository.GetByUserIdAsync(userId);
            return userRoles.Select(ur => ur.Role.Name).ToList();
        }

        private async Task<List<string>> GetUserPermissionsAsync(int userId)
        {
            var userRoles = await _userRoleRepository.GetByUserIdAsync(userId);
            var roleIds = userRoles.Select(ur => ur.RoleId).ToList();
            
            if (!roleIds.Any())
                return new List<string>();

            var permissions = await _context.RolePermissions
                .Include(rp => rp.Permission)
                .Where(rp => roleIds.Contains(rp.RoleId) && !rp.IsDeleted)
                .Select(rp => rp.Permission.Code)
                .Distinct()
                .ToListAsync();

            return permissions;
        }

        public string GenerateJwtToken(UserDto user)
        {
            return GenerateJwtToken(user, new List<string>(), new List<string>());
        }

        public string GenerateJwtToken(UserDto user, List<string> roles, List<string> permissions)
        {
            var tokenHandler = new JwtSecurityTokenHandler();
            var key = Encoding.ASCII.GetBytes(_configuration["Jwt:SecretKey"] ?? "DefaultSecretKey123456789");
            
            var claims = new List<Claim>
            {
                new Claim(ClaimTypes.NameIdentifier, user.Id.ToString()),
                new Claim(ClaimTypes.Name, user.Username),
                new Claim("FullName", user.FullName),
                new Claim("PersonId", user.PersonId.ToString()),
                new Claim("FinCoCode", user.FinCoCode)
            };

            // Add roles to claims
            foreach (var role in roles)
            {
                claims.Add(new Claim(ClaimTypes.Role, role));
            }

            // Add permissions to claims
            foreach (var permission in permissions)
            {
                claims.Add(new Claim("permission", permission));
            }

            var tokenDescriptor = new SecurityTokenDescriptor
            {
                Subject = new ClaimsIdentity(claims),
                Expires = DateTime.UtcNow.AddDays(30), // 30 days expiration
                SigningCredentials = new SigningCredentials(new SymmetricSecurityKey(key), 
                    SecurityAlgorithms.HmacSha256Signature),
                Issuer = _configuration["Jwt:Issuer"] ?? "WaterMeterManagement",
                Audience = _configuration["Jwt:Audience"] ?? "WaterMeterManagement"
            };

            var token = tokenHandler.CreateToken(tokenDescriptor);
            return tokenHandler.WriteToken(token);
        }

        public UserDto? ValidateJwtToken(string token)
        {
            try
            {
                var tokenHandler = new JwtSecurityTokenHandler();
                var key = Encoding.ASCII.GetBytes(_configuration["Jwt:SecretKey"] ?? "DefaultSecretKey123456789");

                tokenHandler.ValidateToken(token, new TokenValidationParameters
                {
                    ValidateIssuerSigningKey = true,
                    IssuerSigningKey = new SymmetricSecurityKey(key),
                    ValidateIssuer = true,
                    ValidIssuer = _configuration["Jwt:Issuer"] ?? "WaterMeterManagement",
                    ValidateAudience = true,
                    ValidAudience = _configuration["Jwt:Audience"] ?? "WaterMeterManagement",
                    ValidateLifetime = true,
                    ClockSkew = TimeSpan.Zero
                }, out SecurityToken validatedToken);

                var jwtToken = (JwtSecurityToken)validatedToken;
                
                return new UserDto
                {
                    Id = int.Parse(jwtToken.Claims.First(x => x.Type == ClaimTypes.NameIdentifier).Value),
                    Username = jwtToken.Claims.First(x => x.Type == ClaimTypes.Name).Value,
                    FullName = jwtToken.Claims.First(x => x.Type == "FullName").Value,
                    PersonId = int.Parse(jwtToken.Claims.First(x => x.Type == "PersonId").Value),
                    FinCoCode = jwtToken.Claims.First(x => x.Type == "FinCoCode").Value,
                    IsAuthenticated = true
                };
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "JWT token validation failed");
                return null;
            }
        }
    }
} 