using Microsoft.EntityFrameworkCore;
using WaterMeterManagement.Data;
using WaterMeterManagement.DTOs;
using WaterMeterManagement.DTOs.Mobile;
using WaterMeterManagement.Models;
using WaterMeterManagement.Services.Interfaces;

namespace WaterMeterManagement.Services
{
    public class TaskService : ITaskService
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<TaskService> _logger;

        public TaskService(ApplicationDbContext context, ILogger<TaskService> logger)
        {
            _context = context;
            _logger = logger;
        }

        public async System.Threading.Tasks.Task<TaskSearchResultDto> GetTasksAsync(TaskSearchDto searchDto)
        {
            try
            {
                var query = _context.Tasks
                    .Where(t => !t.IsDeleted)
                    .Include(t => t.Route)
                    .Include(t => t.WorkPackage)
                    .Include(t => t.WorkPackageItem)
                    .Include(t => t.Meter)
                    .Include(t => t.AssignedUser)
                    .AsQueryable();

                // Apply filters
                if (!string.IsNullOrEmpty(searchDto.Name))
                {
                    query = query.Where(t => t.Name.Contains(searchDto.Name));
                }

                if (!string.IsNullOrEmpty(searchDto.Status))
                {
                    query = query.Where(t => t.Status == searchDto.Status);
                }

                if (!string.IsNullOrEmpty(searchDto.Priority))
                {
                    query = query.Where(t => t.Priority == searchDto.Priority);
                }

                if (!string.IsNullOrEmpty(searchDto.Type))
                {
                    query = query.Where(t => t.Type == searchDto.Type);
                }

                if (searchDto.AssignedUserId.HasValue)
                {
                    query = query.Where(t => t.AssignedUserId == searchDto.AssignedUserId.Value);
                }
                else if (searchDto.AssignedUserId == 0)
                {
                    // Zero specifically means get unassigned tasks
                    query = query.Where(t => t.AssignedUserId == null || t.AssignedUserId == 0);
                }

                if (searchDto.RouteId.HasValue)
                {
                    query = query.Where(t => t.RouteId == searchDto.RouteId);
                }

                if (!string.IsNullOrEmpty(searchDto.WorkPackageName))
                {
                    query = query.Where(t => t.WorkPackage != null && t.WorkPackage.Name == searchDto.WorkPackageName);
                }

                if (searchDto.DueDateFrom.HasValue)
                {
                    query = query.Where(t => t.DueDate >= searchDto.DueDateFrom);
                }

                if (searchDto.DueDateTo.HasValue)
                {
                    query = query.Where(t => t.DueDate <= searchDto.DueDateTo);
                }

                if (searchDto.IsOverdue.HasValue && searchDto.IsOverdue.Value)
                {
                    query = query.Where(t => t.DueDate < DateTime.UtcNow && t.Status != "Completed");
                }

                // Apply sorting
                if (!string.IsNullOrEmpty(searchDto.SortBy))
                {
                    var isDescending = searchDto.SortDirection?.ToLower() == "desc";
                    
                    query = searchDto.SortBy.ToLower() switch
                    {
                        "name" => isDescending ? query.OrderByDescending(t => t.Name) : query.OrderBy(t => t.Name),
                        "status" => isDescending ? query.OrderByDescending(t => t.Status) : query.OrderBy(t => t.Status),
                        "priority" => isDescending ? query.OrderByDescending(t => t.Priority) : query.OrderBy(t => t.Priority),
                        "duedate" => isDescending ? query.OrderByDescending(t => t.DueDate) : query.OrderBy(t => t.DueDate),
                        "createdat" => isDescending ? query.OrderByDescending(t => t.CreatedAt) : query.OrderBy(t => t.CreatedAt),
                        _ => query.OrderByDescending(t => t.CreatedAt)
                    };
                }
                else
                {
                    query = query.OrderByDescending(t => t.CreatedAt);
                }

                var totalCount = await query.CountAsync();
                var totalPages = (int)Math.Ceiling((double)totalCount / searchDto.PageSize);

                var tasks = await query
                    .Skip((searchDto.Page - 1) * searchDto.PageSize)
                    .Take(searchDto.PageSize)
                    .Select(t => new TaskListDto
                    {
                        Id = t.Id,
                        Name = t.Name,
                        Description = t.Description,
                        Status = t.Status,
                        Priority = t.Priority,
                        Type = t.Type,
                        AssignedUserId = t.AssignedUserId,
                        AssignedUserName = t.AssignedUser != null ? t.AssignedUser.FullName : null,
                        CreatedBy = t.CreatedBy,
                        RouteId = t.RouteId,
                        DueDate = t.DueDate,
                        StartDate = t.StartDate,
                        CompletedDate = t.CompletedDate,
                        EstimatedHours = t.EstimatedHours,
                        ActualHours = t.ActualHours,
                        ProgressPercentage = t.ProgressPercentage,
                        Location = t.Location,
                        Instructions = t.Instructions,
                        Notes = t.Notes,
                        CreatedAt = t.CreatedAt,
                        UpdatedAt = t.UpdatedAt,
                        // GPS coordinates
                        Latitude = t.Latitude,
                        Longitude = t.Longitude,
                        // Meter GPS coordinates (from associated water meter)
                        MeterLatitude = t.Meter != null ? t.Meter.Latitude : null,
                        MeterLongitude = t.Meter != null ? t.Meter.Longitude : null,
                        RouteName = t.Route != null ? t.Route.Name : null,
                        WorkPackageName = t.WorkPackage != null ? t.WorkPackage.Name : null,
                        MeterSerialNumber = t.Meter != null ? t.Meter.SerialNumber : null,
                        // Water meter details
                        MeterAssetId = t.Meter != null ? t.Meter.AssetId : null,
                        MeterAccountNumber = t.Meter != null ? t.Meter.AccountNumber : null,
                        MeterType = t.Meter != null ? t.Meter.MeterType : null
                    })
                    .ToListAsync();

                return new TaskSearchResultDto
                {
                    Tasks = tasks,
                    TotalCount = totalCount,
                    PageNumber = searchDto.Page,
                    PageSize = searchDto.PageSize,
                    TotalPages = totalPages
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting tasks");
                throw;
            }
        }

        public async System.Threading.Tasks.Task<TaskListDto?> GetTaskByIdAsync(int id)
        {
            try
            {
                var task = await _context.Tasks
                    .Include(t => t.Route)
                    .Include(t => t.WorkPackage)
                    .Include(t => t.WorkPackageItem)
                    .Include(t => t.Meter)
                    .Include(t => t.AssignedUser)
                    .Where(t => t.Id == id && !t.IsDeleted)
                    .Select(t => new TaskListDto
                    {
                        Id = t.Id,
                        Name = t.Name,
                        Description = t.Description,
                        Status = t.Status,
                        Priority = t.Priority,
                        Type = t.Type,
                        AssignedUserId = t.AssignedUserId,
                        AssignedUserName = t.AssignedUser != null ? t.AssignedUser.FullName : null,
                        CreatedBy = t.CreatedBy,
                        RouteId = t.RouteId,
                        DueDate = t.DueDate,
                        StartDate = t.StartDate,
                        CompletedDate = t.CompletedDate,
                        EstimatedHours = t.EstimatedHours,
                        ActualHours = t.ActualHours,
                        ProgressPercentage = t.ProgressPercentage,
                        Location = t.Location,
                        Instructions = t.Instructions,
                        Notes = t.Notes,
                        CreatedAt = t.CreatedAt,
                        UpdatedAt = t.UpdatedAt,
                        // GPS coordinates
                        Latitude = t.Latitude,
                        Longitude = t.Longitude,
                        // Meter GPS coordinates (from associated water meter)
                        MeterLatitude = t.Meter != null ? t.Meter.Latitude : null,
                        MeterLongitude = t.Meter != null ? t.Meter.Longitude : null,
                        RouteName = t.Route != null ? t.Route.Name : null,
                        WorkPackageName = t.WorkPackage != null ? t.WorkPackage.Name : null,
                        MeterSerialNumber = t.Meter != null ? t.Meter.SerialNumber : null,
                        // Water meter details
                        MeterAssetId = t.Meter != null ? t.Meter.AssetId : null,
                        MeterAccountNumber = t.Meter != null ? t.Meter.AccountNumber : null,
                        MeterType = t.Meter != null ? t.Meter.MeterType : null
                    })
                    .FirstOrDefaultAsync();

                return task;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting task by id: {Id}", id);
                throw;
            }
        }

        public async System.Threading.Tasks.Task<TaskListDto> CreateTaskAsync(CreateTaskDto createDto, string createdBy)
        {
            try
            {
                var task = new Models.WorkTask
                {
                    Name = createDto.Name,
                    Description = createDto.Description,
                    Status = createDto.Status,
                    Priority = createDto.Priority,
                    Type = createDto.Type,
                    AssignedUserId = createDto.AssignedUserId,
                    CreatedBy = createdBy,
                    RouteId = createDto.RouteId,
                    DueDate = createDto.DueDate,
                    StartDate = createDto.StartDate,
                    EstimatedHours = createDto.EstimatedHours,
                    Location = createDto.Location,
                    Instructions = createDto.Instructions,
                    Notes = createDto.Notes,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                };

                _context.Tasks.Add(task);
                await _context.SaveChangesAsync();

                return await GetTaskByIdAsync(task.Id) ?? throw new InvalidOperationException("Failed to retrieve created task");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating task");
                throw;
            }
        }

        public async System.Threading.Tasks.Task<TaskListDto> UpdateTaskAsync(int id, UpdateTaskDto updateDto, string updatedBy)
        {
            try
            {
                var task = await _context.Tasks.FindAsync(id);
                if (task == null || task.IsDeleted)
                    throw new ArgumentException("Task not found");

                // Update properties
                task.Name = updateDto.Name;
                task.Description = updateDto.Description;
                task.Status = updateDto.Status;
                task.Priority = updateDto.Priority;
                task.Type = updateDto.Type;
                task.AssignedUserId = updateDto.AssignedUserId;
                task.RouteId = updateDto.RouteId;
                task.DueDate = updateDto.DueDate;
                task.StartDate = updateDto.StartDate;
                task.CompletedDate = updateDto.CompletedDate;
                task.EstimatedHours = updateDto.EstimatedHours;
                task.ActualHours = updateDto.ActualHours;
                task.ProgressPercentage = updateDto.ProgressPercentage;
                task.Location = updateDto.Location;
                task.Instructions = updateDto.Instructions;
                task.Notes = updateDto.Notes;
                task.UpdatedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                return await GetTaskByIdAsync(task.Id) ?? throw new InvalidOperationException("Failed to retrieve updated task");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating task");
                throw;
            }
        }

        public async System.Threading.Tasks.Task<bool> DeleteTaskAsync(int id, string deletedBy)
        {
            try
            {
                var task = await _context.Tasks.FindAsync(id);
                if (task == null || task.IsDeleted)
                    return false;

                task.IsDeleted = true;
                task.UpdatedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting task");
                throw;
            }
        }

        public async System.Threading.Tasks.Task<bool> UpdateTaskStatusAsync(int taskId, string status, string updatedBy)
        {
            try
            {
                var task = await _context.Tasks.FindAsync(taskId);
                if (task == null || task.IsDeleted)
                    return false;

                task.Status = status;
                task.UpdatedAt = DateTime.UtcNow;

                if (status == "Completed" && !task.CompletedDate.HasValue)
                {
                    task.CompletedDate = DateTime.UtcNow;
                    task.ProgressPercentage = 100;
                }

                await _context.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating task status");
                throw;
            }
        }

        public async System.Threading.Tasks.Task<TaskStatisticsDto> GetTaskStatisticsAsync()
        {
            try
            {
                var tasks = await _context.Tasks.Where(t => !t.IsDeleted).ToListAsync();

                var totalTasks = tasks.Count;
                var completedTasks = tasks.Count(t => t.Status == "Completed");
                var inProgressTasks = tasks.Count(t => t.Status == "InProgress");
                var pendingTasks = tasks.Count(t => t.Status == "Pending");
                var overdueTasks = tasks.Count(t => t.DueDate < DateTime.UtcNow && t.Status != "Completed");
                var highPriorityTasks = tasks.Count(t => t.Priority == "High");
                var mediumPriorityTasks = tasks.Count(t => t.Priority == "Medium");
                var lowPriorityTasks = tasks.Count(t => t.Priority == "Low");

                return new TaskStatisticsDto
                {
                    TotalTasks = totalTasks,
                    CompletedTasks = completedTasks,
                    InProgressTasks = inProgressTasks,
                    PendingTasks = pendingTasks,
                    OverdueTasks = overdueTasks,
                    TaskCompletionRate = totalTasks > 0 ? (decimal)completedTasks / totalTasks * 100 : 0,
                    StatusCounts = new List<TaskStatusCountDto>
                    {
                        new() { Status = "Pending", Count = pendingTasks, Percentage = totalTasks > 0 ? (decimal)pendingTasks / totalTasks * 100 : 0 },
                        new() { Status = "InProgress", Count = inProgressTasks, Percentage = totalTasks > 0 ? (decimal)inProgressTasks / totalTasks * 100 : 0 },
                        new() { Status = "Completed", Count = completedTasks, Percentage = totalTasks > 0 ? (decimal)completedTasks / totalTasks * 100 : 0 }
                    },
                    PriorityCounts = new List<TaskPriorityCountDto>
                    {
                        new() { Priority = "High", Count = highPriorityTasks, Percentage = totalTasks > 0 ? (decimal)highPriorityTasks / totalTasks * 100 : 0 },
                        new() { Priority = "Medium", Count = mediumPriorityTasks, Percentage = totalTasks > 0 ? (decimal)mediumPriorityTasks / totalTasks * 100 : 0 },
                        new() { Priority = "Low", Count = lowPriorityTasks, Percentage = totalTasks > 0 ? (decimal)lowPriorityTasks / totalTasks * 100 : 0 }
                    }
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting task statistics");
                throw;
            }
        }

        // Additional methods for compatibility
        public async System.Threading.Tasks.Task<bool> AssignTaskAsync(int taskId, int userId, string assignedBy)
        {
            try
            {
                var task = await _context.Tasks.FindAsync(taskId);
                if (task == null || task.IsDeleted)
                    return false;

                task.AssignedUserId = userId;
                task.UpdatedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error assigning task");
                throw;
            }
        }

        public async System.Threading.Tasks.Task<bool> BulkAssignTasksAsync(BulkAssignmentDto bulkAssignmentDto, string assignedBy)
        {
            try
            {
                var tasks = await _context.Tasks
                    .Where(t => bulkAssignmentDto.TaskIds.Contains(t.Id) && !t.IsDeleted)
                    .ToListAsync();

                foreach (var task in tasks)
                {
                    task.AssignedUserId = bulkAssignmentDto.UserId;
                    task.UpdatedAt = DateTime.UtcNow;
                }

                await _context.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error bulk assigning tasks");
                throw;
            }
        }

        public async System.Threading.Tasks.Task<List<OverdueTaskDto>> GetOverdueTasksAsync()
        {
            try
            {
                var overdueTasks = await _context.Tasks
                    .Where(t => !t.IsDeleted && t.DueDate < DateTime.UtcNow && t.Status != "Completed")
                    .Select(t => new OverdueTaskDto
                    {
                        Id = t.Id,
                        Name = t.Name,
                        Priority = t.Priority,
                        AssignedUserId = t.AssignedUserId,
                        DueDate = t.DueDate ?? DateTime.MinValue,
                        DaysOverdue = (int)(DateTime.UtcNow - (t.DueDate ?? DateTime.UtcNow)).TotalDays,
                        Location = t.Location,
                        CreatedAt = t.CreatedAt
                    })
                    .ToListAsync();

                return overdueTasks;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting overdue tasks");
                throw;
            }
        }

        public async System.Threading.Tasks.Task<List<TaskListDto>> ReactiveAssignmentAsync(ReactiveAssignmentDto reactiveDto, string assignedBy)
        {
            try
            {
                var tasks = new List<Models.WorkTask>();

                // Create reactive tasks based on the criteria
                foreach (var meterId in reactiveDto.MeterIds)
                {
                    var meter = await _context.WaterMeters.FindAsync(meterId);
                    if (meter != null)
                    {
                        var task = new Models.WorkTask
                        {
                            Name = $"Reactive Reading - {meter.SerialNumber}",
                            Description = reactiveDto.Description ?? "Reactive meter reading task",
                            Status = "Pending",
                            Priority = reactiveDto.Priority ?? "Medium",
                            Type = "Reading",
                            AssignedUserId = reactiveDto.AssignedUserId,
                            CreatedBy = assignedBy,
                            MeterId = meterId,
                            DueDate = reactiveDto.DueDate ?? DateTime.UtcNow.AddDays(1),
                            Location = meter.Address,
                            Instructions = reactiveDto.Instructions,
                            CreatedAt = DateTime.UtcNow,
                            UpdatedAt = DateTime.UtcNow
                        };

                        _context.Tasks.Add(task);
                        tasks.Add(task);
                    }
                }

                await _context.SaveChangesAsync();

                // Return the created tasks
                var result = new List<TaskListDto>();
                foreach (var task in tasks)
                {
                    var taskDto = await GetTaskByIdAsync(task.Id);
                    if (taskDto != null)
                    {
                        result.Add(taskDto);
                    }
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating reactive assignments");
                throw;
            }
        }

        public async System.Threading.Tasks.Task<bool> UpdateTaskProgressAsync(int taskId, int progressPercentage, string updatedBy)
        {
            try
            {
                var task = await _context.Tasks.FindAsync(taskId);
                if (task == null || task.IsDeleted)
                    return false;

                task.ProgressPercentage = Math.Max(0, Math.Min(100, progressPercentage));
                task.UpdatedAt = DateTime.UtcNow;

                // Auto-complete if progress is 100%
                if (progressPercentage >= 100 && task.Status != "Completed")
                {
                    task.Status = "Completed";
                    task.CompletedDate = DateTime.UtcNow;
                }

                await _context.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating task progress");
                throw;
            }
        }

        public async System.Threading.Tasks.Task<List<TaskHistory>> GetTaskHistoryAsync(int taskId)
        {
            try
            {
                // For now, return an empty list as TaskHistory model may not be implemented
                // This can be expanded later with proper audit trail functionality
                return new List<TaskHistory>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting task history");
                throw;
            }
        }

        public async System.Threading.Tasks.Task<byte[]> ExportTasksAsync(TaskSearchDto? searchDto = null)
        {
            try
            {
                var searchCriteria = searchDto ?? new TaskSearchDto { Page = 1, PageSize = int.MaxValue };
                var tasksResult = await GetTasksAsync(searchCriteria);

                // Simple CSV export implementation
                var csv = new System.Text.StringBuilder();
                csv.AppendLine("Id,Name,Description,Status,Priority,Type,AssignedTo,DueDate,Location,CreatedAt");

                foreach (var task in tasksResult.Tasks)
                {
                    csv.AppendLine($"{task.Id},\"{task.Name}\",\"{task.Description}\",{task.Status},{task.Priority},{task.Type},\"{task.AssignedUserId}\",{task.DueDate:yyyy-MM-dd},\"{task.Location}\",{task.CreatedAt:yyyy-MM-dd HH:mm}");
                }

                return System.Text.Encoding.UTF8.GetBytes(csv.ToString());
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error exporting tasks");
                throw;
            }
        }

        public async System.Threading.Tasks.Task<(bool Success, string Message, int ImportedCount)> ImportTasksAsync(Stream fileStream, string importedBy)
        {
            try
            {
                // Basic CSV import implementation
                using var reader = new StreamReader(fileStream);
                var content = await reader.ReadToEndAsync();
                var lines = content.Split('\n', StringSplitOptions.RemoveEmptyEntries);

                if (lines.Length <= 1)
                {
                    return (false, "No data found in file", 0);
                }

                var importedCount = 0;
                for (int i = 1; i < lines.Length; i++) // Skip header
                {
                    var fields = lines[i].Split(',');
                    if (fields.Length >= 6)
                    {
                        var task = new Models.WorkTask
                        {
                            Name = fields[0].Trim('"'),
                            Description = fields[1].Trim('"'),
                            Status = fields[2],
                            Priority = fields[3],
                            Type = fields[4],
                            AssignedUserId = int.TryParse(fields[5].Trim('"'), out var userId) ? userId : null,
                            CreatedBy = importedBy,
                            CreatedAt = DateTime.UtcNow,
                            UpdatedAt = DateTime.UtcNow
                        };

                        _context.Tasks.Add(task);
                        importedCount++;
                    }
                }

                await _context.SaveChangesAsync();
                return (true, $"Successfully imported {importedCount} tasks", importedCount);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error importing tasks");
                return (false, $"Import failed: {ex.Message}", 0);
            }
        }

        public async System.Threading.Tasks.Task<List<TaskPerformanceMetricsDto>> GetTaskPerformanceMetricsAsync()
        {
            try
            {
                var tasks = await _context.Tasks
                    .Where(t => !t.IsDeleted && t.Status == "Completed")
                    .ToListAsync();

                var metrics = new List<TaskPerformanceMetricsDto>();

                // Group by month for performance metrics
                var monthlyGroups = tasks
                    .GroupBy(t => new { t.CreatedAt.Year, t.CreatedAt.Month })
                    .OrderBy(g => g.Key.Year)
                    .ThenBy(g => g.Key.Month);

                foreach (var group in monthlyGroups)
                {
                    var groupTasks = group.ToList();
                    var avgCompletionTime = groupTasks
                        .Where(t => t.CompletedDate.HasValue)
                        .Average(t => (t.CompletedDate!.Value - t.CreatedAt).TotalHours);

                    metrics.Add(new TaskPerformanceMetricsDto
                    {
                        Period = $"{group.Key.Year}-{group.Key.Month:D2}",
                        CompletedTasks = groupTasks.Count,
                        AverageCompletionTimeHours = avgCompletionTime,
                        OnTimeCompletionRate = groupTasks.Count(t => t.CompletedDate <= t.DueDate) / (decimal)groupTasks.Count * 100
                    });
                }

                return metrics;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting task performance metrics");
                throw;
            }
        }

        #region Enhanced Task Assignment Methods

        public async System.Threading.Tasks.Task<AssignmentResultDto> CreateTaskAssignmentAsync(CreateTaskAssignmentDto assignmentDto, string assignedBy)
        {
            try
            {
                var result = new AssignmentResultDto();

                // Validate task exists and is assignable
                var task = await _context.Tasks.FindAsync(assignmentDto.TaskId);
                if (task == null || task.IsDeleted)
                {
                    result.Success = false;
                    result.Message = "Task not found";
                    result.Errors.Add("Task not found or has been deleted");
                    return result;
                }

                // Validate user exists
                var user = await _context.Users.FindAsync(assignmentDto.UserId);
                if (user == null)
                {
                    result.Success = false;
                    result.Message = "User not found";
                    result.Errors.Add("User not found");
                    return result;
                }

                // Check if task is already assigned to this user
                var existingAssignment = await _context.TaskAssignments
                    .Where(ta => ta.TaskId == assignmentDto.TaskId && ta.UserId == assignmentDto.UserId && !ta.IsDeleted)
                    .FirstOrDefaultAsync();

                if (existingAssignment != null)
                {
                    result.Success = false;
                    result.Message = "Task is already assigned to this user";
                    result.Errors.Add("Duplicate assignment");
                    return result;
                }

                // Validate assignment
                var validation = await ValidateTaskAssignmentAsync(assignmentDto.TaskId, assignmentDto.UserId);
                if (!validation.CanAssign)
                {
                    result.Success = false;
                    result.Message = validation.Reason ?? "Assignment validation failed";
                    result.Errors.Add(validation.Reason ?? "Assignment validation failed");
                    result.Warnings.AddRange(validation.Warnings);
                    return result;
                }

                // Create assignment
                var assignment = new TaskAssignment
                {
                    TaskId = assignmentDto.TaskId,
                    UserId = assignmentDto.UserId,
                    AssignedDate = DateTime.UtcNow,
                    AssignedBy = assignedBy,
                    AssignmentType = assignmentDto.AssignmentType,
                    Status = "Assigned",
                    Reason = assignmentDto.Reason,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                };

                _context.TaskAssignments.Add(assignment);

                // Update task's AssignedUserId field
                task.AssignedUserId = assignmentDto.UserId;
                task.UpdatedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                // Return success result
                var createdAssignment = await GetTaskAssignmentByIdAsync(assignment.Id);
                result.Success = true;
                result.Message = $"Task assigned to {user.FullName} successfully";
                result.CreatedAssignments.Add(createdAssignment);
                result.TotalAssigned = 1;
                result.Warnings.AddRange(validation.Warnings);

                _logger.LogInformation("Task {TaskId} assigned to user {UserId} by {AssignedBy}", 
                    assignmentDto.TaskId, assignmentDto.UserId, assignedBy);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating task assignment");
                return new AssignmentResultDto
                {
                    Success = false,
                    Message = "System error during assignment",
                    Errors = { ex.Message }
                };
            }
        }

        public async System.Threading.Tasks.Task<List<TaskAssignmentDto>> GetTaskAssignmentsAsync(int? taskId = null, int? userId = null)
        {
            try
            {
                var query = _context.TaskAssignments
                    .Include(ta => ta.Task)
                    .Include(ta => ta.User)
                    .Where(ta => !ta.IsDeleted)
                    .AsQueryable();

                if (taskId.HasValue)
                {
                    query = query.Where(ta => ta.TaskId == taskId.Value);
                }

                if (userId.HasValue)
                {
                    query = query.Where(ta => ta.UserId == userId.Value);
                }

                var assignments = await query
                    .OrderByDescending(ta => ta.AssignedDate)
                    .Select(ta => new TaskAssignmentDto
                    {
                        Id = ta.Id,
                        TaskId = ta.TaskId,
                        UserId = ta.UserId,
                        AssignedDate = ta.AssignedDate,
                        AssignedBy = ta.AssignedBy,
                        AssignmentType = ta.AssignmentType,
                        Status = ta.Status,
                        AcceptedDate = ta.AcceptedDate,
                        RejectedDate = ta.RejectedDate,
                        Reason = ta.Reason,
                        TaskName = ta.Task.Name,
                        UserName = ta.User.FullName,
                        UserEmail = ta.User.Email,
                        TaskStatus = ta.Task.Status,
                        TaskPriority = ta.Task.Priority
                    })
                    .ToListAsync();

                return assignments;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting task assignments");
                throw;
            }
        }

        public async System.Threading.Tasks.Task<TaskAssignmentValidationDto> ValidateTaskAssignmentAsync(int taskId, int userId)
        {
            try
            {
                var validation = new TaskAssignmentValidationDto
                {
                    CanAssign = true,
                    Warnings = new List<string>()
                };

                // Get task
                var task = await _context.Tasks.FindAsync(taskId);
                if (task == null || task.IsDeleted)
                {
                    validation.CanAssign = false;
                    validation.Reason = "Task not found";
                    return validation;
                }

                // Check task status
                if (task.Status == "Completed" || task.Status == "Cancelled")
                {
                    validation.CanAssign = false;
                    validation.Reason = $"Cannot assign task in {task.Status} status";
                    return validation;
                }

                // Get user workload
                var userWorkload = await GetUserWorkloadAsync(userId);
                if (userWorkload == null)
                {
                    validation.CanAssign = false;
                    validation.Reason = "User not found";
                    return validation;
                }

                validation.UserWorkload = userWorkload;

                // Check availability
                if (userWorkload.AvailabilityStatus == "Offline")
                {
                    validation.CanAssign = false;
                    validation.Reason = "User is offline";
                    return validation;
                }

                // Check workload capacity
                if (userWorkload.ActiveTaskCount >= userWorkload.MaxCapacity)
                {
                    validation.CanAssign = false;
                    validation.Reason = $"User has reached maximum capacity ({userWorkload.MaxCapacity} tasks)";
                    return validation;
                }

                // Add warnings for high workload
                if (userWorkload.WorkloadPercentage > 80)
                {
                    validation.Warnings.Add($"User workload is high ({userWorkload.WorkloadPercentage:F1}%)");
                }

                if (userWorkload.OverdueTaskCount > 0)
                {
                    validation.Warnings.Add($"User has {userWorkload.OverdueTaskCount} overdue tasks");
                }

                // Calculate recommended task count and priority score
                validation.RecommendedTaskCount = Math.Max(0, userWorkload.MaxCapacity - userWorkload.ActiveTaskCount);
                validation.PriorityScore = CalculatePriorityScore(task, userWorkload);

                return validation;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating task assignment");
                return new TaskAssignmentValidationDto
                {
                    CanAssign = false,
                    Reason = "System error during validation"
                };
            }
        }

        public async System.Threading.Tasks.Task<List<UserWorkloadSummaryDto>> GetUserWorkloadSummaryAsync()
        {
            try
            {
                var users = await _context.Users.ToListAsync();
                var workloadSummaries = new List<UserWorkloadSummaryDto>();

                foreach (var user in users)
                {
                    var workload = await GetUserWorkloadAsync(user.Id);
                    if (workload != null)
                    {
                        workloadSummaries.Add(workload);
                    }
                }

                return workloadSummaries.OrderByDescending(w => w.ActiveTaskCount).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting user workload summary");
                throw;
            }
        }

        public async System.Threading.Tasks.Task<UserWorkloadSummaryDto?> GetUserWorkloadAsync(int userId)
        {
            try
            {
                var user = await _context.Users.FindAsync(userId);
                if (user == null)
                    return null;

                // Get user's active tasks
                var activeTasks = await _context.Tasks
                    .Where(t => t.AssignedUserId == userId && !t.IsDeleted && 
                               (t.Status == "Pending" || t.Status == "InProgress"))
                    .ToListAsync();

                // Get user's completed tasks (last 30 days)
                var completedTasks = await _context.Tasks
                    .Where(t => t.AssignedUserId == userId && !t.IsDeleted && 
                               t.Status == "Completed" && 
                               t.CompletedDate >= DateTime.UtcNow.AddDays(-30))
                    .ToListAsync();

                // Get overdue tasks
                var overdueTasks = activeTasks
                    .Where(t => t.DueDate.HasValue && t.DueDate.Value < DateTime.UtcNow)
                    .ToList();

                // Calculate efficiency score (simplified)
                var efficiencyScore = completedTasks.Count > 0 ? 
                    (decimal)(completedTasks.Count(t => t.CompletedDate <= t.DueDate) * 100) / completedTasks.Count : 100m;

                // Mock capacity and other data (in real app, this would come from user profile)
                var maxCapacity = 10; // This should come from user settings
                var workloadPercentage = maxCapacity > 0 ? (decimal)activeTasks.Count / maxCapacity * 100 : 0;

                return new UserWorkloadSummaryDto
                {
                    UserId = user.Id,
                    UserName = user.FullName,
                    Email = user.Email,
                    Department = user.ProfitCentreCode, // Using existing field
                    Zone = user.FinCoCode, // Using existing field
                    ActiveTaskCount = activeTasks.Count,
                    CompletedTaskCount = completedTasks.Count,
                    OverdueTaskCount = overdueTasks.Count,
                    WorkloadPercentage = workloadPercentage,
                    EfficiencyScore = efficiencyScore,
                    AvailabilityStatus = DetermineAvailabilityStatus(workloadPercentage, overdueTasks.Count),
                    LastActivity = user.LastLogin,
                    Skills = GetUserSkills(user), // Mock implementation
                    MaxCapacity = maxCapacity
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting user workload for user {UserId}", userId);
                throw;
            }
        }

        public async System.Threading.Tasks.Task<bool> AcceptTaskAssignmentAsync(int assignmentId, string acceptedBy)
        {
            try
            {
                var assignment = await _context.TaskAssignments.FindAsync(assignmentId);
                if (assignment == null || assignment.IsDeleted)
                    return false;

                assignment.Status = "Accepted";
                assignment.AcceptedDate = DateTime.UtcNow;
                assignment.UpdatedAt = DateTime.UtcNow;

                // Update task status if needed
                var task = await _context.Tasks.FindAsync(assignment.TaskId);
                if (task != null && task.Status == "Pending")
                {
                    task.Status = "InProgress";
                    task.StartDate = DateTime.UtcNow;
                    task.UpdatedAt = DateTime.UtcNow;
                }

                await _context.SaveChangesAsync();

                _logger.LogInformation("Task assignment {AssignmentId} accepted by {AcceptedBy}", 
                    assignmentId, acceptedBy);

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error accepting task assignment");
                throw;
            }
        }

        public async System.Threading.Tasks.Task<bool> RejectTaskAssignmentAsync(int assignmentId, string rejectedBy, string reason)
        {
            try
            {
                var assignment = await _context.TaskAssignments.FindAsync(assignmentId);
                if (assignment == null || assignment.IsDeleted)
                    return false;

                assignment.Status = "Rejected";
                assignment.RejectedDate = DateTime.UtcNow;
                assignment.Reason = reason;
                assignment.UpdatedAt = DateTime.UtcNow;

                // Clear task assignment
                var task = await _context.Tasks.FindAsync(assignment.TaskId);
                if (task != null)
                {
                    task.AssignedUserId = null;
                    task.UpdatedAt = DateTime.UtcNow;
                }

                await _context.SaveChangesAsync();

                _logger.LogInformation("Task assignment {AssignmentId} rejected by {RejectedBy}: {Reason}", 
                    assignmentId, rejectedBy, reason);

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error rejecting task assignment");
                throw;
            }
        }

        public async System.Threading.Tasks.Task<AssignmentResultDto> BulkAssignTasksEnhancedAsync(List<int> taskIds, int userId, string assignmentType, string assignedBy, string? reason = null)
        {
            try
            {
                var result = new AssignmentResultDto
                {
                    CreatedAssignments = new List<TaskAssignmentDto>(),
                    Warnings = new List<string>(),
                    Errors = new List<string>()
                };

                // Validate user exists
                var user = await _context.Users.FindAsync(userId);
                if (user == null)
                {
                    result.Success = false;
                    result.Message = "User not found";
                    result.Errors.Add("User not found");
                    return result;
                }

                // Get user workload
                var userWorkload = await GetUserWorkloadAsync(userId);
                if (userWorkload == null)
                {
                    result.Success = false;
                    result.Message = "Unable to get user workload information";
                    result.Errors.Add("User workload calculation failed");
                    return result;
                }

                // Check if user can handle all tasks
                var totalNewTasks = userWorkload.ActiveTaskCount + taskIds.Count;
                if (totalNewTasks > userWorkload.MaxCapacity)
                {
                    result.Warnings.Add($"Assigning {taskIds.Count} tasks will exceed user capacity. " +
                        $"Current: {userWorkload.ActiveTaskCount}, New: {taskIds.Count}, Max: {userWorkload.MaxCapacity}");
                }

                // Process each task
                foreach (var taskId in taskIds)
                {
                    try
                    {
                        var assignmentDto = new CreateTaskAssignmentDto
                        {
                            TaskId = taskId,
                            UserId = userId,
                            AssignmentType = assignmentType,
                            Reason = reason
                        };

                        var singleResult = await CreateTaskAssignmentAsync(assignmentDto, assignedBy);
                        if (singleResult.Success)
                        {
                            result.CreatedAssignments.AddRange(singleResult.CreatedAssignments);
                            result.TotalAssigned++;
                        }
                        else
                        {
                            result.Errors.Add($"Task {taskId}: {singleResult.Message}");
                            result.TotalFailed++;
                        }

                        result.Warnings.AddRange(singleResult.Warnings);
                    }
                    catch (Exception ex)
                    {
                        result.Errors.Add($"Task {taskId}: {ex.Message}");
                        result.TotalFailed++;
                    }
                }

                // Set overall result
                result.Success = result.TotalAssigned > 0;
                result.Message = $"Assigned {result.TotalAssigned} tasks successfully. {result.TotalFailed} failed.";

                _logger.LogInformation("Bulk assignment completed: {TotalAssigned} assigned, {TotalFailed} failed by {AssignedBy}", 
                    result.TotalAssigned, result.TotalFailed, assignedBy);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in bulk task assignment");
                return new AssignmentResultDto
                {
                    Success = false,
                    Message = "System error during bulk assignment",
                    Errors = { ex.Message }
                };
            }
        }

        #endregion

        #region Helper Methods

        private async System.Threading.Tasks.Task<TaskAssignmentDto> GetTaskAssignmentByIdAsync(int assignmentId)
        {
            var assignment = await _context.TaskAssignments
                .Include(ta => ta.Task)
                .Include(ta => ta.User)
                .Where(ta => ta.Id == assignmentId)
                .Select(ta => new TaskAssignmentDto
                {
                    Id = ta.Id,
                    TaskId = ta.TaskId,
                    UserId = ta.UserId,
                    AssignedDate = ta.AssignedDate,
                    AssignedBy = ta.AssignedBy,
                    AssignmentType = ta.AssignmentType,
                    Status = ta.Status,
                    AcceptedDate = ta.AcceptedDate,
                    RejectedDate = ta.RejectedDate,
                    Reason = ta.Reason,
                    TaskName = ta.Task.Name,
                    UserName = ta.User.FullName,
                    UserEmail = ta.User.Email,
                    TaskStatus = ta.Task.Status,
                    TaskPriority = ta.Task.Priority
                })
                .FirstAsync();

            return assignment;
        }

        private decimal CalculatePriorityScore(Models.WorkTask task, UserWorkloadSummaryDto userWorkload)
        {
            // Simplified priority scoring algorithm
            decimal score = 0;

            // Task priority weight
            score += task.Priority switch
            {
                "Critical" => 100,
                "High" => 75,
                "Medium" => 50,
                "Low" => 25,
                _ => 0
            };

            // User workload factor (lower workload = higher score)
            score += (100 - userWorkload.WorkloadPercentage) * 0.5m;

            // Efficiency factor
            score += userWorkload.EfficiencyScore * 0.3m;

            return Math.Max(0, Math.Min(100, score));
        }

        private string DetermineAvailabilityStatus(decimal workloadPercentage, int overdueCount)
        {
            if (workloadPercentage >= 90 || overdueCount > 3)
                return "Busy";
            
            if (workloadPercentage >= 70 || overdueCount > 1)
                return "Available"; // Still available but getting busy
            
            return "Available";
        }

        private List<string> GetUserSkills(User user)
        {
            // For now, return default skills based on role
            // This should be implemented based on your user skills system
            return new List<string> { "Water Meter Reading", "Field Operations" };
        }

        #endregion

        #region Mobile Methods Implementation

        public async Task<List<MobileTaskDto>> GetUserMobileTasksAsync(int userId, string? status = null, bool includeCompleted = false, int page = 1, int pageSize = 50)
        {
            try
            {
                var query = _context.Tasks
                    .Where(t => !t.IsDeleted && t.AssignedUserId == userId)
                    .Include(t => t.Meter)
                    .Include(t => t.Route)
                    .Include(t => t.WorkPackage)
                    .AsQueryable();

                if (!string.IsNullOrEmpty(status))
                {
                    query = query.Where(t => t.Status == status);
                }

                if (!includeCompleted)
                {
                    query = query.Where(t => t.Status != "Completed");
                }

                // First get the raw data from database
                var tasksData = await query
                    .OrderBy(t => t.DueDate)
                    .Skip((page - 1) * pageSize)
                    .Take(pageSize)
                    .ToListAsync();

                // Then map to DTOs in memory
                var tasks = tasksData.Select(t => new MobileTaskDto
                {
                    Id = t.Id,
                    Name = t.Name,
                    Description = t.Description,
                    Status = t.Status,
                    Priority = t.Priority,
                    DueDate = t.DueDate,
                    CreatedAt = t.CreatedAt,
                    UpdatedAt = t.UpdatedAt,
                    
                    // Basic properties with safe conversions
                    AssignedUserId = t.AssignedUserId, // 直接使用数据库字段
                    AssignedDate = t.CreatedAt,
                    AssignedBy = t.CreatedBy,
                    AssignmentType = "Manual",
                    EstimatedHours = t.EstimatedHours.HasValue ? (int?)Math.Round(t.EstimatedHours.Value) : null,
                    ProgressPercentage = t.ProgressPercentage,
                    
                    // Complex objects with null checks
                    TaskLocation = t.Latitude.HasValue && t.Longitude.HasValue ? new TaskLocationDto
                    {
                        Address = t.Location ?? "Unknown Location",
                        Latitude = t.Latitude.HasValue ? (double?)Convert.ToDouble(t.Latitude.Value) : null,
                        Longitude = t.Longitude.HasValue ? (double?)Convert.ToDouble(t.Longitude.Value) : null,
                        Instructions = t.Instructions ?? ""
                    } : null,
                    
                    MeterInfo = t.Meter != null ? new MeterInfoDto
                    {
                        Id = t.Meter.Id,
                        MeterId = t.Meter.Id,
                        MeterNumber = t.Meter.SerialNumber,
                        SerialNumber = t.Meter.SerialNumber,
                        MeterType = t.Meter.MeterType,
                        Brand = "Unknown", // WaterMeter doesn't have Brand
                        Model = t.Meter.Model ?? "Unknown",
                        Size = t.Meter.MeterType ?? "Unknown",
                        Type = t.Meter.MeterType ?? "Unknown",
                        LastReading = t.Meter.LastReading.HasValue ? (double?)Convert.ToDouble(t.Meter.LastReading.Value) : null,
                        LastReadingDate = t.Meter.LastReadingDate,
                        InstallDate = t.Meter.InstallDate ?? DateTime.MinValue,
                        ManufacturerName = t.Meter.Brand ?? "Unknown",
                        Status = t.Meter.Status,
                        Location = t.Meter.Location
                    } : null,
                    
                    TaskAssignment = new TaskAssignmentInfoDto
                    {
                        AssignedUserId = t.AssignedUserId,
                        AssignedUserName = null, // TODO: Join User table to get actual username
                        AssignedDate = t.CreatedAt,
                        CanReject = true,
                        CanAccept = true,
                        Notes = "",
                        AssignmentType = "Standard",
                        AssignmentPriority = t.Priority
                    },
                    
                    TaskProgress = new TaskProgressDto
                    {
                        Status = t.Status,
                        Progress = t.Status == "Completed" ? 100.0 : (t.Status == "InProgress" ? 50.0 : 0.0),
                        StartedAt = t.CreatedAt,
                        CompletedAt = t.Status == "Completed" ? t.UpdatedAt : null,
                        CurrentStep = t.Status,
                        TotalSteps = 3,
                        CompletedSteps = t.Status == "Completed" ? 3 : (t.Status == "InProgress" ? 1 : 0)
                    },
                    
                    RouteInfo = t.Route != null ? new RouteInfoDto
                    {
                        Id = t.Route.Id,
                        Name = t.Route.Name,
                        Description = t.Route.Description
                    } : null,
                    
                    WorkPackageInfo = new WorkPackageInfoDto
                    {
                        Id = t.WorkPackage?.Id ?? 0,
                        Name = t.WorkPackage?.Name ?? "Default Package",
                        Description = t.WorkPackage?.Description ?? "Task assignment"
                    },
                    
                    SpecialInstructions = !string.IsNullOrEmpty(t.Instructions) ? new List<string> { t.Instructions } : new List<string>(),
                    SafetyNotes = new List<string>(),
                    RequiredPhotos = new List<string> { "Meter Reading", "Location Photo" }
                })
                .ToList();

                return tasks;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting mobile tasks for user {UserId}", userId);
                throw;
            }
        }

        public async Task<MobileTaskDetailDto?> GetMobileTaskDetailAsync(int taskId, int userId)
        {
            try
            {
                var task = await _context.Tasks
                    .Include(t => t.Meter)
                    .Include(t => t.Route)
                    .Include(t => t.WorkPackage)
                    .Where(t => t.Id == taskId && !t.IsDeleted && t.AssignedUserId == userId)
                    .FirstOrDefaultAsync();

                if (task == null) return null;

                return new MobileTaskDetailDto
                {
                    Id = task.Id,
                    Name = task.Name,
                    Description = task.Description,
                    Status = task.Status,
                    Priority = task.Priority,
                    DueDate = task.DueDate,
                    EstimatedDuration = task.EstimatedHours.HasValue ? (int?)Math.Round(task.EstimatedHours.Value * 60) : null,
                    Location = task.Location ?? "Unknown Location",
                    MeterInfo = task.Meter != null ? new MeterInfoDto
                    {
                        Id = task.Meter.Id,
                        SerialNumber = task.Meter.SerialNumber,
                        Size = task.Meter.MeterType ?? "Unknown",
                        Type = task.Meter.MeterType ?? "Unknown",
                        LastReading = task.Meter.LastReading.HasValue ? (double?)Convert.ToDouble(task.Meter.LastReading.Value) : null,
                        LastReadingDate = task.Meter.LastReadingDate,
                        InstallDate = task.Meter.InstallDate ?? DateTime.MinValue,
                        ManufacturerName = task.Meter.Brand ?? "Unknown"
                    } : null,
                    AssignmentInfo = new TaskAssignmentInfoDto
                    {
                        AssignedDate = task.CreatedAt,
                        AssignmentType = "Manual",
                        CanReject = true,
                        CanAccept = true,
                        Notes = task.Notes ?? ""
                    },
                    ProgressInfo = new TaskProgressDto
                    {
                        Status = task.Status,
                        Progress = task.ProgressPercentage,
                        StartedAt = task.StartDate,
                        CompletedAt = task.CompletedDate,
                        TimeSpent = task.ActualHours.HasValue ? (int?)Math.Round(Convert.ToDouble(task.ActualHours.Value)) : null
                    },
                    RouteInfo = task.Route != null ? new RouteInfoDto
                    {
                        Id = task.Route.Id,
                        Name = task.Route.Name,
                        Description = task.Route.Description ?? ""
                    } : null,
                    WorkPackageInfo = task.WorkPackage != null ? new WorkPackageInfoDto
                    {
                        Id = task.WorkPackage.Id,
                        Name = task.WorkPackage.Name,
                        Description = task.WorkPackage.Description ?? ""
                    } : null,
                    SpecialInstructions = !string.IsNullOrEmpty(task.Instructions) ? new List<string> { task.Instructions } : new List<string>(),
                    SafetyNotes = new List<string> { "Follow standard safety procedures" },
                    RequiredPhotos = new List<string> { "Meter Reading", "Location Photo" },
                    RequiresCustomerContact = false
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting mobile task detail for task {TaskId}, user {UserId}", taskId, userId);
                throw;
            }
        }

        public async Task<ServiceResult> StartMobileTaskAsync(int taskId, int userId, StartTaskRequest request)
        {
            try
            {
                var task = await _context.Tasks
                    .Where(t => t.Id == taskId && !t.IsDeleted && t.AssignedUserId == userId)
                    .FirstOrDefaultAsync();

                if (task == null)
                {
                    return new ServiceResult
                    {
                        Success = false,
                        Message = "Task not found or not assigned to user"
                    };
                }

                if (task.Status != "Assigned")
                {
                    return new ServiceResult
                    {
                        Success = false,
                        Message = $"Task cannot be started. Current status: {task.Status}"
                    };
                }

                task.Status = "InProgress";
                task.StartDate = DateTime.UtcNow;
                task.UpdatedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                return new ServiceResult
                {
                    Success = true,
                    Message = "Task started successfully"
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error starting mobile task {TaskId} for user {UserId}", taskId, userId);
                return new ServiceResult
                {
                    Success = false,
                    Message = "Error starting task"
                };
            }
        }

        public async Task<ServiceResult> UpdateMobileTaskStatusAsync(int taskId, int userId, UpdateTaskStatusRequest request)
        {
            try
            {
                var task = await _context.Tasks
                    .Where(t => t.Id == taskId && !t.IsDeleted && t.AssignedUserId == userId)
                    .FirstOrDefaultAsync();

                if (task == null)
                {
                    return new ServiceResult
                    {
                        Success = false,
                        Message = "Task not found or not assigned to user"
                    };
                }

                task.Status = request.Status;
                task.UpdatedAt = DateTime.UtcNow;

                if (!string.IsNullOrEmpty(request.Notes))
                {
                    task.Notes = task.Notes + "\n" + request.Notes;
                }

                await _context.SaveChangesAsync();

                return new ServiceResult
                {
                    Success = true,
                    Message = "Task status updated successfully"
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating mobile task status for task {TaskId}, user {UserId}", taskId, userId);
                return new ServiceResult
                {
                    Success = false,
                    Message = "Error updating task status"
                };
            }
        }

        public async Task<ServiceResult<int>> CompleteMobileTaskAsync(int taskId, int userId, CompleteTaskRequest request)
        {
            try
            {
                var task = await _context.Tasks
                    .Where(t => t.Id == taskId && !t.IsDeleted && t.AssignedUserId == userId)
                    .FirstOrDefaultAsync();

                if (task == null)
                {
                    return new ServiceResult<int>
                    {
                        Success = false,
                        Message = "Task not found or not assigned to user"
                    };
                }

                task.Status = "Completed";
                task.CompletedDate = DateTime.UtcNow;
                task.ProgressPercentage = 100;
                task.UpdatedAt = DateTime.UtcNow;

                if (!string.IsNullOrEmpty(request.CompletionNotes))
                {
                    task.Notes = task.Notes + "\n" + request.CompletionNotes;
                }

                await _context.SaveChangesAsync();

                return new ServiceResult<int>
                {
                    Success = true,
                    Message = "Task completed successfully",
                    Data = taskId
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error completing mobile task {TaskId} for user {UserId}", taskId, userId);
                return new ServiceResult<int>
                {
                    Success = false,
                    Message = "Error completing task"
                };
            }
        }

        public async Task<List<MobileTaskDto>> GetNearbyMobileTasksAsync(int userId, double latitude, double longitude, double radiusKm)
        {
            try
            {
                // Simplified implementation - in real scenario, use spatial queries
                var tasks = await _context.Tasks
                    .Where(t => !t.IsDeleted && t.AssignedUserId == userId && t.Status != "Completed")
                    .Include(t => t.Meter)
                    .Take(20) // Limit for performance
                    .Select(t => new MobileTaskDto
                    {
                        Id = t.Id,
                        Name = t.Name,
                        Description = t.Description,
                        Status = t.Status,
                        Priority = t.Priority,
                        DueDate = t.DueDate,
                        EstimatedDuration = t.EstimatedHours.HasValue ? (int?)(t.EstimatedHours.Value * 60) : null,
                        Location = t.Location ?? "Unknown Location",
                        TaskLocation = new TaskLocationDto
                        {
                            Address = t.Location ?? "Unknown Location",
                            Latitude = t.Latitude.HasValue ? (double?)Convert.ToDouble(t.Latitude.Value) : null,
                            Longitude = t.Longitude.HasValue ? (double?)Convert.ToDouble(t.Longitude.Value) : null,
                            Instructions = t.Instructions ?? ""
                        },
                        MeterInfo = t.Meter != null ? new MeterInfoDto
                        {
                            Id = t.Meter.Id,
                            SerialNumber = t.Meter.SerialNumber,
                            Size = t.Meter.MeterType ?? "Unknown",
                            Type = t.Meter.MeterType ?? "Unknown", 
                            LastReading = t.Meter.LastReading.HasValue ? (double?)Convert.ToDouble(t.Meter.LastReading.Value) : null,
                            LastReadingDate = t.Meter.LastReadingDate,
                            InstallDate = t.Meter.InstallDate ?? DateTime.MinValue,
                            ManufacturerName = t.Meter.Brand ?? "Unknown"
                        } : null
                    })
                    .ToListAsync();

                return tasks;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting nearby mobile tasks for user {UserId}", userId);
                throw;
            }
        }

        public async Task<MobileTaskSummaryDto> GetMobileTaskSummaryAsync(int userId)
        {
            try
            {
                var tasks = await _context.Tasks
                    .Where(t => !t.IsDeleted && t.AssignedUserId == userId)
                    .ToListAsync();

                var today = DateTime.UtcNow.Date;
                var tomorrow = today.AddDays(1);

                return new MobileTaskSummaryDto
                {
                    TotalAssigned = tasks.Count,
                    InProgress = tasks.Count(t => t.Status == "InProgress" || t.Status == "In Progress"),
                    Completed = tasks.Count(t => t.Status == "Completed"),
                    Overdue = tasks.Count(t => t.DueDate.HasValue && t.DueDate.Value < DateTime.UtcNow && t.Status != "Completed"),
                    DueToday = tasks.Count(t => t.DueDate.HasValue && t.DueDate.Value.Date == today && t.Status != "Completed"),
                    DueTomorrow = tasks.Count(t => t.DueDate.HasValue && t.DueDate.Value.Date == tomorrow && t.Status != "Completed"),
                    CompletionRate = tasks.Count > 0 ? (decimal)((double)tasks.Count(t => t.Status == "Completed") / tasks.Count * 100) : 0,
                    ConsecutiveDaysActive = 7, // Mock data
                    UrgentTasks = tasks
                        .Where(t => t.Priority == "High" && t.Status != "Completed")
                        .Take(5)
                        .Select(t => new UrgentTaskDto
                        {
                            Id = t.Id,
                            Name = t.Name,
                            DueDate = t.DueDate ?? DateTime.UtcNow,
                            HoursOverdue = t.DueDate.HasValue && t.DueDate.Value < DateTime.UtcNow ? (int)(DateTime.UtcNow - t.DueDate.Value).TotalHours : 0,
                            Priority = t.Priority
                        })
                        .ToList()
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting mobile task summary for user {UserId}", userId);
                throw;
            }
        }

        public async Task<TaskSyncResponseDto> SyncMobileTaskStatusAsync(int userId, TaskStatusSyncRequest request)
        {
            try
            {
                var response = new TaskSyncResponseDto
                {
                    TotalUpdates = request.StatusUpdates.Count,
                    SuccessfulUpdates = 0,
                    FailedUpdates = 0,
                    Results = new List<TaskSyncResult>(),
                    GeneralErrors = new List<string>()
                };

                foreach (var update in request.StatusUpdates)
                {
                    try
                    {
                        var task = await _context.Tasks
                            .Where(t => t.Id == update.TaskId && !t.IsDeleted && t.AssignedUserId == userId)
                            .FirstOrDefaultAsync();

                        if (task != null)
                        {
                            task.Status = update.Status;
                            task.UpdatedAt = DateTime.UtcNow;

                            if (!string.IsNullOrEmpty(update.Notes))
                            {
                                task.Notes = task.Notes + "\n" + update.Notes;
                            }

                            response.Results.Add(new TaskSyncResult
                            {
                                TaskId = update.TaskId,
                                OfflineId = update.OfflineId,
                                Success = true,
                                ServerTimestamp = DateTime.UtcNow
                            });

                            response.SuccessfulUpdates++;
                        }
                        else
                        {
                            response.Results.Add(new TaskSyncResult
                            {
                                TaskId = update.TaskId,
                                OfflineId = update.OfflineId,
                                Success = false,
                                ErrorMessage = "Task not found or not assigned to user"
                            });

                            response.FailedUpdates++;
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error syncing task {TaskId} for user {UserId}", update.TaskId, userId);
                        
                        response.Results.Add(new TaskSyncResult
                        {
                            TaskId = update.TaskId,
                            OfflineId = update.OfflineId,
                            Success = false,
                            ErrorMessage = "Error updating task"
                        });

                        response.FailedUpdates++;
                    }
                }

                await _context.SaveChangesAsync();
                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error syncing mobile task status for user {UserId}", userId);
                throw;
            }
        }

        #endregion

        public async System.Threading.Tasks.Task<WorkPackageGroupedTasksDto> GetTasksGroupedByWorkPackageAsync(TaskWorkPackageSearchDto searchDto)
        {
            try
            {
                _logger.LogInformation("GetTasksGroupedByWorkPackageAsync - AssignedUserId: '{AssignedUserId}'", searchDto.AssignedUserId?.ToString() ?? "NULL");
                
                var query = _context.Tasks
                    .Where(t => !t.IsDeleted)
                    .Include(t => t.Route)
                    .Include(t => t.WorkPackage)
                    .Include(t => t.WorkPackageItem)
                    .Include(t => t.Meter)
                    .Include(t => t.AssignedUser)
                    .AsQueryable();

                // Apply assignment filters - handle the frontend request properly
                if (searchDto.AssignedUserId.HasValue && searchDto.AssignedUserId.Value > 0)
                {
                    _logger.LogInformation("Filtering for assigned tasks: {AssignedUserId}", searchDto.AssignedUserId.Value);
                    query = query.Where(t => t.AssignedUserId == searchDto.AssignedUserId.Value);
                }
                else if (searchDto.AssignedUserId == 0)
                {
                    _logger.LogInformation("Filtering for unassigned tasks only");
                    query = query.Where(t => t.AssignedUserId == null || t.AssignedUserId == 0);
                }
                else
                {
                    _logger.LogInformation("No assignment filter applied");
                }

                // Count tasks for debugging
                var tasksAfterFilter = await query.CountAsync();
                _logger.LogInformation("Tasks after assignment filter: {Count}", tasksAfterFilter);

                if (!string.IsNullOrEmpty(searchDto.Status))
                {
                    query = query.Where(t => t.Status == searchDto.Status);
                }

                if (!string.IsNullOrEmpty(searchDto.Priority))
                {
                    query = query.Where(t => t.Priority == searchDto.Priority);
                }

                if (!string.IsNullOrEmpty(searchDto.WorkPackageFilter))
                {
                    query = query.Where(t => t.WorkPackage != null && t.WorkPackage.Name == searchDto.WorkPackageFilter);
                }

                // Get all matching tasks
                var allTasks = await query
                    .OrderByDescending(t => t.CreatedAt)
                    .Select(t => new TaskListDto
                    {
                        Id = t.Id,
                        Name = t.Name,
                        Description = t.Description,
                        Status = t.Status,
                        Priority = t.Priority,
                        Type = t.Type,
                        AssignedUserId = t.AssignedUserId,
                        AssignedUserName = t.AssignedUser != null ? t.AssignedUser.FullName : null,
                        CreatedBy = t.CreatedBy,
                        RouteId = t.RouteId,
                        DueDate = t.DueDate,
                        StartDate = t.StartDate,
                        CompletedDate = t.CompletedDate,
                        EstimatedHours = t.EstimatedHours,
                        ActualHours = t.ActualHours,
                        ProgressPercentage = t.ProgressPercentage,
                        Location = t.Location,
                        Instructions = t.Instructions,
                        Notes = t.Notes,
                        CreatedAt = t.CreatedAt,
                        UpdatedAt = t.UpdatedAt,
                        // GPS coordinates
                        Latitude = t.Latitude,
                        Longitude = t.Longitude,
                        // Meter GPS coordinates (from associated water meter)
                        MeterLatitude = t.Meter != null ? t.Meter.Latitude : null,
                        MeterLongitude = t.Meter != null ? t.Meter.Longitude : null,
                        RouteName = t.Route != null ? t.Route.Name : null,
                        WorkPackageName = t.WorkPackage != null ? t.WorkPackage.Name : null,
                        MeterSerialNumber = t.Meter != null ? t.Meter.SerialNumber : null,
                        // Water meter details
                        MeterAssetId = t.Meter != null ? t.Meter.AssetId : null,
                        MeterAccountNumber = t.Meter != null ? t.Meter.AccountNumber : null,
                        MeterType = t.Meter != null ? t.Meter.MeterType : null
                    })
                    .ToListAsync();

                // Group by work package
                var groupedTasks = allTasks
                    .GroupBy(t => t.WorkPackageName ?? "No Work Package")
                    .Select(g => new WorkPackageGroupDto
                    {
                        WorkPackageName = g.Key,
                        TaskCount = g.Count(),
                        Tasks = g.ToList()
                    })
                    .OrderBy(g => g.WorkPackageName)
                    .ToList();

                // Apply pagination at work package level
                var totalWorkPackages = groupedTasks.Count;
                var totalPages = (int)Math.Ceiling((double)totalWorkPackages / searchDto.PageSize);
                var totalTasks = allTasks.Count;

                var pagedWorkPackages = groupedTasks
                    .Skip((searchDto.Page - 1) * searchDto.PageSize)
                    .Take(searchDto.PageSize)
                    .ToList();

                return new WorkPackageGroupedTasksDto
                {
                    WorkPackages = pagedWorkPackages,
                    TotalWorkPackages = totalWorkPackages,
                    TotalTasks = totalTasks,
                    PageNumber = searchDto.Page,
                    PageSize = searchDto.PageSize,
                    TotalPages = totalPages
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting tasks grouped by work package");
                throw;
            }
        }
    }
} 