using OfficeOpenXml;
using WaterMeterManagement.Models;
using WaterMeterManagement.Data;
using Microsoft.EntityFrameworkCore;
using System.Globalization;

namespace WaterMeterManagement.Services
{
    /// <summary>
    /// AMS Excel Import Service
    /// Handles processing of AMS export Excel files with multiple sheets
    /// </summary>
    public class AmsExcelImportService
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<AmsExcelImportService> _logger;
        private readonly AmsDataMappingService _mappingService;
        private readonly AmsValidationService _validationService;

        public AmsExcelImportService(
            ApplicationDbContext context,
            ILogger<AmsExcelImportService> logger,
            AmsDataMappingService mappingService,
            AmsValidationService validationService)
        {
            _context = context;
            _logger = logger;
            _mappingService = mappingService;
            _validationService = validationService;
        }

        /// <summary>
        /// Import result summary
        /// </summary>
        public class ImportResult
        {
            public bool Success { get; set; }
            public int MetersProcessed { get; set; }
            public int MetersImported { get; set; }
            public int MetersSkipped { get; set; }
            public int RoutesProcessed { get; set; }
            public int RoutesImported { get; set; }
            public List<string> Errors { get; set; } = new();
            public List<string> Warnings { get; set; } = new();
            public string Summary => $"Imported {MetersImported}/{MetersProcessed} meters, {RoutesImported}/{RoutesProcessed} routes";
        }

        /// <summary>
        /// Process AMS Excel file with multiple sheets
        /// </summary>
        public async Task<ImportResult> ImportAmsExcelFileAsync(Stream excelStream, string fileName)
        {
            var result = new ImportResult();
            
            try
            {
                _logger.LogInformation("Starting AMS Excel import for file: {FileName}", fileName);
                
                using var package = new ExcelPackage(excelStream);
                
                // Process main meter data sheet
                var masterSheet = FindMasterDataSheet(package);
                if (masterSheet != null)
                {
                    var meterResult = await ProcessMasterDataSheetAsync(masterSheet);
                    result.MetersProcessed = meterResult.MetersProcessed;
                    result.MetersImported = meterResult.MetersImported;
                    result.MetersSkipped = meterResult.MetersSkipped;
                    result.Errors.AddRange(meterResult.Errors);
                    result.Warnings.AddRange(meterResult.Warnings);
                }
                else
                {
                    result.Errors.Add("Master data sheet not found. Expected sheet containing 'MASTER' in name.");
                }

                // Process route assignment sheet
                var dataCheckSheet = FindDataCheckSheet(package);
                if (dataCheckSheet != null)
                {
                    var routeResult = await ProcessDataCheckSheetAsync(dataCheckSheet);
                    result.RoutesProcessed = routeResult.RoutesProcessed;
                    result.RoutesImported = routeResult.RoutesImported;
                    result.Errors.AddRange(routeResult.Errors);
                    result.Warnings.AddRange(routeResult.Warnings);
                }
                else
                {
                    result.Warnings.Add("Data Check sheet not found. Route assignments will not be processed.");
                }

                // Process anomaly data sheet if exists
                var anomalySheet = FindAnomalySheet(package);
                if (anomalySheet != null)
                {
                    await ProcessAnomalySheetAsync(anomalySheet);
                    result.Warnings.Add("Anomaly data processed and flagged in meter records.");
                }

                result.Success = result.Errors.Count == 0;
                
                _logger.LogInformation("AMS Excel import completed. {Summary}", result.Summary);
                
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to import AMS Excel file: {FileName}", fileName);
                result.Errors.Add($"Import failed: {ex.Message}");
                result.Success = false;
                return result;
            }
        }

        /// <summary>
        /// Find the master data sheet (contains "MASTER" in name)
        /// </summary>
        private ExcelWorksheet? FindMasterDataSheet(ExcelPackage package)
        {
            return package.Workbook.Worksheets
                .FirstOrDefault(ws => ws.Name.Contains("MASTER", StringComparison.OrdinalIgnoreCase));
        }

        /// <summary>
        /// Find the data check sheet (contains "Data Check" in name)
        /// </summary>
        private ExcelWorksheet? FindDataCheckSheet(ExcelPackage package)
        {
            return package.Workbook.Worksheets
                .FirstOrDefault(ws => ws.Name.Contains("Data Check", StringComparison.OrdinalIgnoreCase));
        }

        /// <summary>
        /// Find anomaly data sheet (contains "miss" or "anomaly" in name)
        /// </summary>
        private ExcelWorksheet? FindAnomalySheet(ExcelPackage package)
        {
            return package.Workbook.Worksheets
                .FirstOrDefault(ws => ws.Name.Contains("miss", StringComparison.OrdinalIgnoreCase) ||
                                     ws.Name.Contains("anomaly", StringComparison.OrdinalIgnoreCase));
        }

        /// <summary>
        /// Process the master data sheet containing meter information
        /// </summary>
        private async Task<ImportResult> ProcessMasterDataSheetAsync(ExcelWorksheet worksheet)
        {
            var result = new ImportResult();
            var headers = GetHeaderMapping(worksheet);
            
            if (!ValidateRequiredHeaders(headers, result))
            {
                return result;
            }

            var rowCount = worksheet.Dimension?.Rows ?? 0;
            
            for (int row = 2; row <= rowCount; row++) // Skip header row
            {
                try
                {
                    result.MetersProcessed++;
                    
                    var meterData = ExtractMeterDataFromRow(worksheet, row, headers);
                    
                    if (string.IsNullOrEmpty(meterData.AssetId))
                    {
                        result.MetersSkipped++;
                        continue; // Skip rows without Asset ID
                    }

                    var validationResult = await _validationService.ValidateMeterDataAsync(meterData);
                    if (!validationResult.IsValid)
                    {
                        result.Warnings.AddRange(validationResult.Warnings);
                        if (validationResult.HasCriticalErrors)
                        {
                            result.Errors.AddRange(validationResult.Errors);
                            result.MetersSkipped++;
                            continue;
                        }
                    }

                    // Check if meter already exists by multiple criteria
                    var existingMeter = await _context.WaterMeters
                        .Where(m => m.AssetId == meterData.AssetId || 
                                   m.SerialNumber == meterData.MeterNumber ||
                                   m.AccountNumber == meterData.AccountNumber)
                        .FirstOrDefaultAsync();

                    WaterMeter currentMeter;
                    bool isNewMeter = false;

                    if (existingMeter != null)
                    {
                        // Update existing meter
                        _mappingService.UpdateMeterFromAmsData(existingMeter, meterData);
                        existingMeter.LastSyncDate = DateTime.UtcNow;
                        existingMeter.SyncStatus = "Updated";
                        currentMeter = existingMeter;
                        _logger.LogInformation("Updated existing meter: AssetId={AssetId}, MeterNumber={MeterNumber}", 
                            meterData.AssetId, meterData.MeterNumber);
                    }
                    else
                    {
                        // Create new meter
                        var newMeter = _mappingService.CreateMeterFromAmsData(meterData);
                        newMeter.LastSyncDate = DateTime.UtcNow;
                        newMeter.SyncStatus = "Imported";
                        isNewMeter = true;
                        
                        try
                        {
                            await _context.WaterMeters.AddAsync(newMeter);
                            currentMeter = newMeter;
                            _logger.LogInformation("Created new meter: AssetId={AssetId}, MeterNumber={MeterNumber}", 
                                meterData.AssetId, meterData.MeterNumber);
                        }
                        catch (Exception addEx)
                        {
                            // If add fails due to duplicate, try to find and update the existing one
                            _logger.LogWarning(addEx, "Failed to add new meter, attempting to find and update existing one");
                            
                            // Remove the failed entity
                            _context.Entry(newMeter).State = Microsoft.EntityFrameworkCore.EntityState.Detached;
                            
                            // Try to find by any identifier and update
                            var conflictingMeter = await _context.WaterMeters
                                .Where(m => m.AssetId == meterData.AssetId || 
                                           m.SerialNumber == meterData.MeterNumber ||
                                           m.AccountNumber == meterData.AccountNumber)
                                .FirstOrDefaultAsync();
                                
                            if (conflictingMeter != null)
                            {
                                _mappingService.UpdateMeterFromAmsData(conflictingMeter, meterData);
                                conflictingMeter.LastSyncDate = DateTime.UtcNow;
                                conflictingMeter.SyncStatus = "Updated";
                                currentMeter = conflictingMeter;
                                isNewMeter = false;
                                _logger.LogInformation("Resolved conflict by updating meter: AssetId={AssetId}", meterData.AssetId);
                            }
                            else
                            {
                                throw; // Re-throw if we can't resolve
                            }
                        }
                    }

                    // Save meter changes first to get the meter ID
                    try
                    {
                        await _context.SaveChangesAsync();
                        
                        // 🎯 NEW: Create baseline record from AMS data
                        await CreateBaselineRecordForMeter(currentMeter, meterData, isNewMeter);
                        
                        result.MetersImported++;
                    }
                    catch (Exception saveEx)
                    {
                        _logger.LogError(saveEx, "Failed to save meter changes for row {Row}, rolling back", row);
                        result.MetersSkipped++;
                        result.MetersImported--; // Adjust counter
                        result.Errors.Add($"Row {row}: Failed to save meter data - {saveEx.Message}");
                        
                        // Reset the context to clean state
                        foreach (var entry in _context.ChangeTracker.Entries())
                        {
                            entry.State = Microsoft.EntityFrameworkCore.EntityState.Detached;
                        }
                        continue;
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to process meter data at row {Row}", row);
                    result.Errors.Add($"Row {row}: {ex.Message}");
                    result.MetersSkipped++;
                }
            }

            // Final save is no longer needed since we save after each meter
            // await _context.SaveChangesAsync();
            return result;
        }

        /// <summary>
        /// Process the Data Check sheet containing route assignments
        /// </summary>
        private async Task<ImportResult> ProcessDataCheckSheetAsync(ExcelWorksheet worksheet)
        {
            var result = new ImportResult();
            
            try
            {
                var routeData = ExtractRouteAssignments(worksheet);
                
                foreach (var route in routeData)
                {
                    result.RoutesProcessed++;
                    
                    var existingRoute = await _context.Routes
                        .FirstOrDefaultAsync(r => r.RouteName == route.RouteName);

                    if (existingRoute != null)
                    {
                        // Update existing route
                        existingRoute.MeterCount = route.AccountNumbers.Count;
                        existingRoute.LastSyncDate = DateTime.UtcNow;
                    }
                    else
                    {
                        // Create new route
                        var newRoute = new Models.Route
                        {
                            RouteName = route.RouteName,
                            Description = $"Auto-imported from AMS Data Check sheet",
                            Township = "Rolleston", // Default from AMS data
                            Status = "Active",
                            RouteType = "Scheduled",
                            MeterCount = route.AccountNumbers.Count,
                            LastSyncDate = DateTime.UtcNow,
                            Source = "AMS"
                        };
                        await _context.Routes.AddAsync(newRoute);
                    }

                    // Update meter route assignments
                    await UpdateMeterRouteAssignments(route.RouteName, route.AccountNumbers);
                    
                    result.RoutesImported++;
                }

                await _context.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to process Data Check sheet");
                result.Errors.Add($"Route processing failed: {ex.Message}");
            }

            return result;
        }

        /// <summary>
        /// Process anomaly sheet and flag problematic meters
        /// </summary>
        private async Task ProcessAnomalySheetAsync(ExcelWorksheet worksheet)
        {
            var headers = GetHeaderMapping(worksheet);
            var rowCount = worksheet.Dimension?.Rows ?? 0;

            for (int row = 2; row <= rowCount; row++)
            {
                try
                {
                    var assetId = GetCellValue(worksheet, row, headers.GetValueOrDefault("Asset ID", 1));
                    if (string.IsNullOrEmpty(assetId)) continue;

                    var meter = await _context.WaterMeters
                        .FirstOrDefaultAsync(m => m.AssetId == assetId);

                    if (meter != null)
                    {
                        meter.CantRead = true;
                        meter.Condition = "Anomaly detected in AMS data";
                        meter.Comments = (meter.Comments ?? "") + " [AMS Anomaly flagged]";
                        meter.SyncStatus = "Anomaly";
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to process anomaly data at row {Row}", row);
                }
            }

            await _context.SaveChangesAsync();
        }

        /// <summary>
        /// Get header column mapping from first row
        /// </summary>
        private Dictionary<string, int> GetHeaderMapping(ExcelWorksheet worksheet)
        {
            var headers = new Dictionary<string, int>(StringComparer.OrdinalIgnoreCase);
            var columnCount = worksheet.Dimension?.Columns ?? 0;

            for (int col = 1; col <= columnCount; col++)
            {
                var headerValue = worksheet.Cells[1, col].Text?.Trim();
                if (!string.IsNullOrEmpty(headerValue))
                {
                    headers[headerValue] = col;
                }
            }

            return headers;
        }

        /// <summary>
        /// Validate that required headers are present
        /// </summary>
        private bool ValidateRequiredHeaders(Dictionary<string, int> headers, ImportResult result)
        {
            var requiredHeaders = new[] { "Asset ID", "Meter Number", "Account Number", "Road Name", "Township" };
            var missingHeaders = requiredHeaders.Where(h => !headers.ContainsKey(h)).ToList();

            if (missingHeaders.Any())
            {
                result.Errors.Add($"Missing required headers: {string.Join(", ", missingHeaders)}");
                return false;
            }

            return true;
        }

        /// <summary>
        /// Extract meter data from a single row
        /// </summary>
        private MeterData ExtractMeterDataFromRow(ExcelWorksheet worksheet, int row, Dictionary<string, int> headers)
        {
            return new MeterData
            {
                AssetId = GetCellValue(worksheet, row, headers.GetValueOrDefault("Asset ID", 1)),
                MeterNumber = GetCellValue(worksheet, row, headers.GetValueOrDefault("Meter Number", 2)),
                AccountNumber = GetCellValue(worksheet, row, headers.GetValueOrDefault("Account Number", 3)),
                BookNumber = GetCellValue(worksheet, row, headers.GetValueOrDefault("Book Number", 4)),
                Unit = GetIntValue(worksheet, row, headers.GetValueOrDefault("Unit", 5)) ?? 10,
                RoadNumber = GetIntValue(worksheet, row, headers.GetValueOrDefault("Road Number", 6)),
                RoadName = GetCellValue(worksheet, row, headers.GetValueOrDefault("Road Name", 7)) ?? "",
                Township = GetCellValue(worksheet, row, headers.GetValueOrDefault("Township", 8)) ?? "",
                SubArea = GetCellValue(worksheet, row, headers.GetValueOrDefault("Sub Area", 9)),
                LastRead = GetDecimalValue(worksheet, row, headers.GetValueOrDefault("Last Read", 10)),
                RecentChange = GetDecimalValue(worksheet, row, headers.GetValueOrDefault("Recent Change", 11)),
                Subd = GetCellValue(worksheet, row, headers.GetValueOrDefault("Subd", 12)),
                DateOfRead = GetDateValue(worksheet, row, headers.GetValueOrDefault("Date Of Read", 13)),
                Read = GetDecimalValue(worksheet, row, headers.GetValueOrDefault("Read", 14)),
                CantRead = GetBoolValue(worksheet, row, headers.GetValueOrDefault("Can't Read", 15)),
                Condition = GetCellValue(worksheet, row, headers.GetValueOrDefault("Condition", 16)),
                Comments = GetCellValue(worksheet, row, headers.GetValueOrDefault("Comments", 17))
            };
        }

        /// <summary>
        /// Extract route assignments from Data Check sheet
        /// </summary>
        private List<RouteData> ExtractRouteAssignments(ExcelWorksheet worksheet)
        {
            var routes = new List<RouteData>();
            var columnCount = worksheet.Dimension?.Columns ?? 0;

            for (int col = 1; col <= columnCount; col++)
            {
                var routeName = worksheet.Cells[1, col].Text?.Trim();
                if (string.IsNullOrEmpty(routeName)) continue;

                var accountNumbers = new List<string>();
                var rowCount = worksheet.Dimension?.Rows ?? 0;

                for (int row = 2; row <= rowCount; row++)
                {
                    var accountNumber = worksheet.Cells[row, col].Text?.Trim();
                    if (!string.IsNullOrEmpty(accountNumber))
                    {
                        accountNumbers.Add(accountNumber);
                    }
                }

                if (accountNumbers.Any())
                {
                    routes.Add(new RouteData
                    {
                        RouteName = routeName,
                        AccountNumbers = accountNumbers
                    });
                }
            }

            return routes;
        }

        /// <summary>
        /// Update meter route assignments
        /// </summary>
        private async Task UpdateMeterRouteAssignments(string routeName, List<string> accountNumbers)
        {
            var route = await _context.Routes.FirstOrDefaultAsync(r => r.RouteName == routeName);
            if (route == null) return;

            var meters = await _context.WaterMeters
                .Where(m => accountNumbers.Contains(m.AccountNumber))
                .ToListAsync();

            foreach (var meter in meters)
            {
                meter.AssignedRoute = routeName;
                meter.RouteId = route.Id;
            }
        }

        /// <summary>
        /// Helper methods for cell value extraction
        /// </summary>
        private string? GetCellValue(ExcelWorksheet worksheet, int row, int col)
        {
            return worksheet.Cells[row, col].Text?.Trim();
        }

        private int? GetIntValue(ExcelWorksheet worksheet, int row, int col)
        {
            var value = worksheet.Cells[row, col].Text?.Trim();
            return int.TryParse(value, out var result) ? result : null;
        }

        private decimal? GetDecimalValue(ExcelWorksheet worksheet, int row, int col)
        {
            var value = worksheet.Cells[row, col].Text?.Trim();
            return decimal.TryParse(value, NumberStyles.Any, CultureInfo.InvariantCulture, out var result) ? result : null;
        }

        private DateTime? GetDateValue(ExcelWorksheet worksheet, int row, int col)
        {
            var cellValue = worksheet.Cells[row, col].Value;
            
            if (cellValue is DateTime dateTime)
                return dateTime;
                
            if (cellValue is double doubleValue)
                return DateTime.FromOADate(doubleValue);
                
            if (DateTime.TryParse(cellValue?.ToString(), out var parsedDate))
                return parsedDate;
                
            return null;
        }

        private bool GetBoolValue(ExcelWorksheet worksheet, int row, int col)
        {
            var value = worksheet.Cells[row, col].Text?.Trim().ToLower();
            return value == "true" || value == "yes" || value == "1" || value == "x";
        }

        /// <summary>
        /// Data transfer objects
        /// </summary>
        public class MeterData
        {
            public string AssetId { get; set; } = string.Empty;
            public string MeterNumber { get; set; } = string.Empty;
            public string AccountNumber { get; set; } = string.Empty;
            public string? BookNumber { get; set; }
            public int Unit { get; set; } = 10;
            public int? RoadNumber { get; set; }
            public string RoadName { get; set; } = string.Empty;
            public string Township { get; set; } = string.Empty;
            public string? SubArea { get; set; }
            public decimal? LastRead { get; set; }
            public decimal? RecentChange { get; set; }
            public string? Subd { get; set; }
            public DateTime? DateOfRead { get; set; }
            public decimal? Read { get; set; }
            public bool CantRead { get; set; }
            public string? Condition { get; set; }
            public string? Comments { get; set; }
        }

        public class RouteData
        {
            public string RouteName { get; set; } = string.Empty;
            public List<string> AccountNumbers { get; set; } = new();
        }

        /// <summary>
        /// Create baseline record for imported meter
        /// 为导入的水表创建基线记录
        /// </summary>
        private async Task CreateBaselineRecordForMeter(WaterMeter meter, MeterData meterData, bool isNewMeter)
        {
            try
            {
                // Only create baseline record if we have LastRead data
                if (!meterData.LastRead.HasValue)
                {
                    _logger.LogInformation("Skipping baseline creation for meter {AssetId} - no LastRead value", meter.AssetId);
                    return;
                }

                // Check if baseline record already exists for this meter
                var existingBaseline = await _context.BaselineRecords
                    .Where(b => b.MeterId == meter.Id && 
                               b.DataSource == "AMS" && 
                               !b.IsDeleted)
                    .OrderByDescending(b => b.BaselineDate)
                    .FirstOrDefaultAsync();

                if (existingBaseline != null)
                {
                    // Update existing baseline if the data is newer or different
                    if (meterData.DateOfRead.HasValue && meterData.DateOfRead > existingBaseline.BaselineDate ||
                        existingBaseline.BaselineValue != meterData.LastRead.Value)
                    {
                        existingBaseline.BaselineValue = meterData.LastRead.Value;
                        existingBaseline.BaselineDate = meterData.DateOfRead ?? DateTime.UtcNow;
                        existingBaseline.UpdatedAt = DateTime.UtcNow;
                        existingBaseline.UpdatedBy = "AMS Import";
                        existingBaseline.Notes = $"Updated from AMS import on {DateTime.UtcNow:yyyy-MM-dd HH:mm:ss}. Previous value: {existingBaseline.BaselineValue}";
                        
                        _logger.LogInformation("Updated baseline for meter {AssetId}: {OldValue} -> {NewValue}", 
                            meter.AssetId, existingBaseline.BaselineValue, meterData.LastRead.Value);
                    }
                    else
                    {
                        _logger.LogInformation("Baseline for meter {AssetId} is up to date", meter.AssetId);
                    }
                }
                else
                {
                    // Create new baseline record using the mapping service
                    var baselineRecord = new BaselineRecord
                    {
                        MeterId = meter.Id,
                        BaselineValue = meterData.LastRead.Value,
                        BaselineDate = meterData.DateOfRead ?? DateTime.UtcNow,
                        BaselineType = "Initial",
                        Status = "Active",
                        DataSource = "AMS",
                        ImportBatch = $"AMS_Import_{DateTime.UtcNow:yyyyMMdd_HHmmss}",
                        SourceFile = "AMS Excel Import",
                        IsValidated = false,
                        HasValidationErrors = false,
                        IsAnomalous = false,
                        IsCorrected = false,
                        ConfidenceLevel = DetermineConfidenceLevel(meterData),
                        Notes = $"Imported from AMS on {DateTime.UtcNow:yyyy-MM-dd HH:mm:ss}. Serial: {meter.SerialNumber}",
                        CreatedAt = DateTime.UtcNow,
                        UpdatedAt = DateTime.UtcNow,
                        CreatedBy = "AMS Import",
                        UpdatedBy = "AMS Import"
                    };

                    await _context.BaselineRecords.AddAsync(baselineRecord);
                    await _context.SaveChangesAsync();
                    
                    _logger.LogInformation("Created baseline record for meter {AssetId} with value {BaselineValue}", 
                        meter.AssetId, meterData.LastRead.Value);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to create baseline record for meter {AssetId}", meter.AssetId);
                // Don't throw - baseline creation failure shouldn't stop meter import
            }
        }

        /// <summary>
        /// Determine confidence level for baseline based on AMS data quality
        /// 根据AMS数据质量确定基线的可信度
        /// </summary>
        private int DetermineConfidenceLevel(MeterData meterData)
        {
            int confidence = 100;

            // Reduce confidence if there are read issues
            if (meterData.CantRead)
                confidence -= 30;

            // Reduce confidence if no date information
            if (!meterData.DateOfRead.HasValue)
                confidence -= 20;

            // Reduce confidence if condition indicates problems
            if (!string.IsNullOrEmpty(meterData.Condition) && 
                (meterData.Condition.Contains("damaged", StringComparison.OrdinalIgnoreCase) ||
                 meterData.Condition.Contains("broken", StringComparison.OrdinalIgnoreCase) ||
                 meterData.Condition.Contains("error", StringComparison.OrdinalIgnoreCase)))
                confidence -= 25;

            // Ensure minimum confidence of 10
            return Math.Max(confidence, 10);
        }
    }
} 