using WaterMeterManagement.Services.Interfaces;
using WaterMeterManagement.DTOs.Mobile;
using WaterMeterManagement.DTOs;
using WaterMeterManagement.Data;
using WaterMeterManagement.Models;
using Microsoft.EntityFrameworkCore;

namespace WaterMeterManagement.Services
{
    public class PhotoService : IPhotoService
    {
        private readonly ILogger<PhotoService> _logger;
        private readonly ICloudflareR2Service _r2Service;
        private readonly ApplicationDbContext _context;

        public PhotoService(
            ILogger<PhotoService> logger,
            ICloudflareR2Service r2Service,
            ApplicationDbContext context)
        {
            _logger = logger;
            _r2Service = r2Service;
            _context = context;
        }

        public async Task<List<PhotoUploadResult>> UploadMobilePhotosAsync(int readingId, List<MobilePhotoDto> photos, int userId)
        {
            _logger.LogInformation("Uploading {Count} photos for reading {ReadingId}", photos.Count, readingId);

            var results = new List<PhotoUploadResult>();

            foreach (var photo in photos)
            {
                try
                {
                    var result = await UploadSinglePhotoAsync(readingId, photo, userId);
                    results.Add(result);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failed to upload photo {FileName} for reading {ReadingId}", photo.FileName, readingId);
                    results.Add(new PhotoUploadResult
                    {
                        OriginalFileName = photo.FileName,
                        Success = false,
                        Error = ex.Message
                    });
                }
            }

            return results;
        }

        public async Task<PhotoUploadResult> UploadSinglePhotoAsync(int readingId, MobilePhotoDto photo, int userId)
        {
            _logger.LogInformation("=== PhotoService.UploadSinglePhotoAsync START === File: {FileName}, Reading: {ReadingId}, User: {UserId}",
                photo.FileName, readingId, userId);

            try
            {
                var validation = await ValidatePhotoAsync(photo);
                if (!validation.IsValid)
                {
                    return new PhotoUploadResult
                    {
                        OriginalFileName = photo.FileName,
                        Success = false,
                        Error = string.Join(", ", validation.Errors)
                    };
                }

                var photoBytes = Convert.FromBase64String(photo.Base64Data);
                using var photoStream = new MemoryStream(photoBytes);

                var cloudflareUrl = await _r2Service.UploadPhotoAsync(photoStream, photo.FileName, photo.MimeType);

                photoStream.Position = 0;
                var thumbnailUrl = await _r2Service.CreateThumbnailAsync(photoStream, photo.FileName);

                var readingPhoto = new ReadingPhoto
                {
                    ReadingId = readingId,
                    OriginalFileName = photo.FileName,
                    CloudflareUrl = cloudflareUrl,
                    ThumbnailUrl = thumbnailUrl,
                    FileSizeBytes = photo.FileSizeBytes,
                    MimeType = photo.MimeType,
                    UploadTime = DateTime.UtcNow,
                    IsProcessed = false,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow,
                    CreatedBy = userId.ToString(),
                    UpdatedBy = userId.ToString()
                };

                _context.ReadingPhotos.Add(readingPhoto);
                await _context.SaveChangesAsync();

                return new PhotoUploadResult
                {
                    OriginalFileName = photo.FileName,
                    Success = true,
                    UploadedFileName = Path.GetFileName(cloudflareUrl),
                    PhotoId = readingPhoto.Id,
                    Url = cloudflareUrl
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to upload photo {FileName} for reading {ReadingId}", photo.FileName, readingId);
                return new PhotoUploadResult
                {
                    OriginalFileName = photo.FileName,
                    Success = false,
                    Error = ex.Message
                };
            }
        }

        public async Task<(bool Success, string Message)> DeletePhotoAsync(int photoId, int userId)
        {
            try
            {
                var photo = await _context.ReadingPhotos.FindAsync(photoId);
                if (photo == null || photo.IsDeleted)
                {
                    return (false, "Photo not found");
                }

                await _r2Service.DeletePhotoAsync(photo.CloudflareUrl);

                if (!string.IsNullOrEmpty(photo.ThumbnailUrl))
                {
                    await _r2Service.DeletePhotoAsync(photo.ThumbnailUrl);
                }

                photo.IsDeleted = true;
                photo.UpdatedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                _logger.LogInformation("Photo {PhotoId} deleted successfully by user {UserId}", photoId, userId);
                return (true, "Photo deleted successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting photo {PhotoId}", photoId);
                return (false, $"Error deleting photo: {ex.Message}");
            }
        }

        public async Task<List<string>> GetPhotoUrlsAsync(int readingId)
        {
            try
            {
                var photos = await _context.ReadingPhotos
                    .Where(p => p.ReadingId == readingId && !p.IsDeleted)
                    .Select(p => p.CloudflareUrl)
                    .ToListAsync();

                return photos;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting photo URLs for reading {ReadingId}", readingId);
                throw;
            }
        }

        public async Task<List<ReadingPhotoDto>> GetReadingPhotosAsync(int readingId)
        {
            try
            {
                var photos = await _context.ReadingPhotos
                    .Where(p => p.ReadingId == readingId && !p.IsDeleted)
                    .OrderByDescending(p => p.UploadTime)
                    .ToListAsync();

                return photos.Select(p => new ReadingPhotoDto
                {
                    Id = p.Id,
                    MeterReadingId = p.ReadingId,
                    OriginalFileName = p.OriginalFileName,
                    CloudflareUrl = p.CloudflareUrl,
                    ThumbnailUrl = p.ThumbnailUrl,
                    FileSize = p.FileSizeBytes,
                    MimeType = p.MimeType ?? "image/jpeg",
                    UploadTime = p.UploadTime,
                    QualityScore = p.QualityScore,
                    IsProcessed = p.IsProcessed,
                    HasOCR = !string.IsNullOrEmpty(p.OcrResult),
                    OcrResult = p.OcrResult,
                    OcrConfidence = p.OcrConfidence,
                    CreatedAt = p.CreatedAt.ToString("O")
                }).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting photos for reading {ReadingId}", readingId);
                throw;
            }
        }

        public async Task<(bool IsValid, List<string> Errors)> ValidatePhotoAsync(MobilePhotoDto photo)
        {
            var errors = new List<string>();
            
            if (string.IsNullOrEmpty(photo.FileName))
                errors.Add("File name is required");
                
            if (photo.FileSizeBytes > 10 * 1024 * 1024) // 10MB limit
                errors.Add("File size too large (max 10MB)");
            
            return await Task.FromResult((errors.Count == 0, errors));
        }

        public async Task<bool> ValidatePhotoSizeAsync(int fileSizeBytes)
        {
            return await Task.FromResult(fileSizeBytes <= 10 * 1024 * 1024);
        }

        public async Task<string?> ProcessPhotoAsync(MobilePhotoDto photo, string uploadPath)
        {
            _logger.LogInformation("Processing photo {FileName}", photo.FileName);
            return await Task.FromResult($"{uploadPath}/{photo.FileName}");
        }

        public async Task<string?> CreateThumbnailAsync(string originalPath)
        {
            _logger.LogInformation("Creating thumbnail for {OriginalPath}", originalPath);
            return await Task.FromResult($"{originalPath}_thumb.jpg");
        }

        public async Task<List<PhotoUploadResult>> ProcessBatchPhotosAsync(List<MobilePhotoDto> photos, int readingId, int userId)
        {
            return await UploadMobilePhotosAsync(readingId, photos, userId);
        }

        public async Task<PhotoUploadResult> UploadFormFileAsync(IFormFile photo, int userId)
        {
            _logger.LogInformation("=== PhotoService.UploadFormFileAsync START === File: {FileName}, Size: {Size}, User: {UserId}",
                photo.FileName, photo.Length, userId);

            try
            {
                // 基本验证
                if (photo == null || photo.Length == 0)
                {
                    return new PhotoUploadResult
                    {
                        OriginalFileName = photo?.FileName ?? "unknown",
                        Success = false,
                        Error = "No file provided or file is empty"
                    };
                }

                // 文件类型验证
                var allowedTypes = new[] { "image/jpeg", "image/jpg", "image/png", "image/bmp" };
                if (!allowedTypes.Contains(photo.ContentType.ToLower()))
                {
                    return new PhotoUploadResult
                    {
                        OriginalFileName = photo.FileName,
                        Success = false,
                        Error = "Invalid file type. Only JPEG, PNG, and BMP files are allowed."
                    };
                }

                // 文件大小验证 (最大10MB)
                const int maxSize = 10 * 1024 * 1024;
                if (photo.Length > maxSize)
                {
                    return new PhotoUploadResult
                    {
                        OriginalFileName = photo.FileName,
                        Success = false,
                        Error = "File size exceeds 10MB limit"
                    };
                }

                // 上传到R2
                using var photoStream = photo.OpenReadStream();
                var cloudflareUrl = await _r2Service.UploadPhotoAsync(photoStream, photo.FileName, photo.ContentType);

                if (string.IsNullOrEmpty(cloudflareUrl))
                {
                    return new PhotoUploadResult
                    {
                        OriginalFileName = photo.FileName,
                        Success = false,
                        Error = "Failed to upload to R2 storage"
                    };
                }

                // 创建缩略图
                photoStream.Position = 0;
                var thumbnailUrl = await _r2Service.CreateThumbnailAsync(photoStream, photo.FileName);

                _logger.LogInformation("=== PhotoService.UploadFormFileAsync SUCCESS === URL: {Url}, Thumbnail: {ThumbnailUrl}",
                    cloudflareUrl, thumbnailUrl);

                return new PhotoUploadResult
                {
                    OriginalFileName = photo.FileName,
                    FileName = Path.GetFileName(cloudflareUrl),
                    CloudflareUrl = cloudflareUrl,
                    ThumbnailUrl = thumbnailUrl,
                    FileSizeBytes = (int)photo.Length,
                    MimeType = photo.ContentType,
                    Success = true,
                    PhotoId = 0 // 这里没有保存到数据库，所以ID为0
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to upload form file {FileName}", photo.FileName);
                return new PhotoUploadResult
                {
                    OriginalFileName = photo.FileName,
                    Success = false,
                    Error = ex.Message
                };
            }
        }
    }
}