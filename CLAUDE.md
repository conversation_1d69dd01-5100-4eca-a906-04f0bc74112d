# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a comprehensive water meter reading management system consisting of multiple components:

1. **WaterMeterManagement** - ASP.NET Core 8.0 backend API with PostgreSQL
2. **MeterReadingApp** - React Native mobile app for field workers  
3. **CORDE-Mobile-Application** - Legacy React Native app for asset management
4. **water-meter-admin** - Next.js admin web interface
5. **water-meter-ai-annotation** - Python AI/ML system for meter OCR

## Common Development Commands

### Backend API (WaterMeterManagement/)
```bash
# Development
dotnet restore
dotnet run
dotnet ef database update  # Apply migrations
dotnet ef migrations add <MigrationName>  # Create new migration

# Testing & Build
dotnet test
dotnet build
dotnet publish -c Release

# Database reset (development)
dotnet ef database drop --force
dotnet ef database update
```

### Mobile Apps (React Native)

#### MeterReadingApp/
```bash
# Development
yarn install
yarn start  # Start Metro bundler
yarn android  # Run on Android
yarn ios      # Run on iOS (macOS only)

# Build for release
yarn build:android:clean
yarn build:android:release
yarn release  # Clean + build Android release

# Testing & Linting
yarn test
yarn lint
```

#### CORDE-Mobile-Application/
```bash
yarn install
yarn start
yarn android
yarn ios
yarn test
yarn lint
```

### Admin Web Interface (water-meter-admin/)
```bash
# Development
yarn dev     # or npm run dev
yarn build   # or npm run build
yarn start   # or npm run start

# Linting
yarn lint    # or npm run lint
```

### AI Annotation System (water-meter-ai-annotation/)
```bash
# Setup
pip install -r requirements.txt
python setup.py install

# Run annotation
python scripts/run_ai_annotation.py
python scripts/test_model.py
```

### Docker Deployment (deployment-config/)
```bash
# Start all services
docker-compose up -d

# View logs
docker-compose logs -f

# Stop services
docker-compose down
```

## Architecture Overview

### Backend API Structure
- **Controllers/**: REST API endpoints organized by domain
- **Services/**: Business logic layer with interface-based dependency injection
- **Models/**: Entity Framework Core data models with audit fields
- **DTOs/**: Data transfer objects for API communication
- **Repositories/**: Data access layer abstraction
- **Migrations/**: EF Core database migrations

Key architectural patterns:
- Repository pattern with service layer
- JWT authentication with Workbench API integration
- Auto-seeding database initialization
- Comprehensive Swagger documentation at `/swagger`
- Serilog structured logging

### Mobile App Architecture (React Native)
- **Context providers**: AuthContext, ThemeContext for global state
- **Services**: Network communication, database operations, background sync
- **Navigation**: React Navigation with stack and tab navigators
- **Database**: SQLite with react-native-sqlite-storage
- **Authentication**: Local + API-based with JWT tokens
- **Background sync**: Automatic data synchronization when network available

### Key Integration Points
- Mobile apps sync with backend API via JWT-authenticated endpoints
- Backend integrates with Workbench API for user authentication
- PostgreSQL database with Entity Framework Core migrations
- Docker deployment with Nginx reverse proxy
- AI annotation system processes meter photos for OCR

### Database Design
- Audit fields (CreatedAt, CreatedBy, UpdatedAt, UpdatedBy) on all entities
- Foreign key relationships with proper cascading
- PostgreSQL-specific features utilized
- Migration-based schema management

## Development Guidelines

### Code Quality
- TypeScript strict mode enabled for React projects
- ESLint configuration for consistent code style
- Entity Framework migrations for all database changes
- Interface-based service injection in .NET backend

### Authentication Flow
1. Mobile app authenticates with backend API
2. Backend validates credentials against Workbench API
3. JWT token returned for subsequent API calls
4. Token includes user roles and permissions

### Known Issues & Solutions

#### Icon Validation Error in MeterReadingApp
- **Issue**: NativeBase 3.4.28 has strict icon name validation that rejects valid MaterialCommunityIcons
- **Solution**: IconWrapper component handles icon name mapping and fallback
- **Location**: `src/components/common/IconWrapper.tsx`
- **Fix Applied**: Icon name mapping for problematic icons like 'chart' → 'chart-line'

### Testing Strategy
- Unit tests with Jest for React components
- API testing via Swagger UI in development
- Database integration tests in .NET backend
- Manual testing procedures documented in docs/

### Deployment
- Development: Individual component startup
- Production: Docker Compose orchestration
- Environment variables for configuration
- Health checks for service monitoring

## Component-Specific Notes

### MeterReadingApp
- Uses SQLite for offline data storage
- Background sync service for data synchronization
- GPS location services for meter reading
- Camera integration for OCR meter reading
- Theme switching support (dark/light mode)

### WaterMeterManagement (Backend)
- PostgreSQL connection required
- Swagger UI available at https://localhost:7000/swagger in development
- JWT authentication with 30-day token expiration
- Health check endpoint at `/health`

### water-meter-admin
- Next.js 14 with Ant Design components
- Google Maps integration for location features
- Tailwind CSS for styling
- TypeScript for type safety