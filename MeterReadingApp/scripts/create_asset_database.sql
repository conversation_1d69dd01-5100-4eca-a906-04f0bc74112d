-- Water Meter App Asset Database Creation Script
-- Execute this script in SQLite to create the pre-populated database file
-- Place the resulting water_meter_app.db file in android/app/src/main/assets/

-- Version management table
CREATE TABLE IF NOT EXISTS db_version (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  version INTEGER NOT NULL,
  updated_at TEXT DEFAULT CURRENT_TIMESTAMP
);

-- Insert initial version
INSERT INTO db_version (version) VALUES (1);

-- Enhanced users table
CREATE TABLE IF NOT EXISTS users (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  username TEXT UNIQUE NOT NULL,
  password_hash TEXT NOT NULL,
  full_name TEXT,
  email TEXT,
  phone_number TEXT,
  role TEXT DEFAULT 'reader',
  is_active INTEGER DEFAULT 1,
  last_login_date TEXT,
  sync_status TEXT DEFAULT 'synced',
  created_at TEXT DEFAULT CURRENT_TIMESTAMP,
  updated_at TEXT DEFAULT CURRENT_TIMESTAMP
);

-- Meter types reference table
CREATE TABLE IF NOT EXISTS meter_types (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  type_code TEXT UNIQUE NOT NULL,
  type_name TEXT NOT NULL,
  description TEXT,
  manufacturer TEXT,
  model TEXT,
  flow_rate_min REAL,
  flow_rate_max REAL,
  accuracy_class TEXT,
  is_active INTEGER DEFAULT 1,
  created_at TEXT DEFAULT CURRENT_TIMESTAMP
);

-- Areas/Locations reference table
CREATE TABLE IF NOT EXISTS areas (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  area_code TEXT UNIQUE NOT NULL,
  area_name TEXT NOT NULL,
  description TEXT,
  parent_area_id INTEGER,
  latitude REAL,
  longitude REAL,
  is_active INTEGER DEFAULT 1,
  created_at TEXT DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (parent_area_id) REFERENCES areas (id)
);

-- Enhanced water meters table
CREATE TABLE IF NOT EXISTS water_meters (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  meter_number TEXT UNIQUE NOT NULL,
  meter_type_id INTEGER,
  area_id INTEGER,
  customer_name TEXT,
  customer_phone TEXT,
  address TEXT,
  latitude REAL,
  longitude REAL,
  installation_date TEXT,
  last_reading REAL DEFAULT 0,
  last_reading_date TEXT,
  status TEXT DEFAULT 'active',
  sync_status TEXT DEFAULT 'synced',
  created_at TEXT DEFAULT CURRENT_TIMESTAMP,
  updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (meter_type_id) REFERENCES meter_types (id),
  FOREIGN KEY (area_id) REFERENCES areas (id)
);

-- Enhanced meter readings table
CREATE TABLE IF NOT EXISTS meter_readings (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  uuid TEXT UNIQUE NOT NULL,
  meter_id INTEGER NOT NULL,
  user_id INTEGER NOT NULL,
  reading_value REAL NOT NULL,
  previous_reading REAL,
  consumption REAL,
  reading_date TEXT NOT NULL,
  latitude REAL,
  longitude REAL,
  photo_path TEXT,
  notes TEXT,
  reading_method TEXT DEFAULT 'manual',
  quality_score REAL,
  verification_status TEXT DEFAULT 'pending',
  sync_status TEXT DEFAULT 'pending',
  created_at TEXT DEFAULT CURRENT_TIMESTAMP,
  updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (meter_id) REFERENCES water_meters (id),
  FOREIGN KEY (user_id) REFERENCES users (id)
);

-- Tasks table for work assignments
CREATE TABLE IF NOT EXISTS tasks (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  uuid TEXT UNIQUE NOT NULL,
  assigned_user_id INTEGER NOT NULL,
  meter_id INTEGER NOT NULL,
  task_type TEXT DEFAULT 'reading',
  status TEXT DEFAULT 'assigned',
  priority TEXT DEFAULT 'medium',
  scheduled_date TEXT,
  completed_date TEXT,
  notes TEXT,
  sync_status TEXT DEFAULT 'pending',
  created_at TEXT DEFAULT CURRENT_TIMESTAMP,
  updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (assigned_user_id) REFERENCES users (id),
  FOREIGN KEY (meter_id) REFERENCES water_meters (id)
);

-- Sync management table
CREATE TABLE IF NOT EXISTS sync_logs (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  table_name TEXT NOT NULL,
  record_id TEXT,
  operation_type TEXT NOT NULL,
  sync_status TEXT DEFAULT 'pending',
  error_message TEXT,
  retry_count INTEGER DEFAULT 0,
  last_sync_attempt TEXT,
  created_at TEXT DEFAULT CURRENT_TIMESTAMP
);

-- Photos table
CREATE TABLE IF NOT EXISTS photos (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  uuid TEXT UNIQUE NOT NULL,
  meter_reading_id INTEGER,
  photo_path TEXT NOT NULL,
  thumbnail_path TEXT,
  file_size INTEGER,
  upload_status TEXT DEFAULT 'pending',
  sync_status TEXT DEFAULT 'pending',
  created_at TEXT DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (meter_reading_id) REFERENCES meter_readings (id)
);

-- App settings table
CREATE TABLE IF NOT EXISTS app_settings (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  key TEXT UNIQUE NOT NULL,
  value TEXT,
  type TEXT DEFAULT 'string',
  updated_at TEXT DEFAULT CURRENT_TIMESTAMP
);

-- Create performance indexes
CREATE INDEX IF NOT EXISTS idx_meter_readings_meter_id ON meter_readings(meter_id);
CREATE INDEX IF NOT EXISTS idx_meter_readings_user_id ON meter_readings(user_id);
CREATE INDEX IF NOT EXISTS idx_meter_readings_sync_status ON meter_readings(sync_status);
CREATE INDEX IF NOT EXISTS idx_water_meters_area_id ON water_meters(area_id);
CREATE INDEX IF NOT EXISTS idx_water_meters_type_id ON water_meters(meter_type_id);
CREATE INDEX IF NOT EXISTS idx_water_meters_status ON water_meters(status);
CREATE INDEX IF NOT EXISTS idx_tasks_user_id ON tasks(assigned_user_id);
CREATE INDEX IF NOT EXISTS idx_tasks_status ON tasks(status);
CREATE INDEX IF NOT EXISTS idx_sync_logs_table_name ON sync_logs(table_name);
CREATE INDEX IF NOT EXISTS idx_sync_logs_status ON sync_logs(sync_status);

-- ========================================
-- SAMPLE DATA SECTION (Optional)
-- Remove this section if you want to start with empty tables
-- ========================================

-- Sample meter types
INSERT INTO meter_types (type_code, type_name, manufacturer, model, accuracy_class) VALUES
('RESIDENTIAL_15MM', 'Residential 15mm', 'Kamstrup', 'MULTICAL 21', 'Class 2'),
('RESIDENTIAL_20MM', 'Residential 20mm', 'Kamstrup', 'MULTICAL 21', 'Class 2'),
('COMMERCIAL_25MM', 'Commercial 25mm', 'Sensus', 'iPERL', 'Class 1'),
('COMMERCIAL_40MM', 'Commercial 40mm', 'Sensus', 'iPERL', 'Class 1'),
('INDUSTRIAL_50MM', 'Industrial 50mm', 'Itron', 'Aquadis+', 'Class 1');

-- Sample areas
INSERT INTO areas (area_code, area_name, description) VALUES
('CENTRAL', 'Central District', 'Main urban area'),
('NORTH', 'North District', 'Northern residential area'),
('SOUTH', 'South District', 'Southern commercial area'),
('EAST', 'East District', 'Eastern industrial area'),
('WEST', 'West District', 'Western residential area');

-- Sample app settings
INSERT INTO app_settings (key, value, type) VALUES
('app_version', '1.0.0', 'string'),
('sync_interval', '300', 'number'),
('auto_sync_enabled', 'true', 'boolean'),
('photo_quality', '0.8', 'number'),
('gps_accuracy_threshold', '10', 'number');

-- NOTE: Do not include user accounts in the asset database for security reasons
-- Users should be created through the app's registration process or admin import

-- ========================================
-- END OF SCRIPT
-- ======================================== 