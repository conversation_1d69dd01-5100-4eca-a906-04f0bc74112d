# Assets Directory

This directory contains the pre-built SQLite database file for the Water Meter Reading App.

## Database File Placement

Place your pre-built database file here:
- **File name**: `water_meter_app.db`
- **Location**: `android/app/src/main/assets/water_meter_app.db`

## Creating the Database File

1. Use the SQL script provided at `scripts/create_asset_database.sql`
2. Execute it with SQLite:
   ```bash
   sqlite3 water_meter_app.db < scripts/create_asset_database.sql
   ```
3. Copy the resulting file to this directory

## App Behavior

- **With Asset Database**: App will copy the pre-built database on first launch
- **Without Asset Database**: App will create empty tables and log a warning

The app will automatically handle both scenarios gracefully. 