#!/bin/bash

# MeterReadingApp Release Build Script
# 版本: 0.0.1

echo "🚀 开始构建 MeterReadingApp v0.0.1 Release 包..."

# 清理之前的构建
echo "🧹 清理之前的构建..."
yarn clean || echo "跳过清理步骤"
cd android
./gradlew clean
cd ..

# 构建 Release APK
echo "📦 构建 Release APK..."
cd android
./gradlew assembleRelease

if [ $? -eq 0 ]; then
    echo "✅ Release APK 构建成功!"
    echo "📱 APK 位置: android/app/build/outputs/apk/release/water-meter-0.0.1.apk"
    
    # 显示APK信息
    APK_PATH="app/build/outputs/apk/release/water-meter-0.0.1.apk"
    if [ -f "$APK_PATH" ]; then
        APK_SIZE=$(ls -lh "$APK_PATH" | awk '{print $5}')
        echo "📊 APK 大小: $APK_SIZE"
    fi
    
    echo ""
    echo "🎉 MeterReadingApp v0.0.1 构建完成!"
    echo "📤 可以将 water-meter-0.0.1.apk 发送给雇主测试了"
else
    echo "❌ Release APK 构建失败!"
    exit 1
fi

cd .. 