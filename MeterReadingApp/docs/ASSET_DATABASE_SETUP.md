# Asset Database Setup Guide

This guide explains how to create and use a pre-populated SQLite database as an asset for the Water Meter Reading App.

## Overview

Following CORDE's database architecture, the app can use a pre-built SQLite database file that contains initial data. This approach offers several benefits:

1. **Faster First Launch**: No need to download initial data from server
2. **Offline Capability**: App works immediately without network connection
3. **Reduced Server Load**: Eliminates need for bulk data sync on first install
4. **Consistent Initial State**: All installations start with the same base data

## Database File Location

The asset database file should be placed at:
```
android/app/src/main/assets/water_meter_app.db
```

## Creating the Asset Database

### Option 1: Using SQLite Command Line Tool

1. **Install SQLite** (if not already installed):
   - Windows: Download from https://sqlite.org/download.html
   - macOS: `brew install sqlite`
   - Linux: `sudo apt-get install sqlite3`

2. **Create the database file**:
   ```bash
   sqlite3 water_meter_app.db
   ```

3. **Execute the creation script** (see SQL script below)

4. **Copy to assets folder**:
   ```bash
   cp water_meter_app.db android/app/src/main/assets/
   ```

### Option 2: Using DB Browser for SQLite (GUI)

1. Download and install DB Browser for SQLite from https://sqlitebrowser.org/
2. Create a new database named `water_meter_app.db`
3. Execute the SQL scripts in the "Execute SQL" tab
4. Save and copy the file to the assets folder

## Important Configuration

Make sure to update the package name in EnhancedDatabaseManager.ts:

```typescript
// Update this line with your actual app package name
const dbPath = `/data/data/YOUR_ACTUAL_PACKAGE_NAME/databases/${DB_NAME}`;
```

The enhanced database manager will automatically:
1. Try to copy the asset database on first launch
2. Fall back to creating empty tables if asset copy fails
3. Handle version management and migrations
4. Optimize performance with PRAGMA statements

## Database Schema Creation Script

```sql
-- Version management table
CREATE TABLE IF NOT EXISTS db_version (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  version INTEGER NOT NULL,
  updated_at TEXT DEFAULT CURRENT_TIMESTAMP
);

-- Insert initial version
INSERT INTO db_version (version) VALUES (1);

-- Enhanced users table
CREATE TABLE IF NOT EXISTS users (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  username TEXT UNIQUE NOT NULL,
  password_hash TEXT NOT NULL,
  full_name TEXT,
  email TEXT,
  phone_number TEXT,
  role TEXT DEFAULT 'reader',
  is_active INTEGER DEFAULT 1,
  last_login_date TEXT,
  sync_status TEXT DEFAULT 'synced',
  created_at TEXT DEFAULT CURRENT_TIMESTAMP,
  updated_at TEXT DEFAULT CURRENT_TIMESTAMP
);

-- Meter types reference table
CREATE TABLE IF NOT EXISTS meter_types (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  type_code TEXT UNIQUE NOT NULL,
  type_name TEXT NOT NULL,
  description TEXT,
  manufacturer TEXT,
  model TEXT,
  flow_rate_min REAL,
  flow_rate_max REAL,
  accuracy_class TEXT,
  is_active INTEGER DEFAULT 1,
  created_at TEXT DEFAULT CURRENT_TIMESTAMP
);

-- Areas/Locations reference table
CREATE TABLE IF NOT EXISTS areas (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  area_code TEXT UNIQUE NOT NULL,
  area_name TEXT NOT NULL,
  description TEXT,
  parent_area_id INTEGER,
  latitude REAL,
  longitude REAL,
  is_active INTEGER DEFAULT 1,
  created_at TEXT DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (parent_area_id) REFERENCES areas (id)
);

-- Enhanced water meters table
CREATE TABLE IF NOT EXISTS water_meters (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  meter_number TEXT UNIQUE NOT NULL,
  meter_type_id INTEGER,
  area_id INTEGER,
  customer_name TEXT,
  customer_phone TEXT,
  address TEXT,
  latitude REAL,
  longitude REAL,
  installation_date TEXT,
  last_reading REAL DEFAULT 0,
  last_reading_date TEXT,
  status TEXT DEFAULT 'active',
  sync_status TEXT DEFAULT 'synced',
  created_at TEXT DEFAULT CURRENT_TIMESTAMP,
  updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (meter_type_id) REFERENCES meter_types (id),
  FOREIGN KEY (area_id) REFERENCES areas (id)
);

-- Enhanced meter readings table
CREATE TABLE IF NOT EXISTS meter_readings (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  uuid TEXT UNIQUE NOT NULL,
  meter_id INTEGER NOT NULL,
  user_id INTEGER NOT NULL,
  reading_value REAL NOT NULL,
  previous_reading REAL,
  consumption REAL,
  reading_date TEXT NOT NULL,
  latitude REAL,
  longitude REAL,
  photo_path TEXT,
  notes TEXT,
  reading_method TEXT DEFAULT 'manual',
  quality_score REAL,
  verification_status TEXT DEFAULT 'pending',
  sync_status TEXT DEFAULT 'pending',
  created_at TEXT DEFAULT CURRENT_TIMESTAMP,
  updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (meter_id) REFERENCES water_meters (id),
  FOREIGN KEY (user_id) REFERENCES users (id)
);

-- Tasks table for work assignments
CREATE TABLE IF NOT EXISTS tasks (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  uuid TEXT UNIQUE NOT NULL,
  assigned_user_id INTEGER NOT NULL,
  meter_id INTEGER NOT NULL,
  task_type TEXT DEFAULT 'reading',
  status TEXT DEFAULT 'assigned',
  priority TEXT DEFAULT 'medium',
  scheduled_date TEXT,
  completed_date TEXT,
  notes TEXT,
  sync_status TEXT DEFAULT 'pending',
  created_at TEXT DEFAULT CURRENT_TIMESTAMP,
  updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (assigned_user_id) REFERENCES users (id),
  FOREIGN KEY (meter_id) REFERENCES water_meters (id)
);

-- Sync management table
CREATE TABLE IF NOT EXISTS sync_logs (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  table_name TEXT NOT NULL,
  record_id TEXT,
  operation_type TEXT NOT NULL,
  sync_status TEXT DEFAULT 'pending',
  error_message TEXT,
  retry_count INTEGER DEFAULT 0,
  last_sync_attempt TEXT,
  created_at TEXT DEFAULT CURRENT_TIMESTAMP
);

-- Photos table
CREATE TABLE IF NOT EXISTS photos (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  uuid TEXT UNIQUE NOT NULL,
  meter_reading_id INTEGER,
  photo_path TEXT NOT NULL,
  thumbnail_path TEXT,
  file_size INTEGER,
  upload_status TEXT DEFAULT 'pending',
  sync_status TEXT DEFAULT 'pending',
  created_at TEXT DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (meter_reading_id) REFERENCES meter_readings (id)
);

-- App settings table
CREATE TABLE IF NOT EXISTS app_settings (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  key TEXT UNIQUE NOT NULL,
  value TEXT,
  type TEXT DEFAULT 'string',
  updated_at TEXT DEFAULT CURRENT_TIMESTAMP
);

-- Create performance indexes
CREATE INDEX IF NOT EXISTS idx_meter_readings_meter_id ON meter_readings(meter_id);
CREATE INDEX IF NOT EXISTS idx_meter_readings_user_id ON meter_readings(user_id);
CREATE INDEX IF NOT EXISTS idx_meter_readings_sync_status ON meter_readings(sync_status);
CREATE INDEX IF NOT EXISTS idx_water_meters_area_id ON water_meters(area_id);
CREATE INDEX IF NOT EXISTS idx_water_meters_type_id ON water_meters(meter_type_id);
CREATE INDEX IF NOT EXISTS idx_water_meters_status ON water_meters(status);
CREATE INDEX IF NOT EXISTS idx_tasks_user_id ON tasks(assigned_user_id);
CREATE INDEX IF NOT EXISTS idx_tasks_status ON tasks(status);
CREATE INDEX IF NOT EXISTS idx_sync_logs_table_name ON sync_logs(table_name);
CREATE INDEX IF NOT EXISTS idx_sync_logs_status ON sync_logs(sync_status);
```

## Sample Initial Data (Optional)

You can populate the asset database with initial reference data:

```sql
-- Sample meter types
INSERT INTO meter_types (type_code, type_name, manufacturer, model, accuracy_class) VALUES
('RESIDENTIAL_15MM', 'Residential 15mm', 'Kamstrup', 'MULTICAL 21', 'Class 2'),
('RESIDENTIAL_20MM', 'Residential 20mm', 'Kamstrup', 'MULTICAL 21', 'Class 2'),
('COMMERCIAL_25MM', 'Commercial 25mm', 'Sensus', 'iPERL', 'Class 1'),
('COMMERCIAL_40MM', 'Commercial 40mm', 'Sensus', 'iPERL', 'Class 1'),
('INDUSTRIAL_50MM', 'Industrial 50mm', 'Itron', 'Aquadis+', 'Class 1');

-- Sample areas
INSERT INTO areas (area_code, area_name, description) VALUES
('CENTRAL', 'Central District', 'Main urban area'),
('NORTH', 'North District', 'Northern residential area'),
('SOUTH', 'South District', 'Southern commercial area'),
('EAST', 'East District', 'Eastern industrial area'),
('WEST', 'West District', 'Western residential area');

-- Sample admin user (for testing - use proper password hashing in production)
INSERT INTO users (username, password_hash, full_name, email, role) VALUES
('admin', '$2b$10$...', 'System Administrator', '<EMAIL>', 'admin');

-- Sample app settings
INSERT INTO app_settings (key, value, type) VALUES
('app_version', '1.0.0', 'string'),
('sync_interval', '300', 'number'),
('auto_sync_enabled', 'true', 'boolean'),
('photo_quality', '0.8', 'number'),
('gps_accuracy_threshold', '10', 'number');
```

## Real Production Data

For production use, you would typically:

1. **Export from existing system**: Use your current water management system to export:
   - All meter types and specifications
   - Service areas and geographical boundaries
   - Customer and meter information
   - User accounts and permissions
   - System configuration settings

2. **Import via SQL scripts**: Create INSERT statements for all your real data

3. **Validate data integrity**: Ensure all foreign key relationships are correct

## Testing the Asset Database

1. **Create the asset database** with sample data
2. **Place it in the assets folder**
3. **Uninstall the app** completely from the device/emulator
4. **Reinstall and run** the app
5. **Check logs** to confirm "Pre-filled database copied successfully from assets"
6. **Verify data** is available immediately on first launch

## Version Management

The asset database should always have version 1. When you need to update the schema or data:

1. **Create migration scripts** in the EnhancedDatabaseManager
2. **Increment CURRENT_DB_VERSION**
3. **Existing installations** will automatically upgrade via migrations
4. **New installations** will copy the asset database and then apply any newer migrations

This approach ensures both new and existing installations stay synchronized with the latest database schema. 