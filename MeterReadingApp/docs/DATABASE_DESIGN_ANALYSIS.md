# Water Meter Database Design Analysis

Based on CORDE project database implementation patterns

## CORDE Database Design Analysis

### 1. Core Architecture Features

#### 1.1 Offline-First Design
- **Pre-populated Database**: CORDE uses a pre-built SQLite database (`corde_mobile.db`) stored in Android assets
- **First Launch Initialization**: App copies the asset database to app's data directory on first run
- **WAL Mode**: Uses Write-Ahead Logging for better concurrency and performance

#### 1.2 Version Management System
- **Version Table**: `db_version` table tracks current database schema version
- **Migration Scripts**: Structured migration system with version-specific SQL scripts
- **Incremental Updates**: Only runs migrations for versions higher than current

#### 1.3 Database Initialization Flow
```typescript
// CORDE's initialization process:
1. Check if database exists in app data directory
2. If not exists: Copy pre-built database from assets
3. Open database connection
4. Execute PRAGMA statements for optimization
5. Check current version from db_version table
6. Create tables if version is 1 (first time)
7. Run migration scripts for version upgrades
8. Update version number
```

### 2. Key Implementation Patterns

#### 2.1 PRAGMA Optimization
```sql
PRAGMA foreign_keys = ON;
PRAGMA journal_mode = WAL;
PRAGMA busy_timeout = 5000;
PRAGMA synchronous = NORMAL;
PRAGMA cache_size = 100000;
PRAGMA temp_store = MEMORY;
```

#### 2.2 Transaction Management
- Non-transactional queries for PRAGMA statements
- Transactional queries for data operations
- Proper error handling and rollback mechanisms

#### 2.3 File Management
- Asset database copying with error handling
- WAL and SHM file cleanup for consistency
- Database file integrity checks

### 3. Water Meter Project Requirements

#### 3.1 Current Implementation Gaps
1. **No Pre-populated Database**: Currently creates empty database
2. **No Version Management**: No migration system
3. **Basic Initialization**: Simple table creation without optimization
4. **No Asset Database**: Missing initial data structure

#### 3.2 Required Improvements
1. **Asset Database Creation**: Pre-built database with initial data
2. **Version Management**: Implement migration system
3. **Optimization**: Add PRAGMA statements
4. **Initial Data**: Pre-populate reference data (meter types, locations, users)

### 4. Proposed Implementation Plan

#### 4.1 Database Structure Enhancement
```sql
-- Version management table
CREATE TABLE IF NOT EXISTS db_version (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  version INTEGER
);

-- Enhanced user table with sync capabilities
CREATE TABLE IF NOT EXISTS users (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  username TEXT UNIQUE NOT NULL,
  password_hash TEXT NOT NULL,
  full_name TEXT,
  email TEXT,
  role TEXT DEFAULT 'reader',
  is_active INTEGER DEFAULT 1,
  last_login_date TEXT,
  sync_status TEXT DEFAULT 'synced',
  created_at TEXT DEFAULT CURRENT_TIMESTAMP,
  updated_at TEXT DEFAULT CURRENT_TIMESTAMP
);

-- Water meter types reference table
CREATE TABLE IF NOT EXISTS meter_types (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  type_code TEXT UNIQUE NOT NULL,
  type_name TEXT NOT NULL,
  description TEXT,
  manufacturer TEXT,
  model TEXT,
  flow_rate_min REAL,
  flow_rate_max REAL,
  accuracy_class TEXT,
  is_active INTEGER DEFAULT 1
);

-- Locations/Areas reference table
CREATE TABLE IF NOT EXISTS areas (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  area_code TEXT UNIQUE NOT NULL,
  area_name TEXT NOT NULL,
  description TEXT,
  parent_area_id INTEGER,
  latitude REAL,
  longitude REAL,
  is_active INTEGER DEFAULT 1,
  FOREIGN KEY (parent_area_id) REFERENCES areas (id)
);

-- Enhanced water meters table
CREATE TABLE IF NOT EXISTS water_meters (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  meter_number TEXT UNIQUE NOT NULL,
  meter_type_id INTEGER,
  area_id INTEGER,
  customer_name TEXT,
  address TEXT,
  latitude REAL,
  longitude REAL,
  installation_date TEXT,
  last_reading REAL,
  last_reading_date TEXT,
  status TEXT DEFAULT 'active',
  sync_status TEXT DEFAULT 'synced',
  created_at TEXT DEFAULT CURRENT_TIMESTAMP,
  updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (meter_type_id) REFERENCES meter_types (id),
  FOREIGN KEY (area_id) REFERENCES areas (id)
);

-- Enhanced meter readings table
CREATE TABLE IF NOT EXISTS meter_readings (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  uuid TEXT UNIQUE NOT NULL,
  meter_id INTEGER NOT NULL,
  user_id INTEGER NOT NULL,
  reading_value REAL NOT NULL,
  previous_reading REAL,
  consumption REAL,
  reading_date TEXT NOT NULL,
  latitude REAL,
  longitude REAL,
  photo_path TEXT,
  notes TEXT,
  reading_method TEXT DEFAULT 'manual',
  quality_score REAL,
  verification_status TEXT DEFAULT 'pending',
  sync_status TEXT DEFAULT 'pending',
  created_at TEXT DEFAULT CURRENT_TIMESTAMP,
  updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (meter_id) REFERENCES water_meters (id),
  FOREIGN KEY (user_id) REFERENCES users (id)
);

-- Sync management table
CREATE TABLE IF NOT EXISTS sync_logs (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  table_name TEXT NOT NULL,
  record_id TEXT,
  operation_type TEXT NOT NULL, -- 'INSERT', 'UPDATE', 'DELETE'
  sync_status TEXT DEFAULT 'pending', -- 'pending', 'synced', 'failed'
  error_message TEXT,
  retry_count INTEGER DEFAULT 0,
  last_sync_attempt TEXT,
  created_at TEXT DEFAULT CURRENT_TIMESTAMP
);
```

#### 4.2 Migration System Implementation
```typescript
interface Migration {
  version: number;
  description: string;
  script: string;
}

const migrations: Migration[] = [
  {
    version: 2,
    description: "Add meter types reference table",
    script: `
      CREATE TABLE IF NOT EXISTS meter_types (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        type_code TEXT UNIQUE NOT NULL,
        type_name TEXT NOT NULL,
        description TEXT,
        manufacturer TEXT,
        model TEXT,
        is_active INTEGER DEFAULT 1
      );
      
      INSERT INTO meter_types (type_code, type_name, manufacturer, model) VALUES
      ('RESIDENTIAL_15MM', 'Residential 15mm', 'Kamstrup', 'MULTICAL 21'),
      ('RESIDENTIAL_20MM', 'Residential 20mm', 'Kamstrup', 'MULTICAL 21'),
      ('COMMERCIAL_25MM', 'Commercial 25mm', 'Sensus', 'iPERL'),
      ('COMMERCIAL_40MM', 'Commercial 40mm', 'Sensus', 'iPERL');
    `
  },
  {
    version: 3,
    description: "Add areas reference table and update water_meters",
    script: `
      CREATE TABLE IF NOT EXISTS areas (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        area_code TEXT UNIQUE NOT NULL,
        area_name TEXT NOT NULL,
        description TEXT,
        parent_area_id INTEGER,
        is_active INTEGER DEFAULT 1,
        FOREIGN KEY (parent_area_id) REFERENCES areas (id)
      );
      
      INSERT INTO areas (area_code, area_name, description) VALUES
      ('CENTRAL', 'Central District', 'Main urban area'),
      ('NORTH', 'North District', 'Northern residential area'),
      ('SOUTH', 'South District', 'Southern commercial area'),
      ('EAST', 'East District', 'Eastern industrial area'),
      ('WEST', 'West District', 'Western residential area');
      
      ALTER TABLE water_meters ADD COLUMN meter_type_id INTEGER;
      ALTER TABLE water_meters ADD COLUMN area_id INTEGER;
    `
  }
  // Add more migrations as needed
];
```

#### 4.3 Asset Database Creation
1. **Create Initial Database**: Build pre-populated database with reference data
2. **Asset Placement**: Place database file in `android/app/src/main/assets/`
3. **Copy Mechanism**: Implement asset copying on first app launch

#### 4.4 Enhanced Database Manager
- Implement CORDE-style initialization with asset copying
- Add version management and migration system
- Include PRAGMA optimization statements
- Add proper error handling and recovery mechanisms

### 5. Implementation Benefits

#### 5.1 Offline Capability
- Pre-populated reference data available immediately
- No network dependency for basic functionality
- Faster app startup with existing data structure

#### 5.2 Data Integrity
- Version-controlled schema changes
- Structured migration path for updates
- Foreign key constraints for data consistency

#### 5.3 Performance
- WAL mode for better concurrency
- Optimized PRAGMA settings
- Efficient query execution

#### 5.4 Maintainability
- Clear migration history
- Structured database evolution
- Easy rollback and recovery options

### 6. Implementation Status ✅

1. **✅ Enhanced DatabaseManager Implemented**: Complete with CORDE-style architecture
2. **✅ Version Management**: Full migration system with incremental updates
3. **✅ Asset Database Support**: Automatic copying from assets with fallback
4. **✅ PRAGMA Optimization**: WAL mode, foreign keys, and performance tuning
5. **✅ App Integration**: Updated App.tsx to use enhanced database manager

### 7. Asset Database Creation

Use the provided SQL script to create your asset database:

```bash
# Create the database using SQLite command line
sqlite3 water_meter_app.db < scripts/create_asset_database.sql

# Copy to Android assets folder
cp water_meter_app.db android/app/src/main/assets/
```

### 8. Key Features Implemented

- **Offline-First Design**: Pre-populated database for immediate functionality
- **Automatic Asset Copying**: RNFS-based copying with error handling
- **WAL/SHM File Management**: Consistent file state handling
- **Migration System**: Version-controlled database schema updates
- **Performance Optimization**: Indexes and PRAGMA statements
- **Error Recovery**: Graceful fallback to fresh database creation

### 9. Usage

The enhanced database manager automatically:
1. **First Launch**: Copies asset database to app directory
2. **Subsequent Launches**: Uses existing database with version checks
3. **Version Upgrades**: Applies incremental migration scripts
4. **Error Handling**: Falls back to fresh creation if asset copy fails

This implementation provides the water meter application with a robust, offline-capable database system that matches CORDE's proven architecture while being specifically designed for water meter management workflows. 