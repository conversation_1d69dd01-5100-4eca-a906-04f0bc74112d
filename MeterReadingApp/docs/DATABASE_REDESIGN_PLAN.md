# 移动端数据库重构方案
# 完全对齐后端C#模型结构 - 数据源重构版

## 📊 重构目标

### 核心目标
- **完全对齐后端C#模型**: 确保移动端SQLite与后端PostgreSQL数据结构一致
- **数据源架构调整**: 从直接调用接口改为"接口同步→数据库→界面显示"模式
- **保持现有界面**: 所有UI元素和布局保持不变，只做字段数据源对齐
- **专注核心业务**: 支持现有的task list → task详情 → 读表 → 保存提交流程

---

## 🗄️ 新数据库表设计

### 1. 基础审计字段 (BaseEntity 模式)
```sql
-- 所有表都包含的基础字段
id INTEGER PRIMARY KEY AUTOINCREMENT,
created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
is_deleted INTEGER DEFAULT 0,
created_by TEXT DEFAULT '',
updated_by TEXT DEFAULT ''
```

### 2. Users 表 (完全对齐后端User模型)
```sql
CREATE TABLE users (
  -- BaseEntity 字段
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
  is_deleted INTEGER DEFAULT 0,
  created_by TEXT DEFAULT '',
  updated_by TEXT DEFAULT '',
  
  -- User 特有字段
  username TEXT UNIQUE NOT NULL,
  full_name TEXT DEFAULT '',
  email TEXT DEFAULT '',
  person_id INTEGER DEFAULT 0,
  fin_co_code TEXT DEFAULT '',
  mobile_phone TEXT DEFAULT '',
  profit_centre_code TEXT DEFAULT '',
  employee_no TEXT DEFAULT '',
  is_authenticated INTEGER DEFAULT 0,
  last_login TEXT,
  created_date TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_date TEXT DEFAULT CURRENT_TIMESTAMP
);
```

### 3. WaterMeters 表 (对齐后端WaterMeter模型 - 核心字段)
```sql
CREATE TABLE water_meters (
  -- BaseEntity 字段
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
  is_deleted INTEGER DEFAULT 0,
  created_by TEXT DEFAULT '',
  updated_by TEXT DEFAULT '',
  
  -- 核心标识字段
  asset_id TEXT,                    -- AssetId
  serial_number TEXT NOT NULL,     -- SerialNumber (物理表号)
  account_number TEXT,             -- AccountNumber
  
  -- 位置信息 (简化)
  location TEXT NOT NULL DEFAULT '', -- Location 
  address TEXT DEFAULT '',         -- Address
  latitude REAL,                   -- GPS坐标
  longitude REAL,                  -- GPS坐标
  
  -- 基本水表信息
  meter_type TEXT NOT NULL DEFAULT 'Water',
  status TEXT NOT NULL DEFAULT 'Active',
  customer_name TEXT,              -- CustomerName
  last_reading REAL,               -- LastReading
  last_reading_date TEXT,          -- LastReadingDate
  
  -- 同步状态
  sync_status TEXT DEFAULT 'Synced', -- SyncStatus
  last_sync_date TEXT              -- LastSyncDate
);
```

### 4. WorkTasks 表 (对齐后端WorkTask模型 - 支持现有TaskList功能)
```sql
CREATE TABLE work_tasks (
  -- BaseEntity 字段
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
  is_deleted INTEGER DEFAULT 0,
  created_by TEXT DEFAULT '',
  updated_by TEXT DEFAULT '',
  
  -- 任务基本信息 (TaskList界面需要的字段)
  name TEXT NOT NULL,
  description TEXT,
  status TEXT NOT NULL DEFAULT 'Pending',   -- Pending, InProgress, Completed, Cancelled, Failed, Skipped
  priority TEXT NOT NULL DEFAULT 'Medium',  -- Low, Medium, High, Critical
  type TEXT NOT NULL DEFAULT 'MeterReading', -- MeterReading, Inspection, Maintenance, Installation
  assigned_user_id INTEGER,                 -- 分配给的用户ID
  meter_id INTEGER,                         -- 关联的水表ID
  
  -- 时间信息 (TaskList和TaskDetail需要)
  due_date TEXT,
  scheduled_date TEXT,
  completed_date TEXT,
  
  -- 位置信息 (TaskDetail显示)
  location TEXT,
  service_address TEXT,
  
  -- 任务相关信息 (TaskDetail显示)
  instructions TEXT,
  notes TEXT,
  
  -- 外键约束 (简化)
  FOREIGN KEY (assigned_user_id) REFERENCES users (id),
  FOREIGN KEY (meter_id) REFERENCES water_meters (id)
);
```

### 5. Readings 表 (对齐后端Reading模型 - 支持读表功能)
```sql
CREATE TABLE readings (
  -- BaseEntity 字段
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
  is_deleted INTEGER DEFAULT 0,
  created_by TEXT DEFAULT '',
  updated_by TEXT DEFAULT '',
  
  -- 关联信息
  meter_id INTEGER NOT NULL,
  user_id INTEGER NOT NULL,
  task_id INTEGER,                         -- 关联的任务ID
  
  -- 读数信息 (读表界面核心功能)
  reading_value REAL NOT NULL,             -- 读数值
  reading_date TEXT NOT NULL,              -- 读数日期时间
  previous_reading REAL,                   -- 上次读数用于验证
  
  -- 读数方法和质量
  reading_method TEXT NOT NULL DEFAULT 'Manual', -- Manual, OCR
  ocr_confidence INTEGER,                  -- OCR置信度 (0-100)
  
  -- GPS信息 (读表时记录)
  latitude REAL,                           -- GPS纬度
  longitude REAL,                          -- GPS经度
  gps_accuracy REAL,                       -- GPS精度 (米)
  gps_timestamp TEXT,                      -- GPS时间戳
  
  -- 照片信息 (读表时拍照)
  photo_filename TEXT,                     -- 照片文件名
  photo_file_path TEXT,                    -- 照片文件路径
  photo_uploaded INTEGER DEFAULT 0,       -- 照片是否已上传
  
  -- 外键约束
  FOREIGN KEY (meter_id) REFERENCES water_meters (id),
  FOREIGN KEY (user_id) REFERENCES users (id),
  FOREIGN KEY (task_id) REFERENCES work_tasks (id)
);
```

### 6. MeterReadingSyncLogs 表 (同步日志)
```sql
CREATE TABLE meter_reading_sync_logs (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  local_reading_id INTEGER,                -- 本地reading ID
  backend_reading_id INTEGER,              -- 后端reading ID
  task_id INTEGER,                         -- 关联的task ID
  meter_id INTEGER,                        -- 关联的meter ID
  sync_status TEXT NOT NULL DEFAULT 'pending', -- pending, synced, error
  sync_type TEXT NOT NULL DEFAULT 'reading',   -- reading, photo, task_update
  sync_attempts INTEGER DEFAULT 0,
  last_sync_attempt TEXT,
  last_sync_time TEXT,
  error_message TEXT,
  server_response TEXT,
  created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
  
  FOREIGN KEY (local_reading_id) REFERENCES readings (id),
  FOREIGN KEY (task_id) REFERENCES work_tasks (id),
  FOREIGN KEY (meter_id) REFERENCES water_meters (id)
);
```

### 7. 备注：简化设计
```
不需要以下复杂表结构：
- WorkPackages 表 - 移动端不需要工作包管理
- Routes 表 - 移动端不需要路线管理
- 复杂的关联表 - 专注于核心读表业务

移动端专注于简单业务流程：
TaskList → TaskDetail → MeterReading → Save&Submit
```

---

## 🔄 Phase 2: 数据源架构调整和同步逻辑重构 (3-4天)

### 2.1 数据源架构调整

#### 核心改变：从直接调用接口 → 接口同步到数据库 → 界面读取数据库
```
旧架构：TaskListScreen → 直接调用API → 显示数据
新架构：TaskListScreen → 调用API同步最新数据到DB → 从DB读取显示
```

#### 重构服务结构：
```
services/
├── business/
│   ├── WaterMeterBusinessService.ts     (重构 - 对齐新表结构)
│   ├── TaskBusinessService.ts           (重构 - 支持WorkTask模型)
│   ├── UserBusinessService.ts           (重构 - 对齐User模型)
│   ├── ReadingBusinessService.ts        (新增 - Reading业务逻辑)
│   └── index.ts                         (简化导出)
├── sync/
│   ├── BasicDataSyncService.ts          (重构)
│   ├── TaskDataSyncService.ts           (重构 - 支持最新数据获取)
│   ├── BusinessDataSyncService.ts       (重构)
│   └── BackgroundSync.ts                (重构)
└── data/
    ├── repositories/
    │   ├── WaterMeterRepository.ts       (重构 - 对齐新表)
    │   ├── TaskRepository.ts             (重构 - 对齐新表)
    │   ├── UserRepository.ts             (重构 - 对齐新表)
    │   ├── ReadingRepository.ts          (重构 - 对齐新表)
    │   └── index.ts                      (简化导出)
    └── models/
        ├── SyncLog.ts                    (重构)
        └── index.ts                      (重构)
```

### 2.2 界面数据源调整

#### TaskListScreen 数据源改变：
```typescript
// 旧方式：直接调用API
const tasks = await TaskService.getMyAssignments();

// 新方式：先同步再读取数据库  
await TaskDataSyncService.syncRecentTasks(7); // 同步最近7天数据
const tasks = await TaskRepository.getUserTasks(userId);
```

#### 核心API接口对齐：
- `/api/mobile/tasks/sync/all` → 同步到work_tasks表
- `/api/water-meters` → 同步到water_meters表  
- `/api/user` → 同步到users表
- 现有的reading提交接口保持不变

---

## 🧪 Phase 3: 类型定义和验证 (1-2天)

### 3.1 TypeScript类型重构

#### 新增/重构类型定义：
```typescript
// types/BackendModels.ts
export interface BackendWaterMeter {
  id: number;
  assetId?: string;
  serialNumber: string;
  accountNumber?: string;
  bookNumber?: string;
  // ... 完整字段映射
}

export interface BackendWorkTask {
  id: number;
  name: string;
  assignedUserId?: number;
  meterId?: number;
  workPackageId?: number;
  // ... 完整字段映射
}

export interface BackendReading {
  id: number;
  meterId: number;
  userId: number;
  readingValue: number;
  // ... 完整字段映射
}
```

### 3.2 数据验证重构

#### 新增验证规则：
- 字段长度验证 (对齐C#模型的StringLength特性)
- 必填字段验证 (对齐C#模型的Required特性)
- 数据类型验证 (decimal精度、日期格式等)
- 外键完整性验证

---

## 🎯 Phase 4: UI界面数据源适配 (1-2天)

### 4.1 界面数据源更新 (保持现有布局和UI元素)

#### 需要调整数据源的界面：
- **TaskListScreen** → 数据源从API改为数据库，字段对齐WorkTask模型
- **TaskDetailScreen** → 数据源从API改为数据库，显示task详细信息
- **MeterReadingScreen** → 保存数据到Reading表，字段对齐
- **SyncScreen** → 适配新的同步服务，统计数据从数据库获取

### 4.2 界面调整原则

#### 严格限制：
- ❌ 不新增任何界面
- ❌ 不调整现有布局
- ❌ 不改变现有UI元素
- ✅ 只调整数据源：从API → 数据库
- ✅ 只做字段对齐：确保显示的数据正确

---

## ⚡ Phase 5: 性能优化和测试 (2-3天)

### 5.1 数据库性能优化

#### 索引策略：
```sql
-- 关键查询索引
CREATE INDEX idx_water_meters_serial_number ON water_meters(serial_number);
CREATE INDEX idx_water_meters_asset_id ON water_meters(asset_id);
CREATE INDEX idx_work_tasks_assigned_user_id ON work_tasks(assigned_user_id);
CREATE INDEX idx_work_tasks_meter_id ON work_tasks(meter_id);
CREATE INDEX idx_readings_meter_id ON readings(meter_id);
CREATE INDEX idx_readings_user_id ON readings(user_id);
CREATE INDEX idx_sync_logs_sync_status ON meter_reading_sync_logs(sync_status);
```

### 5.2 同步性能优化

#### 优化策略：
- 批量数据处理 (50-100条记录/批次)
- 增量同步支持
- 压缩传输优化
- 后台同步间隔优化

---

## 📋 实施计划总结

### 时间线：
- **总工期**: 11-15个工作日
- **里程碑**: 
  - Day 3: 数据库表结构完成
  - Day 7: 同步逻辑重构完成
  - Day 9: 类型定义和验证完成
  - Day 12: UI界面适配完成
  - Day 15: 性能优化和测试完成

### 风险控制：
- 保留旧表结构作为备份
- 分阶段迁移，支持回滚
- 充分测试数据完整性
- 与后端团队密切协调

### 成功标准：
- ✅ 核心表结构与后端C#模型对齐（专注于现有功能）
- ✅ 数据源架构调整成功：接口→数据库→界面
- ✅ 现有移动端功能完全无损失
- ✅ 界面布局和元素保持100%不变
- ✅ 支持TaskList→TaskDetail→Reading→Submit核心流程

---

**下一步行动**：请确认此重构方案，我将立即开始Phase 1的实施。 