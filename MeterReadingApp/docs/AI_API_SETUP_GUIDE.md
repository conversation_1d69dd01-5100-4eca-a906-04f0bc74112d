# AI API 配置指南

## 📋 概述

本指南帮助您配置水表识别应用的AI API服务。应用支持两种高精度AI识别服务：
- **Claude 3.5 Sonnet** (Anthropic) - 推荐首选
- **GPT-4V** (OpenAI) - 备用选择

## 🔑 获取API密钥

### 1. Claude API密钥 (推荐)

1. 访问 [Anthropic Console](https://console.anthropic.com/)
2. 注册账户并完成验证
3. 前往 **API Keys** 页面
4. 点击 **"Create Key"**
5. 复制密钥 (格式: `sk-ant-api03-...`)

**⚠️ 重要提醒**: 
- ChatGPT/Claude订阅 ≠ API访问权限
- API使用需要单独充值账户余额
- 建议首次充值 $20-50 用于测试

### 2. OpenAI API密钥

1. 访问 [OpenAI Platform](https://platform.openai.com/api-keys)
2. 登录您的OpenAI账户
3. 点击 **"Create new secret key"**
4. 复制密钥 (格式: `sk-proj-...`)

## ⚙️ 配置步骤

### 1. 打开配置文件

导航到: `MeterReadingApp/src/config/ai-config.ts`

### 2. 配置Claude API

```typescript
claude: {
  service: 'claude',
  // 👇 替换为您的真实密钥
  apiKey: 'sk-ant-api03-您的密钥',
  endpoint: 'https://api.anthropic.com/v1/messages',
  model: 'claude-3-5-sonnet-********',
  enabled: true  // 👈 设置为 true 启用
}
```

### 3. 配置OpenAI API

```typescript
gpt4v: {
  service: 'gpt4v',
  // 👇 替换为您的真实密钥
  apiKey: 'sk-proj-您的密钥',
  endpoint: 'https://api.openai.com/v1/chat/completions',
  model: 'gpt-4o',
  enabled: true  // 👈 设置为 true 启用
}
```

## 💰 使用成本

### Claude API
- **图像识别**: ~$0.02-0.05 每次识别
- **月度估算**: 200次识别 ≈ $4-10

### OpenAI GPT-4V
- **图像识别**: ~$0.01-0.03 每次识别  
- **月度估算**: 200次识别 ≈ $2-6

## 🔧 验证配置

### 1. 启动应用查看日志

应用启动时会自动测试API连接：

```
✅ AIApiStrategy: API配置验证成功
🔑 可用服务: claude, gpt4v
🧪 AIApiStrategy: Testing API connections...
✅ claude: Claude API连接成功！
✅ gpt4v: OpenAI API连接成功！
```

### 2. 测试识别功能

1. 打开相机功能
2. 拍摄水表照片
3. 查看识别结果和状态信息

## 🚨 常见问题与故障排除

### ❌ Claude API错误

#### 400 Bad Request
**可能原因**:
- API密钥格式错误
- 请求体格式问题
- 模型名称错误

**解决方案**:
```bash
# 检查API密钥格式
sk-ant-api03-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx

# 确认模型名称
claude-3-5-sonnet-********
```

#### 401 Unauthorized
**可能原因**:
- API密钥无效或过期
- 密钥权限不足

**解决方案**:
1. 重新生成API密钥
2. 检查账户状态
3. 确认密钥复制完整

#### 429 Rate Limit
**可能原因**:
- 请求频率过高
- 账户配额用完

**解决方案**:
1. 降低请求频率
2. 检查账户余额
3. 升级配额限制

### ❌ OpenAI API错误

#### 429 Too Many Requests
**可能原因**:
- 配额不足 (最常见)
- 请求频率超限
- 账户余额不足

**解决方案**:
1. **检查账户余额**: [OpenAI Billing](https://platform.openai.com/account/billing)
2. **充值账户**: 建议充值 $20-50
3. **检查使用限制**: [OpenAI Usage](https://platform.openai.com/account/usage)

#### 401 Invalid API Key
**解决方案**:
1. 重新生成API密钥
2. 确认密钥完整复制
3. 检查密钥权限

#### 400 Bad Request
**可能原因**:
- 模型名称错误
- 请求格式问题

**解决方案**:
```typescript
// 确认使用正确的模型名称
model: 'gpt-4o'  // 不是 'gpt-4v' 或 'gpt-4-vision'
```

### 🔍 调试技巧

#### 1. 查看详细日志

应用会输出详细的API调试信息：

```
Claude API: Sending request to https://api.anthropic.com/v1/messages with model claude-3-5-sonnet-********
Claude API: Request body size: 125674 chars
Claude API: Response status: 200 OK
Claude API: Extracted reading: "12345"
```

#### 2. 常见日志错误

**网络问题**:
```
❌ AIApiStrategy: Network check failed
❌ AIApiStrategy: claude service failed: Network request failed
```

**API配额问题**:
```
❌ OpenAI API配额不足或请求过于频繁，请稍后再试或检查您的账户余额
```

**API密钥问题**:
```
❌ Claude API密钥无效
❌ OpenAI API密钥无效，请检查您的API密钥配置
```

#### 3. 测试单个API

您可以暂时禁用一个API来测试另一个：

```typescript
claude: {
  // ...
  enabled: false  // 暂时禁用Claude
},
gpt4v: {
  // ...
  enabled: true   // 只测试OpenAI
}
```

### 💡 优化建议

#### 1. 成本优化

- **优先级设置**: Claude → OpenAI (Claude通常更准确)
- **网络检测**: 网络差时自动切换到本地识别
- **重试机制**: 失败时自动降级到备用方案

#### 2. 性能优化

- **图像压缩**: 自动压缩大图片以减少API调用时间
- **超时设置**: 15秒超时避免长时间等待
- **缓存策略**: 相同图片避免重复识别

## 📞 支持

如果遇到问题：

1. **检查网络连接**: 确保设备可以访问API endpoints
2. **查看账户状态**: 确认API密钥有效且有余额
3. **重启应用**: 清除可能的缓存问题
4. **查看日志**: 寻找具体错误信息

### 测试连接脚本

您可以使用以下命令测试API连接：

```bash
# Claude API测试
curl -X POST https://api.anthropic.com/v1/messages \
  -H "x-api-key: YOUR_CLAUDE_KEY" \
  -H "anthropic-version: 2023-06-01" \
  -H "content-type: application/json" \
  -d '{"model":"claude-3-5-sonnet-********","max_tokens":10,"messages":[{"role":"user","content":"Hello"}]}'

# OpenAI API测试  
curl -X POST https://api.openai.com/v1/chat/completions \
  -H "Authorization: Bearer YOUR_OPENAI_KEY" \
  -H "Content-Type: application/json" \
  -d '{"model":"gpt-4o","messages":[{"role":"user","content":"Hello"}],"max_tokens":10}'
```

---

**✅ 配置完成后，您的水表识别准确率将达到95%以上！** 