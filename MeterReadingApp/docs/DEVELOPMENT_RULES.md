# MeterReadingApp 开发规则

## 包管理和构建规则

### 1. 使用yarn而非npm
- **所有安装依赖使用**: `yarn add [package-name]`
- **所有开发依赖安装使用**: `yarn add -D [package-name]`
- **启动应用使用**: `yarn android`
- **禁止使用**: npm, npx react-native

### 2. 平台支持规则
- **仅支持Android平台**: 项目只针对Android开发和测试
- **不考虑iOS**: 无需处理iOS相关配置、依赖或兼容性问题
- **Android优先**: 所有功能开发以Android为准

### 3. 构建和版本管理
- **当前版本**: 0.0.1
- **Release构建**: 使用 `yarn release`
- **版本更新**: 同时更新package.json和android/app/build.gradle中的versionName
- **APK文件名**: water-meter-{版本号}.apk (例如: water-meter-0.0.1.apk)
- **APK位置**: android/app/build/outputs/apk/release/water-meter-0.0.1.apk

### 4. 兼容性规则
- **禁止使用**: react-native-vector-icons (兼容性问题)
- **使用emoji代替图标**: 所有UI图标使用emoji (👤, 💧, 📷, 👁️, 🙈 等)
- **OCR功能**: 使用真实的@react-native-ml-kit/text-recognition，不是mock

## UI/UX 规则

### 5. 设计风格
- **登录界面**: 参考CORDE Mobile Application的设计风格
- **主题支持**: 支持明暗主题切换
- **响应式设计**: 支持不同屏幕尺寸

### 6. 组件规范
- **颜色属性**: 所有NativeBase组件必须明确设置颜色属性，避免""空值警告
- **TypeScript**: 严格使用TypeScript类型定义

## API和认证规则

### 7. 认证系统
- **API端点**: https://sicon-mnlweb.sicon.co.nz/WorkbenchLogTest/api
- **认证方式**: Basic Auth with base64编码
- **凭证存储**: 使用react-native-keychain安全存储
- **记住我功能**: 支持自动登录

### 8. 错误处理
- **网络错误**: 提供友好的用户提示
- **认证失败**: 明确的错误信息
- **OCR错误**: 详细的操作指导

## 其他规则

### 9. 代码质量
- **英文注释**: 所有代码注释使用英文
- **English UI**: 所有用户界面文字使用英文
- **English 硬编码**: 所有硬编码部分也都要英文，不要写中文
- **错误日志**: 详细的console.log和错误跟踪

---
**注意**: 这个文件会随着项目发展不断更新，请确保所有开发者遵循这些规则。 