# 水表管理系统同步架构设计文档

## 📋 架构概览

### 核心设计原则
- **职责单一**: 每个服务只负责自己的核心职责
- **业务分离**: 业务逻辑与同步调度完全分离
- **代码复用**: 手动操作和自动调度使用相同的业务逻辑
- **清晰分层**: 同步层只负责调度，不关心具体业务实现

### 三层同步架构
```
📱 同步架构体系
├── 🗃️ 基础数据同步 (BasicDataSync)
├── 📋 任务数据同步 (TaskDataSync)  
└── 💼 业务数据上传 (BusinessDataUpload)
```

## 🏗️ 详细架构设计

### 1. 基础数据同步层
**职责**: 同步不区分时间范围的静态数据
```typescript
// 基础数据业务逻辑
WaterMeterBusinessService {
  fetchAndSaveWaterMeters(): 获取并保存所有水表数据
  fetchAndSaveUsers(): 获取并保存所有用户数据
  fetchAndSaveAreas(): 获取并保存所有区域数据
}

// 基础数据同步调度
BasicDataSyncService {
  syncWaterMeters(): WaterMeterBusinessService.fetchAndSaveWaterMeters()
  syncUsers(): UserBusinessService.fetchAndSaveUsers()
  syncAreas(): AreaBusinessService.fetchAndSaveAreas()
  syncAllBasicData(): 调度所有基础数据同步
}
```

### 2. 任务数据同步层  
**职责**: 同步所有任务数据，支持分页和时间范围
```typescript
// 任务数据业务逻辑
TaskBusinessService {
  fetchAndSaveTasks(pages): 获取并保存任务数据
  fetchAndSaveTaskDetails(taskId): 获取并保存任务详情
  filterTasksByUser(userId): 按用户过滤任务（仅用于界面显示）
}

// 任务数据同步调度
TaskDataSyncService {
  syncTasks(pages): TaskBusinessService.fetchAndSaveTasks(pages)
  syncTaskDetails(taskId): TaskBusinessService.fetchAndSaveTaskDetails(taskId)
  syncTasksByDateRange(start, end): TaskBusinessService.fetchAndSaveTasksByDateRange(start, end)
}
```

### 3. 业务数据上传层
**职责**: 处理读表数据、照片、GPS信息的上传业务
```typescript
// 业务数据上传核心逻辑 (可被手动和自动调用)
BusinessDataUploadService {
  uploadReadingData(readingId): 上传读数数据 + GPS信息
  uploadPhoto(photoId): 上传照片文件
  updateTaskStatus(taskId, status): 更新任务状态
  uploadAllPendingData(): 上传所有待上传数据
}

// 业务数据同步调度 (只负责调度，不关心业务)
BusinessDataSyncService {
  manualUpload(): BusinessDataUploadService.uploadAllPendingData()
  autoUpload(): 检测网络 → BusinessDataUploadService.uploadAllPendingData()
  backgroundUpload(): 定时调用 → BusinessDataUploadService.uploadAllPendingData()
}
```

## 📂 文件结构设计

```
src/services/
├── business/                    # 业务逻辑层
│   ├── WaterMeterBusinessService.ts
│   ├── TaskBusinessService.ts
│   ├── UserBusinessService.ts
│   └── BusinessDataUploadService.ts
├── sync/                        # 同步调度层
│   ├── BasicDataSyncService.ts
│   ├── TaskDataSyncService.ts
│   ├── BusinessDataSyncService.ts
│   └── SyncOrchestrator.ts      # 统一调度器
└── background/                  # 后台服务层
    └── BackgroundSyncService.ts
```

## 🎯 实施计划

### Phase 1: 业务逻辑层 (Business Layer)
**目标**: 实现核心业务逻辑，专注单一职责

#### Step 1.1: 水表业务服务
```typescript
// src/services/business/WaterMeterBusinessService.ts
export class WaterMeterBusinessService {
  static async fetchAndSaveWaterMeters(): Promise<SyncResult> {
    // 1. 调用后端API获取水表数据
    // 2. 验证数据格式
    // 3. 批量保存到本地数据库
    // 4. 返回同步结果
  }
}
```

#### Step 1.2: 任务业务服务
```typescript
// src/services/business/TaskBusinessService.ts
export class TaskBusinessService {
  static async fetchAndSaveTasks(pages: number): Promise<SyncResult> {
    // 参考CORDE LogListService逻辑
    // 1. 分页获取任务数据
    // 2. 处理任务详情
    // 3. 保存到本地数据库
  }
  
  static async filterTasksByUser(userId: number): Promise<Task[]> {
    // 从本地数据库过滤当前用户任务
  }
}
```

#### Step 1.3: 业务数据上传服务
```typescript
// src/services/business/BusinessDataUploadService.ts
export class BusinessDataUploadService {
  static async uploadReadingData(readingId: number): Promise<UploadResult> {
    // 1. 获取本地读数数据
    // 2. 包装GPS、验证数据
    // 3. 调用后端API上传
    // 4. 更新本地同步状态
  }
  
  static async uploadPhoto(photoId: number): Promise<UploadResult> {
    // 1. 获取本地照片文件
    // 2. 压缩/优化照片
    // 3. 上传到后端
    // 4. 更新本地状态
  }
  
  static async uploadAllPendingData(): Promise<UploadResult[]> {
    // 统一入口：上传所有待上传数据
  }
}
```

### Phase 2: 同步调度层 (Sync Layer)
**目标**: 实现同步调度，只负责调用业务逻辑

#### Step 2.1: 基础数据同步调度
```typescript
// src/services/sync/BasicDataSyncService.ts
export class BasicDataSyncService {
  static async syncWaterMeters(): Promise<SyncResult> {
    return await WaterMeterBusinessService.fetchAndSaveWaterMeters();
  }
  
  static async syncAllBasicData(): Promise<SyncResult[]> {
    return await Promise.all([
      this.syncWaterMeters(),
      this.syncUsers(),
      this.syncAreas()
    ]);
  }
}
```

#### Step 2.2: 任务数据同步调度
```typescript
// src/services/sync/TaskDataSyncService.ts
export class TaskDataSyncService {
  static async syncTasks(pages: number = 10): Promise<SyncResult> {
    return await TaskBusinessService.fetchAndSaveTasks(pages);
  }
}
```

#### Step 2.3: 业务数据同步调度
```typescript
// src/services/sync/BusinessDataSyncService.ts
export class BusinessDataSyncService {
  static async manualUpload(): Promise<UploadResult[]> {
    return await BusinessDataUploadService.uploadAllPendingData();
  }
  
  static async autoUpload(): Promise<UploadResult[]> {
    if (await networkService.isNetworkConnected()) {
      return await BusinessDataUploadService.uploadAllPendingData();
    }
    return [];
  }
}
```

### Phase 3: 界面集成层 (UI Integration)
**目标**: 界面调用同步服务，展示同步状态

#### Step 3.1: Sync Data 界面
```typescript
// 界面只调用同步调度服务
const handleSyncBasicData = async () => {
  const result = await BasicDataSyncService.syncAllBasicData();
  updateSyncStatus(result);
};

const handleSyncTasks = async () => {
  const result = await TaskDataSyncService.syncTasks(10);
  updateSyncStatus(result);
};

const handleManualUpload = async () => {
  const result = await BusinessDataSyncService.manualUpload();
  updateUploadStatus(result);
};
```

#### Step 3.2: Task List 界面
```typescript
// 任务列表过滤
const loadMyTasks = async () => {
  const tasks = await TaskBusinessService.filterTasksByUser(currentUserId);
  setTasks(tasks);
};
```

#### Step 3.3: Meter Reading 界面
```typescript
// 读表提交
const handleSubmitReading = async () => {
  // 1. 保存到本地数据库
  const localId = await saveReadingLocally(readingData);
  
  // 2. 尝试立即上传
  await BusinessDataSyncService.autoUpload();
};
```

### Phase 4: 后台服务层 (Background Service)
**目标**: 后台定时任务，自动处理数据同步

#### Step 4.1: 后台同步服务
```typescript
// src/services/background/BackgroundSyncService.ts
export class BackgroundSyncService {
  static startBackgroundSync(): void {
    BackgroundTimer.setInterval(async () => {
      // 只调用业务数据同步，不关心具体实现
      await BusinessDataSyncService.autoUpload();
    }, 30000);
  }
}
```

## 📊 数据流设计

### 基础数据流
```
Web后端 → API → BusinessService → 本地数据库 → 界面显示
```

### 任务数据流  
```
Web后端 → API → BusinessService → 本地数据库 → 用户过滤 → 界面显示
```

### 业务数据流
```
界面操作 → 本地数据库 → BusinessDataUploadService → Web后端
                    ↓
              BackgroundSync (定时重试)
```

## 🎯 关键优势

1. **职责清晰**: 业务逻辑与同步调度完全分离
2. **代码复用**: 手动上传和自动上传使用相同业务逻辑
3. **易于测试**: 每个服务职责单一，便于单元测试
4. **易于维护**: 修改业务逻辑不影响同步调度
5. **扩展性强**: 新增同步功能只需添加对应的业务服务和调度服务

## 📝 实施检查清单

### Phase 1 完成标准
- [ ] WaterMeterBusinessService 实现完成
- [ ] TaskBusinessService 实现完成  
- [ ] BusinessDataUploadService 实现完成
- [ ] 所有业务服务单元测试通过

### Phase 2 完成标准
- [ ] BasicDataSyncService 实现完成
- [ ] TaskDataSyncService 实现完成
- [ ] BusinessDataSyncService 实现完成
- [ ] 同步调度层测试通过

### Phase 3 完成标准
- [ ] Sync Data 界面集成完成
- [ ] Task List 界面集成完成
- [ ] Meter Reading 界面集成完成
- [ ] 用户交互流程测试通过

### Phase 4 完成标准
- [ ] BackgroundSyncService 实现完成
- [ ] 后台定时任务正常运行
- [ ] 网络状态检测正常
- [ ] 离线到在线自动同步正常

---

**下一步**: 开始实施 Phase 1 - 业务逻辑层实现 