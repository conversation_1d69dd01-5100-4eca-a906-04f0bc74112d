# 📋 Release APK 日志查看完整指南

## 🎯 目标
在Windows环境下查看React Native Release APK的完整日志输出，用于调试生产环境问题。

---

## 🔧 1. 修改构建配置

### 1.1 启用Release版本调试
**文件**: `android/app/build.gradle`

```gradle
buildTypes {
    release {
        // Caution! In production, you need to generate your own keystore file.
        // see https://reactnative.dev/docs/signed-apk-android.
        signingConfig signingConfigs.debug
        minifyEnabled enableProguardInReleaseBuilds
        proguardFiles getDefaultProguardFile("proguard-android.txt"), "proguard-rules.pro"
        
        // 启用日志输出 - 用于调试
        debuggable true
        
        // 添加构建配置字段
        buildConfigField "boolean", "ENABLE_LOGGING", "true"
    }
}
```

### 1.2 保留Console日志
**文件**: `metro.config.js`

```javascript
const {getDefaultConfig, mergeConfig} = require('@react-native/metro-config');

const config = {
  transformer: {
    minifierConfig: {
      // 在Release版本中保留console.log
      keep_fnames: true,
      mangle: {
        keep_fnames: true,
      },
    },
  },
};

module.exports = mergeConfig(getDefaultConfig(__dirname), config);
```

---

## 📱 2. 构建调试版Release APK

### 2.1 使用专用构建脚本
**文件**: `build-debug-release.sh`

```bash
#!/bin/bash

# 构建可调试的Release版本
echo "🔧 Building debuggable release APK..."

# 清理之前的构建
echo "🧹 Cleaning previous builds..."
cd android
./gradlew clean
cd ..

# 清理Metro缓存
echo "🗑️ Clearing Metro cache..."
npx react-native start --reset-cache &
METRO_PID=$!
sleep 5
kill $METRO_PID

# 构建Release版本（但启用了调试）
echo "📦 Building release APK with debugging enabled..."
cd android
./gradlew assembleRelease
cd ..

# 检查构建结果
if [ -f "android/app/build/outputs/apk/release/water-meter-0.1.0.apk" ]; then
    echo "✅ Build successful!"
    echo "📱 APK location: android/app/build/outputs/apk/release/water-meter-0.1.0.apk"
    echo ""
    echo "🔍 This APK includes:"
    echo "   - Production API (124.157.93.142)"
    echo "   - Full logging enabled"
    echo "   - Debuggable flag enabled"
    echo ""
    echo "📋 To view logs after installation:"
    echo "   adb logcat | findstr \"ReactNativeJS\""
    echo "   adb logcat | findstr \"API_CONFIG\""
    echo "   adb logcat *:V | findstr \"com.meterreadingapp\""
else
    echo "❌ Build failed!"
    exit 1
fi
```

### 2.2 执行构建
```bash
chmod +x build-debug-release.sh
./build-debug-release.sh
```

---

## 🔍 3. 查看日志方法

### 3.1 Windows PowerShell 命令

#### 基础日志查看
```powershell
# 查看应用相关日志
adb shell logcat | Select-String "com.meterreadingapp"

# 查看React Native日志
adb shell logcat | Select-String "ReactNative"

# 查看API配置日志
adb shell logcat | Select-String "API_CONFIG"

# 查看数据库日志
adb shell logcat | Select-String "DatabaseManager"
```

#### 高级日志查看
```powershell
# 清除旧日志并实时查看
adb logcat -c
adb shell logcat | Select-String -Pattern "com.meterreadingapp|ReactNative|API_CONFIG"

# 保存所有日志到文件
adb shell logcat > app_logs.txt

# 查看特定级别的日志
adb shell logcat *:V | Select-String "com.meterreadingapp"
```

### 3.2 Windows CMD 命令

```cmd
# 查看应用日志
adb shell logcat | findstr "com.meterreadingapp"

# 查看React Native日志
adb shell logcat | findstr "ReactNative"

# 查看多个关键词
adb shell logcat | findstr "API_CONFIG DatabaseManager Environment"

# 保存日志
adb shell logcat > app_logs.txt
```

---

## 🎯 4. 关键日志标识

### 4.1 API配置日志
```
API_CONFIG: Environment check - __DEV__: false, isDev: false, Platform: android
API_CONFIG: Production mode - using production API: http://124.157.93.142/api
```

### 4.2 数据库初始化日志
```
DatabaseManager: Initializing database...
DatabaseManager: db_version table ensured
DatabaseManager: Current DB version: 0, Target: 0
🆕 Fresh database detected, creating all tables...
Creating table: users
Table users created successfully
✅ Fresh database setup completed
```

### 4.3 网络请求日志
```
🚀 Starting Request: {"url": "http://124.157.93.142/api/auth/login", ...}
✅ API Response: {...}
❌ Response Error: {...}
```

---

## 🛠️ 5. 故障排除

### 5.1 如果看不到日志
```powershell
# 尝试不同的日志级别
adb shell logcat *:V

# 检查设备连接
adb devices

# 重启adb服务
adb kill-server
adb start-server
```

### 5.2 常见问题
- **权限问题**: 确保启用了USB调试
- **日志被过滤**: 尝试使用 `*:V` 查看所有级别日志
- **应用崩溃**: 查看 `adb shell logcat | Select-String "FATAL"`

---

## 📋 6. 完整调试流程

1. **修改配置文件** (gradle + metro)
2. **构建调试版Release APK**
3. **安装APK到设备**
4. **启动日志监控**:
   ```powershell
   adb logcat -c
   adb shell logcat | Select-String "com.meterreadingapp"
   ```
5. **启动应用并操作**
6. **分析日志输出**

---

## ✅ 验证成功标志

看到以下日志说明配置成功：
- ✅ API地址正确显示生产环境
- ✅ 数据库表创建成功
- ✅ 网络请求日志可见
- ✅ 错误信息详细显示

这样就能在Release版本中获得与开发环境相同的日志可见性！

---

## 📝 注意事项

1. **生产环境**: 真正的生产版本应该移除 `debuggable true` 配置
2. **性能影响**: 启用调试会略微影响性能
3. **安全考虑**: 调试版本不应发布到应用商店
4. **日志敏感信息**: 确保日志中不包含敏感数据

---

**最后更新**: 2024-08-05
**适用版本**: React Native 0.72+, Android API 21+
