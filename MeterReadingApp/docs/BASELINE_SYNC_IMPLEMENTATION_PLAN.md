# 📋 移动端基线数据同步实施方案文档 (完整版)

## 🎯 项目概述

**目标**：基于现有的BaselineRecord表，在移动端实现基线数据同步，解决TaskList中LastReading显示为0的问题。

**核心原则**：
- 复用现有BaselineRecord表结构
- 实现完整的数据同步机制
- 提供robust的错误处理和日志记录
- 与后端设计完全对齐

---

## 🏗️ 总体架构设计

### 数据流向图
```mermaid
graph TD
    A[后端BaselineRecord表<br/>已存在] --> B[新增移动端同步API<br/>/api/mobile/baselines]
    B --> C[移动端baseline_records表<br/>镜像后端结构]
    C --> D[BaselineService<br/>GetEffectiveLastReading]
    E[TaskList显示] --> D
    F[TaskDetail显示] --> D
    G[BasicDataSyncService] --> B
    H[DebugScreen统计] --> C
```

### 系统层次架构
```mermaid
graph TB
    subgraph "移动端架构"
        UI[UI Layer]
        SVC[Service Layer]
        REPO[Repository Layer] 
        DB[SQLite Database]
    end
    
    subgraph "后端架构"
        API[API Controller]
        BSVC[Backend Service]
        EF[Entity Framework]
        PG[PostgreSQL]
    end
    
    UI --> SVC
    SVC --> REPO
    REPO --> DB
    SVC --> API
    API --> BSVC
    BSVC --> EF
    EF --> PG
```

---

## 📊 数据模型设计

### 后端BaselineRecord结构分析
```csharp
// 基于现有的 WaterMeterManagement/Models/BaselineRecord.cs
public class BaselineRecord : BaseEntity
{
    public int Id { get; set; }
    public int MeterId { get; set; }
    public virtual WaterMeter WaterMeter { get; set; }
    public DateTime BaselineDate { get; set; }
    public decimal BaselineValue { get; set; }
    public string BaselineType { get; set; } // Initial, Periodic, Correction, Migration
    public string Status { get; set; } = "Active"; // Active, Superseded, Invalid
    public string? ImportBatch { get; set; }
    public string? SourceFile { get; set; }
    public string DataSource { get; set; } = "Manual"; // CSV, Excel, Manual, AMS
    public string? ValidationNotes { get; set; }
    public bool IsValidated { get; set; }
    public DateTime? ValidatedDate { get; set; }
    public string? ValidatedBy { get; set; }
    public bool HasValidationErrors { get; set; }
    public string? ValidationErrors { get; set; }
    public bool IsAnomalous { get; set; }
    public string? AnomalyDescription { get; set; }
    public int? PreviousBaselineId { get; set; }
    public decimal? PreviousBaselineValue { get; set; }
    public decimal? VarianceFromPrevious { get; set; }
    public decimal? VariancePercentage { get; set; }
    public bool IsCorrected { get; set; }
    public DateTime? CorrectedDate { get; set; }
    public string? CorrectedBy { get; set; }
    public string? CorrectionReason { get; set; }
    public int ConfidenceLevel { get; set; } = 100;
    public string? Notes { get; set; }
    
    // BaseEntity properties
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
    public string CreatedBy { get; set; }
    public string UpdatedBy { get; set; }
    public bool IsDeleted { get; set; }
}
```

### 移动端SQLite表结构 (完整镜像)

```sql
-- 完整镜像后端BaselineRecord表结构
CREATE TABLE baseline_records (
    id INTEGER PRIMARY KEY,                    -- 直接使用后端ID
    meter_id INTEGER NOT NULL,                 -- 关联水表ID
    baseline_date TEXT NOT NULL,               -- 基线日期 (ISO格式)
    baseline_value REAL NOT NULL,              -- 基线读数值
    baseline_type TEXT NOT NULL,               -- 基线类型: Initial/Periodic/Correction/Migration
    status TEXT NOT NULL DEFAULT 'Active',     -- 状态: Active/Superseded/Invalid
    import_batch TEXT,                         -- 导入批次号
    source_file TEXT,                          -- 源文件名
    data_source TEXT DEFAULT 'Manual',         -- 数据源: CSV/Excel/Manual/AMS
    validation_notes TEXT,                     -- 验证备注
    is_validated INTEGER DEFAULT 0,            -- 是否已验证 (0/1)
    validated_date TEXT,                       -- 验证日期
    validated_by TEXT,                         -- 验证人
    has_validation_errors INTEGER DEFAULT 0,   -- 是否有验证错误
    validation_errors TEXT,                    -- 验证错误信息
    is_anomalous INTEGER DEFAULT 0,            -- 是否异常
    anomaly_description TEXT,                  -- 异常描述
    previous_baseline_id INTEGER,              -- 前一个基线ID
    previous_baseline_value REAL,              -- 前一个基线值
    variance_from_previous REAL,               -- 与前值的差异
    variance_percentage REAL,                  -- 差异百分比
    is_corrected INTEGER DEFAULT 0,            -- 是否被修正
    corrected_date TEXT,                       -- 修正日期
    corrected_by TEXT,                         -- 修正人
    correction_reason TEXT,                    -- 修正原因
    confidence_level INTEGER DEFAULT 100,      -- 置信度 (0-100)
    notes TEXT,                                -- 备注
    
    -- 基础字段 (BaseEntity)
    created_at TEXT NOT NULL,
    updated_at TEXT NOT NULL,
    created_by TEXT NOT NULL,
    updated_by TEXT NOT NULL,
    is_deleted INTEGER DEFAULT 0,
    
    -- 移动端同步字段
    sync_status TEXT DEFAULT 'Synced',         -- 同步状态
    last_sync_date TEXT,                       -- 最后同步时间
    
    FOREIGN KEY (meter_id) REFERENCES water_meters(id),
    FOREIGN KEY (previous_baseline_id) REFERENCES baseline_records(id)
);

-- 性能优化索引
CREATE INDEX IF NOT EXISTS idx_baseline_meter_id ON baseline_records(meter_id);
CREATE INDEX IF NOT EXISTS idx_baseline_status ON baseline_records(status);
CREATE INDEX IF NOT EXISTS idx_baseline_date ON baseline_records(baseline_date);
CREATE INDEX IF NOT EXISTS idx_baseline_type ON baseline_records(baseline_type);
CREATE INDEX IF NOT EXISTS idx_baseline_active ON baseline_records(meter_id, status, baseline_date DESC);
CREATE INDEX IF NOT EXISTS idx_baseline_sync ON baseline_records(sync_status, last_sync_date);
```

---

## 🔌 后端API设计

### 1. 新增移动端基线控制器

```csharp
// Controllers/MobileBaselineController.cs (新建)
[ApiController]
[Route("api/mobile/baselines")]
[Authorize]
public class MobileBaselineController : ControllerBase
{
    private readonly ApplicationDbContext _context;
    private readonly ILogger<MobileBaselineController> _logger;

    public MobileBaselineController(
        ApplicationDbContext context,
        ILogger<MobileBaselineController> logger)
    {
        _context = context;
        _logger = logger;
    }

    /// <summary>
    /// Get all baselines for mobile synchronization with pagination
    /// 获取所有基线数据用于移动端同步，支持分页
    /// </summary>
    [HttpGet("sync/all")]
    public async Task<ActionResult<object>> SyncAllBaselines(
        [FromQuery] int page = 1, 
        [FromQuery] int pageSize = 1000,
        [FromQuery] DateTime? lastSyncDate = null)
    {
        try
        {
            _logger.LogInformation("Mobile baseline sync requested - Page: {Page}, PageSize: {PageSize}", page, pageSize);

            var query = _context.BaselineRecords
                .Include(b => b.WaterMeter)
                .Where(b => !b.IsDeleted);

            // 增量同步：只获取更新的数据
            if (lastSyncDate.HasValue)
            {
                query = query.Where(b => b.UpdatedAt > lastSyncDate.Value);
                _logger.LogInformation("Incremental sync from {LastSyncDate}", lastSyncDate.Value);
            }

            var totalCount = await query.CountAsync();
            var baselines = await query
                .OrderBy(b => b.Id)
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .Select(b => new MobileBaselineDto
                {
                    Id = b.Id,
                    MeterId = b.MeterId,
                    MeterSerialNumber = b.WaterMeter.SerialNumber,
                    BaselineDate = b.BaselineDate,
                    BaselineValue = b.BaselineValue,
                    BaselineType = b.BaselineType,
                    Status = b.Status,
                    ImportBatch = b.ImportBatch,
                    SourceFile = b.SourceFile,
                    DataSource = b.DataSource,
                    ValidationNotes = b.ValidationNotes,
                    IsValidated = b.IsValidated,
                    ValidatedDate = b.ValidatedDate,
                    ValidatedBy = b.ValidatedBy,
                    HasValidationErrors = b.HasValidationErrors,
                    ValidationErrors = b.ValidationErrors,
                    IsAnomalous = b.IsAnomalous,
                    AnomalyDescription = b.AnomalyDescription,
                    PreviousBaselineId = b.PreviousBaselineId,
                    PreviousBaselineValue = b.PreviousBaselineValue,
                    VarianceFromPrevious = b.VarianceFromPrevious,
                    VariancePercentage = b.VariancePercentage,
                    IsCorrected = b.IsCorrected,
                    CorrectedDate = b.CorrectedDate,
                    CorrectedBy = b.CorrectedBy,
                    CorrectionReason = b.CorrectionReason,
                    ConfidenceLevel = b.ConfidenceLevel,
                    Notes = b.Notes,
                    CreatedAt = b.CreatedAt,
                    UpdatedAt = b.UpdatedAt,
                    CreatedBy = b.CreatedBy,
                    UpdatedBy = b.UpdatedBy
                })
                .ToListAsync();

            _logger.LogInformation("Retrieved {Count} baselines out of {Total} total", baselines.Count, totalCount);

            return Ok(new
            {
                data = baselines,
                pagination = new
                {
                    page,
                    pageSize,
                    totalCount,
                    totalPages = (int)Math.Ceiling((double)totalCount / pageSize),
                    hasMore = page * pageSize < totalCount
                },
                metadata = new
                {
                    syncTimestamp = DateTime.UtcNow,
                    apiVersion = "1.0"
                }
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving baselines for mobile sync");
            return StatusCode(500, new { Message = "Internal server error while syncing baselines" });
        }
    }

    /// <summary>
    /// Get baseline statistics for mobile dashboard
    /// 获取基线统计信息用于移动端仪表板
    /// </summary>
    [HttpGet("statistics")]
    public async Task<ActionResult<object>> GetBaselineStatistics()
    {
        try
        {
            var stats = await _context.BaselineRecords
                .Where(b => !b.IsDeleted)
                .GroupBy(b => 1)
                .Select(g => new
                {
                    TotalCount = g.Count(),
                    ActiveCount = g.Count(b => b.Status == "Active"),
                    ValidatedCount = g.Count(b => b.IsValidated),
                    AnomalousCount = g.Count(b => b.IsAnomalous),
                    CorrectedCount = g.Count(b => b.IsCorrected),
                    RecentCount = g.Count(b => b.CreatedAt >= DateTime.Today.AddDays(-30))
                })
                .FirstOrDefaultAsync();

            return Ok(stats ?? new
            {
                TotalCount = 0,
                ActiveCount = 0,
                ValidatedCount = 0,
                AnomalousCount = 0,
                CorrectedCount = 0,
                RecentCount = 0
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving baseline statistics");
            return StatusCode(500, new { Message = "Internal server error while getting statistics" });
        }
    }
}
```

### 2. 完整的DTO定义

```csharp
// DTOs/Mobile/MobileBaselineDto.cs (新建)
namespace WaterMeterManagement.DTOs.Mobile
{
    /// <summary>
    /// Mobile baseline data transfer object for synchronization
    /// 移动端基线数据传输对象，用于同步
    /// </summary>
    public class MobileBaselineDto
    {
        public int Id { get; set; }
        public int MeterId { get; set; }
        public string MeterSerialNumber { get; set; } = string.Empty;
        public DateTime BaselineDate { get; set; }
        public decimal BaselineValue { get; set; }
        public string BaselineType { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
        public string? ImportBatch { get; set; }
        public string? SourceFile { get; set; }
        public string DataSource { get; set; } = string.Empty;
        public string? ValidationNotes { get; set; }
        public bool IsValidated { get; set; }
        public DateTime? ValidatedDate { get; set; }
        public string? ValidatedBy { get; set; }
        public bool HasValidationErrors { get; set; }
        public string? ValidationErrors { get; set; }
        public bool IsAnomalous { get; set; }
        public string? AnomalyDescription { get; set; }
        public int? PreviousBaselineId { get; set; }
        public decimal? PreviousBaselineValue { get; set; }
        public decimal? VarianceFromPrevious { get; set; }
        public decimal? VariancePercentage { get; set; }
        public bool IsCorrected { get; set; }
        public DateTime? CorrectedDate { get; set; }
        public string? CorrectedBy { get; set; }
        public string? CorrectionReason { get; set; }
        public int ConfidenceLevel { get; set; }
        public string? Notes { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
        public string CreatedBy { get; set; } = string.Empty;
        public string UpdatedBy { get; set; } = string.Empty;
    }
}
```

---

## 📱 移动端完整实现

### 1. 完整的TypeScript接口定义

```typescript
// src/database/models/BaselineRecord.ts
export interface BaselineRecord {
  id: number;
  meter_id: number;
  baseline_date: string;
  baseline_value: number;
  baseline_type: string;
  status: string;
  import_batch?: string;
  source_file?: string;
  data_source: string;
  validation_notes?: string;
  is_validated: boolean;
  validated_date?: string;
  validated_by?: string;
  has_validation_errors: boolean;
  validation_errors?: string;
  is_anomalous: boolean;
  anomaly_description?: string;
  previous_baseline_id?: number;
  previous_baseline_value?: number;
  variance_from_previous?: number;
  variance_percentage?: number;
  is_corrected: boolean;
  corrected_date?: string;
  corrected_by?: string;
  correction_reason?: string;
  confidence_level: number;
  notes?: string;
  created_at: string;
  updated_at: string;
  created_by: string;
  updated_by: string;
}

export interface SyncBaselineData {
  id: number;
  meter_id: number;
  meter_serial_number: string;
  baseline_date: string;
  baseline_value: number;
  baseline_type: string;
  status: string;
  import_batch?: string;
  source_file?: string;
  data_source: string;
  validation_notes?: string;
  is_validated: boolean;
  validated_date?: string;
  validated_by?: string;
  has_validation_errors: boolean;
  validation_errors?: string;
  is_anomalous: boolean;
  anomaly_description?: string;
  previous_baseline_id?: number;
  previous_baseline_value?: number;
  variance_from_previous?: number;
  variance_percentage?: number;
  is_corrected: boolean;
  corrected_date?: string;
  corrected_by?: string;
  correction_reason?: string;
  confidence_level: number;
  notes?: string;
  created_at: string;
  updated_at: string;
  created_by: string;
  updated_by: string;
}

export interface BaselineStatistics {
  total: number;
  active: number;
  validated: number;
  anomalous: number;
  corrected: number;
  recent: number;
}
```

### 2. 完整的Repository实现

```typescript
// src/data/repositories/BaselineRepository.ts
import { DatabaseManager } from '../../database/DatabaseManager';
import { BaselineRecord, SyncBaselineData } from '../../database/models/BaselineRecord';

export class BaselineRepository {
  
  /**
   * Upsert a single baseline record
   * 插入或更新单个基线记录
   */
  static async upsert(baselineData: SyncBaselineData): Promise<void> {
    const sql = `
      INSERT OR REPLACE INTO baseline_records (
        id, meter_id, baseline_date, baseline_value, baseline_type, status,
        import_batch, source_file, data_source, validation_notes,
        is_validated, validated_date, validated_by, has_validation_errors, validation_errors,
        is_anomalous, anomaly_description, previous_baseline_id, previous_baseline_value,
        variance_from_previous, variance_percentage, is_corrected, corrected_date,
        corrected_by, correction_reason, confidence_level, notes,
        created_at, updated_at, created_by, updated_by,
        is_deleted, sync_status, last_sync_date
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;
    
    const params = [
      baselineData.id,
      baselineData.meter_id,
      baselineData.baseline_date,
      baselineData.baseline_value,
      baselineData.baseline_type,
      baselineData.status,
      baselineData.import_batch,
      baselineData.source_file,
      baselineData.data_source,
      baselineData.validation_notes,
      baselineData.is_validated ? 1 : 0,
      baselineData.validated_date,
      baselineData.validated_by,
      baselineData.has_validation_errors ? 1 : 0,
      baselineData.validation_errors,
      baselineData.is_anomalous ? 1 : 0,
      baselineData.anomaly_description,
      baselineData.previous_baseline_id,
      baselineData.previous_baseline_value,
      baselineData.variance_from_previous,
      baselineData.variance_percentage,
      baselineData.is_corrected ? 1 : 0,
      baselineData.corrected_date,
      baselineData.corrected_by,
      baselineData.correction_reason,
      baselineData.confidence_level,
      baselineData.notes,
      baselineData.created_at,
      baselineData.updated_at,
      baselineData.created_by,
      baselineData.updated_by,
      0, // is_deleted
      'Synced',
      new Date().toISOString()
    ];
    
    await DatabaseManager.executeSql(sql, params);
  }
  
  /**
   * Bulk upsert baseline records with transaction support
   * 批量插入或更新基线记录，支持事务
   */
  static async bulkUpsert(baselineDataList: SyncBaselineData[]): Promise<void> {
    if (baselineDataList.length === 0) {
      return;
    }

    console.log(`BaselineRepository: Starting bulk upsert of ${baselineDataList.length} records`);
    
    const db = DatabaseManager.getInstance().getDatabase();
    
    await db.transaction(async (tx) => {
      for (const baselineData of baselineDataList) {
        await this.upsert(baselineData);
      }
    });
    
    console.log(`BaselineRepository: Completed bulk upsert of ${baselineDataList.length} records`);
  }
  
  /**
   * Get the latest active baseline for a specific meter
   * 获取指定水表的最新有效基线
   */
  static async getLatestActiveBaseline(meterId: number): Promise<BaselineRecord | null> {
    const sql = `
      SELECT * FROM baseline_records 
      WHERE meter_id = ? 
        AND status = 'Active' 
        AND is_deleted = 0
      ORDER BY baseline_date DESC 
      LIMIT 1
    `;
    
    const result = await DatabaseManager.executeSql(sql, [meterId]);
    return result.rows.length > 0 ? this.mapRowToBaseline(result.rows.item(0)) : null;
  }
  
  /**
   * Get baseline history for a specific meter
   * 获取指定水表的基线历史
   */
  static async getBaselineHistory(meterId: number, limit: number = 10): Promise<BaselineRecord[]> {
    const sql = `
      SELECT * FROM baseline_records 
      WHERE meter_id = ? 
        AND is_deleted = 0
      ORDER BY baseline_date DESC 
      LIMIT ?
    `;
    
    const result = await DatabaseManager.executeSql(sql, [meterId, limit]);
    const baselines: BaselineRecord[] = [];
    
    for (let i = 0; i < result.rows.length; i++) {
      baselines.push(this.mapRowToBaseline(result.rows.item(i)));
    }
    
    return baselines;
  }
  
  /**
   * Get paginated baselines for debug purposes
   * 获取分页的基线数据用于调试
   */
  static async getAllPaginated(page: number, pageSize: number): Promise<BaselineRecord[]> {
    const offset = (page - 1) * pageSize;
    const sql = `
      SELECT 
        br.*,
        wm.serial_number as meter_serial_number
      FROM baseline_records br
      LEFT JOIN water_meters wm ON br.meter_id = wm.id
      WHERE br.is_deleted = 0
      ORDER BY br.baseline_date DESC
      LIMIT ? OFFSET ?
    `;
    
    const result = await DatabaseManager.executeSql(sql, [pageSize, offset]);
    const baselines: BaselineRecord[] = [];
    
    for (let i = 0; i < result.rows.length; i++) {
      baselines.push(this.mapRowToBaseline(result.rows.item(i)));
    }
    
    return baselines;
  }
  
  /**
   * Get total count of baseline records
   * 获取基线记录总数
   */
  static async getTotalCount(): Promise<number> {
    const sql = 'SELECT COUNT(*) as count FROM baseline_records WHERE is_deleted = 0';
    const result = await DatabaseManager.executeSql(sql, []);
    return result.rows.item(0).count;
  }
  
  /**
   * Get count of active baseline records
   * 获取有效基线记录数
   */
  static async getActiveCount(): Promise<number> {
    const sql = `
      SELECT COUNT(*) as count FROM baseline_records 
      WHERE status = 'Active' AND is_deleted = 0
    `;
    const result = await DatabaseManager.executeSql(sql, []);
    return result.rows.item(0).count;
  }
  
  /**
   * Get count of validated baseline records
   * 获取已验证基线记录数
   */
  static async getValidatedCount(): Promise<number> {
    const sql = `
      SELECT COUNT(*) as count FROM baseline_records 
      WHERE is_validated = 1 AND is_deleted = 0
    `;
    const result = await DatabaseManager.executeSql(sql, []);
    return result.rows.item(0).count;
  }
  
  /**
   * Get count of anomalous baseline records
   * 获取异常基线记录数
   */
  static async getAnomalousCount(): Promise<number> {
    const sql = `
      SELECT COUNT(*) as count FROM baseline_records 
      WHERE is_anomalous = 1 AND is_deleted = 0
    `;
    const result = await DatabaseManager.executeSql(sql, []);
    return result.rows.item(0).count;
  }
  
  /**
   * Get count of corrected baseline records
   * 获取已修正基线记录数
   */
  static async getCorrectedCount(): Promise<number> {
    const sql = `
      SELECT COUNT(*) as count FROM baseline_records 
      WHERE is_corrected = 1 AND is_deleted = 0
    `;
    const result = await DatabaseManager.executeSql(sql, []);
    return result.rows.item(0).count;
  }
  
  /**
   * Get count of recent baseline records (last 30 days)
   * 获取最近基线记录数（最近30天）
   */
  static async getRecentCount(): Promise<number> {
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    
    const sql = `
      SELECT COUNT(*) as count FROM baseline_records 
      WHERE created_at >= ? AND is_deleted = 0
    `;
    const result = await DatabaseManager.executeSql(sql, [thirtyDaysAgo.toISOString()]);
    return result.rows.item(0).count;
  }
  
  /**
   * Find baseline by ID
   * 根据ID查找基线
   */
  static async findById(id: number): Promise<BaselineRecord | null> {
    const sql = 'SELECT * FROM baseline_records WHERE id = ? AND is_deleted = 0';
    const result = await DatabaseManager.executeSql(sql, [id]);
    return result.rows.length > 0 ? this.mapRowToBaseline(result.rows.item(0)) : null;
  }
  
  /**
   * Map database row to BaselineRecord object
   * 将数据库行映射为BaselineRecord对象
   */
  private static mapRowToBaseline(row: any): BaselineRecord {
    return {
      id: row.id,
      meter_id: row.meter_id,
      baseline_date: row.baseline_date,
      baseline_value: row.baseline_value,
      baseline_type: row.baseline_type,
      status: row.status,
      import_batch: row.import_batch,
      source_file: row.source_file,
      data_source: row.data_source,
      validation_notes: row.validation_notes,
      is_validated: row.is_validated === 1,
      validated_date: row.validated_date,
      validated_by: row.validated_by,
      has_validation_errors: row.has_validation_errors === 1,
      validation_errors: row.validation_errors,
      is_anomalous: row.is_anomalous === 1,
      anomaly_description: row.anomaly_description,
      previous_baseline_id: row.previous_baseline_id,
      previous_baseline_value: row.previous_baseline_value,
      variance_from_previous: row.variance_from_previous,
      variance_percentage: row.variance_percentage,
      is_corrected: row.is_corrected === 1,
      corrected_date: row.corrected_date,
      corrected_by: row.corrected_by,
      correction_reason: row.correction_reason,
      confidence_level: row.confidence_level,
      notes: row.notes,
      created_at: row.created_at,
      updated_at: row.updated_at,
      created_by: row.created_by,
      updated_by: row.updated_by,
      is_deleted: row.is_deleted === 1,
      sync_status: row.sync_status,
      last_sync_date: row.last_sync_date
    };
  }
}
```

### 3. 完整的Service实现

```typescript
// src/services/BaselineService.ts
import { ApiService } from '../api/ApiService';
import { BaselineRepository } from '../data/repositories/BaselineRepository';
import { SyncBaselineData, BaselineStatistics } from '../database/models/BaselineRecord';

export class BaselineService {
  
  /**
   * Fetch and save all baseline data from backend with pagination
   * 从后端获取并保存所有基线数据，支持分页
   */
  static async fetchAndSaveBaselines(): Promise<{success: boolean, message: string, count: number}> {
    try {
      console.log('BaselineService: Starting baseline data synchronization...');
      
      let page = 1;
      const pageSize = 1000;
      let totalSynced = 0;
      let hasMore = true;
      
      while (hasMore) {
        console.log(`BaselineService: Fetching page ${page} with pageSize ${pageSize}...`);
        
        const response = await ApiService.get('/mobile/baselines/sync/all', {
          page,
          pageSize
        });
        
        if (!response.data || !Array.isArray(response.data)) {
          throw new Error('Invalid API response structure');
        }
        
        const baselines = response.data as SyncBaselineData[];
        console.log(`BaselineService: Received ${baselines.length} baselines from API for page ${page}`);
        
        if (baselines.length > 0) {
          await BaselineRepository.bulkUpsert(baselines);
          totalSynced += baselines.length;
          console.log(`BaselineService: Successfully saved ${baselines.length} baselines to database (Total: ${totalSynced})`);
        }
        
        // 检查是否还有更多数据
        hasMore = response.pagination?.hasMore ?? false;
        page++;
        
        // 如果返回的数据少于pageSize，说明已经是最后一页
        if (baselines.length < pageSize) {
          hasMore = false;
        }
      }
      
      console.log(`BaselineService: Baseline synchronization completed successfully. Total synced: ${totalSynced}`);
      return {
        success: true,
        message: `Successfully synchronized ${totalSynced} baseline records`,
        count: totalSynced
      };
      
    } catch (error) {
      console.error('BaselineService: Error in fetchAndSaveBaselines:', error);
      return {
        success: false,
        message: `Failed to synchronize baselines: ${error}`,
        count: 0
      };
    }
  }
  
  /**
   * Get effective last reading for a meter (business logic)
   * 获取水表的有效最后读数（业务逻辑）
   */
  static async getEffectiveLastReading(meterId: number): Promise<number | null> {
    try {
      console.log(`BaselineService: Getting effective last reading for meter ${meterId}`);
      
      // 1. 先尝试从水表表获取实际读数
      const WaterMeterRepository = (await import('../data/repositories/WaterMeterRepository')).WaterMeterRepository;
      const meter = await WaterMeterRepository.findById(meterId);
      
      if (meter?.last_reading && meter.last_reading > 0) {
        console.log(`BaselineService: Using WaterMeter.last_reading ${meter.last_reading} for meter ${meterId}`);
        return meter.last_reading;
      }
      
      // 2. 如果没有实际读数，获取最新的有效基线
      const latestBaseline = await BaselineRepository.getLatestActiveBaseline(meterId);
      if (latestBaseline) {
        console.log(`BaselineService: Using baseline value ${latestBaseline.baseline_value} for meter ${meterId}`);
        return latestBaseline.baseline_value;
      }
      
      // 3. 如果没有基线，尝试从历史读数获取（降级处理）
      const ReadingRepository = (await import('../data/repositories/ReadingRepository')).ReadingRepository;
      const latestReading = await ReadingRepository.getLatestByMeterId(meterId);
      if (latestReading) {
        console.log(`BaselineService: Using latest reading ${latestReading.reading_value} for meter ${meterId}`);
        return latestReading.reading_value;
      }
      
      console.log(`BaselineService: No effective reading found for meter ${meterId}`);
      return null;
      
    } catch (error) {
      console.error(`BaselineService: Error getting effective last reading for meter ${meterId}:`, error);
      return null;
    }
  }
  
  /**
   * Get comprehensive baseline statistics
   * 获取综合基线统计信息
   */
  static async getBaselineStatistics(): Promise<BaselineStatistics> {
    try {
      console.log('BaselineService: Fetching baseline statistics...');
      
      const [total, active, validated, anomalous, corrected, recent] = await Promise.all([
        BaselineRepository.getTotalCount(),
        BaselineRepository.getActiveCount(),
        BaselineRepository.getValidatedCount(),
        BaselineRepository.getAnomalousCount(),
        BaselineRepository.getCorrectedCount(),
        BaselineRepository.getRecentCount()
      ]);
      
      const statistics = { total, active, validated, anomalous, corrected, recent };
      console.log('BaselineService: Baseline statistics:', statistics);
      
      return statistics;
    } catch (error) {
      console.error('BaselineService: Error getting baseline statistics:', error);
      return { total: 0, active: 0, validated: 0, anomalous: 0, corrected: 0, recent: 0 };
    }
  }
  
  /**
   * Get baseline history for a specific meter
   * 获取指定水表的基线历史
   */
  static async getMeterBaselineHistory(meterId: number, limit: number = 10) {
    try {
      console.log(`BaselineService: Getting baseline history for meter ${meterId}`);
      return await BaselineRepository.getBaselineHistory(meterId, limit);
    } catch (error) {
      console.error(`BaselineService: Error getting baseline history for meter ${meterId}:`, error);
      return [];
    }
  }
  
  /**
   * Incremental sync - fetch only updated baselines
   * 增量同步 - 只获取更新的基线
   */
  static async incrementalSync(lastSyncDate: Date): Promise<{success: boolean, message: string, count: number}> {
    try {
      console.log(`BaselineService: Starting incremental sync from ${lastSyncDate.toISOString()}`);
      
      let page = 1;
      const pageSize = 1000;
      let totalSynced = 0;
      let hasMore = true;
      
      while (hasMore) {
        const response = await ApiService.get('/mobile/baselines/sync/all', {
          page,
          pageSize,
          lastSyncDate: lastSyncDate.toISOString()
        });
        
        if (!response.data || !Array.isArray(response.data)) {
          throw new Error('Invalid API response structure');
        }
        
        const baselines = response.data as SyncBaselineData[];
        console.log(`BaselineService: Incremental sync received ${baselines.length} updated baselines`);
        
        if (baselines.length > 0) {
          await BaselineRepository.bulkUpsert(baselines);
          totalSynced += baselines.length;
        }
        
        hasMore = response.pagination?.hasMore ?? false;
        page++;
        
        if (baselines.length < pageSize) {
          hasMore = false;
        }
      }
      
      console.log(`BaselineService: Incremental sync completed. Total synced: ${totalSynced}`);
      return {
        success: true,
        message: `Incrementally synchronized ${totalSynced} baseline records`,
        count: totalSynced
      };
      
    } catch (error) {
      console.error('BaselineService: Error in incremental sync:', error);
      return {
        success: false,
        message: `Failed to perform incremental sync: ${error}`,
        count: 0
      };
    }
  }
}
```

---

## 🔧 数据库迁移和升级

### DatabaseManager升级 (版本6)

```typescript
// src/database/DatabaseManager.ts - 增加基线表支持
export class DatabaseManager {
  private static instance: DatabaseManager | null = null;
  private static readonly DATABASE_NAME = 'water_meter_app.db';
  private static readonly CURRENT_DB_VERSION = 6; // 版本升级到6
  
  // ... existing code ...
  
  private async initializeDatabase(): Promise<void> {
    try {
      console.log('DatabaseManager: Initializing database...');
      
      const currentVersion = await this.getDatabaseVersion();
      console.log(`DatabaseManager: Current version: ${currentVersion}, Target version: ${DatabaseManager.CURRENT_DB_VERSION}`);
      
      // ... existing version checks ...
      
      // 版本6：添加基线记录表 (新增)
      if (currentVersion < 6) {
        console.log('DatabaseManager: Upgrading to version 6 - Adding baseline records table');
        await this.createBaselineRecordsTable();
        await this.updateDatabaseVersion(6);
        console.log('DatabaseManager: Successfully upgraded to version 6');
      }
      
      console.log('DatabaseManager: Database initialization completed');
    } catch (error) {
      console.error('DatabaseManager: Error during database initialization:', error);
      throw error;
    }
  }
  
  /**
   * Create baseline_records table with complete schema
   * 创建完整的基线记录表
   */
  private async createBaselineRecordsTable(): Promise<void> {
    const sql = `
      CREATE TABLE IF NOT EXISTS baseline_records (
          id INTEGER PRIMARY KEY,                    -- 直接使用后端ID
          meter_id INTEGER NOT NULL,                 -- 关联水表ID
          baseline_date TEXT NOT NULL,               -- 基线日期 (ISO格式)
          baseline_value REAL NOT NULL,              -- 基线读数值
          baseline_type TEXT NOT NULL,               -- 基线类型: Initial/Periodic/Correction/Migration
          status TEXT NOT NULL DEFAULT 'Active',     -- 状态: Active/Superseded/Invalid
          import_batch TEXT,                         -- 导入批次号
          source_file TEXT,                          -- 源文件名
          data_source TEXT DEFAULT 'Manual',         -- 数据源: CSV/Excel/Manual/AMS
          validation_notes TEXT,                     -- 验证备注
          is_validated INTEGER DEFAULT 0,            -- 是否已验证 (0/1)
          validated_date TEXT,                       -- 验证日期
          validated_by TEXT,                         -- 验证人
          has_validation_errors INTEGER DEFAULT 0,   -- 是否有验证错误
          validation_errors TEXT,                    -- 验证错误信息
          is_anomalous INTEGER DEFAULT 0,            -- 是否异常
          anomaly_description TEXT,                  -- 异常描述
          previous_baseline_id INTEGER,              -- 前一个基线ID
          previous_baseline_value REAL,              -- 前一个基线值
          variance_from_previous REAL,               -- 与前值的差异
          variance_percentage REAL,                  -- 差异百分比
          is_corrected INTEGER DEFAULT 0,            -- 是否被修正
          corrected_date TEXT,                       -- 修正日期
          corrected_by TEXT,                         -- 修正人
          correction_reason TEXT,                    -- 修正原因
          confidence_level INTEGER DEFAULT 100,      -- 置信度 (0-100)
          notes TEXT,                                -- 备注
          
          -- 基础字段 (BaseEntity)
          created_at TEXT NOT NULL,
          updated_at TEXT NOT NULL,
          created_by TEXT NOT NULL,
          updated_by TEXT NOT NULL,
          is_deleted INTEGER DEFAULT 0,
          
          -- 移动端同步字段
          sync_status TEXT DEFAULT 'Synced',         -- 同步状态
          last_sync_date TEXT,                       -- 最后同步时间
          
          FOREIGN KEY (meter_id) REFERENCES water_meters(id),
          FOREIGN KEY (previous_baseline_id) REFERENCES baseline_records(id)
      );
      
      -- 性能优化索引
      CREATE INDEX IF NOT EXISTS idx_baseline_meter_id ON baseline_records(meter_id);
      CREATE INDEX IF NOT EXISTS idx_baseline_status ON baseline_records(status);
      CREATE INDEX IF NOT EXISTS idx_baseline_date ON baseline_records(baseline_date);
      CREATE INDEX IF NOT EXISTS idx_baseline_type ON baseline_records(baseline_type);
      CREATE INDEX IF NOT EXISTS idx_baseline_active ON baseline_records(meter_id, status, baseline_date DESC);
      CREATE INDEX IF NOT EXISTS idx_baseline_sync ON baseline_records(sync_status, last_sync_date);
    `;
    
    await this.executeSql(sql, []);
    console.log('DatabaseManager: baseline_records table and indexes created successfully');
  }
  
  // ... rest of existing code ...
}
```

---

## 🔄 同步服务集成

### 更新BasicDataSyncService

```typescript
// src/services/sync/BasicDataSyncService.ts - 完整集成基线同步
import { UserService } from '../UserService';
import { WaterMeterService } from '../WaterMeterService';
import { BaselineService } from '../BaselineService'; // 新增导入
import { TaskService } from '../TaskService';

export interface SyncResult {
  success: boolean;
  message: string;
  details: {
    users: { success: boolean; count: number; message: string };
    waterMeters: { success: boolean; count: number; message: string };
    baselines: { success: boolean; count: number; message: string }; // 新增
    tasks: { success: boolean; count: number; message: string };
  };
}

export class BasicDataSyncService {
  
  /**
   * Comprehensive sync of all basic data including baselines
   * 全面同步所有基础数据，包括基线
   */
  static async syncAllBasicData(): Promise<SyncResult> {
    const results: SyncResult = {
      success: true,
      message: 'Basic data synchronization completed successfully',
      details: {
        users: { success: false, count: 0, message: '' },
        waterMeters: { success: false, count: 0, message: '' },
        baselines: { success: false, count: 0, message: '' }, // 新增
        tasks: { success: false, count: 0, message: '' }
      }
    };
    
    try {
      console.log('BasicDataSyncService: Starting comprehensive data synchronization...');
      
      // Step 1: Sync users (dependency for other data)
      console.log('BasicDataSyncService: Step 1 - Syncing users...');
      const userResult = await UserService.fetchAndSaveUsers();
      results.details.users = userResult;
      console.log(`BasicDataSyncService: Users sync result - Success: ${userResult.success}, Count: ${userResult.count}`);
      
      // Step 2: Sync water meters (dependency for baselines and tasks)
      console.log('BasicDataSyncService: Step 2 - Syncing water meters...');
      const meterResult = await WaterMeterService.fetchAndSaveWaterMeters();
      results.details.waterMeters = meterResult;
      console.log(`BasicDataSyncService: Water meters sync result - Success: ${meterResult.success}, Count: ${meterResult.count}`);
      
      // Step 3: Sync baselines (new addition)
      console.log('BasicDataSyncService: Step 3 - Syncing baselines...');
      const baselineResult = await BaselineService.fetchAndSaveBaselines();
      results.details.baselines = baselineResult;
      console.log(`BasicDataSyncService: Baselines sync result - Success: ${baselineResult.success}, Count: ${baselineResult.count}`);
      
      // Step 4: Sync tasks (depends on meters and users)
      console.log('BasicDataSyncService: Step 4 - Syncing tasks...');
      const taskResult = await TaskService.fetchAndSaveTasks();
      results.details.tasks = taskResult;
      console.log(`BasicDataSyncService: Tasks sync result - Success: ${taskResult.success}, Count: ${taskResult.count}`);
      
      // 评估整体成功状态
      const allSuccess = Object.values(results.details).every(detail => detail.success);
      if (!allSuccess) {
        results.success = false;
        results.message = 'Some synchronization operations failed';
        console.warn('BasicDataSyncService: Some sync operations failed');
      } else {
        console.log('BasicDataSyncService: All synchronization operations completed successfully');
      }
      
      // 生成详细报告
      const totalSynced = Object.values(results.details).reduce((sum, detail) => sum + detail.count, 0);
      console.log(`BasicDataSyncService: Total records synchronized: ${totalSynced}`);
      
      return results;
      
    } catch (error) {
      console.error('BasicDataSyncService: Critical error during synchronization:', error);
      results.success = false;
      results.message = `Synchronization failed with error: ${error}`;
      return results;
    }
  }
  
  /**
   * Incremental sync for updated data only
   * 增量同步（仅更新的数据）
   */
  static async incrementalSync(lastSyncDate: Date): Promise<SyncResult> {
    const results: SyncResult = {
      success: true,
      message: 'Incremental synchronization completed successfully',
      details: {
        users: { success: false, count: 0, message: '' },
        waterMeters: { success: false, count: 0, message: '' },
        baselines: { success: false, count: 0, message: '' },
        tasks: { success: false, count: 0, message: '' }
      }
    };
    
    try {
      console.log(`BasicDataSyncService: Starting incremental sync from ${lastSyncDate.toISOString()}`);
      
      // 基线数据增量同步
      const baselineResult = await BaselineService.incrementalSync(lastSyncDate);
      results.details.baselines = baselineResult;
      
      // 其他数据的增量同步可以后续添加
      // TODO: 实现用户、水表、任务的增量同步
      
      const allSuccess = Object.values(results.details).every(detail => detail.success);
      results.success = allSuccess;
      
      return results;
      
    } catch (error) {
      console.error('BasicDataSyncService: Error during incremental sync:', error);
      results.success = false;
      results.message = `Incremental sync failed: ${error}`;
      return results;
    }
  }
}
```

---

## 🎨 UI层更新和集成

### 更新现有服务调用有效读数逻辑

```typescript
// src/services/MeterService.ts - 集成BaselineService逻辑
import { BaselineService } from './BaselineService';

export class MeterService {
  
  // ... existing methods ...
  
  /**
   * Get effective last reading using baseline fallback logic
   * 获取有效最后读数，使用基线降级逻辑
   */
  static async getEffectiveLastReading(meterId: number): Promise<number | null> {
    return await BaselineService.getEffectiveLastReading(meterId);
  }
}
```

### 更新DebugDataScreen支持基线查看

```typescript
// src/screens/debug/DebugDataScreen.tsx - 添加基线数据查看
const tabs = [
  { key: 'users', title: 'Users' },
  { key: 'meters', title: 'Meters' },
  { key: 'baselines', title: 'Baselines' }, // 新增基线tab
  { key: 'tasks', title: 'Tasks' },
  { key: 'readings', title: 'Readings' }
];

// 在loadData方法中添加基线数据加载逻辑
const loadBaselines = async () => {
  try {
    setLoading(true);
    const baselines = await BaselineRepository.getAllPaginated(currentPage, pageSize);
    setData(baselines);
    
    // 获取统计信息
    const stats = await BaselineService.getBaselineStatistics();
    // 显示统计信息...
    
  } catch (error) {
    console.error('Error loading baselines:', error);
    setError('Failed to load baseline data');
  } finally {
    setLoading(false);
  }
};
```

---

## 🚀 完整实施计划

### Phase 1: 后端API开发 (2天)
**Day 1:**
- [ ] 创建 `MobileBaselineController.cs`
- [ ] 实现 `/api/mobile/baselines/sync/all` 接口
- [ ] 创建 `MobileBaselineDto.cs`
- [ ] 编写单元测试

**Day 2:**
- [ ] 实现统计接口 `/api/mobile/baselines/statistics`
- [ ] 测试API接口性能和数据正确性
- [ ] 文档更新

### Phase 2: 移动端数据层开发 (3天)
**Day 3:**
- [ ] 数据库升级到版本6
- [ ] 创建基线表结构和索引
- [ ] 测试数据库迁移

**Day 4:**
- [ ] 实现 `BaselineRecord.ts` 接口
- [ ] 完成 `BaselineRepository.ts` 全部方法
- [ ] 编写Repository单元测试

**Day 5:**
- [ ] 完成 `BaselineService.ts` 实现
- [ ] 集成API调用逻辑
- [ ] 测试同步功能

### Phase 3: 同步服务集成 (2天)
**Day 6:**
- [ ] 更新 `BasicDataSyncService.ts`
- [ ] 实现基线数据同步流程
- [ ] 测试完整同步链路

**Day 7:**
- [ ] 实现增量同步功能
- [ ] 优化同步性能
- [ ] 错误处理完善

### Phase 4: 业务逻辑集成 (2天)
**Day 8:**
- [ ] 更新 `MeterService.getEffectiveLastReading`
- [ ] 测试TaskList和TaskDetail显示
- [ ] 验证业务逻辑正确性

**Day 9:**
- [ ] 更新后端 `MeterService.GetEffectiveLastReadingAsync`
- [ ] 更新移动端API控制器调用
- [ ] 前后端逻辑一致性测试

### Phase 5: UI集成和测试 (3天)
**Day 10:**
- [ ] 更新DebugDataScreen支持基线查看
- [ ] 实现基线统计显示
- [ ] UI交互测试

**Day 11:**
- [ ] 完整的端到端测试
- [ ] 性能测试和优化
- [ ] 数据一致性验证

**Day 12:**
- [ ] 用户验收测试
- [ ] 文档完善和代码审查
- [ ] 部署准备

### Phase 6: 部署和监控 (2天)
**Day 13:**
- [ ] 生产环境部署准备
- [ ] 监控和日志配置
- [ ] 回滚方案准备

**Day 14:**
- [ ] 生产环境部署
- [ ] 监控数据同步状态
- [ ] 问题修复和优化

**总预计工时：14天**

---

## 🎯 验收标准

### 功能验收
- [ ] 移动端TaskList显示正确的LastReading（不再为0）
- [ ] 基线数据成功同步到移动端（支持分页）
- [ ] 有效读数获取优先级正确：WaterMeter.last_reading > 基线值 > 历史读数
- [ ] 增量同步功能正常工作
- [ ] Debug界面可以查看基线数据和统计

### 性能验收
- [ ] 基线数据同步时间 < 5分钟（10000条记录）
- [ ] TaskList加载时间 < 2秒
- [ ] 有效读数计算时间 < 100ms

### 数据验收
- [ ] 基线数据与后端完全一致
- [ ] 显示逻辑与电脑端一致
- [ ] 外键约束正确处理
- [ ] 数据同步无丢失无重复

### 稳定性验收
- [ ] 网络异常时同步可恢复
- [ ] 数据库升级无数据丢失
- [ ] 错误日志完整可追踪
- [ ] 内存使用稳定无泄漏

---

## 📝 风险控制

### 技术风险
1. **数据量大同步慢** - 分页机制和增量同步
2. **外键约束冲突** - 完善的数据验证和事务处理
3. **版本兼容性** - 渐进式数据库升级

### 业务风险
1. **读数显示不一致** - 完整的端到端测试
2. **同步数据丢失** - 事务保护和重试机制
3. **性能影响** - 异步处理和缓存策略

### 缓解措施
- 分阶段实施，每个阶段独立测试
- 完整的回滚方案
- 详细的监控和日志
- 用户培训和文档支持

---

**现在开始按这个完整版计划实施！** 🚀 