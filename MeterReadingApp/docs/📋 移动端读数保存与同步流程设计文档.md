# 📋 移动端读数保存与同步流程设计文档

## 🏗️ 核心设计原则

基于 CORDE-Mobile-Application 的成功实践，我们的设计应该遵循以下原则：

1. 数据关联简化：同步表只通过 local_reading_id 关联主数据
2. 业务逻辑通用化：同步方法既可立即调用，也可批量调用
3. 状态管理集中化：所有同步状态在同步表中统一管理
4. Task关联设计：Reading通过task_id与Task关联，服务端通过Task识别对应关系

------

## 🗃️ 数据模型设计

### 主数据表：meter_readings (使用现有表)

```sql
CREATE TABLE meter_readings (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  uuid TEXT NOT NULL UNIQUE,

  -- 必填关联字段
  meter_id INTEGER NOT NULL,
  user_id INTEGER NOT NULL,
  task_id INTEGER NOT NULL,        -- 关联Task，服务端通过此字段识别对应关系

  -- 读数数据
  reading_value REAL NOT NULL,
  reading_date TEXT NOT NULL,

  -- 读数方法
  reading_method TEXT NOT NULL DEFAULT 'Manual',
  reading_type TEXT DEFAULT 'Regular',
  data_source TEXT DEFAULT 'Mobile',

  -- GPS位置信息
  latitude REAL,
  longitude REAL,
  gps_accuracy REAL,
  location TEXT,

  -- 状态管理
  status TEXT NOT NULL DEFAULT 'Completed',
  sync_status TEXT DEFAULT 'pending',

  -- 审计字段
  created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
  is_deleted INTEGER DEFAULT 0,
  created_by TEXT DEFAULT 'mobile_app',
  updated_by TEXT DEFAULT 'mobile_app',

  FOREIGN KEY (meter_id) REFERENCES water_meters(id),
  FOREIGN KEY (user_id) REFERENCES users(id),
  FOREIGN KEY (task_id) REFERENCES work_tasks(id)
);
```

### 同步表：meter\_reading\_sync\_logs (简化设计)

```sql
CREATE TABLE meter_reading_sync_logs (
  sync_log_id INTEGER PRIMARY KEY AUTOINCREMENT,
  local_reading_id INTEGER NOT NULL,    -- 关联到meter_readings表
  sync_status TEXT DEFAULT 'pending',   -- pending, synced, error, conflict
  retry_count INTEGER DEFAULT 0,
  last_sync_time TEXT,
  last_update_time TEXT,
  sync_errors TEXT,
  offline_indicator INTEGER DEFAULT 1,
  sync_task_count INTEGER DEFAULT 1,
  conflict_status TEXT DEFAULT 'none',
  created_at TEXT DEFAULT CURRENT_TIMESTAMP,
  updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (local_reading_id) REFERENCES meter_readings(id)
);
```

---

## 📝 核心业务流程

### 步骤1：保存读数数据 (MeterReadingSyncService.saveReadingWithSync)

```ts
static async saveReadingWithSync(
  meterReadingData: any,
  userId: number
): Promise<number> {
  try {
    // 1. 保存主数据到 meter_readings 表
    const readingRequest = {
      meter_id: meterReadingData.meter_id,
      user_id: userId,
      task_id: meterReadingData.task_id,      // 关键：通过task_id关联
      reading_value: meterReadingData.reading_value,
      reading_method: meterReadingData.reading_method || 'Manual',
      latitude: meterReadingData.latitude,
      longitude: meterReadingData.longitude,
      gps_accuracy: meterReadingData.gps_accuracy,
      // 其他字段...
    };

    const localReading = await ReadingRepository.create(readingRequest);

    // 2. 立即创建同步记录（简化设计，不存储server_reading_id）
    const syncLogRequest = {
      local_reading_id: localReading.id!,
      sync_status: 'pending',
      offline_indicator: 1,
      sync_task_count: 1,
      retry_count: 0,
      conflict_status: 'none'
    };

    await MeterReadingSyncLogRepository.create(syncLogRequest);

    // 3. 如果有网络连接，立即尝试同步
    const isConnected = await NetworkService.isConnected();
    if (isConnected) {
      await this.syncReadingToServer(localReading.id!);
    }

    return localReading.id!;
  } catch (error) {
    console.error('Error saving reading with sync:', error);
    throw error;
  }
}
```

### 步骤2：通用同步业务逻辑 (MeterReadingSyncService.syncReadingToServer)

```ts
static async syncReadingToServer(localReadingId: number): Promise<void> {
  try {
    // 1. 获取同步记录
    const syncLog = await MeterReadingSyncLogRepository.findByLocalReadingId(localReadingId);
    if (!syncLog) {
      throw new Error(`Sync log not found for reading ${localReadingId}`);
    }

    // 2. 获取主数据记录
    const reading = await ReadingRepository.findById(localReadingId);
    if (!reading) {
      throw new Error(`Reading not found: ${localReadingId}`);
    }

    // 3. 更新同步状态为同步中
    await MeterReadingSyncLogRepository.update(syncLog.sync_log_id!, {
      sync_status: 'syncing',
      last_update_time: new Date().toISOString()
    });

    // 4. 准备API数据（关键：通过task_id让服务端识别对应关系）
    const apiData: MobileReadingDto = {
      TaskId: reading.task_id,           // 服务端通过此字段识别对应的Task
      MeterId: reading.meter_id,
      ReadingValue: reading.reading_value,
      ReadingDate: reading.reading_date,
      ReadingMethod: reading.reading_method,
      Latitude: reading.latitude,
      Longitude: reading.longitude,
      Notes: reading.notes,
      // 不需要传递server_id，服务端通过TaskId识别
    };

    // 5. 调用后端API
    const response = await ApiService.post('/api/mobile/readings', apiData);

    // 6. 更新同步记录为完成状态（不存储server_reading_id）
    await MeterReadingSyncLogRepository.update(syncLog.sync_log_id!, {
      sync_status: 'synced',
      last_sync_time: new Date().toISOString(),
      last_update_time: new Date().toISOString(),
      sync_errors: null
    });

    console.log(`✅ Reading ${localReadingId} synced successfully`);
  } catch (error) {
    // 7. 处理同步错误
    await this.handleSyncError(localReadingId, error);
  }
}
```

### 步骤3：批量同步处理 (MeterReadingSyncService.syncPendingReadings)

```ts
static async syncPendingReadings(): Promise<void> {
  try {
    // 1. 获取所有待同步的记录
    const pendingSyncLogs = await MeterReadingSyncLogRepository.getPendingSyncLogs(50);
    console.log(`Found ${pendingSyncLogs.length} readings to sync`);

    // 2. 逐个处理同步（避免并发冲突）
    for (const syncLog of pendingSyncLogs) {
      try {
        await this.syncReadingToServer(syncLog.local_reading_id);
        console.log(`Successfully synced reading ${syncLog.local_reading_id}`);
      } catch (error) {
        console.error(`Failed to sync reading ${syncLog.local_reading_id}:`, error);
        // 继续处理下一条记录
      }
    }
  } catch (error) {
    console.error('Error in batch sync:', error);
    throw error;
  }
}
```

### 步骤4：错误处理 (MeterReadingSyncService.handleSyncError)

```ts
private static async handleSyncError(localReadingId: number, error: any): Promise<void> {
  try {
    const syncLog = await MeterReadingSyncLogRepository.findByLocalReadingId(localReadingId);
    if (!syncLog) return;

    await MeterReadingSyncLogRepository.update(syncLog.sync_log_id!, {
      sync_status: 'error',
      retry_count: syncLog.retry_count + 1,
      sync_errors: error.message || 'Unknown error',
      last_update_time: new Date().toISOString()
    });

    console.error(`Sync failed for reading ${localReadingId}, retry count: ${syncLog.retry_count + 1}`);
  } catch (updateError) {
    console.error('Error updating sync log:', updateError);
  }
}
```

---

## 🔄 使用场景

### 场景1：用户点击保存按钮

```ts
// 在 MeterReadingScreen.tsx 中
const handleSaveReading = async () => {
  try {
    const readingId = await MeterReadingSyncService.saveReadingWithSync(
      readingData,
      currentUserId
    );
    // 数据已保存，如果有网络会自动尝试同步
    navigation.goBack();
  } catch (error) {
    showError('Failed to save reading');
  }
};
```

### 场景2：用户主动同步

```ts
// 在 SyncScreen.tsx 中
const handleSyncReadings = async () => {
  try {
    await MeterReadingSyncService.syncPendingReadings();
    showSuccess('Readings synced successfully');
  } catch (error) {
    showError('Sync failed');
  }
};
```

### 场景3：后台自动同步

```ts
// 在 BackgroundSyncService.ts 中
const runBackgroundSync = async () => {
  const isConnected = await NetworkService.isConnected();
  if (isConnected) {
    await MeterReadingSyncService.syncPendingReadings();
  }
};
```

---

## ✅ 关键优势

1. **简洁性**：同步表设计简单，只关联必要的local_reading_id
2. **通用性**：同步逻辑可在多个场景复用
3. **可靠性**：完整的错误处理和重试机制
4. **可追踪性**：详细的同步状态和错误日志
5. **一致性**：与CORDE项目的成功模式保持一致
6. **关联简化**：通过task_id让服务端识别对应关系，无需额外的server_id字段

---

## 💭 总结

这个重新设计的方案解决了原有设计的问题：

### ✅ 修复的问题：

1. **使用现有表结构**：使用已有的`meter_readings`表，而不是创建新的`readings`表
2. **移除冗余字段**：
   - 移除`meter_reading_sync_logs`中的`server_reading_id`字段
   - 移除`meter_readings`中的`server_id`字段
3. **简化关联逻辑**：Reading通过`task_id`与Task关联，服务端通过Task识别对应关系

### 🏗️ 设计原则：

* **数据关联通过task_id**：服务端接收到Reading后，通过task_id与服务端Task表对照即可识别
* **同步表只存储同步状态**：不存储业务相关的冗余字段
* **状态管理集中统一**：所有同步状态在sync_logs表中管理
* **支持离线优先**：完整的离线存储和后续同步机制

### 🔄 业务流程：

1. 移动端保存Reading到`meter_readings`表，包含`task_id`
2. 创建同步记录到`meter_reading_sync_logs`表，只存储同步状态
3. 同步时将Reading数据（包含TaskId）发送到服务端
4. 服务端通过TaskId识别对应的Task，无需额外的ID映射
5. 同步完成后更新本地同步状态，不需要存储服务端返回的ID

这种设计更加简洁、清晰，符合CORDE项目的成功实践模式。

---

## 🚀 实施计划

### 📱 第一部分：移动端代码修改

#### 1. 数据库表结构调整

**1.1 更新 DatabaseManager.ts**
- 修改 `meter_readings` 表结构，移除 `server_id` 字段
- 修改 `meter_reading_sync_logs` 表结构，移除 `server_reading_id` 字段
- 重新生成表结构（不考虑迁移）

**1.2 更新数据库版本**
- 增加数据库版本号
- 添加新的表创建脚本

#### 2. 模型定义调整

**2.1 更新 MeterReading.ts**
- 移除 `server_id` 字段
- 确保 `task_id` 为必填字段
- 更新接口定义

**2.2 更新 MeterReadingSyncLog.ts**
- 移除 `server_reading_id` 字段
- 更新相关接口定义

#### 3. Repository 层调整

**3.1 更新 ReadingRepository.ts**
- 移除所有 `server_id` 相关的SQL操作
- 更新 create、update、find 方法
- 确保 `task_id` 的正确处理

**3.2 更新 MeterReadingSyncLogRepository.ts**
- 移除 `server_reading_id` 相关的SQL操作
- 更新 create、update、find 方法
- 简化同步日志的字段处理

#### 4. Service 层调整

**4.1 更新 MeterReadingSyncService.ts**
- 移除 `server_reading_id` 的处理逻辑
- 更新同步方法，不再存储服务端返回的ID
- 确保通过 `task_id` 进行关联

**4.2 更新 ReadingService.ts**
- 调整API调用，确保字段名与C#后端一致
- 移除 `server_id` 相关逻辑

#### 5. API 接口调整

**5.1 更新 MobileReadingDto**
- 确保字段名与C#后端一致（PascalCase）
- 移除不必要的ID字段
- 重点确保 `TaskId` 字段的正确传递

**5.2 更新 API 调用**
- 调整请求数据格式
- 确保与后端接口规范一致

### 🖥️ 第二部分：后端C#代码修改

#### 1. DTO 定义调整

**1.1 更新 MobileReadingDto.cs**
- 确保字段名为 PascalCase
- 移除不必要的服务端ID字段
- 重点包含 `TaskId` 字段

#### 2. 控制器调整

**2.1 更新 MobileController.cs**
- 调整读数接收接口
- 通过 `TaskId` 识别对应的Task
- 移除服务端ID的返回逻辑

#### 3. 服务层调整

**3.1 更新读数处理服务**
- 通过 `TaskId` 查找对应的Task
- 建立Reading与Task的关联
- 简化ID映射逻辑

#### 4. 数据模型调整

**4.1 确保Task-Reading关联**
- 验证Task与Reading的关联关系
- 确保通过TaskId能正确识别对应关系

---

## 📋 详细修改清单

### 移动端文件清单：

1. **数据库相关**
   - `src/database/DatabaseManager.ts` - 表结构定义
   - `src/database/models/MeterReading.ts` - 模型定义
   - `src/database/models/MeterReadingSyncLog.ts` - 同步日志模型

2. **Repository 层**
   - `src/data/repositories/ReadingRepository.ts` - 读数数据访问
   - `src/data/repositories/MeterReadingSyncLogRepository.ts` - 同步日志数据访问

3. **Service 层**
   - `src/services/MeterReadingSyncService.ts` - 同步服务
   - `src/services/ReadingService.ts` - 读数服务

4. **API 层**
   - `src/api/dto/MobileReadingDto.ts` - API数据传输对象
   - `src/api/MobileReadingApi.ts` - API调用

### 后端文件清单：

1. **DTO 层**
   - `DTOs/MobileReadingDto.cs` - 移动端读数DTO

2. **控制器层**
   - `Controllers/MobileController.cs` - 移动端API控制器

3. **服务层**
   - `Services/MobileReadingService.cs` - 移动端读数处理服务

---

## ⚠️ 重要注意事项

1. **数据库处理**：直接按新建表处理，不需要考虑迁移
2. **代码风格**：后续代码不写注释，保持代码简洁
3. **字段命名**：API接口字段名以C#后端为准（PascalCase）
4. **关联逻辑**：通过 `task_id`/`TaskId` 建立关联，移除所有server_id相关逻辑
5. **同步简化**：同步表只存储同步状态，不存储业务相关冗余字段