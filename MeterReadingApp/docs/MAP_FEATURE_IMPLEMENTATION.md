# Map Feature Implementation

## Overview
Added map functionality to the Water Meter Reading app based on CORDE project's MapMarkerScreen implementation.

## Features

### 🗺️ Core Map Functionality
- Interactive map view using `react-native-maps`
- Water meter location markers
- Current location detection
- Address search functionality
- Date-based filtering of meter readings

### 📍 Marker Features
- Custom water meter markers with different colors for status
- Grouped markers for close locations
- Navigation between multiple markers at same location
- Detailed meter information in callouts
- Marker count indicators for grouped locations

### 🔍 Search & Filter
- Address search with geocoding
- Date range filtering (Today, 3 Days, Week, Month, All)
- Real-time marker updates based on filters

## Implementation Details

### Files Added/Modified

#### New Files
- `src/screens/map/MapScreen.tsx` - Main map screen component
- `src/services/api/GeocodingService.ts` - Geocoding service for address conversion

#### Modified Files
- `src/components/MainScreen/Overview.tsx` - Added map option to main dashboard
- `src/navigation/AppNavigator.tsx` - Added map screen to navigation
- `android/app/src/main/AndroidManifest.xml` - Added location permissions
- `package.json` - Added react-native-maps dependency

### Key Components

#### MapScreen Features
```typescript
interface MeterMarker {
  id: number;
  meter_id?: number;
  address: string;
  meter_number: string;
  latitude: number;
  longitude: number;
  meter_type?: string;
  status?: string;
  last_reading_date?: string;
}
```

#### Marker Grouping
- Automatically groups markers within 0.0001 degree tolerance
- Shows marker count for grouped locations
- Allows navigation between markers in same group

#### Search Functionality
- Real-time address search
- Geocoding service integration
- Fallback to default coordinates

### Differences from CORDE Implementation

1. **Data Model**: Adapted for water meters instead of general logs
2. **Marker Icons**: Uses water-pump icon instead of generic markers
3. **Status Colors**: Blue for active meters, red for inactive
4. **Simplified UI**: Removed some advanced features to focus on core functionality

## Usage

### Accessing Map
1. Open main dashboard
2. Tap "MAP VIEW" option
3. Map loads with current meter locations

### Searching Locations
1. Use search bar at top of map
2. Enter address or location name
3. Map animates to searched location

### Viewing Meter Details
1. Tap any meter marker on map
2. View meter information in callout
3. For grouped markers, use Previous/Next buttons

### Filtering by Date
1. Use date filter dropdown below search
2. Select desired time range
3. Map updates to show relevant meters

## Technical Notes

### Dependencies
- `react-native-maps`: Map display and interaction
- `@react-native-community/geolocation`: Location services
- Native Base components for UI

### Permissions Required
- `ACCESS_FINE_LOCATION`: Precise location access
- `ACCESS_COARSE_LOCATION`: Approximate location access

### TODO Items
1. Integrate with actual meter database
2. Implement real geocoding API (Google Maps/OpenStreetMap)
3. Add route planning between meter locations
4. Implement offline map caching
5. Add meter status synchronization

## Integration with Database

The map screen is designed to integrate with the existing DatabaseManager:

```typescript
// Future implementation
const fetchMeterMarkers = async () => {
  const meters = await DatabaseManager.executeSql(
    'SELECT * FROM meters WHERE latitude IS NOT NULL AND longitude IS NOT NULL'
  );
  // Convert to MeterMarker format
};
```

## Performance Considerations

- Marker grouping reduces rendering load for dense areas
- Address caching prevents redundant geocoding requests
- Efficient re-rendering using React.memo and useCallback where appropriate

## Reference

This implementation is based on CORDE-Mobile-Application's MapMarkerScreen.tsx, adapted for water meter management use case while maintaining the same user experience patterns. 