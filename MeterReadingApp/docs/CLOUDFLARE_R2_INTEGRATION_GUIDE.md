# 🎯 Cloudflare R2 对象存储 .NET 集成完整指南

## 📋 前言

本文档详细介绍如何在 .NET 应用中集成 Cloudflare R2 对象存储服务，包括账号申请、存储桶配置、API 密钥创建以及完整的代码实现。

---

## 🚀 一、准备工作

在开始搭建前，需要准备好以下资源：

- **Cloudflare 账号**：访问 [Cloudflare官网](https://www.cloudflare.com/) 注册免费账号
- **支付方式**：信用卡或 PayPal（仅用于验证，免费额度内不会被扣费）
- **域名一个(可选)**：可以是免费域名，或任意已购买的域名（无需备案），将域名托管到Cloudflare

💡 **提示**：域名不需要备案，只需接入Cloudflare DNS解析即可使用

---

## 🔧 二、创建与配置 R2 存储桶

### 步骤1：创建存储桶

1. 登录 Cloudflare 控制台
2. 左侧菜单选择 **R2 对象存储**，需要添加付款方式VISA卡或Paypal
3. 点击 **创建存储桶**
4. 填写存储桶名称（例如 `water-meter-photos`）
5. 地区选择 **亚太地区（APAC）**
6. 默认存储类选择 **标准**
7. 点击 **创建存储桶**
8. **开启公开访问**：点击 **设置**，公共开发URL栏 点击 **启用**，确认对话框中输入"allow"，点击允许

### 步骤2：绑定自定义域名（可选）

1. 进入刚创建的存储桶
2. 点击 **设置** 标签页
3. 找到 **自定义域** 二级菜单
4. 点击 **添加**，输入自定义域名（例如 `images.yourdomain.com`），点击继续
5. 点击 **连接域**
6. 等待初始化，状态=活动 即成功

💡 **提示**：Cloudflare 会自动添加所需的DNS记录，等待几分钟即可生效。

### 步骤3：创建S3 客户端API访问密钥

1. 左侧菜单选择 **R2 对象存储**，点击概述
2. 点击 **管理API令牌**
3. 点击 **创建 Account API令牌**
4. 配置参数：
   - **令牌名称**：`PicListToken`
   - **权限**：对象读和写: 允许读取、写入和列出特定存储桶中的对象
   - **指定存储桶**：应用于此帐户中的所有存储桶(包括新创建的存储桶)
   - **TTL**：永久
5. 点击底部按钮 **创建 Account API令牌**
6. 在记事本中保存 `<访问密钥 ID>` 和 `<机密访问密钥>` 值，点击完成后无法再次查看

---

## 💻 三、.NET 项目配置

### 步骤1：安装 NuGet 包

```bash
dotnet add package AWSSDK.S3
```

### 步骤2：配置 appsettings.json

```json
{
  "CloudflareR2": {
    "Endpoint": "https://your-account-id.r2.cloudflarestorage.com",
    "BucketName": "water-meter-photos",
    "AccessKey": "your-access-key-id",
    "SecretKey": "your-secret-access-key",
    "CustomDomain": "https://your-account-id.r2.cloudflarestorage.com"
  }
}
```

### 步骤3：创建服务接口

```csharp
// Services/Interfaces/ICloudflareR2Service.cs
using Microsoft.AspNetCore.Http;

namespace WaterMeterManagement.Services.Interfaces
{
    public interface ICloudflareR2Service
    {
        Task<string> UploadPhotoAsync(IFormFile photo);
        Task<string> UploadPhotoAsync(Stream photoStream, string fileName, string contentType);
        Task<bool> DeletePhotoAsync(string photoKey);
        Task<string> GeneratePresignedUrlAsync(string photoKey, TimeSpan expiration);
        Task<bool> PhotoExistsAsync(string photoKey);
        string ExtractKeyFromUrl(string photoUrl);
    }
}
```

### 步骤4：实现 R2 服务

```csharp
// Services/CloudflareR2Service.cs
using Amazon.S3;
using Amazon.S3.Model;
using Microsoft.AspNetCore.Http;
using WaterMeterManagement.Services.Interfaces;

namespace WaterMeterManagement.Services
{
    public class CloudflareR2Service : ICloudflareR2Service
    {
        private readonly IAmazonS3 _s3Client;
        private readonly IConfiguration _configuration;
        private readonly ILogger<CloudflareR2Service> _logger;
        private readonly string _bucketName;
        private readonly string _customDomain;

        public CloudflareR2Service(IConfiguration configuration, ILogger<CloudflareR2Service> logger)
        {
            _configuration = configuration;
            _logger = logger;
            _bucketName = _configuration["CloudflareR2:BucketName"] ?? throw new ArgumentNullException("CloudflareR2:BucketName");
            _customDomain = _configuration["CloudflareR2:CustomDomain"] ?? throw new ArgumentNullException("CloudflareR2:CustomDomain");

            // 按照官方文档示例配置
            var credentials = new Amazon.Runtime.BasicAWSCredentials(
                _configuration["CloudflareR2:AccessKey"],
                _configuration["CloudflareR2:SecretKey"]
            );

            var config = new AmazonS3Config
            {
                ServiceURL = _configuration["CloudflareR2:Endpoint"]
            };

            _s3Client = new AmazonS3Client(credentials, config);

            _logger.LogInformation("R2 Service initialized with endpoint: {Endpoint}, bucket: {Bucket}", 
                _configuration["CloudflareR2:Endpoint"], _bucketName);
        }

        public async Task<string> UploadPhotoAsync(IFormFile photo)
        {
            if (photo == null || photo.Length == 0)
                return string.Empty;

            using var stream = photo.OpenReadStream();
            return await UploadPhotoAsync(stream, photo.FileName, photo.ContentType);
        }

        public async Task<string> UploadPhotoAsync(Stream photoStream, string fileName, string contentType)
        {
            try
            {
                var key = GeneratePhotoKey(fileName);
                
                _logger.LogInformation("=== R2 UPLOAD START === File: {FileName}, ContentType: {ContentType}, Size: {Size}", 
                    key, contentType, photoStream.Length);

                var request = new PutObjectRequest
                {
                    BucketName = _bucketName,
                    Key = key,
                    InputStream = photoStream,
                    ContentType = contentType,
                    Metadata = {
                        ["upload-time"] = DateTime.UtcNow.ToString("O"),
                        ["original-name"] = fileName
                    },
                    // 按照官方文档要求的R2兼容性设置
                    DisablePayloadSigning = true,
                    DisableDefaultChecksumValidation = true
                };

                var response = await _s3Client.PutObjectAsync(request);
                var photoUrl = $"{_customDomain.TrimEnd('/')}/{key}";
                
                _logger.LogInformation("=== R2 UPLOAD SUCCESS === URL: {PhotoUrl}, ETag: {ETag}", 
                    photoUrl, response.ETag);
                
                return photoUrl;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to upload photo {FileName}", fileName);
                return string.Empty;
            }
        }

        public async Task<bool> DeletePhotoAsync(string photoKey)
        {
            try
            {
                var request = new DeleteObjectRequest
                {
                    BucketName = _bucketName,
                    Key = photoKey
                };

                await _s3Client.DeleteObjectAsync(request);
                _logger.LogInformation("Successfully deleted photo: {PhotoKey}", photoKey);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to delete photo: {PhotoKey}", photoKey);
                return false;
            }
        }

        public async Task<string> GeneratePresignedUrlAsync(string photoKey, TimeSpan expiration)
        {
            try
            {
                var request = new GetPreSignedUrlRequest
                {
                    BucketName = _bucketName,
                    Key = photoKey,
                    Verb = HttpVerb.GET,
                    Expires = DateTime.UtcNow.Add(expiration)
                };

                var presignedUrl = await _s3Client.GetPreSignedURLAsync(request);
                return presignedUrl;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to generate presigned URL for: {PhotoKey}", photoKey);
                return string.Empty;
            }
        }

        public async Task<bool> PhotoExistsAsync(string photoKey)
        {
            try
            {
                var request = new GetObjectMetadataRequest
                {
                    BucketName = _bucketName,
                    Key = photoKey
                };

                await _s3Client.GetObjectMetadataAsync(request);
                return true;
            }
            catch (AmazonS3Exception ex) when (ex.StatusCode == System.Net.HttpStatusCode.NotFound)
            {
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to check if photo exists {PhotoKey}", photoKey);
                return false;
            }
        }

        public string ExtractKeyFromUrl(string photoUrl)
        {
            if (string.IsNullOrEmpty(photoUrl))
                return string.Empty;

            var uri = new Uri(photoUrl);
            return uri.AbsolutePath.TrimStart('/');
        }

        private string GeneratePhotoKey(string originalFileName)
        {
            var extension = Path.GetExtension(originalFileName);
            var fileName = $"{Guid.NewGuid().ToString("N")[..8]}_{DateTime.UtcNow:HHmmss}{extension}";
            return $"photos/{DateTime.UtcNow:yyyy/MM/dd}/{fileName}";
        }
    }
}
```

### 步骤5：注册服务

```csharp
// Program.cs
builder.Services.AddScoped<ICloudflareR2Service, CloudflareR2Service>();
```

---

## 🧪 四、测试 R2 连接

### 创建测试控制器

```csharp
// Controllers/R2TestController.cs
using Microsoft.AspNetCore.Mvc;
using WaterMeterManagement.Services.Interfaces;

namespace WaterMeterManagement.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class R2TestController : ControllerBase
    {
        private readonly ICloudflareR2Service _r2Service;
        private readonly ILogger<R2TestController> _logger;

        public R2TestController(ICloudflareR2Service r2Service, ILogger<R2TestController> logger)
        {
            _r2Service = r2Service;
            _logger = logger;
        }

        [HttpGet("connection")]
        public async Task<IActionResult> TestConnection()
        {
            try
            {
                _logger.LogInformation("Testing R2 connection...");

                var testKey = $"test/connection-test-{DateTime.UtcNow:yyyyMMdd-HHmmss}.txt";
                var testContent = $"R2 Connection Test - {DateTime.UtcNow}";
                var testBytes = System.Text.Encoding.UTF8.GetBytes(testContent);

                using var testStream = new MemoryStream(testBytes);
                var uploadedUrl = await _r2Service.UploadPhotoAsync(testStream, testKey, "text/plain");

                if (!string.IsNullOrEmpty(uploadedUrl))
                {
                    var uploadedKey = _r2Service.ExtractKeyFromUrl(uploadedUrl);
                    var presignedUrl = await _r2Service.GeneratePresignedUrlAsync(uploadedKey, TimeSpan.FromMinutes(5));
                    await _r2Service.DeletePhotoAsync(uploadedKey);

                    return Ok(new
                    {
                        success = true,
                        message = "R2 connection successful",
                        uploadedUrl = uploadedUrl,
                        uploadedKey = uploadedKey,
                        presignedUrl = presignedUrl,
                        timestamp = DateTime.UtcNow
                    });
                }
                else
                {
                    return BadRequest(new
                    {
                        success = false,
                        message = "R2 connection failed",
                        error = "Upload returned empty URL"
                    });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error testing R2 connection");
                return StatusCode(500, new
                {
                    success = false,
                    message = "R2 connection test error",
                    error = ex.Message
                });
            }
        }

        [HttpPost("upload-test")]
        public async Task<IActionResult> TestUpload([FromForm] IFormFile file)
        {
            try
            {
                if (file == null || file.Length == 0)
                {
                    return BadRequest(new { success = false, message = "No file provided" });
                }

                var uploadedUrl = await _r2Service.UploadPhotoAsync(file);

                if (!string.IsNullOrEmpty(uploadedUrl))
                {
                    var uploadedKey = _r2Service.ExtractKeyFromUrl(uploadedUrl);
                    var presignedUrl = await _r2Service.GeneratePresignedUrlAsync(uploadedKey, TimeSpan.FromHours(1));

                    return Ok(new
                    {
                        success = true,
                        message = "File uploaded successfully",
                        key = uploadedKey,
                        url = uploadedUrl,
                        presignedUrl = presignedUrl,
                        size = file.Length,
                        contentType = file.ContentType
                    });
                }
                else
                {
                    return BadRequest(new
                    {
                        success = false,
                        message = "Upload failed"
                    });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error testing file upload");
                return StatusCode(500, new
                {
                    success = false,
                    message = "Upload test error",
                    error = ex.Message
                });
            }
        }
    }
}
```

---

## ✅ 五、测试验证

### 1. 连接测试
```bash
curl -X GET "http://localhost:5000/api/R2Test/connection"
```

### 2. 文件上传测试
```bash
curl -X POST "http://localhost:5000/api/R2Test/upload-test" \
  -F "file=@test.jpg"
```

### 3. 成功响应示例
```json
{
  "success": true,
  "message": "R2 connection successful",
  "uploadedUrl": "https://account-id.r2.cloudflarestorage.com/water-meter-photos/photos/2025/08/05/42e9f997_111951.txt",
  "uploadedKey": "photos/2025/08/05/42e9f997_111951.txt",
  "presignedUrl": "https://...",
  "timestamp": "2025-08-05T11:19:53.438875Z"
}
```

---

## 🔑 六、关键配置要点

### 1. 必需的兼容性设置
根据 [Cloudflare R2 官方文档](https://developers.cloudflare.com/r2/examples/aws/aws-sdk-net/)，必须设置：

```csharp
DisablePayloadSigning = true,
DisableDefaultChecksumValidation = true
```

⚠️ **重要**：这两个设置是必需的，因为 Cloudflare R2 不支持 AWS SDK 的 Streaming SigV4 实现。

### 2. 简化的客户端配置
```csharp
var config = new AmazonS3Config
{
    ServiceURL = "https://account-id.r2.cloudflarestorage.com"
};
```

### 3. 正确的认证方式
```csharp
var credentials = new Amazon.Runtime.BasicAWSCredentials(accessKey, secretKey);
var s3Client = new AmazonS3Client(credentials, config);
```

---

## 📊 七、费用说明

### 免费额度（每月）
- **存储空间**：10 GB
- **Class A 操作**：100万次（写入、列表）
- **Class B 操作**：1000万次（读取）
- **出站流量**：10 GB

### 超出免费额度的费用
- **存储**：$0.015/GB/月
- **Class A 操作**：$4.50/百万次
- **Class B 操作**：$0.36/百万次
- **出站流量**：$0.09/GB

---

## 🎯 八、总结

通过本指南，你已经成功：

1. ✅ 创建了 Cloudflare R2 存储桶
2. ✅ 配置了 API 访问密钥
3. ✅ 实现了完整的 .NET R2 集成
4. ✅ 验证了上传、下载、删除功能
5. ✅ 生成了预签名 URL

现在你的 .NET 应用已经可以使用 Cloudflare R2 作为对象存储服务，享受高性能、低成本的云存储体验！

---

**最后更新**：2025-08-05
**适用版本**：.NET 6+, AWS SDK for .NET 3.x
