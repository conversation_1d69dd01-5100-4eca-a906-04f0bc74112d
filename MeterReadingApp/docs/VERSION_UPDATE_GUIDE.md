# 版本更新指南

当需要更新MeterReadingApp版本号时，请按照以下步骤修改相关文件：

## 📝 需要修改的文件

### 1. package.json
```json
{
  "version": "新版本号"
}
```

### 2. android/app/build.gradle
```gradle
defaultConfig {
    versionName "新版本号"
    // versionCode 也可能需要递增
}
```

### 3. build-release.ps1 (PowerShell脚本)
更新以下行中的版本号：
- 第2行：`# 版本: 新版本号`
- 第4行：`Write-Host "🚀 开始构建 MeterReadingApp v新版本号 Release 包..."`
- APK路径中的版本号

### 4. build-release.sh (Bash脚本)
更新以下行中的版本号：
- 第4行：`# 版本: 新版本号`
- 第6行：`echo "🚀 开始构建 MeterReadingApp v新版本号 Release 包..."`
- APK路径中的版本号

### 5. DEVELOPMENT_RULES.md
更新构建和版本管理部分：
- 当前版本号
- APK文件名示例

### 6. RELEASE_NOTES.md
- 添加新版本的发布说明
- 更新构建信息中的版本号和APK文件名

## 🚀 APK文件名格式
- **Release**: `water-meter-{版本号}.apk`
- **Debug**: `water-meter-{版本号}-debug.apk`

## 💡 示例：从0.0.1更新到0.0.2

1. package.json: `"version": "0.0.2"`
2. build.gradle: `versionName "0.0.2"`
3. 脚本中所有`0.0.1`替换为`0.0.2`
4. APK文件名变为`water-meter-0.0.2.apk`

## ⚠️ 注意事项
- 确保所有文件中的版本号保持一致
- 构建前测试所有修改是否正确
- 更新版本号后建议重新构建并测试 