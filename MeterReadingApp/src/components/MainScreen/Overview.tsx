import React, {useState} from 'react';
import {Heading, Flex, Pressable, VStack, Switch, Text, Box} from 'native-base';
import {useTheme} from '../../styles/ThemeContext';
import {useNavigation} from '@react-navigation/native';
import {useWindowDimensions} from 'react-native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { RootStackParamList } from '../../navigation/AppNavigator';
import IconWrapper from '../common/IconWrapper';

const boxData = [
  {
    icon: 'water-pump',
    title: 'METER READING',
    screen: 'MeterReading' as keyof RootStackParamList,
    color: '#4CAF50',
  },
  {
    icon: 'clipboard-list',
    title: 'TASK LIST',
    screen: 'TaskList' as keyof RootStackParamList,
    color: '#2196F3',
  },
  {
    icon: 'sync',
    title: 'SYNC DATA',
    screen: 'Sync' as keyof RootStackParamList,
    color: '#FF9800',
  },
  {
    icon: 'chart-bar',
    title: 'REPORTS',
    screen: 'Report' as keyof RootStackParamList,
    color: '#9C27B0',
  },
  {
    icon: 'map-marker',
    title: 'MAP VIEW',
    screen: 'Map' as keyof RootStackParamList,
    color: '#E91E63',
  },
  {
    icon: 'account',
    title: 'USER PROFILE',
    screen: 'UserProfile' as keyof RootStackParamList,
    color: '#F44336',
  },
  {
    icon: 'cog',
    title: 'SETTINGS',
    screen: 'Settings' as keyof RootStackParamList,
    color: '#607D8B',
  },
  {
    icon: 'bug',
    title: 'DEBUG DATA',
    screen: 'DebugData' as keyof RootStackParamList,
    color: '#FF5722',
  },
];

type NavigationProp = NativeStackNavigationProp<RootStackParamList>;

const Overview: React.FC = () => {
  const navigation = useNavigation<NavigationProp>();
  const {mode} = useTheme();
  const {width, height} = useWindowDimensions();
  const [pressedIndex, setPressedIndex] = useState<number | null>(null);
  const [isOfflineMode, setIsOfflineMode] = useState(false);

  const toggleOfflineMode = async () => {
    const newValue = !isOfflineMode;
    setIsOfflineMode(newValue);
  };

  const networkModeToggle = (
    <Box
      position="absolute"
      top={-30}
      right={-30}
      zIndex={1000}
      bg={mode === 'dark' ? 'gray.800' : 'white'}
      borderRadius="full"
      shadow={1}
      px={1.5}
      py={0.5}
      flexDirection="row"
      alignItems="center"
    >
      <IconWrapper
        name={isOfflineMode ? "wifi-off" : "wifi"}
        size="2xs"
        color={isOfflineMode ? "gray.400" : "green.500"}
        mr={0.5}
        accessibilityLabel={isOfflineMode ? "Offline mode" : "Online mode"}
      />
      <Switch
        size="sm"
        onToggle={toggleOfflineMode}
        isChecked={!isOfflineMode}
        accessibilityLabel="Toggle network mode"
      />
    </Box>
  );

  const handlePress = (key: number) => {
    const targetScreen = boxData[key - 1].screen;
    setPressedIndex(null);
    
    switch (targetScreen) {
      case 'MeterReading':
        navigation.navigate('TaskList');
        break;
      case 'TaskList':
        navigation.navigate('TaskList');
        break;
      case 'Sync':
        navigation.navigate('Sync');
        break;
      case 'Report':
        navigation.navigate('Report');
        break;
      case 'Map':
        navigation.navigate('Map');
        break;
      case 'UserProfile':
        navigation.navigate('UserProfile');
        break;
      case 'Settings':
        navigation.navigate('Settings');
        break;
      case 'DebugData':
        navigation.navigate('DebugData');
        break;
      default:
        console.log('Screen not implemented:', targetScreen);
    }
  };

  const boxContent = (key: number) => (
    <Pressable
      onPress={() => handlePress(key)}
      onPressIn={() => setPressedIndex(key)}
      onPressOut={() => setPressedIndex(null)}
      _pressed={{
        opacity: 0.5,
      }}
      key={key}
      rounded="xl"
      overflow="hidden"
      width="48%"
      height={height * 0.16}
      justifyContent="center"
      alignItems="center"
      mb={3}>
      <VStack space={2} alignItems="center">
        <IconWrapper
          name={boxData[key - 1].icon}
          size={width > 600 ? 14 : 12}
          color={pressedIndex === key ? 'gray.500' : boxData[key - 1].color}
          accessibilityLabel={boxData[key - 1].title}
        />
        <Heading
          size={width > 600 ? 'md' : 'sm'}
          color={mode === 'dark' ? 'white' : 'black'}
          fontFamily="monospace"
          textAlign="center"
          numberOfLines={2}>
          {boxData[key - 1].title}
        </Heading>
      </VStack>
    </Pressable>
  );

  return (
    <Box position="relative" flex={1}>
      {networkModeToggle}
      <Box 
        flex={1}
        pt="0%">
        <Flex
          direction="row"
          wrap="wrap"
          justifyContent="space-between"
          alignItems="flex-start"
          px={0}
          pt={6}
          pb={6}>
          {Array.from({length: boxData.length}, (_, i) => boxContent(i + 1))}
        </Flex>
      </Box>
    </Box>
  );
};

export default Overview; 