import React from 'react';
import {View, Text, ActivityIndicator} from 'react-native';

interface LoadingSpinnerProps {
  size?: 'small' | 'large';
  color?: string;
  message?: string;
  overlay?: boolean;
}

export const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  size = 'large',
  color = '#007AFF',
  message,
  overlay = false,
}) => {
  const content = (
    <View style={{
      alignItems: 'center',
      justifyContent: 'center',
      flex: overlay ? 1 : 0,
      minHeight: overlay ? 0 : 100,
    }}>
      <ActivityIndicator size={size} color={color} />
      {message && (
        <Text style={{
          textAlign: 'center', 
          color: '#666', 
          fontSize: 14,
          marginTop: 16,
          paddingHorizontal: 20,
        }}>
          {message}
        </Text>
      )}
    </View>
  );

  if (overlay) {
    return (
      <View
        style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: 'rgba(255, 255, 255, 0.8)',
          zIndex: 1000,
          justifyContent: 'center',
          alignItems: 'center',
        }}>
        {content}
      </View>
    );
  }

  return content;
}; 