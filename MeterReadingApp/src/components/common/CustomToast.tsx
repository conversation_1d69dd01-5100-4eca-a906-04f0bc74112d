import React from 'react';
import { <PERSON><PERSON>, VS<PERSON>ck, <PERSON><PERSON><PERSON>ck, Text, CloseIcon, IconButton } from 'native-base';
import IconWrapper from './IconWrapper';
import { useTheme } from '../../styles/ThemeContext';

export interface CustomToastProps {
  id: string;
  title: string;
  description: string;
  type: 'success' | 'error' | 'warning' | 'info';
  isClosable?: boolean;
  onClose: () => void;
}

const CustomToast: React.FC<CustomToastProps> = ({
  id,
  title,
  description,
  type,
  isClosable = true,
  onClose,
}) => {
  const { mode } = useTheme();

  const getTypeConfig = (type: string) => {
    switch (type) {
      case 'success':
        return {
          icon: 'check',
          bgColor: mode === 'dark' ? 'green.800' : 'green.50',
          borderColor: 'green.500',
          textColor: mode === 'dark' ? 'green.100' : 'green.800',
          iconColor: 'green.500',
        };
      case 'error':
        return {
          icon: 'alert',
          bgColor: mode === 'dark' ? 'red.800' : 'red.50',
          borderColor: 'red.500',
          textColor: mode === 'dark' ? 'red.100' : 'red.800',
          iconColor: 'red.500',
        };
      case 'warning':
        return {
          icon: 'alert',
          bgColor: mode === 'dark' ? 'yellow.800' : 'yellow.50',
          borderColor: 'yellow.500',
          textColor: mode === 'dark' ? 'yellow.100' : 'yellow.800',
          iconColor: 'yellow.500',
        };
      case 'info':
      default:
        return {
          icon: 'information',
          bgColor: mode === 'dark' ? 'blue.800' : 'blue.50',
          borderColor: 'blue.500',
          textColor: mode === 'dark' ? 'blue.100' : 'blue.800',
          iconColor: 'blue.500',
        };
    }
  };

  const config = getTypeConfig(type);

  return (
    <Alert
      maxW="90%"
      alignSelf="center"
      flexDirection="row"
      status={type as any}
      variant="left-accent"
      bg={config.bgColor}
      borderColor={config.borderColor}
      borderWidth={1}
      borderRadius="lg"
      shadow={3}
    >
      <VStack space={2} flexShrink={1} w="100%">
        <HStack flexShrink={1} space={2} alignItems="center" justifyContent="space-between">
          <HStack space={2} alignItems="center" flexShrink={1}>
            <IconWrapper
              name={config.icon}
              size={5}
              color={config.iconColor}
              label={type}
            />
            <VStack flexShrink={1}>
              <Text
                fontSize="md"
                fontWeight="bold"
                color={config.textColor}
              >
                {title}
              </Text>
              <Text
                fontSize="sm"
                color={config.textColor}
                opacity={0.8}
              >
                {description}
              </Text>
            </VStack>
          </HStack>
          {isClosable && (
            <IconButton
              variant="unstyled"
              icon={<CloseIcon size="3" />}
              _icon={{
                color: config.textColor,
              }}
              onPress={onClose}
            />
          )}
        </HStack>
      </VStack>
    </Alert>
  );
};

export default CustomToast; 