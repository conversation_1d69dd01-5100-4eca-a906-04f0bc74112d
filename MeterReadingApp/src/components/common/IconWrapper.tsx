import React from 'react';
import { Icon, IIconProps } from 'native-base';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';

interface IconWrapperProps extends IIconProps {
  name: string;
  label?: string;
  autoLabel?: boolean;
}

// Icon name mapping to handle problematic icons
const ICON_NAME_MAP: { [key: string]: string } = {
  'chart': 'chart-bar', // Keep the beautiful chart-bar icon
  'network': 'wifi',
  'help-circle': 'help-circle-outline',
  'map': 'map-marker', // Keep the beautiful map-marker icon
  // Add more mappings as needed
};

const IconWrapper: React.FC<IconWrapperProps> = ({ 
  name, 
  label, 
  autoLabel = true, 
  accessibilityLabel,
  ...props 
}) => {
  // Auto-generate label from icon name if not provided
  const generateLabel = (iconName: string) => {
    return iconName
      .split('-')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ') + ' icon';
  };

  const finalLabel = accessibilityLabel || label || (autoLabel ? generateLabel(name) : 'Icon');
  
  // Map problematic icon names to valid ones
  const mappedIconName = ICON_NAME_MAP[name] || name;

  // Try to use NativeBase Icon first, fall back to direct MaterialCommunityIcons on error
  try {
    return (
      <Icon
        as={MaterialCommunityIcons}
        name={mappedIconName}
        accessibilityLabel={finalLabel}
        accessible={true}
        {...props}
      />
    );
  } catch (error) {
    // If NativeBase Icon fails, use MaterialCommunityIcons directly
    console.warn(`Icon validation failed for '${name}', using direct MaterialCommunityIcons`);
    return (
      <Icon
        as={MaterialCommunityIcons}
        name={name} // Use original name for direct access
        accessibilityLabel={finalLabel}
        accessible={true}
        {...props}
      />
    );
  }
};

export default IconWrapper; 