import React from 'react';
import { Box, VStack, HStack, Text, Pressable } from 'native-base';

interface ToastAlertProps {
  id: string;
  title: string;
  description: string;
  isClosable?: boolean;
  onClose: (id: string) => void;
  variant?: 'left-accent' | 'top-accent' | 'outline' | 'subtle' | 'solid';
  status?: 'info' | 'warning' | 'success' | 'error' | 'loading';
}

const ToastAlert: React.FC<ToastAlertProps> = ({
  id,
  title,
  description,
  isClosable = false,
  onClose,
  variant = 'left-accent',
  status = 'info'
}) => {
  // Determine colors based on status
  const getStatusColors = () => {
    switch (status) {
      case 'success':
        return {
          bg: 'green.50',
          borderColor: 'green.500',
          iconColor: 'green.500',
          titleColor: 'green.800',
          descColor: 'green.700',
          icon: '✓'
        };
      case 'error':
        return {
          bg: 'red.50',
          borderColor: 'red.500',
          iconColor: 'red.500',
          titleColor: 'red.800',
          descColor: 'red.700',
          icon: '✕'
        };
      case 'warning':
        return {
          bg: 'yellow.50',
          borderColor: 'yellow.500',
          iconColor: 'yellow.500',
          titleColor: 'yellow.800',
          descColor: 'yellow.700',
          icon: '⚠'
        };
      default:
        return {
          bg: 'blue.50',
          borderColor: 'blue.500',
          iconColor: 'blue.500',
          titleColor: 'blue.800',
          descColor: 'blue.700',
          icon: 'ℹ'
        };
    }
  };

  const colors = getStatusColors();

  return (
    <Box
      maxWidth="100%"
      alignSelf="center"
      bg={colors.bg}
      borderLeftWidth={variant === 'left-accent' ? 4 : 0}
      borderTopWidth={variant === 'top-accent' ? 4 : 0}
      borderColor={colors.borderColor}
      borderRadius="md"
      p={4}
      shadow={2}
    >
      <HStack space={3} alignItems="flex-start" justifyContent="space-between">
        <HStack space={3} flex={1} alignItems="flex-start">
          <Text fontSize="lg" color={colors.iconColor} fontWeight="bold">
            {colors.icon}
          </Text>
          <VStack space={1} flex={1}>
            <Text fontSize="md" fontWeight="bold" color={colors.titleColor}>
              {title}
            </Text>
            <Text fontSize="sm" color={colors.descColor}>
              {description}
            </Text>
          </VStack>
        </HStack>
        {isClosable && (
          <Pressable
            onPress={() => onClose(id)}
            p={1}
            borderRadius="sm"
            _pressed={{ bg: 'gray.200' }}
          >
            <Text fontSize="md" color="gray.500" fontWeight="bold">
              ✕
            </Text>
          </Pressable>
        )}
      </HStack>
    </Box>
  );
};

export default ToastAlert; 