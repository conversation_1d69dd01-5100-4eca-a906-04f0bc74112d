import React from 'react';
import {
  Box,
  Image,
  Text,
  HS<PERSON>ck,
  VS<PERSON><PERSON>,
  <PERSON><PERSON>iew,
  Spinner,
  <PERSON>ge,
  Pressable
} from 'native-base';
import { useTheme } from '../styles/ThemeContext';
import { ReadingPhoto } from '../database/models/MeterReading';
import { IconWrapper } from './common';

interface PhotoPreviewComponentProps {
  photos: ReadingPhoto[];
  uploadProgress: {[key: number]: boolean};
  onPhotoPress?: (photo: ReadingPhoto) => void;
  onDeletePhoto?: (photoId: number) => void;
}

export const PhotoPreviewComponent: React.FC<PhotoPreviewComponentProps> = ({
  photos,
  uploadProgress,
  onPhotoPress,
  onDeletePhoto
}) => {
  const { mode } = useTheme();

  if (photos.length === 0) {
    return null;
  }

  return (
    <Box
      bg={mode === 'dark' ? 'gray.800' : 'white'}
      borderRadius="xl"
      p={4}
      shadow={2}
      borderWidth={1}
      borderColor={mode === 'dark' ? 'gray.700' : 'gray.200'}
    >
      <HStack justifyContent="space-between" alignItems="center" mb={3}>
        <Text fontSize="md" fontWeight="bold" color="blue.600">
          Captured Photos
        </Text>
        <Badge colorScheme="blue" variant="solid">
          {photos.length}
        </Badge>
      </HStack>
      
      <ScrollView horizontal showsHorizontalScrollIndicator={false}>
        <HStack space={3}>
          {photos.map((photo, index) => (
            <Box key={photo.id || index} position="relative">
              <Pressable
                onPress={() => onPhotoPress?.(photo)}
                borderRadius="lg"
                overflow="hidden"
              >
                <Image
                  source={{ uri: photo.file_path }}
                  alt={`Photo ${index + 1}`}
                  width={24}
                  height={24}
                  borderRadius="lg"
                />
                
                {uploadProgress[photo.id!] && (
                  <Box
                    position="absolute"
                    top={0}
                    left={0}
                    right={0}
                    bottom={0}
                    bg="rgba(0,0,0,0.6)"
                    borderRadius="lg"
                    justifyContent="center"
                    alignItems="center"
                  >
                    <Spinner color="white" size="sm" />
                    <Text color="white" fontSize="xs" mt={1}>
                      Uploading...
                    </Text>
                  </Box>
                )}

                {photo.sync_status === 'synced' && (
                  <Box
                    position="absolute"
                    top={1}
                    right={1}
                    bg="green.500"
                    borderRadius="full"
                    p={1}
                  >
                    <IconWrapper 
                      name="check" 
                      size={3} 
                      color="white" 
                      label="Synced" 
                    />
                  </Box>
                )}

                {photo.sync_status === 'error' && (
                  <Box
                    position="absolute"
                    top={1}
                    right={1}
                    bg="red.500"
                    borderRadius="full"
                    p={1}
                  >
                    <IconWrapper 
                      name="exclamation-triangle" 
                      size={3} 
                      color="white" 
                      label="Error" 
                    />
                  </Box>
                )}
              </Pressable>

              {onDeletePhoto && (
                <Pressable
                  position="absolute"
                  top={-2}
                  left={-2}
                  bg="red.500"
                  borderRadius="full"
                  p={1}
                  onPress={() => onDeletePhoto(photo.id!)}
                >
                  <IconWrapper 
                    name="times" 
                    size={3} 
                    color="white" 
                    label="Delete" 
                  />
                </Pressable>
              )}

              <VStack mt={2} alignItems="center" maxWidth={24}>
                <Text 
                  fontSize="xs" 
                  color={mode === 'dark' ? 'gray.400' : 'gray.600'}
                  numberOfLines={1}
                >
                  {photo.filename}
                </Text>
                <Text 
                  fontSize="xs" 
                  color={mode === 'dark' ? 'gray.500' : 'gray.500'}
                >
                  {(photo.file_size / 1024).toFixed(1)}KB
                </Text>
              </VStack>
            </Box>
          ))}
        </HStack>
      </ScrollView>

      <VStack mt={3} space={1}>
        <HStack justifyContent="space-between">
          <Text fontSize="xs" color={mode === 'dark' ? 'gray.400' : 'gray.600'}>
            Synced: {photos.filter(p => p.sync_status === 'synced').length}
          </Text>
          <Text fontSize="xs" color={mode === 'dark' ? 'gray.400' : 'gray.600'}>
            Pending: {photos.filter(p => p.sync_status === 'pending').length}
          </Text>
          <Text fontSize="xs" color={mode === 'dark' ? 'gray.400' : 'gray.600'}>
            Failed: {photos.filter(p => p.sync_status === 'error').length}
          </Text>
        </HStack>
      </VStack>
    </Box>
  );
};
