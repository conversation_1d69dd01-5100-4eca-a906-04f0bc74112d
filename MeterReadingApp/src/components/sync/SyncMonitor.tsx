// Enhanced sync monitoring component
// Displays background sync statistics, network quality, and task status

import React, { useState, useEffect } from 'react';
import { 
  Box, 
  VStack, 
  HStack, 
  Text, 
  Badge, 
  Progress,
  Divider,
  ScrollView,
  Pressable,
  Spinner,
} from 'native-base';
import { useTheme } from '../../styles/ThemeContext';
import { IconWrapper } from '../common';
import { MeterReadingBackgroundSync } from '../../services/sync/BackgroundSync';

interface SyncMonitorProps {
  showDetails?: boolean;
  compact?: boolean;
}

const SyncMonitor: React.FC<SyncMonitorProps> = ({ 
  showDetails = false, 
  compact = false 
}) => {
  const { mode } = useTheme();
  const [statistics, setStatistics] = useState<any>(null);
  const [taskStatus, setTaskStatus] = useState<any[]>([]);
  const [currentTask, setCurrentTask] = useState<string | null>(null);
  const [isRefreshing, setIsRefreshing] = useState(false);

  useEffect(() => {
    loadSyncData();
    
    // Refresh every 10 seconds
    const interval = setInterval(loadSyncData, 10000);
    
    return () => clearInterval(interval);
  }, []);

  const loadSyncData = async () => {
    try {
      const stats = MeterReadingBackgroundSync.getStatistics();
      const tasks = MeterReadingBackgroundSync.getTaskStatus();
      const current = MeterReadingBackgroundSync.getCurrentTask();
      
      setStatistics(stats);
      setTaskStatus(tasks);
      setCurrentTask(current);
    } catch (error) {
      console.error('SyncMonitor: Error loading sync data:', error);
    }
  };

  const handleRefresh = async () => {
    setIsRefreshing(true);
    await loadSyncData();
    setIsRefreshing(false);
  };

  const handleForceSync = async (taskName?: string) => {
    try {
      await MeterReadingBackgroundSync.forceSync(taskName);
      await loadSyncData();
    } catch (error) {
      console.error('SyncMonitor: Error forcing sync:', error);
    }
  };

  const getNetworkQualityColor = (quality: string) => {
    switch (quality) {
      case 'excellent': return 'green.500';
      case 'good': return 'blue.500';
      case 'poor': return 'orange.500';
      case 'offline': return 'red.500';
      default: return 'gray.500';
    }
  };

  const getNetworkQualityIcon = (quality: string) => {
    switch (quality) {
      case 'excellent': return 'wifi';
      case 'good': return 'signal-cellular-3';
      case 'poor': return 'signal-cellular-1';
      case 'offline': return 'wifi-off';
      default: return 'help-circle';
    }
  };

  const getTaskPriorityColor = (taskName: string) => {
    if (taskName.includes('Critical')) return 'red.500';
    if (taskName.includes('High')) return 'orange.500';
    if (taskName.includes('Task')) return 'blue.500';
    return 'gray.500';
  };

  const formatLastRun = (timestamp?: number) => {
    if (!timestamp) return 'Never';
    const now = Date.now();
    const diff = now - timestamp;
    
    if (diff < 60000) return 'Just now';
    if (diff < 3600000) return `${Math.floor(diff / 60000)}m ago`;
    if (diff < 86400000) return `${Math.floor(diff / 3600000)}h ago`;
    return `${Math.floor(diff / 86400000)}d ago`;
  };

  if (!statistics) {
    return (
      <Box p={4} alignItems="center">
        <Spinner size="sm" />
        <Text fontSize="sm" color="gray.500" mt={2}>Loading sync status...</Text>
      </Box>
    );
  }

  if (compact) {
    return (
      <Box
        bg={mode === 'dark' ? 'gray.800' : 'white'}
        borderRadius="lg"
        p={3}
        shadow={1}
        borderWidth={1}
        borderColor={mode === 'dark' ? 'gray.700' : 'gray.100'}
      >
        <HStack alignItems="center" space={3}>
          <IconWrapper 
            name={getNetworkQualityIcon(statistics.networkQuality)} 
            size={4} 
            color={getNetworkQualityColor(statistics.networkQuality)} 
            label="Network" 
          />
          <VStack flex={1}>
            <Text fontSize="sm" fontWeight="medium" color={mode === 'dark' ? 'white' : 'gray.800'}>
              {currentTask ? `Syncing: ${currentTask}` : 'Background Sync Active'}
            </Text>
            <Text fontSize="xs" color={mode === 'dark' ? 'gray.400' : 'gray.600'}>
              {statistics.totalRuns > 0 
                ? `${Math.round((statistics.successfulRuns / statistics.totalRuns) * 100)}% success rate`
                : 'No runs yet'
              }
            </Text>
          </VStack>
          {currentTask && <Spinner size="sm" color="orange.500" />}
        </HStack>
      </Box>
    );
  }

  const successRate = statistics.totalRuns > 0 
    ? (statistics.successfulRuns / statistics.totalRuns) * 100 
    : 0;

  return (
    <Box
      bg={mode === 'dark' ? 'gray.800' : 'white'}
      borderRadius="xl"
      shadow={2}
      borderWidth={1}
      borderColor={mode === 'dark' ? 'gray.700' : 'gray.100'}
    >
      {/* Header */}
      <HStack alignItems="center" justifyContent="space-between" p={4} pb={2}>
        <HStack alignItems="center" space={3}>
          <IconWrapper name="chart-line" size={5} color="orange.500" label="Sync Monitor" />
          <Text fontSize="lg" fontWeight="bold" color={mode === 'dark' ? 'white' : 'gray.800'}>
            Sync Monitor
          </Text>
        </HStack>
        <Pressable onPress={handleRefresh} disabled={isRefreshing}>
          <IconWrapper 
            name={isRefreshing ? "refresh" : "refresh"}
            size={4} 
            color="gray.500" 
            label="Refresh" 
          />
        </Pressable>
      </HStack>

      <ScrollView maxHeight={400}>
        <VStack space={3} p={4} pt={2}>
          {/* Network Quality */}
          <Box>
            <HStack alignItems="center" justifyContent="space-between" mb={2}>
              <Text fontSize="md" fontWeight="medium" color={mode === 'dark' ? 'white' : 'gray.800'}>
                Network Quality
              </Text>
              <Badge
                colorScheme={getNetworkQualityColor(statistics.networkQuality).split('.')[0]}
                variant="solid"
                borderRadius="lg"
                leftIcon={
                  <IconWrapper 
                    name={getNetworkQualityIcon(statistics.networkQuality)} 
                    size={3} 
                    color="white" 
                    label="Network Quality" 
                  />
                }
              >
                {statistics.networkQuality.toUpperCase()}
              </Badge>
            </HStack>
          </Box>

          <Divider />

          {/* Success Rate */}
          <Box>
            <HStack alignItems="center" justifyContent="space-between" mb={2}>
              <Text fontSize="md" fontWeight="medium" color={mode === 'dark' ? 'white' : 'gray.800'}>
                Success Rate
              </Text>
              <Text fontSize="sm" color={mode === 'dark' ? 'gray.400' : 'gray.600'}>
                {statistics.successfulRuns}/{statistics.totalRuns} runs
              </Text>
            </HStack>
            <Progress 
              value={successRate} 
              colorScheme={successRate > 80 ? "green" : successRate > 60 ? "orange" : "red"}
              size="sm"
              borderRadius="full"
            />
            <Text fontSize="xs" color={mode === 'dark' ? 'gray.400' : 'gray.600'} mt={1}>
              {successRate.toFixed(1)}% success rate
            </Text>
          </Box>

          <Divider />

          {/* Current Activity */}
          {currentTask ? (
            <Box>
              <HStack alignItems="center" space={2} mb={2}>
                <Spinner size="sm" color="orange.500" />
                <Text fontSize="md" fontWeight="medium" color={mode === 'dark' ? 'white' : 'gray.800'}>
                  Currently Running
                </Text>
              </HStack>
              <Text fontSize="sm" color="orange.500" fontWeight="medium">
                {currentTask}
              </Text>
            </Box>
          ) : null}

          {/* Task Status */}
          {showDetails && (
            <>
              <Divider />
              <Box>
                <HStack alignItems="center" justifyContent="space-between" mb={3}>
                  <Text fontSize="md" fontWeight="medium" color={mode === 'dark' ? 'white' : 'gray.800'}>
                    Background Tasks
                  </Text>
                  <Pressable onPress={() => handleForceSync()}>
                    <Text fontSize="sm" color="orange.500">Force Sync All</Text>
                  </Pressable>
                </HStack>
                
                <VStack space={2}>
                  {taskStatus.map((task, index) => (
                    <Box key={index}>
                      <HStack alignItems="center" justifyContent="space-between">
                        <VStack flex={1} alignItems="flex-start">
                          <HStack alignItems="center" space={2}>
                            <IconWrapper 
                              name="circle" 
                              size={3} 
                              color={getTaskPriorityColor(task.name)} 
                              label="Priority" 
                            />
                            <Text 
                              fontSize="sm" 
                              fontWeight="medium"
                              color={mode === 'dark' ? 'white' : 'gray.800'}
                            >
                              {task.name}
                            </Text>
                            {task.retryCount && task.retryCount > 0 ? (
                              <Badge colorScheme="orange" variant="outline" size="sm">
                                Retry {task.retryCount}
                              </Badge>
                            ) : null}
                          </HStack>
                          <Text fontSize="xs" color={mode === 'dark' ? 'gray.400' : 'gray.600'}>
                            Last run: {formatLastRun(task.lastRun)}
                            {task.consecutiveFailures && task.consecutiveFailures > 0 
                              ? ` • ${task.consecutiveFailures} failures`
                              : ''
                            }
                          </Text>
                          {task.lastError ? (
                            <Text fontSize="xs" color="red.500" numberOfLines={1}>
                              Error: {task.lastError}
                            </Text>
                          ) : null}
                        </VStack>
                        <Pressable onPress={() => handleForceSync(task.name)}>
                          <IconWrapper name="play" size={4} color="gray.500" label="Force Run" />
                        </Pressable>
                      </HStack>
                      {index < taskStatus.length - 1 && <Divider mt={2} />}
                    </Box>
                  ))}
                </VStack>
              </Box>
            </>
          )}

          {/* Statistics Summary */}
          <Divider />
          <Box>
            <Text fontSize="md" fontWeight="medium" color={mode === 'dark' ? 'white' : 'gray.800'} mb={2}>
              Statistics
            </Text>
            <VStack space={1}>
              <HStack justifyContent="space-between">
                <Text fontSize="sm" color={mode === 'dark' ? 'gray.400' : 'gray.600'}>Total Runs:</Text>
                <Text fontSize="sm" color={mode === 'dark' ? 'white' : 'gray.800'}>{statistics.totalRuns}</Text>
              </HStack>
              <HStack justifyContent="space-between">
                <Text fontSize="sm" color={mode === 'dark' ? 'gray.400' : 'gray.600'}>Successful:</Text>
                <Text fontSize="sm" color="green.500">{statistics.successfulRuns}</Text>
              </HStack>
              <HStack justifyContent="space-between">
                <Text fontSize="sm" color={mode === 'dark' ? 'gray.400' : 'gray.600'}>Failed:</Text>
                <Text fontSize="sm" color="red.500">{statistics.failedRuns}</Text>
              </HStack>
              {statistics.averageUploadTime > 0 ? (
                <HStack justifyContent="space-between">
                  <Text fontSize="sm" color={mode === 'dark' ? 'gray.400' : 'gray.600'}>Avg Time:</Text>
                  <Text fontSize="sm" color={mode === 'dark' ? 'white' : 'gray.800'}>
                    {Math.round(statistics.averageUploadTime / 1000)}s
                  </Text>
                </HStack>
              ) : null}
              {statistics.lastSuccessfulSync ? (
                <HStack justifyContent="space-between">
                  <Text fontSize="sm" color={mode === 'dark' ? 'gray.400' : 'gray.600'}>Last Success:</Text>
                  <Text fontSize="sm" color={mode === 'dark' ? 'white' : 'gray.800'}>
                    {formatLastRun(statistics.lastSuccessfulSync)}
                  </Text>
                </HStack>
              ) : null}
            </VStack>
          </Box>
        </VStack>
      </ScrollView>
    </Box>
  );
};

export default SyncMonitor; 