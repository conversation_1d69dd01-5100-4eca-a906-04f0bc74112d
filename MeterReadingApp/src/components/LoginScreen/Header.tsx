import React from 'react';
import {VStack, Text, Heading, Icon} from 'native-base';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import {useTheme} from '../../styles/ThemeContext';
import TypewriterEffect from './TypewriterEffect';
import {useWindowDimensions} from 'react-native';

const Header: React.FC = () => {
  const {mode} = useTheme();
  const {width} = useWindowDimensions();

  return (
    <VStack space={3} alignItems="center">
      {/* App Logo */}
      <VStack space={2} alignItems="center">
        <Icon
          as={MaterialCommunityIcons}
          name="water"
          size={width > 600 ? 20 : 16}
          color="#FF620A"
          accessibilityLabel="Water meter application logo"
        />
        <Heading
          size={width > 600 ? "xl" : "lg"}
          textAlign="center"
          color={mode === 'dark' ? 'white' : 'black'}
          fontFamily="monospace">
          <TypewriterEffect text="WATER METER Reading System" />
        </Heading>
      </VStack>
    </VStack>
  );
};

export default Header; 