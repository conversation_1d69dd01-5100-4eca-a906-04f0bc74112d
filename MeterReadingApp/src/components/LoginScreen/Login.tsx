import React, { useState, useEffect } from 'react';
import {
  Input,
  Pressable,
  Button,
  VStack,
  HStack,
  Text,
  FormControl,
  Alert,
  Checkbox,
} from 'native-base';
import { IconWrapper } from '../common';
import { useToastManager } from '../../utils/ToastManager';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { RootStackParamList } from '../../navigation/AppNavigator';
import { blackSubtleButtonStyle, orangeOutlineButtonStyle } from '../../styles/buttonStyles';
import { useTheme } from '../../styles/ThemeContext';
import { useAuth } from '../../context/AuthContext';
import { AuthService } from '../../services/AuthService';
import { useWindowDimensions } from 'react-native';
import { getApiBaseUrl } from '../../config/apiConfig';

type LoginScreenNavigationProp = NativeStackNavigationProp<
  RootStackParamList,
  'Login'
>;

const Login: React.FC = () => {
  const [showPassword, setShowPassword] = useState(false);
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [rememberMe, setRememberMe] = useState(false);
  const [debugError, setDebugError] = useState<string>('');

  const navigation = useNavigation<LoginScreenNavigationProp>();
  const { mode } = useTheme();
  const { state, login, clearError } = useAuth();
  const toastManager = useToastManager();
  const { width } = useWindowDimensions();

  useEffect(() => {
    const loadStoredCredentials = async () => {
      const storedCredentials = await AuthService.getStoredCredentials();
      if (storedCredentials) {
        setUsername(storedCredentials.username);
        setPassword(storedCredentials.password);
        setRememberMe(true);
      }
    };

    loadStoredCredentials();
  }, []);

  const handleLogin = async () => {
    if (!username || !password) {
      toastManager.loginError();
      setDebugError('Please enter your username and password.');
      return;
    }

    try {
      setDebugError('');
      await login(username, password, rememberMe);
      toastManager.loginSuccess();
    } catch (error: any) {
      // Error is already handled by the context
      console.error('Login error:', error);

      // 设置详细的调试错误信息
      let errorMessage = 'Login error: ';
      if (error.message) {
        errorMessage += error.message;
      }
      if (error.response) {
        errorMessage += ` (status: ${error.response.status})`;
        if (error.response.data) {
          errorMessage += ` - ${JSON.stringify(error.response.data)}`;
        }
      }
      if (error.code) {
        errorMessage += ` (error code: ${error.code})`;
      }

      setDebugError(errorMessage);
    }
  };

  // Clear error when component unmounts or values change
  useEffect(() => {
    if (state.error) {
      clearError();
    }
  }, [username, password]);

  const inputSize = '2xl';
  const buttonSize = '2xl';

  return (
    <VStack space={4} alignItems="center" width="100%">
      {state.error ? (
        <Alert status="error" width="100%" borderRadius="lg">
          <Alert.Icon />
          <VStack space={2} flexShrink={1} width="100%">
            <Text color="red.600" fontWeight="medium">Login Failed</Text>
            <Text color="red.600">{state.error}</Text>
          </VStack>
        </Alert>
      ) : null}

      {debugError ? (
        <Alert status="warning" width="100%" borderRadius="lg">
          <Alert.Icon />
          <VStack space={2} flexShrink={1} width="100%">
            <Text color="orange.600" fontWeight="medium">test info</Text>
            <Text color="orange.600" fontSize="xs">{debugError}</Text>
            <Text color="orange.600" fontSize="xs">API: {getApiBaseUrl()}</Text>
          </VStack>
        </Alert>
      ) : null}

      <FormControl>
        <FormControl.Label
          _text={{
            fontSize: 'md',
            fontWeight: 'bold',
            color: mode === 'dark' ? 'white' : 'black',
          }}
        >
          Username
        </FormControl.Label>
        <Input
          size={inputSize}
          variant="filled"
          InputLeftElement={
            <IconWrapper
              name="account"
              size={5}
              ml={3}
              color={mode === 'dark' ? 'gray.400' : 'gray.500'}
              label="Username"
            />
          }
          placeholder="firstname.lastname"
          value={username}
          onChangeText={setUsername}
          backgroundColor={mode === 'dark' ? 'gray.700' : 'white'}
          color={mode === 'dark' ? 'white' : 'black'}
          placeholderTextColor={mode === 'dark' ? 'gray.400' : 'gray.500'}
          width="100%"
          fontSize="md"
          height={12}
          borderRadius="lg"
          autoCapitalize="none"
          autoCorrect={false}
          _focus={{
            borderColor: "#FF620A",
            backgroundColor: mode === 'dark' ? 'gray.600' : 'gray.50',
          }}
        />
      </FormControl>

      <FormControl>
        <FormControl.Label
          _text={{
            fontSize: 'md',
            fontWeight: 'bold',
            color: mode === 'dark' ? 'white' : 'black',
          }}
        >
          Password
        </FormControl.Label>
        <Input
          size={inputSize}
          variant="filled"
          type={showPassword ? 'text' : 'password'}
          InputRightElement={
            <Pressable
              onPress={() => setShowPassword(!showPassword)}
              hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
            >
              <IconWrapper
                name={showPassword ? 'eye' : 'eye-off'}
                size={5}
                mr={3}
                color={mode === 'dark' ? 'gray.400' : 'gray.500'}
                label={showPassword ? "Hide password" : "Show password"}
              />
            </Pressable>
          }
          placeholder="Please enter your password"
          value={password}
          onChangeText={setPassword}
          backgroundColor={mode === 'dark' ? 'gray.700' : 'white'}
          color={mode === 'dark' ? 'white' : 'black'}
          placeholderTextColor={mode === 'dark' ? 'gray.400' : 'gray.500'}
          width="100%"
          fontSize="md"
          height={12}
          borderRadius="lg"
          autoCapitalize="none"
          autoCorrect={false}
          _focus={{
            borderColor: "#FF620A",
            backgroundColor: mode === 'dark' ? 'gray.600' : 'gray.50',
          }}
        />
      </FormControl>

      <HStack space={2} alignItems="center" alignSelf="flex-start" width="100%" mt={2}>
        <Checkbox
          value="remember"
          isChecked={rememberMe}
          onChange={setRememberMe}
          accessibilityLabel="Remember login credentials"
          accessibilityHint="Check this to save your login information"
          size="sm"
          colorScheme="orange"
        >
          <Text color={mode === 'dark' ? 'white' : 'black'} fontSize="sm">
            Remember Me
          </Text>
        </Checkbox>
      </HStack>

      <Button
        size={buttonSize}
        onPress={handleLogin}
        width="100%"
        isLoading={state.isLoading}
        isLoadingText="Logging in..."
        isDisabled={state.isLoading}
        height={12}
        borderRadius="lg"
        mt={4}
        leftIcon={
          <IconWrapper
            name="login"
            size={4}
            color="white"
            label="Login"
          />
        }
        bg="#FF620A"
        _pressed={{
          bg: "#E5520A",
        }}
        _disabled={{
          bg: "gray.400",
        }}
        _text={{ 
          fontSize: 'lg',
          fontWeight: 'bold',
          color: 'white'
        }}
      >
        Login
      </Button>
    </VStack>
  );
};

export default Login; 