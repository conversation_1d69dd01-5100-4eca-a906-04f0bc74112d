import React, { useState, useEffect } from 'react';
import {
  Box,
  VStack,
  H<PERSON>tack,
  Text,
  <PERSON><PERSON>,
  Badge,
  Divider,
  ScrollView,
  Alert,
  CloseIcon,
  IconButton,
} from 'native-base';
import { Platform } from 'react-native';
import { useTheme } from '../../styles/ThemeContext';
import { IconWrapper } from '../common';
import { testApiConnection, getIPAddressInstructions, API_CONFIG } from '../../config/apiConfig';
import api from '../../api/BaseApi';

interface NetworkDebuggerProps {
  isVisible?: boolean;
  onClose?: () => void;
}

const NetworkDebugger: React.FC<NetworkDebuggerProps> = ({ isVisible = false, onClose }) => {
  const { mode } = useTheme();
  const [testResults, setTestResults] = useState<{ [key: string]: boolean | null }>({});
  const [testing, setTesting] = useState(false);
  const [showInstructions, setShowInstructions] = useState(false);

  const currentBaseUrl = api.defaults.baseURL;

  const testEndpoints = [
    { name: 'Current API', url: currentBaseUrl },
    { name: 'Android Emulator', url: API_CONFIG.DEVELOPMENT.ANDROID_EMULATOR },
    { name: 'iOS Simulator', url: API_CONFIG.DEVELOPMENT.IOS_SIMULATOR },
  ];

  const runConnectionTest = async (url: string, name: string) => {
    console.log(`🔍 Testing ${name}: ${url}`);
    setTestResults(prev => ({ ...prev, [name]: null }));
    
    try {
      const isConnected = await testApiConnection(url);
      setTestResults(prev => ({ ...prev, [name]: isConnected }));
    } catch (error) {
      console.error(`❌ Test failed for ${name}:`, error);
      setTestResults(prev => ({ ...prev, [name]: false }));
    }
  };

  const runAllTests = async () => {
    setTesting(true);
    setTestResults({});
    
    for (const endpoint of testEndpoints) {
      await runConnectionTest(endpoint.url, endpoint.name);
    }
    
    setTesting(false);
  };

  const getStatusBadge = (status: boolean | null) => {
    if (status === null) {
      return <Badge colorScheme="gray" variant="solid">Testing...</Badge>;
    } else if (status) {
      return <Badge colorScheme="green" variant="solid">✅ Connected</Badge>;
    } else {
      return <Badge colorScheme="red" variant="solid">❌ Failed</Badge>;
    }
  };

  const updateApiUrl = (newUrl: string) => {
    api.updateBaseUrl(newUrl);
    console.log('📡 API URL updated to:', newUrl);
  };

  if (!isVisible) return null;

  return (
    <Box
      position="absolute"
      top={0}
      left={0}
      right={0}
      bottom={0}
      bg="rgba(0,0,0,0.5)"
      zIndex={999}
      justifyContent="center"
      alignItems="center"
    >
      <Box
        bg={mode === 'dark' ? 'gray.800' : 'white'}
        borderRadius="xl"
        p={6}
        m={4}
        maxHeight="80%"
        width="90%"
        shadow={5}
      >
        <HStack justifyContent="space-between" alignItems="center" mb={4}>
          <HStack alignItems="center" space={2}>
            <IconWrapper name="network" size={6} color="blue.500" label="Network" />
            <Text fontSize="lg" fontWeight="bold" color={mode === 'dark' ? 'white' : 'gray.800'}>
              Network Debugger
            </Text>
          </HStack>
          {onClose && (
            <IconButton
              icon={<CloseIcon />}
              onPress={onClose}
              variant="ghost"
              colorScheme="gray"
            />
          )}
        </HStack>

        <ScrollView>
          <VStack space={4}>
            {/* Current Configuration */}
            <Box
              bg={mode === 'dark' ? 'gray.700' : 'gray.50'}
              borderRadius="lg"
              p={4}
            >
              <Text fontSize="md" fontWeight="semibold" color={mode === 'dark' ? 'white' : 'gray.800'} mb={2}>
                Current Configuration
              </Text>
              <VStack space={2}>
                <HStack justifyContent="space-between">
                  <Text fontSize="sm" color={mode === 'dark' ? 'gray.300' : 'gray.600'}>
                    Platform:
                  </Text>
                  <Badge colorScheme="blue" variant="outline">
                    {Platform.OS}
                  </Badge>
                </HStack>
                <HStack justifyContent="space-between">
                  <Text fontSize="sm" color={mode === 'dark' ? 'gray.300' : 'gray.600'}>
                    Development:
                  </Text>
                  <Badge colorScheme={__DEV__ ? 'green' : 'orange'} variant="outline">
                    {__DEV__ ? 'Yes' : 'No'}
                  </Badge>
                </HStack>
                <HStack justifyContent="space-between" alignItems="flex-start">
                  <Text fontSize="sm" color={mode === 'dark' ? 'gray.300' : 'gray.600'}>
                    API URL:
                  </Text>
                  <Text fontSize="sm" color={mode === 'dark' ? 'white' : 'gray.800'} textAlign="right" flex={1}>
                    {currentBaseUrl}
                  </Text>
                </HStack>
              </VStack>
            </Box>

            {/* Connection Tests */}
            <Box>
              <HStack justifyContent="space-between" alignItems="center" mb={3}>
                <Text fontSize="md" fontWeight="semibold" color={mode === 'dark' ? 'white' : 'gray.800'}>
                  Connection Tests
                </Text>
                <Button
                  size="sm"
                  colorScheme="blue"
                  onPress={runAllTests}
                  isLoading={testing}
                  loadingText="Testing..."
                  leftIcon={<IconWrapper name="refresh" size={4} color="white" label="Test" />}
                >
                  Test All
                </Button>
              </HStack>

              <VStack space={2}>
                {testEndpoints.map((endpoint, index) => (
                  <Box
                    key={index}
                    bg={mode === 'dark' ? 'gray.700' : 'gray.50'}
                    borderRadius="lg"
                    p={3}
                  >
                    <HStack justifyContent="space-between" alignItems="center" mb={2}>
                      <Text fontSize="sm" fontWeight="medium" color={mode === 'dark' ? 'white' : 'gray.800'}>
                        {endpoint.name}
                      </Text>
                      {getStatusBadge(testResults[endpoint.name])}
                    </HStack>
                    <Text fontSize="xs" color={mode === 'dark' ? 'gray.400' : 'gray.600'} mb={2}>
                      {endpoint.url}
                    </Text>
                    <HStack space={2}>
                      <Button
                        size="xs"
                        variant="outline"
                        colorScheme="blue"
                        onPress={() => runConnectionTest(endpoint.url, endpoint.name)}
                      >
                        Test
                      </Button>
                      {endpoint.url !== currentBaseUrl && (
                        <Button
                          size="xs"
                          variant="outline"
                          colorScheme="green"
                          onPress={() => updateApiUrl(endpoint.url)}
                        >
                          Use This
                        </Button>
                      )}
                    </HStack>
                  </Box>
                ))}
              </VStack>
            </Box>

            {/* Instructions */}
            <Box>
              <Button
                variant="outline"
                colorScheme="orange"
                onPress={() => setShowInstructions(!showInstructions)}
                leftIcon={<IconWrapper name="help-circle" size={4} color="orange.500" label="Help" />}
              >
                {showInstructions ? 'Hide' : 'Show'} IP Address Instructions
              </Button>

              {showInstructions && (
                <Box mt={3} bg={mode === 'dark' ? 'gray.700' : 'gray.50'} borderRadius="lg" p={4}>
                  <Text fontSize="sm" color={mode === 'dark' ? 'gray.300' : 'gray.600'}>
                    {getIPAddressInstructions()}
                  </Text>
                </Box>
              )}
            </Box>

            {/* Troubleshooting Tips */}
            <Alert status="info" variant="left-accent">
              <VStack space={2}>
                <Text fontSize="sm" fontWeight="medium">
                  Troubleshooting Tips:
                </Text>
                <Text fontSize="xs">
                  • Android Emulator: Use ******** instead of localhost
                </Text>
                <Text fontSize="xs">
                  • iOS Simulator: Use localhost or your computer's IP address
                </Text>
                <Text fontSize="xs">
                  • Make sure your backend server is running on port 5000
                </Text>
                <Text fontSize="xs">
                  • Check firewall settings on your computer
                </Text>
              </VStack>
            </Alert>
          </VStack>
        </ScrollView>
      </Box>
    </Box>
  );
};

export default NetworkDebugger; 