import DatabaseManager from '../../database/DatabaseManager';
import { generateSecureRandomString } from '../../utils/secureRandom';

// Reading interface aligned with new database schema
export interface MeterReading {
  id?: number;
  meter_id: number;
  user_id: number;
  task_id?: number;
  reading_value: number;
  reading_date: string;
  previous_reading?: number;
  reading_method: 'Manual' | 'OCR';
  ocr_confidence?: number;
  latitude?: number;
  longitude?: number;
  gps_accuracy?: number;
  gps_timestamp?: string;
  photo_filename?: string;
  photo_file_path?: string;
  photo_uploaded?: number;
  created_at?: string;
  updated_at?: string;
  is_deleted?: number;
  created_by?: string;
  updated_by?: string;
}

export interface CreateMeterReadingRequest {
  meter_id: number;
  user_id: number;
  task_id?: number;
  reading_value: number;
  previous_reading?: number;
  reading_method?: 'Manual' | 'OCR';
  ocr_confidence?: number;
  latitude?: number;
  longitude?: number;
  gps_accuracy?: number;
  gps_timestamp?: string;
  photo_filename?: string;
  photo_file_path?: string;
  photo_uploaded?: number;
}

export interface UpdateMeterReadingRequest {
  reading_value?: number;
  reading_method?: 'Manual' | 'OCR';
  ocr_confidence?: number;
  latitude?: number;
  longitude?: number;
  gps_accuracy?: number;
  gps_timestamp?: string;
  photo_filename?: string;
  photo_file_path?: string;
  photo_uploaded?: number;
  sync_status?: string;
  sync_error?: string;
  updated_at?: string;
}

export class ReadingRepository {
  private static get db() {
    return DatabaseManager.getInstance();
  }

  /**
   * Create a new meter reading with aligned schema
   */
  static async create(data: CreateMeterReadingRequest): Promise<MeterReading> {
    const currentTime = new Date().toISOString();
    const uuid = `reading_${Date.now()}_${generateSecureRandomString(9)}`;

    const result = await ReadingRepository.db.executeSql(
      `INSERT INTO meter_readings (
        uuid, meter_id, user_id, task_id, reading_value, reading_date,
        reading_method, reading_type, data_source, has_ocr, ocr_confidence,
        latitude, longitude, gps_accuracy, status, is_validated, is_anomalous,
        cant_read, has_photo, photo_count, is_offline_reading, sync_status,
        sync_attempts, created_at, updated_at, created_by, updated_by, is_deleted
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        uuid,
        data.meter_id,
        data.user_id,
        data.task_id || null,
        data.reading_value,
        data.reading_date || currentTime,
        data.reading_method || 'Manual',
        'Regular',
        'Mobile',
        data.ocr_confidence ? 1 : 0,
        data.ocr_confidence || null,
        data.latitude || null,
        data.longitude || null,
        data.gps_accuracy || null,
        'Completed',
        0,
        0,
        0,
        data.photo_filename ? 1 : 0,
        data.photo_filename ? 1 : 0,
        0,
        'pending',
        0,
        currentTime,
        currentTime,
        'mobile_app',
        'mobile_app',
        0
      ]
    );

    const newReading = {
      id: result.insertId,
      uuid,
      meter_id: data.meter_id,
      user_id: data.user_id,
      task_id: data.task_id!,
      reading_value: data.reading_value,
      reading_date: data.reading_date || currentTime,
      reading_method: data.reading_method || 'Manual',
      reading_type: 'Regular',
      data_source: 'Mobile',
      has_ocr: !!data.ocr_confidence,
      ocr_confidence: data.ocr_confidence,
      latitude: data.latitude,
      longitude: data.longitude,
      gps_accuracy: data.gps_accuracy,
      status: 'Completed',
      is_validated: false,
      is_anomalous: false,
      cant_read: false,
      has_photo: !!data.photo_filename,
      photo_count: data.photo_filename ? 1 : 0,
      is_offline_reading: false,
      sync_status: 'pending',
      sync_attempts: 0,
      created_at: currentTime,
      updated_at: currentTime,
      created_by: 'mobile_app',
      updated_by: 'mobile_app',
      is_deleted: false
    };

    // Reading created successfully - upload will be handled by calling service
    if (result.insertId && newReading.id) {
      console.log(`ReadingRepository: Reading ${newReading.id} created successfully`);
    }

    return newReading;
  }

  static async updateSyncStatus(id: number, syncStatus: 'pending' | 'synced' | 'error'): Promise<void> {
    const sql = `
      UPDATE meter_readings
      SET sync_status = ?, updated_at = ?
      WHERE id = ? AND is_deleted = 0
    `;

    const params = [syncStatus, new Date().toISOString(), id];

    await this.db.executeSql(sql, params);
    console.log(`ReadingRepository: Updated reading ${id} sync_status to ${syncStatus}`);
  }

  static async findById(id: number): Promise<MeterReading | null> {
    try {
      const result = await ReadingRepository.db.executeSql(
        'SELECT * FROM meter_readings WHERE id = ? AND is_deleted = 0',
        [id]
      );

      console.log('ReadingRepository.findById: Query result structure:', {
        resultType: typeof result,
        isArray: Array.isArray(result),
        hasRows: result && result.rows !== undefined,
        hasFirstElementRows: result && result[0] && result[0].rows !== undefined
      });

      let rows;
      if (result && result.rows) {
        rows = result.rows;
      } else if (result && result[0] && result[0].rows) {
        rows = result[0].rows;
      } else {
        console.error('ReadingRepository.findById: Unexpected result structure:', result);
        return null;
      }

      if (rows.length === 0) {
        return null;
      }

      const row = rows.item(0);
      return {
        ...row,
        has_ocr: !!row.has_ocr,
        is_validated: !!row.is_validated,
        is_anomalous: !!row.is_anomalous,
        cant_read: !!row.cant_read,
        has_photo: !!row.has_photo,
        is_offline_reading: !!row.is_offline_reading,
        is_deleted: !!row.is_deleted
      };
    } catch (error) {
      console.error('ReadingRepository.findById: Error finding reading by ID:', error);
      return null;
    }
  }

  /**
   * Get readings by meter ID
   */
  static async getByMeterId(meterId: number, limit: number = 10): Promise<MeterReading[]> {
    const result = await ReadingRepository.db.executeSql(
      'SELECT * FROM meter_readings WHERE meter_id = ? AND is_deleted = 0 ORDER BY reading_date DESC LIMIT ?',
      [meterId, limit]
    );

    const readings: MeterReading[] = [];
    for (let i = 0; i < result[0].rows.length; i++) {
      const row = result[0].rows.item(i);
      readings.push({
        ...row,
        has_ocr: !!row.has_ocr,
        is_validated: !!row.is_validated,
        is_anomalous: !!row.is_anomalous,
        cant_read: !!row.cant_read,
        has_photo: !!row.has_photo,
        is_offline_reading: !!row.is_offline_reading,
        is_deleted: !!row.is_deleted
      });
    }
    return readings;
  }

  /**
   * Get readings by user ID
   */
  static async getByUserId(userId: number, limit: number = 50): Promise<MeterReading[]> {
    const result = await ReadingRepository.db.executeSql(
      'SELECT * FROM meter_readings WHERE user_id = ? AND is_deleted = 0 ORDER BY reading_date DESC LIMIT ?',
      [userId, limit]
    );

    const readings: MeterReading[] = [];
    for (let i = 0; i < result[0].rows.length; i++) {
      const row = result[0].rows.item(i);
      readings.push({
        ...row,
        has_ocr: !!row.has_ocr,
        is_validated: !!row.is_validated,
        is_anomalous: !!row.is_anomalous,
        cant_read: !!row.cant_read,
        has_photo: !!row.has_photo,
        is_offline_reading: !!row.is_offline_reading,
        is_deleted: !!row.is_deleted
      });
    }
    return readings;
  }

  /**
   * Get readings by task ID
   */
  static async getByTaskId(taskId: number): Promise<MeterReading[]> {
    const result = await ReadingRepository.db.executeSql(
      'SELECT * FROM meter_readings WHERE task_id = ? AND is_deleted = 0 ORDER BY reading_date DESC',
      [taskId]
    );

    const readings: MeterReading[] = [];
    for (let i = 0; i < result[0].rows.length; i++) {
      const row = result[0].rows.item(i);
      readings.push({
        ...row,
        has_ocr: !!row.has_ocr,
        is_validated: !!row.is_validated,
        is_anomalous: !!row.is_anomalous,
        cant_read: !!row.cant_read,
        has_photo: !!row.has_photo,
        is_offline_reading: !!row.is_offline_reading,
        is_deleted: !!row.is_deleted
      });
    }
    return readings;
  }

  /**
   * Update reading
   */
  static async update(id: number, data: Partial<UpdateMeterReadingRequest>): Promise<MeterReading | null> {
    const updateFields: string[] = [];
    const values: any[] = [];

    if (data.reading_value !== undefined) {
      updateFields.push('reading_value = ?');
      values.push(data.reading_value);
    }
    if (data.reading_method !== undefined) {
      updateFields.push('reading_method = ?');
      values.push(data.reading_method);
    }
    if (data.ocr_confidence !== undefined) {
      updateFields.push('ocr_confidence = ?');
      values.push(data.ocr_confidence);
    }
    if (data.photo_filename !== undefined) {
      updateFields.push('photo_filename = ?');
      values.push(data.photo_filename);
    }
    if (data.photo_file_path !== undefined) {
      updateFields.push('photo_file_path = ?');
      values.push(data.photo_file_path);
    }
    if (data.photo_uploaded !== undefined) {
      updateFields.push('photo_uploaded = ?');
      values.push(data.photo_uploaded);
    }
    if (data.sync_status !== undefined) {
      updateFields.push('sync_status = ?');
      values.push(data.sync_status);
    }
    if (data.sync_error !== undefined) {
      updateFields.push('sync_error = ?');
      values.push(data.sync_error);
    }

    if (updateFields.length === 0) {
      return ReadingRepository.findById(id);
    }

    // Always update the updated_at timestamp
    updateFields.push('updated_at = ?');
    values.push(data.updated_at || new Date().toISOString());
    values.push(id);

    await ReadingRepository.db.executeSql(
      `UPDATE meter_readings SET ${updateFields.join(', ')} WHERE id = ? AND is_deleted = 0`,
      values
    );

    return ReadingRepository.findById(id);
  }

  /**
   * Delete reading (soft delete)
   */
  static async delete(id: number): Promise<boolean> {
    try {
      await ReadingRepository.db.executeSql(
        'UPDATE meter_readings SET is_deleted = 1, updated_at = ? WHERE id = ?',
        [new Date().toISOString(), id]
      );
      return true;
    } catch (error) {
      console.error('ReadingRepository: Error deleting reading:', error);
      return false;
    }
  }

  /**
   * Update photo upload status
   */
  static async updatePhotoUploadStatus(readingId: number, uploaded: boolean): Promise<boolean> {
    try {
      await ReadingRepository.db.executeSql(
        'UPDATE meter_readings SET photo_uploaded = ?, updated_at = ? WHERE id = ?',
        [uploaded ? 1 : 0, new Date().toISOString(), readingId]
      );
      return true;
    } catch (error) {
      console.error('ReadingRepository: Error updating photo upload status:', error);
      return false;
    }
  }

  /**
   * Get readings for upload (not in sync logs or failed sync)
   */
  static async getForUpload(limit: number = 50): Promise<MeterReading[]> {
    try {
      const result = await ReadingRepository.db.executeSql(`
        SELECT r.* FROM meter_readings r
        LEFT JOIN meter_reading_sync_logs s ON r.id = s.local_reading_id
        WHERE r.is_deleted = 0 AND (s.sync_status IS NULL OR s.sync_status != 'synced')
        ORDER BY r.created_at ASC
        LIMIT ?
      `, [limit]);

      console.log('ReadingRepository.getForUpload: Query result structure:', {
        resultType: typeof result,
        isArray: Array.isArray(result),
        hasRows: result && result.rows !== undefined,
        hasFirstElementRows: result && result[0] && result[0].rows !== undefined
      });

      const readings: MeterReading[] = [];
      let rows;

      if (result && result.rows) {
        rows = result.rows;
      } else if (result && result[0] && result[0].rows) {
        rows = result[0].rows;
      } else {
        console.warn('ReadingRepository.getForUpload: Unexpected result structure, returning empty array');
        return readings;
      }

      for (let i = 0; i < rows.length; i++) {
        const row = rows.item(i);
        readings.push({
          ...row,
          has_ocr: !!row.has_ocr,
          is_validated: !!row.is_validated,
          is_anomalous: !!row.is_anomalous,
          cant_read: !!row.cant_read,
          has_photo: !!row.has_photo,
          is_offline_reading: !!row.is_offline_reading,
          is_deleted: !!row.is_deleted
        });
      }

      console.log(`ReadingRepository.getForUpload: Found ${readings.length} readings for upload`);
      return readings;

    } catch (error) {
      console.error('ReadingRepository.getForUpload: Error getting readings for upload:', error);
      return [];
    }
  }

  // Get total reading count
  static async getTotalCount(): Promise<number> {
    try {
      const result = await ReadingRepository.db.executeSql(
        'SELECT COUNT(*) as count FROM meter_readings WHERE is_deleted = 0'
      );
      return result[0].rows.item(0).count;
    } catch (error) {
      console.error('ReadingRepository: Error getting total count:', error);
      return 0;
    }
  }

  // Get today's reading count
  static async getTodayCount(userId?: number): Promise<number> {
    try {
      const today = new Date().toISOString().split('T')[0]; // YYYY-MM-DD
      
      let sql = `
        SELECT COUNT(*) as count FROM meter_readings
        WHERE DATE(reading_date) = ? AND is_deleted = 0
      `;
      const params: any[] = [today];
      
      if (userId) {
        sql += ' AND user_id = ?';
        params.push(userId.toString());
      }
      
      const result = await ReadingRepository.db.executeSql(sql, params);
      return result.rows.item(0).count;
    } catch (error) {
      console.error('ReadingRepository: Error getting today count:', error);
      return 0;
    }
  }

  // Get week's reading count
  static async getWeekCount(userId?: number): Promise<number> {
    try {
      const oneWeekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString();
      
      let sql = `
        SELECT COUNT(*) as count FROM meter_readings
        WHERE reading_date >= ? AND is_deleted = 0
      `;
      const params: any[] = [oneWeekAgo];
      
      if (userId) {
        sql += ' AND user_id = ?';
        params.push(userId.toString());
      }
      
      const result = await ReadingRepository.db.executeSql(sql, params);
      return result[0].rows.item(0).count;
    } catch (error) {
      console.error('ReadingRepository: Error getting week count:', error);
      return 0;
    }
  }

  // Get pending upload count
  static async getPendingUploadCount(): Promise<number> {
    try {
      const result = await ReadingRepository.db.executeSql(`
        SELECT COUNT(*) as count FROM meter_readings r
        LEFT JOIN meter_reading_sync_logs s ON r.id = s.local_reading_id
        WHERE r.is_deleted = 0 AND (s.sync_status IS NULL OR s.sync_status != 'synced')
      `);

      let rows;
      if (result && result.rows) {
        rows = result.rows;
      } else if (result && result[0] && result[0].rows) {
        rows = result[0].rows;
      } else {
        console.warn('ReadingRepository.getPendingUploadCount: Unexpected result structure');
        return 0;
      }

      return rows.item(0).count;
    } catch (error) {
      console.error('ReadingRepository: Error getting pending upload count:', error);
      return 0;
    }
  }

  // Pagination method for debugging
  static async getAllPaginated(page: number = 1, pageSize: number = 10): Promise<{data: any[], total: number}> {
    try {
      const offset = (page - 1) * pageSize;
      
      // Get total count
      const countResult = await DatabaseManager.getInstance().executeSql('SELECT COUNT(*) as total FROM meter_readings WHERE is_deleted = 0');
      const total = countResult.rows.item(0).total;

      const sql = `
        SELECT r.*,
               wm.serial_number as meter_serial_number,
               u.username as user_username,
               wt.name as task_name
        FROM meter_readings r
        LEFT JOIN water_meters wm ON r.meter_id = wm.id
        LEFT JOIN users u ON r.user_id = u.id
        LEFT JOIN work_tasks wt ON r.task_id = wt.id
        WHERE r.is_deleted = 0
        ORDER BY r.created_at DESC
        LIMIT ? OFFSET ?
      `;
      const result = await DatabaseManager.getInstance().executeSql(sql, [pageSize, offset]);
      
      const readings: any[] = [];
      for (let i = 0; i < result.rows.length; i++) {
        const row = result.rows.item(i);
        readings.push({
          id: row.id,
          meter_id: row.meter_id,
          user_id: row.user_id,
          task_id: row.task_id,
          reading_value: row.reading_value,
          reading_date: row.reading_date,
          previous_reading: row.previous_reading,
          reading_method: row.reading_method,
          ocr_confidence: row.ocr_confidence,
          latitude: row.latitude,
          longitude: row.longitude,
          gps_accuracy: row.gps_accuracy,
          gps_timestamp: row.gps_timestamp,
          photo_filename: row.photo_filename,
          photo_file_path: row.photo_file_path,
          photo_uploaded: row.photo_uploaded,
          created_at: row.created_at,
          updated_at: row.updated_at,
          is_deleted: row.is_deleted,
          created_by: row.created_by,
          updated_by: row.updated_by,
          // Extra debug info
          meter_serial_number: row.meter_serial_number,
          user_username: row.user_username,
          task_name: row.task_name
        });
      }
      
      return { data: readings, total };
    } catch (error) {
      console.error('Error getting paginated readings:', error);
      return { data: [], total: 0 };
    }
  }
} 