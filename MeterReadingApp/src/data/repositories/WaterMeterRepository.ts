import DatabaseManager from '../../database/DatabaseManager';
import { WaterMeter, CreateWaterMeterRequest, UpdateWaterMeterRequest, SyncWaterMeterData } from '../../database/models';

export class WaterMeterRepository {
  private static get dbManager() {
    return DatabaseManager.getInstance();
  }

  // Helper method to convert database row to WaterMeter object
  private static mapRowToWaterMeter(row: any): WaterMeter {
    return {
      id: row.id,
      created_at: row.created_at,
      updated_at: row.updated_at,
      is_deleted: row.is_deleted,
      created_by: row.created_by,
      updated_by: row.updated_by,
      asset_id: row.asset_id,
      serial_number: row.serial_number,
      account_number: row.account_number,
      location: row.location,
      address: row.address,
      latitude: row.latitude,
      longitude: row.longitude,
      meter_type: row.meter_type,
      status: row.status,
      customer_name: row.customer_name,
      last_reading: row.last_reading,
      last_reading_date: row.last_reading_date,
      sync_status: row.sync_status,
      last_sync_date: row.last_sync_date,
    };
  }

  // Helper method to convert boolean to SQLite integer
  private static boolToInt(value?: boolean): number {
    return value ? 1 : 0;
  }

  // Upsert water meter using backend ID (INSERT OR REPLACE for sync operations)
  static async upsert(meterData: SyncWaterMeterData): Promise<WaterMeter> {
    const sql = `
      INSERT OR REPLACE INTO water_meters (
        id, created_at, updated_at, is_deleted, created_by, updated_by,
        asset_id, serial_number, account_number, location, address,
        latitude, longitude, meter_type, status, customer_name,
        last_reading, last_reading_date, sync_status, last_sync_date
      )
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    const params = [
      meterData.id,                                   // Use backend ID directly
      meterData.created_at || new Date().toISOString(),
      meterData.updated_at || new Date().toISOString(),
      this.boolToInt(meterData.is_deleted),
      meterData.created_by || 'mobile_app',
      meterData.updated_by || 'mobile_app',
      meterData.asset_id,
      meterData.serial_number,
      meterData.account_number,
      meterData.location,
      meterData.address,
      meterData.latitude,
      meterData.longitude,
      meterData.meter_type,
      meterData.status,
      meterData.customer_name,
      meterData.last_reading,
      meterData.last_reading_date,
      meterData.sync_status || 'Synced',
      meterData.last_sync_date || new Date().toISOString()
    ];

    try {
      await DatabaseManager.getInstance().executeSql(sql, params);
      console.log(`Water meter upserted successfully with backend ID: ${meterData.id}`);
      
      // Return the upserted meter using existing findById method
      const meter = await this.findById(meterData.id);
      return meter!; // We know it exists since we just inserted it
    } catch (error) {
      console.error('Error upserting water meter:', error);
      throw new Error(`Failed to upsert water meter with ID ${meterData.id}: ${error}`);
    }
  }

  // Bulk upsert water meters for sync operations
  static async bulkUpsert(meters: SyncWaterMeterData[]): Promise<void> {
    try {
      console.log(`Starting bulk upsert for ${meters.length} water meters`);
      
      for (const meterData of meters) {
        await this.upsert(meterData);
      }
      
      console.log(`Bulk upsert completed for ${meters.length} water meters`);
    } catch (error) {
      console.error('Error in bulk upsert:', error);
      throw error;
    }
  }

  // Find water meter by ID
  static async findById(id: number): Promise<WaterMeter | null> {
    try {
      const sql = 'SELECT * FROM water_meters WHERE id = ? AND is_deleted = 0';
      const result = await DatabaseManager.getInstance().executeSql(sql, [id]);
      
      if (result.rows.length > 0) {
        return this.mapRowToWaterMeter(result.rows.item(0));
      }
      return null;
    } catch (error) {
      console.error('Error finding water meter by ID:', error);
      throw error;
    }
  }

  // Find water meter by serial number
  static async findBySerialNumber(serialNumber: string): Promise<WaterMeter | null> {
    try {
      const sql = 'SELECT * FROM water_meters WHERE serial_number = ? AND is_deleted = 0';
      const result = await DatabaseManager.getInstance().executeSql(sql, [serialNumber]);
      
      if (result.rows.length > 0) {
        return this.mapRowToWaterMeter(result.rows.item(0));
      }
      return null;
    } catch (error) {
      console.error('Error finding water meter by serial number:', error);
      throw error;
    }
  }

  // Find water meter by asset ID
  static async findByAssetId(assetId: string): Promise<WaterMeter | null> {
    const sql = 'SELECT * FROM water_meters WHERE asset_id = ? AND is_deleted = 0';
    const result = await this.dbManager.executeSql(sql, [assetId]);

    if (result.rows.length === 0) {
      return null;
    }

    const row = result.rows.item(0);
    return this.mapRowToWaterMeter(row);
  }

  // Get water meters by status
  static async getByStatus(status: string): Promise<WaterMeter[]> {
    const sql = `
      SELECT * FROM water_meters 
      WHERE status = ? AND is_deleted = 0 
      ORDER BY created_at DESC
    `;
    
    const result = await this.dbManager.executeSql(sql, [status]);
    
    const meters: WaterMeter[] = [];
    for (let i = 0; i < result.rows.length; i++) {
      const row = result.rows.item(i);
      meters.push(this.mapRowToWaterMeter(row));
    }
    return meters;
  }

  // Get water meters by location (contains search)
  static async getByLocation(locationSearch: string): Promise<WaterMeter[]> {
    const sql = `
      SELECT * FROM water_meters 
      WHERE (location LIKE ? OR address LIKE ?) AND is_deleted = 0 
      ORDER BY location
    `;
    
    const searchTerm = `%${locationSearch}%`;
    const result = await this.dbManager.executeSql(sql, [searchTerm, searchTerm]);
    
    const meters: WaterMeter[] = [];
    for (let i = 0; i < result.rows.length; i++) {
      const row = result.rows.item(i);
      meters.push(this.mapRowToWaterMeter(row));
    }
    return meters;
  }

  // Get water meters with GPS coordinates
  static async getWithCoordinates(): Promise<WaterMeter[]> {
    const sql = `
      SELECT * FROM water_meters 
      WHERE latitude IS NOT NULL AND longitude IS NOT NULL AND is_deleted = 0 
      ORDER BY created_at DESC
    `;
    
    const result = await this.dbManager.executeSql(sql);
    
    const meters: WaterMeter[] = [];
    for (let i = 0; i < result.rows.length; i++) {
      const row = result.rows.item(i);
      meters.push(this.mapRowToWaterMeter(row));
    }
    return meters;
  }

  // Update water meter
  static async update(meterRequest: UpdateWaterMeterRequest): Promise<WaterMeter> {
    const currentTime = new Date().toISOString();
    
    // Build dynamic SQL based on provided fields
    const updateFields: string[] = [];
    const updateValues: any[] = [];
    
    updateFields.push('updated_at = ?');
    updateValues.push(currentTime);
    updateFields.push('updated_by = ?');
    updateValues.push('mobile_app');

    if (meterRequest.asset_id !== undefined) {
      updateFields.push('asset_id = ?');
      updateValues.push(meterRequest.asset_id);
    }
    if (meterRequest.serial_number !== undefined) {
      updateFields.push('serial_number = ?');
      updateValues.push(meterRequest.serial_number);
    }
    if (meterRequest.account_number !== undefined) {
      updateFields.push('account_number = ?');
      updateValues.push(meterRequest.account_number);
    }
    if (meterRequest.location !== undefined) {
      updateFields.push('location = ?');
      updateValues.push(meterRequest.location);
    }
    if (meterRequest.address !== undefined) {
      updateFields.push('address = ?');
      updateValues.push(meterRequest.address);
    }
    if (meterRequest.latitude !== undefined) {
      updateFields.push('latitude = ?');
      updateValues.push(meterRequest.latitude);
    }
    if (meterRequest.longitude !== undefined) {
      updateFields.push('longitude = ?');
      updateValues.push(meterRequest.longitude);
    }
    if (meterRequest.meter_type !== undefined) {
      updateFields.push('meter_type = ?');
      updateValues.push(meterRequest.meter_type);
    }
    if (meterRequest.status !== undefined) {
      updateFields.push('status = ?');
      updateValues.push(meterRequest.status);
    }
    if (meterRequest.customer_name !== undefined) {
      updateFields.push('customer_name = ?');
      updateValues.push(meterRequest.customer_name);
    }
    if (meterRequest.last_reading !== undefined) {
      updateFields.push('last_reading = ?');
      updateValues.push(meterRequest.last_reading);
    }
    if (meterRequest.last_reading_date !== undefined) {
      updateFields.push('last_reading_date = ?');
      updateValues.push(meterRequest.last_reading_date);
    }
    if (meterRequest.sync_status !== undefined) {
      updateFields.push('sync_status = ?');
      updateValues.push(meterRequest.sync_status);
    }
    if (meterRequest.last_sync_date !== undefined) {
      updateFields.push('last_sync_date = ?');
      updateValues.push(meterRequest.last_sync_date);
    }

    const sql = `
      UPDATE water_meters 
      SET ${updateFields.join(', ')} 
      WHERE id = ? AND is_deleted = 0
    `;
    
    updateValues.push(meterRequest.id);
    
    const result = await this.dbManager.executeSql(sql, updateValues);
    
    if (result.rowsAffected === 0) {
      throw new Error(`Failed to update water meter with ID ${meterRequest.id}`);
    }

    return await this.findById(meterRequest.id);
  }

  // Soft delete water meter
  static async delete(id: number): Promise<boolean> {
    const currentTime = new Date().toISOString();
    
    const sql = `
      UPDATE water_meters 
      SET is_deleted = 1, updated_at = ?, updated_by = ?
      WHERE id = ? AND is_deleted = 0
    `;
    
    const result = await this.dbManager.executeSql(sql, [currentTime, 'mobile_app', id]);
    
    return result.rowsAffected > 0;
  }

  // Get all active water meters
  static async getAll(): Promise<WaterMeter[]> {
    const sql = `
      SELECT * FROM water_meters 
      WHERE is_deleted = 0 
      ORDER BY serial_number
    `;
    
    const result = await this.dbManager.executeSql(sql);
    
    const meters: WaterMeter[] = [];
    for (let i = 0; i < result.rows.length; i++) {
      const row = result.rows.item(i);
      meters.push(this.mapRowToWaterMeter(row));
    }
    return meters;
  }

  // Get water meter count by status
  static async getCountByStatus(status: string): Promise<number> {
    const sql = `
      SELECT COUNT(*) as count FROM water_meters 
      WHERE status = ? AND is_deleted = 0
    `;
    
    const result = await this.dbManager.executeSql(sql, [status]);
    const row = result.rows.item(0);
    return row.count;
  }

  // Get total water meter count
  static async getTotalCount(): Promise<number> {
    try {
      const sql = 'SELECT COUNT(*) as count FROM water_meters WHERE is_deleted = 0';
      const result = await DatabaseManager.getInstance().executeSql(sql);
      
      if (result.rows.length > 0) {
        return result.rows.item(0).count;
      }
      return 0;
    } catch (error) {
      console.error('Error getting total water meter count:', error);
      return 0;
    }
  }

  // Legacy bulkInsert method removed - use bulkUpsert instead for sync operations

  // Update sync status
  static async updateSyncStatus(id: number, syncStatus: string, lastSyncDate?: string): Promise<void> {
    const currentTime = new Date().toISOString();
    
    const sql = `
      UPDATE water_meters 
      SET sync_status = ?, last_sync_date = ?, updated_at = ?, updated_by = ?
      WHERE id = ? AND is_deleted = 0
    `;
    
    const params = [
      syncStatus,
      lastSyncDate || currentTime,
      currentTime,
      'mobile_app',
      id
    ];
    
    await this.dbManager.executeSql(sql, params);
  }

  // Clear all water meters (for testing/development)
  static async clearAll(): Promise<void> {
    const sql = 'DELETE FROM water_meters';
    await this.dbManager.executeSql(sql);
  }

  // Pagination method for debugging
  static async getAllPaginated(page: number = 1, pageSize: number = 10): Promise<{data: WaterMeter[], total: number}> {
    try {
      const offset = (page - 1) * pageSize;
      
      // Get total count
      const countResult = await DatabaseManager.getInstance().executeSql('SELECT COUNT(*) as total FROM water_meters WHERE is_deleted = 0');
      const total = countResult.rows.item(0).total;

      // Get paginated data
      const sql = `SELECT * FROM water_meters WHERE is_deleted = 0 ORDER BY created_at DESC LIMIT ? OFFSET ?`;
      const result = await DatabaseManager.getInstance().executeSql(sql, [pageSize, offset]);
      
      const meters: WaterMeter[] = [];
      for (let i = 0; i < result.rows.length; i++) {
        meters.push(this.mapRowToWaterMeter(result.rows.item(i)));
      }
      
      return { data: meters, total };
    } catch (error) {
      console.error('Error getting paginated water meters:', error);
      return { data: [], total: 0 };
    }
  }
} 