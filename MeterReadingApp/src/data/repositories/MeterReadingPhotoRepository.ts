import DatabaseManager from '../../database/DatabaseManager';
import { ReadingPhoto } from '../../database/models/MeterReading';
import { generateSecureRandomString } from '../../utils/secureRandom';

export interface CreatePhotoRequest {
  readingId: number;
  localFilePath: string;
  fileName: string;
  fileSize: number;
  mimeType: string;
  capturedAt: string;
  latitude?: number;
  longitude?: number;
  photoType?: 'meter' | 'location' | 'issue' | 'other';
}

export class MeterReadingPhotoRepository {
  private static db = DatabaseManager.getInstance();

  static async savePhoto(photoData: CreatePhotoRequest): Promise<ReadingPhoto> {
    const uuid = generateSecureRandomString(16);
    const now = new Date().toISOString();
    
    const sql = `
      INSERT INTO reading_photos (
        uuid, reading_id, filename, file_path, file_size, mime_type,
        captured_at, latitude, longitude, photo_type, sync_status,
        created_at, updated_at, is_deleted, upload_retry_count
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;
    
    const params = [
      uuid,
      photoData.readingId,
      photoData.fileName,
      photoData.localFilePath,
      photoData.fileSize,
      photoData.mimeType,
      photoData.capturedAt,
      photoData.latitude || null,
      photoData.longitude || null,
      photoData.photoType || 'meter',
      'pending',
      now,
      now,
      0,
      0
    ];

    const result = await this.db.executeSql(sql, params);
    return await this.findById(result.insertId!);
  }

  static async findById(id: number): Promise<ReadingPhoto> {
    const sql = 'SELECT * FROM reading_photos WHERE id = ? AND is_deleted = 0';
    const result = await this.db.executeSql(sql, [id]);
    
    if (result.rows.length === 0) {
      throw new Error(`Photo with id ${id} not found`);
    }
    
    return this.mapRowToPhoto(result.rows.item(0));
  }

  static async findByReadingId(readingId: number): Promise<ReadingPhoto[]> {
    const sql = `
      SELECT * FROM reading_photos 
      WHERE reading_id = ? AND is_deleted = 0 
      ORDER BY captured_at DESC
    `;
    
    const result = await this.db.executeSql(sql, [readingId]);
    const photos: ReadingPhoto[] = [];
    
    for (let i = 0; i < result.rows.length; i++) {
      photos.push(this.mapRowToPhoto(result.rows.item(i)));
    }
    
    return photos;
  }

  static async updateUploadStatus(
    photoId: number, 
    remoteUrl: string, 
    thumbnailUrl: string | undefined,
    isUploaded: boolean
  ): Promise<void> {
    const sql = `
      UPDATE reading_photos 
      SET remote_url = ?, thumbnail_url = ?, sync_status = ?, updated_at = ?
      WHERE id = ?
    `;
    
    await this.db.executeSql(sql, [
      remoteUrl,
      thumbnailUrl || null,
      isUploaded ? 'synced' : 'error',
      new Date().toISOString(),
      photoId
    ]);
  }

  static async getPendingUploadPhotos(): Promise<ReadingPhoto[]> {
    const sql = `
      SELECT * FROM reading_photos 
      WHERE sync_status IN ('pending', 'error') AND is_deleted = 0
      ORDER BY created_at ASC
    `;
    
    const result = await this.db.executeSql(sql);
    const photos: ReadingPhoto[] = [];
    
    for (let i = 0; i < result.rows.length; i++) {
      photos.push(this.mapRowToPhoto(result.rows.item(i)));
    }
    
    return photos;
  }

  static async getUploadStats(): Promise<{
    pending: number;
    uploaded: number;
    failed: number;
  }> {
    const sql = `
      SELECT 
        COUNT(CASE WHEN sync_status = 'pending' THEN 1 END) as pending,
        COUNT(CASE WHEN sync_status = 'synced' THEN 1 END) as uploaded,
        COUNT(CASE WHEN sync_status = 'error' THEN 1 END) as failed
      FROM reading_photos 
      WHERE is_deleted = 0
    `;
    
    const result = await this.db.executeSql(sql);
    const row = result.rows.item(0);
    
    return {
      pending: row.pending || 0,
      uploaded: row.uploaded || 0,
      failed: row.failed || 0
    };
  }

  static async deletePhoto(photoId: number): Promise<boolean> {
    try {
      const sql = `
        UPDATE reading_photos 
        SET is_deleted = 1, updated_at = ? 
        WHERE id = ?
      `;
      
      await this.db.executeSql(sql, [new Date().toISOString(), photoId]);
      return true;
    } catch (error) {
      console.error('Error deleting photo:', error);
      return false;
    }
  }

  static async executeSql(sql: string, params: any[] = []): Promise<any> {
    return await this.db.executeSql(sql, params);
  }

  static async updateReadingId(photoId: number, readingId: number): Promise<void> {
    const sql = `
      UPDATE reading_photos
      SET
        reading_id = ?,
        updated_at = ?
      WHERE id = ?
    `;

    const params = [
      readingId,
      new Date().toISOString(),
      photoId
    ];

    await DatabaseManager.executeSql(sql, params);
  }

  private static mapRowToPhoto(row: any): ReadingPhoto {
    return {
      id: row.id,
      uuid: row.uuid,
      reading_id: row.reading_id,
      filename: row.filename,
      file_path: row.file_path,
      file_size: row.file_size,
      mime_type: row.mime_type,
      captured_at: row.captured_at,
      latitude: row.latitude,
      longitude: row.longitude,
      photo_type: row.photo_type || 'meter',
      sync_status: row.sync_status || 'pending',
      sync_error: row.sync_error,
      created_at: row.created_at,
      updated_at: row.updated_at,
      is_deleted: !!row.is_deleted,
      remote_url: row.remote_url,
      thumbnail_url: row.thumbnail_url,
      upload_retry_count: row.upload_retry_count || 0
    };
  }
}
