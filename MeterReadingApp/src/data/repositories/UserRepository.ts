import DatabaseManager from '../../database/DatabaseManager';
import { User, CreateUserRequest, UpdateUserRequest, SyncUserData } from '../../database/models';

export class UserRepository {

  // Helper method to convert database row to User object
  private static mapRowToUser(row: any): User {
    return {
      id: row.id,
      created_at: row.created_at,
      updated_at: row.updated_at,
      is_deleted: row.is_deleted,
      created_by: row.created_by,
      updated_by: row.updated_by,
      username: row.username,
      full_name: row.full_name,
      email: row.email,
      person_id: row.person_id,
      fin_co_code: row.fin_co_code,
      mobile_phone: row.mobile_phone,
      profit_centre_code: row.profit_centre_code,
      employee_no: row.employee_no,
      is_authenticated: row.is_authenticated,
      password_hash: row.password_hash,
      role: row.role,
      is_active: row.is_active,
      last_login: row.last_login,
      last_login_date: row.last_login_date,
      created_date: row.created_date,
      updated_date: row.updated_date,
      sync_status: row.sync_status
    };
  }

  // Helper method to convert boolean to SQLite integer
  private static boolToInt(value?: boolean): number {
    return value ? 1 : 0;
  }

  // Upsert user using backend ID (INSERT OR REPLACE for sync operations)
  static async upsert(userData: SyncUserData): Promise<User> {
    const sql = `
      INSERT OR REPLACE INTO users (
        id, created_at, updated_at, is_deleted, created_by, updated_by,
        username, full_name, email, person_id, fin_co_code,
        mobile_phone, profit_centre_code, employee_no, is_authenticated,
        last_login, created_date, updated_date
      )
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    const params = [
      userData.id,
      userData.created_at || new Date().toISOString(),
      userData.updated_at || new Date().toISOString(),
      this.boolToInt(userData.is_deleted),
      userData.created_by || 'mobile_app',
      userData.updated_by || 'mobile_app',
      userData.username,
      userData.full_name || '',
      userData.email || '',
      userData.person_id || 0,
      userData.fin_co_code || '',
      userData.mobile_phone || '',
      userData.profit_centre_code || '',
      userData.employee_no || '',
      this.boolToInt(userData.is_authenticated ? true : false),
      userData.last_login,
      userData.created_date || new Date().toISOString(),
      userData.updated_date || new Date().toISOString()
    ];

    try {
      await DatabaseManager.getInstance().executeSql(sql, params);
      console.log(`User upserted successfully with backend ID: ${userData.id}`);

      // Return the upserted user
      const user = await this.findById(userData.id);
      if (!user) {
        throw new Error(`Failed to retrieve upserted user with ID ${userData.id}`);
      }
      return user;
    } catch (error) {
      console.error('Error upserting user:', error);
      throw new Error(`Failed to upsert user with ID ${userData.id}: ${error}`);
    }
  }

  // Find user by ID
  static async findById(id: number): Promise<User | null> {
    try {
      const sql = 'SELECT * FROM users WHERE id = ? AND is_deleted = 0';
      const result = await DatabaseManager.getInstance().executeSql(sql, [id]);
      
      if (result.rows.length > 0) {
        return this.mapRowToUser(result.rows.item(0));
      }
      return null;
    } catch (error) {
      console.error('Error finding user by ID:', error);
      throw error;
    }
  }

  // Find user by username
  static async findByUsername(username: string): Promise<User | null> {
    try {
      const sql = 'SELECT * FROM users WHERE username = ? AND is_deleted = 0';
      const result = await DatabaseManager.getInstance().executeSql(sql, [username]);
      
      if (result.rows.length > 0) {
        return this.mapRowToUser(result.rows.item(0));
      }
      return null;
    } catch (error) {
      console.error('Error finding user by username:', error);
      throw error;
    }
  }

  // === COMPATIBILITY METHODS FOR AuthService ===

  // Get user by ID (compatibility with AuthService)
  static async getById(userId: number): Promise<User | null> {
    return this.findById(userId);
  }

  // Get user by username (compatibility with AuthService)
  static async getByUsername(username: string): Promise<User | null> {
    return this.findByUsername(username);
  }

  // Get all users
  static async getAll(): Promise<User[]> {
    try {
      const sql = 'SELECT * FROM users WHERE is_deleted = 0 ORDER BY created_at DESC';
      const result = await DatabaseManager.getInstance().executeSql(sql);
      
      const users: User[] = [];
      for (let i = 0; i < result.rows.length; i++) {
        users.push(this.mapRowToUser(result.rows.item(i)));
      }
      return users;
    } catch (error) {
      console.error('Error getting all users:', error);
      return [];
    }
  }

  // Update user
  static async update(userId: number, updateData: Partial<User>): Promise<User | null> {
    try {
      const updateFields: string[] = [];
      const values: any[] = [];

      // Build dynamic update query
      Object.keys(updateData).forEach(key => {
        if (key !== 'id' && updateData[key as keyof User] !== undefined) {
          updateFields.push(`${key} = ?`);
          values.push(updateData[key as keyof User]);
        }
      });

      if (updateFields.length === 0) {
        return this.getById(userId);
      }

      // Always update the updated_at field
      updateFields.push('updated_at = ?');
      values.push(new Date().toISOString());

      const sql = `
        UPDATE users
        SET ${updateFields.join(', ')}
        WHERE id = ? AND is_deleted = 0
      `;
      values.push(userId);

      await DatabaseManager.getInstance().executeSql(sql, values);
      return this.getById(userId);
    } catch (error) {
      console.error('Error updating user:', error);
      throw error;
    }
  }

  // Set authentication status
  static async setAuthenticated(userId: number, isAuthenticated: boolean): Promise<User | null> {
    try {
      const now = new Date().toISOString();
      const sql = `
        UPDATE users
        SET is_authenticated = ?, last_login = ?, updated_at = ?
        WHERE id = ? AND is_deleted = 0
      `;
      
      await DatabaseManager.getInstance().executeSql(sql, [
        this.boolToInt(isAuthenticated),
        isAuthenticated ? now : null,
        now,
        userId
      ]);
      
      return this.getById(userId);
    } catch (error) {
      console.error('Error setting authentication status:', error);
      throw error;
    }
  }

  // Clear all authentication
  static async clearAllAuthentication(): Promise<void> {
    try {
      const now = new Date().toISOString();
      const sql = `
        UPDATE users
        SET is_authenticated = 0, updated_at = ?
        WHERE is_deleted = 0
      `;
      
      await DatabaseManager.getInstance().executeSql(sql, [now]);
    } catch (error) {
      console.error('Error clearing all authentication:', error);
      throw error;
    }
  }

  // Create user (for local operations)
  static async create(userRequest: CreateUserRequest): Promise<User> {
    try {
      const currentTime = new Date().toISOString();
      
      const sql = `
        INSERT INTO users (
          ${userRequest.id ? 'id,' : ''} created_at, updated_at, is_deleted, created_by, updated_by,
          username, full_name, email, person_id, fin_co_code,
          mobile_phone, profit_centre_code, employee_no, is_authenticated,
          created_date, updated_date
        )
        VALUES (${userRequest.id ? '?,' : ''} ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `;

      const params = [
        ...(userRequest.id ? [userRequest.id] : []),
        currentTime,
        currentTime,
        0, // is_deleted
        'mobile_app',
        'mobile_app',
        userRequest.username,
        userRequest.full_name || '',
        userRequest.email || '',
        userRequest.person_id || 0,
        userRequest.fin_co_code || '',
        userRequest.mobile_phone || '',
        userRequest.profit_centre_code || '',
        userRequest.employee_no || '',
        this.boolToInt(userRequest.is_authenticated || false),
        currentTime,
        currentTime
      ];

      const result = await DatabaseManager.getInstance().executeSql(sql, params);
      const userId = userRequest.id || result.insertId;
      
      const user = await this.getById(userId);
      if (!user) {
        throw new Error('Failed to retrieve created user');
      }
      return user;
    } catch (error) {
      console.error('Error creating user:', error);
      throw error;
    }
  }

  // Bulk upsert users for sync operations
  static async bulkUpsert(users: SyncUserData[]): Promise<void> {
    try {
      console.log(`Starting bulk upsert for ${users.length} users`);
      
      for (const userData of users) {
        await this.upsert(userData);
      }
      
      console.log(`Bulk upsert completed for ${users.length} users`);
    } catch (error) {
      console.error('Error in bulk upsert:', error);
      throw error;
    }
  }

  // Get total count of users
  static async getTotalCount(): Promise<number> {
    try {
      const sql = 'SELECT COUNT(*) as count FROM users WHERE is_deleted = 0';
      const result = await DatabaseManager.getInstance().executeSql(sql);
      
      if (result.rows.length > 0) {
        return result.rows.item(0).count;
      }
      return 0;
    } catch (error) {
      console.error('Error getting total user count:', error);
      return 0;
    }
  }

  // Get count of active/authenticated users
  static async getActiveUserCount(): Promise<number> {
    try {
      const sql = 'SELECT COUNT(*) as count FROM users WHERE is_authenticated = 1 AND is_deleted = 0';
      const result = await DatabaseManager.getInstance().executeSql(sql);
      
      if (result.rows.length > 0) {
        return result.rows.item(0).count;
      }
      return 0;
    } catch (error) {
      console.error('Error getting active user count:', error);
      return 0;
    }
  }

  // Pagination method for debugging
  static async getAllPaginated(page: number = 1, pageSize: number = 10): Promise<{data: User[], total: number}> {
    try {
      const offset = (page - 1) * pageSize;
      
      // Get total count
      const countResult = await DatabaseManager.getInstance().executeSql('SELECT COUNT(*) as total FROM users WHERE is_deleted = 0');
      const total = countResult.rows.item(0).total;

      // Get paginated data
      const sql = `SELECT * FROM users WHERE is_deleted = 0 ORDER BY created_at DESC LIMIT ? OFFSET ?`;
      const result = await DatabaseManager.getInstance().executeSql(sql, [pageSize, offset]);
      
      const users: User[] = [];
      for (let i = 0; i < result.rows.length; i++) {
        users.push(this.mapRowToUser(result.rows.item(i)));
      }
      
      return { data: users, total };
    } catch (error) {
      console.error('Error getting paginated users:', error);
      return { data: [], total: 0 };
    }
  }
} 