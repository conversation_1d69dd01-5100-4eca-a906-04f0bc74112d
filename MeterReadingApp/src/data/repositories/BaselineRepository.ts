import DatabaseManager from '../../database/DatabaseManager';
import { SyncBaselineData, BaselineRecord } from '../../database/models/BaselineRecord';

export class BaselineRepository {
  /**
   * Bulk upsert baseline data into local database
   * 批量插入或更新基线数据到本地数据库
   */
  static async bulkUpsert(baselineDataList: SyncBaselineData[]): Promise<void> {
    try {
      console.log(`🔄 BaselineRepository: Starting bulk upsert of ${baselineDataList.length} records`);
      
      if (baselineDataList.length === 0) {
        console.log('⚠️ BaselineRepository: No data to upsert, returning');
        return;
      }

      const db = await DatabaseManager.getInstance().getDatabase();
      
      // Process records one by one to avoid transaction complexity
      let successCount = 0;
      let errorCount = 0;
      
      for (let i = 0; i < baselineDataList.length; i++) {
        const baselineData = baselineDataList[i];
        
        try {
          const sql = `
            INSERT OR REPLACE INTO baseline_records (
              id, meter_id, baseline_date, baseline_value, baseline_type, status,
              import_batch, source_file, data_source, validation_notes,
              is_validated, validated_date, validated_by, has_validation_errors, validation_errors,
              is_anomalous, anomaly_description, previous_baseline_id, previous_baseline_value,
              variance_from_previous, variance_percentage, is_corrected, corrected_date,
              corrected_by, correction_reason, confidence_level, notes,
              created_at, updated_at, created_by, updated_by,
              is_deleted, sync_status, last_sync_date, created_date, updated_date
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
          `;

          const params = [
            baselineData.id, baselineData.meter_id, baselineData.baseline_date, baselineData.baseline_value,
            baselineData.baseline_type, baselineData.status, baselineData.import_batch, baselineData.source_file,
            baselineData.data_source, baselineData.validation_notes, baselineData.is_validated,
            baselineData.validated_date, baselineData.validated_by, baselineData.has_validation_errors,
            baselineData.validation_errors, baselineData.is_anomalous, baselineData.anomaly_description,
            baselineData.previous_baseline_id, baselineData.previous_baseline_value,
            baselineData.variance_from_previous, baselineData.variance_percentage, baselineData.is_corrected,
            baselineData.corrected_date, baselineData.corrected_by, baselineData.correction_reason,
            baselineData.confidence_level, baselineData.notes, baselineData.created_at, baselineData.updated_at,
            baselineData.created_by, baselineData.updated_by, baselineData.is_deleted, baselineData.sync_status,
            baselineData.last_sync_date, baselineData.created_at, baselineData.updated_at
          ];
          
          await DatabaseManager.getInstance().executeSql(sql, params);
          successCount++;
          
        } catch (error: any) {
          errorCount++;
          console.error(`❌ BaselineRepository: Error inserting record ${i + 1}:`, error);
        }
      }
      
      console.log(`✅ BaselineRepository: Completed - ${successCount} successful, ${errorCount} errors`);
      
    } catch (error: any) {
      console.error('BaselineRepository: Error during bulk upsert:', error);
      throw error;
    }
  }

  /**
   * Get all baseline records with pagination
   * 分页获取所有基线记录
   */
  static async getAll(page: number = 1, pageSize: number = 20): Promise<BaselineRecord[]> {
    const offset = (page - 1) * pageSize;
    const sql = `
      SELECT * FROM baseline_records 
      WHERE is_deleted = 0
      ORDER BY baseline_date DESC 
      LIMIT ? OFFSET ?
    `;
    const result = await DatabaseManager.getInstance().executeSql(sql, [pageSize, offset]);
    const baselines: BaselineRecord[] = [];
    
    for (let i = 0; i < result.rows.length; i++) {
      baselines.push(this.mapRowToBaseline(result.rows.item(i)));
    }
    
    return baselines;
  }

  /**
   * Get total count of baseline records
   * 获取基线记录总数
   */
  static async getCount(): Promise<number> {
    const sql = 'SELECT COUNT(*) as count FROM baseline_records WHERE is_deleted = 0';
    const result = await DatabaseManager.getInstance().executeSql(sql, []);
    return result.rows.item(0).count;
  }

  static async getValidatedCount(): Promise<number> {
    const sql = `
      SELECT COUNT(*) as count FROM baseline_records
      WHERE is_validated = 1 AND is_deleted = 0
    `;
    const result = await DatabaseManager.getInstance().executeSql(sql, []);
    return result.rows.item(0).count;
  }

  static async getAnomalousCount(): Promise<number> {
    const sql = `
      SELECT COUNT(*) as count FROM baseline_records
      WHERE is_anomalous = 1 AND is_deleted = 0
    `;
    const result = await DatabaseManager.getInstance().executeSql(sql, []);
    return result.rows.item(0).count;
  }

  static async getCorrectedCount(): Promise<number> {
    const sql = `
      SELECT COUNT(*) as count FROM baseline_records
      WHERE is_corrected = 1 AND is_deleted = 0
    `;
    const result = await DatabaseManager.getInstance().executeSql(sql, []);
    return result.rows.item(0).count;
  }

  /**
   * Get count of recent baseline records (last 30 days)
   * 获取最近基线记录数（最近30天）
   */
  static async getRecentCount(): Promise<number> {
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    
    const sql = `
      SELECT COUNT(*) as count FROM baseline_records 
      WHERE created_at >= ? AND is_deleted = 0
    `;
    const result = await DatabaseManager.getInstance().executeSql(sql, [thirtyDaysAgo.toISOString()]);
    return result.rows.item(0).count;
  }

  static async findById(id: number): Promise<BaselineRecord | null> {
    const sql = 'SELECT * FROM baseline_records WHERE id = ? AND is_deleted = 0';
    const result = await DatabaseManager.getInstance().executeSql(sql, [id]);
    return result.rows.length > 0 ? this.mapRowToBaseline(result.rows.item(0)) : null;
  }

  /**
   * Get baselines by meter ID
   * 根据水表ID获取基线
   */
  static async getByMeterId(meterId: number): Promise<BaselineRecord[]> {
    const sql = `
      SELECT * FROM baseline_records 
      WHERE meter_id = ? AND is_deleted = 0
      ORDER BY baseline_date DESC
    `;
    const result = await DatabaseManager.getInstance().executeSql(sql, [meterId]);
    const baselines: BaselineRecord[] = [];
    
    for (let i = 0; i < result.rows.length; i++) {
      baselines.push(this.mapRowToBaseline(result.rows.item(i)));
    }
    
    return baselines;
  }

  /**
   * Delete baseline record (soft delete)
   * 删除基线记录（软删除）
   */
  static async softDelete(id: number): Promise<void> {
    const sql = `
      UPDATE baseline_records 
      SET is_deleted = 1, updated_at = ? 
      WHERE id = ?
    `;
    await DatabaseManager.getInstance().executeSql(sql, [new Date().toISOString(), id]);
  }

  /**
   * Get the latest active baseline for a specific meter
   * 获取指定水表的最新有效基线
   */
  static async getLatestActiveBaseline(meterId: number): Promise<BaselineRecord | null> {
    const sql = `
      SELECT * FROM baseline_records 
      WHERE meter_id = ? AND status = 'Active' AND is_deleted = 0
      ORDER BY baseline_date DESC 
      LIMIT 1
    `;
    const result = await DatabaseManager.getInstance().executeSql(sql, [meterId]);
    return result.rows.length > 0 ? this.mapRowToBaseline(result.rows.item(0)) : null;
  }

  /**
   * 将数据库行映射为BaselineRecord对象
   */
  private static mapRowToBaseline(row: any): BaselineRecord {
    return {
      id: row.id,
      meter_id: row.meter_id,
      baseline_date: row.baseline_date,
      baseline_value: row.baseline_value,
      baseline_type: row.baseline_type,
      status: row.status,
      import_batch: row.import_batch,
      source_file: row.source_file,
      data_source: row.data_source,
      validation_notes: row.validation_notes,
      is_validated: row.is_validated, // Keep as number (0 or 1)
      validated_date: row.validated_date,
      validated_by: row.validated_by,
      has_validation_errors: row.has_validation_errors, // Keep as number (0 or 1)
      validation_errors: row.validation_errors,
      is_anomalous: row.is_anomalous, // Keep as number (0 or 1)
      anomaly_description: row.anomaly_description,
      previous_baseline_id: row.previous_baseline_id,
      previous_baseline_value: row.previous_baseline_value,
      variance_from_previous: row.variance_from_previous,
      variance_percentage: row.variance_percentage,
      is_corrected: row.is_corrected, // Keep as number (0 or 1)
      corrected_date: row.corrected_date,
      corrected_by: row.corrected_by,
      correction_reason: row.correction_reason,
      confidence_level: row.confidence_level,
      notes: row.notes,
      created_at: row.created_at,
      updated_at: row.updated_at,
      created_by: row.created_by,
      updated_by: row.updated_by,
      is_deleted: row.is_deleted, // Keep as number (0 or 1)
      sync_status: row.sync_status,
      last_sync_date: row.last_sync_date
    };
  }
}