import DatabaseManager from '../../database/DatabaseManager';
import { 
  MeterReadingSyncLog, 
  CreateMeterReadingSyncLogRequest, 
  UpdateMeterReadingSyncLogRequest,
  MeterReadingSyncLogStatus 
} from '../../database/models/MeterReadingSyncLog';

/**
 * Meter Reading Sync Log Repository
 * 水表读数同步日志数据访问层
 */
export class MeterReadingSyncLogRepository {

  /**
   * Create a new sync log entry
   * 创建新的同步日志条目
   */
  static async create(syncLogData: CreateMeterReadingSyncLogRequest): Promise<MeterReadingSyncLog> {
    const now = new Date().toISOString();
    
    const sql = `
      INSERT INTO meter_reading_sync_logs (
        local_reading_id, sync_status, last_sync_time,
        last_update_time, sync_errors, retry_count, conflict_status,
        offline_indicator, sync_task_count, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    const params = [
      syncLogData.local_reading_id,
      syncLogData.sync_status || MeterReadingSyncLogStatus.PENDING,
      syncLogData.last_sync_time || null,
      syncLogData.last_update_time || now,
      syncLogData.sync_errors || null,
      syncLogData.retry_count || 0,
      syncLogData.conflict_status || 'none',
      syncLogData.offline_indicator || 1,
      syncLogData.sync_task_count || 1,
      now,
      now
    ];

    try {
      const result = await DatabaseManager.getInstance().executeSql(sql, params);
      console.log(`✅ MeterReadingSyncLogRepository: Created sync log with ID: ${result.insertId}`);
      
      // Return the created sync log
      return await this.findById(result.insertId!);
    } catch (error) {
      console.error('❌ MeterReadingSyncLogRepository: Error creating sync log:', error);
      throw error;
    }
  }

  /**
   * Find sync log by ID
   * 根据ID查找同步日志
   */
  static async findById(syncLogId: number): Promise<MeterReadingSyncLog> {
    const sql = 'SELECT * FROM meter_reading_sync_logs WHERE sync_log_id = ?';
    
    try {
      const result = await DatabaseManager.getInstance().executeSql(sql, [syncLogId]);
      
      if (result.rows.length === 0) {
        throw new Error(`Sync log with ID ${syncLogId} not found`);
      }

      return this.mapRowToSyncLog(result.rows.item(0));
    } catch (error) {
      console.error(`❌ MeterReadingSyncLogRepository: Error finding sync log ${syncLogId}:`, error);
      throw error;
    }
  }

  /**
   * Find sync log by local reading ID
   * 根据本地读数ID查找同步日志
   */
  static async findByLocalReadingId(localReadingId: number): Promise<MeterReadingSyncLog | null> {
    const sql = 'SELECT * FROM meter_reading_sync_logs WHERE local_reading_id = ?';
    
    try {
      const result = await DatabaseManager.getInstance().executeSql(sql, [localReadingId]);
      
      if (result.rows.length === 0) {
        return null;
      }

      return this.mapRowToSyncLog(result.rows.item(0));
    } catch (error) {
      console.error(`❌ MeterReadingSyncLogRepository: Error finding sync log for reading ${localReadingId}:`, error);
      throw error;
    }
  }

  /**
   * Update sync log
   * 更新同步日志
   */
  static async update(syncLogId: number, updateData: UpdateMeterReadingSyncLogRequest): Promise<MeterReadingSyncLog> {
    const now = new Date().toISOString();
    
    // Build dynamic update query
    const updateFields: string[] = [];
    const params: any[] = [];

    if (updateData.sync_status !== undefined) {
      updateFields.push('sync_status = ?');
      params.push(updateData.sync_status);
    }
    if (updateData.last_sync_time !== undefined) {
      updateFields.push('last_sync_time = ?');
      params.push(updateData.last_sync_time);
    }
    if (updateData.last_update_time !== undefined) {
      updateFields.push('last_update_time = ?');
      params.push(updateData.last_update_time);
    }
    if (updateData.sync_errors !== undefined) {
      updateFields.push('sync_errors = ?');
      params.push(updateData.sync_errors);
    }
    if (updateData.retry_count !== undefined) {
      updateFields.push('retry_count = ?');
      params.push(updateData.retry_count);
    }
    if (updateData.conflict_status !== undefined) {
      updateFields.push('conflict_status = ?');
      params.push(updateData.conflict_status);
    }
    if (updateData.offline_indicator !== undefined) {
      updateFields.push('offline_indicator = ?');
      params.push(updateData.offline_indicator);
    }
    if (updateData.sync_task_count !== undefined) {
      updateFields.push('sync_task_count = ?');
      params.push(updateData.sync_task_count);
    }

    // Always update updated_at
    updateFields.push('updated_at = ?');
    params.push(now);

    // Add WHERE condition
    params.push(syncLogId);

    const sql = `UPDATE meter_reading_sync_logs SET ${updateFields.join(', ')} WHERE sync_log_id = ?`;

    try {
      await DatabaseManager.getInstance().executeSql(sql, params);
      console.log(`✅ MeterReadingSyncLogRepository: Updated sync log ${syncLogId}`);
      
      return await this.findById(syncLogId);
    } catch (error) {
      console.error(`❌ MeterReadingSyncLogRepository: Error updating sync log ${syncLogId}:`, error);
      throw error;
    }
  }

  /**
   * Get pending sync logs
   * 获取待同步的日志
   */
  static async getPendingSyncLogs(limit: number = 50): Promise<MeterReadingSyncLog[]> {
    const sql = `
      SELECT * FROM meter_reading_sync_logs 
      WHERE sync_status = 'pending' 
      ORDER BY sync_log_id ASC 
      LIMIT ?
    `;

    try {
      const result = await DatabaseManager.getInstance().executeSql(sql, [limit]);
      const syncLogs: MeterReadingSyncLog[] = [];

      for (let i = 0; i < result.rows.length; i++) {
        syncLogs.push(this.mapRowToSyncLog(result.rows.item(i)));
      }

      return syncLogs;
    } catch (error) {
      console.error('❌ MeterReadingSyncLogRepository: Error getting pending sync logs:', error);
      throw error;
    }
  }

  /**
   * Get failed sync logs for retry
   * 获取失败的同步日志进行重试
   */
  static async getFailedSyncLogs(maxRetryCount: number = 3, limit: number = 50): Promise<MeterReadingSyncLog[]> {
    const sql = `
      SELECT * FROM meter_reading_sync_logs 
      WHERE sync_status = 'error' AND retry_count < ? 
      ORDER BY sync_log_id ASC 
      LIMIT ?
    `;

    try {
      const result = await DatabaseManager.getInstance().executeSql(sql, [maxRetryCount, limit]);
      const syncLogs: MeterReadingSyncLog[] = [];

      for (let i = 0; i < result.rows.length; i++) {
        syncLogs.push(this.mapRowToSyncLog(result.rows.item(i)));
      }

      return syncLogs;
    } catch (error) {
      console.error('❌ MeterReadingSyncLogRepository: Error getting failed sync logs:', error);
      throw error;
    }
  }

  /**
   * Increment retry count
   * 增加重试次数
   */
  static async incrementRetryCount(syncLogId: number): Promise<void> {
    const now = new Date().toISOString();
    const sql = `
      UPDATE meter_reading_sync_logs 
      SET retry_count = retry_count + 1, last_update_time = ?, updated_at = ? 
      WHERE sync_log_id = ?
    `;

    try {
      await DatabaseManager.getInstance().executeSql(sql, [now, now, syncLogId]);
      console.log(`✅ MeterReadingSyncLogRepository: Incremented retry count for sync log ${syncLogId}`);
    } catch (error) {
      console.error(`❌ MeterReadingSyncLogRepository: Error incrementing retry count for sync log ${syncLogId}:`, error);
      throw error;
    }
  }

  /**
   * Delete sync log
   * 删除同步日志
   */
  static async delete(syncLogId: number): Promise<void> {
    const sql = 'DELETE FROM meter_reading_sync_logs WHERE sync_log_id = ?';

    try {
      await DatabaseManager.getInstance().executeSql(sql, [syncLogId]);
      console.log(`✅ MeterReadingSyncLogRepository: Deleted sync log ${syncLogId}`);
    } catch (error) {
      console.error(`❌ MeterReadingSyncLogRepository: Error deleting sync log ${syncLogId}:`, error);
      throw error;
    }
  }

  /**
   * Get sync statistics
   * 获取同步统计信息
   */
  static async getSyncStatistics(): Promise<{
    pending: number;
    synced: number;
    error: number;
    conflict: number;
    total: number;
  }> {
    const sql = `
      SELECT 
        COUNT(CASE WHEN sync_status = 'pending' THEN 1 END) as pending,
        COUNT(CASE WHEN sync_status = 'synced' THEN 1 END) as synced,
        COUNT(CASE WHEN sync_status = 'error' THEN 1 END) as error,
        COUNT(CASE WHEN sync_status = 'conflict' THEN 1 END) as conflict,
        COUNT(*) as total
      FROM meter_reading_sync_logs
    `;

    try {
      const result = await DatabaseManager.getInstance().executeSql(sql);
      return result.rows.item(0);
    } catch (error) {
      console.error('❌ MeterReadingSyncLogRepository: Error getting sync statistics:', error);
      throw error;
    }
  }

  /**
   * Map database row to MeterReadingSyncLog object
   * 将数据库行映射为MeterReadingSyncLog对象
   */
  private static mapRowToSyncLog(row: any): MeterReadingSyncLog {
    return {
      sync_log_id: row.sync_log_id,
      local_reading_id: row.local_reading_id,
      sync_status: row.sync_status,
      last_sync_time: row.last_sync_time,
      last_update_time: row.last_update_time,
      sync_errors: row.sync_errors,
      retry_count: row.retry_count,
      conflict_status: row.conflict_status,
      offline_indicator: row.offline_indicator,
      sync_task_count: row.sync_task_count,
      created_at: row.created_at,
      updated_at: row.updated_at
    };
  }
} 