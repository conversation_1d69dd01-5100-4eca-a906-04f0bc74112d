import DatabaseManager from '../../database/DatabaseManager';
import { WorkTask, CreateWorkTaskRequest, UpdateWorkTaskRequest } from '../../database/models';
import { WaterMeterRepository } from './WaterMeterRepository';

export class TaskRepository {
  private static get dbManager() {
    return DatabaseManager.getInstance();
  }

  // Helper method to convert database row to WorkTask object
  private static mapRowToWorkTask(row: any): WorkTask {
    return {
      id: row.id,
      created_at: row.created_at,
      updated_at: row.updated_at,
      is_deleted: row.is_deleted,
      created_by: row.created_by,
      updated_by: row.updated_by,
      name: row.name,
      description: row.description,
      status: row.status,
      priority: row.priority,
      type: row.type,
      assigned_user_id: row.assigned_user_id,
      meter_id: row.meter_id,
      due_date: row.due_date,
      scheduled_date: row.scheduled_date,
      completed_date: row.completed_date,
      location: row.location,
      service_address: row.service_address,
      instructions: row.instructions,
      notes: row.notes,
    };
  }

  // Get user's assigned tasks
  static async getUserTasks(userId: number): Promise<WorkTask[]> {
    try {
      const sql = `
        SELECT * FROM work_tasks 
        WHERE assigned_user_id = ? AND is_deleted = 0 
        ORDER BY due_date ASC, created_at DESC
      `;
      const result = await this.dbManager.executeSql(sql, [userId]);
      
      const tasks: WorkTask[] = [];
      for (let i = 0; i < result.rows.length; i++) {
        tasks.push(this.mapRowToWorkTask(result.rows.item(i)));
      }
      return tasks;
    } catch (error) {
      console.error('Error getting user tasks:', error);
      return [];
    }
  }

  // Create a new work task
  static async create(taskRequest: any): Promise<WorkTask> {
    const currentTime = new Date().toISOString();
    
    const sql = `
      INSERT INTO work_tasks (
        ${taskRequest.id ? 'id,' : ''} created_at, updated_at, is_deleted, created_by, updated_by,
        name, description, status, priority, type, assigned_user_id, meter_id,
        due_date, scheduled_date, location, service_address, instructions, notes
      )
      VALUES (${taskRequest.id ? '?,' : ''} ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    const params = [
      ...(taskRequest.id ? [taskRequest.id] : []),
      currentTime,
      currentTime,
      0, // is_deleted
      'mobile_app', // created_by
      'mobile_app', // updated_by
      taskRequest.name,
      taskRequest.description || '',
      taskRequest.status || 'Pending',
      taskRequest.priority || 'Medium',
      taskRequest.type || 'MeterReading',
      taskRequest.assigned_user_id,
      taskRequest.meter_id,
      taskRequest.due_date,
      taskRequest.scheduled_date,
      taskRequest.location || '',
      taskRequest.service_address || '',
      taskRequest.instructions || '',
      taskRequest.notes || ''
    ];

    const result = await this.dbManager.executeSql(sql, params);
    const taskId = taskRequest.id || result.insertId;
    
    const task = await this.findById(taskId);
    if (!task) {
      throw new Error('Failed to retrieve created task');
    }
    return task;
  }

  // Find work task by ID
  static async findById(id: number): Promise<WorkTask> {
    const sql = 'SELECT * FROM work_tasks WHERE id = ? AND is_deleted = 0';
    const result = await this.dbManager.executeSql(sql, [id]);

    if (result.rows.length === 0) {
      throw new Error(`Work task with ID ${id} not found`);
    }

    const row = result.rows.item(0);
    return this.mapRowToWorkTask(row);
  }

  // Get tasks assigned to a specific user
  static async getTasksByUserId(userId: number): Promise<WorkTask[]> {
    const sql = `
      SELECT * FROM work_tasks 
      WHERE assigned_user_id = ? AND is_deleted = 0 
      ORDER BY created_at DESC
    `;
    
    const result = await this.dbManager.executeSql(sql, [userId]);
    
    const tasks: WorkTask[] = [];
    for (let i = 0; i < result.rows.length; i++) {
      const row = result.rows.item(i);
      tasks.push(this.mapRowToWorkTask(row));
    }
    return tasks;
  }

  // Get tasks by status
  static async getTasksByStatus(status: string): Promise<WorkTask[]> {
    const sql = `
      SELECT * FROM work_tasks 
      WHERE status = ? AND is_deleted = 0 
      ORDER BY created_at DESC
    `;
    
    const result = await this.dbManager.executeSql(sql, [status]);
    
    const tasks: WorkTask[] = [];
    for (let i = 0; i < result.rows.length; i++) {
      const row = result.rows.item(i);
      tasks.push(this.mapRowToWorkTask(row));
    }
    return tasks;
  }

  // Get tasks assigned to user with specific status
  static async getUserTasksByStatus(userId: number, status: string): Promise<WorkTask[]> {
    const sql = `
      SELECT * FROM work_tasks 
      WHERE assigned_user_id = ? AND status = ? AND is_deleted = 0 
      ORDER BY created_at DESC
    `;
    
    const result = await this.dbManager.executeSql(sql, [userId, status]);
    
    const tasks: WorkTask[] = [];
    for (let i = 0; i < result.rows.length; i++) {
      const row = result.rows.item(i);
      tasks.push(this.mapRowToWorkTask(row));
    }
    return tasks;
  }

  // Upsert work task using backend ID (INSERT OR REPLACE for sync operations)
  static async upsert(taskData: any): Promise<WorkTask> {
    const sql = `
      INSERT OR REPLACE INTO work_tasks (
        id, created_at, updated_at, is_deleted, created_by, updated_by,
        name, description, status, priority, type, assigned_user_id, meter_id,
        due_date, scheduled_date, completed_date, location, service_address,
        instructions, notes
      )
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    const params = [
      taskData.id,                                   // Use backend ID directly
      taskData.created_at || new Date().toISOString(),
      taskData.updated_at || new Date().toISOString(),
      taskData.is_deleted || 0,
      taskData.created_by || 'mobile_app',
      taskData.updated_by || 'mobile_app',
      taskData.name,
      taskData.description,
      taskData.status,
      taskData.priority,
      taskData.type,
      taskData.assigned_user_id && taskData.assigned_user_id > 0 ? taskData.assigned_user_id : null,  // 外键约束：0转为NULL
      taskData.meter_id,
      taskData.due_date,
      taskData.scheduled_date,
      taskData.completed_date,
      taskData.location,
      taskData.service_address,
      taskData.instructions,
      taskData.notes
    ];

    try {
      await this.dbManager.executeSql(sql, params);
      console.log(`Work task upserted successfully with backend ID: ${taskData.id}`);
      
      // Return the upserted task using direct query
      const result = await this.dbManager.executeSql('SELECT * FROM work_tasks WHERE id = ? AND is_deleted = 0', [taskData.id]);
      if (result.rows.length > 0) {
        return this.mapRowToWorkTask(result.rows.item(0));
      }
      throw new Error(`Failed to retrieve upserted task with ID ${taskData.id}`);
    } catch (error) {
      console.error('Error upserting work task:', error);
      throw new Error(`Failed to upsert work task with ID ${taskData.id}: ${error}`);
    }
  }

  // Update existing task
  static async update(taskId: number, updateData: Partial<WorkTask>): Promise<WorkTask | null> {
    try {
      const updateFields: string[] = [];
      const values: any[] = [];

      // Build dynamic update query
      Object.keys(updateData).forEach(key => {
        if (key !== 'id' && updateData[key as keyof WorkTask] !== undefined) {
          updateFields.push(`${key} = ?`);
          values.push(updateData[key as keyof WorkTask]);
        }
      });

      if (updateFields.length === 0) {
        return this.findById(taskId);
      }

      // Always update the updated_at field
      updateFields.push('updated_at = ?');
      values.push(new Date().toISOString());

      const sql = `
        UPDATE work_tasks
        SET ${updateFields.join(', ')}
        WHERE id = ? AND is_deleted = 0
      `;
      values.push(taskId);

      await this.dbManager.executeSql(sql, values);
      return this.findById(taskId);
    } catch (error) {
      console.error('Error updating task:', error);
      throw error;
    }
  }

  // Soft delete work task
  static async delete(id: number): Promise<boolean> {
    const currentTime = new Date().toISOString();
    
    const sql = `
      UPDATE work_tasks 
      SET is_deleted = 1, updated_at = ?, updated_by = ?
      WHERE id = ? AND is_deleted = 0
    `;
    
    const result = await this.dbManager.executeSql(sql, [currentTime, 'mobile_app', id]);
    
    return result.rowsAffected > 0;
  }

  // Get all active work tasks
  static async getAll(): Promise<WorkTask[]> {
    const sql = `
      SELECT * FROM work_tasks 
      WHERE is_deleted = 0 
      ORDER BY created_at DESC
    `;
    
    const result = await this.dbManager.executeSql(sql);
    
    const tasks: WorkTask[] = [];
    for (let i = 0; i < result.rows.length; i++) {
      const row = result.rows.item(i);
      tasks.push(this.mapRowToWorkTask(row));
    }
    return tasks;
  }

  // Get total task count
  static async getTotalCount(): Promise<number> {
    try {
      const sql = `
        SELECT COUNT(*) as count FROM work_tasks 
        WHERE is_deleted = 0
      `;
      
      const result = await this.dbManager.executeSql(sql, []);
      const row = result.rows.item(0);
      return row.count;
    } catch (error) {
      console.error('TaskRepository: Error getting total count:', error);
      return 0;
    }
  }

  // Get task count by status
  static async getTaskCountByStatus(status: string): Promise<number> {
    try {
      const sql = `
        SELECT COUNT(*) as count FROM work_tasks 
        WHERE status = ? AND is_deleted = 0
      `;
      
      const result = await this.dbManager.executeSql(sql, [status]);
      const row = result.rows.item(0);
      return row.count;
    } catch (error) {
      console.error('TaskRepository: Error getting task count by status:', error);
      return 0;
    }
  }

  // Get user task count by status
  static async getUserTaskCountByStatus(userId: number, status: string): Promise<number> {
    let sql = `
      SELECT COUNT(*) as count FROM work_tasks 
      WHERE assigned_user_id = ? AND is_deleted = 0
    `;
    const params: any[] = [userId];

    // If status is provided and not empty, add status filter
    if (status && status.trim() !== '') {
      sql += ' AND status = ?';
      params.push(status);
    }
    
    const result = await this.dbManager.executeSql(sql, params);
    const row = result.rows.item(0);
    return row.count;
  }

  // Clear all tasks (for testing/development)
  static async clearAll(): Promise<void> {
    const sql = 'DELETE FROM work_tasks';
    await this.dbManager.executeSql(sql);
  }

  // ============ Task Detail and Action Methods ============

  // Get task details (converts WorkTask to MobileTaskDetailDto format for UI compatibility)
  static async getTaskDetails(taskId: number): Promise<any> {
    try {
      const task = await this.findById(taskId);
      
      // Get meter information if task has meter_id
      let meterInfo: any = {};
      if (task.meter_id) {
        try {
          const meter = await WaterMeterRepository.findById(task.meter_id);
          if (meter) {
            meterInfo = {
              meterNumber: meter.serial_number,
              lastReading: meter.last_reading,
              lastReadingDate: meter.last_reading_date ? new Date(meter.last_reading_date) : undefined,
              customerName: meter.customer_name || '',
              address: meter.address || task.service_address || task.location || '',
              latitude: meter.latitude,
              longitude: meter.longitude,
              assetId: meter.asset_id,
              accountNumber: meter.account_number,
              meterType: meter.meter_type,
              status: meter.status
            };
          }
        } catch (error) {
          console.warn(`Could not load meter ${task.meter_id}:`, error);
          meterInfo = {
            meterNumber: task.meter_id.toString(),
            lastReading: 0,
            lastReadingDate: undefined,
            customerName: '',
            address: task.service_address || task.location || '',
            latitude: undefined,
            longitude: undefined
          };
        }
      }
      
      // Convert WorkTask to MobileTaskDetailDto format for compatibility
      return {
        id: task.id,
        name: task.name,
        description: task.description,
        status: task.status,
        priority: task.priority,
        type: task.type,
        meterId: task.meter_id,
        meterNumber: meterInfo.meterNumber || task.meter_id?.toString() || '',
        assignedUserId: task.assigned_user_id,  // 直接映射，无需转换
        dueDate: task.due_date ? new Date(task.due_date) : undefined,
        startDate: task.scheduled_date ? new Date(task.scheduled_date) : undefined,  // Map scheduled_date to startDate
        location: task.location,
        serviceAddress: task.service_address,
        instructions: task.instructions,
        notes: task.notes,
        createdAt: task.created_at ? new Date(task.created_at) : new Date(),
        updatedAt: task.updated_at ? new Date(task.updated_at) : new Date(),
        
        // Meter information from joined data
        lastReading: meterInfo.lastReading || 0,
        lastReadingDate: meterInfo.lastReadingDate,
        customerName: meterInfo.customerName || '',
        customerPhone: '', // Not available in current model
        customerEmail: '', // Not available in current model
        address: meterInfo.address || '',
        latitude: meterInfo.latitude,
        longitude: meterInfo.longitude,
        assetId: meterInfo.assetId,
        accountNumber: meterInfo.accountNumber,
        meterType: meterInfo.meterType,
        
        // Additional assignment fields for API compatibility
        assignedDate: task.created_at ? new Date(task.created_at) : new Date(),
        assignedBy: task.created_by || 'system',
        assignmentType: 'direct',
        estimatedHours: undefined,
        progressPercentage: task.status === 'Completed' ? 100 : (task.status === 'InProgress' ? 50 : 0),
        isUrgent: task.priority === 'Critical' || task.priority === 'High',
        isOverdue: task.due_date ? new Date(task.due_date) < new Date() && task.status !== 'Completed' : false,
        daysUntilDue: task.due_date ? Math.ceil((new Date(task.due_date).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24)) : undefined,
        
        // Additional arrays for detail view
        readingHistory: [],
        assignmentHistory: [],
        attachedPhotos: [],
        alerts: []
      };
    } catch (error) {
      console.error('TaskRepository: Error getting task details:', error);
      throw error;
    }
  }

  // Start a task (update status to InProgress)
  static async startTask(taskId: number): Promise<WorkTask> {
    const currentTime = new Date().toISOString();
    
    const result = await this.update(taskId, {
      status: 'InProgress'
    });
    
    if (!result) {
      throw new Error(`Failed to start task with ID ${taskId}`);
    }
    
    return result;
  }

  // Update task status
  static async updateTaskStatus(taskId: number, status: string): Promise<WorkTask> {
    const updateRequest: any = {
      status: status
    };

    // If completing the task, set completed date
    if (status === 'Completed') {
      updateRequest.completed_date = new Date().toISOString();
    }

    const result = await this.update(taskId, updateRequest);
    
    if (!result) {
      throw new Error(`Failed to update task status for ID ${taskId}`);
    }
    
    return result;
  }

  // Complete a task (shorthand for updateTaskStatus with Completed)
  static async completeTask(taskId: number): Promise<WorkTask> {
    return await this.updateTaskStatus(taskId, 'Completed');
  }

  // Cancel a task
  static async cancelTask(taskId: number): Promise<WorkTask> {
    return await this.updateTaskStatus(taskId, 'Cancelled');
  }

  // Pause a task
  static async pauseTask(taskId: number): Promise<WorkTask> {
    return await this.updateTaskStatus(taskId, 'Pending');
  }

  // Get task with meter information (for reading tasks)
  static async getTaskWithMeterInfo(taskId: number): Promise<any> {
    const sql = `
      SELECT 
        t.*,
        m.serial_number as meter_serial_number,
        m.asset_id as meter_asset_id,
        m.account_number as meter_account_number,
        m.location as meter_location,
        m.address as meter_address,
        m.customer_name as meter_customer_name,
        m.last_reading as meter_last_reading,
        m.last_reading_date as meter_last_reading_date,
        m.latitude as meter_latitude,
        m.longitude as meter_longitude
      FROM work_tasks t
      LEFT JOIN water_meters m ON t.meter_id = m.id
      WHERE t.id = ? AND t.is_deleted = 0
    `;
    
    const result = await this.dbManager.executeSql(sql, [taskId]);
    
    if (result.rows.length === 0) {
      throw new Error(`Task with ID ${taskId} not found`);
    }
    
    const row = result.rows.item(0);
    
    return {
      // Task information
      ...this.mapRowToWorkTask(row),
      
      // Meter information
      meterInfo: {
        serialNumber: row.meter_serial_number,
        assetId: row.meter_asset_id,
        accountNumber: row.meter_account_number,
        location: row.meter_location,
        address: row.meter_address,
        customerName: row.meter_customer_name,
        lastReading: row.meter_last_reading,
        lastReadingDate: row.meter_last_reading_date,
        latitude: row.meter_latitude,
        longitude: row.meter_longitude
      }
    };
  }

  // Pagination method for debugging
  static async getAllPaginated(page: number = 1, pageSize: number = 10): Promise<{data: WorkTask[], total: number}> {
    try {
      const offset = (page - 1) * pageSize;
      
      // Get total count
      const countResult = await this.dbManager.executeSql('SELECT COUNT(*) as total FROM work_tasks WHERE is_deleted = 0');
      const total = countResult.rows.item(0).total;
      
      // Get paginated data with joined meter info
      const sql = `
        SELECT wt.*, wm.serial_number as meter_serial_number 
        FROM work_tasks wt 
        LEFT JOIN water_meters wm ON wt.meter_id = wm.id 
        WHERE wt.is_deleted = 0 
        ORDER BY wt.created_at DESC 
        LIMIT ? OFFSET ?
      `;
      const result = await this.dbManager.executeSql(sql, [pageSize, offset]);
      
      const tasks: WorkTask[] = [];
      for (let i = 0; i < result.rows.length; i++) {
        const row = result.rows.item(i);
        const task = this.mapRowToWorkTask(row);
        // Add meter serial number for debugging
        (task as any).meter_serial_number = row.meter_serial_number;
        tasks.push(task);
      }
      
      return { data: tasks, total };
    } catch (error) {
      console.error('Error getting paginated tasks:', error);
      return { data: [], total: 0 };
    }
  }
} 