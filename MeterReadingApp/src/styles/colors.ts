// CORDE-inspired color scheme for MeterReadingApp
export const colors = {
  // Primary colors based on CORDE theme
  primary: {
    orange: '#FF620A',
    orangeLight: '#FF8A47',
    orangeDark: '#E55407',
  },
  
  // Background colors
  background: {
    light: '#FFFFFF',
    dark: '#000000',
    cardLight: '#F8F9FA',
    cardDark: '#1A1A1A',
  },
  
  // Text colors
  text: {
    primary: '#000000',
    primaryDark: '#FFFFFF',
    secondary: '#6B7280',
    secondaryDark: '#9CA3AF',
    muted: '#9CA3AF',
    mutedDark: '#6B7280',
  },
  
  // Function colors (inspired by CORDE)
  functions: {
    reading: '#4CAF50',
    tasks: '#2196F3', 
    sync: '#FF9800',
    reports: '#9C27B0',
    map: '#F44336',
    settings: '#607D8B',
  },
  
  // Status colors
  status: {
    success: '#10B981',
    error: '#EF4444',
    warning: '#F59E0B',
    info: '#3B82F6',
  },
  
  // Border and shadow colors
  border: {
    light: '#E5E7EB',
    dark: '#374151',
  },
  
  // Transparent overlays
  overlay: {
    light: 'rgba(0, 0, 0, 0.1)',
    dark: 'rgba(255, 255, 255, 0.1)',
  },
};

export const getThemeColors = (mode: 'light' | 'dark') => ({
  background: mode === 'dark' ? colors.background.dark : colors.background.light,
  cardBackground: mode === 'dark' ? colors.background.cardDark : colors.background.cardLight,
  textPrimary: mode === 'dark' ? colors.text.primaryDark : colors.text.primary,
  textSecondary: mode === 'dark' ? colors.text.secondaryDark : colors.text.secondary,
  textMuted: mode === 'dark' ? colors.text.mutedDark : colors.text.muted,
  border: mode === 'dark' ? colors.border.dark : colors.border.light,
  overlay: mode === 'dark' ? colors.overlay.dark : colors.overlay.light,
}); 