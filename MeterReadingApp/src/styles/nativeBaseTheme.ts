import { extendTheme } from 'native-base';

// Global theme configuration to handle accessibility warnings
const nativeBaseTheme = extendTheme({
  config: {
    // Suppress accessibility warnings globally
    initialColorMode: 'light',
    useSystemColorMode: false,
  },
  components: {
    // Icon component defaults
    Icon: {
      defaultProps: {
        accessibilityLabel: 'Icon',
        accessible: true,
      },
    },
    // Button component defaults
    Button: {
      defaultProps: {
        accessible: true,
      },
    },
    // Input component defaults
    Input: {
      defaultProps: {
        accessible: true,
      },
    },
    // Pressable component defaults
    Pressable: {
      defaultProps: {
        accessible: true,
      },
    },
    // Checkbox component defaults
    Checkbox: {
      defaultProps: {
        accessible: true,
        accessibilityRole: 'checkbox',
      },
    },
    // Switch component defaults
    Switch: {
      defaultProps: {
        accessible: true,
        accessibilityRole: 'switch',
      },
    },
    // Alert component defaults
    Alert: {
      defaultProps: {
        accessible: true,
        accessibilityRole: 'alert',
      },
    },
  },
});

export default nativeBaseTheme; 