export const orangeSubtleButtonStyle = {
  bg: 'orange.500',
  _pressed: { bg: 'orange.600' },
  _text: { color: 'white', fontWeight: 'bold' },
};

export const blackSubtleButtonStyle = {
  bg: 'gray.800',
  _pressed: { bg: 'gray.900' },
  _text: { color: 'white', fontWeight: 'bold' },
};

export const orangeOutlineButtonStyle = {
  variant: 'outline',
  borderColor: 'orange.500',
  _text: { color: 'orange.500', fontWeight: 'bold' },
  _pressed: { bg: 'orange.50' },
};

export const takePhotoButtonStyle = {
  bg: 'blue.500',
  _pressed: { bg: 'blue.600' },
  _text: { color: 'white', fontWeight: 'bold' },
};

export const choosePhotoButtonStyle = {
  bg: 'green.500',
  _pressed: { bg: 'green.600' },
  _text: { color: 'white', fontWeight: 'bold' },
}; 