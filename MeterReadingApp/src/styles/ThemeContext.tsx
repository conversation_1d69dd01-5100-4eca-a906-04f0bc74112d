import React, { createContext, useContext, useState, useEffect } from 'react';
import { Appearance, ColorSchemeName } from 'react-native';

const defaultMode = Appearance.getColorScheme() || 'light';

interface ThemeContextType {
  mode: ColorSchemeName;
  setMode: (mode: ColorSchemeName) => void;
}

const ThemeContext = createContext<ThemeContextType>({ 
  mode: defaultMode, 
  setMode: mode => console.log(mode) 
});

export const ThemeProvider: React.FC<{children: React.ReactNode}> = ({ children }) => {
  const [mode, setMode] = useState<ColorSchemeName>(defaultMode);

  useEffect(() => {
    const subscription = Appearance.addChangeListener(({ colorScheme }) => {
      setMode(colorScheme);
    });
    return () => subscription.remove();
  }, []);

  return (
    <ThemeContext.Provider value={{ mode, setMode }}>
      {children}
    </ThemeContext.Provider>
  );
};

export const useTheme = () => useContext(ThemeContext); 