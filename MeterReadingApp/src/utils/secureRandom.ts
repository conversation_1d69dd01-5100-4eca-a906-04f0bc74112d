// Secure random number generation utilities
// Uses crypto.getRandomValues for cryptographically secure randomness

/**
 * Generate a secure random string with alphanumeric characters
 * @param length - Length of the string to generate
 * @returns Secure random string
 */
export function generateSecureRandomString(length: number = 9): string {
  const charset = 'abcdefghijklmnopqrstuvwxyz0123456789';
  const randomBytes = new Uint8Array(length);
  crypto.getRandomValues(randomBytes);
  
  let result = '';
  for (let i = 0; i < length; i++) {
    result += charset[randomBytes[i] % charset.length];
  }
  
  return result;
}

/**
 * Generate a secure random ID with timestamp prefix
 * @param prefix - Prefix for the ID (e.g., 'opencv', 'offline')
 * @returns Secure random ID
 */
export function generateSecureId(prefix: string): string {
  const timestamp = Date.now();
  const randomPart = generateSecureRandomString(9);
  return `${prefix}_${timestamp}_${randomPart}`;
}

/**
 * Generate a secure random float between 0 and 1
 * Replacement for Math.random()
 * @returns Secure random float [0, 1)
 */
export function getSecureRandomFloat(): number {
  const randomBytes = new Uint32Array(1);
  crypto.getRandomValues(randomBytes);
  return randomBytes[0] / (0xFFFFFFFF + 1);
}

/**
 * Generate a secure random number within a range
 * @param min - Minimum value (inclusive)
 * @param max - Maximum value (exclusive)
 * @returns Secure random number in range
 */
export function getSecureRandomInRange(min: number, max: number): number {
  return min + getSecureRandomFloat() * (max - min);
}

/**
 * Generate a secure random GPS coordinate offset
 * Used for mock/test locations
 * @param center - Center coordinate
 * @param range - Maximum offset range (e.g., 0.1 for ±0.1 degrees)
 * @returns Secure random coordinate
 */
export function generateSecureGpsOffset(center: number, range: number = 0.1): number {
  const offset = (getSecureRandomFloat() - 0.5) * range;
  return center + offset;
} 