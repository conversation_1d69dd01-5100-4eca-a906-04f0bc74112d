import { useToast } from 'native-base';
import React from 'react';
import CustomToast from '../components/common/CustomToast';

export type ToastType = 'success' | 'error' | 'warning' | 'info';

export interface ToastOptions {
  title?: string;
  description: string;
  duration?: number;
  placement?: 'top' | 'bottom' | 'center';
  isClosable?: boolean;
  useCustomToast?: boolean;
}

class ToastManager {
  private toastInstance: any = null;

  setToastInstance(toast: any) {
    this.toastInstance = toast;
  }

  private showToast(type: ToastType, options: ToastOptions) {
    if (!this.toastInstance) {
      console.warn('Toast instance not initialized');
      return;
    }

    const { 
      title, 
      description, 
      duration = 3000, 
      placement = 'top', 
      isClosable = true,
      useCustomToast = true 
    } = options;

    // Get emoji and default title based on type
    const getTypeConfig = (type: ToastType) => {
      switch (type) {
        case 'success':
          return { emoji: '✅', defaultTitle: 'Success' };
        case 'error':
          return { emoji: '❌', defaultTitle: 'Error' };
        case 'warning':
          return { emoji: '⚠️', defaultTitle: 'Warning' };
        case 'info':
          return { emoji: 'ℹ️', defaultTitle: 'Info' };
        default:
          return { emoji: 'ℹ️', defaultTitle: 'Info' };
      }
    };

    const { emoji, defaultTitle } = getTypeConfig(type);
    const finalTitle = title || defaultTitle;

    if (useCustomToast) {
      // Use custom toast component
      this.toastInstance.show({
        render: ({ id }: { id: string }) => (
          React.createElement(CustomToast, {
            id,
            title: finalTitle,
            description,
            type,
            isClosable,
            onClose: () => this.toastInstance.close(id),
          })
        ),
        placement,
        duration,
      });
    } else {
      // Use default toast
      this.toastInstance.show({
        title: `${emoji} ${finalTitle}`,
        description,
        duration,
        placement,
        isClosable,
      });
    }
  }

  // Convenience methods for different toast types
  success(message: string, title?: string, options?: Partial<ToastOptions>) {
    this.showToast('success', {
      description: message,
      title,
      ...options,
    });
  }

  error(message: string, title?: string, options?: Partial<ToastOptions>) {
    this.showToast('error', {
      description: message,
      title,
      ...options,
    });
  }

  warning(message: string, title?: string, options?: Partial<ToastOptions>) {
    this.showToast('warning', {
      description: message,
      title,
      ...options,
    });
  }

  info(message: string, title?: string, options?: Partial<ToastOptions>) {
    this.showToast('info', {
      description: message,
      title,
      ...options,
    });
  }

  // Quick methods for common scenarios
  loginSuccess() {
    this.success('Login successful!', 'Welcome', { duration: 2000 });
  }

  loginError() {
    this.error('Please enter username and password', 'Login Required');
  }

  networkError() {
    this.error('Network connection failed, please try again', 'Connection Error');
  }

  saveSuccess() {
    this.success('Data saved successfully', 'Saved');
  }

  validationError(field: string) {
    this.warning(`Please enter a valid ${field}`, 'Validation Error');
  }

  permissionError(permission: string) {
    this.error(`${permission} permission is required`, 'Permission Error');
  }

  operationSuccess(operation: string) {
    this.success(`${operation} completed successfully`, 'Success');
  }

  operationError(operation: string) {
    this.error(`${operation} failed, please try again`, 'Operation Error');
  }

  // OCR specific methods
  ocrSuccess() {
    this.success('Meter reading recognized successfully', 'Recognition Successful');
  }

  ocrError() {
    this.error('Recognition failed, please try another photo', 'Recognition Failed');
  }

  // Camera specific methods
  cameraError() {
    this.error('Failed to take photo, please try again', 'Camera Error');
  }

  galleryError() {
    this.error('Failed to select image, please try again', 'Gallery Error');
  }

  // Input validation methods
  noImageSelected() {
    this.warning('Please take or select a meter photo first', 'No Image Selected');
  }

  manualInputSuccess() {
    this.success('Manual reading has been saved', 'Input Successful');
  }

  invalidInput() {
    this.warning('Please enter a valid reading', 'Input Error');
  }

  // Sync related toasts
  syncSuccess() {
    this.showToast('success', {
      title: "Sync Complete",
      description: "Data synchronized successfully!"
    });
  }

  syncError() {
    this.showToast('error', {
      title: "Sync Failed",
      description: "Failed to synchronize data. Please try again."
    });
  }

  uploadStarted() {
    this.showToast('info', {
      title: "Upload Started",
      description: "Uploading completed tasks to server..."
    });
  }

  downloadStarted() {
    this.showToast('info', {
      title: "Download Started",
      description: "Downloading new tasks from server..."
    });
  }

  fullSyncStarted() {
    this.showToast('info', {
      title: "Full Sync Started",
      description: "Starting complete data synchronization..."
    });
  }
}

// Export singleton instance
export const toastManager = new ToastManager();

// Hook for components to use
export const useToastManager = () => {
  const toast = useToast();
  
  // Initialize toast instance if not already done
  if (!toastManager['toastInstance']) {
    toastManager.setToastInstance(toast);
  }
  
  return toastManager;
}; 