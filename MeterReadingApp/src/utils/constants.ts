// App configuration
export const APP_CONFIG = {
  NAME: 'Meter Reading App',
  VERSION: '1.0.0',
  BUILD_NUMBER: 1,
  
  // Database
  DATABASE_NAME: 'MeterReadingApp.db',
  DATABASE_VERSION: '1.0',
  
  // API
  DEFAULT_API_TIMEOUT: 30000,
  RETRY_ATTEMPTS: 3,
  
  // Sync
  AUTO_SYNC_INTERVAL: 300000, // 5 minutes
  SYNC_BATCH_SIZE: 50,
  MAX_OFFLINE_DAYS: 30,
  
  // Photos
  MAX_PHOTO_SIZE_MB: 10,
  PHOTO_QUALITY: 0.8,
  MAX_PHOTOS_PER_READING: 3,
  
  // Location
  GPS_TIMEOUT: 15000,
  GPS_MAX_AGE: 60000,
  GPS_ACCURACY_THRESHOLD: 100, // meters
  
  // UI
  TOAST_DURATION: 3000,
  LOADING_DELAY: 500,
};

// Storage keys
export const STORAGE_KEYS = {
  AUTH_TOKEN: 'auth_token',
  AUTH_USER: 'auth_user',
  API_BASE_URL: 'api_base_url',
  LAST_SYNC_TIMESTAMP: 'last_sync_timestamp',
  SETTINGS: 'app_settings',
  OFFLINE_DATA: 'offline_data',
  CACHED_IMAGES: 'cached_images',
};

// Sync statuses
export const SYNC_STATUS = {
  SYNCED: 'synced',
  PENDING: 'pending',
  ERROR: 'error',
  UPLOADING: 'uploading',
  DOWNLOADING: 'downloading',
} as const;

// Task statuses
export const TASK_STATUS = {
  ASSIGNED: 'assigned',
  IN_PROGRESS: 'in_progress',
  COMPLETED: 'completed',
  CANCELLED: 'cancelled',
} as const;

// Priority levels
export const PRIORITY = {
  LOW: 'low',
  MEDIUM: 'medium',
  HIGH: 'high',
  URGENT: 'urgent',
} as const;

// User roles
export const USER_ROLES = {
  ADMIN: 'admin',
  SUPERVISOR: 'supervisor',
  READER: 'reader',
} as const;

// Reading methods
export const READING_METHODS = {
  MANUAL: 'manual',
  OCR: 'ocr',
  AUTOMATIC: 'automatic',
} as const;

// Error codes
export const ERROR_CODES = {
  NETWORK_ERROR: 'NETWORK_ERROR',
  AUTH_ERROR: 'AUTH_ERROR',
  DATABASE_ERROR: 'DATABASE_ERROR',
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  PERMISSION_ERROR: 'PERMISSION_ERROR',
  FILE_ERROR: 'FILE_ERROR',
  LOCATION_ERROR: 'LOCATION_ERROR',
  SYNC_ERROR: 'SYNC_ERROR',
} as const;

// File types
export const FILE_TYPES = {
  IMAGE: {
    JPEG: 'image/jpeg',
    PNG: 'image/png',
    WEBP: 'image/webp',
  },
  DOCUMENT: {
    PDF: 'application/pdf',
    CSV: 'text/csv',
    JSON: 'application/json',
  },
} as const;

// Date formats
export const DATE_FORMATS = {
  ISO: 'YYYY-MM-DDTHH:mm:ss.SSSZ',
  DISPLAY: 'MMM DD, YYYY',
  DISPLAY_WITH_TIME: 'MMM DD, YYYY HH:mm',
  API: 'YYYY-MM-DD HH:mm:ss',
  FILE_NAME: 'YYYY-MM-DD_HH-mm-ss',
} as const;

// Regex patterns
export const PATTERNS = {
  EMAIL: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  PHONE: /^\+?[\d\s\-\(\)]+$/,
  METER_NUMBER: /^[A-Z0-9]{6,12}$/,
  READING_VALUE: /^\d+(\.\d{1,3})?$/,
  USERNAME: /^[a-zA-Z0-9_]{3,20}$/,
} as const;

// Permissions
export const PERMISSIONS = {
  LOCATION: 'android.permission.ACCESS_FINE_LOCATION',
  CAMERA: 'android.permission.CAMERA',
  STORAGE: 'android.permission.WRITE_EXTERNAL_STORAGE',
} as const; 