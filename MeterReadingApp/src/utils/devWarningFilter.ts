if (__DEV__) {
  const originalConsoleWarn = console.warn;
  const ignoredWarnings = [
    'SSRProvider is not necessary',
    'new NativeEventEmitter()',
    'If you do not provide children, you must specify an aria-label for accessibility',
    'is not a valid color or brush',
    'Warning: validateDOMNesting',
    'Math.random is not cryptographically secure',
    // Icon-related warnings that we want to see (don't ignore these)
    // 'Failed prop type: Invalid prop `name`' - Keep this visible to catch icon issues
    // Add other warnings to ignore here
  ];

  console.warn = (...args) => {
    if (typeof args[0] === 'string' && ignoredWarnings.some(warning => args[0].includes(warning))) {
      return;
    }
    originalConsoleWarn(...args);
  };
} 