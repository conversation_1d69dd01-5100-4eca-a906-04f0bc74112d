# ToastManager 使用指南

## 基本用法

```tsx
import { useToastManager } from '../utils/ToastManager';

const MyComponent = () => {
  const toastManager = useToastManager();

  // 基本方法
  const showSuccess = () => toastManager.success('操作成功！');
  const showError = () => toastManager.error('操作失败！');
  const showWarning = () => toastManager.warning('请注意！');
  const showInfo = () => toastManager.info('提示信息');

  // 预定义的快捷方法
  const handleLogin = async () => {
    try {
      await login();
      toastManager.loginSuccess(); // ✅ Welcome - Login successful!
    } catch {
      toastManager.loginError(); // ❌ Login Required - Please enter username and password
    }
  };

  // OCR 相关
  const handleOCR = () => {
    toastManager.ocrSuccess(); // ✅ Recognition Successful - Meter reading recognized successfully
    toastManager.ocrError(); // ❌ Recognition Failed - Recognition failed, please try another photo
  };

  // 相机相关
  const handleCamera = () => {
    toastManager.cameraError(); // ❌ Camera Error - Failed to take photo, please try again
    toastManager.galleryError(); // ❌ Gallery Error - Failed to select image, please try again
  };

  // 输入验证相关
  const handleValidation = () => {
    toastManager.noImageSelected(); // ⚠️ No Image Selected - Please take or select a meter photo first
    toastManager.manualInputSuccess(); // ✅ Input Successful - Manual reading has been saved
    toastManager.invalidInput(); // ⚠️ Input Error - Please enter a valid reading
  };

  // 权限相关
  const handlePermission = () => {
    toastManager.permissionError('Camera'); // ❌ Permission Error - Camera permission is required
  };

  // 自定义选项
  const customToast = () => {
    toastManager.success('自定义消息', '自定义标题', {
      duration: 5000,
      placement: 'bottom',
      useCustomToast: false // 使用默认样式
    });
  };
};
```

## 替换前后对比

### 替换前（臃肿）:
```tsx
const toast = useToast();

toast.show({
  title: "✅ Recognition Successful",
  description: "Meter reading recognized successfully",
  duration: 3000,
  placement: 'top',
});
```

### 替换后（简洁）:
```tsx
const toastManager = useToastManager();

toastManager.ocrSuccess();
```

## 可用的快捷方法

- `loginSuccess()` - 登录成功
- `loginError()` - 登录错误
- `networkError()` - 网络错误
- `saveSuccess()` - 保存成功
- `validationError(field)` - 验证错误
- `permissionError(permission)` - 权限错误
- `operationSuccess(operation)` - 操作成功
- `operationError(operation)` - 操作失败
- `ocrSuccess()` - OCR识别成功
- `ocrError()` - OCR识别失败
- `cameraError()` - 相机错误
- `galleryError()` - 图库错误
- `noImageSelected()` - 未选择图片
- `manualInputSuccess()` - 手动输入成功
- `invalidInput()` - 无效输入

## 优势

1. **代码简洁**: 一行代码替代5-8行配置
2. **统一样式**: 所有Toast使用一致的设计和图标
3. **类型安全**: TypeScript支持，减少错误
4. **易于维护**: 集中管理所有Toast消息
5. **自定义选项**: 支持自定义标题、持续时间、位置等 