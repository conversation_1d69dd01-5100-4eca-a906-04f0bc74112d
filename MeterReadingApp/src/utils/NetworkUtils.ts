// utils/NetworkUtils.ts
// Network utilities for Meter Reading App
// Based on CORDE-Mobile-Application/src/utils/NetworkUtils.ts

import NetInfo, {
  NetInfoState,
  NetInfoSubscription,
} from '@react-native-community/netinfo';

export class NetworkService {
  // Singleton pattern for network service
  private static instance: NetworkService;
  // Current network connection status
  private isConnected: boolean = false;
  // Registered listeners for network state changes
  private listeners: ((isConnected: boolean) => void)[] = [];
  // Unsubscribe function for network state listener
  private unsubscribe: NetInfoSubscription | null = null;
  // Promise to ensure network initialization is complete
  private initializationPromise: Promise<void>;

  private constructor() {
    this.initializationPromise = this.initNetInfo();
  }

  public static getInstance(): NetworkService {
    if (!NetworkService.instance) {
      NetworkService.instance = new NetworkService();
    }
    return NetworkService.instance;
  }

  // Initialize network listening and state acquisition
  private async initNetInfo(): Promise<void> {
    return new Promise<void>(resolve => {
      this.unsubscribe = NetInfo.addEventListener(this.handleNetInfoChange);
      // Get current network state
      NetInfo.fetch().then(state => {
        this.handleNetInfoChange(state);
        resolve();
      });
    });
  }

  // Handle network state changes
  private handleNetInfoChange = (state: NetInfoState) => {
    const connected = state.isConnected === true && state.isInternetReachable === true;
    if (this.isConnected !== connected) {
      this.isConnected = connected;
      console.log(`NetworkService: Network status changed to ${connected ? 'connected' : 'disconnected'}`);
      this.notifyListeners();
    }
  };

  // Register network state listener
  public addListener(listener: (isConnected: boolean) => void) {
    this.listeners.push(listener);
  }

  // Remove network state listener
  public removeListener(listener: (isConnected: boolean) => void) {
    this.listeners = this.listeners.filter(l => l !== listener);
  }

  // Notify all registered listeners
  private notifyListeners() {
    this.listeners.forEach(listener => {
      try {
        listener(this.isConnected);
      } catch (error) {
        console.error('NetworkService: Error notifying listener:', error);
      }
    });
  }

  // Get current network connection status
  public async isNetworkConnected(): Promise<boolean> {
    await this.initializationPromise;
    return this.isConnected;
  }

  // Get current network connection status synchronously
  public isNetworkConnectedSync(): boolean {
    return this.isConnected;
  }

  // Get detailed network state
  public async getNetworkState(): Promise<NetInfoState> {
    return await NetInfo.fetch();
  }

  // Clean up resources
  public destroy() {
    if (this.unsubscribe) {
      this.unsubscribe();
      this.unsubscribe = null;
    }
    this.listeners = [];
  }
}

// Singleton instance for global access
export const networkService = NetworkService.getInstance(); 