// Simple password hashing using react-native-get-random-values
// No external dependencies needed - just secure random generation
import 'react-native-get-random-values';

/**
 * Simple but secure password hashing using multiple rounds of SHA-like hashing
 * Uses crypto.getRandomValues for secure salt generation
 */

function simpleHash(data: string): string {
  // Simple hash function that works in React Native
  let hash = 0;
  for (let i = 0; i < data.length; i++) {
    const char = data.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32-bit integer
  }
  return Math.abs(hash).toString(36);
}

function secureSimpleHash(password: string, salt: string, rounds: number): string {
  let hash = password + salt;
  for (let i = 0; i < rounds; i++) {
    hash = simpleHash(hash + i.toString());
  }
  return hash;
}

/**
 * Hash a password with secure salt
 * @param password - Plain text password
 * @param saltRounds - Number of rounds (default 10, meaning 2^10 = 1024 iterations)
 */
export function hashPasswordSync(password: string, saltRounds: number = 10): string {
  // Generate secure random salt
  const saltBytes = crypto.getRandomValues(new Uint8Array(16));
  const salt = Array.from(saltBytes, byte => byte.toString(16).padStart(2, '0')).join('');
  
  const iterations = Math.pow(2, saltRounds);
  const hash = secureSimpleHash(password, salt, iterations);
  
  // Format: $rn$rounds$salt$hash (rn = react-native)
  return `$rn$${saltRounds}$${salt}$${hash}`;
}

/**
 * Compare password with hash
 * @param password - Plain text password
 * @param hash - Stored hash
 */
export function comparePasswordSync(password: string, hash: string): boolean {
  try {
    const parts = hash.split('$');
    if (parts.length !== 5 || parts[1] !== 'rn') {
      return false;
    }
    
    const saltRounds = parseInt(parts[2]);
    const salt = parts[3];
    const storedHash = parts[4];
    
    const iterations = Math.pow(2, saltRounds);
    const testHash = secureSimpleHash(password, salt, iterations);
    
    return testHash === storedHash;
  } catch {
    return false;
  }
}