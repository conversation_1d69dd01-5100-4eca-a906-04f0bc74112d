import React, { createContext, useContext, useReducer, useEffect, ReactNode } from 'react';
import { AuthService } from '../services/AuthService';
import { User } from '../types/User';

interface AuthState {
  isAuthenticated: boolean;
  user: User | null;
  isLoading: boolean;
  error: string | null;
}

type AuthAction =
  | {type: 'LOGIN_START'}
  | {type: 'LOGIN_SUCCESS'; payload: {user: User}}
  | {type: 'LOGIN_ERROR'; payload: string}
  | {type: 'LOGOUT'}
  | {type: 'CLEAR_ERROR'}
  | {type: 'SET_LOADING'; payload: boolean};

interface AuthContextType {
  state: AuthState;
  login: (username: string, password: string, rememberMe?: boolean) => Promise<void>;
  logout: () => Promise<void>;
  clearError: () => void;
}

// Initial state
const initialState: AuthState = {
  isAuthenticated: false,
  user: null,
  isLoading: false,
  error: null,
};

// Reducer
const authReducer = (state: AuthState, action: AuthAction): AuthState => {
  switch (action.type) {
    case 'LOGIN_START':
      return {...state, isLoading: true, error: null};
    case 'LOGIN_SUCCESS':
      return {
        ...state,
        isAuthenticated: true,
        user: action.payload.user,
        isLoading: false,
        error: null,
      };
    case 'LOGIN_ERROR':
      return {
        ...state,
        isAuthenticated: false,
        user: null,
        isLoading: false,
        error: action.payload,
      };
    case 'LOGOUT':
      return {
        ...initialState,
      };
    case 'CLEAR_ERROR':
      return {...state, error: null};
    case 'SET_LOADING':
      return {...state, isLoading: action.payload};
    default:
      return state;
  }
};

// Create context
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Provider component
interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({children}) => {
  const [state, dispatch] = useReducer(authReducer, initialState);

  // Load saved authentication data on app start
  useEffect(() => {
    loadSavedAuth();
  }, []);

  const loadSavedAuth = async () => {
    try {
      dispatch({type: 'SET_LOADING', payload: true});
      
      // First check for current authenticated user
      const currentUser = await AuthService.getCurrentUser();
      if (currentUser && currentUser.isAuthenticated) {
        dispatch({type: 'LOGIN_SUCCESS', payload: {user: currentUser}});
        return;
      }
      
      // Then try auto login if remember me is enabled
      const autoLoginUser = await AuthService.autoLogin();
      if (autoLoginUser && autoLoginUser.isAuthenticated) {
        dispatch({type: 'LOGIN_SUCCESS', payload: {user: autoLoginUser}});
      }
    } catch (error) {
      console.error('Failed to load saved auth:', error);
    } finally {
      dispatch({type: 'SET_LOADING', payload: false});
    }
  };

  const login = async (username: string, password: string, rememberMe: boolean = false): Promise<void> => {
    try {
      dispatch({type: 'LOGIN_START'});

      // Use existing AuthService directly
      const user = await AuthService.login(username, password, rememberMe);

      dispatch({
        type: 'LOGIN_SUCCESS',
        payload: {user},
      });
    } catch (error: any) {
      dispatch({
        type: 'LOGIN_ERROR',
        payload: error.message || 'Login failed',
      });
      throw error;
    }
  };

  const logout = async (): Promise<void> => {
    try {
      // Use existing AuthService
      await AuthService.logout();
      dispatch({type: 'LOGOUT'});
    } catch (error) {
      console.error('Failed to logout:', error);
    }
  };

  const clearError = () => {
    dispatch({type: 'CLEAR_ERROR'});
  };

  const value: AuthContextType = {
    state,
    login,
    logout,
    clearError,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

// Hook to use auth context
export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}; 