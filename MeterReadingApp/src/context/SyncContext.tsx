import React, {createContext, useContext, useReducer, useEffect, ReactNode} from 'react';
import NetInfo from '@react-native-community/netinfo';
import SyncService from '../services/sync/SyncService';
import DatabaseManager from '../database/DatabaseManager';

// Types
interface SyncState {
  isOnline: boolean;
  isSyncing: boolean;
  lastSyncTime: Date | null;
  pendingUploads: number;
  pendingDownloads: number;
  syncProgress: number;
  error: string | null;
  autoSyncEnabled: boolean;
}

type SyncAction =
  | {type: 'SET_ONLINE_STATUS'; payload: boolean}
  | {type: 'SYNC_START'}
  | {type: 'SYNC_SUCCESS'; payload: {timestamp: Date}}
  | {type: 'SYNC_ERROR'; payload: string}
  | {type: 'SYNC_PROGRESS'; payload: number}
  | {type: 'SET_PENDING_COUNTS'; payload: {uploads: number; downloads: number}}
  | {type: 'TOGG<PERSON>_AUTO_SYNC'}
  | {type: 'CLEAR_ERROR'};

interface SyncContextType {
  state: SyncState;
  startSync: () => Promise<void>;
  stopSync: () => void;
  toggleAutoSync: () => void;
  clearError: () => void;
  refreshPendingCounts: () => Promise<void>;
}

// Initial state
const initialState: SyncState = {
  isOnline: false,
  isSyncing: false,
  lastSyncTime: null,
  pendingUploads: 0,
  pendingDownloads: 0,
  syncProgress: 0,
  error: null,
  autoSyncEnabled: true,
};

// Reducer
const syncReducer = (state: SyncState, action: SyncAction): SyncState => {
  switch (action.type) {
    case 'SET_ONLINE_STATUS':
      return {...state, isOnline: action.payload};
    case 'SYNC_START':
      return {...state, isSyncing: true, error: null, syncProgress: 0};
    case 'SYNC_SUCCESS':
      return {
        ...state,
        isSyncing: false,
        lastSyncTime: action.payload.timestamp,
        syncProgress: 100,
        error: null,
      };
    case 'SYNC_ERROR':
      return {
        ...state,
        isSyncing: false,
        error: action.payload,
        syncProgress: 0,
      };
    case 'SYNC_PROGRESS':
      return {...state, syncProgress: action.payload};
    case 'SET_PENDING_COUNTS':
      return {
        ...state,
        pendingUploads: action.payload.uploads,
        pendingDownloads: action.payload.downloads,
      };
    case 'TOGGLE_AUTO_SYNC':
      return {...state, autoSyncEnabled: !state.autoSyncEnabled};
    case 'CLEAR_ERROR':
      return {...state, error: null};
    default:
      return state;
  }
};

// Create context
const SyncContext = createContext<SyncContextType | undefined>(undefined);

// Provider component
interface SyncProviderProps {
  children: ReactNode;
}

export const SyncProvider: React.FC<SyncProviderProps> = ({children}) => {
  const [state, dispatch] = useReducer(syncReducer, initialState);

  // Monitor network connectivity
  useEffect(() => {
    const unsubscribe = NetInfo.addEventListener(state => {
      dispatch({type: 'SET_ONLINE_STATUS', payload: !!state.isConnected});
    });

    return unsubscribe;
  }, []);

  // Auto-sync when coming online
  useEffect(() => {
    if (state.isOnline && state.autoSyncEnabled && !state.isSyncing) {
      // Auto sync with a small delay
      const timer = setTimeout(() => {
        startSync();
      }, 2000);

      return () => clearTimeout(timer);
    }
  }, [state.isOnline, state.autoSyncEnabled]);

  // Refresh pending counts periodically
  useEffect(() => {
    refreshPendingCounts();
    const interval = setInterval(refreshPendingCounts, 30000); // Every 30 seconds

    return () => clearInterval(interval);
  }, []);

  const startSync = async (): Promise<void> => {
    if (!state.isOnline) {
      dispatch({type: 'SYNC_ERROR', payload: 'No internet connection'});
      return;
    }

    if (state.isSyncing) {
      return; // Already syncing
    }

    try {
      dispatch({type: 'SYNC_START'});

      await SyncService.performFullSync((progress: number) => {
        dispatch({type: 'SYNC_PROGRESS', payload: progress});
      });

      dispatch({type: 'SYNC_SUCCESS', payload: {timestamp: new Date()}});
      await refreshPendingCounts();
    } catch (error: any) {
      dispatch({
        type: 'SYNC_ERROR',
        payload: error.message || 'Sync failed',
      });
    }
  };

  const stopSync = () => {
    // Implementation to stop ongoing sync
    dispatch({type: 'SYNC_ERROR', payload: 'Sync cancelled by user'});
  };

  const toggleAutoSync = () => {
    dispatch({type: 'TOGGLE_AUTO_SYNC'});
  };

  const clearError = () => {
    dispatch({type: 'CLEAR_ERROR'});
  };

  const refreshPendingCounts = async (): Promise<void> => {
    try {
      const db = DatabaseManager.getInstance();

      // Count pending uploads
      const uploadResults = await db.executeSql(
        "SELECT COUNT(*) as count FROM meter_readings WHERE sync_status = 'pending'"
      );
      const pendingUploads = uploadResults[0]?.rows?.item(0)?.count || 0;

      // For now, assume downloads are handled differently
      const pendingDownloads = 0;

      dispatch({
        type: 'SET_PENDING_COUNTS',
        payload: {uploads: pendingUploads, downloads: pendingDownloads},
      });
    } catch (error) {
      console.error('Failed to refresh pending counts:', error);
    }
  };

  const value: SyncContextType = {
    state,
    startSync,
    stopSync,
    toggleAutoSync,
    clearError,
    refreshPendingCounts,
  };

  return <SyncContext.Provider value={value}>{children}</SyncContext.Provider>;
};

// Hook to use sync context
export const useSync = (): SyncContextType => {
  const context = useContext(SyncContext);
  if (context === undefined) {
    throw new Error('useSync must be used within a SyncProvider');
  }
  return context;
}; 