import React from 'react';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import LoginScreen from '../screens/auth/LoginScreen';
import MainScreen from '../screens/MainScreen';
import MeterReadingScreen from '../screens/reading/MeterReadingScreen';
import TaskListScreen from '../screens/tasks/TaskListScreen';
import TaskDetailScreen from '../screens/tasks/TaskDetailScreen';
import SyncScreen from '../screens/sync/SyncScreen';
import SettingsScreen from '../screens/settings/SettingsScreen';
import UserProfileScreen from '../screens/profile/UserProfileScreen';
import ReportScreen from '../screens/report/ReportScreen';
import MapScreen from '../screens/map/MapScreen';
import DebugDataScreen from '../screens/debug/DebugDataScreen';
import { useAuth } from '../context/AuthContext';
import { Text, View } from 'react-native';
import { useTheme } from '../styles/ThemeContext';

export type RootStackParamList = {
  Login: undefined;
  Main: undefined;
  MeterReading: { taskId?: string; meterId?: string };
  TaskList: undefined;
  TaskDetail: { taskId: string };
  Sync: undefined;
  Map: undefined;
  Settings: undefined;
  UserProfile: undefined;
  Report: undefined;
  DebugData: undefined;
};

const Stack = createNativeStackNavigator<RootStackParamList>();

const LoadingScreen = () => (
  <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: '#f5f5f5' }}>
    <Text style={{ fontSize: 16, color: '#666' }}>Loading...</Text>
  </View>
);

const AppNavigator = () => {
  const { state } = useAuth();
  const { mode } = useTheme();

  console.log('AppNavigator render - Auth state:', state);

  if (state.isLoading) {
    return <LoadingScreen />;
  }

  // Define common header style based on CORDE's design
  const commonHeaderOptions = {
    headerStyle: {
      backgroundColor: mode === 'dark' ? '#282828' : '#FF620A',
    },
    headerTintColor: 'white',
    headerTitleAlign: 'center' as const,
    headerTitleStyle: {
      fontSize: 18,
      fontWeight: '600' as const,
    },
  };

  return (
    <Stack.Navigator 
      initialRouteName={state.isAuthenticated ? "Main" : "Login"}
      screenOptions={{ 
        contentStyle: { backgroundColor: mode === 'dark' ? '#000000' : '#ffffff' }
      }}
    >
      {state.isAuthenticated ? (
        <>
          {/* Main Screen - No header (like CORDE's MainDashboard) */}
          <Stack.Screen 
            name="Main" 
            component={MainScreen}
            options={{ headerShown: false }}
          />
          
          {/* Meter Reading Screen */}
          <Stack.Screen 
            name="MeterReading" 
            component={MeterReadingScreen}
            options={{
              ...commonHeaderOptions,
              title: 'Meter Reading',
              headerShown: true,
            }}
          />
          
          {/* Task List Screen */}
          <Stack.Screen 
            name="TaskList" 
            component={TaskListScreen}
            options={{
              ...commonHeaderOptions,
              title: 'Task List',
              headerShown: true,
            }}
          />
          
          {/* Task Detail Screen */}
          <Stack.Screen 
            name="TaskDetail" 
            component={TaskDetailScreen}
            options={({route}) => ({
              ...commonHeaderOptions,
              title: `Task Details`,
              headerShown: true,
            })}
          />
          
          {/* Sync Screen */}
          <Stack.Screen 
            name="Sync" 
            component={SyncScreen}
            options={{
              ...commonHeaderOptions,
              title: 'Data Sync',
              headerShown: true,
            }}
          />
          
          {/* Map Screen */}
          <Stack.Screen 
            name="Map" 
            component={MapScreen}
            options={{
              ...commonHeaderOptions,
              title: 'Map View',
              headerShown: true,
            }}
          />
          
          {/* Settings Screen */}
          <Stack.Screen 
            name="Settings" 
            component={SettingsScreen}
            options={{
              ...commonHeaderOptions,
              title: 'Settings',
              headerShown: true,
            }}
          />
          
          {/* User Profile Screen */}
          <Stack.Screen 
            name="UserProfile" 
            component={UserProfileScreen}
            options={{
              ...commonHeaderOptions,
              title: 'User Profile',
              headerShown: true,
            }}
          />
          
          {/* Report Screen */}
          <Stack.Screen 
            name="Report" 
            component={ReportScreen}
            options={{
              ...commonHeaderOptions,
              title: 'Reports',
              headerShown: true,
            }}
          />
          
          {/* Debug Data Screen */}
          <Stack.Screen 
            name="DebugData" 
            component={DebugDataScreen}
            options={{
              ...commonHeaderOptions,
              title: 'Debug Data',
              headerShown: true,
            }}
          />
        </>
      ) : (
        <Stack.Screen 
          name="Login" 
          component={LoginScreen}
          options={{ headerShown: false }}
        />
      )}
    </Stack.Navigator>
  );
};

export default AppNavigator; 