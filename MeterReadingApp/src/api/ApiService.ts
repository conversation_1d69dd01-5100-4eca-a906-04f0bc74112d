import AsyncStorage from '@react-native-async-storage/async-storage';

export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  statusCode?: number;
}

export interface ApiConfig {
  baseURL: string;
  timeout?: number;
  headers?: Record<string, string>;
}

class ApiService {
  private config: ApiConfig = {
    baseURL: 'http://localhost:5000/api', // Default, will be configurable
    timeout: 30000,
    headers: {
      'Content-Type': 'application/json',
    },
  };

  constructor(config?: Partial<ApiConfig>) {
    if (config) {
      this.config = { ...this.config, ...config };
    }
  }

  async setBaseURL(url: string): Promise<void> {
    this.config.baseURL = url;
    await AsyncStorage.setItem('api_base_url', url);
  }

  async loadSavedConfig(): Promise<void> {
    const savedURL = await AsyncStorage.getItem('api_base_url');
    if (savedURL) {
      this.config.baseURL = savedURL;
    }
  }

  private async getAuthHeaders(): Promise<Record<string, string>> {
    const token = await AsyncStorage.getItem('auth_token');
    const headers: Record<string, string> = { ...this.config.headers };
    
    if (token) {
      headers.Authorization = `Bearer ${token}`;
    }
    
    return headers;
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    try {
      const url = `${this.config.baseURL}${endpoint}`;
      const headers = await this.getAuthHeaders();

      const config: RequestInit = {
        ...options,
        headers: {
          ...headers,
          ...options.headers,
        },
      };

      // Add timeout
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), this.config.timeout);
      config.signal = controller.signal;

      const response = await fetch(url, config);
      clearTimeout(timeoutId);

      const responseData = await response.json();

      if (!response.ok) {
        return {
          success: false,
          error: responseData.message || `HTTP ${response.status}`,
          statusCode: response.status,
        };
      }

      return {
        success: true,
        data: responseData,
        statusCode: response.status,
      };
    } catch (error: any) {
      console.error('API Request failed:', error);
      return {
        success: false,
        error: error.message || 'Network error',
      };
    }
  }

  // HTTP Methods
  async get<T>(endpoint: string): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: 'GET' });
  }

  async post<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async put<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  }

  async patch<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'PATCH',
      body: JSON.stringify(data),
    });
  }

  async delete<T>(endpoint: string): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: 'DELETE' });
  }

  // File upload method
  async uploadFile<T>(
    endpoint: string,
    file: any,
    additionalData?: Record<string, string>
  ): Promise<ApiResponse<T>> {
    try {
      const headers = await this.getAuthHeaders();
      delete headers['Content-Type']; // Let the browser set it for FormData

      const formData = new FormData();
      formData.append('file', file);

      if (additionalData) {
        Object.entries(additionalData).forEach(([key, value]) => {
          formData.append(key, value);
        });
      }

      return this.request<T>(endpoint, {
        method: 'POST',
        body: formData,
        headers,
      });
    } catch (error: any) {
      return {
        success: false,
        error: error.message,
      };
    }
  }

  // Health check
  async checkHealth(): Promise<boolean> {
    try {
      const response = await this.get('/health');
      return response.success;
    } catch {
      return false;
    }
  }

  // Authentication endpoints
  async login(username: string, password: string): Promise<ApiResponse<any>> {
    return this.post('/auth/login', { username, password });
  }

  async logout(): Promise<ApiResponse<any>> {
    return this.post('/auth/logout');
  }

  async refreshToken(): Promise<ApiResponse<any>> {
    return this.post('/auth/refresh');
  }

  // Water meter endpoints
  async getWaterMeters(): Promise<ApiResponse<any[]>> {
    return this.get('/water-meters');
  }

  async getWaterMeter(id: number): Promise<ApiResponse<any>> {
    return this.get(`/water-meters/${id}`);
  }

  async getWaterMetersSince(timestamp: string): Promise<ApiResponse<any[]>> {
    return this.get(`/water-meters/since/${timestamp}`);
  }

  // Meter reading endpoints
  async submitMeterReading(reading: any): Promise<ApiResponse<any>> {
    return this.post('/meter-readings', reading);
  }

  async getMeterReadings(meterId?: number): Promise<ApiResponse<any[]>> {
    const endpoint = meterId ? `/meter-readings?meter_id=${meterId}` : '/meter-readings';
    return this.get(endpoint);
  }

  async uploadMeterPhoto(readingId: number, photo: any): Promise<ApiResponse<any>> {
    return this.uploadFile(`/meter-readings/${readingId}/photo`, photo);
  }

  // Task endpoints
  async getTasks(): Promise<ApiResponse<any[]>> {
    return this.get('/tasks');
  }

  async getTask(id: number): Promise<ApiResponse<any>> {
    return this.get(`/tasks/${id}`);
  }

  async updateTaskStatus(id: number, status: string): Promise<ApiResponse<any>> {
    return this.patch(`/tasks/${id}`, { status });
  }

  async getTasksSince(timestamp: string): Promise<ApiResponse<any[]>> {
    return this.get(`/tasks/since/${timestamp}`);
  }

  // Sync endpoints
  async getSyncStatus(): Promise<ApiResponse<any>> {
    return this.get('/sync/status');
  }

  async requestSync(): Promise<ApiResponse<any>> {
    return this.post('/sync/request');
  }
}

export default new ApiService(); 