interface GeocodeResult {
  latitude: number;
  longitude: number;
  address: string;
}

class GeocodingService {
  private static instance: GeocodingService;
  private cache = new Map<string, GeocodeResult>();

  static getInstance(): GeocodingService {
    if (!GeocodingService.instance) {
      GeocodingService.instance = new GeocodingService();
    }
    return GeocodingService.instance;
  }

  async geocodeAddress(address: string): Promise<GeocodeResult | null> {
    try {
      // Check cache first
      if (this.cache.has(address)) {
        return this.cache.get(address)!;
      }

      // For now, return default Auckland coordinates
      // TODO: Implement actual geocoding API integration
      const defaultResult: GeocodeResult = {
        latitude: -36.8485,
        longitude: 174.7633,
        address: address
      };

      // Cache the result
      this.cache.set(address, defaultResult);
      return defaultResult;
    } catch (error) {
      console.error('Geocoding error:', error);
      return null;
    }
  }

  async reverseGeocode(latitude: number, longitude: number): Promise<string | null> {
    try {
      // TODO: Implement reverse geocoding
      return `${latitude.toFixed(4)}, ${longitude.toFixed(4)}`;
    } catch (error) {
      console.error('Reverse geocoding error:', error);
      return null;
    }
  }
}

export default GeocodingService.getInstance(); 