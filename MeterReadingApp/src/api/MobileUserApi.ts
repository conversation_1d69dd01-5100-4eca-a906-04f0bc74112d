import api from './BaseApi';
import {
  MobileUserWorkloadDto,
  MobileUserStatsDto,
  MobileUserProfileDto,
  LocationUpdateDto,
  UserStatusUpdateDto,
  MobileAppPreferencesDto,
  MobileDeviceDto,
  UserLeaderboardDto,
  UserAchievementDto,
  MobileSyncRequestDto,
  MobileSyncResponseDto,
  NearbyUserDto
} from '../types/mobile';

class MobileUserApi {
  private readonly baseUrl = '/mobile/users';

  /**
   * Get current user's workload information
   */
  async getMyWorkload(): Promise<MobileUserWorkloadDto> {
    try {
      const response = await api.get(`${this.baseUrl}/my-workload`);
      return response.data;
    } catch (error) {
      api.handleApiError(error, 'getMyWorkload');
    }
  }

  /**
   * Get current user's detailed statistics
   */
  async getMyStats(): Promise<MobileUserStatsDto> {
    try {
      const response = await api.get(`${this.baseUrl}/my-stats`);
      return response.data;
    } catch (error) {
      api.handleApiError(error, 'getMyStats');
    }
  }

  /**
   * Get current user's profile information
   */
  async getMyProfile(): Promise<MobileUserProfileDto> {
    try {
      const response = await api.get(`${this.baseUrl}/my-profile`);
      return response.data;
    } catch (error) {
      api.handleApiError(error, 'getMyProfile');
    }
  }

  /**
   * Update user's location
   */
  async updateLocation(location: LocationUpdateDto): Promise<{ message: string }> {
    try {
      const response = await api.post(`${this.baseUrl}/location`, location);
      return response.data;
    } catch (error) {
      api.handleApiError(error, 'updateLocation');
    }
  }

  /**
   * Update user's availability status
   */
  async updateStatus(status: UserStatusUpdateDto): Promise<{ 
    message: string; 
    status: string; 
    statusUntil?: Date 
  }> {
    try {
      const response = await api.post(`${this.baseUrl}/status`, status);
      return response.data;
    } catch (error) {
      api.handleApiError(error, 'updateStatus');
    }
  }

  /**
   * Update mobile app preferences
   */
  async updatePreferences(preferences: MobileAppPreferencesDto): Promise<{ message: string }> {
    try {
      const response = await api.put(`${this.baseUrl}/preferences`, preferences);
      return response.data;
    } catch (error) {
      api.handleApiError(error, 'updatePreferences');
    }
  }

  /**
   * Register a mobile device
   */
  async registerDevice(device: MobileDeviceDto): Promise<{ 
    message: string; 
    deviceId: string 
  }> {
    try {
      const response = await api.post(`${this.baseUrl}/register-device`, device);
      return response.data;
    } catch (error) {
      api.handleApiError(error, 'registerDevice');
    }
  }

  /**
   * Get user's registered devices
   */
  async getRegisteredDevices(): Promise<MobileDeviceDto[]> {
    try {
      const response = await api.get(`${this.baseUrl}/devices`);
      return response.data;
    } catch (error) {
      api.handleApiError(error, 'getRegisteredDevices');
    }
  }

  /**
   * Deactivate a mobile device
   */
  async deactivateDevice(deviceId: string): Promise<{ 
    message: string; 
    deviceId: string 
  }> {
    try {
      const response = await api.delete(`${this.baseUrl}/devices/${deviceId}`);
      return response.data;
    } catch (error) {
      api.handleApiError(error, 'deactivateDevice');
    }
  }

  /**
   * Get performance leaderboard
   */
  async getLeaderboard(
    period: 'week' | 'month' | 'quarter' = 'week',
    limit: number = 10
  ): Promise<UserLeaderboardDto[]> {
    try {
      const response = await api.get(`${this.baseUrl}/leaderboard?period=${period}&limit=${limit}`);
      return response.data;
    } catch (error) {
      api.handleApiError(error, 'getLeaderboard');
    }
  }

  /**
   * Get user achievements
   */
  async getMyAchievements(): Promise<UserAchievementDto[]> {
    try {
      const response = await api.get(`${this.baseUrl}/my-achievements`);
      return response.data;
    } catch (error) {
      api.handleApiError(error, 'getMyAchievements');
    }
  }

  /**
   * Sync mobile app data (heartbeat)
   */
  async syncHeartbeat(syncRequest: MobileSyncRequestDto): Promise<MobileSyncResponseDto> {
    try {
      const response = await api.post(`${this.baseUrl}/sync-heartbeat`, syncRequest);
      return response.data;
    } catch (error) {
      api.handleApiError(error, 'syncHeartbeat');
    }
  }

  /**
   * Get nearby team members
   */
  async getNearbyTeamMembers(
    latitude: number,
    longitude: number,
    radiusKm: number = 10.0
  ): Promise<NearbyUserDto[]> {
    try {
      const params = new URLSearchParams({
        latitude: latitude.toString(),
        longitude: longitude.toString(),
        radiusKm: radiusKm.toString(),
      });

      const response = await api.get(`${this.baseUrl}/nearby-team?${params.toString()}`);
      return response.data;
    } catch (error) {
      api.handleApiError(error, 'getNearbyTeamMembers');
    }
  }

  /**
   * Update location with activity tracking
   */
  async updateLocationWithActivity(
    latitude: number,
    longitude: number,
    accuracy?: number,
    activityType: 'reading' | 'travel' | 'break' | 'general' = 'general',
    notes?: string
  ): Promise<{ message: string }> {
    try {
      const locationUpdate: LocationUpdateDto = {
        Latitude: latitude,
        Longitude: longitude,
        Accuracy: accuracy,
        Timestamp: new Date(),
        ActivityType: activityType,
        Notes: notes,
      };

      return await this.updateLocation(locationUpdate);
    } catch (error) {
      api.handleApiError(error, 'updateLocationWithActivity');
    }
  }

  /**
   * Quick status updates
   */
  async setAvailable(notes?: string): Promise<{ message: string; status: string }> {
    try {
      const statusUpdate: UserStatusUpdateDto = {
        Status: 'Available',
        Notes: notes,
      };

      return await this.updateStatus(statusUpdate);
    } catch (error) {
      api.handleApiError(error, 'setAvailable');
    }
  }

  async setBusy(notes?: string, until?: Date): Promise<{ message: string; status: string }> {
    try {
      const statusUpdate: UserStatusUpdateDto = {
        Status: 'Busy',
        Notes: notes,
        StatusUntil: until,
      };

      return await this.updateStatus(statusUpdate);
    } catch (error) {
      api.handleApiError(error, 'setBusy');
    }
  }

  async setOnBreak(notes?: string, until?: Date): Promise<{ message: string; status: string }> {
    try {
      const statusUpdate: UserStatusUpdateDto = {
        Status: 'Break',
        Notes: notes,
        StatusUntil: until,
      };

      return await this.updateStatus(statusUpdate);
    } catch (error) {
      api.handleApiError(error, 'setOnBreak');
    }
  }

  async setOffline(): Promise<{ message: string; status: string }> {
    try {
      const statusUpdate: UserStatusUpdateDto = {
        Status: 'Offline',
      };

      return await this.updateStatus(statusUpdate);
    } catch (error) {
      api.handleApiError(error, 'setOffline');
    }
  }

  /**
   * Get user dashboard data
   */
  async getDashboardData(): Promise<{
    workload: MobileUserWorkloadDto;
    stats: MobileUserStatsDto;
    achievements: UserAchievementDto[];
    recentActivity: any[];
  }> {
    try {
      // Fetch multiple endpoints in parallel
      const [workload, stats, achievements] = await Promise.all([
        this.getMyWorkload(),
        this.getMyStats(),
        this.getMyAchievements(),
      ]);

      return {
        workload,
        stats,
        achievements: achievements.filter(a => a.IsNew).slice(0, 3), // Show only new achievements
        recentActivity: [], // This could be populated from other APIs
      };
    } catch (error) {
      api.handleApiError(error, 'getDashboardData');
      throw error; // Re-throw to properly handle the promise rejection
    }
  }

  /**
   * Register device with current device info
   */
  async registerCurrentDevice(
    deviceId: string,
    deviceName: string,
    platform: string,
    osVersion: string,
    appVersion: string
  ): Promise<{ message: string; deviceId: string }> {
    try {
      const device: MobileDeviceDto = {
        DeviceId: deviceId,
        DeviceName: deviceName,
        Platform: platform,
        OSVersion: osVersion,
        AppVersion: appVersion,
        LastUsed: new Date(),
        IsActive: true,
        RegisteredDate: new Date(),
      };

      return await this.registerDevice(device);
    } catch (error) {
      api.handleApiError(error, 'registerCurrentDevice');
    }
  }

  /**
   * Create sync heartbeat data
   */
  createSyncRequest(
    deviceId: string,
    appVersion: string,
    lastSyncTime: Date,
    latitude?: number,
    longitude?: number,
    activityStatus: string = 'active',
    pendingTaskUpdates: number = 0,
    pendingReadings: number = 0
  ): MobileSyncRequestDto {
    return {
      DeviceId: deviceId,
      AppVersion: appVersion,
      LastSyncTime: lastSyncTime,
      Latitude: latitude,
      Longitude: longitude,
      ActivityStatus: activityStatus,
      PendingTaskUpdates: pendingTaskUpdates,
      PendingReadings: pendingReadings,
    };
  }

  /**
   * Get user performance metrics
   */
  async getPerformanceMetrics(period: 'day' | 'week' | 'month' = 'week'): Promise<{
    completionRate: number;
    averageTime: number;
    qualityScore: number;
    productivity: number;
    rank: number;
    trend: 'up' | 'down' | 'stable';
  }> {
    try {
      const stats = await this.getMyStats();
      const leaderboard = await this.getLeaderboard(period === 'day' ? 'week' : period);
      
      const userEntry = leaderboard.find(entry => entry.IsCurrentUser);
      const rank = userEntry ? userEntry.Rank : leaderboard.length + 1;

      // Calculate trend (this is simplified - real implementation would need historical data)
      const trend: 'up' | 'down' | 'stable' = 'stable';

      return {
        completionRate: stats.CompletionRate,
        averageTime: stats.AverageCompletionTime,
        qualityScore: stats.AverageQualityScore,
        productivity: stats.TotalTasksCompleted / Math.max(1, stats.TotalTasksAssigned) * 100,
        rank,
        trend,
      };
    } catch (error) {
      api.handleApiError(error, 'getPerformanceMetrics');
    }
  }
}

export default new MobileUserApi(); 