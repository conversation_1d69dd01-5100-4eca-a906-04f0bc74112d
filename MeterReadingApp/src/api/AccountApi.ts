import axios from 'axios';
import { apiConfig } from '../config/apiConfig';
import * as Keychain from 'react-native-keychain';

interface LoginResponse {
  PersonID: number;
  FullName: string;
  FinCoCode: string;
}

// Create a separate axios instance for SICON API
const siconApi = axios.create({
  baseURL: apiConfig.SICON_API,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
});

// Add request interceptor for SICON API
siconApi.interceptors.request.use((config) => {
  console.log('🔄 SICON API Request:', {
    method: config.method,
    url: config.url,
    baseURL: config.baseURL,
    headers: config.headers,
    auth: config.auth ? { username: config.auth.username, password: '[HIDDEN]' } : undefined,
  });
  return config;
});

// Add response interceptor for SICON API
siconApi.interceptors.response.use(
  (response) => {
    console.log('✅ SICON API Response:', {
      status: response.status,
      url: response.config.url,
      data: response.data,
    });
    return response;
  },
  (error) => {
    console.error('❌ SICON API Error:', {
      message: error.message,
      status: error.response?.status,
      url: error.config?.url,
      data: error.response?.data,
    });
    return Promise.reject(error);
  }
);

export const login = async (username: string, password: string): Promise<LoginResponse> => {
  try {
    const response = await siconApi.get<LoginResponse>('/AccountApi', {
      auth: {
        username,
        password,
      },
    });

    console.log('Login response:', response.data);
    
    // Store credentials securely in Keychain
    await Keychain.setGenericPassword(
      'MeterApp_Credentials',
      JSON.stringify({ username, password }),
      { service: 'MeterApp_Credentials' }
    );

    return response.data;
  } catch (error) {
    console.error('Login error:', error);
    throw error;
  }
};

export const fetchAccountData = async () => {
  try {
    const response = await siconApi.get('/AccountApi');
    return response.data;
  } catch (error) {
    console.error('There was a problem with the axios operation:', error);
    throw error;
  }
}; 