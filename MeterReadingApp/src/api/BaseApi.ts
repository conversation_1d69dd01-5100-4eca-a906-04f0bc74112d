import axios, { AxiosInstance } from 'axios';
import { encode } from 'base-64';
import * as Keychain from 'react-native-keychain';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Platform } from 'react-native';
import { getApiBaseUrl, API_CONFIG } from '../config/apiConfig';

// Define the base URLs for different environments
const BASE_URLS = {
  // New Water Meter Management API - dynamically determined
  WATER_METER_API: getApiBaseUrl(),
  // Legacy SICON API (for fallback)
  SICON_API: API_CONFIG.SICON_API,
};

// Use the new API by default
const BASE_URL = BASE_URLS.WATER_METER_API;

// Log the configuration for debugging
console.log('🔧 API Configuration:', {
  platform: Platform.OS,
  isDev: __DEV__,
  baseUrl: BASE_URL,
});

interface CustomAxiosInstance extends AxiosInstance {
  handleApiError: (error: any, context: string) => never;
  setJwtToken: (token: string) => void;
  clearJwtToken: () => void;
  switchToLegacyApi: () => void;
  switchToNewApi: () => void;
  updateBaseUrl: (newUrl: string) => void;
  loginForJWT: (username: string, password: string) => Promise<any>;
}

// Create an instance of axios for the new API
const api: CustomAxiosInstance = axios.create({
  baseURL: BASE_URL,
  timeout: 30000, // Increased timeout for mobile
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
  transformRequest: [
    (data, headers) => {
      if (headers['Content-Type'] === 'application/json') {
        return JSON.stringify(data);
      }
      return data;
    },
  ],
}) as CustomAxiosInstance;

// JWT token management
let jwtToken: string | null = null;

// Add JWT login function
api.loginForJWT = async (username: string, password: string) => {
  try {
    console.log('🔐 Attempting JWT login for user:', username);
    
    const response = await api.post('/auth/login', {
      username,
      password,
      rememberMe: true
    });
    
    console.log('🔍 JWT Login Response:', response.data);
    
    if (response.data.success && response.data.token) {
      console.log('✅ JWT login successful');
      await api.setJwtToken(response.data.token);
      return response.data;
    } else {
      console.log('❌ JWT login failed:', response.data);
      throw new Error(response.data.message || 'JWT login failed');
    }
  } catch (error: any) {
    console.error('❌ JWT login error:', error);
    console.error('❌ Error details:', error.response?.data);
    console.error('❌ Error status:', error.response?.status);
    throw error;
  }
};

// Add a request interceptor that sets the authentication header
api.interceptors.request.use(async (config) => {
  try {
    // Try JWT token first (for new API)
    if (!jwtToken) {
      jwtToken = await AsyncStorage.getItem('jwt_token');
    }
    
    if (jwtToken) {
      config.headers.Authorization = `Bearer ${jwtToken}`;
      console.log('🔐 Using JWT token for authentication');
    } else {
      // Fallback to Basic Auth for both new API (when no JWT) and legacy SICON API
      const credentials = await Keychain.getGenericPassword({ service: 'MeterApp_Credentials' });
      if (credentials) {
        const parsedCredentials = JSON.parse(credentials.password);
        const encodedCredentials = encode(`${parsedCredentials.username}:${parsedCredentials.password}`);
        config.headers.Authorization = `Basic ${encodedCredentials}`;
        console.log('🔐 Using Basic Auth for authentication');
      } else {
        console.log('⚠️ No authentication credentials found');
      }
    }
  } catch (error) {
    console.error('Error setting auth header:', error);
  }

  // Add device information for mobile requests
  config.headers['X-Device-Platform'] = 'mobile';
  config.headers['X-App-Version'] = '1.0.0'; // This should come from app config

  const fullUrl = `${config.baseURL}${config.url}`;
  console.log('🚀 Starting Request', {
    method: config.method?.toUpperCase(),
    url: fullUrl,
    platform: Platform.OS,
    headers: { ...config.headers, Authorization: config.headers.Authorization ? '[REDACTED]' : undefined },
    data: config.data,
  });
  return config;
});

const handleApiError = (error: any, context: string): never => {
  console.error(`❌ Error in ${context}:`, error);
  
  if (error.response) {
    console.error('Response data:', error.response.data);
    console.error('Response status:', error.response.status);
    console.error('Response headers:', error.response.headers);
    
    // Handle JWT token expiration
    if (error.response.status === 401 && jwtToken) {
      console.log('JWT token expired, clearing token');
      api.clearJwtToken();
    }
  } else if (error.request) {
    console.error('No response received:', error.request);
    console.error('🔍 Network debugging info:', {
      platform: Platform.OS,
      baseURL: api.defaults.baseURL,
      url: error.config?.url,
      timeout: error.config?.timeout,
    });
    
    // Provide helpful debugging information
    if (Platform.OS === 'android') {
      console.log('💡 Android Emulator Tip: Make sure you\'re using ******** instead of localhost');
    } else if (Platform.OS === 'ios') {
      console.log('💡 iOS Simulator Tip: If localhost doesn\'t work, try using your computer\'s IP address');
    }
  } else {
    console.error('Error message:', error.message);
  }
  throw error;
};

// Add a response interceptor for handling responses and errors
api.interceptors.response.use(
  response => {
    console.log('✅ API Response:', {
      status: response.status,
      url: response.config.url,
      dataType: typeof response.data,
      dataSize: JSON.stringify(response.data).length,
    });
    return response;
  },
  async error => {
    console.error('❌ Response Error:', {
      status: error.response?.status,
      statusText: error.response?.statusText,
      url: error.config?.url,
      data: error.response?.data,
      message: error.message,
    });
    
    // Handle 401 errors by clearing JWT token
    if (error.response?.status === 401 && jwtToken) {
      await api.clearJwtToken();
    }
    
    return Promise.reject(error);
  },
);

// JWT token management functions
api.setJwtToken = async (token: string) => {
  jwtToken = token;
  await AsyncStorage.setItem('jwt_token', token);
  console.log('JWT token set successfully');
};

api.clearJwtToken = async () => {
  jwtToken = null;
  await AsyncStorage.removeItem('jwt_token');
  console.log('JWT token cleared');
};

// API switching functions
api.switchToNewApi = () => {
  api.defaults.baseURL = BASE_URLS.WATER_METER_API;
  console.log('Switched to new Water Meter Management API:', BASE_URLS.WATER_METER_API);
};

api.switchToLegacyApi = () => {
  api.defaults.baseURL = BASE_URLS.SICON_API;
  console.log('Switched to legacy SICON API');
};

// Add function to update base URL dynamically
api.updateBaseUrl = (newUrl: string) => {
  api.defaults.baseURL = newUrl;
  BASE_URLS.WATER_METER_API = newUrl;
  console.log('📡 Updated base URL to:', newUrl);
};

// Add handleApiError to the api object
api.handleApiError = handleApiError;

// Initialize token on app start
(async () => {
  try {
    const storedToken = await AsyncStorage.getItem('jwt_token');
    if (storedToken) {
      jwtToken = storedToken;
      console.log('JWT token loaded from storage');
    }
  } catch (error) {
    console.error('Error loading JWT token:', error);
  }
})();

// Export the configured axios instance
export default api; 