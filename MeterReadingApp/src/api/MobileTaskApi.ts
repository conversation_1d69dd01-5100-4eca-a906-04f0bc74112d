import api from './BaseApi';
import { MobileTaskDto, MobileTaskDetailDto, MobileTaskSummaryDto, StartTaskRequest, UpdateTaskStatusRequest, CompleteTaskRequest, TaskStatusSyncRequest, TaskSyncResponseDto } from '../types/mobile';

class MobileTaskApi {
  private readonly baseUrl = '/mobile/tasks';

  /**
   * Get current user's assigned tasks
   */
  async getMyAssignments(
    status?: string,
    includeCompleted: boolean = false,
    page: number = 1,
    pageSize: number = 50
  ): Promise<MobileTaskDto[]> {
    try {
      const params = new URLSearchParams();
      if (status) params.append('status', status);
      if (includeCompleted) params.append('includeCompleted', 'true');
      params.append('page', page.toString());
      params.append('pageSize', pageSize.toString());

      const response = await api.get(`${this.baseUrl}/my-assignments?${params.toString()}`);
      return response.data;
    } catch (error) {
      api.handleApiError(error, 'getMyAssignments');
      throw error;
    }
  }

  /**
   * Get detailed task information
   */
  async getTaskDetails(taskId: number): Promise<MobileTaskDetailDto> {
    try {
      const response = await api.get(`${this.baseUrl}/${taskId}/details`);
      return response.data;
    } catch (error) {
      api.handleApiError(error, 'getTaskDetails');
    }
  }

  /**
   * Start working on a task
   */
  async startTask(taskId: number, request: StartTaskRequest): Promise<{ message: string; taskId: number }> {
    try {
      const response = await api.put(`${this.baseUrl}/${taskId}/start`, request);
      return response.data;
    } catch (error) {
      api.handleApiError(error, 'startTask');
    }
  }

  /**
   * Update task status
   */
  async updateTaskStatus(taskId: number, request: UpdateTaskStatusRequest): Promise<{ message: string; taskId: number; newStatus: string }> {
    try {
      const response = await api.put(`${this.baseUrl}/${taskId}/status`, request);
      return response.data;
    } catch (error) {
      api.handleApiError(error, 'updateTaskStatus');
    }
  }

  /**
   * Complete a task
   */
  async completeTask(taskId: number, request: CompleteTaskRequest): Promise<{ message: string; taskId: number; readingId: number }> {
    try {
      const response = await api.put(`${this.baseUrl}/${taskId}/complete`, request);
      return response.data;
    } catch (error) {
      api.handleApiError(error, 'completeTask');
    }
  }

  /**
   * Accept an assignment
   */
  async acceptAssignment(taskId: number): Promise<{ message: string; taskId: number }> {
    try {
      const response = await api.post(`${this.baseUrl}/${taskId}/accept`);
      return response.data;
    } catch (error) {
      api.handleApiError(error, 'acceptAssignment');
    }
  }

  /**
   * Reject an assignment
   */
  async rejectAssignment(taskId: number, reason: string): Promise<{ message: string; taskId: number }> {
    try {
      const response = await api.post(`${this.baseUrl}/${taskId}/reject`, { reason });
      return response.data;
    } catch (error) {
      api.handleApiError(error, 'rejectAssignment');
    }
  }

  /**
   * Get tasks near current location
   */
  async getNearbyTasks(
    latitude: number,
    longitude: number,
    radiusKm: number = 5.0
  ): Promise<MobileTaskDto[]> {
    try {
      const params = new URLSearchParams({
        latitude: latitude.toString(),
        longitude: longitude.toString(),
        radiusKm: radiusKm.toString(),
      });

      const response = await api.get(`${this.baseUrl}/nearby?${params.toString()}`);
      return response.data;
    } catch (error) {
      api.handleApiError(error, 'getNearbyTasks');
      throw error;
    }
  }

  /**
   * Get task summary for mobile dashboard
   */
  async getTaskSummary(): Promise<MobileTaskSummaryDto> {
    try {
      const response = await api.get(`${this.baseUrl}/summary`);
      return response.data;
    } catch (error) {
      api.handleApiError(error, 'getTaskSummary');
      throw error;
    }
  }

  /**
   * Sync offline task status changes
   */
  async syncTaskStatus(request: TaskStatusSyncRequest): Promise<TaskSyncResponseDto> {
    try {
      const response = await api.post(`${this.baseUrl}/sync-status`, request);
      return response.data;
    } catch (error) {
      api.handleApiError(error, 'syncTaskStatus');
    }
  }

  /**
   * Get tasks by status with caching support
   */
  async getTasksByStatus(status: string[], useCache: boolean = true): Promise<MobileTaskDto[]> {
    try {
      const cacheKey = `tasks_${status.join('_')}`;
      
      if (useCache) {
        // Try to get from cache first (implementation depends on your caching strategy)
        const cachedTasks = await this.getCachedTasks(cacheKey);
        if (cachedTasks) {
          return cachedTasks;
        }
      }

      const tasks: MobileTaskDto[] = [];
      for (const taskStatus of status) {
        const statusTasks = await this.getMyAssignments(taskStatus, false, 1, 100);
        tasks.push(...statusTasks);
      }

      // Cache the results
      if (useCache) {
        await this.setCachedTasks(cacheKey, tasks);
      }

      return tasks;
    } catch (error) {
      api.handleApiError(error, 'getTasksByStatus');
    }
  }

  /**
   * Get urgent tasks
   */
  async getUrgentTasks(): Promise<MobileTaskDto[]> {
    try {
      const allTasks = await this.getMyAssignments(undefined, false, 1, 100);
      return allTasks.filter(task => task.isUrgent || task.isOverdue);
    } catch (error) {
      api.handleApiError(error, 'getUrgentTasks');
      throw error;
    }
  }

  /**
   * Search tasks by criteria
   */
  async searchTasks(query: string, filters?: {
    status?: string[];
    priority?: string[];
    dueDate?: { from?: Date; to?: Date };
    location?: { latitude: number; longitude: number; radius: number };
  }): Promise<MobileTaskDto[]> {
    try {
      // This is a client-side implementation since the API doesn't have a specific search endpoint
      let tasks = await this.getMyAssignments(undefined, true, 1, 500);

      // Apply query filter
      if (query) {
        const lowerQuery = query.toLowerCase();
        tasks = tasks.filter(task =>
          task.name.toLowerCase().includes(lowerQuery) ||
          task.description?.toLowerCase().includes(lowerQuery) ||
          task.meterNumber?.toLowerCase().includes(lowerQuery) ||
          task.address?.toLowerCase().includes(lowerQuery)
        );
      }

      // Apply additional filters
      if (filters) {
        if (filters.status && filters.status.length > 0) {
          tasks = tasks.filter(task => filters.status!.includes(task.status));
        }

        if (filters.priority && filters.priority.length > 0) {
          tasks = tasks.filter(task => filters.priority!.includes(task.priority));
        }

        if (filters.dueDate) {
          tasks = tasks.filter(task => {
            if (!task.dueDate) return false;
            const dueDate = new Date(task.dueDate);
            if (filters.dueDate!.from && dueDate < filters.dueDate!.from) return false;
            if (filters.dueDate!.to && dueDate > filters.dueDate!.to) return false;
            return true;
          });
        }

        if (filters.location && filters.location.latitude && filters.location.longitude) {
          // Filter by location radius (this would be more efficient on the server)
          tasks = tasks.filter(task => {
            if (!task.latitude || !task.longitude) return false;
                          const distance = this.calculateDistance(
                filters.location!.latitude,
                filters.location!.longitude,
                task.latitude,
                task.longitude
            );
            return distance <= filters.location!.radius;
          });
        }
      }

      return tasks;
    } catch (error) {
      api.handleApiError(error, 'searchTasks');
    }
  }

  // Private helper methods
  private async getCachedTasks(key: string): Promise<MobileTaskDto[] | null> {
    // Implement your caching logic here (AsyncStorage, SQLite, etc.)
    return null;
  }

  private async setCachedTasks(key: string, tasks: MobileTaskDto[]): Promise<void> {
    // Implement your caching logic here
  }

  private calculateDistance(lat1: number, lng1: number, lat2: number, lng2: number): number {
    const R = 6371; // Radius of the Earth in km
    const dLat = this.deg2rad(lat2 - lat1);
    const dLng = this.deg2rad(lng2 - lng1);
    const a =
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos(this.deg2rad(lat1)) * Math.cos(this.deg2rad(lat2)) *
      Math.sin(dLng / 2) * Math.sin(dLng / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    return R * c;
  }

  private deg2rad(deg: number): number {
    return deg * (Math.PI / 180);
  }
}

export default new MobileTaskApi(); 