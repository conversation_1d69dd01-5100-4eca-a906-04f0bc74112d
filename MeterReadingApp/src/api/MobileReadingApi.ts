import api from './BaseApi';
import { generateSecureId } from '../utils/secureRandom';
import AuthService from '../services/AuthService';
import {
  MobileReadingDto,
  ReadingResponseDto,
  ReadingValidationDto,
  BatchReadingRequest,
  BatchReadingResponseDto,
  CompleteTaskRequest,
  UpdateReadingRequest,
  MobileReadingHistoryDto,
  MobileReadingStatsDto,
  OfflineReadingSyncRequest,
  OfflineReadingSyncResponse,
  MobilePhotoDto,
  PhotoUploadResult
} from '../types/mobile';

class MobileReadingApi {
  private readonly baseUrl = '/mobile/readings';

  /**
   * Submit a single meter reading
   */
  async submitReading(reading: MobileReadingDto): Promise<ReadingResponseDto> {
    try {
      const response = await api.post(this.baseUrl, reading);
      return response.data;
    } catch (error) {
      api.handleApiError(error, 'submitReading');
    }
  }

  /**
   * Submit multiple readings in batch (for offline sync)
   */
  async submitBatchReadings(request: BatchReadingRequest): Promise<BatchReadingResponseDto> {
    try {
      const response = await api.post(`${this.baseUrl}/batch`, request);
      return response.data;
    } catch (error) {
      api.handleApiError(error, 'submitBatchReadings');
    }
  }

  /**
   * Validate a reading before submission
   */
  async validateReading(reading: MobileReadingDto): Promise<ReadingValidationDto> {
    try {
      const response = await api.post(`${this.baseUrl}/validate`, reading);
      return response.data;
    } catch (error) {
      api.handleApiError(error, 'validateReading');
    }
  }

  /**
   * Upload photos for a reading
   */
  async uploadPhotos(readingId: number, photos: MobilePhotoDto[]): Promise<PhotoUploadResult[]> {
    try {
      const response = await api.post(`${this.baseUrl}/${readingId}/photos`, photos);
      return response.data;
    } catch (error) {
      api.handleApiError(error, 'uploadPhotos');
    }
  }

  /**
   * Get reading history for a meter
   */
  async getReadingHistory(meterId: number, limit: number = 10): Promise<MobileReadingHistoryDto[]> {
    try {
      const response = await api.get(`${this.baseUrl}/history/${meterId}?limit=${limit}`);
      return response.data;
    } catch (error) {
      api.handleApiError(error, 'getReadingHistory');
    }
  }

  /**
   * Get recent readings by current user
   */
  async getMyRecentReadings(limit: number = 20, page: number = 1): Promise<MobileReadingHistoryDto[]> {
    try {
      const response = await api.get(`${this.baseUrl}/my-recent?limit=${limit}&page=${page}`);
      return response.data;
    } catch (error) {
      api.handleApiError(error, 'getMyRecentReadings');
    }
  }

  /**
   * Update reading with additional information
   */
  async updateReading(readingId: number, reading: MobileReadingDto): Promise<ReadingResponseDto> {
    try {
      const response = await api.put(`${this.baseUrl}/${readingId}`, reading);
      return response.data;
    } catch (error) {
      api.handleApiError(error, 'updateReading');
    }
  }

  /**
   * Delete a reading (soft delete)
   */
  async deleteReading(readingId: number): Promise<{ message: string; readingId: number }> {
    try {
      const response = await api.delete(`${this.baseUrl}/${readingId}`);
      return response.data;
    } catch (error) {
      api.handleApiError(error, 'deleteReading');
    }
  }

  /**
   * Submit reading and complete task in one operation
   */
  async submitReadingAndCompleteTask(request: CompleteTaskRequest): Promise<ReadingResponseDto> {
    try {
      const response = await api.post(`${this.baseUrl}/submit-and-complete`, request);
      return response.data;
    } catch (error) {
      api.handleApiError(error, 'submitReadingAndCompleteTask');
    }
  }

  /**
   * Get reading statistics for current user
   */
  async getMyReadingStats(): Promise<MobileReadingStatsDto> {
    try {
      const response = await api.get(`${this.baseUrl}/my-stats`);
      return response.data;
    } catch (error) {
      api.handleApiError(error, 'getMyReadingStats');
    }
  }

  /**
   * Sync offline readings
   */
  async syncOfflineReadings(request: OfflineReadingSyncRequest): Promise<OfflineReadingSyncResponse> {
    try {
      const response = await api.post(`${this.baseUrl}/sync-offline`, request);
      return response.data;
    } catch (error) {
      api.handleApiError(error, 'syncOfflineReadings');
    }
  }

  /**
   * Quick reading submission with minimal data
   */
  async quickSubmitReading(
    taskId: number,
    meterId: number,
    readingValue: number,
    notes?: string,
    photos?: MobilePhotoDto[]
  ): Promise<ReadingResponseDto> {
    try {
      const reading: MobileReadingDto = {
        // 必填字段
        taskId: taskId,
        meterId: meterId,
        userId: await this.getCurrentUserId(),
        readingValue: readingValue,
        readingDate: new Date(),
        readingMethod: 'Manual',
        readingType: 'Regular',
        dataSource: 'Mobile',
        status: 'Completed',

        // OCR相关
        hasOCR: false,

        // 验证信息
        isValidated: false,

        // 异常检测
        isAnomalous: false,
        cantRead: false,

        // 备注
        notes: notes,

        // 移动端专用字段
        photos: photos || [],
        isOfflineReading: false,
      };

      return await this.submitReading(reading);
    } catch (error) {
      api.handleApiError(error, 'quickSubmitReading');
    }
  }

  /**
   * Submit reading with OCR data
   */
  async submitOcrReading(
    taskId: number,
    meterId: number,
    readingValue: number,
    ocrConfidence: number,
    photo: MobilePhotoDto,
    notes?: string
  ): Promise<ReadingResponseDto> {
    try {
      const reading: MobileReadingDto = {
        // 必填字段
        taskId: taskId,
        meterId: meterId,
        userId: await this.getCurrentUserId(),
        readingValue: readingValue,
        readingDate: new Date(),
        readingMethod: 'OCR',
        readingType: 'Regular',
        dataSource: 'Mobile',
        status: 'Completed',

        // OCR相关
        hasOCR: true,
        ocrStatus: 'Success',
        ocrConfidence: ocrConfidence,

        // 验证信息
        isValidated: false,

        // 异常检测
        isAnomalous: false,
        cantRead: false,

        // 备注
        notes: notes,

        // 移动端专用字段
        photos: [photo],
        isOfflineReading: false,
      };

      return await this.submitReading(reading);
    } catch (error) {
      api.handleApiError(error, 'submitOcrReading');
    }
  }

  /**
   * Prepare reading for offline storage
   */
  async prepareOfflineReading(
    taskId: number,
    meterId: number,
    readingValue: number,
    method: 'Manual' | 'OCR' = 'Manual',
    notes?: string,
    photos?: MobilePhotoDto[]
  ): Promise<MobileReadingDto> {
    return {
      // 必填字段
      taskId: taskId,
      meterId: meterId,
      userId: await this.getCurrentUserId(),
      readingValue: readingValue,
      readingDate: new Date(),
      readingMethod: method,
      readingType: 'Regular',
      dataSource: 'Mobile',
      status: 'Completed',

      // OCR相关
      hasOCR: method === 'OCR',

      // 验证信息
      isValidated: false,

      // 异常检测
      isAnomalous: false,
      cantRead: false,

      // 备注
      notes: notes,

      // 移动端专用字段
      photos: photos || [],
      isOfflineReading: true,
      offlineId: this.generateOfflineId(),
    };
  }

  /**
   * Validate reading data before submission
   */
  async validateReadingData(reading: MobileReadingDto): Promise<{
    isValid: boolean;
    errors: string[];
    warnings: string[];
  }> {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Basic validation
    if (!reading.taskId || reading.taskId <= 0) {
      errors.push('Valid Task ID is required');
    }

    if (!reading.meterId || reading.meterId <= 0) {
      errors.push('Valid Meter ID is required');
    }

    if (reading.readingValue < 0) {
      errors.push('Reading value cannot be negative');
    }

    if (!reading.readingDate) {
      errors.push('Reading date is required');
    }

    // Warnings for data quality
    if (reading.ocrConfidence && reading.ocrConfidence < 50) {
      warnings.push('Low OCR confidence - consider retaking photo');
    }

    if (!reading.photos || reading.photos.length === 0) {
      warnings.push('No photos attached - photos are recommended for verification');
    }

    if (reading.readingMethod === 'OCR' && (!reading.ocrConfidence || reading.ocrConfidence < 70)) {
      warnings.push('OCR confidence is low - manual verification recommended');
    }

    // Server-side validation if basic validation passes
    if (errors.length === 0) {
      try {
        const serverValidation = await this.validateReading(reading);
        errors.push(...serverValidation.Errors);
        warnings.push(...serverValidation.Warnings);
      } catch (error) {
        warnings.push('Could not perform server-side validation - will be validated on submission');
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
    };
  }

  /**
   * Get reading statistics summary
   */
  async getReadingSummary(): Promise<{
    todayCount: number;
    weekCount: number;
    monthCount: number;
    averageQuality: number;
    lastReading?: MobileReadingHistoryDto;
  }> {
    try {
      const stats = await this.getMyReadingStats();
      const recent = await this.getMyRecentReadings(1);

      return {
        todayCount: stats.readingsThisWeek, // Assuming this includes today
        weekCount: stats.readingsThisWeek,
        monthCount: stats.readingsThisMonth,
        averageQuality: stats.averageQualityScore,
        lastReading: recent.length > 0 ? recent[0] : undefined,
      };
    } catch (error) {
      api.handleApiError(error, 'getReadingSummary');
    }
  }

  // Private helper methods
  private generateOfflineId(): string {
    return generateSecureId('offline');
  }

  private async getCurrentUserId(): Promise<number> {
    try {
      const currentUser = await AuthService.getCurrentUser();

      if (!currentUser || !currentUser.id) {
        throw new Error('No authenticated user found');
      }

      return currentUser.id;
    } catch (error) {
      console.error('MobileReadingApi: Error getting current user ID:', error);
      throw new Error('Unable to get current user ID. Please ensure you are logged in.');
    }
  }
}

export default new MobileReadingApi(); 