import DatabaseManager from './DatabaseManager';

interface MigrationResult {
  success: boolean;
  migratedRecords: number;
  errorMessage?: string;
  tableName: string;
}

export class DataMigrationService {
  private dbManager: typeof DatabaseManager;

  constructor() {
    this.dbManager = DatabaseManager;
  }

  // Check if old tables exist
  private async checkOldTablesExist(): Promise<{[key: string]: boolean}> {
    try {
      const oldTables = ['meter_readings', 'tasks', 'meter_types', 'areas'];
      const existingTables: {[key: string]: boolean} = {};

      for (const tableName of oldTables) {
        try {
          await this.dbManager.executeSql(`SELECT COUNT(*) FROM ${tableName} LIMIT 1`);
          existingTables[tableName] = true;
          console.log(`Old table ${tableName} exists`);
        } catch (error) {
          existingTables[tableName] = false;
          console.log(`Old table ${tableName} does not exist`);
        }
      }

      return existingTables;
    } catch (error) {
      console.error('Error checking old tables:', error);
      return {};
    }
  }

  // Migrate users data from old schema to new schema
  private async migrateUsersData(): Promise<MigrationResult> {
    const tableName = 'users';
    
    try {
      // Check if old users table has incompatible structure
      const result = await this.dbManager.executeSql(
        `SELECT id, username, full_name, email, phone_number, role, is_active, 
         last_login_date, created_at, updated_at FROM users LIMIT 1`
      );

      if (result.rows.length === 0) {
        return {
          success: true,
          migratedRecords: 0,
          tableName,
        };
      }

      // Get all users from old structure
      const oldUsers = await this.dbManager.executeSql(
        `SELECT id, username, full_name, email, phone_number, role, is_active, 
         last_login_date, created_at, updated_at FROM users`
      );

      // Backup old data
      await this.dbManager.executeSql(`DROP TABLE IF EXISTS users_backup`);
      await this.dbManager.executeSql(
        `CREATE TABLE users_backup AS SELECT * FROM users`
      );

      // Drop old table and recreate with new structure
      await this.dbManager.executeSql(`DROP TABLE users`);
      
      // New table will be created by createAlignedTables()
      
      let migratedCount = 0;

      // Migrate each user record to new structure
      for (let i = 0; i < oldUsers.rows.length; i++) {
        const oldUser = oldUsers.rows.item(i);
        
        try {
          await this.dbManager.executeSql(
            `INSERT INTO users (
              id, username, full_name, email, mobile_phone, 
              is_authenticated, last_login, created_date, updated_date,
              created_at, updated_at, is_deleted, created_by, updated_by
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
            [
              oldUser.id,
              oldUser.username,
              oldUser.full_name || '',
              oldUser.email || '',
              oldUser.phone_number || '',
              oldUser.is_active || 0,
              oldUser.last_login_date,
              oldUser.created_at,
              oldUser.updated_at,
              oldUser.created_at,
              oldUser.updated_at,
              0, // is_deleted
              'migration',
              'migration'
            ]
          );
          migratedCount++;
        } catch (error) {
          console.error(`Error migrating user ${oldUser.id}:`, error);
        }
      }

      console.log(`Migrated ${migratedCount} users successfully`);
      
      return {
        success: true,
        migratedRecords: migratedCount,
        tableName,
      };

    } catch (error) {
      console.error('Error migrating users data:', error);
      return {
        success: false,
        migratedRecords: 0,
        errorMessage: error instanceof Error ? error.message : 'Unknown error',
        tableName,
      };
    }
  }

  // Migrate water meters data
  private async migrateWaterMetersData(): Promise<MigrationResult> {
    const tableName = 'water_meters';
    
    try {
      // Check if old water_meters table exists
      const result = await this.dbManager.executeSql(
        `SELECT id, meter_number, customer_name, customer_phone, address,
         latitude, longitude, last_reading, last_reading_date, status,
         created_at, updated_at FROM water_meters LIMIT 1`
      );

      if (result.rows.length === 0) {
        return {
          success: true,
          migratedRecords: 0,
          tableName,
        };
      }

      // Get all water meters from old structure
      const oldMeters = await this.dbManager.executeSql(
        `SELECT id, meter_number, customer_name, customer_phone, address,
         latitude, longitude, last_reading, last_reading_date, status,
         created_at, updated_at FROM water_meters`
      );

      // Backup old data
      await this.dbManager.executeSql(`DROP TABLE IF EXISTS water_meters_backup`);
      await this.dbManager.executeSql(
        `CREATE TABLE water_meters_backup AS SELECT * FROM water_meters`
      );

      // Drop old table
      await this.dbManager.executeSql(`DROP TABLE water_meters`);

      let migratedCount = 0;

      // Migrate each meter record to new structure
      for (let i = 0; i < oldMeters.rows.length; i++) {
        const oldMeter = oldMeters.rows.item(i);
        
        try {
          await this.dbManager.executeSql(
            `INSERT INTO water_meters (
              id, serial_number, location, address, latitude, longitude,
              meter_type, status, customer_name, last_reading, last_reading_date,
              sync_status, created_at, updated_at, is_deleted, created_by, updated_by
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
            [
              oldMeter.id,
              oldMeter.meter_number, // Map meter_number to serial_number
              oldMeter.address || '', // Use address as location
              oldMeter.address || '',
              oldMeter.latitude,
              oldMeter.longitude,
              'Water', // Default meter type
              oldMeter.status || 'Active',
              oldMeter.customer_name || '',
              oldMeter.last_reading,
              oldMeter.last_reading_date,
              'Synced',
              oldMeter.created_at,
              oldMeter.updated_at,
              0, // is_deleted
              'migration',
              'migration'
            ]
          );
          migratedCount++;
        } catch (error) {
          console.error(`Error migrating water meter ${oldMeter.id}:`, error);
        }
      }

      console.log(`Migrated ${migratedCount} water meters successfully`);
      
      return {
        success: true,
        migratedRecords: migratedCount,
        tableName,
      };

    } catch (error) {
      console.error('Error migrating water meters data:', error);
      return {
        success: false,
        migratedRecords: 0,
        errorMessage: error instanceof Error ? error.message : 'Unknown error',
        tableName,
      };
    }
  }

  // Migrate tasks data to work_tasks
  private async migrateTasksData(): Promise<MigrationResult> {
    const tableName = 'work_tasks';
    
    try {
      // Check if old tasks table exists
      const result = await this.dbManager.executeSql(
        `SELECT id, assigned_user_id, meter_id, task_type, status, priority,
         scheduled_date, completed_date, notes, created_at, updated_at 
         FROM tasks LIMIT 1`
      );

      if (result.rows.length === 0) {
        return {
          success: true,
          migratedRecords: 0,
          tableName,
        };
      }

      // Get all tasks from old structure
      const oldTasks = await this.dbManager.executeSql(
        `SELECT id, assigned_user_id, meter_id, task_type, status, priority,
         scheduled_date, completed_date, notes, created_at, updated_at 
         FROM tasks`
      );

      // Backup old data
      await this.dbManager.executeSql(`DROP TABLE IF EXISTS tasks_backup`);
      await this.dbManager.executeSql(
        `CREATE TABLE tasks_backup AS SELECT * FROM tasks`
      );

      // Drop old table
      await this.dbManager.executeSql(`DROP TABLE tasks`);

      let migratedCount = 0;

      // Migrate each task record to new structure
      for (let i = 0; i < oldTasks.rows.length; i++) {
        const oldTask = oldTasks.rows.item(i);
        
        try {
          await this.dbManager.executeSql(
            `INSERT INTO work_tasks (
              id, name, description, status, priority, type, assigned_user_id, meter_id,
              due_date, scheduled_date, completed_date, notes,
              created_at, updated_at, is_deleted, created_by, updated_by
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
            [
              oldTask.id,
              `Task ${oldTask.id}`, // Generate task name
              `Migrated task of type ${oldTask.task_type}`,
              oldTask.status || 'Pending',
              oldTask.priority || 'Medium',
              oldTask.task_type || 'MeterReading',
              oldTask.assigned_user_id,
              oldTask.meter_id,
              oldTask.scheduled_date, // Use scheduled_date as due_date
              oldTask.scheduled_date,
              oldTask.completed_date,
              oldTask.notes || '',
              oldTask.created_at,
              oldTask.updated_at,
              0, // is_deleted
              'migration',
              'migration'
            ]
          );
          migratedCount++;
        } catch (error) {
          console.error(`Error migrating task ${oldTask.id}:`, error);
        }
      }

      console.log(`Migrated ${migratedCount} tasks successfully`);
      
      return {
        success: true,
        migratedRecords: migratedCount,
        tableName,
      };

    } catch (error) {
      console.error('Error migrating tasks data:', error);
      return {
        success: false,
        migratedRecords: 0,
        errorMessage: error instanceof Error ? error.message : 'Unknown error',
        tableName,
      };
    }
  }

  // Migrate meter readings data
  private async migrateMeterReadingsData(): Promise<MigrationResult> {
    const tableName = 'readings';
    
    try {
      // Check if old meter_readings table exists
      const result = await this.dbManager.getInstance().executeSql(
        `SELECT id, meter_id, reading_value, reading_date, read_by,
         latitude, longitude, gps_accuracy, photo_filename, ocr_confidence,
         reading_type, created_at, updated_at FROM meter_readings LIMIT 1`
      );

      if (result.rows.length === 0) {
        return {
          success: true,
          migratedRecords: 0,
          tableName,
        };
      }

      // Get all readings from old structure
      const oldReadings = await this.dbManager.getInstance().executeSql(
        `SELECT id, meter_id, reading_value, reading_date, read_by,
         latitude, longitude, gps_accuracy, photo_filename, ocr_confidence,
         reading_type, created_at, updated_at FROM meter_readings`
      );

      // Backup old data
      await this.dbManager.executeSql(`DROP TABLE IF EXISTS meter_readings_backup`);
      await this.dbManager.executeSql(
        `CREATE TABLE meter_readings_backup AS SELECT * FROM meter_readings`
      );

      // Drop old table
      await this.dbManager.executeSql(`DROP TABLE meter_readings`);

      let migratedCount = 0;

      // Migrate each reading record to new structure
      for (let i = 0; i < oldReadings.rows.length; i++) {
        const oldReading = oldReadings.rows.item(i);
        
        try {
          // Try to find user ID based on read_by field
          let userId = 1; // Default user ID
          if (oldReading.read_by) {
            const userResult = await this.dbManager.executeSql(
              `SELECT id FROM users WHERE username = ? LIMIT 1`,
              [oldReading.read_by]
            );
            if (userResult.rows.length > 0) {
              userId = userResult.rows.item(0).id;
            }
          }

          await this.dbManager.executeSql(
            `INSERT INTO readings (
              id, meter_id, user_id, reading_value, reading_date,
              reading_method, ocr_confidence, latitude, longitude, gps_accuracy,
              photo_filename, created_at, updated_at, is_deleted, created_by, updated_by
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
            [
              oldReading.id,
              oldReading.meter_id,
              userId,
              oldReading.reading_value,
              oldReading.reading_date,
              oldReading.reading_type || 'Manual',
              oldReading.ocr_confidence,
              oldReading.latitude,
              oldReading.longitude,
              oldReading.gps_accuracy,
              oldReading.photo_filename,
              oldReading.created_at,
              oldReading.updated_at,
              0, // is_deleted
              'migration',
              'migration'
            ]
          );
          migratedCount++;
        } catch (error) {
          console.error(`Error migrating reading ${oldReading.id}:`, error);
        }
      }

      console.log(`Migrated ${migratedCount} readings successfully`);
      
      return {
        success: true,
        migratedRecords: migratedCount,
        tableName,
      };

    } catch (error) {
      console.error('Error migrating readings data:', error);
      return {
        success: false,
        migratedRecords: 0,
        errorMessage: error instanceof Error ? error.message : 'Unknown error',
        tableName,
      };
    }
  }

  // Main migration method
  async performDataMigration(): Promise<MigrationResult[]> {
    console.log('Starting data migration from old schema to new aligned schema...');
    
    const results: MigrationResult[] = [];
    
    try {
      // Check which old tables exist
      const oldTablesExist = await this.checkOldTablesExist();
      
      // Migrate users first (needed for foreign keys)
      if (oldTablesExist.users !== false) {
        const usersResult = await this.migrateUsersData();
        results.push(usersResult);
      }

      // Migrate water meters (needed for foreign keys)
      if (oldTablesExist.water_meters !== false) {
        const metersResult = await this.migrateWaterMetersData();
        results.push(metersResult);
      }

      // Migrate tasks
      if (oldTablesExist.tasks !== false) {
        const tasksResult = await this.migrateTasksData();
        results.push(tasksResult);
      }

      // Migrate readings
      if (oldTablesExist.meter_readings !== false) {
        const readingsResult = await this.migrateMeterReadingsData();
        results.push(readingsResult);
      }

      console.log('Data migration completed successfully');
      
      // Log summary
      const totalMigrated = results.reduce((sum, result) => sum + result.migratedRecords, 0);
      const successfulMigrations = results.filter(result => result.success).length;
      
      console.log(`Migration summary: ${successfulMigrations}/${results.length} tables migrated successfully`);
      console.log(`Total records migrated: ${totalMigrated}`);
      
      return results;

    } catch (error) {
      console.error('Critical error during data migration:', error);
      
      results.push({
        success: false,
        migratedRecords: 0,
        errorMessage: error instanceof Error ? error.message : 'Unknown error',
        tableName: 'migration_process'
      });
      
      return results;
    }
  }

  // Clean up backup tables after successful migration
  async cleanupBackupTables(): Promise<void> {
    const backupTables = [
      'users_backup',
      'water_meters_backup', 
      'tasks_backup',
      'meter_readings_backup'
    ];

    try {
      for (const tableName of backupTables) {
        await this.dbManager.executeSql(`DROP TABLE IF EXISTS ${tableName}`);
        console.log(`Cleaned up backup table: ${tableName}`);
      }
      console.log('All backup tables cleaned up successfully');
    } catch (error) {
      console.error('Error cleaning up backup tables:', error);
    }
  }

  // Restore from backup if migration fails
  async restoreFromBackup(): Promise<void> {
    const backupTables = [
      { backup: 'users_backup', original: 'users' },
      { backup: 'water_meters_backup', original: 'water_meters' },
      { backup: 'tasks_backup', original: 'tasks' },
      { backup: 'meter_readings_backup', original: 'meter_readings' }
    ];

    try {
      for (const { backup, original } of backupTables) {
        // Check if backup exists
        try {
          await this.dbManager.executeSql(`SELECT COUNT(*) FROM ${backup} LIMIT 1`);
          
          // Drop current table and restore from backup
          await this.dbManager.executeSql(`DROP TABLE IF EXISTS ${original}`);
          await this.dbManager.executeSql(`ALTER TABLE ${backup} RENAME TO ${original}`);
          
          console.log(`Restored ${original} from ${backup}`);
        } catch (error) {
          console.log(`Backup table ${backup} does not exist, skipping restore`);
        }
      }
      console.log('Database restored from backup successfully');
    } catch (error) {
      console.error('Error restoring from backup:', error);
      throw error;
    }
  }
} 