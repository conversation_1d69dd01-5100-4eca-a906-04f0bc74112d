/**
 * Baseline Record model for mobile application
 * 移动端基线记录模型
 */

export interface BaselineRecord {
  id: number;
  meter_id: number;
  baseline_date: string;
  baseline_value: number;
  baseline_type: string;
  status: string;
  import_batch?: string;
  source_file?: string;
  data_source: string;
  validation_notes?: string;
  is_validated: number;
  validated_date?: string | null;
  validated_by?: string;
  has_validation_errors: number;
  validation_errors?: string;
  is_anomalous: number;
  anomaly_description?: string;
  previous_baseline_id?: number | null;
  previous_baseline_value?: number | null;
  variance_from_previous?: number | null;
  variance_percentage?: number | null;
  is_corrected: number;
  corrected_date?: string | null;
  corrected_by?: string;
  correction_reason?: string;
  confidence_level: number;
  notes?: string;
  created_at: string;
  updated_at: string;
  created_by: string;
  updated_by: string;
  is_deleted: number;
  sync_status: string;
  last_sync_date?: string;
}

// Backend API response format (camelCase)
export interface BackendBaselineData {
  id: number;
  meterId: number;
  meterSerialNumber?: string;
  baselineDate: string;
  baselineValue: number;
  baselineType: string;
  status: string;
  importBatch?: string;
  sourceFile?: string;
  dataSource: string;
  validationNotes?: string;
  isValidated: boolean;
  validatedDate?: string | null;
  validatedBy?: string;
  hasValidationErrors: boolean;
  validationErrors?: string;
  isAnomalous: boolean;
  anomalyDescription?: string;
  previousBaselineId?: number | null;
  previousBaselineValue?: number | null;
  varianceFromPrevious?: number | null;
  variancePercentage?: number | null;
  isCorrected: boolean;
  correctedDate?: string | null;
  correctedBy?: string;
  correctionReason?: string;
  confidenceLevel: number;
  notes?: string;
  createdAt: string;
  updatedAt: string;
  createdBy: string;
  updatedBy: string;
}

// Mobile database format (snake_case)
export interface SyncBaselineData {
  id: number;
  meter_id: number;
  baseline_date: string;
  baseline_value: number;
  baseline_type: string;
  status: string;
  import_batch?: string;
  source_file?: string;
  data_source: string;
  validation_notes?: string;
  is_validated: number;
  validated_date?: string | null;
  validated_by?: string;
  has_validation_errors: number;
  validation_errors?: string;
  is_anomalous: number;
  anomaly_description?: string;
  previous_baseline_id?: number | null;
  previous_baseline_value?: number | null;
  variance_from_previous?: number | null;
  variance_percentage?: number | null;
  is_corrected: number;
  corrected_date?: string | null;
  corrected_by?: string;
  correction_reason?: string;
  confidence_level: number;
  notes?: string;
  created_at: string;
  updated_at: string;
  created_by: string;
  updated_by: string;
  is_deleted: number;
  sync_status: string;
  last_sync_date?: string;
}

/**
 * Baseline statistics for dashboard display
 * 仪表板显示的基线统计信息
 */
export interface BaselineStatistics {
  total: number;
  active: number;
  validated: number;
  anomalous: number;
  corrected: number;
  recent: number;
}

/**
 * API response structure for baseline sync
 * 基线同步的API响应结构
 */
export interface BaselineSyncResponse {
  data: BackendBaselineData[];
  pagination: {
    page: number;
    pageSize: number;
    totalCount: number;
    totalPages: number;
    hasMore: boolean;
  };
  metadata: {
    syncTimestamp: string;
    apiVersion: string;
  };
} 