// Work Task model aligned with database schema (work_tasks table)
export interface WorkTask {
  // ============ BaseEntity fields ============
  id?: number;
  created_at?: string;
  updated_at?: string;
  is_deleted?: number;
  created_by?: string;
  updated_by?: string;

  // ============ Core task fields ============
  name: string;                           // NOT NULL
  description?: string;
  status: string;                         // NOT NULL, default 'Pending'
  priority: string;                       // NOT NULL, default 'Medium'  
  type: string;                          // NOT NULL, default 'MeterReading'
  assigned_user_id?: number;             // Foreign key to users
  meter_id?: number;                     // Foreign key to water_meters

  // ============ Time management ============
  due_date?: string;
  scheduled_date?: string;
  completed_date?: string;

  // ============ Location information ============
  location?: string;
  service_address?: string;

  // ============ Task details ============
  instructions?: string;
  notes?: string;
}

// Create request for new work task
export interface CreateWorkTaskRequest {
  name: string;
  description?: string;
  status?: string;
  priority?: string;
  type?: string;
  assigned_user_id?: number;
  meter_id?: number;
  due_date?: string;
  scheduled_date?: string;
  location?: string;
  service_address?: string;
  instructions?: string;
  notes?: string;
}

// Update request for existing work task
export interface UpdateWorkTaskRequest {
  id: number;
  name?: string;
  description?: string;
  status?: string;
  priority?: string;
  type?: string;
  assigned_user_id?: number;
  meter_id?: number;
  due_date?: string;
  scheduled_date?: string;
  completed_date?: string;
  location?: string;
  service_address?: string;
  instructions?: string;
  notes?: string;
}

// Legacy Task interface for backward compatibility
export interface Task extends WorkTask {
  // Legacy fields mapping
  server_id?: number;
  uuid?: string;
  task_type?: 'reading' | 'maintenance' | 'inspection';
  assignment_type?: string;
  assigned_by?: string;
  assignment_id?: number;
  start_date?: string;
  estimated_hours?: number;
  actual_hours?: number;
  progress_percentage?: number;
  work_package_name?: string;
  route_name?: string;
  started_at?: string;
  completed_at?: string;
  started_latitude?: number;
  started_longitude?: number;
  completed_latitude?: number;
  completed_longitude?: number;
  device_info?: string;
  sync_status?: 'synced' | 'pending' | 'error';
  
  // UI display fields
  meter_number?: string;  // For UI display, derived from water_meters.serial_number
}

export interface TaskAssignmentHistory {
  id?: number;
  task_id: number;
  user_id: number;
  assigned_by: number;
  assigned_at: string;
  unassigned_at?: string;
  reason?: string;
  notes?: string;
  sync_status?: 'synced' | 'pending' | 'error';
  created_at?: string;
  updated_at?: string;
}

export interface OfflineTask {
  id?: number;
  original_task_id?: number;
  task_data: string; // JSON string of task
  created_at: string;
  expires_at?: string;
  priority: number;
}

// TASK_STATUS constant is now in Constants.ts to avoid duplication 