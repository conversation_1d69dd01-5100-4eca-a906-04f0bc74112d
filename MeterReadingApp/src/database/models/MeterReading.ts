/**
 * 移动端水表读数模型 - 与后端MeterReading模型对齐
 * Mobile Water Meter Reading Model - Aligned with Backend MeterReading Model
 */
export interface MeterReading {
  id?: number;
  uuid: string;

  meter_id: number;
  user_id: number;
  task_id: number;

  reading_value: number;
  reading_date: string;

  reading_method: 'Manual' | 'OCR' | 'Estimated';
  reading_type: 'Regular' | 'Reactive' | 'Emergency';
  data_source: 'Mobile' | 'Web' | 'Import';

  has_ocr: boolean;
  ocr_status?: 'Success' | 'Failed' | 'LowConfidence';
  ocr_confidence?: number;

  latitude?: number;
  longitude?: number;
  gps_accuracy?: number;
  location?: string;

  status: 'Completed' | 'Pending' | 'Validated' | 'Rejected';
  validation_status?: 'Valid' | 'Invalid' | 'RequiresReview';

  is_validated: boolean;
  validated_by?: number;
  validation_date?: string;
  validation_comments?: string;

  is_anomalous: boolean;
  anomaly_reason?: string;
  anomaly_type?: string;
  cant_read: boolean;
  cant_read_reason?: string;

  notes?: string;

  has_photo: boolean;
  photo_count: number;
  photo_filenames?: string;

  device_info?: string;
  app_version?: string;

  is_offline_reading: boolean;
  offline_id?: string;
  sync_status: 'pending' | 'synced' | 'error';
  sync_error?: string;
  sync_attempts: number;

  created_at: string;
  updated_at: string;
  created_by: string;
  updated_by: string;
  is_deleted: boolean;
}

/**
 * 读数照片模型
 * Reading Photo Model
 */
export interface ReadingPhoto {
  id?: number;
  uuid: string;
  reading_id?: number;

  filename: string;
  file_path: string;
  file_size: number;
  mime_type: string;

  captured_at: string;
  latitude?: number;
  longitude?: number;
  photo_type: 'meter' | 'location' | 'issue' | 'other';

  sync_status: 'pending' | 'synced' | 'error';
  sync_error?: string;
  remote_url?: string;
  thumbnail_url?: string;
  upload_retry_count?: number;

  created_at: string;
  updated_at: string;
  is_deleted: boolean;
}

/**
 * 离线读数存储模型
 * Offline Reading Storage Model
 */
export interface OfflineReading {
  id?: number;
  reading_data: string;                 // JSON string of MeterReading
  created_at: string;
  expires_at?: string;
  priority: number;                     // 1-5, 5为最高优先级
  retry_count: number;                  // 重试次数
  last_error?: string;                  // 最后一次错误信息
}

/**
 * 读数验证结果
 * Reading Validation Result
 */
export interface ReadingValidationResult {
  is_valid: boolean;
  warnings: string[];
  errors: string[];
  anomaly_detected: boolean;
  anomaly_type?: string;
  anomaly_reason?: string;
  suggested_value?: number;
  confidence_score: number;             // 0-100
}