import { BaseEntity } from './index';

// User interface aligned with backend C# User model and local usage
export interface User extends BaseEntity {
  // Core user identity
  username: string;                    // UNIQUE NOT NULL
  full_name?: string;                  // Maps to FullName
  email?: string;                      // Maps to Email
  
  // Backend specific fields
  person_id?: number;                  // Maps to PersonId
  fin_co_code?: string;                // Maps to FinCoCode
  mobile_phone?: string;               // Maps to MobilePhone
  profit_centre_code?: string;         // Maps to ProfitCentreCode
  employee_no?: string;                // Maps to EmployeeNo
  
  // Authentication fields
  is_authenticated?: number;           // 0 or 1 (SQLite boolean)
  password_hash?: string;              // For local authentication
  is_active?: number;                  // 0 or 1 (SQLite boolean) - maps to backend active status
  role?: 'reader' | 'admin' | 'supervisor';  // User role for permissions
  
  // Timestamps (extending BaseEntity)
  last_login?: string;                 // Maps to LastLogin
  last_login_date?: string;            // Alternative field name for compatibility
  created_date?: string;               // Maps to CreatedDate
  updated_date?: string;               // Maps to UpdatedDate
  
  // Sync status
  sync_status?: 'synced' | 'pending' | 'error';
}

// Create user request interface (for new user creation)
export interface CreateUserRequest {
  id?: number;                         // Optional backend ID (for sync operations)
  username: string;
  full_name?: string;
  email?: string;
  person_id?: number;
  fin_co_code?: string;
  mobile_phone?: string;
  profit_centre_code?: string;
  employee_no?: string;
  is_authenticated?: boolean;
}

// Update user request interface (for user updates)
export interface UpdateUserRequest {
  full_name?: string;
  email?: string;
  person_id?: number;
  fin_co_code?: string;
  mobile_phone?: string;
  profit_centre_code?: string;
  employee_no?: string;
  is_authenticated?: boolean;
  last_login?: string;
}

// User statistics interface (kept for compatibility)
export interface UserStats {
  id?: number;
  user_id: number;
  total_readings?: number;
  readings_this_month?: number;
  readings_today?: number;
  accuracy_rate?: number;
  avg_time_per_reading?: number;
  total_distance_km?: number;
  sync_status?: 'synced' | 'pending' | 'error';
  created_at?: string;
  updated_at?: string;
}

export interface UserPreferences {
  id?: number;
  user_id: number;
  theme?: 'light' | 'dark' | 'auto';
  language?: string;
  notifications_enabled?: boolean;
  sound_enabled?: boolean;
  vibration_enabled?: boolean;
  auto_sync?: boolean;
  sync_frequency?: 'manual' | 'every_hour' | 'every_day';
  offline_storage_limit?: number;
  created_at?: string;
  updated_at?: string;
}

export interface UserAchievement {
  id?: number;
  user_id: number;
  achievement_type: string;
  achievement_name: string;
  description?: string;
  earned_date: string;
  points?: number;
  badge_url?: string;
  sync_status?: 'synced' | 'pending' | 'error';
  created_at?: string;
  updated_at?: string;
}

// Sync user data interface (for bulk sync operations from backend)
export interface SyncUserData {
  id: number;                          // Backend ID (required for sync)
  username: string;
  full_name?: string;
  email?: string;
  person_id?: number;
  fin_co_code?: string;
  mobile_phone?: string;
  profit_centre_code?: string;
  employee_no?: string;
  is_authenticated?: number;
  last_login?: string;
  created_date?: string;
  updated_date?: string;
  created_at?: string;
  updated_at?: string;
  created_by?: string;
  updated_by?: string;
  is_deleted?: boolean;                // BaseEntity field
}

// USER_STATUS constant is now in Constants.ts to avoid duplication 