/**
 * Meter Reading Sync Log Model
 * 水表读数同步日志模型 - 用于跟踪读数的同步状态
 */
export interface MeterReadingSyncLog {
  sync_log_id?: number;

  local_reading_id: number;

  sync_status: 'pending' | 'synced' | 'error' | 'conflict';
  last_sync_time?: string;
  last_update_time?: string;

  sync_errors?: string;
  retry_count: number;
  conflict_status: 'none' | 'resolved' | 'pending';

  offline_indicator: number;
  sync_task_count: number;

  created_at?: string;
  updated_at?: string;
}

/**
 * Create Meter Reading Sync Log Request
 * 创建水表读数同步日志请求
 */
export interface CreateMeterReadingSyncLogRequest {
  local_reading_id: number;
  sync_status?: 'pending' | 'synced' | 'error' | 'conflict';
  last_sync_time?: string;
  last_update_time?: string;
  sync_errors?: string;
  retry_count?: number;
  conflict_status?: 'none' | 'resolved' | 'pending';
  offline_indicator?: number;
  sync_task_count?: number;
}

/**
 * Update Meter Reading Sync Log Request
 * 更新水表读数同步日志请求
 */
export interface UpdateMeterReadingSyncLogRequest {
  sync_status?: 'pending' | 'synced' | 'error' | 'conflict';
  last_sync_time?: string;
  last_update_time?: string;
  sync_errors?: string;
  retry_count?: number;
  conflict_status?: 'none' | 'resolved' | 'pending';
  offline_indicator?: number;
  sync_task_count?: number;
}

/**
 * Meter Reading Sync Log Status Enum
 * 水表读数同步日志状态枚举
 */
export const MeterReadingSyncLogStatus = {
  PENDING: 'pending' as const,
  SYNCED: 'synced' as const,
  ERROR: 'error' as const,
  CONFLICT: 'conflict' as const
} as const;

/**
 * Meter Reading Sync Log Conflict Status Enum
 * 水表读数同步日志冲突状态枚举
 */
export const MeterReadingSyncLogConflictStatus = {
  NONE: 'none' as const,
  RESOLVED: 'resolved' as const,
  PENDING: 'pending' as const
} as const; 