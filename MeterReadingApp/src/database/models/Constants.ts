// Database constants and enums

export const TABLE_NAMES = {
  USERS: 'users',
  WATER_METERS: 'water_meters',
  TASKS: 'tasks',
  METER_READINGS: 'meter_readings',
  PHOTOS: 'photos',
  LOCATIONS: 'locations',
  TASK_ASSIGNMENT_HISTORY: 'task_assignment_history',
  USER_PREFERENCES: 'user_preferences',
  DEVICES: 'devices',
  USER_ACHIEVEMENTS: 'user_achievements',
  SYNC_QUEUE: 'sync_queue',
  SYNC_LOGS: 'sync_logs',
  APP_SETTINGS: 'app_settings',
  CACHE_ENTRIES: 'cache_entries',
  USER_STATS: 'user_stats',
  OFFLINE_TASKS: 'offline_tasks',
  OFFLINE_READINGS: 'offline_readings'
} as const;

export const SYNC_STATUS = {
  SYNCED: 'synced',
  PENDING: 'pending',
  ERROR: 'error'
} as const;

export const TASK_STATUS = {
  ASSIGNED: 'assigned',
  IN_PROGRESS: 'in_progress',
  COMPLETED: 'completed',
  CANCELLED: 'cancelled',
  PAUSED: 'paused'
} as const;

export const USER_STATUS = {
  AVAILABLE: 'Available',
  BUSY: 'Busy',
  BREAK: 'Break',
  OFFLINE: 'Offline'
} as const; 