// Water Meter model aligned with database schema (water_meters table)
export interface WaterMeter {
  // ============ BaseEntity fields ============
  id?: number;
  created_at?: string;
  updated_at?: string;
  is_deleted?: number;
  created_by?: string;
  updated_by?: string;

  // ============ Core identity fields ============
  asset_id?: string;                      // AssetId
  serial_number: string;                  // SerialNumber (NOT NULL)
  account_number?: string;                // AccountNumber

  // ============ Location information ============
  location: string;                       // Location (NOT NULL, default '')
  address?: string;                       // Address
  latitude?: number;                      // GPS coordinates
  longitude?: number;                     // GPS coordinates

  // ============ Meter information ============
  meter_type: string;                     // MeterType (NOT NULL, default 'Water')
  status: string;                         // Status (NOT NULL, default 'Active')
  customer_name?: string;                 // CustomerName
  last_reading?: number;                  // LastReading
  last_reading_date?: string;             // LastReadingDate

  // ============ Sync status ============
  sync_status?: string;                   // SyncStatus (default 'Synced')
  last_sync_date?: string;                // LastSyncDate
}

// Create request for new water meter
export interface CreateWaterMeterRequest {
  asset_id?: string;
  serial_number: string;
  account_number?: string;
  location: string;
  address?: string;
  latitude?: number;
  longitude?: number;
  meter_type?: string;
  status?: string;
  customer_name?: string;
  last_reading?: number;
  last_reading_date?: string;
  sync_status?: string;
}

// Update request for existing water meter
export interface UpdateWaterMeterRequest {
  asset_id?: string;
  account_number?: string;
  location?: string;
  address?: string;
  latitude?: number;
  longitude?: number;
  meter_type?: string;
  status?: string;
  customer_name?: string;
  last_reading?: number;
  last_reading_date?: string;
  sync_status?: string;
}

// Sync water meter data interface (for bulk sync operations from backend)
export interface SyncWaterMeterData {
  id: number;                             // Backend ID (required for sync)
  asset_id?: string;
  serial_number: string;
  account_number?: string;
  location: string;
  address?: string;
  latitude?: number;
  longitude?: number;
  meter_type: string;
  status: string;
  customer_name?: string;
  last_reading?: number;
  last_reading_date?: string;
  sync_status?: string;
  last_sync_date?: string;
  created_at?: string;
  updated_at?: string;
  created_by?: string;
  updated_by?: string;
  is_deleted?: boolean;                   // BaseEntity field
} 