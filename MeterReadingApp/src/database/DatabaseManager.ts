// @ts-ignore - SQLite types handled manually
import SQLite from 'react-native-sqlite-storage';
import * as RNFS from 'react-native-fs';
import { DataMigrationService } from './DataMigrationService';

// Enable debugging and promises for SQLite
SQLite.DEBUG(true);
SQLite.enablePromise(true);

// Database configuration
const DB_NAME = 'water_meter_app.db';
const DB_LOCATION = 'default';
const CURRENT_DB_VERSION = 0;

interface Migration {
  version: number;
  description: string;
  script: string;
}

class DatabaseManager {
  private static instance: DatabaseManager;
  private database: SQLite.SQLiteDatabase | null = null;

  private constructor() {}

  static getInstance(): DatabaseManager {
    if (!DatabaseManager.instance) {
      DatabaseManager.instance = new DatabaseManager();
    }
    return DatabaseManager.instance;
  }

  // Open database connection
  private async openDatabase(): Promise<SQLite.SQLiteDatabase> {
    return new Promise((resolve, reject) => {
      SQLite.openDatabase(
        {
          name: DB_NAME,
          location: DB_LOCATION,
        },
        (dbObject: any) => {
          resolve(dbObject);
        },
        (error: any) => {
          console.error('Error opening database:', error);
          reject(error);
        }
      );
    });
  }

  // Execute non-transactional queries (for PRAGMA statements)
  private async executeNonTransactionalQuery(
    sql: string,
    params: any[] = []
  ): Promise<any> {
    if (!this.database) {
      throw new Error('Database not initialized');
    }

    return new Promise((resolve, reject) => {
      // Use readTransaction for PRAGMA statements to avoid transaction conflicts
      this.database!.readTransaction((tx: any) => {
        tx.executeSql(
          sql,
          params,
          (tx: any, results: any) => {
            resolve(results);
          },
          (error: any) => {
            console.error(`Error executing non-transactional query: ${sql}`, error);
            reject(error);
          }
        );
      }, (error: any) => {
        console.error(`Transaction error for query: ${sql}`, error);
        reject(error);
      });
    });
  }

  // Execute transactional queries
  private async executeQuery(sql: string, params: any[] = []): Promise<any> {
    if (!this.database) {
      throw new Error('Database not initialized');
    }

    return new Promise((resolve, reject) => {
      this.database!.transaction((tx: any) => {
        tx.executeSql(
          sql,
          params,
          (tx: any, results: any) => {
            resolve(results);
          },
          (error: any) => {
            console.error(`Error executing query: ${sql}`, error);
            reject(error);
          }
        );
      });
    });
  }

  // Get current database version
  private async getCurrentDBVersion(): Promise<number> {
    try {
      const result = await this.executeSql('SELECT version FROM db_version LIMIT 1');
      if (result.rows.length > 0) {
        return result.rows.item(0).version;
      }
    } catch (error) {
      console.log('Database version table does not exist yet, returning version 0');
    }
    return 0;
  }

  // Update database version
  private async updateDBVersion(version: number): Promise<void> {
    try {
      await this.executeQuery('UPDATE db_version SET version = ?', [version]);
      console.log(`Database version updated to ${version}`);
    } catch (error) {
      console.error('Error updating database version:', error);
      throw error;
    }
  }

  // Handle database version table creation/update
  private async updateDBVersionTable(): Promise<void> {
    try {
      // Check if db_version table exists and has data
      const versionExists = await this.getCurrentDBVersion();
      
      if (versionExists === 0) {
        // Insert initial version
        await this.executeQuery('INSERT INTO db_version (version) VALUES (?)', [CURRENT_DB_VERSION]);
        console.log(`Initial database version set to ${CURRENT_DB_VERSION}`);
      } else {
        // Update existing version
        await this.updateDBVersion(CURRENT_DB_VERSION);
      }
    } catch (error) {
      console.error('Error updating database version table:', error);
      throw error;
    }
  }

  // Initialize PRAGMA statements for optimization
  private async initPRAGMAStatements(): Promise<void> {
    try {
      // Execute PRAGMA statements that are safe in any context
      await this.executeNonTransactionalQuery('PRAGMA foreign_keys = ON');
      await this.executeNonTransactionalQuery('PRAGMA busy_timeout = 5000');
      await this.executeNonTransactionalQuery('PRAGMA cache_size = 100000');
      await this.executeNonTransactionalQuery('PRAGMA temp_store = MEMORY');

      // Skip problematic PRAGMA statements that might conflict with transactions
      console.log('Basic PRAGMA statements executed successfully');

      // Try to set journal mode and synchronous mode separately
      try {
        await this.executeNonTransactionalQuery('PRAGMA journal_mode = WAL');
        console.log('WAL mode enabled');
      } catch (error) {
        console.log('WAL mode already enabled or not supported:', error);
      }

      try {
        await this.executeNonTransactionalQuery('PRAGMA synchronous = NORMAL');
        console.log('Synchronous mode set to NORMAL');
      } catch (error) {
        console.log('Synchronous mode setting skipped (may be in transaction):', error);
      }

    } catch (error) {
      console.error('Error executing PRAGMA statements:', error);
      // Don't throw error, just log it - PRAGMA failures shouldn't stop initialization
    }
  }

  // Create all tables aligned with backend C# models using backend IDs
  private async createBackendAlignedTables(): Promise<void> {
    const createTablesSQL = [
      // Version management table (keep autoincrement for local management)
      `CREATE TABLE IF NOT EXISTS db_version (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        version INTEGER NOT NULL,
        updated_at TEXT DEFAULT CURRENT_TIMESTAMP
      )`,

      // Users table - using backend User IDs directly
      `CREATE TABLE IF NOT EXISTS users (
        id INTEGER PRIMARY KEY,
        created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
        is_deleted INTEGER DEFAULT 0,
        created_by TEXT DEFAULT '',
        updated_by TEXT DEFAULT '',
        username TEXT UNIQUE NOT NULL,
        full_name TEXT DEFAULT '',
        email TEXT DEFAULT '',
        person_id INTEGER DEFAULT 0,
        fin_co_code TEXT DEFAULT '',
        mobile_phone TEXT DEFAULT '',
        profit_centre_code TEXT DEFAULT '',
        employee_no TEXT DEFAULT '',
        is_authenticated INTEGER DEFAULT 0,
        password_hash TEXT,
        role TEXT DEFAULT 'reader',
        is_active INTEGER DEFAULT 1,
        last_login TEXT,
        last_login_date TEXT,
        created_date TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_date TEXT DEFAULT CURRENT_TIMESTAMP,
        sync_status TEXT DEFAULT 'local'
      )`,

      // Water meters table - using backend WaterMeter IDs directly
      `CREATE TABLE IF NOT EXISTS water_meters (
        id INTEGER PRIMARY KEY,
        created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
        is_deleted INTEGER DEFAULT 0,
        created_by TEXT DEFAULT '',
        updated_by TEXT DEFAULT '',
        asset_id TEXT,
        serial_number TEXT NOT NULL,
        account_number TEXT,
        location TEXT NOT NULL DEFAULT '',
        address TEXT DEFAULT '',
        latitude REAL,
        longitude REAL,
        meter_type TEXT NOT NULL DEFAULT 'Water',
        status TEXT NOT NULL DEFAULT 'Active',
        customer_name TEXT,
        last_reading REAL,
        last_reading_date TEXT,
        sync_status TEXT DEFAULT 'Synced',
        last_sync_date TEXT
      )`,

      // Work tasks table - using backend WorkTask IDs directly
      `CREATE TABLE IF NOT EXISTS work_tasks (
        id INTEGER PRIMARY KEY,
        created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
        is_deleted INTEGER DEFAULT 0,
        created_by TEXT DEFAULT '',
        updated_by TEXT DEFAULT '',
        name TEXT NOT NULL,
        description TEXT,
        status TEXT NOT NULL DEFAULT 'Pending',
        priority TEXT NOT NULL DEFAULT 'Medium',
        type TEXT NOT NULL DEFAULT 'MeterReading',
        assigned_user_id INTEGER,
        meter_id INTEGER,
        due_date TEXT,
        scheduled_date TEXT,
        completed_date TEXT,
        location TEXT,
        service_address TEXT,
        instructions TEXT,
        notes TEXT,
        FOREIGN KEY (assigned_user_id) REFERENCES users (id),
        FOREIGN KEY (meter_id) REFERENCES water_meters (id)
      )`,

      `CREATE TABLE IF NOT EXISTS meter_readings (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        uuid TEXT NOT NULL UNIQUE,

        meter_id INTEGER NOT NULL,
        user_id INTEGER NOT NULL,
        task_id INTEGER NOT NULL,

        reading_value REAL NOT NULL,
        reading_date TEXT NOT NULL,

        reading_method TEXT NOT NULL DEFAULT 'Manual',
        reading_type TEXT DEFAULT 'Regular',
        data_source TEXT DEFAULT 'Mobile',

        has_ocr INTEGER DEFAULT 0,
        ocr_status TEXT,
        ocr_confidence REAL,

        latitude REAL,
        longitude REAL,
        gps_accuracy REAL,
        location TEXT,

        status TEXT NOT NULL DEFAULT 'Completed',
        validation_status TEXT,

        is_validated INTEGER DEFAULT 0,
        validated_by INTEGER,
        validation_date TEXT,
        validation_comments TEXT,

        is_anomalous INTEGER DEFAULT 0,
        anomaly_reason TEXT,
        anomaly_type TEXT,
        cant_read INTEGER DEFAULT 0,
        cant_read_reason TEXT,

        notes TEXT,

        has_photo INTEGER DEFAULT 0,
        photo_count INTEGER DEFAULT 0,
        photo_filenames TEXT,
        device_info TEXT,
        app_version TEXT,
        is_offline_reading INTEGER DEFAULT 0,
        offline_id TEXT,
        sync_status TEXT DEFAULT 'pending',
        sync_error TEXT,
        sync_attempts INTEGER DEFAULT 0,

        created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
        created_by TEXT DEFAULT '',
        updated_by TEXT DEFAULT '',
        is_deleted INTEGER DEFAULT 0,

        FOREIGN KEY (meter_id) REFERENCES water_meters (id),
        FOREIGN KEY (user_id) REFERENCES users (id),
        FOREIGN KEY (task_id) REFERENCES work_tasks (id),
        FOREIGN KEY (validated_by) REFERENCES users (id)
      )`,

      // Reading photos table
      `CREATE TABLE IF NOT EXISTS reading_photos (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        server_id INTEGER,
        uuid TEXT NOT NULL UNIQUE,
        reading_id INTEGER,
        reading_server_id INTEGER,

        -- 照片信息
        filename TEXT NOT NULL,
        file_path TEXT NOT NULL,
        file_size INTEGER NOT NULL,
        mime_type TEXT NOT NULL,

        -- 拍摄信息
        captured_at TEXT NOT NULL,
        latitude REAL,
        longitude REAL,
        photo_type TEXT NOT NULL DEFAULT 'meter',

        -- 云存储信息
        remote_url TEXT,
        thumbnail_url TEXT,
        upload_retry_count INTEGER DEFAULT 0,
        uploaded_at TEXT,

        -- 同步状态
        sync_status TEXT DEFAULT 'pending',
        sync_error TEXT,

        -- 审计字段
        created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
        is_deleted INTEGER DEFAULT 0,

        FOREIGN KEY (reading_id) REFERENCES meter_readings (id)
      )`,

      // Offline readings table
      `CREATE TABLE IF NOT EXISTS offline_readings (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        reading_data TEXT NOT NULL,
        created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
        expires_at TEXT,
        priority INTEGER DEFAULT 1,
        retry_count INTEGER DEFAULT 0,
        last_error TEXT
      )`,

      // Baseline records table - complete schema with all required fields
      `CREATE TABLE IF NOT EXISTS baseline_records (
        id INTEGER PRIMARY KEY,
        meter_id INTEGER NOT NULL,
        baseline_date TEXT NOT NULL,
        baseline_value REAL NOT NULL,
        baseline_type TEXT NOT NULL,
        status TEXT NOT NULL DEFAULT 'Active',
        import_batch TEXT,
        source_file TEXT,
        data_source TEXT DEFAULT 'Manual',
        validation_notes TEXT,
        is_validated INTEGER DEFAULT 0,
        validated_date TEXT,
        validated_by TEXT,
        has_validation_errors INTEGER DEFAULT 0,
        validation_errors TEXT,
        is_anomalous INTEGER DEFAULT 0,
        anomaly_description TEXT,
        previous_baseline_id INTEGER,
        previous_baseline_value REAL,
        variance_from_previous REAL,
        variance_percentage REAL,
        is_corrected INTEGER DEFAULT 0,
        corrected_date TEXT,
        corrected_by TEXT,
        correction_reason TEXT,
        confidence_level INTEGER DEFAULT 100,
        notes TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        created_by TEXT NOT NULL,
        updated_by TEXT NOT NULL,
        is_deleted INTEGER DEFAULT 0,
        sync_status TEXT DEFAULT 'Synced',
        last_sync_date TEXT,
        created_date TEXT NOT NULL,
        updated_date TEXT NOT NULL,
        FOREIGN KEY (meter_id) REFERENCES water_meters(id),
        FOREIGN KEY (previous_baseline_id) REFERENCES baseline_records(id)
      )`,

      `CREATE TABLE IF NOT EXISTS meter_reading_sync_logs (
        sync_log_id INTEGER PRIMARY KEY AUTOINCREMENT,
        local_reading_id INTEGER NOT NULL,
        last_sync_time TEXT,
        last_update_time TEXT,
        sync_status TEXT NOT NULL DEFAULT 'pending',
        sync_errors TEXT,
        offline_indicator INTEGER DEFAULT 1,
        sync_task_count INTEGER DEFAULT 1,
        conflict_status TEXT DEFAULT 'none',
        retry_count INTEGER DEFAULT 0,
        created_at TEXT,
        updated_at TEXT,
        FOREIGN KEY (local_reading_id) REFERENCES meter_readings (id)
      )`
    ];

    // Create indexes for performance optimization
    const createIndexesSQL = [
      // Users indexes
      `CREATE INDEX IF NOT EXISTS idx_users_username ON users(username)`,
      `CREATE INDEX IF NOT EXISTS idx_users_is_deleted ON users(is_deleted)`,
      
      // Water meters indexes
      `CREATE INDEX IF NOT EXISTS idx_water_meters_serial_number ON water_meters(serial_number)`,
      `CREATE INDEX IF NOT EXISTS idx_water_meters_asset_id ON water_meters(asset_id)`,
      `CREATE INDEX IF NOT EXISTS idx_water_meters_is_deleted ON water_meters(is_deleted)`,
      
      // Work tasks indexes
      `CREATE INDEX IF NOT EXISTS idx_work_tasks_assigned_user_id ON work_tasks(assigned_user_id)`,
      `CREATE INDEX IF NOT EXISTS idx_work_tasks_meter_id ON work_tasks(meter_id)`,
      `CREATE INDEX IF NOT EXISTS idx_work_tasks_status ON work_tasks(status)`,
      `CREATE INDEX IF NOT EXISTS idx_work_tasks_is_deleted ON work_tasks(is_deleted)`,
      
      // Meter readings indexes
      `CREATE INDEX IF NOT EXISTS idx_meter_readings_meter_id ON meter_readings(meter_id)`,
      `CREATE INDEX IF NOT EXISTS idx_meter_readings_user_id ON meter_readings(user_id)`,
      `CREATE INDEX IF NOT EXISTS idx_meter_readings_task_id ON meter_readings(task_id)`,
      `CREATE INDEX IF NOT EXISTS idx_meter_readings_date ON meter_readings(reading_date)`,
      `CREATE INDEX IF NOT EXISTS idx_meter_readings_sync_status ON meter_readings(sync_status)`,
      `CREATE INDEX IF NOT EXISTS idx_meter_readings_uuid ON meter_readings(uuid)`,
      `CREATE INDEX IF NOT EXISTS idx_meter_readings_is_deleted ON meter_readings(is_deleted)`,

      // Reading photos indexes
      `CREATE INDEX IF NOT EXISTS idx_reading_photos_reading_id ON reading_photos(reading_id)`,
      `CREATE INDEX IF NOT EXISTS idx_reading_photos_uuid ON reading_photos(uuid)`,
      `CREATE INDEX IF NOT EXISTS idx_reading_photos_sync_status ON reading_photos(sync_status)`,

      // Offline readings indexes
      `CREATE INDEX IF NOT EXISTS idx_offline_readings_priority ON offline_readings(priority DESC)`,
      `CREATE INDEX IF NOT EXISTS idx_offline_readings_created_at ON offline_readings(created_at)`,

      // Baseline records indexes
      `CREATE INDEX IF NOT EXISTS idx_baseline_meter_id ON baseline_records(meter_id)`,
      `CREATE INDEX IF NOT EXISTS idx_baseline_status ON baseline_records(status)`,
      `CREATE INDEX IF NOT EXISTS idx_baseline_date ON baseline_records(baseline_date)`,
      `CREATE INDEX IF NOT EXISTS idx_baseline_type ON baseline_records(baseline_type)`,
      `CREATE INDEX IF NOT EXISTS idx_baseline_active ON baseline_records(meter_id, status, baseline_date DESC)`,
      `CREATE INDEX IF NOT EXISTS idx_baseline_sync ON baseline_records(sync_status, last_sync_date)`,

      // Sync logs indexes
      `CREATE INDEX IF NOT EXISTS idx_sync_logs_sync_status ON meter_reading_sync_logs(sync_status)`,
      `CREATE INDEX IF NOT EXISTS idx_sync_logs_local_reading_id ON meter_reading_sync_logs(local_reading_id)`,
      `CREATE INDEX IF NOT EXISTS idx_sync_logs_retry_count ON meter_reading_sync_logs(retry_count)`
    ];

    try {
      console.log('Creating backend-aligned database tables...');
      
      // Create tables first
      for (let i = 0; i < createTablesSQL.length; i++) {
        const sql = createTablesSQL[i];
        const tableName = sql.match(/CREATE TABLE IF NOT EXISTS (\w+)/)?.[1] || `table_${i}`;
        console.log(`Creating table: ${tableName}`);
        await this.executeQuery(sql);
        console.log(`Table ${tableName} created successfully`);
      }
      
      console.log('Creating performance indexes...');
      // Create indexes after tables
      for (const sql of createIndexesSQL) {
        const indexName = sql.match(/CREATE INDEX IF NOT EXISTS (\w+)/)?.[1] || 'unknown_index';
        console.log(`Creating index: ${indexName}`);
        await this.executeQuery(sql);
        console.log(`Index ${indexName} created successfully`);
      }
      
      console.log('All backend-aligned tables and indexes created successfully');
    } catch (error) {
      console.error('Error creating backend-aligned tables:', error);
      throw error;
    }
  }

  // Handle data migration from old schema to new schema
  private async performDataMigration(currentVersion: number): Promise<void> {
    if (currentVersion < 3) {
      console.log('Performing data migration from old schema to aligned schema...');
      
      try {
        // Create migration service instance
        const migrationService = new DataMigrationService();
        
        // Perform the migration
        const migrationResults = await migrationService.performDataMigration();
        
        // Check if migration was successful
        const allSuccessful = migrationResults.every(result => result.success);
        
        if (allSuccessful) {
          console.log('Data migration completed successfully');
          
          // Clean up backup tables after successful migration
          setTimeout(async () => {
            try {
              await migrationService.cleanupBackupTables();
            } catch (error) {
              console.error('Error cleaning up backup tables:', error);
            }
          }, 5000); // Clean up after 5 seconds
          
        } else {
          console.error('Some data migrations failed');
          
          // Log failed migrations
          const failedMigrations = migrationResults.filter(result => !result.success);
          failedMigrations.forEach(result => {
            console.error(`Failed to migrate ${result.tableName}: ${result.errorMessage}`);
          });
          
          // Note: We don't restore from backup automatically
          // This allows manual inspection of the issue
          console.log('Migration completed with some failures. Backup tables are preserved for manual inspection.');
        }
        
      } catch (error) {
        console.error('Critical error during data migration:', error);
        throw error;
      }
    }
  }

  // Define migration scripts for database schema updates
  private getMigrations(): Migration[] {
    return [
      {
        version: 2,
        description: "Legacy compatibility migration",
        script: `SELECT COUNT(*) FROM users;`
      },
      {
        version: 3,
        description: "Backend C# model alignment migration",
        script: `
          -- This migration handles transition from old schema to new aligned schema
          -- Drop old incompatible tables if they exist
          DROP TABLE IF EXISTS meter_types;
          DROP TABLE IF EXISTS areas;
          DROP TABLE IF EXISTS photos;
          DROP TABLE IF EXISTS app_settings;

          -- The new tables will be created by createAlignedTables()
          SELECT 1;
        `
      }
    ];
  }

  // Run database migrations
  private async runMigrations(currentVersion: number): Promise<void> {
    const migrations = this.getMigrations();
    
    for (const migration of migrations) {
      if (currentVersion < migration.version) {
        try {
          console.log(`Running migration to version ${migration.version}: ${migration.description}`);
          
          // Split migration script into individual statements
          const statements = migration.script
            .split(';')
            .map(stmt => stmt.trim())
            .filter(stmt => stmt.length > 0);

          for (const statement of statements) {
            await this.executeQuery(statement);
          }

          await this.updateDBVersion(migration.version);
          currentVersion = migration.version;
          
          console.log(`Migration to version ${migration.version} completed successfully`);
        } catch (error) {
          console.error(`Migration to version ${migration.version} failed:`, error);
          throw error;
        }
      }
    }
  }

  // Force database reset - drops all tables and recreates with aligned schema
  async resetDatabase(): Promise<void> {
    try {
      console.log('Starting database reset...');

      if (!this.database) {
        this.database = await this.openDatabase();
      }

      // Get all table names
      const result = await this.executeQuery(`
        SELECT name FROM sqlite_master 
        WHERE type='table' AND name NOT LIKE 'sqlite_%'
      `);

      // Drop all existing tables
      for (let i = 0; i < result.rows.length; i++) {
        const tableName = result.rows.item(i).name;
        console.log(`Dropping table: ${tableName}`);
        await this.executeQuery(`DROP TABLE IF EXISTS ${tableName}`);
      }

      // Recreate with backend aligned schema
      console.log('Recreating tables with backend aligned schema...');
      await this.createBackendAlignedTables();

      // Set version to current
      await this.executeQuery(`
        CREATE TABLE IF NOT EXISTS db_version (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          version INTEGER NOT NULL,
          updated_at TEXT DEFAULT CURRENT_TIMESTAMP
        )
      `);
      await this.executeQuery('INSERT INTO db_version (version) VALUES (5)');

      console.log('Database reset completed successfully');
    } catch (error) {
      console.error('Error resetting database:', error);
      throw error;
    }
  }

  // Initialize database
  async initializeDatabase(): Promise<void> {
    try {
      console.log('DatabaseManager: Initializing database...');
      this.database = await this.openDatabase();

      await this.initPRAGMAStatements();

      // First, ensure db_version table exists
      await this.executeQuery(`
        CREATE TABLE IF NOT EXISTS db_version (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          version INTEGER
        )
      `);
      console.log('DatabaseManager: db_version table ensured');

      // Check current version and upgrade if needed
      const currentVersion = await this.getCurrentDBVersion();
      console.log(`DatabaseManager: Current DB version: ${currentVersion}, Target: ${CURRENT_DB_VERSION}`);

      // If this is a fresh database (version 0), create all tables
      if (currentVersion === 0) {
        console.log('🆕 Fresh database detected, creating all tables...');
        await this.createBackendAlignedTables();
        await this.executeQuery('INSERT INTO db_version (version) VALUES (?)', [CURRENT_DB_VERSION]);
        console.log(`✅ Fresh database setup completed, version set to ${CURRENT_DB_VERSION}`);
      } else if (currentVersion < CURRENT_DB_VERSION) {
        console.log(`🔧 Upgrading database from version ${currentVersion} to version ${CURRENT_DB_VERSION}...`);
        await this.upgradeToCurrentVersion(currentVersion);
      } else {
        console.log(`Database is already at version ${currentVersion}`);
      }

      console.log('DatabaseManager: Database initialization completed successfully');
    } catch (error) {
      console.error('DatabaseManager: Error initializing database:', error);
      throw error;
    }
  }

  // Upgrade database to current version
  async upgradeToCurrentVersion(currentVersion: number): Promise<void> {
    try {
      console.log(`🔄 Upgrading database from version ${currentVersion} to version ${CURRENT_DB_VERSION}...`);

      if (currentVersion < 5) {
        // If older than version 5, do a complete reset
        console.log('🔧 Version is older than 5, performing complete reset...');
        await this.forceResetDatabase();
      } else if (currentVersion < 7) {
        // Version 6 to 7: Update meter_readings table structure
        console.log('📄 Updating database structure for version 7...');
        await this.forceResetDatabase(); // For now, do a complete reset for major schema changes
        await this.updateDBVersion(CURRENT_DB_VERSION);
        console.log(`✅ Successfully upgraded to version ${CURRENT_DB_VERSION}`);
      } else {
        console.log(`✅ Database already at version ${CURRENT_DB_VERSION}`);
      }
    } catch (error) {
      console.error(`❌ Error upgrading to version ${CURRENT_DB_VERSION}:`, error);
      throw error;
    }
  }

  // Upgrade database to version 6 (baseline_records table) - kept for compatibility
  async upgradeToVersion6(currentVersion: number): Promise<void> {
    try {
      console.log(`🔄 Upgrading database from version ${currentVersion} to version 6...`);

      if (currentVersion < 5) {
        // If older than version 5, do a complete reset
        console.log('🔧 Version is older than 5, performing complete reset...');
        await this.forceResetDatabase();
      } else {
        // Version 5 to 6: Just add baseline_records table
        console.log('📄 Adding baseline_records table...');
        await this.createBaselineRecordsTable();
        await this.updateDBVersion(6);
        console.log('✅ Successfully upgraded to version 6');
      }
    } catch (error) {
      console.error('❌ Error upgrading to version 6:', error);
      throw error;
    }
  }


  /**
   * Create baseline_records table with complete schema (DEPRECATED - use createAllTables)
   * 创建完整的基线记录表
   */
  private async createBaselineRecordsTable(): Promise<void> {
    const sql = `
      CREATE TABLE IF NOT EXISTS baseline_records (
          id INTEGER PRIMARY KEY,                    -- 直接使用后端ID
          meter_id INTEGER NOT NULL,                 -- 关联水表ID
          baseline_date TEXT NOT NULL,               -- 基线日期 (ISO格式)
          baseline_value REAL NOT NULL,              -- 基线读数值
          baseline_type TEXT NOT NULL,               -- 基线类型: Initial/Periodic/Correction/Migration
          status TEXT NOT NULL DEFAULT 'Active',     -- 状态: Active/Superseded/Invalid
          import_batch TEXT,                         -- 导入批次号
          source_file TEXT,                          -- 源文件名
          data_source TEXT DEFAULT 'Manual',         -- 数据源: CSV/Excel/Manual/AMS
          validation_notes TEXT,                     -- 验证备注
          is_validated INTEGER DEFAULT 0,            -- 是否已验证 (0/1)
          validated_date TEXT,                       -- 验证日期
          validated_by TEXT,                         -- 验证人
          has_validation_errors INTEGER DEFAULT 0,   -- 是否有验证错误
          validation_errors TEXT,                    -- 验证错误信息
          is_anomalous INTEGER DEFAULT 0,            -- 是否异常
          anomaly_description TEXT,                  -- 异常描述
          previous_baseline_id INTEGER,              -- 前一个基线ID
          previous_baseline_value REAL,              -- 前一个基线值
          variance_from_previous REAL,               -- 与前值的差异
          variance_percentage REAL,                  -- 差异百分比
          is_corrected INTEGER DEFAULT 0,            -- 是否被修正
          corrected_date TEXT,                       -- 修正日期
          corrected_by TEXT,                         -- 修正人
          correction_reason TEXT,                    -- 修正原因
          confidence_level INTEGER DEFAULT 100,      -- 置信度 (0-100)
          notes TEXT,                                -- 备注
          
          -- 基础字段 (BaseEntity)
          created_at TEXT NOT NULL,
          updated_at TEXT NOT NULL,
          created_by TEXT NOT NULL,
          updated_by TEXT NOT NULL,
          is_deleted INTEGER DEFAULT 0,
          
          -- 移动端同步字段
          sync_status TEXT DEFAULT 'Synced',         -- 同步状态
          last_sync_date TEXT,                       -- 最后同步时间
          
          FOREIGN KEY (meter_id) REFERENCES water_meters(id),
          FOREIGN KEY (previous_baseline_id) REFERENCES baseline_records(id)
      );
      
      -- 性能优化索引
      CREATE INDEX IF NOT EXISTS idx_baseline_meter_id ON baseline_records(meter_id);
      CREATE INDEX IF NOT EXISTS idx_baseline_status ON baseline_records(status);
      CREATE INDEX IF NOT EXISTS idx_baseline_date ON baseline_records(baseline_date);
      CREATE INDEX IF NOT EXISTS idx_baseline_type ON baseline_records(baseline_type);
      CREATE INDEX IF NOT EXISTS idx_baseline_active ON baseline_records(meter_id, status, baseline_date DESC);
      CREATE INDEX IF NOT EXISTS idx_baseline_sync ON baseline_records(sync_status, last_sync_date);
    `;
    
    await this.executeQuery(sql);
    console.log('✅ baseline_records table and indexes created successfully');
  }

  // Force database reset (for critical schema changes) - CORDE style
  async forceResetDatabase(): Promise<void> {
    try {
      console.log('🗑️  Force resetting database...');

      // Disable foreign key constraints temporarily
      await this.executeNonTransactionalQuery('PRAGMA foreign_keys = OFF');

      // Drop all tables in correct order
      const tablesToDrop = [
        'baseline_records',
        'meter_reading_sync_logs',
        'reading_photos',
        'offline_readings',
        'meter_readings',
        'work_tasks',
        'water_meters',
        'users',
        'db_version'
      ];

      for (const tableName of tablesToDrop) {
        try {
          await this.executeNonTransactionalQuery(`DROP TABLE IF EXISTS ${tableName}`);
          console.log(`✅ Dropped table: ${tableName}`);
        } catch (error) {
          console.log(`⚠️  Error dropping table ${tableName}: ${error}`);
        }
      }

      // Re-enable foreign key constraints
      await this.executeNonTransactionalQuery('PRAGMA foreign_keys = ON');

      // Create db_version table first
      await this.executeQuery(`
        CREATE TABLE IF NOT EXISTS db_version (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          version INTEGER
        )
      `);

      // Insert initial version
      await this.executeQuery('INSERT INTO db_version (version) VALUES (?)', [CURRENT_DB_VERSION]);

      // Create all tables
      await this.createBackendAlignedTables();

      console.log(`✅ Database force reset completed, version set to ${CURRENT_DB_VERSION}`);

    } catch (error) {
      console.error('❌ Error in force reset:', error);
      throw error;
    }
  }



  // Get database instance
  async getDatabase(): Promise<SQLite.SQLiteDatabase> {
    if (!this.database) {
      await this.initializeDatabase();
    }
    return this.database!;
  }

  // Execute SQL query (public interface)
  async executeSql(query: string, params: any[] = []): Promise<any> {
    return this.executeQuery(query, params);
  }

  // Close database connection
  async closeDatabase(): Promise<void> {
    if (this.database) {
      try {
        // Execute checkpoint to ensure all changes are written
        await this.executeNonTransactionalQuery('PRAGMA wal_checkpoint(FULL)');
        await this.database.close();
        this.database = null;
        console.log('Database connection closed properly');
      } catch (error) {
        console.error('Error closing database:', error);
        throw error;
      }
    }
  }

  // Clear all data (for testing/reset purposes)
  async clearAllData(): Promise<void> {
    const tables = [
      'baseline_records', 'meter_reading_sync_logs', 'reading_photos', 'offline_readings',
      'meter_readings', 'work_tasks', 'water_meters', 'users'
    ];

    try {
      for (const table of tables) {
        await this.executeQuery(`DELETE FROM ${table}`);
      }
      console.log('All aligned data cleared successfully');
    } catch (error) {
      console.error('Error clearing aligned data:', error);
      throw error;
    }
  }

  // Force clean database recreation (for major schema updates)
  async recreateCleanDatabase(): Promise<void> {
    try {
      console.log('📦 DatabaseManager: Starting clean database recreation...');
      
      // Force reset to latest schema
      await this.forceResetDatabase();
      
      console.log('✅ DatabaseManager: Clean database recreation completed successfully');
      console.log(`🔧 DatabaseManager: Database is now at version ${CURRENT_DB_VERSION} with clean schema`);
      
    } catch (error) {
      console.error('❌ DatabaseManager: Error during clean database recreation:', error);
      throw error;
    }
  }

  // Get table schema information (for debugging)
  async getTableSchema(tableName: string): Promise<any> {
    try {
      const result = await this.executeQuery(`PRAGMA table_info(${tableName})`);
      return result;
    } catch (error) {
      console.error(`Error getting schema for table ${tableName}:`, error);
      throw error;
    }
  }

  // List all tables (for debugging)
  async listTables(): Promise<string[]> {
    try {
      const result = await this.executeQuery(
        `SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'`
      );
      const tables: string[] = [];
      for (let i = 0; i < result.rows.length; i++) {
        tables.push(result.rows.item(i).name);
      }
      return tables;
    } catch (error) {
      console.error('Error listing tables:', error);
      throw error;
    }
  }


}

export default DatabaseManager;