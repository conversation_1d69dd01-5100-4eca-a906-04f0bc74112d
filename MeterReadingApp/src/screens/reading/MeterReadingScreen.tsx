import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  VStack,
  Button,
  Image,
  Text,
  Box,
  Spinner,
  HStack,
  Input,
  Pressable,
  ScrollView,
  Badge,
  Divider,
} from 'native-base';
import { useTheme } from '../../styles/ThemeContext';
import { useAuth } from '../../context/AuthContext';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { launchCamera, launchImageLibrary } from 'react-native-image-picker';
import { Platform, PermissionsAndroid } from 'react-native';
import { useToastManager } from '../../utils/ToastManager';
import { generateSecureRandomString } from '../../utils/secureRandom';
import { IconWrapper } from '../../components/common';
import { 
  ImageRecognitionManager,
  createRecognitionManager,
  RecognitionResult,
  RecognitionError,
  OpenCVStrategy 
} from '../../services/recognition';
import OpenCVProcessor, { OpenCVProcessorRef } from '../../services/recognition/opencv/OpenCVProcessor';
import NetInfo from '@react-native-community/netinfo';
import { RootStackParamList } from '../../navigation/AppNavigator';
import { locationService, LocationData, LocationValidationResult } from '../../services/LocationService';
import { readingValidationService, ValidationResult } from '../../services/ReadingValidationService';
import { BusinessDataSyncService } from '../../services/sync';
import { ReadingRepository } from '../../data';
import { MobileTaskDetailDto } from '../../types/mobile';
import { TaskRepository } from '../../data/repositories';
import { BaselineService } from '../../services/BaselineService';
import { MeterReadingSyncService } from '../../services/MeterReadingSyncService';
import { ReadingService } from '../../services/ReadingService';
import { PhotoCaptureService } from '../../services/PhotoCaptureService';
import { MeterReadingPhotoRepository } from '../../data/repositories/MeterReadingPhotoRepository';
import { ReadingPhoto } from '../../database/models/MeterReading';
import { PhotoPreviewComponent } from '../../components/PhotoPreviewComponent';

// Function to request camera permission
const requestCameraPermission = async () => {
  if (Platform.OS === 'android') {
    try {
      const granted = await PermissionsAndroid.request(
        PermissionsAndroid.PERMISSIONS.CAMERA,
        {
          title: "Camera Permission Required",
          message: "This app needs camera permission to take meter photos",
          buttonNeutral: "Ask Later",
          buttonNegative: "Cancel",
          buttonPositive: "OK"
        }
      );
      return granted === PermissionsAndroid.RESULTS.GRANTED;
    } catch (err) {
      console.warn(err);
      return false;
    }
  }
  return true;
};

type MeterReadingScreenRouteProp = RouteProp<RootStackParamList, 'MeterReading'>;

const MeterReadingScreen: React.FC = () => {
  // Navigation and route
  const route = useRoute<MeterReadingScreenRouteProp>();
  const navigation = useNavigation();
  const { taskId, meterId } = route.params || {};
  const { mode } = useTheme();
  const { state: authState } = useAuth();
  const toastManager = useToastManager();

  // Task and meter information state
  const [taskDetail, setTaskDetail] = useState<MobileTaskDetailDto | null>(null);
  const [baselineReading, setBaselineReading] = useState<number | null>(null);
  const [isLoadingTask, setIsLoadingTask] = useState<boolean>(!!taskId);

  // OCR and image processing state
  const [selectedImage, setSelectedImage] = useState<any>(null);
  const [processedImage, setProcessedImage] = useState<any>(null);
  const [recognizedText, setRecognizedText] = useState<string | null>(null);
  const [isProcessing, setIsProcessing] = useState<boolean>(false);
  const [manualInput, setManualInput] = useState<string>('');
  const [processingSteps, setProcessingSteps] = useState<string[]>([]);
  const [recognitionResult, setRecognitionResult] = useState<RecognitionResult | null>(null);

  // GPS location state
  const [currentLocation, setCurrentLocation] = useState<LocationData | null>(null);
  const [locationValidation, setLocationValidation] = useState<LocationValidationResult | null>(null);
  const [isGettingLocation, setIsGettingLocation] = useState<boolean>(false);

  // Reading validation and saving state
  const [validationResult, setValidationResult] = useState<ValidationResult | null>(null);
  const [finalReading, setFinalReading] = useState<string>('');
  const [isSavingReading, setIsSavingReading] = useState<boolean>(false);
  const [readingSaved, setReadingSaved] = useState<boolean>(false);

  // Photo management state
  const [capturedPhotos, setCapturedPhotos] = useState<ReadingPhoto[]>([]);
  const [isCapturingPhoto, setIsCapturingPhoto] = useState<boolean>(false);

  // OCR recognition manager
  const [recognitionManager, setRecognitionManager] = useState<ImageRecognitionManager | null>(null);
  const [opencvReady, setOpencvReady] = useState<boolean>(false);
  const opencvProcessorRef = useRef<OpenCVProcessorRef>(null);
  
  // Initialization flags to prevent multiple setup
  const initializationRef = useRef<boolean>(false);
  const opencvStrategyBoundRef = useRef<boolean>(false);

  // Load task details if taskId is provided
  useEffect(() => {
    const loadTaskDetails = async () => {
      if (!taskId) {
        setIsLoadingTask(false);
        return;
      }

      try {
        console.log(`MeterReadingScreen: Loading task details for ID: ${taskId}`);
        const details = await TaskRepository.getTaskDetails(parseInt(taskId));
        
        if (details) {
          setTaskDetail(details);
          console.log(`MeterReadingScreen: Task loaded - Meter: ${details.meterNumber}`);
          
          // If no last reading exists, try to get baseline reading
          if ((!details.lastReading || details.lastReading === 0) && details.meterId) {
            try {
              console.log(`MeterReadingScreen: No last reading, fetching baseline for meter ${details.meterId}`);
              const baseline = await BaselineService.getBaselineForMeter(details.meterId);
              if (baseline) {
                setBaselineReading(baseline.baseline_value);
                console.log(`MeterReadingScreen: Found baseline reading: ${baseline.baseline_value}`);
              } else {
                console.log(`MeterReadingScreen: No baseline found for meter ${details.meterId}`);
              }
            } catch (baselineError) {
              console.warn('MeterReadingScreen: Error fetching baseline reading:', baselineError);
            }
          }
        } else {
          console.warn(`MeterReadingScreen: Task ${taskId} not found`);
          toastManager.error('Task not found');
        }
      } catch (error: any) {
        console.error('MeterReadingScreen: Error loading task:', error);
        toastManager.error('Failed to load task details');
      } finally {
        setIsLoadingTask(false);
      }
    };

    loadTaskDetails();
  }, [taskId]);

  // Initialize GPS location service
  useEffect(() => {
    const initializeLocation = async () => {
      try {
        await locationService.initialize();
        
        // Get initial location if permission granted
        if (locationService.isLocationPermissionGranted()) {
          const location = locationService.getLastKnownLocation();
          if (location) {
            setCurrentLocation(location);
            setLocationValidation(locationService.validateLocationAccuracy(location));
          }
        }
      } catch (error) {
        console.error('MeterReadingScreen: Error initializing GPS:', error);
      }
    };

    initializeLocation();
  }, []);

  // Clear recognition state when image changes
  useEffect(() => {
    setRecognizedText(null);
    setProcessedImage(null);
    setProcessingSteps([]);
    setRecognitionResult(null);
  }, [selectedImage]);

  // Initialize recognition manager once
  useEffect(() => {
    if (!initializationRef.current) {
      console.log('MeterReadingScreen: Initializing OCR recognition manager...');
      const manager = createRecognitionManager();
      setRecognitionManager(manager);
      initializationRef.current = true;
    }
    
    return () => {
      // Cleanup if needed
    };
  }, []);

  // 绑定 OpenCV processor 到 strategy
  useEffect(() => {
    if (recognitionManager && opencvReady && opencvProcessorRef.current && !opencvStrategyBoundRef.current) {
      console.log('MeterReadingScreen: Binding OpenCV processor to strategy (ONCE)');
      
      try {
        // 创建新的 OpenCV strategy 实例
        const opencvStrategyWithProcessor = new OpenCVStrategy(opencvProcessorRef.current);
        
        // 注册到 manager
        recognitionManager.registerStrategy(opencvStrategyWithProcessor);
        console.log('✅ OpenCV strategy with processor registered successfully!');
        
        // 更新 HybridStrategy 的内部 strategies
        const allStrategies = recognitionManager.getAvailableStrategies();
        const hybridStrategy = allStrategies.find(s => s.name === 'Hybrid') as any;
        
        if (hybridStrategy && hybridStrategy.strategies) {
          console.log('MeterReadingScreen: Updating HybridStrategy internal strategies...');
          
          // 移除旧的 OpenCV strategy
          hybridStrategy.strategies = hybridStrategy.strategies.filter((s: any) => s.name !== 'OpenCV');
          
          // 添加新的 OpenCV strategy
          hybridStrategy.strategies.push(opencvStrategyWithProcessor);
          
          console.log('✅ HybridStrategy internal strategies updated successfully!');
          console.log('HybridStrategy now has strategies:', hybridStrategy.strategies.map((s: any) => s.name));
        }
        
        // 设置标志防止重复绑定
        opencvStrategyBoundRef.current = true;
        
        // 测试连接
        setTimeout(() => {
          const canHandle = opencvStrategyWithProcessor.canHandle('mechanical_circular' as any, 'good' as any);
          console.log('OpenCV strategy canHandle test result:', canHandle);
        }, 100);
        
      } catch (error) {
        console.error('Error binding OpenCV processor to strategy:', error);
      }
    }
  }, [recognitionManager, opencvReady]); // 移除 opencvProcessorRef.current 依赖

  // Handle OpenCV processor ready - 使用 useCallback 防止重复调用
  const handleOpenCVReady = useCallback(() => {
    console.log('MeterReadingScreen: OpenCV processor ready callback triggered');
    
    if (!opencvReady) {
      console.log('MeterReadingScreen: Setting OpenCV ready state to true');
      setOpencvReady(true);
      
      // 延迟测试通信以确保完全准备就绪
      setTimeout(() => {
        if (opencvProcessorRef.current) {
          console.log('MeterReadingScreen: Testing OpenCV WebView communication...');
          opencvProcessorRef.current.testCommunication()
            .then(() => {
              console.log('MeterReadingScreen: ✅ OpenCV WebView communication test successful');
            })
            .catch((error) => {
              console.error('MeterReadingScreen: ❌ OpenCV WebView communication test failed:', error);
            });
        }
      }, 1000); // 1秒延迟确保完全准备
    } else {
      console.log('MeterReadingScreen: OpenCV already ready, ignoring callback');
    }
  }, [opencvReady]);

  const handleOpenCVError = useCallback((error: string) => {
    console.error('MeterReadingScreen: OpenCV processor error:', error);
    setOpencvReady(false);
    opencvStrategyBoundRef.current = false; // 重置绑定标志
    toastManager.error(`OpenCV initialization failed: ${error}`);
  }, [toastManager]);

  const handleTakePhoto = async () => {
    try {
      setIsCapturingPhoto(true);

      const hasPermission = await requestCameraPermission();
      if (!hasPermission) {
        toastManager.permissionError('Camera');
        return;
      }

      console.log("Opening camera...");
      const result = await launchCamera({
        mediaType: 'photo',
        includeBase64: false,
        quality: 0.8,
      });

      console.log("Camera result:", result);
      if (result.assets && result.assets.length > 0) {
        setSelectedImage(result.assets[0]);

        const asset = result.assets[0];
        if (asset.uri) {
          const compressedUri = await PhotoCaptureService.compressPhoto(asset.uri);

          const photoData = {
            readingId: 0,
            localFilePath: compressedUri,
            fileName: asset.fileName || `photo_${Date.now()}.jpg`,
            fileSize: asset.fileSize || 0,
            mimeType: asset.type || 'image/jpeg',
            capturedAt: new Date().toISOString(),
            latitude: currentLocation?.latitude,
            longitude: currentLocation?.longitude,
            photoType: 'meter' as const
          };

          const savedPhoto = await MeterReadingPhotoRepository.savePhoto(photoData);
          setCapturedPhotos(prev => [...prev, savedPhoto]);

          toastManager.success('Photo captured and saved locally');
        }
      }
    } catch (error: any) {
      console.error('Camera error:', error);
      toastManager.error(`Camera error: ${error.message}`);
    } finally {
      setIsCapturingPhoto(false);
    }
  };

  const handleChoosePhoto = async () => {
    try {
      console.log("Opening gallery...");
      const result = await launchImageLibrary({
        mediaType: 'photo',
        includeBase64: false,
        quality: 0.8,
      });

      console.log("Gallery result:", result);
      if (result.assets && result.assets.length > 0) {
        setSelectedImage(result.assets[0]);
      }
    } catch (error) {
      console.error('Photo selection error:', error);
      toastManager.galleryError();
    }
  };

  // GPS location functions
  const getCurrentLocation = async () => {
    if (isGettingLocation) return;

    setIsGettingLocation(true);
    try {
      console.log('MeterReadingScreen: Getting current GPS location...');
      const location = await locationService.getCurrentLocationForReading({
        enableHighAccuracy: true,
        timeout: 15000,
        forceRefresh: true
      });

      setCurrentLocation(location);
      const validation = locationService.validateLocationAccuracy(location);
      setLocationValidation(validation);

      // Show GPS status to user
      if (validation.warningLevel === 'low') {
        toastManager.success(validation.message);
      } else if (validation.warningLevel === 'medium') {
        toastManager.warning(validation.message);
      } else {
        toastManager.warning(validation.message);
      }

      console.log(`MeterReadingScreen: GPS location acquired - ${location.latitude}, ${location.longitude} (${location.accuracy}m)`);
    } catch (error: any) {
      console.error('MeterReadingScreen: GPS error:', error);
      toastManager.error(`GPS error: ${error.message}`);
      setLocationValidation(null);
    } finally {
      setIsGettingLocation(false);
    }
  };

  const handleRecognize = async () => {
    if (!selectedImage) {
      toastManager.noImageSelected();
      return;
    }

    if (!recognitionManager) {
      toastManager.error('Recognition system not initialized');
      return;
    }

    // Get current GPS location before processing
    if (!currentLocation || locationValidation?.warningLevel === 'high') {
      console.log('MeterReadingScreen: Getting GPS location before OCR...');
      await getCurrentLocation();
    }

    // Check network status and inform user
    try {
      const netInfo = await NetInfo.fetch();
      const isConnected = netInfo.isConnected && netInfo.isInternetReachable;
      
      if (isConnected) {
        toastManager.info('🌐 Network available - Using AI-powered recognition');
      } else {
        toastManager.info('📱 No network - Using local recognition only');
      }
    } catch (error) {
      console.log('Network check failed:', error);
    }

    setIsProcessing(true);
    try {
      const imagePath = selectedImage.uri;
      console.log('MeterReadingScreen: Starting OCR recognition with GPS integration...');

      // Use existing OCR strategy (AI + OpenCV + Google Kit)
      const result = await recognitionManager.recognize(imagePath, {
        strategies: ['hybrid'], // Use hybrid strategy for intelligent AI-first, local-fallback
        fallbackEnabled: true,
        confidenceThreshold: 0.5,
        timeout: 30000, // 30 seconds timeout for AI API calls
        debugMode: true,
      });

      console.log('MeterReadingScreen: OCR recognition completed:', result);

      // Update UI with results
      setRecognitionResult(result);
      setRecognizedText(result.reading);
      setProcessingSteps(result.metadata.processingSteps);
      
      // Set manual input to recognized value for editing
      setManualInput(result.reading || '');
      setFinalReading(result.reading || '');
      
      // Set processed image if available
      if (result.metadata.processedImageUri) {
        setProcessedImage({
          uri: result.metadata.processedImageUri,
          fileName: 'processed_' + (selectedImage.fileName || 'meter_image'),
        });
      }

      // Show success message with strategy information
      const strategyInfo = result.strategy === 'AIApi' 
        ? '🤖 AI-powered recognition' 
        : result.strategy === 'MLKit' 
          ? '📱 Local ML Kit recognition'
          : `${result.strategy} recognition`;
          
      toastManager.success(`✅ ${strategyInfo} successful (${Math.round(result.confidence * 100)}% confidence)`);

      // Show additional info if AI API was used
      if (result.metadata.aiService) {
        console.log(`🎯 AI Service used: ${result.metadata.aiService}`);
      }

    } catch (error) {
      console.error('MeterReadingScreen: Recognition error:', error);
      
      if (error instanceof RecognitionError) {
        // Provide user-friendly error messages
        if (error.strategy === 'AIApi') {
          toastManager.error(`🌐 AI recognition failed: ${error.message}. Trying local recognition...`);
        } else {
          toastManager.error(`📱 Recognition failed (${error.strategy}): ${error.message}`);
        }
      } else {
        toastManager.error('❌ Recognition failed: Please try again or check your image quality');
      }
      
      setRecognizedText('');
      setRecognitionResult(null);
    } finally {
      setIsProcessing(false);
    }
  };

  const handleManualInput = () => {
    if (manualInput.trim()) {
      setRecognizedText(manualInput.trim());
      setFinalReading(manualInput.trim());
      toastManager.manualInputSuccess();
      setManualInput('');
    } else {
      toastManager.invalidInput();
    }
  };

  // Validate reading with comprehensive validation
  const validateReading = async (readingValue: string): Promise<ValidationResult | null> => {
    if (!readingValue || !taskDetail) return null;

    const reading = parseFloat(readingValue);
    if (isNaN(reading)) {
      toastManager.error('Invalid reading value');
      return null;
    }

    console.log('MeterReadingScreen: Validating reading...', reading);

    // Use effective previous reading (actual reading or baseline)
    const effectivePreviousReading = (taskDetail.lastReading && taskDetail.lastReading > 0) 
      ? taskDetail.lastReading 
      : (baselineReading || 0);

    const validationInput = {
      currentReading: reading,
      previousReading: effectivePreviousReading,
      previousReadingDate: taskDetail.lastReadingDate ? new Date(taskDetail.lastReadingDate) : undefined,
      ocrConfidence: recognitionResult?.confidence,
      gpsLocation: currentLocation || undefined,
      meterNumber: taskDetail.meterNumber,
      taskId: taskDetail.id
    };

    try {
      const result = await readingValidationService.validateReading(validationInput);
      console.log('MeterReadingScreen: Validation completed:', result);
      return result;
    } catch (error) {
      console.error('MeterReadingScreen: Validation error:', error);
      toastManager.error('Reading validation failed');
      return null;
    }
  };





  // Save meter reading with validation and sync
  const handleSaveReading = async () => {
    if (!finalReading) {
      toastManager.error('Please enter a reading value');
      return;
    }

    if (!taskDetail || !taskDetail.id) {
      toastManager.error('Task information is missing. Please navigate from a valid task.');
      return;
    }

    if (!currentLocation) {
      toastManager.error('GPS location required. Please get GPS coordinates first.');
      return;
    }

    setIsSavingReading(true);

    try {
      console.log('MeterReadingScreen: Starting save reading process...');

      // Step 1: Validate the reading
      const validation = await validateReading(finalReading);
      if (!validation) {
        setIsSavingReading(false);
        return;
      }

      setValidationResult(validation);

      // Step 2: Check if validation requires review
      if (validation.requiresReview) {
        const shouldContinue = await new Promise<boolean>((resolve) => {
          toastManager.warning(`Reading requires review: ${validation.reviewReason}`);
          
          // Use setTimeout to show confirmation after warning toast
          setTimeout(() => {
            // Here you could use a proper Alert/Dialog component
            // For now, we'll continue with the save
            resolve(true);
          }, 100);
        });

        if (!shouldContinue) {
          setIsSavingReading(false);
          return;
        }
      }

      // Step 3: Prepare reading data
      const readingValue = parseFloat(finalReading);
      const consumption = taskDetail.lastReading ? readingValue - taskDetail.lastReading : undefined;

      const readingData = {
        taskId: taskDetail.id,
        meterId: taskDetail.meterId,
        meterNumber: taskDetail.meterNumber,
        readingValue,
        previousReading: taskDetail.lastReading,
        consumption,
        readingDate: new Date().toISOString(),
        readBy: authState.user?.username || 'mobile_user',
        status: 'Pending',
        
        // GPS and location data
        latitude: currentLocation?.latitude,
        longitude: currentLocation?.longitude,
        gpsAccuracy: currentLocation?.accuracy,
        
        // Photo and OCR data
        hasPhoto: !!selectedImage,
        hasOCR: !!recognitionResult,
        ocrStatus: recognitionResult ? 'processed' : null,
        ocrConfidence: recognitionResult?.confidence,
        photoFilename: selectedImage?.fileName,
        
        // Reading metadata
        notes: '',
        readingType: 'Regular',
        dataSource: 'Mobile App',
        qualityScore: 8,
        readingMethod: (recognitionResult ? 'OCR' : 'Manual') as 'Manual' | 'OCR',
        
        // Sync status
        syncStatus: 'pending'
      };

      // Step 4: Save reading using new sync service
      console.log('MeterReadingScreen: Saving reading with sync service...');

      const meterReadingData = {
        uuid: `reading_${Date.now()}_${generateSecureRandomString(9)}`,
        meter_id: readingData.meterId,
        user_id: parseInt(authState.user?.id || '1'),
        task_id: taskDetail.id,
        reading_value: readingData.readingValue,
        reading_date: readingData.readingDate,
        reading_method: readingData.readingMethod || 'Manual',
        reading_type: 'Regular' as 'Regular' | 'Reactive' | 'Emergency',
        data_source: 'Mobile',
        status: 'Completed',
        has_ocr: readingData.readingMethod === 'OCR',
        ocr_confidence: readingData.ocrConfidence,
        is_validated: false,
        is_anomalous: false,
        cant_read: false,
        notes: readingData.notes,
        has_photo: !!selectedImage,
        photo_count: selectedImage ? 1 : 0,
        is_offline_reading: true,
        latitude: readingData.latitude,
        longitude: readingData.longitude,
        gps_accuracy: readingData.gpsAccuracy,
        location: taskDetail?.location || '',
        created_by: authState.user?.username || 'mobile_user',
        updated_by: authState.user?.username || 'mobile_user'
      };
 
      // Step 4: Prepare photos for saving
      const photosToSave = [];

      // Add OCR photo if exists
      if (selectedImage?.uri) {
        try {
          console.log('MeterReadingScreen: Preparing OCR photo for saving...');
          const compressedUri = await PhotoCaptureService.compressPhoto(selectedImage.uri);

          photosToSave.push({
            localFilePath: compressedUri,
            fileName: selectedImage.fileName || `meter_photo_${Date.now()}.jpg`,
            fileSize: selectedImage.fileSize || 0,
            mimeType: selectedImage.type || 'image/jpeg',
            capturedAt: new Date().toISOString(),
            latitude: currentLocation?.latitude,
            longitude: currentLocation?.longitude,
            photoType: 'meter' as const
          });
        } catch (photoError) {
          console.error('Failed to prepare OCR photo:', photoError);
        }
      }

      // Add captured photos
      photosToSave.push(...capturedPhotos);

      // Step 5: Save reading with photos using ReadingService
      const localReadingId = await ReadingService.saveReadingWithPhotos(
        meterReadingData,
        photosToSave
      );

      if (!localReadingId) {
        throw new Error('Failed to save reading');
      }

      console.log('MeterReadingScreen: Reading and photos saved successfully with ID:', localReadingId);

    // Step 6: Trigger immediate upload using new unified method
    try {
      console.log('MeterReadingScreen: Triggering immediate upload for reading with photos...');
      const uploadSuccess = await MeterReadingSyncService.uploadReadingWithPhotos(localReadingId);
      if (uploadSuccess) {
        console.log('MeterReadingScreen: Immediate upload completed successfully');
      } else {
        console.log('MeterReadingScreen: Upload skipped (no network) or failed - will retry later');
      }
    } catch (uploadError) {
      console.error('MeterReadingScreen: Immediate upload failed:', uploadError);
      // Don't fail the save process if upload fails - it will be retried later
    }

      // Step 8: Show success message
      setReadingSaved(true);
      toastManager.success(`Reading saved successfully! (ID: ${localReadingId})`);

      // Note: Sync is handled automatically by MeterReadingSyncService
      // If network is available, it will sync immediately
      // If not, it will be synced by background service

      // Step 9: Navigate back or show completion UI
      setTimeout(() => {
        navigation.goBack();
      }, 2000);

    } catch (error: any) {
      console.error('MeterReadingScreen: Error saving reading:', error);
      toastManager.error(`Failed to save reading: ${error.message}`);
    } finally {
      setIsSavingReading(false);
    }
  };

  const handleTestAI = async () => {
    try {
      toastManager.info('🧪 Testing AI API connection...');
      
      // Import the test function
      const { testAllEnabledApis } = require('../../config/ai-config');
      const results = await testAllEnabledApis();
      
      let hasSuccess = false;
      let errorMessages: string[] = [];
      
      Object.entries(results).forEach(([service, result]: [string, any]) => {
        if (result.success) {
          hasSuccess = true;
          toastManager.success(`✅ ${service}: ${result.message}`);
        } else {
          errorMessages.push(`❌ ${service}: ${result.message}`);
          console.log(`${service} failed:`, result);
          
          // Provide specific troubleshooting for OpenAI API network issues
          if (service === 'gpt4v' && result.message.includes('Network request failed')) {
            console.log('🔧 OpenAI API Network Troubleshooting:');
            console.log('  1. Check if your network blocks api.openai.com');
            console.log('  2. Try using a different network (mobile hotspot)');
            console.log('  3. Corporate/school networks often block OpenAI');
            console.log('  4. VPN might be required in some regions');
            console.log('  5. Claude works but OpenAI fails = network-specific blocking');
          }
        }
      });
      
      if (!hasSuccess) {
        // Show detailed error for debugging
        errorMessages.forEach(msg => {
          console.log(msg);
        });
        toastManager.error('AI API test failed - will use local recognition');
      } else if (errorMessages.length > 0) {
        toastManager.info('Some AI APIs failed - backup strategies available');
      }
      
    } catch (error) {
      console.error('AI API test error:', error);
      toastManager.error(`AI API test error: ${(error as Error).message}`);
    }
  };

  const handleRemoveImage = () => {
    setSelectedImage(null);
    setProcessedImage(null);
    setRecognizedText(null);
    setProcessingSteps([]);
    setRecognitionResult(null);
  };

  // Render loading screen while task is loading
  if (isLoadingTask) {
    return (
      <Box
        flex={1}
        bg={mode === 'dark' ? 'black' : 'gray.50'}
        justifyContent="center"
        alignItems="center"
      >
        <Spinner color="orange.500" size="lg" />
        <Text fontSize="md" color={mode === 'dark' ? 'white' : 'gray.800'} mt={4}>
          Loading task details...
        </Text>
      </Box>
    );
  }

  return (
    <Box
      bg={mode === 'dark' ? 'black' : 'gray.50'}
      flex={1}
      safeArea
    >
      <ScrollView
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{ flexGrow: 1, paddingBottom: 20 }}
      >
        <VStack px={4} py={4} space={4}>

          {/* Water Meter Information Card */}
          {taskDetail && (
            <Box
              bg={mode === 'dark' ? 'gray.800' : 'white'}
              borderRadius="xl"
              p={4}
              shadow={2}
              borderWidth={1}
              borderColor={mode === 'dark' ? 'gray.700' : 'gray.200'}
            >
              <HStack justifyContent="space-between" alignItems="center" mb={3}>
                <Text fontSize="md" fontWeight="bold" color="blue.500">
                  Water Meter Information
                </Text>
                <Badge
                  colorScheme="blue"
                  variant="solid"
                  borderRadius="lg"
                  _text={{ fontSize: 'xs', fontWeight: 'bold' }}
                >
                  Task #{taskDetail.id}
                </Badge>
              </HStack>
              
              <VStack space={2}>
                <HStack justifyContent="space-between" alignItems="center">
                  <Text fontSize="sm" color={mode === 'dark' ? 'gray.400' : 'gray.600'}>
                    Meter Number
                  </Text>
                  <Text fontSize="lg" fontWeight="bold" color={mode === 'dark' ? 'white' : 'gray.800'}>
                    {taskDetail.meterNumber}
                  </Text>
                </HStack>

                {/* Display reading info - prioritize actual reading over baseline */}
                {taskDetail.lastReading !== undefined && taskDetail.lastReading > 0 ? (
                  <HStack justifyContent="space-between" alignItems="center">
                    <Text fontSize="sm" color={mode === 'dark' ? 'gray.400' : 'gray.600'}>
                      Previous Reading
                    </Text>
                    <Text fontSize="md" fontWeight="bold" color="green.500">
                      {taskDetail.lastReading?.toLocaleString()} L
                    </Text>
                  </HStack>
                ) : baselineReading !== null ? (
                  <HStack justifyContent="space-between" alignItems="center">
                    <VStack alignItems="flex-end">
                      <Text fontSize="sm" color={mode === 'dark' ? 'gray.400' : 'gray.600'}>
                        Baseline Reading
                      </Text>
                      <Text fontSize="xs" color="orange.500" italic>
                        (No previous reading)
                      </Text>
                    </VStack>
                    <Text fontSize="md" fontWeight="bold" color="orange.500">
                      {baselineReading.toLocaleString()} L
                    </Text>
                  </HStack>
                ) : (
                  <HStack justifyContent="space-between" alignItems="center">
                    <Text fontSize="sm" color={mode === 'dark' ? 'gray.400' : 'gray.600'}>
                      Previous Reading
                    </Text>
                    <Text fontSize="md" color="red.500" italic>
                      No reading available
                    </Text>
                  </HStack>
                )}

                {taskDetail.address && (
                  <HStack justifyContent="space-between" alignItems="flex-start">
                    <Text fontSize="sm" color={mode === 'dark' ? 'gray.400' : 'gray.600'}>
                      Address
                    </Text>
                    <Text fontSize="sm" color={mode === 'dark' ? 'gray.300' : 'gray.700'} flex={1} textAlign="right">
                      {taskDetail.address}
                    </Text>
                  </HStack>
                )}

                {/* GPS Location Row */}
                <HStack justifyContent="space-between" alignItems="center">
                  <Text fontSize="sm" color={mode === 'dark' ? 'gray.400' : 'gray.600'}>
                    GPS Location
                  </Text>
                  <HStack space={2} alignItems="center">
                    {currentLocation ? (
                      <Text fontSize="sm" color={mode === 'dark' ? 'gray.300' : 'gray.700'}>
                        {currentLocation.latitude.toFixed(6)}, {currentLocation.longitude.toFixed(6)}
                      </Text>
                    ) : (
                      <Text fontSize="sm" color="gray.500">
                        Not available
                      </Text>
                    )}
                    <Button
                      onPress={getCurrentLocation}
                      size="xs"
                      bg="purple.500"
                      _pressed={{ bg: "purple.600" }}
                      isLoading={isGettingLocation}
                      isLoadingText="Getting..."
                      leftIcon={<IconWrapper name="map-marker-radius" size={3} color="white" label="GPS" />}
                      px={2}
                      py={1}
                    >
                      <Text color="white" fontSize="xs">
                        Get GPS
                      </Text>
                    </Button>
                  </HStack>
                </HStack>
              </VStack>
            </Box>
          )}

          {/* Photo Capture Area */}
          <Box
            width="100%"
            height={200}
            borderWidth={2}
            borderColor={mode === 'dark' ? 'gray.600' : 'gray.300'}
            borderStyle="dashed"
            borderRadius="xl"
            bg={mode === 'dark' ? 'gray.800' : 'gray.50'}
            justifyContent="center"
            alignItems="center"
            p={4}
          >
            {selectedImage ? (
              <Box position="relative" width="100%" height="100%" alignItems="center">
                <Image
                  source={{ uri: selectedImage.uri }}
                  alt="Meter Photo"
                  width="100%"
                  height="85%"
                  resizeMode="contain"
                  borderRadius="lg"
                />
                <Text mt={1} fontSize="xs" color={mode === 'dark' ? 'gray.400' : 'gray.600'} numberOfLines={1}>
                  {selectedImage.fileName || 'Meter Photo'}
                </Text>
                <Pressable
                  position="absolute"
                  top={2}
                  right={2}
                  onPress={handleRemoveImage}
                  bg="red.500"
                  borderRadius="full"
                  p={1}
                  zIndex={1}
                >
                  <IconWrapper name="close" size={4} color="white" label="Remove image" />
                </Pressable>
              </Box>
            ) : (
              <VStack space={4} alignItems="center" width="100%">
                <IconWrapper 
                  name="camera" 
                  size={16} 
                  color={mode === 'dark' ? 'gray.500' : 'gray.400'}
                  label="Camera placeholder"
                />
                <Text 
                  fontSize="md" 
                  color={mode === 'dark' ? 'gray.400' : 'gray.500'}
                  textAlign="center"
                >
                  Take or choose meter photo
                </Text>
              </VStack>
            )}
          </Box>

          {/* Camera Action Buttons */}
          <HStack space={3} justifyContent="center">
            <Button
              onPress={handleTakePhoto}
              bg="blue.500"
              _pressed={{ bg: "blue.600" }}
              leftIcon={isCapturingPhoto ?
                <Spinner color="white" size="sm" /> :
                <IconWrapper name="camera" size={5} color="white" label="Camera" />
              }
              flex={1}
              height={12}
              borderRadius="lg"
              isDisabled={isCapturingPhoto}
            >
              <Text color="white" fontWeight="bold">
                {isCapturingPhoto ? 'Capturing...' : 'Take Photo'}
              </Text>
            </Button>
            
            <Button
              onPress={handleChoosePhoto}
              bg="green.500"
              _pressed={{ bg: "green.600" }}
              leftIcon={<IconWrapper name="image" size={5} color="white" label="Gallery" />}
              flex={1}
              height={12}
              borderRadius="lg"
            >
              <Text color="white" fontWeight="bold">Choose Photo</Text>
            </Button>
          </HStack>

          {/* Captured Photos Preview */}
          <PhotoPreviewComponent
            photos={capturedPhotos}
            uploadProgress={{}}
            onPhotoPress={(photo) => {
              console.log('Photo pressed:', photo.filename);
            }}
          />

          {/* OCR Recognition Section */}
          {selectedImage && (
            <VStack space={3}>
              <Divider />
              
              {/* OCR System Status */}
              <HStack space={2} alignItems="center" justifyContent="center">
                <Box
                  w={3}
                  h={3}
                  borderRadius="full"
                  bg={opencvReady ? "green.500" : "orange.400"}
                />
                <Text fontSize="sm" color={mode === 'dark' ? 'gray.400' : 'gray.600'}>
                  OCR System: {opencvReady ? "AI + OpenCV + ML Kit Ready" : "ML Kit Ready (Loading OpenCV...)"}
                </Text>
              </HStack>

              {/* Recognition Button */}
              <Button
                onPress={handleRecognize}
                isDisabled={isProcessing}
                bg={isProcessing ? "gray.400" : "orange.500"}
                _pressed={{ bg: isProcessing ? "gray.400" : "orange.600" }}
                _disabled={{ bg: "gray.400" }}
                leftIcon={
                  isProcessing ? 
                    <Spinner color="white" size="sm" /> : 
                    <IconWrapper name="camera" size={4} color="white" label="Recognition" />
                }
                height={12}
                borderRadius="xl"
                shadow={2}
                px={6}
                py={3}
              >
                <Text color="white" fontWeight="bold" fontSize="sm">
                  {isProcessing ? "Processing..." : "Recognize Reading"}
                </Text>
              </Button>

              {/* AI API Test Button */}
              <Button
                onPress={handleTestAI}
                variant="outline"
                borderColor="blue.400"
                _text={{ color: "blue.400" }}
                height={10}
                borderRadius="lg"
              >
                <Text fontSize="sm">Test AI Connection</Text>
              </Button>
            </VStack>
          )}

          {/* OCR Results Card */}
          {recognitionResult && (
            <Box
              bg={mode === 'dark' ? 'gray.800' : 'white'}
              borderRadius="xl"
              p={4}
              shadow={2}
              borderWidth={1}
              borderColor={mode === 'dark' ? 'gray.700' : 'gray.200'}
            >
              <HStack justifyContent="space-between" alignItems="center" mb={3}>
                <Text fontSize="md" fontWeight="bold" color="green.500">
                  OCR Recognition Results
                </Text>
                <Badge
                  colorScheme={recognitionResult.confidence > 0.8 ? "green" : 
                               recognitionResult.confidence > 0.6 ? "orange" : "red"}
                  variant="solid"
                  borderRadius="lg"
                  _text={{ fontSize: 'xs', fontWeight: 'bold' }}
                >
                  {Math.round(recognitionResult.confidence * 100)}% confidence
                </Badge>
              </HStack>
              
              <VStack space={3}>
                <HStack justifyContent="space-between" alignItems="center">
                  <Text fontSize="sm" color={mode === 'dark' ? 'gray.400' : 'gray.600'}>
                    Detected Reading
                  </Text>
                  <Text fontSize="xl" fontWeight="bold" color={mode === 'dark' ? 'white' : 'gray.800'}>
                    {recognitionResult.reading || 'No reading detected'}
                  </Text>
                </HStack>

                <HStack justifyContent="space-between" alignItems="center">
                  <Text fontSize="sm" color={mode === 'dark' ? 'gray.400' : 'gray.600'}>
                    Recognition Method
                  </Text>
                  <Text fontSize="sm" color="blue.500" fontWeight="medium">
                    {recognitionResult.strategy === 'AIApi' ? '🤖 AI-Powered' : 
                     recognitionResult.strategy === 'OpenCV' ? '🔧 OpenCV' : 
                     recognitionResult.strategy === 'MLKit' ? '📱 ML Kit' : 
                     recognitionResult.strategy}
                  </Text>
                </HStack>

                {currentLocation && (
                  <HStack justifyContent="space-between" alignItems="center">
                    <Text fontSize="sm" color={mode === 'dark' ? 'gray.400' : 'gray.600'}>
                      GPS Coordinates
                    </Text>
                    <Text fontSize="xs" color={mode === 'dark' ? 'gray.300' : 'gray.700'}>
                      {currentLocation.latitude.toFixed(6)}, {currentLocation.longitude.toFixed(6)}
                    </Text>
                  </HStack>
                )}
              </VStack>
            </Box>
          )}

          {/* Validation Results Card */}
          {validationResult && (
            <Box
              bg={mode === 'dark' ? 'gray.800' : 'white'}
              borderRadius="xl"
              p={4}
              shadow={2}
              borderWidth={1}
              borderColor={mode === 'dark' ? 'gray.700' : 'gray.200'}
            >
              <HStack justifyContent="space-between" alignItems="center" mb={3}>
                <Text fontSize="md" fontWeight="bold" color="orange.500">
                  Reading Validation
                </Text>
                <Badge
                  colorScheme={validationResult.validationStatus === 'passed' ? "green" : 
                               validationResult.validationStatus === 'warning' ? "orange" : 
                               validationResult.validationStatus === 'requires_review' ? "purple" : "red"}
                  variant="solid"
                  borderRadius="lg"
                  _text={{ fontSize: 'xs', fontWeight: 'bold' }}
                >
                  {validationResult.validationScore}/100
                </Badge>
              </HStack>
              
              <VStack space={2}>
                <HStack justifyContent="space-between" alignItems="center">
                  <Text fontSize="sm" color={mode === 'dark' ? 'gray.400' : 'gray.600'}>
                    Status
                  </Text>
                  <Text fontSize="sm" fontWeight="medium" color={
                    validationResult.validationStatus === 'passed' ? 'green.500' : 
                    validationResult.validationStatus === 'warning' ? 'orange.500' : 
                    validationResult.validationStatus === 'requires_review' ? 'purple.500' : 'red.500'
                  }>
                    {validationResult.validationStatus.replace('_', ' ').toUpperCase()}
                  </Text>
                </HStack>

                {validationResult.warnings.length > 0 && (
                  <VStack space={1}>
                    <Text fontSize="sm" color={mode === 'dark' ? 'gray.400' : 'gray.600'}>
                      Validation Messages:
                    </Text>
                    {validationResult.warnings.slice(0, 3).map((warning, index) => (
                      <HStack key={index} space={2} alignItems="flex-start">
                        <Box mt={0.5}>
                          <IconWrapper 
                            name={warning.type === 'error' ? 'alert' :
                                  warning.type === 'warning' ? 'alert' : 'information'}
                            size={3} 
                            color={warning.type === 'error' ? 'red.500' : 
                                   warning.type === 'warning' ? 'orange.500' : 'blue.500'} 
                            label={warning.type} 
                          />
                        </Box>
                        <Text
                          color={mode === 'dark' ? 'gray.300' : 'gray.700'}
                          fontSize="xs"
                          flex={1}
                        >
                          {warning.message}
                        </Text>
                      </HStack>
                    ))}
                  </VStack>
                )}

                {validationResult.isAnomaly && (
                  <HStack space={2} alignItems="center">
                    <IconWrapper name="alert" size={4} color="red.500" label="Anomaly" />
                    <Text fontSize="sm" color="red.500" fontWeight="medium">
                      Anomaly Detected: {validationResult.anomalyType}
                    </Text>
                  </HStack>
                )}
              </VStack>
            </Box>
          )}

          {/* Manual Input Section */}
          <Box
            bg={mode === 'dark' ? 'gray.800' : 'white'}
            borderRadius="xl"
            p={4}
            shadow={2}
            borderWidth={1}
            borderColor={mode === 'dark' ? 'gray.700' : 'gray.200'}
          >
            <Text fontSize="md" fontWeight="bold" color="yellow.600" mb={3}>
              Manual Reading Input
            </Text>
            
            <VStack space={3}>
              <Input
                placeholder="Enter meter reading manually"
                value={manualInput}
                onChangeText={setManualInput}
                keyboardType="numeric"
                fontSize="md"
                bg={mode === 'dark' ? 'gray.700' : 'gray.50'}
                borderColor={mode === 'dark' ? 'gray.600' : 'gray.300'}
                color={mode === 'dark' ? 'white' : 'gray.800'}
                _focus={{
                  borderColor: "yellow.500",
                  bg: mode === 'dark' ? 'gray.700' : 'white'
                }}
              />
              
              <HStack space={2}>
                <Button
                  onPress={handleManualInput}
                  bg="yellow.500"
                  _pressed={{ bg: "yellow.600" }}
                  isDisabled={!manualInput.trim()}
                  leftIcon={<IconWrapper name="pencil" size={4} color="white" label="Manual input" />}
                  flex={1}
                >
                  <Text color="white" fontWeight="bold">
                    Use Manual Input
                  </Text>
                </Button>

                <Button
                  onPress={() => validateReading(manualInput)}
                  variant="outline"
                  borderColor="yellow.500"
                  _text={{ color: "yellow.500" }}
                  isDisabled={!manualInput.trim()}
                  leftIcon={<IconWrapper name="check-circle" size={4} color="yellow.500" label="Validate" />}
                  flex={1}
                >
                  <Text fontSize="sm">Validate</Text>
                </Button>
              </HStack>
            </VStack>
          </Box>

          {/* Processing Steps */}
          {processingSteps.length > 0 && (
            <Box
              bg={mode === 'dark' ? 'gray.800' : 'white'}
              borderRadius="xl"
              p={4}
              shadow={2}
              borderWidth={1}
              borderColor={mode === 'dark' ? 'gray.700' : 'gray.200'}
            >
              <Text fontSize="md" fontWeight="bold" color="blue.500" mb={3}>
                Processing Steps
              </Text>
              
              <VStack space={1}>
                {processingSteps.map((step, index) => (
                  <HStack key={index} space={2} alignItems="flex-start">
                    <Box mt={0.5}>
                      <IconWrapper name="check" size={3} color="green.500" label="Done" />
                    </Box>
                    <Text
                      color={mode === 'dark' ? 'gray.300' : 'gray.700'}
                      fontSize="sm"
                      flex={1}
                      lineHeight="sm"
                    >
                      {step}
                    </Text>
                  </HStack>
                ))}
              </VStack>
            </Box>
          )}

          {/* Processed Image Preview */}
          {processedImage && (
            <Box
              bg={mode === 'dark' ? 'gray.800' : 'white'}
              borderRadius="xl"
              p={4}
              shadow={2}
              borderWidth={1}
              borderColor={mode === 'dark' ? 'gray.700' : 'gray.200'}
            >
              <Text fontSize="md" fontWeight="bold" color="purple.500" mb={3}>
                Processed Image
              </Text>
              <Image
                source={{ uri: processedImage.uri }}
                alt="Processed Meter Photo"
                width="100%"
                height={200}
                resizeMode="contain"
                borderRadius="lg"
              />
            </Box>
          )}

          {/* Final Reading and Save Section */}
          {(finalReading || recognizedText) && (
            <Box
              bg={mode === 'dark' ? 'gray.800' : 'white'}
              borderRadius="xl"
              p={4}
              shadow={2}
              borderWidth={1}
              borderColor={mode === 'dark' ? 'gray.700' : 'gray.200'}
            >
              <Text fontSize="md" fontWeight="bold" color="green.500" mb={3}>
                Final Reading & Save
              </Text>
              
              <VStack space={4}>
                <HStack justifyContent="space-between" alignItems="center">
                  <Text fontSize="sm" color={mode === 'dark' ? 'gray.400' : 'gray.600'}>
                    Reading Value
                  </Text>
                  <Text fontSize="xl" fontWeight="bold" color={mode === 'dark' ? 'white' : 'gray.800'}>
                    {finalReading || recognizedText} L
                  </Text>
                </HStack>

                {/* Show consumption calculation using effective previous reading */}
                {(finalReading || recognizedText) && (
                  (() => {
                    const currentReading = parseFloat(finalReading || recognizedText || '0');
                    const effectivePreviousReading = (taskDetail?.lastReading && taskDetail.lastReading > 0) 
                      ? taskDetail.lastReading 
                      : (baselineReading || 0);
                    
                    if (effectivePreviousReading > 0) {
                      const consumption = currentReading - effectivePreviousReading;
                      const isBaseline = !taskDetail?.lastReading || taskDetail.lastReading === 0;
                      
                      return (
                        <HStack justifyContent="space-between" alignItems="center">
                          <VStack alignItems="flex-start">
                            <Text fontSize="sm" color={mode === 'dark' ? 'gray.400' : 'gray.600'}>
                              Consumption
                            </Text>
                            {isBaseline && (
                              <Text fontSize="xs" color="orange.500" italic>
                                (vs baseline)
                              </Text>
                            )}
                          </VStack>
                          <Text fontSize="md" fontWeight="bold" color={isBaseline ? "orange.500" : "blue.500"}>
                            {consumption.toLocaleString()} L
                          </Text>
                        </HStack>
                      );
                    }
                    return null;
                  })()
                )}

                {currentLocation && (
                  <HStack justifyContent="space-between" alignItems="center">
                    <Text fontSize="sm" color={mode === 'dark' ? 'gray.400' : 'gray.600'}>
                      GPS Status
                    </Text>
                    <HStack space={2} alignItems="center">
                      <Box
                        w={2}
                        h={2}
                        borderRadius="full"
                        bg={locationValidation?.warningLevel === 'low' ? 'green.500' : 
                            locationValidation?.warningLevel === 'medium' ? 'orange.500' : 'red.500'}
                      />
                      <Text fontSize="sm" color={mode === 'dark' ? 'gray.300' : 'gray.700'}>
                        {currentLocation.accuracy.toFixed(1)}m accuracy
                      </Text>
                    </HStack>
                  </HStack>
                )}

                <Divider />

                <Button
                  onPress={handleSaveReading}
                  bg={readingSaved ? "green.500" : "blue.500"}
                  _pressed={{ bg: readingSaved ? "green.600" : "blue.600" }}
                  isLoading={isSavingReading}
                  isLoadingText="Saving..."
                  isDisabled={readingSaved || !currentLocation}
                  leftIcon={
                    readingSaved ?
                      <IconWrapper name="check-circle" size={5} color="white" label="Saved" /> :
                      <IconWrapper name="content-save" size={5} color="white" label="Save" />
                  }
                  width="100%"
                  height={12}
                  borderRadius="lg"
                >
                  <Text color="white" fontWeight="bold" fontSize="md">
                    {readingSaved ? "Reading Saved Successfully!" : "Save Meter Reading"}
                  </Text>
                </Button>

                {!currentLocation && (
                  <Text fontSize="xs" color="red.500" textAlign="center">
                    GPS location required before saving. Please get GPS coordinates first.
                  </Text>
                )}
              </VStack>
            </Box>
          )}

        </VStack>
      </ScrollView>
      
      {/* OpenCV Processor Component */}
      <OpenCVProcessor
        ref={opencvProcessorRef}
        onReady={handleOpenCVReady}
        onError={handleOpenCVError}
      />
    </Box>
  );
};

export default MeterReadingScreen;