import React, { useState, useEffect } from 'react';
import { 
  Box, 
  VStack, 
  HStack, 
  Text, 
  Button, 
  ScrollView,
  Progress,
  Spinner,
  Divider,
  Pressable,
} from 'native-base';
import { useTheme } from '../../styles/ThemeContext';
import { IconWrapper } from '../../components/common';
import { useToastManager } from '../../utils/ToastManager';
import { 
  BasicDataSyncService, 
  TaskDataSyncService, 
  BusinessDataSyncService 
} from '../../services/sync';
import { BaselineService } from '../../services';
import { SyncResult, UploadResult } from '../../types/SyncTypes';
import SyncMonitor from '../../components/sync/SyncMonitor';

const SyncScreen: React.FC = () => {
  const { mode } = useTheme();
  const toastManager = useToastManager();
  const [isSyncing, setIsSyncing] = useState(false);
  const [lastSyncTime, setLastSyncTime] = useState('Never');
  const [syncProgress, setSyncProgress] = useState(0);
  const [isBasicDataSyncing, setIsBasicDataSyncing] = useState(false);
  const [isTaskDataSyncing, setIsTaskDataSyncing] = useState(false);
  const [isBusinessDataUploading, setIsBusinessDataUploading] = useState(false);
  const [isBaselineSyncing, setIsBaselineSyncing] = useState(false);
  const [isLoadingStats, setIsLoadingStats] = useState(true);
  const [syncStats, setSyncStats] = useState({
    pendingUploads: 0,
    pendingDownloads: 0,
    totalTasks: 0,
    completedTasks: 0,
    totalWaterMeters: 0,
    totalUsers: 0,
    totalBaselines: 0,
  });

  // Load statistics on component mount
  useEffect(() => {
    loadSyncStatistics();
  }, []);

  const loadSyncStatistics = async () => {
    try {
      setIsLoadingStats(true);
      const [basicStats, taskStats, businessStats, baselineStats] = await Promise.all([
        BasicDataSyncService.getBasicDataStatistics(),
        TaskDataSyncService.getTaskStatistics(),
        BusinessDataSyncService.getUploadSummary(),
        BaselineService.getBaselineStatistics()
      ]);

      setSyncStats({
        pendingUploads: (businessStats?.pendingReadings || 0) + (businessStats?.pendingPhotos || 0) + (businessStats?.pendingTaskUpdates || 0),
        pendingDownloads: 0, // Will be calculated based on server comparison
        totalTasks: taskStats?.totalTasks || 0,
        completedTasks: taskStats?.completedTasks || 0,
        totalWaterMeters: basicStats?.waterMeters?.totalMeters || 0,
        totalUsers: basicStats?.users?.totalUsers || 0,
        totalBaselines: baselineStats?.totalRecords || 0,
      });

      if (businessStats?.lastUploadTime) {
        setLastSyncTime(new Date(businessStats.lastUploadTime).toLocaleString());
      }
    } catch (error) {
      console.error('Error loading sync statistics:', error);
      // Set safe default values on error
      setSyncStats({
        pendingUploads: 0,
        pendingDownloads: 0,
        totalTasks: 0,
        completedTasks: 0,
        totalWaterMeters: 0,
        totalUsers: 0,
        totalBaselines: 0,
      });
    } finally {
      setIsLoadingStats(false);
    }
  };



  const handleFullSync = async () => {
    setIsSyncing(true);
    setSyncProgress(0);
    
    try {
      // Step 1: Basic Data Sync (25%)
      setSyncProgress(10);
      const basicResults = await BasicDataSyncService.syncAllBasicData();
      setSyncProgress(25);
      
      // Step 2: Baseline Sync (40%)
      console.log('🔄 Starting baseline sync...');
      const baselineResult = await BaselineService.fetchAndSaveBaselines();
      console.log('✅ Baseline sync result:', baselineResult);
      setSyncProgress(40);
      
      // Step 3: Task Data Sync (65%)
      const taskResult = await TaskDataSyncService.syncTasks(10);
      setSyncProgress(65);
      
      // Step 4: Business Data Upload (85%)
      const uploadResults = await BusinessDataSyncService.uploadNow();
      setSyncProgress(85);
      
      await loadSyncStatistics();
      setSyncProgress(100);
      
      setLastSyncTime(new Date().toLocaleString());
      
      // Check results and show appropriate message
      const basicSuccessCount = basicResults.filter(r => r.success).length;
      const uploadSuccessCount = uploadResults.filter(r => r.success).length;
      
      if (basicSuccessCount > 0 || baselineResult.success || taskResult.success || uploadSuccessCount > 0) {
        toastManager.syncSuccess();
      } else {
        toastManager.syncError();
      }
      
    } catch (error) {
      console.error('Full sync error:', error);
      toastManager.syncError();
    } finally {
      setIsSyncing(false);
      setSyncProgress(0);
    }
  };

  const handleBasicDataSync = async () => {
    setIsBasicDataSyncing(true);
    
    try {
      const results = await BasicDataSyncService.syncAllBasicData();
      await loadSyncStatistics();
      
      const successCount = results.filter(r => r.success).length;
      if (successCount > 0) {
        toastManager.success(`Successfully synced ${successCount} basic data types`);
      } else {
        toastManager.error('Basic data sync failed');
      }
    } catch (error) {
      console.error('Basic data sync error:', error);
      toastManager.error('Basic data sync failed');
    } finally {
      setIsBasicDataSyncing(false);
    }
  };

  const handleBaselineSync = async () => {
    setIsBaselineSyncing(true);
    
    try {
      console.log('🔄 BaselineSync: Starting baseline synchronization...');
      const result = await BaselineService.fetchAndSaveBaselines();
      console.log('✅ BaselineSync: Sync completed:', result);
      
      await loadSyncStatistics();
      
      if (result.success) {
        toastManager.success(`Successfully synced ${result.count} baseline records`);
        console.log(`✅ BaselineSync: Successfully synced ${result.count} baseline records`);
      } else {
        toastManager.error(`Baseline sync failed: ${result.message}`);
        console.error('❌ BaselineSync: Failed:', result.message);
      }
    } catch (error) {
      console.error('❌ BaselineSync: Error:', error);
      toastManager.error('Baseline sync failed');
    } finally {
      setIsBaselineSyncing(false);
    }
  };

  const handleTaskDataSync = async () => {
    setIsTaskDataSyncing(true);
    
    try {
      const result = await TaskDataSyncService.syncTasks(10);
      await loadSyncStatistics();
      
      if (result.success) {
        toastManager.success(`Successfully synced ${result.recordCount} tasks`);
      } else {
        toastManager.error('Task data sync failed');
      }
    } catch (error) {
      console.error('Task data sync error:', error);
      toastManager.error('Task data sync failed');
    } finally {
      setIsTaskDataSyncing(false);
    }
  };

  const handleBusinessDataUpload = async () => {
    setIsBusinessDataUploading(true);
    
    try {
      const results = await BusinessDataSyncService.uploadNow();
      await loadSyncStatistics();
      
      const successCount = results.filter(r => r.success).length;
      if (successCount > 0) {
        toastManager.success(`Successfully uploaded ${successCount} items`);
        setLastSyncTime(new Date().toLocaleString());
      } else {
        toastManager.error('Business data upload failed');
      }
    } catch (error) {
      console.error('Business data upload error:', error);
      toastManager.error('Business data upload failed');
    } finally {
      setIsBusinessDataUploading(false);
    }
  };

  // Remove hardcoded syncStats - now using dynamic state

  const StatCard = ({ icon, title, value, color }: { 
    icon: string; 
    title: string; 
    value: number | string; 
    color: string;
  }) => (
    <Box
      bg={mode === 'dark' ? 'gray.800' : 'white'}
      borderRadius="xl"
      p={3}
      shadow={1}
      borderWidth={1}
      borderColor={mode === 'dark' ? 'gray.700' : 'gray.100'}
      flex={1}
      alignItems="center"
    >
      <IconWrapper name={icon} size={5} color={color} label={title} />
      <Text 
        fontSize="xl" 
        fontWeight="bold" 
        color={color}
        mt={1}
      >
        {value ?? 0}
      </Text>
      <Text 
        fontSize="xs" 
        color={mode === 'dark' ? 'gray.400' : 'gray.600'}
        textAlign="center"
        mt={1}
      >
        {title}
      </Text>
    </Box>
  );

  const SyncOption = ({ icon, title, subtitle, onPress, isLoading = false }: {
    icon: string;
    title: string;
    subtitle: string;
    onPress: () => void;
    isLoading?: boolean;
  }) => (
    <Pressable
      p={4}
      _pressed={{ bg: mode === 'dark' ? 'gray.700' : 'gray.100' }}
      onPress={onPress}
      isDisabled={isLoading}
      opacity={isLoading ? 0.7 : 1}
    >
      <HStack alignItems="center" space={3} width="100%">
        {isLoading ? (
          <Spinner color="orange.500" size="sm" />
        ) : (
          <IconWrapper name={icon} size={5} color="orange.500" label={title} />
        )}
        <VStack flex={1} alignItems="flex-start">
          <Text fontSize="md" fontWeight="medium" color={mode === 'dark' ? 'white' : 'gray.800'}>
            {title}
          </Text>
          <Text fontSize="sm" color={mode === 'dark' ? 'gray.400' : 'gray.600'}>
            {subtitle}
          </Text>
        </VStack>
      </HStack>
    </Pressable>
  );

  if (isLoadingStats) {
    return (
      <Box
        flex={1}
        bg={mode === 'dark' ? 'black' : 'gray.50'}
        safeArea
        justifyContent="center"
        alignItems="center"
      >
        <Spinner size="lg" color="orange.500" />
        <Text fontSize="md" color={mode === 'dark' ? 'white' : 'gray.800'} mt={3}>
          Loading sync data...
        </Text>
      </Box>
    );
  }

  return (
    <Box
      flex={1}
      bg={mode === 'dark' ? 'black' : 'gray.50'}
      safeArea
    >
      <ScrollView flex={1} p={4}>
        <VStack space={4}>
          {/* Header */}
          <Box>
            <HStack alignItems="center" space={3} mb={2}>
              <IconWrapper name="sync" size={6} color="orange.500" label="Sync" />
              <VStack>
                <Text fontSize="xl" fontWeight="bold" color={mode === 'dark' ? 'white' : 'gray.800'}>
                  Data Sync
                </Text>
                <Text fontSize="sm" color={mode === 'dark' ? 'gray.400' : 'gray.600'}>
                  Synchronize your data with server
                </Text>
              </VStack>
            </HStack>
          </Box>

          {/* Sync Status */}
          <Box
            bg={mode === 'dark' ? 'gray.800' : 'white'}
            borderRadius="xl"
            p={4}
            shadow={1}
            borderWidth={1}
            borderColor={mode === 'dark' ? 'gray.700' : 'gray.100'}
          >
            <HStack alignItems="center" space={3} mb={3}>
              <IconWrapper 
                name={isSyncing ? "sync" : "check-circle"} 
                size={5} 
                color={isSyncing ? "orange.500" : "green.500"} 
                label="Status" 
              />
              <VStack flex={1}>
                <Text fontSize="md" fontWeight="medium" color={mode === 'dark' ? 'white' : 'gray.800'}>
                  {isSyncing ? 'Syncing in progress...' : 'Ready to sync'}
                </Text>
                <Text fontSize="sm" color={mode === 'dark' ? 'gray.400' : 'gray.600'}>
                  Last sync: {lastSyncTime}
                </Text>
              </VStack>
            </HStack>

            {isSyncing && (
              <Box mb={3}>
                <Progress
                  value={syncProgress}
                  bg="gray.200"
                  _filledTrack={{
                    bg: "orange.500"
                  }}
                  size="sm"
                />
                <Text fontSize="xs" color={mode === 'dark' ? 'gray.400' : 'gray.600'} mt={1}>
                  {syncProgress}% complete
                </Text>
              </Box>
            )}

            <VStack space={3}>
              <HStack space={3}>
                <StatCard
                  icon="clipboard-list"
                  title="Tasks"
                  value={syncStats.totalTasks}
                  color="green.500"
                />
                <StatCard
                  icon="upload"
                  title="Pending"
                  value={syncStats.pendingUploads}
                  color="orange.500"
                />
              </HStack>
              
              <HStack space={3}>
                <StatCard
                  icon="database"
                  title="Water Meters"
                  value={syncStats.totalWaterMeters}
                  color="blue.500"
                />
                <StatCard
                  icon="account"
                  title="Users"
                  value={syncStats.totalUsers}
                  color="purple.500"
                />
              </HStack>

              <HStack space={3}>
                <StatCard
                  icon="trending-up"
                  title="Baselines"
                  value={syncStats.totalBaselines}
                  color="cyan.500"
                />
                <StatCard
                  icon="chart-line"
                  title="Completed"
                  value={syncStats.completedTasks}
                  color="teal.500"
                />
              </HStack>
            </VStack>
          </Box>

          {/* Sync Monitor */}
          <SyncMonitor showDetails={true} />

          {/* Main Sync Button */}
          <Button
            onPress={handleFullSync}
            isDisabled={isSyncing}
            bg={isSyncing ? "gray.400" : "orange.500"}
            _pressed={{ bg: isSyncing ? "gray.400" : "orange.600" }}
            size="lg"
            borderRadius="xl"
            leftIcon={
              isSyncing ? 
                <Spinner color="white" size="sm" /> : 
                <IconWrapper name="sync" size={5} color="white" label="Sync" />
            }
          >
            <Text color="white" fontSize="md" fontWeight="bold">
              {isSyncing ? 'Syncing Data...' : 'Start Full Sync'}
            </Text>
          </Button>

          {/* Sync Options */}
          <Box
            bg={mode === 'dark' ? 'gray.800' : 'white'}
            borderRadius="xl"
            shadow={1}
            borderWidth={1}
            borderColor={mode === 'dark' ? 'gray.700' : 'gray.100'}
            overflow="hidden"
          >
            <Box p={4} pb={2}>
              <Text fontSize="lg" fontWeight="bold" color={mode === 'dark' ? 'white' : 'gray.800'}>
                Sync Options
              </Text>
            </Box>
            
            <VStack divider={<Divider />}>
              <SyncOption
                icon="database"
                title="Sync Basic Data"
                subtitle={`Water meters, users (${syncStats.totalWaterMeters} meters, ${syncStats.totalUsers} users)`}
                onPress={handleBasicDataSync}
                isLoading={isBasicDataSyncing}
              />
              <SyncOption
                icon="trending-up"
                title="Sync Baseline Data"
                subtitle={`Meter baseline records (${syncStats.totalBaselines} baselines)`}
                onPress={handleBaselineSync}
                isLoading={isBaselineSyncing}
              />
              <SyncOption
                icon="clipboard-list"
                title="Sync Task Data"
                subtitle={`Download all tasks (${syncStats.totalTasks} total)`}
                onPress={handleTaskDataSync}
                isLoading={isTaskDataSyncing}
              />
              <SyncOption
                icon="upload"
                title="Upload Business Data"
                subtitle={`Upload readings and photos (${syncStats.pendingUploads} pending)`}
                onPress={handleBusinessDataUpload}
                isLoading={isBusinessDataUploading}
              />
            </VStack>
          </Box>
        </VStack>
      </ScrollView>
    </Box>
  );
};

export default SyncScreen; 