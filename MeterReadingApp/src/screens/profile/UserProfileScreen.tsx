import React from 'react';
import { 
  Box, 
  VStack, 
  HStack, 
  Text, 
  ScrollView, 
  Button,
  Avatar,
  Divider,
  AlertDialog,
  Pressable,
} from 'native-base';
import { useAuth } from '../../context/AuthContext';
import { useTheme } from '../../styles/ThemeContext';
import { IconWrapper } from '../../components/common';
import { useToastManager } from '../../utils/ToastManager';
import { useRef, useState } from 'react';

const UserProfileScreen: React.FC = () => {
  const { state, logout } = useAuth();
  const { mode } = useTheme();
  const toastManager = useToastManager();
  const [isLogoutOpen, setIsLogoutOpen] = useState(false);
  const cancelRef = useRef(null);

  const handleLogout = () => {
    setIsLogoutOpen(false);
    logout();
    toastManager.info('You have been logged out', 'Goodbye');
  };

  const profileData = [
    { 
      label: 'Username', 
      value: state.user?.username || 'N/A',
      icon: 'account'
    },
    { 
      label: 'Full Name', 
      value: state.user?.fullName || 'N/A',
      icon: 'account'
    },
    { 
      label: 'Person ID', 
      value: state.user?.personId?.toString() || 'N/A',
      icon: 'identifier'
    },
    { 
      label: 'Company Code', 
      value: state.user?.finCoCode || 'N/A',
      icon: 'office-building'
    },
    { 
      label: 'Last Login', 
      value: state.user?.lastLogin || 'N/A',
      icon: 'clock-time-four'
    },
  ];

  const InfoRow = ({ icon, label, value }: { icon: string; label: string; value: string }) => (
    <HStack alignItems="center" space={3} py={3}>
      <Box
        bg={mode === 'dark' ? 'gray.700' : 'gray.100'}
        borderRadius="lg"
        p={2}
      >
        <IconWrapper name={icon} size={4} color="orange.500" label={label} />
      </Box>
      <VStack flex={1} space={1}>
        <Text fontSize="sm" color={mode === 'dark' ? 'gray.400' : 'gray.600'}>
          {label}
        </Text>
        <Text fontSize="md" color={mode === 'dark' ? 'white' : 'gray.800'} fontWeight="medium">
          {value}
        </Text>
      </VStack>
    </HStack>
  );

  const ActionButton = ({ icon, title, subtitle, color, onPress }: {
    icon: string;
    title: string;
    subtitle: string;
    color: string;
    onPress: () => void;
  }) => (
    <Pressable
      onPress={onPress}
      _pressed={{ bg: mode === 'dark' ? 'gray.700' : 'gray.100' }}
      p={3}
    >
      <HStack alignItems="center" space={3} width="100%">
        <Box
          bg={`${color}.100`}
          borderRadius="lg"
          p={2}
        >
          <IconWrapper name={icon} size={4} color={`${color}.500`} label={title} />
        </Box>
        <VStack flex={1} alignItems="flex-start">
          <Text 
            fontSize="md" 
            fontWeight="medium"
            color={mode === 'dark' ? 'white' : 'gray.800'}
          >
            {title}
          </Text>
          <Text 
            fontSize="sm" 
            color={mode === 'dark' ? 'gray.400' : 'gray.600'}
          >
            {subtitle}
          </Text>
        </VStack>
        <IconWrapper name="chevron-right" size={4} color="gray.400" label="Action" />
      </HStack>
    </Pressable>
  );

  return (
    <Box
      flex={1}
      bg={mode === 'dark' ? 'black' : 'gray.50'}
      safeArea
    >
      <ScrollView flex={1} showsVerticalScrollIndicator={false}>
        <VStack space={4} p={4} pb={6}>
          {/* Header */}
          <Box>
            <HStack alignItems="center" space={3} mb={4}>
              <IconWrapper name="account" size={6} color="orange.500" label="Profile" />
              <VStack>
                <Text fontSize="xl" fontWeight="bold" color={mode === 'dark' ? 'white' : 'gray.800'}>
                  User Profile
                </Text>
                <Text fontSize="sm" color={mode === 'dark' ? 'gray.400' : 'gray.600'}>
                  Manage your account information
                </Text>
              </VStack>
            </HStack>
          </Box>

          {/* Profile Card */}
          <Box
            bg={mode === 'dark' ? 'gray.800' : 'white'}
            borderRadius="xl"
            p={4}
            shadow={2}
            borderWidth={1}
            borderColor={mode === 'dark' ? 'gray.700' : 'gray.100'}
            alignItems="center"
          >
            <Avatar
              size="lg"
              bg="orange.500"
              _text={{ fontSize: 'lg', fontWeight: 'bold' }}
              mb={3}
            >
              {state.user?.fullName?.charAt(0) || state.user?.username?.charAt(0) || 'U'}
            </Avatar>
            
            <Text 
              fontSize="xl" 
              fontWeight="bold" 
              color={mode === 'dark' ? 'white' : 'gray.800'}
              textAlign="center"
            >
              {state.user?.fullName || state.user?.username || 'User'}
            </Text>
            
            <HStack alignItems="center" space={2} mt={2}>
              <IconWrapper name="water-pump" size={4} color="orange.500" label="Role" />
              <Text 
                fontSize="md" 
                color={mode === 'dark' ? 'gray.400' : 'gray.600'}
              >
                Water Meter Reader
              </Text>
            </HStack>
          </Box>

          {/* Profile Information */}
          <Box
            bg={mode === 'dark' ? 'gray.800' : 'white'}
            borderRadius="xl"
            shadow={1}
            borderWidth={1}
            borderColor={mode === 'dark' ? 'gray.700' : 'gray.100'}
            overflow="hidden"
          >
            <Box p={4} pb={2}>
              <Text fontSize="lg" fontWeight="bold" color={mode === 'dark' ? 'white' : 'gray.800'}>
                Account Information
              </Text>
            </Box>
            
            <VStack divider={<Divider />} px={4} pb={4}>
              {profileData.map((item, index) => (
                <InfoRow 
                  key={index} 
                  icon={item.icon}
                  label={item.label} 
                  value={item.value} 
                />
              ))}
            </VStack>
          </Box>

          {/* Actions */}
          <VStack space={3}>
            <Text fontSize="lg" fontWeight="bold" color={mode === 'dark' ? 'white' : 'gray.800'}>
              Actions
            </Text>
            
            <Box
              bg={mode === 'dark' ? 'gray.800' : 'white'}
              borderRadius="xl"
              shadow={1}
              borderWidth={1}
              borderColor={mode === 'dark' ? 'gray.700' : 'gray.100'}
              overflow="hidden"
            >
              <VStack divider={<Divider />}>
                <ActionButton
                  icon="account-edit"
                  title="Edit Profile"
                  subtitle="Update your personal information"
                  color="blue"
                  onPress={() => toastManager.info('Edit profile feature coming soon')}
                />
                <ActionButton
                  icon="shield-account"
                  title="Change Password"
                  subtitle="Update your account password"
                  color="green"
                  onPress={() => toastManager.info('Change password feature coming soon')}
                />
                <ActionButton
                  icon="cog"
                  title="Preferences"
                  subtitle="Customize app settings"
                  color="purple"
                  onPress={() => toastManager.info('Preferences feature coming soon')}
                />
              </VStack>
            </Box>
          </VStack>

          {/* Logout Button */}
          <Button
            onPress={() => setIsLogoutOpen(true)}
            bg="red.500"
            _pressed={{ bg: "red.600" }}
            size="lg"
            borderRadius="xl"
            leftIcon={<IconWrapper name="logout" size={5} color="white" label="Logout" />}
          >
            <Text color="white" fontSize="md" fontWeight="bold">
              Logout
            </Text>
          </Button>
        </VStack>
      </ScrollView>

      {/* Logout Confirmation Dialog */}
      <AlertDialog
        isOpen={isLogoutOpen}
        onClose={() => setIsLogoutOpen(false)}
        leastDestructiveRef={cancelRef}
      >
        <AlertDialog.Content>
          <AlertDialog.CloseButton />
          <AlertDialog.Header>Logout</AlertDialog.Header>
          <AlertDialog.Body>
            Are you sure you want to logout? You will need to sign in again to access the app.
          </AlertDialog.Body>
          <AlertDialog.Footer>
            <Button.Group space={2}>
              <Button
                variant="ghost"
                colorScheme="coolGray"
                onPress={() => setIsLogoutOpen(false)}
                ref={cancelRef}
              >
                Cancel
              </Button>
              <Button colorScheme="red" onPress={handleLogout}>
                Logout
              </Button>
            </Button.Group>
          </AlertDialog.Footer>
        </AlertDialog.Content>
      </AlertDialog>
    </Box>
  );
};

export default UserProfileScreen; 