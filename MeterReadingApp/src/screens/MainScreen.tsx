import * as React from 'react';
import {Text, VStack, Box} from 'native-base';
import {useTheme} from '../styles/ThemeContext';
import {useWindowDimensions} from 'react-native';
import Overview from '../components/MainScreen/Overview';
import SyncMonitor from '../components/sync/SyncMonitor';

const MainScreen = () => {
  const {mode} = useTheme();
  const {width, height} = useWindowDimensions();

  const containerPadding = width > 600 ? 40 : 24;
  const logoHeight = height * 0.15; // 15% of screen height

  return (
    <Box
      bg={mode === 'dark' ? '#000000' : '#ffffff'}
      style={{
        paddingHorizontal: containerPadding,
        flex: 1,
      }}
      height="100%"
      safeArea>
      <VStack space={2} alignItems="center" height="100%">
        <Box
          alignItems="center"
          width="100%"
          height={`${logoHeight}px`}
          justifyContent="center">
          <Text
            fontSize={width > 600 ? '3xl' : '2xl'}
            fontWeight="bold"
            color={mode === 'dark' ? 'white' : 'black'}
            textAlign="center"
            fontFamily="monospace"
            letterSpacing={2}
          >
            CORDE WATER METER
          </Text>
        </Box>
        <Box flex={1} width="100%">
          <VStack space={4}>
            <Box px={4}>
              <SyncMonitor compact={true} />
            </Box>
            <Box px={4} flex={1}>
              <Overview />
            </Box>
          </VStack>
        </Box>
      </VStack>
    </Box>
  );
};

export default MainScreen;