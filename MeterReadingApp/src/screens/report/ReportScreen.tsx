import React, { useState } from 'react';
import { 
  Box, 
  VStack, 
  HStack, 
  Text, 
  ScrollView, 
  Button,
  Progress,
  Pressable,
} from 'native-base';
import { useTheme } from '../../styles/ThemeContext';
import { IconWrapper } from '../../components/common';
import { useToastManager } from '../../utils/ToastManager';

const ReportScreen: React.FC = () => {
  const { mode } = useTheme();
  const toastManager = useToastManager();
  const [selectedPeriod, setSelectedPeriod] = useState('Today');

  const periods = ['Today', 'This Week', 'This Month', 'This Year'];

  const reportData = {
    totalReadings: 45,
    completedTasks: 38,
    pendingTasks: 7,
    failedReadings: 2,
    averageTime: '12 min',
    accuracy: '96%',
  };

  const PeriodButton = ({ period }: { period: string }) => (
    <Pressable
      onPress={() => setSelectedPeriod(period)}
      _pressed={{ opacity: 0.8 }}
    >
      <Box
        bg={selectedPeriod === period ? 'orange.500' : (mode === 'dark' ? 'gray.700' : 'gray.100')}
        borderRadius="xl"
        px={4}
        py={2}
        mr={2}
        mb={2}
      >
        <Text
          fontSize="sm"
          fontWeight="medium"
          color={selectedPeriod === period ? 'white' : (mode === 'dark' ? 'white' : 'gray.700')}
        >
          {period}
        </Text>
      </Box>
    </Pressable>
  );

  const StatCard = ({ icon, title, value, color, percentage }: { 
    icon: string; 
    title: string; 
    value: string | number; 
    color: string;
    percentage?: number;
  }) => (
    <Box
      bg={mode === 'dark' ? 'gray.800' : 'white'}
      borderRadius="xl"
      p={4}
      shadow={1}
      borderWidth={1}
      borderColor={mode === 'dark' ? 'gray.700' : 'gray.100'}
      flex={1}
    >
      <HStack alignItems="center" space={3} mb={3}>
        <Box
          bg={`${color}.100`}
          borderRadius="lg"
          p={2}
        >
          <IconWrapper name={icon} size={5} color={`${color}.500`} label={title} />
        </Box>
        <VStack flex={1}>
          <Text 
            fontSize="2xl" 
            fontWeight="bold" 
            color={mode === 'dark' ? 'white' : 'gray.800'}
          >
            {value}
          </Text>
          <Text 
            fontSize="xs" 
            color={mode === 'dark' ? 'gray.400' : 'gray.600'}
            numberOfLines={2}
          >
            {title}
          </Text>
        </VStack>
      </HStack>
      
      {percentage !== undefined && (
        <VStack space={1}>
          <Progress 
            value={percentage} 
            colorScheme={color} 
            size="sm"
            borderRadius="full"
          />
          <Text fontSize="xs" color={`${color}.500`} textAlign="right">
            {percentage}%
          </Text>
        </VStack>
      )}
    </Box>
  );

  const SummaryRow = ({ label, value, color, icon }: {
    label: string;
    value: string;
    color: string;
    icon: string;
  }) => (
    <HStack justifyContent="space-between" alignItems="center" py={3}>
      <HStack alignItems="center" space={3}>
        <IconWrapper name={icon} size={4} color={`${color}.500`} label={label} />
        <Text fontSize="md" color={mode === 'dark' ? 'gray.300' : 'gray.600'}>
          {label}
        </Text>
      </HStack>
      <Text fontSize="md" fontWeight="bold" color={`${color}.500`}>
        {value}
      </Text>
    </HStack>
  );

  const ActionButton = ({ icon, title, subtitle, color, onPress }: {
    icon: string;
    title: string;
    subtitle: string;
    color: string;
    onPress: () => void;
  }) => (
    <Pressable
      onPress={onPress}
      _pressed={{ bg: mode === 'dark' ? 'gray.700' : 'gray.100' }}
      p={3}
    >
      <HStack alignItems="center" space={3} width="100%">
        <Box
          bg={`${color}.100`}
          borderRadius="lg"
          p={2}
        >
          <IconWrapper name={icon} size={4} color={`${color}.500`} label={title} />
        </Box>
        <VStack flex={1} alignItems="flex-start">
          <Text 
            fontSize="md" 
            fontWeight="medium"
            color={mode === 'dark' ? 'white' : 'gray.800'}
          >
            {title}
          </Text>
          <Text 
            fontSize="sm" 
            color={mode === 'dark' ? 'gray.400' : 'gray.600'}
          >
            {subtitle}
          </Text>
        </VStack>
        <IconWrapper name="download" size={4} color="gray.400" label="Download" />
      </HStack>
    </Pressable>
  );

  return (
    <Box
      flex={1}
      bg={mode === 'dark' ? 'black' : 'gray.50'}
      safeArea
    >
      <ScrollView flex={1} showsVerticalScrollIndicator={false}>
        <VStack space={4} p={4} pb={6}>
          {/* Header */}
          <Box>
            <HStack alignItems="center" space={3} mb={4}>
              <IconWrapper name="chart-bar" size={6} color="orange.500" label="Reports" />
              <VStack>
                <Text fontSize="xl" fontWeight="bold" color={mode === 'dark' ? 'white' : 'gray.800'}>
                  Performance Reports
                </Text>
                <Text fontSize="sm" color={mode === 'dark' ? 'gray.400' : 'gray.600'}>
                  Track your reading statistics
                </Text>
              </VStack>
            </HStack>
          </Box>

          {/* Period Selection */}
          <Box
            bg={mode === 'dark' ? 'gray.800' : 'white'}
            borderRadius="xl"
            p={4}
            shadow={1}
            borderWidth={1}
            borderColor={mode === 'dark' ? 'gray.700' : 'gray.100'}
          >
            <Text fontSize="md" fontWeight="bold" color={mode === 'dark' ? 'white' : 'gray.800'} mb={3}>
              Time Period
            </Text>
            <HStack flexWrap="wrap">
              {periods.map((period) => (
                <PeriodButton key={period} period={period} />
              ))}
            </HStack>
          </Box>

          {/* Statistics Overview */}
          <VStack space={3}>
            <Text fontSize="lg" fontWeight="bold" color={mode === 'dark' ? 'white' : 'gray.800'}>
              Statistics Overview
            </Text>
            
            <VStack space={2}>
              {/* Row 1 */}
              <HStack space={2}>
                <StatCard 
                  icon="clipboard-list-outline" 
                  title="Total Readings" 
                  value={reportData.totalReadings} 
                  color="orange"
                  percentage={75}
                />
                <StatCard 
                  icon="check-circle" 
                  title="Completed Tasks" 
                  value={reportData.completedTasks} 
                  color="green"
                  percentage={84}
                />
              </HStack>
              
              {/* Row 2 */}
              <HStack space={2}>
                <StatCard 
                  icon="clock-outline" 
                  title="Pending Tasks" 
                  value={reportData.pendingTasks} 
                  color="yellow"
                  percentage={16}
                />
                <StatCard 
                  icon="close-circle" 
                  title="Failed Readings" 
                  value={reportData.failedReadings} 
                  color="red"
                  percentage={4}
                />
              </HStack>
              
              {/* Row 3 */}
              <HStack space={2}>
                <StatCard 
                  icon="timer-outline" 
                  title="Average Time" 
                  value={reportData.averageTime} 
                  color="blue"
                />
                <StatCard 
                  icon="target" 
                  title="Accuracy Rate" 
                  value={reportData.accuracy} 
                  color="purple"
                  percentage={96}
                />
              </HStack>
            </VStack>
          </VStack>

          {/* Performance Summary */}
          <Box
            bg={mode === 'dark' ? 'gray.800' : 'white'}
            borderRadius="xl"
            p={4}
            shadow={1}
            borderWidth={1}
            borderColor={mode === 'dark' ? 'gray.700' : 'gray.100'}
          >
            <Text fontSize="lg" fontWeight="bold" color={mode === 'dark' ? 'white' : 'gray.800'} mb={3}>
              Performance Summary
            </Text>
            
            <VStack space={1}>
              <SummaryRow
                icon="chart-line"
                label="Completion Rate"
                value="84%"
                color="green"
              />
              <SummaryRow
                icon="medal"
                label="Quality Score"
                value="Excellent"
                color="orange"
              />
              <SummaryRow
                icon="trending-up"
                label="Productivity"
                value="Above Average"
                color="blue"
              />
            </VStack>
          </Box>

          {/* Export Options */}
          <VStack space={3}>
            <Text fontSize="lg" fontWeight="bold" color={mode === 'dark' ? 'white' : 'gray.800'}>
              Export Options
            </Text>
            
            <Box
              bg={mode === 'dark' ? 'gray.800' : 'white'}
              borderRadius="xl"
              shadow={1}
              borderWidth={1}
              borderColor={mode === 'dark' ? 'gray.700' : 'gray.100'}
              overflow="hidden"
            >
              <VStack>
                <ActionButton
                  icon="file-pdf-box"
                  title="Export PDF Report"
                  subtitle="Generate detailed performance report"
                  color="red"
                  onPress={() => toastManager.info('Generating PDF report...', 'Export Started')}
                />
                <ActionButton
                  icon="file-excel-box"
                  title="Download CSV Data"
                  subtitle="Export raw data for analysis"
                  color="green"
                  onPress={() => toastManager.info('Preparing CSV download...', 'Export Started')}
                />
                <ActionButton
                  icon="email"
                  title="Email Report"
                  subtitle="Send report to supervisor"
                  color="blue"
                  onPress={() => toastManager.info('Opening email client...', 'Email Report')}
                />
              </VStack>
            </Box>
          </VStack>
        </VStack>
      </ScrollView>
    </Box>
  );
};

export default ReportScreen; 