import React from 'react';
import { VStack, Box, Text } from 'native-base';
import { LinearGradient } from 'react-native-linear-gradient';
import { useTheme } from '../../styles/ThemeContext';
import { useWindowDimensions } from 'react-native';
import Header from '../../components/LoginScreen/Header';
import Login from '../../components/LoginScreen/Login';
import { getApiBaseUrl } from '../../config/apiConfig';

const LoginScreen = () => {
  const { mode } = useTheme();
  const { height, width } = useWindowDimensions();

  const gradientColors = mode === 'dark'
    ? ['#1a1a1a', '#2d3748', '#4a5568']
    : ['#f7fafc', '#edf2f7', '#e2e8f0'];

  const currentApiUrl = getApiBaseUrl();

  return (
    <Box flex={1} safeArea>
      <LinearGradient
        colors={gradientColors}
        style={{ flex: 1 }}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        <VStack
          flex={1}
          justifyContent="center"
          px={width > 600 ? '15%' : '5%'}
          py={4}>
          
          {/* Header Section */}
          <Box alignItems="center" flex={0.6} justifyContent="center">
            <Header />
          </Box>
          
          {/* Login Form Section */}
          <Box flex={0.4} justifyContent="center" minHeight="300px">
            <Box
              bg={mode === 'dark' ? 'rgba(45, 55, 72, 0.9)' : 'rgba(255, 255, 255, 0.9)'}
              borderRadius="xl"
              p={5}
              shadow={6}
              borderWidth={1}
              borderColor={mode === 'dark' ? 'gray.600' : 'gray.200'}
            >
              <Login />
            </Box>
          </Box>

          <Box mt={4} p={2} bg={mode === 'dark' ? 'gray.800' : 'gray.100'} borderRadius="md">
            <Text fontSize="xs" color={mode === 'dark' ? 'gray.300' : 'gray.600'} textAlign="center">
              API: {currentApiUrl}
            </Text>
            <Text fontSize="xs" color={mode === 'dark' ? 'gray.400' : 'gray.500'} textAlign="center">
              __DEV__: {__DEV__ ? 'true' : 'false'}
            </Text>
          </Box>
        </VStack>
      </LinearGradient>
    </Box>
  );
};

export default LoginScreen; 