import React, { useState, useEffect } from 'react';
import {
  Box,
  VStack,
  HStack,
  Text,
  Pressable,
  ScrollView,
  FlatList,
  Button,
  Center,
  Spinner,
  Badge,
  Divider
  } from 'native-base';
  import { useTheme } from '../../styles/ThemeContext';
  import DatabaseManager from '../../database/DatabaseManager';
  
  interface TabItem {
    id: string;
    name: string;
    color: string;
  }

const tabs: TabItem[] = [
  { id: 'users', name: 'Users', color: '#4CAF50' },
  { id: 'water_meters', name: 'Meters', color: '#2196F3' },
  { id: 'work_tasks', name: 'Tasks', color: '#FF9800' },
  { id: 'meter_readings', name: 'Readings', color: '#E91E63' },
  { id: 'baselines', name: 'Baselines', color: '#00BCD4' },
  { id: 'reading_photos', name: 'Photos', color: '#9C27B0' },
  { id: 'sync_logs', name: 'Sync Logs', color: '#795548' },
  { id: 'offline_readings', name: 'Offline', color: '#607D8B' },
  { id: 'db_version', name: 'Version', color: '#FFC107' },
];

const DebugDataScreen: React.FC = () => {
  const { mode } = useTheme();
  const [activeTab, setActiveTab] = useState<string>('users');
  const [data, setData] = useState<any[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [totalRecords, setTotalRecords] = useState<number>(0);
  const pageSize = 10;

  const bgColor = mode === 'dark' ? '#1a1a1a' : '#ffffff';
  const textColor = mode === 'dark' ? '#ffffff' : '#000000';
  const cardBgColor = mode === 'dark' ? '#2d2d2d' : '#f5f5f5';

  // 获取实际表名
  const getTableName = (tabId: string): string => {
    const tableNames = {
      'users': 'users',
      'water_meters': 'water_meters',
      'work_tasks': 'work_tasks',
      'meter_readings': 'meter_readings',
      'baselines': 'baseline_records',
      'reading_photos': 'reading_photos',
      'sync_logs': 'meter_reading_sync_logs',
      'offline_readings': 'offline_readings',
      'db_version': 'db_version'
    };
    return tableNames[tabId] || tabId;
  };

  const loadData = async (tab: string, page: number = 1) => {
    setLoading(true);
    try {
      const offset = (page - 1) * pageSize;
      let tableName = '';
      let joinQuery = '';
      
      // 确定表名和查询
      switch (tab) {
        case 'users':
          tableName = 'users';
          break;
        case 'water_meters':
          tableName = 'water_meters';
          break;
        case 'work_tasks':
          tableName = 'work_tasks';
          joinQuery = `
            SELECT wt.*, wm.serial_number as meter_serial_number
            FROM work_tasks wt
            LEFT JOIN water_meters wm ON wt.meter_id = wm.id
            WHERE wt.is_deleted = 0
            ORDER BY wt.created_at DESC
            LIMIT ${pageSize} OFFSET ${offset}
          `;
          break;
        case 'meter_readings':
          tableName = 'meter_readings';
          joinQuery = `
            SELECT mr.*, wm.serial_number as meter_serial_number, u.username as user_username
            FROM meter_readings mr
            LEFT JOIN water_meters wm ON mr.meter_id = wm.id
            LEFT JOIN users u ON mr.user_id = u.id
            WHERE mr.is_deleted = 0
            ORDER BY mr.created_at DESC
            LIMIT ${pageSize} OFFSET ${offset}
          `;
          break;
        case 'baselines':
          tableName = 'baseline_records';
          joinQuery = `
            SELECT br.*, wm.serial_number as meter_serial_number
            FROM baseline_records br
            LEFT JOIN water_meters wm ON br.meter_id = wm.id
            WHERE br.is_deleted = 0
            ORDER BY br.baseline_date DESC
            LIMIT ${pageSize} OFFSET ${offset}
          `;
          break;
        case 'reading_photos':
          tableName = 'reading_photos';
          joinQuery = `
            SELECT rp.*, mr.reading_value as reading_value
            FROM reading_photos rp
            LEFT JOIN meter_readings mr ON rp.reading_id = mr.id
            WHERE rp.is_deleted = 0
            ORDER BY rp.created_at DESC
            LIMIT ${pageSize} OFFSET ${offset}
          `;
          break;
        case 'sync_logs':
          tableName = 'meter_reading_sync_logs';
          joinQuery = `
            SELECT msl.*, mr.reading_value as reading_value
            FROM meter_reading_sync_logs msl
            LEFT JOIN meter_readings mr ON msl.local_reading_id = mr.id
            ORDER BY msl.created_at DESC
            LIMIT ${pageSize} OFFSET ${offset}
          `;
          break;
        case 'offline_readings':
          tableName = 'offline_readings';
          break;
        case 'db_version':
          tableName = 'db_version';
          break;
      }

      // 获取总数 - 根据表类型调整查询条件
      let countSql = '';
      if (['db_version', 'offline_readings'].includes(tab)) {
        // 这些表可能没有 is_deleted 字段
        countSql = `SELECT COUNT(*) as total FROM ${tableName}`;
      } else if (tab === 'sync_logs') {
        // sync_logs 表名特殊处理
        countSql = `SELECT COUNT(*) as total FROM meter_reading_sync_logs`;
      } else {
        // 大部分表都有 is_deleted 字段
        countSql = `SELECT COUNT(*) as total FROM ${tableName} WHERE is_deleted = 0`;
      }

      const countResult = await DatabaseManager.getInstance().executeSql(countSql);
      const total = countResult.rows.item(0).total;

      // 获取数据
      let dataSql = '';
      if (joinQuery) {
        dataSql = joinQuery;
      } else if (['db_version', 'offline_readings'].includes(tab)) {
        // 这些表可能没有 is_deleted 字段
        dataSql = `
          SELECT * FROM ${tableName}
          ORDER BY ${tab === 'db_version' ? 'id' : 'created_at'} DESC
          LIMIT ${pageSize} OFFSET ${offset}
        `;
      } else {
        // 大部分表都有 is_deleted 字段
        dataSql = `
          SELECT * FROM ${tableName}
          WHERE is_deleted = 0
          ORDER BY created_at DESC
          LIMIT ${pageSize} OFFSET ${offset}
        `;
      }

      const dataResult = await DatabaseManager.getInstance().executeSql(dataSql);
      
      const records: any[] = [];
      for (let i = 0; i < dataResult.rows.length; i++) {
        const row = dataResult.rows.item(i);
        records.push(row);
      }
      
      setData(records);
      setTotalRecords(total);
      setCurrentPage(page);
      
      console.log(`📊 Loaded ${records.length} ${tab} records (Total: ${total})`);
    } catch (error) {
      console.error('Error loading data:', error);
      // 如果数据库查询失败，显示错误信息但保持界面可用
      setData([{ 
        error: 'Database query failed', 
        message: error instanceof Error ? error.message : 'Unknown error',
        tab: tab 
      }]);
      setTotalRecords(0);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadData(activeTab, 1);
  }, [activeTab]);

  const handleTabPress = (tabId: string) => {
    setActiveTab(tabId);
    setCurrentPage(1);
  };

  const handlePageChange = (newPage: number) => {
    loadData(activeTab, newPage);
  };

  const renderFieldValue = (value: any): string => {
    if (value === null || value === undefined) return 'NULL';
    if (typeof value === 'boolean') return value ? 'true' : 'false';
    if (typeof value === 'object') return JSON.stringify(value);
    return String(value);
  };

  const renderDataCard = ({ item, index }: { item: any; index: number }) => {
    // 根据不同表显示不同的关键字段
    const getDisplayFields = () => {
      switch (activeTab) {
        case 'users':
          return ['id', 'username', 'full_name', 'email', 'role', 'is_active', 'last_login_date', 'sync_status'];
        case 'water_meters':
          return ['id', 'serial_number', 'account_number', 'customer_name', 'address', 'latitude', 'longitude', 'status'];
        case 'work_tasks':
          return ['id', 'meter_serial_number', 'name', 'task_type', 'status', 'priority', 'scheduled_date'];
        case 'meter_readings':
          return [
            'id', 'uuid', 'meter_serial_number', 'user_username', 'reading_value',
            'reading_date', 'reading_method', 'sync_status', 'created_at'
          ];
        case 'baselines':
          return ['id', 'meter_serial_number', 'baseline_value', 'baseline_date', 'data_source', 'is_validated'];
        case 'reading_photos':
          return ['id', 'reading_value', 'filename', 'file_size', 'mime_type', 'photo_type', 'sync_status'];
        case 'sync_logs':
          return ['sync_log_id', 'local_reading_id', 'reading_value', 'sync_status', 'retry_count', 'last_sync_time'];
        case 'offline_readings':
          return ['id', 'reading_value', 'reading_date', 'sync_status', 'created_at'];
        case 'db_version':
          return ['id', 'version'];
        default:
          return Object.keys(item);
      }
    };

    const displayFields = getDisplayFields().filter(field => item.hasOwnProperty(field));

    return (
      <Box
        bg={cardBgColor}
        borderRadius="sm"
        p={2}
        mb={1}
        borderWidth={1}
        borderColor={mode === 'dark' ? '#404040' : '#e0e0e0'}
      >


        <VStack space={0}>
          {displayFields.map((field) => (
            <HStack key={field} justifyContent="space-between" alignItems="flex-start" py={0.5}>
              <Text
                fontSize="xs"
                fontWeight="medium"
                color={textColor}
                flex={1}
                numberOfLines={1}
              >
                {field}:
              </Text>
              <Text
                fontSize="xs"
                color={mode === 'dark' ? '#cccccc' : '#666666'}
                flex={2}
                textAlign="right"
                numberOfLines={1}
              >
                {renderFieldValue(item[field])}
              </Text>
            </HStack>
          ))}
        </VStack>
      </Box>
    );
  };

  const totalPages = Math.ceil(totalRecords / pageSize);

  return (
    <Box bg={bgColor} flex={1} safeArea>
      <VStack flex={1} p={4}>
        {/* Tab Navigation - Compact horizontal layout */}
        <HStack space={1} mb={3} justifyContent="space-between">
          {tabs.map((tab) => (
            <Pressable
              key={tab.id}
              onPress={() => handleTabPress(tab.id)}
              bg={activeTab === tab.id ? tab.color : mode === 'dark' ? '#404040' : '#e0e0e0'}
              px={2}
              py={2}
              borderRadius="md"
              flex={1}
              _pressed={{ opacity: 0.7 }}
            >
              <Text
                color={activeTab === tab.id ? 'white' : textColor}
                fontWeight={activeTab === tab.id ? 'bold' : 'normal'}
                fontSize="xs"
                textAlign="center"
                numberOfLines={1}
              >
                {tab.name}
              </Text>
            </Pressable>
          ))}
        </HStack>

        <Divider mb={2} />

        {/* Table Title */}
        <Text fontSize="md" fontWeight="bold" color={textColor} textAlign="center" mb={3}>
          {getTableName(activeTab)}
        </Text>

        {/* Data List */}
        <Box flex={1}>
          {loading ? (
            <Center flex={1}>
              <Spinner size="lg" color={tabs.find(t => t.id === activeTab)?.color} />
              <Text color={textColor} mt={2}>Loading {activeTab}...</Text>
            </Center>
          ) : data.length === 0 ? (
            <Center flex={1}>
              <Text color={textColor} fontSize="lg">
                No {activeTab} found
              </Text>
            </Center>
          ) : (
            <FlatList
              data={data}
              renderItem={renderDataCard}
              keyExtractor={(item, index) => `${activeTab}-${item.id || index}`}
              showsVerticalScrollIndicator={false}
            />
          )}
        </Box>

        {/* Pagination with Total Info */}
        {!loading && (
          <VStack space={2} mt={4}>
            <Text color={textColor} fontSize="sm" textAlign="center">
              Total: {totalRecords} records | Showing: {data.length}
            </Text>
            
            {totalPages > 1 && (
              <HStack justifyContent="center" alignItems="center" space={2}>
                <Button
                  size="sm"
                  variant="outline"
                  isDisabled={currentPage === 1}
                  onPress={() => handlePageChange(currentPage - 1)}
                >
                  Previous
                </Button>
                
                <Text color={textColor} fontSize="sm" mx={2}>
                  {currentPage} of {totalPages}
                </Text>
                
                <Button
                  size="sm"
                  variant="outline"
                  isDisabled={currentPage === totalPages}
                  onPress={() => handlePageChange(currentPage + 1)}
                >
                  Next
                </Button>
              </HStack>
            )}
          </VStack>
        )}



        {/* Refresh Button */}
        <Button
          mt={4}
          bg={tabs.find(t => t.id === activeTab)?.color}
          onPress={() => loadData(activeTab, currentPage)}
          _pressed={{ opacity: 0.8 }}
        >
          <Text color="white" fontWeight="bold">
            Refresh Data
          </Text>
        </Button>
      </VStack>
    </Box>
  );
};

export default DebugDataScreen; 