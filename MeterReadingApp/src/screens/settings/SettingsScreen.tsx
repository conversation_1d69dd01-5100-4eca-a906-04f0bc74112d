import React from 'react';
import { 
  Box, 
  VStack, 
  HStack, 
  Text, 
  ScrollView, 
  Switch,
  Pressable,
  Divider,
} from 'native-base';
import { useTheme } from '../../styles/ThemeContext';
import { IconWrapper } from '../../components/common';
import { useToastManager } from '../../utils/ToastManager';

const SettingsScreen: React.FC = () => {
  const { mode, setMode } = useTheme();
  const toastManager = useToastManager();
  
  const toggleTheme = () => {
    setMode(mode === 'dark' ? 'light' : 'dark');
    toastManager.success(`Switched to ${mode === 'dark' ? 'light' : 'dark'} theme`, 'Theme Changed');
  };

  const settingItems = [
    {
      title: 'Dark Mode',
      subtitle: 'Switch between light and dark theme',
      icon: 'theme-light-dark',
      type: 'switch',
      value: mode === 'dark',
      onValueChange: toggleTheme,
    },
    {
      title: 'Auto Sync',
      subtitle: 'Automatically sync data when connected',
      icon: 'sync',
      type: 'switch',
      value: true,
      onValueChange: () => toastManager.info('Auto sync setting updated'),
    },
    {
      title: 'Camera Quality',
      subtitle: 'High quality images for meter readings',
      icon: 'camera-enhance',
      type: 'option',
      value: 'High',
      onPress: () => toastManager.info('Camera quality settings'),
    },
    {
      title: 'GPS Accuracy',
      subtitle: 'Location accuracy for readings',
      icon: 'crosshairs-gps',
      type: 'option',
      value: 'Precise',
      onPress: () => toastManager.info('GPS accuracy settings'),
    },
    {
      title: 'Language',
      subtitle: 'App display language',
      icon: 'translate',
      type: 'option',
      value: 'English',
      onPress: () => toastManager.info('Language settings'),
    },
    {
      title: 'About',
      subtitle: 'Version 1.0.0',
      icon: 'information',
      type: 'info',
      onPress: () => toastManager.info('Water Meter Reading App v1.0.0', 'About'),
    },
  ];

  const SettingItem = ({ item }: { item: any }) => (
    <Pressable
      onPress={item.onPress}
      disabled={item.type === 'switch'}
      _pressed={{ opacity: 0.8 }}
    >
      <Box p={4}>
        <HStack alignItems="center" space={3}>
          <Box
            bg={mode === 'dark' ? 'gray.700' : 'gray.100'}
            borderRadius="lg"
            p={2}
          >
            <IconWrapper name={item.icon} size={5} color="orange.500" label={item.title} />
          </Box>
          
          <VStack flex={1} space={1}>
            <Text 
              fontSize="md" 
              fontWeight="medium"
              color={mode === 'dark' ? 'white' : 'gray.800'}
            >
              {item.title}
            </Text>
            <Text 
              fontSize="sm" 
              color={mode === 'dark' ? 'gray.400' : 'gray.600'}
            >
              {item.subtitle}
            </Text>
          </VStack>
          
          <Box>
            {item.type === 'switch' ? (
              <Switch
                value={item.value}
                onValueChange={item.onValueChange}
                trackColor={{ false: mode === 'dark' ? '#4A5568' : '#E2E8F0', true: '#FF620A' }}
                thumbColor={item.value ? '#fff' : '#f4f3f4'}
                size="md"
              />
            ) : item.type === 'option' ? (
              <HStack alignItems="center" space={2}>
                <Text fontSize="sm" color="orange.500" fontWeight="medium">
                  {item.value}
                </Text>
                <IconWrapper name="chevron-right" size={4} color="gray.400" label="Options" />
              </HStack>
            ) : (
              <IconWrapper name="chevron-right" size={4} color="gray.400" label="Info" />
            )}
          </Box>
        </HStack>
      </Box>
    </Pressable>
  );

  const settingGroups = [
    { title: 'App Settings', items: settingItems.slice(0, 2) },
    { title: 'Reading Settings', items: settingItems.slice(2, 4) },
    { title: 'General', items: settingItems.slice(4) },
  ];

  return (
    <Box
      flex={1}
      bg={mode === 'dark' ? 'black' : 'gray.50'}
      safeArea
    >
      <ScrollView flex={1}>
        <VStack space={6} p={4}>
          {/* Header */}
          <Box>
            <HStack alignItems="center" space={3} mb={2}>
              <IconWrapper name="cog" size={6} color="orange.500" label="Settings" />
              <VStack>
                <Text fontSize="xl" fontWeight="bold" color={mode === 'dark' ? 'white' : 'gray.800'}>
                  Settings
                </Text>
                <Text fontSize="sm" color={mode === 'dark' ? 'gray.400' : 'gray.600'}>
                  Customize your app experience
                </Text>
              </VStack>
            </HStack>
          </Box>

          {/* Settings Groups */}
          {settingGroups.map((group, groupIndex) => (
            <Box key={groupIndex}>
              <Text 
                fontSize="lg" 
                fontWeight="bold" 
                color={mode === 'dark' ? 'white' : 'gray.800'}
                mb={3}
                px={2}
              >
                {group.title}
              </Text>
              
              <Box
                bg={mode === 'dark' ? 'gray.800' : 'white'}
                borderRadius="xl"
                shadow={1}
                borderWidth={1}
                borderColor={mode === 'dark' ? 'gray.700' : 'gray.100'}
                overflow="hidden"
              >
                <VStack divider={<Divider />}>
                  {group.items.map((item, itemIndex) => (
                    <SettingItem key={itemIndex} item={item} />
                  ))}
                </VStack>
              </Box>
            </Box>
          ))}

          {/* Footer info */}
          <Box
            bg={mode === 'dark' ? 'gray.800' : 'white'}
            borderRadius="xl"
            p={4}
            shadow={1}
            borderWidth={1}
            borderColor={mode === 'dark' ? 'gray.700' : 'gray.100'}
            alignItems="center"
          >
            <IconWrapper name="water-pump" size={8} color="orange.500" label="App icon" />
            <Text 
              fontSize="md" 
              fontWeight="bold" 
              color={mode === 'dark' ? 'white' : 'gray.800'}
              mt={2}
            >
              Water Meter Reading App
            </Text>
            <Text 
              fontSize="sm" 
              color={mode === 'dark' ? 'gray.400' : 'gray.600'}
              textAlign="center"
              mt={1}
            >
              Professional meter reading solution for utilities
            </Text>
          </Box>
        </VStack>
      </ScrollView>
    </Box>
  );
};

export default SettingsScreen; 