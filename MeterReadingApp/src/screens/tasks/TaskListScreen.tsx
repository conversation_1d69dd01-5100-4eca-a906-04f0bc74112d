import React, { useState, useEffect } from 'react';
import { RefreshControl } from 'react-native';
import { 
  Box, 
  VStack, 
  HStack, 
  Text, 
  FlatList, 
  Pressable,
  Badge,
  Di<PERSON>r,
  <PERSON>ner,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  IconButton,
} from 'native-base';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { RootStackParamList } from '../../navigation/AppNavigator';
import { useTheme } from '../../styles/ThemeContext';
import { IconWrapper } from '../../components/common';
import { MobileTaskDto } from '../../types/mobile';
import { TaskDataSyncService } from '../../services/sync';
import { Task } from '../../database/models';
import { useAuth } from '../../context/AuthContext';
import { useToastManager } from '../../utils/ToastManager';
import NetworkDebugger from '../../components/debug/NetworkDebugger';

type TaskListScreenNavigationProp = NativeStackNavigationProp<RootStackParamList, 'TaskList'>;

const TaskListScreen: React.FC = () => {
  const navigation = useNavigation<TaskListScreenNavigationProp>();
  const { mode } = useTheme();
  const toastManager = useToastManager();
  const { state: authState } = useAuth();

  // State management
  const [tasks, setTasks] = useState<Task[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [filter, setFilter] = useState<string>('all');
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [showDebugger, setShowDebugger] = useState(false);

  // Load tasks on component mount
  useEffect(() => {
    loadTasks();
  }, [filter]);

  const loadTasks = async (pageNum: number = 1, isRefresh: boolean = false) => {
    try {
      if (isRefresh) {
        setRefreshing(true);
      } else {
        setLoading(true);
      }
      
      setError(null);
      
      if (!authState.user?.id) {
        throw new Error('User not authenticated');
      }

      // Get user's tasks from local database (filtered by TaskBusinessService)
      let allUserTasks = await TaskDataSyncService.getUserTasks(parseInt(authState.user.id) || 0);
      
      // Apply filter locally (aligned with backend WorkTask status enum)
      let filteredTasks: Task[] = [];
      if (filter === 'all') {
        filteredTasks = allUserTasks;
      } else if (filter === 'pending') {
        filteredTasks = allUserTasks.filter(task => task.status === 'Pending');
      } else if (filter === 'in_progress') {
        filteredTasks = allUserTasks.filter(task => task.status === 'InProgress');
      } else if (filter === 'completed') {
        filteredTasks = allUserTasks.filter(task => task.status === 'Completed');
      }
      
      // Implement pagination locally (for consistency with old behavior)
      const startIndex = (pageNum - 1) * 50;
      const endIndex = startIndex + 50;
      const paginatedTasks = filteredTasks.slice(startIndex, endIndex);
      
      if (pageNum === 1) {
        setTasks(paginatedTasks);
      } else {
        setTasks(prev => [...prev, ...paginatedTasks]);
      }
      
      setHasMore(endIndex < filteredTasks.length);
      setPage(pageNum);
      
      if (paginatedTasks.length === 0 && pageNum === 1) {
        console.log('No tasks found for current filter:', filter);
      }
      
    } catch (err: any) {
      console.error('Error loading tasks:', err);
      setError(err.message || 'Failed to load tasks');
      toastManager.error('Failed to load tasks. Please try again.');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const onRefresh = () => {
    loadTasks(1, true);
  };

  const loadMore = () => {
    if (!loading && hasMore) {
      loadTasks(page + 1, false);
    }
  };

  const getStatusConfig = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'completed':
        return { color: 'green.500', icon: 'check-circle', text: 'Completed' };
      case 'inprogress':
      case 'in progress':
      case 'in_progress':
        return { color: 'blue.500', icon: 'clock', text: 'In Progress' };
      case 'pending':
        return { color: 'orange.500', icon: 'clipboard', text: 'Pending' };
      case 'cancelled':
        return { color: 'red.500', icon: 'close-circle', text: 'Cancelled' };
      case 'failed':
        return { color: 'red.600', icon: 'alert-circle', text: 'Failed' };
      case 'skipped':
        return { color: 'yellow.500', icon: 'skip-next', text: 'Skipped' };
      default:
        return { color: 'gray.500', icon: 'help-circle', text: status || 'Unknown' };
    }
  };

  const getPriorityConfig = (priority: string) => {
    switch (priority?.toLowerCase()) {
      case 'critical':
        return { color: 'red.600', icon: 'arrow-up-bold-circle', text: 'Critical' };
      case 'high':
        return { color: 'red.500', icon: 'arrow-up-bold', text: 'High' };
      case 'medium':
        return { color: 'yellow.500', icon: 'minus', text: 'Medium' };
      case 'low':
        return { color: 'green.500', icon: 'arrow-down-bold', text: 'Low' };
      default:
        return { color: 'gray.500', icon: 'minus', text: priority || 'Normal' };
    }
  };

  const renderTaskItem = ({ item }: { item: Task }) => {
    const statusConfig = getStatusConfig(item.status);
    const priorityConfig = getPriorityConfig(item.priority);

    return (
      <Pressable
        onPress={() => item?.id && navigation.navigate('TaskDetail', { taskId: item.id.toString() })}
        _pressed={{ opacity: 0.8 }}
        mb={3}
      >
        <Box
          bg={mode === 'dark' ? 'gray.800' : 'white'}
          borderRadius="xl"
          p={4}
          shadow={2}
          borderWidth={1}
          borderColor={mode === 'dark' ? 'gray.700' : 'gray.100'}
        >
          {/* Header with Meter ID and Status */}
          <HStack justifyContent="space-between" alignItems="center" mb={3}>
            <HStack alignItems="center" space={2}>
              <IconWrapper name="water" size={5} color="orange.500" label="Meter" />
              <Text 
                fontSize="lg" 
                fontWeight="bold" 
                color={mode === 'dark' ? 'white' : 'gray.800'}
              >
                Meter #{item.meter_number || item.meter_id || 'N/A'}
              </Text>
            </HStack>
            <Badge
              colorScheme={statusConfig.color.split('.')[0]}
              variant="solid"
              borderRadius="lg"
              _text={{ fontSize: 'xs', fontWeight: 'bold' }}
              leftIcon={<IconWrapper name={statusConfig.icon} size={3} color="white" label="Status" />}
            >
              {statusConfig.text}
            </Badge>
          </HStack>

          {/* Task Name and Description */}
          <VStack space={2} mb={3}>
            <Text 
              fontSize="md" 
              fontWeight="medium"
              color={mode === 'dark' ? 'white' : 'gray.800'}
            >
              {item.name}
            </Text>
            {item.description && (
              <Text 
                fontSize="sm" 
                color={mode === 'dark' ? 'gray.400' : 'gray.600'}
                numberOfLines={2}
              >
                {item.description}
              </Text>
            )}
          </VStack>

          {/* Location Info */}
          <VStack space={2} mb={3}>
            {item.work_package_name && (
              <HStack alignItems="center" space={2}>
                <IconWrapper name="package" size={4} color="gray.500" label="Work Package" />
                <Text 
                  fontSize="sm" 
                  color={mode === 'dark' ? 'gray.200' : 'gray.700'}
                >
                  {item.work_package_name}
                </Text>
              </HStack>
            )}
            {item.location && (
              <HStack alignItems="center" space={2}>
                <IconWrapper name="map-marker" size={4} color="gray.500" label="Location" />
                <Text 
                  fontSize="sm" 
                  color={mode === 'dark' ? 'gray.400' : 'gray.600'}
                  flex={1}
                >
                  {item.location}
                </Text>
              </HStack>
            )}
          </VStack>

          <Divider mb={3} />

          {/* Footer with Priority and Due Date */}
          <HStack justifyContent="space-between" alignItems="center">
            <HStack alignItems="center" space={2}>
              <IconWrapper name={priorityConfig.icon} size={4} color={priorityConfig.color} label="Priority" />
              <Text fontSize="sm" color={priorityConfig.color} fontWeight="medium">
                {priorityConfig.text} Priority
              </Text>
            </HStack>
            <HStack alignItems="center" space={2}>
              {item.due_date && (
                <Text fontSize="sm" color={mode === 'dark' ? 'gray.400' : 'gray.600'}>
                  Due: {new Date(item.due_date).toLocaleDateString()}
                </Text>
              )}
              <IconWrapper name="chevron-right" size={4} color="gray.400" label="View details" />
            </HStack>
          </HStack>
        </Box>
      </Pressable>
    );
  };

  const renderFilterButton = (filterKey: string, title: string) => (
    <Pressable
      onPress={() => setFilter(filterKey)}
      _pressed={{ opacity: 0.8 }}
      mr={3}
    >
      <Box
        bg={filter === filterKey ? 'orange.500' : (mode === 'dark' ? 'gray.700' : 'gray.100')}
        borderRadius="xl"
        px={4}
        py={2}
      >
        <Text
          fontSize="sm"
          fontWeight="medium"
          color={filter === filterKey ? 'white' : (mode === 'dark' ? 'white' : 'gray.700')}
        >
          {title}
        </Text>
      </Box>
    </Pressable>
  );

  const renderLoadingFooter = () => {
    if (!loading || page === 1) return null;
    return (
      <Box py={4} alignItems="center">
        <Spinner color="orange.500" />
        <Text fontSize="sm" color="gray.500" mt={2}>
          Loading more tasks...
        </Text>
      </Box>
    );
  };

  const renderEmptyState = () => {
    if (loading) return null;
    
    return (
      <Box flex={1} justifyContent="center" alignItems="center" py={8}>
        <IconWrapper name="clipboard" size={16} color="gray.400" label="No tasks" />
        <Text fontSize="lg" fontWeight="medium" color="gray.500" mt={4}>
          No tasks found
        </Text>
        <Text fontSize="sm" color="gray.400" textAlign="center" mt={2} px={8}>
          {filter === 'all' 
            ? 'You have no tasks assigned at the moment.'
            : `No ${filter.replace('_', ' ')} tasks found.`
          }
        </Text>
        <Button
          mt={4}
          variant="outline"
          colorScheme="orange"
          onPress={onRefresh}
          leftIcon={<IconWrapper name="refresh" size={4} color="orange.500" label="Refresh" />}
        >
          Refresh
        </Button>
      </Box>
    );
  };

  const renderErrorState = () => {
    if (!error) return null;
    
    return (
      <Box flex={1} justifyContent="center" alignItems="center" py={8}>
        <IconWrapper name="alert" size={16} color="red.500" label="Error" />
        <Text fontSize="lg" fontWeight="medium" color="red.500" mt={4}>
          Failed to load tasks
        </Text>
        <Text fontSize="sm" color="gray.500" textAlign="center" mt={2} px={8}>
          {error}
        </Text>
        <VStack space={3} mt={4}>
          <Button
            colorScheme="orange"
            onPress={onRefresh}
            leftIcon={<IconWrapper name="refresh" size={4} color="white" label="Retry" />}
          >
            Try Again
          </Button>
          <Button
            variant="outline"
            colorScheme="blue"
            onPress={() => setShowDebugger(true)}
            leftIcon={<IconWrapper name="network" size={4} color="blue.500" label="Debug" />}
          >
            Network Debug
          </Button>
        </VStack>
      </Box>
    );
  };

  return (
    <Box
      flex={1}
      bg={mode === 'dark' ? 'black' : 'gray.50'}
      safeArea
    >
      {/* Header */}
      <Box px={4} pt={4} pb={2}>
        <HStack justifyContent="space-between" alignItems="center">
          <HStack alignItems="center" space={3}>
            <IconWrapper name="clipboard-list" size={6} color="orange.500" label="Tasks" />
            <VStack>
              <Text fontSize="xl" fontWeight="bold" color={mode === 'dark' ? 'white' : 'gray.800'}>
                Task List
              </Text>
              <Text fontSize="sm" color={mode === 'dark' ? 'gray.400' : 'gray.600'}>
                {loading ? 'Loading...' : `${tasks.length} tasks`}
              </Text>
            </VStack>
          </HStack>
          
          {/* Debug Button */}
          <IconButton
            icon={<IconWrapper name="network" size={5} color="blue.500" label="Debug" />}
            onPress={() => setShowDebugger(true)}
            variant="ghost"
            colorScheme="blue"
          />
        </HStack>
      </Box>

      {/* Filter Buttons */}
      <Box px={4} pb={4}>
        <HStack space={0}>
          {renderFilterButton('all', 'All')}
          {renderFilterButton('pending', 'Pending')}
          {renderFilterButton('in_progress', 'In Progress')}
          {renderFilterButton('completed', 'Completed')}
        </HStack>
      </Box>

      {/* Content */}
      {error ? renderErrorState() : (
        tasks.length === 0 ? renderEmptyState() : (
          <FlatList
            data={tasks}
            renderItem={renderTaskItem}
            keyExtractor={(item, index) => item?.id?.toString() || `task-${index}`}
            contentContainerStyle={{ padding: 16 }}
            showsVerticalScrollIndicator={false}
            refreshControl={
              <RefreshControl
                refreshing={refreshing}
                onRefresh={onRefresh}
                colors={['#f97316']}
                tintColor="#f97316"
              />
            }
            onEndReached={loadMore}
            onEndReachedThreshold={0.1}
            ListFooterComponent={renderLoadingFooter}
          />
        )
      )}

      {/* Loading Overlay */}
      {loading && page === 1 && (
        <Box
          position="absolute"
          top={0}
          left={0}
          right={0}
          bottom={0}
          bg="rgba(0,0,0,0.1)"
          justifyContent="center"
          alignItems="center"
        >
          <Box
            bg={mode === 'dark' ? 'gray.800' : 'white'}
            borderRadius="xl"
            p={6}
            shadow={2}
            alignItems="center"
          >
            <Spinner color="orange.500" size="lg" />
            <Text fontSize="md" color={mode === 'dark' ? 'white' : 'gray.800'} mt={4}>
              Loading tasks...
            </Text>
          </Box>
        </Box>
      )}

      {/* Network Debugger */}
      <NetworkDebugger 
        isVisible={showDebugger}
        onClose={() => setShowDebugger(false)}
      />
    </Box>
  );
};

export default TaskListScreen; 