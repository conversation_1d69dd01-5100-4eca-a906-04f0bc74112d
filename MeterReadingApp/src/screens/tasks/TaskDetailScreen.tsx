import React, { useState, useEffect } from 'react';
import { useFocusEffect } from '@react-navigation/native';
import { 
  Box, 
  VStack, 
  HStack, 
  Text, 
  ScrollView, 
  <PERSON><PERSON>,
  Badge,
  Di<PERSON>r,
  Spinner,
  Pressable,
} from 'native-base';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { RootStackParamList } from '../../navigation/AppNavigator';
import { useTheme } from '../../styles/ThemeContext';
import { IconWrapper } from '../../components/common';
import { useToastManager } from '../../utils/ToastManager';
import { MobileTaskDetailDto } from '../../types/mobile';
import { TaskRepository } from '../../data/repositories';
import { BaselineService } from '../../services/BaselineService';
import { WorkTask } from '../../database/models';
import { Linking, Alert as RNAlert } from 'react-native';

type TaskDetailScreenNavigationProp = NativeStackNavigationProp<RootStackParamList, 'TaskDetail'>;
type TaskDetailScreenRouteProp = RouteProp<RootStackParamList, 'TaskDetail'>;

const TaskDetailScreen: React.FC = () => {
  const navigation = useNavigation<TaskDetailScreenNavigationProp>();
  const route = useRoute<TaskDetailScreenRouteProp>();
  const { mode } = useTheme();
  const toastManager = useToastManager();
  const { taskId } = route.params;

  // State management
  const [taskDetail, setTaskDetail] = useState<MobileTaskDetailDto | null>(null);
  const [baselineReading, setBaselineReading] = useState<number | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [actionLoading, setActionLoading] = useState(false);

  // Load task details on component mount and when screen gains focus
  useEffect(() => {
    loadTaskDetails();
  }, [taskId]);

  // Reload task details when screen gains focus (e.g., returning from MeterReadingScreen)
  useFocusEffect(
    React.useCallback(() => {
      loadTaskDetails();
    }, [taskId])
  );

  const loadTaskDetails = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const details = await TaskRepository.getTaskDetails(parseInt(taskId));
      
      if (details) {
        setTaskDetail(details);
        
        // If no last reading exists, try to get baseline reading
        if ((!details.lastReading || details.lastReading === 0) && details.meterId) {
          try {
            console.log(`TaskDetailScreen: No last reading, fetching baseline for meter ${details.meterId}`);
            const baseline = await BaselineService.getBaselineForMeter(details.meterId);
            if (baseline) {
              setBaselineReading(baseline.baseline_value);
              console.log(`TaskDetailScreen: Found baseline reading: ${baseline.baseline_value}`);
            } else {
              console.log(`TaskDetailScreen: No baseline found for meter ${details.meterId}`);
            }
          } catch (baselineError) {
            console.warn('TaskDetailScreen: Error fetching baseline reading:', baselineError);
          }
        }
      } else {
        setError('Task not found or not accessible');
      }
    } catch (err: any) {
      console.error('Error loading task details:', err);
      setError(err.message || 'Failed to load task details');
      toastManager.error('Failed to load task details');
    } finally {
      setLoading(false);
    }
  };

  const handleStartReading = async () => {
    if (!taskDetail) return;
    
    try {
      setActionLoading(true);
      
      // Start the task if not already started
      if (taskDetail.status?.toLowerCase() === 'pending') {
        await TaskRepository.startTask(taskDetail.id);
      }
      
      // Navigate to meter reading screen
      navigation.navigate('MeterReading', { 
        taskId: taskDetail.id.toString(), 
        meterId: taskDetail.meterId?.toString() || taskDetail.id.toString()
      });
      
    } catch (err: any) {
      console.error('Error starting reading:', err);
      toastManager.error('Failed to start reading');
    } finally {
      setActionLoading(false);
    }
  };

  const handleCallCustomer = () => {
    if (!taskDetail?.customerPhone) {
      toastManager.info('No phone number available');
      return;
    }
    
    const phoneUrl = `tel:${taskDetail.customerPhone}`;
    Linking.canOpenURL(phoneUrl).then(supported => {
      if (supported) {
        Linking.openURL(phoneUrl);
      } else {
        toastManager.error('Phone app not available');
      }
    });
  };

  const handleNavigate = () => {
    if (!taskDetail?.address || taskDetail.address === 'Address not available') {
      toastManager.info('No location information available');
      return;
    }

    let navigationUrl = '';
    
    if (taskDetail.latitude && taskDetail.longitude) {
      navigationUrl = `google.navigation:q=${taskDetail.latitude},${taskDetail.longitude}`;
    } else {
      navigationUrl = `google.navigation:q=${encodeURIComponent(taskDetail.address!)}`;
    }

    Linking.canOpenURL(navigationUrl).then(supported => {
      if (supported) {
        Linking.openURL(navigationUrl);
      } else {
        // Fallback to maps app
        const mapsUrl = taskDetail.latitude && taskDetail.longitude 
          ? `maps://0,0?q=${taskDetail.latitude},${taskDetail.longitude}`
          : `maps://0,0?q=${encodeURIComponent(taskDetail.address!)}`;
        Linking.openURL(mapsUrl).catch(() => {
          toastManager.error('Navigation app not available');
        });
      }
    });
  };

  const handleCompleteTask = async () => {
    if (!taskDetail) return;

    RNAlert.alert(
      'Complete Task',
      'Are you sure you want to mark this task as completed?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Complete',
          style: 'default',
          onPress: async () => {
            try {
              setActionLoading(true);
              await TaskRepository.updateTaskStatus(taskDetail.id, 'Completed');
              await loadTaskDetails();
              toastManager.success('Task completed successfully');
            } catch (err: any) {
              toastManager.error('Failed to complete task');
            } finally {
              setActionLoading(false);
            }
          }
        }
      ]
    );
  };

  const getStatusConfig = (status?: string) => {
    switch (status?.toLowerCase()) {
      case 'completed':
        return { color: 'green.500', text: 'Completed' };
      case 'inprogress':
      case 'in progress':
      case 'in_progress':
        return { color: 'blue.500', text: 'In Progress' };
      case 'pending':
        return { color: 'orange.500', text: 'Pending' };
      case 'cancelled':
        return { color: 'red.500', text: 'Cancelled' };
      case 'failed':
        return { color: 'red.600', text: 'Failed' };
      case 'skipped':
        return { color: 'yellow.500', text: 'Skipped' };
      default:
        return { color: 'gray.500', text: status || 'Unknown' };
    }
  };

  const getPriorityColor = (priority?: string) => {
    switch (priority?.toLowerCase()) {
      case 'critical':
        return 'red.600';
      case 'high':
        return 'red.500';
      case 'medium':
        return 'yellow.500';
      case 'low':
        return 'green.500';
      default:
        return 'gray.500';
    }
  };

  const isTaskCompleted = () => {
    return taskDetail?.status?.toLowerCase() === 'completed';
  };

  const getDaysUntilDue = () => {
    if (!taskDetail?.dueDate) return null;
    const today = new Date();
    const dueDate = new Date(taskDetail.dueDate);
    const diffTime = dueDate.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  if (loading) {
    return (
      <Box
        flex={1}
        bg={mode === 'dark' ? 'black' : 'gray.50'}
        justifyContent="center"
        alignItems="center"
      >
        <Spinner color="orange.500" size="lg" />
        <Text fontSize="md" color={mode === 'dark' ? 'white' : 'gray.800'} mt={4}>
          Loading task details...
        </Text>
      </Box>
    );
  }

  if (error || !taskDetail) {
    return (
      <Box
        flex={1}
        bg={mode === 'dark' ? 'black' : 'gray.50'}
        justifyContent="center"
        alignItems="center"
        px={8}
      >
        <IconWrapper name="alert" size={16} color="red.500" label="Error" />
        <Text fontSize="lg" fontWeight="medium" color="red.500" mt={4}>
          Failed to load task
        </Text>
        <Text fontSize="sm" color="gray.500" textAlign="center" mt={2}>
          {error || 'Task not found'}
        </Text>
        <Button
          mt={4}
          colorScheme="orange"
          onPress={loadTaskDetails}
          leftIcon={<IconWrapper name="refresh" size={4} color="white" label="Retry" />}
        >
          Try Again
        </Button>
      </Box>
    );
  }

  const statusConfig = getStatusConfig(taskDetail.status);
  const daysUntilDue = getDaysUntilDue();

  return (
    <Box
      flex={1}
      bg={mode === 'dark' ? 'black' : 'gray.50'}
      safeArea
    >
      <ScrollView flex={1} showsVerticalScrollIndicator={false}>
        <VStack space={4} p={4}>
          
          {/* Task Status Header */}
          <Box
            bg={mode === 'dark' ? 'gray.800' : 'white'}
            borderRadius="lg"
            p={4}
            borderWidth={1}
            borderColor={mode === 'dark' ? 'gray.700' : 'gray.200'}
          >
            <HStack justifyContent="space-between" alignItems="center" mb={2}>
              <Text 
                fontSize="xl" 
                fontWeight="bold" 
                color={mode === 'dark' ? 'white' : 'gray.800'}
                flex={1}
              >
                {taskDetail.name}
              </Text>
              <Badge
                colorScheme={statusConfig.color.split('.')[0]}
                variant="solid"
                borderRadius="md"
              >
                {statusConfig.text}
              </Badge>
            </HStack>

            <HStack justifyContent="space-between" alignItems="center">
              <Text fontSize="sm" color={mode === 'dark' ? 'gray.400' : 'gray.600'}>
                Task ID: {taskDetail.id}
              </Text>
              
              {taskDetail.priority && taskDetail.priority.toLowerCase() !== 'normal' && (
                <Text 
                  fontSize="sm" 
                  fontWeight="bold" 
                  color={getPriorityColor(taskDetail.priority)}
                >
                  {taskDetail.priority.toUpperCase()} PRIORITY
                </Text>
              )}
            </HStack>

            {daysUntilDue !== null && daysUntilDue <= 1 && (
              <Box mt={2} p={2} bg={daysUntilDue < 0 ? "red.100" : "orange.100"} borderRadius="md">
                <Text 
                  fontSize="sm" 
                  fontWeight="bold" 
                  color={daysUntilDue < 0 ? "red.700" : "orange.700"}
                  textAlign="center"
                >
                  {daysUntilDue < 0 
                    ? `OVERDUE by ${Math.abs(daysUntilDue)} day(s)`
                    : daysUntilDue === 0 
                      ? "DUE TODAY"
                      : "DUE TOMORROW"
                  }
                </Text>
              </Box>
            )}
          </Box>

          {/* Water Meter Information */}
          <Box
            bg={mode === 'dark' ? 'gray.800' : 'white'}
            borderRadius="lg"
            p={4}
            borderWidth={1}
            borderColor={mode === 'dark' ? 'gray.700' : 'gray.200'}
          >
            <Text fontSize="md" fontWeight="bold" color="blue.500" mb={3}>
              Water Meter Information
            </Text>
            
            <VStack space={3}>
              <HStack justifyContent="space-between" alignItems="center">
                <Text fontSize="sm" color={mode === 'dark' ? 'gray.400' : 'gray.600'}>
                  Meter Number
                </Text>
                <Text fontSize="lg" fontWeight="bold" color={mode === 'dark' ? 'white' : 'gray.800'}>
                  {taskDetail.meterNumber}
                </Text>
              </HStack>

              {/* Asset ID and Account Number */}
              {(taskDetail.assetId || taskDetail.accountNumber) && (
                <HStack justifyContent="space-between" alignItems="center">
                  <Text fontSize="sm" color={mode === 'dark' ? 'gray.400' : 'gray.600'}>
                    Asset ID / Account
                  </Text>
                  <Text fontSize="sm" fontWeight="medium" color={mode === 'dark' ? 'white' : 'gray.800'}>
                    {taskDetail.assetId || 'N/A'} / {taskDetail.accountNumber || 'N/A'}
                  </Text>
                </HStack>
              )}
              
              {/* Display reading info */}
              {taskDetail.lastReading != null && taskDetail.lastReading > 0 ? (
                <HStack justifyContent="space-between" alignItems="center">
                  <Text fontSize="sm" color={mode === 'dark' ? 'gray.400' : 'gray.600'}>
                    Last Reading
                  </Text>
                  <Text fontSize="lg" fontWeight="bold" color="green.500">
                    {taskDetail.lastReading.toLocaleString()} L
                  </Text>
                </HStack>
              ) : baselineReading != null ? (
                <HStack justifyContent="space-between" alignItems="center">
                  <VStack flex={1}>
                    <Text fontSize="sm" color={mode === 'dark' ? 'gray.400' : 'gray.600'}>
                      Baseline Reading
                    </Text>
                    <Text fontSize="xs" color="orange.500" italic>
                      (No actual reading available)
                    </Text>
                  </VStack>
                  <Text fontSize="lg" fontWeight="bold" color="orange.500">
                    {baselineReading.toLocaleString()} L
                  </Text>
                </HStack>
              ) : (
                <HStack justifyContent="space-between" alignItems="center">
                  <Text fontSize="sm" color={mode === 'dark' ? 'gray.400' : 'gray.600'}>
                    Reading Status
                  </Text>
                  <Text fontSize="md" color="red.500" italic>
                    No reading available
                  </Text>
                </HStack>
              )}
              
              {taskDetail.lastReadingDate && (
                <HStack justifyContent="space-between" alignItems="center">
                  <Text fontSize="sm" color={mode === 'dark' ? 'gray.400' : 'gray.600'}>
                    Last Reading Date
                  </Text>
                  <Text fontSize="md" color={mode === 'dark' ? 'gray.300' : 'gray.700'}>
                    {new Date(taskDetail.lastReadingDate).toLocaleDateString('en-NZ')}
                  </Text>
                </HStack>
              )}

              {/* Address */}
              <VStack space={2}>
                <Text 
                  fontSize="sm" 
                  color={mode === 'dark' ? 'white' : 'gray.800'}
                  textAlign="center"
                >
                  {taskDetail.address || 'Address not available'}
                </Text>
                
                {(taskDetail.latitude && taskDetail.longitude) && (
                  <Text fontSize="xs" color={mode === 'dark' ? 'gray.400' : 'gray.600'} textAlign="center">
                    GPS: {taskDetail.latitude.toFixed(6)}, {taskDetail.longitude.toFixed(6)}
                  </Text>
                )}
                
                {taskDetail.address && taskDetail.address !== 'Address not available' && (
                  <Button
                    onPress={handleNavigate}
                    variant="outline"
                    colorScheme="purple"
                    size="sm"
                    leftIcon={<IconWrapper name="navigation" size={4} color="purple.500" label="Navigate" />}
                  >
                    Navigate to Location
                  </Button>
                )}
              </VStack>
            </VStack>
          </Box>

          {/* Task Instructions */}
          {taskDetail.instructions && (
            <Box
              bg={mode === 'dark' ? 'gray.800' : 'white'}
              borderRadius="lg"
              p={4}
              borderWidth={1}
              borderColor={mode === 'dark' ? 'gray.700' : 'gray.200'}
            >
              <Text fontSize="md" fontWeight="bold" color="yellow.600" mb={3}>
                Special Instructions
              </Text>
              <Text fontSize="sm" color={mode === 'dark' ? 'gray.300' : 'gray.700'} lineHeight="lg">
                {taskDetail.instructions}
              </Text>
            </Box>
          )}

          {/* Action Buttons */}
          {!isTaskCompleted() && (
            <VStack space={3}>
              {/* Primary Action Button */}
              <Button
                onPress={handleStartReading}
                bg="orange.500"
                _pressed={{ bg: "orange.600" }}
                size="lg"
                borderRadius="xl"
                isLoading={actionLoading}
                isLoadingText="Opening..."
                leftIcon={<IconWrapper name="camera-plus" size={5} color="white" label="Reading" />}
              >
                <Text color="white" fontSize="md" fontWeight="bold">
                  Take Meter Reading
                </Text>
              </Button>
              
              {/* Secondary Actions */}
              <HStack space={3}>
                <Button
                  onPress={handleNavigate}
                  variant="outline"
                  borderColor="blue.500"
                  _text={{ color: "blue.500" }}
                  flex={1}
                  size="lg"
                  borderRadius="xl"
                  leftIcon={<IconWrapper name="navigation" size={4} color="blue.500" label="Navigate" />}
                >
                  Navigate
                </Button>
                
                {taskDetail.customerPhone && (
                  <Button
                    onPress={handleCallCustomer}
                    variant="outline"
                    borderColor="green.500"
                    _text={{ color: "green.500" }}
                    flex={1}
                    size="lg"
                    borderRadius="xl"
                    leftIcon={<IconWrapper name="phone" size={4} color="green.500" label="Call" />}
                  >
                    Call Customer
                  </Button>
                )}
              </HStack>

              {/* Complete Task Button */}
              {taskDetail.status?.toLowerCase() === 'inprogress' && (
                <Button
                  onPress={handleCompleteTask}
                  bg="green.500"
                  _pressed={{ bg: "green.600" }}
                  size="lg"
                  borderRadius="xl"
                  isLoading={actionLoading}
                  isLoadingText="Completing..."
                  leftIcon={<IconWrapper name="check-circle" size={5} color="white" label="Complete" />}
                >
                  <Text color="white" fontSize="md" fontWeight="bold">
                    Complete Task
                  </Text>
                </Button>
              )}
            </VStack>
          )}

          {/* Due Date Information */}
          {taskDetail.dueDate && (
            <Box
              bg={mode === 'dark' ? 'gray.800' : 'white'}
              borderRadius="lg"
              p={4}
              borderWidth={1}
              borderColor={mode === 'dark' ? 'gray.700' : 'gray.200'}
            >
              <HStack justifyContent="space-between" alignItems="center">
                <Text fontSize="md" fontWeight="bold" color={mode === 'dark' ? 'gray.300' : 'gray.700'}>
                  Due Date
                </Text>
                <Text 
                  fontSize="md" 
                  fontWeight="bold" 
                  color={daysUntilDue !== null && daysUntilDue <= 1 ? "red.500" : (mode === 'dark' ? 'white' : 'gray.800')}
                >
                  {new Date(taskDetail.dueDate).toLocaleDateString('en-NZ')}
                </Text>
              </HStack>
            </Box>
          )}

          {/* Task Notes */}
          {taskDetail.notes && (
            <Box
              bg={mode === 'dark' ? 'gray.800' : 'white'}
              borderRadius="lg"
              p={4}
              borderWidth={1}
              borderColor={mode === 'dark' ? 'gray.700' : 'gray.200'}
            >
              <Text fontSize="md" fontWeight="bold" color={mode === 'dark' ? 'gray.300' : 'gray.700'} mb={3}>
                Notes
              </Text>
              <Text fontSize="sm" color={mode === 'dark' ? 'gray.300' : 'gray.700'} lineHeight="lg">
                {taskDetail.notes}
              </Text>
            </Box>
          )}

          {/* Customer Information */}
          <Divider my={2} />
          
          <Box
            bg={mode === 'dark' ? 'gray.800' : 'white'}
            borderRadius="lg"
            p={4}
            borderWidth={1}
            borderColor={mode === 'dark' ? 'gray.700' : 'gray.200'}
          >
            <Text fontSize="md" fontWeight="bold" color="green.500" mb={3}>
              Customer Information
            </Text>
            
            <VStack space={2}>
              {taskDetail.customerName && (
                <HStack justifyContent="space-between" alignItems="center">
                  <Text fontSize="sm" color={mode === 'dark' ? 'gray.400' : 'gray.600'}>
                    Name
                  </Text>
                  <Text fontSize="md" color={mode === 'dark' ? 'white' : 'gray.800'}>
                    {taskDetail.customerName}
                  </Text>
                </HStack>
              )}
              
              {taskDetail.customerPhone && (
                <HStack justifyContent="space-between" alignItems="center">
                  <Text fontSize="sm" color={mode === 'dark' ? 'gray.400' : 'gray.600'}>
                    Phone
                  </Text>
                  <Pressable onPress={handleCallCustomer}>
                    <Text fontSize="md" color="blue.500" underline>
                      {taskDetail.customerPhone}
                    </Text>
                  </Pressable>
                </HStack>
              )}
              
              {taskDetail.customerEmail && (
                <HStack justifyContent="space-between" alignItems="center">
                  <Text fontSize="sm" color={mode === 'dark' ? 'gray.400' : 'gray.600'}>
                    Email
                  </Text>
                  <Text fontSize="md" color={mode === 'dark' ? 'gray.300' : 'gray.700'}>
                    {taskDetail.customerEmail}
                  </Text>
                </HStack>
              )}
            </VStack>
          </Box>

          <Box height={4} />
        </VStack>
      </ScrollView>
    </Box>
  );
};

export default TaskDetailScreen; 