import React, { useState, useEffect, useRef, useCallback } from 'react';
import { StyleSheet, TouchableOpacity } from 'react-native';
import { Box, Spinner, Text, useToast, HStack, Input, Select } from 'native-base';
import MapView, { Marker, Callout, Region } from 'react-native-maps';
import { useTheme } from '../../styles/ThemeContext';
import { useRoute, useNavigation } from '@react-navigation/native';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import { VStack, IconButton } from 'native-base';
import { debounce } from 'lodash';
import GeocodingService from '../../api/GeocodingService';
import IconWrapper from '../../components/common/IconWrapper';

interface MeterMarker {
  id: number;
  meter_id?: number;
  address: string;
  meter_number: string;
  latitude: number;
  longitude: number;
  meter_type?: string;
  status?: string;
  last_reading_date?: string;
  isHighlighted?: boolean;
}

interface MarkerGroup {
  id: string;
  markers: MeterMarker[];
  currentIndex: number;
}

interface DateFilter {
  range: 'today' | '3days' | 'week' | 'month' | 'all';
  startDate?: Date;
  endDate?: Date;
}

const DEFAULT_ADDRESS = 'Auckland, New Zealand';

// Global address cache
const globalAddressCache = new Map<string, { lat: number; lng: number }>();

const MapScreen: React.FC = () => {
  const [markerGroups, setMarkerGroups] = useState<MarkerGroup[]>([]);
  const [flattedMarkers, setFlattedMarkers] = useState<MeterMarker[]>([]);
  const [loading, setLoading] = useState(true);
  const [region, setRegion] = useState<Region | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const { mode } = useTheme();
  const toast = useToast();
  const mapRef = useRef<MapView>(null);
  const route = useRoute();
  const navigation = useNavigation();
  const [selectedMarker, setSelectedMarker] = useState<string | null>(null);
  const [dateFilter, setDateFilter] = useState<DateFilter>({ range: 'today' });
  const [highlightedMeter, setHighlightedMeter] = useState<MeterMarker | null>(null);

  const processedAddresses = useRef(new Set<string>());

  useEffect(() => {
    const params = route.params as any;
    const { meter_address, meter_number } = params || {};
    console.log('Received params in MapScreen:', {
      meter_address,
      meter_number,
    });

    if (meter_address) {
      geocodeAddress(meter_address, meter_number);
    } else {
      initializeMap();
    }
  }, [route.params]);

  useEffect(() => {
    updateDateFilterDates('today');
  }, []);

  const fetchTopMeter = async () => {
    try {
      // TODO: Implement meter data fetching from database
      const sampleMeter = {
        id: 1,
        meter_number: 'M001',
        address: DEFAULT_ADDRESS,
        latitude: -36.8485,
        longitude: 174.7633,
        meter_type: 'Digital',
        status: 'Active'
      };
      
      geocodeAddress(sampleMeter.address, sampleMeter.meter_number);
    } catch (error) {
      console.error('Error fetching top meter:', error);
      toast.show({
        title: 'Error',
        description: 'Failed to load meter data',
      });
      geocodeAddress(DEFAULT_ADDRESS, DEFAULT_ADDRESS);
    }
  };

  const initializeMap = async () => {
    setLoading(true);

    // Load default address first
    await geocodeAddress(DEFAULT_ADDRESS, DEFAULT_ADDRESS);

    try {
      // TODO: Implement location service
      const location = {
        latitude: -36.8485,
        longitude: 174.7633
      };

      console.log('Current location:', location);
      if (location) {
        const newRegion = {
          latitude: location.latitude,
          longitude: location.longitude,
          latitudeDelta: 0.01,
          longitudeDelta: 0.01,
        };
        setRegion(newRegion);
        const currentLocationMarker = {
          id: 1,
          meter_id: 0,
          address: 'Current Location',
          meter_number: 'Current',
          latitude: location.latitude,
          longitude: location.longitude,
        };
        setFlattedMarkers([currentLocationMarker]);
        setMarkerGroups(groupMarkersByLocation([currentLocationMarker]));

        await fetchMeterMarkers();

        if (mapRef.current) {
          mapRef.current.animateToRegion(newRegion, 1000);
        }
      } else {
        await fetchTopMeter();
      }
    } catch (error) {
      console.error('Error getting current location:', error);
      await fetchTopMeter();
    } finally {
      setLoading(false);
    }
  };

  const updateDateFilterDates = async (range: string) => {
    const now = new Date();
    const endDate = new Date(now);
    let startDate = new Date(now);

    switch (range) {
      case 'today':
        startDate.setHours(0, 0, 0, 0);
        break;
      case '3days':
        startDate.setDate(now.getDate() - 2);
        startDate.setHours(0, 0, 0, 0);
        break;
      case 'week':
        startDate.setDate(now.getDate() - 7);
        startDate.setHours(0, 0, 0, 0);
        break;
      case 'month':
        startDate.setMonth(now.getMonth() - 1);
        startDate.setHours(0, 0, 0, 0);
        break;
      case 'all':
        startDate = new Date(2000, 0, 1);
        break;
    }

    setDateFilter({
      range: range as any,
      startDate,
      endDate
    });

    // TODO: Implement meter filtering by date
    await fetchMeterMarkers();
  };

  const fetchMeterMarkers = async () => {
    try {
      // TODO: Implement fetching meter data from database
      const sampleMeters: MeterMarker[] = [
        {
          id: 1,
          meter_id: 1,
          address: 'Queen Street, Auckland',
          meter_number: 'M001',
          latitude: -36.8485,
          longitude: 174.7633,
          meter_type: 'Digital',
          status: 'Active'
        },
        {
          id: 2,
          meter_id: 2,
          address: 'Karangahape Road, Auckland',
          meter_number: 'M002',
          latitude: -36.8555,
          longitude: 174.7583,
          meter_type: 'Analog',
          status: 'Active'
        }
      ];

      setFlattedMarkers(sampleMeters);
      setMarkerGroups(groupMarkersByLocation(sampleMeters));
    } catch (error) {
      console.error('Error fetching meter markers:', error);
      toast.show({
        title: 'Error',
        description: 'Failed to load meter markers',
      });
    }
  };

  const geocodeAddress = async (address: string, meter_number?: string) => {
    try {
      const result = await GeocodingService.geocodeAddress(address);
      if (result) {
        const newRegion = {
          latitude: result.latitude,
          longitude: result.longitude,
          latitudeDelta: 0.01,
          longitudeDelta: 0.01,
        };
        
        setRegion(newRegion);
        if (mapRef.current) {
          mapRef.current.animateToRegion(newRegion, 1000);
        }
      }
    } catch (error) {
      console.error('Geocoding failed:', error);
      // Fallback to default coordinates
      const defaultCoords = {
        latitude: -36.8485,
        longitude: 174.7633,
        latitudeDelta: 0.01,
        longitudeDelta: 0.01,
      };
      setRegion(defaultCoords);
    }
  };

  const handleSearch = () => {
    if (searchQuery.trim()) {
      geocodeAddress(searchQuery);
    }
  };

  const handleMarkerPress = (marker: MeterMarker) => {
    setHighlightedMeter(marker);
  };

  const navigateMarker = (groupId: string, direction: 'next' | 'prev') => {
    setMarkerGroups(prevGroups => {
      return prevGroups.map(group => {
        if (group.id === groupId) {
          const newIndex = direction === 'next'
            ? (group.currentIndex + 1) % group.markers.length
            : group.currentIndex === 0
              ? group.markers.length - 1
              : group.currentIndex - 1;
          
          const newMarker = group.markers[newIndex];
          setHighlightedMeter(newMarker);
          
          return { ...group, currentIndex: newIndex };
        }
        return group;
      });
    });
  };

  const formatDate = (dateString: string | undefined) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString();
  };

  const groupMarkersByLocation = (markers: MeterMarker[]): MarkerGroup[] => {
    const tolerance = 0.0001;
    const groups = new Map<string, MeterMarker[]>();

    markers.forEach(marker => {
      const roundedLat = Math.round(marker.latitude / tolerance) * tolerance;
      const roundedLng = Math.round(marker.longitude / tolerance) * tolerance;
      const key = `${roundedLat},${roundedLng}`;

      if (!groups.has(key)) {
        groups.set(key, []);
      }
      groups.get(key)!.push(marker);
    });

    return Array.from(groups.entries()).map(([key, groupMarkers]) => ({
      id: key,
      markers: groupMarkers,
      currentIndex: 0,
    }));
  };

  const renderMarker = (group: MarkerGroup) => {
    const currentMarker = group.markers[group.currentIndex];
    const hasMultiple = group.markers.length > 1;

    return (
      <Marker
        key={group.id}
        coordinate={{
          latitude: currentMarker.latitude,
          longitude: currentMarker.longitude,
        }}
        onPress={() => handleMarkerPress(currentMarker)}
      >
        <VStack alignItems="center">
          <Box
            bg={currentMarker.status === 'Active' ? 'blue.500' : 'red.500'}
            borderRadius="full"
            p={2}
            borderWidth={2}
            borderColor="white"
          >
            <Icon
              as={MaterialCommunityIcons}
                              name="water"
              size={4}
              color="white"
            />
          </Box>
          {hasMultiple && (
            <Box
              bg="orange.500"
              borderRadius="full"
              px={2}
              py={1}
              position="absolute"
              top={-2}
              right={-2}
            >
              <Text color="white" fontSize="xs" fontWeight="bold">
                {group.markers.length}
              </Text>
            </Box>
          )}
        </VStack>

        <Callout>
          <Box p={2} minWidth={200}>
            <VStack space={2}>
              <Text fontWeight="bold" fontSize="md">
                {currentMarker.meter_number}
              </Text>
              <Text fontSize="sm" color="gray.600">
                {currentMarker.address}
              </Text>
              <Text fontSize="sm">
                Type: {currentMarker.meter_type || 'Unknown'}
              </Text>
              <Text fontSize="sm">
                Status: {currentMarker.status || 'Unknown'}
              </Text>
              {currentMarker.last_reading_date && (
                <Text fontSize="sm">
                  Last Reading: {formatDate(currentMarker.last_reading_date)}
                </Text>
              )}
              
              {hasMultiple && (
                <HStack space={2} mt={2}>
                  <TouchableOpacity
                    onPress={() => navigateMarker(group.id, 'prev')}
                    style={{
                      backgroundColor: '#3182CE',
                      padding: 4,
                      borderRadius: 4,
                      flex: 1,
                    }}
                  >
                    <Text color="white" textAlign="center" fontSize="xs">
                      Previous
                    </Text>
                  </TouchableOpacity>
                  <TouchableOpacity
                    onPress={() => navigateMarker(group.id, 'next')}
                    style={{
                      backgroundColor: '#3182CE',
                      padding: 4,
                      borderRadius: 4,
                      flex: 1,
                    }}
                  >
                    <Text color="white" textAlign="center" fontSize="xs">
                      Next
                    </Text>
                  </TouchableOpacity>
                </HStack>
              )}
            </VStack>
          </Box>
        </Callout>
      </Marker>
    );
  };

  if (loading) {
    return (
      <Box flex={1} justifyContent="center" alignItems="center">
        <Spinner size="lg" />
        <Text mt={2}>Loading map...</Text>
      </Box>
    );
  }

  return (
    <Box flex={1}>
      {/* Search Bar */}
      <Box
        position="absolute"
        top={4}
        left={4}
        right={4}
        zIndex={1000}
        bg={mode === 'dark' ? 'gray.800' : 'white'}
        borderRadius="lg"
        shadow={3}
        p={3}
      >
        <HStack space={2} alignItems="center">
          <Input
            flex={1}
            placeholder="Search address..."
            value={searchQuery}
            onChangeText={setSearchQuery}
            onSubmitEditing={handleSearch}
            fontSize="md"
          />
          <IconButton
            onPress={handleSearch}
            icon={<IconWrapper name="magnify" size={5} />}
            bg="blue.500"
            _pressed={{ bg: 'blue.600' }}
          />
        </HStack>
      </Box>

      {/* Date Filter */}
      <Box
        position="absolute"
        top={20}
        left={4}
        right={4}
        zIndex={999}
        bg={mode === 'dark' ? 'gray.800' : 'white'}
        borderRadius="lg"
        shadow={3}
        p={3}
      >
        <Select
          selectedValue={dateFilter.range}
          onValueChange={(value) => updateDateFilterDates(value)}
          placeholder="Filter by date"
        >
          <Select.Item label="Today" value="today" />
          <Select.Item label="Last 3 Days" value="3days" />
          <Select.Item label="Last Week" value="week" />
          <Select.Item label="Last Month" value="month" />
          <Select.Item label="All" value="all" />
        </Select>
      </Box>

      {/* Map */}
      {region && (
        <MapView
          ref={mapRef}
          style={StyleSheet.absoluteFillObject}
          initialRegion={region}
          showsUserLocation={true}
          showsMyLocationButton={true}
        >
          {markerGroups.map(renderMarker)}
        </MapView>
      )}
    </Box>
  );
};

export default MapScreen; 