import 'react-native-get-random-values';
import React, { useEffect, useState } from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { NativeBaseProvider } from 'native-base';
import AppNavigator from './navigation/AppNavigator';
import { ThemeProvider } from './styles/ThemeContext';
import { AuthProvider } from './context/AuthContext';
import { ErrorBoundary } from './components/common/ErrorBoundary';
import { LoadingSpinner } from './components/common/LoadingSpinner';
import { View } from 'react-native';
import nativeBaseTheme from './styles/nativeBaseTheme';
import DatabaseManager from './database/DatabaseManager';
import './utils/devWarningFilter';
import { MeterReadingBackgroundSync } from './services/sync/BackgroundSync';
import { networkService } from './utils/NetworkUtils';

const LoadingScreen = ({ message }: { message?: string }) => (
  <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
    <LoadingSpinner message={message || 'Initializing...'} />
  </View>
);

export default function App() {
  const [isReady, setIsReady] = useState(false);

  useEffect(() => {
    const initializeApp = async () => {
      try {
        console.log('Starting app initialization...');

        // Force reset database to fix table structure issues
        console.log('🔄 Force resetting database to fix table structure...');
        const dbManager = DatabaseManager.getInstance();
        await dbManager.initializeDatabase();
        // await dbManager.forceResetDatabase();
        // console.log('✅ Database force reset completed');

        console.log('Database initialization complete');
        
        // Initialize GPS location service  
        const { locationService } = require('./services/LocationService');
        await locationService.initialize();
        console.log('GPS location service initialization complete');
        
        // Initialize meter reading sync service (no longer needed - using static methods)
        console.log('Meter reading sync service initialization complete');
        
        // Migrate existing passwords to hash format (run once)
        // await AuthService.migratePasswordsToHash();
        
        // Debug: Show all users (for development)
        // await AuthService.debugAllUsers();
        
        // Create test user for development (uncomment if needed)
        // try {
        //   await AuthService.createLocalUser('test.user', 'password123', 'Test User', 'reader');
        //   console.log('Test user created');
        // } catch (error) {
        //   console.log('Test user already exists or error:', error.message);
        // }
        
        // Initialize network monitoring (automatically initialized in constructor)
        await networkService.isNetworkConnected(); // Wait for network service to be ready
        
        // Start background sync for meter readings
        MeterReadingBackgroundSync.startBackgroundSync();
        
        console.log('MeterReadingApp: Initialization completed');
        setIsReady(true);
      } catch (error: any) {
        console.error('Error initializing app:', error);
        setIsReady(true); // Still allow app to start
      }
    };

    initializeApp();

    // Cleanup function to properly close database on app exit
    return () => {
      DatabaseManager.getInstance().closeDatabase()
        .then(() => console.log('Database cleanup complete'))
        .catch((err: any) => console.error('Error during database cleanup:', err));
      MeterReadingBackgroundSync.stopBackgroundSync();
      networkService.destroy();
    };
  }, []);

  // Show loading screen while initializing
  if (!isReady) {
    return <LoadingScreen message="Setting up your meter reading app..." />;
  }

  return (
    <ErrorBoundary>
      <ThemeProvider>
        <AuthProvider>
          <NavigationContainer>
            <NativeBaseProvider theme={nativeBaseTheme}>
              <AppNavigator />
            </NativeBaseProvider>
          </NavigationContainer>
        </AuthProvider>
      </ThemeProvider>
    </ErrorBoundary>
  );
} 