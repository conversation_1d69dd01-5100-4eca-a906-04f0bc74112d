// 🔑 AI API Configuration
// Please configure your API keys here

export interface AIServiceConfig {
  service: 'claude' | 'gpt4v';
  apiKey: string;
  endpoint: string;
  model: string;
  enabled: boolean;
}

export const AI_CONFIG: Record<string, AIServiceConfig> = {
  claude: {
    service: 'claude',
    // 👇 Replace the key below with your real key from https://console.anthropic.com/
    apiKey: '************************************************************************************************************',
    endpoint: 'https://api.anthropic.com/v1/messages',
    model: 'claude-3-5-sonnet-********',
    enabled: true  // 👈 Enable Claude as backup strategy
  },
  gpt4v: {
    service: 'gpt4v',
    // 👇 Replace with a newly generated key from https://platform.openai.com/account/api-keys
    apiKey: '********************************************************************************************************************************************************************',
    endpoint: 'https://api.openai.com/v1/chat/completions',
    model: 'gpt-4o-mini',
    enabled: false  // 👈 Set this to true after updating the key
  }
};

// Validate if API keys are properly configured
export function validateApiConfig(): { 
  hasValidConfig: boolean; 
  availableServices: string[];
  issues: string[];
} {
  const availableServices: string[] = [];
  const issues: string[] = [];

  Object.entries(AI_CONFIG).forEach(([name, config]) => {
    if (config.enabled) {
      if (!config.apiKey || config.apiKey.includes('YOUR_') || config.apiKey.includes('_HERE')) {
        issues.push(`${name}: API key not properly configured`);
      } else {
        availableServices.push(name);
      }
    }
  });

  return {
    hasValidConfig: availableServices.length > 0,
    availableServices,
    issues
  };
}

// Get enabled service names (string array)
export function getEnabledServices(): (keyof typeof AI_CONFIG)[] {
  return Object.entries(AI_CONFIG)
    .filter(([name, config]) => 
      config.enabled && 
      config.apiKey && 
      !config.apiKey.includes('YOUR_') && 
      !config.apiKey.includes('_HERE')
    )
    .map(([name, config]) => name as keyof typeof AI_CONFIG);
}

// Get enabled service configurations (config object array)
export function getEnabledServiceConfigs(): AIServiceConfig[] {
  return Object.values(AI_CONFIG).filter(config => 
    config.enabled && 
    config.apiKey && 
    !config.apiKey.includes('YOUR_') && 
    !config.apiKey.includes('_HERE')
  );
}

// Test API connection
export async function testApiConnection(serviceName: keyof typeof AI_CONFIG): Promise<{
  success: boolean;
  message: string;
  details?: any;
}> {
  const config = AI_CONFIG[serviceName];
  
  if (!config.enabled) {
    return {
      success: false,
      message: `${serviceName} service not enabled`
    };
  }

  if (!config.apiKey || config.apiKey.includes('YOUR_') || config.apiKey.includes('_HERE')) {
    return {
      success: false,
      message: `${serviceName} API key not properly configured`
    };
  }

  try {
    console.log(`🧪 Testing ${serviceName} API connection...`);
    
    if (serviceName === 'claude') {
      const response = await fetch(config.endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-api-key': config.apiKey,
          'anthropic-version': '2023-06-01'
        },
        body: JSON.stringify({
          model: config.model,
          max_tokens: 10,
          messages: [
            {
              role: "user",
              content: [
                {
                  type: "text",
                  text: "Hello"
                }
              ]
            }
          ]
        })
      });

      if (response.status === 401) {
        return {
          success: false,
          message: 'Claude API key invalid'
        };
      } else if (response.status === 400) {
        const errorData = await response.json();
        return {
          success: false,
          message: 'Claude API request format error',
          details: errorData
        };
      } else if (response.ok) {
        return {
          success: true,
          message: 'Claude API connection successful!'
        };
      } else {
        const errorData = await response.json();
        return {
          success: false,
          message: `Claude API error: ${response.status}`,
          details: errorData
        };
      }
    } else if (serviceName === 'gpt4v') {
      const response = await fetch(config.endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${config.apiKey}`
        },
        body: JSON.stringify({
          model: config.model,
          messages: [
            {
              role: "user",
              content: "Hello"
            }
          ],
          max_tokens: 10
        })
      });

      if (response.status === 401) {
        return {
          success: false,
          message: 'OpenAI API key invalid'
        };
      } else if (response.status === 429) {
        return {
          success: false,
          message: 'OpenAI API quota insufficient or too many requests'
        };
      } else if (response.status === 400) {
        const errorData = await response.json();
        return {
          success: false,
          message: 'OpenAI API request format error',
          details: errorData
        };
      } else if (response.ok) {
        return {
          success: true,
          message: 'OpenAI API connection successful!'
        };
      } else {
        const errorData = await response.json();
        return {
          success: false,
          message: `OpenAI API error: ${response.status}`,
          details: errorData
        };
      }
    }

    return {
      success: false,
      message: `Unsupported service: ${serviceName}`
    };
  } catch (error) {
    console.error(`${serviceName} API test failed:`, error);
    return {
      success: false,
      message: `${serviceName} API connection failed: ${(error as Error).message}`
    };
  }
}

// Test all enabled APIs
export async function testAllEnabledApis(): Promise<Record<string, any>> {
  const results: Record<string, any> = {};
  const enabledServices = getEnabledServices();
  
  console.log('🧪 Testing all enabled APIs:', enabledServices);
  
  for (const serviceName of enabledServices) {
    results[serviceName] = await testApiConnection(serviceName);
  }
  
  return results;
} 

/**
 * Test OpenAI API key with detailed error information
 */
export async function testOpenAIAPI(): Promise<{success: boolean, error?: string, details?: any}> {
  const config = AI_CONFIG.gpt4v;
  
  if (!config.enabled || !config.apiKey) {
    return { success: false, error: 'OpenAI API not configured or disabled' };
  }

  // Simple test request
  const testBody = {
    model: config.model,
    messages: [
      {
        role: "user",
        content: "Hello, this is a test message. Please respond with just 'TEST OK'."
      }
    ],
    max_tokens: 10
  };

  try {
    console.log(`🧪 Testing OpenAI API with key: ${config.apiKey.substring(0, 20)}...`);
    console.log(`🧪 Using model: ${config.model}`);
    
    const response = await fetch(config.endpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${config.apiKey}`
      },
      body: JSON.stringify(testBody)
    });

    console.log(`🧪 OpenAI API test response status: ${response.status} ${response.statusText}`);

    if (!response.ok) {
      const errorData = await response.json();
      console.log('🧪 OpenAI API test error details:', errorData);
      
      // Specific error handling
      if (response.status === 401) {
        return { 
          success: false, 
          error: 'API key invalid or not activated', 
          details: errorData 
        };
      } else if (response.status === 429) {
        return { 
          success: false, 
          error: 'Too many requests or quota insufficient', 
          details: errorData 
        };
      } else if (response.status === 403) {
        return { 
          success: false, 
          error: 'No access permission, may need to set up billing information', 
          details: errorData 
        };
      } else {
        return { 
          success: false, 
          error: `API error: ${response.status} - ${errorData.error?.message || response.statusText}`, 
          details: errorData 
        };
      }
    }

    const data = await response.json();
    console.log('🧪 OpenAI API test successful:', data.choices?.[0]?.message?.content);
    return { success: true };

  } catch (error) {
    console.error('🧪 OpenAI API test failed:', error);
    return { 
      success: false, 
      error: `Network error: ${error instanceof Error ? error.message : String(error)}` 
    };
  }
} 