import { Platform } from 'react-native';

// API Configuration for different environments
export const API_CONFIG = {
  // Development configuration
  DEVELOPMENT: {
    // Default URLs for different platforms
    ANDROID_EMULATOR: 'http://********:5000/api',
    IOS_SIMULATOR: 'http://localhost:5000/api',
    
    // If you need to use your computer's IP address instead of localhost
    // Find your IP with: ipconfig (Windows) or ifconfig (Mac/Linux)
    // Uncomment and replace with your actual IP:
    CUSTOM_IP: 'http://***************:5000/api',
  } as {
    ANDROID_EMULATOR: string;
    IOS_SIMULATOR: string;
    CUSTOM_IP?: string;
  },
  
  // Production configuration
  PRODUCTION: {
    API_URL: 'http://**************/api',
  },
  
  // Legacy SICON API - must match backend configuration
  SICON_API: 'https://sicon-mnlweb.sicon.co.nz/WorkbenchTest/api',
};

export const getEnvironmentInfo = () => {
  return {
    __DEV__,
    isProduction: !__DEV__,
    platform: Platform.OS,
    currentApiUrl: getApiBaseUrl(),
  };
};

// Function to get the correct base URL based on platform and environment
export const getApiBaseUrl = (): string => {
  const isDev = __DEV__;

  const logMessage = `API_CONFIG: Environment check - __DEV__: ${__DEV__}, isDev: ${isDev}, Platform: ${Platform.OS}`;
  console.log(logMessage);
  // console.warn(logMessage);

  if (isDev) {
    const devMessage = 'API_CONFIG: Development mode detected';
    console.log(devMessage);
    // console.warn(devMessage);

    // Check if there's a custom IP configured
    if (API_CONFIG.DEVELOPMENT.CUSTOM_IP) {
      const customMessage = `API_CONFIG: Using custom IP - ${API_CONFIG.DEVELOPMENT.CUSTOM_IP}`;
      console.log(customMessage);
      // console.warn(customMessage);
      return API_CONFIG.DEVELOPMENT.CUSTOM_IP;
    }

    // Use platform-specific URLs
    if (Platform.OS === 'android') {
      const androidMessage = 'API_CONFIG: Android emulator detected - using ********';
      console.log(androidMessage);
      // console.warn(androidMessage);
      return API_CONFIG.DEVELOPMENT.ANDROID_EMULATOR;
    } else if (Platform.OS === 'ios') {
      const iosMessage = 'API_CONFIG: iOS simulator detected - using localhost';
      console.log(iosMessage);
      // console.warn(iosMessage);
      return API_CONFIG.DEVELOPMENT.IOS_SIMULATOR;
    } else {
      const webMessage = 'API_CONFIG: Web/Other platform detected - using localhost';
      console.log(webMessage);
      // console.warn(webMessage);
      return API_CONFIG.DEVELOPMENT.IOS_SIMULATOR;
    }
  } else {
    const prodMessage = `API_CONFIG: Production mode - using production API: ${API_CONFIG.PRODUCTION.API_URL}`;
    console.log(prodMessage);
    // console.warn(prodMessage);
    // console.error(prodMessage);
    return API_CONFIG.PRODUCTION.API_URL;
  }
};

// Network connectivity test function
export const testApiConnection = async (baseUrl: string): Promise<boolean> => {
  try {
    console.log('🔍 Testing connection to:', baseUrl);
    
    const response = await fetch(`${baseUrl}/health`, {
      method: 'GET',
      timeout: 5000,
    } as any);
    
    const isConnected = response.ok;
    console.log(isConnected ? '✅ Connection successful' : '❌ Connection failed');
    return isConnected;
  } catch (error) {
    console.error('❌ Connection test failed:', error);
    return false;
  }
};

// Helper function to get your computer's IP address instructions
export const getIPAddressInstructions = (): string => {
  const instructions = `
🔧 How to find your computer's IP address:

Windows:
1. Open Command Prompt (cmd)
2. Type: ipconfig
3. Look for "IPv4 Address" under your network adapter

Mac/Linux:
1. Open Terminal
2. Type: ifconfig
3. Look for "inet" address (usually starts with 192.168 or 10.0)

Example IP addresses:
- *************
- ************
- **********

Then update the CUSTOM_IP in apiConfig.ts with your IP address.
`;
  
  return instructions;
};

// Export for easier access
export const apiConfig = API_CONFIG;

export default {
  getApiBaseUrl,
  testApiConnection,
  getIPAddressInstructions,
  API_CONFIG,
}; 