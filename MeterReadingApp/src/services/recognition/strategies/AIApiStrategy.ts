import { Platform } from 'react-native';
import NetInfo from '@react-native-community/netinfo';
import RNFS from 'react-native-fs';
import {
  IRecognitionStrategy,
  RecognitionResult,
  RecognitionConfig,
  MeterType,
  ImageQuality,
  RecognitionError,
} from '../types';
import { 
  AI_CONFIG, 
  AIServiceConfig, 
  validateApiConfig, 
  getEnabledServices,
  testAllEnabledApis,
  testOpenAIAPI 
} from '../../../config/ai-config';

export class AIApiStrategy implements IRecognitionStrategy {
  readonly name = 'AIApi';
  readonly priority = 1; // Highest priority when network available

  private preferredService: keyof typeof AI_CONFIG = 'claude';
  private networkTimeout = 15000; // 15 seconds timeout for API calls

  constructor() {
    // Validate configuration on startup
    this.validateConfiguration();
    // Test API connections asynchronously
    this.testApiConnections();
  }

  private validateConfiguration(): void {
    const validation = validateApiConfig();
    
    if (!validation.hasValidConfig) {
      console.warn('🔑 AIApiStrategy: No valid API keys configured');
      console.warn('📝 Please configure API keys in: src/config/ai-config.ts');
      
      validation.issues.forEach(issue => {
        console.warn(`   - ${issue}`);
      });
    } else {
      console.log('✅ AIApiStrategy: API configuration validation successful');
      console.log(`🔑 Available services: ${validation.availableServices.join(', ')}`);
    }
  }

  private async testApiConnections(): Promise<void> {
    try {
      console.log('🧪 AIApiStrategy: Testing API connections...');
      const results = await testAllEnabledApis();
      
      Object.entries(results).forEach(([service, result]) => {
        if (result.success) {
          console.log(`✅ ${service}: ${result.message}`);
        } else {
          console.error(`❌ ${service}: ${result.message}`);
          if (result.details) {
            console.error(`   详细信息:`, result.details);
          }
        }
      });
      
    } catch (error) {
      console.error('🧪 AIApiStrategy: API connection test failed:', error);
    }
  }

  canHandle(imageType: MeterType, imageQuality: ImageQuality): boolean {
    // Check if we have any valid API configuration
    const validation = validateApiConfig();
    return validation.hasValidConfig;
  }

  async recognize(imageUri: string, config: RecognitionConfig): Promise<RecognitionResult> {
    const startTime = Date.now();
    
    try {
      console.log('AIApiStrategy: Starting AI-powered recognition');

      // Step 1: Check network connectivity
      const isConnected = await this.checkNetworkConnectivity();
      if (!isConnected) {
        throw new RecognitionError('No network connection available', this.name);
      }

      // Step 2: Convert image to base64
      const { base64, mediaType } = await this.imageToBase64(imageUri);
      
      // Step 3: Call AI service with fallback
      const result = await this.callAIServiceWithFallback(base64, imageUri, mediaType);
      
      const processingTime = Date.now() - startTime;
      
      console.log(`AIApiStrategy: AI recognition completed in ${processingTime}ms`);
      
      return {
        ...result,
        strategy: this.name,
        processingTime,
        metadata: {
          ...result.metadata,
          processingSteps: [
            'Network connectivity check',
            'Image base64 conversion', 
            `AI service call (${this.preferredService})`,
            'Response parsing and validation'
          ],
          originalImageUri: imageUri,
          aiService: this.preferredService,
        },
      };
      
    } catch (error) {
      console.error('AIApiStrategy: Recognition failed:', error);
      throw new RecognitionError(
        `AI API recognition failed: ${(error as Error).message}`,
        this.name,
        error as Error
      );
    }
  }

  /**
   * Check network connectivity
   */
  private async checkNetworkConnectivity(): Promise<boolean> {
    try {
      const netInfo = await NetInfo.fetch();
      const isConnected = netInfo.isConnected && netInfo.isInternetReachable;
      console.log(`AIApiStrategy: Network status - Connected: ${isConnected}`);
      return !!isConnected;
    } catch (error) {
      console.error('AIApiStrategy: Network check failed:', error);
      return false;
    }
  }

  /**
   * Convert image to base64 and detect format
   */
  private async imageToBase64(imageUri: string): Promise<{base64: string, mediaType: string}> {
    try {
      let imagePath = imageUri;
      
      // Handle different URI formats
      if (imagePath.startsWith('file://')) {
        imagePath = imagePath.replace('file://', '');
      }
      
      const base64 = await RNFS.readFile(imagePath, 'base64');
      
      // Detect image format from file extension
      let mediaType = 'image/jpeg'; // default
      
      if (imagePath.toLowerCase().includes('.png')) {
        mediaType = 'image/png';
      } else if (imagePath.toLowerCase().includes('.jpg') || imagePath.toLowerCase().includes('.jpeg')) {
        mediaType = 'image/jpeg';
      } else if (imagePath.toLowerCase().includes('.webp')) {
        mediaType = 'image/webp';
      } else if (imagePath.toLowerCase().includes('.gif')) {
        mediaType = 'image/gif';
      } else {
        // Try to detect from base64 header (magic bytes)
        if (base64.startsWith('iVBOR')) {
          mediaType = 'image/png';
        } else if (base64.startsWith('/9j/')) {
          mediaType = 'image/jpeg';
        } else if (base64.startsWith('UklGR')) {
          mediaType = 'image/webp';
        } else if (base64.startsWith('R0lGOD')) {
          mediaType = 'image/gif';
        }
      }
      
      console.log(`AIApiStrategy: Image converted to base64, size: ${base64.length} chars, detected format: ${mediaType}`);
      console.log(`AIApiStrategy: Image path: ${imagePath}`);
      
      return {
        base64,
        mediaType
      };
    } catch (error) {
      console.error('AIApiStrategy: Image conversion failed:', error);
      throw new Error('Failed to convert image to base64');
    }
  }

  /**
   * Call AI service with fallback between different providers
   */
  private async callAIServiceWithFallback(base64Image: string, originalUri: string, mediaType: string): Promise<RecognitionResult> {
    const services: (keyof typeof AI_CONFIG)[] = getEnabledServices();
    let lastError: Error | null = null;

    console.log(`AIApiStrategy: Available AI services: [${services.join(', ')}]`);
    console.log(`AIApiStrategy: Will try ${services.length} services in order`);

    for (let i = 0; i < services.length; i++) {
      const serviceName = services[i];
      try {
        console.log(`AIApiStrategy: Attempting service ${i + 1}/${services.length}: ${serviceName}`);
        const result = await this.callAIService(serviceName, base64Image, originalUri, mediaType);
        console.log(`AIApiStrategy: ✅ ${serviceName} service succeeded!`);
        this.preferredService = serviceName; // Remember successful service
        return result;
      } catch (error) {
        console.error(`AIApiStrategy: ❌ ${serviceName} service failed:`, error);
        lastError = error as Error;
        
        if (i < services.length - 1) {
          console.log(`AIApiStrategy: 🔄 Trying next service: ${services[i + 1]}`);
        } else {
          console.log(`AIApiStrategy: 💥 All ${services.length} AI services have failed`);
        }
        continue;
      }
    }

    throw new Error(`All AI services failed. Last error: ${lastError?.message}`);
  }

  /**
   * Call specific AI service
   */
  private async callAIService(serviceName: keyof typeof AI_CONFIG, base64Image: string, originalUri: string, mediaType: string): Promise<RecognitionResult> {
    const config = AI_CONFIG[serviceName];
    
    switch (serviceName) {
      case 'claude':
        return await this.callClaudeAPI(config, base64Image, originalUri, mediaType);
      case 'gpt4v':
        return await this.callOpenAIAPI(config, base64Image, originalUri, mediaType);
      default:
        throw new Error(`Unsupported AI service: ${serviceName}`);
    }
  }

  /**
   * Call Claude API
   */
  private async callClaudeAPI(config: AIServiceConfig, base64Image: string, originalUri: string, mediaType: string): Promise<RecognitionResult> {
    const prompt = `Analyze this water meter image and extract the COMPLETE current reading.

CRITICAL INSTRUCTIONS:
- Read BOTH the black digits (main reading) AND red digits (decimal part)
- Combine them into one complete number with decimal point
- Format: [BLACK_DIGITS].[RED_DIGITS] (e.g., "06194.95")
- If you see black "06194" and red "95", return "06194.95"
- If no red digits visible, just return the black digits (e.g., "06194")
- If unclear, return "UNCLEAR"
- NO explanations, NO units, NO additional text

Water meter format explanation:
- Black digits = main meter reading (cubic meters)
- Red digits = decimal fraction (sub-units)
- Together they form the complete reading

Example responses:
- "06194.95" (black: 06194, red: 95)
- "12345.67" (black: 12345, red: 67)
- "00123" (only black digits visible)
- "UNCLEAR"

Just the complete number!`;

    const requestBody = {
      model: config.model,
      max_tokens: 100,
      messages: [
        {
          role: "user",
          content: [
            {
              type: "text",
              text: prompt
            },
            {
              type: "image",
              source: {
                type: "base64",
                media_type: mediaType,
                data: base64Image
              }
            }
          ]
        }
      ]
    };

    console.log(`Claude API: Sending request to ${config.endpoint} with model ${config.model}`);
    console.log(`Claude API: Request body size: ${JSON.stringify(requestBody).length} chars`);

    // Create manual abort controller for React Native compatibility
    const abortController = new AbortController();
    const timeoutId = setTimeout(() => {
      abortController.abort();
    }, this.networkTimeout);

    try {
      const response = await fetch(config.endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-api-key': config.apiKey,
          'anthropic-version': '2023-06-01'
        },
        body: JSON.stringify(requestBody),
        signal: abortController.signal
      });

      clearTimeout(timeoutId);

      console.log(`Claude API: Response status: ${response.status} ${response.statusText}`);

      if (!response.ok) {
        // Try to get more detailed error information
        let errorMessage = `Claude API error: ${response.status} ${response.statusText}`;
        try {
          const errorData = await response.json();
          console.log('Claude API error details:', errorData);
          if (errorData.error?.message) {
            errorMessage += ` - ${errorData.error.message}`;
          }
          if (errorData.error?.type) {
            errorMessage += ` (${errorData.error.type})`;
          }
        } catch (parseError) {
          console.log('Could not parse Claude API error response');
        }
        throw new Error(errorMessage);
      }

      const data = await response.json();
      console.log('Claude API response structure:', Object.keys(data));
      
      const reading = data.content?.[0]?.text?.trim() || '';
      console.log(`Claude API: Extracted reading: "${reading}"`);
      
      if (!reading || reading === 'UNCLEAR') {
        throw new Error('Claude could not extract a clear reading');
      }

      // Validate the reading is numeric
      const numericReading = this.validateAndCleanReading(reading);
      
      return {
        reading: numericReading,
        confidence: 0.95, // High confidence for AI services
        strategy: this.name,
        processingTime: 0, // Will be set by caller
        metadata: {
          processingSteps: ['Claude AI analysis'],
          originalImageUri: originalUri,
          aiResponse: reading,
          cleanedReading: numericReading,
        }
      };
    } catch (error) {
      clearTimeout(timeoutId);
      if (error instanceof Error && error.name === 'AbortError') {
        throw new Error('Claude API request timed out');
      }
      throw error;
    }
  }

  /**
   * Call OpenAI GPT-4V API
   */
  private async callOpenAIAPI(config: AIServiceConfig, base64Image: string, originalUri: string, mediaType: string): Promise<RecognitionResult> {
    const prompt = `Analyze this water meter image and extract the COMPLETE current reading.

CRITICAL INSTRUCTIONS:
- Read BOTH the black digits (main reading) AND red digits (decimal part)
- Combine them into one complete number with decimal point
- Format: [BLACK_DIGITS].[RED_DIGITS] (e.g., "06194.95")
- If you see black "06194" and red "95", return "06194.95"
- If no red digits visible, just return the black digits (e.g., "06194")
- If unclear, return "UNCLEAR"
- NO explanations, NO units, NO additional text

Water meter format explanation:
- Black digits = main meter reading (cubic meters)
- Red digits = decimal fraction (sub-units)
- Together they form the complete reading

Example responses:
- "06194.95" (black: 06194, red: 95)
- "12345.67" (black: 12345, red: 67)
- "00123" (only black digits visible)
- "UNCLEAR"

Just the complete number!`;

    const requestBody = {
      model: config.model,
      max_tokens: 100,
      messages: [
        {
          role: "user",
          content: [
            {
              type: "text",
              text: prompt
            },
            {
              type: "image_url",
              image_url: {
                url: `data:${mediaType};base64,${base64Image}`,
                detail: "high"
              }
            }
          ]
        }
      ]
    };

    console.log(`OpenAI API: Sending request to ${config.endpoint} with model ${config.model}`);
    console.log(`OpenAI API: Request body size: ${JSON.stringify(requestBody).length} chars`);

    // Create manual abort controller for React Native compatibility
    const abortController = new AbortController();
    const timeoutId = setTimeout(() => {
      abortController.abort();
    }, this.networkTimeout);

    try {
      const response = await fetch(config.endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${config.apiKey}`
        },
        body: JSON.stringify(requestBody),
        signal: abortController.signal
      });

      clearTimeout(timeoutId);

      console.log(`OpenAI API: Response status: ${response.status} ${response.statusText}`);

      if (!response.ok) {
        // Handle specific error codes
        let errorMessage = `OpenAI API error: ${response.status} ${response.statusText}`;
        
        if (response.status === 429) {
          errorMessage = 'OpenAI API quota insufficient or too many requests, please try again later or check your account balance';
        } else if (response.status === 401) {
          errorMessage = 'OpenAI API key invalid, please check your API key configuration';
        } else if (response.status === 400) {
          errorMessage = 'OpenAI API request format error';
        }

        // Try to get more detailed error information
        try {
          const errorData = await response.json();
          console.log('OpenAI API error details:', errorData);
          if (errorData.error?.message) {
            errorMessage += ` - ${errorData.error.message}`;
          }
          if (errorData.error?.code) {
            errorMessage += ` (${errorData.error.code})`;
          }
        } catch (parseError) {
          console.log('Could not parse OpenAI API error response');
        }
        
        throw new Error(errorMessage);
      }

      const data = await response.json();
      console.log('OpenAI API response structure:', Object.keys(data));
      
      const reading = data.choices?.[0]?.message?.content?.trim() || '';
      console.log(`OpenAI API: Extracted reading: "${reading}"`);
      
      if (!reading || reading === 'UNCLEAR') {
        throw new Error('GPT-4V could not extract a clear reading');
      }

      // Validate the reading is numeric
      const numericReading = this.validateAndCleanReading(reading);
      
      return {
        reading: numericReading,
        confidence: 0.93, // Slightly lower than Claude
        strategy: this.name,
        processingTime: 0, // Will be set by caller
        metadata: {
          processingSteps: ['GPT-4V AI analysis'],
          originalImageUri: originalUri,
          aiResponse: reading,
          cleanedReading: numericReading,
        }
      };
    } catch (error) {
      clearTimeout(timeoutId);
      if (error instanceof Error && error.name === 'AbortError') {
        throw new Error('OpenAI API request timed out');
      }
      throw error;
    }
  }

  /**
   * Validate and clean the reading value
   */
  private validateAndCleanReading(reading: string): string {
    console.log(`AIApiStrategy: Validating reading: "${reading}"`);
    
    // First try to extract numbers using regex to handle AI responses with explanations
    const numberMatches = reading.match(/\b\d+\.?\d*\b/g);
    
    if (!numberMatches || numberMatches.length === 0) {
      throw new Error(`No numeric value found in AI response: ${reading}`);
    }
    
    // Take the first (and usually only) number found
    let cleaned = numberMatches[0];
    console.log(`AIApiStrategy: Extracted number: "${cleaned}"`);
    
    // Ensure we have a valid number
    const value = parseFloat(cleaned);
    if (isNaN(value)) {
      throw new Error(`Could not parse number: ${cleaned}`);
    }
    
    if (value < 0) {
      throw new Error(`Reading cannot be negative: ${value}`);
    }
    
    if (value > 99999999) {
      throw new Error(`Reading seems too large: ${value}`);
    }
    
    // Format to reasonable precision
    if (cleaned.includes('.')) {
      // Keep up to 3 decimal places and remove trailing zeros
      return value.toFixed(3).replace(/\.?0+$/, '');
    }
    
    console.log(`AIApiStrategy: Valid reading extracted: ${value}`);
    return value.toString();
  }

  /**
   * Set preferred AI service
   */
  setPreferredService(service: keyof typeof AI_CONFIG): void {
    this.preferredService = service;
    console.log(`AIApiStrategy: Preferred service set to ${service}`);
  }

  /**
   * Get current network status
   */
  async getNetworkStatus(): Promise<{isConnected: boolean, connectionType?: string}> {
    try {
      const netInfo = await NetInfo.fetch();
      return {
        isConnected: !!(netInfo.isConnected && netInfo.isInternetReachable),
        connectionType: netInfo.type
      };
    } catch (error) {
      return { isConnected: false };
    }
  }
} 