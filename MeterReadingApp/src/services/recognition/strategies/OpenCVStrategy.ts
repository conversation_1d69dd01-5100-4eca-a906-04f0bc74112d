import {
  IRecognitionStrategy,
  RecognitionResult,
  RecognitionConfig,
  MeterType,
  ImageQuality,
  RecognitionError,
  ROI,
  DigitInfo,
} from '../types';

const RNFS = require('react-native-fs');

// Import OpenCV processor reference type
export interface OpenCVProcessorInstance {
  processWaterMeterImage: (imageUri: string) => Promise<{
    reading: string;
    confidence: number;
    roiBounds: ROI;
    detectedDigits: DigitInfo[];
    processedImageUri: string;
  }>;
}

export class OpenCVStrategy implements IRecognitionStrategy {
  readonly name = 'OpenCV';
  readonly priority = 1; // Higher priority, preferred strategy

  private processor: OpenCVProcessorInstance | null = null;

  constructor(processor?: OpenCVProcessorInstance) {
    this.processor = processor || null;
  }

  // Method to set the processor instance
  setProcessor(processor: OpenCVProcessorInstance): void {
    this.processor = processor;
    console.log('OpenCVStrategy: Processor instance set');
  }

  canHandle(imageType: MeterType, imageQuality: ImageQuality): boolean {
    console.log(`OpenCV canHandle check: processor=${!!this.processor}, imageType=${imageType}, imageQuality=${imageQuality}`);
    const canHandle = this.processor !== null;
    console.log(`OpenCV canHandle result: ${canHandle}`);
    return canHandle;
  }

  async recognize(imageUri: string, config: RecognitionConfig): Promise<RecognitionResult> {
    const startTime = Date.now();
    console.log(`OpenCVStrategy: Starting recognition for: ${imageUri}`);
    
    if (!this.processor) {
      throw new RecognitionError('OpenCV processor not initialized', this.name);
    }

    try {
      console.log('OpenCVStrategy: Calling OpenCV processor...');
      const result = await this.processor.processWaterMeterImage(imageUri);
      
      const processingTime = Date.now() - startTime;
      console.log(`OpenCVStrategy: Processor returned result:`, {
        reading: result.reading,
        confidence: result.confidence,
        hasProcessedImageUri: !!result.processedImageUri
      });
      
      // 检查是否是 EXTREME_OPENCV_PROCESSED 结果
      if (result.reading === 'EXTREME_OPENCV_PROCESSED') {
        console.log('OpenCVStrategy: Detected EXTREME_OPENCV_PROCESSED result');
        
        // 从结果中提取 processedImageBase64
        const opencvResult = result as any;
        if (opencvResult.processedImageBase64) {
          console.log('OpenCVStrategy: Found processedImageBase64 in result, length:', opencvResult.processedImageBase64.length);
          
          try {
            // 将 base64 保存为文件
            const processedImageUri = await this.saveBase64AsFile(
              opencvResult.processedImageBase64, 
              'opencv_processed_image.png'
            );
            console.log('OpenCVStrategy: Saved processed image to:', processedImageUri);
            
            const recognitionResult: RecognitionResult = {
              reading: 'OpenCV Image Enhanced', // 用户友好的消息
              confidence: 1.0,
              strategy: this.name,
              processingTime,
              metadata: {
                roiBounds: result.roiBounds || { x: 0, y: 0, width: 1, height: 1 },
                processingSteps: [
                  'OpenCV extreme image processing',
                  'Color inversion applied',
                  'High contrast enhancement (3.0x)',
                  'Edge detection overlay',
                  'Image saved for display'
                ],
                detectedDigits: result.detectedDigits || [],
                originalImageUri: imageUri,
                processedImageUri: processedImageUri, // 🔥 关键！设置处理后的图像 URI
              },
            };
            
            console.log(`OpenCVStrategy: EXTREME processing completed in ${processingTime}ms, processedImageUri: ${processedImageUri}`);
            return recognitionResult;
            
          } catch (saveError) {
            console.error('OpenCVStrategy: Failed to save processed image:', saveError);
            // 即使保存失败，也返回结果但没有处理后的图像
            const recognitionResult: RecognitionResult = {
              reading: 'OpenCV Processing Complete',
              confidence: 1.0,
              strategy: this.name,
              processingTime,
              metadata: {
                roiBounds: result.roiBounds || { x: 0, y: 0, width: 1, height: 1 },
                processingSteps: [
                  'OpenCV extreme image processing',
                  'Color inversion applied',
                  'High contrast enhancement (3.0x)',
                  'Edge detection overlay',
                  'Image save failed - displaying processing info only'
                ],
                detectedDigits: result.detectedDigits || [],
                originalImageUri: imageUri,
              },
            };
            return recognitionResult;
          }
        } else {
          console.warn('OpenCVStrategy: EXTREME_OPENCV_PROCESSED but no processedImageBase64 found');
        }
      }
      
      // 处理普通的 OpenCV 结果
      const recognitionResult: RecognitionResult = {
        reading: result.reading || '0',
        confidence: result.confidence || 0.0,
        strategy: this.name,
        processingTime,
        metadata: {
          roiBounds: result.roiBounds || { x: 0, y: 0, width: 1, height: 1 },
          processingSteps: [
            'OpenCV image loading',
            'Grayscale conversion',
            'Gaussian blur for noise reduction',
            'ROI detection (digit display area)',
            'Adaptive thresholding',
            'Contour detection for digits',
            'Digit recognition and validation'
          ],
          detectedDigits: result.detectedDigits || [],
          originalImageUri: imageUri,
          processedImageUri: result.processedImageUri || imageUri,
        },
      };

      console.log(`OpenCVStrategy: Recognition completed in ${processingTime}ms, result: ${result.reading}`);
      return recognitionResult;
      
    } catch (error) {
      console.error('OpenCVStrategy: Recognition failed:', error);
      throw new RecognitionError(
        `OpenCV recognition failed: ${(error as Error).message}`,
        this.name,
        error as Error
      );
    }
  }

  // 将 base64 保存为文件的辅助方法
  private async saveBase64AsFile(base64Data: string, filename: string): Promise<string> {
    try {
      const timestamp = Date.now();
      const path = `${RNFS.CachesDirectoryPath}/${timestamp}_${filename}`;
      
      // 移除 data URL 前缀（如果存在）
      const cleanBase64 = base64Data.replace(/^data:image\/[a-z]+;base64,/, '');
      
      console.log('OpenCVStrategy: Saving base64 to file:', path);
      await RNFS.writeFile(path, cleanBase64, 'base64');
      
      const fileUri = `file://${path}`;
      console.log('OpenCVStrategy: File saved successfully:', fileUri);
      
      // 验证文件是否存在
      const exists = await RNFS.exists(path);
      if (!exists) {
        throw new Error('File was not created successfully');
      }
      
      return fileUri;
    } catch (error) {
      console.error('OpenCVStrategy: Failed to save base64 as file:', error);
      throw new Error(`Failed to save processed image: ${(error as Error).message}`);
    }
  }

  validateResult(result: RecognitionResult): boolean {
    // OpenCV 特定验证
    const reading = result.reading;
    
    // 允许 OpenCV 处理结果
    if (reading === 'OpenCV Image Enhanced' || 
        reading === 'OpenCV Processing Complete' || 
        reading === 'OpenCV Processing Completed') {
      console.log('OpenCVStrategy: Validated OpenCV processing result:', reading);
      return true;
    }
    
    // 应该有检测到的单个数字
    if (!result.metadata.detectedDigits || result.metadata.detectedDigits.length === 0) {
      return false;
    }
    
    // 所有检测到的数字应该有合理的置信度
    const avgDigitConfidence = result.metadata.detectedDigits.reduce(
      (sum, digit) => sum + digit.confidence, 0
    ) / result.metadata.detectedDigits.length;
    
    if (avgDigitConfidence < 0.5) {
      return false;
    }

    return true;
  }
}