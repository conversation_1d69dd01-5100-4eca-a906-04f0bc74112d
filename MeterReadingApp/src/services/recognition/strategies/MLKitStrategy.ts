import { Platform } from 'react-native';
import TextRecognition from '@react-native-ml-kit/text-recognition';
import {
  IRecognitionStrategy,
  RecognitionResult,
  RecognitionConfig,
  MeterType,
  ImageQuality,
  RecognitionError,
} from '../types';
import ImageProcessingService from '../../ImageProcessingService';

export class MLKitStrategy implements IRecognitionStrategy {
  readonly name = 'MLKit';
  readonly priority = 2; // Lower priority, used as fallback

  private imageProcessingService = ImageProcessingService.getInstance();

  canHandle(imageType: MeterType, imageQuality: ImageQuality): boolean {
    // ML Kit can handle any type but works better with digital displays
    // and good quality images
    return true;
  }

  async recognize(imageUri: string, config: RecognitionConfig): Promise<RecognitionResult> {
    const startTime = Date.now();
    
    try {
      // console.log(`MLKitStrategy: Starting recognition for image: ${imageUri}`);

      // Step 1: Preprocess the image
      const preprocessingResult = await this.preprocess(imageUri);
      
      // Step 2: Perform OCR recognition
      const reading = await this.performOcrRecognition(preprocessingResult);
      
      const processingTime = Date.now() - startTime;
      
      if (!reading || reading.length === 0) {
        throw new RecognitionError('No valid meter reading found', this.name);
      }

      const result: RecognitionResult = {
        reading,
        confidence: this.calculateConfidence(reading),
        strategy: this.name,
        processingTime,
        metadata: {
          processingSteps: ['Image preprocessing', 'ML Kit OCR', 'Pattern extraction'],
          originalImageUri: imageUri,
          processedImageUri: preprocessingResult,
        },
      };

      console.log(`MLKitStrategy: Recognition completed in ${processingTime}ms, result: ${reading}`);
      return result;
      
    } catch (error) {
      console.error('MLKitStrategy: Recognition failed:', error);
      throw new RecognitionError(
        `ML Kit recognition failed: ${(error as Error).message}`,
        this.name,
        error as Error
      );
    }
  }

  async preprocess(imageUri: string): Promise<string> {
    try {
      
      // Use the existing preprocessing logic for mechanical meters
      const result = await this.imageProcessingService.preprocessMechanicalMeter(imageUri);
      
      return result.processedImageUri;
    } catch (error) {
      console.error('MLKitStrategy: Preprocessing failed:', error);
      // Return original image if preprocessing fails
      return imageUri;
    }
  }

  private async performOcrRecognition(imagePath: string): Promise<string> {
    try {

      // Ensure Android uses file:// format
      let correctPath = imagePath;
      if (Platform.OS === 'android' && !imagePath.startsWith('file://')) {
        correctPath = `file://${imagePath}`;
      }

      const result = await TextRecognition.recognize(correctPath);

      const meterReading = this.extractMeterReading(result);
      return meterReading;
    } catch (error) {
      console.error('MLKitStrategy: OCR processing error:', error);
      throw error;
    }
  }

  // 在 MLKitStrategy.ts 中替换 extractMeterReading 方法
  private extractMeterReading(ocrResult: any): string {
    let allDigitSequences: string[] = [];

    if (!ocrResult || !ocrResult.blocks || !Array.isArray(ocrResult.blocks)) {
      console.log('MLKitStrategy: Invalid OCR result or no text blocks');
      return '';
    }

    console.log('MLKitStrategy: Processing OCR blocks...');

    ocrResult.blocks.forEach((block: any, index: number) => {
      if (block && block.text) {
        const blockText = block.text.trim();
        console.log(`MLKitStrategy: Block ${index}: "${blockText}"`);
        
        // 字符替换（处理常见OCR错误）
        let cleanedText = blockText
          .replace(/O/g, '0')  // O → 0
          .replace(/o/g, '0')  // o → 0  
          .replace(/I/g, '1')  // I → 1
          .replace(/l/g, '1')  // l → 1
          .replace(/S/g, '5')  // S → 5
          .replace(/s/g, '5')  // s → 5
          
        // 提取纯数字序列
        const digitSequences = cleanedText.match(/\d+(\.\d+)?/g);
        
        if (digitSequences) {
          digitSequences.forEach(seq => {
            if (seq.length >= 4) { // 至少4位数字
              allDigitSequences.push(seq);
              console.log(`MLKitStrategy: Found potential reading from "${blockText}": ${seq}`);
            }
          });
        }
      }
    });

    console.log('MLKitStrategy: All potential readings:', allDigitSequences);

    if (allDigitSequences.length === 0) {
      console.log('MLKitStrategy: No potential readings found');
      return '';
    }

    // 过滤和选择最佳读数
    const validReadings = allDigitSequences.filter(seq => {
      const value = parseFloat(seq);
      return value >= 0 && value <= 99999999 && seq.length >= 4 && seq.length <= 8;
    });

    if (validReadings.length === 0) {
      console.log('MLKitStrategy: No valid readings found');
      return '';
    }

    // 简单的优先级：
    // 1. 6-7位数字（典型水表读数）
    // 2. 其他长度按从长到短
    const sortedReadings = validReadings.sort((a, b) => {
      const aLen = a.length;
      const bLen = b.length;
      
      // 优先6-7位
      const aIsPreferred = (aLen >= 6 && aLen <= 7);
      const bIsPreferred = (bLen >= 6 && bLen <= 7);
      
      if (aIsPreferred && !bIsPreferred) return -1;
      if (bIsPreferred && !aIsPreferred) return 1;
      
      // 其他情况按长度降序
      return bLen - aLen;
    });

    const bestReading = sortedReadings[0];
    console.log('MLKitStrategy: Selected best reading:', bestReading);
    console.log('MLKitStrategy: All valid candidates:', sortedReadings);

    return bestReading;
  }

  private calculateConfidence(reading: string): number {
    if (!reading || reading.length === 0) {
      return 0;
    }

    let confidence = 0.5; // Base confidence

    // Higher confidence for readings that look like typical water meter values
    const digits = reading.split('.')[0].length;
    
    if (digits >= 4 && digits <= 5) {
      confidence += 0.3; // Good digit count
    }
    
    if (reading.includes('.')) {
      confidence += 0.1; // Decimal readings are more precise
    }
    
    // Check if reading is in reasonable range
    const value = parseFloat(reading);
    if (value >= 100 && value <= 99999) {
      confidence += 0.2; // Reasonable value range
    }

    return Math.min(confidence, 1.0);
  }

  validateResult(result: RecognitionResult): boolean {
    // Additional ML Kit specific validation
    const reading = result.reading;
    
    // Should not be obviously wrong patterns (removed 96 restriction)
    if (reading.startsWith('0000')) {
      return false;
    }
    
    // Should have reasonable confidence
    if (result.confidence < 0.3) {
      return false;
    }

    return true;
  }
} 