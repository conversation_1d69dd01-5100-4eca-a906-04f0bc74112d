import {
  IRecognitionStrategy,
  RecognitionResult,
  RecognitionConfig,
  MeterType,
  ImageQuality,
  RecognitionError,
} from '../types';
import NetInfo from '@react-native-community/netinfo';

export class HybridStrategy implements IRecognitionStrategy {
  readonly name = 'Hybrid';
  readonly priority = 0; // Management layer priority

  private strategies: IRecognitionStrategy[];

  constructor(strategies: IRecognitionStrategy[]) {
    this.strategies = strategies;
  }

  canHandle(imageType: MeterType, imageQuality: ImageQuality): boolean {
    // Hybrid can handle anything if at least one strategy can
    return this.strategies.some(s => s.canHandle(imageType, imageQuality));
  }

  async recognize(imageUri: string, config: RecognitionConfig): Promise<RecognitionResult> {
    const startTime = Date.now();
    
    try {
      console.log(`HybridStrategy: Starting intelligent recognition with network-first approach`);

      // Step 1: Check network connectivity
      const networkStatus = await this.checkNetworkConnectivity();
      console.log(`HybridStrategy: Network status:`, networkStatus);

      // Step 2: Select strategies based on network availability and user preference
      const selectedStrategies = await this.selectStrategiesBasedOnNetwork(networkStatus);
      console.log(`HybridStrategy: Selected strategies:`, selectedStrategies.map(s => s.name));

      // Step 3: Execute strategies with intelligent fallback
      const result = await this.executeStrategiesWithFallback(
        imageUri, 
        config, 
        selectedStrategies,
        networkStatus
      );

      const totalTime = Date.now() - startTime;
      console.log(`HybridStrategy: Completed in ${totalTime}ms with strategy: ${result.strategy}`);

      // Add hybrid metadata with network information
      result.metadata.processingSteps.unshift(
        networkStatus.isConnected 
          ? 'Network available - AI API prioritized' 
          : 'Network unavailable - Local processing only'
      );
      result.metadata.networkStatus = networkStatus;
      result.processingTime = totalTime;

      return result;

    } catch (error) {
      console.error('HybridStrategy: All strategies failed:', error);
      throw new RecognitionError(
        `Hybrid strategy failed: ${(error as Error).message}`,
        this.name,
        error as Error
      );
    }
  }

  /**
   * Check network connectivity for AI API availability
   */
  private async checkNetworkConnectivity(): Promise<{isConnected: boolean, connectionType?: string, quality: 'good' | 'poor' | 'none'}> {
    try {
      const netInfo = await NetInfo.fetch();
      const isConnected = !!(netInfo.isConnected && netInfo.isInternetReachable);
      
      let quality: 'good' | 'poor' | 'none' = 'none';
      
      if (isConnected) {
        // Determine connection quality for strategy selection
        if (netInfo.type === 'wifi' || netInfo.type === 'ethernet') {
          quality = 'good';
        } else if (netInfo.type === 'cellular') {
          // Check cellular details if available
          const details = netInfo.details as any;
          if (details?.cellularGeneration === '4g' || details?.cellularGeneration === '5g') {
            quality = 'good';
          } else {
            quality = 'poor';
          }
        } else {
          quality = 'poor';
        }
      }
      
      return {
        isConnected,
        connectionType: netInfo.type,
        quality
      };
    } catch (error) {
      console.error('HybridStrategy: Network check failed:', error);
      return { isConnected: false, quality: 'none' };
    }
  }

  /**
   * Select strategies based on network availability - AI first when connected, local when not
   */
  private async selectStrategiesBasedOnNetwork(networkStatus: {isConnected: boolean, quality: 'good' | 'poor' | 'none'}): Promise<IRecognitionStrategy[]> {
    const aiApiStrategy = this.strategies.find(s => s.name.toLowerCase() === 'aiapi');
    const opencvStrategy = this.strategies.find(s => s.name.toLowerCase() === 'opencv');
    const mlkitStrategy = this.strategies.find(s => s.name.toLowerCase() === 'mlkit');

    const selectedStrategies: IRecognitionStrategy[] = [];

    // Check if AI API is actually configured and available
    const hasValidAIConfig = this.isAIApiConfigured(aiApiStrategy);
    
    if (networkStatus.isConnected && networkStatus.quality === 'good' && aiApiStrategy && hasValidAIConfig) {
      // Priority 1: AI API when network is good AND properly configured
      selectedStrategies.push(aiApiStrategy);
      console.log('HybridStrategy: AI API prioritized - good network connection and valid configuration');
      
      // Add local strategies as fallback
      if (opencvStrategy) selectedStrategies.push(opencvStrategy);
      if (mlkitStrategy) selectedStrategies.push(mlkitStrategy);
      
    } else if (networkStatus.isConnected && networkStatus.quality === 'poor' && aiApiStrategy && hasValidAIConfig) {
      // Priority 2: Try AI API but with faster timeout, then local
      selectedStrategies.push(aiApiStrategy);
      if (mlkitStrategy) selectedStrategies.push(mlkitStrategy); // Skip OpenCV for speed
      console.log('HybridStrategy: AI API with poor network - fast fallback enabled');
      
    } else {
      // Priority 3: No network or AI not configured - local processing only
      if (!hasValidAIConfig && networkStatus.isConnected) {
        console.log('HybridStrategy: AI API not configured - using local processing only');
      } else {
        console.log('HybridStrategy: No network - using local processing only');
      }
      
      // Local strategies in order of effectiveness
      if (opencvStrategy) selectedStrategies.push(opencvStrategy);
      if (mlkitStrategy) selectedStrategies.push(mlkitStrategy);
    }

    // Ensure we have at least one strategy
    if (selectedStrategies.length === 0) {
      // Fallback to all available strategies
      selectedStrategies.push(...this.strategies);
      console.log('HybridStrategy: No optimal strategies found, using all available');
    }

    return selectedStrategies;
  }

  /**
   * Execute strategies with intelligent fallback and performance optimization
   */
  private async executeStrategiesWithFallback(
    imageUri: string,
    config: RecognitionConfig,
    strategies: IRecognitionStrategy[],
    networkStatus: {isConnected: boolean, quality: 'good' | 'poor' | 'none'}
  ): Promise<RecognitionResult> {
    const results: Array<{strategy: string, result?: RecognitionResult, error?: Error}> = [];
    let enhancedImageUri: string | null = null;

    for (let i = 0; i < strategies.length; i++) {
      const strategy = strategies[i];
      const isLastStrategy = i === strategies.length - 1;
      
      try {
        console.log(`HybridStrategy: Attempting strategy ${i + 1}/${strategies.length}: ${strategy.name}`);
        
        let result: RecognitionResult;
        let strategyConfig = { ...config };

        // Adjust timeout based on network and strategy
        if (strategy.name.toLowerCase() === 'aiapi') {
          if (networkStatus.quality === 'poor') {
            strategyConfig.timeout = 8000; // Shorter timeout for poor network
          } else {
            strategyConfig.timeout = 15000; // Normal timeout for good network
          }
        }

        // Use enhanced image if available from previous OpenCV processing
        const imageToProcess = enhancedImageUri || imageUri;
        result = await strategy.recognize(imageToProcess, strategyConfig);
        
        // Handle OpenCV image enhancement
        if (strategy.name.toLowerCase() === 'opencv') {
          if ((result.reading === 'OpenCV Image Enhanced' || 
              result.reading === 'EXTREME_OPENCV_PROCESSED') && 
              result.metadata?.processedImageUri) {
            console.log('HybridStrategy: OpenCV provided enhanced image for subsequent processing');
            enhancedImageUri = result.metadata.processedImageUri;
            
            // If this is not the last strategy, continue to next strategy with enhanced image
            if (!isLastStrategy) {
              console.log('HybridStrategy: OpenCV enhancement complete, continuing to next strategy...');
              continue;
            }
          }
        }

        // Check if result is valid and has good confidence
        if (this.isGoodResult(result)) {
          console.log(`HybridStrategy: Strategy ${strategy.name} succeeded with confidence ${result.confidence}`);
          results.push({ strategy: strategy.name, result });
          
          // For AI API results or high-confidence results, return immediately
          if (strategy.name.toLowerCase() === 'aiapi' || result.confidence >= 0.9) {
            console.log(`HybridStrategy: High-confidence result from ${strategy.name}, stopping execution`);
            return result;
          }
        }
        
        results.push({ strategy: strategy.name, result });
        
        // If it's a decent result and we have fallbacks, continue to get better result
        if (result.confidence >= 0.7 && !isLastStrategy) {
          console.log(`HybridStrategy: Decent result from ${strategy.name}, trying next strategy for better accuracy`);
          continue;
        }
        
        // Return the result if it's acceptable or last strategy
        if (result.confidence >= 0.5 || isLastStrategy) {
          return result;
        }

      } catch (error) {
        console.error(`HybridStrategy: Strategy ${strategy.name} failed:`, error);
        results.push({ strategy: strategy.name, error: error as Error });
        
        // If it's AI API failure and we have network issues, inform user
        if (strategy.name.toLowerCase() === 'aiapi') {
          console.log('HybridStrategy: AI API failed, falling back to local processing...');
        }
        
        // Continue to next strategy unless it's the last one
        if (!isLastStrategy) {
          continue;
        }
      }
    }

    // If we get here, all strategies failed or returned poor results
    const bestResult = results
      .filter(r => r.result)
      .sort((a, b) => (b.result?.confidence || 0) - (a.result?.confidence || 0))[0];

    if (bestResult?.result) {
      console.log(`HybridStrategy: Returning best available result from ${bestResult.strategy}`);
      return bestResult.result;
    }

    // All strategies failed
    const errorMessages = results
      .filter(r => r.error)
      .map(r => `${r.strategy}: ${r.error?.message}`)
      .join('; ');

    throw new RecognitionError(
      `All strategies failed. ${networkStatus.isConnected ? 'Network available but AI API failed.' : 'No network - local processing failed.'} Errors: ${errorMessages}`,
      'hybrid_fallback'
    );
  }

  /**
   * Determine if a result is good enough to use immediately
   */
  private isGoodResult(result: RecognitionResult): boolean {
    // Check confidence threshold
    if (result.confidence < 0.5) {
      return false;
    }

    // Check if reading looks valid (numeric)
    const reading = result.reading.trim();
    if (!reading || reading === 'UNCLEAR' || reading === 'EXTREME_OPENCV_PROCESSED') {
      return false;
    }

    // Check if it's a valid number
    const numericValue = parseFloat(reading.replace(/[^\d.]/g, ''));
    if (isNaN(numericValue) || numericValue < 0 || numericValue > 99999999) {
      return false;
    }

    return true;
  }

  /**
   * Check if AI API is properly configured
   */
  private isAIApiConfigured(aiApiStrategy: IRecognitionStrategy | undefined): boolean {
    if (!aiApiStrategy) return false;
    
    try {
      // Import validation function
      const { validateApiConfig } = require('../../../config/ai-config');
      const validation = validateApiConfig();
      return validation.hasValidConfig;
    } catch (error) {
      console.log('HybridStrategy: Unable to validate AI config:', error);
      return false;
    }
  }

  /**
   * Analyze image to determine optimal strategy approach
   */
  private async analyzeImageForStrategy(imageUri: string): Promise<any> {
    // Basic analysis for strategy selection
    return {
      meterType: 'mechanical_circular',
      imageQuality: 'good',
      hasClearDigits: true,
      lightingCondition: 'good',
      recommendedApproach: 'ai_first_local_fallback'
    };
  }

  /**
   * Get network-aware processing summary
   */
  getProcessingSummary(networkStatus: {isConnected: boolean, quality: string}): string {
    if (networkStatus.isConnected) {
      return `AI-powered recognition (${networkStatus.quality} connection) with local fallback`;
    } else {
      return 'Local processing only (no network connection)';
    }
  }
}