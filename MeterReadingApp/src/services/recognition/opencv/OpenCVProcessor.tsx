import React, { useRef, useImperativeHandle, forwardRef, useState, useEffect } from 'react';
import { WebView } from 'react-native-webview';
import { ROI, DigitInfo } from '../types';
import { generateSecureId } from '../../../utils/secureRandom';

// Import React Native FileSystem
const RNFS = require('react-native-fs');

export interface OpenCVProcessorRef {
  processWaterMeterImage: (imageUri: string) => Promise<{
    reading: string;
    confidence: number;
    roiBounds: ROI;
    detectedDigits: DigitInfo[];
    processedImageUri: string;
  }>;
  testCommunication: () => Promise<void>;
}

interface OpenCVProcessorProps {
  onReady?: () => void;
  onError?: (error: string) => void;
}

const OpenCVProcessor = forwardRef<OpenCVProcessorRef, OpenCVProcessorProps>(
  ({ onReady, onError }, ref) => {
    
    const webViewRef = useRef<WebView>(null);
    const processingRequests = useRef<Map<string, { resolve: Function; reject: Function; }>>(new Map());
    const [webViewReady, setWebViewReady] = useState(false);
    const [openCVFullyReady, setOpenCVFullyReady] = useState(false);
    const instanceId = useRef(generateSecureId('opencv'));

    // Clear old requests on unmount
    useEffect(() => {
      return () => {
        console.log(`OpenCVProcessor v8.0: [${instanceId.current}] Cleaning up...`);
        processingRequests.current.clear();
      };
    }, []);

    useImperativeHandle(ref, () => ({
      processWaterMeterImage: async (imageUri: string) => {
        console.log(`OpenCVProcessor v8.0: [${instanceId.current}] Starting processWaterMeterImage...`);
        
        if (!webViewReady || !openCVFullyReady) {
          throw new Error(`OpenCV processor not ready - webViewReady:${webViewReady}, openCVFullyReady:${openCVFullyReady}`);
        }

        return new Promise(async (resolve, reject) => {
          try {
            // Convert image file to base64
            console.log(`OpenCVProcessor v8.0: [${instanceId.current}] Converting image to base64...`);
            let imagePath = imageUri;
            
            // Remove file:// prefix if present
            if (imagePath.startsWith('file://')) {
              imagePath = imagePath.replace('file://', '');
            }
            
            // Read image as base64
            const base64Image = await RNFS.readFile(imagePath, 'base64');
            console.log(`OpenCVProcessor v8.0: [${instanceId.current}] Image converted to base64, length:`, base64Image.length);
            
            const requestId = `${instanceId.current}_${Date.now()}`;
            processingRequests.current.set(requestId, { resolve, reject });

            const message = {
              type: 'PROCESS_WATER_METER',
              requestId,
              imageBase64: base64Image,
              originalImageUri: imageUri,
              instanceId: instanceId.current
            };

            console.log(`OpenCVProcessor v8.0: [${instanceId.current}] Sending message to WebView:`, {
              type: message.type,
              requestId: message.requestId,
              instanceId: message.instanceId,
              base64Preview: base64Image.substring(0, 50) + '...'
            });
            
            if (webViewRef.current) {
              webViewRef.current.postMessage(JSON.stringify(message));
            } else {
              throw new Error('WebView reference not available');
            }

            // Set timeout for processing
            setTimeout(() => {
              if (processingRequests.current.has(requestId)) {
                processingRequests.current.delete(requestId);
                reject(new Error(`OpenCV processing timeout after 30 seconds for ${instanceId.current}`));
              }
            }, 30000);
            
          } catch (error) {
            console.error(`OpenCVProcessor v8.0: [${instanceId.current}] Error in processWaterMeterImage:`, error);
            reject(error);
          }
        });
      },
      
      testCommunication: async () => {
        console.log(`OpenCVProcessor v8.0: [${instanceId.current}] Testing communication, webViewReady:`, webViewReady, 'openCVFullyReady:', openCVFullyReady);
        
        if (!webViewReady) {
          throw new Error(`WebView not ready for testing in ${instanceId.current}`);
        }
        
        return new Promise((resolve, reject) => {
          const requestId = `test_${instanceId.current}_${Date.now()}`;
          processingRequests.current.set(requestId, { resolve, reject });

          const message = {
            type: 'TEST_COMMUNICATION',
            requestId,
            instanceId: instanceId.current
          };

          console.log(`OpenCVProcessor v8.0: [${instanceId.current}] Sending test message:`, message);

          setTimeout(() => {
            if (webViewRef.current) {
              webViewRef.current.postMessage(JSON.stringify(message));
              console.log(`OpenCVProcessor v8.0: [${instanceId.current}] Test message sent to WebView`);
            } else {
              console.error(`OpenCVProcessor v8.0: [${instanceId.current}] WebView ref not available`);
              reject(new Error('WebView reference not available'));
            }
          }, 100);

          setTimeout(() => {
            if (processingRequests.current.has(requestId)) {
              processingRequests.current.delete(requestId);
              console.error(`OpenCVProcessor v8.0: [${instanceId.current}] Test communication timeout`);
              reject(new Error(`Test communication timeout for ${instanceId.current}`));
            }
          }, 5000);
        });
      },
    }));

    const handleWebViewMessage = (event: any) => {
      try {
        const data = JSON.parse(event.nativeEvent.data);
        
        // Only process messages for this instance
        if (data.instanceId && data.instanceId !== instanceId.current) {
          console.log(`OpenCVProcessor v8.0: [${instanceId.current}] Ignoring message for different instance:`, data.instanceId);
          return;
        }
    
        
        switch (data.type) {
          case 'OPENCV_READY':
            console.log(`OpenCVProcessor v8.0: [${instanceId.current}] OpenCV.js loaded successfully`);
            setWebViewReady(true);
            // Wait a bit more to ensure OpenCV is fully ready
            setTimeout(() => {
              setOpenCVFullyReady(true);
              console.log(`OpenCVProcessor v8.0: [${instanceId.current}] OpenCV is now FULLY ready for processing`);
              onReady?.();
            }, 1000);
            break;
            
          case 'PROCESS_RESULT':
            const { requestId, result, error } = data;
            console.log(`OpenCVProcessor v8.0: [${instanceId.current}] Received result for request ${requestId}:`, { 
              hasResult: !!result, 
              hasError: !!error,
              reading: result?.reading,
              confidence: result?.confidence 
            });
            const promise = processingRequests.current.get(requestId);
            
            if (promise) {
              processingRequests.current.delete(requestId);
              
              if (error) {
                console.log(`OpenCVProcessor v8.0: [${instanceId.current}] Rejecting promise with error:`, error);
                promise.reject(new Error(error));
              } else {
                console.log(`OpenCVProcessor v8.0: [${instanceId.current}] Resolving promise with result`);
                promise.resolve(result);
              }
            } else {
              console.warn(`OpenCVProcessor v8.0: [${instanceId.current}] No pending promise found for request:`, requestId);
            }
            break;
            
          case 'ERROR':
            console.error(`OpenCVProcessor v8.0: [${instanceId.current}] WebView error:`, data.message);
            onError?.(data.message);
            break;
            
          case 'LOG':
            console.log(`OpenCV WebView v8.0: [${instanceId.current}]`, data.message);
            break;
            
          default:
            console.log(`OpenCVProcessor v8.0: [${instanceId.current}] Unknown message type:`, data.type, data);
        }
      } catch (error) {
        console.error(`OpenCVProcessor v8.0: [${instanceId.current}] Failed to parse WebView message:`, error);
      }
    };

    // Generate unique cache busting ID
    const cacheId = `v8_0_${instanceId.current}`;
    const htmlContent = `<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <!-- CACHE BUST ID: ${cacheId} -->
    <title>OpenCV WebView v8.0 SINGLETON - ${cacheId}</title>
</head>
<body>
    <canvas id="canvas" style="display: none;"></canvas>
    <img id="sourceImage" style="display: none;" />
    
    <script>
        console.log('🚀 OpenCV WebView HTML loaded - Version 8.0 SINGLETON - ID: ${cacheId}')
        
        const INSTANCE_ID = '${instanceId.current}';
        let cv = null;
        let cvReady = false;
        
        // Logging function
        function log(message) {
            console.log('OpenCV WebView v8.0:', message);
            try {
                window.ReactNativeWebView.postMessage(JSON.stringify({
                    type: 'LOG',
                    message: 'v8.0: ' + message,
                    instanceId: INSTANCE_ID
                }));
            } catch (e) {
                console.error('Failed to send log message:', e);
            }
        }
        
        // Load OpenCV.js from local assets
        function loadOpenCV() {
            log('Loading OpenCV.js from local assets...');
            
            // Set up OpenCV module configuration BEFORE loading the script
            window.Module = {
                onRuntimeInitialized: function() {
                    log('🎉 OpenCV.js Runtime Initialized!');
                    
                    // OpenCV is now fully ready
                    const cvObj = window.cv || globalThis.cv || cv;
                    
                    if (cvObj && cvObj.Mat) {
                        log('✅ OpenCV cv object is ready with Mat constructor');
                        window.cv = cvObj;
                        globalThis.cv = cvObj;
                        cv = cvObj;
                        cvReady = true;
                        
                        // Test basic functionality
                        try {
                            const testMat = new cvObj.Mat(1, 1, cvObj.CV_8UC1);
                            testMat.delete();
                            log('✅ OpenCV functionality test passed');
                            
                            // Send ready signal
                            sendReadySignal();
                            
                        } catch (testError) {
                            log('❌ OpenCV functionality test failed: ' + testError.message);
                        }
                    } else {
                        log('❌ OpenCV cv object not available after runtime initialization');
                    }
                }
            };
            
            const script = document.createElement('script');
            script.src = 'file:///android_asset/js/opencv.js';
            script.async = true;
            
            script.onload = function() {
                log('OpenCV.js script loaded successfully from assets');
            };
            
            script.onerror = function(error) {
                log('Failed to load OpenCV.js from assets: ' + JSON.stringify(error));
            };
            
            document.head.appendChild(script);
        }
        
        function sendReadySignal() {
            try {
                log('Sending OPENCV_READY signal...');
                
                const readyMessage = JSON.stringify({
                    type: 'OPENCV_READY',
                    timestamp: Date.now(),
                    version: 'v8.0',
                    instanceId: INSTANCE_ID
                });
                
                if (window.ReactNativeWebView && window.ReactNativeWebView.postMessage) {
                    window.ReactNativeWebView.postMessage(readyMessage);
                    log('✅ OPENCV_READY sent successfully');
                } else {
                    log('❌ ReactNativeWebView.postMessage not available');
                }
                
            } catch (sendError) {
                log('❌ Failed to send OPENCV_READY signal: ' + sendError.message);
            }
        }
        
        // Enhanced OpenCV readiness check
        function isOpenCVFullyReady() {
            try {
                if (!cvReady || !window.cv || !window.cv.Mat) {
                    log('OpenCV not ready: cvReady=' + cvReady + ', window.cv=' + !!window.cv + ', Mat=' + !!(window.cv && window.cv.Mat));
                    return false;
                }
                
                // More thorough test
                const testMat = new window.cv.Mat(10, 10, window.cv.CV_8UC3);
                const rows = testMat.rows;
                const cols = testMat.cols;
                testMat.delete();
                
                if (rows === 10 && cols === 10) {
                    log('✅ OpenCV full readiness test passed');
                    return true;
                } else {
                    log('❌ OpenCV readiness test failed: unexpected dimensions');
                    return false;
                }
            } catch (error) {
                log('❌ OpenCV readiness test error: ' + error.message);
                return false;
            }
        }

        // Enhanced processing function
        function processWaterMeterImageExtreme(requestId, base64Image, originalImageUri) {
            log('🎯 Starting OCR-OPTIMIZED processing for request: ' + requestId);
            
            try {
                if (!isOpenCVFullyReady()) {
                    const errorMsg = 'OpenCV.js not fully ready for processing';
                    log('❌ ' + errorMsg);
                    window.ReactNativeWebView.postMessage(JSON.stringify({
                        type: 'PROCESS_RESULT',
                        requestId: requestId,
                        error: errorMsg,
                        instanceId: INSTANCE_ID
                    }));
                    return;
                }
                
                log('💪 OpenCV VALIDATED and ready, proceeding with OCR-OPTIMIZED processing...');
                log('📊 Base64 image data length: ' + base64Image.length);
                
                const imageLoadTimeout = setTimeout(() => {
                    log('⏰ Image loading timeout after 10 seconds');
                    window.ReactNativeWebView.postMessage(JSON.stringify({
                        type: 'PROCESS_RESULT',
                        requestId: requestId,
                        error: 'Image loading timeout - image too large or corrupted',
                        instanceId: INSTANCE_ID
                    }));
                }, 10000);
                
                const img = new Image();
                
                img.onload = function() {
                    try {
                        clearTimeout(imageLoadTimeout);
                        log('🖼️ Image loaded successfully, size: ' + img.width + 'x' + img.height);
                        
                        const canvas = document.createElement('canvas');
                        canvas.width = img.width;
                        canvas.height = img.height;
                        const ctx = canvas.getContext('2d');
                        
                        if (!ctx) {
                            throw new Error('Failed to get canvas 2D context');
                        }
                        
                        ctx.drawImage(img, 0, 0);
                        log('🎨 Canvas created, starting OCR-OPTIMIZED processing...');
                        
                        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
                        const src = cv.matFromImageData(imageData);
                        
                        log('📊 Created Mat from image data: ' + src.rows + 'x' + src.cols);
                        
                        // OCR-OPTIMIZED PIPELINE
                        const gray = new cv.Mat();
                        const denoised = new cv.Mat();
                        const contrast = new cv.Mat();
                        const sharpened = new cv.Mat();
                        
                        // Step 1: Convert to grayscale
                        cv.cvtColor(src, gray, cv.COLOR_RGBA2GRAY);
                        log('✅ Step 1: Converted to grayscale');
                        
                        // Step 2: 轻微去噪但保持边缘
                        const blurKernel = new cv.Size(3, 3);
                        cv.GaussianBlur(gray, denoised, blurKernel, 0.6, 0.6, cv.BORDER_DEFAULT);
                        log('✅ Step 2: Applied edge-preserving denoising');
                        
                        // Step 3: 适度的对比度增强（针对OCR优化）
                        denoised.convertTo(contrast, -1, 1.2, 8); // 1.2x 对比度, +8 亮度
                        log('✅ Step 3: Applied OCR-friendly contrast enhancement');
                        
                        // Step 4: 轻微锐化（专门增强数字边缘）
                        const sharpenKernel = cv.matFromArray(3, 3, cv.CV_32FC1, [
                            0, -0.5, 0,
                            -0.5, 3, -0.5,
                            0, -0.5, 0
                        ]);
                        cv.filter2D(contrast, sharpened, cv.CV_8U, sharpenKernel);
                        log('✅ Step 4: Applied gentle sharpening for digit edges');
                        
                        // Convert result back to canvas
                        const resultCanvas = document.createElement('canvas');
                        resultCanvas.width = sharpened.cols;
                        resultCanvas.height = sharpened.rows;
                        cv.imshow(resultCanvas, sharpened);
                        
                        // Convert to base64
                        const processedBase64 = resultCanvas.toDataURL('image/png');
                        log('🎯 OCR-OPTIMIZED processing completed, result size: ' + processedBase64.length);
                        
                        // Clean up matrices
                        src.delete();
                        gray.delete();
                        denoised.delete();
                        contrast.delete();
                        sharpened.delete();
                        sharpenKernel.delete();
                        
                        // Return result
                        window.ReactNativeWebView.postMessage(JSON.stringify({
                            type: 'PROCESS_RESULT',
                            requestId: requestId,
                            result: {
                                reading: 'EXTREME_OPENCV_PROCESSED',
                                confidence: 1.0,
                                processedImageBase64: processedBase64,
                                processingSteps: [
                                    'Grayscale conversion',
                                    'Edge-preserving denoising',
                                    'OCR-friendly contrast enhancement (1.2x + 8)',
                                    'Gentle sharpening for digit edges',
                                    'Optimized for digit recognition'
                                ]
                            },
                            instanceId: INSTANCE_ID
                        }));
                        
                        log('🚀 OCR-OPTIMIZED processing completed successfully!');
                        
                    } catch (processingError) {
                        clearTimeout(imageLoadTimeout);
                        log('💥 Processing error: ' + processingError.message);
                        window.ReactNativeWebView.postMessage(JSON.stringify({
                            type: 'PROCESS_RESULT',
                            requestId: requestId,
                            error: 'Processing failed: ' + processingError.message,
                            instanceId: INSTANCE_ID
                        }));
                    }
                };
                
                img.onerror = function(errorEvent) {
                    clearTimeout(imageLoadTimeout);
                    const errorMsg = 'Failed to load image for processing';
                    log('💥 Image load error: ' + errorMsg);
                    window.ReactNativeWebView.postMessage(JSON.stringify({
                        type: 'PROCESS_RESULT',
                        requestId: requestId,
                        error: errorMsg + ' - Check image format and size',
                        instanceId: INSTANCE_ID
                    }));
                };
                
                if (!base64Image || base64Image.length < 100) {
                    clearTimeout(imageLoadTimeout);
                    log('💥 Invalid base64 data: length=' + (base64Image ? base64Image.length : 0));
                    window.ReactNativeWebView.postMessage(JSON.stringify({
                        type: 'PROCESS_RESULT',
                        requestId: requestId,
                        error: 'Invalid image data - base64 too short or empty',
                        instanceId: INSTANCE_ID
                    }));
                    return;
                }
                
                log('🔄 Setting image source with base64 data...');
                img.src = 'data:image/png;base64,' + base64Image;
                
            } catch (error) {
                log('💥 Fatal processing error: ' + error.message);
                window.ReactNativeWebView.postMessage(JSON.stringify({
                    type: 'PROCESS_RESULT',
                    requestId: requestId,
                    error: 'Fatal error: ' + error.message,
                    instanceId: INSTANCE_ID
                }));
            }
        }
        
        // Message handler
        function setupMessageHandler() {
            log('🔧 Setting up message handlers...');
            
            const messageHandler = function(event) {
                try {
                    const message = JSON.parse(event.data);
                    
                    // Only process messages for this instance
                    if (message.instanceId && message.instanceId !== INSTANCE_ID) {
                        return; // Ignore messages for other instances
                    }
                    
                    log('📨 Processing message: ' + message.type + ' for instance: ' + INSTANCE_ID);
                    
                    if (message.type === 'PROCESS_WATER_METER') {
                        log('🎯 PROCESS_WATER_METER message received!');
                        processWaterMeterImageExtreme(message.requestId, message.imageBase64, message.originalImageUri);
                    } else if (message.type === 'TEST_COMMUNICATION') {
                        log('🧪 TEST_COMMUNICATION received, requestId: ' + message.requestId);
                        
                        // Send back test response immediately
                        try {
                            window.ReactNativeWebView.postMessage(JSON.stringify({
                                type: 'PROCESS_RESULT',
                                requestId: message.requestId,
                                result: { test: 'success', version: '8.0-SINGLETON', instanceId: INSTANCE_ID },
                                instanceId: INSTANCE_ID
                            }));
                            log('✅ Test response sent successfully');
                        } catch (sendError) {
                            log('❌ Error sending test response: ' + sendError.message);
                        }
                    }
                } catch (parseError) {
                    log('💥 JSON parse error: ' + parseError.message);
                }
            };
            
            // React Native WebView specific message handling
            window.addEventListener('message', messageHandler);
            document.addEventListener('message', messageHandler);
            
            log('✅ All message handlers registered for instance: ' + INSTANCE_ID);
        }
        
        // Initialize
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', function() {
                setupMessageHandler();
                loadOpenCV();
            });
        } else {
            setupMessageHandler();
            loadOpenCV();
        }
        
        log('OpenCV v8.0 SINGLETON functions defined for instance: ' + INSTANCE_ID);
    </script>
</body>
</html>`;

    return (
      <WebView
        ref={webViewRef}
        source={{ 
          html: htmlContent,
          baseUrl: `file:///android_asset/opencv_${instanceId.current}/`
        }}
        style={{ height: 0, width: 0 }}
        onLoadStart={() => {
          console.log(`OpenCVProcessor v8.0: [${instanceId.current}] WebView load started`);
        }}
        onLoadEnd={() => {
          console.log(`OpenCVProcessor v8.0: [${instanceId.current}] WebView load ended`);
        }}
        onMessage={handleWebViewMessage}
        javaScriptEnabled={true}
        domStorageEnabled={false}
        cacheEnabled={false}
        incognito={true}
        mixedContentMode="always"
        allowFileAccess={true}
        allowFileAccessFromFileURLs={true}
        allowUniversalAccessFromFileURLs={true}
        onShouldStartLoadWithRequest={() => true}
        startInLoadingState={false}
        scalesPageToFit={false}
      />
    );
  }
);

export default OpenCVProcessor;