// Main exports for the recognition module
export { ImageRecognitionManager } from './ImageRecognitionManager';
export * from './strategies';
export * from './types';

// Factory function to create a configured recognition manager
export function createRecognitionManager() {
  // Import specific modules to avoid circular dependency
  const { ImageRecognitionManager } = require('./ImageRecognitionManager');
  const { MLKitStrategy } = require('./strategies/MLKitStrategy');
  const { OpenCVStrategy } = require('./strategies/OpenCVStrategy');
  const { HybridStrategy } = require('./strategies/HybridStrategy');
  const { AIApiStrategy } = require('./strategies/AIApiStrategy');
  const { DEFAULT_RECOGNITION_CONFIG } = require('./types');

  // Create recognition manager
  const manager = new ImageRecognitionManager(DEFAULT_RECOGNITION_CONFIG);

  // Create strategy instances
  const aiApiStrategy = new AIApiStrategy();      // Priority 1: AI API (when network available)
  const mlkitStrategy = new MLKitStrategy();      // Priority 2: Local ML Kit
  const opencvStrategy = new OpenCVStrategy();    // Priority 3: OpenCV preprocessing
  
  // Register individual strategies
  manager.registerStrategy(aiApiStrategy);   // Highest priority for network recognition
  manager.registerStrategy(mlkitStrategy);   // Reliable local fallback
  manager.registerStrategy(opencvStrategy);  // Image preprocessing support
  
  // Create and register hybrid strategy with all available strategies
  // HybridStrategy will intelligently choose between AI API (network) and local strategies
  const hybridStrategy = new HybridStrategy([aiApiStrategy, opencvStrategy, mlkitStrategy]);
  manager.registerStrategy(hybridStrategy);

  console.log('Recognition manager created with AI-first strategy:', manager.getStats());
  console.log('✅ AI API strategy registered - will prioritize network recognition');
  
  return manager;
} 