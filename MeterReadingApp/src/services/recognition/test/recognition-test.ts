// Simple test for the new recognition architecture
import { createRecognitionManager } from '../index';
import { MeterType, ImageQuality } from '../types';

export async function testRecognitionArchitecture() {
  console.log('🧪 Testing Recognition Architecture...');
  
  try {
    // Test 1: Create recognition manager
    console.log('📝 Test 1: Creating recognition manager...');
    const manager = createRecognitionManager();
    
    const stats = manager.getStats();
    console.log('✅ Recognition manager created successfully:', stats);
    
    // Test 2: Check available strategies
    console.log('📝 Test 2: Checking available strategies...');
    const strategies = manager.getAvailableStrategies();
    console.log('✅ Available strategies:', strategies.map((s: any) => `${s.name} (priority: ${s.priority})`));
    
    // Test 3: Test strategy capabilities
    console.log('📝 Test 3: Testing strategy capabilities...');
    strategies.forEach((strategy: any) => {
      const canHandleMechanical = strategy.canHandle(MeterType.MECHANICAL_CIRCULAR, ImageQuality.GOOD);
      const canHandleDigital = strategy.canHandle(MeterType.DIGITAL_LCD, ImageQuality.GOOD);
      console.log(`✅ ${strategy.name}: Mechanical=${canHandleMechanical}, Digital=${canHandleDigital}`);
    });
    
    // Test 4: Image analysis (mock)
    console.log('📝 Test 4: Testing image analysis...');
    const analysis = await manager.analyzeImage('mock-image-uri');
    console.log('✅ Image analysis result:', analysis);
    
    console.log('🎉 All tests passed! Recognition architecture is working correctly.');
    return true;
    
  } catch (error) {
    console.error('❌ Test failed:', error);
    return false;
  }
}

// Self-test when this file is imported
if (require.main === module) {
  testRecognitionArchitecture().then(success => {
    process.exit(success ? 0 : 1);
  });
} 