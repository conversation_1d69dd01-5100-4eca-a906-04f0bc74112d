// Test for OpenCV integration
import { OpenCVStrategy } from '../strategies/OpenCVStrategy';
import { MeterType, ImageQuality } from '../types';

// Mock OpenCV processor for testing
class MockOpenCVProcessor {
  async processWaterMeterImage(imageUri: string) {
    console.log('MockOpenCVProcessor: Processing image:', imageUri);
    
    // Simulate processing time
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    return {
      reading: '08104',
      confidence: 0.85,
      roiBounds: {
        x: 0.15,
        y: 0.08,
        width: 0.7,
        height: 0.35
      },
      detectedDigits: [
        { digit: '0', confidence: 0.9, bounds: { x: 0.15, y: 0.08, width: 0.1, height: 0.2 } },
        { digit: '8', confidence: 0.8, bounds: { x: 0.25, y: 0.08, width: 0.1, height: 0.2 } },
        { digit: '1', confidence: 0.9, bounds: { x: 0.35, y: 0.08, width: 0.1, height: 0.2 } },
        { digit: '0', confidence: 0.85, bounds: { x: 0.45, y: 0.08, width: 0.1, height: 0.2 } },
        { digit: '4', confidence: 0.8, bounds: { x: 0.55, y: 0.08, width: 0.1, height: 0.2 } },
      ],
      processedImageUri: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=='
    };
  }
}

export async function testOpenCVIntegration() {
  console.log('🧪 Testing OpenCV Integration...');
  
  try {
    // Test 1: Create OpenCV strategy
    console.log('📝 Test 1: Creating OpenCV strategy...');
    const strategy = new OpenCVStrategy();
    console.log('✅ OpenCV strategy created');
    
    // Test 2: Check initial state (should not handle without processor)
    console.log('📝 Test 2: Testing initial state...');
    const canHandleInitial = strategy.canHandle(MeterType.MECHANICAL_CIRCULAR, ImageQuality.GOOD);
    console.log('✅ Initial canHandle:', canHandleInitial, '(should be false)');
    
    // Test 3: Set processor
    console.log('📝 Test 3: Setting mock processor...');
    const mockProcessor = new MockOpenCVProcessor();
    strategy.setProcessor(mockProcessor as any);
    console.log('✅ Processor set');
    
    // Test 4: Check state after processor is set
    console.log('📝 Test 4: Testing state after processor set...');
    const canHandleAfter = strategy.canHandle(MeterType.MECHANICAL_CIRCULAR, ImageQuality.GOOD);
    console.log('✅ After processor canHandle:', canHandleAfter, '(should be true)');
    
    // Test 5: Test recognition
    console.log('📝 Test 5: Testing recognition...');
    const result = await strategy.recognize('mock-image-uri', {
      strategies: ['opencv'],
      fallbackEnabled: false,
      parallelProcessing: false,
      confidenceThreshold: 0.5,
      timeout: 10000
    });
    
    console.log('✅ Recognition result:', {
      reading: result.reading,
      confidence: result.confidence,
      strategy: result.strategy,
      processingTime: result.processingTime,
      stepsCount: result.metadata.processingSteps.length,
      digitsDetected: result.metadata.detectedDigits?.length
    });
    
    // Test 6: Validate result format
    console.log('📝 Test 6: Validating result format...');
    const isValidFormat = /^\d+$/.test(result.reading) && 
                         result.confidence >= 0 && result.confidence <= 1 &&
                         result.strategy === 'OpenCV' &&
                         result.metadata.processingSteps.length > 0;
    
    console.log('✅ Result format valid:', isValidFormat);
    
    console.log('🎉 All OpenCV integration tests passed!');
    return true;
    
  } catch (error) {
    console.error('❌ OpenCV integration test failed:', error);
    return false;
  }
}

// Self-test when this file is imported
if (require.main === module) {
  testOpenCVIntegration().then(success => {
    process.exit(success ? 0 : 1);
  });
} 