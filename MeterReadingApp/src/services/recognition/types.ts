// Core types and interfaces for image recognition system

export enum MeterType {
  MECHANICAL_CIRCULAR = 'mechanical_circular',
  DIGITAL_LCD = 'digital_lcd',
  HYBRID = 'hybrid',
  UNKNOWN = 'unknown'
}

export enum ImageQuality {
  POOR = 'poor',
  FAIR = 'fair',
  GOOD = 'good',
  EXCELLENT = 'excellent'
}

export interface ROI {
  x: number;
  y: number;
  width: number;
  height: number;
}

export interface DigitInfo {
  digit: string;
  confidence: number;
  bounds: ROI;
}

export interface RecognitionResult {
  reading: string;
  confidence: number;
  strategy: string;
  processingTime: number;
  metadata: {
    roiBounds?: ROI;
    processingSteps: string[];
    detectedDigits?: DigitInfo[];
    originalImageUri?: string;
    processedImageUri?: string;
    // AI API specific fields
    aiService?: string;
    aiResponse?: string;
    cleanedReading?: string;
    networkStatus?: {
      isConnected: boolean;
      connectionType?: string;
      quality?: 'good' | 'poor' | 'none';
    };
  };
  fallbackRequired?: boolean;
  error?: string;
}

export interface RecognitionConfig {
  strategies: string[];
  fallbackEnabled: boolean;
  parallelProcessing: boolean;
  confidenceThreshold: number;
  timeout: number;
  meterType?: MeterType;
  debugMode?: boolean;
}

export interface IRecognitionStrategy {
  readonly name: string;
  readonly priority: number;
  
  canHandle(imageType: MeterType, imageQuality: ImageQuality): boolean;
  recognize(imageUri: string, config: RecognitionConfig): Promise<RecognitionResult>;
  preprocess?(imageUri: string): Promise<string>;
  validateResult?(result: RecognitionResult): boolean;
}

export interface ImageAnalysisResult {
  meterType: MeterType;
  imageQuality: ImageQuality;
  hasClearDigits: boolean;
  lightingCondition: 'poor' | 'fair' | 'good' | 'excellent';
  recommendedStrategies: string[];
}

export class RecognitionError extends Error {
  constructor(
    message: string,
    public strategy: string,
    public originalError?: Error
  ) {
    super(message);
    this.name = 'RecognitionError';
  }
}

// Default configuration
export const DEFAULT_RECOGNITION_CONFIG: RecognitionConfig = {
  strategies: ['hybrid', 'opencv', 'mlkit'],
  fallbackEnabled: true,
  parallelProcessing: false,
  confidenceThreshold: 0.7,
  timeout: 10000, // 10 seconds
  debugMode: __DEV__,
}; 