// services/LocationService.ts
// GPS Location Service for Meter Reading App
// Based on CORDE architecture with enhancements for meter reading workflow

import Geolocation from '@react-native-community/geolocation';
import { PermissionsAndroid, Platform } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import DatabaseManager from '../database/DatabaseManager';

const LOCATION_PERMISSION_KEY = 'meter_reading_location_permission';
const LAST_LOCATION_KEY = 'meter_reading_last_location';

export interface LocationData {
  latitude: number;
  longitude: number;
  accuracy: number;
  timestamp: number;
}

export interface LocationValidationResult {
  isValid: boolean;
  accuracy: number;
  message: string;
  warningLevel: 'low' | 'medium' | 'high';
}

export class LocationService {
  private static instance: LocationService;
  private isInitialized: boolean = false;
  private permissionGranted: boolean = false;
  private watchId: number | null = null;
  private lastLocation: LocationData | null = null;
  private locationListeners: Set<(location: LocationData) => void> = new Set();

  // GPS accuracy requirements for meter readings
  private readonly EXCELLENT_ACCURACY = 5; // meters
  private readonly GOOD_ACCURACY = 10; // meters
  private readonly ACCEPTABLE_ACCURACY = 20; // meters
  private readonly MAX_ACCURACY = 50; // meters

  private constructor() {}

  public static getInstance(): LocationService {
    if (!LocationService.instance) {
      LocationService.instance = new LocationService();
    }
    return LocationService.instance;
  }

  public async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      console.log('LocationService: Initializing GPS service for meter reading...');

      // Configure Geolocation for high accuracy meter reading
      Geolocation.setRNConfiguration({
        skipPermissionRequests: false,
        authorizationLevel: 'whenInUse',
        locationProvider: 'auto' // Use GPS + Network for best accuracy
      });

      // Check stored permission status
      const granted = await AsyncStorage.getItem(LOCATION_PERMISSION_KEY);
      this.permissionGranted = granted === 'true';

      if (!this.permissionGranted) {
        await this.requestLocationPermission();
      }

      // Load last known location if recent (within 2 minutes for meter reading)
      const lastLocationStr = await AsyncStorage.getItem(LAST_LOCATION_KEY);
      if (lastLocationStr) {
        const lastLocation = JSON.parse(lastLocationStr) as LocationData;
        if (Date.now() - lastLocation.timestamp < 2 * 60 * 1000) {
          this.lastLocation = lastLocation;
        }
      }

      // Initialize GPS settings in database
      await this.initializeGPSSettings();

      // Start location monitoring if permission granted
      if (this.permissionGranted) {
        this.startWatchingLocation();
      }

      this.isInitialized = true;
      console.log('LocationService: GPS service initialized successfully');
    } catch (error) {
      console.error('LocationService: Error initializing GPS service:', error);
      throw error;
    }
  }

  private async initializeGPSSettings(): Promise<void> {
    // GPS settings now using in-memory constants instead of database
    // This avoids database dependency issues during initialization
    console.log('LocationService: GPS settings initialized with default values');
    
    // Default settings are now defined as class constants:
    // EXCELLENT_ACCURACY = 5, GOOD_ACCURACY = 10, ACCEPTABLE_ACCURACY = 20, MAX_ACCURACY = 50
  }

  private async requestLocationPermission(): Promise<boolean> {
    try {
      if (Platform.OS === 'android') {
        const granted = await PermissionsAndroid.request(
          PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
          {
            title: 'Meter Reading Location Permission',
            message: 'This app needs precise location access to record GPS coordinates with meter readings for accuracy and compliance.',
            buttonNeutral: 'Ask Later',
            buttonNegative: 'Cancel',
            buttonPositive: 'Allow'
          }
        );

        this.permissionGranted = granted === PermissionsAndroid.RESULTS.GRANTED;
        await AsyncStorage.setItem(LOCATION_PERMISSION_KEY, String(this.permissionGranted));

        if (this.permissionGranted) {
          this.startWatchingLocation();
        }

        return this.permissionGranted;
      }

      // iOS permissions are handled automatically
      this.permissionGranted = true;
      await AsyncStorage.setItem(LOCATION_PERMISSION_KEY, 'true');
      this.startWatchingLocation();
      return true;
    } catch (error) {
      console.error('LocationService: Error requesting location permission:', error);
      return false;
    }
  }

  private startWatchingLocation(): void {
    if (this.watchId !== null) return;

    console.log('LocationService: Starting GPS monitoring for meter readings...');

    this.watchId = Geolocation.watchPosition(
      (position) => {
        const location: LocationData = {
          latitude: position.coords.latitude,
          longitude: position.coords.longitude,
          accuracy: position.coords.accuracy || 999,
          timestamp: Date.now()
        };
        
        this.lastLocation = location;
        this.saveLocation(location);
        this.notifyListeners(location);
        
        console.log(`LocationService: GPS updated - Accuracy: ${location.accuracy}m`);
      },
      (error) => {
        // Filter out common GPS errors that don't need logging
        if (error.code !== 3 || !error.message.includes('temporarily')) {
          console.error('LocationService: GPS watch error:', error);
        }
      },
      {
        enableHighAccuracy: true,
        distanceFilter: 5, // Update every 5 meters for meter reading accuracy
        interval: 5000, // Check every 5 seconds
        fastestInterval: 2000 // Minimum 2 second intervals
      }
    );
  }

  private async saveLocation(location: LocationData): Promise<void> {
    try {
      await AsyncStorage.setItem(LAST_LOCATION_KEY, JSON.stringify(location));
    } catch (error) {
      console.error('LocationService: Error saving location:', error);
    }
  }

  private notifyListeners(location: LocationData): void {
    this.locationListeners.forEach(listener => {
      try {
        listener(location);
      } catch (error) {
        console.error('LocationService: Error notifying location listener:', error);
      }
    });
  }

  public async getCurrentLocationForReading(
    options: { 
      enableHighAccuracy?: boolean; 
      timeout?: number; 
      maximumAge?: number;
      forceRefresh?: boolean;
    } = {}
  ): Promise<LocationData> {
    if (!this.isInitialized) {
      await this.initialize();
    }

    if (!this.permissionGranted) {
      throw new Error('GPS permission not granted. Location access is required for meter readings.');
    }

    // For meter readings, we need fresh GPS data unless explicitly using cache
    const maxAge = options.forceRefresh ? 0 : (options.maximumAge ?? 10000);
    
    // Check if we have recent, accurate location
    if (!options.forceRefresh && this.lastLocation && 
        (Date.now() - this.lastLocation.timestamp < maxAge) &&
        this.lastLocation.accuracy <= this.GOOD_ACCURACY) {
      console.log(`LocationService: Using cached GPS location (${this.lastLocation.accuracy}m accuracy)`);
      return this.lastLocation;
    }

    console.log('LocationService: Getting fresh GPS location for meter reading...');

    return new Promise((resolve, reject) => {
      Geolocation.getCurrentPosition(
        position => {
          const location: LocationData = {
            latitude: position.coords.latitude,
            longitude: position.coords.longitude,
            accuracy: position.coords.accuracy || 999,
            timestamp: Date.now()
          };
          
          this.lastLocation = location;
          this.saveLocation(location);
          
          console.log(`LocationService: Fresh GPS acquired - Accuracy: ${location.accuracy}m`);
          resolve(location);
        },
        error => {
          console.error('LocationService: Error getting current location:', error);
          
          // If high accuracy fails, try standard accuracy as fallback
          if (error.code === 3 && options.enableHighAccuracy !== false) {
            console.log('LocationService: High accuracy timeout, trying standard accuracy...');
            this.getCurrentLocationForReading({ 
              ...options, 
              enableHighAccuracy: false, 
              timeout: (options.timeout ?? 15000) / 2 
            })
              .then(resolve)
              .catch(reject);
          } else {
            reject(new Error(`GPS location failed: ${error.message}`));
          }
        },
        {
          enableHighAccuracy: options.enableHighAccuracy ?? true,
          timeout: options.timeout ?? 15000,
          maximumAge: maxAge,
        }
      );
    });
  }

  public validateLocationAccuracy(location: LocationData): LocationValidationResult {
    const accuracy = location.accuracy;

    if (accuracy <= this.EXCELLENT_ACCURACY) {
      return {
        isValid: true,
        accuracy,
        message: `Excellent GPS accuracy (${accuracy.toFixed(1)}m)`,
        warningLevel: 'low'
      };
    } else if (accuracy <= this.GOOD_ACCURACY) {
      return {
        isValid: true,
        accuracy,
        message: `Good GPS accuracy (${accuracy.toFixed(1)}m)`,
        warningLevel: 'low'
      };
    } else if (accuracy <= this.ACCEPTABLE_ACCURACY) {
      return {
        isValid: true,
        accuracy,
        message: `Acceptable GPS accuracy (${accuracy.toFixed(1)}m)`,
        warningLevel: 'medium'
      };
    } else if (accuracy <= this.MAX_ACCURACY) {
      return {
        isValid: true,
        accuracy,
        message: `Poor GPS accuracy (${accuracy.toFixed(1)}m) - Consider retrying`,
        warningLevel: 'high'
      };
    } else {
      return {
        isValid: false,
        accuracy,
        message: `GPS accuracy too low (${accuracy.toFixed(1)}m) - Please try again`,
        warningLevel: 'high'
      };
    }
  }

  public addLocationListener(listener: (location: LocationData) => void): void {
    this.locationListeners.add(listener);
  }

  public removeLocationListener(listener: (location: LocationData) => void): void {
    this.locationListeners.delete(listener);
  }

  public getLastKnownLocation(): LocationData | null {
    return this.lastLocation;
  }

  public isLocationPermissionGranted(): boolean {
    return this.permissionGranted;
  }

  public cleanup(): void {
    if (this.watchId !== null) {
      Geolocation.clearWatch(this.watchId);
      this.watchId = null;
    }
    this.locationListeners.clear();
  }
}

export const locationService = LocationService.getInstance(); 