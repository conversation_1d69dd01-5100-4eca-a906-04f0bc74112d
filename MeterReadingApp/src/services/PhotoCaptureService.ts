import { launchCamera, launchImageLibrary, ImagePickerResponse, MediaType } from 'react-native-image-picker';
import ImageResizer from 'react-native-image-resizer';
import { Platform, PermissionsAndroid } from 'react-native';

export interface PhotoCaptureResult {
  uri: string;
  fileName: string;
  fileSize: number;
  type: string;
  width?: number;
  height?: number;
}

export interface CompressionConfig {
  maxWidth: number;
  maxHeight: number;
  quality: number;
  format: 'JPEG' | 'PNG';
}

export class PhotoCaptureService {
  
  static async capturePhoto(): Promise<PhotoCaptureResult> {
    const hasPermission = await this.requestCameraPermission();
    if (!hasPermission) {
      throw new Error('Camera permission denied');
    }

    const options = {
      mediaType: 'photo' as MediaType,
      quality: 0.9,
      maxWidth: 2048,
      maxHeight: 2048,
      includeBase64: false,
      saveToPhotos: false
    };

    return new Promise((resolve, reject) => {
      launchCamera(options, (response: ImagePickerResponse) => {
        if (response.didCancel) {
          reject(new Error('User cancelled photo capture'));
          return;
        }

        if (response.errorMessage) {
          reject(new Error(response.errorMessage));
          return;
        }

        if (response.assets && response.assets[0]) {
          const asset = response.assets[0];
          resolve({
            uri: asset.uri!,
            fileName: asset.fileName || `photo_${Date.now()}.jpg`,
            fileSize: asset.fileSize || 0,
            type: asset.type || 'image/jpeg',
            width: asset.width,
            height: asset.height
          });
        } else {
          reject(new Error('No photo captured'));
        }
      });
    });
  }

  static async pickFromGallery(): Promise<PhotoCaptureResult> {
    const options = {
      mediaType: 'photo' as MediaType,
      quality: 0.9,
      maxWidth: 2048,
      maxHeight: 2048,
      includeBase64: false
    };

    return new Promise((resolve, reject) => {
      launchImageLibrary(options, (response: ImagePickerResponse) => {
        if (response.didCancel) {
          reject(new Error('User cancelled photo selection'));
          return;
        }

        if (response.errorMessage) {
          reject(new Error(response.errorMessage));
          return;
        }

        if (response.assets && response.assets[0]) {
          const asset = response.assets[0];
          resolve({
            uri: asset.uri!,
            fileName: asset.fileName || `photo_${Date.now()}.jpg`,
            fileSize: asset.fileSize || 0,
            type: asset.type || 'image/jpeg',
            width: asset.width,
            height: asset.height
          });
        } else {
          reject(new Error('No photo selected'));
        }
      });
    });
  }

  static async compressPhoto(
    photoUri: string, 
    config?: Partial<CompressionConfig>
  ): Promise<string> {
    const defaultConfig: CompressionConfig = {
      maxWidth: 1920,
      maxHeight: 1440,
      quality: 80, // Fixed quality for local compression
      format: 'JPEG'
    };

    const finalConfig = { ...defaultConfig, ...config };

    try {
      const compressedImage = await ImageResizer.createResizedImage(
        photoUri,
        finalConfig.maxWidth,
        finalConfig.maxHeight,
        finalConfig.format,
        finalConfig.quality,
        0,
        undefined,
        false,
        { mode: 'contain' }
      );

      return compressedImage.uri;
    } catch (error) {
      console.error('Photo compression failed:', error);
      throw new Error('Failed to compress photo');
    }
  }

  static async createThumbnail(photoUri: string): Promise<string> {
    try {
      const thumbnail = await ImageResizer.createResizedImage(
        photoUri,
        300,
        300,
        'JPEG',
        70,
        0,
        undefined,
        false,
        { mode: 'cover' }
      );

      return thumbnail.uri;
    } catch (error) {
      console.error('Thumbnail creation failed:', error);
      throw new Error('Failed to create thumbnail');
    }
  }



  private static async requestCameraPermission(): Promise<boolean> {
    if (Platform.OS === 'ios') {
      return true;
    }

    try {
      const granted = await PermissionsAndroid.request(
        PermissionsAndroid.PERMISSIONS.CAMERA,
        {
          title: 'Camera Permission',
          message: 'This app needs access to camera to take photos of water meters',
          buttonNeutral: 'Ask Me Later',
          buttonNegative: 'Cancel',
          buttonPositive: 'OK',
        }
      );

      return granted === PermissionsAndroid.RESULTS.GRANTED;
    } catch (err) {
      console.warn('Camera permission error:', err);
      return false;
    }
  }

  static async validatePhotoSize(fileSize: number): Promise<boolean> {
    const maxSize = 10 * 1024 * 1024; // 10MB
    return fileSize <= maxSize;
  }

  static async getPhotoMetadata(photoUri: string): Promise<{
    size: number;
    dimensions?: { width: number; height: number };
  }> {
    try {
      const RNFS = require('react-native-fs');
      const stats = await RNFS.stat(photoUri);
      
      return {
        size: stats.size,
        dimensions: undefined
      };
    } catch (error) {
      console.error('Failed to get photo metadata:', error);
      return { size: 0 };
    }
  }
}
