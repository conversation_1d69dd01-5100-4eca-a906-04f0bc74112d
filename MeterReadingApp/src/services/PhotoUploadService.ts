import RNFS from 'react-native-fs';
import { MeterReadingPhotoRepository } from '../data/repositories/MeterReadingPhotoRepository';
import { ReadingPhoto } from '../database/models/MeterReading';
import api from '../api/BaseApi';
import { networkService } from '../utils/NetworkUtils';

export interface UploadResult {
  success: boolean;
  message: string;
  remoteUrl?: string;
  thumbnailUrl?: string;
}

export class PhotoUploadService {
  
  static async uploadPhoto(photo: ReadingPhoto): Promise<UploadResult> {
    try {
      const fileExists = await RNFS.exists(photo.file_path);
      if (!fileExists) {
        throw new Error('Local file not found');
      }

      const formData = new FormData();
      formData.append('photo', {
        uri: photo.file_path,
        type: photo.mime_type,
        name: photo.filename
      } as any);
      formData.append('originalFileName', photo.filename);
      formData.append('fileSize', photo.file_size.toString());
      formData.append('mimeType', photo.mime_type);

      const response = await api.post(
        `/meter-reading/${photo.reading_id}/photos`,
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
          timeout: 60000,
        }
      );

      if (response.data.success) {
        await MeterReadingPhotoRepository.updateUploadStatus(
          photo.id!,
          response.data.cloudflareUrl,
          response.data.thumbnailUrl,
          true
        );

        return {
          success: true,
          message: 'Photo uploaded successfully',
          remoteUrl: response.data.cloudflareUrl,
          thumbnailUrl: response.data.thumbnailUrl
        };
      } else {
        throw new Error(response.data.message);
      }
    } catch (error: any) {
      console.error('Photo upload failed:', error);
      
      await this.incrementRetryCount(photo.id!);
      
      return {
        success: false,
        message: error.message || 'Upload failed'
      };
    }
  }

  static async uploadPendingPhotos(): Promise<void> {
    const pendingPhotos = await MeterReadingPhotoRepository.getPendingUploadPhotos();
    
    for (const photo of pendingPhotos) {
      if (photo.upload_retry_count >= 3) {
        console.log(`Photo ${photo.id} exceeded retry limit, skipping`);
        continue;
      }

      await this.uploadPhoto(photo);
      
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  }

  static async schedulePhotoUpload(photo: ReadingPhoto): Promise<void> {
    if (networkService.isConnected()) {
      await this.uploadPhoto(photo);
    } else {
      console.log('Network unavailable, photo will be uploaded when connection is restored');
    }
  }

  private static async incrementRetryCount(photoId: number): Promise<void> {
    const sql = `
      UPDATE reading_photos 
      SET upload_retry_count = upload_retry_count + 1, updated_at = ?
      WHERE id = ?
    `;
    
    await MeterReadingPhotoRepository.executeSql(sql, [
      new Date().toISOString(),
      photoId
    ]);
  }

  static async getUploadProgress(): Promise<{
    pending: number;
    uploaded: number;
    failed: number;
  }> {
    const stats = await MeterReadingPhotoRepository.getUploadStats();
    return stats;
  }
}
