// Data upload service
// Core business logic for uploading reading data, photos, and GPS information
// Can be called by manual upload or background sync

import { UploadResult } from '../types/SyncTypes';
import api from '../api/BaseApi';
import { ReadingRepository } from '../data/repositories/ReadingRepository';
import { MeterReadingSyncLogRepository } from '../data/repositories/MeterReadingSyncLogRepository';
import { MeterReadingSyncLogStatus } from '../database/models/MeterReadingSyncLog';
import MobileReadingApi from '../api/MobileReadingApi';
import { WaterMeterRepository } from '../data/repositories/WaterMeterRepository';
import { MeterReadingPhotoRepository } from '../data/repositories/MeterReadingPhotoRepository';

export class DataUploadService {
  /**
   * Upload all pending data (unified entry point for manual and automatic calls)
   * This is the main method that background sync and manual upload both use
   */
  static async uploadAllPendingData(): Promise<UploadResult[]> {
    const results: UploadResult[] = [];

    try {
      console.log('DataUploadService: Starting upload of all pending data...');

      // 1. Upload pending readings
      const readingResult = await this.uploadPendingReadings();
      results.push(readingResult);

      // 2. Upload pending photos
      const photoResult = await this.uploadPendingPhotos();
      results.push(photoResult);

      // 3. Update task statuses
      const taskResult = await this.updateTaskStatuses();
      results.push(taskResult);

      console.log('DataUploadService: Completed upload of all pending data');
      
      return results;

    } catch (error) {
      console.error('DataUploadService: Error uploading pending data:', error);
      
      // Return error result
      results.push({
        success: false,
        type: 'error',
        message: error instanceof Error ? error.message : 'Unknown upload error',
        timestamp: new Date().toISOString()
      });

      return results;
    }
  }

  /**
   * Upload pending readings to backend
   */
  static async uploadPendingReadings(): Promise<UploadResult> {
    try {
      console.log('DataUploadService: Starting upload of pending readings...');

      const pendingReadings = await ReadingRepository.getForUpload(50);
      
      if (pendingReadings.length === 0) {
        console.log('DataUploadService: No pending readings to upload');
        return {
          success: true,
          type: 'readings',
          message: 'No pending readings to upload',
          recordCount: 0,
          timestamp: new Date().toISOString()
        };
      }

      console.log(`DataUploadService: Found ${pendingReadings.length} pending readings`);
      
      let successCount = 0;
      let failureCount = 0;
      const errors: string[] = [];

      for (const reading of pendingReadings) {
        try {
          const mobileReading = await this.convertToMobileReadingDto(reading);
          
          // Submit reading - backend will handle duplicate detection by task_id
          console.log(`DataUploadService: Submitting reading ${reading.id} for task ${reading.task_id}, meter ${reading.meter_id}`);
          const response = await MobileReadingApi.submitReading(mobileReading);
          
          if (response && response.success) {
            await this.updateSyncLogOnSuccess(reading.id!, response.readingId);
            successCount++;
            console.log(`DataUploadService: Successfully processed reading ${reading.id} -> server ID ${response.readingId}`);
          } else {
            await this.updateSyncLogOnFailure(reading.id!, response?.message || 'Unknown server error');
            failureCount++;
            errors.push(`Reading ${reading.id}: ${response?.message || 'Unknown server error'}`);
          }
          
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Unknown error';
          await this.updateSyncLogOnFailure(reading.id!, errorMessage);
          failureCount++;
          errors.push(`Reading ${reading.id}: ${errorMessage}`);
          console.error(`DataUploadService: Failed to process reading ${reading.id}:`, error);
        }
      }

      const message = `Uploaded ${successCount}/${pendingReadings.length} readings`;
      console.log(`DataUploadService: ${message}`);

      return {
        success: failureCount === 0,
        type: 'readings',
        message: failureCount > 0 ? `${message}. ${failureCount} failed.` : message,
        recordCount: successCount,
        errors: errors.length > 0 ? errors : undefined,
        timestamp: new Date().toISOString()
      };

    } catch (error) {
      console.error('DataUploadService: Error uploading pending readings:', error);
      return {
        success: false,
        type: 'readings',
        message: error instanceof Error ? error.message : 'Failed to upload readings',
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
   * Upload pending photos to backend
   */
  static async uploadPendingPhotos(): Promise<UploadResult> {
    try {
      console.log('DataUploadService: Uploading pending photos...');

      // This would handle photo upload logic
      // For now, return success placeholder
      
      return {
        success: true,
        type: 'photos',
        message: 'Successfully uploaded pending photos',
        recordCount: 0, // Would be actual count
        timestamp: new Date().toISOString()
      };

    } catch (error) {
      console.error('DataUploadService: Error uploading photos:', error);
      return {
        success: false,
        type: 'photos',
        message: error instanceof Error ? error.message : 'Failed to upload photos',
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
   * Update task statuses after successful uploads
   */
  static async updateTaskStatuses(): Promise<UploadResult> {
    try {
      console.log('DataUploadService: Updating task statuses...');

      // This would update task statuses based on successful uploads
      // For now, return success placeholder
      
      return {
        success: true,
        type: 'tasks',
        message: 'Successfully updated task statuses',
        recordCount: 0, // Would be actual count
        timestamp: new Date().toISOString()
      };

    } catch (error) {
      console.error('DataUploadService: Error updating task statuses:', error);
      return {
        success: false,
        type: 'tasks',
        message: error instanceof Error ? error.message : 'Failed to update task statuses',
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
   * Get upload summary/statistics
   */
  static async getUploadSummary(): Promise<{
    pendingReadings: number;
    pendingPhotos: number;
    pendingTasks: number;
    lastUploadTime?: string;
  }> {
    try {
      // This would query repositories for pending upload counts
      // For now, return placeholder data
      
      return {
        pendingReadings: 0,
        pendingPhotos: 0,
        pendingTasks: 0,
        lastUploadTime: new Date().toISOString()
      };

    } catch (error) {
      console.error('DataUploadService: Error getting upload summary:', error);
      return {
        pendingReadings: 0,
        pendingPhotos: 0,
        pendingTasks: 0
      };
    }
  }

  private static async convertToMobileReadingDto(reading: any): Promise<any> {
    // Get meter information to include meter number
    let meterNumber: string | null = null;
    try {
      const meter = await WaterMeterRepository.findById(reading.meter_id);
      meterNumber = meter?.serial_number || null;
    } catch (error) {
      console.warn('DataUploadService: Could not fetch meter number for meter_id:', reading.meter_id, error);
    }

    // Get associated photos and load Base64 data
    let photos: any[] = [];
    try {
      console.log(`DataUploadService: Looking for photos with reading_id=${reading.id}`);
      const readingPhotos = await MeterReadingPhotoRepository.findByReadingId(reading.id);
      console.log(`DataUploadService: Found ${readingPhotos.length} photos for reading ${reading.id}`);

      for (const photo of readingPhotos) {
        try {
          // Load Base64 data from file
          let base64Data = null;
          if (photo.file_path) {
            const RNFS = require('react-native-fs');
            const fileExists = await RNFS.exists(photo.file_path);
            if (fileExists) {
              base64Data = await RNFS.readFile(photo.file_path, 'base64');
              console.log(`DataUploadService: Loaded Base64 data for photo ${photo.filename} (${base64Data.length} chars)`);
            } else {
              console.warn(`DataUploadService: Photo file not found: ${photo.file_path}`);
            }
          }

          photos.push({
            Uuid: photo.uuid || `photo_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            FileName: photo.filename,
            Base64Data: base64Data,
            FilePath: photo.file_path,
            FileSizeBytes: photo.file_size,
            MimeType: photo.mime_type,
            CapturedAt: photo.captured_at,
            Latitude: photo.latitude,
            Longitude: photo.longitude,
            PhotoType: photo.photo_type
          });
        } catch (photoError) {
          console.error(`DataUploadService: Error loading photo ${photo.filename}:`, photoError);
        }
      }

      console.log(`DataUploadService: Prepared ${photos.length} photos for reading ${reading.id}`);
    } catch (error) {
      console.warn('DataUploadService: Could not fetch photos for reading_id:', reading.id, error);
    }

    // Ensure TaskId and UserId are present and valid
    if (!reading.task_id || reading.task_id <= 0) {
      throw new Error(`Invalid task_id: ${reading.task_id} for reading ${reading.id}`);
    }
    
    if (!reading.user_id || reading.user_id <= 0) {
      throw new Error(`Invalid user_id: ${reading.user_id} for reading ${reading.id}`);
    }

    return {
      TaskId: reading.task_id,
      MeterId: reading.meter_id,
      MeterNumber: meterNumber,
      UserId: reading.user_id,
      ReadingValue: reading.reading_value,
      ReadingDate: new Date(reading.reading_date),
      ReadingMethod: reading.reading_method || 'Manual',
      ReadingType: reading.reading_type || 'Regular',
      DataSource: reading.data_source || 'Mobile',
      HasOCR: reading.has_ocr || false,
      OcrConfidence: reading.ocr_confidence || null,
      Latitude: reading.latitude || null,
      Longitude: reading.longitude || null,
      GpsAccuracy: reading.gps_accuracy || null,
      Status: reading.status || 'Completed',
      IsValidated: reading.is_validated || false,
      IsAnomalous: reading.is_anomalous || false,
      CantRead: reading.cant_read || false,
      Notes: reading.notes || null,
      IsOfflineReading: reading.is_offline_reading || false,
      Photos: photos
    };
  }

  private static async updateSyncLogOnSuccess(localReadingId: number, serverReadingId?: number): Promise<void> {
    try {
      const syncLog = await MeterReadingSyncLogRepository.findByLocalReadingId(localReadingId);
      if (syncLog) {
        await MeterReadingSyncLogRepository.update(syncLog.sync_log_id!, {
          sync_status: MeterReadingSyncLogStatus.SYNCED,
          last_sync_time: new Date().toISOString(),
          last_update_time: new Date().toISOString(),
          sync_errors: null,
          offline_indicator: 0
        });
      }
    } catch (error) {
      console.error('DataUploadService: Error updating sync log on success:', error);
    }
  }

  private static async updateSyncLogOnFailure(localReadingId: number, errorMessage: string): Promise<void> {
    try {
      const syncLog = await MeterReadingSyncLogRepository.findByLocalReadingId(localReadingId);
      if (syncLog) {
        const newRetryCount = (syncLog.retry_count || 0) + 1;
        const maxRetries = 3;
        
        await MeterReadingSyncLogRepository.update(syncLog.sync_log_id!, {
          sync_status: newRetryCount >= maxRetries ? MeterReadingSyncLogStatus.FAILED : MeterReadingSyncLogStatus.PENDING,
          last_update_time: new Date().toISOString(),
          sync_errors: errorMessage,
          retry_count: newRetryCount
        });
      }
    } catch (error) {
      console.error('DataUploadService: Error updating sync log on failure:', error);
    }
  }
} 