/**
 * Universal Upload Service
 * Provides immediate upload functionality when data is saved
 * Handles network detection, retry logic, and queue management
 */

import NetInfo from '@react-native-community/netinfo';
import { DataUploadService } from './DataUploadService';
import { UploadResult } from '../types/SyncTypes';
import { ReadingRepository } from '../data/repositories/ReadingRepository';
import MobileReadingApi from '../api/MobileReadingApi';
import { WaterMeterRepository } from '../data/repositories/WaterMeterRepository';
import { PhotoUploadService } from './PhotoUploadService';
import { MeterReadingPhotoRepository } from '../data/repositories/MeterReadingPhotoRepository';

export interface UploadOptions {
  immediate?: boolean;           // Force immediate upload regardless of network
  retryOnFailure?: boolean;      // Retry if upload fails
  maxRetries?: number;           // Maximum retry attempts
  retryDelayMs?: number;         // Delay between retries
  priority?: 'low' | 'medium' | 'high'; // Upload priority
}

export interface QueuedUpload {
  id: string;
  type: 'reading' | 'photo' | 'task';
  localId: number;
  options: UploadOptions;
  attempts: number;
  lastAttempt?: Date;
  error?: string;
  createdAt: Date;
}

class UniversalUploadService {
  private static instance: UniversalUploadService;
  private uploadQueue: QueuedUpload[] = [];
  private isProcessingQueue = false;
  private networkAvailable = false;

  private constructor() {
    this.initializeNetworkListener();
  }

  static getInstance(): UniversalUploadService {
    if (!UniversalUploadService.instance) {
      UniversalUploadService.instance = new UniversalUploadService();
    }
    return UniversalUploadService.instance;
  }

  /**
   * Initialize network listener
   */
  private initializeNetworkListener(): void {
    NetInfo.addEventListener(state => {
      const wasOffline = !this.networkAvailable;
      this.networkAvailable = state.isConnected && state.isInternetReachable;
      
      console.log(`UniversalUploadService: Network status changed - Available: ${this.networkAvailable}`);
      
      // If network just became available, process queue
      if (wasOffline && this.networkAvailable) {
        console.log('UniversalUploadService: Network restored, processing upload queue');
        this.processUploadQueue();
      }
    });
  }

  /**
   * Queue reading for upload (called when saving reading)
   */
  async uploadReading(localReadingId: number, options: UploadOptions = {}): Promise<UploadResult> {
    console.log(`UniversalUploadService: Queuing reading ${localReadingId} for upload`);
    
    const defaultOptions: UploadOptions = {
      immediate: true,
      retryOnFailure: true,
      maxRetries: 3,
      retryDelayMs: 2000,
      priority: 'high',
      ...options
    };

    // If immediate upload is requested and network is available, try now
    if (defaultOptions.immediate && this.networkAvailable) {
      try {
        console.log(`UniversalUploadService: Attempting immediate upload for reading ${localReadingId}`);
        const result = await this.performReadingUpload(localReadingId);
        
        if (result.success) {
          console.log(`UniversalUploadService: Immediate upload successful for reading ${localReadingId}`);
          return result;
        } else {
          console.log(`UniversalUploadService: Immediate upload failed, queuing for retry: ${result.message}`);
        }
      } catch (error) {
        console.error(`UniversalUploadService: Immediate upload error for reading ${localReadingId}:`, error);
      }
    }

    // Add to queue for later processing
    const queuedUpload: QueuedUpload = {
      id: this.generateUploadId('reading', localReadingId),
      type: 'reading',
      localId: localReadingId,
      options: defaultOptions,
      attempts: 0,
      createdAt: new Date()
    };

    this.addToQueue(queuedUpload);

    // If network is available, process queue immediately
    if (this.networkAvailable) {
      this.processUploadQueue();
    }

    return {
      success: false,
      type: 'readings',
      message: 'Reading queued for upload',
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Queue photo for upload
   */
  async uploadPhoto(localPhotoId: number, options: UploadOptions = {}): Promise<UploadResult> {
    console.log(`UniversalUploadService: Queuing photo ${localPhotoId} for upload`);
    
    const defaultOptions: UploadOptions = {
      immediate: false, // Photos are usually larger, don't force immediate
      retryOnFailure: true,
      maxRetries: 2,
      retryDelayMs: 5000,
      priority: 'medium',
      ...options
    };

    const queuedUpload: QueuedUpload = {
      id: this.generateUploadId('photo', localPhotoId),
      type: 'photo',
      localId: localPhotoId,
      options: defaultOptions,
      attempts: 0,
      createdAt: new Date()
    };

    this.addToQueue(queuedUpload);

    // Process queue if network available and immediate upload requested
    if (this.networkAvailable && defaultOptions.immediate) {
      this.processUploadQueue();
    }

    return {
      success: false,
      type: 'photos',
      message: 'Photo queued for upload',
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Process the upload queue
   */
  private async processUploadQueue(): Promise<void> {
    if (this.isProcessingQueue || !this.networkAvailable) {
      return;
    }

    this.isProcessingQueue = true;
    console.log(`UniversalUploadService: Processing upload queue (${this.uploadQueue.length} items)`);

    try {
      // Sort by priority and creation time
      const sortedQueue = this.uploadQueue.sort((a, b) => {
        const priorityOrder = { high: 3, medium: 2, low: 1 };
        const aPriority = priorityOrder[a.options.priority || 'medium'];
        const bPriority = priorityOrder[b.options.priority || 'medium'];
        
        if (aPriority !== bPriority) {
          return bPriority - aPriority; // Higher priority first
        }
        
        return a.createdAt.getTime() - b.createdAt.getTime(); // Older first
      });

      // Process each item in queue
      for (let i = sortedQueue.length - 1; i >= 0; i--) {
        const upload = sortedQueue[i];
        
        if (!this.networkAvailable) {
          console.log('UniversalUploadService: Network lost during queue processing');
          break;
        }

        try {
          console.log(`UniversalUploadService: Processing ${upload.type} upload for local ID ${upload.localId}`);
          
          let result: UploadResult;
          
          switch (upload.type) {
            case 'reading':
              result = await this.performReadingUpload(upload.localId);
              break;
            case 'photo':
              result = await this.performPhotoUpload(upload.localId);
              break;
            default:
              console.warn(`UniversalUploadService: Unknown upload type: ${upload.type}`);
              continue;
          }

          if (result.success) {
            // Remove from queue on success
            console.log(`UniversalUploadService: Upload successful for ${upload.type} ${upload.localId}, removing from queue`);
            this.uploadQueue.splice(i, 1);
          } else {
            // Handle failure
            upload.attempts++;
            upload.lastAttempt = new Date();
            upload.error = result.message;

            const maxRetries = upload.options.maxRetries || 3;
            if (upload.attempts >= maxRetries) {
              console.log(`UniversalUploadService: Max retries reached for ${upload.type} ${upload.localId}, removing from queue`);
              this.uploadQueue.splice(i, 1);
            } else {
              console.log(`UniversalUploadService: Upload failed for ${upload.type} ${upload.localId}, will retry (${upload.attempts}/${maxRetries})`);
              
              // Add delay before next processing
              if (upload.options.retryDelayMs) {
                await this.delay(upload.options.retryDelayMs);
              }
            }
          }
          
        } catch (error) {
          console.error(`UniversalUploadService: Error processing upload for ${upload.type} ${upload.localId}:`, error);
          upload.attempts++;
          upload.error = error instanceof Error ? error.message : 'Unknown error';
        }
      }

    } finally {
      this.isProcessingQueue = false;
      console.log(`UniversalUploadService: Finished processing queue (${this.uploadQueue.length} items remaining)`);
    }
  }

  /**
   * Perform actual reading upload
   */
  private async performReadingUpload(localReadingId: number): Promise<UploadResult> {
    try {
      const reading = await ReadingRepository.findById(localReadingId);
      if (!reading) {
        return {
          success: false,
          type: 'readings',
          message: `Reading ${localReadingId} not found`,
          timestamp: new Date().toISOString()
        };
      }

      // Check if already synced
      if (reading.sync_status === 'synced') {
        return {
          success: true,
          type: 'readings',
          message: `Reading ${localReadingId} already synced`,
          timestamp: new Date().toISOString()
        };
      }

      const mobileReading = await this.convertToMobileReadingDto(reading);
      const response = await MobileReadingApi.submitReading(mobileReading);

      if (response && response.success) {
        await this.updateReadingSyncStatus(localReadingId, 'synced');

        // Upload associated photos
        await this.uploadReadingPhotos(localReadingId);

        return {
          success: true,
          type: 'readings',
          message: `Reading ${localReadingId} uploaded successfully`,
          timestamp: new Date().toISOString()
        };
      } else {
        await this.updateReadingSyncStatus(localReadingId, 'pending', response?.message);
        return {
          success: false,
          type: 'readings',
          message: response?.message || 'Upload failed',
          timestamp: new Date().toISOString()
        };
      }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      await this.updateReadingSyncStatus(localReadingId, 'pending', errorMessage);
      return {
        success: false,
        type: 'readings',
        message: errorMessage,
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
   * Upload photos associated with a reading
   */
  private async uploadReadingPhotos(readingId: number): Promise<void> {
    try {
      const photos = await MeterReadingPhotoRepository.findByReadingId(readingId);
      const pendingPhotos = photos.filter(p => p.sync_status === 'pending');

      if (pendingPhotos.length === 0) {
        console.log(`UniversalUploadService: No pending photos for reading ${readingId}`);
        return;
      }

      console.log(`UniversalUploadService: Uploading ${pendingPhotos.length} photos for reading ${readingId}`);

      for (const photo of pendingPhotos) {
        try {
          await PhotoUploadService.uploadPhoto(photo);
        } catch (error) {
          console.error(`UniversalUploadService: Failed to upload photo ${photo.id}:`, error);
        }
      }
    } catch (error) {
      console.error(`UniversalUploadService: Error uploading photos for reading ${readingId}:`, error);
    }
  }

  /**
   * Perform actual photo upload
   */
  private async performPhotoUpload(localPhotoId: number): Promise<UploadResult> {
    // Placeholder for photo upload logic
    return {
      success: true,
      type: 'photos',
      message: `Photo ${localPhotoId} upload placeholder`,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Convert reading to mobile DTO (reuse from DataUploadService)
   */
  private async convertToMobileReadingDto(reading: any): Promise<any> {
    // Get meter information to include meter number
    let meterNumber: string | null = null;
    try {
      const meter = await WaterMeterRepository.findById(reading.meter_id);
      meterNumber = meter?.serial_number || null;
    } catch (error) {
      console.warn('UniversalUploadService: Could not fetch meter number for meter_id:', reading.meter_id, error);
    }

    // Ensure TaskId and UserId are present and valid
    if (!reading.task_id || reading.task_id <= 0) {
      throw new Error(`Invalid task_id: ${reading.task_id} for reading ${reading.id}`);
    }
    
    if (!reading.user_id || reading.user_id <= 0) {
      throw new Error(`Invalid user_id: ${reading.user_id} for reading ${reading.id}`);
    }

    return {
      TaskId: reading.task_id,
      MeterId: reading.meter_id,
      MeterNumber: meterNumber,
      UserId: reading.user_id,
      ReadingValue: reading.reading_value,
      ReadingDate: new Date(reading.reading_date),
      ReadingMethod: reading.reading_method || 'Manual',
      ReadingType: reading.reading_type || 'Regular',
      DataSource: reading.data_source || 'Mobile',
      HasOCR: reading.has_ocr || false,
      OcrConfidence: reading.ocr_confidence || null,
      Latitude: reading.latitude || null,
      Longitude: reading.longitude || null,
      GpsAccuracy: reading.gps_accuracy || null,
      Status: reading.status || 'Completed',
      IsValidated: reading.is_validated || false,
      IsAnomalous: reading.is_anomalous || false,
      CantRead: reading.cant_read || false,
      Notes: reading.notes || null,
      IsOfflineReading: reading.is_offline_reading || false,
      Photos: []
    };
  }

  /**
   * Update reading sync status
   */
  private async updateReadingSyncStatus(localReadingId: number, status: string, error?: string): Promise<void> {
    try {
      await ReadingRepository.update(localReadingId, {
        sync_status: status,
        sync_error: error || null,
        updated_at: new Date().toISOString()
      });
    } catch (err) {
      console.error('UniversalUploadService: Error updating reading sync status:', err);
    }
  }

  /**
   * Add upload to queue
   */
  private addToQueue(upload: QueuedUpload): void {
    // Remove existing entry for same item
    this.uploadQueue = this.uploadQueue.filter(
      existing => existing.id !== upload.id
    );
    
    this.uploadQueue.push(upload);
    console.log(`UniversalUploadService: Added ${upload.type} ${upload.localId} to queue (${this.uploadQueue.length} total)`);
  }

  /**
   * Generate unique upload ID
   */
  private generateUploadId(type: string, localId: number): string {
    return `${type}_${localId}`;
  }

  /**
   * Utility delay function
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Get queue status
   */
  getQueueStatus(): {
    total: number;
    pending: number;
    failed: number;
    isProcessing: boolean;
    networkAvailable: boolean;
  } {
    const failed = this.uploadQueue.filter(upload => 
      upload.attempts >= (upload.options.maxRetries || 3)
    ).length;

    return {
      total: this.uploadQueue.length,
      pending: this.uploadQueue.length - failed,
      failed: failed,
      isProcessing: this.isProcessingQueue,
      networkAvailable: this.networkAvailable
    };
  }

  /**
   * Force process queue (for manual sync)
   */
  async forceProcessQueue(): Promise<void> {
    console.log('UniversalUploadService: Force processing queue requested');
    if (this.networkAvailable) {
      await this.processUploadQueue();
    } else {
      console.log('UniversalUploadService: Cannot force process - network not available');
    }
  }

  /**
   * Clear failed uploads from queue
   */
  clearFailedUploads(): number {
    const beforeCount = this.uploadQueue.length;
    this.uploadQueue = this.uploadQueue.filter(upload => 
      upload.attempts < (upload.options.maxRetries || 3)
    );
    const removed = beforeCount - this.uploadQueue.length;
    console.log(`UniversalUploadService: Cleared ${removed} failed uploads from queue`);
    return removed;
  }
}

export default UniversalUploadService.getInstance();