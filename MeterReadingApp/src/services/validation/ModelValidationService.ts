// Model Validation Service
// Validation logic aligned with backend C# model validation rules
// Implements StringLength, Required, Range and other validation attributes

import { 
  BackendWorkTask, 
  BackendWaterMeter, 
  BackendReading, 
  BackendUser 
} from '../../types/BackendModels';

// Validation result interface
export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
}

export interface ValidationError {
  field: string;
  message: string;
  code: string;
}

export class ModelValidationService {
  
  // ============ WorkTask Validation ============
  static validateWorkTask(task: Partial<BackendWorkTask>): ValidationResult {
    const errors: ValidationError[] = [];

    // Required fields validation
    if (!task.name || task.name.trim() === '') {
      errors.push({
        field: 'name',
        message: 'Task name is required',
        code: 'REQUIRED'
      });
    }

    if (!task.status || task.status.trim() === '') {
      errors.push({
        field: 'status',
        message: 'Task status is required',
        code: 'REQUIRED'
      });
    }

    if (!task.priority || task.priority.trim() === '') {
      errors.push({
        field: 'priority',
        message: 'Task priority is required',
        code: 'REQUIRED'
      });
    }

    if (!task.type || task.type.trim() === '') {
      errors.push({
        field: 'type',
        message: 'Task type is required',
        code: 'REQUIRED'
      });
    }

    if (!task.createdBy || task.createdBy.trim() === '') {
      errors.push({
        field: 'createdBy',
        message: 'Created by field is required',
        code: 'REQUIRED'
      });
    }

    // StringLength validations (aligned with C# model)
    if (task.name && task.name.length > 200) {
      errors.push({
        field: 'name',
        message: 'Task name cannot exceed 200 characters',
        code: 'STRING_LENGTH'
      });
    }

    if (task.description && task.description.length > 1000) {
      errors.push({
        field: 'description',
        message: 'Description cannot exceed 1000 characters',
        code: 'STRING_LENGTH'
      });
    }

    if (task.status && task.status.length > 50) {
      errors.push({
        field: 'status',
        message: 'Status cannot exceed 50 characters',
        code: 'STRING_LENGTH'
      });
    }

    if (task.priority && task.priority.length > 50) {
      errors.push({
        field: 'priority',
        message: 'Priority cannot exceed 50 characters',
        code: 'STRING_LENGTH'
      });
    }

    if (task.type && task.type.length > 50) {
      errors.push({
        field: 'type',
        message: 'Type cannot exceed 50 characters',
        code: 'STRING_LENGTH'
      });
    }

    if (task.assignedTo && task.assignedTo.length > 100) {
      errors.push({
        field: 'assignedTo',
        message: 'Assigned to cannot exceed 100 characters',
        code: 'STRING_LENGTH'
      });
    }

    if (task.createdBy && task.createdBy.length > 100) {
      errors.push({
        field: 'createdBy',
        message: 'Created by cannot exceed 100 characters',
        code: 'STRING_LENGTH'
      });
    }

    if (task.location && task.location.length > 200) {
      errors.push({
        field: 'location',
        message: 'Location cannot exceed 200 characters',
        code: 'STRING_LENGTH'
      });
    }

    if (task.serviceAddress && task.serviceAddress.length > 200) {
      errors.push({
        field: 'serviceAddress',
        message: 'Service address cannot exceed 200 characters',
        code: 'STRING_LENGTH'
      });
    }

    if (task.instructions && task.instructions.length > 1000) {
      errors.push({
        field: 'instructions',
        message: 'Instructions cannot exceed 1000 characters',
        code: 'STRING_LENGTH'
      });
    }

    if (task.notes && task.notes.length > 1000) {
      errors.push({
        field: 'notes',
        message: 'Notes cannot exceed 1000 characters',
        code: 'STRING_LENGTH'
      });
    }

    // Range validations
    if (task.progressPercentage !== undefined && (task.progressPercentage < 0 || task.progressPercentage > 100)) {
      errors.push({
        field: 'progressPercentage',
        message: 'Progress percentage must be between 0 and 100',
        code: 'RANGE'
      });
    }

    // Status enum validation
    const validStatuses = ['Pending', 'InProgress', 'Completed', 'Cancelled', 'Failed', 'Skipped'];
    if (task.status && !validStatuses.includes(task.status)) {
      errors.push({
        field: 'status',
        message: 'Invalid status value',
        code: 'INVALID_ENUM'
      });
    }

    // Priority enum validation
    const validPriorities = ['Low', 'Medium', 'High', 'Critical'];
    if (task.priority && !validPriorities.includes(task.priority)) {
      errors.push({
        field: 'priority',
        message: 'Invalid priority value',
        code: 'INVALID_ENUM'
      });
    }

    // Type enum validation
    const validTypes = ['MeterReading', 'Inspection', 'Maintenance', 'Installation'];
    if (task.type && !validTypes.includes(task.type)) {
      errors.push({
        field: 'type',
        message: 'Invalid task type value',
        code: 'INVALID_ENUM'
      });
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  // ============ WaterMeter Validation ============
  static validateWaterMeter(meter: Partial<BackendWaterMeter>): ValidationResult {
    const errors: ValidationError[] = [];

    // Required fields validation
    if (!meter.serialNumber || meter.serialNumber.trim() === '') {
      errors.push({
        field: 'serialNumber',
        message: 'Serial number is required',
        code: 'REQUIRED'
      });
    }

    if (!meter.location || meter.location.trim() === '') {
      errors.push({
        field: 'location',
        message: 'Location is required',
        code: 'REQUIRED'
      });
    }

    if (!meter.meterType || meter.meterType.trim() === '') {
      errors.push({
        field: 'meterType',
        message: 'Meter type is required',
        code: 'REQUIRED'
      });
    }

    if (!meter.status || meter.status.trim() === '') {
      errors.push({
        field: 'status',
        message: 'Status is required',
        code: 'REQUIRED'
      });
    }

    if (!meter.address || meter.address.trim() === '') {
      errors.push({
        field: 'address',
        message: 'Address is required',
        code: 'REQUIRED'
      });
    }

    if (!meter.source || meter.source.trim() === '') {
      errors.push({
        field: 'source',
        message: 'Source is required',
        code: 'REQUIRED'
      });
    }

    if (!meter.syncStatus || meter.syncStatus.trim() === '') {
      errors.push({
        field: 'syncStatus',
        message: 'Sync status is required',
        code: 'REQUIRED'
      });
    }

    if (!meter.meterSize || meter.meterSize.trim() === '') {
      errors.push({
        field: 'meterSize',
        message: 'Meter size is required',
        code: 'REQUIRED'
      });
    }

    // StringLength validations (aligned with C# model)
    if (meter.assetId && meter.assetId.length > 20) {
      errors.push({
        field: 'assetId',
        message: 'Asset ID cannot exceed 20 characters',
        code: 'STRING_LENGTH'
      });
    }

    if (meter.serialNumber && meter.serialNumber.length > 50) {
      errors.push({
        field: 'serialNumber',
        message: 'Serial number cannot exceed 50 characters',
        code: 'STRING_LENGTH'
      });
    }

    if (meter.accountNumber && meter.accountNumber.length > 20) {
      errors.push({
        field: 'accountNumber',
        message: 'Account number cannot exceed 20 characters',
        code: 'STRING_LENGTH'
      });
    }

    if (meter.location && meter.location.length > 100) {
      errors.push({
        field: 'location',
        message: 'Location cannot exceed 100 characters',
        code: 'STRING_LENGTH'
      });
    }

    if (meter.address && meter.address.length > 200) {
      errors.push({
        field: 'address',
        message: 'Address cannot exceed 200 characters',
        code: 'STRING_LENGTH'
      });
    }

    if (meter.meterType && meter.meterType.length > 50) {
      errors.push({
        field: 'meterType',
        message: 'Meter type cannot exceed 50 characters',
        code: 'STRING_LENGTH'
      });
    }

    if (meter.status && meter.status.length > 20) {
      errors.push({
        field: 'status',
        message: 'Status cannot exceed 20 characters',
        code: 'STRING_LENGTH'
      });
    }

    if (meter.customerName && meter.customerName.length > 100) {
      errors.push({
        field: 'customerName',
        message: 'Customer name cannot exceed 100 characters',
        code: 'STRING_LENGTH'
      });
    }

    // Decimal precision validation for coordinates
    if (meter.latitude !== undefined && (meter.latitude < -90 || meter.latitude > 90)) {
      errors.push({
        field: 'latitude',
        message: 'Latitude must be between -90 and 90',
        code: 'RANGE'
      });
    }

    if (meter.longitude !== undefined && (meter.longitude < -180 || meter.longitude > 180)) {
      errors.push({
        field: 'longitude',
        message: 'Longitude must be between -180 and 180',
        code: 'RANGE'
      });
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  // ============ Reading Validation ============
  static validateReading(reading: Partial<BackendReading>): ValidationResult {
    const errors: ValidationError[] = [];

    // Required fields validation
    if (reading.meterId === undefined || reading.meterId <= 0) {
      errors.push({
        field: 'meterId',
        message: 'Valid meter ID is required',
        code: 'REQUIRED'
      });
    }

    if (reading.userId === undefined || reading.userId <= 0) {
      errors.push({
        field: 'userId',
        message: 'Valid user ID is required',
        code: 'REQUIRED'
      });
    }

    if (reading.readingValue === undefined) {
      errors.push({
        field: 'readingValue',
        message: 'Reading value is required',
        code: 'REQUIRED'
      });
    }

    if (!reading.readingDate || reading.readingDate.trim() === '') {
      errors.push({
        field: 'readingDate',
        message: 'Reading date is required',
        code: 'REQUIRED'
      });
    }

    if (!reading.readingMethod || reading.readingMethod.trim() === '') {
      errors.push({
        field: 'readingMethod',
        message: 'Reading method is required',
        code: 'REQUIRED'
      });
    }

    // Range validations
    if (reading.readingValue !== undefined && reading.readingValue < 0) {
      errors.push({
        field: 'readingValue',
        message: 'Reading value cannot be negative',
        code: 'RANGE'
      });
    }

    if (reading.ocrConfidence !== undefined && (reading.ocrConfidence < 0 || reading.ocrConfidence > 100)) {
      errors.push({
        field: 'ocrConfidence',
        message: 'OCR confidence must be between 0 and 100',
        code: 'RANGE'
      });
    }

    if (reading.qualityScore !== undefined && (reading.qualityScore < 0 || reading.qualityScore > 10)) {
      errors.push({
        field: 'qualityScore',
        message: 'Quality score must be between 0 and 10',
        code: 'RANGE'
      });
    }

    // Date validation
    if (reading.readingDate) {
      const date = new Date(reading.readingDate);
      if (isNaN(date.getTime())) {
        errors.push({
          field: 'readingDate',
          message: 'Invalid reading date format',
          code: 'INVALID_DATE'
        });
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  // ============ User Validation ============
  static validateUser(user: Partial<BackendUser>): ValidationResult {
    const errors: ValidationError[] = [];

    // Required fields validation
    if (!user.username || user.username.trim() === '') {
      errors.push({
        field: 'username',
        message: 'Username is required',
        code: 'REQUIRED'
      });
    }

    if (!user.createdDate || user.createdDate.trim() === '') {
      errors.push({
        field: 'createdDate',
        message: 'Created date is required',
        code: 'REQUIRED'
      });
    }

    if (!user.updatedDate || user.updatedDate.trim() === '') {
      errors.push({
        field: 'updatedDate',
        message: 'Updated date is required',
        code: 'REQUIRED'
      });
    }

    // Username uniqueness and format validation would be handled by backend
    if (user.username && user.username.length > 100) {
      errors.push({
        field: 'username',
        message: 'Username cannot exceed 100 characters',
        code: 'STRING_LENGTH'
      });
    }

    // Email format validation
    if (user.email && user.email.trim() !== '') {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(user.email)) {
        errors.push({
          field: 'email',
          message: 'Invalid email format',
          code: 'INVALID_FORMAT'
        });
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  // ============ Utility Methods ============
  static validateAllModels(data: {
    task?: Partial<BackendWorkTask>;
    meter?: Partial<BackendWaterMeter>;
    reading?: Partial<BackendReading>;
    user?: Partial<BackendUser>;
  }): ValidationResult {
    const allErrors: ValidationError[] = [];

    if (data.task) {
      const taskResult = this.validateWorkTask(data.task);
      allErrors.push(...taskResult.errors);
    }

    if (data.meter) {
      const meterResult = this.validateWaterMeter(data.meter);
      allErrors.push(...meterResult.errors);
    }

    if (data.reading) {
      const readingResult = this.validateReading(data.reading);
      allErrors.push(...readingResult.errors);
    }

    if (data.user) {
      const userResult = this.validateUser(data.user);
      allErrors.push(...userResult.errors);
    }

    return {
      isValid: allErrors.length === 0,
      errors: allErrors
    };
  }
} 