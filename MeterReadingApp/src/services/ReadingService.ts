// Reading service
// Core business logic for managing meter reading data
// Aligned with new readings database schema
// Updated to work with backend ID alignment

import { ReadingRepository } from '../data/repositories';
import { SyncResult } from '../types/SyncTypes';
import { MobileReadingDto } from '../types/mobile';
import MobileReadingApi from '../api/MobileReadingApi';
import { MeterReadingPhotoRepository } from '../data/repositories/MeterReadingPhotoRepository';
import { MeterReadingSyncLogRepository } from '../data/repositories/MeterReadingSyncLogRepository';

// Reading data interface aligned with new schema
export interface ReadingData {
  id?: number;
  meter_id: number;                     // Local meter ID (for database storage)
  user_id: number;                      // Local user ID (for database storage)
  task_id?: number;                     // Local task ID (for database storage)
  reading_value: number;
  reading_date: string;
  previous_reading?: number;
  reading_method: string;
  ocr_confidence?: number;
  latitude?: number;
  longitude?: number;
  gps_accuracy?: number;
  gps_timestamp?: string;
  photo_filename?: string;
  photo_file_path?: string;
  photo_uploaded?: number;
}

// Submission data for backend API (uses backend IDs)
export interface ReadingSubmissionData {
  taskId: number;                       // Backend task ID
  meterId: number;                      // Backend meter ID  
  readingValue: number;
  readingDate: string;
  latitude?: number;
  longitude?: number;
  locationAccuracy?: number;
  readingMethod: string;
  notes?: string;
  photoPaths?: string[];
  isOfflineReading?: boolean;
  offlineId?: string;
  isValidated?: boolean;
  validationWarnings?: string[];
  previousReading?: number;
  previousReadingDate?: string;
}

export interface ReadingStatistics {
  totalReadings: number;
  todayReadings: number;
  weekReadings: number;
  pendingUpload: number;
  averagePerDay: number;
}

export class ReadingService {
  private static readingApi = MobileReadingApi;

  /**
   * Save a new reading to local database
   */
  static async saveReading(readingData: ReadingData): Promise<number | null> {
    try {
      const result = await ReadingRepository.create(readingData);
      console.log('ReadingService: Successfully saved reading to local database');
      return result.id || null;
    } catch (error) {
      console.error('ReadingService: Error saving reading:', error);
      return null;
    }
  }

  /**
   * Save reading with photos and create sync log
   * Complete save operation for reading + photos + sync tracking
   */
  static async saveReadingWithPhotos(
    readingData: ReadingData,
    photos: any[] = []
  ): Promise<number | null> {
    try {
      console.log('ReadingService: Starting saveReadingWithPhotos...');

      // Step 1: Save reading to database
      const readingId = await this.saveReading(readingData);
      if (!readingId) {
        throw new Error('Failed to save reading');
      }

      console.log(`ReadingService: Reading saved with ID: ${readingId}`);

      // Step 2: Save photos if any
      if (photos.length > 0) {
        console.log(`ReadingService: Saving ${photos.length} photos...`);
        for (const photo of photos) {
          try {
            const photoData = {
              ...photo,
              readingId: readingId // Associate with the reading (use camelCase)
            };
            const savedPhoto = await MeterReadingPhotoRepository.savePhoto(photoData);
            console.log(`ReadingService: Photo saved with ID: ${savedPhoto.id}`);
          } catch (photoError) {
            console.error('ReadingService: Error saving photo:', photoError);
            // Continue with other photos even if one fails
          }
        }
      }

      // Step 3: Create sync log
      const syncLogRequest = {
        local_reading_id: readingId,
        sync_status: 'pending' as const,
        offline_indicator: 1,
        sync_task_count: 1,
        retry_count: 0,
        conflict_status: 'none' as const
      };

      await MeterReadingSyncLogRepository.create(syncLogRequest);
      console.log(`ReadingService: Sync log created for reading ${readingId}`);

      return readingId;

    } catch (error) {
      console.error('ReadingService: Error in saveReadingWithPhotos:', error);
      return null;
    }
  }

  /**
   * Submit reading to backend using backend IDs
   * Key benefit: Direct submission with backend IDs, no mapping needed
   */
  static async submitReading(submissionData: ReadingSubmissionData): Promise<boolean> {
    try {
      console.log(`ReadingService: Submitting reading for task ${submissionData.taskId}, meter ${submissionData.meterId}`);

      // Convert to MobileReadingDto format
      const mobileReading: MobileReadingDto = {
        taskId: submissionData.taskId,                      // Backend task ID (direct use)
        meterId: submissionData.meterId,                    // Backend meter ID (direct use)
        readingValue: submissionData.readingValue,
        readingDate: new Date(submissionData.readingDate),
        latitude: submissionData.latitude,
        longitude: submissionData.longitude,
        locationAccuracy: submissionData.locationAccuracy,
        readingMethod: submissionData.readingMethod,
        notes: submissionData.notes,
        photoPaths: submissionData.photoPaths || [],
        photos: [], // Will be handled separately if needed
        isOfflineReading: submissionData.isOfflineReading || false,
        offlineId: submissionData.offlineId,
        isValidated: submissionData.isValidated || true,
        validationWarnings: submissionData.validationWarnings || [],
        previousReading: submissionData.previousReading,
        previousReadingDate: submissionData.previousReadingDate ? new Date(submissionData.previousReadingDate) : undefined
      };

      const result = await this.readingApi.submitReading(mobileReading);
      
      if (result.success) {
        console.log(`ReadingService: Successfully submitted reading with ID: ${result.readingId}`);
        return true;
      } else {
        console.error('ReadingService: Backend rejected reading submission:', result.message);
        return false;
      }

    } catch (error) {
      console.error('ReadingService: Error submitting reading:', error);
      return false;
    }
  }

  /**
   * Submit reading and complete task in one operation
   * Uses backend IDs directly
   */
  static async submitReadingAndCompleteTask(submissionData: ReadingSubmissionData): Promise<boolean> {
    try {
      console.log(`ReadingService: Submitting reading and completing task ${submissionData.taskId}`);

      // Use the specialized API endpoint for reading + task completion
      const result = await this.readingApi.submitReadingAndCompleteTask({
        reading: {
          taskId: submissionData.taskId,                    // Backend task ID
          meterId: submissionData.meterId,                  // Backend meter ID
          readingValue: submissionData.readingValue,
          readingDate: new Date(submissionData.readingDate),
          latitude: submissionData.latitude,
          longitude: submissionData.longitude,
          locationAccuracy: submissionData.locationAccuracy,
          readingMethod: submissionData.readingMethod,
          notes: submissionData.notes,
          photoPaths: submissionData.photoPaths || [],
          photos: [],
          isOfflineReading: submissionData.isOfflineReading || false,
          offlineId: submissionData.offlineId,
          isValidated: submissionData.isValidated || true,
          validationWarnings: submissionData.validationWarnings || [],
          previousReading: submissionData.previousReading,
          previousReadingDate: submissionData.previousReadingDate ? new Date(submissionData.previousReadingDate) : undefined
        },
        completionNotes: 'Reading submitted and task completed via mobile app'
      });

      if (result.success) {
        console.log(`ReadingService: Successfully submitted reading and completed task: ${result.readingId}`);
        return true;
      } else {
        console.error('ReadingService: Backend rejected reading and task completion:', result.message);
        return false;
      }

    } catch (error) {
      console.error('ReadingService: Error submitting reading and completing task:', error);
      return false;
    }
  }

  /**
   * Get reading statistics from local database
   */
  static async getReadingStatistics(): Promise<ReadingStatistics> {
    try {
      const totalReadings = await ReadingRepository.getTotalCount();
      const todayReadings = await ReadingRepository.getTodayCount();
      const weekReadings = await ReadingRepository.getWeekCount();
      const pendingUpload = await ReadingRepository.getPendingUploadCount();

      return {
        totalReadings,
        todayReadings,
        weekReadings,
        pendingUpload,
        averagePerDay: weekReadings / 7
      };
    } catch (error) {
      console.error('ReadingService: Error getting reading statistics:', error);
      return {
        totalReadings: 0,
        todayReadings: 0,
        weekReadings: 0,
        pendingUpload: 0,
        averagePerDay: 0
      };
    }
  }

  /**
   * Get readings by user
   */
  static async getUserReadings(userId: number, limit: number = 50) {
    try {
      return await ReadingRepository.getByUserId(userId, limit);
    } catch (error) {
      console.error('ReadingService: Error getting user readings:', error);
      return [];
    }
  }

  /**
   * Get readings by meter
   */
  static async getMeterReadings(meterId: number, limit: number = 50) {
    try {
      return await ReadingRepository.getByMeterId(meterId, limit);
    } catch (error) {
      console.error('ReadingService: Error getting meter readings:', error);
      return [];
    }
  }
} 