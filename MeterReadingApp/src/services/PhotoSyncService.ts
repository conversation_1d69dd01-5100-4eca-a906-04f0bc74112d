import { MeterReadingPhotoRepository } from '../data/repositories/MeterReadingPhotoRepository';
import { PhotoUploadService } from './PhotoUploadService';
import { ReadingPhoto } from '../database/models/MeterReading';
import { networkService } from '../utils/NetworkUtils';

export class PhotoSyncService {
  private static isRunning = false;

  static async syncPendingPhotos(): Promise<{
    success: boolean;
    uploaded: number;
    failed: number;
    message: string;
  }> {
    if (this.isRunning) {
      return {
        success: false,
        uploaded: 0,
        failed: 0,
        message: 'Sync already in progress'
      };
    }

    if (!networkService.isConnected()) {
      return {
        success: false,
        uploaded: 0,
        failed: 0,
        message: 'No network connection'
      };
    }

    this.isRunning = true;
    let uploadedCount = 0;
    let failedCount = 0;

    try {
      const pendingPhotos = await MeterReadingPhotoRepository.getPendingUploadPhotos();
      
      if (pendingPhotos.length === 0) {
        return {
          success: true,
          uploaded: 0,
          failed: 0,
          message: 'No pending photos to sync'
        };
      }

      console.log(`PhotoSyncService: Starting sync of ${pendingPhotos.length} pending photos`);

      for (const photo of pendingPhotos) {
        if (photo.upload_retry_count && photo.upload_retry_count >= 3) {
          console.log(`PhotoSyncService: Skipping photo ${photo.id} - exceeded retry limit`);
          continue;
        }

        try {
          const result = await PhotoUploadService.uploadPhoto(photo);
          if (result.success) {
            uploadedCount++;
            console.log(`PhotoSyncService: Successfully uploaded photo ${photo.id}`);
          } else {
            failedCount++;
            console.error(`PhotoSyncService: Failed to upload photo ${photo.id}: ${result.message}`);
          }
        } catch (error) {
          failedCount++;
          console.error(`PhotoSyncService: Error uploading photo ${photo.id}:`, error);
        }

        await new Promise(resolve => setTimeout(resolve, 1000));
      }

      return {
        success: true,
        uploaded: uploadedCount,
        failed: failedCount,
        message: `Sync completed: ${uploadedCount} uploaded, ${failedCount} failed`
      };

    } catch (error) {
      console.error('PhotoSyncService: Sync error:', error);
      return {
        success: false,
        uploaded: uploadedCount,
        failed: failedCount,
        message: `Sync error: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    } finally {
      this.isRunning = false;
    }
  }

  static async syncPhotosForReading(readingId: number): Promise<boolean> {
    try {
      const photos = await MeterReadingPhotoRepository.findByReadingId(readingId);
      const pendingPhotos = photos.filter(p => p.sync_status === 'pending');

      if (pendingPhotos.length === 0) {
        return true;
      }

      console.log(`PhotoSyncService: Syncing ${pendingPhotos.length} photos for reading ${readingId}`);

      for (const photo of pendingPhotos) {
        try {
          await PhotoUploadService.uploadPhoto(photo);
        } catch (error) {
          console.error(`PhotoSyncService: Failed to upload photo ${photo.id}:`, error);
        }
      }

      return true;
    } catch (error) {
      console.error(`PhotoSyncService: Error syncing photos for reading ${readingId}:`, error);
      return false;
    }
  }

  static async getUploadStats(): Promise<{
    pending: number;
    uploaded: number;
    failed: number;
  }> {
    try {
      return await MeterReadingPhotoRepository.getUploadStats();
    } catch (error) {
      console.error('PhotoSyncService: Error getting upload stats:', error);
      return { pending: 0, uploaded: 0, failed: 0 };
    }
  }

  static async retryFailedUploads(): Promise<void> {
    try {
      const sql = `
        UPDATE reading_photos 
        SET sync_status = 'pending', upload_retry_count = 0, updated_at = ?
        WHERE sync_status = 'error' AND upload_retry_count < 3
      `;
      
      await MeterReadingPhotoRepository.executeSql(sql, [new Date().toISOString()]);
      console.log('PhotoSyncService: Reset failed uploads for retry');
    } catch (error) {
      console.error('PhotoSyncService: Error retrying failed uploads:', error);
    }
  }

  static isCurrentlyRunning(): boolean {
    return this.isRunning;
  }
}
