import { ReadingRepository } from '../data/repositories/ReadingRepository';
import { MeterReadingSyncLogRepository } from '../data/repositories/MeterReadingSyncLogRepository';
import { MeterReadingPhotoRepository } from '../data/repositories/MeterReadingPhotoRepository';
import { TaskRepository } from '../data/repositories/TaskRepository';
import { networkService } from '../utils/NetworkUtils';
import { DataUploadService } from './DataUploadService';
import { MeterReadingSyncLogStatus } from '../database/models/MeterReadingSyncLog';

/**
 * Meter Reading Sync Service
 * 水表读数同步服务 - 负责读数的保存和同步逻辑
 */
export class MeterReadingSyncService {

  /**
   * Upload reading with photos to backend
   * 上传读数和照片到后端服务器
   */
  static async uploadReadingWithPhotos(readingId: number): Promise<boolean> {
    try {
      console.log(`MeterReadingSyncService: Starting upload for reading ${readingId}`);

      // Step 1: Check network connectivity
      if (!networkService.isNetworkConnectedSync()) {
        console.log('MeterReadingSyncService: No network connection, skipping upload');
        return false;
      }

      // Step 2: Query reading data
      const reading = await ReadingRepository.findById(readingId);
      if (!reading) {
        throw new Error(`Reading with ID ${readingId} not found`);
      }

      // Step 3: Query associated photos
      const photos = await MeterReadingPhotoRepository.findByReadingId(readingId);
      console.log(`MeterReadingSyncService: Found ${photos.length} photos for reading ${readingId}`);

      // Step 4: Upload to backend using DataUploadService
      // For now, we'll use the existing uploadPendingReadings method
      // TODO: Create a specific method for single reading upload
      const result = await DataUploadService.uploadPendingReadings();

      if (result.success) {
        // Step 5: Update sync status
        await this.updateSyncStatus(readingId, 'synced');
        console.log(`MeterReadingSyncService: Successfully uploaded reading ${readingId}`);
        return true;
      } else {
        await this.updateSyncStatus(readingId, 'error', result.errors?.join(', ') || 'Upload failed');
        return false;
      }

    } catch (error) {
      console.error(`MeterReadingSyncService: Error uploading reading ${readingId}:`, error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      await this.updateSyncStatus(readingId, 'error', errorMessage);
      return false;
    }
  }

  /**
   * Update sync status for a reading
   * 更新读数的同步状态
   */
  static async updateSyncStatus(
    localReadingId: number,
    status: 'pending' | 'synced' | 'error' | 'conflict',
    errorMessage?: string
  ): Promise<void> {
    try {
      const syncLog = await MeterReadingSyncLogRepository.findByLocalReadingId(localReadingId);

      if (!syncLog) {
        console.warn(`MeterReadingSyncService: No sync log found for reading ${localReadingId}`);
        return;
      }

      const updateData: any = {
        sync_status: status,
        last_update_time: new Date().toISOString(),
        sync_errors: errorMessage
      };

      if (status === 'synced') {
        updateData.last_sync_time = new Date().toISOString();
      }

      await MeterReadingSyncLogRepository.update(syncLog.sync_log_id!, updateData);
      console.log(`MeterReadingSyncService: Updated sync status to ${status} for reading ${localReadingId}`);

      // Also update the reading table's sync_status
      if (status === 'synced') {
        await ReadingRepository.updateSyncStatus(localReadingId, 'synced');
        console.log(`MeterReadingSyncService: Updated reading ${localReadingId} sync_status to synced`);

        // Update task status to inprogress if reading is synced
        const reading = await ReadingRepository.findById(localReadingId);
        if (reading?.task_id) {
          await TaskRepository.updateTaskStatus(reading.task_id, 'InProgress');
          console.log(`MeterReadingSyncService: Updated task ${reading.task_id} status to InProgress`);
        }
      }

    } catch (error) {
      console.error(`MeterReadingSyncService: Error updating sync status for reading ${localReadingId}:`, error);
      throw error;
    }
  }

  /**
   * Get pending readings for sync
   * 获取待同步的读数
   */
  static async getPendingReadingsForSync(limit: number = 50): Promise<any[]> {
    try {
      const pendingSyncLogs = await MeterReadingSyncLogRepository.getPendingSyncLogs(limit);
      const readings = [];

      for (const syncLog of pendingSyncLogs) {
        try {
          const reading = await ReadingRepository.findById(syncLog.local_reading_id);
          readings.push({
            syncLog,
            reading
          });
        } catch (error) {
          console.warn(`MeterReadingSyncService: Reading ${syncLog.local_reading_id} not found, skipping`);
        }
      }

      return readings;
    } catch (error) {
      console.error('MeterReadingSyncService: Error getting pending readings:', error);
      throw error;
    }
  }

  static async getFailedReadingsForRetry(maxRetryCount: number = 3, limit: number = 50): Promise<any[]> {
    try {
      const failedSyncLogs = await MeterReadingSyncLogRepository.getFailedSyncLogs(maxRetryCount, limit);
      const readings = [];

      for (const syncLog of failedSyncLogs) {
        try {
          const reading = await ReadingRepository.findById(syncLog.local_reading_id);
          readings.push({
            syncLog,
            reading
          });
        } catch (error) {
          console.warn(`MeterReadingSyncService: Reading ${syncLog.local_reading_id} not found, skipping`);
        }
      }

      return readings;
    } catch (error) {
      console.error('MeterReadingSyncService: Error getting failed readings:', error);
      throw error;
    }
  }

  static async incrementRetryCount(localReadingId: number): Promise<void> {
    try {
      const syncLog = await MeterReadingSyncLogRepository.findByLocalReadingId(localReadingId);

      if (!syncLog) {
        console.warn(`MeterReadingSyncService: No sync log found for reading ${localReadingId}`);
        return;
      }

      await MeterReadingSyncLogRepository.incrementRetryCount(syncLog.sync_log_id!);

    } catch (error) {
      console.error(`MeterReadingSyncService: Error incrementing retry count for reading ${localReadingId}:`, error);
      throw error;
    }
  }

  static async getSyncStatistics(): Promise<{
    pending: number;
    synced: number;
    error: number;
    conflict: number;
    total: number;
  }> {
    try {
      return await MeterReadingSyncLogRepository.getSyncStatistics();
    } catch (error) {
      console.error('MeterReadingSyncService: Error getting sync statistics:', error);
      throw error;
    }
  }
} 