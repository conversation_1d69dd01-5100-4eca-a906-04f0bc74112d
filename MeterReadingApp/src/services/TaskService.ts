// Task service
// Core business logic for fetching and saving task data
// Based on CORDE LogListService.ts patterns
// Aligned with new work_tasks database schema
// Updated to use Repository layer instead of direct database access

import { SyncResult, TaskApiData } from '../types/SyncTypes';
import { Task } from '../database/models';
import { TaskRepository, WaterMeterRepository } from '../data/repositories';
import api from '../api/BaseApi';

export class TaskService {
  /**
   * Fetch and save tasks with pagination
   * This method handles large datasets by fetching in chunks
   */
  static async fetchAndSaveTasks(pageSize: number = 100): Promise<SyncResult> {
    const operation = 'fetchAndSaveTasks';
    const syncTime = new Date().toISOString();

    try {
      console.log('TaskService: Starting task data sync...');

      let totalSaved = 0;
      let currentPage = 1;
      let hasMoreData = true;

      while (hasMoreData) {
        try {
          // Fetch tasks with pagination
          const response = await api.get('/mobile/tasks/sync/all', {
            params: {
              page: currentPage,
              pageSize: pageSize
            }
          });

          if (!response.data || !Array.isArray(response.data)) {
            console.warn(`TaskService: Invalid response structure on page ${currentPage}`);
            break;
          }

          const tasks: TaskApiData[] = response.data;
          console.log(`TaskService: Page ${currentPage} - Fetched ${tasks.length} tasks`);
          
          // 🐛 详细日志：打印移动端接收到的第一条任务数据
          if (tasks.length > 0 && currentPage === 1) {
            const firstTask = tasks[0];
            console.log('=== 移动端接收到的API数据（第一条）===');
            console.log('原始API数据:', JSON.stringify(firstTask, null, 2));
            console.log('关键字段检查:');
            console.log('- assignedUserId:', firstTask.assignedUserId);
            console.log('- meterId:', firstTask.meterId);
            console.log('- meterNumber:', firstTask.meterNumber);
            console.log('=== 移动端API数据结束 ===');
          }

          if (tasks.length === 0) {
            hasMoreData = false;
            break;
          }

          // Process and save this batch
          const batchSaved = await this.processTaskData(tasks);
          totalSaved += batchSaved;

          console.log(`TaskService: Page ${currentPage} - Saved ${batchSaved}/${tasks.length} tasks`);

          // Check if we have more data
          if (tasks.length < pageSize) {
            hasMoreData = false;
          } else {
            currentPage++;
          }

        } catch (error) {
          console.error(`TaskService: Error fetching page ${currentPage}:`, error);
          break;
        }
      }

      console.log(`TaskService: Sync completed - Total saved: ${totalSaved} tasks`);

      return {
        success: true,
        recordCount: totalSaved,
        syncTime,
        operation
      };

    } catch (error) {
      console.error('TaskService: Error in fetchAndSaveTasks:', error);
      return {
        success: false,
        recordCount: 0,
        errorMessage: error instanceof Error ? error.message : 'Unknown error',
        syncTime,
        operation
      };
    }
  }

  /**
   * Fetch and save specific tasks by IDs
   */
  static async fetchAndSaveTasksByIds(taskIds: number[]): Promise<SyncResult> {
    const operation = 'fetchAndSaveTasksByIds';
    const syncTime = new Date().toISOString();

    try {
      console.log(`TaskService: Fetching ${taskIds.length} specific tasks...`);

      const tasks: TaskApiData[] = [];
      
      for (const taskId of taskIds) {
        try {
          const response = await api.get(`/mobile/tasks/${taskId}`);
          if (response.data) {
            tasks.push(response.data);
          }
        } catch (error) {
          console.warn(`TaskService: Failed to fetch task ${taskId}:`, error);
        }
      }

      const savedCount = await this.processTaskData(tasks);

      console.log(`TaskService: Successfully saved ${savedCount} specific tasks`);

      return {
        success: true,
        recordCount: savedCount,
        syncTime,
        operation
      };

    } catch (error) {
      console.error('TaskService: Error in fetchAndSaveTasksByIds:', error);
      return {
        success: false,
        recordCount: 0,
        errorMessage: error instanceof Error ? error.message : 'Unknown error',
        syncTime,
        operation
      };
    }
  }

  /**
   * Get task statistics from local database
   */
  static async getTaskStatistics(): Promise<{
    totalTasks: number;
    pendingTasks: number;
    completedTasks: number;
  }> {
    try {
      const totalTasks = await TaskRepository.getTotalCount();
      const pendingTasks = await TaskRepository.getTaskCountByStatus('Pending');
      const completedTasks = await TaskRepository.getTaskCountByStatus('Completed');

      return {
        totalTasks,
        pendingTasks,
        completedTasks
      };
    } catch (error) {
      console.error('TaskService: Error getting task statistics:', error);
      return {
        totalTasks: 0,
        pendingTasks: 0,
        completedTasks: 0
      };
    }
  }

  /**
   * Get user's assigned tasks
   */
  static async getUserTasks(userId: number): Promise<Task[]> {
    try {
      const workTasks = await TaskRepository.getUserTasks(userId);
      // WorkTask[] is compatible with Task[] since Task extends WorkTask
      return workTasks as Task[];
    } catch (error) {
      console.error('TaskService: Error getting user tasks:', error);
      return [];
    }
  }

  /**
   * Find task by ID
   */
  static async findTaskById(id: number): Promise<Task | null> {
    try {
      const workTask = await TaskRepository.findById(id);
      // WorkTask is compatible with Task since Task extends WorkTask
      return workTask as Task;
    } catch (error) {
      console.error('TaskService: Error finding task by ID:', error);
      return null;
    }
  }

  /**
   * Process and save task data to local database
   * Key fix: Use meterSerialNumber to find local meter_id instead of using backend meterId
   */
  private static async processTaskData(tasks: TaskApiData[]): Promise<number> {
    if (tasks.length === 0) {
      return 0;
    }

    try {
      let savedCount = 0;
      let isFirstTask = true;  // 标记是否为第一条任务

      for (const taskData of tasks) {
        try {
          console.log(`TaskService: Processing task ${taskData.id} with meter serial ${taskData.meterNumber}`);

          // Critical fix: Find local meter by serial number instead of using backend meterId
          let localMeterId: number | null = null;
          
          if (taskData.meterNumber) {
            const localMeter = await WaterMeterRepository.findBySerialNumber(taskData.meterNumber);
            if (localMeter) {
              localMeterId = localMeter.id!;
              console.log(`TaskService: Found local meter ID ${localMeterId} for serial ${taskData.meterNumber}`);
            } else {
              console.warn(`TaskService: Water meter not found for serial number: ${taskData.meterNumber}`);
              // Skip this task if we can't find the associated water meter
              continue;
            }
          } else {
            console.warn(`TaskService: Task ${taskData.id} has no meterNumber`);
            continue;
          }

          // Map API data to our local Task model using local meter ID
          const workTask: any = {
            id: taskData.id,                              // Use backend task ID directly
            name: taskData.name || `Task ${taskData.id}`,
            description: taskData.description || '',
            status: taskData.status || 'Pending',
            priority: taskData.priority || 'Medium',
            type: taskData.type || 'MeterReading',
            assigned_user_id: taskData.assignedUserId,    // 直接对应，无需映射
            meter_id: localMeterId,                       // Use LOCAL meter ID (found by serial number)
            due_date: taskData.dueDate,
            scheduled_date: taskData.startDate,           // Use startDate (updated field name)
            completed_date: null,                         // Will be set when task is completed
            location: taskData.location || '',
            service_address: taskData.address || '',
            instructions: taskData.instructions || '',
            notes: taskData.notes || '',
            created_at: taskData.createdAt || new Date().toISOString(),
            updated_at: new Date().toISOString(),
            is_deleted: 0,
            created_by: 'sync',
            updated_by: 'sync'
          };

          // 🐛 详细日志：打印第一条任务的数据转换过程
          if (isFirstTask) {
            console.log('=== 移动端第一条任务数据转换 ===');
            console.log('API数据 -> 数据库数据映射:');
            console.log('- taskData.assignedUserId:', taskData.assignedUserId, '-> assigned_user_id:', workTask.assigned_user_id);
            console.log('- taskData.meterId:', taskData.meterId, '-> meter_id:', workTask.meter_id);
            console.log('- taskData.meterNumber:', taskData.meterNumber, '-> 通过序列号查找到meter_id:', localMeterId);
            console.log('最终workTask对象:', JSON.stringify(workTask, null, 2));
            console.log('=== 移动端数据转换结束 ===');
            isFirstTask = false;
          }

          // Use repository upsert with backend ID (INSERT OR REPLACE)
          await TaskRepository.upsert(workTask);
          console.log(`TaskService: Upserted task ${taskData.id} successfully`);

          savedCount++;
        } catch (error) {
          console.error(`TaskService: Failed to save task ${taskData.id}:`, error);
        }
      }

      return savedCount;
    } catch (error) {
      console.error('TaskService: Error in processTaskData:', error);
      return 0;
    }
  }
} 