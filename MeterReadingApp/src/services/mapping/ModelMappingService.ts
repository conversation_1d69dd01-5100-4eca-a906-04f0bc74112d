// Model Mapping Service
// Handles conversion between frontend and backend data models
// Ensures consistent field mapping and data transformation

import { 
  BackendWorkTask, 
  BackendWaterMeter, 
  BackendReading, 
  BackendUser 
} from '../../types/BackendModels';
import { MobileTaskDto, MeterInfoDto } from '../../types/mobile';
import { WorkTask, WaterMeter } from '../../database/models';

export class ModelMappingService {

  // ============ Backend to Frontend Mapping ============

  /**
   * Convert Backend WorkTask to Mobile Task DTO
   */
  static backendTaskToMobileDto(backendTask: BackendWorkTask, meterInfo?: BackendWaterMeter): MobileTaskDto {
    return {
      // Core WorkTask fields
      id: backendTask.id,
      name: backendTask.name,
      description: backendTask.description,
      status: backendTask.status,
      priority: backendTask.priority,
      type: backendTask.type,

      // Assignment fields
      assignedUserId: backendTask.assignedUserId,

      // Water meter relation
      meterId: backendTask.meterId,
      meterNumber: meterInfo?.serialNumber || backendTask.meterId?.toString(),

      // Time management
      dueDate: backendTask.dueDate ? new Date(backendTask.dueDate) : undefined,
      scheduledDate: backendTask.scheduledDate ? new Date(backendTask.scheduledDate) : undefined,
      completedDate: backendTask.completedDate ? new Date(backendTask.completedDate) : undefined,
      startDate: backendTask.startDate ? new Date(backendTask.startDate) : undefined,

      // Location information
      location: backendTask.location,
      serviceAddress: backendTask.serviceAddress,

      // Task details
      instructions: backendTask.instructions,
      notes: backendTask.notes,

      // Water meter information (from joined data)
      meterType: meterInfo?.meterType,
      assetId: meterInfo?.assetId,
      accountNumber: meterInfo?.accountNumber,
      lastReading: meterInfo?.lastReading || meterInfo?.lastRead,
      lastReadingDate: meterInfo?.lastReadingDate ? new Date(meterInfo.lastReadingDate) : undefined,

      // Customer information (from joined data)
      customerName: meterInfo?.customerName,
      customerPhone: undefined, // Not available in backend model
      customerEmail: undefined, // Not available in backend model
      address: meterInfo?.address || backendTask.serviceAddress,

      // Location information (from meter)
      latitude: backendTask.latitude || meterInfo?.latitude,
      longitude: backendTask.longitude || meterInfo?.longitude,

      // Legacy assignment fields (for backward compatibility)
      assignedUserId: backendTask.assignedUserId,
      assignedDate: new Date(backendTask.createdAt),
      assignedBy: backendTask.createdBy,
      assignmentType: 'direct',

      // Progress information
      estimatedHours: backendTask.estimatedHours,
      progressPercentage: backendTask.progressPercentage,

      // Additional meter information
      meterInfo: meterInfo ? this.backendMeterToMeterInfoDto(meterInfo) : undefined,

      // Mobile-specific computed fields
      isUrgent: backendTask.priority === 'High' || backendTask.priority === 'Critical',
      isOverdue: backendTask.dueDate ? new Date(backendTask.dueDate) < new Date() && backendTask.status !== 'Completed' : false,
      daysUntilDue: backendTask.dueDate ? Math.ceil((new Date(backendTask.dueDate).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24)) : undefined,

      // Audit fields
      createdAt: new Date(backendTask.createdAt),
      updatedAt: new Date(backendTask.updatedAt)
    };
  }

  /**
   * Convert Backend WaterMeter to MeterInfo DTO
   */
  static backendMeterToMeterInfoDto(backendMeter: BackendWaterMeter): MeterInfoDto {
    return {
      // Core identity fields
      id: backendMeter.id,
      assetId: backendMeter.assetId,
      serialNumber: backendMeter.serialNumber,
      accountNumber: backendMeter.accountNumber,

      // Location information
      location: backendMeter.location,
      address: backendMeter.address,
      latitude: backendMeter.latitude,
      longitude: backendMeter.longitude,

      // Meter information
      meterType: backendMeter.meterType,
      status: backendMeter.status,
      customerName: backendMeter.customerName,
      lastReading: backendMeter.lastReading || backendMeter.lastRead,
      lastReadingDate: backendMeter.lastReadingDate ? new Date(backendMeter.lastReadingDate) : undefined,

      // Legacy/additional fields
      meterId: backendMeter.id,
      meterNumber: backendMeter.serialNumber,
      brand: backendMeter.brand,
      model: backendMeter.model,
      size: backendMeter.meterSize,
      type: backendMeter.meterType,
      installDate: backendMeter.installDate ? new Date(backendMeter.installDate) : undefined,
      manufacturerName: backendMeter.brand
    };
  }

  // ============ Frontend to Backend Mapping ============

  /**
   * Convert Mobile Task DTO to Backend WorkTask for API submission
   */
  static mobileDtoToBackendTask(mobileTask: MobileTaskDto): Partial<BackendWorkTask> {
    return {
      id: mobileTask.id,
      name: mobileTask.name,
      description: mobileTask.description,
      status: mobileTask.status,
      priority: mobileTask.priority,
      type: mobileTask.type,
      assignedUserId: mobileTask.assignedUserId,
      meterId: mobileTask.meterId,
      dueDate: mobileTask.dueDate?.toISOString(),
      scheduledDate: mobileTask.scheduledDate?.toISOString(),
      completedDate: mobileTask.completedDate?.toISOString(),
      startDate: mobileTask.startDate?.toISOString(),
      location: mobileTask.location,
      serviceAddress: mobileTask.serviceAddress,
      instructions: mobileTask.instructions,
      notes: mobileTask.notes,
      estimatedHours: mobileTask.estimatedHours,
      progressPercentage: mobileTask.progressPercentage || 0,
      latitude: mobileTask.latitude,
      longitude: mobileTask.longitude
    };
  }

  /**
   * Convert MeterInfo DTO to Backend WaterMeter for API submission
   */
  static meterInfoDtoToBackendMeter(meterInfo: MeterInfoDto): Partial<BackendWaterMeter> {
    return {
      id: meterInfo.id,
      assetId: meterInfo.assetId,
      serialNumber: meterInfo.serialNumber,
      accountNumber: meterInfo.accountNumber,
      location: meterInfo.location,
      address: meterInfo.address || meterInfo.location,
      latitude: meterInfo.latitude,
      longitude: meterInfo.longitude,
      meterType: meterInfo.meterType,
      status: meterInfo.status,
      customerName: meterInfo.customerName,
      lastReading: meterInfo.lastReading,
      lastReadingDate: meterInfo.lastReadingDate?.toISOString(),
      brand: meterInfo.brand,
      model: meterInfo.model,
      meterSize: meterInfo.size || '',
      installDate: meterInfo.installDate?.toISOString(),
      source: 'Mobile',
      syncStatus: 'Pending'
    };
  }

  // ============ Database to Frontend Mapping ============

  /**
   * Convert Database WorkTask to Mobile Task DTO
   */
  static dbTaskToMobileDto(dbTask: WorkTask, meterInfo?: WaterMeter): MobileTaskDto {
    return {
      // Core WorkTask fields
      id: dbTask.id || 0,
      name: dbTask.name,
      description: dbTask.description,
      status: dbTask.status,
      priority: dbTask.priority,
      type: dbTask.type,

      // Assignment fields
      assignedUserId: dbTask.assigned_user_id,

      // Water meter relation
      meterId: dbTask.meter_id,
      meterNumber: meterInfo?.serial_number || dbTask.meter_id?.toString(),

      // Time management
      dueDate: dbTask.due_date ? new Date(dbTask.due_date) : undefined,
      scheduledDate: dbTask.scheduled_date ? new Date(dbTask.scheduled_date) : undefined,
      completedDate: dbTask.completed_date ? new Date(dbTask.completed_date) : undefined,

      // Location information
      location: dbTask.location,
      serviceAddress: dbTask.service_address,

      // Task details
      instructions: dbTask.instructions,
      notes: dbTask.notes,

      // Water meter information (from joined data)
      meterType: meterInfo?.meter_type,
      assetId: meterInfo?.asset_id,
      accountNumber: meterInfo?.account_number,
      lastReading: meterInfo?.last_reading,
      lastReadingDate: meterInfo?.last_reading_date ? new Date(meterInfo.last_reading_date) : undefined,

      // Customer information (from joined data)
      customerName: meterInfo?.customer_name,
      address: meterInfo?.address || dbTask.service_address,

      // Location information (from meter)
      latitude: meterInfo?.latitude,
      longitude: meterInfo?.longitude,

      // Legacy assignment fields
      assignedUserId: dbTask.assigned_user_id,
      assignedDate: dbTask.created_at ? new Date(dbTask.created_at) : new Date(),
      assignedBy: dbTask.created_by || 'system',
      assignmentType: 'direct',

      // Progress information
      progressPercentage: dbTask.status === 'Completed' ? 100 : (dbTask.status === 'InProgress' ? 50 : 0),

      // Mobile-specific computed fields
      isUrgent: dbTask.priority === 'High' || dbTask.priority === 'Critical',
      isOverdue: dbTask.due_date ? new Date(dbTask.due_date) < new Date() && dbTask.status !== 'Completed' : false,
      daysUntilDue: dbTask.due_date ? Math.ceil((new Date(dbTask.due_date).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24)) : undefined,

      // Audit fields
      createdAt: dbTask.created_at ? new Date(dbTask.created_at) : new Date(),
      updatedAt: dbTask.updated_at ? new Date(dbTask.updated_at) : new Date()
    };
  }

  /**
   * Convert Database WaterMeter to MeterInfo DTO
   */
  static dbMeterToMeterInfoDto(dbMeter: WaterMeter): MeterInfoDto {
    return {
      // Core identity fields
      id: dbMeter.id || 0,
      assetId: dbMeter.asset_id,
      serialNumber: dbMeter.serial_number,
      accountNumber: dbMeter.account_number,

      // Location information
      location: dbMeter.location,
      address: dbMeter.address,
      latitude: dbMeter.latitude,
      longitude: dbMeter.longitude,

      // Meter information
      meterType: dbMeter.meter_type,
      status: dbMeter.status,
      customerName: dbMeter.customer_name,
      lastReading: dbMeter.last_reading,
      lastReadingDate: dbMeter.last_reading_date ? new Date(dbMeter.last_reading_date) : undefined,

      // Legacy fields
      meterId: dbMeter.id,
      meterNumber: dbMeter.serial_number
    };
  }

  // ============ Frontend to Database Mapping ============

  /**
   * Convert Mobile Task DTO to Database WorkTask for local storage
   */
  static mobileDtoToDbTask(mobileTask: MobileTaskDto): Partial<WorkTask> {
    return {
      id: mobileTask.id,
      name: mobileTask.name,
      description: mobileTask.description,
      status: mobileTask.status,
      priority: mobileTask.priority,
      type: mobileTask.type,
      assigned_user_id: mobileTask.assignedUserId,
      meter_id: mobileTask.meterId,
      due_date: mobileTask.dueDate?.toISOString(),
      scheduled_date: mobileTask.scheduledDate?.toISOString(),
      completed_date: mobileTask.completedDate?.toISOString(),
      location: mobileTask.location,
      service_address: mobileTask.serviceAddress,
      instructions: mobileTask.instructions,
      notes: mobileTask.notes
    };
  }

  /**
   * Convert MeterInfo DTO to Database WaterMeter for local storage
   */
  static meterInfoDtoToDbMeter(meterInfo: MeterInfoDto): Partial<WaterMeter> {
    return {
      id: meterInfo.id,
      asset_id: meterInfo.assetId,
      serial_number: meterInfo.serialNumber,
      account_number: meterInfo.accountNumber,
      location: meterInfo.location,
      address: meterInfo.address,
      latitude: meterInfo.latitude,
      longitude: meterInfo.longitude,
      meter_type: meterInfo.meterType,
      status: meterInfo.status,
      customer_name: meterInfo.customerName,
      last_reading: meterInfo.lastReading,
      last_reading_date: meterInfo.lastReadingDate?.toISOString(),
      sync_status: 'Synced'
    };
  }

  // ============ Utility Methods ============

  /**
   * Safely convert date string to Date object
   */
  static safeStringToDate(dateString?: string): Date | undefined {
    if (!dateString) return undefined;
    const date = new Date(dateString);
    return isNaN(date.getTime()) ? undefined : date;
  }

  /**
   * Safely convert Date object to ISO string
   */
  static safeDateToString(date?: Date): string | undefined {
    if (!date || isNaN(date.getTime())) return undefined;
    return date.toISOString();
  }

  /**
   * Clean string values (trim and handle null/undefined)
   */
  static cleanString(value?: string | null): string | undefined {
    if (value === null || value === undefined) return undefined;
    const trimmed = value.toString().trim();
    return trimmed === '' ? undefined : trimmed;
  }

  /**
   * Ensure numeric values are valid
   */
  static safeNumber(value?: number | null): number | undefined {
    if (value === null || value === undefined || isNaN(value)) return undefined;
    return value;
  }

  /**
   * Batch conversion helper for lists
   */
  static batchConvert<TInput, TOutput>(
    items: TInput[],
    converter: (item: TInput) => TOutput
  ): TOutput[] {
    return items.map(converter);
  }
} 