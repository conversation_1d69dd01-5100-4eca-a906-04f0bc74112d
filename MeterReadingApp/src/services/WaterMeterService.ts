// Water Meter service
// Core business logic for fetching and managing water meter data
// Aligned with new water_meters database schema

import { WaterMeterRepository } from '../data/repositories';
import { SyncResult } from '../types/SyncTypes';
import { WaterMeter } from '../database/models';
import { BaselineService } from './BaselineService';
import api from '../api/BaseApi';

// Backend API water meter interface
interface BackendWaterMeterData {
  id: number;
  assetId?: string;
  serialNumber: string;
  accountNumber?: string;
  location: string;
  address?: string;
  latitude?: number;
  longitude?: number;
  meterType?: string;
  status?: string;
  customerName?: string;
  lastReading?: number;
  lastReadingDate?: string;
  // Additional backend fields
  bookNumber?: string;
  unit?: string;
  roadName?: string;
  township?: string;
  recentChange?: string;
  customerCode?: string;
  brand?: string;
  model?: string;
  batteryLevel?: number;
  routeId?: number;
  meterSize?: string;
}

export class WaterMeterService {
  /**
   * Fetch and save water meters from backend API
   */
  static async fetchAndSaveWaterMeters(): Promise<SyncResult> {
    const operation = 'fetchAndSaveWaterMeters';
    const syncTime = new Date().toISOString();

    try {
      console.log('WaterMeterService: Starting water meter data fetch...');

      // Fetch ALL pages from backend API
      let allBackendMeters: BackendWaterMeterData[] = [];
      let currentPage = 1;
      const pageSize = 1000;
      let hasMoreData = true;

      while (hasMoreData) {
        console.log(`WaterMeterService: Fetching page ${currentPage} (pageSize: ${pageSize})...`);
        
        const response = await api.get('/water-meters', {
          params: {
            pageSize: pageSize,
            page: currentPage
          }
        });

        // Handle different response structures
        let pageMeters: BackendWaterMeterData[] = [];
        
        if (Array.isArray(response.data)) {
          // Direct array response
          pageMeters = response.data;
        } else if (response.data && Array.isArray(response.data.data)) {
          // Paginated response with data property
          pageMeters = response.data.data;
        } else if (response.data && Array.isArray(response.data.items)) {
          // Response with items property
          pageMeters = response.data.items;
        } else {
          console.error('WaterMeterService: Unexpected API response structure:', response.data);
          throw new Error(`Invalid API response structure. Expected array or object with data/items property, got: ${typeof response.data}`);
        }

        console.log(`WaterMeterService: Page ${currentPage} returned ${pageMeters.length} water meters`);
        
        // Add to total collection
        allBackendMeters = allBackendMeters.concat(pageMeters);
        
        // Check if we should continue fetching
        if (pageMeters.length < pageSize) {
          // Last page (partial or empty)
          hasMoreData = false;
          console.log(`WaterMeterService: Reached last page (page ${currentPage} returned ${pageMeters.length} < ${pageSize})`);
        } else {
          // Continue to next page
          currentPage++;
        }
      }

      console.log(`WaterMeterService: Fetched total ${allBackendMeters.length} water meters from ${currentPage} pages`);
      const backendMeters = allBackendMeters;

      // Map to our local model format with backend IDs
      const waterMeters = backendMeters.map(meter => ({
        id: meter.id,                                   // Backend ID preserved
        asset_id: meter.assetId,
        serial_number: meter.serialNumber,
        account_number: meter.accountNumber,
        location: meter.location || '',
        address: meter.address,
        latitude: meter.latitude,
        longitude: meter.longitude,
        meter_type: meter.meterType || 'Water',
        status: meter.status || 'Active',
        customer_name: meter.customerName,
        last_reading: meter.lastReading,
        last_reading_date: meter.lastReadingDate,
        sync_status: 'Synced',
        last_sync_date: new Date().toISOString(),
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        is_deleted: false,                              // Boolean for SyncWaterMeterData
        created_by: 'sync',
        updated_by: 'sync'
      }));

      // Save using Repository with backend ID alignment
      await WaterMeterRepository.bulkUpsert(waterMeters);

      console.log(`WaterMeterService: Successfully saved ${waterMeters.length} water meters`);

      return {
        success: true,
        recordCount: waterMeters.length,
        syncTime,
        operation
      };

    } catch (error) {
      console.error('WaterMeterService: Error in fetchAndSaveWaterMeters:', error);
      return {
        success: false,
        recordCount: 0,
        errorMessage: error instanceof Error ? error.message : 'Unknown error',
        syncTime,
        operation
      };
    }
  }

  /**
   * Get water meter statistics
   */
  static async getWaterMeterStatistics(): Promise<{
    totalMeters: number;
    activeMeters: number;
    inactiveMeters: number;
  }> {
    try {
      const totalMeters = await WaterMeterRepository.getTotalCount();
      const activeMeters = await WaterMeterRepository.getCountByStatus('Active');
      const inactiveMeters = await WaterMeterRepository.getCountByStatus('Inactive');

      return {
        totalMeters,
        activeMeters,
        inactiveMeters
      };
    } catch (error) {
      console.error('WaterMeterService: Error getting statistics:', error);
      return {
        totalMeters: 0,
        activeMeters: 0,
        inactiveMeters: 0
      };
    }
  }

  /**
   * Get water meter by ID
   */
  static async getWaterMeterById(meterId: number): Promise<WaterMeter | null> {
    try {
      return await WaterMeterRepository.findById(meterId);
    } catch (error) {
      console.error('WaterMeterService: Error getting meter by ID:', error);
      return null;
    }
  }

  /**
   * Search water meters by serial number
   */
  static async searchWaterMetersBySerialNumber(serialNumber: string): Promise<WaterMeter[]> {
    try {
      const meter = await WaterMeterRepository.findBySerialNumber(serialNumber);
      return meter ? [meter] : [];
    } catch (error) {
      console.error('WaterMeterService: Error searching by serial number:', error);
      return [];
    }
  }

  /**
   * Get water meters by location/area
   */
  static async getWaterMetersByLocation(location: string): Promise<WaterMeter[]> {
    try {
      return await WaterMeterRepository.getByLocation(location);
    } catch (error) {
      console.error('WaterMeterService: Error getting meters by location:', error);
      return [];
    }
  }

  /**
   * Get effective last reading for a meter using baseline fallback logic
   * 获取水表的有效最后读数，使用基线降级逻辑
   */
  static async getEffectiveLastReading(meterId: number): Promise<number | null> {
    try {
      console.log(`WaterMeterService: Getting effective last reading for meter ${meterId}`);
      return await BaselineService.getEffectiveLastReading(meterId);
    } catch (error) {
      console.error(`WaterMeterService: Error getting effective last reading for meter ${meterId}:`, error);
      return null;
    }
  }
} 