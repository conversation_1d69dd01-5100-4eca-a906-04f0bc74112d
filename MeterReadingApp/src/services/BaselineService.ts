import api from '../api/BaseApi'; // 使用与其他API相同的api实例
import { BaselineRepository } from '../data/repositories/BaselineRepository';
import { SyncBaselineData, BackendBaselineData, BaselineRecord, BaselineStatistics, BaselineSyncResponse } from '../database/models/BaselineRecord';
import DatabaseManager from '../database/DatabaseManager';

/**
 * Baseline Service - Business logic for baseline data management
 * 基线服务 - 基线数据管理的业务逻辑
 */
export class BaselineService {
  
  /**
   * Fetch and save all baseline data from backend with pagination
   * 从后端获取并保存所有基线数据，支持分页
   */
  static async fetchAndSaveBaselines(): Promise<{success: boolean, message: string, count: number}> {
    try {
      console.log('BaselineService: Starting baseline sync...');
      
      let page = 1;
      const pageSize = 1000;
      let totalSynced = 0;
      let hasMore = true;

      while (hasMore) {
        try {
          const response = await api.get('/mobile/baselines/sync/all', {
            params: { page, pageSize }
          });

          if (!response || !response.data) {
            console.warn(`BaselineService: No data for page ${page}`);
            break;
          }

          let baselines: BackendBaselineData[];
          let paginationInfo: any;

          if (Array.isArray(response.data)) {
            baselines = response.data;
            paginationInfo = (response as any).pagination;
          } else if (response.data.data && Array.isArray(response.data.data)) {
            baselines = response.data.data;
            paginationInfo = response.data.pagination;
          } else {
            console.error('BaselineService: Invalid response structure');
            break;
          }

          if (baselines.length > 0) {
            const transformedBaselines: SyncBaselineData[] = baselines.map(b => ({
              id: b.id, meter_id: b.meterId, baseline_date: b.baselineDate, baseline_value: b.baselineValue,
              baseline_type: b.baselineType, status: b.status, import_batch: b.importBatch || '',
              source_file: b.sourceFile || '', data_source: b.dataSource, validation_notes: b.validationNotes || '',
              is_validated: b.isValidated ? 1 : 0, validated_date: b.validatedDate || null,
              validated_by: b.validatedBy || '', has_validation_errors: b.hasValidationErrors ? 1 : 0,
              validation_errors: b.validationErrors || '', is_anomalous: b.isAnomalous ? 1 : 0,
              anomaly_description: b.anomalyDescription || '', previous_baseline_id: b.previousBaselineId || null,
              previous_baseline_value: b.previousBaselineValue || null, variance_from_previous: b.varianceFromPrevious || null,
              variance_percentage: b.variancePercentage || null, is_corrected: b.isCorrected ? 1 : 0,
              corrected_date: b.correctedDate || null, corrected_by: b.correctedBy || '',
              correction_reason: b.correctionReason || '', confidence_level: b.confidenceLevel,
              notes: b.notes || '', created_at: b.createdAt, updated_at: b.updatedAt,
              created_by: b.createdBy, updated_by: b.updatedBy, is_deleted: 0, sync_status: 'Synced',
              last_sync_date: new Date().toISOString()
            }));

            await BaselineRepository.bulkUpsert(transformedBaselines);
            totalSynced += baselines.length;
          }

          hasMore = paginationInfo?.hasMore ?? false;
          page++;
          
          if (baselines.length < pageSize) {
            hasMore = false;
          }
          
        } catch (error: any) {
          console.error(`BaselineService: Error on page ${page}:`, error);
          if (error?.message?.includes('Network')) {
            break;
          }
          page++;
        }
      }

      console.log(`BaselineService: Sync completed. Total: ${totalSynced}`);
      return { success: true, message: `Synced ${totalSynced} baselines`, count: totalSynced };
      
    } catch (error: any) {
      console.error('BaselineService: Sync failed:', error);
      return { success: false, message: `Sync failed: ${error.message}`, count: 0 };
    }
  }

  /**
   * Get effective last reading for a meter (considering baseline and readings)
   * 获取水表的有效最后读数（考虑基线和读数）
   */
  static async getEffectiveLastReading(meterId: number): Promise<number | null> {
    try {
      console.log(`BaselineService: Getting effective last reading for meter ${meterId}`);
      
      // Priority 1: Try to get last_reading from WaterMeter table
      const meterResult = await DatabaseManager.getInstance().executeSql(
        'SELECT last_reading FROM water_meters WHERE id = ? AND is_deleted = 0',
        [meterId]
      );
      
      if (meterResult.rows.length > 0 && meterResult.rows.item(0).last_reading !== null && meterResult.rows.item(0).last_reading > 0) {
        const lastReading = meterResult.rows.item(0).last_reading;
        console.log(`BaselineService: Found last_reading from water_meters: ${lastReading}`);
        return lastReading;
      }
      
      // Priority 2: Get latest active baseline value
      const latestBaseline = await BaselineRepository.getLatestActiveBaseline(meterId);
      if (latestBaseline) {
        console.log(`BaselineService: Found latest baseline: ${latestBaseline.baseline_value} (Date: ${latestBaseline.baseline_date})`);
        return latestBaseline.baseline_value;
      }
      
      // Priority 3: Fallback to latest reading from readings table
      const readingResult = await DatabaseManager.getInstance().executeSql(
        'SELECT current_reading FROM readings WHERE meter_id = ? AND is_deleted = 0 ORDER BY reading_date DESC LIMIT 1',
        [meterId]
      );
      
      if (readingResult.rows.length > 0 && readingResult.rows.item(0).current_reading !== null) {
        const currentReading = readingResult.rows.item(0).current_reading;
        console.log(`BaselineService: Found fallback reading: ${currentReading}`);
        return currentReading;
      }
      
      console.log(`BaselineService: No effective last reading found for meter ${meterId}`);
      return null;
      
    } catch (error) {
      console.error(`BaselineService: Error getting effective last reading for meter ${meterId}:`, error);
      return null;
    }
  }

  /**
   * Get baseline statistics
   * 获取基线统计信息
   */
  static async getBaselineStatistics(): Promise<BaselineStatistics> {
    try {
      console.log('BaselineService: Fetching baseline statistics...');
      
      const [totalResult, activeResult, validatedResult, anomalousResult, correctedResult, recentResult] = await Promise.all([
        DatabaseManager.getInstance().executeSql('SELECT COUNT(*) as count FROM baseline_records WHERE is_deleted = 0'),
        DatabaseManager.getInstance().executeSql(`
      SELECT COUNT(*) as count FROM baseline_records
      WHERE status = 'Active' AND is_deleted = 0
    `),
        DatabaseManager.getInstance().executeSql(`
      SELECT COUNT(*) as count FROM baseline_records
      WHERE is_validated = 1 AND is_deleted = 0
    `),
        DatabaseManager.getInstance().executeSql(`
      SELECT COUNT(*) as count FROM baseline_records
      WHERE is_anomalous = 1 AND is_deleted = 0
    `),
        DatabaseManager.getInstance().executeSql(`
      SELECT COUNT(*) as count FROM baseline_records
      WHERE is_corrected = 1 AND is_deleted = 0
    `),
        DatabaseManager.getInstance().executeSql(`
      SELECT COUNT(*) as count FROM baseline_records 
      WHERE created_at >= ? AND is_deleted = 0
    `, [new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString()]) // Last 30 days
      ]);

      const stats: BaselineStatistics = {
        total: totalResult.rows.item(0).count,
        active: activeResult.rows.item(0).count,
        validated: validatedResult.rows.item(0).count,
        anomalous: anomalousResult.rows.item(0).count,
        corrected: correctedResult.rows.item(0).count,
        recent: recentResult.rows.item(0).count
      };

      console.log('BaselineService: Baseline statistics:', stats);
      return stats;
      
    } catch (error) {
      console.error('BaselineService: Error getting baseline statistics:', error);
      return {
        total: 0,
        active: 0,
        validated: 0,
        anomalous: 0,
        corrected: 0,
        recent: 0
      };
    }
  }

  /**
   * Get baseline history for a meter
   * 获取水表的基线历史
   */
  static async getMeterBaselineHistory(meterId: number, limit: number = 10): Promise<BaselineRecord[]> {
    try {
      const result = await DatabaseManager.getInstance().executeSql(`
        SELECT * FROM baseline_records
        WHERE meter_id = ? AND is_deleted = 0
        ORDER BY baseline_date DESC
        LIMIT ?
      `, [meterId, limit]);

      const baselines: BaselineRecord[] = [];
      for (let i = 0; i < result.rows.length; i++) {
        baselines.push(result.rows.item(i));
      }

      return baselines;
    } catch (error) {
      console.error(`BaselineService: Error getting baseline history for meter ${meterId}:`, error);
      return [];
    }
  }

  /**
   * Incremental sync - fetch only updated baselines
   * 增量同步 - 只获取更新的基线
   */
  static async incrementalSync(lastSyncDate: Date): Promise<{success: boolean, message: string, count: number}> {
    try {
      console.log(`BaselineService: Starting incremental sync from ${lastSyncDate.toISOString()}`);
      
      let page = 1;
      const pageSize = 1000;
      let totalSynced = 0;
      let hasMore = true;
      
      while (hasMore) {
        console.log(`BaselineService: Fetching incremental page ${page}...`);
        
        try {
          // 使用与其他API相同的调用方式
          const response = await api.get('/mobile/baselines/sync/all', {
            params: {
              page,
              pageSize,
              lastSyncDate: lastSyncDate.toISOString()
            }
          });
          
          if (!response || !response.data) {
            console.warn(`BaselineService: Invalid incremental response for page ${page}`);
            break;
          }
          
          // Handle response structure
          let baselines: BackendBaselineData[];
          let paginationInfo: any;
          
          if (Array.isArray(response.data)) {
            baselines = response.data;
            paginationInfo = (response as any).pagination;
          } else if (response.data.data && Array.isArray(response.data.data)) {
            baselines = response.data.data;
            paginationInfo = response.data.pagination;
          } else {
            throw new Error('Invalid incremental sync API response structure');
          }
          
          console.log(`BaselineService: Incremental sync received ${baselines.length} updated baselines for page ${page}`);
          
          if (baselines.length > 0) {
            // 🎯 Transform camelCase backend data to snake_case for mobile database
            const transformedBaselines: SyncBaselineData[] = baselines.map(b => ({
              id: b.id,
              meter_id: b.meterId,
              baseline_date: b.baselineDate,
              baseline_value: b.baselineValue,
              baseline_type: b.baselineType,
              status: b.status,
              import_batch: b.importBatch || '',
              source_file: b.sourceFile || '',
              data_source: b.dataSource,
              validation_notes: b.validationNotes || '',
              is_validated: b.isValidated ? 1 : 0,
              validated_date: b.validatedDate || null,
              validated_by: b.validatedBy || '',
              has_validation_errors: b.hasValidationErrors ? 1 : 0,
              validation_errors: b.validationErrors || '',
              is_anomalous: b.isAnomalous ? 1 : 0,
              anomaly_description: b.anomalyDescription || '',
              previous_baseline_id: b.previousBaselineId || null,
              previous_baseline_value: b.previousBaselineValue || null,
              variance_from_previous: b.varianceFromPrevious || null,
              variance_percentage: b.variancePercentage || null,
              is_corrected: b.isCorrected ? 1 : 0,
              corrected_date: b.correctedDate || null,
              corrected_by: b.correctedBy || '',
              correction_reason: b.correctionReason || '',
              confidence_level: b.confidenceLevel,
              notes: b.notes || '',
              created_at: b.createdAt,
              updated_at: b.updatedAt,
              created_by: b.createdBy,
              updated_by: b.updatedBy,
              is_deleted: 0,
              sync_status: 'Synced',
              last_sync_date: new Date().toISOString()
            }));
            
            await BaselineRepository.bulkUpsert(transformedBaselines);
            totalSynced += baselines.length;
          }
          
          hasMore = paginationInfo?.hasMore ?? false;
          page++;
          
          if (baselines.length < pageSize) {
            hasMore = false;
          }
          
        } catch (error) {
          console.error(`BaselineService: Error in incremental sync page ${page}:`, error);
          break;
        }
      }
      
      console.log(`BaselineService: Incremental sync completed. Updated: ${totalSynced}`);
      
      return {
        success: true,
        message: `Successfully updated ${totalSynced} baseline records`,
        count: totalSynced
      };
      
    } catch (error) {
      console.error('BaselineService: Error in incremental sync:', error);
      return {
        success: false,
        message: `Failed to sync updated baselines: ${error.message}`,
        count: 0
      };
    }
  }

  /**
   * Check if baseline data exists
   * 检查是否存在基线数据
   */
  static async hasBaselineData(): Promise<boolean> {
    try {
      const result = await DatabaseManager.getInstance().executeSql('SELECT COUNT(*) as count FROM baseline_records WHERE is_deleted = 0');
      return result.rows.item(0).count > 0;
    } catch (error) {
      console.error('BaselineService: Error checking baseline data existence:', error);
      return false;
    }
  }

  /**
   * Get baseline for specific meter
   * 获取特定水表的基线
   */
  static async getBaselineForMeter(meterId: number): Promise<BaselineRecord | null> {
    try {
      return await BaselineRepository.getLatestActiveBaseline(meterId);
    } catch (error) {
      console.error(`BaselineService: Error getting baseline for meter ${meterId}:`, error);
      return null;
    }
  }

  /**
   * Validate baseline data integrity
   * 验证基线数据完整性
   */
  static async validateBaselineData(): Promise<{isValid: boolean, issues: string[]}> {
    try {
      const issues: string[] = [];
      
      // Check for negative baseline values
      const negativeValueCheck = await DatabaseManager.getInstance().executeSql(`
        SELECT COUNT(*) as count FROM baseline_records
        WHERE baseline_value < 0 AND is_deleted = 0
      `);

      if (negativeValueCheck.rows.item(0).count > 0) {
        issues.push(`Found ${negativeValueCheck.rows.item(0).count} baseline records with negative values`);
      }

      // Check for missing meter references
      const orphanedBaselineCheck = await DatabaseManager.getInstance().executeSql(`
        SELECT COUNT(*) as count FROM baseline_records br
        LEFT JOIN water_meters wm ON br.meter_id = wm.id
        WHERE wm.id IS NULL AND br.is_deleted = 0
      `);
      
      if (orphanedBaselineCheck.rows.item(0).count > 0) {
        issues.push(`Found ${orphanedBaselineCheck.rows.item(0).count} baseline records without valid meter references`);
      }
      
      return {
        isValid: issues.length === 0,
        issues
      };
      
    } catch (error) {
      console.error('BaselineService: Error validating baseline data:', error);
      return {
        isValid: false,
        issues: [`Validation failed: ${error.message}`]
      };
    }
  }

  /**
   * Clear all baseline data (for debugging/reset)
   * 清除所有基线数据（用于调试/重置）
   */
  static async clearAllBaselines(): Promise<void> {
    try {
      await DatabaseManager.getInstance().executeSql('DELETE FROM baseline_records');
      console.log('BaselineService: All baseline data cleared');
    } catch (error) {
      console.error('BaselineService: Error clearing baseline data:', error);
      throw error;
    }
  }
} 