import ImageResizer from 'react-native-image-resizer';
import { Platform } from 'react-native';

export interface ProcessingOptions {
  enhanceContrast?: boolean;
  removeNoise?: boolean;
  sharpenEdges?: boolean;
  detectROI?: boolean;
  resizeForProcessing?: boolean;
}

export interface ProcessingResult {
  processedImageUri: string;
  roiBounds?: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
  processingSteps: string[];
}

class ImageProcessingService {
  private static instance: ImageProcessingService;

  public static getInstance(): ImageProcessingService {
    if (!ImageProcessingService.instance) {
      ImageProcessingService.instance = new ImageProcessingService();
    }
    return ImageProcessingService.instance;
  }

  /**
   * Main preprocessing function for water meter images
   */
  public async preprocessMeterImage(
    imageUri: string,
    options: ProcessingOptions = {}
  ): Promise<ProcessingResult> {
    const {
      resizeForProcessing = true,
    } = options;

    let currentImageUri = imageUri;
    const processingSteps: string[] = [];

    try {
      console.log('Starting image preprocessing for:', imageUri);

      // Step 1: Resize image for optimal OCR processing
      if (resizeForProcessing) {
        currentImageUri = await this.optimizeForOCR(currentImageUri);
        processingSteps.push('Image resized and optimized for text recognition');
      }

      console.log('Image preprocessing completed successfully');
      console.log('Processing steps:', processingSteps);

      return {
        processedImageUri: currentImageUri,
        roiBounds: {
          x: 0,
          y: 0,
          width: 1,
          height: 1,
        },
        processingSteps,
      };
    } catch (error: any) {
      console.error('Error during image preprocessing:', error);
      throw new Error(`Image preprocessing failed: ${error.message}`);
    }
  }

  /**
   * Apply specific preprocessing for mechanical water meters
   */
  public async preprocessMechanicalMeter(imageUri: string): Promise<ProcessingResult> {
    try {
      console.log('Preprocessing mechanical water meter image');

      const processingSteps: string[] = [];

      // Step 1: Optimize image for OCR
      const optimizedUri = await this.optimizeForOCR(imageUri);
      processingSteps.push('Image optimized for mechanical meter digit recognition');

      // Step 2: Create a high-quality version for better text recognition
      const enhancedUri = await this.enhanceImageQuality(optimizedUri);
      processingSteps.push('Image quality enhanced for clearer digit recognition');

      // Step 3: Crop to the central reading display area (more precise ROI)
      const croppedUri = await this.cropToReadingArea(enhancedUri);
      processingSteps.push('Cropped to focus on the main reading display area');

      return {
        processedImageUri: croppedUri,
        roiBounds: {
          x: 0.25,
          y: 0.35,
          width: 0.5,
          height: 0.3,
        },
        processingSteps,
      };
    } catch (error: any) {
      console.error('Error preprocessing mechanical meter:', error);
      throw new Error(`Mechanical meter preprocessing failed: ${error.message}`);
    }
  }

  /**
   * Optimize image for better OCR results
   */
  public async optimizeForOCR(imageUri: string): Promise<string> {
    try {
      console.log('Optimizing image for OCR');

      // Detect input format from URI
      const isPng = imageUri.toLowerCase().includes('.png') || imageUri.toLowerCase().includes('image/png');
      const outputFormat = isPng ? 'PNG' : 'JPEG';
      const quality = isPng ? 100 : 95; // PNG uses 100, JPEG uses 95
      
      console.log(`Input format detected as: ${outputFormat}`);

      // Resize to optimal dimensions for text recognition
      // Text recognition works best with images around 800x600 resolution
      const optimizedImage = await ImageResizer.createResizedImage(
        imageUri,
        800, // Optimal width for OCR
        600, // Optimal height for OCR
        outputFormat,
        quality, // Quality based on format
        0, // rotation
        undefined, // outputPath
        false, // keepMeta
        {
          mode: 'contain', // Maintain aspect ratio
          onlyScaleDown: false,
        }
      );

      console.log('OCR optimization completed');
      return optimizedImage.uri;
    } catch (error) {
      console.error('Error optimizing for OCR:', error);
      return imageUri; // Return original if optimization fails
    }
  }

  /**
   * Enhance image quality using ImageResizer with high quality settings
   */
  private async enhanceImageQuality(imageUri: string): Promise<string> {
    try {
      // Detect input format
      const isPng = imageUri.toLowerCase().includes('.png') || imageUri.toLowerCase().includes('image/png');
      const outputFormat = isPng ? 'PNG' : 'JPEG';
      const quality = isPng ? 100 : 100; // Maximum quality for both
      
      // Re-process with maximum quality for better text recognition
      const enhancedImage = await ImageResizer.createResizedImage(
        imageUri,
        800, // Fixed width for consistency
        600, // Fixed height for consistency
        outputFormat,
        quality, // Maximum quality
        0, // rotation
        undefined, // outputPath
        false, // keepMeta
        {
          mode: 'contain',
          onlyScaleDown: false,
        }
      );
      return enhancedImage.uri;
    } catch (error) {
      console.error('Error enhancing image quality:', error);
      // Return original image if enhancement fails
      return imageUri;
    }
  }

  /**
   * Crop image to focus specifically on the meter reading display area
   * This targets the central circular display where the actual readings are shown
   */
  private async cropToReadingArea(imageUri: string): Promise<string> {
    try {
      console.log('Cropping to meter reading display area');

      // Detect input format
      const isPng = imageUri.toLowerCase().includes('.png') || imageUri.toLowerCase().includes('image/png');
      const outputFormat = isPng ? 'PNG' : 'JPEG';
      const quality = 100; // Maximum quality for text recognition

      // For water meters like KENT, the reading display is typically:
      // - Located in the center of the meter face
      // - Circular or rectangular area with the actual consumption numbers
      // - Usually occupies about 40-60% of the image center area
      // - Positioned below any brand name/model number but above technical specs

      // Crop to center area, focusing on the main reading display
      const croppedImage = await ImageResizer.createResizedImage(
        imageUri,
        400, // Smaller crop focusing on reading area
        300, // Smaller crop focusing on reading area
        outputFormat,
        quality, // Maximum quality for text recognition
        0, // rotation
        undefined, // outputPath
        false, // keepMeta
        {
          mode: 'cover', // This will crop to focus on center area
          onlyScaleDown: false,
        }
      );

      console.log('Reading area crop completed');
      return croppedImage.uri;
    } catch (error) {
      console.error('Error cropping to reading area:', error);
      // Return original image if cropping fails
      return imageUri;
    }
  }

  /**
   * Simple image quality analysis
   */
  public async analyzeImageQuality(imageUri: string): Promise<{
    isGoodQuality: boolean;
    recommendations: string[];
  }> {
    try {
      // Since we can't easily get dimensions with just ImageResizer,
      // we'll provide general recommendations
      const recommendations: string[] = [];
      
      recommendations.push('Make sure the meter reading is clearly visible');
      recommendations.push('Ensure good lighting without shadows or glare');
      recommendations.push('Hold the camera steady and focus on the number display');
      recommendations.push('Take the photo from directly in front of the meter');

      return {
        isGoodQuality: true, // Assume good quality for now
        recommendations,
      };
    } catch (error: any) {
      console.error('Error analyzing image quality:', error);
      return {
        isGoodQuality: false,
        recommendations: ['Unable to analyze image quality'],
      };
    }
  }

  /**
   * Create a processed version optimized for water meter reading
   */
  public async processWaterMeterImage(imageUri: string): Promise<{
    processedImageUri: string;
    processingInfo: string[];
  }> {
    try {
      console.log('Processing water meter image for optimal recognition');

      const processingInfo: string[] = [];

      // Step 1: Resize to optimal size for ML Kit text recognition
      const step1 = await ImageResizer.createResizedImage(
        imageUri,
        1024, // Good resolution for ML Kit
        768,
        'JPEG',
        90,
        0,
        undefined,
        false,
        {
          mode: 'contain',
          onlyScaleDown: true, // Don't upscale small images
        }
      );
      processingInfo.push('Resized to optimal resolution for text recognition');

      // Step 2: Apply high quality compression for better edge definition
      const step2 = await ImageResizer.createResizedImage(
        step1.uri,
        step1.width,
        step1.height,
        'JPEG',
        100, // Maximum quality
        0,
        undefined,
        false,
        {
          mode: 'contain',
          onlyScaleDown: false,
        }
      );
      processingInfo.push('Applied high-quality compression for better text clarity');

      return {
        processedImageUri: step2.uri,
        processingInfo,
      };
    } catch (error: any) {
      console.error('Error processing water meter image:', error);
      throw new Error(`Water meter image processing failed: ${error.message}`);
    }
  }
}

export default ImageProcessingService; 