// User service
// Core business logic for fetching and saving user data
// Aligned with new database schema

import { UserRepository } from '../data/repositories';
import { SyncResult, UserApiData } from '../types/SyncTypes';
import { SyncUserData } from '../database/models';
import api from '../api/BaseApi';

// Backend API response interface - fields are lowercase!
interface BackendUserData {
  id: number;
  username: string;
  fullName?: string;
  personId?: string;
  finCoCode?: string;
  email?: string;
  mobilePhone?: string;
  profitCentreCode?: string;
  employeeNo?: string;
  isAuthenticated?: boolean;
  createdDate?: string;
  updatedDate?: string;
  roles?: Array<{ id: number; name: string }>;
}

export class UserService {
  /**
   * Fetch and save all users from backend API
   * Core business logic - can be called by sync services or UI
   */
  static async fetchAndSaveUsers(): Promise<SyncResult> {
    const operation = 'fetchAndSaveUsers';
    const syncTime = new Date().toISOString();

    try {
      console.log('UserService: Starting user data fetch...');

      // 1. Call backend API to get user data
      const response = await api.get('/user', {
        params: {
          pageSize: 1000, // Get all users in one request
          page: 1
        }
      });

      if (!response.data || !Array.isArray(response.data)) {
        throw new Error('Invalid API response structure');
      }

      const backendUsers: BackendUserData[] = response.data;
      console.log(`UserService: Fetched ${backendUsers.length} users from API`);

      // Debug: Validate data structure
      if (backendUsers.length > 0) {
        console.log('UserService: First user sample keys:', Object.keys(backendUsers[0]));
      }

      // 2. Map backend data to our UserApiData format
      const users: UserApiData[] = backendUsers.map(u => ({
        id: u.id,
        username: u.username,
        fullName: u.fullName || '',
        email: u.email || '',
        phone: u.mobilePhone || '',
        role: u.roles?.[0]?.name || 'user',
        department: u.profitCentreCode || '',
        zone: u.finCoCode || '',
        isActive: u.isAuthenticated,
        createdAt: u.createdDate,
        updatedAt: u.updatedDate
      }));

      // 3. Validate data format
      const validatedUsers = this.validateUserData(users);

      // 4. Batch save to local database using Repository with backend IDs
      const syncUsers = this.mapToSyncUserData(validatedUsers);
      await UserRepository.bulkUpsert(syncUsers);

      console.log(`UserService: Successfully saved ${syncUsers.length} users to local database`);

      return {
        success: true,
        recordCount: syncUsers.length,
        syncTime,
        operation
      };

    } catch (error) {
      console.error('UserService: Error in fetchAndSaveUsers:', error);
      return {
        success: false,
        recordCount: 0,
        errorMessage: error instanceof Error ? error.message : 'Unknown error',
        syncTime,
        operation
      };
    }
  }

  /**
   * Fetch and save specific users by IDs
   */
  static async fetchAndSaveUsersByIds(userIds: number[]): Promise<SyncResult> {
    const operation = 'fetchAndSaveUsersByIds';
    const syncTime = new Date().toISOString();

    try {
      console.log(`UserService: Fetching ${userIds.length} specific users...`);

      // Fetch multiple users by IDs
      const users: UserApiData[] = [];
      
      for (const userId of userIds) {
        try {
          const response = await api.get(`/user/${userId}`);
          if (response.data) {
            users.push(response.data);
          }
        } catch (error) {
          console.warn(`UserService: Failed to fetch user ${userId}:`, error);
        }
      }

      // Validate and save
      const validatedUsers = this.validateUserData(users);
      const syncUsers = this.mapToSyncUserData(validatedUsers);
      await UserRepository.bulkUpsert(syncUsers);

      console.log(`UserService: Successfully saved ${syncUsers.length} specific users`);

      return {
        success: true,
        recordCount: syncUsers.length,
        syncTime,
        operation
      };

    } catch (error) {
      console.error('UserService: Error in fetchAndSaveUsersByIds:', error);
      return {
        success: false,
        recordCount: 0,
        errorMessage: error instanceof Error ? error.message : 'Unknown error',
        syncTime,
        operation
      };
    }
  }

  /**
   * Get user statistics from local database
   */
  static async getUserStatistics(): Promise<{
    totalUsers: number;
    activeUsers: number;
  }> {
    try {
      const totalUsers = await UserRepository.getTotalCount();
      const activeUsers = await UserRepository.getActiveUserCount();

      return {
        totalUsers,
        activeUsers
      };
    } catch (error) {
      console.error('UserService: Error getting user statistics:', error);
      return {
        totalUsers: 0,
        activeUsers: 0
      };
    }
  }

  /**
   * Find user by username (for authentication)
   */
  static async findUserByUsername(username: string) {
    try {
      return await UserRepository.findByUsername(username);
    } catch (error) {
      console.error('UserService: Error finding user by username:', error);
      return null;
    }
  }

  /**
   * Find user by ID
   */
  static async findUserById(id: number) {
    try {
      return await UserRepository.findById(id);
    } catch (error) {
      console.error('UserService: Error finding user by ID:', error);
      return null;
    }
  }

  /**
   * Validate user data integrity
   */
  private static validateUserData(users: UserApiData[]): UserApiData[] {
    return users.filter(user => {
      // Validate required fields
      if (!user.id || !user.username) {
        console.warn('UserService: Skipping user with missing required fields:', user);
        return false;
      }

      // Validate username format
      if (typeof user.username !== 'string' || user.username.trim().length === 0) {
        console.warn('UserService: Skipping user with invalid username:', user);
        return false;
      }

      return true;
    });
  }

  /**
   * Map UserApiData to SyncUserData for Repository operations
   * Ensures backend ID is preserved for database sync
   */
  private static mapToSyncUserData(users: UserApiData[]): SyncUserData[] {
    const currentTime = new Date().toISOString();
    
    return users.map(user => ({
      id: user.id,                                    // Backend ID preserved!
      username: user.username,
      full_name: user.fullName || '',
      email: user.email || '',
      person_id: 0, // Not provided by current API
      fin_co_code: user.zone || '',
      mobile_phone: user.phone || '',
      profit_centre_code: user.department || '',
      employee_no: '', // Not provided by current API
      is_authenticated: user.isActive ? 1 : 0,
      last_login: null, // Not provided by current API
      created_date: user.createdAt || currentTime,
      updated_date: currentTime,
      created_at: user.createdAt || currentTime,
      updated_at: currentTime,
      created_by: 'sync',
      updated_by: 'sync',
      is_deleted: false                               // Not deleted by default
    }));
  }
} 