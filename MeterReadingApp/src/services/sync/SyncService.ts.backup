// services/sync/SyncService.ts
// Business Data Sync Service - only handles scheduling, not business logic
// Uses BusinessDataUploadService for actual upload operations

import { BusinessDataUploadService } from '../business/BusinessDataUploadService';
import { UploadResult } from '../../types/SyncTypes';
import { networkService } from '../../utils/NetworkUtils';

export class BusinessDataSyncService {
  /**
   * Manual upload - called by UI when user clicks upload button
   * Only handles scheduling, delegates to business service
   */
  static async manualUpload(): Promise<UploadResult[]> {
    console.log('BusinessDataSyncService: Manual upload triggered by user');
    
    try {
      return await BusinessDataUploadService.uploadAllPendingData();
    } catch (error) {
      console.error('BusinessDataSyncService: Error in manual upload:', error);
      return [{
        success: false,
        errorMessage: error instanceof Error ? error.message : 'Unknown error',
        uploadTime: new Date().toISOString()
      }];
    }
  }

  /**
   * Auto upload - called when network is detected
   * Only handles scheduling and network check, delegates to business service
   */
  static async autoUpload(): Promise<UploadResult[]> {
    console.log('BusinessDataSyncService: Auto upload triggered by network detection');
    
    try {
      // Check network connectivity before attempting upload
      if (!await networkService.isNetworkConnected()) {
        console.log('BusinessDataSyncService: No network connection, skipping auto upload');
        return [];
      }

      return await BusinessDataUploadService.uploadAllPendingData();
    } catch (error) {
      console.error('BusinessDataSyncService: Error in auto upload:', error);
      return [{
        success: false,
        errorMessage: error instanceof Error ? error.message : 'Unknown error',
        uploadTime: new Date().toISOString()
      }];
    }
  }

      // Update local reading status to 'synced'
      await db.executeSql(
        'UPDATE meter_readings SET sync_status = ?, updated_at = ? WHERE id = ?',
        ['synced', new Date().toISOString(), localReadingId]
      );

      // Update sync log status
      if (syncLog.sync_log_id) {
        await SyncLog.update(syncLog.sync_log_id, {
          server_reading_id: serverReadingId,
          sync_status: 'synced',
          last_sync_time: new Date().toISOString(),
          sync_errors: null // Clear any previous errors
        });
      }

    } catch (error) {
      console.error('MeterReadingSyncService: Error syncing reading to server:', error);
      await this.handleSyncError(localReadingId, error);
    }
  }

  // Handle sync errors (following CORDE pattern)
  private static async handleSyncError(localReadingId: number, error: any): Promise<void> {
    try {
      const syncLog = await SyncLog.getByLocalReadingId(localReadingId);
      if (syncLog?.sync_log_id) {
        await SyncLog.update(syncLog.sync_log_id, {
          sync_status: 'error',
          sync_errors: error.message || 'Unknown sync error',
          last_update_time: new Date().toISOString()
        });
        
        await SyncLog.incrementRetryCount(syncLog.sync_log_id);
      }
    } catch (updateError) {
      console.error('MeterReadingSyncService: Error updating sync error status:', updateError);
    }
  }

  // Sync all pending readings (following CORDE syncOpenLogs pattern)
  static async syncPendingReadings(): Promise<void> {
    console.log('MeterReadingSyncService: Starting syncPendingReadings');
    
    if (!networkService.isNetworkConnectedSync()) {
      console.log('MeterReadingSyncService: No network connection. Skipping syncPendingReadings.');
      return;
    }

    const pendingLogs = await SyncLog.getPendingSyncLogs();

    for (const syncLog of pendingLogs) {
      try {
        await this.syncPendingReadingToServer(syncLog.local_reading_id);
      } catch (error) {
        console.error(`MeterReadingSyncService: Failed to sync reading ${syncLog.local_reading_id}:`, error);
      }
    }
  }

  // Retry failed syncs (following CORDE pattern)
  static async retryFailedSyncs(): Promise<void> {
    
    if (!networkService.isNetworkConnectedSync()) {
      console.log('MeterReadingSyncService: No network connection. Skipping retryFailedSyncs.');
      return;
    }

    const errorLogs = await SyncLog.getErrorSyncLogs();

    for (const syncLog of errorLogs) {
      try {
        await this.syncPendingReadingToServer(syncLog.local_reading_id);
      } catch (error) {
        console.error(`MeterReadingSyncService: Failed to retry sync reading ${syncLog.local_reading_id}:`, error);
      }
    }
  }

  // Get sync statistics
  static async getSyncStatistics(): Promise<{
    pending: number;
    syncing: number;
    synced: number;
    error: number;
    total: number;
  }> {
    return await SyncLog.getSyncStatistics();
  }
} 