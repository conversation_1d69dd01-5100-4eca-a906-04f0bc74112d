// Business Data Sync Service - Scheduling Layer
// Only handles scheduling and coordination, delegates all business logic to DataUploadService
// Follows the principle: Sync services schedule, Business services implement

import { DataUploadService } from '../DataUploadService';
import { UploadResult } from '../../types/SyncTypes';
import { networkService } from '../../utils/NetworkUtils';

export class BusinessDataSyncService {
  /**
   * Manual upload trigger - immediate execution
   * Called from UI when user manually triggers upload
   */
  static async uploadNow(): Promise<UploadResult[]> {
    try {
      console.log('BusinessDataSyncService: Manual upload triggered');
      return await DataUploadService.uploadAllPendingData();
    } catch (error) {
      console.error('BusinessDataSyncService: Manual upload failed:', error);
      return [{
        success: false,
        type: 'error',
        message: error instanceof Error ? error.message : 'Manual upload failed',
        timestamp: new Date().toISOString()
      }];
    }
  }

  /**
   * Interval-based background upload
   * Called periodically when app is active
   */
  static async backgroundUpload(): Promise<UploadResult[]> {
    try {
      // Check network connectivity first
      const isConnected = await networkService.isNetworkConnected();
      if (!isConnected) {
        console.log('BusinessDataSyncService: Skipping background upload - no network');
        return [{
          success: false,
          type: 'network',
          message: 'No network connection available',
          timestamp: new Date().toISOString()
        }];
      }

      console.log('BusinessDataSyncService: Background upload started');
      return await DataUploadService.uploadAllPendingData();
    } catch (error) {
      console.error('BusinessDataSyncService: Background upload failed:', error);
      return [{
        success: false,
        type: 'error',
        message: error instanceof Error ? error.message : 'Background upload failed',
        timestamp: new Date().toISOString()
      }];
    }
  }

  /**
   * Network-aware upload when connectivity is restored
   * Called when network connection is detected after being offline
   */
  static async onNetworkReconnected(): Promise<UploadResult[]> {
    try {
      console.log('BusinessDataSyncService: Network reconnected, starting upload');
      return await DataUploadService.uploadAllPendingData();
    } catch (error) {
      console.error('BusinessDataSyncService: Network reconnection upload failed:', error);
      return [{
        success: false,
        type: 'error',
        message: error instanceof Error ? error.message : 'Reconnection upload failed',
        timestamp: new Date().toISOString()
      }];
    }
  }

  /**
   * Get upload summary for UI display
   * Pure data retrieval - no upload logic
   */
  static async getUploadSummary(): Promise<any> {
    try {
      return await DataUploadService.getUploadSummary();
    } catch (error) {
      console.error('BusinessDataSyncService: Error getting upload summary:', error);
      return {
        pendingReadings: 0,
        pendingPhotos: 0,
        pendingTasks: 0
      };
    }
  }
} 