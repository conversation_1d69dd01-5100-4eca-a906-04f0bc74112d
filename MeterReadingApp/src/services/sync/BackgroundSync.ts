// services/sync/BackgroundSync.ts
// Enhanced Background sync service for meter readings
// Implements intelligent retry, network detection, priority management
// Based on CORDE-Mobile-Application patterns with improvements

import BackgroundTimer from 'react-native-background-timer';
import { BusinessDataSyncService } from './BusinessDataSyncService';
import { BasicDataSyncService } from './BasicDataSyncService';
import { TaskDataSyncService } from './TaskDataSyncService';
import { networkService } from '../../utils/NetworkUtils';
import { UploadResult } from '../../types/SyncTypes';

// Sync task priorities
type SyncPriority = 'critical' | 'high' | 'normal' | 'low';

// Network quality levels
type NetworkQuality = 'excellent' | 'good' | 'poor' | 'offline';

interface EnhancedSyncTask {
  name: string;
  syncFunction: () => Promise<UploadResult[]>;
  interval: number; // in milliseconds
  priority: SyncPriority;
  maxRetries: number;
  backoffMultiplier: number;
  requiresGoodNetwork: boolean;
  lastRun?: number;
  retryCount?: number;
  lastError?: string;
  consecutiveFailures?: number;
}

interface SyncStatistics {
  totalRuns: number;
  successfulRuns: number;
  failedRuns: number;
  averageUploadTime: number;
  lastSuccessfulSync?: number;
  networkQuality: NetworkQuality;
}

class BackgroundSyncManager {
  private isInitialized = false;
  private backgroundTimer: number | null = null;
  private isRunning = false;
  private currentSyncTask: string | null = null;
  private statistics: SyncStatistics = {
    totalRuns: 0,
    successfulRuns: 0,
    failedRuns: 0,
    averageUploadTime: 0,
    networkQuality: 'offline'
  };
  
  // Define enhanced sync tasks with priority and intelligence
  private syncTasks: EnhancedSyncTask[] = [
    {
      name: 'MeterReadingUpload',
      syncFunction: async () => await BusinessDataSyncService.backgroundUpload(),
      interval: 3 * 60 * 1000,
      priority: 'critical',
      maxRetries: 5,
      backoffMultiplier: 1.5,
      requiresGoodNetwork: false,
      retryCount: 0,
      consecutiveFailures: 0
    }
  ];

  async initialize(): Promise<void> {
    if (this.isInitialized) return;
    this.isInitialized = true;

    await this.startNetworkMonitoring();

    console.log('BackgroundSyncManager: Enhanced background sync service initialized');
  }

  private async startNetworkMonitoring(): Promise<void> {
    const initialNetworkStatus = await networkService.isNetworkConnected();
    this.statistics.networkQuality = initialNetworkStatus ? 'good' : 'offline';
    console.log(`BackgroundSyncManager: Initial network quality: ${this.statistics.networkQuality}`);

    networkService.addListener((isConnected) => {
      this.statistics.networkQuality = isConnected ? 'good' : 'offline';
      console.log(`BackgroundSyncManager: Network quality updated to: ${this.statistics.networkQuality}`);

      if (isConnected) {
        console.log('BackgroundSyncManager: Network available, triggering immediate sync check');
        this.executeSyncTasks();
      }
    });
  }



  async startBackgroundSync(): Promise<void> {
    await this.initialize();
    console.log('BackgroundSyncManager: Starting enhanced background sync');

    if (this.backgroundTimer !== null) {
      BackgroundTimer.clearInterval(this.backgroundTimer);
    }

    // Use adaptive interval based on network quality and task priorities
    const baseInterval = this.getAdaptiveInterval();
    
    this.backgroundTimer = BackgroundTimer.setInterval(() => {
      this.executeSyncTasks();
    }, baseInterval);

    // Initial execution with slight delay
    setTimeout(() => {
      this.executeSyncTasks();
    }, 5000);
  }

  stopBackgroundSync(): void {
    console.log('BackgroundSyncManager: Stopping enhanced background sync');
    
    if (this.backgroundTimer !== null) {
      BackgroundTimer.clearInterval(this.backgroundTimer);
      this.backgroundTimer = null;
    }

    networkService.removeListener('backgroundSync');
    this.isRunning = false;
    this.currentSyncTask = null;
  }

  private getAdaptiveInterval(): number {
    // Adapt interval based on network quality and recent success rate
    const successRate = this.statistics.totalRuns > 0 
      ? this.statistics.successfulRuns / this.statistics.totalRuns 
      : 0;

    let baseInterval = 60000; // 1 minute base

    switch (this.statistics.networkQuality) {
      case 'good':
        baseInterval = 60000;
        break;
      case 'offline':
        baseInterval = 300000;
        break;
      default:
        baseInterval = 60000;
        break;
    }

    // Adjust based on success rate
    if (successRate < 0.5) {
      baseInterval *= 2; // Double interval if success rate is low
    } else if (successRate > 0.9) {
      baseInterval *= 0.8; // Reduce interval if success rate is high
    }

    return Math.max(baseInterval, 30000); // Minimum 30 seconds
  }

  private async executeSyncTasks(): Promise<void> {
    if (this.isRunning) {
      console.log('BackgroundSyncManager: Sync already running, skipping');
      return;
    }

    this.isRunning = true;
    const now = Date.now();
    
    try {
      // Sort tasks by priority (critical first)
      const priorityOrder: SyncPriority[] = ['critical', 'high', 'normal', 'low'];
      const sortedTasks = this.syncTasks.sort((a, b) => {
        return priorityOrder.indexOf(a.priority) - priorityOrder.indexOf(b.priority);
      });

      for (const task of sortedTasks) {
        // Skip if not enough time has passed and not a retry
        if (task.lastRun && task.retryCount === 0 && (now - task.lastRun) < task.interval) {
          continue;
        }

        // Skip if network quality doesn't meet requirements
        if (task.requiresGoodNetwork && this.isNetworkQualityPoor()) {
          console.log(`BackgroundSyncManager: Skipping ${task.name} due to poor network quality`);
          continue;
        }

        // Skip if too many consecutive failures
        if ((task.consecutiveFailures || 0) >= task.maxRetries) {
          // Reset after extended backoff period
          const backoffPeriod = task.interval * Math.pow(task.backoffMultiplier, task.maxRetries);
          if (task.lastRun && (now - task.lastRun) > backoffPeriod) {
            task.consecutiveFailures = 0;
            task.retryCount = 0;
            console.log(`BackgroundSyncManager: Resetting ${task.name} after extended backoff`);
          } else {
            continue;
          }
        }

        await this.executeTask(task);
      }
    } catch (error) {
      console.error('BackgroundSyncManager: Error in executeSyncTasks:', error);
    } finally {
      this.isRunning = false;
      this.currentSyncTask = null;
    }
  }

  private async executeTask(task: EnhancedSyncTask): Promise<void> {
    const startTime = Date.now();
    this.currentSyncTask = task.name;
    
    try {
      console.log(`BackgroundSyncManager: Executing task: ${task.name} (attempt ${(task.retryCount || 0) + 1}/${task.maxRetries + 1})`);
      
      const results = await task.syncFunction();
      const successCount = results.filter(r => r.success).length;
      const totalCount = results.length;

      // Update task status
      task.lastRun = Date.now();
      task.lastError = undefined;
      
      if (successCount === totalCount && totalCount > 0) {
        // Complete success
        task.retryCount = 0;
        task.consecutiveFailures = 0;
        this.updateStatistics(true, Date.now() - startTime);
        console.log(`BackgroundSyncManager: Task ${task.name} completed successfully (${successCount}/${totalCount})`);
      } else if (successCount > 0) {
        // Partial success
        task.retryCount = 0;
        task.consecutiveFailures = 0;
        this.updateStatistics(true, Date.now() - startTime);
        console.log(`BackgroundSyncManager: Task ${task.name} partially completed (${successCount}/${totalCount})`);
      } else {
        // Complete failure
        throw new Error(`All ${totalCount} operations failed`);
      }

    } catch (error: any) {
      const errorMessage = error.message || 'Unknown error';
      console.error(`BackgroundSyncManager: Task ${task.name} failed:`, errorMessage);
      
      task.lastError = errorMessage;
      task.retryCount = (task.retryCount || 0) + 1;
      task.consecutiveFailures = (task.consecutiveFailures || 0) + 1;
      
      this.updateStatistics(false, Date.now() - startTime);
      
      // Schedule retry with exponential backoff
      if (task.retryCount < task.maxRetries) {
        const retryDelay = task.interval * Math.pow(task.backoffMultiplier, task.retryCount);
        console.log(`BackgroundSyncManager: Scheduling retry for ${task.name} in ${retryDelay}ms`);
        
        setTimeout(() => {
          this.executeSingleTask(task);
        }, retryDelay);
      }
    }
  }

  private async executeSingleTask(task: EnhancedSyncTask): Promise<void> {
    if (!this.isRunning) {
      await this.executeTask(task);
    }
  }

  private isNetworkQualityPoor(): boolean {
    return this.statistics.networkQuality === 'offline';
  }

  private updateStatistics(success: boolean, executionTime: number): void {
    this.statistics.totalRuns++;
    
    if (success) {
      this.statistics.successfulRuns++;
      this.statistics.lastSuccessfulSync = Date.now();
      
      // Update average execution time (exponential moving average)
      if (this.statistics.averageUploadTime === 0) {
        this.statistics.averageUploadTime = executionTime;
      } else {
        this.statistics.averageUploadTime = (this.statistics.averageUploadTime * 0.8) + (executionTime * 0.2);
      }
    } else {
      this.statistics.failedRuns++;
    }
  }

  // Public methods for monitoring and control
  
  public getStatistics(): SyncStatistics {
    return { ...this.statistics };
  }

  public getCurrentTask(): string | null {
    return this.currentSyncTask;
  }

  public getTaskStatus(): Array<{name: string, lastRun?: number, retryCount?: number, consecutiveFailures?: number, lastError?: string}> {
    return this.syncTasks.map(task => ({
      name: task.name,
      lastRun: task.lastRun,
      retryCount: task.retryCount,
      consecutiveFailures: task.consecutiveFailures,
      lastError: task.lastError
    }));
  }

  public async forceSync(taskName?: string): Promise<void> {
    if (taskName) {
      const task = this.syncTasks.find(t => t.name === taskName);
      if (task) {
        console.log(`BackgroundSyncManager: Force executing task: ${taskName}`);
        await this.executeTask(task);
      }
    } else {
      console.log('BackgroundSyncManager: Force executing all tasks');
      await this.executeSyncTasks();
    }
  }

  public adjustTaskPriority(taskName: string, newPriority: SyncPriority): void {
    const task = this.syncTasks.find(t => t.name === taskName);
    if (task) {
      task.priority = newPriority;
      console.log(`BackgroundSyncManager: Updated ${taskName} priority to ${newPriority}`);
    }
  }
}

// Export singleton instance
export const MeterReadingBackgroundSync = new BackgroundSyncManager(); 