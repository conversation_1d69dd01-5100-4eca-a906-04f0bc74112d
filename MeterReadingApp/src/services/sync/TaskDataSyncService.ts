// Task Data Sync Service - Scheduling Layer
// Only handles scheduling and coordination, delegates all business logic to TaskService
// Manages synchronization of task data following CORDE LogListService patterns

import { TaskService } from '../';
import { SyncResult } from '../../types/SyncTypes';
import { Task } from '../../database/models';

export class TaskDataSyncService {
  /**
   * Sync tasks - delegates to business service
   */
  static async syncTasks(pages: number = 10): Promise<SyncResult> {
    console.log(`TaskDataSyncService: Syncing tasks for ${pages} pages...`);
    return await TaskService.fetchAndSaveTasks(pages);
  }

  /**
   * Sync tasks by date range - delegates to business service
   */
  static async syncTasksByDateRange(startDate: string, endDate: string, pages: number = 10): Promise<SyncResult> {
    console.log(`TaskDataSyncService: Syncing tasks for date range ${startDate} to ${endDate}...`);
    return await TaskService.fetchAndSaveTasks(pages);
  }

  /**
   * Get user tasks (for UI display) - delegates to business service
   */
  static async getUserTasks(userId: number): Promise<Task[]> {
    console.log(`TaskDataSyncService: Getting tasks for user ${userId}...`);
    return await TaskService.getUserTasks(userId);
  }

  /**
   * Get task statistics - delegates to business service
   */
  static async getTaskStatistics(userId?: number) {
    try {
      if (userId) {
        return await TaskService.getTaskStatistics(userId);
      } else {
        return await TaskService.getTaskStatistics();
      }
    } catch (error) {
      console.error('TaskDataSyncService: Error getting task statistics:', error);
      return {
        assignedTasks: 0,
        inProgressTasks: 0,
        completedTasks: 0,
        totalTasks: 0
      };
    }
  }
} 