// Basic Data Sync Service - Scheduling Layer
// Only handles scheduling and coordination, delegates all business logic to Business services
// Manages synchronization of static data: water meters, users, areas

import { WaterMeterService, UserService } from '../';
import { BaselineService } from '../BaselineService';
import { SyncResult } from '../../types/SyncTypes';

export class BasicDataSyncService {
  /**
   * Sync water meters - delegates to business service
   */
  static async syncWaterMeters(): Promise<SyncResult> {
    console.log('BasicDataSyncService: Syncing water meters...');
    return await WaterMeterService.fetchAndSaveWaterMeters();
  }

  /**
   * Sync users - delegates to business service
   */
  static async syncUsers(): Promise<SyncResult> {
    console.log('BasicDataSyncService: Syncing users...');
    return await UserService.fetchAndSaveUsers();
  }

  /**
   * Sync baselines - delegates to baseline service
   */
  static async syncBaselines(): Promise<SyncResult> {
    console.log('BasicDataSyncService: Syncing baselines...');
    const result = await BaselineService.fetchAndSaveBaselines();
    
    return {
      success: result.success,
      recordCount: result.count,
      errorMessage: result.success ? undefined : result.message,
      syncTime: new Date().toISOString(),
      operation: 'syncBaselines'
    };
  }

  /**
   * Sync all basic data - coordinates multiple business services
   */
  static async syncAllBasicData(): Promise<SyncResult[]> {
    console.log('BasicDataSyncService: Starting sync of all basic data...');

    try {
      const results: SyncResult[] = [];

      // Step 1: Sync users first (no dependencies)
      console.log('BasicDataSyncService: Step 1 - Syncing users...');
      const usersResult = await this.syncUsers();
      results.push(usersResult);

      // Step 2: Sync water meters (depends on users)
      console.log('BasicDataSyncService: Step 2 - Syncing water meters...');
      const waterMetersResult = await this.syncWaterMeters();
      results.push(waterMetersResult);

      // Step 3: Sync baselines (depends on water meters)
      console.log('BasicDataSyncService: Step 3 - Syncing baselines...');
      const baselinesResult = await this.syncBaselines();
      results.push(baselinesResult);

      const successCount = results.filter(r => r.success).length;
      console.log(`BasicDataSyncService: Completed basic data sync. ${successCount}/${results.length} operations successful`);

      return results;
    } catch (error) {
      console.error('BasicDataSyncService: Error in syncAllBasicData:', error);

      return [{
        success: false,
        recordCount: 0,
        errorMessage: error instanceof Error ? error.message : 'Unknown error',
        syncTime: new Date().toISOString(),
        operation: 'syncAllBasicData'
      }];
    }
  }

  /**
   * Get basic data statistics - delegates to business services
   */
  static async getBasicDataStatistics() {
    try {
      const [waterMeterStats, userStats, baselineStats] = await Promise.all([
        WaterMeterService.getWaterMeterStatistics(),
        UserService.getUserStatistics(),
        BaselineService.getBaselineStatistics()
      ]);

      return {
        waterMeters: waterMeterStats,
        users: userStats,
        baselines: baselineStats
      };
    } catch (error) {
      console.error('BasicDataSyncService: Error getting statistics:', error);
      return {
        waterMeters: { totalMeters: 0, activeMeters: 0, inactiveMeters: 0, maintenanceMeters: 0 },
        users: { totalUsers: 0, activeUsers: 0, inactiveUsers: 0, adminUsers: 0, readerUsers: 0, supervisorUsers: 0 },
        baselines: { total: 0, active: 0, validated: 0, anomalous: 0, corrected: 0, recent: 0 }
      };
    }
  }
} 