// Removed: import { login } from '../api/AccountApi'; // No longer needed - using unified backend auth
import { User } from '../types/User';
import { User as DBUser, SyncUserData, CreateUserRequest } from '../database/models/User';
import { UserRepository } from '../data/repositories/UserRepository';
import * as Keychain from 'react-native-keychain';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { hashPasswordSync, comparePasswordSync } from '../utils/passwordHash';
import api from '../api/BaseApi';

const USER_INFO_KEY = 'MeterApp_UserInfo';
const USER_CREDENTIALS_KEY = 'MeterApp_Credentials';
const REMEMBER_ME_KEY = 'MeterApp_RememberMe';
const LAST_LOGIN_USER_ID = 'MeterApp_LastLoginUserId';

class AuthService {
  
  static async login(username: string, password: string, rememberMe: boolean = false): Promise<User> {
    try {
      // Try local authentication first
      const localUser = await this.localAuthenticate(username, password);
      if (localUser) {
        console.log('Local authentication successful for user:', username);
        
        // If user chooses remember me, clear other users' remember me settings
        if (rememberMe) {
          await this.clearOtherRememberMe(localUser.id!);
        }

        // Record last login user ID
        await AsyncStorage.setItem(LAST_LOGIN_USER_ID, localUser.id!.toString());
        await this.updateRememberMeStatus(rememberMe);
        
        // Convert DB user to API user format
        const user: User = {
          id: localUser.id!.toString(),
          username: localUser.username,
          fullName: localUser.full_name || '',
          personId: parseInt(localUser.id!.toString()), // Use DB ID as person ID for local users
          finCoCode: 'LOCAL',
          isAuthenticated: true,
          lastLogin: new Date().toISOString(),
        };

        // Store user info and credentials
        await this.storeCredentials(user, password, rememberMe);
        return user;
      }

      // API authentication
      console.log('Attempting API authentication for user:', username);
      const apiUser = await this.apiAuthenticate(username, password, rememberMe);
      return apiUser;
      
    } catch (error: any) {
      console.error('Login error:', error);
      throw error;
    }
  }

  static async logout(username?: string): Promise<void> {
    try {
      console.log('Logging out user:', username);
      
      // Get current user if username not provided
      if (!username) {
        const currentUser = await this.getCurrentUser();
        if (currentUser) {
          username = currentUser.username;
        }
      }

      // Update user authentication status in database
      if (username) {
        const user = await UserRepository.getByUsername(username);
        if (user) {
          await UserRepository.setAuthenticated(user.id!, false);
        }
      }
      
      // Clear all stored data
      await Keychain.resetGenericPassword({ service: USER_CREDENTIALS_KEY });
      await Keychain.resetGenericPassword({ service: USER_INFO_KEY });
      if (username) {
        await Keychain.resetGenericPassword({ service: `${REMEMBER_ME_KEY}_${username}` });
      }
      
      // Clear remember me status and last login user
      await AsyncStorage.removeItem(LAST_LOGIN_USER_ID);
      await this.updateRememberMeStatus(false);
      
      console.log('Logout completed');
    } catch (error) {
      console.error('Error during logout:', error);
    }
  }

  static async getCurrentUser(): Promise<User | null> {
    try {
      // Check if this is first launch
      const isFirstLaunch = await AsyncStorage.getItem('firstLaunch') === null;
      if (isFirstLaunch) {
        // First launch, clear all user states
        await UserRepository.clearAllAuthentication();
        await AsyncStorage.setItem('firstLaunch', 'done');
        return null;
      }

      // Get last login user ID
      const lastLoginUserId = await AsyncStorage.getItem(LAST_LOGIN_USER_ID);
      if (!lastLoginUserId) return null;

      // Get user from database
      const dbUser = await UserRepository.getById(parseInt(lastLoginUserId));
      if (dbUser && dbUser.is_active) {
        // Convert DB user to API user format
        const user: User = {
          id: dbUser.id!.toString(),
          username: dbUser.username,
          fullName: dbUser.full_name || '',
          personId: dbUser.id!, // Use DB ID as person ID for local users
          finCoCode: 'LOCAL',
          isAuthenticated: true,
          lastLogin: dbUser.last_login_date,
        };
        return user;
      }

      return null;
    } catch (error) {
      console.error('Error in getCurrentUser:', error);
      return null;
    }
  }

  static async getStoredCredentials(): Promise<{ username: string; password: string } | null> {
    try {
      const result = await Keychain.getGenericPassword({ service: USER_CREDENTIALS_KEY });
      if (result) {
        return JSON.parse(result.password);
      }
      return null;
    } catch (error) {
      console.error('Error retrieving stored credentials:', error);
      return null;
    }
  }

  static async isRememberMeEnabled(): Promise<boolean> {
    try {
      const rememberMe = await AsyncStorage.getItem(REMEMBER_ME_KEY);
      return rememberMe === 'true';
    } catch (error) {
      console.error('Error checking remember me status:', error);
      return false;
    }
  }

  static async autoLogin(): Promise<User | null> {
    try {
      const isRememberMe = await this.isRememberMeEnabled();
      console.log('Auto login - Remember Me enabled:', isRememberMe);
      
      if (isRememberMe) {
        const storedCredentials = await this.getStoredCredentials();
        const userInfo = await this.getStoredUserInfo();
        console.log('Auto login - Stored credentials:', storedCredentials);
        console.log('Auto login - User info:', userInfo);
        
        if (storedCredentials && userInfo && storedCredentials.username === userInfo.username) {
          const user = await this.login(storedCredentials.username, storedCredentials.password, true);
          if (user) {
            if (userInfo.personId !== undefined) user.personId = userInfo.personId;
            if (userInfo.fullName !== undefined) user.fullName = userInfo.fullName;
            if (userInfo.finCoCode !== undefined) user.finCoCode = userInfo.finCoCode;
            console.log('Auto login successful for user:', user.username);
          }
          return user;
        }
      }
    } catch (error) {
      console.error('Auto login error:', error);
    }
    console.log('Auto login failed');
    return null;
  }

  // Get stored user info
  static async getStoredUserInfo(): Promise<Partial<User> | null> {
    try {
      const result = await Keychain.getGenericPassword({ service: USER_INFO_KEY });
      if (result) {
        return JSON.parse(result.password);
      }
      return null;
    } catch (error) {
      console.error('Error retrieving stored user info:', error);
      return null;
    }
  }

  // Create local user (for testing or admin purposes)
  static async createLocalUser(username: string, password: string, fullName: string, role: string = 'reader'): Promise<DBUser> {
    try {
      // Check if user already exists
      const existingUser = await UserRepository.getByUsername(username);
      if (existingUser) {
        throw new Error('User already exists');
      }

      // Hash the password
      const saltRounds = 10;
      const hashedPassword = hashPasswordSync(password, saltRounds);

      // Create user object for local creation (without backend ID)
      const createUserRequest: CreateUserRequest = {
        username,
        full_name: fullName,
        email: '',
        is_authenticated: false
      };

      // Save to database using create method (for local users)
      const savedUser = await UserRepository.create(createUserRequest);
      
      // Update password hash separately
      await UserRepository.update(savedUser.id!, { password_hash: hashedPassword });
      console.log('Local user created successfully:', username);
      return savedUser;
    } catch (error) {
      console.error('Error creating local user:', error);
      throw error;
    }
  }

  // Update user password
  static async updateUserPassword(username: string, oldPassword: string, newPassword: string): Promise<void> {
    try {
      // Authenticate with old password first
      const user = await this.localAuthenticate(username, oldPassword);
      if (!user) {
        throw new Error('Invalid current password');
      }

      // Hash new password
      const saltRounds = 10;
      const hashedPassword = bcrypt.hashSync(newPassword, saltRounds);

      // Update password in database
      await UserRepository.update(user.id!, { password_hash: hashedPassword });
      console.log('Password updated successfully for user:', username);
    } catch (error) {
      console.error('Error updating password:', error);
      throw error;
    }
  }

  // Migrate existing plain text passwords to hashed passwords
  static async migratePasswordsToHash(): Promise<void> {
    try {
      const users = await UserRepository.getAll();
      console.log('Starting password migration...');
      
      for (const user of users) {
        // Check if password is already hashed (bcrypt hashes start with $2a$, $2b$, or $2y$)
        if (user.password_hash && !user.password_hash.startsWith('$2')) {
          console.log(`Migrating password for user: ${user.username}`);
          
          // Hash the plain text password
          const saltRounds = 10;
          const hashedPassword = bcrypt.hashSync(user.password_hash, saltRounds);
          
          // Update the user with hashed password
          await UserRepository.update(user.id!, { password_hash: hashedPassword });
          console.log(`Password migrated for user: ${user.username}`);
        }
      }
      
      console.log('Password migration completed');
    } catch (error) {
      console.error('Error during password migration:', error);
    }
  }

  // Debug method to show all users
  static async debugAllUsers(): Promise<void> {
    try {
      const users = await UserRepository.getAll();
      console.log('All users in database:');
      users.forEach(user => {
        console.log(`Username: ${user.username}, ID: ${user.id}, Active: ${user.is_active}, Last Login: ${user.last_login_date}, Hash: ${user.password_hash?.substring(0, 10)}...`);
      });
    } catch (error) {
      console.error('Error getting all users:', error);
    }
  }

  static async isAuthenticated(): Promise<boolean> {
    const user = await this.getCurrentUser();
    return user ? user.isAuthenticated : false;
  }

  // Local authentication method
  static async localAuthenticate(username: string, password: string): Promise<DBUser | null> {
    try {
      // First check if user exists in local database
      const user = await UserRepository.getByUsername(username);
      if (user && user.is_active && user.password_hash) {
        // Compare password with stored hash
        const isPasswordValid = comparePasswordSync(password, user.password_hash);
        if (isPasswordValid) {
          // Update user authentication status
          await UserRepository.setAuthenticated(user.id!, true);
          console.log('Local authentication successful for user:', username);
          return user;
        }
      }
      
      // Fallback: check stored credentials for backward compatibility
      const storedCredentials = await this.getStoredCredentials();
      if (storedCredentials && storedCredentials.username === username && storedCredentials.password === password) {
        if (user && user.is_active) {
          // Update user authentication status
          await UserRepository.setAuthenticated(user.id!, true);
          console.log('Local authentication successful (fallback) for user:', username);
          return user;
        }
      }
      
      return null;
    } catch (error) {
      console.error('Error during local authentication:', error);
      throw error;
    }
  }

  // API authentication method - Unified backend authentication
  static async apiAuthenticate(username: string, password: string, rememberMe: boolean): Promise<User> {
    try {
      console.log('🔐 Attempting unified backend authentication for user:', username);
      
      // Call backend auth API directly - it handles both admin and regular users
      const jwtResponse = await api.post('/auth/login', {
        username,
        password,
        rememberMe: true
      });
      
      if (jwtResponse.data.success && jwtResponse.data.token && jwtResponse.data.user) {
        console.log('✅ Backend authentication successful');
        
        // Set JWT token
        await api.setJwtToken(jwtResponse.data.token);
        
        // Create user object from backend response
        const backendUser = jwtResponse.data.user;
        const user: User = {
          id: backendUser.id.toString(),
          username: backendUser.username,
          fullName: backendUser.fullName,
          personId: backendUser.personId,
          finCoCode: backendUser.finCoCode,
          isAuthenticated: true,
          lastLogin: new Date().toISOString(),
        };

        // Store user in local database with backend ID
        const saltRounds = 10;
        const hashedPassword = hashPasswordSync(password, saltRounds);
        const syncUserData: SyncUserData = {
          id: backendUser.id,                           // Use backend ID
          username,
          full_name: backendUser.fullName || '',
          email: backendUser.email || '',
          person_id: backendUser.personId || 0,
          fin_co_code: backendUser.finCoCode || '',
          mobile_phone: backendUser.mobilePhone || '',
          profit_centre_code: backendUser.profitCentreCode || '',
          employee_no: backendUser.employeeNo || '',
          is_authenticated: 1,
          last_login: new Date().toISOString(),
          created_date: new Date().toISOString(),
          updated_date: new Date().toISOString(),
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          created_by: 'auth_service',
          updated_by: 'auth_service',
          is_deleted: false
        };

        const savedUser = await UserRepository.upsert(syncUserData);
        
        // Update password hash separately (since it's not in sync data)
        await UserRepository.update(savedUser.id!, { password_hash: hashedPassword });
        
        // Store credentials and user info
        await this.storeCredentials(user, password, rememberMe);
        await this.updateRememberMeStatus(rememberMe);
        
        // Save last login user ID
        await AsyncStorage.setItem(LAST_LOGIN_USER_ID, savedUser.id!.toString());

        return user;
      } else {
        throw new Error(jwtResponse.data.message || 'Authentication failed');
      }
    } catch (error: any) {
      console.error('API authentication error:', error);
      
      // Check if it's a network error or invalid credentials
      if (error.response?.status === 401) {
        throw new Error('Invalid username or password');
      } else if (error.code === 'NETWORK_ERROR' || !error.response) {
        throw new Error('Network error. Please check your connection.');
      } else {
        throw new Error('Login failed. Please try again.');
      }
    }
  }

  // Clear other users' remember me settings
  static async clearOtherRememberMe(currentUserId: number): Promise<void> {
    try {
      // Clear all users' remember me flags except current user
      const users = await UserRepository.getAll();
      for (const user of users) {
        if (user.id !== currentUserId) {
          await Keychain.resetGenericPassword({
            service: `${REMEMBER_ME_KEY}_${user.username}`
          });
        }
      }
    } catch (error) {
      console.error('Error clearing other remember me settings:', error);
    }
  }

  // Update remember me status
  static async updateRememberMeStatus(rememberMe: boolean): Promise<void> {
    try {
      await Keychain.setGenericPassword(REMEMBER_ME_KEY, rememberMe ? 'true' : 'false', { service: REMEMBER_ME_KEY });
      console.log('Remember Me status updated:', rememberMe);
    } catch (error) {
      console.error('Error updating Remember Me status:', error);
      throw error;
    }
  }

  private static async storeUserInfo(user: User): Promise<void> {
    try {
      await Keychain.setGenericPassword(
        USER_INFO_KEY,
        JSON.stringify(user),
        { service: USER_INFO_KEY }
      );
      console.log('User info stored successfully');
    } catch (error) {
      console.error('Error storing user info:', error);
      throw error;
    }
  }

  private static async storeCredentials(user: User, password: string, rememberMe: boolean): Promise<void> {
    console.log('Storing credentials for user:', user.username);

    const userInfo = JSON.stringify({
      username: user.username,
      personId: user.personId,
      fullName: user.fullName,
      finCoCode: user.finCoCode,
    });

    try {
      // Store user credentials
      await Keychain.setGenericPassword(
        USER_CREDENTIALS_KEY,
        JSON.stringify({ username: user.username, password }),
        { service: USER_CREDENTIALS_KEY }
      );
      console.log('User credentials stored successfully');

      if (rememberMe) {
        // Store remember me flag for specific user
        await Keychain.setGenericPassword(
          `${REMEMBER_ME_KEY}_${user.username}`,
          'true',
          { service: `${REMEMBER_ME_KEY}_${user.username}` }
        );
      }

      // Store user info
      await Keychain.setGenericPassword(USER_INFO_KEY, userInfo, { service: USER_INFO_KEY });
      console.log('User info stored successfully');
    } catch (error) {
      console.error('Error storing credentials:', error);
      throw error;
    }
  }
}

// Export AuthService
export { AuthService };
export default AuthService; 