// services/ReadingValidationService.ts
// Multi-layer data validation system for meter readings
// Implements business rules, GPS accuracy, and OCR confidence validation

import { LocationData, LocationValidationResult } from './LocationService';

export interface ReadingValidationInput {
  currentReading: number;
  previousReading?: number;
  previousReadingDate?: Date;
  ocrConfidence?: number;
  gpsLocation?: LocationData;
  meterNumber: string;
  taskId?: number;
}

export interface ValidationWarning {
  type: 'info' | 'warning' | 'error';
  code: string;
  message: string;
  severity: 'low' | 'medium' | 'high';
}

export interface ValidationResult {
  isValid: boolean;
  requiresReview: boolean;
  reviewReason?: string;
  isAnomaly: boolean;
  anomalyType?: string;
  warnings: ValidationWarning[];
  validationScore: number; // 0-100
  validationStatus: 'passed' | 'warning' | 'failed' | 'requires_review';
}

export class ReadingValidationService {
  private static instance: ReadingValidationService;

  // Validation thresholds
  private readonly MIN_OCR_CONFIDENCE = 0.6;
  private readonly GOOD_OCR_CONFIDENCE = 0.8;
  private readonly MAX_GPS_ACCURACY = 50; // meters
  private readonly GOOD_GPS_ACCURACY = 20; // meters
  
  // Business rule thresholds
  private readonly MAX_CONSUMPTION_PER_DAY = 1000; // liters per day
  private readonly MAX_CONSUMPTION_PER_MONTH = 10000; // liters per month
  private readonly ANOMALY_CONSUMPTION_THRESHOLD = 5000; // liters
  private readonly NEGATIVE_READING_TOLERANCE = -10; // Allow small negative readings for meter rollover

  private constructor() {}

  public static getInstance(): ReadingValidationService {
    if (!ReadingValidationService.instance) {
      ReadingValidationService.instance = new ReadingValidationService();
    }
    return ReadingValidationService.instance;
  }

  public async validateReading(input: ReadingValidationInput): Promise<ValidationResult> {
    console.log('ReadingValidationService: Starting multi-layer validation...');
    
    const warnings: ValidationWarning[] = [];
    let validationScore = 100;
    let isValid = true;
    let requiresReview = false;
    let reviewReason = '';
    let isAnomaly = false;
    let anomalyType = '';

    // Layer 1: Basic Data Validation
    const basicValidation = this.validateBasicData(input);
    warnings.push(...basicValidation.warnings);
    validationScore -= basicValidation.scorePenalty;
    if (!basicValidation.isValid) {
      isValid = false;
    }

    // Layer 2: OCR Confidence Validation
    if (input.ocrConfidence !== undefined) {
      const ocrValidation = this.validateOCRConfidence(input.ocrConfidence);
      warnings.push(...ocrValidation.warnings);
      validationScore -= ocrValidation.scorePenalty;
      if (ocrValidation.requiresReview) {
        requiresReview = true;
        reviewReason = ocrValidation.reviewReason || '';
      }
    }

    // Layer 3: GPS Accuracy Validation
    if (input.gpsLocation) {
      const gpsValidation = this.validateGPSAccuracy(input.gpsLocation);
      warnings.push(...gpsValidation.warnings);
      validationScore -= gpsValidation.scorePenalty;
      if (gpsValidation.requiresReview) {
        requiresReview = true;
        reviewReason += (reviewReason ? '; ' : '') + (gpsValidation.reviewReason || '');
      }
    }

    // Layer 4: Business Rules Validation
    if (input.previousReading !== undefined) {
      const businessValidation = await this.validateBusinessRules(input);
      warnings.push(...businessValidation.warnings);
      validationScore -= businessValidation.scorePenalty;
      
      if (businessValidation.isAnomaly) {
        isAnomaly = true;
        anomalyType = businessValidation.anomalyType || '';
        requiresReview = true;
        reviewReason += (reviewReason ? '; ' : '') + 'Consumption anomaly detected';
      }
      
      if (!businessValidation.isValid) {
        isValid = false;
      }
    }

    // Layer 5: Historical Pattern Analysis (simplified)
    const patternValidation = await this.validateHistoricalPattern(input);
    warnings.push(...patternValidation.warnings);
    validationScore -= patternValidation.scorePenalty;

    // Determine final validation status
    const validationStatus = this.determineValidationStatus(validationScore, requiresReview, isValid);

    const result: ValidationResult = {
      isValid,
      requiresReview,
      reviewReason: reviewReason || undefined,
      isAnomaly,
      anomalyType: anomalyType || undefined,
      warnings,
      validationScore: Math.max(0, Math.min(100, validationScore)),
      validationStatus
    };

    console.log('ReadingValidationService: Validation completed:', {
      score: result.validationScore,
      status: result.validationStatus,
      warningCount: warnings.length,
      requiresReview,
      isAnomaly
    });

    return result;
  }

  private validateBasicData(input: ReadingValidationInput): { 
    isValid: boolean; 
    warnings: ValidationWarning[]; 
    scorePenalty: number;
  } {
    const warnings: ValidationWarning[] = [];
    let isValid = true;
    let scorePenalty = 0;

    // Check for negative readings
    if (input.currentReading < 0) {
      warnings.push({
        type: 'error',
        code: 'NEGATIVE_READING',
        message: 'Reading cannot be negative',
        severity: 'high'
      });
      isValid = false;
      scorePenalty += 50;
    }

    // Check for unreasonably high readings
    if (input.currentReading > 999999) {
      warnings.push({
        type: 'warning',
        code: 'HIGH_READING',
        message: 'Reading value is unusually high',
        severity: 'medium'
      });
      scorePenalty += 20;
    }

    // Check for zero readings
    if (input.currentReading === 0) {
      warnings.push({
        type: 'warning',
        code: 'ZERO_READING',
        message: 'Zero reading detected - please verify',
        severity: 'medium'
      });
      scorePenalty += 15;
    }

    return { isValid, warnings, scorePenalty };
  }

  private validateOCRConfidence(confidence: number): {
    warnings: ValidationWarning[];
    scorePenalty: number;
    requiresReview: boolean;
    reviewReason?: string;
  } {
    const warnings: ValidationWarning[] = [];
    let scorePenalty = 0;
    let requiresReview = false;
    let reviewReason = '';

    if (confidence < this.MIN_OCR_CONFIDENCE) {
      warnings.push({
        type: 'error',
        code: 'LOW_OCR_CONFIDENCE',
        message: `OCR confidence too low (${Math.round(confidence * 100)}%)`,
        severity: 'high'
      });
      requiresReview = true;
      reviewReason = 'Low OCR confidence requires manual verification';
      scorePenalty += 40;
    } else if (confidence < this.GOOD_OCR_CONFIDENCE) {
      warnings.push({
        type: 'warning',
        code: 'MEDIUM_OCR_CONFIDENCE',
        message: `OCR confidence is moderate (${Math.round(confidence * 100)}%)`,
        severity: 'medium'
      });
      scorePenalty += 20;
    } else {
      warnings.push({
        type: 'info',
        code: 'GOOD_OCR_CONFIDENCE',
        message: `OCR confidence is good (${Math.round(confidence * 100)}%)`,
        severity: 'low'
      });
    }

    return { warnings, scorePenalty, requiresReview, reviewReason };
  }

  private validateGPSAccuracy(location: LocationData): {
    warnings: ValidationWarning[];
    scorePenalty: number;
    requiresReview: boolean;
    reviewReason?: string;
  } {
    const warnings: ValidationWarning[] = [];
    let scorePenalty = 0;
    let requiresReview = false;
    let reviewReason = '';

    if (location.accuracy > this.MAX_GPS_ACCURACY) {
      warnings.push({
        type: 'error',
        code: 'POOR_GPS_ACCURACY',
        message: `GPS accuracy too poor (${location.accuracy.toFixed(1)}m)`,
        severity: 'high'
      });
      requiresReview = true;
      reviewReason = 'Poor GPS accuracy requires location verification';
      scorePenalty += 30;
    } else if (location.accuracy > this.GOOD_GPS_ACCURACY) {
      warnings.push({
        type: 'warning',
        code: 'MODERATE_GPS_ACCURACY',
        message: `GPS accuracy is moderate (${location.accuracy.toFixed(1)}m)`,
        severity: 'medium'
      });
      scorePenalty += 15;
    } else {
      warnings.push({
        type: 'info',
        code: 'GOOD_GPS_ACCURACY',
        message: `GPS accuracy is good (${location.accuracy.toFixed(1)}m)`,
        severity: 'low'
      });
    }

    // Check GPS timestamp freshness
    const gpsAge = Date.now() - location.timestamp;
    if (gpsAge > 5 * 60 * 1000) { // 5 minutes
      warnings.push({
        type: 'warning',
        code: 'OLD_GPS_DATA',
        message: 'GPS data is more than 5 minutes old',
        severity: 'medium'
      });
      scorePenalty += 10;
    }

    return { warnings, scorePenalty, requiresReview, reviewReason };
  }

  private async validateBusinessRules(input: ReadingValidationInput): Promise<{
    isValid: boolean;
    warnings: ValidationWarning[];
    scorePenalty: number;
    isAnomaly: boolean;
    anomalyType?: string;
  }> {
    const warnings: ValidationWarning[] = [];
    let isValid = true;
    let scorePenalty = 0;
    let isAnomaly = false;
    let anomalyType = '';

    if (input.previousReading === undefined) {
      return { isValid, warnings, scorePenalty, isAnomaly };
    }

    const consumption = input.currentReading - input.previousReading;
    const daysSinceLastReading = input.previousReadingDate 
      ? Math.max(1, (Date.now() - input.previousReadingDate.getTime()) / (1000 * 60 * 60 * 24))
      : 30; // Default to 30 days if no date

    // Check for negative consumption (meter rollover or data entry error)
    if (consumption < this.NEGATIVE_READING_TOLERANCE) {
      if (consumption < -1000) {
        // Likely meter rollover
        warnings.push({
          type: 'warning',
          code: 'METER_ROLLOVER',
          message: 'Possible meter rollover detected',
          severity: 'medium'
        });
        isAnomaly = true;
        anomalyType = 'meter_rollover';
        scorePenalty += 20;
      } else {
        // Small negative reading
        warnings.push({
          type: 'warning',
          code: 'NEGATIVE_CONSUMPTION',
          message: 'Reading is lower than previous reading',
          severity: 'medium'
        });
        scorePenalty += 25;
      }
    }

    // Check for abnormally high consumption
    const dailyConsumption = consumption / daysSinceLastReading;
    if (dailyConsumption > this.MAX_CONSUMPTION_PER_DAY) {
      warnings.push({
        type: 'warning',
        code: 'HIGH_DAILY_CONSUMPTION',
        message: `High daily consumption: ${dailyConsumption.toFixed(0)} L/day`,
        severity: 'high'
      });
      isAnomaly = true;
      anomalyType = 'high_consumption';
      scorePenalty += 30;
    }

    // Check for anomalous consumption patterns
    if (consumption > this.ANOMALY_CONSUMPTION_THRESHOLD) {
      warnings.push({
        type: 'warning',
        code: 'CONSUMPTION_ANOMALY',
        message: `Unusual consumption pattern: ${consumption.toLocaleString()} L`,
        severity: 'high'
      });
      isAnomaly = true;
      anomalyType = anomalyType || 'consumption_anomaly';
      scorePenalty += 25;
    }

    // Check for zero consumption over extended period
    if (consumption === 0 && daysSinceLastReading > 7) {
      warnings.push({
        type: 'warning',
        code: 'ZERO_CONSUMPTION',
        message: 'No consumption detected over extended period',
        severity: 'medium'
      });
      scorePenalty += 15;
    }

    // Normal consumption range validation
    if (consumption > 0 && consumption < this.MAX_CONSUMPTION_PER_DAY && dailyConsumption < 500) {
      warnings.push({
        type: 'info',
        code: 'NORMAL_CONSUMPTION',
        message: `Normal consumption: ${consumption.toLocaleString()} L`,
        severity: 'low'
      });
    }

    return { isValid, warnings, scorePenalty, isAnomaly, anomalyType };
  }

  private async validateHistoricalPattern(input: ReadingValidationInput): Promise<{
    warnings: ValidationWarning[];
    scorePenalty: number;
  }> {
    const warnings: ValidationWarning[] = [];
    let scorePenalty = 0;

    // Simplified historical pattern analysis
    // In a full implementation, this would query historical data from the database
    
    // For now, just add a placeholder validation
    if (input.currentReading > 0) {
      warnings.push({
        type: 'info',
        code: 'PATTERN_ANALYSIS',
        message: 'Reading pattern appears normal',
        severity: 'low'
      });
    }

    return { warnings, scorePenalty };
  }

  private determineValidationStatus(
    score: number, 
    requiresReview: boolean, 
    isValid: boolean
  ): 'passed' | 'warning' | 'failed' | 'requires_review' {
    if (!isValid) {
      return 'failed';
    }
    
    if (requiresReview) {
      return 'requires_review';
    }
    
    if (score >= 80) {
      return 'passed';
    } else if (score >= 60) {
      return 'warning';
    } else {
      return 'requires_review';
    }
  }

  public getValidationSummary(result: ValidationResult): string {
    const { validationScore, validationStatus, warnings } = result;
    const errorCount = warnings.filter(w => w.type === 'error').length;
    const warningCount = warnings.filter(w => w.type === 'warning').length;

    let summary = `Validation Score: ${validationScore}/100 (${validationStatus})`;
    
    if (errorCount > 0) {
      summary += ` | ${errorCount} error(s)`;
    }
    
    if (warningCount > 0) {
      summary += ` | ${warningCount} warning(s)`;
    }

    if (result.requiresReview) {
      summary += ' | Requires Review';
    }

    return summary;
  }
}

export const readingValidationService = ReadingValidationService.getInstance(); 