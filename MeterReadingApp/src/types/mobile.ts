// Mobile Task Types - Aligned with backend WorkTask model
export interface MobileTaskDto {
  // ============ Core WorkTask fields ============
  id: number;
  name: string;
  description?: string;
  status: string;                    // Pending, InProgress, Completed, Cancelled, Failed, Skipped
  priority: string;                  // Low, Medium, High, Critical
  type: string;                      // MeterReading, Inspection, Maintenance, Installation
  
  // ============ Assignment fields ============ 
  assignedUserId?: number;           // Maps to AssignedUserId in backend MobileTaskDto
  assignedBy: string;                // Maps to AssignedBy in backend
  assignmentType: string;            // Maps to AssignmentType in backend
  
  // ============ Water meter relation ============
  meterId: number;                   // Maps to MeterId in backend MobileTaskDto
  meterNumber: string;               // Maps to MeterNumber in backend MobileTaskDto
  meterType?: string;                // Maps to MeterType in backend
  assetId?: string;                  // Maps to AssetId in backend
  
  // ============ Time management ============
  dueDate?: Date;                    // Maps to DueDate in backend
  startDate?: Date;                  // Maps to StartDate in backend (not scheduledDate)
  
  // ============ Location information ============
  location?: string;                 // Maps to Location
  serviceAddress?: string;           // Maps to ServiceAddress (new field)
  
  // ============ Task details ============
  instructions?: string;             // Maps to Instructions
  notes?: string;                    // Maps to Notes
  
  // ============ Water meter information (from joined data) ============
  accountNumber?: string;
  lastReading?: number;
  lastReadingDate?: Date;
  
  // ============ Customer information (from joined data) ============
  customerName?: string;
  customerPhone?: string;
  customerEmail?: string;
  address?: string;
  
  // ============ Location information (from meter) ============
  latitude?: number;
  longitude?: number;
  
  // ============ Legacy assignment fields (for backward compatibility) ============
  assignedDate?: Date;
  
  // ============ Progress information ============
  estimatedHours?: number;
  progressPercentage?: number;
  
  // ============ Additional meter information ============
  meterInfo?: MeterInfoDto;
  
  // ============ Mobile-specific computed fields ============
  isUrgent?: boolean;
  isOverdue?: boolean;
  daysUntilDue?: number;
  
  // ============ Audit fields (BaseEntity) ============
  createdAt: Date;
  updatedAt: Date;
}

// Water Meter Info DTO - Aligned with backend WaterMeter model
export interface MeterInfoDto {
  // ============ Core identity fields ============
  id: number;
  assetId?: string;                   // Maps to AssetId
  serialNumber: string;               // Maps to SerialNumber (required)
  accountNumber?: string;             // Maps to AccountNumber
  
  // ============ Location information ============
  location: string;                   // Maps to Location (required)
  address?: string;                   // Maps to Address
  latitude?: number;                  // Maps to Latitude
  longitude?: number;                 // Maps to Longitude
  
  // ============ Meter information ============
  meterType: string;                  // Maps to MeterType (required)
  status: string;                     // Maps to Status (required)
  customerName?: string;              // Maps to CustomerName
  lastReading?: number;               // Maps to LastReading
  lastReadingDate?: Date;             // Maps to LastReadingDate
  
  // ============ Legacy/additional fields (for backward compatibility) ============
  meterId?: number;                   // Legacy field
  meterNumber?: string;               // Legacy field - same as serialNumber
  brand?: string;
  model?: string;
  size?: string;
  type?: string;                      // Legacy field
  installDate?: Date;
  manufacturerName?: string;
}

export interface MobileTaskDetailDto extends MobileTaskDto {
  workPackageName?: string;
  routeName?: string;
  completedDate?: Date;
  actualHours?: number;
  readingHistory: MobileReadingHistoryDto[];
  assignmentHistory: MobileTaskAssignmentHistoryDto[];
  attachedPhotos: string[];
  alerts: string[];
}

export interface MobileReadingHistoryDto {
  Id: number;
  ReadingValue: number;
  ReadingDate: Date;
  ReadingMethod: string;
  Notes?: string;
  ReadByUser: string;
  QualityScore?: number;
}

export interface MobileTaskAssignmentHistoryDto {
  Id: number;
  AssignedDate: Date;
  AssignedBy: string;
  AssignmentType: string;
  Status: string;
  AcceptedDate?: Date;
  RejectedDate?: Date;
  Reason?: string;
}

export interface StartTaskRequest {
  StartLatitude?: number;
  StartLongitude?: number;
  Notes?: string;
  DeviceInfo: string;
}

export interface UpdateTaskStatusRequest {
  Status: string;
  Notes?: string;
  Latitude?: number;
  Longitude?: number;
  Timestamp: Date;
}

export interface MobileTaskSummaryDto {
  TotalAssigned: number;
  InProgress: number;
  Completed: number;
  Overdue: number;
  DueToday: number;
  DueTomorrow: number;
  CompletionRate: number;
  ConsecutiveDaysActive: number;
  UrgentTasks: UrgentTaskDto[];
}

export interface UrgentTaskDto {
  Id: number;
  Name: string;
  DueDate: Date;
  HoursOverdue: number;
  Priority: string;
}

export interface TaskStatusSyncRequest {
  StatusUpdates: TaskStatusUpdate[];
  SyncTimestamp: Date;
}

export interface TaskStatusUpdate {
  TaskId: number;
  Status: string;
  UpdateTime: Date;
  Notes?: string;
  Latitude?: number;
  Longitude?: number;
  OfflineId?: string;
}

export interface TaskSyncResponseDto {
  TotalUpdates: number;
  SuccessfulUpdates: number;
  FailedUpdates: number;
  Results: TaskSyncResult[];
  GeneralErrors: string[];
}

export interface TaskSyncResult {
  TaskId: number;
  OfflineId?: string;
  Success: boolean;
  ErrorMessage?: string;
  ServerTimestamp?: Date;
}

/**
 * 移动端读数DTO - 与后端MeterReading模型对齐
 * Mobile Reading DTO - Aligned with Backend MeterReading Model
 */
export interface MobileReadingDto {
  TaskId: number;
  MeterId: number;
  UserId: number;
  ReadingValue: number;
  ReadingDate: Date;
  ReadingMethod: 'Manual' | 'OCR' | 'Estimated';
  ReadingType?: 'Regular' | 'Reactive' | 'Emergency';
  DataSource?: 'Mobile' | 'Web' | 'Import';
  HasOCR?: boolean;
  OcrStatus?: 'Success' | 'Failed' | 'LowConfidence';
  OcrConfidence?: number;
  Latitude?: number;
  Longitude?: number;
  GpsAccuracy?: number;
  Location?: string;
  Status?: 'Completed' | 'Pending' | 'Validated' | 'Rejected';
  ValidationStatus?: 'Valid' | 'Invalid' | 'RequiresReview';
  IsValidated?: boolean;
  ValidatedBy?: number;
  ValidationDate?: Date;
  ValidationComments?: string;
  IsAnomalous?: boolean;
  AnomalyReason?: string;
  AnomalyType?: string;
  CantRead?: boolean;
  CantReadReason?: string;
  Notes?: string;
  Photos?: MobilePhotoDto[];
  DeviceInfo?: string;
  AppVersion?: string;
  IsOfflineReading?: boolean;
  OfflineId?: string;
}

/**
 * 移动端照片DTO
 * Mobile Photo DTO
 */
export interface MobilePhotoDto {
  Id?: number;
  Uuid: string;
  FileName: string;
  FilePath: string;
  Base64Data?: string;
  FileSizeBytes: number;
  MimeType: string;
  CapturedAt: Date;
  Latitude?: number;
  Longitude?: number;
  PhotoType: 'meter' | 'location' | 'issue' | 'other';
  SyncStatus: 'pending' | 'synced' | 'error';
  SyncError?: string;
}

export interface CompleteTaskRequest {
  reading: MobileReadingDto;
  completionNotes?: string;
  issuesEncountered: string[];
  requiresFollowUp: boolean;
  followUpReason?: string;
  taskStartTime: Date;
  taskCompletionTime: Date;
  actualMinutesSpent: number;
  customerContacted: boolean;
  customerFeedback?: string;
  completionLatitude?: number;
  completionLongitude?: number;
}

export interface ReadingResponseDto {
  readingId: number;
  success: boolean;
  message: string;
  warnings: string[];
  errors: string[];
  isWithinExpectedRange: boolean;
  expectedMinValue?: number;
  expectedMaxValue?: number;
  consumptionRate?: number;
  photoResults: PhotoUploadResult[];
  requiresReview: boolean;
  reviewReason?: string;
  nextActions: string[];
}

export interface PhotoUploadResult {
  originalFileName: string;
  uploadedFileName?: string;
  success: boolean;
  errorMessage?: string;
  thumbnailUrl?: string;
  fullSizeUrl?: string;
}

export interface BatchReadingRequest {
  readings: MobileReadingDto[];
  syncTimestamp: Date;
  deviceId: string;
  appVersion: string;
}

export interface BatchReadingResponseDto {
  TotalSubmitted: number;
  SuccessfullyProcessed: number;
  Failed: number;
  Results: ReadingResponseDto[];
  GeneralErrors: string[];
  AllSuccessful: boolean;
}

export interface ReadingValidationDto {
  IsValid: boolean;
  Errors: string[];
  Warnings: string[];
  DailyConsumption?: number;
  IsConsumptionNormal: boolean;
  ConsumptionAnalysis?: string;
  HasRequiredPhotos: boolean;
  PhotoCount: number;
  PhotoIssues: string[];
}

export interface UpdateReadingRequest {
  ReadingValue?: number;
  Notes?: string;
  QualityScore?: number;
  AdditionalPhotos: MobilePhotoDto[];
}

export interface MobileReadingStatsDto {
  totalReadings: number;
  readingsThisWeek: number;
  readingsThisMonth: number;
  averageQualityScore: number;
  photosUploaded: number;
  issuesReported: number;
  averageReadingTime: number; // in minutes
  lastReadingDate?: Date;
  consecutiveDaysWithReadings: number;
  last7Days: DailyReadingCount[];
}

export interface DailyReadingCount {
  date: Date;
  count: number;
  averageQuality: number;
}

export interface OfflineReadingSyncRequest {
  OfflineReadings: OfflineReading[];
  SyncTimestamp: Date;
  DeviceId: string;
}

export interface OfflineReading {
  OfflineId: string;
  Reading: MobileReadingDto;
  OfflineTimestamp: Date;
  WasCompleted: boolean;
}

export interface OfflineReadingSyncResponse {
  TotalOfflineReadings: number;
  SuccessfullySynced: number;
  Failed: number;
  Results: OfflineReadingSyncResult[];
  GeneralErrors: string[];
}

export interface OfflineReadingSyncResult {
  OfflineId: string;
  Success: boolean;
  ReadingId?: number;
  ErrorMessage?: string;
  ServerTimestamp?: Date;
  RequiresReview: boolean;
}

// Mobile User Types
export interface MobileUserWorkloadDto {
  UserId: number;
  UserName: string;
  FullName: string;
  Email: string;
  Department?: string;
  Zone?: string;
  
  // Workload statistics
  ActiveTaskCount: number;
  CompletedTaskCount: number;
  OverdueTaskCount: number;
  TotalAssignedToday: number;
  WorkloadPercentage: number;
  EfficiencyScore: number;
  
  // Today's statistics
  TodayCompletedCount: number;
  TodayPendingCount: number;
  TodayProgress: number;
  
  // This week's statistics
  WeekCompletedCount: number;
  WeekAssignedCount: number;
  
  // Status information
  AvailabilityStatus: string;
  LastActivity?: Date;
  LastLocationUpdate?: Date;
  CurrentLatitude?: number;
  CurrentLongitude?: number;
  
  // Capacity and skills
  MaxCapacity: number;
  Skills: string[];
  
  // Computed fields
  IsAtCapacity: boolean;
  IsOverloaded: boolean;
  WorkloadStatus: string;
}

export interface MobileUserStatsDto {
  // Overall statistics
  TotalTasksAssigned: number;
  TotalTasksCompleted: number;
  TotalReadingsSubmitted: number;
  AverageCompletionTime: number; // in hours
  CompletionRate: number; // percentage
  CurrentStreak: number; // consecutive completion days
  
  // This week
  WeekTasksCompleted: number;
  WeekReadingsSubmitted: number;
  WeekAverageTime: number;
  
  // This month
  MonthTasksCompleted: number;
  MonthReadingsSubmitted: number;
  MonthAverageTime: number;
  
  // Quality metrics
  AverageQualityScore: number;
  PhotosSubmitted: number;
  IssuesReported: number;
  CustomersContacted: number;
  
  // Performance trends
  Last7Days: DailyPerformanceDto[];
  Last6Months: MonthlyPerformanceDto[];
  
  // Recognition and achievements
  Achievements: string[];
  Rank: number; // among all users
  TotalUsers: number;
  
  // Recent activity
  LastTaskCompleted?: Date;
  LastReadingSubmitted?: Date;
  LastTaskLocation?: string;
}

export interface DailyPerformanceDto {
  Date: Date;
  TasksCompleted: number;
  ReadingsSubmitted: number;
  HoursWorked: number;
  AverageQuality: number;
}

export interface MonthlyPerformanceDto {
  Year: number;
  Month: number;
  MonthName: string;
  TasksCompleted: number;
  ReadingsSubmitted: number;
  TotalHours: number;
  CompletionRate: number;
}

export interface LocationUpdateDto {
  Latitude: number;
  Longitude: number;
  Accuracy?: number;
  Timestamp: Date;
  ActivityType: string; // reading, travel, break, general
  Notes?: string;
}

export interface MobileUserProfileDto {
  UserId: number;
  UserName: string;
  FullName: string;
  Email: string;
  Phone?: string;
  Department?: string;
  Zone?: string;
  Role: string;
  
  // Employment info
  StartDate?: Date;
  EmployeeId?: string;
  Supervisor?: string;
  
  // App preferences
  Preferences: MobileAppPreferencesDto;
  
  // Device information
  RegisteredDevices: MobileDeviceDto[];
  
  // Last login info
  LastLoginDate?: Date;
  LastLoginDevice?: string;
  LastLoginLocation?: string;
}

export interface MobileAppPreferencesDto {
  EnableNotifications: boolean;
  EnableLocationTracking: boolean;
  EnableOfflineMode: boolean;
  EnableAutoSync: boolean;
  Theme: string; // light, dark, auto
  Language: string;
  SyncIntervalMinutes: number;
  RequirePhotoForReading: boolean;
  EnableGPSValidation: boolean;
}

export interface MobileDeviceDto {
  DeviceId: string;
  DeviceName: string;
  Platform: string; // iOS, Android
  OSVersion: string;
  AppVersion: string;
  LastUsed: Date;
  IsActive: boolean;
  RegisteredDate: Date;
}

export interface UserStatusUpdateDto {
  Status: string; // Available, Busy, Break, Offline
  Notes?: string;
  StatusUntil?: Date;
  Latitude?: number;
  Longitude?: number;
}

export interface UserLeaderboardDto {
  UserId: number;
  FullName: string;
  Department: string;
  Rank: number;
  TasksCompleted: number;
  CompletionRate: number;
  AverageQuality: number;
  Score: number;
  IsCurrentUser: boolean;
}

export interface UserAchievementDto {
  Id: number;
  Name: string;
  Description: string;
  IconName: string;
  EarnedDate: Date;
  Category: string;
  Points: number;
  IsNew: boolean;
}

export interface MobileSyncRequestDto {
  DeviceId: string;
  AppVersion: string;
  LastSyncTime: Date;
  Latitude?: number;
  Longitude?: number;
  ActivityStatus: string;
  PendingTaskUpdates: number;
  PendingReadings: number;
}

export interface MobileSyncResponseDto {
  ServerTime: Date;
  HasNewAssignments: boolean;
  NewAssignmentCount: number;
  HasUpdatedTasks: boolean;
  UpdatedTaskCount: number;
  RequiresFullSync: boolean;
  SystemMessages: string[];
  Notifications: string[];
  ForceUpdateUrl?: string;
  IsAppVersionSupported: boolean;
}

export interface NearbyUserDto {
  UserId: number;
  FullName: string;
  Department: string;
  DistanceKm: number;
  Status: string;
  LastLocationUpdate?: Date;
  ActiveTasks: number;
  CanContactDirectly: boolean;
}

// Utility Types
export interface ApiResponse<T> {
  data: T;
  success: boolean;
  message?: string;
  errors?: string[];
}

export interface PaginatedResponse<T> {
  data: T[];
  totalCount: number;
  pageNumber: number;
  pageSize: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

export interface FilterOptions {
  status?: string[];
  priority?: string[];
  dateRange?: {
    from?: Date;
    to?: Date;
  };
  location?: {
    latitude: number;
    longitude: number;
    radius: number;
  };
  assignedTo?: number[];
  meterType?: string[];
}

export interface SortOptions {
  field: string;
  direction: 'asc' | 'desc';
}

export interface SearchRequest {
  query?: string;
  filters?: FilterOptions;
  sort?: SortOptions;
  page: number;
  pageSize: number;
}

// Authentication Types
export interface LoginRequest {
  username: string;
  password: string;
  deviceId?: string;
  deviceName?: string;
}

export interface LoginResponse {
  token: string;
  refreshToken?: string;
  user: MobileUserProfileDto;
  expiresAt: Date;
}

export interface RefreshTokenRequest {
  refreshToken: string;
}

// Error Types
export interface ApiError {
  message: string;
  code?: string;
  details?: any;
  timestamp: Date;
}

export interface ValidationError {
  field: string;
  message: string;
  value?: any;
}

// Offline Support Types
export interface OfflineQueueItem {
  id: string;
  type: 'task_update' | 'reading_submission' | 'photo_upload' | 'location_update';
  data: any;
  timestamp: Date;
  retryCount: number;
  maxRetries: number;
}

export interface SyncStatus {
  isOnline: boolean;
  lastSyncTime?: Date;
  pendingItems: number;
  syncInProgress: boolean;
  errors: string[];
} 

export interface MeterReadingData {
  id?: number;
  meterId: number;
  meterNumber?: string;
  readingValue: number;
  readingDate: string;
  readBy?: string;
  status?: string;
  latitude?: number | null;
  longitude?: number | null;
  gpsAccuracy?: number | null;
  hasPhoto?: boolean;
  hasOCR?: boolean;
  ocrStatus?: string;
  ocrConfidence?: number;
  notes?: string;
  syncStatus?: string;
  createdAt?: string;
  updatedAt?: string;
} 