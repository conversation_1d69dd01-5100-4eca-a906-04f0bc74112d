// Backend Model Types - Aligned with C# models
// These interfaces represent the exact structure expected from backend APIs

// ============ Backend WorkTask Model ============
export interface BackendWorkTask {
  // BaseEntity fields (inherited from C# BaseEntity)
  id: number;
  createdAt: string;              // DateTime in C#
  updatedAt?: string;             // DateTime? in C#  
  isDeleted: boolean;             // bool in C#
  createdBy: string;              // string in C#
  updatedBy?: string;             // string? in C#

  // WorkTask specific fields (from C# WorkTask model)
  name: string;                   // [Required, StringLength(200)]
  description?: string;           // [StringLength(1000)]
  status: string;                 // [Required] - enum: Pending, InProgress, Completed, Cancelled, Failed, Skipped
  priority: string;               // [Required] - enum: Low, Medium, High, Critical  
  type: string;                   // [Required] - enum: MeterReading, Inspection, Maintenance, Installation
  assignedUserId?: number;        // int? - foreign key to User
  meterId?: number;               // int? - foreign key to WaterMeter
  workPackageId?: number;         // int? - foreign key to WorkPackage
  
  // Date fields
  dueDate?: string;               // DateTime?
  scheduledDate?: string;         // DateTime?
  completedDate?: string;         // DateTime?
  
  // Location fields
  location?: string;              // [StringLength(500)]
  serviceAddress?: string;        // [StringLength(500)]
  
  // Task content
  instructions?: string;          // [StringLength(2000)]
  notes?: string;                 // [StringLength(2000)]
}

// ============ Backend WaterMeter Model ============
export interface BackendWaterMeter {
  // BaseEntity fields
  id: number;
  createdAt: string;
  updatedAt: string;
  isDeleted: boolean;
  createdBy: string;
  updatedBy: string;

  // AMS Core fields
  assetId?: string;
  serialNumber: string;
  accountNumber?: string;
  bookNumber?: string;
  unit: number;
  
  // Geographic location
  roadNumber?: number;
  roadName?: string;
  township?: string;
  subArea?: string;
  location: string;
  address: string;
  latitude?: number;
  longitude?: number;
  
  // Reading information
  lastRead?: number;
  recentChange?: number;
  subd?: string;
  dateOfRead?: string;
  read?: number;
  cantRead: boolean;
  condition?: string;
  comments?: string;
  
  // Legacy reading fields
  lastReading?: number;
  lastReadingDate?: string;
  
  // Meter information
  meterType: string;
  status: string;
  installDate?: string;
  customerCode?: string;
  customerName?: string;
  brand?: string;
  model?: string;
  batteryLevel?: number;
  communicationMethod?: string;
  lastMaintenanceDate?: string;
  nextMaintenanceDate?: string;
  notes?: string;
  
  // AMS system fields
  source: string;
  routeId?: number;
  assignedRoute?: string;
  routeSequence?: number;
  syncStatus: string;
  lastSyncDate?: string;
  meterSize: string;
}

// ============ Backend Reading Model ============
export interface BackendReading {
  // BaseEntity fields
  id: number;
  createdAt: string;
  updatedAt: string;
  isDeleted: boolean;
  createdBy: string;
  updatedBy: string;

  // Core reading fields
  meterId: number;
  userId: number;
  taskId?: number;
  readingValue: number;
  readingDate: string;
  previousReading?: number;
  
  // Reading method and quality
  readingMethod: string;            // Manual, OCR, Automatic
  ocrConfidence?: number;
  
  // GPS information
  latitude?: number;
  longitude?: number;
  gpsAccuracy?: number;
  gpsTimestamp?: string;
  
  // Photo information
  photoFilename?: string;
  photoFilePath?: string;
  photoUploaded: boolean;
  
  // Reading metadata
  notes?: string;
  readingType?: string;
  dataSource?: string;
  qualityScore?: number;
  
  // Validation and status
  isValidated: boolean;
  validatedBy?: string;
  validatedAt?: string;
  status?: string;
  
  // Consumption calculation
  consumption?: number;
  consumptionPeriod?: number;
  
  // Meter details at time of reading
  meterSerialNumber?: string;
  meterLocation?: string;
}

// ============ Backend User Model ============
export interface BackendUser {
  // BaseEntity fields (inherited from C# BaseEntity)
  id: number;
  createdAt: string;              // DateTime in C#
  updatedAt?: string;             // DateTime? in C#
  isDeleted: boolean;             // bool in C#
  createdBy: string;              // string in C#
  updatedBy?: string;             // string? in C#

  // User specific fields (from C# User model)
  username: string;               // [Required, StringLength(50)]
  fullName?: string;              // [StringLength(100)]
  email?: string;                 // [StringLength(255)]
  personId?: number;              // int? - foreign key
  finCoCode?: string;             // [StringLength(10)]
  mobilePhone?: string;           // [StringLength(20)]
  profitCentreCode?: string;      // [StringLength(20)]
  employeeNo?: string;            // [StringLength(50)]
  isAuthenticated: boolean;       // bool
  lastLogin?: string;             // DateTime?
  createdDate: string;            // DateTime (required)
  updatedDate?: string;           // DateTime?
}

// ============ API Response Wrappers ============
export interface BackendApiResponse<T> {
  data: T;
  success: boolean;
  message?: string;
  errors?: string[];
  pagination?: {
    page: number;
    pageSize: number;
    totalPages: number;
    totalCount: number;
  };
}

export interface BackendPaginatedResponse<T> {
  data: T[];
  totalCount: number;
  pageNumber: number;
  pageSize: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

// ============ API Request Types ============
export interface BackendCreateReadingRequest {
  meterId: number;
  readingValue: number;
  readingDate: string;
  readingMethod: string;
  latitude?: number;
  longitude?: number;
  gpsAccuracy?: number;
  photoFilename?: string;
  notes?: string;
  taskId?: number;
}

export interface BackendUpdateTaskRequest {
  id: number;
  status?: string;
  currentReading?: string;
  completedDate?: string;
  notes?: string;
  actualHours?: number;
  progressPercentage?: number;
}

// ============ Mobile API Response Types ============
export interface BackendMobileTaskResponse {
  tasks: BackendWorkTask[];
  totalCount: number;
  pageNumber: number;
  pageSize: number;
}

export interface BackendMobileReadingResponse {
  reading: BackendReading;
  syncStatus: string;
  uploadTime: string;
} 