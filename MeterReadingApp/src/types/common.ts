// Common status types
export type SyncStatus = 'synced' | 'pending' | 'error';
export type TaskStatus = 'assigned' | 'in_progress' | 'completed' | 'cancelled';
export type Priority = 'low' | 'medium' | 'high' | 'urgent';
export type UserRole = 'admin' | 'reader' | 'supervisor';

// API response wrapper
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  statusCode?: number;
}

// Pagination
export interface PaginationParams {
  page: number;
  limit: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// Search and filter
export interface SearchParams {
  query?: string;
  filters?: Record<string, any>;
  dateRange?: {
    start: string;
    end: string;
  };
}

// Location
export interface LocationCoords {
  latitude: number;
  longitude: number;
  accuracy?: number;
  timestamp?: number;
}

// File upload
export interface FileUpload {
  uri: string;
  type: string;
  name: string;
  size?: number;
}

// Error types
export interface AppError {
  code: string;
  message: string;
  details?: any;
  timestamp: Date;
}

// Settings
export interface AppSettings {
  syncInterval: number; // in minutes
  autoSync: boolean;
  compressionEnabled: boolean;
  maxPhotoSize: number; // in MB
  gpsAccuracyThreshold: number; // in meters
  offlineDataRetention: number; // in days
}

// Validation result
export interface ValidationResult {
  isValid: boolean;
  errors: string[];
}

// Export result
export interface ExportResult {
  success: boolean;
  filePath?: string;
  error?: string;
  recordCount?: number;
} 