// Sync and upload related types
// Common types used across business and sync services

export interface SyncResult {
  success: boolean;
  recordCount: number;
  errorMessage?: string;
  syncTime: string;
  operation: string;
}

export interface UploadResult {
  success: boolean;
  type: string;
  message: string;
  recordCount?: number;
  recordId?: number;
  serverResponse?: any;
  errors?: string[];
  timestamp: string;
}

export interface SyncStatistics {
  pending: number;
  synced: number;
  error: number;
  total: number;
}

export interface BusinessDataSummary {
  pendingReadings: number;
  pendingPhotos: number;
  pendingTaskUpdates: number;
  lastUploadTime?: string;
}

// Water meter data from backend API
export interface WaterMeterApiData {
  id: number;
  meterNumber: string;
  serialNumber?: string;
  meterType?: string;
  location?: string;
  address?: string;
  latitude?: number;
  longitude?: number;
  installationDate?: string;
  lastReading?: number;
  lastReadingDate?: string;
  status: 'Active' | 'Inactive' | 'Maintenance';
  customerName?: string;
  customerPhone?: string;
  customerEmail?: string;
  createdAt: string;
  updatedAt: string;
}

// Task data from backend API - 完全对齐后端MobileTaskDto
export interface TaskApiData {
  // 基础任务信息 - 匹配后端字段
  id: number;                        // Id
  name: string;                      // Name  
  description?: string;              // Description
  status: string;                    // Status
  priority: string;                  // Priority
  type: string;                      // Type (不是taskType)
  dueDate?: string;                  // DueDate
  startDate?: string;                // StartDate (不是scheduledDate)
  location?: string;                 // Location
  instructions?: string;             // Instructions
  notes?: string;                    // Notes
  
  // 水表信息 - 匹配后端字段
  meterId: number;                   // MeterId
  meterNumber: string;               // MeterNumber (水表序列号)
  meterType?: string;                // MeterType
  assetId?: string;                  // AssetId
  accountNumber?: string;            // AccountNumber
  lastReading?: number;              // LastReading
  lastReadingDate?: string;          // LastReadingDate
  
  // 客户信息
  customerName?: string;             // CustomerName
  customerPhone?: string;            // CustomerPhone  
  customerEmail?: string;            // CustomerEmail
  address?: string;                  // Address
  
  // 位置信息
  latitude?: number;                 // Latitude
  longitude?: number;                // Longitude
  
  // 分配信息 - 统一使用数据库字段名
  assignedUserId?: number;           // AssignedUserId (直接对应数据库字段)
  assignedDate: string;              // AssignedDate
  assignedBy: string;                // AssignedBy
  assignmentType: string;            // AssignmentType
  
  // 进度信息
  estimatedHours?: number;           // EstimatedHours
  progressPercentage: number;        // ProgressPercentage
  
  // 审计字段 - 匹配后端字段
  createdAt: string;                 // CreatedAt
  updatedAt: string;                 // UpdatedAt
}

// User data from backend API  
export interface UserApiData {
  id: number;
  username: string;
  fullName?: string;
  email?: string;
  phone?: string;
  role: string;
  department?: string;
  zone?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
} 