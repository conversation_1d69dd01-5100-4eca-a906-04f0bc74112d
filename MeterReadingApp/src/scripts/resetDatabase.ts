import { DatabaseManager } from '../database/DatabaseManager';

export class DatabaseResetService {
  static async resetDatabase(): Promise<void> {
    try {
      console.log('🔄 Starting database reset...');
      
      const dbManager = DatabaseManager.getInstance();
      await dbManager.initializeDatabase();
      
      await dbManager.forceResetDatabase();
      
      console.log('✅ Database reset completed successfully');
      console.log('📋 All tables have been recreated with the latest schema');
      
    } catch (error) {
      console.error('❌ Error resetting database:', error);
      throw error;
    }
  }

  static async checkDatabaseVersion(): Promise<void> {
    try {
      const dbManager = DatabaseManager.getInstance();
      await dbManager.initializeDatabase();
      
      const version = await dbManager.getCurrentVersion();
      console.log(`📊 Current database version: ${version}`);
      
    } catch (error) {
      console.error('❌ Error checking database version:', error);
      throw error;
    }
  }
}
