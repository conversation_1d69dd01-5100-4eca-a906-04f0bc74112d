# Meter Reading App

A React Native application for water meter reading with OCR (Optical Character Recognition) functionality.

## Features

- **📱 Cross-platform**: Built with React Native for iOS and Android
- **📷 Camera Integration**: Take photos of water meters
- **🖼️ Gallery Support**: Select images from device gallery
- **🔍 OCR Recognition**: Automatic text recognition using ML Kit
- **✏️ Manual Input**: Manual entry option for meter readings
- **🌙 Dark/Light Theme**: Support for both dark and light modes
- **🔐 Simple Authentication**: Basic login system
- **📊 Clean UI**: Modern and intuitive user interface

## Tech Stack

- **React Native**: 0.72.6
- **TypeScript**: Full type safety
- **Native Base**: UI component library
- **React Navigation**: Navigation system
- **ML Kit Text Recognition**: OCR functionality
- **React Native Image Picker**: Camera and gallery access
- **Async Storage**: Local data persistence

## Installation

1. Clone the repository:
```bash
git clone https://github.com/YOUR_USERNAME/MeterReadingApp.git
cd MeterReadingApp
```

2. Install dependencies:
```bash
yarn install
```

3. For iOS (if on macOS):
```bash
cd ios && pod install && cd ..
```

4. Run the application:

For Android:
```bash
yarn android
```

For iOS:
```bash
yarn ios
```

## Usage

1. **Login**: Use username `admin` and password `password`
2. **Take Photo**: Tap "Take Photo" to capture a meter image
3. **Select from Gallery**: Tap "Choose Photo" to select existing image
4. **Recognize**: Tap "Recognize Meter Reading" to extract numbers
5. **Manual Input**: Enter readings manually if needed

## Permissions

The app requires the following permissions:
- **Camera**: To take photos of meters
- **Gallery/Photos**: To select existing images

## Project Structure

```
src/
├── components/          # Reusable UI components
├── context/            # React Context providers
├── navigation/         # Navigation configuration
├── screens/           # Screen components
├── styles/            # Theme and styling
├── utils/             # Utility functions
└── App.tsx           # Main app component
```

## Development

### Prerequisites

- Node.js (>=16)
- React Native CLI
- Android Studio (for Android development)
- Xcode (for iOS development, macOS only)

### Running in Development

```bash
# Start Metro bundler
yarn start

# Run on Android
yarn android

# Run on iOS
yarn ios
```

## Building for Production

### Android

```bash
cd android
./gradlew assembleRelease
```

### iOS

1. Open `ios/MeterReadingApp.xcworkspace` in Xcode
2. Select your target device/simulator
3. Choose "Product" > "Archive"

## Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit your changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to the branch (`git push origin feature/AmazingFeature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Acknowledgments

- ML Kit for OCR functionality
- Native Base for UI components
- React Native community for excellent libraries

## Screenshots

*Add screenshots of your app here*

## Troubleshooting

### Common Issues

1. **Metro bundler issues**: Run `yarn start --reset-cache`
2. **Android build issues**: Clean project with `cd android && ./gradlew clean`
3. **iOS build issues**: Clean build folder in Xcode

### Support

If you encounter any issues, please create an issue on GitHub with:
- Device information
- Error messages
- Steps to reproduce
