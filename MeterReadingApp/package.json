{"name": "MeterReadingApp", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "test": "jest", "build:android:release": "cd android && ./gradlew assembleRelease", "build:android:clean": "cd android && ./gradlew clean", "release": "yarn build:android:clean && yarn build:android:release"}, "dependencies": {"@react-native-async-storage/async-storage": "^2.1.2", "@react-native-community/geolocation": "^3.4.0", "@react-native-community/netinfo": "^11.4.1", "@react-native-ml-kit/text-recognition": "^1.5.2", "@react-navigation/native": "^6.1.17", "@react-navigation/native-stack": "^6.9.26", "@types/react-native-vector-icons": "^6.4.18", "axios": "^1.9.0", "base-64": "^1.0.0", "date-fns": "^4.1.0", "date-fns-tz": "^3.2.0", "lodash": "^4.17.21", "native-base": "^3.4.28", "react": "18.2.0", "react-dom": "18.2.0", "react-native": "0.74.5", "react-native-background-timer": "^2.4.1", "react-native-fs": "^2.20.0", "react-native-get-random-values": "^1.11.0", "react-native-image-picker": "^8.2.1", "react-native-image-resizer": "^1.4.5", "react-native-keychain": "^10.0.0", "react-native-linear-gradient": "^2.8.3", "react-native-maps": "1.18.0", "react-native-permissions": "^4.1.5", "react-native-safe-area-context": "^5.4.1", "react-native-screens": "^4.11.1", "react-native-sqlite-storage": "^6.0.1", "react-native-svg": "^15.12.0", "react-native-vector-icons": "^10.2.0", "react-native-webview": "^13.15.0"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@react-native/babel-preset": "0.74.87", "@react-native/eslint-config": "0.74.87", "@react-native/metro-config": "0.74.87", "@react-native/typescript-config": "0.74.87", "@types/base-64": "^1.0.2", "@types/lodash": "^4.17.17", "@types/react": "^18.2.6", "@types/react-native-maps": "0.24.2", "@types/react-test-renderer": "^18.0.0", "babel-jest": "^29.6.3", "eslint": "^8.19.0", "jest": "^29.6.3", "prettier": "2.8.8", "react-test-renderer": "18.2.0", "typescript": "5.0.4"}, "engines": {"node": ">=18"}}