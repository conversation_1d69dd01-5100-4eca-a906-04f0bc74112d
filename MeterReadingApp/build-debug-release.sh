#!/bin/bash

# 构建可调试的Release版本
# 这个版本使用生产API但保留完整的日志输出

echo "🔧 Building debuggable release APK..."

# 清理之前的构建
echo "🧹 Cleaning previous builds..."
cd android
./gradlew clean
cd ..

# 清理Metro缓存
echo "🗑️ Clearing Metro cache..."
npx react-native start --reset-cache &
METRO_PID=$!
sleep 5
kill $METRO_PID

# 构建Release版本（但启用了调试）
echo "📦 Building release APK with debugging enabled..."
cd android
./gradlew assembleRelease
cd ..

# 检查构建结果
if [ -f "android/app/build/outputs/apk/release/water-meter-0.1.0.apk" ]; then
    echo "✅ Build successful!"
    echo "📱 APK location: android/app/build/outputs/apk/release/water-meter-0.1.0.apk"
    echo ""
    echo "🔍 This APK includes:"
    echo "   - Production API (124.157.93.142)"
    echo "   - Full logging enabled"
    echo "   - Debuggable flag enabled"
    echo ""
    echo "📋 To view logs after installation:"
    echo "   adb logcat | findstr \"ReactNativeJS\""
    echo "   adb logcat | findstr \"API_CONFIG\""
    echo "   adb logcat *:V | findstr \"com.meterreadingapp\""
else
    echo "❌ Build failed!"
    exit 1
fi
