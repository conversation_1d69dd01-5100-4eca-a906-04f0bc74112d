# MeterReadingApp Release Build Script (PowerShell)
# 版本: 0.0.1

Write-Host "🚀 开始构建 MeterReadingApp v0.0.1 Release 包..." -ForegroundColor Green

# 清理之前的构建
Write-Host "🧹 清理之前的构建..." -ForegroundColor Yellow
try {
    yarn clean
} catch {
    Write-Host "跳过清理步骤" -ForegroundColor Yellow
}

Set-Location android
.\gradlew.bat clean
Set-Location ..

# 构建 Release APK
Write-Host "📦 构建 Release APK..." -ForegroundColor Blue
Set-Location android
.\gradlew.bat assembleRelease

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ Release APK 构建成功!" -ForegroundColor Green
    Write-Host "📱 APK 位置: android\app\build\outputs\apk\release\water-meter-0.0.1.apk" -ForegroundColor Cyan
    
    # 显示APK信息
    $APKPath = "app\build\outputs\apk\release\water-meter-0.0.1.apk"
    if (Test-Path $APKPath) {
        $APKSize = (Get-Item $APKPath).Length
        $APKSizeMB = [math]::Round($APKSize / 1MB, 2)
        Write-Host "📊 APK 大小: $APKSizeMB MB" -ForegroundColor Cyan
    }
    
    Write-Host ""
    Write-Host "🎉 MeterReadingApp v0.0.1 构建完成!" -ForegroundColor Green
    Write-Host "📤 可以将 water-meter-0.0.1.apk 发送给雇主测试了" -ForegroundColor Green
} else {
    Write-Host "❌ Release APK 构建失败!" -ForegroundColor Red
    Set-Location ..
    exit 1
}

Set-Location .. 