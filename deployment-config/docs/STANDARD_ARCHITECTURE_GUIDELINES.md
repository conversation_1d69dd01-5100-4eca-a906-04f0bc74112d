# 标准架构开发规范

## 概述

本文档定义了项目开发的标准架构模式和编码规范，所有新项目必须遵循此规范，现有项目应逐步重构以符合此标准。

## ⚠️ AI助手特别提醒

**在开始任何开发任务前，AI助手必须先阅读并内化以下开发思维原则：**

1. **不要就事论事** - 思考背后的模式和可复用性
2. **不要重新发明轮子** - 优先使用现成的框架和库
3. **不要过度设计** - 最简单的解决方案往往是最好的
4. **主动识别重复代码** - 立即考虑抽象和组件化
5. **了解生态系统** - 熟悉各种框架的最佳实践

**常见错误模式（AI助手必须避免）：**
- ❌ 写48行if判断而不使用AutoMapper
- ❌ 重复编写相似的表格编辑代码而不提取组件
- ❌ 手动编辑配置文件而不使用包管理器
- ❌ 复杂的JsonElement解析而不使用简单的DTO映射

## 核心原则

### 0. 语言规范
- **代码**: 全部英文
- **注释**: 全部英文
- **命名**: 全部英文
- **与我的对话**: 中文

## 开发思维原则 (AI助手必读)

### 1. 模式识别
- **看到重复代码** → 立即想到抽象和复用
- **看到手动判断** → 想到是否有框架解决方案
- **看到类似需求** → 想到通用组件设计

### 2. 工具优先
- **优先使用成熟的框架和库**
- **了解生态系统的最佳实践**
- **避免重新发明轮子**

### 3. 简单优先
- **最简单的解决方案往往是最好的**
- **复杂性是技术债务**
- **可读性比聪明的代码更重要**

### 4. AI助手改进要点
- **主动思考**：不只是完成任务，要思考背后的模式
- **经验积累**：学习什么是好代码，什么是坏代码
- **工具熟悉**：了解各种框架和库的最佳用法
- **简单优先**：遇到问题先想最简单的解决方案

## 架构原则

### 1. 后端MVC三层架构
- **Controller层**: 只负责HTTP请求/响应处理，参数验证
- **Service层**: 业务逻辑处理，数据验证，事务管理
- **Repository层**: 纯数据访问，CRUD操作

### 2. KISS原则 (Keep It Simple, Stupid)
- 每个方法职责单一
- 避免过度设计
- 代码简洁易懂

### 3. 全局异常处理
- Controller不使用try-catch
- 统一的异常处理中间件
- 标准化的错误响应格式

## 后端架构规范

### 1. 项目结构

```
WaterMeterManagement/
├── Controllers/           # HTTP请求处理
├── Services/             # 业务逻辑
│   ├── Interfaces/       # Service接口
│   └── Implementation/   # Service实现
├── Repositories/         # 数据访问
│   ├── Interfaces/       # Repository接口
│   └── Implementation/   # Repository实现
├── DTOs/                 # 数据传输对象
├── Models/               # 实体模型
├── Enums/                # 枚举定义
├── Exceptions/           # 自定义异常
├── Middlewares/          # 中间件
└── Constants/            # 常量定义
```

### 2. 全局异常处理架构

#### 错误码枚举
```csharp
public enum ErrorCode
{
    // 通用错误 (1000-1999)
    UnknownError = 1000,
    ValidationError = 1001,
    DatabaseError = 1002,
    
    // 用户相关 (2000-2999)
    UserNotFound = 2000,
    UserAlreadyExists = 2001,
    
    // 角色相关 (3000-3999)
    RoleNotFound = 3000,
    RoleAlreadyExists = 3001,
    
    // 按业务模块递增...
}
```

#### 自定义业务异常
```csharp
public class BusinessException : Exception
{
    public ErrorCode ErrorCode { get; }
    public int HttpStatusCode { get; }
    
    public BusinessException(ErrorCode errorCode, int httpStatusCode = 400)
        : base(ErrorMessages.GetMessage(errorCode)) { }
}
```

#### 全局异常处理中间件
```csharp
public class GlobalExceptionMiddleware
{
    // 捕获所有未处理异常
    // 转换为标准ApiResponse格式
    // 设置适当的HTTP状态码
    // 记录详细错误日志
}
```

### 3. 统一API响应格式

```csharp
public class ApiResponse<T>
{
    public int Code { get; set; }           // 业务错误码
    public bool Success { get; set; }       // 操作成功标识
    public string Message { get; set; }     // 用户友好消息
    public T? Data { get; set; }           // 响应数据
    
    public static ApiResponse<T> SuccessResult(T data, string message = "Success")
    public static ApiResponse<T> ErrorResult(string message, int code = 500)
}
```

### 4. Controller层规范

#### ✅ 正确写法
```csharp
[HttpPost("user/{userId}/roles")]
public async Task<ActionResult<ApiResponse<UserRoleAssignmentResultDto>>> AssignRolesToUser(
    int userId, [FromBody] AssignRolesDto assignRolesDto)
{
    var currentUser = User.Identity?.Name ?? "System";
    var result = await _userRoleService.AssignRolesToUserAsync(userId, assignRolesDto.RoleIds, currentUser);
    
    var message = result.ErrorCount == 0 
        ? "All roles assigned successfully"
        : $"Partial success: {result.SuccessCount} succeeded, {result.ErrorCount} failed";

    return Ok(ApiResponse<UserRoleAssignmentResultDto>.SuccessResult(result, message));
}
```

#### ❌ 错误写法
```csharp
[HttpPost]
public async Task<ActionResult> SomeAction()
{
    try  // ❌ 不要在Controller中使用try-catch
    {
        // 业务逻辑 ❌ 业务逻辑应该在Service层
        var data = await _repository.GetDataAsync();
        if (data == null)
        {
            return NotFound(new { message = "Not found" }); // ❌ 不统一的响应格式
        }
        return Ok(data);
    }
    catch (Exception ex)
    {
        return StatusCode(500, ex.Message); // ❌ 手动异常处理
    }
}
```

### 5. Service层规范

```csharp
public class UserRoleService : IUserRoleService
{
    public async Task<UserRoleAssignmentResultDto> AssignRolesToUserAsync(int userId, List<int> roleIds, string assignedBy)
    {
        // 业务验证
        var user = await _userRepository.GetByIdAsync(userId);
        if (user == null)
        {
            throw new BusinessException(ErrorCode.UserNotFound, 404);
        }
        
        // 业务逻辑处理
        // 数据转换
        // 返回结果
    }
}
```

### 6. Repository层规范

```csharp
public class UserRoleRepository : IUserRoleRepository
{
    public async Task<string> UpsertUserRole(int userId, int roleId, string updatedBy)
    {
        // 纯数据访问逻辑
        // 不包含业务验证
        // 不使用try-catch（交给全局处理器）
    }
}
```

### 7. AutoMapper + EF Core 最佳实践

#### ✅ 正确的部分更新实现
```csharp
public class WaterMeterService : IWaterMeterService
{
    private readonly ApplicationDbContext _context;
    private readonly IMapper _mapper;

    public async Task<WaterMeterDto?> UpdateWaterMeterAsync(int id, UpdateWaterMeterDto updateDto)
    {
        var meter = await _context.WaterMeters.FirstOrDefaultAsync(m => m.Id == id);
        if (meter == null) return null;

        // 使用 AutoMapper 映射，自动忽略 null 值
        _mapper.Map(updateDto, meter);

        // EF Core 自动检测变更，只更新实际变化的字段
        await _context.SaveChangesAsync();
        return _mapper.Map<WaterMeterDto>(meter);
    }
}
```

#### AutoMapper 配置
```csharp
public class WaterMeterMappingProfile : Profile
{
    public WaterMeterMappingProfile()
    {
        // 创建到实体的映射，忽略null值
        CreateMap<UpdateWaterMeterDto, WaterMeter>()
            .ForAllMembers(opt => opt.Condition((src, dest, srcMember) => srcMember != null));

        // 从实体到DTO的映射
        CreateMap<WaterMeter, WaterMeterDto>();
        CreateMap<WaterMeter, WaterMeterListDto>();
    }
}
```

#### DTO 设计原则
```csharp
// Update DTO - 所有字段都应该是可空的，支持部分更新
public class UpdateWaterMeterDto
{
    public string? Location { get; set; }
    public string? Address { get; set; }
    public string? MeterType { get; set; }
    public string? Status { get; set; }
    public DateTime? InstallDate { get; set; }
    public string? AccountNumber { get; set; }
    public string? CustomerName { get; set; }
    // ... 其他字段
}
```

#### ❌ 错误的手动字段判断（垃圾代码）
```csharp
// 不要这样写！
public async Task<WaterMeterDto?> UpdateWaterMeterAsync(int id, UpdateWaterMeterDto updateDto)
{
    var meter = await _context.WaterMeters.FirstOrDefaultAsync(m => m.Id == id);
    if (meter == null) return null;

    // ❌ 大量重复的手动判断代码
    if (updateDto.Location != null) meter.Location = updateDto.Location;
    if (updateDto.Address != null) meter.Address = updateDto.Address;
    if (updateDto.MeterType != null) meter.MeterType = updateDto.MeterType;
    if (updateDto.Status != null) meter.Status = updateDto.Status;
    // ... 48行类似的垃圾代码

    await _context.SaveChangesAsync();
    return MapToDto(meter);
}
```

#### 核心优势
- **DRY原则**: 消除重复代码
- **自动映射**: AutoMapper 自动处理字段映射
- **变更跟踪**: EF Core 自动检测变更，只更新变化的字段
- **部分更新**: 只发送和更新实际变化的字段
- **类型安全**: 完整的 TypeScript 支持

## 前端架构规范

### 1. 项目结构

```
src/
├── api/                  # API调用层
├── services/             # 业务逻辑层
├── components/           # UI组件
│   ├── pages/           # 页面组件
│   └── common/          # 通用组件
├── types/               # TypeScript类型定义
├── utils/               # 工具函数
└── hooks/               # 自定义Hooks
```

### 2. API层规范

```typescript
export class UserRoleApi {
    private static readonly BASE_URL = '/userrole';
    
    static async getUserRoles(userId: number): Promise<ApiResponse<UserRoleDto[]>> {
        const response = await api.get(`${this.BASE_URL}/user/${userId}/roles`);
        return response.data;
    }
}
```

### 3. Service层规范

```typescript
export class UserRoleService {
    static async getUserRolesForTable(userId: number): Promise<UserRoleTableData[]> {
        const response = await UserRoleApi.getUserRoles(userId);
        
        if (!response.success) {
            message.error(response.message || '获取用户角色失败');
            return [];
        }
        
        // 数据转换和业务逻辑处理
        return response.data.map(role => ({
            ...role,
            key: `${role.userId}-${role.roleId}`,
            createdAt: this.formatDateTime(role.createdAt)
        }));
    }
}
```

### 4. 组件层规范

```typescript
const UserRoleManagement: React.FC = () => {
    // 只负责UI渲染和用户交互
    // 业务逻辑调用Service层
    // 不直接调用API

    const handleAssignRoles = async () => {
        const success = await UserRoleService.assignRolesToUser(formData);
        if (success) {
            // 更新UI状态
        }
    };
};
```

### 5. 组件化开发思维

#### 核心原则
- **DRY原则**: 消除重复代码，提取可复用组件
- **配置驱动**: 通过配置而非代码实现功能
- **单一职责**: 每个组件只负责一个功能
- **可组合性**: 组件可以灵活组合使用

#### ✅ 正确的组件化实现 - EditableTable
```typescript
// 类型定义
export interface EditableColumnConfig<T = any> {
  key: keyof T;
  title: string;
  type?: 'text' | 'number' | 'select' | 'date' | 'textarea';
  editable: boolean;
  width?: number;
  placeholder?: string;
  rules?: any[];
  options?: SelectOption[];
  render?: (text: any, record: T) => ReactNode;
}

// 可复用的EditableTable组件
export function EditableTable<T extends Record<string, any>>({
  data,
  columns,
  onSave,
  rowKey,
  customActions,
  ...props
}: EditableTableProps<T>) {
  // 通用的编辑逻辑
  // 自动的变更检测
  // 灵活的Actions配置
}
```

#### 使用示例 - 配置驱动
```typescript
// 水表管理页面 - 只需配置，无需重复代码
const editableColumns: EditableColumnConfig<WaterMeterListDto>[] = [
  {
    key: 'accountNumber',
    title: 'Account',
    type: 'text',
    editable: true,
    placeholder: 'Enter account number',
    render: (accountNumber: string, record) => (
      <div>
        {accountNumber && <div><strong>{accountNumber}</strong></div>}
        {record.customerName && (
          <div style={{ fontSize: '12px', color: '#666' }}>
            {record.customerName}
          </div>
        )}
      </div>
    )
  },
  {
    key: 'location',
    title: 'Location',
    type: 'text',
    editable: true,
    rules: [{ required: true, message: 'Location is required' }],
    render: (text: string) => (
      <Space><EnvironmentOutlined />{text}</Space>
    )
  },
  {
    key: 'status',
    title: 'Status',
    type: 'select',
    editable: true,
    options: [
      { label: 'Active', value: 'Active' },
      { label: 'Inactive', value: 'Inactive' }
    ]
  }
];

// 使用组件 - 简洁明了
<EditableTable
  data={meters}
  columns={editableColumns}
  onSave={handleTableSave}
  rowKey="id"
  customActions={renderCustomActions}
  pagination={paginationConfig}
/>
```

#### ❌ 错误的重复代码实现
```typescript
// 不要这样写！每个字段都要写大量重复代码
{
  title: 'Location',
  render: (text: string, record) => {
    const editing = isEditing(record);
    return editing ? (
      <Form.Item name="location" style={{ margin: 0 }} rules={[...]}>
        <Input placeholder="Enter location" />
      </Form.Item>
    ) : (
      <Space><EnvironmentOutlined />{text}</Space>
    );
  }
},
{
  title: 'Status',
  render: (text: string, record) => {
    const editing = isEditing(record);
    return editing ? (
      <Form.Item name="status" style={{ margin: 0 }} rules={[...]}>
        <Select placeholder="Select status">
          <Option value="Active">Active</Option>
          <Option value="Inactive">Inactive</Option>
        </Select>
      </Form.Item>
    ) : (
      <Tag color={text === 'Active' ? 'green' : 'red'}>{text}</Tag>
    );
  }
}
// ... 每个字段都要重复这样的代码
```

#### 组件化优势
- **代码复用**: 一次编写，多处使用
- **维护性**: 修改组件即可影响所有使用处
- **一致性**: 统一的交互体验
- **开发效率**: 新页面只需配置，无需编码
- **类型安全**: 完整的TypeScript支持

## 命名规范

### 1. 模块命名
- **Controller**: `{ModuleName}Controller`
- **Service**: `{ModuleName}Service` / `I{ModuleName}Service`
- **Repository**: `{ModuleName}Repository` / `I{ModuleName}Repository`
- **DTO**: `{ModuleName}Dto` / `{Action}{ModuleName}Dto`

### 2. API路由规范
- **RESTful设计**: `/api/{module}/{action}`
- **示例**: 
  - `GET /api/userrole/user/{userId}/roles`
  - `POST /api/userrole/user/{userId}/roles`
  - `DELETE /api/userrole/user/{userId}/role/{roleId}`

### 3. 前后端字段映射
- **后端**: PascalCase (C#标准)
- **前端**: camelCase (TypeScript标准)
- **自动转换**: 通过序列化配置处理

## 开发流程

### 1. 新功能开发
1. 定义DTO和类型
2. 创建Repository接口和实现
3. 创建Service接口和实现
4. 创建Controller
5. 创建前端API层
6. 创建前端Service层
7. 创建前端组件

### 2. 代码审查要点

#### 架构检查
- [ ] 是否遵循三层架构
- [ ] Controller是否包含业务逻辑
- [ ] 是否使用了不必要的try-catch
- [ ] 是否使用统一的ApiResponse格式
- [ ] 异常处理是否正确
- [ ] 命名是否规范

#### 开发思维检查
- [ ] **模式识别**: 是否识别并消除了重复代码？
- [ ] **工具优先**: 是否使用了现成的框架解决方案而非手动实现？
- [ ] **简单优先**: 解决方案是否足够简单？是否存在过度设计？
- [ ] **抽象复用**: 相似功能是否提取为可复用组件？
- [ ] **框架最佳实践**: 是否遵循了框架的推荐用法？

#### 具体实现检查
- [ ] 后端更新操作是否使用AutoMapper + EF Core而非手动字段判断？
- [ ] 前端是否使用可复用组件而非重复编写相似代码？
- [ ] 是否优先使用包管理器而非手动编辑配置文件？
- [ ] Update DTO是否正确设计为可空字段支持部分更新？

### 3. 重构指导
- **优先级**: 新功能 > 核心模块 > 边缘功能
- **渐进式**: 逐模块重构，不影响现有功能
- **测试**: 重构后必须通过功能测试

## 最佳实践

### 1. 异常处理
- ✅ 使用BusinessException抛出业务异常
- ✅ 让全局异常处理器处理所有异常
- ❌ 不在Controller/Repository中使用try-catch

### 2. 数据验证
- ✅ 在Service层进行业务验证
- ✅ 在Controller层进行参数验证
- ✅ 使用DataAnnotations进行模型验证

### 3. 日志记录
- ✅ 在Service层记录业务日志
- ✅ 在全局异常处理器中记录错误日志
- ✅ 使用结构化日志格式

### 4. 性能优化
- ✅ 使用异步方法 (async/await)
- ✅ 合理使用Include避免N+1查询
- ✅ 实现分页查询
- ✅ 使用缓存减少数据库访问

### 5. 后端数据更新最佳实践
- ✅ 使用AutoMapper进行对象映射，配置忽略null值
- ✅ 利用EF Core的变更跟踪，只更新实际变化的字段
- ✅ Update DTO中所有字段都设为可空，支持部分更新
- ❌ 不要手动写大量if判断来检查字段是否为null
- ❌ 不要在DTO中设置必填字段的默认值（如string.Empty）

### 6. 前端组件化最佳实践
- ✅ 提取可复用组件，消除重复代码
- ✅ 使用配置驱动的方式定义组件行为
- ✅ 组件支持自定义渲染和扩展功能
- ✅ 实现智能的变更检测，只发送实际变化的数据
- ❌ 不要为每个字段重复编写相同的编辑逻辑
- ❌ 不要在组件中硬编码业务逻辑

### 7. 包管理最佳实践
- ✅ 始终使用包管理器（npm、dotnet add package）管理依赖
- ✅ 确保包版本兼容性，避免版本冲突
- ✅ 使用锁定文件（package-lock.json、.csproj）确保环境一致性
- ❌ 不要手动编辑包配置文件（package.json、.csproj）
- ❌ 不要忽略版本兼容性警告

## 示例模板

### 后端模板
参考项目中的标准模板：
- `UserRoleController` - Controller层示例
- `UserRoleService` - Service层示例
- `UserRoleRepository` - Repository层示例
- `WaterMeterService` - AutoMapper + EF Core 最佳实践示例
- `WaterMeterMappingProfile` - AutoMapper配置示例

### 前端模板
参考项目中的标准模板：
- `UserRoleApi` - 前端API层示例
- `UserRoleService` - 前端Service层示例
- `UserRoleManagement` - 前端组件示例
- `EditableTable` - 可复用组件化设计示例
- `PaginatedTable` - 分页组件使用示例
- `ExportButton` - 导出功能组件使用示例
- `water-meters/page.tsx` - 组件化使用示例

### 组件库
项目中的可复用组件：

- `EditableTable` - 可编辑表格组件
  - 支持多种字段类型（text、number、select、date、textarea）
  - 配置驱动的列定义
  - 自动变更检测和部分更新
  - 自定义Actions支持
  - 完整的TypeScript类型支持

- `PaginatedTable` - 分页表格组件
  - 标准化分页功能，支持数据管理高分页限制（1000条/页）
  - 配置选项：标准分页（10,20,50,100）和数据管理分页（10,20,50,100,500,1000）
  - 统一的分页行为和API响应格式
  - Hook支持：usePagination、useDataManagementPagination

- `ExportButton` - 导出功能组件
  - 支持多种格式：Excel、CSV、PDF
  - 两种组件：ExportButton（简单按钮）、ExportDropdown（高级下拉菜单）
  - 预配置：ExportConfigs.WaterMeters、ExportConfigs.Baselines等
  - 自动包含搜索参数，支持自定义文件名
  - 完整的错误处理和加载状态

- `DynamicSelect` - 通用动态下拉组件
  - 基于策略模式的后端架构，支持多种数据源（database、enum、api、custom）
  - 高度可配置：支持单选/多选、搜索、参数过滤、字段映射
  - 统一API接口：`/api/dynamic-select/options`
  - 完整的TypeScript类型支持和错误处理
  - 使用示例：
    ```typescript
    // 数据库策略 - 基础用法
    <DynamicSelect
      strategy="database"
      dataSource="watermeter.township"
      placeholder="Select Township"
      onChange={handleChange}
    />

    // 枚举策略 - 多选模式
    <DynamicSelect
      strategy="enum"
      dataSource="priority"
      mode="multiple"
      placeholder="Select Priorities"
    />

    // 带参数过滤 - 级联下拉
    <DynamicSelect
      strategy="database"
      dataSource="watermeter.subarea"
      params={{ township: selectedTownship, status: 'active' }}
      placeholder="Select Sub Areas"
      disabled={!selectedTownship}
    />
    ```
  - 支持的数据源：
    - Database: `watermeter.township`, `watermeter.subarea`, `watermeter.metertype`, `route.name`, `workpackage.servicarea`
    - Enum: `meterstatus`, `priority`, `frequency`, `communicationmethod`
  - 扩展方式：在对应策略类中添加新的数据源case即可

- `NotificationManager` - 通用提示组件
  - 统一的错误信息提取和显示
  - 标准化的成功/错误/警告提示
  - 重复项检测和友好提示
  - 网络错误和权限错误处理
  -  **预定义方法**:
  - `showCreateSuccess(itemType)` - 创建成功提示
  - `showUpdateSuccess(itemType)` - 更新成功提示
  - `showDeleteSuccess(itemType)` - 删除成功提示
  - `showDuplicateWarning(itemType, itemName)` - 重复项警告
  - `showDeleteConfirm(itemName, onConfirm)` - 删除确认对话框
  - `handleError(error, context)` - 统一错误处理
  - `extractErrorMessage(error)` - 错误信息提取

所有新模块都应该按照这些模板来实现，优先使用现有的可复用组件。
