# 移动端Task分配功能实现计划

## 📊 项目概述

### 目标
实现移动端MeterReadingApp与后端WaterMeterManagement的Task分配功能集成，使Staff用户能够登录后获取分配给自己的读表任务，并执行任务提交数据。

### 核心业务流程
```
Staff登录 → 获取个人分配任务 → 执行读表任务 → 上传数据 → 同步到后端
```

## 🏗️ 现有架构分析

### MeterReadingApp 现有功能
- ✅ **完整的用户认证系统** - 本地用户管理、API认证、记住我功能
- ✅ **任务管理基础** - TaskListScreen、TaskDetailScreen、Task数据模型
- ✅ **读表功能** - MeterReadingScreen、OCR集成准备
- ✅ **数据同步框架** - SyncScreen、SyncLog模型
- ✅ **本地数据库** - SQLite、完整数据模型（User、Task、MeterReading等）
- ✅ **导航系统** - React Navigation、多屏幕支持
- ✅ **UI组件库** - NativeBase、主题支持、响应式设计

### 技术栈
- **React Native** - 跨平台移动开发
- **TypeScript** - 类型安全
- **SQLite** - 本地数据存储
- **React Navigation** - 路由管理
- **NativeBase** - UI组件库
- **Axios** - HTTP请求库

### 现有API集成
- **BaseApi配置** - `https://sicon-mnlweb.sicon.co.nz/WorkbenchLogTest/api`
- **AccountApi** - 用户认证接口
- **Basic认证** - 用户名密码编码认证

## 🎯 需求分析

### 主要功能需求
1. **任务获取** - 根据登录用户获取分配的任务列表
2. **任务执行** - 开始任务、记录进度、完成任务
3. **数据同步** - 实时同步任务状态和读数数据
4. **离线支持** - 无网络时本地缓存，有网络时批量同步
5. **状态管理** - 任务状态的双向同步

### 用户角色
- **Staff用户** - 现场读表人员，执行分配的任务

### 业务场景
1. **日常工作流程**
   - 早上：打开App，查看今日分配任务
   - 现场：选择任务，查看详情，执行读表
   - 完成：提交读数，更新任务状态
   - 晚上：同步所有数据到后端

2. **异常处理**
   - 无法读取水表：记录异常原因
   - 网络中断：离线模式继续工作
   - 设备故障：数据恢复和重试机制

## 📋 实现计划

### Phase 1: API集成基础（1天）
**目标：建立与WaterMeterManagement的API连接**

#### 1.1 后端API开发
- [ ] 创建MobileTaskController控制器
- [ ] 实现移动端专用API端点
- [ ] 配置移动端认证机制
- [ ] API文档和测试

#### 1.2 移动端API集成
- [ ] 更新BaseApi配置
- [ ] 创建TaskApi服务
- [ ] 实现移动端认证集成
- [ ] API连接测试

#### 1.3 数据模型同步
- [ ] 更新移动端Task模型
- [ ] 添加UserWorkload模型
- [ ] 数据库迁移脚本

### Phase 2: 任务数据集成（1-2天）
**目标：用真实任务数据替换mock数据**

#### 2.1 任务列表功能
- [ ] 修改TaskListScreen使用真实API
- [ ] 实现任务筛选和排序
- [ ] 添加下拉刷新功能
- [ ] 任务状态指示器

#### 2.2 任务详情功能
- [ ] 更新TaskDetailScreen显示完整信息
- [ ] 添加客户联系信息
- [ ] 集成地图位置显示
- [ ] 任务历史记录

#### 2.3 离线数据缓存
- [ ] 实现任务数据本地缓存
- [ ] 离线模式任务查看
- [ ] 数据同步状态管理

### Phase 3: 任务状态管理（1天）
**目标：实现任务状态的双向同步**

#### 3.1 任务状态更新
- [ ] 任务开始功能 - 更新为"In Progress"
- [ ] 任务完成功能 - 提交读数并标记"Completed"
- [ ] 任务失败处理 - 记录失败原因
- [ ] 状态变更历史记录

#### 3.2 读数提交集成
- [ ] 集成读数API提交
- [ ] 照片上传功能
- [ ] GPS位置记录
- [ ] 数据验证机制

#### 3.3 批量同步
- [ ] 批量状态更新
- [ ] 冲突解决机制
- [ ] 重试逻辑
- [ ] 同步进度显示

### Phase 4: 增强功能（1天）
**目标：优化用户体验**

#### 4.1 用户体验优化
- [ ] 任务通知推送
- [ ] 工作负载统计显示
- [ ] 任务完成度可视化
- [ ] 性能监控

#### 4.2 地图集成
- [ ] 任务位置地图显示
- [ ] 路线规划建议
- [ ] 附近任务显示
- [ ] GPS导航集成

#### 4.3 离线模式优化
- [ ] 智能数据预加载
- [ ] 离线地图缓存
- [ ] 后台同步优化
- [ ] 网络状态监控

## 🔌 API设计规划

### 需要新增的后端API端点

#### MobileTaskController
```csharp
[Route("api/mobile/tasks")]
public class MobileTaskController : ControllerBase
{
    // 获取当前用户的分配任务
    [HttpGet("my-assignments")]
    public async Task<ActionResult<List<MobileTaskDto>>> GetMyAssignedTasks()
    
    // 开始执行任务
    [HttpPut("{taskId}/start")]
    public async Task<ActionResult> StartTask(int taskId)
    
    // 完成任务
    [HttpPut("{taskId}/complete")]
    public async Task<ActionResult> CompleteTask(int taskId, [FromBody] CompleteTaskRequest request)
    
    // 更新任务状态
    [HttpPut("{taskId}/status")]
    public async Task<ActionResult> UpdateTaskStatus(int taskId, [FromBody] string status)
    
    // 获取任务详情
    [HttpGet("{taskId}")]
    public async Task<ActionResult<MobileTaskDetailDto>> GetTaskDetail(int taskId)
}
```

#### MobileReadingController
```csharp
[Route("api/mobile/readings")]
public class MobileReadingController : ControllerBase
{
    // 提交单个读数
    [HttpPost]
    public async Task<ActionResult<ReadingResponseDto>> SubmitReading([FromBody] MobileReadingDto reading)
    
    // 批量提交读数
    [HttpPost("batch")]
    public async Task<ActionResult<BatchReadingResponseDto>> SubmitBatchReadings([FromBody] List<MobileReadingDto> readings)
    
    // 上传读数照片
    [HttpPost("{readingId}/photos")]
    public async Task<ActionResult> UploadReadingPhotos(int readingId, List<IFormFile> photos)
}
```

#### MobileUserController
```csharp
[Route("api/mobile/users")]
public class MobileUserController : ControllerBase
{
    // 获取当前用户工作负载
    [HttpGet("my-workload")]
    public async Task<ActionResult<UserWorkloadDto>> GetMyWorkload()
    
    // 获取用户统计信息
    [HttpGet("my-stats")]
    public async Task<ActionResult<UserStatsDto>> GetMyStats()
    
    // 更新用户位置
    [HttpPost("location")]
    public async Task<ActionResult> UpdateUserLocation([FromBody] LocationUpdateDto location)
}
```

### 数据传输对象（DTOs）

#### MobileTaskDto
```csharp
public class MobileTaskDto
{
    public int Id { get; set; }
    public string Name { get; set; }
    public string Description { get; set; }
    public string Status { get; set; }
    public string Priority { get; set; }
    public string Type { get; set; }
    public DateTime? DueDate { get; set; }
    public DateTime? StartDate { get; set; }
    public string Location { get; set; }
    public string Instructions { get; set; }
    
    // 水表信息
    public string MeterNumber { get; set; }
    public string MeterType { get; set; }
    public double? LastReading { get; set; }
    public DateTime? LastReadingDate { get; set; }
    
    // 客户信息
    public string CustomerName { get; set; }
    public string CustomerPhone { get; set; }
    public string CustomerEmail { get; set; }
    public string Address { get; set; }
    
    // 位置信息
    public double? Latitude { get; set; }
    public double? Longitude { get; set; }
}
```

#### MobileReadingDto
```csharp
public class MobileReadingDto
{
    public int TaskId { get; set; }
    public int MeterId { get; set; }
    public double ReadingValue { get; set; }
    public DateTime ReadingDate { get; set; }
    public double? Latitude { get; set; }
    public double? Longitude { get; set; }
    public string ReadingMethod { get; set; } // manual, ocr, automatic
    public string Notes { get; set; }
    public int? QualityScore { get; set; }
    public List<string> PhotoPaths { get; set; }
}
```

### 认证机制
- **当前方案**: Basic Authentication（用户名/密码编码）
- **建议升级**: JWT Token认证（更安全，支持过期刷新）
- **移动端适配**: 支持离线token验证

## 🗄️ 数据库设计

### 移动端本地数据库扩展

#### Task表增强
```sql
ALTER TABLE tasks ADD COLUMN meter_number TEXT;
ALTER TABLE tasks ADD COLUMN customer_name TEXT;
ALTER TABLE tasks ADD COLUMN customer_phone TEXT;
ALTER TABLE tasks ADD COLUMN customer_email TEXT;
ALTER TABLE tasks ADD COLUMN address TEXT;
ALTER TABLE tasks ADD COLUMN latitude REAL;
ALTER TABLE tasks ADD COLUMN longitude REAL;
ALTER TABLE tasks ADD COLUMN due_date TEXT;
ALTER TABLE tasks ADD COLUMN instructions TEXT;
ALTER TABLE tasks ADD COLUMN assignment_id INTEGER;
```

#### 新增表结构
```sql
-- 用户工作负载表
CREATE TABLE user_workload (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    active_task_count INTEGER DEFAULT 0,
    completed_task_count INTEGER DEFAULT 0,
    overdue_task_count INTEGER DEFAULT 0,
    workload_percentage REAL DEFAULT 0,
    efficiency_score REAL DEFAULT 0,
    last_updated TEXT,
    sync_status TEXT DEFAULT 'pending'
);

-- 任务分配表
CREATE TABLE task_assignments (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    task_id INTEGER NOT NULL,
    assignment_id INTEGER NOT NULL,
    assigned_date TEXT,
    accepted_date TEXT,
    status TEXT DEFAULT 'assigned',
    sync_status TEXT DEFAULT 'pending',
    FOREIGN KEY (task_id) REFERENCES tasks (id)
);
```

## 🔄 数据同步策略

### 同步机制设计
1. **启动同步** - App启动时检查网络，同步最新任务
2. **实时同步** - 任务状态变更时立即尝试同步
3. **批量同步** - 网络恢复时批量上传待同步数据
4. **冲突解决** - 服务器时间戳优先原则

### 离线支持策略
- **任务缓存** - 预加载一周内的分配任务
- **读数存储** - 本地SQLite存储所有读数数据
- **照片管理** - 本地存储照片，网络恢复时上传
- **状态队列** - 离线状态变更排队等待同步

## 🚀 部署和测试计划

### 开发环境配置
1. **后端API开发** - WaterMeterManagement项目
2. **移动端开发** - MeterReadingApp项目
3. **API测试** - Postman/Swagger测试套件
4. **集成测试** - 移动端与后端联调

### 测试策略
1. **单元测试** - API端点测试、数据模型测试
2. **集成测试** - 端到端任务流程测试
3. **性能测试** - 大量任务数据处理性能
4. **离线测试** - 网络中断场景测试
5. **用户验收测试** - 实际使用场景验证

## 📈 成功指标

### 功能指标
- [ ] Staff用户能够成功登录并获取分配任务
- [ ] 任务状态能够实时同步到后端
- [ ] 读数数据能够准确提交并存储
- [ ] 离线模式下功能正常运行
- [ ] 数据同步成功率 > 95%

### 性能指标
- [ ] 任务列表加载时间 < 3秒
- [ ] 读数提交响应时间 < 5秒
- [ ] App启动时间 < 10秒
- [ ] 离线数据同步完成时间 < 30秒
- [ ] 内存使用量 < 100MB

### 用户体验指标
- [ ] 界面操作流畅，无卡顿
- [ ] 错误提示清晰明确
- [ ] 离线模式用户感知良好
- [ ] 数据丢失率 = 0%

## 🎯 风险评估与缓解

### 技术风险
1. **API兼容性** - 移动端与后端版本不匹配
   - 缓解：版本控制、向后兼容设计
2. **数据同步冲突** - 离线数据与服务器数据冲突
   - 缓解：时间戳机制、冲突解决策略
3. **性能问题** - 大量任务数据处理慢
   - 缓解：分页加载、数据优化

### 业务风险
1. **数据丢失** - 网络问题导致数据丢失
   - 缓解：本地持久化、重试机制
2. **用户体验** - 复杂操作影响工作效率
   - 缓解：简化流程、用户培训

## 📅 里程碑时间表

### Week 1
- **Day 1**: Phase 1 - API集成基础
- **Day 2-3**: Phase 2 - 任务数据集成
- **Day 4**: Phase 3 - 任务状态管理
- **Day 5**: Phase 4 - 增强功能

### Week 2
- **Day 1-2**: 集成测试和调试
- **Day 3**: 性能优化
- **Day 4**: 用户验收测试
- **Day 5**: 文档完善和部署准备

## 📚 参考文档

- [WaterMeterManagement API文档](./TASK_ASSIGNMENT_API.md)
- [移动端架构设计](./MOBILE_ARCHITECTURE.md)
- [数据同步设计](./DATA_SYNC_DESIGN.md)
- [测试计划](./MOBILE_TESTING_PLAN.md)

---

*文档版本: v1.0*  
*创建日期: 2024-01-XX*  
*最后更新: 2024-01-XX* 