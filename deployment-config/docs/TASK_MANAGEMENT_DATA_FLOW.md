# Task任务管理系统数据流向文档

## 概述

本文档详细描述了水表抄表任务管理系统的完整数据流向，包括后端管理系统、移动端应用、以及各个组件之间的交互关系。

## 系统架构概览

```
┌─────────────────────┐    ┌─────────────────────┐    ┌─────────────────────┐
│                     │    │                     │    │                     │
│   管理员前端        │    │   .NET Core 后端    │    │   移动端应用        │
│ (water-meter-admin) │    │(WaterMeterManagement)│    │ (MeterReadingApp)   │
│                     │    │                     │    │                     │
│ - Next.js           │    │ - TaskController    │    │ - React Native      │
│ - Task管理界面      │◄──►│ - TaskService       │◄──►│ - TaskService       │
│ - JWT认证           │    │ - PostgreSQL        │    │ - SICON API登录     │
│                     │    │                     │    │                     │
└─────────────────────┘    └─────────────────────┘    └─────────────────────┘
                                      │
                                      ▼
                           ┌─────────────────────┐
                           │                     │
                           │   PostgreSQL 数据库 │
                           │                     │
                           │ - Tasks表           │
                           │ - Users表           │
                           │ - 其他相关表        │
                           │                     │
                           └─────────────────────┘
```

## 核心组件说明

### 1. 后端系统 (WaterMeterManagement)

#### 1.1 核心模型

**WorkTask模型** (`Models/WorkTask.cs`)：
```csharp
public class WorkTask : BaseEntity
{
    public int Id { get; set; }
    public string Name { get; set; }
    public string Description { get; set; }
    public string Status { get; set; }  // assigned, in_progress, completed, paused
    public string Priority { get; set; } // low, medium, high
    public string Type { get; set; }
    public DateTime? DueDate { get; set; }
    public DateTime? StartDate { get; set; }
    public DateTime? CompletedDate { get; set; }
    public double? EstimatedHours { get; set; }
    public double? ActualHours { get; set; }
    public double ProgressPercentage { get; set; }
    public string Location { get; set; }
    public string Instructions { get; set; }
    public string Notes { get; set; }
    public string AssignedUserId { get; set; }  // JWT token中的用户ID
    public string CreatedBy { get; set; }
    
    // 继承自BaseEntity：
    // - CreatedAt, UpdatedAt, CreatedBy, UpdatedBy, IsDeleted
}
```

#### 1.2 API接口

**TaskController** (`Controllers/TaskController.cs`)：
- `GET /api/task` - 获取任务列表（支持分页、排序、过滤）
- `GET /api/task/{id}` - 获取单个任务详情
- `POST /api/task` - 创建新任务
- `PUT /api/task/{id}` - 更新任务
- `PATCH /api/task/{id}/status` - 更新任务状态
- `PATCH /api/task/{id}/progress` - 更新任务进度
- `DELETE /api/task/{id}` - 删除任务
- `GET /api/task/statistics` - 获取任务统计信息
- `GET /api/task/overdue` - 获取过期任务

#### 1.3 数据传输对象

**TaskDto** (`DTOs/TaskDto.cs`)：
```csharp
public class TaskDto
{
    public int Id { get; set; }
    public string Name { get; set; }
    public string Description { get; set; }
    public string Status { get; set; }
    public string Priority { get; set; }
    public string Type { get; set; }
    public string AssignedTo { get; set; }
    public string CreatedBy { get; set; }
    public DateTime? DueDate { get; set; }
    public DateTime? StartDate { get; set; }
    public DateTime? CompletedDate { get; set; }
    public double? EstimatedHours { get; set; }
    public double? ActualHours { get; set; }
    public double ProgressPercentage { get; set; }
    public string Location { get; set; }
    public string Instructions { get; set; }
    public string Notes { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
}
```

### 2. 前端管理系统 (water-meter-admin)

#### 2.1 任务管理界面

**主要功能**：
- 任务列表查看（分页、排序、过滤）
- 创建新任务
- 编辑任务信息
- 分配任务给用户
- 查看任务统计信息
- 任务状态管理

#### 2.2 API调用

**TaskService** (`src/services/TaskService.ts`)：
```typescript
class TaskService {
  async getTasks(params: TaskSearchParams): Promise<TaskSearchResult>
  async getTaskById(id: number): Promise<TaskDto>
  async createTask(task: CreateTaskDto): Promise<TaskDto>
  async updateTask(id: number, task: UpdateTaskDto): Promise<TaskDto>
  async deleteTask(id: number): Promise<void>
  async updateTaskStatus(id: number, status: string): Promise<void>
  async getTaskStatistics(): Promise<TaskStatistics>
}
```

### 3. 移动端应用 (MeterReadingApp)

#### 3.1 认证流程

**双重认证机制**：
1. **SICON API登录** - 用于用户身份验证
2. **JWT Token获取** - 用于后端API调用

```typescript
// 认证流程
async login(username: string, password: string) {
  // 1. 调用SICON API验证用户
  const siconResponse = await fetch('https://sicon-mnlweb.sicon.co.nz/WorkbenchLogTest/api/Account/Login', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ username, password })
  });

  if (siconResponse.ok) {
    // 2. 获取本地JWT token
    const jwtResponse = await this.localApi.post('/Auth/login', { username, password });
    const jwtToken = jwtResponse.data.token;
    
    // 3. 保存JWT token用于后续API调用
    await AsyncStorage.setItem('jwt_token', jwtToken);
    
    return { success: true, token: jwtToken };
  }
}
```

#### 3.2 任务数据处理

**数据转换层**：
移动端使用不同的数据结构，需要进行格式转换：

```typescript
// 后端TaskDto -> 移动端MobileTaskDto
function convertToMobileTaskDto(backendTask: TaskDto): MobileTaskDto {
  return {
    Id: backendTask.id,
    Name: backendTask.name || 'Unnamed Task',
    Status: backendTask.status || 'assigned',
    Priority: backendTask.priority || 'medium',
    
    // 水表信息（模拟数据）
    MeterId: backendTask.id,
    MeterNumber: `M${backendTask.id.toString().padStart(6, '0')}`,
    MeterType: 'Standard',
    LastReading: 12450,
    LastReadingDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
    
    // 客户信息（模拟数据）
    CustomerName: `Customer ${backendTask.id}`,
    CustomerPhone: '+64 21 123 4567',
    CustomerEmail: `customer${backendTask.id}@example.com`,
    Address: backendTask.location || `${backendTask.id} Main Street, Christchurch`,
    
    // 位置信息（模拟数据）
    Latitude: -43.5321 + (Math.random() - 0.5) * 0.1,
    Longitude: 172.6362 + (Math.random() - 0.5) * 0.1,
    
    // 其他字段映射...
  };
}
```

#### 3.3 移动端TaskService

**TaskService** (`src/services/TaskService.ts`)：
```typescript
class TaskService {
  // 获取我的任务分配
  async getMyAssignments(
    status?: string,
    includeCompleted?: boolean,
    page: number = 1,
    pageSize: number = 50
  ): Promise<MobileTaskDto[]>

  // 获取任务详情
  async getTaskDetail(taskId: number): Promise<MobileTaskDto | null>
  async getTaskDetails(taskId: number): Promise<MobileTaskDetailDto | null>

  // 任务操作
  async updateTaskStatus(taskId: number, status: string, notes?: string): Promise<boolean>
  async updateTaskProgress(taskId: number, progressPercentage: number): Promise<boolean>
  async startTask(taskId: number): Promise<boolean>

  // 统计信息
  async getTaskStatistics(): Promise<any>
  async getOverdueTasks(): Promise<MobileTaskDto[]>
}
```

## 完整数据流向

### 1. 任务创建流程

```
管理员 → 前端界面 → POST /api/task → TaskController → TaskService → 数据库
  ↓
保存任务信息，状态为'assigned'
```

### 2. 任务分配流程

```
管理员 → 前端界面 → PUT /api/task/{id} → 更新AssignedUserId → 数据库
  ↓
任务被分配给特定用户
```

### 3. 移动端获取任务流程

```
移动端用户 → 登录SICON API → 获取JWT Token → GET /api/task → TaskController
  ↓
过滤当前用户的任务 → 返回TaskDto[] → 转换为MobileTaskDto[] → 显示在移动端
```

### 4. 任务执行流程

```
移动端用户 → 选择任务 → 点击"开始任务" → PATCH /api/task/{id}/status
  ↓
更新状态为'in_progress' → 进入抄表界面 → 完成抄表 → 更新状态为'completed'
```

## API调用时序图

```
移动端                    后端API                   数据库
  │                         │                        │
  │ 1. 登录SICON API        │                        │
  │ 2. 获取JWT Token        │                        │
  │                         │                        │
  │ 3. GET /api/task       │                        │
  │ ──────────────────────► │                        │
  │                         │ 4. 查询用户任务         │
  │                         │ ──────────────────────► │
  │                         │ 5. 返回任务列表         │
  │                         │ ◄────────────────────── │
  │ 6. 返回TaskDto[]       │                        │
  │ ◄────────────────────── │                        │
  │ 7. 转换为MobileTaskDto[] │                        │
  │                         │                        │
  │ 8. 点击任务详情         │                        │
  │ 9. GET /api/task/{id}  │                        │
  │ ──────────────────────► │                        │
  │                         │ 10. 查询任务详情        │
  │                         │ ──────────────────────► │
  │                         │ 11. 返回任务详情        │
  │                         │ ◄────────────────────── │
  │ 12. 返回TaskDto        │                        │
  │ ◄────────────────────── │                        │
  │ 13. 转换为MobileTaskDetailDto │                  │
  │                         │                        │
  │ 14. 开始任务           │                        │
  │ 15. PATCH /api/task/{id}/status │                │
  │ ──────────────────────► │                        │
  │                         │ 16. 更新任务状态        │
  │                         │ ──────────────────────► │
  │                         │ 17. 更新成功           │
  │                         │ ◄────────────────────── │
  │ 18. 返回成功           │                        │
  │ ◄────────────────────── │                        │
```

## 关键配置说明

### 1. 网络配置

**移动端API配置** (`src/api/apiConfig.ts`)：
```typescript
const API_CONFIG = {
  // 生产环境使用用户IP地址
  BASE_URL: Platform.OS === 'android' 
    ? 'http://***************:5000/api'  // Android模拟器
    : 'http://localhost:5000/api',      // iOS模拟器
  
  // SICON API配置
  SICON_BASE_URL: 'https://sicon-mnlweb.sicon.co.nz/WorkbenchLogTest/api',
  
  // 超时配置
  TIMEOUT: 10000,
  
  // 重试配置
  RETRY_ATTEMPTS: 3,
  RETRY_DELAY: 1000,
};
```

### 2. 后端服务器配置

**启动配置** (`Properties/launchSettings.json`)：
```json
{
  "profiles": {
    "WaterMeterManagement": {
      "commandName": "Project",
      "launchBrowser": true,
      "launchUrl": "swagger",
      "environmentVariables": {
        "ASPNETCORE_ENVIRONMENT": "Development"
      },
      "applicationUrl": "http://0.0.0.0:5000"  // 绑定到所有网络接口
    }
  }
}
```

### 3. 数据库配置

**连接字符串** (`appsettings.json`)：
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Host=localhost;Database=WaterMeterDB;Username=postgres;Password=your_password"
  }
}
```

## 数据同步机制

### 1. 实时数据更新

- 前端管理系统创建/更新任务后，移动端需要刷新任务列表
- 移动端更新任务状态后，前端管理系统可以看到最新状态
- 所有数据变更都会更新数据库中的审计字段（UpdatedAt, UpdatedBy等）

### 2. 离线支持（规划中）

- 移动端可以缓存任务列表
- 支持离线更新任务状态
- 网络恢复后自动同步数据变更

### 3. 冲突解决

- 使用时间戳进行冲突检测
- 后更新的数据优先原则
- 关键冲突需要人工处理

## 性能优化

### 1. 分页加载

- 任务列表支持分页加载
- 移动端默认每页50条记录
- 支持上拉加载更多

### 2. 缓存策略

- 移动端缓存任务列表数据
- 任务详情页面缓存
- 网络请求结果缓存

### 3. 数据库优化

- 为常用查询字段建立索引
- 软删除机制（IsDeleted字段）
- 审计日志自动记录

## 错误处理

### 1. 网络错误

- 自动重试机制
- 降级处理（显示缓存数据）
- 友好的错误提示

### 2. 认证错误

- JWT token过期自动刷新
- 认证失败返回登录页面
- 权限不足的友好提示

### 3. 数据错误

- 数据验证失败的详细错误信息
- 数据转换错误的处理
- 数据库操作失败的回滚

## 安全考虑

### 1. 认证授权

- 双重认证机制（SICON + JWT）
- JWT token有效期管理
- 用户权限控制

### 2. 数据传输

- API调用使用HTTPS（生产环境）
- 敏感数据加密传输
- 请求签名验证

### 3. 数据存储

- 密码哈希存储
- 敏感数据加密存储
- 审计日志记录

## 监控和日志

### 1. 应用日志

- 详细的API调用日志
- 错误堆栈跟踪
- 性能指标记录

### 2. 数据库日志

- SQL查询日志
- 数据变更日志
- 性能慢查询日志

### 3. 监控指标

- API响应时间
- 错误率统计
- 用户活跃度

## 部署架构

### 1. 开发环境

```
开发机器 (localhost)
├── 后端服务 (http://localhost:5000)
├── 前端管理系统 (http://localhost:3000)
├── PostgreSQL数据库 (localhost:5432)
└── 移动端开发 (React Native Metro)
```

### 2. 生产环境

```
生产服务器
├── 后端服务 (https://api.yourdomain.com)
├── 前端管理系统 (https://admin.yourdomain.com)
├── PostgreSQL数据库 (内网)
└── 移动端应用 (App Store/Google Play)
```

## 扩展性考虑

### 1. 水平扩展

- 后端API服务可以多实例部署
- 数据库支持读写分离
- 负载均衡器分发请求

### 2. 功能扩展

- 模块化设计，便于添加新功能
- 插件化架构支持
- 微服务架构演进

### 3. 数据扩展

- 支持分库分表
- 历史数据归档
- 大数据分析支持

## 总结

本Task任务管理系统采用了现代化的分布式架构，前后端分离，移动端与后端通过RESTful API交互。系统支持完整的任务生命周期管理，从创建、分配、执行到完成，每个环节都有相应的数据流转和状态管理。

通过双重认证机制、数据转换层、错误处理机制等设计，确保了系统的安全性、可靠性和用户体验。同时，系统具有良好的扩展性和维护性，能够满足未来的业务发展需求。 