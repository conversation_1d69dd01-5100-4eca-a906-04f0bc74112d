# 审计字段管理架构 (Audit Field Management Architecture)

## 🎯 设计理念

本系统采用基于**BaseEntity继承 + DbContext拦截**的优雅方案，充分利用面向对象继承特性，在数据层自动管理所有审计字段，实现真正的**关注点分离**。

## 🏗️ 核心架构

### 1. BaseEntity - 继承的力量
```csharp
public abstract class BaseEntity
{
    public int Id { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
    public bool IsDeleted { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
    public string UpdatedBy { get; set; } = string.Empty;
}
```

**设计优点**：
- ✅ **继承统一管理**：所有实体自动拥有审计字段
- ✅ **OOP原则**：符合面向对象设计原则
- ✅ **类型安全**：编译时检查，运行时保障
- ✅ **IDE智能提示**：完整的IntelliSense支持

### 2. 实体继承 - 自动获得审计能力
```csharp
public class WorkPackage : BaseEntity
{
    // 业务字段
    public string Name { get; set; } = string.Empty;
    public string? Description { get; set; }
    
    // 审计字段自动继承，无需手动定义
    // CreatedAt, UpdatedAt, CreatedBy, UpdatedBy, IsDeleted
}
```

### 3. DbContext自动拦截 - 数据层统一处理
```csharp
public class ApplicationDbContext : DbContext
{
    private readonly IHttpContextAccessor _httpContextAccessor;

    public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
    {
        var currentUser = GetCurrentUser();
        var now = DateTime.UtcNow;

        foreach (var entry in ChangeTracker.Entries<BaseEntity>())
        {
            switch (entry.State)
            {
                case EntityState.Added:
                    entry.Entity.CreatedAt = now;
                    entry.Entity.CreatedBy = currentUser;
                    entry.Entity.UpdatedAt = now;
                    entry.Entity.UpdatedBy = currentUser;
                    entry.Entity.IsDeleted = false;
                    break;
                    
                case EntityState.Modified:
                    entry.Property(e => e.CreatedAt).IsModified = false;
                    entry.Property(e => e.CreatedBy).IsModified = false;
                    entry.Entity.UpdatedAt = now;
                    entry.Entity.UpdatedBy = currentUser;
                    break;
            }
        }

        return await base.SaveChangesAsync(cancellationToken);
    }
}
```

## 🔄 完整工作流程

```mermaid
graph TD
    A[前端提交业务数据] --> B[Controller接收DTO]
    B --> C[Service处理业务逻辑]
    C --> D[创建/更新Entity]
    D --> E[调用SaveChangesAsync]
    E --> F[DbContext自动拦截]
    F --> G[检测Entity状态]
    G --> H{新增还是更新?}
    H -->|新增| I[设置Created + Updated字段]
    H -->|更新| J[保护Created字段<br/>更新Updated字段]
    I --> K[保存到数据库]
    J --> K
    K --> L[返回完整数据给前端]
```

## 🎨 各层职责清晰

### 前端层 (Frontend)
```typescript
interface UpdateWorkPackageDto {
  id: number;
  name: string;
  description?: string;
  // 只关心业务字段，不涉及审计字段
  // 审计字段完全由后端自动管理
}
```

### Controller层 (API)
```csharp
[HttpPut("{id}")]
public async Task<ActionResult<WorkPackageDto>> UpdateWorkPackage(int id, [FromBody] UpdateWorkPackageDto updateDto)
{
    // 直接处理业务逻辑，无需关心审计字段
    var workPackage = await _workPackageService.UpdateWorkPackageAsync(updateDto);
    return Ok(workPackage);
}
```

### Service层 (Business Logic)
```csharp
public async Task<WorkPackageDto> UpdateWorkPackageAsync(UpdateWorkPackageDto updateDto)
{
    var entity = await _context.WorkPackages.FindAsync(updateDto.Id);
    
    // 只更新业务字段
    entity.Name = updateDto.Name;
    entity.Description = updateDto.Description;
    
    // 审计字段由DbContext自动管理
    await _context.SaveChangesAsync();
    
    return MapToDto(entity);
}
```

### 数据层 (Data Access)
```csharp
// 自动拦截所有BaseEntity的保存操作
// 无需任何手动干预，完全透明
```

## 🔧 技术实现细节

### 1. 用户上下文获取
```csharp
private string GetCurrentUser()
{
    var user = _httpContextAccessor?.HttpContext?.User;
    
    if (user?.Identity?.IsAuthenticated == true)
    {
        return user.FindFirst(ClaimTypes.Name)?.Value ??
               user.FindFirst(ClaimTypes.NameIdentifier)?.Value ??
               user.FindFirst("username")?.Value ??
               user.FindFirst("sub")?.Value ??
               user.Identity.Name ??
               "System";
    }
    
    return "System";
}
```

### 2. 创建操作自动处理
- ✅ **CreatedAt**: 当前UTC时间
- ✅ **CreatedBy**: 当前认证用户
- ✅ **UpdatedAt**: 同CreatedAt
- ✅ **UpdatedBy**: 同CreatedBy
- ✅ **IsDeleted**: false

### 3. 更新操作智能保护
- ✅ **保护CreatedAt**: 标记为不可修改
- ✅ **保护CreatedBy**: 标记为不可修改
- ✅ **更新UpdatedAt**: 当前UTC时间
- ✅ **更新UpdatedBy**: 当前认证用户

## 🚀 架构优势

### 1. 开发效率优势
- **零配置**: 继承BaseEntity即获得审计能力
- **零维护**: 审计逻辑集中在DbContext，全局统一
- **零错误**: 消除手动设置审计字段的人为错误

### 2. 架构优雅性
- **关注点分离**: 前端专注业务，后端专注审计
- **单一职责**: 每层只处理自己的职责
- **开放封闭**: 对扩展开放，对修改封闭

### 3. 性能优势
- **批量处理**: 一次SaveChanges处理所有实体
- **最小开销**: 仅在保存时拦截，运行时零额外成本
- **内存友好**: 无额外对象创建，无内存泄漏

### 4. 维护优势
- **一处修改全局生效**: 修改DbContext影响所有实体
- **易于测试**: 清晰的边界，易于单元测试
- **易于调试**: 集中的审计逻辑，问题排查简单

## 📋 最佳实践

### 1. 实体设计
```csharp
// ✅ 正确：继承BaseEntity
public class YourEntity : BaseEntity
{
    public string BusinessField { get; set; } = string.Empty;
    // 审计字段自动继承
}

// ❌ 错误：手动定义审计字段
public class YourEntity
{
    public string BusinessField { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; } // 不要手动定义
}
```

### 2. DTO设计
```csharp
// ✅ 正确：只包含业务字段
public class UpdateEntityDto
{
    public string BusinessField { get; set; } = string.Empty;
    // 不包含任何审计字段
}

// ❌ 错误：包含审计字段
public class UpdateEntityDto
{
    public string BusinessField { get; set; } = string.Empty;
    public string UpdatedBy { get; set; } = string.Empty; // 不要包含
}
```

### 3. Service实现
```csharp
// ✅ 正确：只处理业务逻辑
public async Task<EntityDto> UpdateAsync(UpdateEntityDto dto)
{
    var entity = await _context.Entities.FindAsync(dto.Id);
    entity.BusinessField = dto.BusinessField;
    
    await _context.SaveChangesAsync(); // 审计字段自动设置
    
    return MapToDto(entity);
}

// ❌ 错误：手动设置审计字段
public async Task<EntityDto> UpdateAsync(UpdateEntityDto dto)
{
    var entity = await _context.Entities.FindAsync(dto.Id);
    entity.BusinessField = dto.BusinessField;
    entity.UpdatedBy = GetCurrentUser(); // 不要手动设置
    entity.UpdatedAt = DateTime.UtcNow;  // 不要手动设置
    
    await _context.SaveChangesAsync();
}
```

## 🔍 与其他方案对比

| 特性 | BaseEntity继承方案 | Attribute拦截方案 | 手动设置方案 |
|------|-------------------|-------------------|--------------|
| **代码简洁性** | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ | ⭐ |
| **类型安全性** | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐ |
| **维护成本** | ⭐⭐⭐⭐⭐ | ⭐⭐ | ⭐ |
| **性能表现** | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐ |
| **扩展性** | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐ |
| **OOP原则** | ⭐⭐⭐⭐⭐ | ⭐⭐ | ⭐ |

## 🎯 总结

**BaseEntity + DbContext拦截**方案是经典且优雅的企业级解决方案，它：

1. **充分利用继承**：体现面向对象设计的精髓
2. **数据层统一管理**：在最合适的层面处理审计逻辑
3. **零侵入性**：对现有代码几乎无影响
4. **高性能**：最小的运行时开销
5. **易维护**：集中管理，全局一致

这种方案被大量企业级应用采用，是审计字段管理的**最佳实践**。 