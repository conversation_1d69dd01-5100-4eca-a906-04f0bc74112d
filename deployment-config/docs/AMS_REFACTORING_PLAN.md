# AMS Data Model Refactoring Plan

## Overview

This document outlines the comprehensive refactoring plan to align our water meter management system with AMS (Asset Management System) export file structure. Instead of creating new models, we are **enhancing existing models** to be fully compatible with AMS data while maintaining backward compatibility.

## AMS Excel File Structure Analysis

### Source File: `AMS Export Meter Data Used to Create Schedule.xlsx`

**Sheet 1: "Rolleston Area 1 MASTER"**
- Asset ID, Meter Number, Account Number, Book Number, Unit
- Road Number, Road Name, Township, Sub Area
- Last Read, Recent Change, Subd, Date Of Read, Read
- Can't Read, Condition, Comments

**Sheet 2: "Rolleston Area 1 Enter miss"**
- Missing/anomalous readings data

**Sheet 3: "Data Check"** 
- Route assignments (Route18, Route 10A, Route17, etc.)
- Account Numbers organized by routes

## Refactoring Phases

### Phase 1: Backend Data Models (Week 1-2)

#### 1.1 Enhanced Existing Models
- **Meter.cs** - Enhanced with AMS fields while maintaining backward compatibility
- **Reading.cs** - Enhanced reading records with full AMS and mobile compatibility
- **Route.cs** - Enhanced route management from "Data Check" sheet
- **TaskAssignment.cs** - Enhanced task assignments (existing Assignment model)
- **BaselineRecord.cs** - Enhanced baseline data management
- **ReadingPhoto.cs** - Enhanced photo management for readings

#### 1.2 Database Migration Strategy
- Create new tables with AMS prefix
- Data migration scripts from existing models
- Maintain backward compatibility during transition
- Foreign key relationships and constraints

### Phase 2: API Controllers Refactoring (Week 2-3)

#### 2.1 New Controllers
- **AmsMeterController.cs** - Meter CRUD operations
- **AmsReadingController.cs** - Reading management
- **AmsImportController.cs** - Excel/CSV import functionality
- **AmsRouteController.cs** - Route management
- **AmsTaskController.cs** - Task assignment and scheduling

#### 2.2 Import/Export Services
- **AmsExcelImportService.cs** - Multi-sheet Excel processing
- **AmsDataMappingService.cs** - Data transformation utilities
- **AmsValidationService.cs** - Data validation and error handling

### Phase 3: Frontend Admin Panel (Week 3-4)

#### 3.1 TypeScript Interfaces
- Update `/types/water-meter.ts` with AMS-aligned interfaces
- New interfaces for route management and imports
- Comprehensive DTOs for all CRUD operations

#### 3.2 New Admin Pages
- AMS Data Import wizard
- Route management interface  
- Enhanced meter management with AMS fields
- Reading validation and anomaly management

### Phase 4: Mobile App Data Models (Week 4-5)

#### 4.1 Mobile Database Schema
- Update SQLite schema for AMS compatibility
- Offline data sync improvements
- Enhanced reading validation

#### 4.2 Mobile Services
- Updated sync services for AMS data
- Enhanced OCR integration with AMS validation
- Improved offline queue management

### Phase 5: Data Migration & Testing (Week 5-6)

#### 5.1 Migration Scripts
- Export existing data to AMS format
- Validate data integrity
- Performance testing with large datasets

#### 5.2 Integration Testing
- End-to-end testing scenarios
- Mobile-to-backend sync validation
- AMS import/export workflows

## Implementation Priorities

### High Priority (Must Have)
1. AmsMeter and AmsReading models
2. AMS Excel import functionality
3. Route management from "Data Check" sheet
4. Basic CRUD operations

### Medium Priority (Should Have)
1. Advanced validation and anomaly detection
2. Enhanced mobile sync
3. Comprehensive admin interface
4. Reporting and analytics

### Low Priority (Nice to Have)
1. Advanced route optimization
2. Predictive anomaly detection
3. Integration with external mapping services
4. Advanced reporting dashboards

## Risk Mitigation

### Data Loss Prevention
- Comprehensive backup strategy
- Rollback procedures for each phase
- Parallel system operation during transition

### Performance Considerations
- Database indexing strategy for AMS fields
- Optimized queries for large datasets
- Caching strategies for frequently accessed data

### Compatibility Assurance
- Maintain existing API endpoints during transition
- Gradual migration of mobile apps
- Backward compatibility layers

## Success Criteria

### Technical Metrics
- 100% data migration success rate
- <2 second response time for AMS data queries
- <5% increase in mobile app sync time
- Zero data loss during migration

### Business Metrics
- Successful import of AMS Excel files
- Reduced manual data entry by 80%
- Improved data accuracy and consistency
- Enhanced route management efficiency

## Timeline Summary

```
Week 1-2:  Backend Models & Database Migration
Week 3:    API Controllers & Services  
Week 4:    Frontend Admin Interface
Week 5:    Mobile App Updates
Week 6:    Testing, Validation & Deployment
```

## Next Steps

1. **Immediate**: Create AMS data models
2. **Day 2-3**: Implement Excel import service
3. **Week 1**: Complete backend infrastructure
4. **Week 2**: Begin frontend integration
5. **Week 3**: Mobile app updates
6. **Week 4**: End-to-end testing

This refactoring will position our system for seamless AMS integration while maintaining all existing functionality and improving data management capabilities. 