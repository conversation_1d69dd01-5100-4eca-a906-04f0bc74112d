# 📋 Work Package Management 实现方案文档

## 📊 文档概述

**文档目的**: 记录Work Package Management模块的完整实现方案，包括界面设计、技术架构和实现细节  
**创建时间**: 2025年6月  
**对应用户故事**: User Story 3 - 可重用Work Package计划管理  
**设计决策**: 基于用户体验优化，采用向导式创建流程

---

## 🎯 业务需求分析

### User Story 3 原始需求
```
作为Administrator，我希望通过导出水表CSV、添加计划名称后重新上传的方式创建Work Package，
同时支持地图预览验证，以便快速创建和管理可重用的抄表工作包。
```

### 扩展需求（会议决定）
- **Excel工作流**: 导出 → 编辑 → 上传创建
- **在线创建**: 直接在网页筛选 → 在线编辑 → 立即保存
- **地图预览**: 验证Work Package内水表的地理分布
- **可重用性**: Work Package可以重复使用和修订

---

## 🖥️ 界面设计方案

### 1. 主界面 - Work Package列表页

```
Work Package Management
┌─────────────────────────────────────────────────────────────┐
│ [+ 新建Work Package]  [批量操作]  [导出报表]               │
├─────────────────────────────────────────────────────────────┤
│ 搜索: [________________] 状态: [全部▼] 创建时间: [________] │
├─────────────────────────────────────────────────────────────┤
│ Work Package列表                                            │
├──────────────┬──────────┬──────────┬──────────┬─────────────┤
│ 名称         │ 创建时间  │ 水表数量  │ 状态     │ 操作        │
├──────────────┼──────────┼──────────┼──────────┼─────────────┤
│ 北区1月抄表   │ 2024-01-15│ 125个    │ 进行中   │ [查看][编辑] │
│ 南区应急检查  │ 2024-01-14│ 45个     │ 已完成   │ [查看][编辑] │
│ 商业区季度检查│ 2024-01-10│ 89个     │ 待分配   │ [查看][编辑] │
└──────────────┴──────────┴──────────┴──────────┴─────────────┘
```

**设计说明**:
- 主界面专注于Work Package的管理
- 不显示具体水表数据，保持界面简洁
- 提供搜索、筛选、分页功能

### 2. 新建Work Package向导

#### Step 1: 创建方式选择
```
┌─────────────新建Work Package - 选择创建方式─────────────┐
│                                                        │
│ 请选择Work Package创建方式:                             │
│                                                        │
│ ○ Excel导入创建 (推荐批量创建)                          │
│   └─ 适合: 大量水表、多个Work Package、离线编辑         │
│   └─ 流程: 导出模板 → Excel编辑 → 上传创建             │
│                                                        │
│ ○ 在线创建 (快速单个创建)                              │
│   └─ 适合: 少量水表、单个Work Package、即时创建         │
│   └─ 流程: 筛选水表 → 在线设置 → 立即保存              │
│                                                        │
│                  [继续]    [取消]                       │
└────────────────────────────────────────────────────────┘
```

#### Step 2A: Excel导入向导 (如果选择Excel方式)

**步骤2A-1: 筛选和导出水表**
```
┌─────────────Excel创建向导 - 第1步: 选择水表─────────────┐
│ 筛选条件设置:                                           │
│ ├─ 地区: [全部▼] 状态: [未分配▼] 类型: [全部▼]          │
│ ├─ 地址包含: [_____________] GPS范围: [设置范围]        │
│ └─ 安装日期: [从____到____] 上次抄表: [从____到____]    │
│ ─────────────────────────────────────────────────────── │
│ 符合条件的水表预览 (共234个):                           │
│ ☑️ 全选   显示前50个，其余将包含在导出中                │
│ ├─ 19M1567747 | 123 Main Street | 未分配 | 数字表      │
│ ├─ 17M245630  | 456 Oak Avenue  | 未分配 | 机械表      │
│ ├─ 18M334567  | 789 Pine Road   | 未分配 | 数字表      │
│ └─ ... (显示更多)                                      │
│ ─────────────────────────────────────────────────────── │
│ 📍 [地图预览筛选结果] - 查看水表地理分布                │
│ ─────────────────────────────────────────────────────── │
│           [导出Excel模板]  [返回上一步]                │
└─────────────────────────────────────────────────────────┘
```

**步骤2A-2: 上传编辑后的Excel**
```
┌─────────────Excel创建向导 - 第2步: 上传Work Package─────┐
│ 上传已编辑的Excel文件:                                  │
│ [选择文件] work_package_edited.xlsx                    │
│ 或拖拽文件到此区域                                      │
│ ─────────────────────────────────────────────────────── │
│ ✅ 文件解析成功 - 发现以下Work Package:                 │
│ ├─ "北区2月抄表" (125个水表)                           │
│ │  └─ 19M1567747, 17M245630, 18M334567...             │
│ ├─ "南区定期检查" (67个水表)                           │
│ │  └─ 16M123456, 15M789012, 14M345678...              │
│ └─ "商业区巡检" (42个水表)                             │
│    └─ 20M987654, 19M654321, 18M321098...              │
│ ─────────────────────────────────────────────────────── │
│ 📍 [地图预览所有Work Package] - 验证地理分布            │
│ 📊 [数据验证报告] - 检查水表ID有效性                    │
│ ─────────────────────────────────────────────────────── │
│ ⚠️  验证结果:                                          │
│ ├─ ✅ 水表ID全部有效                                   │
│ ├─ ✅ 没有重复分配                                     │
│ └─ ⚠️  3个水表已分配到其他Work Package (将跳过)        │
│ ─────────────────────────────────────────────────────── │
│        [创建Work Package]  [重新上传]  [返回]          │
└─────────────────────────────────────────────────────────┘
```

#### Step 2B: 在线创建向导 (如果选择在线方式)

**步骤2B-1: 筛选水表**
```
┌─────────────在线创建向导 - 第1步: 筛选水表─────────────┐
│ 筛选条件: [同Excel向导的筛选界面]                       │
│ 符合条件水表: 234个                                    │
│ [水表列表显示，支持多选]                               │
│ 📍 [地图预览选中水表]                                   │
│ ─────────────────────────────────────────────────────── │
│              [下一步: 设置Work Package]                │
└─────────────────────────────────────────────────────────┘
```

**步骤2B-2: 设置Work Package**
```
┌─────────────在线创建向导 - 第2步: 设置Work Package─────┐
│ Work Package基本信息:                                   │
│ ├─ 名称: [北区2月抄表_____________________]            │
│ ├─ 描述: [月度例行抄表检查_________________]            │
│ ├─ 优先级: [中等▼] 预估工时: [16]小时                  │
│ └─ 频率模板: [月度▼] 负责团队: [选择团队▼]             │
│ ─────────────────────────────────────────────────────── │
│ 包含水表: 已选择125个水表                               │
│ 📍 [地图预览Work Package覆盖区域]                       │
│ 📊 [统计信息] 地区分布、水表类型分布等                  │
│ ─────────────────────────────────────────────────────── │
│            [创建Work Package]  [返回修改]              │
└─────────────────────────────────────────────────────────┘
```

---

## 🏗️ 技术架构设计

### 1. 路由结构
```typescript
// Work Package Management 路由设计
/work-packages                          // GET: 主列表页
/work-packages/create                   // GET: 创建方式选择页
/work-packages/create/excel            // GET: Excel导入向导页面
/work-packages/create/online           // GET: 在线创建向导页面
/work-packages/{id}                    // GET: 查看Work Package详情
/work-packages/{id}/edit               // GET: 编辑Work Package
/work-packages/{id}/meters             // GET: Work Package包含的水表
/work-packages/{id}/assignments        // GET: Work Package的任务分配
```

### 2. Controller设计
```csharp
[Route("work-packages")]
public class WorkPackageController : ControllerBase
{
    private readonly IWorkPackageService _workPackageService;
    private readonly IMeterService _meterService;
    private readonly IExcelService _excelService;
    
    // 主列表页
    [HttpGet]
    public async Task<IActionResult> Index([FromQuery] WorkPackageSearchDto search)
    {
        var result = await _workPackageService.GetPagedAsync(search);
        return View(result);
    }
    
    // 创建方式选择页
    [HttpGet("create")]
    public IActionResult CreateSelect()
    {
        return View();
    }
    
    // Excel向导页面
    [HttpGet("create/excel")]
    public IActionResult CreateExcel()
    {
        return View();
    }
    
    // 导出Excel模板
    [HttpPost("create/excel/export-template")]
    public async Task<IActionResult> ExportTemplate([FromBody] MeterFilterDto filter)
    {
        // 调用Meter服务获取筛选后的水表数据
        var meters = await _meterService.GetFilteredMetersAsync(filter);
        
        // 创建Work Package Excel模板
        var excel = await _excelService.CreateWorkPackageTemplateAsync(meters);
        
        var fileName = $"work_package_template_{DateTime.Now:yyyyMMdd_HHmmss}.xlsx";
        return File(excel, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", fileName);
    }
    
    // Excel文件导入
    [HttpPost("create/excel/import")]
    public async Task<IActionResult> ImportFromExcel(IFormFile file)
    {
        var result = await _workPackageService.CreateFromExcelAsync(file);
        return Json(new { success = true, workPackages = result });
    }
    
    // 在线创建
    [HttpPost("create/online")]
    public async Task<IActionResult> CreateOnline([FromBody] CreateWorkPackageDto dto)
    {
        var result = await _workPackageService.CreateAsync(dto);
        return Json(new { success = true, workPackage = result });
    }
    
    // 获取筛选后的水表数据 (用于向导中显示)
    [HttpPost("get-filtered-meters")]
    public async Task<IActionResult> GetFilteredMeters([FromBody] MeterFilterDto filter)
    {
        var meters = await _meterService.GetFilteredMetersAsync(filter);
        return Json(meters);
    }
}
```

### 3. 服务层设计
```csharp
public interface IWorkPackageService
{
    Task<PagedResultDto<WorkPackageListDto>> GetPagedAsync(WorkPackageSearchDto search);
    Task<WorkPackageDetailDto> GetByIdAsync(int id);
    Task<WorkPackageDetailDto> CreateAsync(CreateWorkPackageDto dto);
    Task<List<WorkPackageDto>> CreateFromExcelAsync(IFormFile file);
    Task<WorkPackageDetailDto> UpdateAsync(int id, UpdateWorkPackageDto dto);
    Task DeleteAsync(int id);
}

public class WorkPackageService : IWorkPackageService
{
    private readonly IWorkPackageRepository _workPackageRepository;
    private readonly IMeterService _meterService;
    private readonly IExcelParserService _excelParser;
    
    public async Task<List<WorkPackageDto>> CreateFromExcelAsync(IFormFile file)
    {
        // 1. 解析Excel文件
        var excelData = await _excelParser.ParseWorkPackageExcelAsync(file);
        
        // 2. 验证水表ID有效性
        var validationResult = await ValidateMetersAsync(excelData.Select(x => x.MeterId));
        
        // 3. 按ScheduleName分组
        var groups = excelData
            .Where(x => !string.IsNullOrEmpty(x.ScheduleName))
            .GroupBy(x => x.ScheduleName.Trim());
        
        // 4. 为每个组创建Work Package
        var createdWorkPackages = new List<WorkPackageDto>();
        foreach (var group in groups)
        {
            var workPackage = new CreateWorkPackageDto
            {
                Name = group.Key,
                Description = $"Created from Excel import on {DateTime.Now:yyyy-MM-dd}",
                MeterIds = group.Select(x => x.MeterId).ToList(),
                CreatedBy = GetCurrentUsername()
            };
            
            var created = await CreateAsync(workPackage);
            createdWorkPackages.Add(created);
        }
        
        return createdWorkPackages;
    }
    
    private async Task<ValidationResult> ValidateMetersAsync(IEnumerable<string> meterIds)
    {
        var result = new ValidationResult();
        
        foreach (var meterId in meterIds)
        {
            var meter = await _meterService.GetByIdAsync(meterId);
            if (meter == null)
            {
                result.Errors.Add($"水表 {meterId} 不存在");
            }
            else if (meter.Status == MeterStatus.Allocated)
            {
                result.Warnings.Add($"水表 {meterId} 已分配到其他Work Package");
            }
        }
        
        return result;
    }
}
```

### 4. Excel模板格式设计
```csharp
public class ExcelService : IExcelService
{
    public async Task<byte[]> CreateWorkPackageTemplateAsync(List<MeterDto> meters)
    {
        using var package = new ExcelPackage();
        var worksheet = package.Workbook.Worksheets.Add("Work Package Template");
        
        // 设置表头
        var headers = new[]
        {
            "MeterID", "AccountNo", "Address", "GPS_Lat", "GPS_Lng", 
            "MeterType", "SubArea", "ScheduleName"
        };
        
        for (int i = 0; i < headers.Length; i++)
        {
            worksheet.Cells[1, i + 1].Value = headers[i];
            worksheet.Cells[1, i + 1].Style.Font.Bold = true;
        }
        
        // 填充水表数据
        for (int i = 0; i < meters.Count; i++)
        {
            var meter = meters[i];
            var row = i + 2;
            
            worksheet.Cells[row, 1].Value = meter.MeterId;
            worksheet.Cells[row, 2].Value = meter.AccountNo;
            worksheet.Cells[row, 3].Value = meter.Address;
            worksheet.Cells[row, 4].Value = meter.GpsLatitude;
            worksheet.Cells[row, 5].Value = meter.GpsLongitude;
            worksheet.Cells[row, 6].Value = meter.MeterType;
            worksheet.Cells[row, 7].Value = meter.SubArea;
            worksheet.Cells[row, 8].Value = ""; // ScheduleName空列，供用户填写
        }
        
        // 设置ScheduleName列的背景色提示
        var scheduleNameColumn = worksheet.Cells[2, 8, meters.Count + 1, 8];
        scheduleNameColumn.Style.Fill.PatternType = ExcelFillStyle.Solid;
        scheduleNameColumn.Style.Fill.BackgroundColor.SetColor(Color.LightYellow);
        
        // 添加说明工作表
        var instructionSheet = package.Workbook.Worksheets.Add("使用说明");
        instructionSheet.Cells[1, 1].Value = "Work Package Excel模板使用说明";
        instructionSheet.Cells[2, 1].Value = "1. 在ScheduleName列填写Work Package名称";
        instructionSheet.Cells[3, 1].Value = "2. 相同名称的水表将创建为同一个Work Package";
        instructionSheet.Cells[4, 1].Value = "3. ScheduleName为空的行将被忽略";
        instructionSheet.Cells[5, 1].Value = "4. 编辑完成后，保存文件并上传到系统";
        
        worksheet.Cells.AutoFitColumns();
        return package.GetAsByteArray();
    }
}
```

---

## 📊 数据模型设计

### 1. Work Package实体
```csharp
public class WorkPackage
{
    public int Id { get; set; }
    public string Name { get; set; }
    public string Description { get; set; }
    public WorkPackageStatus Status { get; set; }
    public Priority Priority { get; set; }
    public string ServiceArea { get; set; }
    public string SubArea { get; set; }
    public int EstimatedHours { get; set; }
    public bool IsTemplate { get; set; }
    public string CreatedBy { get; set; }
    public DateTime CreatedDate { get; set; }
    public DateTime? ModifiedDate { get; set; }
    
    // 导航属性
    public virtual ICollection<WorkPackageMeter> WorkPackageMeters { get; set; }
    public virtual ICollection<Assignment> Assignments { get; set; }
}

public class WorkPackageMeter
{
    public int WorkPackageId { get; set; }
    public string MeterId { get; set; }
    public int Sequence { get; set; } // 抄表顺序
    
    public virtual WorkPackage WorkPackage { get; set; }
    public virtual Meter Meter { get; set; }
}

public enum WorkPackageStatus
{
    Draft,      // 草稿
    Active,     // 活跃
    Completed,  // 已完成
    Archived    // 已归档
}
```

### 2. DTO设计
```csharp
public class CreateWorkPackageDto
{
    public string Name { get; set; }
    public string Description { get; set; }
    public Priority Priority { get; set; } = Priority.Medium;
    public List<string> MeterIds { get; set; } = new();
    public string ServiceArea { get; set; }
    public string SubArea { get; set; }
    public int EstimatedHours { get; set; }
    public bool IsTemplate { get; set; }
}

public class WorkPackageListDto
{
    public int Id { get; set; }
    public string Name { get; set; }
    public string Description { get; set; }
    public WorkPackageStatus Status { get; set; }
    public int MeterCount { get; set; }
    public string CreatedBy { get; set; }
    public DateTime CreatedDate { get; set; }
    public int CompletedMeterCount { get; set; }
    public decimal ProgressPercentage { get; set; }
}

public class MeterFilterDto
{
    public string Area { get; set; }
    public MeterStatus? Status { get; set; }
    public string MeterType { get; set; }
    public string AddressContains { get; set; }
    public DateTime? InstallDateFrom { get; set; }
    public DateTime? InstallDateTo { get; set; }
    public double? GpsLatMin { get; set; }
    public double? GpsLatMax { get; set; }
    public double? GpsLngMin { get; set; }
    public double? GpsLngMax { get; set; }
}
```

---

## 🎯 前端实现要点

### 1. Vue.js组件结构
```typescript
// Work Package管理主页面
WorkPackageManagement.vue
├── WorkPackageList.vue          // Work Package列表
├── CreateWorkPackageWizard.vue  // 创建向导
│   ├── MethodSelection.vue      // 方式选择
│   ├── ExcelImportWizard.vue    // Excel导入向导
│   │   ├── MeterFilter.vue      // 水表筛选
│   │   └── ExcelUpload.vue      // Excel上传
│   └── OnlineCreateWizard.vue   // 在线创建向导
├── WorkPackageDetail.vue        // Work Package详情
└── MapPreview.vue              // 地图预览组件
```

### 2. 状态管理 (Pinia)
```typescript
export const useWorkPackageStore = defineStore('workPackage', {
  state: () => ({
    workPackages: [] as WorkPackageListDto[],
    currentWorkPackage: null as WorkPackageDetailDto | null,
    createWizardState: {
      method: null, // 'excel' | 'online'
      filteredMeters: [] as MeterDto[],
      selectedMeters: [] as string[],
      uploadedFile: null as File | null
    }
  }),
  
  actions: {
    async fetchWorkPackages(search?: WorkPackageSearchDto) {
      const response = await workPackageApi.getPagedAsync(search);
      this.workPackages = response.items;
    },
    
    async exportTemplate(filter: MeterFilterDto) {
      const blob = await workPackageApi.exportTemplate(filter);
      downloadFile(blob, 'work_package_template.xlsx');
    },
    
    async createFromExcel(file: File) {
      const result = await workPackageApi.importFromExcel(file);
      await this.fetchWorkPackages(); // 刷新列表
      return result;
    }
  }
});
```

### 3. 关键组件实现示例
```vue
<!-- ExcelImportWizard.vue -->
<template>
  <div class="excel-import-wizard">
    <el-steps :active="currentStep" finish-status="success">
      <el-step title="选择水表" description="筛选并导出模板"></el-step>
      <el-step title="上传文件" description="上传编辑后的Excel"></el-step>
      <el-step title="确认创建" description="验证并创建Work Package"></el-step>
    </el-steps>
    
    <!-- Step 1: 筛选水表 -->
    <div v-if="currentStep === 0">
      <MeterFilter @filter-changed="onFilterChanged" />
      <MeterList :meters="filteredMeters" readonly />
      <MapPreview :meters="filteredMeters" />
      <div class="step-actions">
        <el-button type="primary" @click="exportTemplate">导出Excel模板</el-button>
        <el-button @click="nextStep" :disabled="!templateExported">下一步</el-button>
      </div>
    </div>
    
    <!-- Step 2: 上传Excel -->
    <div v-if="currentStep === 1">
      <ExcelUpload @file-uploaded="onFileUploaded" />
      <div class="step-actions">
        <el-button @click="prevStep">上一步</el-button>
        <el-button type="primary" @click="parseExcel" :disabled="!uploadedFile">解析文件</el-button>
      </div>
    </div>
    
    <!-- Step 3: 确认创建 -->
    <div v-if="currentStep === 2">
      <WorkPackagePreview :work-packages="parsedWorkPackages" />
      <MapPreview :work-packages="parsedWorkPackages" />
      <div class="step-actions">
        <el-button @click="prevStep">上一步</el-button>
        <el-button type="primary" @click="createWorkPackages">创建Work Package</el-button>
      </div>
    </div>
  </div>
</template>
```

---

## 🚀 实施计划

### Phase 1: 基础架构 (2-3天)
1. ✅ 创建Work Package实体和数据库表
2. ✅ 实现Work Package基础CRUD服务
3. ✅ 创建Work Package Controller和基础路由
4. ✅ 实现主列表页面和基本查询功能

### Phase 2: Excel工作流 (3-4天)
1. ✅ 实现水表筛选功能
2. ✅ 开发Excel模板导出功能
3. ✅ 实现Excel解析和Work Package创建
4. ✅ 添加数据验证和错误处理

### Phase 3: 在线创建功能 (2-3天)
1. ✅ 实现在线筛选和选择界面
2. ✅ 开发Work Package在线设置页面
3. ✅ 集成实时保存功能

### Phase 4: 地图集成和优化 (2-3天)
1. ✅ 集成地图预览组件
2. ✅ 实现Work Package地理分布可视化
3. ✅ 添加地图筛选和交互功能

### Phase 5: 测试和优化 (1-2天)
1. ✅ 单元测试和集成测试
2. ✅ 性能优化和UI/UX改进
3. ✅ 文档完善和部署

---

## 💡 设计决策记录

### 1. 为什么选择向导式设计？
- **用户体验**: 复杂流程分步骤引导，降低学习曲线
- **错误减少**: 每步验证，及早发现问题
- **流程清晰**: 用户明确知道当前位置和下一步操作

### 2. 为什么保留Excel工作流？
- **批量操作**: Excel适合大批量数据编辑
- **离线编辑**: 支持离线环境下的数据准备
- **用户习惯**: 许多管理员更熟悉Excel操作

### 3. 为什么在Work Package模块而不是Meter Management？
- **业务逻辑**: Work Package是目标，水表是手段
- **用户心智模型**: 用户想创建Work Package，自然在该模块操作
- **功能内聚**: 相关功能集中在一个模块，便于维护

### 4. 架构设计考量
- **服务分离**: Work Package Service调用Meter Service，保持模块独立
- **数据验证**: 多层验证确保数据一致性
- **扩展性**: 预留接口支持未来功能扩展

---

## 🔧 配置和部署

### 1. 数据库迁移
```csharp
// 添加Work Package相关表的迁移
dotnet ef migrations add AddWorkPackageManagement
dotnet ef database update
```

### 2. 依赖注入配置
```csharp
// Program.cs
services.AddScoped<IWorkPackageService, WorkPackageService>();
services.AddScoped<IWorkPackageRepository, WorkPackageRepository>();
services.AddScoped<IExcelService, ExcelService>();
```

### 3. 权限配置
```csharp
// 在DbInitializer.cs中添加Work Package相关权限
var workPackagePermissions = new[]
{
    "work-package:create",
    "work-package:read", 
    "work-package:update",
    "work-package:delete",
    "work-package:export",
    "work-package:import"
};
```

---

## 📚 参考资料

- [User Story 3: 可重用Work Package计划管理](./USER_STORIES.md#story-3)
- [Work Package数据模型设计](./WORK_PACKAGE_DEVELOPMENT_PLAN.md)
- [Excel导入导出规范](./EXCEL_INTEGRATION_SPEC.md)
- [地图组件集成指南](./MAP_INTEGRATION_GUIDE.md)

---

**文档维护**: 此文档应随着实现过程中的设计变更及时更新，确保与实际代码保持一致。 