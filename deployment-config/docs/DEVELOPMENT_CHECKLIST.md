# 🔍 开发前检查清单

## 📋 编码前必须完成的步骤

### 1️⃣ **现有代码审查** (必须完成)
- [ ] 搜索相关功能的现有实现
- [ ] 检查是否有类似命名的类/接口
- [ ] 分析现有架构和设计模式
- [ ] 评估复用 vs 新建的合理性

### 2️⃣ **功能重叠检查**
```bash
# 搜索相关Service/Repository
grep -r "class.*Service" src/
grep -r "class.*Repository" src/
grep -r "interface.*Service" src/

# 搜索相关功能关键词
grep -r "sync\|Sync" src/
grep -r "auth\|Auth" src/  
grep -r "location\|Location" src/
```

### 3️⃣ **架构一致性检查**
- [ ] 新功能是否符合现有分层架构？
- [ ] 命名是否与现有模式一致？
- [ ] 是否违反了单一职责原则？

### 4️⃣ **复用评估标准**
| 条件 | 动作 |
|------|------|
| 功能完全相同 | ✅ 直接使用现有类 |
| 功能80%相同 | ✅ 扩展现有类 |
| 功能50%相同 | 🤔 评估重构可能性 |
| 功能<30%相同 | ✅ 创建新类(但要避免命名冲突) |

## 🚫 **禁止行为**
- ❌ 不查看现有代码就开始编写
- ❌ 创建功能重复的类
- ❌ 使用过于相似的命名
- ❌ 破坏现有架构模式

## ✅ **推荐行为** 
- ✅ 优先扩展现有功能
- ✅ 保持命名一致性
- ✅ 遵循现有设计模式
- ✅ 文档化架构决策

## 🔄 **重构时机**
当发现重复时，应该：
1. 立即停止新开发
2. 分析重复的根本原因
3. 设计合并/重构方案
4. 执行重构
5. 继续功能开发 