# CORDE Mobile Application Environment Management

## Overview

This document outlines the environment management strategy implemented for the CORDE Mobile Application, enabling seamless switching between development/testing and production environments through a simplified configuration approach.

## Architecture Design

### Environment Strategy

The application supports three distinct operational modes:

1. **Development Debug Mode** (`__DEV__ = true`)
   - Local development with hot reload
   - Local SharePoint API endpoints
   - Debug logging enabled

2. **Testing Environment Build** (`__DEV__ = false`, `isProductionBuild = false`)
   - Production build targeting test servers
   - Test API endpoints
   - UI displays "(DEV)" indicator

3. **Production Environment Build** (`__DEV__ = false`, `isProductionBuild = true`)
   - Production build targeting live servers
   - Production API endpoints
   - UI displays "(PROD)" indicator

### API Endpoint Configuration

#### Main API Endpoints
- **Testing**: `https://sicon-mnlweb.sicon.co.nz/WorkbenchTest/api`
- **Production**: `https://sicon-mnlweb.sicon.co.nz/Workbench/api`

#### SharePoint API Endpoints
- **Development Debug**: 
  - Android: `http://********:5088/api`
  - iOS: `http://localhost:5088/api`
- **Testing/Production Build**: `http://**************/sharepoint-api`

## Implementation

### Code Structure

The environment management is centralized in `src/api/BaseApi.ts`:

```typescript
// Environment Configuration - Change this line for different builds
const isProductionBuild = false; // false=testing, true=production

// Main API URL selection
const BASE_URL = isProductionBuild 
  ? 'https://sicon-mnlweb.sicon.co.nz/Workbench/api'
  : 'https://sicon-mnlweb.sicon.co.nz/WorkbenchTest/api';

// SharePoint API URL selection
const getSharePointApiBaseUrl = (): string => {
  if (__DEV__) {
    return Platform.OS === 'android' 
      ? 'http://********:5088/api' 
      : 'http://localhost:5088/api';
  }
  return 'http://**************/sharepoint-api';
};
```

### Build Process

#### Testing Environment Build (Default)
```bash
cd android && ./gradlew assembleRelease
```
- Uses testing API endpoints
- App displays "(DEV)" indicator
- App name: "CORDE Mobile Dev"

#### Production Environment Build
1. Change `isProductionBuild` to `true` in BaseApi.ts
2. Execute build command:
   ```bash
   cd android && ./gradlew assembleRelease
   ```
3. Change `isProductionBuild` back to `false`
- Uses production API endpoints
- App displays "(PROD)" indicator  
- App name: "CORDE Mobile"

## Branch Management Strategy

### Branch Structure
- **develop**: Development and testing environment
- **main**: Production-ready stable releases

### Workflow
1. Daily development occurs on `develop` branch
2. Features are tested in testing environment
3. Stable features are merged to `main` branch
4. Production builds are created from `main` branch

## Benefits

### Simplicity
- Single variable controls all environment switching
- No additional dependencies required
- Maintains existing build commands
- Clear and understandable logic flow

### Safety
- Default configuration targets testing environment
- Explicit action required for production builds
- Prevents accidental production deployments
- Clear visual indicators for environment identification

### Maintainability
- Centralized configuration management
- Easy to extend for additional environments
- Minimal code changes required
- Self-documenting code structure

## Future Enhancements

### Potential Improvements
1. **Automated Environment Detection**: Implement build-time environment variable injection
2. **Configuration Files**: Move to external configuration files for easier management
3. **Environment-Specific Features**: Enable/disable features based on environment
4. **Automated Testing**: Integrate environment-specific test suites

### SharePoint Environment Separation
Currently, both testing and production builds use the same SharePoint endpoint. Future enhancement could include:
- Separate SharePoint testing environment
- Environment-specific SharePoint configurations
- Dynamic SharePoint endpoint selection

## Usage Guidelines

### For Developers
1. Keep `isProductionBuild = false` during development
2. Test thoroughly in testing environment before production
3. Always verify environment indicators in UI
4. Document any environment-specific configurations

### For Production Releases
1. Ensure all changes are merged to `main` branch
2. Switch to production configuration
3. Build and test production APK
4. Verify production environment connectivity
5. Reset to testing configuration after build

## Troubleshooting

### Common Issues
- **Wrong API endpoints**: Verify `isProductionBuild` value
- **SharePoint connectivity**: Check network configuration and endpoint availability
- **Environment indicators**: Ensure UI components reflect current environment
- **Build failures**: Verify all dependencies and configurations are correct

### Verification Steps
1. Check API endpoint in network logs
2. Verify environment indicator in UI
3. Test core functionality in target environment
4. Confirm data isolation between environments

## Conclusion

This environment management strategy provides a robust, simple, and maintainable approach to handling multiple deployment environments for the CORDE Mobile Application. The design prioritizes simplicity and safety while maintaining the flexibility needed for future enhancements.
