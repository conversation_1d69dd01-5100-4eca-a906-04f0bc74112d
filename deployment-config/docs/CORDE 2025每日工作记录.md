claude-code-webui

Server: *********

Username: adminluke

Password: (same as above)

ls#8876&CR

netstat -ano | findstr :8081

taskkill /PID 1234 /F

macos-mouse-jiggler-c/mouse_jiggler 

for /f "tokens=5" %a in ('netstat -ano ^| findstr :8081') do taskkill /F /PID %a

Lindaj. <PERSON><PERSON> Braun

CORDEwinter24!

192.168.110.121

255.255.255.0

192.168.110.1

打包APK：终端输入

./gradlew clean

./gradlew assembleDebug --no-build-cache

cd android
./gradlew assembleRelease

./gradlew bundleRelease

curl -sSL https://raw.githubusercontent.com/SpotX-Official/SpotX-Bash/main/spotx.sh)

虚拟机安装后在命令行查看日志：
adb logcat -s ReactNativeJS



sharepoint

Directory (tenant) ID: 637a79a6-985f-4dcd-aa06-354f067419bf

Application (client) ID: d50296cf-228e-4139-a530-71a68b516c58

Client Secret: ****************************************

Secret Expiry: 21/08/2027

部署流程：

后端：

docker build -t *********:5001/watermeter-backend:latest ./WaterMeterManagement
docker build --platform linux/amd64 --no-cache -t *********:5001/watermeter-backend:latest ./WaterMeterManagement
docker push *********:5001/watermeter-backend:latest

前端：
docker build --platform linux/amd64 --no-cache -t *********:5001/watermeter-frontend:latest ./water-meter-admin
docker push *********:5001/watermeter-frontend:latest

服务器：

ssh adminluke@*********

cd /opt/watermeter
docker-compose up -d backend
docker-compose up -d frontend

**# 停止容器**
docker stop watermeter-backend
docker stop watermeter-frontend

**# 删除容器**
docker rm watermeter-backend
docker rm watermeter-frontend

**# 删除旧的镜像，强制重新拉取**
docker rmi -f *********:5001/watermeter-backend:latest
docker rmi -f *********:5001/watermeter-frontend:latest

docker rmi -f localhost:5001/watermeter-frontend
docker rmi -f localhost:5001/watermeter-backend

**# 手动拉取最新的镜像**
docker pull *********:5001/watermeter-backend:latest
docker pull *********:5001/watermeter-frontend:latest

**#查看本地存在镜像并删除无用的**
docker images
docker image prune -f

**#删除注册中心无用镜像**

查看当前运行镜像：
docker ps --format "table {{.Names}}\t{{.Image}}\t{{.CreatedAt}}"

**查看docker hub中可用镜像**

curl http://*********:5001/v2/_catalog

**检查docker hub中特定镜像**

curl http://*********:5001/v2/watermeter-backend/tags/list

curl -X GET http://*********:5001/v2/sharepoint-coordinates-service/manifests/latest

#### 确保应用已停止运行

adb shell am force-stop com.awesomeproject

#### 执行 SQLite 命令

adb shell "run-as com.awesomeproject sqlite3 /data/data/com.awesomeproject/databases/corde_mobile.db 'PRAGMA wal_checkpoint(FULL); VACUUM;'"

adb shell pm clear com.awesomeproject

uninstall com.awesomeproject

虚拟机安装项目文件夹下的APK：
`adb install android\app\build\outputs\apk\release\app-release.apk`

重装vscode：

pkill -f "Visual Studio Code"

sudo rm -rf /Applications/Visual\ Studio\ Code.app

Password:

rm -rf ~/Library/Application\ Support/Code  

rm -rf ~/Library/Caches/com.microsoft.VSCode  

rm -rf ~/.vscode  

rm -rf ~/Library/Logs/Code  

rm -rf ~/Library/Saved\ Application\ State/com.microsoft.VSCode.savedState 

PowerShell

```shell
Enable-WindowsOptionalFeature -Online -FeatureName VirtualMachinePlatform
Disable-WindowsOptionalFeature -Online -FeatureName VirtualMachinePlatform
```

CMD

```shell
dism.exe /online /enable-feature /featurename:VirtualMachinePlatform /all
dism.exe /online /disable-feature /featurename:VirtualMachinePlatform
```

下周工作：

Refactor icons and database schema updates

- Updated icon names in various components for consistency (e.g., changed 'water-pump' to 'water', 'clipboard-list' to 'clipboard').
- Commented out force reset database functionality in App component to prevent unintended data loss during initialization.
- Modified BaselineRepository and WaterMeterRepository to use the singleton instance of DatabaseManager for SQL execution.
- Enhanced Baseline records table schema in DatabaseManager to include additional fields for better data tracking.
- Removed deprecated BackgroundMeterReadingSyncService and refactored sync tasks in BackgroundSyncManager for improved performance.
- Adjusted sync logic in BasicDataSyncService to ensure proper order of operations based on dependencies.
- Updated devWarningFilter to retain important icon-related warnings for better debugging.

feat: Update versioning, enhance reading upload process, and implement universal upload service



feat: Implement task-based duplicate detection and update functionality in MobileReadingController and related services



Fix: Handle undefined values in rendering for ReadingRecords component

feat: Integrate Cloudflare R2 for photo uploads and implement related services

feat: Add endpoints to update photo quality and retrieve photo by ID

Add photo management features: implement delete and update quality functions in service and UI



feat: Implement GPS service with geocoding and batch update functionality

Add GPS functionality: implement GPS update and batch update features in WaterMetersPage and create gps.service for handling GPS operations

fix: Properly handle promise rejections in API methods to eliminate warnings and improve error propagation

feat: Add GPS coordinates to Task and WaterMeter DTOs, and update related services

feat: Enhance GPS functionality in Water Meter Management

- Added GPS coordinates fields to TaskListDto and WaterMeterListDto interfaces.
- Updated backend services to return GPS coordinates for tasks and water meters.
- Improved map marker logic to display accurate GPS locations for tasks.
- Enhanced info window content in GoogleMapView to include location and meter serial number.
- Redesigned water meter edit modal for a more compact layout, ensuring all fields fit on one screen.
- Fixed issues with missing fields in the water meter view and edit interfaces, including Account Number and Asset ID.
- Implemented consistent styling across all modals for better user experience.



下周任务：

feat: Enhance RouteMapView with pagination and waypoint handling, and update GoogleMapView to support path rendering

feat: Implement NotificationManager for centralized notification handling and error management

feat: Add duplication and calculation functionality for frequency templates

feat: Update FrequencyCalculationDto to include scheduled dates, first and last occurrences, and frequency description

Add migration to include reactive task fields in Tasks table

\- Added AmsTaskNumber column (string, max length 50)

\- Added AssetId column (string, max length 50)

\- Added TaskCategory column (string, max length 20, non-nullable with default value)

feat: Enhance Task Management with user reassignment, task category, and water meter selection

\- Removed existing TaskCategory column from Tasks table.

\- Added TaskCategory column as an integer to represent enum values, with a default value of 0 (Planned).

\- Implemented migration to ensure backward compatibility by allowing rollback to the previous string representation of TaskCategory.

feat: Add task category support and enhance DynamicSelect for improved value handling

feat: Auto-sync AssetId from WaterMeter when creating or updating tasks

feat: Enhance asset ID rendering to fallback to meter asset ID when not available

feat: Implement translation service and enhance TaskListDto with base functionality





Enhance SharePoint Coordinates Service with comprehensive updates

- Updated .gitignore to include additional files and directories for better exclusion.
- Modified HealthController to allow anonymous access for health checks.
- Enhanced Swagger configuration with Basic Authentication support.
- Implemented CORS policy to allow all origins.
- Updated application URL in launch settings for consistency.
- Revised docker-compose.yml to include detailed environment variables for SharePoint integration.
- Removed obsolete init-database.sql file.
- Created README.md with detailed project information, features, and deployment instructions.
- Added appsettings.Development.json for logging configuration.
- Introduced a user-friendly login.html for service authentication with error handling and default credentials.

Refactor code structure for improved readability and maintainability

上架google play store:

feat: Update application namespace, version, and database paths; add new permissions and improve build configurations
