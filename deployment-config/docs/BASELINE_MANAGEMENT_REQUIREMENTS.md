# 📊 基线管理模块需求文档

## 📋 文档概述

**文档目的**: 定义基线管理模块的完整功能需求和业务规则  
**创建时间**: 2025年6月  
**对应用户故事**: User Story 2 - 基线读数验证  
**模块位置**: Web管理端 - Master Data → Baseline Management  

---

## 🎯 业务背景

### 1. 业务价值
基线管理是水表抄表系统的核心质量控制模块，通过建立和维护每个水表的参考读数，实现：
- **数据质量保障**: 自动检测异常读数，提高数据准确性
- **效率提升**: 减少人工数据审核工作量
- **风险控制**: 及时发现表计故障、人为错误等问题
- **合规支撑**: 为SDC提供可靠的抄表数据

### 2. 业务场景
- **SDC定期提供基线文件** → 管理员批量导入建立参考数据
- **现场抄表数据验证** → 移动端实时比较基线检测异常
- **异常数据处理** → 管理员分析处理异常情况
- **基线数据维护** → 持续更新和优化基线质量

---

## 🔧 功能需求

### 1. 基线数据管理

#### 1.1 数据导入功能
**需求描述**: 支持从SDC提供的Excel/CSV文件批量导入基线数据

**功能要点**:
- 支持Excel (.xlsx) 和 CSV (.csv) 格式文件上传
- 文件大小限制：50MB以内
- 支持拖拽上传和文件选择两种方式
- 实时显示上传进度

**文件格式要求**:
```
必需字段：
- SerialNumber: 水表序列号
- BaselineDate: 基线日期 (yyyy-MM-dd)
- BaselineValue: 基线读数 (数字，支持小数点后4位)

可选字段：
- BaselineType: 基线类型 (Initial/Periodic/Correction/Migration)
- Status: 状态 (Active/Superseded/Invalid)
- ValidationNotes: 验证备注
```

**验证规则**:
- 水表序列号必须在系统中存在
- 基线日期不能为空且格式正确
- 基线读数必须为正数
- 同一水表同一日期不能有重复基线（除非是修正）

**导入结果**:
- 显示导入总数、成功数、失败数
- 详细列出失败记录及错误原因
- 生成ImportBatch编号便于追踪
- 支持下载导入结果报告

#### 1.2 基线记录维护
**需求描述**: 提供基线记录的完整CRUD操作

**查询功能**:
- 支持按水表序列号、位置、基线类型、状态、数据来源等多维度搜索
- 支持按基线日期范围筛选
- 支持按验证状态筛选（已验证/未验证）
- 支持按异常状态筛选（正常/异常）
- 支持分页显示，每页10/20/50/100条记录
- 支持按日期、读数、方差等字段排序

**列表显示**:
- 水表序列号、位置、基线日期、基线读数
- 基线类型、状态、数据来源
- 与前值的方差百分比（颜色编码）
- 验证状态（已验证/待验证）
- 异常标记（正常/异常）
- 操作按钮（查看/编辑/验证/修正/删除）

**创建/编辑功能**:
- 手动创建单条基线记录
- 编辑现有基线记录的所有字段
- 自动计算与前值的方差
- 支持添加验证备注

**删除功能**:
- 支持删除单条记录
- 删除前确认提示
- 记录删除操作日志

### 2. 数据质量控制

#### 2.1 数据验证管理
**需求描述**: 对导入和录入的基线数据进行验证管理

**验证流程**:
- 新导入的数据默认标记为"未验证"状态
- 管理员可以批量或单独验证数据
- 验证时可以添加验证备注
- 记录验证人员和验证时间

**验证界面**:
- 显示未验证数据列表
- 支持批量选择验证
- 验证弹窗显示详细信息
- 提供验证通过/不通过选项

#### 2.2 异常检测与处理
**需求描述**: 自动检测异常基线数据并提供处理工具

**异常检测规则**:
- 读数方差超过20%标记为高度异常（红色）
- 读数方差10-20%标记为需要关注（橙色）
- 读数方差小于10%标记为正常（绿色）
- 读数为负数或为0标记为异常
- 读数小于前值标记为可能的表倒转

**异常处理功能**:
- 异常数据列表显示
- 异常详情查看（包含历史基线对比）
- 异常原因分析工具
- 基线修正功能
- 异常忽略功能（标记为已处理）

#### 2.3 基线修正管理
**需求描述**: 提供基线数据的修正功能

**修正流程**:
- 识别需要修正的基线数据
- 分析修正原因（表计故障、人为错误等）
- 录入新的基线值
- 记录修正原因和操作人员
- 将原基线标记为"Superseded"

**修正记录**:
- 完整的修正历史追踪
- 修正前后数值对比
- 修正原因记录
- 修正人员和时间记录

### 3. 监控与分析

#### 3.1 统计仪表板
**需求描述**: 提供基线数据的统计分析界面

**关键指标**:
- 基线总数量
- 未验证数量（橙色警告）
- 异常数量（红色警告）
- 本月新增数量
- 数据质量评分

**趋势分析**:
- 月度基线导入趋势
- 异常数据变化趋势
- 数据质量改善趋势

#### 3.2 报表功能
**需求描述**: 提供基线数据的报表导出功能

**报表类型**:
- 基线数据明细报表
- 异常数据汇总报表
- 数据质量分析报表
- 导入批次报表

**导出格式**:
- Excel格式导出
- CSV格式导出
- PDF格式报表

### 4. 移动端集成

#### 4.1 基线数据下发
**需求描述**: 为移动端提供基线数据接口

**下发规则**:
- 只下发状态为"Active"的基线数据
- 按水表提供最新的基线值
- 提供基线日期和数据来源信息
- 支持增量同步

#### 4.2 读数验证支持
**需求描述**: 支持移动端的读数验证功能

**验证配置**:
- 异常阈值配置（可调整的百分比）
- 验证规则配置
- 异常处理指导

---

## 🖥️ 界面设计要求

### 1. 主界面布局

#### 1.1 统计卡片区域
```
┌─────────────────────────────────────────────────────────────┐
│ 📊 统计概览                                                 │
├─────────────┬─────────────┬─────────────┬─────────────────┤
│ 📈 基线总数  │ ⚠️ 未验证    │ 🚨 异常数据  │ 📅 本月新增     │
│ 1,245      │ 23         │ 8          │ 156             │
│ 个         │ 个         │ 个         │ 个              │
└─────────────┴─────────────┴─────────────┴─────────────────┘
```

#### 1.2 操作工具栏
```
┌─────────────────────────────────────────────────────────────┐
│ [📁 导入基线] [📊 导出报表] [🔍 高级搜索] [⚙️ 设置]          │
└─────────────────────────────────────────────────────────────┘
```

#### 1.3 搜索筛选区域
```
┌─────────────────────────────────────────────────────────────┐
│ 水表序列号: [_________] 位置: [_________] 类型: [全部 ▼]      │
│ 状态: [全部 ▼] 来源: [全部 ▼] 日期: [开始日期] - [结束日期]   │
│ 验证状态: [全部 ▼] 异常状态: [全部 ▼] [🔍 搜索] [🔄 重置]    │
└─────────────────────────────────────────────────────────────┘
```

### 2. 数据表格设计

#### 2.1 表格列定义
```
┌────────────┬──────────┬──────────┬──────────┬──────────┬──────────┬────────────┐
│ 水表序列号  │ 位置     │ 基线日期  │ 基线读数  │ 基线类型  │ 方差%    │ 操作       │
├────────────┼──────────┼──────────┼──────────┼──────────┼──────────┼────────────┤
│ 19M1567747 │ Main St  │ 2025-06-01│ 125.45m³│ Initial  │ +15.2%🟡 │ [👁️][✏️][✅] │
│ 17M245630  │ Oak Ave  │ 2025-06-02│ 89.12m³ │ Periodic │ +2.1%🟢  │ [👁️][✏️][🚨] │
│ 18M334567  │ Pine Rd  │ 2025-06-03│ 245.67m³│ Periodic │ +25.8%🔴│ [👁️][✏️][⚠️] │
└────────────┴──────────┴──────────┴──────────┴──────────┴──────────┴────────────┘
```

#### 2.2 状态标识设计
- **验证状态**: ✅ 已验证 / ⏳ 待验证
- **异常状态**: 🚨 异常 / ✅ 正常
- **方差颜色**: 🟢 正常(<10%) / 🟡 关注(10-20%) / 🔴 异常(>20%)
- **基线类型**: 🏷️ Initial / 🔄 Periodic / 🔧 Correction / 📦 Migration

### 3. 弹窗界面设计

#### 3.1 导入向导
```
┌─────────────────基线数据导入向导─────────────────┐
│ 第1步: 选择文件                                 │
│ [📁 选择文件] 或 拖拽文件到此区域               │
│ 支持格式: .xlsx, .csv (最大50MB)               │
│ ─────────────────────────────────────────────── │
│ 第2步: 数据预览                                 │
│ ✅ 检测到 1,245 条记录                         │
│ ⚠️ 发现 12 条错误记录                          │
│ [📋 查看错误详情]                              │
│ ─────────────────────────────────────────────── │
│ 第3步: 确认导入                                 │
│ [📥 开始导入] [❌ 取消]                         │
└─────────────────────────────────────────────────┘
```

#### 3.2 异常处理界面
```
┌─────────────────异常基线处理─────────────────┐
│ 水表信息: 19M1567747 - 123 Main Street      │
│ 当前基线: 245.67m³ (2025-06-03)            │
│ 前次基线: 195.23m³ (2025-05-03)            │
│ 方差: +25.8% (超出正常范围)                  │
│ ─────────────────────────────────────────── │
│ 处理选项:                                   │
│ ○ 接受当前值 (标记为已处理)                 │
│ ○ 修正基线值: [_______] m³                  │
│ ○ 标记为无效                               │
│ ─────────────────────────────────────────── │
│ 处理原因: [________________________]       │
│ [💾 保存] [❌ 取消]                          │
└─────────────────────────────────────────────┘
```

---

## 🔧 技术需求

### 1. 性能要求
- **查询响应时间**: 普通查询 < 2秒，复杂查询 < 5秒
- **导入处理能力**: 支持10万条记录的批量导入
- **并发用户数**: 支持50个管理员同时操作
- **数据存储**: 支持千万级基线记录存储

### 2. 数据库设计

#### 2.1 BaselineRecord表结构
```sql
CREATE TABLE BaselineRecord (
    Id int IDENTITY(1,1) PRIMARY KEY,
    MeterId int NOT NULL,
    BaselineDate datetime2 NOT NULL,
    BaselineValue decimal(12,4) NOT NULL,
    BaselineType nvarchar(20) NOT NULL DEFAULT 'Periodic',
    Status nvarchar(20) NOT NULL DEFAULT 'Active',
    ImportBatch nvarchar(50) NULL,
    SourceFile nvarchar(100) NULL,
    SourceRowNumber int NULL,
    DataSource nvarchar(20) NOT NULL DEFAULT 'Manual',
    ValidationNotes nvarchar(500) NULL,
    IsValidated bit NOT NULL DEFAULT 0,
    ValidatedDate datetime2 NULL,
    ValidatedBy nvarchar(50) NULL,
    HasValidationErrors bit NOT NULL DEFAULT 0,
    ValidationErrors nvarchar(1000) NULL,
    IsAnomalous bit NOT NULL DEFAULT 0,
    AnomalyDescription nvarchar(500) NULL,
    PreviousBaselineId int NULL,
    PreviousBaselineValue decimal(12,4) NULL,
    VarianceFromPrevious decimal(12,4) NULL,
    VariancePercentage decimal(8,4) NULL,
    IsCorrected bit NOT NULL DEFAULT 0,
    CorrectedDate datetime2 NULL,
    CorrectedBy nvarchar(50) NULL,
    CorrectionReason nvarchar(500) NULL,
    CreatedAt datetime2 NOT NULL DEFAULT GETUTCDATE(),
    CreatedBy nvarchar(50) NOT NULL DEFAULT 'System',
    UpdatedAt datetime2 NOT NULL DEFAULT GETUTCDATE(),
    UpdatedBy nvarchar(50) NOT NULL DEFAULT 'System',
    
    FOREIGN KEY (MeterId) REFERENCES WaterMeter(Id),
    INDEX IX_BaselineRecord_MeterId (MeterId),
    INDEX IX_BaselineRecord_BaselineDate (BaselineDate),
    INDEX IX_BaselineRecord_Status (Status),
    INDEX IX_BaselineRecord_IsValidated (IsValidated),
    INDEX IX_BaselineRecord_IsAnomalous (IsAnomalous)
);
```

#### 2.2 关键索引策略
- MeterId + BaselineDate (复合索引，支持按水表查询历史)
- ImportBatch (支持按批次查询)
- IsValidated + IsAnomalous (支持质量控制查询)
- CreatedAt (支持时间范围查询)

### 3. API接口设计

#### 3.1 核心接口列表
```
GET    /api/baseline/records              # 获取基线记录列表
GET    /api/baseline/records/{id}         # 获取基线记录详情
POST   /api/baseline/records              # 创建基线记录
PUT    /api/baseline/records/{id}         # 更新基线记录
DELETE /api/baseline/records/{id}         # 删除基线记录
POST   /api/baseline/import               # 导入基线数据
GET    /api/baseline/statistics           # 获取统计数据
GET    /api/baseline/anomalies            # 获取异常数据
POST   /api/baseline/validate/{id}        # 验证基线数据
POST   /api/baseline/correct/{id}         # 修正基线数据
GET    /api/baseline/history/{meterId}    # 获取水表基线历史
GET    /api/baseline/export               # 导出基线数据
```

#### 3.2 数据传输格式
```json
{
  "records": [
    {
      "id": 1,
      "meterId": 123,
      "meterSerialNumber": "19M1567747",
      "meterLocation": "123 Main Street",
      "baselineDate": "2025-06-01T00:00:00Z",
      "baselineValue": 125.45,
      "baselineType": "Initial",
      "status": "Active",
      "dataSource": "CSV",
      "isValidated": true,
      "hasValidationErrors": false,
      "isAnomalous": false,
      "variancePercentage": 15.2
    }
  ],
  "totalCount": 1245,
  "page": 1,
  "pageSize": 10
}
```

### 4. 安全要求
- **权限控制**: 基于角色的访问控制（RBAC）
- **操作日志**: 记录所有修改操作
- **数据备份**: 重要操作前自动备份
- **输入验证**: 严格的数据验证和SQL注入防护

---

## ✅ 验收标准

### 1. 功能验收标准

#### 1.1 数据导入功能
- ✅ 成功导入包含10,000条记录的Excel文件
- ✅ 正确识别和报告数据格式错误
- ✅ 生成完整的导入结果报告
- ✅ 支持中断后的断点续传

#### 1.2 数据查询功能
- ✅ 多维度搜索结果准确
- ✅ 分页和排序功能正常
- ✅ 搜索响应时间 < 2秒

#### 1.3 异常检测功能
- ✅ 正确识别方差超过阈值的异常数据
- ✅ 异常数据实时更新统计
- ✅ 异常处理流程完整

#### 1.4 数据验证功能
- ✅ 批量验证功能正常
- ✅ 验证状态正确更新
- ✅ 验证日志完整记录

### 2. 性能验收标准
- ✅ 页面加载时间 < 3秒
- ✅ 大数据量查询响应时间 < 5秒
- ✅ 导入10万条记录用时 < 10分钟
- ✅ 支持50个并发用户操作

### 3. 用户体验验收标准
- ✅ 界面操作直观易懂
- ✅ 错误提示信息清晰
- ✅ 异常数据突出显示
- ✅ 操作流程符合用户习惯

### 4. 数据质量验收标准
- ✅ 数据导入准确率 > 99.9%
- ✅ 异常检测覆盖率 > 95%
- ✅ 数据验证完整性 100%
- ✅ 操作日志记录完整

---

## 📋 实施计划

### Phase 1: 基础功能开发 (2周)
- 基线记录CRUD功能
- 数据导入导出功能
- 基础查询和筛选功能

### Phase 2: 质量控制功能 (2周)
- 异常检测算法实现
- 数据验证流程
- 基线修正功能

### Phase 3: 监控分析功能 (1周)
- 统计仪表板
- 报表功能
- 趋势分析

### Phase 4: 集成测试优化 (1周)
- 移动端API集成
- 性能优化
- 用户体验优化

---

## 📞 支持与维护

### 1. 培训材料
- 用户操作手册
- 常见问题解答
- 操作视频教程

### 2. 技术支持
- 系统监控告警
- 定期数据备份
- 性能监控分析

### 3. 持续改进
- 用户反馈收集
- 功能优化迭代
- 新需求评估

---

**文档维护**: 此需求文档应随着业务需求变化及时更新，确保与实际系统功能保持一致。 