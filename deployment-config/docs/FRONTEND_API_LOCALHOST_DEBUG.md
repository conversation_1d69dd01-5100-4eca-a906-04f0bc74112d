# Docker部署Next.js应用时API请求指向localhost的排错实战

## 问题现象

在Linux服务器上使用Docker部署水表管理系统后，用户反馈前端功能异常。通过浏览器开发者工具检查发现，前端发出的API请求仍然指向 `http://localhost:5000/api/auth/login`，而不是预期的相对路径 `/api/auth/login`。

**关键症状**：
- 部署环境：Linux服务器 (*********)
- 架构：Next.js前端 + .NET Core后端 + PostgreSQL + Nginx反向代理
- 网络模式：Docker Host网络模式
- 错误：API请求使用绝对路径指向localhost而非相对路径

## 问题分析过程

### 第一次尝试：修改代码中的硬编码地址
**错误思路**：最初认为是代码中硬编码了localhost地址，尝试直接修改源码。
**用户纠正**：明确指出不应修改代码，因为本地开发需要这些默认值，应通过环境变量解决。

### 第二次尝试：检查环境变量配置
通过代码审查发现多个服务文件使用了不同的环境变量：

```typescript
// amis-sync.service.ts
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:5000';

// api.ts  
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || '/api';

// quickaccess.service.ts
baseURL: process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8080/api',
```

**发现问题**：不同服务使用了不同的环境变量名和默认值。

### 第三次尝试：Docker环境变量设置
在Dockerfile中设置环境变量：
```dockerfile
ENV NEXT_PUBLIC_API_BASE_URL=""
```

**新问题**：空字符串被JavaScript的 `||` 操作符忽略，继续使用默认值 `http://localhost:5000`。

### 第四次尝试：理解Next.js环境变量机制
**关键发现**：
1. `NEXT_PUBLIC_*` 环境变量必须在构建时设置才能生效
2. 空字符串会被 `||` 操作符忽略
3. 需要设置一个有效的非空值

## 根本原因

### 技术层面
1. **Next.js构建时环境变量**: `NEXT_PUBLIC_*` 变量在构建时被静态替换到代码中
2. **JavaScript逻辑问题**: `process.env.NEXT_PUBLIC_API_BASE_URL || 'default'` 中，空字符串被视为falsy值
3. **服务配置不一致**: 不同服务文件使用了不同的环境变量名和默认值

### 架构层面
```typescript
// 问题代码结构
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:5000';
// ...
baseURL: `${API_BASE_URL}/api`,  // 最终变成: http://localhost:5000/api
```

## 解决方案

### 最终修复
在Dockerfile中设置正确的环境变量值：

```dockerfile
# 构建阶段
ENV NEXT_PUBLIC_API_BASE_URL="."
ENV NEXT_PUBLIC_API_URL=/api
ENV BACKEND_URL=http://localhost:5000

# 运行阶段
ENV NEXT_PUBLIC_API_BASE_URL=""
ENV NEXT_PUBLIC_API_URL=/api
ENV BACKEND_URL=http://localhost:5000
```

### 为什么使用 "." 作为值？
- `API_BASE_URL` 变成 `"."`
- `baseURL` 变成 `"./api"`，这是正确的相对路径
- 避免了空字符串被 `||` 操作符忽略的问题

## 关键技术点总结

### 1. Next.js环境变量特性
- `NEXT_PUBLIC_*` 变量在构建时静态替换
- 运行时设置无效，必须在构建时正确配置

### 2. JavaScript逻辑陷阱
```javascript
// 问题代码
const value = process.env.VAR || 'default';
// 当 process.env.VAR = "" 时，仍然会使用 'default'

// 正确处理
const value = process.env.VAR !== undefined ? process.env.VAR : 'default';
```

### 3. Docker多阶段构建注意事项
- 构建阶段和运行阶段都需要正确设置环境变量
- 环境变量的值必须在JavaScript逻辑中有效

### 4. 微服务API配置统一性
- 确保所有服务使用相同的环境变量名
- 避免不同服务有不同的默认值配置

## 预防措施

### 1. 代码层面
```typescript
// 推荐的环境变量处理方式
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL !== undefined 
  ? process.env.NEXT_PUBLIC_API_BASE_URL 
  : 'http://localhost:5000';
```

### 2. 配置层面
- 统一环境变量命名规范
- 在不同环境中验证环境变量值
- 使用配置验证工具

### 3. 部署层面
- 构建时检查关键环境变量
- 部署后验证API请求路径
- 建立环境配置检查清单

## 总结

这次排错的核心教训是：**在微服务架构的Docker部署中，环境变量的设置不仅要考虑值的正确性，还要理解构建工具的处理机制和JavaScript的逻辑特性**。

Next.js的构建时环境变量替换机制，结合JavaScript的truthy/falsy判断，造成了看似简单的配置问题变得复杂。解决方案的关键在于为环境变量设置一个在目标逻辑中有效的值，而不是依赖默认值机制。

**经验教训**：
1. 深入理解框架的环境变量处理机制
2. 测试环境变量在不同值下的行为
3. 保持配置的一致性和可预测性
4. 建立完整的部署验证流程 