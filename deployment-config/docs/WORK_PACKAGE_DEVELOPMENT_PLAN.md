# Work Package & Task 两层架构开发计划

## 📋 概念澄清

### Work Package（工作包）- 管理层面
- **定义**：包含多个水表的工作集合
- **用途**：批量管理、重复使用、模板化
- **管理者**：系统管理员、调度员
- **包含内容**：
  - 一组相关的水表（meters）
  - 指定的读表顺序（read order）
  - 分配的现场人员
  - 执行频率（每月、每季度等）
  - 服务区域
  - 开始日期

### Task（任务）- 执行层面
- **定义**：单个水表的具体读表任务
- **用途**：现场人员具体执行的工作项
- **执行者**：现场抄表员
- **包含内容**：
  - 具体的水表ID
  - 地址和GPS位置
  - 上次读数
  - 执行状态
  - 完成时间

## 🎯 替换的现有概念

### 被替换的概念
1. **Schedule（计划）** → **Work Package（工作包）**
2. **部分 Route（路线）** → **集成到 Work Package 中**
3. **单独的 Assignment（分配）** → **Work Package Assignment（工作包分配）**

### 保留的概念
1. **Task（任务）** - 升级为执行层面的具体任务
2. **WaterMeter（水表）** - 基础数据模型
3. **User（用户）** - 现场人员和管理员

## 📅 开发计划（6周）

### Phase 1: 数据模型重构（第1周）

#### 1.1 新建 Work Package 相关模型
```csharp
// Models/WorkPackage.cs
public class WorkPackage : BaseEntity
{
    public string Name { get; set; }
    public string Description { get; set; }
    public string PackageType { get; set; } // Scheduled, Reactive, Emergency
    public string Status { get; set; } // Draft, Active, InProgress, Completed
    public DateTime PlannedStartDate { get; set; }
    public DateTime PlannedEndDate { get; set; }
    public string Frequency { get; set; } // Monthly, Quarterly, Annually
    public string ServiceArea { get; set; }
    public int TotalMeters { get; set; }
    public int CompletedMeters { get; set; }
    public decimal ProgressPercentage { get; set; }
    public string AssignedTeam { get; set; }
    public decimal EstimatedHours { get; set; }
    
    // Navigation Properties
    public virtual ICollection<WorkPackageItem> Items { get; set; }
    public virtual ICollection<WorkPackageAssignment> Assignments { get; set; }
}

// Models/WorkPackageItem.cs
public class WorkPackageItem : BaseEntity
{
    public int WorkPackageId { get; set; }
    public int MeterId { get; set; }
    public int SequenceOrder { get; set; }
    public string Status { get; set; } // Pending, InProgress, Completed
    public DateTime? ScheduledDate { get; set; }
    
    public virtual WorkPackage WorkPackage { get; set; }
    public virtual WaterMeter Meter { get; set; }
}

// Models/WorkPackageAssignment.cs
public class WorkPackageAssignment : BaseEntity
{
    public int WorkPackageId { get; set; }
    public string UserId { get; set; }
    public DateTime AssignedDate { get; set; }
    public string AssignmentType { get; set; } // Primary, Backup
    public string Status { get; set; } // Assigned, Accepted, InProgress, Completed
    
    public virtual WorkPackage WorkPackage { get; set; }
    public virtual User User { get; set; }
}
```

#### 1.2 升级 Task 模型
```csharp
// Models/WorkTask.cs (升级现有模型)
public class WorkTask : BaseEntity
{
    // 新增 Work Package 关联
    public int? WorkPackageId { get; set; }
    public int? WorkPackageItemId { get; set; }
    
    // 现有字段保持
    public string Name { get; set; }
    public string Status { get; set; }
    public string Priority { get; set; }
    public string Type { get; set; } = "MeterReading";
    public int? MeterId { get; set; } // 关联具体水表
    
    // 新增字段
    public DateTime? ScheduledDate { get; set; }
    public string ServiceAddress { get; set; }
    public decimal? Latitude { get; set; }
    public decimal? Longitude { get; set; }
    public string LastReading { get; set; }
    
    // Navigation Properties
    public virtual WorkPackage? WorkPackage { get; set; }
    public virtual WorkPackageItem? WorkPackageItem { get; set; }
    public virtual WaterMeter? Meter { get; set; }
}
```

#### 1.3 数据库迁移
- 创建新表的迁移脚本
- 数据迁移脚本（Schedule → WorkPackage）
- 索引优化

### Phase 2: 后端 API 开发（第2周）

#### 2.1 Work Package 管理 API
```csharp
// Controllers/WorkPackageController.cs
[ApiController]
[Route("api/[controller]")]
public class WorkPackageController : ControllerBase
{
    // CRUD 操作
    [HttpGet] // 获取工作包列表
    [HttpGet("{id}")] // 获取单个工作包详情
    [HttpPost] // 创建工作包
    [HttpPut("{id}")] // 更新工作包
    [HttpDelete("{id}")] // 删除工作包
    
    // 批量操作
    [HttpPost("bulk-create")] // 批量创建工作包
    [HttpPost("{id}/assign")] // 分配工作包
    [HttpPost("{id}/generate-tasks")] // 生成任务
    
    // CSV 导入/导出
    [HttpPost("import-csv")] // 从 CSV 导入工作包
    [HttpGet("export-csv")] // 导出工作包到 CSV
    
    // AMS 集成
    [HttpPost("import-ams")] // 从 AMS Excel 导入
}
```

#### 2.2 Task 管理 API 升级
```csharp
// Controllers/TaskController.cs (升级现有)
[ApiController]
[Route("api/[controller]")]
public class TaskController : ControllerBase
{
    // 新增 Work Package 相关接口
    [HttpGet("by-work-package/{workPackageId}")] // 获取工作包的所有任务
    [HttpPost("generate-from-work-package")] // 从工作包生成任务
    [HttpGet("mobile/assigned/{userId}")] // 移动端：获取用户分配的任务
    [HttpPost("mobile/update-status")] // 移动端：更新任务状态
}
```

#### 2.3 服务层开发
```csharp
// Services/WorkPackageService.cs
public interface IWorkPackageService
{
    Task<WorkPackageDto> CreateWorkPackageAsync(CreateWorkPackageDto dto);
    Task<WorkPackageDto> GetWorkPackageAsync(int id);
    Task<PagedResult<WorkPackageListDto>> GetWorkPackagesAsync(WorkPackageSearchDto search);
    Task<bool> AssignWorkPackageAsync(int workPackageId, List<string> userIds);
    Task<List<WorkTaskDto>> GenerateTasksFromWorkPackageAsync(int workPackageId);
    Task<(bool Success, string Message)> ImportFromCsvAsync(Stream csvStream);
    Task<(bool Success, string Message)> ImportFromAmsAsync(Stream excelStream);
}
```

### Phase 3: 前端管理界面开发（第3周）

#### 3.1 菜单结构调整
```typescript
// 新的菜单结构
const menuItems = [
  {
    key: 'work-packages',
    label: 'Work Package Management',
    children: [
      { key: 'work-packages/list', label: 'Work Package List' },
      { key: 'work-packages/create', label: 'Create Work Package' },
      { key: 'work-packages/templates', label: 'Package Templates' },
      { key: 'work-packages/assignments', label: 'Package Assignments' },
      { key: 'work-packages/frequency', label: 'Frequency Settings' }
    ]
  },
  {
    key: 'task-monitoring',
    label: 'Task Monitoring',
    children: [
      { key: 'tasks/active', label: 'Active Tasks' },
      { key: 'tasks/completed', label: 'Completed Tasks' },
      { key: 'tasks/overdue', label: 'Overdue Tasks' },
      { key: 'tasks/performance', label: 'Task Performance' }
    ]
  }
]
```

#### 3.2 Work Package 管理页面
- **Work Package List**: 工作包列表，支持筛选、搜索、批量操作
- **Create Work Package**: 创建工作包向导，支持选择水表、设置参数
- **Package Assignment**: 工作包分配界面，支持批量分配给用户
- **Package Templates**: 可重用的工作包模板管理

#### 3.3 Task 监控页面
- **Task Dashboard**: 任务总览仪表板
- **Active Tasks**: 进行中的任务列表
- **Task Performance**: 任务执行绩效分析

### Phase 4: 移动端适配（第4周）

#### 4.1 移动端 API 适配
```typescript
// 移动端专用接口
interface MobileTaskApi {
  getAssignedTasks(userId: string): Promise<MobileTaskDto[]>;
  updateTaskStatus(taskId: number, status: string): Promise<boolean>;
  submitMeterReading(taskId: number, reading: MeterReadingDto): Promise<boolean>;
  syncOfflineData(): Promise<SyncResult>;
}
```

#### 4.2 移动端界面调整
- **Task List Screen**: 显示分配给用户的具体任务
- **Task Detail Screen**: 任务详情，包含水表信息、位置、历史读数
- **Work Package Overview**: 工作包概览（只读）

### Phase 5: CSV 导入/导出功能（第5周）

#### 5.1 CSV 格式定义
```csv
// work_package_template.csv
PackageName,Description,ServiceArea,Frequency,PlannedStartDate,MeterIds,AssignedUsers
"North Zone January","北区1月抄表","North Zone","Monthly","2025-01-01","M001,M002,M003","user1,user2"
```

#### 5.2 导入/导出服务
```csharp
// Services/CsvImportExportService.cs
public interface ICsvImportExportService
{
    Task<(bool Success, string Message, int ImportedCount)> ImportWorkPackagesAsync(Stream csvStream);
    Task<byte[]> ExportWorkPackagesToCsvAsync(WorkPackageSearchDto? filter = null);
    Task<byte[]> ExportTasksToCsvAsync(TaskSearchDto? filter = null);
}
```

### Phase 6: AMS 集成和测试（第6周）

#### 6.1 AMS Excel 导入增强
- 支持从 AMS Excel 直接创建 Work Package
- 自动识别路线信息并创建工作包
- 数据验证和错误处理

#### 6.2 端到端测试
- Work Package 创建到 Task 执行的完整流程
- 移动端和 Web 端数据同步
- 性能测试和优化

## 🚀 实施优先级

### 高优先级（必须完成）
1. ✅ Work Package 和 Task 数据模型
2. ✅ 基础 CRUD API
3. ✅ Web 端工作包管理界面
4. ✅ CSV 导入/导出功能

### 中优先级（应该完成）
1. 🔄 移动端任务列表适配
2. 🔄 AMS Excel 导入增强
3. 🔄 工作包模板功能
4. 🔄 任务监控仪表板

### 低优先级（可以推迟）
1. ⏳ 高级报表和分析
2. ⏳ 自动化工作包生成
3. ⏳ 地图集成优化
4. ⏳ 移动端离线同步优化

## 📊 成功标准

### 技术指标
- Work Package 创建时间 < 30秒（包含100个水表）
- Task 生成时间 < 10秒（从 Work Package）
- 移动端任务列表加载时间 < 3秒
- CSV 导入成功率 > 95%

### 业务指标
- 支持批量创建工作包（一次处理500+水表）
- 支持工作包模板重用
- 现场人员任务执行效率提升30%
- 管理员工作包管理效率提升50%

## 🔄 迁移策略

### 数据迁移
1. **Schedule → Work Package**: 现有调度数据转换为工作包
2. **Assignment → Work Package Assignment**: 分配关系迁移
3. **Task 升级**: 现有任务关联到工作包

### 向后兼容
1. 保留现有 API 端点（标记为 deprecated）
2. 渐进式迁移，支持新旧系统并行运行
3. 移动端逐步升级，支持旧版本

## 📝 下一步行动

1. **立即开始**: Phase 1 数据模型设计和创建
2. **本周完成**: Work Package 基础模型和迁移脚本
3. **下周开始**: 后端 API 开发
4. **两周后**: 前端界面开发

这个计划将彻底重构我们的工作管理方式，从分散的调度+分配模式升级为统一的工作包+任务两层架构，大大提高管理效率和用户体验。 