# 移动端API建立实施计划

## 📋 Phase 1 详细实施计划

### 🎯 目标
建立WaterMeterManagement后端与MeterReadingApp移动端的API连接，实现任务分配功能的基础架构。

## 🔧 后端API开发计划

### 1.1 创建移动端控制器

#### 步骤1: 创建MobileTaskController
**文件位置**: `WaterMeterManagement/Controllers/MobileTaskController.cs`

**功能要求**:
- 继承ControllerBase
- 支持认证授权
- 返回移动端优化的数据格式
- 包含错误处理和日志记录

**实现任务**:
```csharp
[ApiController]
[Route("api/mobile/tasks")]
[Authorize] // 需要认证
public class MobileTaskController : ControllerBase
{
    // 依赖注入
    private readonly ITaskService _taskService;
    private readonly ILogger<MobileTaskController> _logger;
    private readonly IUserService _userService;
    
    // 构造函数
    public MobileTaskController(
        ITaskService taskService, 
        ILogger<MobileTaskController> logger,
        IUserService userService)
    
    // API端点实现
    [HttpGet("my-assignments")]           // 获取我的分配任务
    [HttpPut("{taskId}/start")]          // 开始任务
    [HttpPut("{taskId}/complete")]       // 完成任务
    [HttpPut("{taskId}/status")]         // 更新任务状态
    [HttpGet("{taskId}/detail")]         // 获取任务详情
}
```

#### 步骤2: 创建MobileReadingController
**文件位置**: `WaterMeterManagement/Controllers/MobileReadingController.cs`

**功能要求**:
- 处理读数数据提交
- 支持照片上传
- 批量数据处理
- 数据验证

**实现任务**:
```csharp
[ApiController]
[Route("api/mobile/readings")]
[Authorize]
public class MobileReadingController : ControllerBase
{
    [HttpPost]                           // 提交单个读数
    [HttpPost("batch")]                  // 批量提交读数
    [HttpPost("{readingId}/photos")]     // 上传读数照片
    [HttpGet("my-readings")]             // 获取我的读数历史
}
```

#### 步骤3: 创建MobileUserController
**文件位置**: `WaterMeterManagement/Controllers/MobileUserController.cs`

**功能要求**:
- 用户工作负载信息
- 用户统计数据
- 位置更新

**实现任务**:
```csharp
[ApiController]
[Route("api/mobile/users")]
[Authorize]
public class MobileUserController : ControllerBase
{
    [HttpGet("my-workload")]             // 获取我的工作负载
    [HttpGet("my-stats")]                // 获取我的统计信息
    [HttpPost("location")]               // 更新用户位置
    [HttpGet("profile")]                 // 获取用户profile
}
```

### 1.2 创建移动端DTO

#### 步骤1: 创建MobileTaskDto
**文件位置**: `WaterMeterManagement/DTOs/Mobile/MobileTaskDto.cs`

```csharp
namespace WaterMeterManagement.DTOs.Mobile
{
    public class MobileTaskDto
    {
        public int Id { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public string Status { get; set; }
        public string Priority { get; set; }
        public string Type { get; set; }
        public DateTime? DueDate { get; set; }
        public DateTime? StartDate { get; set; }
        public string Location { get; set; }
        public string Instructions { get; set; }
        public string Notes { get; set; }
        
        // 水表信息
        public int MeterId { get; set; }
        public string MeterNumber { get; set; }
        public string MeterType { get; set; }
        public double? LastReading { get; set; }
        public DateTime? LastReadingDate { get; set; }
        
        // 客户信息
        public string CustomerName { get; set; }
        public string CustomerPhone { get; set; }
        public string CustomerEmail { get; set; }
        public string Address { get; set; }
        
        // 位置信息
        public double? Latitude { get; set; }
        public double? Longitude { get; set; }
        
        // 分配信息
        public int AssignmentId { get; set; }
        public DateTime AssignedDate { get; set; }
        public string AssignedBy { get; set; }
        public string AssignmentType { get; set; }
        
        // 估算信息
        public int? EstimatedHours { get; set; }
        public int ProgressPercentage { get; set; }
    }
}
```

#### 步骤2: 创建MobileReadingDto
**文件位置**: `WaterMeterManagement/DTOs/Mobile/MobileReadingDto.cs`

```csharp
public class MobileReadingDto
{
    public int TaskId { get; set; }
    public int MeterId { get; set; }
    public double ReadingValue { get; set; }
    public DateTime ReadingDate { get; set; }
    public double? Latitude { get; set; }
    public double? Longitude { get; set; }
    public string ReadingMethod { get; set; } // manual, ocr, automatic
    public string Notes { get; set; }
    public int? QualityScore { get; set; } // 1-10, OCR confidence
    public List<string> PhotoPaths { get; set; } = new();
    public string DeviceInfo { get; set; }
    public bool IsOfflineReading { get; set; }
}

public class CompleteTaskRequest
{
    public MobileReadingDto Reading { get; set; }
    public string CompletionNotes { get; set; }
    public List<string> IssuesEncountered { get; set; } = new();
}

public class ReadingResponseDto
{
    public int ReadingId { get; set; }
    public bool Success { get; set; }
    public string Message { get; set; }
    public List<string> Warnings { get; set; } = new();
    public List<string> Errors { get; set; } = new();
}
```

#### 步骤3: 创建移动端用户DTO
**文件位置**: `WaterMeterManagement/DTOs/Mobile/MobileUserDto.cs`

```csharp
public class MobileUserWorkloadDto
{
    public int UserId { get; set; }
    public string UserName { get; set; }
    public string FullName { get; set; }
    public string Email { get; set; }
    public string Department { get; set; }
    public string Zone { get; set; }
    
    // 工作负载统计
    public int ActiveTaskCount { get; set; }
    public int CompletedTaskCount { get; set; }
    public int OverdueTaskCount { get; set; }
    public int TotalAssignedToday { get; set; }
    public double WorkloadPercentage { get; set; }
    public double EfficiencyScore { get; set; }
    
    // 今日统计
    public int TodayCompletedCount { get; set; }
    public int TodayPendingCount { get; set; }
    public double TodayProgress { get; set; }
    
    // 状态信息
    public string AvailabilityStatus { get; set; }
    public DateTime? LastActivity { get; set; }
    public DateTime? LastLocationUpdate { get; set; }
    public double? CurrentLatitude { get; set; }
    public double? CurrentLongitude { get; set; }
}

public class MobileUserStatsDto
{
    public int TotalTasksAssigned { get; set; }
    public int TotalTasksCompleted { get; set; }
    public int TotalReadingsSubmitted { get; set; }
    public double AverageCompletionTime { get; set; } // 小时
    public double CompletionRate { get; set; } // 百分比
    public int CurrentStreak { get; set; } // 连续完成天数
    
    // 本周统计
    public int WeekTasksCompleted { get; set; }
    public int WeekReadingsSubmitted { get; set; }
    
    // 本月统计
    public int MonthTasksCompleted { get; set; }
    public int MonthReadingsSubmitted { get; set; }
    
    // 质量指标
    public double AverageQualityScore { get; set; }
    public int PhotosSubmitted { get; set; }
    public int IssuesReported { get; set; }
}

public class LocationUpdateDto
{
    public double Latitude { get; set; }
    public double Longitude { get; set; }
    public double? Accuracy { get; set; }
    public DateTime Timestamp { get; set; }
    public string ActivityType { get; set; } // reading, travel, break
}
```

### 1.3 服务层扩展

#### 步骤1: 扩展TaskService
**文件位置**: `WaterMeterManagement/Services/Interfaces/ITaskService.cs`

**新增方法**:
```csharp
public interface ITaskService
{
    // 现有方法...
    
    // 移动端专用方法
    Task<List<MobileTaskDto>> GetMyAssignedTasksAsync(int userId);
    Task<MobileTaskDetailDto> GetMobileTaskDetailAsync(int taskId, int userId);
    Task<bool> StartTaskAsync(int taskId, int userId);
    Task<bool> CompleteTaskAsync(int taskId, CompleteTaskRequest request, int userId);
    Task<bool> UpdateTaskStatusMobileAsync(int taskId, string status, int userId);
    Task<MobileUserWorkloadDto> GetUserWorkloadMobileAsync(int userId);
}
```

#### 步骤2: 实现移动端服务
**文件位置**: `WaterMeterManagement/Services/TaskService.cs`

**实现任务**:
```csharp
public async Task<List<MobileTaskDto>> GetMyAssignedTasksAsync(int userId)
{
    // 1. 查询分配给用户的任务
    // 2. 包含水表信息、客户信息、位置信息
    // 3. 过滤状态（只返回active状态的任务）
    // 4. 按优先级和到期日期排序
    // 5. 转换为MobileTaskDto格式
}

public async Task<bool> StartTaskAsync(int taskId, int userId)
{
    // 1. 验证任务是否分配给该用户
    // 2. 检查任务当前状态
    // 3. 更新任务状态为"In Progress"
    // 4. 记录开始时间
    // 5. 更新分配状态
    // 6. 记录操作日志
}
```

### 1.4 认证机制配置

#### 步骤1: 配置移动端认证
**文件位置**: `WaterMeterManagement/Program.cs`

**配置任务**:
```csharp
// 为移动端API配置认证
builder.Services.AddAuthentication("Mobile")
    .AddScheme<MobileAuthenticationSchemeOptions, MobileAuthenticationHandler>(
        "Mobile", 
        options => { }
    );

// 配置授权策略
builder.Services.AddAuthorization(options =>
{
    options.AddPolicy("MobileUser", policy =>
        policy.RequireAuthenticatedUser()
              .RequireClaim("UserType", "Mobile"));
});
```

#### 步骤2: 创建移动端认证处理器
**文件位置**: `WaterMeterManagement/Authentication/MobileAuthenticationHandler.cs`

**实现任务**:
```csharp
public class MobileAuthenticationHandler : AuthenticationHandler<MobileAuthenticationSchemeOptions>
{
    // 处理移动端认证逻辑
    // 支持Basic Authentication
    // 验证用户名密码
    // 返回Claims身份信息
}
```

## 📱 移动端API集成计划

### 2.1 更新BaseApi配置

#### 步骤1: 修改API基础URL
**文件位置**: `MeterReadingApp/src/api/BaseApi.ts`

**修改任务**:
```typescript
// 从当前的SICON API切换到本地WaterMeterManagement API
const BASE_URL = 'http://localhost:5000/api'; // 开发环境
// const BASE_URL = 'https://your-production-url/api'; // 生产环境

// 为移动端API添加特殊配置
const MOBILE_API_BASE = `${BASE_URL}/mobile`;
```

#### 步骤2: 增强认证机制
**修改任务**:
```typescript
// 支持移动端token认证
// 添加设备信息到请求头
// 支持离线token缓存
// 添加自动重试机制
```

### 2.2 创建移动端API服务

#### 步骤1: 创建MobileTaskApi
**文件位置**: `MeterReadingApp/src/api/MobileTaskApi.ts`

```typescript
import api from './BaseApi';
import { MobileTaskDto, MobileTaskDetailDto, CompleteTaskRequest } from '../types/mobile';

export class MobileTaskApi {
  // 获取我的分配任务
  static async getMyAssignedTasks(): Promise<MobileTaskDto[]> {
    const response = await api.get('/mobile/tasks/my-assignments');
    return response.data;
  }
  
  // 开始任务
  static async startTask(taskId: number): Promise<boolean> {
    const response = await api.put(`/mobile/tasks/${taskId}/start`);
    return response.data.success;
  }
  
  // 完成任务
  static async completeTask(taskId: number, request: CompleteTaskRequest): Promise<any> {
    const response = await api.put(`/mobile/tasks/${taskId}/complete`, request);
    return response.data;
  }
  
  // 更新任务状态
  static async updateTaskStatus(taskId: number, status: string): Promise<boolean> {
    const response = await api.put(`/mobile/tasks/${taskId}/status`, { status });
    return response.data.success;
  }
  
  // 获取任务详情
  static async getTaskDetail(taskId: number): Promise<MobileTaskDetailDto> {
    const response = await api.get(`/mobile/tasks/${taskId}/detail`);
    return response.data;
  }
}
```

#### 步骤2: 创建MobileReadingApi
**文件位置**: `MeterReadingApp/src/api/MobileReadingApi.ts`

```typescript
export class MobileReadingApi {
  // 提交单个读数
  static async submitReading(reading: MobileReadingDto): Promise<ReadingResponseDto>
  
  // 批量提交读数
  static async submitBatchReadings(readings: MobileReadingDto[]): Promise<BatchReadingResponseDto>
  
  // 上传照片
  static async uploadPhotos(readingId: number, photos: File[]): Promise<any>
  
  // 获取读数历史
  static async getMyReadings(): Promise<MobileReadingDto[]>
}
```

#### 步骤3: 创建MobileUserApi
**文件位置**: `MeterReadingApp/src/api/MobileUserApi.ts`

```typescript
export class MobileUserApi {
  // 获取我的工作负载
  static async getMyWorkload(): Promise<MobileUserWorkloadDto>
  
  // 获取我的统计信息
  static async getMyStats(): Promise<MobileUserStatsDto>
  
  // 更新位置
  static async updateLocation(location: LocationUpdateDto): Promise<boolean>
  
  // 获取个人资料
  static async getProfile(): Promise<MobileUserDto>
}
```

### 2.3 数据模型同步

#### 步骤1: 创建移动端类型定义
**文件位置**: `MeterReadingApp/src/types/mobile.ts`

```typescript
// 导入所有移动端相关的TypeScript类型定义
export interface MobileTaskDto {
  id: number;
  name: string;
  description?: string;
  status: 'assigned' | 'in_progress' | 'completed' | 'cancelled';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  type: 'reading' | 'maintenance' | 'inspection';
  dueDate?: string;
  startDate?: string;
  location?: string;
  instructions?: string;
  notes?: string;
  
  // 水表信息
  meterId: number;
  meterNumber: string;
  meterType?: string;
  lastReading?: number;
  lastReadingDate?: string;
  
  // 客户信息
  customerName?: string;
  customerPhone?: string;
  customerEmail?: string;
  address?: string;
  
  // 位置信息
  latitude?: number;
  longitude?: number;
  
  // 分配信息
  assignmentId: number;
  assignedDate: string;
  assignedBy: string;
  assignmentType: string;
  
  // 进度信息
  estimatedHours?: number;
  progressPercentage: number;
}

// ... 其他类型定义
```

#### 步骤2: 更新本地数据库模型
**文件位置**: `MeterReadingApp/src/database/models/index.ts`

**修改任务**:
```typescript
// 扩展Task接口以支持移动端数据
export interface Task {
  // 现有字段...
  
  // 新增移动端字段
  assignment_id?: number;
  meter_number?: string;
  customer_name?: string;
  customer_phone?: string;
  customer_email?: string;
  address?: string;
  latitude?: number;
  longitude?: number;
  due_date?: string;
  instructions?: string;
  estimated_hours?: number;
  progress_percentage?: number;
}
```

## 🧪 测试计划

### 3.1 后端API测试

#### 单元测试
```csharp
// MobileTaskControllerTests.cs
[TestClass]
public class MobileTaskControllerTests
{
    [TestMethod]
    public async Task GetMyAssignedTasks_ShouldReturnUserTasks()
    
    [TestMethod]
    public async Task StartTask_ShouldUpdateStatus()
    
    [TestMethod]
    public async Task CompleteTask_ShouldSubmitReading()
}
```

#### 集成测试
- API端点响应测试
- 数据库操作测试
- 认证授权测试

### 3.2 移动端API测试

#### API连接测试
```typescript
// __tests__/api/MobileTaskApi.test.ts
describe('MobileTaskApi', () => {
  test('should fetch assigned tasks', async () => {
    const tasks = await MobileTaskApi.getMyAssignedTasks();
    expect(tasks).toBeInstanceOf(Array);
  });
  
  test('should start task successfully', async () => {
    const result = await MobileTaskApi.startTask(1);
    expect(result).toBe(true);
  });
});
```

### 3.3 端到端测试

#### 测试场景
1. **用户登录流程测试**
2. **任务获取测试**
3. **任务状态更新测试**
4. **读数提交测试**
5. **离线模式测试**

## 📅 实施时间表

### Day 1: 后端API开发
- **Morning (9:00-12:00)**
  - 创建MobileTaskController
  - 创建基础DTO
  - 配置路由和认证
  
- **Afternoon (13:00-17:00)**
  - 实现TaskService移动端方法
  - 创建MobileReadingController
  - 创建MobileUserController

### Day 1: 移动端API集成
- **Evening (18:00-21:00)**
  - 更新BaseApi配置
  - 创建MobileTaskApi
  - 创建移动端类型定义
  - 基础连接测试

## ✅ 验收标准

### 后端API验收
- [ ] 所有移动端API端点正常响应
- [ ] 认证机制工作正常
- [ ] 数据格式符合移动端需求
- [ ] API响应时间 < 2秒
- [ ] 单元测试覆盖率 > 80%

### 移动端集成验收
- [ ] 成功连接到后端API
- [ ] 用户登录后能获取任务列表
- [ ] 任务状态更新成功同步
- [ ] 错误处理机制完善
- [ ] 离线模式基础支持

### 性能标准
- [ ] API响应时间 < 2秒
- [ ] 移动端加载时间 < 5秒
- [ ] 数据同步成功率 > 95%
- [ ] 内存使用 < 50MB

## 🚨 风险点与应对

### 技术风险
1. **API兼容性问题**
   - 应对：严格的数据格式定义和验证
   
2. **认证机制复杂性**
   - 应对：简化认证流程，使用成熟方案
   
3. **数据同步冲突**
   - 应对：明确的冲突解决策略

### 进度风险
1. **开发时间不足**
   - 应对：优先实现核心功能，次要功能可延后
   
2. **测试时间压缩**
   - 应对：边开发边测试，自动化测试

## 📝 文档要求

### API文档
- [ ] Swagger/OpenAPI规范
- [ ] 请求/响应示例
- [ ] 错误码说明
- [ ] 认证方式说明

### 开发文档
- [ ] 代码注释完整
- [ ] README更新
- [ ] 部署指南
- [ ] 故障排除指南

---

*文档版本: v1.0*  
*创建日期: 2024-01-XX*  
*预计完成: Phase 1 - Day 1* 