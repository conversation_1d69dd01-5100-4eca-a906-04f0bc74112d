# 🧠 水表识别模型训练方案 (基于150张照片)

## 🎯 目标调整

**基础版本目标**：
- 基于150张真实水表照片训练专用模型
- 验证技术路线可行性
- 为后续数据扩展建立基础架构
- 目标准确率：**读数识别 ≥ 80%**，**编号识别 ≥ 75%**

## 📊 数据策略 (针对小数据集优化)

### 数据划分 (150张)
```
├── 训练集：105张 (70%)
├── 验证集：30张 (20%) 
└── 测试集：15张 (10%)
```

### 数据增强策略 (关键！)
**目标：将105张扩展到2000+张训练样本**

```python
# 激进数据增强配置 (小数据集必须)
augmentation_pipeline = albumentations.Compose([
    # 几何变换
    A.Rotate(limit=15, p=0.8),
    A.Perspective(scale=(0.05, 0.15), p=0.6),
    A.<PERSON>ft<PERSON>caleRotate(shift_limit=0.1, scale_limit=0.2, rotate_limit=10, p=0.8),
    
    # 光照变换 (水表拍摄条件变化大)
    A.RandomBrightnessContrast(brightness_limit=0.3, contrast_limit=0.3, p=0.9),
    A.RandomGamma(gamma_limit=(70, 130), p=0.5),
    A.RandomShadow(shadow_roi=(0, 0.5, 1, 1), num_shadows_lower=1, num_shadows_upper=3, p=0.4),
    
    # 质量模拟
    A.GaussNoise(var_limit=(10, 80), p=0.6),
    A.Blur(blur_limit=4, p=0.4),
    A.MotionBlur(blur_limit=5, p=0.3),
    
    # 颜色变换
    A.HueSaturationValue(hue_shift_limit=20, sat_shift_limit=30, val_shift_limit=20, p=0.5),
    A.ColorJitter(brightness=0.2, contrast=0.2, saturation=0.2, hue=0.1, p=0.5),
    
    # 天气模拟
    A.RandomRain(slant_lower=-10, slant_upper=10, drop_length=10, drop_width=1, p=0.2),
    A.RandomFog(fog_coef_lower=0.1, fog_coef_upper=0.3, p=0.2),
])

# 每张原图生成20个变体 = 105 * 20 = 2100张训练图
augmentation_factor = 20
```

## 🏗 模型架构 (小数据集优化)

### Stage 1: 水表检测 (Transfer Learning)

```python
# 使用预训练YOLOv8n + 微调
from ultralytics import YOLO

# 1. 加载COCO预训练权重
model = YOLO('yolov8n.pt')

# 2. 冻结backbone，只训练head
def freeze_backbone(model):
    for name, param in model.model.named_parameters():
        if 'head' not in name:  # 冻结除了head以外的所有层
            param.requires_grad = False
    return model

# 3. 分阶段训练
# 阶段1: 冻结backbone，快速适应
frozen_model = freeze_backbone(model)
frozen_model.train(data='water_meter.yaml', epochs=50, lr0=0.01)

# 阶段2: 解冻backbone，精细调优
model.train(data='water_meter.yaml', epochs=100, lr0=0.001)
```

### Stage 2: 字符识别 (轻量化CRNN)

```python
class LightweightCRNN(nn.Module):
    def __init__(self, num_classes=37):  # 0-9 + A-Z + 特殊字符
        super().__init__()
        # 轻量化CNN (MobileNetV3风格)
        self.feature_extractor = nn.Sequential(
            # 第一阶段：基础特征
            nn.Conv2d(3, 32, 3, padding=1),
            nn.BatchNorm2d(32),
            nn.ReLU(),
            nn.MaxPool2d(2),
            
            # 第二阶段：深度可分离卷积
            self._make_depthwise_conv(32, 64),
            nn.MaxPool2d(2),
            
            self._make_depthwise_conv(64, 128),
            nn.MaxPool2d((2, 1)),  # 只在高度方向pool
            
            # 第三阶段：特征精炼
            self._make_depthwise_conv(128, 256),
            nn.AdaptiveAvgPool2d((1, None)),  # 高度压缩为1
        )
        
        # 序列建模 (轻量化LSTM)
        self.rnn = nn.LSTM(256, 128, batch_first=True, bidirectional=True)
        
        # 字符分类
        self.classifier = nn.Linear(256, num_classes)
        
    def _make_depthwise_conv(self, in_channels, out_channels):
        return nn.Sequential(
            # 深度可分离卷积 (减少参数)
            nn.Conv2d(in_channels, in_channels, 3, padding=1, groups=in_channels),
            nn.Conv2d(in_channels, out_channels, 1),
            nn.BatchNorm2d(out_channels),
            nn.ReLU(),
        )
```

## 🎯 训练策略 (小数据集特化)

### 1. 渐进式训练

```python
# 训练阶段规划
training_phases = {
    "Phase 1": {
        "description": "水表检测基础训练",
        "data": "150张原图 + 基础增强(5x)",
        "epochs": 50,
        "focus": "检测水表位置"
    },
    "Phase 2": {
        "description": "区域定位精细化",
        "data": "增强数据 + 手工调优的困难样本",
        "epochs": 100,
        "focus": "准确定位读数和编号区域"
    },
    "Phase 3": {
        "description": "字符识别训练",
        "data": "裁剪的区域图像 + 激进增强",
        "epochs": 200,
        "focus": "字符序列识别"
    },
    "Phase 4": {
        "description": "端到端优化",
        "data": "全流程数据",
        "epochs": 50,
        "focus": "整体pipeline优化"
    }
}
```

### 2. 数据有效性最大化

```python
# 困难样本挖掘
def hard_negative_mining(model, dataset, threshold=0.3):
    """找出模型表现差的样本，重点训练"""
    hard_samples = []
    for img, label in dataset:
        pred = model(img)
        if accuracy(pred, label) < threshold:
            hard_samples.append((img, label))
    return hard_samples

# 主动学习策略
def active_learning_selection(model, unlabeled_pool, n_select=20):
    """选择模型最不确定的样本进行标注"""
    uncertainties = []
    for img in unlabeled_pool:
        pred = model(img)
        uncertainty = calculate_uncertainty(pred)
        uncertainties.append((img, uncertainty))
    
    # 选择最不确定的样本
    selected = sorted(uncertainties, key=lambda x: x[1], reverse=True)[:n_select]
    return selected
```

### 3. 小数据集验证策略

```python
# K-fold交叉验证 (小数据集必须)
from sklearn.model_selection import KFold

def k_fold_validation(dataset, k=5):
    kf = KFold(n_splits=k, shuffle=True, random_state=42)
    results = []
    
    for fold, (train_idx, val_idx) in enumerate(kf.split(dataset)):
        print(f"Training Fold {fold+1}/{k}")
        
        # 分割数据
        train_data = [dataset[i] for i in train_idx]
        val_data = [dataset[i] for i in val_idx]
        
        # 训练模型
        model = create_model()
        model.train(train_data)
        
        # 验证
        acc = model.evaluate(val_data)
        results.append(acc)
    
    return np.mean(results), np.std(results)
```

## 📝 标注策略 (高效标注)

### 分层标注方法

```json
{
  "image_id": "meter_001.jpg",
  "image_size": {"width": 1024, "height": 768},
  "annotations": {
    "stage1_detection": {
      "meter_bbox": [100, 50, 400, 500],
      "meter_type": "mechanical_circular"
    },
    "stage2_regions": {
      "reading_bbox": [150, 200, 300, 80],
      "serial_bbox": [120, 450, 350, 40],
      "model_bbox": [200, 500, 200, 30]
    },
    "stage3_text": {
      "reading": "001234.567",
      "serial": "WM2024001", 
      "model": "DM-15"
    }
  }
}
```

### 标注工具建议

1. **检测标注**: LabelImg (免费，简单易用)
2. **文本标注**: 自定义Python脚本
3. **质量检查**: LabelStudio (可视化验证)

```python
# 自动化标注辅助脚本
def auto_annotation_assistant(image_path):
    """使用现有OCR预标注，人工校验"""
    # 1. 用现有MLKit粗略识别
    rough_text = mlkit_recognize(image_path)
    
    # 2. 生成候选标注
    candidates = {
        "reading": extract_numbers(rough_text),
        "serial": extract_alphanumeric(rough_text)
    }
    
    # 3. 人工校验界面
    verified = manual_verify_ui(image_path, candidates)
    return verified
```

## ⏰ 实施时间表 (紧凑版)

| 周次 | 任务 | 具体工作 | 产出 |
|------|------|----------|------|
| **Week 1** | 数据准备 | 150张照片整理、分类、质量评估 | 数据集划分 |
| **Week 2** | 标注工作 | 检测框+文本标注，质量检查 | 标注数据集 |
| **Week 3** | 增强+检测训练 | 数据增强，YOLOv8训练 | 检测模型v1 |
| **Week 4** | 识别训练 | CRNN训练，端到端集成 | 识别模型v1 |
| **Week 5** | 优化+转换 | 模型优化，TFLite转换 | 移动端模型 |
| **Week 6** | 验证+调优 | 性能测试，参数调优 | 最终模型 |

**总计：6周**

## 📊 验收指标 (现实版本)

### 核心指标

| 指标 | 基础版目标 | 验收标准 |
|------|------------|----------|
| **读数识别准确率** | ≥ 80% | 测试集15张照片 |
| **编号识别准确率** | ≥ 75% | 清晰照片为主 |
| **模型大小** | ≤ 25MB | 两个模型总和 |
| **推理时间** | ≤ 4秒 | Android中端机 |

### 细分指标

```python
validation_metrics = {
    "reading_recognition": {
        "exact_match": 0.80,      # 完全匹配
        "digit_level": 0.90,      # 单个数字准确率
        "sequence_similarity": 0.85 # 序列相似度
    },
    "serial_recognition": {
        "exact_match": 0.75,
        "character_level": 0.85
    },
    "detection_performance": {
        "meter_detection": 0.95,   # 水表检测率
        "region_localization": 0.85 # 区域定位准确率
    },
    "robustness": {
        "different_lighting": 0.75,
        "different_angles": 0.70,
        "blurry_images": 0.65
    }
}
```

## 🚀 交付清单

### 模型文件
- `water_meter_detector_v1.tflite` (~12MB)
- `meter_text_recognizer_v1.tflite` (~10MB)

### 代码和文档
- 训练代码和脚本
- 模型性能报告
- Android集成示例代码
- 标注数据集 (可用于后续扩展)

### 扩展准备
- 数据扩展计划
- 模型迭代roadmap
- 新数据集成流程

## 🔄 后续扩展策略

**基础版成功后的扩展路径**：

1. **数据扩展** (200张 → 500张 → 1000张+)
2. **模型增强** (更大的网络，更复杂的架构)
3. **功能扩展** (更多字段识别，缺陷检测)
4. **性能优化** (更快的推理，更小的模型)

---

**这个方案专门针对150张照片优化，重点是快速验证可行性，为后续扩展奠定基础！** 🎯 