# Water Meter Management System - Registry模式部署完整方案

## 📋 部署架构概述

### 技术栈
- **后端**: ASP.NET Core 8.0 + Entity Framework Core
- **前端**: Next.js 14
- **数据库**: PostgreSQL 16
- **Web服务器**: Nginx
- **容器化**: Docker + Docker Compose
- **镜像管理**: 私有Docker Registry

### 网络架构
- **网络模式**: Host模式（简化网络配置）
- **端口分配**:
  - Frontend: 3000 (内部)
  - Backend API: 5000 (内部)
  - Docker Registry: 5001 (内部)
  - PostgreSQL: 5432 (内部)
  - Nginx: 80/443 (对外)

### 部署策略
- **构建方式**: 本地构建镜像，推送到服务器私有Registry
- **数据迁移**: 使用EF Core自动迁移机制
- **配置管理**: 环境变量 + Docker Compose
- **日志管理**: 持久化存储到宿主机

## 🚀 完整分步部署流程

### 第一步：服务器环境准备

#### 1.1 SSH连接服务器
```bash
ssh user@*********
```

#### 1.2 更新系统
```bash
sudo apt update
sudo apt upgrade -y
```

#### 1.3 安装Docker
```bash
# 下载Docker安装脚本
curl -fsSL https://get.docker.com -o get-docker.sh

# 执行安装
sudo sh get-docker.sh

# 将当前用户加入docker组
sudo usermod -aG docker $USER

# 重新登录以生效
exit
ssh user@*********
```

#### 1.4 验证Docker安装
```bash
docker --version
docker run hello-world
```

#### 1.5 安装Docker Compose
```bash
sudo curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
docker-compose --version
```

#### 1.6 创建项目目录结构
```bash
sudo mkdir -p /opt/watermeter
sudo chown $USER:$USER /opt/watermeter
cd /opt/watermeter
mkdir -p logs nginx uploads database/init
```

### 第二步：上传配置文件到服务器

#### 2.1 从本地上传配置文件
```bash
# 在本地Windows命令行执行（确保在项目根目录 D:\LU\693\CORED）
scp docker-compose.yml user@*********:/opt/watermeter/
scp nginx/nginx.conf user@*********:/opt/watermeter/nginx/
```

#### 2.2 验证文件上传成功
```bash
# 在服务器执行
cd /opt/watermeter
ls -la
ls -la nginx/
```

### 第三步：服务器环境配置

#### 3.1 创建环境变量文件
```bash
# 在服务器执行
cd /opt/watermeter
cat > .env << 'EOF'
POSTGRES_PASSWORD=123456
JWT_SECRET_KEY=WaterMeterManagement_SuperSecretKey_2024_ForProductionUse_32Chars
EOF

# 设置安全权限
chmod 600 .env
```

#### 3.2 验证环境变量文件
```bash
cat .env
ls -la .env
```

### 第四步：启动服务器基础设施

#### 4.1 启动私有Registry
```bash
cd /opt/watermeter
docker-compose up -d registry
```

#### 4.2 验证Registry状态
```bash
# 检查容器状态
docker ps | grep registry

# 测试Registry API
curl http://localhost:5001/v2/_catalog

# 查看Registry日志
docker-compose logs registry
```

#### 4.3 启动PostgreSQL数据库
```bash
docker-compose up -d postgres
```

#### 4.4 验证数据库状态
```bash
# 查看启动日志
docker-compose logs postgres

# 等待数据库完全启动
sleep 30

# 测试数据库连接
docker-compose exec postgres pg_isready -U postgres -d WaterMeterManagementDb
```

### 第五步：本地构建后端镜像

#### 5.1 确保本地Docker运行
```bash
# 在本地Windows命令行执行
docker --version
```

#### 5.2 检查VPN连接
```bash
# 确保能访问服务器Registry
ping *********
telnet ********* 5001
上面命令不行就用下面的：
Test-NetConnection -ComputerName ********* -Port 5001
```

#### 5.3 构建后端镜像
```bash
# 在本地项目根目录执行
cd D:\LU\693\CORED
docker build -t *********:5001/watermeter-backend:latest ./WaterMeterManagement
```

#### 5.4 推送后端镜像到Registry
在Windows上配置Docker Desktop允许不安全的Registry，配置Docker Engine：
···json
{
  "builder": {
    "gc": {
      "defaultKeepStorage": "20GB",
      "enabled": true
    }
  },
  "experimental": false,
  "features": {
    "buildkit": true
  },
  "insecure-registries": [
    "*********:5001"
  ]
} 
```

```bash
docker push *********:5001/watermeter-backend:latest

重新不带缓存构建
docker build --no-cache -t *********:5001/watermeter-backend:latest ./WaterMeterManagement
```

#### 5.5 验证后端镜像推送成功
```bash
# 在服务器执行，查看Registry中的镜像
curl http://localhost:5001/v2/_catalog
curl http://localhost:5001/v2/watermeter-backend/tags/list
```

### 第六步：本地构建前端镜像

#### 6.1 构建前端镜像
```bash
# 在本地项目根目录执行
docker build --no-cache -t *********:5001/watermeter-frontend:latest ./water-meter-admin
```

#### 6.2 推送前端镜像到Registry
```bash
docker push *********:5001/watermeter-frontend:latest
```

#### 6.3 验证前端镜像推送成功
```bash
# 在服务器执行
curl http://localhost:5001/v2/watermeter-frontend/tags/list
```

### 第七步：服务器启动应用服务

#### 7.1 启动后端服务
```bash
# 在服务器执行
cd /opt/watermeter
docker-compose up -d backend
```

#### 7.2 验证后端服务状态
```bash
# 查看启动日志
docker-compose logs backend

# 等待服务完全启动
sleep 30

# 测试健康检查
curl http://localhost:5000/health

# 测试API文档
curl http://localhost:5000/swagger
```

#### 7.3 启动前端服务
```bash
docker-compose up -d frontend
```

#### 7.4 验证前端服务状态
```bash
# 查看启动日志
docker-compose logs frontend

# 测试前端访问
curl http://localhost:3000
```

# 停止前端容器
docker stop watermeter-frontend

# 删除前端容器
docker rm watermeter-frontend

# （可选）删除旧的前端镜像，强制重新拉取
docker rmi localhost:5001/watermeter-frontend:latest

# 手动拉取最新的前端镜像
docker pull *********:5001/watermeter-frontend:latest

# 检查镜像的创建时间
docker inspect *********:5001/watermeter-frontend:latest | grep -A 5 "Created"

# 确保在正确的目录
cd /opt/watermeter

# 启动前端服务
docker-compose up -d frontend

# 检查所有服务状态
docker-compose ps

# 在服务器执行
docker stop watermeter-frontend
docker rm watermeter-frontend
docker rmi localhost:5001/watermeter-frontend:latest
docker system prune -f  # 清理缓存

# 重新拉取并启动
docker pull localhost:5001/watermeter-frontend:latest
docker-compose up -d frontend

# 查看服务器上所有的镜像并删除过期的
docker images
docker image prune -f

#### 7.5 启动Nginx反向代理
```bash
docker-compose up -d nginx
```

#### 7.6 验证Nginx状态
```bash
# 查看Nginx日志
docker-compose logs nginx

# 测试完整访问链路
curl http://localhost/health
curl http://localhost/swagger
```

### 第八步：系统整体验证

#### 8.1 检查所有服务状态
```bash
# 在服务器执行
docker-compose ps
```

应该看到5个服务都是`Up`状态：
- watermeter-registry
- watermeter-postgres  
- watermeter-backend
- watermeter-frontend
- watermeter-nginx

#### 8.2 外部访问测试
在本地浏览器访问：
- **前端管理界面**: http://*********
- **API文档**: http://*********/swagger
- **健康检查**: http://*********/health

#### 8.3 功能测试
- 尝试登录前端管理界面
- 测试API调用
- 检查数据库连接

## 🔧 关键配置文件说明

### docker-compose.yml 核心配置
```yaml
services:
  registry:
    image: registry:2                    # 私有Registry
    environment:
      - REGISTRY_HTTP_ADDR=0.0.0.0:5001 # 监听5001端口
      
  backend:
    image: localhost:5001/watermeter-backend:latest  # 从Registry拉取
    
  frontend:
    image: localhost:5001/watermeter-frontend:latest # 从Registry拉取
```

### 部署流程核心理解
1. **Registry**: 存储镜像的私有仓库
2. **本地构建**: 在开发机器构建镜像
3. **推送**: 将镜像推送到服务器Registry
4. **拉取**: 服务器从Registry拉取镜像运行

## 🚨 常见问题排查

### Registry连接问题
```bash
# 检查Registry端口
netstat -tlnp | grep 5001

# 检查Registry容器状态
docker ps | grep registry

# 重启Registry
docker-compose restart registry
```

### 镜像推送失败
```bash
# 检查网络连接
ping *********

# 检查Registry可访问性
curl http://*********:5001/v2/_catalog

# 检查Docker daemon配置（如果需要）
# 可能需要配置insecure registry
```

### 数据库连接问题
```bash
# 查看数据库日志
docker-compose logs postgres

# 进入数据库容器检查
docker-compose exec postgres psql -U watermeter_user -d WaterMeterManagementDb

# 重启数据库
docker-compose restart postgres
```

### 服务启动失败
```bash
# 查看详细错误日志
docker-compose logs [service_name]

# 检查镜像是否存在
docker images | grep watermeter

# 手动拉取镜像测试
docker pull localhost:5001/watermeter-backend:latest
```

## 🔄 日常维护和更新

### 代码更新流程
当需要更新应用时：

1. **本地修改代码**
2. **重新构建镜像**
   ```bash
   docker build -t *********:5001/watermeter-backend:latest ./WaterMeterManagement
   ```
3. **推送新镜像**
   ```bash
   docker push *********:5001/watermeter-backend:latest
   ```
4. **服务器更新服务**
   ```bash
   docker-compose pull backend
   docker-compose up -d backend
   ```

### 日志查看
```bash
# 查看所有服务日志
docker-compose logs

# 查看特定服务日志
docker-compose logs backend
docker-compose logs frontend
docker-compose logs postgres
docker-compose logs nginx
docker-compose logs registry

# 实时查看日志
docker-compose logs -f backend
```

### 数据备份
```bash
# 数据库备份
docker-compose exec postgres pg_dump -U watermeter_user WaterMeterManagementDb > backup_$(date +%Y%m%d_%H%M%S).sql

# 恢复数据库
docker-compose exec -T postgres psql -U watermeter_user -d WaterMeterManagementDb < backup_20241201_120000.sql
```

### 系统清理
```bash
# 清理无用镜像
docker image prune -f

# 清理无用容器
docker container prune -f

# 清理无用卷
docker volume prune -f
```

## 📊 性能监控

### 健康检查
```bash
# 检查所有服务健康状态
curl http://*********/health

# 检查各服务响应时间
time curl http://*********/api/health
```

### 资源使用监控
```bash
# 查看容器资源使用
docker stats

# 查看磁盘使用
df -h

# 查看内存使用
free -h
```

## 🔐 安全建议

### 环境变量安全
- 使用强密码
- 定期更换JWT密钥
- 限制.env文件权限为600

### 网络安全
- 只开放必要端口
- 考虑使用SSL证书
- 定期更新系统和Docker镜像

### Registry安全
- 考虑为Registry添加认证
- 定期清理旧镜像
- 备份Registry数据

---

## 📞 技术支持

### 联系方式
- 开发者: [你的联系方式]
- 紧急联系: [紧急联系方式]

### 文档版本
- 版本: v2.0
- 更新日期: 2024年12月
- 部署模式: Registry模式

---

*此文档基于Registry模式部署策略编写，会根据实际部署情况持续更新完善。*

## 数据库迁移详细记录

### 问题分析
从SQL Server LocalDB迁移到PostgreSQL时遇到的主要问题：

1. **数据类型不兼容**
   - SQL Server: `nvarchar(max)`
   - PostgreSQL: `text` 或 `varchar`

2. **EF Core版本不匹配**
   - 原版本: Microsoft.EntityFrameworkCore.Tools 9.0.5
   - 修复版本: Microsoft.EntityFrameworkCore.Tools 8.0.11
   - 匹配: Npgsql.EntityFrameworkCore.PostgreSQL 8.0.11

### 解决步骤

#### 第一步：更新项目依赖
```xml
<!-- WaterMeterManagement.csproj -->
<PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="8.0.11" />
<PackageReference Include="AspNetCore.HealthChecks.NpgSql" Version="9.0.0" />
<PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="8.0.11" />
```

#### 第二步：修改数据库上下文
```csharp
// Program.cs
builder.Services.AddDbContext<ApplicationDbContext>(options =>
    options.UseNpgsql(builder.Configuration.GetConnectionString("DefaultConnection")));

// Health Checks
builder.Services.AddHealthChecks()
    .AddNpgSql(builder.Configuration.GetConnectionString("DefaultConnection")!);
```

#### 第三步：移除SQL Server特定注解
移除以下模型类中的SQL Server特定数据类型注解：
- `Models/Configuration.cs`
- `Models/ConfigurationTemplate.cs`

```csharp
// 移除前
[Column(TypeName = "nvarchar(max)")]
public string Value { get; set; } = string.Empty;

// 移除后 (让EF Core自动选择PostgreSQL兼容类型)
public string Value { get; set; } = string.Empty;
```

#### 第四步：重新生成Migration
```bash
# 删除旧的Migration文件
dotnet ef migrations remove

# 生成新的PostgreSQL兼容Migration
dotnet ef migrations add InitialMigrationPostgreSQL_Fixed
```

### 迁移验证
- ✅ 编译成功，无错误
- ✅ Migration文件无SQL Server特定语法
- ✅ 数据类型全部为PostgreSQL兼容
- ⏳ 运行时测试待进行

## 下一步部署流程

### 1. 本地测试
```bash
cd /d/LU/693/CORED/WaterMeterManagement
dotnet build
dotnet run
```

### 2. Docker配置
- 创建Dockerfile (后端)
- 创建docker-compose.yml
- 配置PostgreSQL服务
- 配置Nginx反向代理

### 3. 服务器部署
- 设置私有Docker Registry
- 构建并推送镜像
- 在目标服务器运行容器

## 技术栈最终确认
- **后端**: ASP.NET Core 8.0 + PostgreSQL 16
- **前端**: Next.js 14 + TypeScript
- **部署**: Docker + Docker Compose
- **数据库**: PostgreSQL (替代SQL Server)
- **反向代理**: Nginx
- **容器编排**: Docker Compose
- **镜像仓库**: 私有Docker Registry

## 注意事项
1. PostgreSQL连接字符串需要在部署时更新
2. EF Core Migration将在应用启动时自动执行
3. 所有环境变量需要在docker-compose.yml中配置
4. 确保服务器防火墙开放必要端口

---
*更新时间: 2025-06-24*
*状态: 数据库迁移已完成，准备进行本地测试*