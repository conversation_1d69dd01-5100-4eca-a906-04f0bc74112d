version: '3.8'

services:
  # Private Registry  
  registry:
    image: registry:2
    container_name: watermeter-registry
    restart: unless-stopped
    network_mode: host
    environment:
      - REGISTRY_HTTP_ADDR=0.0.0.0:5001
      - TZ=Pacific/Auckland
    volumes:
      - registry_data:/var/lib/registry

  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: watermeter-postgres
    network_mode: host
    environment:
      POSTGRES_DB: WaterMeterManagementDb
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      TZ: Pacific/Auckland
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ../database/init:/docker-entrypoint-initdb.d
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d WaterMeterManagementDb"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Backend API
  backend:
    image: localhost:5001/watermeter-backend:latest
    container_name: watermeter-backend
    network_mode: host
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ASPNETCORE_URLS=http://0.0.0.0:5000
      - ConnectionStrings__DefaultConnection=Host=localhost;Port=5432;Database=WaterMeterManagementDb;Username=postgres;Password=${POSTGRES_PASSWORD}
      - Jwt__SecretKey=${JWT_SECRET_KEY}
      - Jwt__Issuer=WaterMeterManagement
      - Jwt__Audience=WaterMeterManagement
      - Jwt__ExpirationDays=30
      - TZ=Pacific/Auckland
    volumes:
      - ../logs:/app/logs
      - ../uploads:/app/uploads
    depends_on:
      postgres:
        condition: service_healthy
      registry:
        condition: service_started
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Frontend Management Interface
  frontend:
    image: localhost:5001/watermeter-frontend:latest
    container_name: watermeter-frontend
    network_mode: host
    environment:
      - NODE_ENV=production
      - TZ=Pacific/Auckland
    depends_on:
      backend:
        condition: service_healthy
    restart: unless-stopped

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: watermeter-nginx
    network_mode: host
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - ../logs/nginx:/var/log/nginx
    depends_on:
      - frontend
      - backend
    restart: unless-stopped

volumes:
  postgres_data:
    driver: local
  registry_data:
    driver: local