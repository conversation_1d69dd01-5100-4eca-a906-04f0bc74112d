# 坐标数据导入功能使用指南

## 功能概述

CORDE Mobile Application现在支持从Excel文件导入资产坐标数据，以优化地图显示性能。通过预先导入坐标数据，应用将优先使用本地坐标而不是调用Google Maps API，从而显著提升地图加载速度。

## Excel文件格式要求

### 必需列
Excel文件必须包含以下三列（列名不区分大小写）：

| 列名 | 数据类型 | 说明 |
|------|----------|------|
| AMSKEY | 数字 | 资产ID，对应数据库中的asset_id |
| latitude | 数字 | 纬度坐标 |
| longitude | 数字 | 经度坐标 |

### 示例数据格式
```
AMSKEY    latitude        longitude
516925    -43.61380511    172.5086473
516923    -43.63682301    172.4884575
518503    -43.48174405    171.9345057
518132    -43.52008821    171.9462866
```

## 使用步骤

### 1. 准备Excel文件
- 确保Excel文件包含正确的列名和数据格式
- 建议使用.xlsx格式
- 数据应从第二行开始（第一行为标题行）

### 2. 导入坐标数据
1. 打开CORDE Mobile应用
2. 导航到"同步"页面
3. 选择"Basic Data"标签
4. 点击"Import Coordinates from Excel"按钮
5. 从设备中选择准备好的Excel文件
6. 等待导入完成

### 3. 查看导入结果
- 导入成功后会显示成功提示，包含导入的记录数量
- 在同步页面可以看到"Coordinates"统计信息
- 导入失败时会显示具体的错误信息

## 功能特性

### 数据验证
- 自动验证Excel文件格式和必需列
- 检查数据类型和有效性
- 跳过无效数据行并记录警告

### 重复导入处理
- 支持多次导入同一文件
- 使用UPSERT操作，相同asset_id的记录会被更新
- 不会产生重复数据

### 进度显示
- 实时显示导入进度
- 详细的状态消息
- 导入完成后显示统计信息

### 性能优化
- 地图显示时优先使用本地坐标数据
- 本地坐标不存在时自动回退到Google Maps API
- 显著减少API调用次数和地图加载时间

## 故障排除

### 常见错误及解决方案

#### "Excel file not found"
- 确保选择了正确的文件
- 检查文件是否存在于设备上

#### "Missing required column: [列名]"
- 检查Excel文件是否包含所有必需列
- 确认列名拼写正确（AMSKEY, latitude, longitude）

#### "No valid coordinate data found"
- 检查数据格式是否正确
- 确保AMSKEY、latitude、longitude都是有效数字
- 检查是否有数据行（除标题行外）

#### "Invalid AMSKEY/latitude/longitude"
- 确保坐标数据为有效的数字格式
- AMSKEY应为正整数
- latitude范围通常在-90到90之间
- longitude范围通常在-180到180之间

### 性能建议
- 建议单次导入不超过10,000条记录
- 大文件可以分批导入
- 导入过程中避免切换到其他应用

## 技术实现

### 数据库表结构
```sql
CREATE TABLE asset_coordinates (
  asset_id INTEGER PRIMARY KEY UNIQUE NOT NULL,
  latitude REAL NOT NULL,
  longitude REAL NOT NULL
);
```

### 地图优化逻辑
1. 查询log记录的asset_id
2. 从asset_coordinates表获取坐标
3. 如果本地坐标存在，直接使用
4. 如果本地坐标不存在，调用Google Maps API
5. 缓存API结果以供后续使用

## 更新日志

### v1.0.0
- 初始版本发布
- 支持Excel文件导入
- 实现地图性能优化
- 添加数据验证和错误处理
