# 📱 CORDE Mobile Application - Google Play Store 部署资源

本文件夹包含CORDE Mobile Application上架Google Play Store所需的所有文档、脚本和模板。

## 📁 文件夹结构

```
google-play-deployment/
├── README.md                    # 本文件
├── docs/                        # 部署文档
│   └── GOOGLE_PLAY_STORE_DEPLOYMENT_GUIDE.md
├── scripts/                     # 自动化脚本
│   └── prepare-play-store.sh
├── templates/                   # 模板文件
│   └── PRIVACY_POLICY_TEMPLATE.md
└── assets/                      # 应用资源
    ├── icons/                   # 应用图标
    ├── screenshots/             # 应用截图
    └── descriptions/            # 应用描述
```

## 🚀 快速开始

### 1. 阅读部署指南
```bash
# 查看完整的部署指南
cat docs/GOOGLE_PLAY_STORE_DEPLOYMENT_GUIDE.md
```

### 2. 运行准备脚本
```bash
# 执行自动化准备脚本
./scripts/prepare-play-store.sh
```

### 3. 准备应用资源
- 创建512x512px应用图标
- 准备至少2张应用截图
- 根据模板编写隐私政策

## 📋 上架检查清单

### ✅ 技术准备
- [ ] 生产Keystore已生成
- [ ] 签名配置已完成
- [ ] AAB构建配置已设置
- [ ] 应用构建测试通过

### ✅ 应用资料
- [ ] 应用图标 (512x512px)
- [ ] 应用截图 (至少2张)
- [ ] 应用描述 (简短+完整)
- [ ] 隐私政策URL

### ✅ Play Console
- [ ] 开发者账号已激活 ($25已支付)
- [ ] 应用已创建
- [ ] 应用包已上传
- [ ] 商店信息已填写
- [ ] 内容分级已完成
- [ ] 数据安全表单已填写

## 📞 支持

如有问题，请参考：
1. **部署指南**: `docs/GOOGLE_PLAY_STORE_DEPLOYMENT_GUIDE.md`
2. **隐私政策模板**: `templates/PRIVACY_POLICY_TEMPLATE.md`
3. **自动化脚本**: `scripts/prepare-play-store.sh`

## ⚠️ 重要提醒

1. **Keystore安全**: 妥善保管keystore文件和密码
2. **敏感信息**: 不要将gradle.properties提交到代码仓库
3. **测试充分**: 在正式发布前充分测试应用
4. **合规要求**: 确保应用符合Google Play政策

## 🎯 预计时间

- **技术配置**: 2-3小时
- **资料准备**: 1-2小时  
- **Play Console配置**: 1-2小时
- **审核等待**: 1-3个工作日

**总计**: 约1周内完成上架

---

**最后更新**: 2025-08-28  
**状态**: 准备阶段
