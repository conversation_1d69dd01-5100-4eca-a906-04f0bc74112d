#!/bin/bash

# CORDE Mobile Application - Google Play Store 准备脚本
# 版本: 1.0
# 用途: 自动化准备Google Play Store上架所需的配置

set -e  # 遇到错误立即退出

echo "🚀 开始准备CORDE Mobile Application Google Play Store上架..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目路径
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/../.." && pwd)"
ANDROID_DIR="$PROJECT_ROOT/CORDE-Mobile-Application/android"
APP_DIR="$ANDROID_DIR/app"

echo -e "${BLUE}项目根目录: $PROJECT_ROOT${NC}"
echo -e "${BLUE}Android目录: $ANDROID_DIR${NC}"

# 检查必要工具
check_tools() {
    echo -e "\n${YELLOW}🔍 检查必要工具...${NC}"
    
    if ! command -v keytool &> /dev/null; then
        echo -e "${RED}❌ keytool未找到，请安装Java JDK${NC}"
        exit 1
    fi
    
    if ! command -v gradle &> /dev/null && [ ! -f "$ANDROID_DIR/gradlew" ]; then
        echo -e "${RED}❌ Gradle未找到${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}✅ 工具检查完成${NC}"
}

# 检查当前项目状态
check_project_status() {
    echo -e "\n${YELLOW}📋 检查当前项目状态...${NC}"
    
    if [ ! -f "$APP_DIR/build.gradle" ]; then
        echo -e "${RED}❌ 未找到build.gradle文件${NC}"
        exit 1
    fi
    
    # 显示当前版本信息
    echo -e "${BLUE}当前版本信息:${NC}"
    grep -E "(versionCode|versionName|applicationId)" "$APP_DIR/build.gradle" || true
    
    # 检查签名配置
    echo -e "\n${BLUE}当前签名配置:${NC}"
    grep -A 15 "signingConfigs" "$APP_DIR/build.gradle" || true
    
    echo -e "${GREEN}✅ 项目状态检查完成${NC}"
}

# 创建备份
create_backup() {
    echo -e "\n${YELLOW}💾 创建配置备份...${NC}"
    
    BACKUP_DIR="$PROJECT_ROOT/deployment-backup/$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$BACKUP_DIR"
    
    # 备份关键文件
    cp "$APP_DIR/build.gradle" "$BACKUP_DIR/"
    [ -f "$ANDROID_DIR/gradle.properties" ] && cp "$ANDROID_DIR/gradle.properties" "$BACKUP_DIR/"
    
    echo -e "${GREEN}✅ 备份已创建: $BACKUP_DIR${NC}"
}

# 生成生产Keystore
generate_keystore() {
    echo -e "\n${YELLOW}🔐 生成生产Keystore...${NC}"
    
    KEYSTORE_FILE="$APP_DIR/corde-release-key.keystore"
    
    if [ -f "$KEYSTORE_FILE" ]; then
        echo -e "${YELLOW}⚠️  Keystore文件已存在: $KEYSTORE_FILE${NC}"
        read -p "是否要重新生成? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            echo -e "${BLUE}跳过Keystore生成${NC}"
            return
        fi
    fi
    
    echo -e "${BLUE}请输入Keystore信息:${NC}"
    read -p "Keystore密码: " -s KEYSTORE_PASSWORD
    echo
    read -p "Key密码: " -s KEY_PASSWORD
    echo
    read -p "组织名称 [CORDE]: " ORG_NAME
    ORG_NAME=${ORG_NAME:-CORDE}
    read -p "城市 [Christchurch]: " CITY
    CITY=${CITY:-Christchurch}
    read -p "省份 [Canterbury]: " STATE
    STATE=${STATE:-Canterbury}
    read -p "国家代码 [NZ]: " COUNTRY
    COUNTRY=${COUNTRY:-NZ}
    
    # 生成keystore
    keytool -genkeypair -v -storetype PKCS12 \
        -keystore "$KEYSTORE_FILE" \
        -alias corde-key-alias \
        -keyalg RSA \
        -keysize 2048 \
        -validity 10000 \
        -dname "CN=CORDE Development Team, OU=$ORG_NAME, O=$ORG_NAME, L=$CITY, ST=$STATE, C=$COUNTRY" \
        -storepass "$KEYSTORE_PASSWORD" \
        -keypass "$KEY_PASSWORD"
    
    echo -e "${GREEN}✅ Keystore生成完成: $KEYSTORE_FILE${NC}"
    
    # 创建gradle.properties
    GRADLE_PROPS="$ANDROID_DIR/gradle.properties"
    echo -e "\n${YELLOW}📝 更新gradle.properties...${NC}"
    
    # 备份现有文件
    [ -f "$GRADLE_PROPS" ] && cp "$GRADLE_PROPS" "$GRADLE_PROPS.backup"
    
    # 添加签名配置
    cat >> "$GRADLE_PROPS" << EOF

# Release signing config
MYAPP_RELEASE_STORE_FILE=corde-release-key.keystore
MYAPP_RELEASE_KEY_ALIAS=corde-key-alias
MYAPP_RELEASE_STORE_PASSWORD=$KEYSTORE_PASSWORD
MYAPP_RELEASE_KEY_PASSWORD=$KEY_PASSWORD

# Build optimization
org.gradle.jvmargs=-Xmx2048m -XX:MaxPermSize=512m -XX:+HeapDumpOnOutOfMemoryError -Dfile.encoding=UTF-8
org.gradle.parallel=true
org.gradle.configureondemand=true
org.gradle.daemon=true
EOF
    
    echo -e "${GREEN}✅ gradle.properties已更新${NC}"
    
    # 安全提醒
    echo -e "\n${RED}🚨 重要安全提醒:${NC}"
    echo -e "${RED}1. 请妥善保管keystore文件和密码${NC}"
    echo -e "${RED}2. 建议将keystore文件备份到安全位置${NC}"
    echo -e "${RED}3. 不要将gradle.properties提交到代码仓库${NC}"
    
    # 添加到.gitignore
    GITIGNORE="$PROJECT_ROOT/.gitignore"
    if [ -f "$GITIGNORE" ]; then
        if ! grep -q "gradle.properties" "$GITIGNORE"; then
            echo "android/gradle.properties" >> "$GITIGNORE"
            echo -e "${GREEN}✅ gradle.properties已添加到.gitignore${NC}"
        fi
    fi
}

# 配置AAB构建
configure_aab() {
    echo -e "\n${YELLOW}📦 配置Android App Bundle (AAB)构建...${NC}"
    
    # 这里需要手动修改build.gradle文件
    echo -e "${BLUE}需要手动配置build.gradle文件:${NC}"
    echo -e "${BLUE}1. 在android块中添加bundle配置${NC}"
    echo -e "${BLUE}2. 启用资源压缩和代码混淆${NC}"
    echo -e "${BLUE}3. 配置分包策略${NC}"
    
    echo -e "\n${YELLOW}请参考部署指南中的详细配置步骤${NC}"
}

# 构建测试
build_test() {
    echo -e "\n${YELLOW}🔨 构建测试...${NC}"
    
    cd "$ANDROID_DIR"
    
    # 清理之前的构建
    echo -e "${BLUE}清理之前的构建...${NC}"
    ./gradlew clean
    
    # 构建Release APK进行测试
    echo -e "${BLUE}构建Release APK...${NC}"
    ./gradlew assembleRelease
    
    # 检查构建结果
    RELEASE_APK=$(find app/build/outputs/apk/release -name "*.apk" | head -1)
    if [ -f "$RELEASE_APK" ]; then
        APK_SIZE=$(ls -lh "$RELEASE_APK" | awk '{print $5}')
        echo -e "${GREEN}✅ APK构建成功!${NC}"
        echo -e "${GREEN}📱 APK位置: $RELEASE_APK${NC}"
        echo -e "${GREEN}📊 APK大小: $APK_SIZE${NC}"
    else
        echo -e "${RED}❌ APK构建失败${NC}"
        exit 1
    fi
    
    cd "$PROJECT_ROOT"
}

# 生成检查清单
generate_checklist() {
    echo -e "\n${YELLOW}📋 生成上架检查清单...${NC}"
    
    CHECKLIST_FILE="$PROJECT_ROOT/deployment-config/docs/PLAY_STORE_CHECKLIST.md"
    
    cat > "$CHECKLIST_FILE" << 'EOF'
# Google Play Store 上架检查清单

## 技术准备
- [ ] 生产Keystore已生成并安全保存
- [ ] gradle.properties已配置签名信息
- [ ] build.gradle已配置AAB构建
- [ ] Release APK/AAB构建成功
- [ ] 应用在测试设备正常运行

## 应用资料
- [ ] 应用图标 (512x512px PNG)
- [ ] 应用截图 (至少2张)
- [ ] 应用描述 (简短+完整)
- [ ] 隐私政策URL
- [ ] 应用分类选择

## Play Console配置
- [ ] 应用已创建
- [ ] 应用包已上传
- [ ] 商店信息已填写
- [ ] 内容分级已完成
- [ ] 数据安全表单已填写
- [ ] 目标受众已设置

## 最终检查
- [ ] 所有必填项已完成
- [ ] 应用商店页面预览正常
- [ ] 准备提交审核

## 发布后
- [ ] 监控审核状态
- [ ] 准备用户支持
- [ ] 设置更新计划
EOF
    
    echo -e "${GREEN}✅ 检查清单已生成: $CHECKLIST_FILE${NC}"
}

# 显示下一步指导
show_next_steps() {
    echo -e "\n${GREEN}🎉 准备工作完成!${NC}"
    echo -e "\n${YELLOW}📋 下一步操作:${NC}"
    echo -e "${BLUE}1. 手动配置build.gradle文件 (参考部署指南)${NC}"
    echo -e "${BLUE}2. 准备应用图标和截图${NC}"
    echo -e "${BLUE}3. 编写隐私政策${NC}"
    echo -e "${BLUE}4. 构建AAB文件进行最终测试${NC}"
    echo -e "${BLUE}5. 在Google Play Console创建应用${NC}"
    echo -e "${BLUE}6. 上传应用包并填写商店信息${NC}"
    echo -e "${BLUE}7. 提交审核${NC}"
    
    echo -e "\n${YELLOW}📚 参考文档:${NC}"
    echo -e "${BLUE}- 详细部署指南: deployment-config/docs/GOOGLE_PLAY_STORE_DEPLOYMENT_GUIDE.md${NC}"
    echo -e "${BLUE}- 上架检查清单: deployment-config/docs/PLAY_STORE_CHECKLIST.md${NC}"
    
    echo -e "\n${RED}🚨 重要提醒:${NC}"
    echo -e "${RED}- 妥善保管keystore文件和密码${NC}"
    echo -e "${RED}- 不要将敏感信息提交到代码仓库${NC}"
    echo -e "${RED}- 在正式发布前充分测试应用${NC}"
}

# 主函数
main() {
    echo -e "${GREEN}=== CORDE Mobile Application Google Play Store 准备脚本 ===${NC}"
    
    check_tools
    check_project_status
    create_backup
    
    read -p "是否要生成生产Keystore? (Y/n): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Nn]$ ]]; then
        generate_keystore
    fi
    
    configure_aab
    
    read -p "是否要进行构建测试? (Y/n): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Nn]$ ]]; then
        build_test
    fi
    
    generate_checklist
    show_next_steps
    
    echo -e "\n${GREEN}✅ 脚本执行完成!${NC}"
}

# 执行主函数
main "$@"
