# 📱 应用资源文件夹

本文件夹用于存放Google Play Store上架所需的各种应用资源文件。

## 📁 文件夹结构

```
assets/
├── README.md                    # 本文件
├── icons/                       # 应用图标
│   ├── app-icon-512x512.png    # 主应用图标 (必需)
│   ├── app-icon-1024x1024.png  # 高分辨率图标 (可选)
│   └── adaptive-icon/           # 自适应图标资源
├── screenshots/                 # 应用截图
│   ├── phone/                   # 手机截图
│   ├── tablet/                  # 平板截图 (可选)
│   └── tv/                      # TV截图 (可选)
└── descriptions/                # 本地化描述
    ├── en-US/                   # 英文描述
    └── zh-CN/                   # 中文描述 (可选)
```

## 🎨 应用图标要求

### 主应用图标 (必需)
- **文件名**: `app-icon-512x512.png`
- **尺寸**: 512x512 像素
- **格式**: PNG (32位)
- **背景**: 不透明，无透明度
- **内容**: 清晰、简洁的图标设计
- **大小**: 建议小于1MB

### 设计指南
- 遵循Material Design图标设计原则
- 确保在不同背景下清晰可见
- 避免使用文字（除非是品牌标识）
- 保持简洁，避免过于复杂的细节

## 📱 应用截图要求

### 手机截图 (必需)
- **数量**: 最少2张，最多8张
- **尺寸**: 
  - 16:9 比例: 1920x1080px
  - 9:16 比例: 1080x1920px
- **格式**: PNG或JPEG
- **大小**: 每张不超过8MB

### 截图内容建议
1. **登录界面** - 展示应用入口
2. **任务列表** - 展示主要功能
3. **地图界面** - 展示定位功能
4. **数据录入** - 展示工作流程
5. **设置界面** - 展示应用配置

### 截图设计技巧
- 使用真实数据，避免Lorem ipsum
- 确保界面整洁，无错误信息
- 突出核心功能和价值
- 保持一致的视觉风格
- 考虑添加简短的功能说明文字

## 🌍 本地化资源

### 支持语言
- **en-US** (英语 - 美国) - 主要语言
- **zh-CN** (中文 - 简体) - 可选

### 本地化内容
- 应用名称
- 简短描述
- 完整描述
- 截图中的文字内容
- 关键词标签

## 📋 资源检查清单

### 图标资源
- [ ] 主应用图标 (512x512px PNG)
- [ ] 图标设计符合Material Design规范
- [ ] 图标在不同背景下清晰可见
- [ ] 文件大小合适 (<1MB)

### 截图资源
- [ ] 至少2张手机截图
- [ ] 截图尺寸正确 (16:9 或 9:16)
- [ ] 截图内容展示核心功能
- [ ] 截图质量高，无模糊
- [ ] 每张截图 <8MB

### 描述资源
- [ ] 英文描述已准备
- [ ] 中文描述已准备 (如需要)
- [ ] 关键词已优化
- [ ] 内容符合Google Play政策

## 🛠️ 资源创建工具

### 图标设计
- **Adobe Illustrator** - 专业矢量设计
- **Figma** - 在线协作设计
- **Canva** - 简单易用的设计工具
- **Android Asset Studio** - Google官方图标工具

### 截图工具
- **Android Studio** - 模拟器截图
- **ADB** - 真机截图命令
- **Figma** - 截图美化和标注
- **Photoshop** - 专业图像处理

### 截图命令示例
```bash
# 使用ADB截取设备截图
adb shell screencap -p /sdcard/screenshot.png
adb pull /sdcard/screenshot.png ./screenshots/

# 或使用Android Studio Device Manager截图
```

## 📐 尺寸参考

### 常用Android屏幕尺寸
- **1080x1920** (9:16) - 主流手机
- **1440x2560** (9:16) - 高分辨率手机
- **1920x1080** (16:9) - 横屏显示
- **2560x1440** (16:9) - 高分辨率横屏

### Google Play要求
- **最小宽度**: 320px
- **最大宽度**: 3840px
- **宽高比**: 16:9 到 9:16 之间

## 🎯 优化建议

### ASO (应用商店优化)
- 使用高质量的视觉资源
- 截图突出核心价值主张
- 图标设计独特且易识别
- 本地化适应目标市场

### 用户体验
- 截图展示真实使用场景
- 突出解决的问题和提供的价值
- 保持视觉一致性
- 考虑用户的第一印象

---

## 📞 需要帮助？

如需创建或优化应用资源，请参考：
- **设计指南**: `../docs/GOOGLE_PLAY_STORE_DEPLOYMENT_GUIDE.md`
- **描述模板**: `../templates/APP_DESCRIPTION_TEMPLATE.md`
- **检查清单**: `../docs/PLAY_STORE_CHECKLIST.md`

---

**最后更新**: 2025-08-28  
**状态**: 等待资源文件上传
