# 📋 Google Play Store 上架检查清单

## 🎯 使用说明
- 使用 `[x]` 标记已完成项目
- 使用 `[ ]` 标记未完成项目
- 按顺序完成各个阶段的任务

---

## 📋 Phase 1: 技术准备

### 🔐 签名配置
- [ ] 生产Keystore已生成
  - [ ] Keystore文件: `android/app/corde-release-key.keystore`
  - [ ] Keystore密码已记录并安全保存
  - [ ] Key密码已记录并安全保存
  - [ ] 证书有效期至少25年
- [ ] gradle.properties已配置
  - [ ] MYAPP_RELEASE_STORE_FILE已设置
  - [ ] MYAPP_RELEASE_KEY_ALIAS已设置
  - [ ] MYAPP_RELEASE_STORE_PASSWORD已设置
  - [ ] MYAPP_RELEASE_KEY_PASSWORD已设置
- [ ] build.gradle签名配置已更新
  - [ ] release buildType使用正确的signingConfig
  - [ ] debuggable设置为false (生产环境)

### 📦 构建配置
- [ ] Android App Bundle (AAB) 配置已启用
- [ ] 代码混淆和资源压缩已启用
- [ ] ProGuard规则已配置
- [ ] 应用分包策略已设置
- [ ] 版本信息已更新
  - [ ] versionCode已递增
  - [ ] versionName已更新
  - [ ] applicationId正确

### 🔨 构建测试
- [ ] Release APK构建成功
- [ ] Release AAB构建成功
- [ ] 应用在测试设备正常安装
- [ ] 所有核心功能正常工作
- [ ] 性能测试通过
- [ ] 内存泄漏检查通过

---

## 📋 Phase 2: 应用资料准备

### 🎨 视觉资源
- [ ] 应用图标 (512x512px)
  - [ ] PNG格式，32位色彩
  - [ ] 无透明背景
  - [ ] 高质量，清晰可见
  - [ ] 符合Material Design规范
- [ ] 功能图片 (1024x500px) - 可选
- [ ] 手机截图 (至少2张，最多8张)
  - [ ] 16:9 或 9:16 比例
  - [ ] 展示主要功能界面
  - [ ] 高质量，无模糊
  - [ ] 每张不超过8MB

### 📝 文本内容
- [ ] 应用名称确定
  - [ ] 主要名称: "CORDE Mobile"
  - [ ] 符合Google Play命名规范
- [ ] 简短描述 (80字符以内)
  - [ ] 清晰描述应用用途
  - [ ] 包含关键词
- [ ] 完整描述 (最多4000字符)
  - [ ] 详细功能介绍
  - [ ] 目标用户说明
  - [ ] 关键优势突出
  - [ ] 格式美观，易读

### 🏷️ 分类和标签
- [ ] 应用类别选择: Business
- [ ] 内容标签设置
- [ ] 目标受众确定: 18+

---

## 📋 Phase 3: 合规和政策

### 🔒 隐私政策
- [ ] 隐私政策文档已编写
- [ ] 隐私政策已发布到网站
- [ ] 隐私政策URL可访问
- [ ] 内容符合GDPR要求
- [ ] 包含所有必需信息:
  - [ ] 数据收集说明
  - [ ] 数据使用目的
  - [ ] 数据共享情况
  - [ ] 用户权利说明
  - [ ] 联系方式

### 📊 数据安全表单
- [ ] 位置数据收集已声明
- [ ] 照片和文件收集已声明
- [ ] 个人信息收集已声明
- [ ] 数据使用目的已说明
- [ ] 数据共享情况已说明
- [ ] 安全措施已描述

### 🎯 内容分级
- [ ] 内容分级问卷已完成
- [ ] 分级证书已获得
- [ ] 目标年龄组已设置

---

## 📋 Phase 4: Google Play Console配置

### 🏪 应用创建
- [ ] Google Play开发者账号已激活
- [ ] $25注册费已支付
- [ ] 应用已在Console中创建
- [ ] 应用名称已设置
- [ ] 默认语言已选择

### 📤 应用包上传
- [ ] AAB文件已上传到生产轨道
- [ ] 版本说明已填写
- [ ] 应用包通过验证
- [ ] 没有警告或错误

### 🏬 商店信息
- [ ] 应用详情已填写
  - [ ] 应用名称
  - [ ] 简短描述
  - [ ] 完整描述
- [ ] 图形资源已上传
  - [ ] 应用图标
  - [ ] 手机截图
  - [ ] 功能图片 (如有)
- [ ] 分类信息已设置
  - [ ] 应用类别
  - [ ] 内容标签

### 🛡️ 应用内容
- [ ] 隐私政策URL已提供
- [ ] 数据安全表单已完成
- [ ] 内容分级已完成
- [ ] 目标受众已设置
- [ ] 广告政策已确认

### 🔧 高级设置
- [ ] 国家/地区可用性已设置
- [ ] 设备兼容性已配置
- [ ] 用户反馈设置已配置
- [ ] 联系方式已提供

---

## 📋 Phase 5: 最终检查和发布

### ✅ 预发布检查
- [ ] 所有必填项已完成
- [ ] 应用商店页面预览正常
- [ ] 所有资源显示正确
- [ ] 描述文本无错误
- [ ] 联系信息正确

### 🚀 发布准备
- [ ] 发布说明已准备
- [ ] 用户支持流程已建立
- [ ] 监控工具已设置
- [ ] 更新计划已制定

### 📤 提交审核
- [ ] 应用已提交到生产轨道
- [ ] 审核状态监控中
- [ ] 准备回应审核反馈

---

## 📋 Phase 6: 发布后维护

### 📊 监控指标
- [ ] 安装量监控
- [ ] 崩溃率监控
- [ ] ANR率监控
- [ ] 用户评分监控
- [ ] 性能指标监控

### 🔄 持续维护
- [ ] 用户评论回复流程
- [ ] 定期更新计划
- [ ] 安全补丁策略
- [ ] 功能改进路线图

---

## 🚨 重要提醒

### 安全注意事项
- [ ] Keystore文件已备份到安全位置
- [ ] 密码已使用密码管理器保存
- [ ] gradle.properties已添加到.gitignore
- [ ] 敏感信息未提交到代码仓库

### 合规要求
- [ ] 应用符合Google Play政策
- [ ] 隐私政策符合法律要求
- [ ] 数据处理符合GDPR规定
- [ ] 权限使用合理且必要

---

## 📞 支持联系

如遇问题，请参考：
- 部署指南: `GOOGLE_PLAY_STORE_DEPLOYMENT_GUIDE.md`
- 自动化脚本: `../scripts/prepare-play-store.sh`
- 模板文件: `../templates/`

---

**检查清单版本**: 1.0  
**最后更新**: 2025-08-28  
**预计完成时间**: 5-8小时 (分阶段完成)
