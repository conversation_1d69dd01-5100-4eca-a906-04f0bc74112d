# 📱 应用ID更新说明

## 🎯 更新概述

应用ID已从默认的 `com.awesomeproject` 更新为基于公司域名的正式ID `nz.corde.mobile`。

## 📋 更新详情

### 原始配置
- **应用ID**: `com.awesomeproject`
- **包名**: `com.awesomeproject`
- **Java包结构**: `com/awesomeproject/`

### 更新后配置
- **应用ID**: `nz.corde.mobile`
- **包名**: `nz.corde.mobile`
- **Java包结构**: `nz/corde/mobile/`
- **基于域名**: `corde.nz` (反向域名格式)

## 🔧 已修改的文件

### 1. Android构建配置
**文件**: `android/app/build.gradle`
```gradle
// 修改前
namespace "com.awesomeproject"
defaultConfig {
    applicationId "com.awesomeproject"
    
// 修改后
namespace "nz.corde.mobile"
defaultConfig {
    applicationId "nz.corde.mobile"
```

### 2. Java源文件
**MainActivity.kt**
- **位置**: `android/app/src/main/java/nz/corde/mobile/MainActivity.kt`
- **包声明**: `package nz.corde.mobile`

**MainApplication.kt**
- **位置**: `android/app/src/main/java/nz/corde/mobile/MainApplication.kt`
- **包声明**: `package nz.corde.mobile`

### 3. 文档更新
**部署指南**
- 更新了应用基本信息中的应用ID
- 确保所有示例使用正确的应用ID

## ✅ 验证检查

### 构建验证
- [ ] Android项目可以正常构建
- [ ] 应用可以正常安装到设备
- [ ] 应用功能正常运行
- [ ] 包名在APK中正确显示

### 配置验证
```bash
# 检查构建配置
grep -r "nz.corde.mobile" android/app/build.gradle

# 检查Java包结构
find android/app/src/main/java -name "*.kt" -exec grep "package nz.corde.mobile" {} \;

# 验证APK包名
aapt dump badging app-release.apk | grep package
```

## 🚨 重要注意事项

### 1. 应用ID不可逆
- 一旦发布到Google Play Store，应用ID不能更改
- 更改应用ID相当于创建全新应用
- 用户需要重新下载和安装

### 2. 签名一致性
- 必须使用相同的keystore签名
- 确保签名配置正确
- 测试签名和发布签名要匹配

### 3. 数据迁移
- 新应用ID意味着独立的应用数据
- 用户数据不会自动迁移
- 需要考虑数据导入/导出功能

## 🔄 回滚计划

如果需要回滚到原始配置：

### 1. 恢复build.gradle
```gradle
namespace "com.awesomeproject"
defaultConfig {
    applicationId "com.awesomeproject"
```

### 2. 恢复Java包结构
```bash
# 创建原始目录
mkdir -p android/app/src/main/java/com/awesomeproject

# 移动文件并更新包声明
# MainActivity.kt: package com.awesomeproject
# MainApplication.kt: package com.awesomeproject
```

### 3. 清理构建
```bash
cd android
./gradlew clean
cd ..
```

## 📋 测试清单

### 构建测试
- [ ] `./gradlew assembleDebug` 成功
- [ ] `./gradlew assembleRelease` 成功
- [ ] APK文件正常生成
- [ ] 应用可以安装到设备

### 功能测试
- [ ] 应用启动正常
- [ ] 登录功能正常
- [ ] 核心功能正常
- [ ] 数据同步正常

### 包名验证
- [ ] APK包名为 `nz.corde.mobile`
- [ ] 应用在设备上显示正确
- [ ] 卸载重装功能正常

## 🎯 下一步行动

### 立即执行
1. **测试构建**: 验证应用可以正常构建
2. **功能测试**: 确保所有功能正常工作
3. **包名验证**: 确认APK包名正确

### 发布前
1. **生成新keystore**: 使用正确的组织信息
2. **更新签名配置**: 确保使用新的应用ID
3. **最终测试**: 完整的端到端测试

## 📞 支持信息

如遇到问题，请检查：
1. **构建错误**: 清理项目并重新构建
2. **包名冲突**: 确保没有重复的包声明
3. **签名问题**: 验证keystore配置正确

---

**更新日期**: 2025-08-28  
**更新人**: AI Assistant  
**状态**: 已完成  
**影响**: 需要重新构建和测试应用
