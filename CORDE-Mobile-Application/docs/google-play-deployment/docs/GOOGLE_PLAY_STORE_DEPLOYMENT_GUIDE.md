# 📱 Google Play Store 上架部署指南

## 🎯 概述

本指南详细说明如何将CORDE Mobile Application上架到Google Play Store，包括所有必需的配置、文档和步骤。

---

## 💰 费用说明

### 必需费用
- **Google Play开发者账号**: $25 USD (一次性费用，已获得)
- **应用上传和更新**: 免费

### 可选费用
- 图标设计: $0-100 (可自行设计)
- 隐私政策法律审查: $0-500 (可使用模板)

---

## 📋 上架前检查清单

### ✅ 已完成项目配置
根据项目检查，以下配置已就绪：

1. **应用基本信息**:
   - 应用ID: `nz.corde.mobile`
   - 当前版本: 1.6 (versionCode: 38)
   - 目标API: 已配置

2. **构建配置**:
   - Release构建脚本已配置
   - APK输出文件名: `corde-mobile-{version}.apk`

### ❌ 需要完成的配置

#### 1. 生产环境签名配置
**当前状态**: 使用debug keystore (不适用于生产)
**需要**: 生成生产keystore

#### 2. 应用包格式
**当前状态**: 生成APK格式
**需要**: 配置Android App Bundle (AAB) - Google Play强制要求

#### 3. API级别要求 (重要更新)
**当前状态**: 目标API 34 (Android 14)
**最新要求**:
- **2025年8月31日前**: 必须目标Android 14 (API level 34)或更高
- **2025年8月31日后**: 必须目标Android 15 (API level 35)或更高

#### 4. 应用信息完善
**需要**:
- 应用图标 (512x512px)
- 应用截图 (至少2张)
- 应用描述
- 隐私政策URL (强制要求)

#### 5. 开发者账号验证
**需要**: 完成Google Play Console身份验证 (新账号必需)

---

## � 2025年重要更新

### Google Play Store最新要求 (2025年)

#### API级别要求变更
- **2025年8月31日前**: 应用必须目标Android 14 (API level 34)或更高
- **2025年8月31日后**: 新应用和应用更新必须目标Android 15 (API level 35)或更高
- **例外情况**: 企业内部应用可申请延期至2025年11月1日

#### 数据安全要求加强
- 所有应用必须完成详细的数据安全表单
- 必须提供隐私政策URL
- 位置数据收集需要明确说明用途

#### 开发者身份验证
- 新开发者账号需要完成身份验证流程
- 可能需要提供政府颁发的身份证件

---

## �🔧 技术配置步骤

### Step 1: 生成生产Keystore

```bash
# 在项目根目录执行
cd CORDE-Mobile-Application/android/app

# 生成keystore文件
keytool -genkeypair -v -storetype PKCS12 -keystore corde-release-key.keystore -alias corde-key-alias -keyalg RSA -keysize 2048 -validity 10000

# 输入以下信息:
# - Keystore密码 (请记录保存)
# - Key密码 (请记录保存)  
# - 姓名: CORDE Development Team
# - 组织单位: CORDE
# - 组织: CORDE
# - 城市: [您的城市]
# - 省份: [您的省份]
# - 国家代码: NZ (或您的国家)
```

### Step 2: 配置Gradle签名

创建 `android/gradle.properties` 文件:
```properties
MYAPP_RELEASE_STORE_FILE=corde-release-key.keystore
MYAPP_RELEASE_KEY_ALIAS=corde-key-alias
MYAPP_RELEASE_STORE_PASSWORD=您的keystore密码
MYAPP_RELEASE_KEY_PASSWORD=您的key密码
```

### Step 3: 更新API级别 (重要)

修改 `android/app/build.gradle` 确保符合最新要求:
```gradle
android {
    compileSdk 35  // 使用最新的编译SDK

    defaultConfig {
        applicationId "nz.corde.mobile"
        minSdk 24      // 最低支持Android 7.0
        targetSdk 35   // 目标Android 15 (2025年8月31日后必需)
        versionCode 38
        versionName "1.6"
    }

    // 其他配置...
}
```

### Step 4: 配置AAB构建

修改 `android/app/build.gradle`:
```gradle
android {
    // 现有配置...

    bundle {
        language {
            enableSplit = false
        }
        density {
            enableSplit = true
        }
        abi {
            enableSplit = true
        }
    }
}
```

### Step 5: 构建生产版本

```bash
# 构建AAB文件 (推荐)
cd android
./gradlew bundleRelease

# AAB文件位置: android/app/build/outputs/bundle/release/app-release.aab

# 或构建APK (备用)
./gradlew assembleRelease
```

---

## 📱 应用商店资料准备

### 1. 应用图标
- **尺寸**: 512x512px
- **格式**: PNG (32位)
- **要求**: 高质量，无透明背景

### 2. 应用截图
- **最少**: 2张手机截图
- **尺寸**: 16:9 或 9:16 比例
- **建议**: 展示主要功能界面

### 3. 应用描述

**简短描述** (80字符以内):
```
CORDE Mobile - 专业的资产管理移动应用
```

**完整描述**:
```
CORDE Mobile Application是一款专业的资产管理移动应用，为现场工作人员提供高效的任务管理和数据收集解决方案。

主要功能：
• 任务管理 - 查看和完成分配的工作任务
• 资产定位 - 精确的GPS定位和地图导航  
• 数据收集 - 现场数据录入和照片上传
• 离线同步 - 支持离线工作，自动同步数据
• 实时通信 - 与后台系统实时数据交换

适用于：
• 公用事业公司
• 设施管理团队
• 现场服务人员
• 资产维护团队

CORDE Mobile让现场工作更高效、更准确、更便捷。
```

### 4. 应用分类
- **主要类别**: 商务 (Business)
- **次要类别**: 效率 (Productivity)

---

## 🔒 隐私政策和合规

### 1. 隐私政策要求
Google Play要求应用必须提供隐私政策URL，说明：
- 收集哪些数据
- 如何使用数据  
- 如何保护数据
- 用户权利

### 2. 数据安全表单
需要在Play Console中填写：
- 应用收集的数据类型
- 数据使用目的
- 数据共享情况
- 安全措施

### 3. 目标受众
- **目标年龄**: 18岁以上
- **内容分级**: 适合所有人

---

## 🚀 发布流程

### 1. 创建应用
1. 登录 [Google Play Console](https://play.google.com/console)
2. 点击"创建应用"
3. 填写应用名称: "CORDE Mobile"
4. 选择默认语言: 英语
5. 选择应用类型: 应用
6. 选择免费或付费: 免费

### 2. 上传应用包
1. 进入"发布" > "生产"
2. 点击"创建新版本"
3. 上传AAB文件
4. 填写版本说明

### 3. 完善商店信息
1. 上传应用图标
2. 添加应用截图
3. 填写应用描述
4. 设置分类和标签

### 4. 内容分级
1. 完成内容分级问卷
2. 获得分级证书

### 5. 目标受众和内容
1. 选择目标年龄组
2. 确认广告政策合规

### 6. 数据安全
1. 填写数据安全表单
2. 提供隐私政策URL

### 7. 应用内容
1. 确认应用功能
2. 声明权限使用

### 8. 发布审核
1. 检查所有必填项
2. 提交审核
3. 等待Google审核 (通常1-3天)

---

## ⚠️ 注意事项

### 安全提醒
1. **Keystore文件**: 妥善保管，丢失无法恢复
2. **密码安全**: 使用强密码并安全存储
3. **版本控制**: 每次更新递增versionCode

### 常见问题
1. **审核被拒**: 通常因为隐私政策或权限说明不完整
2. **签名错误**: 确保使用正确的keystore文件
3. **AAB构建失败**: 检查Gradle配置和依赖

### 后续维护
1. **定期更新**: 保持应用与最新Android API兼容
2. **用户反馈**: 及时回复用户评价和问题
3. **性能监控**: 使用Play Console监控应用性能

---

## 📞 支持联系

如需技术支持或有疑问，请联系开发团队。

**预计完成时间**: 配置完成后1-3个工作日内可提交审核。

---

## 🛠️ 详细实施步骤

### Phase 1: 准备工作 (30分钟)

#### 1.1 检查当前项目状态
```bash
# 检查当前版本信息
cd CORDE-Mobile-Application
cat android/app/build.gradle | grep -E "(versionCode|versionName|applicationId)"

# 检查当前签名配置
cat android/app/build.gradle | grep -A 10 "signingConfigs"
```

#### 1.2 备份当前配置
```bash
# 创建备份目录
mkdir -p deployment-backup/$(date +%Y%m%d)

# 备份关键文件
cp android/app/build.gradle deployment-backup/$(date +%Y%m%d)/
cp android/gradle.properties deployment-backup/$(date +%Y%m%d)/ 2>/dev/null || echo "gradle.properties不存在"
```

### Phase 2: 生产签名配置 (45分钟)

#### 2.1 生成生产Keystore
```bash
# 进入Android应用目录
cd android/app

# 生成keystore (请替换相应信息)
keytool -genkeypair -v -storetype PKCS12 \
  -keystore corde-release-key.keystore \
  -alias corde-key-alias \
  -keyalg RSA \
  -keysize 2048 \
  -validity 10000 \
  -dname "CN=CORDE Development Team, OU=CORDE, O=CORDE, L=Christchurch, ST=Canterbury, C=NZ"

# 验证keystore
keytool -list -v -keystore corde-release-key.keystore -alias corde-key-alias
```

#### 2.2 配置Gradle属性
创建/更新 `android/gradle.properties`:
```properties
# 现有配置保持不变...

# 添加签名配置
MYAPP_RELEASE_STORE_FILE=corde-release-key.keystore
MYAPP_RELEASE_KEY_ALIAS=corde-key-alias
MYAPP_RELEASE_STORE_PASSWORD=您的keystore密码
MYAPP_RELEASE_KEY_PASSWORD=您的key密码

# 优化构建
org.gradle.jvmargs=-Xmx2048m -XX:MaxPermSize=512m -XX:+HeapDumpOnOutOfMemoryError -Dfile.encoding=UTF-8
org.gradle.parallel=true
org.gradle.configureondemand=true
org.gradle.daemon=true
```

#### 2.3 更新build.gradle签名配置
需要修改 `android/app/build.gradle` 中的 `signingConfigs` 部分。

### Phase 3: AAB构建配置 (30分钟)

#### 3.1 启用Bundle构建
在 `android/app/build.gradle` 中添加bundle配置。

#### 3.2 优化应用大小
```gradle
android {
    // 启用资源压缩
    buildTypes {
        release {
            minifyEnabled true
            shrinkResources true
            proguardFiles getDefaultProguardFile("proguard-android-optimize.txt"), "proguard-rules.pro"
        }
    }

    // 分包配置
    splits {
        abi {
            enable true
            reset()
            include "arm64-v8a", "armeabi-v7a", "x86", "x86_64"
            universalApk false
        }
    }
}
```

### Phase 4: 应用信息准备 (60分钟)

#### 4.1 应用图标设计
- 使用现有logo创建512x512px图标
- 确保在不同背景下清晰可见
- 保存为PNG格式，无透明背景

#### 4.2 截图准备
建议截图内容：
1. 登录界面
2. 任务列表界面
3. 地图定位界面
4. 数据录入界面
5. 设置界面

截图规格：
- 分辨率: 1080x1920 (9:16) 或 1920x1080 (16:9)
- 格式: PNG或JPEG
- 大小: 每张不超过8MB

#### 4.3 隐私政策准备
创建隐私政策文档，包含：
- 数据收集说明
- 数据使用目的
- 数据存储和安全
- 用户权利
- 联系方式

### Phase 5: 构建和测试 (45分钟)

#### 5.1 构建AAB文件
```bash
# 清理之前的构建
cd android
./gradlew clean

# 构建Release AAB
./gradlew bundleRelease

# 验证构建结果
ls -la app/build/outputs/bundle/release/
```

#### 5.2 本地测试AAB
```bash
# 安装bundletool (如果未安装)
# 下载: https://github.com/google/bundletool/releases

# 从AAB生成APK进行测试
java -jar bundletool.jar build-apks \
  --bundle=app/build/outputs/bundle/release/app-release.aab \
  --output=corde-mobile.apks

# 安装到设备测试
java -jar bundletool.jar install-apks --apks=corde-mobile.apks
```

### Phase 6: Play Console配置 (90分钟)

#### 6.1 创建应用
1. 访问 [Google Play Console](https://play.google.com/console)
2. 创建新应用: "CORDE Mobile"
3. 选择默认语言: English (United States)
4. 应用类型: App
5. 免费或付费: Free

#### 6.2 上传应用包
1. 导航到 "Release" > "Production"
2. 创建新版本
3. 上传 `app-release.aab`
4. 填写版本说明

#### 6.3 商店信息配置
**应用详情**:
- 应用名称: CORDE Mobile
- 简短说明: Professional asset management mobile application
- 完整说明: [使用上面提供的描述]

**图形资源**:
- 应用图标: 上传512x512px图标
- 功能图片: 1024x500px (可选)
- 手机截图: 上传2-8张截图

**分类**:
- 应用类别: Business
- 内容分级: 待完成问卷

#### 6.4 内容分级
完成Google Play内容分级问卷：
1. 应用类型: 实用工具/生产力
2. 暴力内容: 无
3. 性内容: 无
4. 亵渎语言: 无
5. 受管制物质: 无
6. 赌博和竞赛: 无
7. 用户生成内容: 有 (照片上传)

#### 6.5 数据安全
填写数据安全表单：
- 位置数据: 收集 (用于资产定位)
- 照片和视频: 收集 (用于工作记录)
- 个人信息: 收集 (用户账户)
- 应用活动: 收集 (用于功能实现)

### Phase 7: 最终检查和提交 (30分钟)

#### 7.1 预发布检查清单
- [ ] AAB文件已上传并通过验证
- [ ] 应用信息完整填写
- [ ] 图标和截图已上传
- [ ] 隐私政策URL已提供
- [ ] 内容分级已完成
- [ ] 数据安全表单已填写
- [ ] 目标受众已设置
- [ ] 应用内容声明已完成

#### 7.2 提交审核
1. 检查所有必填项是否完成
2. 预览应用商店页面
3. 提交到生产轨道
4. 等待Google审核

---

## 📊 时间和成本估算

### 总时间投入: 约5.5小时
- Phase 1: 准备工作 - 30分钟
- Phase 2: 签名配置 - 45分钟
- Phase 3: AAB配置 - 30分钟
- Phase 4: 应用信息 - 60分钟
- Phase 5: 构建测试 - 45分钟
- Phase 6: Play Console - 90分钟
- Phase 7: 最终检查 - 30分钟

### 总成本: $25 USD
- Google Play开发者账号: $25 (已支付)
- 其他费用: $0 (使用现有资源)

---

## 🚨 重要安全提醒

### Keystore安全
1. **备份keystore文件**: 存储在安全位置，建议多处备份
2. **密码管理**: 使用密码管理器安全存储密码
3. **访问控制**: 限制keystore文件访问权限
4. **版本控制**: 不要将keystore文件提交到代码仓库

### 敏感信息保护
1. **gradle.properties**: 添加到 `.gitignore`
2. **API密钥**: 使用环境变量或加密存储
3. **用户数据**: 遵循GDPR和隐私法规

---

## 📈 发布后维护

### 监控指标
- 安装量和卸载率
- 崩溃率和ANR率
- 用户评分和评论
- 性能指标

### 更新策略
- 定期安全更新
- 功能改进和优化
- Android API级别更新
- 用户反馈响应

### 合规维护
- 隐私政策更新
- 数据安全审查
- 权限使用审核
- 第三方库更新

---

## 🎯 成功标准

### 技术标准
- [ ] AAB文件成功构建
- [ ] 应用在测试设备正常运行
- [ ] 所有功能正常工作
- [ ] 性能指标达标

### 商店标准
- [ ] 通过Google Play审核
- [ ] 应用成功发布
- [ ] 用户可以正常下载安装
- [ ] 应用商店信息完整准确

**下一步**: 开始Phase 1的准备工作，检查当前项目状态。
