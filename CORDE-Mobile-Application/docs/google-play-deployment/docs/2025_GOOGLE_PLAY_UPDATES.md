# 🚨 Google Play Store 2025年重要更新

## 📋 概述

本文档详细说明Google Play Store在2025年实施的重要政策和技术要求更新，确保CORDE Mobile Application符合最新标准。

**文档基于**: Google官方开发者文档 (最后更新: 2025-08-27)

---

## 🎯 关键时间节点

### 2025年8月31日 - 重要截止日期

#### API级别要求
- **新应用**: 必须目标Android 15 (API level 35)或更高
- **应用更新**: 必须目标Android 15 (API level 35)或更高
- **Wear OS/Android TV**: 必须目标Android 14 (API level 34)或更高

#### 延期选项
- **截止日期**: 2025年11月1日 (可申请延期)
- **适用范围**: 仅限企业内部应用
- **申请方式**: 通过Play Console提交延期表单

---

## 📱 技术要求更新

### 1. API级别配置

#### 当前CORDE Mobile配置
```gradle
// android/app/build.gradle
android {
    compileSdk 34          // 需要更新到35
    
    defaultConfig {
        targetSdk 34       // 需要更新到35
        minSdk 24          // 保持不变
    }
}
```

#### 更新后配置
```gradle
// android/app/build.gradle
android {
    compileSdk 35          // 更新: 使用最新SDK
    
    defaultConfig {
        applicationId "nz.corde.mobile"
        minSdk 24          // 最低支持Android 7.0
        targetSdk 35       // 更新: 目标Android 15
        versionCode 39     // 递增版本号
        versionName "1.7"  // 更新版本名
    }
}
```

### 2. 权限和安全更新

#### 新增权限要求
```xml
<!-- android/app/src/main/AndroidManifest.xml -->

<!-- 前台服务权限 (Android 9+) -->
<uses-permission android:name="android.permission.FOREGROUND_SERVICE" />

<!-- 位置权限细化 (Android 10+) -->
<uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
<uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
<uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION" />

<!-- 通知权限 (Android 13+) -->
<uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
```

#### Intent Filter导出声明 (Android 12+)
```xml
<!-- 所有intent filter必须明确声明exported属性 -->
<activity
    android:name=".MainActivity"
    android:exported="true">
    <intent-filter>
        <action android:name="android.intent.action.MAIN" />
        <category android:name="android.intent.category.LAUNCHER" />
    </intent-filter>
</activity>
```

### 3. 构建配置优化

#### Gradle配置更新
```gradle
// android/app/build.gradle
android {
    compileSdk 35
    buildToolsVersion "35.0.0"
    
    defaultConfig {
        targetSdk 35
        // 其他配置...
    }
    
    buildTypes {
        release {
            minifyEnabled true
            shrinkResources true
            proguardFiles getDefaultProguardFile("proguard-android-optimize.txt"), "proguard-rules.pro"
            
            // 签名配置
            signingConfig signingConfigs.release
        }
    }
    
    // AAB优化配置
    bundle {
        language {
            enableSplit = false
        }
        density {
            enableSplit = true
        }
        abi {
            enableSplit = true
        }
    }
}
```

---

## 🔒 数据安全和隐私要求

### 1. 数据安全表单 (强制)

#### 必须声明的数据类型
- **位置数据**: 精确位置、大概位置
- **个人信息**: 姓名、电子邮件地址
- **照片和视频**: 用户生成的照片
- **应用活动**: 应用交互、应用内搜索历史记录
- **设备或其他标识符**: 设备ID

#### 数据使用目的声明
- **应用功能**: 资产定位、任务管理
- **分析**: 应用性能监控
- **开发者通信**: 客户支持

#### 数据共享声明
- **第三方共享**: 是/否
- **数据传输**: 加密传输
- **用户控制**: 可删除数据

### 2. 隐私政策要求

#### 必须包含的内容
- 收集的数据类型详细说明
- 数据使用目的和法律依据
- 数据共享和传输说明
- 用户权利和控制选项
- 联系方式和投诉渠道

#### 隐私政策URL要求
- 必须是HTTPS链接
- 内容必须与应用功能匹配
- 必须可公开访问
- 支持应用支持的所有语言

---

## 👤 开发者账号要求

### 1. 身份验证流程

#### 新开发者账号
- **身份验证**: 政府颁发的身份证件
- **地址验证**: 银行对账单或水电费账单
- **电话验证**: SMS或语音验证
- **处理时间**: 通常3-7个工作日

#### 现有开发者账号
- **定期审查**: Google可能要求重新验证
- **政策合规**: 必须保持政策合规状态
- **联系信息**: 保持联系信息最新

### 2. 开发者政策合规

#### 内容政策
- 应用内容必须适合目标受众
- 不得包含误导性信息
- 必须提供准确的应用描述

#### 技术政策
- 应用必须稳定运行
- 不得包含恶意代码
- 必须遵循Android设计规范

---

## 🛠️ 实施检查清单

### Phase 1: 技术更新 (立即执行)
- [ ] 更新compileSdk到35
- [ ] 更新targetSdk到35
- [ ] 更新buildToolsVersion到35.0.0
- [ ] 添加必需的权限声明
- [ ] 更新intent filter导出属性
- [ ] 测试应用在Android 15上的兼容性

### Phase 2: 数据安全合规 (本周完成)
- [ ] 完成数据安全表单
- [ ] 更新隐私政策内容
- [ ] 确保隐私政策URL可访问
- [ ] 验证数据处理声明准确性

### Phase 3: 构建和测试 (下周完成)
- [ ] 构建新的AAB文件
- [ ] 在多个Android版本上测试
- [ ] 验证所有功能正常工作
- [ ] 检查性能和稳定性

### Phase 4: 提交和发布 (月底前)
- [ ] 在Play Console中更新应用信息
- [ ] 上传新的AAB文件
- [ ] 提交审核
- [ ] 监控审核状态

---

## ⚠️ 风险和缓解措施

### 高风险项目
1. **API级别不符合**: 应用无法发布更新
   - **缓解**: 立即更新到API 35
   
2. **数据安全表单不完整**: 审核被拒
   - **缓解**: 详细填写所有必需字段
   
3. **隐私政策不符合**: 政策违规
   - **缓解**: 使用专业模板并法律审查

### 中风险项目
1. **权限使用不当**: 用户体验差
   - **缓解**: 仅请求必需权限
   
2. **应用兼容性问题**: 在新Android版本上崩溃
   - **缓解**: 充分测试和调试

---

## 📞 支持资源

### Google官方资源
- [Target API Level Requirements](https://developer.android.com/google/play/requirements/target-sdk)
- [Data Safety in Play Console](https://support.google.com/googleplay/android-developer/answer/10787469)
- [Privacy Policy Requirements](https://support.google.com/googleplay/android-developer/answer/113469)

### 开发工具
- [Android Studio](https://developer.android.com/studio) - 最新版本
- [SDK Manager](https://developer.android.com/studio/intro/update#sdk-manager) - 更新SDK
- [Play Console](https://play.google.com/console) - 应用管理

### 测试工具
- [Firebase Test Lab](https://firebase.google.com/docs/test-lab) - 设备测试
- [Android Emulator](https://developer.android.com/studio/run/emulator) - 本地测试

---

## 📈 时间线和里程碑

### 即时行动 (今天)
- 更新API级别配置
- 开始数据安全表单填写

### 本周目标
- 完成技术配置更新
- 完成隐私政策更新
- 开始兼容性测试

### 下周目标
- 完成所有测试
- 构建最终AAB文件
- 准备Play Console更新

### 月底目标
- 提交应用更新
- 通过Google审核
- 成功发布新版本

---

**重要提醒**: 这些要求是强制性的，不符合要求的应用将无法在Google Play Store上发布或更新。建议立即开始实施这些更新。

**最后更新**: 2025-08-28  
**基于**: Google Play官方政策文档  
**状态**: 需要立即实施
