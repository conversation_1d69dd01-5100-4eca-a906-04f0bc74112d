# CORDE Mobile Application - 项目结构详细分析

## 项目概述

CORDE Mobile Application 是一个基于 React Native 的移动端应用程序，主要用于现场工作日志管理、资产管理和数据同步。该应用采用了模块化的清洁架构设计，支持离线工作和在线同步功能。

## 技术栈

### 核心框架
- **React Native 0.74.2** - 主要框架
- **TypeScript** - 类型安全的 JavaScript
- **React 18.2.0** - UI 组件库

### 导航与状态管理
- **@react-navigation/native** - 主导航系统
- **@react-navigation/native-stack** - 堆栈导航
- **@react-navigation/bottom-tabs** - 底部标签导航
- **React Context** - 状态管理 (AuthContext, SyncContext, ThemeContext)

### 数据库与存储
- **react-native-sqlite-storage** - 本地 SQLite 数据库
- **@react-native-async-storage/async-storage** - 异步存储
- **react-native-keychain** - 安全凭证存储

### UI 组件与样式
- **native-base** - UI 组件库
- **react-native-vector-icons** - 图标库
- **react-native-linear-gradient** - 渐变效果

### 网络与数据同步
- **axios** - HTTP 客户端
- **@react-native-community/netinfo** - 网络状态检测

### 地图与位置服务
- **react-native-maps** - 地图组件
- **@react-native-community/geolocation** - 位置服务
- **react-native-geolocation-service** - 增强位置服务

### 文件处理
- **react-native-fs** - 文件系统操作
- **react-native-document-picker** - 文档选择器
- **react-native-image-picker** - 图片选择器
- **xlsx** - Excel 文件处理

## 项目架构

### 1. 应用入口 (`src/App.tsx`)
```typescript
- 数据库初始化 (initDB)
- 位置服务初始化 (locationService.initialize)
- 后台同步服务启动 (BackgroundSyncService.startBackgroundSync)
- 全局 Provider 包装 (ThemeProvider, AuthProvider, SyncProvider)
```

### 2. 导航系统 (`src/navigation/`)
- **AppNavigator.tsx** - 主导航器，基于用户认证状态的条件渲染
- **DebugTabNavigator.tsx** - 调试页面标签导航
- **SyncLogTabNavigator.tsx** - 同步日志标签导航

### 3. 屏幕组件 (`src/screens/`)
- **LoginScreen.tsx** - 登录界面
- **MainDashboardScreen.tsx** - 主仪表板
- **NewLogScreen.tsx** - 新建日志
- **LogListScreen.tsx** - 日志列表
- **LogHeaderScreen.tsx** - 日志详情
- **SyncLogScreen.tsx** - 同步日志管理
- **DebugScreen.tsx** - 调试信息
- **SettingsScreen.tsx** - 设置页面
- **MapMarkerScreen.tsx** - 地图标记
- **CreateGroupScreen.tsx** - 创建组
- **CompleteGroupScreen.tsx** - 完成组

### 4. 组件架构 (`src/components/`)
按功能模块组织：
```
components/
├── LoginScreen/          # 登录相关组件
├── MainDashboardScreen/  # 仪表板组件
├── NewLogScreen/         # 新建日志组件
├── LogListScreen/        # 日志列表组件
├── LogHeaderScreen/      # 日志详情组件
├── SyncLogScreen/        # 同步相关组件
├── DebugScreen/          # 调试组件
├── SettingsScreen/       # 设置组件
├── CreateGroupScreen/    # 创建组组件
├── CompleteGroupScreen/  # 完成组组件
└── UserProfile/          # 用户配置文件
```

### 5. 数据层架构

#### 数据库模型 (`src/database/`)
- **DatabaseInit.tsx** - 数据库初始化和迁移
- **Users.ts** - 用户数据模型
- **LogList.ts** - 日志列表数据模型
- **Assets.ts** - 资产数据模型
- **ServiceAgreement.ts** - 服务协议数据模型
- **Staff.ts** - 员工数据模型
- **ExtensionGroup.ts** - 扩展组数据模型
- **ExtensionColumn.ts** - 扩展列数据模型
- **ExtensionLine.ts** - 扩展行数据模型
- **StoredFiles.ts** - 存储文件数据模型
- **MobileSyncLogs.ts** - 移动同步日志数据模型

#### API 层 (`src/api/`)
- **BaseApi.ts** - 基础 API 配置和拦截器
- **AccountApi.ts** - 账户相关 API
- **LogApi.ts** - 日志相关 API
- **AssetsApi.ts** - 资产相关 API
- **StaffApi.ts** - 员工相关 API
- **ServiceAgreementApi.ts** - 服务协议 API
- **ExtensionGroupApi.ts** - 扩展组 API
- **ExtensionColumnApi.ts** - 扩展列 API
- **GoogleMapsApi.ts** - Google 地图 API

### 6. 服务层 (`src/services/`)
- **AuthService.ts** - 认证服务 (登录、登出、自动登录)
- **SyncService.ts** - 数据同步服务
- **BackgroundSyncService.ts** - 后台同步服务
- **LogListService.ts** - 日志列表服务
- **AssetsService.ts** - 资产服务
- **LocationService.ts** - 位置服务
- **NetworkService.ts** - 网络服务

### 7. Context 状态管理 (`src/context/`)
- **AuthContext.tsx** - 认证状态管理
- **SyncContext.tsx** - 同步状态管理
- **ThemeContext.tsx** - 主题状态管理

## 数据流

### 1. 认证流程
```
用户输入 → AuthService.login() → API验证/本地验证 → 
AuthContext更新 → AppNavigator路由切换
```

### 2. 数据同步流程
```
SyncService.startAllSyncTasks() → 
各种Service同步 (LogListService, AssetsService等) → 
API获取数据 → SQLite本地存储 → UI更新
```

### 3. 日志管理流程
```
创建日志 → NewLogScreen → LogList数据模型 → 
本地SQLite存储 → 后台同步到服务器
```

## 关键特性

### 1. 离线优先设计
- SQLite 本地数据库存储
- 网络状态检测
- 后台数据同步
- 离线工作能力

### 2. 认证系统
- 基础认证 (Basic Auth)
- 记住我功能
- 自动登录
- 多用户支持

### 3. 数据同步策略
- 首次启动全量同步
- 日常增量同步
- 按需手动同步
- 日期范围同步

### 4. 地图与位置功能
- 实时位置获取
- 地图标记
- 资产坐标导入
- Google Maps 集成

### 5. 文件管理
- 图片上传
- 文档处理
- Excel 导入
- 文件本地存储

## 数据库设计

### 核心表结构
1. **users** - 用户信息
2. **log_list** - 工作日志
3. **assets** - 资产信息
4. **service_agreement** - 服务协议
5. **staff** - 员工信息
6. **extension_group/column/line** - 扩展字段系统
7. **stored_files** - 文件存储
8. **mobile_sync_logs** - 同步日志

### 数据库迁移
- 版本控制系统
- 自动迁移脚本
- PRAGMA 优化设置

## 配置与环境

### 环境配置
- 开发/测试/生产环境 API 端点
- Google Maps API 密钥
- 数据库配置

### 构建配置
- Android/iOS 平台支持
- TypeScript 配置
- Metro 打包配置
- ESLint 代码规范

## 性能优化

### 1. 数据库优化
- WAL 模式启用
- 索引优化
- 批量操作
- 连接池管理

### 2. 网络优化
- 请求拦截器
- 错误处理
- 超时设置
- 重试机制

### 3. UI 优化
- 懒加载
- 虚拟化列表
- 图片优化
- 内存管理

## 开发规范

### 代码组织
- 功能模块化
- 清洁架构原则
- 单一职责原则
- 依赖注入

### 命名规范
- 英文命名
- 驼峰命名法
- 语义化命名
- 一致性原则

## 部署与维护

### 平台支持
- Android (最低 API 级别支持)
- iOS (最低版本支持)
- 热更新支持

### 监控与日志
- 错误日志记录
- 性能监控
- 用户行为跟踪
- 崩溃报告

这个 CORDE Mobile Application 是一个设计良好、功能完整的企业级移动应用，具有强大的离线工作能力和数据同步功能，适合现场工作人员使用。
