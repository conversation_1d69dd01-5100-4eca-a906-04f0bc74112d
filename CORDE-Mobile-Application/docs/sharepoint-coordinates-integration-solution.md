# SharePoint坐标数据集成解决方案

## 📋 **项目背景**

客户需要将移动应用中的坐标数据同步功能从文件选择器改为直接连接SharePoint Excel文件，实现自动化数据获取，提升用户体验。

**核心需求：**
- 移除文件选择器，用户无需手动选择文件
- 直接连接特定的SharePoint Excel文件
- 用户无需登录Microsoft账户
- 该文件有访问限制且定期更新
- 用户体验要简单

## 🏗️ **整体架构设计**

### **架构图**
```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   SharePoint    │    │   后端服务        │    │   移动应用       │
│                 │    │ (新建服务)     │    │     co            │
│ assets_coord... │◄───┤                  │◄───┤ React Native    │
│ .xlsx           │    │ + 新增坐标API     │    │ App             │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

### **各组件职责**

#### **1. SharePoint (数据源)**
- **作用**: 存储和维护坐标数据的Excel文件
- **位置**: 客户的SharePoint站点
- **配置**: 无需额外配置，客户现有维护
- **数据**: `assets_coordinates.xlsx` 文件

#### **2. 后端服务 (数据中介)**
- **作用**: 数据中介和缓存层
- **位置**: 现有的C#水表后端服务器
- **部署**: 在现有项目中添加新功能
- **职责**:
  - 通过Microsoft Graph API从SharePoint获取Excel数据
  - 将Excel转换为JSON格式
  - 缓存数据避免频繁请求SharePoint
  - 为移动端提供标准的REST API

#### **3. 移动应用 (数据消费者)**
- **作用**: 数据消费者
- **位置**: 用户手机上
- **修改**: **新增SharePoint同步功能，保留现有文件导入功能**
- **职责**: 
  - **保持**: 现有文件选择器导入功能
  - **新增**: 调用后端API同步SharePoint坐标数据到现有asset_coordinates表

## 🔄 **数据流向设计**

### **初始配置阶段**
```
1. 客户 → 在Azure AD中注册应用，提供认证信息
2. 开发者 → 在后端配置Microsoft Graph API连接
3. 开发者 → 在后端添加坐标同步服务
4. 开发者 → 修改移动端调用新API
```

### **日常运行流程**
```
用户点击"同步SharePoint坐标" (新增功能)
    ↓
移动端发送请求到后端API 
    ↓
后端检查缓存
    ↓ (如果缓存过期)
后端通过Microsoft Graph API从SharePoint下载Excel
    ↓
后端解析Excel为JSON (AMSKEY → asset_id, latitude, longitude)
    ↓
后端缓存数据并返回给移动端
    ↓
移动端接收JSON数据
    ↓
移动端调用现有AssetCoordinates.upsertBatch()存储到asset_coordinates表

注意: 现有文件导入功能保持不变，这是额外的同步方式
```

## 🔐 **认证与权限体系**

### **Azure AD租户权限关系**
```
客户组织 (SICONNZ公司)
├── Azure AD租户：siconnz.onmicrosoft.com  
├── SharePoint：siconnz.sharepoint.com
├── 员工：<EMAIL>, <EMAIL>
└── 我们创建的应用：CORDE-Backend-App
```

### **权限工作原理**
1. **应用注册在客户租户下** → 获得租户内部身份
2. **配置应用权限** → Sites.Read.All, Files.Read.All
3. **管理员授权** → 正式授权给应用访问权限
4. **应用可访问** → 客户租户下的所有SharePoint站点和文件

### **访问方式对比**
| 访问方式 | 适用场景 | 认证方式 | 程序化访问 |
|---------|---------|---------|-----------|
| 内部员工 | 日常办公 | 公司Azure AD账号 | ❌ |
| 外部人员 | 临时访问 | 共享链接 + 邮箱验证 | ❌ |
| **后端应用** | **自动化集成** | **应用注册 + Client Secret** | **✅** |

## 🔧 **技术实现方案**

### **第1步：Azure AD应用注册配置**

客户需要在Azure门户完成：

```
1. 登录 https://portal.azure.com
2. 进入 "Azure Active Directory" → "App registrations" 
3. 点击 "New registration"
4. 填写应用信息：
   - Name: "CORDE Mobile Backend"
   - Account types: "Single tenant"
5. 注册完成后获取：
   - Application (client) ID
   - Directory (tenant) ID
6. 创建 Client Secret：
   - "Certificates & secrets" → "New client secret"
   - 复制 Secret Value (只显示一次)
7. 配置API权限：
   - "API permissions" → "Add a permission"
   - 选择 "Microsoft Graph" → "Application permissions"
   - 添加权限：Sites.Read.All, Files.Read.All
   - 点击 "Grant admin consent" (管理员同意)
```

### **第2步：获取SharePoint文件路径信息**

客户提供文件的完整路径信息：
- ✅ SharePoint站点URL: `https://siconnz.sharepoint.com/sites/DataProcessing`
- ❓ 文件的具体路径: `/Shared Documents/assets_coordinates.xlsx` (需要确认)
- ❓ 或通过Microsoft Graph Explorer获取Site ID、Drive ID、Item ID (需要获取)

**✅ 已获得的信息：**
1. **SharePoint站点**: `siconnz.sharepoint.com/sites/DataProcessing`
2. **文件名**: `assets_coordinates.xlsx`
3. **文件访问链接**: [Guest Access Link](https://siconnz.sharepoint.com/:x:/r/sites/DataProcessing/_layouts/15/guestaccess.aspx?email=szz185%40gmail.com&e=RB7DVc&share=ETHwR7di7RxJuHivrM2lEU0B5TDtUDJzVTISEb11tEiqRw)

**❓ 下一步需要：**
1. **访问认证信息**: 通过 `https://nz.onetimesecret.com/secret/5oinftk4v9ckf2zj46sjkuvde6oex42` 获取Azure AD凭据
Directory (tenant) ID: 637a79a6-985f-4dcd-aa06-354f067419bf
Application (client) ID: d50296cf-228e-4139-a530-71a68b516c58
Client Secret: ****************************************
Secret Expiry: 21/08/2027
2. **获取具体文件路径**: 使用Graph API确定文件的准确位置

### **第3步：NuGet包依赖**
```xml
<PackageReference Include="Microsoft.Graph" Version="5.36.0" />
<PackageReference Include="Microsoft.Graph.Auth" Version="1.0.0-preview.7" />
<PackageReference Include="ClosedXML" Version="0.102.1" />
<PackageReference Include="Microsoft.Extensions.Caching.Memory" Version="7.0.0" />
```

### **第4步：配置文件设置**
```json
{
  "SharePointConfig": {
    "TenantId": "YOUR_TENANT_ID",
    "ClientId": "YOUR_CLIENT_ID", 
    "ClientSecret": "YOUR_CLIENT_SECRET",
    "SiteId": "YOUR_SITE_ID",
    "DriveId": "YOUR_DRIVE_ID",
    "FileId": "YOUR_EXCEL_FILE_ID",
    "CacheExpiryMinutes": 60
  }
}
```

### **第5步：核心认证服务实现**

```csharp
public class SharePointAuthService
{
    private readonly string _tenantId;
    private readonly string _clientId; 
    private readonly string _clientSecret;

    public SharePointAuthService(IConfiguration configuration)
    {
        _tenantId = configuration["SharePointConfig:TenantId"];
        _clientId = configuration["SharePointConfig:ClientId"];
        _clientSecret = configuration["SharePointConfig:ClientSecret"];
    }

    // 获取Access Token的核心方法
    public async Task<string> GetAccessTokenAsync()
    {
        var app = ConfidentialClientApplicationBuilder
            .Create(_clientId)
            .WithClientSecret(_clientSecret)
            .WithAuthority(new Uri($"https://login.microsoftonline.com/{_tenantId}"))
            .Build();

        // 使用应用权限（不需要用户登录）
        string[] scopes = { "https://graph.microsoft.com/.default" };
        
        var result = await app.AcquireTokenForClient(scopes).ExecuteAsync();
        return result.AccessToken;
    }
}
```

### **第6步：文件获取服务实现（基于文件名查找）**

```csharp
public class SharePointFileService
{
    private readonly HttpClient _httpClient;
    private readonly SharePointAuthService _authService;
    private readonly IConfiguration _configuration;
    private readonly string _siteUrl;
    private readonly string _fileName;

    public SharePointFileService(
        HttpClient httpClient, 
        SharePointAuthService authService,
        IConfiguration configuration)
    {
        _httpClient = httpClient;
        _authService = authService;
        _configuration = configuration;
        _siteUrl = configuration["SharePointConfig:SiteUrl"];
        _fileName = configuration["SharePointConfig:FileName"];
    }

    // 通过文件名查找并下载文件
    public async Task<byte[]> GetFileByNameAsync()
    {
        try
        {
            var accessToken = await _authService.GetAccessTokenAsync();
            _httpClient.DefaultRequestHeaders.Authorization = 
                new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

            // Step 1: 获取站点信息
            var siteResponse = await _httpClient.GetAsync(
                "https://graph.microsoft.com/v1.0/sites/siconnz.sharepoint.com:/sites/DataProcessing");
            siteResponse.EnsureSuccessStatusCode();
            
            var siteData = await siteResponse.Content.ReadAsStringAsync();
            var siteInfo = JsonSerializer.Deserialize<JsonElement>(siteData);
            var siteId = siteInfo.GetProperty("id").GetString();

            // Step 2: 获取默认文档库
            var driveResponse = await _httpClient.GetAsync(
                $"https://graph.microsoft.com/v1.0/sites/{siteId}/drive");
            driveResponse.EnsureSuccessStatusCode();
            
            var driveData = await driveResponse.Content.ReadAsStringAsync();
            var driveInfo = JsonSerializer.Deserialize<JsonElement>(driveData);
            var driveId = driveInfo.GetProperty("id").GetString();

            // Step 3: 搜索指定文件
            var searchUrl = $"https://graph.microsoft.com/v1.0/sites/{siteId}/drive/root/search(q='{_fileName}')";
            var searchResponse = await _httpClient.GetAsync(searchUrl);
            searchResponse.EnsureSuccessStatusCode();
            
            var searchData = await searchResponse.Content.ReadAsStringAsync();
            var searchResult = JsonSerializer.Deserialize<JsonElement>(searchData);
            
            var files = searchResult.GetProperty("value").EnumerateArray();
            var targetFile = files.FirstOrDefault(f => 
                f.GetProperty("name").GetString().Equals(_fileName, StringComparison.OrdinalIgnoreCase));
                
            if (targetFile.ValueKind == JsonValueKind.Undefined)
            {
                throw new FileNotFoundException($"File '{_fileName}' not found in SharePoint site");
            }

            // Step 4: 下载文件内容
            var downloadUrl = targetFile.GetProperty("@microsoft.graph.downloadUrl").GetString();
            var fileResponse = await _httpClient.GetAsync(downloadUrl);
            fileResponse.EnsureSuccessStatusCode();
            
            return await fileResponse.Content.ReadAsByteArrayAsync();
        }
        catch (Exception ex)
        {
            throw new Exception($"Failed to download file from SharePoint: {ex.Message}", ex);
        }
    }

    // 通过Site ID和Drive ID获取文件（推荐方式）
    public async Task<Stream> GetFileByIdAsync()
    {
        var accessToken = await _authService.GetAccessTokenAsync();
        _httpClient.DefaultRequestHeaders.Authorization = 
            new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

        var siteId = _configuration["SharePointConfig:SiteId"];
        var driveId = _configuration["SharePointConfig:DriveId"];
        var itemId = _configuration["SharePointConfig:FileId"];

        var url = $"https://graph.microsoft.com/v1.0/sites/{siteId}/drives/{driveId}/items/{itemId}/content";
        
        var response = await _httpClient.GetAsync(url);
        response.EnsureSuccessStatusCode();
        
        return await response.Content.ReadAsStreamAsync();
    }

    // 检查文件是否有更新（实现缓存逻辑）
    public async Task<bool> IsFileModifiedAsync(DateTime lastCheck)
    {
        var accessToken = await _authService.GetAccessTokenAsync();
        _httpClient.DefaultRequestHeaders.Authorization = 
            new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

        var siteId = _configuration["SharePointConfig:SiteId"];
        var driveId = _configuration["SharePointConfig:DriveId"];
        var itemId = _configuration["SharePointConfig:FileId"];

        var url = $"https://graph.microsoft.com/v1.0/sites/{siteId}/drives/{driveId}/items/{itemId}";
        
        var response = await _httpClient.GetAsync(url);
        var jsonResponse = await response.Content.ReadAsStringAsync();
        var fileInfo = JsonSerializer.Deserialize<JsonElement>(jsonResponse);
        
        var lastModified = DateTime.Parse(fileInfo.GetProperty("lastModifiedDateTime").GetString());
        return lastModified > lastCheck;
    }
}
```

### **第7步：坐标数据服务实现**

```csharp
using Microsoft.Graph;
using Microsoft.Graph.Auth;
using Microsoft.Identity.Client;
using ClosedXML.Excel;
using System.Text.Json;

public class SharePointCoordinatesService
{
    private readonly IConfiguration _configuration;
    private readonly IMemoryCache _cache;
    private readonly ILogger<SharePointCoordinatesService> _logger;
    private readonly SharePointFileService _fileService;
    private const string CACHE_KEY = "asset_coordinates";

    public SharePointCoordinatesService(
        IConfiguration configuration, 
        IMemoryCache cache,
        ILogger<SharePointCoordinatesService> logger,
        SharePointFileService fileService)
    {
        _configuration = configuration;
        _cache = cache;
        _logger = logger;
        _fileService = fileService;
    }

    public async Task<object> GetCoordinatesAsync()
    {
        try
        {
            // Check cache first
            if (_cache.TryGetValue(CACHE_KEY, out object cachedData))
            {
                _logger.LogInformation("Returning cached coordinates data");
                return cachedData;
            }

            // Fetch from SharePoint
            var coordinates = await FetchCoordinatesFromSharePointAsync();
            
            // Cache the data
            var cacheExpiry = TimeSpan.FromMinutes(
                _configuration.GetValue<int>("SharePointConfig:CacheExpiryMinutes"));
            _cache.Set(CACHE_KEY, coordinates, cacheExpiry);

            _logger.LogInformation($"Fetched and cached {((List<object>)coordinates).Count} coordinates");
            return coordinates;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error fetching coordinates");
            throw;
        }
    }

    private async Task<List<object>> FetchCoordinatesFromSharePointAsync()
    {
        // Get file content from SharePoint
        var fileStream = await _fileService.GetFileByIdAsync();

        // Parse Excel file
        return ParseExcelToCoordinates(fileStream);
    }

    private List<object> ParseExcelToCoordinates(Stream excelStream)
    {
        var coordinates = new List<object>();

        using var workbook = new XLWorkbook(excelStream);
        var worksheet = workbook.Worksheet(1); // First worksheet

        // Excel格式: AMSKEY, latitude, longitude (直接对齐移动端asset_coordinates表)
        // 示例数据: 516925, -43.61380511, 172.5086473
        var lastRow = worksheet.LastRowUsed().RowNumber();
        
        for (int row = 2; row <= lastRow; row++) // Skip header row
        {
            var coordinate = new
            {
                asset_id = int.Parse(worksheet.Cell(row, 1).GetString()),  // AMSKEY → asset_id
                latitude = worksheet.Cell(row, 2).GetDouble(),             // latitude → latitude  
                longitude = worksheet.Cell(row, 3).GetDouble()             // longitude → longitude
            };

            coordinates.Add(coordinate);
        }

        return coordinates;
    }

    public async Task RefreshCacheAsync()
    {
        _cache.Remove(CACHE_KEY);
        await GetCoordinatesAsync();
    }
}
```

### **第8步：API控制器实现**

```csharp
[ApiController]
[Route("api/[controller]")]
public class CoordinatesController : ControllerBase
{
    private readonly SharePointCoordinatesService _coordinatesService;

    public CoordinatesController(SharePointCoordinatesService coordinatesService)
    {
        _coordinatesService = coordinatesService;
    }

    [HttpGet]
    public async Task<IActionResult> GetCoordinates()
    {
        try
        {
            var coordinates = await _coordinatesService.GetCoordinatesAsync();
            return Ok(new { data = coordinates, timestamp = DateTime.UtcNow });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { error = ex.Message });
        }
    }

    [HttpPost("refresh")]
    public async Task<IActionResult> RefreshCoordinates()
    {
        try
        {
            await _coordinatesService.RefreshCacheAsync();
            return Ok(new { message = "Cache refreshed successfully" });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { error = ex.Message });
        }
    }
}
```

### **第9步：依赖注入配置**

```csharp
// In Program.cs (ASP.NET Core 6+)
builder.Services.AddMemoryCache();
builder.Services.AddHttpClient();
builder.Services.AddScoped<SharePointAuthService>();
builder.Services.AddScoped<SharePointFileService>();
builder.Services.AddScoped<SharePointCoordinatesService>();

// 或在 Startup.cs
services.AddMemoryCache();
services.AddHttpClient();
services.AddScoped<SharePointAuthService>();
services.AddScoped<SharePointFileService>();
services.AddScoped<SharePointCoordinatesService>();
```

## 📱 **CORDE-Mobile-Application集成修改**

**⚠️ 重要：保持现有所有功能不变，仅新增SharePoint同步功能**

### **API端点配置**

```typescript
// 在 CORDE-Mobile-Application/src/api/BaseApi.ts 中添加
export const SHAREPOINT_COORDINATES_URL = 'http://your-server:5088/api/coordinates';
```

### **新增同步功能 (不影响现有文件导入)**

```typescript
import { AssetCoordinates } from '../database/AssetCoordinates';

// 新增SharePoint坐标同步服务 (与现有文件导入功能并存)
const syncCoordinatesFromSharePoint = async () => {
  try {
    setIsLoading(true);
    
    // 使用现有认证方式
    const credentials = await AuthService.getStoredCredentials();
    const authHeader = credentials 
      ? encode(`${credentials.username}:${credentials.password}`)
      : encode('luke.shi:gentoo666');
    
    const response = await fetch(SHAREPOINT_COORDINATES_URL, {
      method: 'GET',
      headers: {
        'Authorization': `Basic ${authHeader}`,
        'Content-Type': 'application/json',
      },
    });
    
    const result = await response.json();
    
    if (response.ok) {
      // API返回格式: [{ asset_id: 516925, latitude: -43.61380511, longitude: 172.5086473 }]
      // 直接映射到现有AssetCoordinates类
      const coordinates = result.data.map((item: any) => new AssetCoordinates({
        asset_id: item.asset_id,
        latitude: item.latitude,
        longitude: item.longitude
      }));
      
      // 使用现有的批量插入方法，存储到asset_coordinates表
      await AssetCoordinates.upsertBatch(coordinates);
      Alert.alert('Success', `${coordinates.length} coordinates synced from SharePoint`);
    } else {
      throw new Error(result.error || 'Failed to fetch coordinates');
    }
  } catch (error) {
    console.error('SharePoint sync error:', error);
    Alert.alert('Error', 'Failed to sync coordinates from SharePoint');
  } finally {
    setIsLoading(false);
  }
};

// 新增处理函数 (与现有函数并存)
const handleSyncFromSharePoint = async () => {
  await syncCoordinatesFromSharePoint();
};

// 注意: 现有的handleImportCoordinates()函数完全保持不变
```

### **UI修改 (最小化改动)**

```typescript
// 在现有坐标导入界面添加新按钮，与文件导入按钮并列
<View style={styles.buttonContainer}>
  <Button 
    title="Import from File" 
    onPress={handleImportCoordinates}  // 保持现有功能不变
    style={styles.importButton}
  />
  <Button 
    title="Sync from SharePoint"       // 新增功能
    onPress={handleSyncFromSharePoint}
    style={styles.syncButton}
  />
</View>
```

## 🚀 **部署实施计划**

### **阶段1: 后端部署**
- **位置**: 现有的水表后端服务器
- **任务**: 
  - 添加NuGet包依赖
  - 添加坐标服务类和控制器
  - 配置SharePoint连接参数
  - 测试API端点

### **阶段2: 移动端修改**
- **位置**: React Native项目
- **任务**:
  - 修改坐标导入逻辑
  - 将文件选择器替换为API调用
  - 更新UI提示信息
  - 测试完整流程

### **阶段3: 配置与测试**
- **任务**:
  - 客户提供Azure AD认证信息
  - 配置SharePoint文件路径
  - 端到端测试
  - 性能优化和错误处理

## 🔍 **权限验证与故障排除**

### **权限验证清单**
```
✅ 应用注册在客户的Azure AD租户
✅ 配置了正确的权限 (Sites.Read.All, Files.Read.All)
✅ 管理员点击了"Grant admin consent"
✅ 文件确实在客户的SharePoint租户内
✅ 应用使用正确的Tenant ID, Client ID, Client Secret
✅ SharePoint文件路径信息正确
```

### **常见问题解决**
| 问题 | 原因 | 解决方案 |
|------|------|----------|
| 403 Forbidden | 权限不足或未获得管理员同意 | 检查权限配置，重新获得管理员同意 |
| 404 Not Found | 文件路径错误或不在同一租户 | 确认文件URL和租户信息 |
| 认证失败 | 应用注册在错误的租户 | 在正确的Azure AD中重新注册应用 |

## 📈 **技术优势与特性**

### **核心优势**
- ✅ **安全性高**: SharePoint认证信息不暴露给移动端
- ✅ **稳定性强**: 后端缓存减少SharePoint访问频率  
- ✅ **用户体验简单**: 移动端只需要调用一个API
- ✅ **维护方便**: 集中管理数据格式和访问频率
- ✅ **企业级**: 符合企业安全标准和权限管理

### **缓存策略**
- **问题**: SharePoint API有访问限制和延迟
- **解决**: 后端缓存1小时，避免频繁请求
- **好处**: 提高响应速度，减少API调用成本

### **认证机制**
- **应用权限**: 后端服务使用固定的应用凭据
- **无用户交互**: 移动端用户无需登录Microsoft账户
- **一次配置**: 管理员配置一次，所有用户受益

## 📝 **客户需要提供的信息**

### **必需信息**
1. **Azure AD认证信息**:
   - Tenant ID
   - Client ID  
   - Client Secret
   - 管理员同意确认

2. **SharePoint文件信息**:
   - SharePoint站点URL
   - 文件的Site ID、Drive ID、Item ID
   - 或文件的完整路径

3. **部署环境信息**:
   - 后端服务部署位置偏好
   - API访问域名配置

## 🎯 **总结**

这个解决方案通过Microsoft Graph API实现了移动应用与SharePoint的无缝集成，在保证安全性和稳定性的同时，大大简化了用户操作流程。后端服务作为中介层，不仅解决了认证和权限问题，还通过缓存机制优化了性能。整个方案符合企业级应用的安全标准，为后续的功能扩展奠定了良好的基础。

---

## 🔧 **后端服务技术架构设计**

基于邮件沟通和认证信息，现在制定详细的C#后端服务实施计划。

### **项目规格**
- **参考项目**: WaterMeterManagement
- **技术栈**: ASP.NET Core 7.0+ (与WaterMeterManagement保持一致)
- **数据库**: PostgreSQL (用于缓存坐标数据)
- **部署**: Docker容器化
- **缓存**: Redis + In-Memory (双层缓存)
- **监控**: Serilog结构化日志

### **项目结构设计**
```
SharePointCoordinatesService/
├── Controllers/
│   └── CoordinatesController.cs
├── Services/
│   ├── SharePointAuthService.cs
│   ├── SharePointFileService.cs
│   └── CoordinatesService.cs
├── Models/
│   ├── CoordinateModel.cs
│   └── SharePointConfig.cs
├── Data/
│   ├── CoordinatesDbContext.cs
│   └── Migrations/
├── DTOs/
│   └── CoordinateDto.cs
├── Repositories/
│   └── CoordinatesRepository.cs
├── Middlewares/
│   └── ExceptionHandlingMiddleware.cs
├── Dockerfile
├── docker-compose.yml
└── appsettings.json
```

### **数据库设计**

**是否需要数据库？** ✅ **强烈建议使用**

**原因：**
1. **缓存持久化**: 防止服务重启丢失缓存数据
2. **离线能力**: SharePoint不可用时仍能提供数据
3. **性能优化**: 数据库查询比实时API调用快
4. **历史记录**: 跟踪数据变化和同步日志
5. **Docker部署**: 容器重启时保持数据完整性

**推荐数据库表设计：**
```sql
-- 后端缓存表 (用于SharePoint数据缓存，移动端继续使用现有表)
CREATE SCHEMA IF NOT EXISTS sharepoint_coords;

CREATE TABLE sharepoint_coords.SharePointCoordinatesCache (
    Id SERIAL PRIMARY KEY,
    AmsKey VARCHAR(100) NOT NULL UNIQUE,  -- 对应Excel的AMSKEY字段
    Latitude DECIMAL(10, 8) NOT NULL,     -- 对应Excel的latitude字段
    Longitude DECIMAL(11, 8) NOT NULL,    -- 对应Excel的longitude字段
    LastModified TIMESTAMP NOT NULL,
    CreatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UpdatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 注意: 移动端继续使用现有的asset_coordinates表，无需修改:
-- CREATE TABLE asset_coordinates (
--   asset_id INTEGER PRIMARY KEY UNIQUE NOT NULL,  -- 对应AMSKEY
--   latitude REAL NOT NULL,
--   longitude REAL NOT NULL
-- )

-- 同步日志表
CREATE TABLE sharepoint_coords.SyncLogs (
    Id SERIAL PRIMARY KEY,
    SyncStartTime TIMESTAMP NOT NULL,
    SyncEndTime TIMESTAMP,
    RecordsProcessed INTEGER,
    Status VARCHAR(20) NOT NULL, -- 'Success', 'Failed', 'InProgress'
    ErrorMessage TEXT,
    SharePointFileModified TIMESTAMP,
    CreatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 索引优化
CREATE INDEX idx_coords_ams_key ON sharepoint_coords.SharePointCoordinatesCache(AmsKey);
CREATE INDEX idx_coords_location ON sharepoint_coords.SharePointCoordinatesCache(Latitude, Longitude);
CREATE INDEX idx_sync_logs_status ON sharepoint_coords.SyncLogs(Status);
CREATE INDEX idx_sync_logs_time ON sharepoint_coords.SyncLogs(SyncStartTime DESC);
```

### **技术栈配置**

**NuGet包清单** (参考WaterMeterManagement版本):
```xml
<PackageReference Include="Microsoft.EntityFrameworkCore" Version="7.0.20" />
<PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="7.0.20" />
<PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="7.0.18" />
<PackageReference Include="Microsoft.Graph" Version="5.56.0" />
<PackageReference Include="Microsoft.Graph.Auth" Version="1.0.0-preview.7" />
<PackageReference Include="Microsoft.Identity.Client" Version="4.61.3" />
<PackageReference Include="ClosedXML" Version="0.102.1" />
<PackageReference Include="AutoMapper" Version="12.0.1" />
<PackageReference Include="AutoMapper.Extensions.Microsoft.DependencyInjection" Version="12.0.1" />
<PackageReference Include="Serilog.AspNetCore" Version="7.0.0" />
<PackageReference Include="Serilog.Sinks.PostgreSQL" Version="2.3.0" />
<PackageReference Include="StackExchange.Redis" Version="2.6.122" />
<PackageReference Include="Swashbuckle.AspNetCore" Version="6.5.0" />
<PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="7.0.20" />
```

### **环境配置文件**

**appsettings.json**:
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Host=postgres;Database=SharePointCoordinatesDB;Username=coorduser;Password=SecurePassword123!",
    "Redis": "redis:6379"
  },
  "SharePointConfig": {
    "TenantId": "637a79a6-985f-4dcd-aa06-354f067419bf",
    "ClientId": "d50296cf-228e-4139-a530-71a68b516c58",
    "ClientSecret": "****************************************",
    "SiteUrl": "https://siconnz.sharepoint.com/sites/DataProcessing",
    "FileName": "assets_coordinates.xlsx",
    "CacheExpiryMinutes": 60,
    "RetryAttempts": 3,
    "TimeoutSeconds": 30
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning",
      "Microsoft.Graph": "Information"
    }
  },
  "AllowedHosts": "*",
  "Authentication": {
    "ApiKey": "CORDE-SharePoint-Service-2024",
    "EnableApiKeyAuth": true
  }
}
```

**appsettings.Production.json**:
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Host=production-postgres;Database=SharePointCoordinatesDB;Username=${POSTGRES_USER};Password=${POSTGRES_PASSWORD}",
    "Redis": "production-redis:6379"
  },
  "SharePointConfig": {
    "ClientSecret": "${SHAREPOINT_CLIENT_SECRET}"
  },
  "Logging": {
    "LogLevel": {
      "Default": "Warning",
      "SharePointCoordinatesService": "Information"
    }
  }
}
```

### **Docker部署配置**

**Dockerfile**:
```dockerfile
FROM mcr.microsoft.com/dotnet/aspnet:7.0 AS base
WORKDIR /app
EXPOSE 80
EXPOSE 443

FROM mcr.microsoft.com/dotnet/sdk:7.0 AS build
WORKDIR /src
COPY ["SharePointCoordinatesService.csproj", "."]
RUN dotnet restore "SharePointCoordinatesService.csproj"
COPY . .
WORKDIR "/src"
RUN dotnet build "SharePointCoordinatesService.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "SharePointCoordinatesService.csproj" -c Release -o /app/publish /p:UseAppHost=false

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .

# Install timezone data and set to NZ timezone
RUN apt-get update && apt-get install -y tzdata && \
    ln -sf /usr/share/zoneinfo/Pacific/Auckland /etc/localtime && \
    echo "Pacific/Auckland" > /etc/timezone

ENTRYPOINT ["dotnet", "SharePointCoordinatesService.dll"]
```

**docker-compose.yml**:
```yaml
version: '3.8'

services:
  sharepoint-service:
    build: .
    container_name: sharepoint-coordinates-service
    ports:
      - "5088:80"   # 避免与现有服务端口冲突
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - POSTGRES_USER=coorduser
      - POSTGRES_PASSWORD=SecurePassword123!
      - SHAREPOINT_CLIENT_SECRET=****************************************
    depends_on:
      - postgres
      - redis
    networks:
      - sharepoint-network
    volumes:
      - ./logs:/app/logs
    restart: unless-stopped

  postgres:
    image: postgres:15
    container_name: sharepoint-postgres
    environment:
      - POSTGRES_DB=SharePointCoordinatesDB
      - POSTGRES_USER=coorduser
      - POSTGRES_PASSWORD=SecurePassword123!
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-scripts:/docker-entrypoint-initdb.d
    ports:
      - "5433:5432"
    networks:
      - sharepoint-network
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    container_name: sharepoint-redis
    ports:
      - "6380:6379"
    volumes:
      - redis_data:/data
    networks:
      - sharepoint-network
    restart: unless-stopped
    command: redis-server --appendonly yes

  # 可选：添加Redis管理界面
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: redis-web-ui
    environment:
      - REDIS_HOSTS=redis:redis:6379
    ports:
      - "8081:8081"
    depends_on:
      - redis
    networks:
      - sharepoint-network

volumes:
  postgres_data:
  redis_data:

networks:
  sharepoint-network:
    driver: bridge
```

### **高级功能设计**

**1. 智能缓存策略**:
```csharp
public class IntelligentCacheService
{
    // 三层缓存：内存 -> Redis -> 数据库
    // 自动失效检测
    // 文件变更监控
    // 预加载策略
}
```

**2. 数据同步调度**:
```csharp
public class BackgroundSyncService : BackgroundService
{
    // 定时检查SharePoint文件更新
    // 增量同步策略
    // 故障重试机制
    // 同步状态监控
}
```

**3. API安全机制**:
```csharp
[ApiKeyAuth]
[Route("api/coordinates")]
public class CoordinatesController : ControllerBase
{
    // API密钥认证
    // 请求频率限制
    // IP白名单控制
    // 审计日志记录
}
```

**4. 健康检查端点**:
```csharp
// /health - 服务状态
// /health/sharepoint - SharePoint连接状态
// /health/database - 数据库连接状态
// /health/cache - 缓存状态
```

### **部署实施步骤**

**第1步：开发环境设置**
```bash
# 1. 创建项目目录
mkdir SharePointCoordinatesService
cd SharePointCoordinatesService

# 2. 初始化.NET项目
dotnet new webapi -n SharePointCoordinatesService
cd SharePointCoordinatesService

# 3. 添加必要的NuGet包
dotnet add package Microsoft.EntityFrameworkCore
dotnet add package Npgsql.EntityFrameworkCore.PostgreSQL
dotnet add package Microsoft.Graph
# ... 其他包

# 4. 运行迁移
dotnet ef migrations add InitialCreate
dotnet ef database update
```

**第2步：本地测试部署**
```bash
# 1. 启动Docker服务
docker-compose up -d postgres redis

# 2. 运行应用
dotnet run

# 3. 测试API端点
curl http://localhost:5000/api/coordinates
curl http://localhost:5000/health
```

**第3步：生产环境部署**
```bash
# 1. 构建Docker镜像
docker build -t sharepoint-coordinates-service .

# 2. 生产环境部署
docker-compose -f docker-compose.prod.yml up -d

# 3. 监控服务状态
docker-compose logs -f sharepoint-service
```

### **监控与运维**

**1. 日志策略**:
- **结构化日志**: 使用Serilog记录详细操作日志
- **性能监控**: 记录API响应时间和SharePoint调用延迟
- **错误跟踪**: 详细的异常信息和堆栈跟踪
- **审计日志**: 记录所有API调用和数据变更

**2. 告警机制**:
- SharePoint连接失败告警
- 数据库连接异常告警
- 缓存失效告警
- API响应时间超时告警

**3. 备份策略**:
- 数据库定期备份 (每日)
- 配置文件版本控制
- Docker镜像版本管理

### **安全考虑**

**1. 敏感信息保护**:
- Client Secret通过环境变量传递
- 数据库密码加密存储
- API密钥轮换机制

**2. 网络安全**:
- HTTPS强制加密
- 防火墙规则配置
- VPN或内网访问限制

**3. 数据安全**:
- 坐标数据加密存储
- 审计日志完整性保护
- 定期安全扫描

### **性能优化**

**1. 数据库优化**:
- 坐标数据索引优化
- 查询性能监控
- 连接池配置

**2. 缓存优化**:
- Redis集群部署
- 缓存命中率监控
- 内存使用优化

**3. API优化**:
- 响应数据压缩
- 分页查询支持
- 并发请求限制

### **总投入估算**

**开发时间**: 5-7个工作日
- 核心服务开发: 3天
- Docker配置和测试: 2天
- 文档和部署: 1-2天

**服务器资源**:
- CPU: 2核心
- 内存: 4GB
- 存储: 50GB SSD
- 带宽: 标准企业带宽

**维护成本**: 每月1-2小时监控和维护

## 🎯 **最终方案总结**

### **关键设计原则**

✅ **功能策略**: 
- **完全保持现有文件导入功能不变**
- **新增SharePoint同步接口作为额外选择**
- 移动端两种导入方式并存，用户可以灵活选择

✅ **数据集成**: 
- 后端：复用现有PostgreSQL，新增独立Schema缓存SharePoint数据
- 移动端：继续使用现有asset_coordinates表，数据结构无需修改
- API格式：直接对齐移动端 (AMSKEY→asset_id, latitude, longitude)

✅ **部署优化**: 
- 移除Redis依赖，使用In-Memory缓存降低复杂度
- 仅容器化应用服务，不部署额外数据库实例
- 独立端口5088避免与现有服务冲突

✅ **移动端集成**: 
- 复用现有AssetCoordinates类和upsertBatch方法
- 复用现有Basic认证机制 (luke.shi:gentoo666)
- UI最小化改动：仅新增"Sync from SharePoint"按钮

这个方案的最大优势是**零破坏性**：所有现有功能保持不变，用户可以继续使用熟悉的文件导入方式，同时获得SharePoint自动同步的额外便利。技术架构完全对齐WaterMeterManagement项目，确保一致性和可维护性。
