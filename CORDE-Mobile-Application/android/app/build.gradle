apply plugin: "com.android.application"
apply plugin: "org.jetbrains.kotlin.android"
apply plugin: "com.facebook.react"

react {
    // Keep the configuration as is
}

def enableProguardInReleaseBuilds = false
def jscFlavor = 'org.webkit:android-jsc:+'

android {
    ndkVersion rootProject.ext.ndkVersion
    buildToolsVersion rootProject.ext.buildToolsVersion
    compileSdkVersion rootProject.ext.compileSdkVersion

    namespace "com.awesomeproject"
    defaultConfig {
        applicationId "com.awesomeproject"
        minSdkVersion rootProject.ext.minSdkVersion
        targetSdkVersion rootProject.ext.targetSdkVersion
        versionCode 30
        versionName "1.4.0"
        // Updated dimension strategy to be consistent
        missingDimensionStrategy 'react-native-camera', 'general'
    }
    packagingOptions {
        pickFirst '**/*.so'
    }
    signingConfigs {
        release {
            if (project.hasProperty('MYAPP_RELEASE_STORE_FILE')) {
                storeFile file(MYAPP_RELEASE_STORE_FILE)
                storePassword MYAPP_RELEASE_STORE_PASSWORD
                keyAlias MYAPP_RELEASE_KEY_ALIAS
                keyPassword MYAPP_RELEASE_KEY_PASSWORD
            }
        }
        debug {
            storeFile file('debug.keystore')
            storePassword 'android'
            keyAlias 'androiddebugkey'
            keyPassword 'android'
        }
    }
    buildTypes {
        debug {
            signingConfig signingConfigs.debug
        }
        release {
            signingConfig signingConfigs.release
            minifyEnabled enableProguardInReleaseBuilds
            proguardFiles getDefaultProguardFile("proguard-android.txt"), "proguard-rules.pro"
        }
    }

    // Custom APK output filename
    applicationVariants.all { variant ->
        variant.outputs.all {
            def versionName = variant.versionName
            def buildType = variant.buildType.name

            if (buildType == "release") {
                outputFileName = "corde-mobile-${versionName}.apk"
            } else {
                outputFileName = "corde-mobile-${versionName}-debug.apk"
            }
        }
    }
}

dependencies {
    implementation("com.facebook.react:react-android")
    implementation project(':react-native-sqlite-storage')
    implementation project(':react-native-camera')
    implementation project(':react-native-image-picker')

    if (hermesEnabled.toBoolean()) {
        implementation("com.facebook.react:hermes-android")
    } else {
        implementation jscFlavor
    }
}

apply from: file("../../node_modules/@react-native-community/cli-platform-android/native_modules.gradle"); applyNativeModulesAppBuildGradle(project)
