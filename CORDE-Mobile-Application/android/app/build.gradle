apply plugin: "com.android.application"
apply plugin: "org.jetbrains.kotlin.android"
apply plugin: "com.facebook.react"

react {
    // Keep the configuration as is
}

def enableProguardInReleaseBuilds = false
def jscFlavor = 'org.webkit:android-jsc:+'

android {
    ndkVersion rootProject.ext.ndkVersion
    buildToolsVersion rootProject.ext.buildToolsVersion
    compileSdkVersion rootProject.ext.compileSdkVersion

    namespace "nz.corde.mobile"
    defaultConfig {
        applicationId "nz.corde.mobile"
        minSdkVersion rootProject.ext.minSdkVersion
        targetSdkVersion rootProject.ext.targetSdkVersion
        versionCode 39
        versionName "1.7"
        // Updated dimension strategy to be consistent
        missingDimensionStrategy 'react-native-camera', 'general'
    }
    packagingOptions {
        pickFirst '**/*.so'
    }
    signingConfigs {
        release {
            if (project.hasProperty('MYAPP_RELEASE_STORE_FILE')) {
                storeFile file(MYAPP_RELEASE_STORE_FILE)
                storePassword MYAPP_RELEASE_STORE_PASSWORD
                keyAlias MYAPP_RELEASE_KEY_ALIAS
                keyPassword MYAPP_RELEASE_KEY_PASSWORD
            }
        }
        debug {
            storeFile file('debug.keystore')
            storePassword 'android'
            keyAlias 'androiddebugkey'
            keyPassword 'android'
        }
    }
    buildTypes {
        debug {
            signingConfig signingConfigs.debug
        }
        release {
            signingConfig signingConfigs.release
            minifyEnabled enableProguardInReleaseBuilds
            proguardFiles getDefaultProguardFile("proguard-android.txt"), "proguard-rules.pro"
        }
    }

    bundle {
        language {
            enableSplit = false
        }
        density {
            enableSplit = true
        }
        abi {
            enableSplit = true
        }
    }

    // Generate dynamic app name based on environment
    android.applicationVariants.all { variant ->
        variant.mergeResourcesProvider.get().doLast {
            // Check if this is a production build by reading the BaseApi.ts file
            def baseApiFile = file("../../src/api/BaseApi.ts")
            def isProductionBuild = false
            if (baseApiFile.exists()) {
                def content = baseApiFile.text
                isProductionBuild = content.contains("export const isProductionBuild = true")
            }

            // Generate strings.xml with appropriate app name
            def appName = isProductionBuild ? "CORDE Mobile" : "CORDE Mobile Test"
            def stringsXml = """<resources>
    <string name="app_name">${appName}</string>
</resources>"""

            def stringsFile = file("${buildDir}/intermediates/merged_res/${variant.name}/values/strings.xml")
            stringsFile.parentFile.mkdirs()
            stringsFile.text = stringsXml
        }
    }

    // Custom APK output filename based on environment
    applicationVariants.all { variant ->
        variant.outputs.all {
            def versionName = variant.versionName
            def buildType = variant.buildType.name

            // Check if this is a production build by reading the BaseApi.ts file
            def baseApiFile = file("../../src/api/BaseApi.ts")
            def isProductionBuild = false
            if (baseApiFile.exists()) {
                def content = baseApiFile.text
                // Look for "export const isProductionBuild = true"
                isProductionBuild = content.contains("export const isProductionBuild = true")
            }

            if (buildType == "release") {
                if (isProductionBuild) {
                    outputFileName = "corde-mobile-${versionName}.apk"
                } else {
                    outputFileName = "corde-mobile-test-${versionName}.apk"
                }
            } else {
                outputFileName = "corde-mobile-${versionName}-debug.apk"
            }
        }
    }

    // Custom AAB output filename based on environment
    android.applicationVariants.all { variant ->
        if (variant.buildType.name == "release") {
            def versionName = variant.versionName

            // Check if this is a production build by reading the BaseApi.ts file
            def baseApiFile = file("../../src/api/BaseApi.ts")
            def isProductionBuild = false
            if (baseApiFile.exists()) {
                def content = baseApiFile.text
                isProductionBuild = content.contains("export const isProductionBuild = true")
            }

            variant.outputs.all { output ->
                def bundleTask = tasks.findByName("bundle${variant.name.capitalize()}")
                if (bundleTask != null) {
                    bundleTask.doLast {
                        def bundleDir = file("${buildDir}/outputs/bundle/${variant.name}")
                        def originalFile = file("${bundleDir}/app-${variant.name}.aab")

                        if (originalFile.exists()) {
                            def newFileName = isProductionBuild ?
                                "corde-mobile-${versionName}.aab" :
                                "corde-mobile-test-${versionName}.aab"
                            def newFile = file("${bundleDir}/${newFileName}")

                            if (originalFile.renameTo(newFile)) {
                                println "AAB renamed to: ${newFileName}"
                            }
                        }
                    }
                }
            }
        }
    }
}

dependencies {
    implementation("com.facebook.react:react-android")
    implementation project(':react-native-sqlite-storage')
    implementation project(':react-native-camera')
    implementation project(':react-native-image-picker')

    if (hermesEnabled.toBoolean()) {
        implementation("com.facebook.react:hermes-android")
    } else {
        implementation jscFlavor
    }
}

apply from: file("../../node_modules/@react-native-community/cli-platform-android/native_modules.gradle"); applyNativeModulesAppBuildGradle(project)
