# macOS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db

# Windows
Thumbs.db
Desktop.ini
$RECYCLE.BIN/

# Xcode
#
build/
*.pbxuser
!default.pbxuser
*.mode1v3
!default.mode1v3
*.mode2v3
!default.mode2v3
*.perspectivev3
!default.perspectivev3
xcuserdata
*.xccheckout
*.moved-aside
DerivedData
*.hmap
*.ipa
*.xcuserstate
**/.xcode.env.local

# Android/IntelliJ
#
build/
.idea
.gradle
android/local.properties
*.iml
*.hprof
.cxx/
*.keystore
!debug.keystore

# node.js
#
node_modules/
npm-debug.log
yarn-error.log

# fastlane
#
# It is recommended to not store the screenshots in the git repo. Instead, use fastlane to re-generate the
# screenshots whenever they are needed.
# For more information about the recommended setup visit:
# https://docs.fastlane.tools/best-practices/source-control/

**/fastlane/report.xml
**/fastlane/Preview.html
**/fastlane/screenshots
**/fastlane/test_output

# Bundle artifact
*.jsbundle

# Ruby / CocoaPods
**/Pods/
/vendor/bundle/

# Temporary files created by Metro to check the health of the file watcher
.metro-health-check*

# testing
/coverage

# Yarn
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/sdks
!.yarn/versions
/.vscode
/android/app/src/main/res/drawable-hdpi/node_modules_reactnativecalendars_src_calendar_img_next.png
/android/app/src/main/res/drawable-hdpi/node_modules_reactnativecalendars_src_calendar_img_previous.png
/android/app/src/main/res/drawable-hdpi/node_modules_reactnativecalendars_src_img_down.png
/android/app/src/main/res/drawable-hdpi/node_modules_reactnativecalendars_src_img_up.png
/android/app/src/main/res/drawable-hdpi/node_modules_reactnavigation_elements_src_assets_backicon.png
/android/app/src/main/res/drawable-hdpi/node_modules_reactnavigation_nativestack_node_modules_reactnavigation_elements_src_assets_backicon.png
/android/app/src/main/res/drawable-mdpi/node_modules_reactnativecalendars_src_img_down.png
/android/app/src/main/res/drawable-mdpi/node_modules_reactnativecalendars_src_img_up.png
/android/app/src/main/res/drawable-mdpi/node_modules_reactnavigation_elements_src_assets_backicon.png
/android/app/src/main/res/drawable-mdpi/node_modules_reactnavigation_elements_src_assets_backiconmask.png
/android/app/src/main/res/drawable-mdpi/node_modules_reactnavigation_nativestack_node_modules_reactnavigation_elements_src_assets_backicon.png
/android/app/src/main/res/drawable-mdpi/node_modules_reactnavigation_nativestack_node_modules_reactnavigation_elements_src_assets_backiconmask.png
/android/app/src/main/res/drawable-mdpi/src_assets_backgrounds_corde_background_v21.png
/android/app/src/main/res/drawable-mdpi/src_assets_backgrounds_corde_background_v22.png
/android/app/src/main/res/drawable-mdpi/src_assets_logotype_corde_logotype_black.png
/android/app/src/main/res/drawable-mdpi/src_assets_logotype_corde_logotype_white.png
/android/app/src/main/res/drawable-mdpi/src_assets_mark_corde_mark_black.png
/android/app/src/main/res/drawable-mdpi/src_assets_mark_corde_mark_white.png
/android/app/src/main/res/drawable-xhdpi/node_modules_reactnativecalendars_src_calendar_img_next.png
/android/app/src/main/res/drawable-xhdpi/node_modules_reactnativecalendars_src_calendar_img_previous.png
/android/app/src/main/res/drawable-xhdpi/node_modules_reactnativecalendars_src_img_down.png
/android/app/src/main/res/drawable-xhdpi/node_modules_reactnativecalendars_src_img_up.png
/android/app/src/main/res/drawable-xhdpi/node_modules_reactnavigation_elements_src_assets_backicon.png
/android/app/src/main/res/drawable-xhdpi/node_modules_reactnavigation_nativestack_node_modules_reactnavigation_elements_src_assets_backicon.png
/android/app/src/main/res/drawable-xxhdpi/node_modules_reactnativecalendars_src_calendar_img_next.png
/android/app/src/main/res/drawable-xxhdpi/node_modules_reactnativecalendars_src_calendar_img_previous.png
/android/app/src/main/res/drawable-xxhdpi/node_modules_reactnativecalendars_src_img_down.png
/android/app/src/main/res/drawable-xxhdpi/node_modules_reactnativecalendars_src_img_up.png
/android/app/src/main/res/drawable-xxhdpi/node_modules_reactnavigation_elements_src_assets_backicon.png
/android/app/src/main/res/drawable-xxhdpi/node_modules_reactnavigation_nativestack_node_modules_reactnavigation_elements_src_assets_backicon.png
/android/app/src/main/res/drawable-xxxhdpi/node_modules_reactnativecalendars_src_calendar_img_next.png
/android/app/src/main/res/drawable-xxxhdpi/node_modules_reactnativecalendars_src_calendar_img_previous.png
/android/app/src/main/res/drawable-xxxhdpi/node_modules_reactnativecalendars_src_img_down.png
/android/app/src/main/res/drawable-xxxhdpi/node_modules_reactnativecalendars_src_img_up.png
/android/app/src/main/res/drawable-xxxhdpi/node_modules_reactnavigation_elements_src_assets_backicon.png
/android/app/src/main/res/drawable-xxxhdpi/node_modules_reactnavigation_nativestack_node_modules_reactnavigation_elements_src_assets_backicon.png

/android/gradle.properties
/android/app/my-release-key.keystore

/android/app/src/main/assets/corde_mobile.db-shm
/android/app/src/main/assets/corde_mobile.db-wal
android/corde-mobile-1.7.apks
android/bundletool.jar
