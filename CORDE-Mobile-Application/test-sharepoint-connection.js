#!/usr/bin/env node

/**
 * SharePoint API Connection Test Script
 * This script tests the connection to the SharePoint Coordinates Service
 */

const https = require('https');
const http = require('http');

const TEST_CONFIG = {
  // Test URLs
  urls: [
    'http://**************/sharepoint-api/coordinates/paged',
    'http://**************:5088/api/coordinates/paged',
    'http://**************/sharepoint-health',
    'http://**************:5088/health'
  ],
  
  // Authentication
  auth: {
    username: 'luke.shi',
    password: 'gentoo666'
  },
  
  // Request options
  timeout: 30000
};

function makeRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const isHttps = url.startsWith('https://');
    const client = isHttps ? https : http;
    
    const authString = Buffer.from(`${TEST_CONFIG.auth.username}:${TEST_CONFIG.auth.password}`).toString('base64');
    
    const requestOptions = {
      method: 'GET',
      headers: {
        'Authorization': `Basic ${authString}`,
        'Content-Type': 'application/json',
        'User-Agent': 'CORDE-Test-Script/1.0.0',
        'Accept': 'application/json',
        ...options.headers
      },
      timeout: TEST_CONFIG.timeout
    };

    const req = client.request(url, requestOptions, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        resolve({
          url,
          status: res.statusCode,
          headers: res.headers,
          data: data.substring(0, 500) // Limit data for display
        });
      });
    });

    req.on('error', (error) => {
      reject({
        url,
        error: error.message,
        code: error.code
      });
    });

    req.on('timeout', () => {
      req.destroy();
      reject({
        url,
        error: 'Request timeout',
        code: 'TIMEOUT'
      });
    });

    req.end();
  });
}

async function testConnections() {
  console.log('🧪 SharePoint API Connection Test');
  console.log('=' .repeat(50));
  console.log(`📅 Test Time: ${new Date().toISOString()}`);
  console.log(`🔐 Auth User: ${TEST_CONFIG.auth.username}`);
  console.log('');

  for (const url of TEST_CONFIG.urls) {
    console.log(`🌐 Testing: ${url}`);
    
    try {
      const result = await makeRequest(url);
      console.log(`✅ Status: ${result.status}`);
      console.log(`📊 Response: ${result.data.substring(0, 100)}...`);
      
      if (result.headers['content-type']) {
        console.log(`📋 Content-Type: ${result.headers['content-type']}`);
      }
      
    } catch (error) {
      console.log(`❌ Error: ${error.error}`);
      console.log(`🔍 Code: ${error.code || 'UNKNOWN'}`);
    }
    
    console.log('-'.repeat(30));
  }

  console.log('');
  console.log('🏁 Test completed');
}

// Run the test
testConnections().catch(console.error);
