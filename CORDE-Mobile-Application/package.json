{"name": "AwesomeProject", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "test": "jest"}, "dependencies": {"@react-native-async-storage/async-storage": "^2.0.0", "@react-native-camera-roll/camera-roll": "^7.8.3", "@react-native-clipboard/clipboard": "^1.14.1", "@react-native-community/datetimepicker": "^8.1.1", "@react-native-community/geolocation": "^3.3.0", "@react-native-community/netinfo": "^11.3.2", "@react-navigation/bottom-tabs": "^6.6.1", "@react-navigation/material-top-tabs": "^6.6.14", "@react-navigation/native": "^6.1.17", "@react-navigation/native-stack": "^6.9.26", "@types/react-native": "^0.73.0", "axios": "^1.7.2", "date-fns": "^3.6.0", "date-fns-tz": "^3.1.3", "lodash": "^4.17.21", "native-base": "^3.4.28", "react": "18.2.0", "react-dom": "^18.3.1", "react-native": "0.74.2", "react-native-background-timer": "^2.4.1", "react-native-base64": "^0.2.1", "react-native-calendars": "^1.1306.0", "react-native-camera": "latest", "react-native-datepicker": "^1.7.2", "react-native-document-picker": "^9.3.0", "react-native-draggable": "^3.3.0", "react-native-fs": "^2.20.0", "react-native-geolocation-service": "^5.3.1", "react-native-gesture-handler": "^2.24.0", "react-native-image-picker": "^8.2.1", "react-native-keychain": "^8.2.0", "react-native-linear-gradient": "^2.8.3", "react-native-maps": "^1.18.0", "react-native-pager-view": "^6.3.3", "react-native-permissions": "^4.1.5", "react-native-safe-area-context": "^4.10.5", "react-native-screens": "latest", "react-native-sqlite-storage": "^6.0.1", "react-native-svg": "12.1.1", "react-native-tab-view": "^3.5.2", "react-native-vector-icons": "^10.1.0", "react-native-vision-camera": "^4.5.2", "react-native-webview": "^13.13.5", "xlsx": "^0.18.5"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.24.7", "@babel/runtime": "^7.20.0", "@react-native/babel-preset": "0.74.84", "@react-native/eslint-config": "0.74.84", "@react-native/metro-config": "0.74.84", "@react-native/typescript-config": "0.74.84", "@types/react": "^18.2.6", "@types/react-native-maps": "^0.24.2", "@types/react-test-renderer": "^18.0.0", "babel-jest": "^29.6.3", "eslint": "^8.19.0", "jest": "^29.6.3", "prettier": "2.8.8", "react-test-renderer": "18.2.0", "typescript": "^5.0.4"}, "engines": {"node": ">=18"}, "packageManager": "yarn@3.6.4"}