# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

CORDE Mobile Application is a React Native offline-capable field data collection app designed for water meter reading and asset management. The application enables field workers to collect data offline and sync with a central server when connectivity is restored.

### Key Features
- **Offline-first architecture**: Full functionality without network connectivity using SQLite
- **Data synchronization**: Background sync with remote API when network available
- **GPS integration**: Location tracking and coordinate management
- **Map integration**: Google Maps with asset visualization and coordinate optimization
- **File management**: Photo and document upload capabilities
- **User authentication**: Basic auth with auto-login functionality

## Development Commands

### Core Commands
```bash
# Development
yarn install           # Install dependencies
yarn start            # Start Metro bundler
yarn android          # Run on Android device/emulator
yarn ios              # Run on iOS device/simulator (macOS only)

# Testing & Quality
yarn test             # Run Jest tests
yarn lint             # Run ESLint

# Build (check package.json for additional build scripts)
# Android builds require Android Studio and SDK setup
# iOS builds require Xcode (macOS only)
```

### Database Operations
The app uses a pre-seeded SQLite database (`android/app/src/main/assets/corde_mobile.db`) that gets copied to device storage on first launch.

### Important Environment Setup
- **Android**: Requires Android SDK 23+ (API level 23), target SDK 34
- **iOS**: Requires iOS deployment target as specified in Podfile
- **Node**: Requires Node.js 18+ (specified in package.json engines)
- **Package Manager**: Uses Yarn 3.6.4

## Code Architecture

### Core Architecture Patterns

#### 1. Service Layer Pattern
- **Services** (`src/services/`): Business logic and external integrations
  - `SyncService`: Orchestrates all data synchronization
  - `AuthService`: Handles authentication and user management
  - `LocationService`: GPS and location management with caching
  - `LogListService`: Core business logic for work logs
  - `NetworkService`: Network status and connectivity management

#### 2. Database Layer
- **SQLite with Migration System**: Versioned database schema in `DatabaseInit.tsx`
- **Repository Pattern**: Each database table has corresponding class in `src/database/`
- **Migration Strategy**: Version-controlled schema updates with rollback capability
- **Offline-first Design**: All data operations work offline, sync when connected

#### 3. Context-based State Management
- **AuthContext** (`src/context/AuthContext.tsx`): Global authentication state
- **SyncContext** (`src/context/SyncContext.tsx`): Synchronization status and progress
- **ThemeContext** (`src/styles/ThemeContext.tsx`): App theming and dark/light mode

#### 4. Navigation Architecture
- **Stack Navigator**: Primary navigation using React Navigation 6
- **Tab Navigators**: Nested tab navigation for complex workflows
- **Conditional Routing**: Different navigation trees for authenticated vs non-authenticated users

### Key Technical Patterns

#### Data Synchronization Flow
```
Local SQLite ← → SyncService ← → Remote API
     ↓               ↓
Background Tasks → Conflict Resolution → User Notifications
```

#### Map Performance Optimization
- **Global Address Cache**: Persistent geocoding cache to reduce Google Maps API calls
- **Asset Coordinates**: Local coordinate database to avoid API dependency
- **Marker Grouping**: Location-based clustering for performance
- **Debounced Updates**: Throttled map region changes

#### Database Schema Design
- **Audit Fields**: All entities have created/updated timestamps where applicable
- **Sync Tracking**: Mobile-specific sync metadata for conflict resolution
- **Foreign Key Relationships**: Proper relational design with cascade handling
- **Migration-Safe**: Schema changes through versioned migration system

## Key Components and Integration Points

### Core Screens
- **LoginScreen**: Authentication with auto-login capability
- **MainDashboardScreen**: Central hub with quick actions and overview
- **LogListScreen**: Work log management with filtering and sorting
- **MapMarkerScreen**: Geographic visualization with performance optimization
- **SyncLogScreen**: Data synchronization management and monitoring
- **NewLogScreen**: Create new work logs with file attachments

### Critical Services Integration

#### Location Services
- **GPS Integration**: Uses `@react-native-community/geolocation`
- **Permission Handling**: Automatic permission requests with fallbacks
- **Caching Strategy**: 30-second cache for location data to improve performance
- **Accuracy Management**: Prefers high accuracy, falls back to standard

#### Google Maps Integration
- **API Configuration**: API key in `BaseApi.ts`
- **Geocoding Optimization**: Persistent cache to minimize API calls
- **Asset Coordinate Fallback**: Local coordinates preferred over API calls
- **Map Performance**: Marker clustering and debounced region updates

#### File Management
- **Local Storage**: Using `react-native-fs` for file operations
- **Upload Queue**: Background file sync with retry mechanism
- **Photo Capture**: Camera integration with `react-native-image-picker`
- **Document Picker**: Support for various file types

### Database Design Highlights

#### Core Tables
- **log_list**: Primary work log data with GPS coordinates
- **assets**: Asset information with hierarchical relationships
- **asset_coordinates**: Optimized coordinate storage for map performance
- **users/staff**: Authentication and user management
- **mobile_sync_logs**: Sync tracking and conflict resolution
- **stored_files**: File metadata and upload tracking

#### Performance Optimizations
- **WAL Mode**: Write-Ahead Logging for better concurrent access
- **Foreign Keys**: Enabled for data integrity
- **Pragmas**: Optimized cache size and synchronous mode
- **Indexes**: Strategic indexing for common query patterns

## Development Guidelines

### Code Organization
- **Component Structure**: Screen components in `/screens/`, reusable in `/components/`
- **Type Safety**: TypeScript strict mode enabled
- **Service Interfaces**: Interface-based dependency injection pattern
- **Database Models**: Dedicated model classes for each table

### Sync Strategy
- **Offline First**: All operations work offline, sync opportunistically
- **Conflict Resolution**: Last-write-wins with user notification
- **Background Sync**: Automatic sync on network connectivity
- **Manual Sync**: User-initiated sync with progress feedback

### Performance Considerations
- **Memory Management**: Careful handling of large datasets and images
- **Battery Optimization**: Efficient GPS usage and background tasks
- **Network Efficiency**: Batched API calls and intelligent caching
- **UI Responsiveness**: Asynchronous operations with loading states

### Security Practices
- **Credential Storage**: Secure storage using `react-native-keychain`
- **API Authentication**: Basic auth with encoded credentials
- **Local Data**: SQLite database encryption considerations
- **File Security**: Secure file storage in app-specific directories

## Important Technical Notes

### Known Issues and Solutions
- **Icon Validation**: NativeBase icon name validation requires specific icon mappings
- **Database Concurrency**: WAL mode enabled for multi-thread access
- **Memory Leaks**: Careful cleanup of listeners and subscriptions
- **Android Permissions**: Runtime permission handling for location and camera

### Asset Coordinate Optimization
The app implements a sophisticated coordinate caching system:
1. **Local Database**: `asset_coordinates` table for frequently accessed locations
2. **Import Feature**: Excel-based coordinate bulk import functionality
3. **API Fallback**: Google Maps Geocoding as backup when local data unavailable
4. **Performance Impact**: Reduces API calls by up to 100% for cached locations

### Deployment Considerations
- **Database Migration**: Automatic schema updates on app version changes
- **Asset Bundling**: Pre-seeded database included in app bundle
- **Build Configuration**: Environment-specific configurations for different deployments
- **Testing Strategy**: Both unit tests and integration testing with SQLite

## File Structure Highlights

### Critical Configuration Files
- `android/app/src/main/assets/corde_mobile.db`: Pre-seeded database
- `src/database/DatabaseInit.tsx`: Schema and migration management
- `src/api/BaseApi.ts`: API configuration and Google Maps integration
- `src/navigation/AppNavigator.tsx`: Navigation structure and auth flow

### Service Layer
- `src/services/SyncService.ts`: Central synchronization orchestration
- `src/services/AuthService.ts`: Authentication and user management
- `src/services/LocationService.ts`: GPS and location handling
- `src/services/NetworkService.ts`: Connectivity monitoring

This architecture ensures robust offline functionality while maintaining data consistency and providing excellent user experience in field conditions.