import React, {useState, useEffect} from 'react';
import {
  VStack,
  ScrollView,
  Avatar,
  Text,
  Button,
  useToast,
  Box,
} from 'native-base';
import {useNavigation} from '@react-navigation/native';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import ThemeToggle from '../LoginScreen/ThemeToggle';
import {useTheme} from '../../styles/ThemeContext';
import {RootStackParamList} from '../../navigation/AppNavigator';
import {AuthService} from '../../services/AuthService';
import {useAuth} from '../../context/AuthContext';
import ToastAlert from "../NewLogScreen/ToastAlert.tsx";



type SettingsScreenNavigationProp = NativeStackNavigationProp<
  RootStackParamList,
  'Settings'
>;

const Settings: React.FC = () => {
  const navigation = useNavigation<SettingsScreenNavigationProp>();
  const {mode} = useTheme();
  const {user, setUser} = useAuth();
  const toast = useToast();

  const [username, setUsername] = useState('');
  const [avatarUrl, setAvatarUrl] = useState('');

  const defaultAvatarPath = require('../../assets/Mark/CORDE_Mark_MarginHalf_White.jpg');

  useEffect(() => {
    if (user) {
      setUsername(user.full_name);
    }
  }, [user]);

  const navigateToAccountManagement = () => {
    navigation.navigate('UserProfile');
  };

  const navigateToDebugScreen = () => {
    navigation.navigate('Debug');
  };

  const handleLogout = async () => {
    try {
      if (user && user.username) {
        await AuthService.logout(user.username);
        setUser(null);
        navigation.navigate('Login');
        toast.show({
          render: ({id}) => (
            <ToastAlert
              id={id}
              title="Logout Successful"
              description="You have been logged out."
              isClosable={true}
              onClose={() => toast.close(id)}
              variant="left-accent"
              status="success"
            />
          ),
          placement: 'top',
        });
      } else {
        throw new Error('Missing user information');
      }
    } catch (error) {
      console.error('logout failure:', error);
      toast.show({
        render: ({id}) => (
          <ToastAlert
            id={id}
            title="Logout Failure"
            description="An error occurred during logout."
            isClosable={true}
            onClose={() => toast.close(id)}
            variant="left-accent"
            status="error"
          />
        ),
        placement: 'top',
      });
    }
  };

  const textColor = mode === 'dark' ? 'white' : 'black';
  const bgColor = mode === 'dark' ? 'gray.800' : 'gray.100';
  const pressedColor = mode === 'dark' ? 'gray.700' : 'gray.200';

  return (
    <ScrollView>
      <VStack space={4} alignItems="center" p={4}>
        <Avatar
          size="2xl"
          source={avatarUrl ? {uri: avatarUrl} : defaultAvatarPath}
        />
        <Text fontSize="2xl" fontWeight="bold" color={textColor}>
          {username}
        </Text>
        <Box width="100%">
          <ThemeToggle />
        </Box>
        <VStack space={4} width="100%">
          <Button
            leftIcon={
              <MaterialCommunityIcons
                name="account-cog"
                size={30}
                color={textColor}
              />
            }
            onPress={navigateToAccountManagement}
            bg={bgColor}
            _text={{fontSize: 'xl', color: textColor}}
            _pressed={{bg: pressedColor}}
            height={16}
            justifyContent="flex-start"
            px={4}>
            Account Management
          </Button>
          <Button
            leftIcon={
              <MaterialCommunityIcons name="bug" size={30} color={textColor} />
            }
            onPress={navigateToDebugScreen}
            bg={bgColor}
            _text={{fontSize: 'xl', color: textColor}}
            _pressed={{bg: pressedColor}}
            height={16}
            justifyContent="flex-start"
            px={4}>
            Debug Interface
          </Button>

        </VStack>
        <Button
          onPress={handleLogout}
          colorScheme="orange"
          width="100%"
          height={16}
          _text={{fontSize: 'xl'}}>
          Logout
        </Button>
      </VStack>
    </ScrollView>
  );
};

export default Settings;
