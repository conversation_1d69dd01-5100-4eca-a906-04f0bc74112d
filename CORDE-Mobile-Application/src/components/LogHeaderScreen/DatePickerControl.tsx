import React from 'react';
import {Button, FormControl, Text} from 'native-base';
import DateTimePicker from '@react-native-community/datetimepicker';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import {formatDate, formatTime} from '../../utils/dateUtils';
import { orangeGhostButtonStyle } from '../../styles/buttonStyles';
import {useTheme} from '../../styles/ThemeContext';

interface DatePickerControlProps {
  label: string;
  date: Date | undefined;
  showPicker: boolean;
  setShowPicker: (show: boolean) => void;
  onChange: (event: any, selectedDate?: Date) => void;
  iconName: string;
  textStyle: any;
  mode: 'light' | 'dark';
}

export const DatePickerControl: React.FC<DatePickerControlProps> = ({
  label,
  date,
  showPicker,
  setShowPicker,
  onChange,
  iconName,
  textStyle,
  mode,
}) => {
  const dateTimePickerStyle = orangeGhostButtonStyle(mode);

  const handlePress = () => setShowPicker(true);
  const displayDate = date
    ? (label.includes('Date') ? formatDate(date) : formatTime(date))
    : '';

  const labelColor = mode === 'dark' ? 'white' : 'black';

  return (
    <FormControl isRequired={true}>
      <FormControl.Label>
        <Text fontSize="sm" fontWeight="bold" mb={1} color={labelColor}>{label}</Text>
      </FormControl.Label>
      <Button
        {...dateTimePickerStyle}
        onPress={handlePress}
        leftIcon={
          <MaterialCommunityIcons name={iconName} size={20} />
        }
        height={10}
        _text={{
          ...textStyle,
          fontSize: "sm",
          color: mode === 'dark' ? 'white' : 'black',
        }}
        justifyContent="flex-start"
      >
        {displayDate}
      </Button>
      {showPicker && (
        <DateTimePicker
          value={date || new Date()}
          mode={label.includes('Date') ? 'date' : 'time'}
          display="default"
          onChange={onChange}
          textColor={mode === 'dark' ? 'white' : 'black'}
          style={{
            backgroundColor: mode === 'dark' ? '#333' : '#fff',
            width: '100%',
            height: 100,
          }}
        />
      )}
    </FormControl>
  );
};

export default DatePickerControl;
