import React from 'react';
import {Button, IButtonProps} from 'native-base';
import LinearGradient from 'react-native-linear-gradient';
import {StyleSheet} from 'react-native';

const GradientButton = ({
  children,
  gradientColors,
  style,
  ...rest
}: IButtonProps & {gradientColors: string[]}) => {
  return (
    <LinearGradient
      colors={gradientColors}
      start={{x: 0, y: 0.5}}
      end={{x: 1, y: 0.5}}
      style={[styles.gradient, style]}>
      <Button {...rest} style={{backgroundColor: 'transparent'}}>
        {children}
      </Button>
    </LinearGradient>
  );
};

const styles = StyleSheet.create({
  gradient: {
    borderRadius: 4,
  },
});

export default GradientButton;
