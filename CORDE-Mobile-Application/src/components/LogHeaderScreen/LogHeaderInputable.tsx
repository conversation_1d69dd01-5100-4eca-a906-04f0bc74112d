import React, {useCallback, useEffect, useRef, useState} from 'react';
import {
  Button,
  FormControl,
  IconButton,
  ScrollView,
  TextArea,
  HStack,
  Accordion,
  Text,
  Box,
  Icon,
  useToast,
  VStack,
} from 'native-base';
import {
  graySubtleButtonStyle,
  orangeSubtleButtonStyle,
  blueSubtleButtonStyle,
  orangeGhostButtonStyle,
} from '../../styles/buttonStyles.ts';
import {useDateTimePicker} from '../../hooks/useDateTimePicker';
import {DatePickerControl} from './DatePickerControl.tsx';
import DocumentPicker from 'react-native-document-picker';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import {useTheme} from '../../styles/ThemeContext';
import {AssetSelect} from '../NewLogScreen/AssetSelect';
import {Assets} from '../../database/Assets';
import Geolocation from '@react-native-community/geolocation';
import DynamicFormField from './DynamicFormField.tsx';
import {AssetClassActivity} from '../../database/AssetClassActivity.ts';
import {AssetAttributeValue} from '../../database/AssetAttributeValue.ts';
import FileUploadComponent from '../NewLogScreen/FileUploadComponent.tsx';
import {LogListService} from '../../services/LogListService.ts';
import ToastAlert from '../NewLogScreen/ToastAlert';
import {LogExtensions} from '../../database/LogExtensions.ts';
import {useNavigation} from '@react-navigation/native';
import {locationService} from "../../services/LocationService.ts";

const LogHeaderInputable: React.FC<{
  item: any;
  setItem?: (item: any) => void;
}> = ({item, setItem}) => {
  const {mode} = useTheme();
  const navigation = useNavigation();
  const submitButtonStyle = orangeSubtleButtonStyle(mode);
  const cancelButtonStyle = graySubtleButtonStyle(mode);
  const dateTimePickerStyle = orangeGhostButtonStyle(mode);

  const [showOnSiteDatePicker, setShowOnSiteDatePicker] = useState(false);
  const [showOnSiteTimePicker, setShowOnSiteTimePicker] = useState(false);
  const [showCompleteDatePicker, setShowCompleteDatePicker] = useState(false);
  const [showCompleteTimePicker, setShowCompleteTimePicker] = useState(false);
  const [onSiteGPS, setOnSiteGPS] = useState('');
  const [completeGPS, setCompleteGPS] = useState('');

  const [selectedFiles, setSelectedFiles] = useState([]);
  const [uploadedFiles, setUploadedFiles] = useState([]);
  const [selectedAsset, setSelectedAsset] = useState<Assets | null>(null);
  const [formValues, setFormValues] = useState({});
  const [assetClassActivities, setAssetClassActivities] = useState<
  AssetClassActivity[]
  >([]);
  const [localItem, setLocalItem] = useState(item);
  const [isGettingLocation, setIsGettingLocation] = useState(false);
  const [attachments, setAttachments] = useState<any[]>([]);
  const toast = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const localItemRef = useRef(item);

  const {showPicker: showDatePicker, setShowPicker: setShowDatePicker} =
    useDateTimePicker(new Date());
  const {
    showPicker: showStartTimePicker,
    setShowPicker: setShowStartTimePicker,
  } = useDateTimePicker(new Date());

  const handleFieldChange = (columnName: string, value: any) => {
    setFormValues(prev => {
      return {...prev, [columnName]: value};
    });
  };

  useEffect(() => {
    localItemRef.current = item;
  }, [item]);

  useEffect(() => {

    if (item) {
      setLocalItem(item);
      setOnSiteGPS(
        `Coord: ${item.arrive_latitude?.toFixed(
          4,
        )}, ${item.arrive_longitude?.toFixed(4)}`,
      );
      setCompleteGPS(
        `Coord: ${item.completion_latitude?.toFixed(
          4,
        )}, ${item.completion_longitude?.toFixed(4)}`,
      );

      // Initialise formValues to use all the fields in the item.
      const initialFormValues = {
        ...item,
        arrive_date: item.arrive_date || null,
        arrive_time: item.arrive_time || null,
        completed_date: item.completed_date || null,
        completed_time: item.completed_time || null,
        completion_details: item.completion_details || '',
      };

      // Load data from LogExtensions
      const loadExtensionData = async () => {
        if (item.local_log_id) {
          try {
            const extensions = await LogExtensions.getByLocalLogId(
              item.local_log_id,
            );
            const extensionValues = {};
            extensions.forEach(ext => {
              extensionValues[ext.name] = ext.value_str;
            });

            // Update the extended fields in formValues.
            if (item.extend_columns && Array.isArray(item.extend_columns)) {
              item.extend_columns.forEach(column => {
                initialFormValues[column.column_name] =
                  extensionValues[column.column_name] || '';
              });
            }

            setFormValues(initialFormValues);
          } catch (error) {
            console.error('Error loading extension data:', error);
          }
        }
      };

      loadExtensionData();
    }
  }, [item]);

  const updateDynamicFieldsRef = useRef(null);

  const updateDynamicFields = useCallback(
    (asset: Assets, activities: AssetClassActivity[]) => {

      // console.log('Updating dynamic fields with activities:', activities);
      // console.log('Current extend_columns:', localItem.extend_columns);

      const currentLocalItem = localItemRef.current;

      // console.log('updateDynamicFields called with:', { asset, activities });
      if (!currentLocalItem || !currentLocalItem.extend_columns) {
        console.warn('localItem or localItem.extend_columns is undefined');
        return;
      }

      const updatedExtendColumns = currentLocalItem.extend_columns.map(column => {
        if (column.column_name === 'AssetClassActivity') {
          // console.log('Rendering DynamicFormField:', column);
          const newComboList = activities.map(activity => ({
            label: activity.activity,
            value: activity.activity,
          }));
          if (
            JSON.stringify(column.combo_list) !== JSON.stringify(newComboList)
          ) {
            return {
              ...column,
              combo_list: newComboList,
            };
          }
        }
        return column;
      });

      if (
        JSON.stringify(currentLocalItem.extend_columns) !==
        JSON.stringify(updatedExtendColumns)
      ) {
        setLocalItem(prevItem => ({
          ...prevItem,
          extend_columns: updatedExtendColumns,
        }));
      }
      setAssetClassActivities(activities);
    },
    [],
  );

  updateDynamicFieldsRef.current = updateDynamicFields;

  const handleAssetChange = useCallback(async (asset: Assets) => {
    // console.log('Asset changed:', asset);
    // console.log('Loaded activities:', activities);
    // console.log('handleAssetChange called with asset:', asset);
    setSelectedAsset(asset);

    handleFieldChange('asset_id', asset.asset_id);
    handleFieldChange('asset_code', asset.asset_code);
    handleFieldChange('asset_location', asset.asset_location);

    const attributes = await AssetAttributeValue.getByAssetId(asset.asset_id);
    // console.log('Loaded attributes:', attributes);

    const class1 = attributes.find(
      attr => attr.data_label === 'Class1',
    )?.text_data;
    const class2 = attributes.find(
      attr => attr.data_label === 'Class2',
    )?.text_data;
    const class3 = attributes.find(
      attr => attr.data_label === 'Class3',
    )?.text_data;
    // console.log('Extracted classes:', { class1, class2, class3 });

    const activities = await AssetClassActivity.getActivities(
      class1,
      class2,
      class3,
    );
    // console.log('Loaded activities:', activities);

    if (updateDynamicFieldsRef.current) {
      updateDynamicFieldsRef.current(asset, activities);
    }
  }, []);

  useEffect(() => {
    if (item && item.asset_id) {
      Assets.getById(item.asset_id).then(asset => {
        if (asset) {
          handleAssetChange(asset);
        }
      });
    }
  }, [item, handleAssetChange]);

  const handleAssetSelect = useCallback(
    (asset: Assets) => {
      handleAssetChange(asset);
    },
    [handleAssetChange],
  );

  const getCurrentLocation = async (
    callback: (lat: number, long: number) => void,
  ) => {
    if (isGettingLocation) return;

    setIsGettingLocation(true);
    try {
      const location = await locationService.getCurrentLocation({
        enableHighAccuracy: true,
        timeout: 15000,
        maximumAge: 10000,
      });
      callback(location.latitude, location.longitude);
    } catch (error) {
      console.error('Error getting location:', error);
      toast.show({
        render: ({id}) => (
          <ToastAlert
            id={id}
            title="Location Error"
            description="Unable to get current location. Please try again."
            isClosable={true}
            onClose={() => toast.close(id)}
            variant="left-accent"
            status="error"
          />
        ),
        placement: 'top',
      });
    } finally {
      setIsGettingLocation(false);
    }
  };

  const updateOnSiteData = async () => {
    const now = new Date();
    handleFieldChange('arrive_date', now.toISOString());
    handleFieldChange('arrive_time', now.toISOString());
    await getCurrentLocation((lat, long) => {
      handleFieldChange('arrive_latitude', lat);
      handleFieldChange('arrive_longitude', long);
    });
  };

  const updateCompleteData = async () => {
    const now = new Date();
    handleFieldChange('completed_date', now.toISOString());
    handleFieldChange('completed_time', now.toISOString());
    await getCurrentLocation((lat, long) => {
      handleFieldChange('completion_latitude', lat);
      handleFieldChange('completion_longitude', long);
    });
  };

  const handleFilesChanged = (files: any[]) => {
    setAttachments(files);
  };

  const handleSaveDraft = useCallback(async () => {
    console.log('Saving draft with the following data:');
    console.log('formValues:', formValues);
    console.log('localItem:', localItem);
    console.log('attachments:', attachments);
    try {
      // Extract dynamic column values from formValues
      const extendColumns = localItem.extend_columns.map(column => ({
        column_name: column.column_name,
        value: formValues[column.column_name] || '',
      }));

      const updatedItem = {
        ...localItem,
        ...formValues,
      };

      // Pass extendColumns to the saveDraft method.
      console.log('Saving draft with:', {
        updatedItem,
        attachments,
        extendColumns,
      });
      await LogListService.saveDraft(updatedItem, attachments, extendColumns);
      toast.show({
        render: ({id}) => (
          <ToastAlert
            id={id}
            title="Draft Saved"
            description="Draft saved successfully."
            isClosable={true}
            onClose={toast.close}
            variant="left-accent"
            status="success"
          />
        ),
        placement: 'top',
        duration: 3000,
      });

      if (setItem) {
        setItem(updatedItem);
      }
    } catch (error) {
      toast.show({
        render: ({id}) => (
          <ToastAlert
            id={id}
            title="Error"
            description="Error saving draft."
            isClosable={true}
            onClose={toast.close}
            variant="left-accent"
            status="error"
          />
        ),
        placement: 'top',
        duration: 3000,
      });
    }
  }, [localItem, formValues, attachments, setItem, toast]);

  const handleSubmit = useCallback(async () => {
    try {
      setIsSubmitting(true);

      // 准备扩展列数据
      const extendColumns = localItem.extend_columns.map(column => ({
        column_name: column.column_name,
        value: formValues[column.column_name] || '',
      }));

      // 准备提交数据
      const updatedItem = {
        ...localItem,
        ...formValues,
        asset_id: selectedAsset?.asset_id || localItem.asset_id,
        asset_code: selectedAsset?.asset_code || localItem.asset_code,
        asset_location: selectedAsset?.asset_location || localItem.asset_location,
      };

      // 提交数据，包括附件
      await LogListService.submitLogWithFiles(updatedItem, extendColumns, attachments);

      toast.show({
        render: ({id}) => (
          <ToastAlert
            id={id}
            title="Success"
            description="Log submitted successfully."
            isClosable={true}
            onClose={toast.close}
            variant="left-accent"
            status="success"
          />
        ),
        placement: 'top',
        duration: 3000,
      });

      if (setItem) {
        setItem(updatedItem);
      }
    } catch (error) {
      console.error('Submit error:', error);
      toast.show({
        render: ({id}) => (
          <ToastAlert
            id={id}
            title="Error"
            description={error.message || "Error submitting log."}
            isClosable={true}
            onClose={toast.close}
            variant="left-accent"
            status="error"
          />
        ),
        placement: 'top',
        duration: 3000,
      });
    } finally {
      setIsSubmitting(false);
    }
  }, [localItem, formValues, selectedAsset, attachments, setItem, toast]);

  const textStyle = {
    fontSize: 'xl',
    color: mode === 'dark' ? 'white' : 'black',
    mb: 2,
  };

  const labelStyle = {
    fontSize: 'lg',
    fontWeight: 'bold',
    color: mode === 'dark' ? 'orange.300' : 'orange.600',
    mb: 1,
  };

  const gradientColors = ['#FF9800', '#F57C00'];

  const handleCancel = () => {
    navigation.goBack();
  };

  return (
    <ScrollView>
      <FormControl isRequired={item.log_type === 'Reactive'} isInvalid={false}>
        <FormControl.Label _text={{ fontSize: 'sm', mb: 1 }}>Completion Details</FormControl.Label>
        <TextArea
          value={formValues.completion_details}
          onChangeText={value => handleFieldChange('completion_details', value)}
          color={mode === 'dark' ? 'white' : 'black'}
        />
      </FormControl>
      <FormControl isRequired={false} isInvalid={false}>
        <FormControl.Label _text={{ fontSize: 'sm', mb: 1 }}>Asset</FormControl.Label>
        <AssetSelect
          onSelect={handleAssetSelect}
          initialAssetId={item ? item.asset_id : undefined}
        />
      </FormControl>
      <HStack>
        <Box flex={5}>
          <DatePickerControl
            label="On-Site Date"
            date={formValues.arrive_date ? new Date(formValues.arrive_date) : undefined}
            showPicker={showOnSiteDatePicker}
            setShowPicker={setShowOnSiteDatePicker}
            onChange={async (event, selectedDate) => {
              setShowOnSiteDatePicker(false);
              if (selectedDate) {
                handleFieldChange('arrive_date', selectedDate.toISOString());
                await getCurrentLocation((lat, long) => {
                  handleFieldChange('arrive_latitude', lat);
                  handleFieldChange('arrive_longitude', long);
                });
              }
            }}
            iconName="calendar"
            mode={mode}
          />
        </Box>
        <Box flex={4}>
          <DatePickerControl
            label="On-Site Time"
            date={formValues.arrive_time ? new Date(formValues.arrive_time) : undefined}
            showPicker={showOnSiteTimePicker}
            setShowPicker={setShowOnSiteTimePicker}
            onChange={async (event, selectedDate) => {
              setShowOnSiteTimePicker(false);
              if (selectedDate) {
                handleFieldChange('arrive_time', selectedDate.toISOString());
                await getCurrentLocation((lat, long) => {
                  handleFieldChange('arrive_latitude', lat);
                  handleFieldChange('arrive_longitude', long);
                });
              }
            }}
            iconName="clock-outline"
            mode={mode}
          />
        </Box>
        <Box flex={1}>
          <FormControl>
            <FormControl.Label>GPS</FormControl.Label>
            <IconButton
              {...dateTimePickerStyle}
              icon={
                <Icon
                  as={MaterialCommunityIcons}
                  name="map-marker-radius"
                  onPress={updateOnSiteData}
                />
              }
              isLoading={isGettingLocation}
              isLoadingText="Getting location..."
            />
          </FormControl>
        </Box>
      </HStack>
      <HStack justifyContent="flex-end">
        <Text color={mode === 'dark' ? 'white' : 'black'} fontSize="xs">
          {formValues.arrive_latitude && formValues.arrive_longitude
            ? `Coord: ${formValues.arrive_latitude.toFixed(
              4,
            )}, ${formValues.arrive_longitude.toFixed(4)}`
            : 'Coord: Not set'}
        </Text>
      </HStack>

      <HStack space={4}>
        <Box flex={5}>
          <DatePickerControl
            label="Comp Date"
            date={
              formValues.completed_date
                ? new Date(formValues.completed_date)
                : undefined
            }
            showPicker={showCompleteDatePicker}
            setShowPicker={setShowCompleteDatePicker}
            onChange={async (event, selectedDate) => {
              setShowCompleteDatePicker(false);
              if (selectedDate) {
                handleFieldChange('completed_date', selectedDate.toISOString());
                await getCurrentLocation((lat, long) => {
                  handleFieldChange('completion_latitude', lat);
                  handleFieldChange('completion_longitude', long);
                });
              }
            }}
            iconName="calendar"
            mode={mode}
          />
        </Box>
        <Box flex={4}>
          <DatePickerControl
            label="Comp Time"
            date={
              formValues.completed_time
                ? new Date(formValues.completed_time)
                : undefined
            }
            showPicker={showCompleteTimePicker}
            setShowPicker={setShowCompleteTimePicker}
            onChange={async (event, selectedDate) => {
              setShowCompleteTimePicker(false);
              if (selectedDate) {
                handleFieldChange('completed_time', selectedDate.toISOString());
                await getCurrentLocation((lat, long) => {
                  handleFieldChange('completion_latitude', lat);
                  handleFieldChange('completion_longitude', long);
                });
              }
            }}
            iconName="clock-outline"
            mode={mode}
          />
        </Box>
        <Box flex={1}>
          <FormControl>
            <FormControl.Label>GPS</FormControl.Label>
            <IconButton
              {...dateTimePickerStyle}
              icon={
                <Icon
                  as={MaterialCommunityIcons}
                  name="map-marker-radius"
                  onPress={updateCompleteData}
                />
              }
              isLoading={isGettingLocation}
              isLoadingText="Getting location..."
            />
          </FormControl>
        </Box>
      </HStack>
      <HStack justifyContent="flex-end">
        <Text color={mode === 'dark' ? 'white' : 'black'} fontSize="xs">
          {formValues.completion_latitude && formValues.completion_longitude
            ? `Coord: ${formValues.completion_latitude.toFixed(
              4,
            )}, ${formValues.completion_longitude.toFixed(4)}`
            : 'Coord: Not set'}
        </Text>
      </HStack>

      <Accordion>
        <Accordion.Item>
          <Accordion.Summary>
            <Text color={mode === 'dark' ? 'white' : 'black'}>
              {localItem.service_agreement_description}
            </Text>
            <Accordion.Icon />
          </Accordion.Summary>
          <Accordion.Details py={2}>
            {localItem?.extend_columns?.map((column, index) => (
              <DynamicFormField
                key={index}
                column={column}
                value={formValues[column.column_name]}
                onChange={value => handleFieldChange(column.column_name, value)}
                mode={mode}
              />
            ))}
          </Accordion.Details>
        </Accordion.Item>
      </Accordion>

      <FileUploadComponent
        localLogId={item.local_log_id}
        onFilesSelected={handleFilesChanged}
      />

      <HStack justifyContent="space-between" space={1}>
        <Button
          flex={1}
          {...cancelButtonStyle}
          leftIcon={<Icon as={MaterialCommunityIcons} name="cancel" />}
          onPress={handleCancel}>
          Cancel
        </Button>
        <Button
          onPress={handleSubmit}
          flex={1}
          {...submitButtonStyle}
          leftIcon={<Icon as={MaterialCommunityIcons} name="briefcase-check" />}
          isLoading={isSubmitting}
          isLoadingText="Submitting">
          Submit
        </Button>
      </HStack>
    </ScrollView>
  );
};

export default LogHeaderInputable;
