import React from 'react';
import {ScrollView, HStack, Text, VStack} from 'native-base';
import GradientButton from './GradientButton.tsx';
import {gradientButtonStyle} from '../../styles/buttonStyles.ts';
import {useTheme} from '../../styles/ThemeContext.tsx';
import { useNavigation } from '@react-navigation/native';

const LogHeaderReadOnly: React.FC<{item: any}> = ({item}) => {
  const {mode} = useTheme();
  const navigation = useNavigation();

  const handleMapPress = () => {
    console.log('Navigating to MapMarker with log_address:', item.log_address);
    navigation.navigate('Map Marker', {
      log_address: item.log_address,
      description: item.description
    });
  };

  return (
    <ScrollView>
      <HStack justifyContent="space-between" alignItems="flex-start" marginTop={2}>
        <VStack flex={8}>
          <Text color={mode === 'dark' ? 'trueGray.400' : 'trueGray.500'}  fontWeight="bold">
            Site Address
          </Text>
          <Text
            color={mode === 'dark' ? 'white' : 'black'}
            underline={true}
            isTruncated={false}
            numberOfLines={0}
            wordWrap="break-word"
          >
            {item.log_address}
          </Text>
        </VStack>
        <GradientButton
          {...gradientButtonStyle}
          height={10}
          width={20}
          alignSelf="flex-start"
          onPress={handleMapPress}
        >
          Map
        </GradientButton>
      </HStack>

      <Text
        color={mode === 'dark' ? 'trueGray.400' : 'trueGray.500'}
        fontWeight="bold"
      >
        Description
      </Text>
      <Text
        color={mode === 'dark' ? 'white' : 'black'}

        underline={true}
        isTruncated={false}
        numberOfLines={0}
        wordWrap="break-word">
        {item.description?.replace(/"/g, '') || ''}
      </Text>

      <Text
        color={mode === 'dark' ? 'trueGray.400' : 'trueGray.500'}
        fontWeight="bold"
      >
        Log Details
      </Text>
      <Text
        color={mode === 'dark' ? 'white' : 'black'}
        underline={true}
        isTruncated={false}
        numberOfLines={0}
        wordWrap="break-word">
        {item.comments}
      </Text>

      <Text
        color={mode === 'dark' ? 'trueGray.400' : 'trueGray.500'}
        fontWeight="bold"
      >
        Contact Detail
      </Text>
      <Text
        color={mode === 'dark' ? 'white' : 'black'}
        underline={true}
        isTruncated={false}
        numberOfLines={0}
        wordWrap="break-word">
        {item.contactDetails}
      </Text>
    </ScrollView>
  );
};

export default LogHeaderReadOnly;
