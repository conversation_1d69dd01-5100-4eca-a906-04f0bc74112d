import React, {useEffect, useMemo, useState} from 'react';
import {
  FormControl,
  Input,
  Select,
  Switch,
  TextArea,
  Button,
  Text, Box, HStack,
} from 'native-base';
import DateTimePicker from '@react-native-community/datetimepicker';
import {Platform} from 'react-native';

interface ColumnData {
  column_name: string;
  data_type: string;
  required: boolean;
  combo_list?: Array<{label: string; value: string}>;
}

interface DynamicFormFieldProps {
  column: ColumnData;
  value: any;
  onChange: (value: any) => void;
  mode: 'light' | 'dark';
}

const DynamicFormField: React.FC<DynamicFormFieldProps> = ({
  column,
  value,
  onChange,
  mode,
}) => {
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [dateValue, setDateValue] = useState<Date | null>(null);

  useEffect(() => {
    if (column.data_type === 'Date' || column.data_type === 'Time') {
      const newDate = value ? new Date(value) : null;
      setDateValue(newDate);
    }
  }, [value, column.data_type]);

  const formatDate = (date: Date | string | null): string => {
    if (!date) return '';
    const d = date instanceof Date ? date : new Date(date);
    return d.toLocaleDateString();
  };

  const formatTime = (date: Date | string | null): string => {
    if (!date) return '';
    const d = date instanceof Date ? date : new Date(date);
    return d.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const formattedValue = useMemo(() => {
    if (!dateValue) return '';
    return column.data_type === 'Date'
      ? formatDate(dateValue)
      : formatTime(dateValue);
  }, [dateValue, column.data_type]);

  const displayValue = useMemo(() => {
    if (formattedValue) return formattedValue;
    if (value instanceof Date) return value.toLocaleDateString();
    if (typeof value === 'string') return value;
    return 'No value';
  }, [formattedValue, value]);

  const fieldStyle = {
    fontSize: 'sm',
    height: 10,
    borderWidth: 1,
    borderColor: mode === 'dark' ? 'gray.600' : 'gray.300',
    borderRadius: 'sm',
    color: mode === 'dark' ? 'white' : 'black',
    backgroundColor: mode === 'dark' ? 'gray.700' : 'white',
  };

  const labelColor = mode === 'dark' ? 'white' : 'black';


  const renderField = () => {
    /*console.log(`Rendering ${column.data_type} field:`, {
      columnName: column.column_name,
      value: value,
      dateValue: dateValue
    });*/
    switch (column.data_type) {
      case 'Combo':
        return (
          <Select
            selectedValue={value}
            onValueChange={onChange}
            accessibilityLabel={`Choose ${column.column_name}`}
            placeholder={`Select ${column.column_name}`}
            _input={{
              readOnly: true,
            }}
            {...fieldStyle}>
            {column.combo_list?.map((option, index) => (
              <Select.Item
                key={index}
                label={option.label}
                value={option.value}
              />
            ))}
          </Select>
        );
      case 'Text':
      case 'String':
        return (
          <TextArea
            value={value}
            onChangeText={onChange}
            placeholder={`Enter ${column.column_name}`}
            {...fieldStyle}
          />
        );
      case 'Quantity':
      case 'Number':
      case 'Int':
        return (
          <Input
            value={value?.toString()}
            onChangeText={text => onChange(text ? parseFloat(text) : null)}
            keyboardType="numeric"
            placeholder={`Enter ${column.column_name}`}
            {...fieldStyle}
          />
        );
      case 'Yes/No':
        return (
          <HStack alignItems="center" space={4}>
            <Switch
              isChecked={value === '-1' || value === true}
              onToggle={newValue => onChange(newValue ? '-1' : '0')}
              size="md"
            />
            <Text color={mode === 'dark' ? 'white' : 'black'}>
              {value === '1' || value === true ? 'Yes' : 'No'}
            </Text>
          </HStack>
        );
      case 'Date':
      case 'Time':
        return (
          <Button
            onPress={() => setShowDatePicker(true)}
            {...fieldStyle}
            _text={{
              color: mode === 'dark' ? 'gray.200' : 'gray.700',  // 根据模式调整文字颜色
              fontSize: 'md'  // 保持字体大小一致
            }}
          >
            {value
              ? (column.data_type === 'Date' ? formatDate(value) : formatTime(value))
              : `Select ${column.data_type}`
            }
          </Button>
        );
      default:
        return (
          <Input
            value={value}
            onChangeText={onChange}
            placeholder={`Enter ${column.column_name}`}
            {...fieldStyle}
          />
        );
    }
  };

  /*console.log(`Rendering field ${column.column_name}:`, {
    dataType: column.data_type,
    value: value,
    formattedValue: column.data_type === 'Date' ? formatDate(dateValue) : (column.data_type === 'Time' ? formatTime(dateValue) : value)
  });*/

  return (
    <FormControl isRequired={column.required} mb={2}>
      <FormControl.Label _text={{fontSize: 'sm', fontWeight: 'medium'}}>
          {column.column_name}
      </FormControl.Label>
      {renderField()}
      {showDatePicker && (column.data_type === 'Date' || column.data_type === 'Time') && (
        <DateTimePicker
          value={value ? new Date(value) : new Date()}
          mode={column.data_type.toLowerCase()}
          display="default"
          onChange={(event, selectedDate) => {
            setShowDatePicker(Platform.OS === 'ios');
            if (selectedDate) {
              onChange(selectedDate.toISOString());
            }
          }}
        />
      )}
    </FormControl>
  );
};

export default DynamicFormField;
