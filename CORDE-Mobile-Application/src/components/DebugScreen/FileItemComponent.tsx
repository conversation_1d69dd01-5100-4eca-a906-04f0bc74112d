import React from 'react';
import { VSta<PERSON>, Box, Text, Divider, Button, FlatList } from "native-base";
import { useCopyToClipboard } from '../../utils/clipboard';

const FileItemComponent = ({ files }) => {
  const copyToClipboard = useCopyToClipboard();

  const renderItem = ({ item: file, index }: { item: any; index: number }) => (
    <Box key={index} bg="coolGray.100" p={3} rounded="md" mb={2}>
      <VStack space={2}>
        <Text fontWeight="bold">ID: {file.id || 'N/A'}</Text>
        <Text>Name: {file.file_name || 'N/A'}</Text>
        <Text>Extension: {file.file_extension || 'N/A'}</Text>
        <Text>Content Type: {file.content_type || 'N/A'}</Text>
        <Text>Related Record ID: {file.related_record_id || 'N/A'}</Text>
        <Text>Related Record Type: {file.related_record_type || 'N/A'}</Text>
        <Text>Local Log ID: {file.local_log_id || 'N/A'}</Text>
        <Text>File Path: {file.file_path || 'N/A'}</Text>
      </VStack>
      <Button onPress={() => copyToClipboard(JSON.stringify(file, null, 2))} marginY={2} variant="subtle" colorScheme="secondary">
        Copy Details
      </Button>
      <Divider my={2} />
    </Box>
  );

  if (!files || files.length === 0) {
    return (
      <Box p={4} bg="coolGray.50" rounded="md" m={2}>
        <Text textAlign="center" color="coolGray.500">
          No files data found
        </Text>
      </Box>
    );
  }

  return (
    <FlatList
      data={files}
      renderItem={renderItem}
      keyExtractor={(item, index) => `file-${item.id || index}`}
      contentContainerStyle={{ padding: 8 }}
      showsVerticalScrollIndicator={true}
    />
  );
};

export default FileItemComponent;
