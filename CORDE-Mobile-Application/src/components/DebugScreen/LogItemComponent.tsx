import React, { useState, useEffect } from 'react';
import { VStack, Box, Text, Divider, Button, FlatList, HStack, IconButton, Heading, Badge, Pressable, Spinner, useToast, Input, Select } from "native-base";
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import { useCopyToClipboard } from '../../utils/clipboard';
import { LogList } from '../../database/LogList';

const LogItemComponent = ({ logs: initialLogs }) => {
  const [logs, setLogs] = useState(initialLogs || []);
  const [expandedItems, setExpandedItems] = useState<Set<number>>(new Set());
  const [loading, setLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalRecords, setTotalRecords] = useState(0);
  const [hasMore, setHasMore] = useState(false);
  const [pageSize] = useState(10);
  const [allocationPersonId, setAllocationPersonId] = useState<string>('');
  
  const copyToClipboard = useCopyToClipboard();
  const toast = useToast();

  // Load data with pagination and filtering
  const loadLogs = async (page: number = 1, reset: boolean = false, personId?: number) => {
    try {
      setLoading(true);
      const result = await LogList.getAllPaginated(page, pageSize, personId);
      
      if (reset) {
        setLogs(result.data);
      } else {
        setLogs(prev => [...prev, ...result.data]);
      }
      
      setTotalRecords(result.total);
      setHasMore(result.hasMore);
      setCurrentPage(page);
    } catch (error) {
      console.error('Error loading logs:', error);
      toast.show({
        title: "Error loading logs",
        placement: "top"
      });
    } finally {
      setLoading(false);
    }
  };

  // Refresh data
  const refreshData = async () => {
    const personId = allocationPersonId ? parseInt(allocationPersonId) : undefined;
    await loadLogs(1, true, personId);
  };

  // Filter by allocation person
  const handleFilterByPerson = async () => {
    const personId = allocationPersonId ? parseInt(allocationPersonId) : undefined;
    await loadLogs(1, true, personId);
  };

  // Load more data
  const loadMore = async () => {
    if (hasMore && !loading) {
      const personId = allocationPersonId ? parseInt(allocationPersonId) : undefined;
      await loadLogs(currentPage + 1, false, personId);
    }
  };

  // Toggle item expansion
  const toggleExpanded = (localLogId: number) => {
    const newExpanded = new Set(expandedItems);
    if (newExpanded.has(localLogId)) {
      newExpanded.delete(localLogId);
    } else {
      newExpanded.add(localLogId);
    }
    setExpandedItems(newExpanded);
  };

  // Get status color
  const getStatusColor = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'completed': return 'success';
      case 'allocated': return 'info';
      case 'draft': return 'warning';
      case 'open': return 'primary';
      default: return 'coolGray';
    }
  };

  useEffect(() => {
    loadLogs(1, true);
  }, []);

  const renderItem = ({ item: log, index }: { item: any; index: number }) => {
    const isExpanded = expandedItems.has(log.local_log_id);
    
    return (
      <Box key={index} bg="white" p={3} rounded="md" mb={2} shadow={1}>
        <Pressable onPress={() => toggleExpanded(log.local_log_id)}>
          <HStack justifyContent="space-between" alignItems="center" mb={isExpanded ? 3 : 0}>
            <VStack flex={1} space={1}>
              <HStack space={2} alignItems="center">
                <Text fontWeight="bold" fontSize="md">
                  {log.log_header_id || `Local ID: ${log.local_log_id}`}
                </Text>
                <Badge colorScheme={getStatusColor(log.status)} variant="subtle">
                  {log.status || 'Unknown'}
                </Badge>
              </HStack>
              <Text fontSize="sm" color="coolGray.600" numberOfLines={isExpanded ? undefined : 1}>
                {log.description || 'No description'}
              </Text>
              <Text fontSize="xs" color="coolGray.500">
                {log.allocation_person_full_name} • {log.schd_date}
              </Text>
            </VStack>
            <IconButton
              icon={
                <MaterialCommunityIcons 
                  name={isExpanded ? "chevron-up" : "chevron-down"} 
                  size={20} 
                  color="#6B7280" 
                />
              }
              onPress={() => toggleExpanded(log.local_log_id)}
              variant="ghost"
              size="sm"
            />
          </HStack>
        </Pressable>

        {isExpanded && (
          <VStack space={2} mt={2}>
            <Divider />
            <VStack space={1}>
              <Text fontSize="xs" color="coolGray.500">Local ID: {log.local_log_id || 'N/A'}</Text>
              <Text fontSize="xs" color="coolGray.500">Header ID: {log.log_header_id || 'N/A'}</Text>
              <Text fontSize="xs" color="coolGray.500">Service Agreement ID: {log.service_agreement_id || 'N/A'}</Text>
              <Text fontSize="xs" color="coolGray.500">Asset: {log.asset_description || 'N/A'} ({log.asset_code || 'N/A'})</Text>
              <Text fontSize="xs" color="coolGray.500">Order No: {log.order_no || 'N/A'}</Text>
              <Text fontSize="xs" color="coolGray.500">Completed: {log.is_completed ? 'Yes' : 'No'}</Text>
              <Text fontSize="xs" color="coolGray.500">Draft: {log.is_draft ? 'Yes' : 'No'}</Text>
              <Text fontSize="xs" color="coolGray.500">Created: {log.create_log_timestamp || 'N/A'}</Text>
              <Text fontSize="xs" color="coolGray.500">Updated: {log.updated_date || 'N/A'}</Text>
              <Text fontSize="xs" color="coolGray.500">Service Type: {log.service_type || 'N/A'}</Text>
              <Text fontSize="xs" color="coolGray.500">Priority: {log.priority || 'N/A'}</Text>
              {log.asset_latitude && log.asset_longitude && (
                <Text fontSize="xs" color="coolGray.500">
                  Coordinates: {log.asset_latitude}, {log.asset_longitude}
                </Text>
              )}
            </VStack>
            <Button 
              onPress={() => copyToClipboard(JSON.stringify(log, null, 2))} 
              variant="subtle" 
              colorScheme="secondary"
              size="sm"
            >
              Copy Details
            </Button>
          </VStack>
        )}
      </Box>
    );
  };

  if (!logs || logs.length === 0) {
    return (
      <VStack flex={1} space={4} p={4}>
        {/* Filter Section */}
        <VStack space={3} bg="coolGray.50" p={3} rounded="md">
          <Text fontSize="sm" fontWeight="bold">Filter by Allocation Person ID</Text>
          <HStack space={2} alignItems="center">
            <Input
              flex={1}
              placeholder="Enter Person ID"
              value={allocationPersonId}
              onChangeText={setAllocationPersonId}
              keyboardType="numeric"
              size="sm"
            />
            <Button onPress={handleFilterByPerson} size="sm" isLoading={loading}>
              Filter
            </Button>
            <IconButton
              icon={<MaterialCommunityIcons name="refresh" size={16} />}
              onPress={refreshData}
              variant="ghost"
              size="sm"
              isDisabled={loading}
            />
          </HStack>
        </VStack>

        <Box p={4} bg="coolGray.50" rounded="md" alignItems="center">
          <Text textAlign="center" color="coolGray.500">
            No logs data found
          </Text>
        </Box>
      </VStack>
    );
  }

  return (
    <VStack flex={1} space={4}>
      {/* Filter Section */}
      <Box bg="coolGray.50" p={3} rounded="md" mx={3} mt={3}>
        <VStack space={3}>
          <Text fontSize="sm" fontWeight="bold">Filter by Allocation Person ID</Text>
          <HStack space={2} alignItems="center">
            <Input
              flex={1}
              placeholder="Enter Person ID"
              value={allocationPersonId}
              onChangeText={setAllocationPersonId}
              keyboardType="numeric"
              size="sm"
            />
            <Button onPress={handleFilterByPerson} size="sm" isLoading={loading}>
              Filter
            </Button>
          </HStack>
        </VStack>
      </Box>

      {/* Header with counts and controls */}
      <Box bg="white" p={3} mx={3} rounded="md" shadow={1}>
        <HStack justifyContent="space-between" alignItems="center">
          <HStack space={2} alignItems="center">
            <Text fontSize="sm" fontWeight="bold">
              Logs ({logs.length}/{totalRecords} total)
            </Text>
          </HStack>
          <HStack space={2}>
            <IconButton
              icon={<MaterialCommunityIcons name="refresh" size={20} />}
              onPress={refreshData}
              variant="ghost"
              size="sm"
              isDisabled={loading}
            />
            <IconButton
              icon={
                <MaterialCommunityIcons 
                  name={expandedItems.size > 0 ? "collapse-all" : "expand-all"} 
                  size={20} 
                />
              }
              onPress={() => {
                if (expandedItems.size > 0) {
                  setExpandedItems(new Set());
                } else {
                  setExpandedItems(new Set(logs.map(log => log.local_log_id).filter(Boolean)));
                }
              }}
              variant="ghost"
              size="sm"
            />
          </HStack>
        </HStack>
      </Box>

      {/* List */}
      <FlatList
        data={logs}
        renderItem={renderItem}
        keyExtractor={(item, index) => `log-${item.local_log_id || index}`}
        contentContainerStyle={{ padding: 12 }}
        showsVerticalScrollIndicator={true}
        onEndReached={loadMore}
        onEndReachedThreshold={0.1}
        ListFooterComponent={() => {
          if (loading) {
            return (
              <Box p={4} alignItems="center">
                <HStack space={2} alignItems="center">
                  <Spinner size="sm" />
                  <Text fontSize="sm" color="coolGray.600">Loading...</Text>
                </HStack>
              </Box>
            );
          }
          if (hasMore) {
            return (
              <Box p={4} alignItems="center">
                <Button onPress={loadMore} variant="ghost" size="sm">
                  Load More
                </Button>
              </Box>
            );
          }
          return null;
        }}
      />
    </VStack>
  );
};

export default LogItemComponent;
