import React from 'react';
import { VS<PERSON><PERSON>, Box, Text, Divider, But<PERSON>, FlatList } from "native-base";
import { useCopyToClipboard } from '../../utils/clipboard';

const LogItemComponent = ({logs}) => {
  const copyToClipboard = useCopyToClipboard();

  const renderItem = ({ item: log, index }: { item: any; index: number }) => (
    <Box key={index} bg="coolGray.100" p={3} rounded="md" mb={2}>
      <VStack space={2}>
        <Text fontWeight="bold">ID: {log.local_log_id || 'N/A'}</Text>
        <Text>Header ID: {log.log_header_id || 'N/A'}</Text>
        <Text>Description: {log.description || 'N/A'}</Text>
        <Text>
          Service Agreement ID: {log.service_agreement_id || 'N/A'}
        </Text>
        <Text>Status: {log.status || 'N/A'}</Text>
        <Text>Scheduled Date: {log.schd_date || 'N/A'}</Text>
        <Text>
          Allocation Person: {log.allocation_person_full_name || 'N/A'}
        </Text>
        <Text>
          Asset: {log.asset_description || 'N/A'} ({log.asset_code || 'N/A'}
          )
        </Text>
        <Text>Order No: {log.order_no || 'N/A'}</Text>
        <Text>Completed: {log.is_completed ? 'Yes' : 'No'}</Text>
        <Text>Draft: {log.is_draft ? 'Yes' : 'No'}</Text>
        <Text>Created: {log.create_log_timestamp || 'N/A'}</Text>
        <Text>Updated: {log.updated_date || 'N/A'}</Text>
        <Text>Service Type: {log.service_type || 'N/A'}</Text>
        <Text>Priority: {log.priority || 'N/A'}</Text>
      </VStack>
      <Button onPress={() => copyToClipboard(JSON.stringify(log, null, 2))} marginY={2} variant="subtle" colorScheme="secondary">
        Copy Details
      </Button>
      <Divider my={2} />
    </Box>
  );

  if (!logs || logs.length === 0) {
    return (
      <Box p={4} bg="coolGray.50" rounded="md" m={2}>
        <Text textAlign="center" color="coolGray.500">
          No logs data found
        </Text>
      </Box>
    );
  }

  return (
    <FlatList
      data={logs}
      renderItem={renderItem}
      keyExtractor={(item, index) => `log-${item.local_log_id || index}`}
      contentContainerStyle={{ padding: 8 }}
      showsVerticalScrollIndicator={true}
    />
  );
};

export default LogItemComponent;
