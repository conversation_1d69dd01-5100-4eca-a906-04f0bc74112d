import React from 'react';
import { VStack, Box, Text, Divider, Button, FlatList } from "native-base";
import { useCopyToClipboard } from '../../utils/clipboard';

const LogExtensionItemComponent = ({ logExtensions }) => {
  const copyToClipboard = useCopyToClipboard();

  const renderItem = ({ item: extension, index }: { item: any; index: number }) => (
    <Box key={index} bg="coolGray.100" p={3} rounded="md" mb={2}>
      <VStack space={2}>
        <Text fontWeight="bold">ID: {extension.log_extension_id || 'N/A'}</Text>
        <Text>Local Log ID: {extension.local_log_id || 'N/A'}</Text>
        <Text>Name: {extension.name || 'N/A'}</Text>
        <Text>Value: {extension.value_str || 'N/A'}</Text>
      </VStack>
      <Button onPress={() => copyToClipboard(JSON.stringify(extension, null, 2))} marginY={2} variant="subtle" colorScheme="secondary">
        Copy Details
      </Button>
      <Divider my={2} />
    </Box>
  );

  if (!logExtensions || logExtensions.length === 0) {
    return (
      <Box p={4} bg="coolGray.50" rounded="md" m={2}>
        <Text textAlign="center" color="coolGray.500">
          No log extensions data found
        </Text>
      </Box>
    );
  }

  return (
    <FlatList
      data={logExtensions}
      renderItem={renderItem}
      keyExtractor={(item, index) => `extension-${item.log_extension_id || index}`}
      contentContainerStyle={{ padding: 8 }}
      showsVerticalScrollIndicator={true}
    />
  );
};

export default LogExtensionItemComponent;
