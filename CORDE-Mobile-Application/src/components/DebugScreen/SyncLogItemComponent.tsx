import React, { useState, useEffect } from 'react';
import { VSta<PERSON>, Box, Text, Divider, <PERSON><PERSON>, FlatList, HStack, IconButton, <PERSON>ing, Badge, Pressable, Spinner, useToast } from "native-base";
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import { useCopyToClipboard } from '../../utils/clipboard';
import { MobileSyncLogs } from '../../database/MobileSyncLogs';

const SyncLogItemComponent = ({ syncLogs: initialSyncLogs }) => {
  const [syncLogs, setSyncLogs] = useState(initialSyncLogs || []);
  const [expandedItems, setExpandedItems] = useState<Set<number>>(new Set());
  const [loading, setLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalRecords, setTotalRecords] = useState(0);
  const [hasMore, setHasMore] = useState(false);
  const [pageSize] = useState(10);
  
  const copyToClipboard = useCopyToClipboard();
  const toast = useToast();

  // Load data with pagination
  const loadSyncLogs = async (page: number = 1, reset: boolean = false) => {
    try {
      setLoading(true);
      const result = await MobileSyncLogs.getAllPaginated(page, pageSize);
      
      if (reset) {
        setSyncLogs(result.data);
      } else {
        setSyncLogs(prev => [...prev, ...result.data]);
      }
      
      setTotalRecords(result.total);
      setHasMore(result.hasMore);
      setCurrentPage(page);
    } catch (error) {
      console.error('Error loading sync logs:', error);
      toast.show({
        title: "Error loading sync logs",
        placement: "top"
      });
    } finally {
      setLoading(false);
    }
  };

  // Refresh data
  const refreshData = async () => {
    await loadSyncLogs(1, true);
  };

  // Toggle item expansion
  const toggleExpanded = (syncLogId: number) => {
    const newExpanded = new Set(expandedItems);
    if (newExpanded.has(syncLogId)) {
      newExpanded.delete(syncLogId);
    } else {
      newExpanded.add(syncLogId);
    }
    setExpandedItems(newExpanded);
  };

  // Get status color
  const getStatusColor = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'completed':
      case 'synced': return 'success';
      case 'pending':
      case 'open': return 'warning';
      case 'error':
      case 'failed': return 'error';
      case 'submitting': return 'info';
      default: return 'gray';
    }
  };

  // Format date
  const formatDate = (dateString: string) => {
    if (!dateString) return 'N/A';
    try {
      return new Date(dateString).toLocaleString();
    } catch {
      return dateString;
    }
  };

  // Load data on component mount
  useEffect(() => {
    if (!initialSyncLogs || initialSyncLogs.length === 0) {
      loadSyncLogs(1, true);
    } else {
      setSyncLogs(initialSyncLogs);
      setTotalRecords(initialSyncLogs.length);
    }
  }, []);

  const renderItem = ({ item: syncLog, index }: { item: any; index: number }) => {
    const isExpanded = expandedItems.has(syncLog.sync_log_id);
    
    return (
      <Box key={syncLog.sync_log_id || index} bg="coolGray.50" rounded="lg" mb={3} shadow={1}>
        {/* Header - Always visible */}
        <Pressable onPress={() => toggleExpanded(syncLog.sync_log_id)}>
          <Box p={4} bg="coolGray.100" roundedTop="lg">
            <HStack justifyContent="space-between" alignItems="center">
              <VStack flex={1} space={1}>
                <HStack space={2} alignItems="center">
                  <Text fontWeight="bold" fontSize="md">
                    Sync Log #{syncLog.sync_log_id || 'N/A'}
                  </Text>
                  <Badge 
                    colorScheme={getStatusColor(syncLog.sync_status)} 
                    variant="solid"
                    rounded="full"
                  >
                    {syncLog.sync_status || 'Unknown'}
                  </Badge>
                </HStack>
                <Text fontSize="sm" color="coolGray.600">
                  {formatDate(syncLog.last_sync_time || syncLog.last_update_time)}
                </Text>
                <Text fontSize="xs" color="coolGray.500">
                  Local Log ID: {syncLog.local_log_id || 'N/A'} • Retries: {syncLog.retry_count || 0}
                </Text>
              </VStack>
              <IconButton
                icon={
                  <MaterialCommunityIcons 
                    name={isExpanded ? "chevron-up" : "chevron-down"} 
                    size={20} 
                    color="coolGray.600" 
                  />
                }
                onPress={() => toggleExpanded(syncLog.sync_log_id)}
                variant="ghost"
                size="sm"
              />
            </HStack>
          </Box>
        </Pressable>

        {/* Detailed info - Collapsible */}
        {isExpanded && (
          <Box p={4} bg="white" roundedBottom="lg">
            <VStack space={3}>
              <Divider />
              
              {/* Basic Info */}
              <VStack space={2}>
                <Heading size="xs" color="coolGray.700">Basic Information</Heading>
                <HStack justifyContent="space-between">
                  <Text fontSize="sm" color="coolGray.600">User ID:</Text>
                  <Text fontSize="sm" fontWeight="medium">{syncLog.user_id || 'N/A'}</Text>
                </HStack>
                <HStack justifyContent="space-between">
                  <Text fontSize="sm" color="coolGray.600">Log Header ID:</Text>
                  <Text fontSize="sm" fontWeight="medium">{syncLog.log_header_id || 'N/A'}</Text>
                </HStack>
                <HStack justifyContent="space-between">
                  <Text fontSize="sm" color="coolGray.600">Task Count:</Text>
                  <Text fontSize="sm" fontWeight="medium">{syncLog.sync_task_count || 'N/A'}</Text>
                </HStack>
                <HStack justifyContent="space-between">
                  <Text fontSize="sm" color="coolGray.600">Offline Indicator:</Text>
                  <Badge colorScheme={syncLog.offline_indicator ? 'warning' : 'success'} variant="outline">
                    {syncLog.offline_indicator ? 'Yes' : 'No'}
                  </Badge>
                </HStack>
              </VStack>

              <Divider />

              {/* Status Info */}
              <VStack space={2}>
                <Heading size="xs" color="coolGray.700">Status Information</Heading>
                <HStack justifyContent="space-between">
                  <Text fontSize="sm" color="coolGray.600">Conflict Status:</Text>
                  <Text fontSize="sm" fontWeight="medium">{syncLog.conflict_status || 'N/A'}</Text>
                </HStack>
                <HStack justifyContent="space-between">
                  <Text fontSize="sm" color="coolGray.600">Last Update:</Text>
                  <Text fontSize="sm" fontWeight="medium">{formatDate(syncLog.last_update_time)}</Text>
                </HStack>
              </VStack>

              {/* Errors */}
              {syncLog.sync_errors && (
                <>
                  <Divider />
                  <VStack space={2}>
                    <Heading size="xs" color="error.600">Error Information</Heading>
                    <Box bg="error.50" p={3} rounded="md" borderLeftWidth={3} borderLeftColor="error.500">
                      <Text fontSize="sm" color="error.700">{syncLog.sync_errors}</Text>
                    </Box>
                  </VStack>
                </>
              )}

              {/* Actions */}
              <Divider />
              <Button 
                onPress={() => copyToClipboard(JSON.stringify(syncLog, null, 2))} 
                variant="outline" 
                colorScheme="blue"
                size="sm"
                leftIcon={<MaterialCommunityIcons name="content-copy" size={16} />}
              >
                Copy Full Details
              </Button>
            </VStack>
          </Box>
        )}
      </Box>
    );
  };

  if (!syncLogs || syncLogs.length === 0) {
    return (
      <VStack flex={1}>
        {/* Header with pagination and controls */}
        <Box p={3} bg="coolGray.100">
          <HStack justifyContent="space-between" alignItems="center">
            <Text fontSize="xs" color="coolGray.500">(0 total)</Text>
            <HStack space={2}>
              <IconButton
                icon={<MaterialCommunityIcons name="refresh" size={20} />}
                onPress={refreshData}
                variant="ghost"
                size="sm"
                isDisabled={loading}
              />
            </HStack>
          </HStack>
        </Box>
        
        <Box p={4} bg="coolGray.50" rounded="md" m={2} flex={1} justifyContent="center">
          <VStack space={3} alignItems="center">
            <MaterialCommunityIcons name="database-off" size={48} color="coolGray.400" />
            <Text textAlign="center" color="coolGray.500" fontSize="md">
              No sync logs data found
            </Text>
          </VStack>
        </Box>
      </VStack>
    );
  }

  return (
    <VStack flex={1}>
      {/* Header with pagination and controls */}
      <Box p={3} bg="coolGray.100">
        <HStack justifyContent="space-between" alignItems="center">
          <HStack space={2} alignItems="center">
            {/* Pagination buttons */}
            <IconButton
              icon={<MaterialCommunityIcons name="chevron-left" size={16} />}
              onPress={() => currentPage > 1 && loadSyncLogs(currentPage - 1, true)}
              variant="outline"
              size="xs"
              isDisabled={loading || currentPage <= 1}
            />
            <Text fontSize="xs" color="coolGray.600" minWidth="60px" textAlign="center">
              {currentPage} / {Math.ceil(totalRecords / pageSize) || 1}
            </Text>
            <IconButton
              icon={<MaterialCommunityIcons name="chevron-right" size={16} />}
              onPress={() => hasMore && loadSyncLogs(currentPage + 1, true)}
              variant="outline"
              size="xs"
              isDisabled={loading || !hasMore}
            />
            <Text fontSize="xs" color="coolGray.500">
              ({totalRecords} total)
            </Text>
          </HStack>
          <HStack space={2}>
            <IconButton
              icon={<MaterialCommunityIcons name="refresh" size={20} />}
              onPress={refreshData}
              variant="ghost"
              size="sm"
              isDisabled={loading}
            />
            <IconButton
              icon={
                <MaterialCommunityIcons 
                  name={expandedItems.size > 0 ? "collapse-all" : "expand-all"} 
                  size={20} 
                />
              }
              onPress={() => {
                if (expandedItems.size > 0) {
                  setExpandedItems(new Set());
                } else {
                  setExpandedItems(new Set(syncLogs.map(log => log.sync_log_id).filter(Boolean)));
                }
              }}
              variant="ghost"
              size="sm"
            />
          </HStack>
        </HStack>
      </Box>

      {/* List */}
      <FlatList
        data={syncLogs}
        renderItem={renderItem}
        keyExtractor={(item, index) => `synclog-${item.sync_log_id || index}`}
        contentContainerStyle={{ padding: 12 }}
        showsVerticalScrollIndicator={true}
        ListFooterComponent={() => {
          if (loading) {
            return (
              <Box p={4} alignItems="center">
                <HStack space={2} alignItems="center">
                  <Spinner size="sm" />
                  <Text fontSize="sm" color="coolGray.600">Loading...</Text>
                </HStack>
              </Box>
            );
          }
          return null;
        }}
      />
    </VStack>
  );
};

export default SyncLogItemComponent;
