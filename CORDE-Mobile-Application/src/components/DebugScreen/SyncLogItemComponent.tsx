import React from 'react';
import { VStack, Box, Text, Divider, Button, FlatList } from "native-base";
import { useCopyToClipboard } from '../../utils/clipboard';

const SyncLogItemComponent = ({ syncLogs }) => {
  const copyToClipboard = useCopyToClipboard();

  const renderItem = ({ item: syncLog, index }: { item: any; index: number }) => (
    <Box key={index} bg="coolGray.100" p={3} rounded="md" mb={2}>
      <VStack space={2}>
        <Text fontWeight="bold">ID: {syncLog.sync_log_id || 'N/A'}</Text>
        <Text>User ID: {syncLog.user_id || 'N/A'}</Text>
        <Text>Local Log ID: {syncLog.local_log_id || 'N/A'}</Text>
        <Text>Status: {syncLog.sync_status || 'N/A'}</Text>
        <Text>Last Sync Time: {syncLog.last_sync_time || 'N/A'}</Text>
        <Text>Errors: {syncLog.sync_errors || 'None'}</Text>
        <Text>Offline Indicator: {syncLog.offline_indicator ? 'Yes' : 'No'}</Text>
        <Text>Task Count: {syncLog.sync_task_count || 'N/A'}</Text>
        <Text>Conflict Status: {syncLog.conflict_status || 'N/A'}</Text>
        <Text>Retry Count: {syncLog.retry_count || 'N/A'}</Text>
      </VStack>
      <Button onPress={() => copyToClipboard(JSON.stringify(syncLog, null, 2))} marginY={2} variant="subtle" colorScheme="secondary">
        Copy Details
      </Button>
      <Divider my={2} />
    </Box>
  );

  if (!syncLogs || syncLogs.length === 0) {
    return (
      <Box p={4} bg="coolGray.50" rounded="md" m={2}>
        <Text textAlign="center" color="coolGray.500">
          No sync logs data found
        </Text>
      </Box>
    );
  }

  return (
    <FlatList
      data={syncLogs}
      renderItem={renderItem}
      keyExtractor={(item, index) => `synclog-${item.sync_log_id || index}`}
      contentContainerStyle={{ padding: 8 }}
      showsVerticalScrollIndicator={true}
    />
  );
};

export default SyncLogItemComponent;
