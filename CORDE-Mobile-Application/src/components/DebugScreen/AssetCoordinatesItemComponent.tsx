import React from 'react';
import {VStack, Box, Text, FlatList} from 'native-base';

interface AssetCoordinatesItemComponentProps {
  assetCoordinates: any[];
}

const AssetCoordinatesItemComponent: React.FC<AssetCoordinatesItemComponentProps> = ({
  assetCoordinates,
}) => {
  if (!assetCoordinates || assetCoordinates.length === 0) {
    return (
      <Box p={4} bg="coolGray.50" rounded="md" m={2}>
        <Text textAlign="center" color="coolGray.500">
          No asset coordinates data found
        </Text>
      </Box>
    );
  }

  const renderItem = ({ item: coordinate, index }: { item: any; index: number }) => (
    <Box key={index} bg="coolGray.100" p={3} rounded="md" mb={2}>
      <VStack space={2}>
        <Text fontWeight="bold">Asset ID: {coordinate.asset_id || 'N/A'}</Text>
        <Text>Latitude: {coordinate.latitude || 'N/A'}</Text>
        <Text>Longitude: {coordinate.longitude || 'N/A'}</Text>
      </VStack>
    </Box>
  );

  return (
    <FlatList
      data={assetCoordinates}
      renderItem={renderItem}
      keyExtractor={(item, index) => `coordinate-${item.asset_id || index}`}
      contentContainerStyle={{ padding: 8 }}
      showsVerticalScrollIndicator={true}
    />
  );
};

export default AssetCoordinatesItemComponent;
