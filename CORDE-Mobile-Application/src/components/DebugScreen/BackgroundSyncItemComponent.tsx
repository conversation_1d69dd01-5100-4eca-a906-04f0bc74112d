import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  VStack,
  HS<PERSON>ck,
  <PERSON>,
  <PERSON><PERSON>,
  ScrollView,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>
} from 'native-base';
import { BackgroundSyncService } from '../../services/BackgroundSyncService';

const BackgroundSyncItemComponent = () => {
  const [syncStatus, setSyncStatus] = useState<any>(null);
  const [refreshKey, setRefreshKey] = useState(0);
  const [lastAction, setLastAction] = useState<{action: string, status: 'success' | 'error' | 'loading', time: Date} | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const refreshStatus = () => {
    try {
      const status = BackgroundSyncService.getStatus();
      setSyncStatus(status);
      setRefreshKey(prev => prev + 1);
      setLastAction({action: 'Status refreshed', status: 'success', time: new Date()});
    } catch (error) {
      console.error('Error getting sync status:', error);
      setLastAction({action: 'Error getting status', status: 'error', time: new Date()});
    }
  };

  const manualSync = async () => {
    try {
      setIsLoading(true);
      setLastAction({action: 'Manual sync in progress...', status: 'loading', time: new Date()});
      
      await BackgroundSyncService.manualSync();
      refreshStatus();
      setLastAction({action: 'Manual sync completed', status: 'success', time: new Date()});
    } catch (error) {
      console.error('Error in manual sync:', error);
      setLastAction({action: 'Manual sync failed', status: 'error', time: new Date()});
    } finally {
      setIsLoading(false);
    }
  };

  const resetTimers = () => {
    try {
      BackgroundSyncService.resetTaskTimers();
      refreshStatus();
      setLastAction({action: 'Task timers reset', status: 'success', time: new Date()});
    } catch (error) {
      console.error('Error resetting timers:', error);
      setLastAction({action: 'Error resetting timers', status: 'error', time: new Date()});
    }
  };

  const restartBackgroundSync = () => {
    try {
      setIsLoading(true);
      setLastAction({action: 'Restarting background sync...', status: 'loading', time: new Date()});
      
      BackgroundSyncService.stopBackgroundSync();
      setTimeout(() => {
        BackgroundSyncService.startBackgroundSync();
        refreshStatus();
        setLastAction({action: 'Background sync restarted', status: 'success', time: new Date()});
        setIsLoading(false);
      }, 1000);
    } catch (error) {
      console.error('Error restarting background sync:', error);
      setLastAction({action: 'Error restarting sync', status: 'error', time: new Date()});
      setIsLoading(false);
    }
  };

  useEffect(() => {
    refreshStatus();
    
    // Auto refresh every 10 seconds
    const interval = setInterval(refreshStatus, 10000);
    return () => clearInterval(interval);
  }, []);

  const formatTime = (timestamp: number | null) => {
    if (!timestamp) return 'Never';
    return new Date(timestamp).toLocaleString();
  };

  const getStatusColor = (isActive: boolean) => {
    return isActive ? 'success' : 'error';
  };

  return (
    <ScrollView p={4}>
      <VStack space={4}>
        <Heading size="md">🔄 Background Sync Monitor</Heading>
        
        {/* Action Status Display */}
        {lastAction && (
          <Alert 
            w="100%" 
            status={lastAction.status === 'loading' ? 'info' : lastAction.status}
            variant="left-accent"
          >
            <VStack flex={1} space={1}>
              <HStack space={2} alignItems="center">
                {lastAction.status === 'loading' && <Spinner size="sm" />}
                <Text fontSize="sm" fontWeight="medium">
                  {lastAction.action}
                </Text>
              </HStack>
              <Text fontSize="xs" color="gray.600">
                {lastAction.time.toLocaleTimeString()}
              </Text>
            </VStack>
          </Alert>
        )}
        
        {/* Control Buttons */}
        <HStack space={2} flexWrap="wrap">
          <Button 
            size="sm" 
            onPress={refreshStatus}
            isDisabled={isLoading}
          >
            Refresh Status
          </Button>
          <Button 
            size="sm" 
            colorScheme="blue" 
            onPress={manualSync}
            isLoading={isLoading && lastAction?.action.includes('Manual sync')}
            isDisabled={isLoading}
          >
            Manual Sync
          </Button>
          <Button 
            size="sm" 
            colorScheme="orange" 
            onPress={resetTimers}
            isDisabled={isLoading}
          >
            Reset Timers
          </Button>
          <Button 
            size="sm" 
            colorScheme="red" 
            onPress={restartBackgroundSync}
            isLoading={isLoading && lastAction?.action.includes('Restarting')}
            isDisabled={isLoading}
          >
            Restart Sync
          </Button>
        </HStack>

        <Divider />

        {/* Service Status */}
        {syncStatus && (
          <VStack space={3}>
            <Box p={3} bg="gray.100" borderRadius="md">
              <Heading size="sm" mb={2}>Service Status</Heading>
              <VStack space={2}>
                <HStack justifyContent="space-between" alignItems="center">
                  <Text>Initialized:</Text>
                  <Badge colorScheme={getStatusColor(syncStatus.isInitialized)}>
                    {syncStatus.isInitialized ? 'YES' : 'NO'}
                  </Badge>
                </HStack>
                
                <HStack justifyContent="space-between" alignItems="center">
                  <Text>Timer Active:</Text>
                  <Badge colorScheme={getStatusColor(syncStatus.hasActiveTimer)}>
                    {syncStatus.hasActiveTimer ? 'YES' : 'NO'}
                  </Badge>
                </HStack>
                
                <HStack justifyContent="space-between" alignItems="center">
                  <Text>Timer ID:</Text>
                  <Text fontSize="xs" color="gray.600">
                    {syncStatus.timerId || 'None'}
                  </Text>
                </HStack>
                
                <HStack justifyContent="space-between" alignItems="center">
                  <Text>Last Execution:</Text>
                  <Text fontSize="xs" color="gray.600">
                    {formatTime(syncStatus.lastExecutionTime)}
                  </Text>
                </HStack>
              </VStack>
            </Box>

            {/* Task Status */}
            <Box p={3} bg="gray.100" borderRadius="md">
              <Heading size="sm" mb={2}>Sync Tasks</Heading>
              <VStack space={3}>
                {syncStatus.syncTasks?.map((task: any, index: number) => (
                  <Box key={index} p={2} bg="white" borderRadius="sm" borderWidth={1} borderColor="gray.200">
                    <VStack space={1}>
                      <HStack justifyContent="space-between" alignItems="center">
                        <Text fontWeight="bold">{task.name}</Text>
                        <Badge colorScheme="blue" variant="outline">
                          {task.interval / 1000 / 60}min
                        </Badge>
                      </HStack>
                      
                      <Text fontSize="xs" color="gray.600">
                        Last Run: {formatTime(task.lastRun)}
                      </Text>
                      
                      <Text fontSize="xs" color="gray.600">
                        Next Run: {task.nextRun === 'Never run' ? 'Never run' : 
                          typeof task.nextRun === 'string' ? task.nextRun : 
                          new Date(task.nextRun).toLocaleString()}
                      </Text>
                    </VStack>
                  </Box>
                ))}
              </VStack>
            </Box>

            {/* Debug Info */}
            <Box p={3} bg="yellow.50" borderRadius="md" borderWidth={1} borderColor="yellow.200">
              <Heading size="sm" mb={2}>🐛 Debug Info</Heading>
              <Text fontSize="xs" color="gray.700">
                Refresh Key: {refreshKey}
              </Text>
              <Text fontSize="xs" color="gray.700">
                Current Time: {new Date().toLocaleString()}
              </Text>
              <Text fontSize="xs" color="gray.700" mt={1}>
                📝 Check console logs for detailed sync execution info
              </Text>
            </Box>
          </VStack>
        )}

        {!syncStatus && (
          <Box p={4} bg="red.50" borderRadius="md">
            <Text color="red.600">
              Unable to get background sync status. Click "Refresh Status" to try again.
            </Text>
          </Box>
        )}
      </VStack>
    </ScrollView>
  );
};

export default BackgroundSyncItemComponent;
