import React, {useEffect, useState} from 'react';
import {
  Modal,
  VStack,
  FormControl,
  Input,
  Select,
  Button,
  Box,
  Pressable,
  Icon,
  ScrollView,
  HStack,
  Text,
  AlertDialog,
} from 'native-base';
import DateTimePicker from '@react-native-community/datetimepicker';
import {useTheme} from '../../styles/ThemeContext';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import {blackSubtleButtonStyle, orangeOutlineButtonStyle, graySubtleButtonStyle} from '../../styles/buttonStyles';
import {saveFilter, getSavedFilters, SavedFilter, deleteFilter} from '../../database/Filters';
import {ServiceAgreement} from "../../database/ServiceAgreement.ts";

interface FilterComponentProps {
  isOpen: boolean;
  onApply: (filters: any) => void;
  onCancel: () => void;
  onClear: () => void;
  initialFilters: any;
}

const FilterComponent: React.FC<FilterComponentProps> = ({
                                                           isOpen,
                                                           onApply,
                                                           onCancel,
                                                           onClear,
                                                           initialFilters,
                                                         }) => {
  const {mode} = useTheme();

  // Color mode values
  const bgColor = mode === 'dark' ? 'gray.900' : 'white';
  const textColor = mode === 'dark' ? 'white' : 'black';
  const inputBgColor = mode === 'dark' ? 'gray.800' : 'gray.100';
  const labelColor = mode === 'dark' ? 'gray.300' : 'gray.600';

  const [filters, setFilters] = useState({
    dateFrom: new Date(),
    dateTo: new Date(),
    complTargetFrom: new Date(),
    complTargetTo: new Date(),
    ...initialFilters
  });
  const [savedFilters, setSavedFilters] = useState<SavedFilter[]>([]);
  const [selectedFilterId, setSelectedFilterId] = useState<string | null>(null);
  const [showSaveDialog, setShowSaveDialog] = useState(false);
  const [newFilterName, setNewFilterName] = useState('');
  const [serviceAgreements, setServiceAgreements] = useState<ServiceAgreement[]>([]);

  useEffect(() => {
    setFilters(initialFilters);
    loadSavedFilters();
  }, [initialFilters]);

  const loadSavedFilters = async () => {
    try {
      const filters = await getSavedFilters();
      setSavedFilters(filters);
    } catch (error) {
      console.error('Error loading saved filters:', error);
    }
  };

  useEffect(() => {
    const loadServiceAgreements = async () => {
      try {
        const agreements = await ServiceAgreement.getAll(200);
        setServiceAgreements(agreements);
      } catch (error) {
        console.error('Error loading service agreements:', error);
      }
    };

    loadServiceAgreements();
  }, []);


  const [showDatePicker, setShowDatePicker] = useState({
    dateFrom: false,
    dateTo: false,
    complTargetFrom: false,
    complTargetTo: false,
  });

  const handleDateChange = (event, selectedDate, field) => {
    setShowDatePicker({...showDatePicker, [field]: false});
    if (selectedDate) {
      // Set proper time based on field type (same logic as MapMarkerScreen)
      const adjustedDate = new Date(selectedDate);
      if (field === 'dateFrom' || field === 'complTargetFrom') {
        // Start of day: 00:00:00
        adjustedDate.setHours(0, 0, 0, 0);
      } else if (field === 'dateTo' || field === 'complTargetTo') {
        // End of day: 23:59:59
        adjustedDate.setHours(23, 59, 59, 999);
      }
      setFilters({...filters, [field]: adjustedDate});
    }
  };

  const handleSaveFilter = async () => {
    if (newFilterName) {
      try {
        await saveFilter(newFilterName, filters);
        setNewFilterName('');
        setShowSaveDialog(false);
        await loadSavedFilters();
      } catch (error) {
        console.error('Error saving filter:', error);
      }
    }
  };

  const handleDeleteFilter = async () => {
    if (selectedFilterId) {
      try {
        await deleteFilter(Number(selectedFilterId));
        setSelectedFilterId(null);
        await loadSavedFilters();
      } catch (error) {
        console.error('Error deleting filter:', error);
      }
    }
  };

  const renderDatePicker = (fromField, toField, label) => (
    <FormControl mb={1}>
      <FormControl.Label _text={{fontSize: 'xs', color: labelColor}}>{label}</FormControl.Label>
      <HStack space={1}>
        <Pressable flex={1} onPress={() => setShowDatePicker({...showDatePicker, [fromField]: true})}>
          <Input
            value={filters[fromField] ? filters[fromField].toLocaleDateString() : ''}
            isReadOnly={true}
            size="xs"
            height={8}
            bg={inputBgColor}
            color={textColor}
            InputRightElement={
              <Icon as={MaterialCommunityIcons} name="calendar" size={4} mr="1" color={labelColor} />
            }
          />
        </Pressable>
        <Pressable flex={1} onPress={() => setShowDatePicker({...showDatePicker, [toField]: true})}>
          <Input
            value={filters[toField] ? filters[toField].toLocaleDateString() : ''}
            isReadOnly={true}
            size="xs"
            height={8}
            bg={inputBgColor}
            color={textColor}
            InputRightElement={
              <Icon as={MaterialCommunityIcons} name="calendar" size={4} mr="1" color={labelColor} />
            }
          />
        </Pressable>
      </HStack>
      {showDatePicker[fromField] && (
        <DateTimePicker
          value={filters[fromField] || new Date()}
          mode="date"
          display="default"
          onChange={(event, selectedDate) => handleDateChange(event, selectedDate, fromField)}
        />
      )}
      {showDatePicker[toField] && (
        <DateTimePicker
          value={filters[toField] || new Date()}
          mode="date"
          display="default"
          onChange={(event, selectedDate) => handleDateChange(event, selectedDate, toField)}
        />
      )}
    </FormControl>
  );

  const renderFormControl = (label, value, onChangeText, placeholder, isSelect = false, options = []) => (
    <FormControl mb={1}>
      <FormControl.Label _text={{fontSize: 'xs', color: labelColor}}>{label}</FormControl.Label>
      {isSelect ? (
        <Select
          selectedValue={value}
          onValueChange={onChangeText}
          placeholder={placeholder}
          size="xs"
          height={8}
          bg={inputBgColor}
          color={textColor}
        >
          {options.map(option => (
            <Select.Item key={option.value} label={option.label} value={option.value} />
          ))}
        </Select>
      ) : (
        <Input
          value={value}
          onChangeText={onChangeText}
          placeholder={placeholder}
          size="xs"
          height={8}
          bg={inputBgColor}
          color={textColor}
        />
      )}
    </FormControl>
  );

  return (
    <Modal isOpen={isOpen} onClose={onCancel} size="full">
      <Modal.Content maxWidth="400px" bg={bgColor}>
        <Modal.CloseButton />
        <Modal.Header _text={{color: textColor}}>Filters</Modal.Header>
        <Modal.Body>
          <ScrollView showsVerticalScrollIndicator={false}>
            <HStack space={2} alignItems="flex-start">
              <VStack space={0} flex={1}>
                {renderFormControl('Address', filters.streetAddress, (text) => setFilters({...filters, streetAddress: text}), 'Search')}

                {renderFormControl('Log Type', filters.logType, (value) => setFilters({...filters, logType: value}), 'Select', true, [
                  {label: 'Planned', value: 'Planned'},
                  {label: 'Reactive', value: 'Reactive'},
                ])}

                {renderFormControl(
                  'Task Type',
                  filters.taskType,
                  (value) => setFilters({...filters, taskType: value}),
                  'Select',
                  true,
                  serviceAgreements.map(agreement => ({
                    label: `${agreement.code} - ${agreement.description}`,
                    value: agreement.id.toString()
                  }))
                )}

                {renderFormControl('Job', filters.job, (text) => setFilters({...filters, job: text}), 'Search')}
                {renderFormControl('Log No', filters.logNo, (text) => setFilters({...filters, logNo: text}), 'Search')}
                {renderFormControl('Site', filters.site, (text) => setFilters({...filters, site: text}), 'Search')}
              </VStack>

              <VStack space={0} flex={1}>
                {renderDatePicker('dateFrom', 'dateTo', 'Date')}
                {renderFormControl('Status', filters.logStatus, (value) => setFilters({...filters, logStatus: value}), 'Select', true, [
                  {label: 'Open', value: 'Open'},
                  {label: 'In Scheduled', value: 'Scheduled'},
                  {label: 'Allocated', value: 'Allocated'},
                  {label: 'In Progress', value: 'In Progress'},
                  {label: 'Client Approv. Reqd.', value: 'Client Approv. Reqd.'},
                  {label: 'Completed', value: 'Completed'},
                  {label: 'Finalised', value: 'Finalised'},
                  {label: 'Logged', value: 'Logged'},
                ])}
                {renderFormControl('Priority', filters.priority, (value) => setFilters({...filters, priority: value}), 'Select', true, [
                  { label: '2Day : 2 Day Priority', value: '2Day' },
                  { label: '2Month : 2 Month tasks', value: '2Month' },
                  { label: '2Week : 2 Weekly Tasks', value: '2Week' },
                  { label: '3Day : 3 Day Priority', value: '3Day' },
                  { label: '3Month : 3 Monthly Tasks', value: '3Month' },
                  { label: '3Week : 3 Weekly Tasks', value: '3Week' },
                  { label: '4Month : 4 Month Tasks', value: '4Month' },
                  { label: '6Month : 6 Monthly Tasks', value: '6Month' },
                  { label: '6Week : 6 Weekly Tasks', value: '6Week' },
                  { label: 'Daily : Daily Tasks', value: 'Daily' },
                  { label: 'HDCNonUrg : HDC Non Urgent', value: 'HDCNonUrg' },
                  { label: 'HDCR2 : Minor Risk', value: 'HDCR2' },
                  { label: 'HDCR3 : Medium Risk', value: 'HDCR3' },
                  { label: 'HDCR4 : Significant Risk', value: 'HDCR4' },
                  { label: 'HDCR5 : Serious Risk', value: 'HDCR5' },
                  { label: 'HDCRouCy : HDC Routine/CyclicWork', value: 'HDCRouCy' },
                  { label: 'HDCUrg : HDC-Urgent', value: 'HDCUrg' },
                  { label: 'MonthEM : (EOM)-Complete in the month scheduled', value: 'MonthEM' },
                  { label: 'Monthly : 1 Month Tasks', value: 'Monthly' },
                  { label: 'OS P1 : SDC Parks Priority 1 (P1)', value: 'OS P1' },
                  { label: 'OS P2 : SDC Parks Priority 2 (P2)', value: 'OS P2' },
                  { label: 'OS P3 : SDC Parks Priority 3 (P3)', value: 'OS P3' },
                  { label: 'OS P4 : SDC Parks Priority 4 (P4)', value: 'OS P4' },
                  { label: 'OS P5 : SDC Parks Priority 5 (P5)', value: 'OS P5' },
                  { label: 'SP PLAN : C1202 Planned Maintenance', value: 'SP PLAN' },
                  { label: 'WA P5 : SDC Water Priority 5 (P5)', value: 'WA P5' },
                  { label: 'WDCHighP : WDC-High Priority 1', value: 'WDCHighP' },
                  { label: 'WDCProg : WDC-Programmed/Quoted-4', value: 'WDCProg' },
                  { label: 'WDCRouCy : WDC-Routine Cyclic 3', value: 'WDCRouCy' },
                  { label: 'WDCUrgWrk : WDC-Urgent Works 2', value: 'WDCUrgWrk' },
                  { label: 'Weekly : Weekly Tasks', value: 'Weekly' },
                  { label: 'WorkWK : Work Week Daily (Mon-Fri)', value: 'WorkWK' },
                  { label: 'GR2MOWSEA : Grade 2 Mowing Seasonal Addition', value: 'GR2MOWSEA' },
                ])}
                {renderDatePicker('complTargetFrom', 'complTargetTo', 'Target')}
                {renderFormControl('Description', filters.description, (text) => setFilters({...filters, description: text}), 'Search')}
                {renderFormControl('Asset Code', filters.assetCode, (text) => setFilters({...filters, assetCode: text}), 'Search')}
              </VStack>
            </HStack>

            <HStack space={2} mt={4}>
              <Select
                flex={1}
                placeholder="Load saved filter"
                selectedValue={selectedFilterId}
                onValueChange={(itemValue) => {
                  setSelectedFilterId(itemValue);
                  const selectedFilter = savedFilters.find(f => f.id === Number(itemValue));
                  if (selectedFilter) {
                    setFilters(selectedFilter.filter_criteria);
                  }
                }}
              >
                {savedFilters.map(filter => (
                  <Select.Item key={filter.id} label={filter.filter_name} value={filter.id.toString()} />
                ))}
              </Select>
              <Button onPress={() => setShowSaveDialog(true)} size="sm">Save</Button>
              {selectedFilterId && (
                <Button onPress={handleDeleteFilter} size="sm" colorScheme="red">Delete</Button>
              )}
            </HStack>
          </ScrollView>
        </Modal.Body>
        <Modal.Footer>
          <HStack space={2} justifyContent="space-between" width="100%">
            <Button onPress={onCancel} flex={1} {...(mode === 'dark' ? graySubtleButtonStyle(mode) : blackSubtleButtonStyle)} size="sm">Cancel</Button>
            <Button onPress={onClear} flex={1} {...(mode === 'dark' ? graySubtleButtonStyle(mode) : blackSubtleButtonStyle)} size="sm">Clear</Button>
            <Button onPress={() => onApply(filters)} flex={1} {...(mode === 'dark' ? graySubtleButtonStyle(mode) : blackSubtleButtonStyle)} size="sm">Apply</Button>
          </HStack>
        </Modal.Footer>
      </Modal.Content>

      <AlertDialog isOpen={showSaveDialog} onClose={() => setShowSaveDialog(false)} leastDestructiveRef={undefined}>
        <AlertDialog.Content>
          <AlertDialog.CloseButton />
          <AlertDialog.Header>Save Filter</AlertDialog.Header>
          <AlertDialog.Body>
            <Input
              placeholder="Enter filter name"
              value={newFilterName}
              onChangeText={setNewFilterName}
            />
          </AlertDialog.Body>
          <AlertDialog.Footer>
            <Button.Group space={2}>
              <Button
                variant="unstyled"
                colorScheme="coolGray"
                onPress={() => setShowSaveDialog(false)}
              >
                Cancel
              </Button>
              <Button colorScheme="orange" onPress={handleSaveFilter}>
                Save
              </Button>
            </Button.Group>
          </AlertDialog.Footer>
        </AlertDialog.Content>
      </AlertDialog>
    </Modal>
  );
};

export default FilterComponent;
