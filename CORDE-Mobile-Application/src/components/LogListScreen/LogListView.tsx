import React, {useState, memo, useCallback, useMemo, useEffect} from 'react';
import {
  Spinner,
  Text,
  FlatList,
  View,
  HStack,
  Icon,
  IconButton,
  Input,
  Accordion,
  Box,
  Flex,
  VStack,
  Divider,
  useToast,
  Pressable, Checkbox,
} from 'native-base';
import {useLogList} from '../../hooks/useLogList';
import {StyleSheet, RefreshControl} from 'react-native';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import {useTheme} from '../../styles/ThemeContext.tsx';
import {useNavigation} from '@react-navigation/native';
import FilterComponent from './FilterComponent';
import SortingModal from './SortingModal';
import ToastAlert from '../NewLogScreen/ToastAlert.tsx';
import {LogFilter} from '../../types/LogFilterTypes.ts';
import {updateFilterLastUsed} from '../../database/Filters';
import { debounce } from 'lodash';
import {processFiltersForNZ, processDateRangeForNZ} from '../../utils/dateUtils';

const LogItem = memo(({item}) => {
  const navigation = useNavigation();
  const {mode} = useTheme();

  const handlePress = () => {
    navigation.navigate('Log Header', {
      item: {
        ...item,
        contactDetails: item.contact_details,
      },
    });
  };

  return (
    <Accordion>
      <Accordion.Item>
        <Accordion.Summary
          _expanded={{
            backgroundColor: mode === 'dark' ? 'orange.900' : 'orange.400',
          }}>
          <Flex direction="row" align="center" flex={1}>
            <Pressable onPress={handlePress} flex={2}>
              <Text
                underline
                isTruncated={false}
                flexShrink={1}
                color={mode === 'dark' ? 'white' : 'black'}>
                {item.log_header_id}
              </Text>
            </Pressable>
            <Flex flex={8} direction="row" justifyContent="space-between">
              <Text
                isTruncated={false}
                flexShrink={1}
                flex={1}
                color={mode === 'dark' ? 'white' : 'black'}>
                {item.log_type === 'Planned'
                  ? (item.task_code ? `${item.task_code} - ` : '') + item.log_address
                  : item.log_type === 'Reactive'
                    ? (item.description ? `${item.description.replace(/"/g, '')} - ` : '') + item.log_address
                    : item.log_address}
              </Text>
              <Accordion.Icon />
            </Flex>
          </Flex>
        </Accordion.Summary>
        <Accordion.Details
          style={{
            backgroundColor: mode === 'dark' ? '#282828' : '#fff7ed',
          }}>
          <Box>
            <VStack space={2}>
              <HStack space={4}>
                <VStack flex={1}>
                  <Text
                    color={mode === 'dark' ? 'orange.400' : 'orange.700'}
                    fontSize="sm">
                    Dscription
                  </Text>
                  <Text
                    color={mode === 'dark' ? 'white' : 'black'}
                    flexShrink={1}
                    numberOfLines={0}>
                    {item.description?.replace(/"/g, '') || ''}
                  </Text>
                </VStack>
                <VStack flex={1}>
                  <Text
                    color={mode === 'dark' ? 'orange.400' : 'orange.700'}
                    fontSize="sm">
                    Scheduled Date
                  </Text>
                  <Text
                    color={mode === 'dark' ? 'white' : 'black'}
                    flexShrink={1}
                    numberOfLines={0}>
                    {item.schd_date}
                  </Text>
                </VStack>
              </HStack>
              <Divider my="1" />
              <HStack space={4}>
                <VStack flex={1}>
                  <Text
                    color={mode === 'dark' ? 'orange.400' : 'orange.700'}
                    fontSize="sm">
                    Compl. Target Date
                  </Text>
                  <Text
                    color={mode === 'dark' ? 'white' : 'black'}
                    flexShrink={1}
                    numberOfLines={0}>
                    {item.completed_date}
                  </Text>
                </VStack>
                <VStack flex={1}>
                  <Text
                    color={mode === 'dark' ? 'orange.400' : 'orange.700'}
                    fontSize="sm">
                    Priority
                  </Text>
                  <Text
                    color={mode === 'dark' ? 'white' : 'black'}
                    flexShrink={1}
                    numberOfLines={0}>
                    {item.priority}
                  </Text>
                </VStack>
              </HStack>
              <Divider my="1" />
              <HStack space={4}>
                <VStack flex={1}>
                  <Text
                    color={mode === 'dark' ? 'orange.400' : 'orange.700'}
                    fontSize="sm">
                    Client
                  </Text>
                  <Text
                    color={mode === 'dark' ? 'white' : 'black'}
                    flexShrink={1}
                    numberOfLines={0}>
                    {item.company_id}
                  </Text>
                </VStack>
                <VStack flex={1}>
                  <Text
                    color={mode === 'dark' ? 'orange.400' : 'orange.700'}
                    fontSize="sm">
                    Status
                  </Text>
                  <Text
                    color={mode === 'dark' ? 'white' : 'black'}
                    flexShrink={1}
                    numberOfLines={0}>
                    {item.status}
                  </Text>
                </VStack>
              </HStack>
              <Divider my="1" />
              <HStack space={4}>
                <VStack flex={1}>
                  <Text
                    color={mode === 'dark' ? 'orange.400' : 'orange.700'}
                    fontSize="sm">
                    Allocated
                  </Text>
                  <Text
                    color={mode === 'dark' ? 'white' : 'black'}
                    flexShrink={1}
                    numberOfLines={0}>
                    {item.allocation_person_full_name}
                  </Text>
                </VStack>
                <VStack flex={1}>
                  <Text
                    color={mode === 'dark' ? 'orange.400' : 'orange.700'}
                    fontSize="sm">
                    Job Code
                  </Text>
                  <Text
                    color={mode === 'dark' ? 'white' : 'black'}
                    flexShrink={1}
                    numberOfLines={0}>
                    {item.asset_code}
                  </Text>
                </VStack>
              </HStack>
              <Divider my="1" />
              <HStack space={4}>
                <VStack flex={1}>
                  <Text
                    color={mode === 'dark' ? 'orange.400' : 'orange.700'}
                    fontSize="sm">
                    LogType
                  </Text>
                  <Text
                    color={mode === 'dark' ? 'white' : 'black'}
                    flexShrink={1}
                    numberOfLines={0}>
                    {item.log_type}
                  </Text>
                </VStack>
              </HStack>
            </VStack>
          </Box>
        </Accordion.Details>
      </Accordion.Item>
    </Accordion>
  );
}, arePropsEqual);

function arePropsEqual(prevProps, nextProps) {
  return prevProps.item.log_header_id === nextProps.item.log_header_id;
}

const LogListView = () => {
  const {mode} = useTheme();
  const toast = useToast();

  const {
    data,
    loading,
    error,
    fetchMoreData,
    refreshData,
    hasMoreData,
    applyFilters,
    sortBy,
    sortOrder,
    setSortBy,
    setSortOrder,
    applySorting
  } = useLogList();

  const [refreshing, setRefreshing] = useState(false);
  const [showFilterModal, setShowFilterModal] = useState(false);
  const [showSortingModal, setShowSortingModal] = useState(false);
  const [searchKeyword, setSearchKeyword] = useState('');
  const [currentFilters, setCurrentFilters] = useState<LogFilter>({});

  const renderItem = useCallback(({item}) => <LogItem item={item} />, []);
  const [isFilterLoading, setIsFilterLoading] = useState(false);
  const [isSearchLoading, setIsSearchLoading] = useState(false);
  const [selectedLogType, setSelectedLogType] = useState<'All' | 'Reactive' | 'Planned'>('All');


  const [syncOptions, setSyncOptions] = useState({
    useDefaultDateRange: true
  });

  // 在组件顶部定义防抖的搜索函数
  const debouncedSearch = useCallback(
    debounce((text: string, filters: LogFilter, applyFn: Function) => {
      const searchFilters: LogFilter = {
        ...filters,
        searchKeyword: text,
      };

      applyFn(searchFilters).catch(err => {
        console.error('Error applying search filters:', err);
        toast.show({
          render: ({id}) => (
            <ToastAlert
              id={id}
              title="Error"
              description="Failed to search logs. Please try again."
              isClosable={true}
              onClose={toast.close}
              variant="left-accent"
              status="error"
            />
          ),
          placement: 'top',
          duration: 3000,
        });
      });
    }, 500),  // 500ms 延迟
    []
  );

// 修改 handleSearchChange
  const handleSearchChange = useCallback((text: string) => {
    setSearchKeyword(text);
    debouncedSearch(text, currentFilters, applyFilters);
  }, [currentFilters, applyFilters, debouncedSearch]);

  const keyExtractor = useCallback((item, index) => {
    return `${item.log_header_id}-${index}`
      ? `${item.log_header_id}-${index}`
      : `fallback-key-${index}`;
  }, []);

  const handleApplyFilter = useCallback(
    async (filters: LogFilter, filterId?: number) => {
      setShowFilterModal(false);
      try {
        setIsFilterLoading(true);

        // Process dates for New Zealand timezone only for database query (like CreateGroup)
        const processedDateRange = processDateRangeForNZ({
          startDate: filters.dateFrom,
          endDate: filters.dateTo
        });

        // Create query filters with processed dates
        const queryFilters = {
          ...filters,
          dateFrom: processedDateRange.startDate,
          dateTo: processedDateRange.endDate,
          ...(filters.logType === undefined &&
            selectedLogType !== 'All' &&
            { logType: selectedLogType })
        };

        // Set display filters with original dates (not processed)
        const displayFilters = {
          ...filters,
          ...(filters.logType === undefined &&
            selectedLogType !== 'All' &&
            { logType: selectedLogType })
        };

        console.log('Applied filters:', queryFilters);
        setCurrentFilters(displayFilters);  // Save original dates for display

        if (filterId) {
          await updateFilterLastUsed(filterId);
        }

        await applyFilters(queryFilters);
      } catch (err) {
        console.error('Error applying filters:', err);
        toast.show({
          render: ({id}) => (
            <ToastAlert
              id={id}
              title="Error"
              description="Failed to apply filters. Please try again."
              isClosable={true}
              onClose={() => toast.close(id)}
              variant="left-accent"
              status="error"
            />
          ),
          placement: 'top',
          duration: 3000,
        });
      } finally {
        setIsFilterLoading(false);
      }
    },
    [applyFilters, selectedLogType, toast],
  );

  // 在 LogListView.tsx 中，修改 handleApplySorting 函数
  const handleApplySorting = useCallback(
    async (newSortBy: string, newSortOrder: 'asc' | 'desc') => {
      try {
        await applySorting(newSortBy, newSortOrder);
      } catch (err) {
        console.error('Error applying sorting:', err);
        toast.show({
          render: ({id}) => (
            <ToastAlert
              id={id}
              title="Error"
              description="Failed to apply sorting. Please try again."
              isClosable={true}
              onClose={() => toast.close(id)}
              variant="left-accent"
              status="error"
            />
          ),
          placement: 'top',
          duration: 3000,
        });
      }
    },
    [applySorting, toast]
  );

  const handleCancelFilter = useCallback(() => {
    setShowFilterModal(false);
  }, []);

  const handleClearFilter = useCallback(() => {
    const emptyFilters: LogFilter = {
      logStatus: '',
      priority: '',
      logType: '',
      streetAddress: '',
      taskType: '',
      dateFrom: undefined,
      dateTo: undefined,
      job: '',
      complTargetFrom: undefined,
      complTargetTo: undefined,
      logNo: '',
      description: '',
      site: '',
      assetCode: '',
    };
    setCurrentFilters(emptyFilters);
    applyFilters(emptyFilters).catch(err => {
      console.error('Error clearing filters:', err);
      toast.show({
        render: ({id}) => (
          <ToastAlert
            id={id}
            title="Error"
            description="Failed to clear filters. Please try again."
            isClosable={true}
            onClose={toast.close}
            variant="left-accent"
            status="error"
          />
        ),
        placement: 'top',
        duration: 3000,
      });
    });
  }, [applyFilters, toast]);

  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    await refreshData();
    setRefreshing(false);
  }, [refreshData]);

  const handleLoadMore = useCallback(() => {
    if (!loading && hasMoreData) {
      fetchMoreData().catch(err => {
        console.error('Error loading more data:', err);
        toast.show({
          render: ({id}) => (
            <ToastAlert
              id={id}
              title="Error"
              description="Failed to load more logs. Please try again."
              isClosable={true}
              onClose={toast.close}
              variant="left-accent"
              status="error"
            />
          ),
          placement: 'top',
          duration: 3000,
        });
      });
    }
  }, [loading, hasMoreData, fetchMoreData, toast]);

  const handleLogTypeChange = useCallback(
    async (type: 'All' | 'Reactive' | 'Planned') => {
      setSelectedLogType(type);
      try {
        setIsFilterLoading(true);
        // 创建新的过滤器对象，排除旧的 logType
        const { logType: _, ...restFilters } = currentFilters;

        const updatedFilters = {
          ...restFilters,
          // 只在非 All 的情况下添加 logType
          ...(type !== 'All' && { logType: type }),
        };
        setCurrentFilters(updatedFilters);
        await applyFilters(updatedFilters);
      } catch (error) {
        console.error('Error changing log type:', error);
        toast.show({
          render: ({id}) => (
            <ToastAlert
              id={id}
              title="Error"
              description="Failed to change log type. Please try again."
              isClosable={true}
              onClose={() => toast.close(id)}
              variant="left-accent"
              status="error"
            />
          ),
          placement: 'top',
        });
      } finally {
        setIsFilterLoading(false);
      }
    },
    [currentFilters, applyFilters, toast],
  );

  const renderFooter = useCallback(() => {
    if (loading) {
      return <Spinner color="orange.500" />;
    }
    if (!hasMoreData) {
      return (
        <Text textAlign="center" color="gray.500" fontSize="sm" py={2}>
          No more logs to load
        </Text>
      );
    }
    return null;
  }, [loading, hasMoreData]);


  const handleSyncOptionsChange = useCallback(
    async (newSyncOptions) => {
      setSyncOptions(newSyncOptions);
      // 将新的 syncOptions 作为过滤条件的一部分传递给 applyFilters
      await applyFilters({
        ...currentFilters,  // 保持其他过滤条件不变
        syncOptions: newSyncOptions
      });
    },
    [currentFilters, applyFilters]
  );

  const renderSyncCheckbox = () => (
    <HStack flex={2} alignItems="center" space={1}>
      <Checkbox
        value="useDefaultDateRange"
        isChecked={syncOptions.useDefaultDateRange}
        onChange={() => {
          handleSyncOptionsChange({
            ...syncOptions,
            useDefaultDateRange: !syncOptions.useDefaultDateRange
          });
        }}
        accessibilityLabel="Fetch details"
        colorScheme="orange"
      >
        <Text fontSize="xs" color={mode === 'dark' ? 'white' : 'black'}>
          Fast
        </Text>
      </Checkbox>
    </HStack>
  );

  if (error) {
    return (
      <VStack space={2} alignItems="center" justifyContent="center" flex={1}>
        <Text>Error loading logs: {error.message}</Text>
        <IconButton
          icon={<Icon as={MaterialCommunityIcons} name="refresh" />}
          onPress={handleRefresh}
        />
      </VStack>
    );
  }

  return (
    <View style={styles.container}>

      <HStack
        justifyContent="center"
        mb={2}
        bg={mode === 'dark' ? 'gray.800' : 'gray.100'}
        p={1}
        borderRadius="lg"
      >
        <Pressable
          flex={1}
          onPress={() => handleLogTypeChange('All')}
          style={{
            backgroundColor: selectedLogType === 'All'
              ? (mode === 'dark' ? '#FF620A' : '#FF8A3D')
              : 'transparent',
            padding: 8,
            borderRadius: 8,
          }}
        >
          <Text
            textAlign="center"
            color={selectedLogType === 'All' ? 'white' : mode === 'dark' ? 'white' : 'black'}
            fontWeight={selectedLogType === 'All' ? 'bold' : 'normal'}
          >
            All
          </Text>
        </Pressable>
        <Pressable
          flex={1}
          onPress={() => handleLogTypeChange('Reactive')}
          style={{
            backgroundColor: selectedLogType === 'Reactive'
              ? (mode === 'dark' ? '#FF620A' : '#FF8A3D')
              : 'transparent',
            padding: 8,
            borderRadius: 8,
          }}
        >
          <Text
            textAlign="center"
            color={selectedLogType === 'Reactive' ? 'white' : mode === 'dark' ? 'white' : 'black'}
            fontWeight={selectedLogType === 'Reactive' ? 'bold' : 'normal'}
          >
            Reactive
          </Text>
        </Pressable>
        <Pressable
          flex={1}
          onPress={() => handleLogTypeChange('Planned')}
          style={{
            backgroundColor: selectedLogType === 'Planned'
              ? (mode === 'dark' ? '#FF620A' : '#FF8A3D')
              : 'transparent',
            padding: 8,
            borderRadius: 8,
          }}
        >
          <Text
            textAlign="center"
            color={selectedLogType === 'Planned' ? 'white' : mode === 'dark' ? 'white' : 'black'}
            fontWeight={selectedLogType === 'Planned' ? 'bold' : 'normal'}
          >
            Planned
          </Text>
        </Pressable>
      </HStack>

      <HStack marginBottom={2}>
        <IconButton
          flex={2}
          icon={<Icon as={MaterialCommunityIcons} name="filter" />}
          _icon={{
            color: mode === 'dark' ? 'white' : 'amber.500',
            size: '2xl',
          }}
          _pressed={{
            bg: mode === 'dark' ? 'amber.400:alpha.50' : 'amber.400:alpha.20',
            _icon: {
              name: 'filter-outline',
            },
          }}
          onPress={() => setShowFilterModal(true)}
        />
        {isFilterLoading && (
          <Box
            position="absolute"
            top={0}
            left={0}
            right={0}
            bottom={0}
            justifyContent="center"
            alignItems="center"
            bg={mode === "dark" ? "rgba(0,0,0,0.3)" : "rgba(255,255,255,0.3)"}
            zIndex={999}>
            <Spinner color="orange.500" />
          </Box>
        )}
        <Input
          variant="outline"
          placeholder="Search ID or description"
          value={searchKeyword}
          onChangeText={(text) => setSearchKeyword(text)}
          flex={5}  // 调整 flex 比例
          backgroundColor={mode === 'dark' ? 'trueGray.700' : 'trueGray.100'}
          color={mode === 'dark' ? 'white' : 'black'}
        />
        <IconButton
          flex={1}
          icon={<Icon as={MaterialCommunityIcons} name="magnify" />}
          _icon={{
            color: mode === 'dark' ? 'white' : 'amber.500',
            size: 'xl',
          }}
          _pressed={{
            bg: mode === 'dark' ? 'amber.400:alpha.50' : 'amber.400:alpha.20',
          }}
          onPress={async () => {
            setIsSearchLoading(true);
            try {
              const searchFilters: LogFilter = {
                ...currentFilters,
                searchKeyword,
              };
              await applyFilters(searchFilters);
            } catch (err) {
              console.error('Error applying search:', err);
              toast.show({
                render: ({id}) => (
                  <ToastAlert
                    id={id}
                    title="Error"
                    description="Failed to search logs. Please try again."
                    isClosable={true}
                    onClose={() => toast.close(id)}
                    variant="left-accent"
                    status="error"
                  />
                ),
                placement: 'top',
              });
            } finally {
              setIsSearchLoading(false);
            }
          }}
        />
        {renderSyncCheckbox()}
        <IconButton
          flex={2}
          icon={<Icon as={MaterialCommunityIcons} name={sortOrder === 'asc' ? 'sort-ascending' : 'sort-descending'} />}
          _icon={{
            color: mode === 'dark' ? 'white' : 'amber.500',
            size: '2xl',
          }}
          _pressed={{
            bg: mode === 'dark' ? 'amber.400:alpha.50' : 'amber.400:alpha.20',
          }}
          onPress={() => setShowSortingModal(true)}
        />
      </HStack>
      {isSearchLoading && (
        <Box
          position="absolute"
          top={0}
          left={0}
          right={0}
          bottom={0}
          justifyContent="center"
          alignItems="center"
          bg={mode === "dark" ? "rgba(0,0,0,0.3)" : "rgba(255,255,255,0.3)"}
          zIndex={999}
        >
          <Spinner color="orange.500" />
        </Box>
      )}
      <FlatList
        data={data}
        renderItem={renderItem}
        keyExtractor={keyExtractor}
        onEndReached={handleLoadMore}
        onEndReachedThreshold={0.5}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
        }
        ListFooterComponent={renderFooter}
        ListEmptyComponent={() =>
          !loading && <Text textAlign="center">No logs found</Text>
        }
      />
      <FilterComponent
        isOpen={showFilterModal}
        onApply={handleApplyFilter}
        onCancel={handleCancelFilter}
        onClear={handleClearFilter}
        initialFilters={currentFilters}
      />
      <SortingModal
        isOpen={showSortingModal}
        onClose={() => setShowSortingModal(false)}
        onApply={handleApplySorting}
        currentSortBy={sortBy}
        currentSortOrder={sortOrder}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    marginTop: 10,
  },
});

export default LogListView;
