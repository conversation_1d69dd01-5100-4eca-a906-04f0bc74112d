import React from 'react';
import {
  <PERSON><PERSON>,
  Select,
  But<PERSON>,
  VStack,
  HStack,
  Text,
  useColorModeValue,
} from 'native-base';
import { useTheme } from '../../styles/ThemeContext';

const sortOptions = [
  {label: 'Log Header', value: 'log_header_id'},
  {label: 'Scheduled Date', value: 'schd_date'},
  {label: 'Compl. Target Date', value: 'completed_date'},
  {label: 'Description', value: 'description'},
  {label: 'Priority', value: 'priority'},
  {label: 'Status', value: 'status'},
];

interface SortingModalProps {
  isOpen: boolean;
  onClose: () => void;
  onApply: (sortBy: string, sortOrder: 'asc' | 'desc') => void;
  currentSortBy: string;
  currentSortOrder: 'asc' | 'desc';
}

const SortingModal: React.FC<SortingModalProps> = ({
  isOpen,
  onClose,
  onApply,
  currentSortBy,
  currentSortOrder,
}) => {
  const { mode } = useTheme();
  const [sortBy, setSortBy] = React.useState(currentSortBy);
  const [sortOrder, setSortOrder] = React.useState(currentSortOrder);

  const handleApply = () => {
    onApply(sortBy, sortOrder);
    onClose();
  };

  const bgColor = mode === 'dark' ? 'gray.800' : 'white';
  const textColor = mode === 'dark' ? 'white' : 'black';
  const inputBgColor = mode === 'dark' ? 'gray.700' : 'gray.100';
  const labelColor = mode === 'dark' ? 'gray.300' : 'gray.700';
  const modalHeaderBgColor = mode === 'dark' ? 'gray.600' : 'white';
  const modalHeaderTextColor = mode === 'dark' ? 'white' : 'black';

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="full">
      <Modal.Content maxWidth="90%" bg={bgColor}>
        <Modal.CloseButton />
        <Modal.Header bg={modalHeaderBgColor} borderBottomWidth={0}>
          <Text color={modalHeaderTextColor} fontSize="xl" fontWeight="bold">
            Sorting
          </Text>
        </Modal.Header>
        <Modal.Body>
          <VStack space={6}>
            <VStack>
              <Text fontSize="lg" fontWeight="bold" color={labelColor}>
                Sort By
              </Text>
              <Select
                selectedValue={sortBy}
                onValueChange={value => setSortBy(value)}
                size="lg"
                height={12}
                bg={inputBgColor}
                color={textColor}
                fontSize="lg">
                {sortOptions.map(option => (
                  <Select.Item
                    key={option.value}
                    label={option.label}
                    value={option.value}
                  />
                ))}
              </Select>
            </VStack>
            <VStack>
              <Text fontSize="lg" fontWeight="bold" color={labelColor}>
                Order
              </Text>
              <Select
                selectedValue={sortOrder}
                onValueChange={(value: 'asc' | 'desc') => setSortOrder(value)}
                size="lg"
                height={12}
                bg={inputBgColor}
                color={textColor}
                fontSize="lg">
                <Select.Item label="Ascending" value="asc" />
                <Select.Item label="Descending" value="desc" />
              </Select>
            </VStack>
          </VStack>
        </Modal.Body>
        <Modal.Footer bg={modalHeaderBgColor} borderTopWidth={0}>
          <HStack space={4} justifyContent="flex-end">
            <Button
              onPress={onClose}
              size="lg"
              height={12}
              fontSize="lg"
              colorScheme="gray"
              variant="outline"
              _text={{ color: mode === 'dark' ? 'white' : 'black' }}
            >
              Cancel
            </Button>
            <Button
              onPress={handleApply}
              size="lg"
              height={12}
              fontSize="lg"
              colorScheme="orange"
              _text={{ color: 'white' }}
            >
              Apply
            </Button>
          </HStack>
        </Modal.Footer>
      </Modal.Content>
    </Modal>
  );
};

export default SortingModal;
