import React, {useState, useEffect} from 'react';
import {
  <PERSON><PERSON><PERSON>ck,
  Button,
  HStack,
  Text,
  ScrollView,
  Icon,
  Divider,
  Box,
  useToast,
} from 'native-base';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import {useTheme} from '../../styles/ThemeContext';
import {Calendar} from 'react-native-calendars';
import {
  blackSubtleButtonStyle,
  orangeOutlineButtonStyle,
} from '../../styles/buttonStyles.ts';
import {syncLogContainerBackgroundColorStyles} from '../../styles/screenGeneralStyles.ts';
import {networkService} from '../../utils/NetworkUtils';
import SyncService, {DateRange} from '../../services/SyncService';
import {SyncStorageService} from '../../services/SyncStorageService';
import {formatDateToNZ} from '../../utils/dateUtils';
import ToastAlert from '../NewLogScreen/ToastAlert.tsx';
import {useSync} from "../../context/SyncContext.tsx";

const SelectedDateSync = () => {
  const {mode} = useTheme();
  const containerStyle = syncLogContainerBackgroundColorStyles(mode);
  const toast = useToast();

  const [selectedRange, setSelectedRange] = useState({start: null, end: null});
  const { isSyncingSelectedDate, setIsSyncingSelectedDate } = useSync();
  const [lastSyncTime, setLastSyncTime] = useState('Never');
  const [syncStatus, setSyncStatus] = useState('Not Started');
  const [taskCount, setTaskCount] = useState(0);
  const [isError, setIsError] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');

  useEffect(() => {
    loadLastSyncInfo();
  }, []);

  const loadLastSyncInfo = async () => {
    const lastSync = await SyncStorageService.getSelectedDateSyncInfo();
    if (lastSync) {
      setLastSyncTime(formatDateToNZ(lastSync.lastSyncTime));
      setSyncStatus(lastSync.status);
      setTaskCount(lastSync.taskCount);
    }
  };

  // Function to handle date selection and range marking
  const onDayPress = day => {
    if (!selectedRange.start || (selectedRange.start && selectedRange.end)) {
      setSelectedRange({start: day.dateString, end: null});
    } else if (!selectedRange.end) {
      setSelectedRange({...selectedRange, end: day.dateString});
    }
  };

  // Generate marked dates between start and end dates
  const generateMarkedDates = () => {
    const {start, end} = selectedRange;
    const markedDates = {};

    if (start) {
      markedDates[start] = {
        startingDay: true,
        color: 'orange',
        textColor: 'white',
      };
    }

    if (start && end) {
      let currentDate = new Date(start);
      let endDate = new Date(end);

      while (currentDate <= endDate) {
        const dateString = currentDate.toISOString().split('T')[0];
        if (dateString === start) {
          markedDates[dateString] = {
            startingDay: true,
            color: 'orange',
            textColor: 'white',
          };
        } else if (dateString === end) {
          markedDates[dateString] = {
            endingDay: true,
            color: 'orange',
            textColor: 'white',
          };
        } else {
          markedDates[dateString] = {color: 'orange', textColor: 'white'};
        }
        currentDate.setDate(currentDate.getDate() + 1);
      }
    }

    return markedDates;
  };

  const handleSync = async () => {
    if (!selectedRange.start || !selectedRange.end) {
      toast.show({
        render: ({id}) => (
          <ToastAlert
            id={id}
            title="Date Range Required"
            description="Please select both start and end dates."
            isClosable={true}
            onClose={toast.close}
            variant="left-accent"
            status="warning"
          />
        ),
        placement: 'top',
        duration: 3000,
      });
      return;
    }

    setIsSyncingSelectedDate(true);
    try {
      const isConnected = await networkService.isNetworkConnected();
      if (!isConnected) {
        throw new Error('No network connection available.');
      }

      let totalTaskCount = 0;
      const dateRange: DateRange = {
        startDate: selectedRange.start,
        endDate: selectedRange.end,
      };
      const result = await SyncService.syncSelectedDate(dateRange);
      totalTaskCount = result.taskCount;
      const now = new Date().toISOString();
      const formattedNow = formatDateToNZ(now);
      setLastSyncTime(formattedNow);
      setSyncStatus('Success');
      setTaskCount(totalTaskCount);
      toast.show({
        render: ({id}) => (
          <ToastAlert
            id={id}
            title="Sync Completed"
            description="Selected date range has been successfully synchronized."
            isClosable={true}
            onClose={toast.close}
            variant="left-accent"
            status="success"
          />
        ),
        placement: 'top',
      });
      await SyncStorageService.saveSelectedDateSyncInfo({
        lastSyncTime: now,
        status: 'Success',
        taskCount: totalTaskCount,
      });
    } catch (error) {
      setSyncStatus('Failed');
      setIsError(true);
      setErrorMessage(error.message || 'An unknown error occurred');
      toast.show({
        render: ({id}) => (
          <ToastAlert
            id={id}
            title="Sync Failed"
            description={error.message || 'An unknown error occurred.'}
            isClosable={true}
            onClose={toast.close}
            variant="left-accent"
            status="error"
          />
        ),
        placement: 'top',
      });
    } finally {
      setIsSyncingSelectedDate(false);
    }
  };

  return (
    <ScrollView contentContainerStyle={containerStyle.containerBackgroundColor}>
      <Calendar
        markingType={'period'}
        onDayPress={onDayPress}
        markedDates={generateMarkedDates()}
        theme={{
          calendarBackground: 'transparent',
          todayTextColor: 'orange',
          selectedDayBackgroundColor: 'orange',
          selectedDayTextColor: 'white',
          arrowColor: 'orange',
          textDayFontSize: 18,
          textMonthFontSize: 20,
          textDayHeaderFontSize: 16,
          'stylesheet.calendar.header': {
            week: {
              marginTop: 10,
              marginBottom: 10,
              flexDirection: 'row',
              justifyContent: 'space-around',
            },
          },
        }}
      />
      <Button
        leftIcon={
          <Icon as={MaterialCommunityIcons} name="calendar-sync" size="xl" />
        }
        {...(mode === 'dark'
          ? orangeOutlineButtonStyle
          : blackSubtleButtonStyle)}
        marginY="4"
        marginX="4"
        onPress={handleSync}
        isLoading={isSyncingSelectedDate}
        isLoadingText="Syncing..."
        height="60px"
        fontSize="16px">
        Sync Selected Date Range
      </Button>
      <Box marginX="4">
        <HStack space={4} marginBottom="4">
          <VStack flex={1}>
            <HStack alignItems="center" space={2}>
              <Icon
                as={MaterialCommunityIcons}
                name="clock-outline"
                size="md"
                color={mode === 'dark' ? 'trueGray.400' : 'trueGray.500'}
              />
              <Text
                color={mode === 'dark' ? 'trueGray.400' : 'trueGray.500'}
                fontSize="lg"
                fontWeight="bold">
                Last Sync
              </Text>
            </HStack>
            <Text
              color={mode === 'dark' ? 'white' : 'black'}
              fontSize="lg"
              numberOfLines={2}>
              {lastSyncTime}
            </Text>
          </VStack>
          <VStack flex={1}>
            <HStack alignItems="center" space={2}>
              <Icon
                as={MaterialCommunityIcons}
                name="information-outline"
                size="md"
                color={mode === 'dark' ? 'trueGray.400' : 'trueGray.500'}
              />
              <Text
                color={mode === 'dark' ? 'trueGray.400' : 'trueGray.500'}
                fontSize="lg"
                fontWeight="bold">
                Status
              </Text>
            </HStack>
            <Text
              color={mode === 'dark' ? 'white' : 'black'}
              fontSize="lg"
              numberOfLines={2}>
              {syncStatus}
            </Text>
          </VStack>
        </HStack>
        <Divider my="2" thickness="2" />
        <HStack space={4}>
          <VStack flex={1}>
            <HStack alignItems="center" space={2}>
              <Icon
                as={MaterialCommunityIcons}
                name="format-quote-open"
                size="md"
                color={mode === 'dark' ? 'trueGray.400' : 'trueGray.500'}
              />
              <Text
                color={mode === 'dark' ? 'trueGray.400' : 'trueGray.500'}
                fontSize="lg"
                fontWeight="bold">
                Details
              </Text>
            </HStack>
            <Text
              color={mode === 'dark' ? 'white' : 'black'}
              fontSize="lg"
              numberOfLines={3}>
              {isError ? errorMessage : 'Sync completed successfully'}
            </Text>
          </VStack>
          <VStack flex={1}>
            <HStack alignItems="center" space={2}>
              <Icon
                as={MaterialCommunityIcons}
                name="math-norm-box"
                size="md"
                color={mode === 'dark' ? 'trueGray.400' : 'trueGray.500'}
              />
              <Text
                color={mode === 'dark' ? 'trueGray.400' : 'trueGray.500'}
                fontSize="lg"
                fontWeight="bold">
                Task Count
              </Text>
            </HStack>
            <Text
              color={mode === 'dark' ? 'white' : 'black'}
              fontSize="lg"
              numberOfLines={2}>
              {taskCount}
            </Text>
          </VStack>
        </HStack>
      </Box>
    </ScrollView>
  );
};

export default SelectedDateSync;
