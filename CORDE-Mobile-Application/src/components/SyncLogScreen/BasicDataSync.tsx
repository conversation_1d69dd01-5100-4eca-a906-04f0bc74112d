import React, {useState, useEffect} from 'react';
import {
  VStack,
  Box,
  Button,
  HStack,
  Slide,
  WarningIcon,
  Text,
  ScrollView,
  Icon,
  Divider,
  useToast,
} from 'native-base';
import {
  errorIconStyle,
  errorSlideStyle,
  errorTextStyle,
} from '../../styles/slideStyles';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import {useTheme} from '../../styles/ThemeContext';
import {
  blackSubtleButtonStyle,
  orangeOutlineButtonStyle,
} from '../../styles/buttonStyles.ts';
import {logListContainerStyles} from '../../styles/screenGeneralStyles.ts';
import SyncService from '../../services/SyncService';
import {SyncStorageService} from '../../services/SyncStorageService';
import {formatDateToNZ} from '../../utils/dateUtils';
import ToastAlert from "../NewLogScreen/ToastAlert.tsx";
import {useSync} from "../../context/SyncContext.tsx";
import DocumentPicker from 'react-native-document-picker';
import {AssetCoordinates} from '../../database/AssetCoordinates';

const BasicDataSync = () => {
  const {mode} = useTheme();
  const containerStyle = logListContainerStyles(mode);
  const toast = useToast();

  const { isSyncingBasicData, setIsSyncingBasicData } = useSync();
  const [lastSyncTime, setLastSyncTime] = useState('Never');
  const [syncStatus, setSyncStatus] = useState('Not Started');
  const [taskCount, setTaskCount] = useState(0);
  const [isError, setIsError] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');

  // Coordinate import states
  const [isImportingCoordinates, setIsImportingCoordinates] = useState(false);
  const [importProgress, setImportProgress] = useState(0);
  const [importMessage, setImportMessage] = useState('');
  const [coordinateCount, setCoordinateCount] = useState(0);

  useEffect(() => {
    loadLastSyncInfo();
    loadCoordinateCount();
  }, []);

  const loadLastSyncInfo = async () => {
    const lastSync = await SyncStorageService.getBasicSyncInfo();
    if (lastSync) {
      setLastSyncTime(formatDateToNZ(lastSync.lastSyncTime));
      setSyncStatus(lastSync.status);
      setTaskCount(lastSync.taskCount);
    }
  };

  const loadCoordinateCount = async () => {
    try {
      const count = await AssetCoordinates.getCount();
      setCoordinateCount(count);
    } catch (error) {
      console.error('Error loading coordinate count:', error);
    }
  };

  const handleSync = async () => {
    setIsSyncingBasicData(true);
    setIsError(false);
    setSyncStatus('In Progress');

    try {

      await Promise.all([
        SyncService.syncExtensionData(),
        SyncService.syncAssetClassActivities(),
        SyncService.syncServiceAgreements(),
        SyncService.syncStaff()
      ]);

      // Assets data is dependent on the above data
      await SyncService.syncAssets();

      const now = new Date().toISOString();
      const formattedNow = formatDateToNZ(now);
      setLastSyncTime(formattedNow);
      setSyncStatus('Success');
      setTaskCount(5); // 5 tasks completed
      toast.show({
        render: ({id}) => (
          <ToastAlert
            id={id}
            title="Sync Completed"
            description="Basic data has been successfully synchronized."
            isClosable={true}
            onClose={toast.close}
            variant="left-accent"
            status="success"
          />
        ),
        placement: 'top',
      });
      await SyncStorageService.saveBasicSyncInfo({
        lastSyncTime: now,
        status: 'Success',
        taskCount: 5,
      });
    } catch (error) {
      setIsError(true);
      setSyncStatus('Failed');
      setErrorMessage(error.message || 'An unknown error occurred');
      toast.show({
        render: ({id}) => (
          <ToastAlert
            id={id}
            title="Sync Failed"
            description={error.message || 'An unknown error occurred'}
            isClosable={true}
            onClose={toast.close}
            variant="left-accent"
            status="error"
          />
        ),
        placement: 'top',
      });
    } finally {
      setIsSyncingBasicData(false);
    }
  };

  const handleImportCoordinates = async () => {
    try {
      setIsImportingCoordinates(true);
      setImportProgress(0);
      setImportMessage('Selecting Excel file...');

      // Pick Excel file
      const result = await DocumentPicker.pickSingle({
        type: [DocumentPicker.types.xlsx, DocumentPicker.types.xls],
        copyTo: 'documentDirectory',
      });

      if (!result.fileCopyUri) {
        throw new Error('Failed to copy file');
      }

      setImportMessage('Processing Excel file...');

      // Import coordinates
      const importResult = await SyncService.importAssetCoordinates(
        result.fileCopyUri,
        (progress, message) => {
          setImportProgress(progress);
          setImportMessage(message);
        }
      );

      if (importResult.success) {
        await loadCoordinateCount();
        toast.show({
          render: ({id}) => (
            <ToastAlert
              id={id}
              title="Import Completed"
              description={`Successfully imported ${importResult.importedRecords} coordinates out of ${importResult.totalRecords} records`}
              isClosable={true}
              onClose={toast.close}
              variant="left-accent"
              status="success"
            />
          ),
          placement: 'top',
        });
      } else {
        throw new Error(importResult.errorMessage || 'Import failed');
      }

    } catch (error) {
      console.error('Error importing coordinates:', error);

      if (error.code === 'DOCUMENT_PICKER_CANCELED') {
        setImportMessage('Import cancelled');
      } else {
        toast.show({
          render: ({id}) => (
            <ToastAlert
              id={id}
              title="Import Failed"
              description={error.message || 'Failed to import coordinates'}
              isClosable={true}
              onClose={toast.close}
              variant="left-accent"
              status="error"
            />
          ),
          placement: 'top',
        });
      }
    } finally {
      setIsImportingCoordinates(false);
      setImportProgress(0);
      setImportMessage('');
    }
  };

  return (
    <ScrollView contentContainerStyle={containerStyle.containerBackgroundColor}>
      <Button
        onPress={handleSync}
        isLoading={isSyncingBasicData}
        isDisabled={isSyncingBasicData}
        isLoadingText="Syncing..."
        leftIcon={
          <Icon as={MaterialCommunityIcons} name="database-sync" size="xl" />
        }
        {...(mode === 'dark'
          ? orangeOutlineButtonStyle
          : blackSubtleButtonStyle)}
        marginY="4"
        marginX="4"
        height="60px"
        fontSize="16px">
        Sync Basic Data
      </Button>

      <Button
        onPress={handleImportCoordinates}
        isLoading={isImportingCoordinates}
        isDisabled={isImportingCoordinates || isSyncingBasicData}
        isLoadingText={importMessage || "Importing..."}
        leftIcon={
          <Icon as={MaterialCommunityIcons} name="map-marker-plus" size="xl" />
        }
        {...(mode === 'dark'
          ? orangeOutlineButtonStyle
          : blackSubtleButtonStyle)}
        marginY="2"
        marginX="4"
        height="60px"
        fontSize="16px">
        Import Coordinates from Excel
      </Button>
      <Box marginX="4">
        <HStack space={4} marginBottom="4">
          <VStack flex={1}>
            <HStack alignItems="center" space={2}>
              <Icon
                as={MaterialCommunityIcons}
                name="clock-outline"
                size="md"
                color={mode === 'dark' ? 'trueGray.400' : 'trueGray.500'}
              />
              <Text
                color={mode === 'dark' ? 'trueGray.400' : 'trueGray.500'}
                fontSize="lg"
                fontWeight="bold">
                Last Sync
              </Text>
            </HStack>
            <Text
              color={mode === 'dark' ? 'white' : 'black'}
              fontSize="lg"
              numberOfLines={2}>
              {lastSyncTime}
            </Text>
          </VStack>
          <VStack flex={1}>
            <HStack alignItems="center" space={2}>
              <Icon
                as={MaterialCommunityIcons}
                name="information-outline"
                size="md"
                color={mode === 'dark' ? 'trueGray.400' : 'trueGray.500'}
              />
              <Text
                color={mode === 'dark' ? 'trueGray.400' : 'trueGray.500'}
                fontSize="lg"
                fontWeight="bold">
                Status
              </Text>
            </HStack>
            <Text
              color={mode === 'dark' ? 'white' : 'black'}
              fontSize="lg"
              numberOfLines={2}>
              {syncStatus}
            </Text>
          </VStack>
        </HStack>
        <Divider my="3" thickness="2" />
        <HStack space={4}>
          <VStack flex={1}>
            <HStack alignItems="center" space={2}>
              <Icon
                as={MaterialCommunityIcons}
                name="format-quote-open"
                size="md"
                color={mode === 'dark' ? 'trueGray.400' : 'trueGray.500'}
              />
              <Text
                color={mode === 'dark' ? 'trueGray.400' : 'trueGray.500'}
                fontSize="lg"
                fontWeight="bold">
                Details
              </Text>
            </HStack>
            <Text
              color={mode === 'dark' ? 'white' : 'black'}
              fontSize="lg"
              numberOfLines={3}>
              {isError ? errorMessage : 'Sync completed successfully'}
            </Text>
          </VStack>
          <VStack flex={1}>
            <HStack alignItems="center" space={2}>
              <Icon
                as={MaterialCommunityIcons}
                name="math-norm-box"
                size="md"
                color={mode === 'dark' ? 'trueGray.400' : 'trueGray.500'}
              />
              <Text
                color={mode === 'dark' ? 'trueGray.400' : 'trueGray.500'}
                fontSize="lg"
                fontWeight="bold">
                Task Count
              </Text>
            </HStack>
            <Text
              color={mode === 'dark' ? 'white' : 'black'}
              fontSize="lg"
              numberOfLines={2}>
              {taskCount}
            </Text>
          </VStack>
        </HStack>
        <Divider my="3" thickness="2" />
        <HStack space={4}>
          <VStack flex={1}>
            <HStack alignItems="center" space={2}>
              <Icon
                as={MaterialCommunityIcons}
                name="map-marker"
                size="md"
                color={mode === 'dark' ? 'trueGray.400' : 'trueGray.500'}
              />
              <Text
                color={mode === 'dark' ? 'trueGray.400' : 'trueGray.500'}
                fontSize="lg"
                fontWeight="bold">
                Coordinates
              </Text>
            </HStack>
            <Text
              color={mode === 'dark' ? 'white' : 'black'}
              fontSize="lg">
              {coordinateCount} assets with coordinates
            </Text>
          </VStack>
        </HStack>
      </Box>
      <Slide in={isError} placement="top">
        <Box {...errorSlideStyle}>
          <HStack space={3} alignItems="center">
            <WarningIcon {...errorIconStyle} size="lg" />
            <Text {...errorTextStyle} fontSize="lg">
              {errorMessage}
            </Text>
          </HStack>
        </Box>
      </Slide>
    </ScrollView>
  );
};

export default BasicDataSync;
