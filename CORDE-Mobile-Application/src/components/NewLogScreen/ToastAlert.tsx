import React from 'react';
import { <PERSON><PERSON>, VStack, <PERSON><PERSON><PERSON><PERSON>, Text, IconButton, CloseIcon } from 'native-base';

interface ToastAlertProps {
  id: string;
  title: string;
  description: string;
  isClosable?: boolean;
  onClose: (id: string) => void;
  variant?: 'left-accent' | 'top-accent' | 'outline' | 'subtle' | 'solid';
  status?: 'info' | 'warning' | 'success' | 'error' | 'loading';
}

const ToastAlert: React.FC<ToastAlertProps> = ({
  id,
  title,
  description,
  isClosable = false,
  onClose,
  variant = 'left-accent',
  status = 'info'
}) => (
  <Alert maxWidth="100%" alignSelf="center" flexDirection="row" status={status} variant={variant}>
    <VStack space={2} flexShrink={1} w="100%">
      <HStack flexShrink={1} alignItems="center" justifyContent="space-between">
        <HStack space={3} flexShrink={1} alignItems="center">
          <Alert.Icon size="lg" />
          <Text fontSize="xl" fontWeight="bold" flexShrink={1}>
            {title}
          </Text>
        </HStack>
        {isClosable ? (
          <IconButton
            variant="unstyled"
            icon={<CloseIcon size="lg" />}
            onPress={() => onClose(id)}
          />
        ) : null}
      </HStack>
      <Text px="6" fontSize="lg">
        {description}
      </Text>
    </VStack>
  </Alert>
);

export default ToastAlert;
