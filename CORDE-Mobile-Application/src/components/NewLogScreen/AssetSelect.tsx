import React, {useState, useEffect, useRef} from 'react';
import {
  Dimensions,
  Modal,
  FlatList,
  TouchableWithoutFeedback,
} from 'react-native';
import {
  Input,
  Box,
  Text,
  HStack,
  VStack,
  IconButton,
  Pressable,
  useToast,
} from 'native-base';
import {Assets} from '../../database/Assets';
import {useTheme} from '../../styles/ThemeContext';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { Gesture, GestureDetector, GestureHandlerRootView } from 'react-native-gesture-handler';
import ToastAlert from "./ToastAlert.tsx";

const windowWidth = Dimensions.get('window').width;
const windowHeight = Dimensions.get('window').height;

interface AssetSelectProps {
  onSelect: (asset: Assets) => void;
  reset: number;
  initialAssetId?: number;
}

export const AssetSelect: React.FC<AssetSelectProps> = ({
  onSelect,
  reset,
  initialAssetId,
}) => {
  const [assets, setAssets] = useState<Assets[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [isOpen, setIsOpen] = useState(false);
  const {mode} = useTheme();
  const inputRef = useRef(null);
  const toast = useToast();

  useEffect(() => {
    if (initialAssetId) {
      loadInitialAsset();
    }
  }, [initialAssetId]);

  useEffect(() => {
    loadAssets(); // Now called every time searchQuery changes.
  }, [searchQuery]);

  useEffect(() => {
    setSearchQuery('');
    setIsOpen(false);
  }, [reset]);

  const loadInitialAsset = async () => {
    try {
      const asset = await Assets.getById(initialAssetId);
      if (asset) {
        setAssets([asset]);
        setSearchQuery(`${asset.asset_code} - ${asset.description}`);
      }
    } catch (error) {
      console.error('Error loading initial asset:', error);
      toast.show({
        render: ({id}) => (
          <ToastAlert
            id={id}
            title="Error"
            description="Failed to load initial asset. Please try again."
            isClosable={true}
            onClose={() => toast.close(id)}
            variant="left-accent"
            status="error"
          />
        ),
        placement: 'top',
      });
    }
  };

  const loadAssets = async () => {
    try {
      let results: Assets[];
      if (searchQuery.trim() === '') {
        results = await Assets.getAll(10);
      } else {
        results = await Assets.search(searchQuery, 10);
      }
      setAssets(results);
    } catch (error) {
      console.error('Error loading assets:', error);
      toast.show({
        render: ({id}) => (
          <ToastAlert
            id={id}
            title="Error"
            description="Failed to load assets. Please try again."
            isClosable={true}
            onClose={() => toast.close(id)}
            variant="left-accent"
            status="error"
          />
        ),
        placement: 'top',
      });
    }
  };

  const handleSelect = (asset: Assets) => {
    onSelect(asset);
    setSearchQuery(`${asset.asset_code} - ${asset.description}`);
    setIsOpen(false);
  };

  const renderItem = ({item}: {item: Assets}) => {
    const tap = Gesture.Tap()
      .onStart(() => {
        handleSelect(item);
      });

    return (
      <GestureDetector gesture={tap}>
        <HStack py={0.5} px={1} alignItems="center" space={0}>
          <Text
            isTruncated={false}
            flexShrink={1}
            fontSize="xs"
            color={mode === 'dark' ? 'white' : 'black'}
            flex={3}
            marginRight={2}>
            {item.asset_code}
          </Text>
          <Text
            isTruncated={false}
            flexShrink={1}
            fontSize="xs"
            color={mode === 'dark' ? 'gray.400' : 'gray.500'}
            flex={7}>
            {item.description}
          </Text>
        </HStack>
      </GestureDetector>
    );
  };

  const toggleDropdown = () => {
    setIsOpen(!isOpen);
    if (!isOpen && searchQuery.trim() === '') {
      loadAssets();
    }
  };

  return (
    <Box>
      <Pressable onPress={toggleDropdown} flex={1}>
        <Input
          variant="filled"
          ref={inputRef}
          value={searchQuery}
          placeholder="Select or search for asset"
          color={mode === 'dark' ? 'white' : 'black'}
          backgroundColor={mode === 'dark' ? 'gray.700' : 'gray.100'}
          readOnly={true}
          fontSize="xs"
          height={10}
          _focus={{
            backgroundColor: mode === 'dark' ? 'gray.600' : 'white',
            borderColor: 'orange.500',
          }}
          InputRightElement={
            <IconButton
              icon={
                <Icon
                  name={isOpen ? 'menu-up' : 'menu-down'}
                  size={20}
                  color={mode === 'dark' ? 'white' : 'black'}
                />
              }
              onPress={toggleDropdown}
              _pressed={{bg: mode === 'dark' ? 'gray.600' : 'orange.100'}}
            />
          }
        />
      </Pressable>

      <Modal
        visible={isOpen}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setIsOpen(false)}>
        <GestureHandlerRootView style={{ flex: 1 }}>
          <TouchableWithoutFeedback onPress={() => setIsOpen(false)}>
          <Box
            flex={1}
            bg="rgba(0,0,0,0.5)"
            justifyContent="center"
            alignItems="center">
            <TouchableWithoutFeedback>
              <VStack
                width={windowWidth - 40}
                maxHeight={windowHeight * 0.8}
                bg={mode === 'dark' ? 'trueGray.800' : 'white'}
                borderRadius="md"
                shadow={2}
                p={4}>
                <Input
                  value={searchQuery}
                  onChangeText={text => {
                    setSearchQuery(text);
                  }}
                  placeholder="Search by code or description"
                  backgroundColor={
                    mode === 'dark' ? 'trueGray.700' : 'orange.50'
                  }
                  color={mode === 'dark' ? 'white' : 'black'}
                  mb={2}
                  fontSize="sm"
                  height={10}
                  InputRightElement={
                    <IconButton
                      icon={
                        <Icon
                          name="broom"
                          size={20}
                          color={mode === 'dark' ? 'white' : 'black'}
                        />
                      }
                      onPress={() => setSearchQuery('')}
                      _pressed={{
                        bg: mode === 'dark' ? 'trueGray.700' : 'orange.50',
                      }}
                    />
                  }
                />
                <FlatList
                  data={assets}
                  renderItem={renderItem}
                  keyExtractor={item => item.asset_id.toString()}
                  onScroll={e => e.stopPropagation()}
                />
              </VStack>
            </TouchableWithoutFeedback>
          </Box>
        </TouchableWithoutFeedback>
        </GestureHandlerRootView>
      </Modal>
    </Box>
  );
};
