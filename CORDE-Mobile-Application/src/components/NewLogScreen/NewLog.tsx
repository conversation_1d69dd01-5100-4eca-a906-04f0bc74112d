import React, {useState, useEffect, useMemo} from 'react';
import {
  Input,
  VStack,
  FormControl,
  TextArea,
  Button,
  HStack,
  ScrollView,
  Icon,
  useToast,
} from 'native-base';
import {
  graySubtleButtonStyle,
  orangeSubtleButtonStyle,
} from '../../styles/buttonStyles';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import {useTheme} from '../../styles/ThemeContext';
import {ServiceAgreementSelect} from './ServiceAgreementSelect';
import {ServiceAgreement} from '../../database/ServiceAgreement';
import {AssetSelect} from './AssetSelect';
import {Assets} from '../../database/Assets';
import {formatCurrentDateTime} from '../../utils/dateUtils';
import {NewLogModel} from '../../models/NewLogModel.ts';
import {LogListService} from '../../services/LogListService.ts';
import {StaffSelect} from './StaffSelect';
import {Staff} from '../../database/Staff';
import {useNavigation} from '@react-navigation/native';
import FileUploadComponent from './FileUploadComponent.tsx';
import ToastAlert from './ToastAlert';
import {useAuth} from '../../context/AuthContext.tsx';

const NewLog: React.FC = () => {
  const {mode} = useTheme();
  const createButtonStyle = orangeSubtleButtonStyle(mode);
  const cancelButtonStyle = graySubtleButtonStyle(mode);
  const navigation = useNavigation();
  const toast = useToast();

  const [orderNo, setOrderNo] = useState('');
  const [description, setDescription] = useState('');
  const [selectedStaff, setSelectedStaff] = useState<Staff | null>(null);
  const [isDefaultSet, setIsDefaultSet] = useState(false);
  const [selectedAgreement, setSelectedAgreement] =
    useState<ServiceAgreement | null>(null);
  const [selectedAsset, setSelectedAsset] = useState<Assets | null>(null);
  const [uploadedFiles, setUploadedFiles] = useState([]);
  const [isCreating, setIsCreating] = useState(false);
  const [selectedFiles, setSelectedFiles] = useState<any[]>([]);
  const {user} = useAuth();

  useEffect(() => {
    setOrderNo(formatCurrentDateTime());
  }, []);

  // Use useMemo to optimise performance by only recalculating when the user changes
  const defaultStaff = useMemo(() => {
    if (user && user.person_id) {
      return {person_id: user.person_id, full_name: user.full_name} as Staff;
    }
    return null;
  }, [user]);

  useEffect(() => {
    if (defaultStaff && !isDefaultSet) {
      setSelectedStaff(defaultStaff);
      setIsDefaultSet(true);
    }
  }, [defaultStaff, isDefaultSet]);

  const [resetKey, setResetKey] = useState(0);
  const resetForm = () => {
    setOrderNo(formatCurrentDateTime());
    setDescription('');
    setSelectedStaff(null);
    setSelectedAgreement(null);
    setSelectedAsset(null);
    setSelectedFiles([]);
    setUploadedFiles([]);
    setResetKey(prevKey => prevKey + 1);
  };

  const handleFilesSelected = (files: any[]) => {
    setSelectedFiles(files);
  };

  const handleStaffSelect = (staff: Staff | null) => {
    setSelectedStaff(staff);
  };

  const handleCreate = async () => {
    if (!selectedAgreement) {
      toast.show({
        render: ({id}) => (
          <ToastAlert
            id={id}
            title="Validation Error"
            description="Please select a Service Agreement."
            isClosable={true}
            onClose={() => toast.close(id)}
            variant="left-accent"
            status="error"
          />
        ),
        placement: 'top',
      });
      return;
    }

    setIsCreating(true);

    const newLogData: NewLogModel = {
      serviceAgreementID: selectedAgreement.id,
      serviceAgreementCode: selectedAgreement.code,
      orderNo: orderNo,
      description: description,
      allocatedPersonId: selectedStaff?.person_id,
      assetID: selectedAsset?.asset_id,
    };

    try {
      await LogListService.createNewLog(newLogData, selectedFiles);
      toast.show({
        render: ({id}) => (
          <ToastAlert
            id={id}
            title="Success"
            description="New log created successfully."
            isClosable={true}
            onClose={() => toast.close(id)}
            variant="left-accent"
            status="success"
          />
        ),
        placement: 'top',
        duration: 3000,
      });
      resetForm();
    } catch (error) {
      console.error('Error creating new log:', error);
      toast.show({
        render: ({id}) => (
          <ToastAlert
            id={id}
            title="Error"
            description="Error creating new log."
            isClosable={true}
            onClose={() => toast.close(id)}
            variant="left-accent"
            status="error"
          />
        ),
        placement: 'top',
      });
    } finally {
      setIsCreating(false);
    }
  };

  const handleCancel = () => {
    navigation.goBack();
  };

  return (
    <ScrollView>
      <VStack space={1}>
        <FormControl isRequired={true}>
          <FormControl.Label _text={{fontSize: 'sm', fontWeight: 'bold'}}>
            Service Agreement
          </FormControl.Label>
          <ServiceAgreementSelect
            onSelect={agreement => setSelectedAgreement(agreement)}
            reset={resetKey}
          />
        </FormControl>
        <FormControl isRequired={true} isInvalid={false}>
          <FormControl.Label _text={{fontSize: 'sm', fontWeight: 'bold'}}>
            Order No
          </FormControl.Label>
          <Input
            value={orderNo}
            onChangeText={setOrderNo}
            color={mode === 'dark' ? 'white' : 'black'}
            backgroundColor={mode === 'dark' ? 'gray.700' : 'gray.100'}
            variant="filled"
            fontSize="sm"
            height={10}
            _focus={{
              backgroundColor: mode === 'dark' ? 'gray.600' : 'white',
              borderColor: 'orange.500',
            }}
          />
        </FormControl>
        <FormControl isRequired={false} isInvalid={false}>
          <FormControl.Label _text={{fontSize: 'sm', fontWeight: 'bold'}}>
            Asset
          </FormControl.Label>
          <AssetSelect
            onSelect={asset => setSelectedAsset(asset)}
            reset={resetKey}
          />
        </FormControl>
        <FormControl isRequired={true} isInvalid={false}>
          <FormControl.Label _text={{fontSize: 'sm', fontWeight: 'bold'}}>
            Allocate To
          </FormControl.Label>
          <StaffSelect
            onSelect={handleStaffSelect}
            reset={resetKey}
            defaultStaff={defaultStaff}
            selectedStaff={selectedStaff}
          />
        </FormControl>
        <FormControl isRequired={true}>
          <FormControl.Label _text={{fontSize: 'sm', fontWeight: 'bold'}}>
            Description
          </FormControl.Label>
          <TextArea
            value={description}
            onChangeText={setDescription}
            autoCompleteType={undefined}
            color={mode === 'dark' ? 'white' : 'black'}
            fontSize="sm"
            height={10}
          />
        </FormControl>
        <FileUploadComponent onFilesSelected={handleFilesSelected} />
      </VStack>
      <HStack justifyContent="space-between" space={1} mt={2}>
        <Button
          flex={1}
          {...cancelButtonStyle}
          leftIcon={
            <Icon as={MaterialCommunityIcons} name="cancel" size="sm" />
          }
          onPress={handleCancel}
          height={10}
          fontSize="sm">
          Cancel
        </Button>
        <Button
          onPress={handleCreate}
          flex={1}
          {...createButtonStyle}
          leftIcon={<Icon as={MaterialCommunityIcons} name="plus" size="sm" />}
          isDisabled={isCreating}
          height={10}
          fontSize="sm">
          {isCreating ? 'Creating...' : 'Create'}
        </Button>
      </HStack>
    </ScrollView>
  );
};

export default NewLog;
