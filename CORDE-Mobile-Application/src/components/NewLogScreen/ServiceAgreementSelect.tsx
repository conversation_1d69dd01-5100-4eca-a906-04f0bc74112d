import React, {useState, useEffect, useRef} from 'react';
import {
  Dimensions,
  Modal,
  FlatList,
  TouchableWithoutFeedback,
} from 'react-native';
import {
  Input,
  Box,
  Text,
  VStack,
  IconButton,
  Pressable,
  Flex,
  HStack,
} from 'native-base';
import {ServiceAgreement} from '../../database/ServiceAgreement';
import {useTheme} from '../../styles/ThemeContext';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import {Gesture, GestureDetector, GestureHandlerRootView} from 'react-native-gesture-handler';

const windowWidth = Dimensions.get('window').width;
const windowHeight = Dimensions.get('window').height;

interface ServiceAgreementSelectProps {
  onSelect: (agreement: ServiceAgreement | null) => void;
  reset: number;
}

export const ServiceAgreementSelect: React.FC<ServiceAgreementSelectProps> = ({
  onSelect,
  reset,
}) => {
  const [agreements, setAgreements] = useState<ServiceAgreement[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [isOpen, setIsOpen] = useState(false);
  const {mode} = useTheme();
  const inputRef = useRef(null);

  useEffect(() => {
    loadAgreements();
  }, [searchQuery]);

  useEffect(() => {
    setSearchQuery('');
    setIsOpen(false);
  }, [reset]);

  const loadAgreements = async () => {
    let results: ServiceAgreement[];
    if (searchQuery.trim() === '') {
      results = await ServiceAgreement.getAll(200);
    } else {
      results = await ServiceAgreement.search(searchQuery, 200);
    }
    setAgreements(results);
  };

  const handleSelect = (agreement: ServiceAgreement) => {
    onSelect(agreement);
    setSearchQuery(`${agreement.code} - ${agreement.description}`);
    setIsOpen(false);
  };

  const renderItem = ({item}: {item: ServiceAgreement}) => {
    const tap = Gesture.Tap()
      .onStart(() => {
        handleSelect(item);
      });

    return (
      <GestureDetector gesture={tap}>
        <HStack py={0.5} px={1} alignItems="center" space={0}>
          <Text
            isTruncated={false}
            flexShrink={1}
            flex={6}
            marginRight={1}
            fontSize="xs"
            color={mode === 'dark' ? 'white' : 'black'}>
            {item.code}
          </Text>
          <Flex flex={5} direction="row" justifyContent="space-between">
            <Text
              isTruncated={false}
              flexShrink={1}
              flex={4}
              fontSize="xs"
              color={mode === 'dark' ? 'gray.400' : 'gray.500'}>
              {item.description}{item.id}
            </Text>
          </Flex>
        </HStack>
      </GestureDetector>
    );
  };

  const toggleDropdown = () => {
    setIsOpen(!isOpen);
    if (!isOpen) {
      loadAgreements();
    }
  };

  return (
    <Box>
      <Pressable onPress={toggleDropdown} flex={1}>
        <Input
          variant="filled"
          ref={inputRef}
          value={searchQuery}
          placeholder="Select or search for service agreement"
          color={mode === 'dark' ? 'white' : 'black'}
          backgroundColor={mode === 'dark' ? 'gray.700' : 'gray.100'}
          readOnly={true}
          fontSize="xs"
          height={10}
          _focus={{
            backgroundColor: mode === 'dark' ? 'gray.600' : 'white',
            borderColor: 'orange.500',
          }}
          InputRightElement={
            <IconButton
              icon={
                <Icon
                  name={isOpen ? 'menu-up' : 'menu-down'}
                  size={20}
                  color={mode === 'dark' ? 'white' : 'black'}
                />
              }
              onPress={toggleDropdown}
              _pressed={{bg: mode === 'dark' ? 'gray.600' : 'orange.100'}}
            />
          }
        />
      </Pressable>
      <Modal
        visible={isOpen}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setIsOpen(false)}>
        <GestureHandlerRootView style={{ flex: 1 }}>
          <TouchableWithoutFeedback
            onPress={() => {
              setIsOpen(false);
            }}
          >
            <Box
              flex={1}
              bg="rgba(0,0,0,0.5)"
              justifyContent="center"
              alignItems="center">
              <TouchableWithoutFeedback
                onPress={(e) => {
                  e.stopPropagation();
                }}>
                <VStack
                  width={windowWidth - 40}
                  maxHeight={windowHeight * 0.8}
                  bg={mode === 'dark' ? 'trueGray.800' : 'white'}
                  borderRadius="md"
                  shadow={2}
                  p={4}>
                  <Input
                    value={searchQuery}
                    onChangeText={setSearchQuery}
                    placeholder="Search by code or description"
                    backgroundColor={
                      mode === 'dark' ? 'trueGray.700' : 'orange.50'
                    }
                    color={mode === 'dark' ? 'white' : 'black'}
                    mb={2}
                    fontSize="sm"
                    height={10}
                    InputRightElement={
                      <IconButton
                        icon={
                          <Icon
                            name="broom"
                            size={20}
                            color={mode === 'dark' ? 'white' : 'black'}
                          />
                        }
                        onPress={() => setSearchQuery('')}
                        _pressed={{
                          bg: mode === 'dark' ? 'trueGray.700' : 'orange.50',
                        }}
                      />
                    }
                  />
                  <FlatList
                    data={agreements}
                    renderItem={renderItem}
                    keyExtractor={item => item.id.toString()}
                    onScroll={e => e.stopPropagation()}
                  />
                </VStack>
              </TouchableWithoutFeedback>
            </Box>
          </TouchableWithoutFeedback>
        </GestureHandlerRootView>
      </Modal>
    </Box>
  );
};
