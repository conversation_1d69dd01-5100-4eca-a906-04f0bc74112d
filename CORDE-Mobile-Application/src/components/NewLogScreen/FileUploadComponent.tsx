import React, {useState, useEffect} from 'react';
import {
  View,
  Image,
  TouchableOpacity,
  FlatList,
  Platform,
  PermissionsAndroid,
} from 'react-native';
import {Button, Icon, HStack, VStack, Text, useToast} from 'native-base';
import {
  launchCamera,
  launchImageLibrary,
  CameraOptions,
  ImageLibraryOptions,
} from 'react-native-image-picker';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import {useTheme} from '../../styles/ThemeContext.tsx';
import {blueSubtleButtonStyle, choosePhotoButtonStyle, takePhotoButtonStyle} from '../../styles/buttonStyles.ts';
import {StoredFiles} from '../../database/StoredFiles';
import ToastAlert from './ToastAlert';

interface FileUploadComponentProps {
  localLogId?: number;
  onFilesSelected: (files: any[]) => void;
  initialFiles?: any[];
}

const FileUploadComponent: React.FC<FileUploadComponentProps> = ({
  localLogId,
  onFilesSelected,
  initialFiles = [],
}) => {
  const [files, setFiles] = useState<any[]>(initialFiles);
  const {mode} = useTheme();
  const toast = useToast();
  const uploadButtonStyle = blueSubtleButtonStyle(mode);
  const takePhotoButton= takePhotoButtonStyle(mode);
  const choosePhotoButton= choosePhotoButtonStyle(mode);

  useEffect(() => {
    if (localLogId) {
      loadExistingFiles(localLogId);
    }
  }, [localLogId]);

  const loadExistingFiles = async localLogId => {
    try {
      const existingFiles = await StoredFiles.getByLocalLogId(localLogId);
      const formattedFiles = existingFiles.map(file => ({
        name: file.file_name,
        type: file.content_type,
        uri: `file://${file.file_path}`,
        id: file.id,
      }));
      setFiles(formattedFiles);
      onFilesSelected(formattedFiles);
    } catch (error) {
      console.error('Error loading existing files:', error);
    }
  };

  const requestCameraPermission = async () => {
    if (Platform.OS === 'android') {
      try {
        const granted = await PermissionsAndroid.request(
          PermissionsAndroid.PERMISSIONS.CAMERA,
          {
            title: 'App Camera Permission',
            message: 'App needs access to your camera',
            buttonNeutral: 'Ask Me Later',
            buttonNegative: 'Cancel',
            buttonPositive: 'OK',
          },
        );
        return granted === PermissionsAndroid.RESULTS.GRANTED;
      } catch (err) {
        console.warn(err);
        return false;
      }
    }
    return true;
  };

  const handleImagePicker = async (type: 'camera' | 'library') => {
    if (type === 'camera') {
      const hasPermission = await requestCameraPermission();
      if (!hasPermission) {
        console.log('Camera permission denied');
        return;
      }
    }

    const options: CameraOptions | ImageLibraryOptions = {
      mediaType: 'photo',
      includeBase64: false,
      maxHeight: 2000,
      maxWidth: 2000,
    };

    const launch = type === 'camera' ? launchCamera : launchImageLibrary;

    launch(options, response => {
      if (response.didCancel) {
        console.log('User cancelled image picker');
      } else if (response.errorMessage) {
        console.log('ImagePicker Error: ', response.errorMessage);
      } else if (response.assets && response.assets.length > 0) {
        const newFiles = response.assets.map(asset => ({
          name: asset.fileName,
          type: asset.type,
          uri: asset.uri,
        }));

        // Filter newFiles to exclude any files that already exist in the files array
        const uniqueFiles = newFiles.filter(newFile =>
          !files.some(file => file.name === newFile.name));

        const duplicates = newFiles.length !== uniqueFiles.length;

        if (duplicates) {
          toast.show({
            render: ({ id }) => (
              <ToastAlert
                id={id}
                title="Duplicate Photo"
                description="You've already selected one or more of these photos."
                isClosable={true}
                onClose={toast.close}
              />
            ),
            placement: 'top',
            duration: 3000,
          });
        }

        const updatedFiles = [...files, ...uniqueFiles];
        setFiles(updatedFiles);
        onFilesSelected(updatedFiles);

        // const updatedFiles = [...files, ...newFiles];
        // setFiles(updatedFiles);
        // onFilesSelected(updatedFiles);
      }
    });
  };

  const removeFile = (index: number) => {
    const newFiles = files.filter((_, i) => i !== index);
    setFiles(newFiles);
    onFilesSelected(newFiles);
  };

  const renderFileItem = ({item, index}: {item: any; index: number}) => (
    <View style={{marginRight: 20, marginBottom: 20}}>
      <Image source={{uri: item.uri}} style={{width: 80, height: 80, borderRadius: 8}} />
      <TouchableOpacity
        onPress={() => removeFile(index)}
        style={{
          position: 'absolute',
          top: 5,
          right: 5,
          backgroundColor: 'rgba(255,255,255,0.8)',
          borderRadius: 15,
          padding: 5,
        }}>
        <Icon
          as={MaterialCommunityIcons}
          name="close-circle"
          size="sm"
          color="red.500"
        />
      </TouchableOpacity>
      <Text style={{width: 100, fontSize: 12, marginTop: 5, color: mode === 'dark' ? 'white' : 'black'}} numberOfLines={1}>
        {item.name}
      </Text>
    </View>
  );

  return (
    <VStack space={0}>
      <HStack space={1} justifyContent="space-between" my={4}>
        <Button
          flex={1}
          onPress={() => handleImagePicker('camera')}
          leftIcon={<Icon as={MaterialCommunityIcons} name="camera" size="sm" />}
          {...takePhotoButton}
          height={10}
          fontSize="sm"
        >
          Take Photo
        </Button>
        <Button
          flex={1}
          onPress={() => handleImagePicker('library')}
          leftIcon={<Icon as={MaterialCommunityIcons} name="image" size="sm" />}
          {...choosePhotoButton}
          height={10}
          fontSize="sm"
        >
          Choose Photo
        </Button>
      </HStack>
      {files.length > 0 && (
        <FlatList
          data={files}
          renderItem={renderFileItem}
          keyExtractor={(item, index) => index.toString()}
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={{paddingVertical: 1}}
        />
      )}
    </VStack>
  );
};

export default FileUploadComponent;
