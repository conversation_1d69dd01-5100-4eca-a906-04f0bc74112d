import React, {useState, useEffect, useRef} from 'react';
import {
  Dimensions,
  Modal,
  FlatList,
  TouchableWithoutFeedback,
} from 'react-native';
import {
  Input,
  Box,
  Text,
  HStack,
  VStack,
  IconButton,
  Pressable,
} from 'native-base';
import {Staff} from '../../database/Staff';
import {useTheme} from '../../styles/ThemeContext';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { Gesture, GestureDetector, GestureHandlerRootView } from 'react-native-gesture-handler';

const windowWidth = Dimensions.get('window').width;
const windowHeight = Dimensions.get('window').height;

interface StaffSelectProps {
  onSelect: (staff: Staff) => void;
  reset: number;
  defaultStaff?: Staff | null;
  selectedStaff: Staff | null;
}

export const StaffSelect: React.FC<StaffSelectProps> = ({
  onSelect,
  reset,
  defaultStaff,
  selectedStaff,
}) => {
  const [staffList, setStaffList] = useState<Staff[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [isOpen, setIsOpen] = useState(false);
  const {mode} = useTheme();
  const inputRef = useRef(null);

  useEffect(() => {
    if (selectedStaff) {
      setSearchQuery(`${selectedStaff.full_name} (${selectedStaff.person_id})`);
    } else {
      setSearchQuery('');
    }
  }, [selectedStaff]);

  useEffect(() => {
    loadStaff();
  }, [searchQuery]);

  useEffect(() => {
    setSearchQuery('');
    setIsOpen(false);
  }, [reset]);

  const loadStaff = async () => {
    let results: Staff[];
    if (searchQuery.trim() === '') {
      results = await Staff.getAll(10);
    } else {
      results = await Staff.search(searchQuery, 10);
    }
    setStaffList(results);
  };

  const handleSelect = (staff: Staff) => {
    onSelect(staff);
    setSearchQuery(`${staff.full_name} (${staff.person_id})`);
    setIsOpen(false);
  };

  const renderItem = ({item}: {item: Staff}) => {
    const tap = Gesture.Tap()
      .onStart(() => {
        handleSelect(item);
      });

    return (
      <GestureDetector gesture={tap}>
        <HStack py={0.5} px={2} alignItems="center" space={0}>
          <Text
            fontSize="xs"
            color={mode === 'dark' ? 'white' : 'black'}
            flex={1}
            marginRight={2}>
            {item.full_name}
          </Text>
          <Text fontSize="xs" color={mode === 'dark' ? 'gray.400' : 'gray.500'}>
            {item.person_id}
          </Text>
        </HStack>
      </GestureDetector>
    );
  };

  const toggleDropdown = () => {
    setIsOpen(!isOpen);
    loadStaff();
  };

  return (
    <Box>
      <Pressable onPress={toggleDropdown} flex={1}>
        <Input
          variant="filled"
          ref={inputRef}
          value={searchQuery}
          placeholder="Select or search for staff"
          color={mode === 'dark' ? 'white' : 'black'}
          backgroundColor={mode === 'dark' ? 'gray.700' : 'gray.100'}
          readOnly={true}
          fontSize="xs"
          height={10}
          _focus={{
            backgroundColor: mode === 'dark' ? 'gray.600' : 'white',
            borderColor: 'orange.500',
          }}
          InputRightElement={
            <IconButton
              icon={
                <Icon
                  name={isOpen ? 'menu-up' : 'menu-down'}
                  size={20}
                  color={mode === 'dark' ? 'white' : 'black'}
                />
              }
              onPress={toggleDropdown}
              _pressed={{bg: mode === 'dark' ? 'gray.600' : 'orange.100'}}
            />
          }
        />
      </Pressable>

      <Modal
        visible={isOpen}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setIsOpen(false)}>
        <GestureHandlerRootView style={{ flex: 1 }}>
          <TouchableWithoutFeedback onPress={() => setIsOpen(false)}>
            <Box
              flex={1}
              bg="rgba(0,0,0,0.5)"
              justifyContent="center"
              alignItems="center">
              <TouchableWithoutFeedback>
                <VStack
                  width={windowWidth - 40}
                  maxHeight={windowHeight * 0.8}
                  bg={mode === 'dark' ? 'trueGray.800' : 'white'}
                  borderRadius="md"
                  shadow={2}
                  p={4}>
                  <Input
                    value={searchQuery}
                    onChangeText={setSearchQuery}
                    placeholder="Search by name or ID"
                    backgroundColor={
                      mode === 'dark' ? 'trueGray.700' : 'orange.50'
                    }
                    color={mode === 'dark' ? 'white' : 'black'}
                    mb={2}
                    fontSize="sm"
                    height={10}
                    InputRightElement={
                      <IconButton
                        icon={
                          <Icon
                            name="broom"
                            size={20}
                            color={mode === 'dark' ? 'white' : 'black'}
                          />
                        }
                        onPress={() => setSearchQuery('')}
                        _pressed={{
                          bg: mode === 'dark' ? 'trueGray.700' : 'orange.50',
                        }}
                      />
                    }
                  />
                  <FlatList
                    data={staffList}
                    renderItem={renderItem}
                    keyExtractor={item => item.person_id.toString()}
                    onScroll={e => e.stopPropagation()}
                  />
                </VStack>
              </TouchableWithoutFeedback>
            </Box>
          </TouchableWithoutFeedback>
        </GestureHandlerRootView>
      </Modal>
    </Box>
  );
};
