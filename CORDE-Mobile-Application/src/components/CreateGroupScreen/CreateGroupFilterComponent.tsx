import React, {useState, useEffect} from 'react';
import {
  Modal,
  VStack,
  FormControl,
  Input,
  Button,
  ScrollView,
  HStack,
  Text,
  Box,
  Pressable,
  Icon,
} from 'native-base';
import DateTimePicker from '@react-native-community/datetimepicker';
import {useTheme} from '../../styles/ThemeContext';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';

interface FilterProps {
  address: string;
  description: string;
  asset: string;
  dateFrom?: Date;
  dateTo?: Date;
}

interface CreateGroupFilterComponentProps {
  isOpen: boolean;
  onApply: (filters: FilterProps) => void;
  onCancel: () => void;
  onClear: () => void;
  initialFilters: FilterProps;
}

const CreateGroupFilterComponent: React.FC<CreateGroupFilterComponentProps> = ({
  isOpen,
  onApply,
  onCancel,
  onClear,
  initialFilters,
}) => {
  const {mode} = useTheme();

  // Color mode values
  const bgColor = mode === 'dark' ? 'gray.800' : 'white';
  const textColor = mode === 'dark' ? 'white' : 'black';
  const inputBgColor = mode === 'dark' ? 'gray.700' : 'gray.100';
  const labelColor = mode === 'dark' ? 'gray.300' : 'gray.700';
  const modalHeaderBgColor = mode === 'dark' ? 'gray.600' : 'white';
  const modalHeaderTextColor = mode === 'dark' ? 'white' : 'black';

  const [filters, setFilters] = useState({
    address: initialFilters.address || '',
    description: initialFilters.description || '',
    asset: initialFilters.asset || '',
    dateFrom: initialFilters.dateFrom || undefined,
    dateTo: initialFilters.dateTo || undefined,
  });

  const [showDatePicker, setShowDatePicker] = useState({
    dateFrom: false,
    dateTo: false,
  });

  useEffect(() => {
    setFilters({
      address: initialFilters.address || '',
      description: initialFilters.description || '',
      asset: initialFilters.asset || '',
      dateFrom: initialFilters.dateFrom || undefined,
      dateTo: initialFilters.dateTo || undefined,
    });
  }, [initialFilters]);

  const formatDateForDisplay = (date: Date | undefined): string => {
    if (!date) return '';
    // Format date as DD/MM/YYYY for New Zealand
    return date.toLocaleDateString('en-NZ');
  };

  const handleDateChange = (event: any, selectedDate: Date | undefined, field: string) => {
    setShowDatePicker({...showDatePicker, [field]: false});
    if (selectedDate) {
      setFilters({...filters, [field]: selectedDate});
    }
  };

  const renderFormControl = (label: string, value: string, onChangeText: (text: string) => void, placeholder: string) => (
    <FormControl mb={1}>
      <FormControl.Label
        _text={{fontSize: 'sm', fontWeight: 'bold', color: labelColor}}>
        {label}
      </FormControl.Label>
      <Input
        value={value}
        onChangeText={onChangeText}
        placeholder={placeholder}
        size="sm"
        height={8}
        bg={inputBgColor}
        color={textColor}
        fontSize="sm"
      />
    </FormControl>
  );

  const renderDateRangePicker = () => (
    <FormControl mb={1}>
      <FormControl.Label _text={{fontSize: 'sm', fontWeight: 'bold', color: labelColor}}>
        Date Range
      </FormControl.Label>
      <HStack space={1}>
        <Pressable flex={1} onPress={() => setShowDatePicker({...showDatePicker, dateFrom: true})}>
          <Input
            value={formatDateForDisplay(filters.dateFrom)}
            isReadOnly={true}
            size="sm"
            height={8}
            bg={inputBgColor}
            color={textColor}
            fontSize="sm"
            placeholder="From Date"
            InputRightElement={
              <Icon as={MaterialCommunityIcons} name="calendar" size={4} mr="1" color={labelColor} />
            }
          />
        </Pressable>
        <Pressable flex={1} onPress={() => setShowDatePicker({...showDatePicker, dateTo: true})}>
          <Input
            value={formatDateForDisplay(filters.dateTo)}
            isReadOnly={true}
            size="sm"
            height={8}
            bg={inputBgColor}
            color={textColor}
            fontSize="sm"
            placeholder="To Date"
            InputRightElement={
              <Icon as={MaterialCommunityIcons} name="calendar" size={4} mr="1" color={labelColor} />
            }
          />
        </Pressable>
      </HStack>
      {showDatePicker.dateFrom && (
        <DateTimePicker
          value={filters.dateFrom || new Date()}
          mode="date"
          display="default"
          onChange={(event, selectedDate) => handleDateChange(event, selectedDate, 'dateFrom')}
        />
      )}
      {showDatePicker.dateTo && (
        <DateTimePicker
          value={filters.dateTo || new Date()}
          mode="date"
          display="default"
          onChange={(event, selectedDate) => handleDateChange(event, selectedDate, 'dateTo')}
        />
      )}
    </FormControl>
  );

  return (
    <Modal isOpen={isOpen} onClose={onCancel} size="full">
      <Modal.Content maxWidth="90%" bg={bgColor}>
        <Modal.CloseButton />
        <Modal.Header bg={modalHeaderBgColor} borderBottomWidth={0} py={2}>
          <Text color={modalHeaderTextColor} fontSize="lg" fontWeight="bold">
            Filters
          </Text>
        </Modal.Header>
        <Modal.Body py={2}>
          <ScrollView showsVerticalScrollIndicator={false}>
            <VStack space={1}>
              {renderDateRangePicker()}
              {renderFormControl(
                'Address',
                filters.address,
                text => setFilters({...filters, address: text}),
                'Search Address',
              )}
              {renderFormControl(
                'Description',
                filters.description,
                text => setFilters({...filters, description: text}),
                'Search Description',
              )}
              {renderFormControl(
                'Asset',
                filters.asset,
                text => setFilters({...filters, asset: text}),
                'Search Asset',
              )}
            </VStack>
          </ScrollView>
        </Modal.Body>
        <Modal.Footer bg={modalHeaderBgColor} borderTopWidth={0} py={2}>
          <HStack space={2} justifyContent="flex-end">
            <Button
              onPress={onCancel}
              size="sm"
              height={8}
              fontSize="sm"
              colorScheme={mode === 'dark' ? 'gray' : 'gray'}
              variant={mode === 'dark' ? 'outline' : 'outline'}
              _text={{color: mode === 'dark' ? 'white' : 'black'}}>
              Cancel
            </Button>
            <Button
              onPress={onClear}
              size="sm"
              height={8}
              fontSize="sm"
              colorScheme={mode === 'dark' ? 'red' : 'red'}
              variant={mode === 'dark' ? 'subtle' : 'subtle'}
              _text={{color: mode === 'dark' ? 'red.600' : 'red.600'}}>
              Clear
            </Button>
            <Button
              onPress={() => onApply(filters)}
              size="sm"
              height={8}
              fontSize="sm"
              colorScheme="orange"
              _text={{color: 'white'}}>
              Apply
            </Button>
          </HStack>
        </Modal.Footer>
      </Modal.Content>
    </Modal>
  );
};

export default CreateGroupFilterComponent;