import React, {useState, memo, useCallback, useMemo, useEffect} from 'react';
import {StyleSheet, TouchableOpacity} from 'react-native';
import {
  VStack,
  ScrollView,
  FormControl,
  Button,
  Icon,
  Text,
  Checkbox,
  View,
  Spinner,
  Box,
  HStack, Select, FlatList,
} from 'native-base';
import {ServiceAgreementSelect} from '../NewLogScreen/ServiceAgreementSelect';
import {ServiceAgreement} from '../../database/ServiceAgreement';
import {LogList} from '../../database/LogList';
import {
  blackSubtleButtonStyle,
  orangeOutlineButtonStyle,
  orangeSubtleButtonStyle,
} from '../../styles/buttonStyles.ts';
import {useTheme} from '../../styles/ThemeContext.tsx';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import {useNavigation} from '@react-navigation/native';
import CreateGroupFilterComponent from './CreateGroupFilterComponent';
import {formatScheduleDate, processDateRangeForNZ} from '../../utils/dateUtils';

// Extend ServiceAgreement interface for extend_columns
interface ExtendedServiceAgreement extends ServiceAgreement {
  extend_columns?: any[];
}

const CreateGroup: React.FC = () => {
  const navigation = useNavigation();
  const {mode} = useTheme();
  const createButtonStyle = orangeSubtleButtonStyle(mode || 'light');

  const [selectedAgreement, setSelectedAgreement] =
    useState<ExtendedServiceAgreement | null>(null);
  const [resetKey, setResetKey] = useState(0);
  const [relatedLogs, setRelatedLogs] = useState<LogList[]>([]);
  const [selectedLogIds, setSelectedLogIds] = useState<string[]>([]);
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const [filters, setFilters] = useState({
    address: '',
    description: '',
    asset: '',
    dateFrom: undefined,
    dateTo: undefined,
  });

  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [isLoading, setIsLoading] = useState(false);



  const fetchRelatedLogs = async (
    agreementId: number,
    pageNumber: number = 1,
    startDate?: Date,
    endDate?: Date
  ) => {
    try {
      setIsLoading(true);
      const pageSize = 10; // Display 10 items per page

      // Clear existing data if this is the first page
      if (pageNumber === 1) {
        setRelatedLogs([]);
      }
      
      // Only apply date filtering when dates are explicitly passed or set in filters
      const effectiveStartDate = startDate || filters.dateFrom;
      const effectiveEndDate = endDate || filters.dateTo;


      // Apply date filtering (if dates are set)
      const logs = await LogList.getScheduledByServiceAgreementIdWithPaging(
        agreementId,
        pageNumber,
        pageSize,
        effectiveStartDate,
        effectiveEndDate
      );

      // Determine if there's more data to load
      setHasMore(logs.length === pageSize);

      // Merge data
      if (pageNumber === 1) {
        setRelatedLogs(logs);
      } else {
        setRelatedLogs(prev => [...prev, ...logs]);
      }

      setPage(pageNumber);
    } catch (error) {
      console.error('Error fetching related logs:', error);
      setRelatedLogs([]);
    } finally {
      setIsLoading(false);
    }
  };

  // Handle load more when scrolled to bottom
  const handleLoadMore = () => {
    if (!isLoading && hasMore && selectedAgreement) {
      // Pass current filter dates to maintain filtering on pagination
      fetchRelatedLogs(selectedAgreement.id, page + 1, filters.dateFrom, filters.dateTo);
    }
  };

  const handleAgreementSelect = async (agreement: ServiceAgreement | null) => {

    if (agreement) {
      // Don't use date filtering on initial load
      fetchRelatedLogs(agreement.id, 1);

      const extend_columns = await LogList.getExtendColumns(agreement.id);
      const updatedAgreement: ExtendedServiceAgreement = {
        ...agreement,
        extend_columns: extend_columns,
      };

      setSelectedAgreement(updatedAgreement);
      setSelectedLogIds([]);
    } else {
      setSelectedAgreement(null);
      setRelatedLogs([]);
      setSelectedLogIds([]);
    }
  };

  const handleLogSelection = (logId: string) => {
    setSelectedLogIds(prevIds => {
      if (prevIds.includes(logId)) {
        return prevIds.filter(id => id !== logId);
      } else {
        return [...prevIds, logId];
      }
    });
  };

  useEffect(() => {
    // console.log('Current selectedLogIds:', selectedLogIds);
  }, [selectedLogIds]);

  const renderLogList = () => {
    if (!selectedAgreement) {
      return (
        <Text
          fontSize="sm"
          color={mode === 'dark' ? 'trueGray.400' : 'trueGray.500'}
          textAlign="center">
          Please select a service agreement
        </Text>
      );
    }

    if (relatedLogs.length === 0 && !isLoading) {
      return (
        <Text
          fontSize="sm"
          color={mode === 'dark' ? 'trueGray.400' : 'trueGray.500'}
          textAlign="center">
          No logs were found
        </Text>
      );
    }

    return (
      <Box flex={1} mb={4}>
        <FlatList
          data={relatedLogs}
          keyExtractor={(item) => item.local_log_id?.toString() || ''}
          onEndReached={handleLoadMore}
          onEndReachedThreshold={0.5}
          renderItem={({item: log}) => (
            <TouchableOpacity
              onPress={() => handleLogSelection(log.local_log_id?.toString() || '')}>
              <Box
                bg={mode === 'dark' ? 'gray.800' : 'gray.100'}
                px={3}
                py={2}
                borderBottomWidth={1}
                borderBottomColor={mode === 'dark' ? 'gray.700' : 'gray.200'}>
                <HStack alignItems="center" space={3}>
                  <Box>
                    <Checkbox
                      size="sm"
                      isChecked={selectedLogIds.includes(log.local_log_id?.toString() || '')}
                      onChange={() => handleLogSelection(log.local_log_id?.toString() || '')}
                      value={log.local_log_id?.toString() || ''}
                      accessibilityLabel={`Select log ${log.local_log_id}`}
                      colorScheme="orange"
                    >
                      {" "}
                    </Checkbox>
                  </Box>

                  <VStack flex={1}>
                    <HStack justifyContent="space-between" alignItems="center">
                      <Text
                        fontSize="sm"
                        color={mode === 'dark' ? 'orange.300' : 'orange.600'}
                        fontWeight="bold">
                        #{log.log_header_id}
                      </Text>
                      <Text
                        fontSize="sm"
                        color={mode === 'dark' ? 'trueGray.400' : 'trueGray.600'}>
                        {log.asset_code}
                      </Text>
                    </HStack>

                    <HStack justifyContent="space-between" alignItems="center" mt={1}>
                      <Text
                        fontSize="xs"
                        color={mode === 'dark' ? 'blue.300' : 'blue.600'}
                        fontWeight="medium">
                        Scheduled: {formatScheduleDate(log.schd_date)}
                      </Text>
                    </HStack>

                    <Text
                      fontSize="sm"
                      color={mode === 'dark' ? 'white' : 'black'}
                      fontWeight="semibold">
                      {log.log_address} | {log.log_type} | {log.service_agreement_id}
                    </Text>

                    <Text
                      fontSize="sm"
                      color={mode === 'dark' ? 'trueGray.300' : 'trueGray.700'}
                      numberOfLines={1}>
                      {log.asset_description}
                    </Text>

                    <Text
                      fontSize="sm"
                      color={mode === 'dark' ? 'trueGray.400' : 'trueGray.600'}
                      numberOfLines={1}
                      italic>
                      {log.description?.replace(/"/g, '') || ''}
                    </Text>
                  </VStack>
                </HStack>
              </Box>
            </TouchableOpacity>
          )}
          ListFooterComponent={isLoading ? <Spinner color="orange.500" size="sm" my={2} /> : null}
        />
      </Box>
    );
  };

  const handleReadyForCompletion = () => {
    const selectedLogs = relatedLogs.filter(log =>
      selectedLogIds.includes(log.local_log_id?.toString() || ''),
    );
    
    (navigation as any).navigate('Complete Group', {
      selectedAgreement: selectedAgreement,
      relatedLogs: selectedLogs,
      extendColumns: selectedAgreement?.extend_columns || [],
    });
  };

  const handleFilterApply = (newFilters: any) => {

    // Keep original filters for display (don't modify the original dates)
    setFilters(newFilters);
    setIsFilterOpen(false);

    // Process date filters for New Zealand timezone only for database query
    const processedDateRange = processDateRangeForNZ({
      startDate: newFilters.dateFrom,
      endDate: newFilters.dateTo
    });

    // If a Service Agreement is selected, reload data
    if (selectedAgreement) {
      setPage(1);
      // First reload data with date range (using processed dates)
      fetchRelatedLogs(selectedAgreement.id, 1, processedDateRange.startDate, processedDateRange.endDate).then(() => {
        // Then apply text filtering in memory
        if (newFilters.address || newFilters.description || newFilters.asset) {
          setRelatedLogs(prevLogs => prevLogs.filter(log => {
            const addressMatch = !newFilters.address || 
              log.log_address.toLowerCase().includes(newFilters.address.toLowerCase());
            const descriptionMatch = !newFilters.description || 
              (log.description?.toLowerCase() || '').includes(newFilters.description.toLowerCase());
            const assetMatch = !newFilters.asset || 
              log.asset_description.toLowerCase().includes(newFilters.asset.toLowerCase());
            
            return addressMatch && descriptionMatch && assetMatch;
          }));
        }
      });
    }
  };

  const handleFilterClear = () => {
    const clearedFilters = {
      address: '',
      description: '',
      asset: '',
      dateFrom: undefined,
      dateTo: undefined,
    };
    
    setFilters(clearedFilters);
    
    // Reload all related logs without date filtering
    if (selectedAgreement) {
      setPage(1);
      fetchRelatedLogs(selectedAgreement.id, 1);
    }
  };

  return (
    <View style={[styles.container, { position: 'relative' }]}>
      <VStack flex={1} position="relative">
        {/* Top fixed area */}
        <Box position="relative" zIndex={1}>
          {/* Add Filters button */}
          <HStack justifyContent="flex-start" alignItems="center" mb={2}>
            <Button
              size="sm"
              height={8}
              {...(mode === 'dark'
                ? orangeOutlineButtonStyle
                : blackSubtleButtonStyle)}
              leftIcon={
                <Icon as={MaterialCommunityIcons} name="filter" size="xs" />
              }
              onPress={() => setIsFilterOpen(true)}
              _text={{fontSize: 'xs', color: mode === 'dark' ? 'white' : 'white'}}>
              Add Filters
            </Button>
          </HStack>

          {/* Service Agreement selector */}
          <Box mb={4}>
            <ServiceAgreementSelect
              onSelect={handleAgreementSelect}
              reset={resetKey}
            />
          </Box>

          {/* Simple spacing */}
          <Box mb={8} />
        </Box>

        {/* Middle scrollable area */}
        <Box flex={1}>
          {renderLogList()}
        </Box>

        {/* Bottom fixed area */}
        <Button
          {...createButtonStyle}
          onPress={handleReadyForCompletion}
          leftIcon={
            <Icon
              as={MaterialCommunityIcons}
              name="format-list-checks"
              size="sm"
            />
          }
          isDisabled={selectedLogIds.length === 0 || selectedLogIds.length === 1}
          height={16}
          justifyContent="flex-start"
          width="100%"
        >
          <HStack alignItems="center" space={1}>
            <Text
              fontSize="sm"
              color={mode === 'dark' ? 'white' : 'black'}
              numberOfLines={1}
              flexShrink={1}
            >
              Ready Completion
            </Text>
            <Text
              fontSize="sm"
              color={mode === 'dark' ? 'white' : 'black'}
              numberOfLines={1}
            >
              ({selectedLogIds.length} selected)
            </Text>
          </HStack>
        </Button>

        <CreateGroupFilterComponent
          isOpen={isFilterOpen}
          onApply={handleFilterApply}
          onCancel={() => setIsFilterOpen(false)}
          onClear={handleFilterClear}
          initialFilters={filters}
        />
      </VStack>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 10,
  },
});

export default CreateGroup;
