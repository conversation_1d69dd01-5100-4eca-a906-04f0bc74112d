// src/components/UserProfileScreen/UserProfile.tsx

import React, { useState, useEffect } from 'react';
import {
  VStack,
  ScrollView,
  Text,
  Box,
  Avatar,
  HStack,
  Icon,
  useToast,
} from 'native-base';
import { useNavigation } from '@react-navigation/native';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import { useTheme } from '../../styles/ThemeContext';
import { AuthService } from '../../services/AuthService';
import ToastAlert from "../NewLogScreen/ToastAlert";

const UserProfile: React.FC = () => {
  const [user, setUser] = useState<any>(null);
  const { mode } = useTheme();
  const navigation = useNavigation();
  const toast = useToast();

  useEffect(() => {
    fetchUserData();
  }, []);

  const fetchUserData = async () => {
    try {
      const currentUser = await AuthService.getCurrentUser();
      setUser(currentUser);
    } catch (error) {
      console.error('Error fetching user data:', error);
      toast.show({
        render: ({ id }) => (
          <ToastAlert
            id={id}
            title="Error"
            description="Failed to load user data."
            isClosable={true}
            onClose={() => toast.close(id)}
            variant="left-accent"
            status="error"
          />
        ),
        placement: 'top',
      });
    }
  };

  const textColor = mode === 'dark' ? 'white' : 'black';
  const bgColor = mode === 'dark' ? 'gray.800' : 'white';

  const InfoItem = ({ icon, label, value }: { icon: string; label: string; value: string }) => (
    <HStack space={4} alignItems="center">
      <Icon as={MaterialCommunityIcons} name={icon} size={6} color="orange.500" />
      <VStack>
        <Text fontSize="sm" color="gray.500">{label}</Text>
        <Text fontSize="md" color={textColor}>{value || 'Not provided'}</Text>
      </VStack>
    </HStack>
  );

  return (
    <ScrollView bg={bgColor}>
      <VStack space={6} alignItems="center" p={6}>
        <Avatar
          size="2xl"
          source={require('../../assets/Mark/CORDE_Mark_MarginHalf_White.jpg')}
        />
        <Text fontSize="3xl" fontWeight="bold" color={textColor}>
          {user?.full_name}
        </Text>
        <Box width="100%" bg={mode === 'dark' ? 'gray.700' : 'gray.100'} p={4} rounded="md">
          <VStack space={4}>
            <InfoItem icon="account" label="Username" value={user?.username} />
            <InfoItem icon="email" label="Email" value={user?.email} />
            <InfoItem icon="phone" label="Phone" value={user?.phone_number} />
            <InfoItem icon="identifier" label="Person ID" value={user?.person_id?.toString()} />
            <InfoItem icon="office-building" label="Fin Co Code" value={user?.fin_co_code} />
            <InfoItem icon="clock" label="Last Login" value={user?.last_login} />
          </VStack>
        </Box>
      </VStack>
    </ScrollView>
  );
};

export default UserProfile;
