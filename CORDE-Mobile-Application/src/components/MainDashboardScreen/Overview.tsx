import React, {useEffect, useState} from 'react';
import {Heading, Flex, Pressable, VStack, Switch, Text, Box, HStack, Icon} from 'native-base';
import {useTheme} from '../../styles/ThemeContext';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import {useNavigation} from '@react-navigation/native';
import {useWindowDimensions} from 'react-native';
import {networkService} from "../../utils/NetworkUtils.ts";

const boxData = [
  {
    icon: 'clipboard-list',
    title: 'LOG LIST',
    screen: 'Log List',
    color: '#4CAF50',
  },
  {
    icon: 'clipboard-plus',
    title: 'NEW LOG',
    screen: 'Create New Log',
    color: '#2196F3',
  },
  {icon: 'cloud-sync', title: 'SYNC LOG', screen: 'Sync Log', color: '#FF9800'},
  {
    icon: 'clipboard-check-multiple',
    title: 'LOG GROUP',
    screen: 'Create Group',
    color: '#9C27B0',
  },
  {
    icon: 'map-marker',
    title: 'MAP MARKER',
    screen: 'Map Marker',
    color: '#F44336',
  },
  {icon: 'cog', title: 'SETTINGS', screen: 'Settings', color: '#607D8B'},
];

const Overview: React.FC = () => {
  const navigation = useNavigation();
  const {mode} = useTheme();
  const {width, height} = useWindowDimensions();
  const [pressedIndex, setPressedIndex] = useState<number | null>(null);
  const [isOfflineMode, setIsOfflineMode] = useState(false);

  useEffect(() => {
    // Load current offline mode setting
    setIsOfflineMode(networkService.getManualOfflineMode());
  }, []);

  const toggleOfflineMode = async () => {
    const newValue = !isOfflineMode;
    setIsOfflineMode(newValue);
    await networkService.setManualOfflineMode(newValue);
  };

  const networkModeToggle = (
    <Box
      position="absolute"
      top={0}
      right={1}
      zIndex={1000}
      bg={mode === 'dark' ? 'gray.800' : 'white'}
      borderRadius="full"
      shadow={3}
      p={2}
      flexDirection="row"
      alignItems="center"
    >
      <Icon
        as={MaterialCommunityIcons}
        name={isOfflineMode ? "wifi-off" : "wifi"}
        size="sm"
        color={isOfflineMode ? "gray.400" : "green.500"}
        mr={1}
      />
      <Switch
        size="sm"
        onToggle={toggleOfflineMode}
        isChecked={!isOfflineMode}
        mr={1}
      />
    </Box>
  );

  const handlePress = (key: number) => {
    const targetScreen = boxData[key - 1].screen;
    if (targetScreen) {
      navigation.navigate(targetScreen);
    }
    setPressedIndex(null);
  };

  const boxContent = (key: number) => (
    <Pressable
      onPress={() => handlePress(key)}
      onPressIn={() => setPressedIndex(key)}
      onPressOut={() => setPressedIndex(null)}
      _pressed={{
        opacity: 0.5,
        transform: [{scale: 0.95}],
      }}
      key={key}
      rounded="xl"
      overflow="hidden"
      width="48%"
      height={height * 0.25}
      justifyContent="center"
      alignItems="center"
      mb={4}>
      <VStack space={2} alignItems="center">
        <MaterialCommunityIcons
          name={
            pressedIndex === key
              ? `${boxData[key - 1].icon}-outline`
              : boxData[key - 1].icon
          }
          size={width > 600 ? 60 : 48}
          color={boxData[key - 1].color}
        />
        <Heading
          size={width > 600 ? 'md' : 'sm'}
          color={mode === 'dark' ? 'white' : 'black'}
          fontFamily="monospace"
          textAlign="center">
          {boxData[key - 1].title}
        </Heading>
      </VStack>
    </Pressable>
  );

  return (
    <Box position="relative" flex={1}>
      {networkModeToggle}
      <Flex
        direction="row"
        wrap="wrap"
        justifyContent="space-between"
        alignContent="flex-start"
        height="100%">
        {Array.from({length: boxData.length}, (_, i) => boxContent(i + 1))}
      </Flex>
    </Box>
  );
};

export default Overview;
