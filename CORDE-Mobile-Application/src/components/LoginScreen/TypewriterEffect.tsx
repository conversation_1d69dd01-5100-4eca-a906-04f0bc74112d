import React, {useState, useEffect} from 'react';
import {Text} from 'native-base';
import {useTheme} from '../../styles/ThemeContext';

const TypewriterEffect = ({text, speed = 100}) => {
  const {mode} = useTheme(); // Use theme to determine text color
  const textColor = mode === 'dark' ? 'white' : 'black';

  const [displayedText, setDisplayedText] = useState('');
  const [cursorVisible, setCursorVisible] = useState(true);

  useEffect(() => {
    let index = 0;
    const textTimer = setInterval(() => {
      setDisplayedText(prev => prev + text[index]);
      index++;
      if (index === text.length) {
        clearInterval(textTimer);
        setCursorVisible(false); // Hide cursor after text is displayed
      }
    }, speed);

    // Cursor blinking effect
    const cursorTimer =
      cursorVisible &&
      setInterval(() => {
        setCursorVisible(prev => !prev);
      }, 500); // Cursor blinks every 500ms

    return () => {
      clearInterval(textTimer);
      clearInterval(cursorTimer);
    };
  }, [text, speed]);

  return (
    <Text style={{fontFamily: 'monospace', color: textColor}}>
      {displayedText}
      {cursorVisible && (
        <Text style={{backgroundColor: textColor, width: 8, height: '100%'}}>
          {' '}
        </Text>
      )}
    </Text>
  );
};

export default TypewriterEffect;
