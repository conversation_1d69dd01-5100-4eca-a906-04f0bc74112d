import React from 'react';
import {HStack, Icon, Switch} from 'native-base';
import {useTheme} from '../../styles/ThemeContext';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';

const ThemeToggle = () => {
  const {mode, setMode} = useTheme();

  const toggleTheme = () => {
    setMode(mode === 'dark' ? 'light' : 'dark');
  };

  return (
    <HStack justifyContent="flex-end">
      <Switch
        isChecked={mode === 'dark'}
        onToggle={toggleTheme}
        offTrackColor="orange.100"
        onTrackColor="orange.200"
        onThumbColor="orange.500"
        offThumbColor="orange.50"
      />
      {mode === 'dark' ? (
        <Icon
          as={MaterialCommunityIcons}
          name="weather-night"
          size="lg"
          color="orange.100"
        />
      ) : (
        <Icon
          as={MaterialCommunityIcons}
          name="white-balance-sunny"
          size="lg"
          color="orange.400"
        />
      )}
    </HStack>
  );
};

export default ThemeToggle;
