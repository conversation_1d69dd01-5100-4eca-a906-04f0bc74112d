import React from 'react';
import {VStack, Image, Heading} from 'native-base';
import {useTheme} from '../../styles/ThemeContext';
import TypewriterEffect from './TypewriterEffect';
import {useWindowDimensions} from 'react-native';

const Header: React.FC = () => {
  const {mode} = useTheme();
  const {width} = useWindowDimensions();
  const imagePath =
    mode === 'dark'
      ? require('../../assets/Mark/CORDE_Mark_White.png')
      : require('../../assets/Mark/CORDE_Mark_Black.png');

  return (
    <VStack space={2} alignItems="center">
      <Image
        key={mode}
        source={imagePath}
        alt="CORDE Mark"
        size="xl"
        resizeMode="contain"
      />
      <Heading
        size="2xl"
        textAlign="center"
        color={mode === 'dark' ? 'white' : 'black'}>
        <TypewriterEffect text="Hello CORDE!" />
      </Heading>
    </VStack>
  );
};

export default Header;
