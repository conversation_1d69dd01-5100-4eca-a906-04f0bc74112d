import React, {useEffect, useState} from 'react';
import {
  Input,
  Icon,
  Pressable,
  Button,
  Checkbox,
  useToast,
  VStack,
  HStack,
  Text,
  FormControl,
} from 'native-base';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import {useNavigation} from '@react-navigation/native';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {RootStackParamList} from '../../navigation/AppNavigator';
import {
  blackSubtleButtonStyle,
  orangeOutlineButtonStyle,
} from '../../styles/buttonStyles';
import {useTheme} from '../../styles/ThemeContext';
import {AuthService} from '../../services/AuthService';
import {useAuth} from '../../context/AuthContext';
import {useWindowDimensions} from 'react-native';
import ToastAlert from '../NewLogScreen/ToastAlert.tsx';

type LoginScreenNavigationProp = NativeStackNavigationProp<
  RootStackParamList,
  'Login'
>;

const Login: React.FC = () => {
  const [showPassword, setShowPassword] = useState(false);
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [rememberMe, setRememberMe] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const navigation = useNavigation<LoginScreenNavigationProp>();
  const {mode} = useTheme();
  const {setUser, checkAuth} = useAuth();
  const toast = useToast();
  const {width} = useWindowDimensions();

  useEffect(() => {
    const loadStoredCredentials = async () => {
      const storedCredentials = await AuthService.getStoredCredentials();
      if (storedCredentials) {
        setUsername(storedCredentials.username);
        setPassword(storedCredentials.password);
        setRememberMe(true);
      }
    };

    loadStoredCredentials();
  }, []);

  useEffect(() => {
    checkAuth();
  }, [checkAuth]);

  const handleLogin = async () => {
    if (!username || !password) {
      toast.show({
        render: ({id}) => (
          <ToastAlert
            id={id}
            title="Login Error"
            description="Please enter your username and password."
            isClosable={true}
            onClose={() => toast.close(id)}
            variant="left-accent"
            status="error"
          />
        ),
        placement: 'top',
      });
      return;
    }

    setIsLoading(true);
    try {
      const user = await AuthService.login(username, password, rememberMe);
      setUser(user);
      navigation.navigate('Main Dashboard');
    } catch (error) {
      console.error('Login Failure:', error);
      toast.show({
        render: ({id}) => (
          <ToastAlert
            id={id}
            title="Login Failure"
            description={
              (error.message, 'Please check your username and password.')
            }
            isClosable={true}
            onClose={() => toast.close(id)}
            variant="left-accent"
            status="error"
          />
        ),
        placement: 'top',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const inputSize = '2xl';
  const buttonSize = '2xl';

  return (
    <VStack space={4} alignItems="center" width="100%">
      <FormControl>
        <FormControl.Label
          _text={{
            fontSize: 'lg',
            fontWeight: 'bold',
            color: mode === 'dark' ? 'white' : 'black',
          }}>
          Username
        </FormControl.Label>
        <Input
          size={inputSize}
          variant="filled"
          InputLeftElement={
            <Icon
              as={<MaterialCommunityIcons name="account" />}
              size={8}
              ml={2}
              color={mode === 'dark' ? 'white' : 'black'}
            />
          }
          placeholder="firstname.lastname"
          value={username}
          onChangeText={setUsername}
          backgroundColor={mode === 'dark' ? 'gray.700' : 'white'}
          color={mode === 'dark' ? 'white' : 'black'}
          width="100%"
          fontSize="lg"
          height={12}
        />
      </FormControl>

      <FormControl>
        <FormControl.Label
          _text={{
            fontSize: 'lg',
            fontWeight: 'bold',
            color: mode === 'dark' ? 'white' : 'black',
          }}>
          Password
        </FormControl.Label>
        <Input
          size={inputSize}
          variant="filled"
          type={showPassword ? 'text' : 'password'}
          InputRightElement={
            <Pressable
              onPress={() => setShowPassword(!showPassword)}
              hitSlop={{top: 10, bottom: 10, left: 10, right: 10}}>
              <Icon
                as={
                  <MaterialCommunityIcons
                    name={showPassword ? 'eye' : 'eye-off'}
                  />
                }
                size={8}
                mr={2}
                color={mode === 'dark' ? 'white' : 'black'}
              />
            </Pressable>
          }
          placeholder="Please enter your password"
          value={password}
          onChangeText={setPassword}
          backgroundColor={mode === 'dark' ? 'gray.700' : 'white'}
          color={mode === 'dark' ? 'white' : 'black'}
          width="100%"
          fontSize="lg"
          height={12}
        />
      </FormControl>

      <HStack space={2} alignItems="center" alignSelf="flex-end">
        <Checkbox
          value="remember"
          isChecked={rememberMe}
          onChange={isSelected => setRememberMe(isSelected)}
          accessibilityLabel="RememberMe"
          size="sm"
          colorScheme="orange">
          <Text color={mode === 'dark' ? 'white' : 'black'} fontSize="md">
            Remember Me
          </Text>
        </Checkbox>
      </HStack>

      <Button
        size={buttonSize}
        onPress={handleLogin}
        width="100%"
        isLoading={isLoading}
        isLoadingText="Logging in..."
        _text={{fontSize: 'xl'}}
        height={12}
        {...(mode === 'dark'
          ? orangeOutlineButtonStyle
          : blackSubtleButtonStyle)}>
        Login
      </Button>
    </VStack>
  );
};

export default Login;
