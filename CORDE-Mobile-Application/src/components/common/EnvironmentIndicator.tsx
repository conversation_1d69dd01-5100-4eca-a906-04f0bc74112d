import React from 'react';
import {Badge, Box} from 'native-base';
import {isProductionBuild} from '../../api/BaseApi';

const GlobalEnvironmentIndicator: React.FC = () => {
  // Only show indicator for non-production builds
  if (isProductionBuild) {
    return null;
  }

  return (
    <Box
      position="absolute"
      bottom={6}
      right={4}
      zIndex={9999}
      opacity={0.8}>
      <Badge
        colorScheme="green"
        variant="solid"
        rounded="full"
        px={3}
        py={1}
        shadow={3}
        _text={{
          fontSize: 'xs',
          fontWeight: 'bold',
          color: 'white',
        }}>
        TEST
      </Badge>
    </Box>
  );
};

export default GlobalEnvironmentIndicator;
