import React, {useState, useCallback, useEffect} from 'react';
import {VStack, ScrollView, HStack, Box, Button, Icon, Text, IconButton, useToast} from 'native-base';
import {DatePickerControl} from '../LogHeaderScreen/DatePickerControl';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import {useTheme} from '../../styles/ThemeContext';
import {
  orangeSubtleButtonStyle,
  orangeGhostButtonStyle,
} from '../../styles/buttonStyles';
import DynamicFormField from '../LogHeaderScreen/DynamicFormField';
import {RootStackParamList} from '../../navigation/AppNavigator.tsx';
import {RouteProp, useRoute} from '@react-navigation/native';
import {LogGroupService} from '../../services/LogGroupService';
import ToastAlert from '../NewLogScreen/ToastAlert.tsx';
import {locationService} from "../../services/LocationService.ts";

type CompleteGroupScreenRouteProp = RouteProp<
RootStackParamList,
'Complete Group'
>;

const CompleteGroup: React.FC = () => {
  const route = useRoute<CompleteGroupScreenRouteProp>();
  const {mode} = useTheme();
  const submitButtonStyle = orangeSubtleButtonStyle(mode);
  const dateTimePickerStyle = orangeGhostButtonStyle(mode);
  const toast = useToast();

  const [formValues, setFormValues] = useState({});
  const [localItem, setLocalItem] = useState<any>(null);
  const [isGettingLocation, setIsGettingLocation] = useState(false);
  const [showDatePicker, setShowDatePicker] = useState({
    arrive_date: false,
    arrive_time: false,
    completed_date: false,
    completed_time: false,
  });

  useEffect(() => {
    if (route.params) {
      const {selectedAgreement, relatedLogs, extendColumns} = route.params;
      setLocalItem({
        ...selectedAgreement,
        extend_columns: extendColumns,
        relatedLogs,
      });

      const now = new Date();
      const initialFormValues = {
        arrive_date: null,
        arrive_time: null,
        completed_date: null,
        completed_time: null,
        arrive_latitude: null,
        arrive_longitude: null,
        completion_latitude: null,
        completion_longitude: null,
      };

      extendColumns.forEach(column => {
        initialFormValues[column.column_name] = '';
      });

      setFormValues(initialFormValues);
    }
  }, [route]);

  const handleDateChange = useCallback(
    (field: string, event: any, selectedDate?: Date) => {
      setShowDatePicker(prev => ({...prev, [field]: false}));
      if (selectedDate) {
        setFormValues(prev => ({
          ...prev,
          [field]: selectedDate,
        }));
      }
    },
    [],
  );

  const getCurrentLocation = async (callback: (lat: number, long: number) => void) => {
    if (isGettingLocation) return;

    setIsGettingLocation(true);
    try {
      const location = await locationService.getCurrentLocation({
        enableHighAccuracy: true,
        timeout: 15000,
        maximumAge: 10000,
      });


      if (location && typeof location.latitude === 'number' && typeof location.longitude === 'number') {
        callback(location.latitude, location.longitude);
      } else {
        throw new Error('Invalid location data received');
      }
    } catch (error) {
      console.error('Error getting location:', error);
      toast.show({
        render: ({id}) => (
          <ToastAlert
            id={id}
            title="Location Error"
            description="Unable to get current location. Please try again."
            isClosable={true}
            onClose={() => toast.close(id)}
            variant="left-accent"
            status="error"
          />
        ),
        placement: 'top',
      });
    } finally {
      setIsGettingLocation(false);
    }
  };

  const updateOnSiteData = async () => {
    try {
      const now = new Date();
      setFormValues(prev => ({
        ...prev,
        arrive_date: now,
        arrive_time: now,
      }));

      await getCurrentLocation((lat, long) => {
        console.log('Location received:', lat, long); // 添加调试日志
        setFormValues(prev => ({
          ...prev,
          arrive_latitude: lat,
          arrive_longitude: long,
        }));
      });
    } catch (error) {
      console.error('Error updating onsite data:', error);
    }
  };

  const updateCompleteData = async () => {
    try {
      const now = new Date();
      setFormValues(prev => ({
        ...prev,
        completed_date: now,
        completed_time: now,
      }));

      await getCurrentLocation((lat, long) => {
        console.log('Location received:', lat, long); // 添加调试日志
        setFormValues(prev => ({
          ...prev,
          completion_latitude: lat,
          completion_longitude: long,
        }));
      });
    } catch (error) {
      console.error('Error updating complete data:', error);
    }
  };

  const handleFieldChange = (columnName: string, value: any) => {
    setFormValues(prev => ({...prev, [columnName]: value}));
  };

  const handleSubmit = async () => {
    try {
      if (!localItem || !localItem.relatedLogs) {
        throw new Error('No logs selected for completion');
      }

      const logIds = localItem.relatedLogs.map(log => log.local_log_id);
      const submittedExtendColumns = localItem.extend_columns.map(column => ({
        column_name: column.column_name,
        value: formValues[column.column_name] || '',
      }));

      console.log('Submitting group completion:', logIds, formValues, submittedExtendColumns);

      const submittedFormValues = {
        ...formValues,
        arrive_date: formValues.arrive_date
          ? (formValues.arrive_date instanceof Date
            ? formValues.arrive_date.toISOString()
            : formValues.arrive_date)
          : null,
        // Do the same for other date fields
        arrive_time: formValues.arrive_time
          ? (formValues.arrive_time instanceof Date
            ? formValues.arrive_time.toISOString()
            : formValues.arrive_time)
          : null,
        completed_date: formValues.completed_date
          ? (formValues.completed_date instanceof Date
            ? formValues.completed_date.toISOString()
            : formValues.completed_date)
          : null,
        completed_time: formValues.completed_time
          ? (formValues.completed_time instanceof Date
            ? formValues.completed_time.toISOString()
            : formValues.completed_time)
          : null,
        arrive_latitude: formValues.arrive_latitude,
        arrive_longitude: formValues.arrive_longitude,
        completion_latitude: formValues.completion_latitude,
        completion_longitude: formValues.completion_longitude,
      };

      await LogGroupService.completeGroupLogs(
        logIds,
        submittedFormValues,
        submittedExtendColumns,
      );

      toast.show({
        render: ({id}) => (
          <ToastAlert
            id={id}
            title="Success"
            description="Group logs completed successfully."
            isClosable={true}
            onClose={() => toast.close(id)}
            variant="left-accent"
            status="success"
          />
        ),
        placement: 'top',
      });
    } catch (error) {
      console.error('Error submitting group completion:', error);
      toast.show({
        render: ({id}) => (
          <ToastAlert
            id={id}
            title="Error"
            description="Failed to complete group logs."
            isClosable={true}
            onClose={() => toast.close(id)}
            variant="left-accent"
            status="error"
          />
        ),
        placement: 'top',
      });
    }
  };

  const sectionStyle = {
    backgroundColor: mode === 'dark' ? 'gray.800' : 'gray.100',
    borderRadius: 'sm',
    marginBottom: 2,
    padding: 2,
    shadow: 1,
  };

  const labelStyle = {
    fontSize: 'sm',
    fontWeight: 'bold',
    color: mode === 'dark' ? 'orange.300' : 'orange.600',
    marginBottom: 1,
  };

  const textStyle = {
    fontSize: 'sm',
    color: mode === 'dark' ? 'white' : 'black',
  };

  return (
    <VStack flex={1} space={1} p={1}>
      <ScrollView flex={1}>
        <VStack space={1}>
          <Box {...sectionStyle}>
            <Text {...labelStyle}>On-Site Information</Text>
            <HStack space={1}>
              <Box flex={5}>
                <DatePickerControl
                  label="Date"
                  date={formValues.arrive_date ? new Date(formValues.arrive_date) : undefined}
                  showPicker={showDatePicker.arrive_date}
                  setShowPicker={show =>
                    setShowDatePicker(prev => ({...prev, arrive_date: show}))
                  }
                  onChange={async (event, selectedDate) => {
                    setShowDatePicker(prev => ({...prev, arrive_date: false}));
                    if (selectedDate) {
                      handleFieldChange('arrive_date', selectedDate.toISOString());
                      await getCurrentLocation((lat, long) => {
                        handleFieldChange('arrive_latitude', lat);
                        handleFieldChange('arrive_longitude', long);
                      });
                    }
                  }}
                  iconName="calendar"
                  textStyle={textStyle}
                  mode={mode}
                />
              </Box>
              <Box flex={4}>
                <DatePickerControl
                  label="Time"
                  date={formValues.arrive_time ? new Date(formValues.arrive_time) : undefined}
                  showPicker={showDatePicker.arrive_time}
                  setShowPicker={show =>
                    setShowDatePicker(prev => ({...prev, arrive_time: show}))
                  }
                  onChange={async (event, selectedDate) => {
                    setShowDatePicker(prev => ({...prev, arrive_time: false}));
                    if (selectedDate) {
                      handleFieldChange('arrive_time', selectedDate.toISOString());
                      await getCurrentLocation((lat, long) => {
                        handleFieldChange('arrive_latitude', lat);
                        handleFieldChange('arrive_longitude', long);
                      });
                    }
                  }}
                  iconName="clock-outline"
                  textStyle={textStyle}
                  mode={mode}
                />
              </Box>
              <Box flex={1}>
                <Text {...labelStyle}>GPS</Text>
                <IconButton
                  {...dateTimePickerStyle}
                  icon={
                    <Icon
                      as={MaterialCommunityIcons}
                      name="map-marker-radius"
                      onPress={updateOnSiteData}
                    />
                  }
                  isLoading={isGettingLocation}
                  isLoadingText="Getting location..."
                />
              </Box>
            </HStack>
            <HStack justifyContent="flex-end">
              <Text color={mode === 'dark' ? 'white' : 'black'} fontSize="xs">
                {formValues.arrive_latitude && formValues.arrive_longitude
                  ? `Coord: ${formValues.arrive_latitude.toFixed(
                    4,
                  )}, ${formValues.arrive_longitude.toFixed(4)}`
                  : 'Coord: Not set'}
              </Text>
            </HStack>
          </Box>

          <Box {...sectionStyle}>
            <Text {...labelStyle}>Completion Information</Text>
            <HStack space={1}>
              <Box flex={5}>
                <DatePickerControl
                  label="Date"
                  date={formValues.completed_date ? new Date(formValues.completed_date) : undefined}
                  showPicker={showDatePicker.completed_date}
                  setShowPicker={show =>
                    setShowDatePicker(prev => ({...prev, completed_date: show}))
                  }
                  onChange={async (event, selectedDate) => {
                    setShowDatePicker(prev => ({...prev, completed_date: false}));
                    if (selectedDate) {
                      handleFieldChange('completed_date', selectedDate.toISOString());
                      await getCurrentLocation((lat, long) => {
                        handleFieldChange('completion_latitude', lat);
                        handleFieldChange('completion_longitude', long);
                      });
                    }
                  }}
                  iconName="calendar"
                  textStyle={textStyle}
                  mode={mode}
                />
              </Box>
              <Box flex={4}>
                <DatePickerControl
                  label="Time"
                  date={formValues.completed_time ? new Date(formValues.completed_time) : undefined}
                  showPicker={showDatePicker.completed_time}
                  setShowPicker={show =>
                    setShowDatePicker(prev => ({...prev, completed_time: show}))
                  }
                  onChange={async (event, selectedDate) => {
                    setShowDatePicker(prev => ({...prev, completed_time: false}));
                    if (selectedDate) {
                      handleFieldChange('completed_time', selectedDate.toISOString());
                      await getCurrentLocation((lat, long) => {
                        handleFieldChange('completion_latitude', lat);
                        handleFieldChange('completion_longitude', long);
                      });
                    }
                  }}
                  iconName="clock-outline"
                  textStyle={textStyle}
                  mode={mode}
                />
              </Box>
              <Box flex={1}>
                <Text {...labelStyle}>GPS</Text>
                <IconButton
                  {...dateTimePickerStyle}
                  icon={
                    <Icon
                      as={MaterialCommunityIcons}
                      name="map-marker-radius"
                      onPress={updateCompleteData}
                    />
                  }
                  isLoading={isGettingLocation}
                  isLoadingText="Getting location..."
                />
              </Box>
            </HStack>
            <HStack justifyContent="flex-end">
              <Text color={mode === 'dark' ? 'white' : 'black'} fontSize="xs">
                {formValues.completion_latitude && formValues.completion_longitude
                  ? `Coord: ${formValues.completion_latitude.toFixed(
                    4,
                  )}, ${formValues.completion_longitude.toFixed(4)}`
                  : 'Coord: Not set'}
              </Text>
            </HStack>
          </Box>

          <Box {...sectionStyle}>
            <Text {...labelStyle}>
              {localItem?.description ||
                'No Extension fields related to the Service Agreement'}
            </Text>
            {localItem?.extend_columns?.map((column, index) => (
              <DynamicFormField
                key={index}
                column={column}
                value={formValues[column.column_name]}
                onChange={value => handleFieldChange(column.column_name, value)}
                mode={mode}
              />
            ))}
          </Box>
        </VStack>
      </ScrollView>
      <Button
        {...submitButtonStyle}
        onPress={handleSubmit}
        leftIcon={
          <Icon as={MaterialCommunityIcons} name="briefcase-check" size="sm" />
        }
        height={10}
        fontSize="sm">
        Submit Group Completion
      </Button>
    </VStack>
  );
};

export default CompleteGroup;
