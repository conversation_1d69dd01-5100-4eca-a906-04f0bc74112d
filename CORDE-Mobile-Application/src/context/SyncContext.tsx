import React, { createContext, useState, useContext } from 'react';

interface SyncContextType {
  isSyncingBasicData: boolean;
  setIsSyncingBasicData: (syncing: boolean) => void;
  isSyncingSelectedDate: boolean;
  setIsSyncingSelectedDate: (syncing: boolean) => void;
}

const SyncContext = createContext<SyncContextType>({
  isSyncingBasicData: false,
  setIsSyncingBasicData: () => {},
  isSyncingSelectedDate: false,
  setIsSyncingSelectedDate: () => {},
});

export const SyncProvider: React.FC = ({ children }) => {
  const [isSyncingBasicData, setIsSyncingBasicData] = useState(false);
  const [isSyncingSelectedDate, setIsSyncingSelectedDate] = useState(false);

  return (
    <SyncContext.Provider
      value={{
        isSyncingBasicData,
        setIsSyncingBasicData,
        isSyncingSelectedDate,
        setIsSyncingSelectedDate,
      }}
    >
      {children}
    </SyncContext.Provider>
  );
};

export const useSync = () => useContext(SyncContext);
