import React, { createContext, useState, useContext, useEffect, useCallback } from 'react';
import { AuthService } from '../services/AuthService';
import { User } from '../database/Users';

interface AuthContextType {
  user: User | null;
  setUser: (user: User | null) => void;
  isLoading: boolean;
  checkAuth: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType>({
  user: null,
  setUser: () => {},
  isLoading: true,
  checkAuth: async () => {},
});

export const AuthProvider: React.FC = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [hasCheckedAuth, setHasCheckedAuth] = useState(false);

  const checkAuth = useCallback(async () => {
    if (hasCheckedAuth) return; // 避免重复检查
    setIsLoading(true);
    try {
      const authenticatedUser = await AuthService.getCurrentUser();
      // console.log('Authenticated user:', authenticatedUser);
      if (authenticatedUser) {
        setUser(authenticatedUser);
      } else {
        const autoLoginUser = await AuthService.autoLogin();
        if (autoLoginUser) {
          setUser(autoLoginUser);
        }
      }
    } catch (error) {
      console.error('Auth check failed:', error);
    } finally {
      setIsLoading(false);
      setHasCheckedAuth(true);
    }
  }, [hasCheckedAuth]);

  useEffect(() => {
    checkAuth();
  }, [checkAuth]);

  const contextValue = {
    user,
    setUser: (newUser: User | null) => {
      setUser(newUser);
      setHasCheckedAuth(false); // 重置认证检查状态
    },
    isLoading,
    checkAuth,
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => useContext(AuthContext);
