import React, {useEffect} from 'react';
import {createNativeStackNavigator} from '@react-navigation/native-stack';
import LoginScreen from '../screens/LoginScreen';
import MainDashboardScreen from '../screens/MainDashboardScreen';
import NewLogScreen from '../screens/NewLogScreen.tsx';
import LogListScreen from '../screens/LogListScreen.tsx';
import LogHeaderScreen from '../screens/LogHeaderScreen.tsx';
import SyncLogScreen from '../screens/SyncLogScreen.tsx';
import DebugScreen from '../screens/DebugScreen.tsx';
import MapMarkerScreen from '../screens/MapMarkerScreen';
import SettingsScreen from '../screens/SettingsScreen';
import CreateGroupScreen from '../screens/CreateGroupScreen.tsx';
import CompleteGroupScreen from '../screens/CompleteGroupScreen.tsx';
import {LogList} from "../database/LogList.ts";
import {ServiceAgreement} from "../database/ServiceAgreement.ts";
import {useAuth} from "../context/AuthContext.tsx";
import {Text, View} from "react-native";
import UserProfile from "../components/UserProfile/UserProfile.tsx";
import { networkService } from '../utils/NetworkUtils';

export type RootStackParamList = {
  Login: undefined;
  MainDashboard: undefined;
  NewLog: undefined;
  LogList: undefined;
  LogHeader: {item: {log_header_id: number} | undefined};
  SyncLog: undefined;
  'Complete Group': {
    selectedAgreement: ServiceAgreement;
    relatedLogs: LogList[];
    extendColumns: any[];
  };
  UserProfile: undefined;
};

const Stack = createNativeStackNavigator<RootStackParamList>();

const LoadingScreen = () => (
  <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
    <Text>Loading...</Text>
  </View>
);

const AppNavigator: React.FC = () => {
  const { user, isLoading } = useAuth();

  // console.log('AppNavigator render:', { user, isLoading });

  if (isLoading) {
    return <LoadingScreen />;
  }

  const onNavigationStateChange = (state: any) => {
    const currentRouteName = state?.routes[state.index]?.name || '';
    networkService.setCurrentScreen(currentRouteName);
  };

  return (
    <Stack.Navigator
      initialRouteName={user ? "Main Dashboard" : "Login"}
      screenListeners={{
        state: (e) => {
          onNavigationStateChange(e.data.state);
        },
      }}
    >
      {user ? (
        <>
          <Stack.Screen
            name="Main Dashboard"
            component={MainDashboardScreen}
            options={{ headerShown: false }}
          />
          <Stack.Screen
            name="Create New Log"
            component={NewLogScreen}
            options={{
              headerShown: true,
              headerStyle: {
                backgroundColor: '#282828',
              },
              headerTintColor: 'white',
              headerTitleAlign: 'center',
              // headerRight: () => <ThemeToggle />,
            }}
          />
          <Stack.Screen
            name="Log List"
            component={LogListScreen}
            options={{
              headerShown: true,
              headerStyle: {
                backgroundColor: '#282828',
              },
              headerTintColor: 'white',
              headerTitleAlign: 'center',
            }}
          />
          <Stack.Screen
            name="Log Header"
            component={LogHeaderScreen}
            options={({route}) => ({
              headerShown: true,
              headerStyle: {
                backgroundColor: '#282828',
              },
              headerTintColor: 'white',
              headerTitleAlign: 'center',
              title: `Log Header: ${
                route.params?.item?.log_header_id ?? 'Unknown'
              }`,
              headerTitleStyle: {
                fontSize: 18,
                fontWeight: 'normal',
              },
            })}
          />
          <Stack.Screen
            name="Sync Log"
            component={SyncLogScreen}
            options={{
              headerShown: true,
              headerStyle: {
                backgroundColor: '#282828',
              },
              headerTintColor: 'white',
              headerTitleAlign: 'center',
            }}
          />
          <Stack.Screen
            name="Debug"
            component={DebugScreen}
            options={{
              headerShown: true,
              headerStyle: {
                backgroundColor: '#282828',
              },
              headerTintColor: 'white',
              headerTitleAlign: 'center',
            }}
          />
          <Stack.Screen
            name="UserProfile"
            component={UserProfile}
            options={{
              title: 'User Profile',
              headerStyle: {
                backgroundColor: '#282828',
              },
              headerTintColor: 'white',
              headerTitleAlign: 'center',
            }}
          />
          <Stack.Screen
            name="Map Marker"
            component={MapMarkerScreen}
            options={{
              title: 'Map Marker',
              headerStyle: {
                backgroundColor: '#282828',
              },
              headerTintColor: 'white',
              headerTitleAlign: 'center',
            }}
          />
          <Stack.Screen
            name="Settings"
            component={SettingsScreen}
            options={{
              headerShown: true,
              headerStyle: {
                backgroundColor: '#282828',
              },
              headerTintColor: 'white',
              headerTitleAlign: 'center',
            }}
          />
          <Stack.Screen
            name="Create Group"
            component={CreateGroupScreen}
            options={{
              headerShown: true,
              headerStyle: {
                backgroundColor: '#282828',
              },
              headerTintColor: 'white',
              headerTitleAlign: 'center',
            }}
          />
          <Stack.Screen
            name="Complete Group"
            component={CompleteGroupScreen}
            options={{
              title: 'Complete Group',
              headerStyle: {
                backgroundColor: '#282828',
              },
              headerTintColor: 'white',
              headerTitleAlign: 'center',
            }}
            listeners={{
              focus: (e) => {
                // console.log('CompleteGroup screen focused', e);
              },
            }}
          />
        </>
      ) : (
        <Stack.Screen
          name="Login"
          component={LoginScreen}
          options={{ headerShown: false }}
        />
      )}
    </Stack.Navigator>
  );
};

export default AppNavigator;
