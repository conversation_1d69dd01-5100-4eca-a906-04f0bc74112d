import React from 'react';
import {createMaterialTopTabNavigator} from '@react-navigation/material-top-tabs';
import SelectedDateSync from '../components/SyncLogScreen/SelectedDateSync';
import BasicDataSync from '../components/SyncLogScreen/BasicDataSync';
import { useTheme } from '../styles/ThemeContext';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';

const Tab = createMaterialTopTabNavigator();

const SyncLogTabNavigator = () => {
  const { mode } = useTheme();

  return (
    <Tab.Navigator
      screenOptions={{
        tabBarActiveTintColor: mode === 'dark' ? 'white' : 'black',
        tabBarInactiveTintColor: mode === 'dark' ? 'gray' : 'darkgray',
        tabBarIndicatorStyle: {
          backgroundColor: 'orange',
          height: 4,
        },
        tabBarStyle: {
          backgroundColor: mode === 'dark' ? 'black' : 'white',
          height: 60,
        },
        tabBarPressColor: 'orange',
        tabBarLabelStyle: {
          fontSize: 16,
          fontWeight: 'bold',
        },
      }}>
      <Tab.Screen
        name="Selected Date"
        component={SelectedDateSync}
      />
      <Tab.Screen
        name="Basic Data"
        component={BasicDataSync}
      />
    </Tab.Navigator>
  );
};

export default SyncLogTabNavigator;
