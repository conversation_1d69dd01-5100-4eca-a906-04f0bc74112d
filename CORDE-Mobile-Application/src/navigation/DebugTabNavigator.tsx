import React from 'react';
import {createMaterialTopTabNavigator} from '@react-navigation/material-top-tabs';
import {useTheme} from 'native-base';
import LogItemComponent from '../components/DebugScreen/LogItemComponent';
import FileItemComponent from '../components/DebugScreen/FileItemComponent';
import SyncLogItemComponent from '../components/DebugScreen/SyncLogItemComponent';
import LogExtensionItemComponent from '../components/DebugScreen/LogExtensionItemComponent';
import AssetCoordinatesItemComponent from '../components/DebugScreen/AssetCoordinatesItemComponent';

const Tab = createMaterialTopTabNavigator();

const DebugTabNavigator = ({data}) => {
  const {colors} = useTheme();

  return (
    <Tab.Navigator
      screenOptions={{
        tabBarActiveTintColor: colors.pink[600],
        tabBarInactiveTintColor: colors.gray[400],
        tabBarLabelStyle: {fontSize: 12, fontWeight: 'bold'},
        tabBarIndicatorStyle: {
          backgroundColor: colors.pink[400],
        },
        tabBarPressColor: 'pink',
      }}>
      <Tab.Screen name="Logs">
        {() => <LogItemComponent logs={data.logs} />}
      </Tab.Screen>
      <Tab.Screen name="Files">
        {() => <FileItemComponent files={data.files} />}
      </Tab.Screen>
      <Tab.Screen name="Sync Logs">
        {() => <SyncLogItemComponent syncLogs={data.syncLogs} />}
      </Tab.Screen>
      <Tab.Screen name="Log Extensions">
        {() => <LogExtensionItemComponent logExtensions={data.logExtensions} />}
      </Tab.Screen>
      <Tab.Screen name="Asset Coordinates">
        {() => <AssetCoordinatesItemComponent assetCoordinates={data.assetCoordinates} />}
      </Tab.Screen>
    </Tab.Navigator>
  );
};

export default DebugTabNavigator;
