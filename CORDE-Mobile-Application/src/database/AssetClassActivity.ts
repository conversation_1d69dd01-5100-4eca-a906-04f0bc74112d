import { executeQuery } from './DatabaseInit';

export class AssetClassActivity {
  line_id: number;
  class1: string;
  class2: string;
  class3: string;
  activity: string;
  contract_code: string;

  constructor(data: {
    line_id: number;
    class1: string;
    class2: string;
    class3: string;
    activity: string;
    contract_code: string;
  }) {
    this.line_id = data.line_id;
    this.class1 = data.class1;
    this.class2 = data.class2;
    this.class3 = data.class3;
    this.activity = data.activity;
    this.contract_code = data.contract_code;
  }

  static async upsert(activity: AssetClassActivity): Promise<void> {
    try {
      const existingActivity = await this.getById(activity.line_id);
      if (existingActivity) {
        await this.update(activity.line_id, activity);
      } else {
        await this.insert(activity);
      }
      console.log('AssetClassActivity upserted successfully');
    } catch (error) {
      console.error('Error upserting AssetClassActivity:', error);
    }
  }

  static async insert(activity: AssetClassActivity): Promise<void> {
    try {
      await executeQuery(
        `
        INSERT INTO asset_class_activity (
          line_id, class1, class2, class3, activity, contract_code
        ) VALUES (?, ?, ?, ?, ?, ?)
      `,
        [
          activity.line_id,
          activity.class1,
          activity.class2,
          activity.class3,
          activity.activity,
          activity.contract_code,
        ]
      );
      console.log('AssetClassActivity inserted successfully');
    } catch (error) {
      console.error('Error inserting AssetClassActivity:', error);
    }
  }

  static async getAll(): Promise<AssetClassActivity[]> {
    try {
      const result = await executeQuery('SELECT * FROM asset_class_activity');
      let activities: AssetClassActivity[] = [];
      for (let i = 0; i < result.rows.length; i++) {
        activities.push(new AssetClassActivity(result.rows.item(i)));
      }
      return activities;
    } catch (error) {
      console.error('Error fetching AssetClassActivities:', error);
      return [];
    }
  }

  static async getById(lineId: number): Promise<AssetClassActivity | null> {
    try {
      const result = await executeQuery(
        'SELECT * FROM asset_class_activity WHERE line_id = ?',
        [lineId]
      );
      if (result.rows.length > 0) {
        return new AssetClassActivity(result.rows.item(0));
      }
      return null;
    } catch (error) {
      console.error('Error fetching AssetClassActivity by ID:', error);
      return null;
    }
  }

  static async update(
    lineId: number,
    activity: Partial<AssetClassActivity>
  ): Promise<void> {
    const { class1, class2, class3, activity: act, contract_code } = activity;
    try {
      await executeQuery(
        `
        UPDATE asset_class_activity
        SET
          class1 = COALESCE(?, class1),
          class2 = COALESCE(?, class2),
          class3 = COALESCE(?, class3),
          activity = COALESCE(?, activity),
          contract_code = COALESCE(?, contract_code)
        WHERE line_id = ?
      `,
        [class1, class2, class3, act, contract_code, lineId]
      );
      console.log('AssetClassActivity updated successfully');
    } catch (error) {
      console.error('Error updating AssetClassActivity:', error);
    }
  }

  static async delete(lineId: number): Promise<void> {
    try {
      await executeQuery('DELETE FROM asset_class_activity WHERE line_id = ?', [
        lineId,
      ]);
      console.log('AssetClassActivity deleted successfully');
    } catch (error) {
      console.error('Error deleting AssetClassActivity:', error);
    }
  }

  static async getActivities(class1?: string, class2?: string, class3?: string): Promise<AssetClassActivity[]> {
    try {
      let query = 'SELECT * FROM asset_class_activity WHERE 1=1';
      let params: (string | null)[] = [];

      if (class1) {
        query += ' AND class1 = ?';
        params.push(class1);
      }
      if (class2) {
        query += ' AND class2 = ?';
        params.push(class2);
      }
      if (class3) {
        query += ' AND class3 = ?';
        params.push(class3);
      }

      const result = await executeQuery(query, params);
      let activities: AssetClassActivity[] = [];
      for (let i = 0; i < result.rows.length; i++) {
        activities.push(new AssetClassActivity(result.rows.item(i)));
      }
      return activities;
    } catch (error) {
      console.error('Error fetching AssetClassActivities:', error);
      return [];
    }
  }

}
