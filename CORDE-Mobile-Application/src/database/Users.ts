// src/database/Users.ts
import { executeQuery } from './DatabaseInit';

export class User {
  user_id?: number;
  username: string;
  full_name: string;
  email: string;
  phone_number: string;
  last_login: string;
  last_error_message: string;
  show_tutorial: boolean;
  person_id: number;
  fin_co_code: string;
  is_authenticated: boolean;

  constructor(data: {
    user_id?: number;
    username: string;
    full_name: string;
    email?: string;
    phone_number?: string;
    last_login?: string;
    last_error_message?: string;
    show_tutorial?: boolean;
    person_id: number;
    fin_co_code: string;
    is_authenticated: boolean;
  }) {
    this.user_id = data.user_id;
    this.username = data.username;
    this.full_name = data.full_name;
    this.email = data.email || '';
    this.phone_number = data.phone_number || '';
    this.last_login = data.last_login || '';
    this.last_error_message = data.last_error_message || '';
    this.show_tutorial = data.show_tutorial ?? true;
    this.person_id = data.person_id;
    this.fin_co_code = data.fin_co_code;
    this.is_authenticated = data.is_authenticated;
  }

  static async upsert(user: User): Promise<User> {
    const existingUser = await this.getByUsername(user.username);
    if (existingUser) {
      user.user_id = existingUser.user_id;
      await this.update(existingUser.user_id!, user);
      return user;
    } else {
      const userId = await this.insert(user);
      user.user_id = userId;
      return user;
    }
  }

  static async insert(user: User): Promise<void> {
    const sql = `
      INSERT INTO users (
        username, full_name, email, phone_number, last_login, 
        last_error_message, show_tutorial, person_id, fin_co_code, is_authenticated
      )
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;
    await executeQuery(sql, [
      user.username, user.full_name, user.email, user.phone_number,
      user.last_login, user.last_error_message, user.show_tutorial ? 1 : 0,
      user.person_id, user.fin_co_code, user.is_authenticated ? 1 : 0
    ]);
  }

  static async update(userId: number, userData: Partial<User>): Promise<void> {
    const updateFields = [];
    const values = [];

    for (const [key, value] of Object.entries(userData)) {
      if (value !== undefined) {
        updateFields.push(`${key} = ?`);
        values.push(value);
      }
    }

    if (updateFields.length === 0) {
      return; // No fields to update
    }

    const sql = `
    UPDATE users
    SET ${updateFields.join(', ')}
    WHERE user_id = ?
  `;
    values.push(userId);

    await executeQuery(sql, values);
  }

  static async updateAll(updates: Partial<User>): Promise<void> {
    const sql = `
      UPDATE users
      SET is_authenticated = ?
    `;
    await executeQuery(sql, [updates.is_authenticated ? 1 : 0]);
  }

  static async getAll(): Promise<User[]> {
    const sql = 'SELECT * FROM users';
    const result = await executeQuery(sql);
    return result.rows.raw().map(row => new User(row));
  }

  static async getByUsername(username: string): Promise<User | null> {
    const sql = 'SELECT * FROM users WHERE username = ? LIMIT 1';
    const result = await executeQuery(sql, [username]);
    if (result.rows.length > 0) {
      return new User(result.rows.item(0));
    }
    return null;
  }

  static async updateLastLogin(userId: number, lastLogin: string): Promise<void> {
    const sql = `
      UPDATE users
      SET last_login = ?
      WHERE user_id = ?
    `;
    await executeQuery(sql, [lastLogin, userId]);
  }

  static async updateLastErrorMessage(userId: number, errorMessage: string): Promise<void> {
    const sql = `
      UPDATE users
      SET last_error_message = ?
      WHERE user_id = ?
    `;
    await executeQuery(sql, [errorMessage, userId]);
  }

  static async updateShowTutorial(userId: number, showTutorial: boolean): Promise<void> {
    const sql = `
      UPDATE users
      SET show_tutorial = ?
      WHERE user_id = ?
    `;
    await executeQuery(sql, [showTutorial ? 1 : 0, userId]);
  }

  static async getById(userId: number) {
    const sql = 'SELECT * FROM users WHERE user_id = ? LIMIT 1';
    await executeQuery(sql, [userId]);
  }

  static async getByPersonId(personId: number): Promise<User | null> {
    try {
      const sql = 'SELECT * FROM users WHERE person_id = ? LIMIT 1';
      const result = await executeQuery(sql, [personId]);

      if (result.rows.length > 0) {
        return new User(result.rows.item(0));
      }
      return null;
    } catch (error) {
      console.error('Error getting user by person_id:', error);
      return null;
    }
  }


}
