export const COMBO_LIST_OPTIONS = {
  AssetClassActivity: [], // 这里应该是从存储过程获取的数据，暂时留空
  RepairType: [
    {label: 'Under Contract', value: 'Under Contract'},
    {label: 'Dayworks', value: 'Dayworks'},
    {label: 'Approval Req', value: 'Approval Req'},
  ],
  TskElyCloseSDC: [
    {label: 'NP Closed by CORDE', value: 'NP Closed by CORDE'},
    {label: 'NP Closed by CORDE H&S', value: 'NP Closed by CORDE H&S'},
    {label: 'NP Outside Contract', value: 'NP Outside Contract'},
    {label: 'NP Weather', value: 'NP Weather'},
    {label: 'NP Within Specification', value: 'NP Within Specification'},
  ],
  HDCGradeFault: [
    {label: 'GDC Corrugations', value: 'GDC Corrugations'},
    {label: 'GDP Potholes', value: 'GDP Potholes'},
    {label: 'USR Poor Cross Sect', value: 'USR Poor Cross Sect'},
    {label: 'SR Scouring>70', value: 'SR Scouring>70'},
    {label: 'SR2 Scouring >400', value: 'SR2 Scouring >400'},
    {label: 'R3 Rutting', value: 'R3 Rutting'},
  ],
  FordStatus: [
    {label: '1 Open', value: '1 Open'},
    {label: '2 Closed', value: '2 Closed'},
    {label: '3 Extreme Caution', value: '3 Extreme Caution'},
  ],
  AssetCond: [
    {label: '0 - Cannot Assess', value: '0 - Cannot Assess'},
    {label: '1 - Excellent', value: '1 - Excellent'},
    {label: '2 - Good', value: '2 - Good'},
    {label: '3 - Average', value: '3 - Average'},
    {label: '4 - Poor', value: '4 - Poor'},
    {label: '5 - Very Poor', value: '5 - Very Poor'},
  ],
};
