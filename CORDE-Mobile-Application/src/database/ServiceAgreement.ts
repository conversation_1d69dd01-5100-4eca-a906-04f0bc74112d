import { executeQuery } from './DatabaseInit';

export class ServiceAgreement {
  id: number;
  code: string;
  description: string;
  details: string;
  client_source: string;
  company_id: string;
  default_job_code: string;
  default_parent_agreement: string;

  constructor(data: {
    id: number;
    code: string;
    description: string;
    details: string;
    client_source: string;
    company_id: string;
    default_job_code: string;
    default_parent_agreement: string;
  }) {
    this.id = data.id;
    this.code = data.code;
    this.description = data.description;
    this.details = data.details;
    this.client_source = data.client_source;
    this.company_id = data.company_id;
    this.default_job_code = data.default_job_code;
    this.default_parent_agreement = data.default_parent_agreement;
  }

  static async upsert(serviceAgreement: ServiceAgreement): Promise<void> {
    try {
      const existingAgreement = await this.getById(serviceAgreement.id);
      if (existingAgreement) {
        await this.update(serviceAgreement.id, serviceAgreement);
      } else {
        await this.insert(serviceAgreement);
      }
      console.log('ServiceAgreement upserted successfully');
    } catch (error) {
      console.error('Error upserting ServiceAgreement:', error);
    }
  }

  static async insert(serviceAgreement: ServiceAgreement): Promise<void> {
    try {
      await executeQuery(
        `
        INSERT INTO service_agreement (
          id, code, description, details, client_source, company_id, default_job_code, default_parent_agreement
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
      `,
        [
          serviceAgreement.id,
          serviceAgreement.code,
          serviceAgreement.description,
          serviceAgreement.details,
          serviceAgreement.client_source,
          serviceAgreement.company_id,
          serviceAgreement.default_job_code,
          serviceAgreement.default_parent_agreement,
        ]
      );
      console.log('ServiceAgreement inserted successfully');
    } catch (error) {
      console.error('Error inserting ServiceAgreement:', error);
    }
  }

  static async getAll(limit: number = 10): Promise<ServiceAgreement[]> {
    try {
      const result = await executeQuery(`SELECT * FROM service_agreement LIMIT ?`, [limit]);
      let agreements: ServiceAgreement[] = [];
      for (let i = 0; i < result.rows.length; i++) {
        agreements.push(new ServiceAgreement(result.rows.item(i)));
      }
      return agreements;
    } catch (error) {
      console.error('Error fetching ServiceAgreements:', error);
      return [];
    }
  }

  static async getById(id: number): Promise<ServiceAgreement | null> {
    try {
      const result = await executeQuery(
        'SELECT * FROM service_agreement WHERE id = ?',
        [id]
      );
      if (result.rows.length > 0) {
        return new ServiceAgreement(result.rows.item(0));
      }
      return null;
    } catch (error) {
      console.error('Error fetching ServiceAgreement by ID:', error);
      return null;
    }
  }

  static async update(
    id: number,
    serviceAgreement: Partial<ServiceAgreement>
  ): Promise<void> {
    const { code, description, details, client_source, company_id, default_job_code, default_parent_agreement } = serviceAgreement;
    try {
      await executeQuery(
        `
        UPDATE service_agreement
        SET
          code = COALESCE(?, code),
          description = COALESCE(?, description),
          details = COALESCE(?, details),
          client_source = COALESCE(?, client_source),
          company_id = COALESCE(?, company_id),
          default_job_code = COALESCE(?, default_job_code),
          default_parent_agreement = COALESCE(?, default_parent_agreement)
        WHERE id = ?
      `,
        [code, description, details, client_source, company_id, default_job_code, default_parent_agreement, id]
      );
      console.log('ServiceAgreement updated successfully');
    } catch (error) {
      console.error('Error updating ServiceAgreement:', error);
    }
  }

  static async delete(id: number): Promise<void> {
    try {
      await executeQuery('DELETE FROM service_agreement WHERE id = ?', [
        id,
      ]);
      console.log('ServiceAgreement deleted successfully');
    } catch (error) {
      console.error('Error deleting ServiceAgreement:', error);
    }
  }

  static async search(query: string, limit: number = 10): Promise<ServiceAgreement[]> {
    try {
      const result = await executeQuery(
        `SELECT * FROM service_agreement 
       WHERE code LIKE ? OR description LIKE ? 
       LIMIT ?`,
        [`%${query}%`, `%${query}%`, limit]
      );
      let agreements: ServiceAgreement[] = [];
      for (let i = 0; i < result.rows.length; i++) {
        agreements.push(new ServiceAgreement(result.rows.item(i)));
      }
      return agreements;
    } catch (error) {
      console.error('Error searching ServiceAgreements:', error);
      return [];
    }
  }

}
