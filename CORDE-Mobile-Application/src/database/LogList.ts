import {executeQuery} from './DatabaseInit';
import {COMBO_LIST_OPTIONS} from './ComboListOptions.ts';
import {NewLogModel} from '../models/NewLogModel.ts';
import {UpdateLogModel} from '../models/UpdateLogModel.ts';
import {LogFilter} from "../types/LogFilterTypes.ts";
import {AuthService} from "../services/AuthService.ts";

export class LogList {
  local_log_id?: number;
  log_header_id?: number;
  status: string;
  schd_date: string;
  log_type: string;
  company_id: string;
  allocation_person_id: number;
  allocation_person_full_name: string;
  completed_time: string;
  completed_date: string;
  description: string;
  log_details: string;
  arrive_date: string;
  arrive_time: string;
  service_agreement_id: number;
  asset_id: number;
  completion_details: string;
  contact_details: string;
  asset_code: string;
  asset_description: string;
  log_address: string;
  stored_files: string;
  arrive_latitude: number;
  arrive_longitude: number;
  completion_latitude: number;
  completion_longitude: number;
  is_completed: boolean;
  is_draft: boolean;
  create_log_timestamp: string;
  event_time_stamp: string;
  updated_date: string;
  service_type: string;
  priority: string;
  comments: string;
  order_no: string;
  service_agreement_description?: string;
  task_code?: string;
  // Asset coordinates from asset_coordinates table
  asset_latitude?: number;
  asset_longitude?: number;
  extend_columns?: Array<{
    data_type: string;
    required: boolean;
    combo_list: string | Array<{label: string; value: string}>;
    column_name: string;
  }>;

  constructor(data: {
    local_log_id?: number;
    log_header_id?: number;
    status: string;
    schd_date: string;
    log_type: string;
    company_id: string;
    allocation_person_id: number;
    allocation_person_full_name: string;
    completed_time: string;
    completed_date: string;
    description: string;
    log_details: string;
    arrive_date: string;
    arrive_time: string;
    service_agreement_id: number;
    asset_id: number;
    completion_details: string;
    contact_details: string;
    asset_code: string;
    asset_description: string;
    log_address: string;
    stored_files: string;
    arrive_latitude: number;
    arrive_longitude: number;
    completion_latitude: number;
    completion_longitude: number;
    is_completed: boolean;
    is_draft: boolean;
    create_log_timestamp: string;
    event_time_stamp: string;
    updated_date: string;
    service_type: string;
    priority: string;
    comments: string;
    order_no: string;
    service_agreement_description?: string;
    task_code?: string;
    asset_latitude?: number;
    asset_longitude?: number;
    extend_columns?: any[];
  }) {
    this.local_log_id = data.local_log_id;
    this.log_header_id = data.log_header_id;
    this.status = data.status;
    this.schd_date = data.schd_date;
    this.log_type = data.log_type;
    this.company_id = data.company_id;
    this.allocation_person_id = data.allocation_person_id;
    this.allocation_person_full_name = data.allocation_person_full_name;
    this.completed_time = data.completed_time;
    this.completed_date = data.completed_date;
    this.description = data.description;
    this.log_details = data.log_details;
    this.arrive_date = data.arrive_date;
    this.arrive_time = data.arrive_time;
    this.service_agreement_id = data.service_agreement_id;
    this.asset_id = data.asset_id;
    this.completion_details = data.completion_details;
    this.contact_details = data.contact_details;
    this.asset_code = data.asset_code;
    this.asset_description = data.asset_description;
    this.log_address = data.log_address;
    this.stored_files = data.stored_files;
    this.arrive_latitude = data.arrive_latitude;
    this.arrive_longitude = data.arrive_longitude;
    this.completion_latitude = data.completion_latitude;
    this.completion_longitude = data.completion_longitude;
    this.is_completed = data.is_completed;
    this.is_draft = data.is_draft;
    this.create_log_timestamp = data.create_log_timestamp;
    this.event_time_stamp = data.event_time_stamp;
    this.updated_date = data.updated_date;
    this.service_type = data.service_type;
    this.priority = data.priority;
    this.comments = data.comments;
    this.order_no = data.order_no;
    this.service_agreement_description = data.service_agreement_description;
    this.task_code = data.task_code;
    this.asset_latitude = data.asset_latitude;
    this.asset_longitude = data.asset_longitude;
    this.extend_columns = data.extend_columns;
  }

  static async insert(log: LogList): Promise<number> {
    try {
      const result = await executeQuery(
        `
        INSERT INTO log_list (
          log_header_id, status, schd_date, log_type, company_id, allocation_person_id, allocation_person_full_name, completed_time,
          completed_date, description, log_details, arrive_date, arrive_time, service_agreement_id, asset_id,
          completion_details, contact_details, asset_code, asset_description, log_address, stored_files,
          arrive_latitude, arrive_longitude, completion_latitude, completion_longitude,
          is_completed, is_draft, create_log_timestamp, event_time_stamp, updated_date, service_type, priority, comments, order_no, task_code
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?,?)
      `,
        [
          log.log_header_id,
          log.status,
          log.schd_date,
          log.log_type,
          log.company_id,
          log.allocation_person_id,
          log.allocation_person_full_name,
          log.completed_time,
          log.completed_date,
          log.description,
          log.log_details,
          log.arrive_date,
          log.arrive_time,
          log.service_agreement_id,
          log.asset_id,
          log.completion_details,
          log.contact_details,
          log.asset_code,
          log.asset_description,
          log.log_address,
          log.stored_files,
          log.arrive_latitude,
          log.arrive_longitude,
          log.completion_latitude,
          log.completion_longitude,
          log.is_completed,
          log.is_draft,
          log.create_log_timestamp,
          log.event_time_stamp,
          log.updated_date,
          log.service_type,
          log.priority,
          log.comments,
          log.order_no,
          log.task_code,
        ],
      );
      console.log('LogList inserted successfully');
      return result.insertId;
    } catch (error) {
      console.error('Error inserting log:', error);
      throw error;
    }
  }

  static async getAll(
    page: number = 1,
    pageSize: number = 20,
  ): Promise<
    (LogList & {service_agreement_description: string; extend_columns: any[]})[]
  > {
    try {
      const offset = (page - 1) * pageSize;
      const query = `
      SELECT l.*, sa.description AS service_agreement_description
      FROM log_list l
      LEFT JOIN service_agreement sa ON l.service_agreement_id = sa.id
      ORDER BY l.log_header_id DESC
      LIMIT ? OFFSET ?
    `;

      const result = await executeQuery(query, [pageSize, offset]);

      let logs: (LogList & {
        service_agreement_description: string;
        extend_columns: any[];
      })[] = [];

      for (let i = 0; i < result.rows.length; i++) {
        const row = result.rows.item(i);
        const logHeader = new LogList(row);
        logHeader.service_agreement_description =
          row.service_agreement_description;
        logHeader.extend_columns = await this.getExtendColumns(
          logHeader.service_agreement_id,
        );
        logs.push(
          logHeader as LogList & {
            service_agreement_description: string;
            extend_columns: any[];
          },
        );
      }

      return logs;
    } catch (error) {
      console.error('Error fetching logs:', error);
      throw error;
    }
  }

  private static getComboListOptions(
    columnName: string,
  ): Array<{label: string; value: string}> {
    return COMBO_LIST_OPTIONS[columnName] || [];
  }

  static async getExtendColumns(
    serviceAgreementId: number,
  ): Promise<any[]> {
    const query = `
      SELECT DISTINCT c.data_type, c.required, c.combo_list, c.column_name
      FROM extension_group g
             JOIN extension_line l ON g.extension_group_id = l.extension_group_id
             JOIN extension_column c ON l.extension_column_id = c.extension_column_id
             JOIN service_agreement sa ON g.group_control_value = sa.id
      WHERE sa.id = ?
    `;

    const result = await executeQuery(query, [serviceAgreementId]);

    let extendColumns = [];
    for (let i = 0; i < result.rows.length; i++) {
      const row = result.rows.item(i);
      if (row.data_type === 'Combo') {
        row.combo_list = this.getComboListOptions(row.column_name);
      }
      extendColumns.push(row);
    }

    return extendColumns;
  }

  static async getById(logHeaderId: number): Promise<LogList | null> {
    try {
      const result = await executeQuery(
        'SELECT * FROM log_list WHERE log_header_id = ?',
        [logHeaderId],
      );
      if (result.rows.length > 0) {
        return new LogList(result.rows.item(0));
      }
      return null;
    } catch (error) {
      console.error('Error fetching log by ID:', error);
      return null;
    }
  }
  static async getByLocalLogId(localLogId: number): Promise<LogList | null> {
    try {
      const result = await executeQuery(
        'SELECT * FROM log_list WHERE local_log_id = ?',
        [localLogId],
      );
      if (result.rows.length > 0) {
        return new LogList(result.rows.item(0));
      }
      return null;
    } catch (error) {
      console.error('Error fetching log by ID:', error);
      return null;
    }
  }

  static async update(
    localLogId: number,
    log: Partial<LogList>,
  ): Promise<void> {
    const {
      log_header_id,
      status,
      schd_date,
      log_type,
      company_id,
      allocation_person_id,
      allocation_person_full_name,
      completed_time,
      completed_date,
      description,
      log_details,
      arrive_date,
      arrive_time,
      service_agreement_id,
      asset_id,
      completion_details,
      contact_details,
      asset_code,
      asset_description,
      log_address,
      stored_files,
      arrive_latitude,
      arrive_longitude,
      completion_latitude,
      completion_longitude,
      is_completed,
      is_draft,
      create_log_timestamp,
      event_time_stamp,
      service_type,
      priority,
      comments,
      order_no,
      task_code,
    } = log;
    try {
      await executeQuery(
        `
        UPDATE log_list
        SET
          log_header_id = COALESCE(?, log_header_id),
          status = COALESCE(?, status),
          schd_date = COALESCE(?, schd_date),
          log_type = COALESCE(?, log_type),
          company_id = COALESCE(?, company_id),
          allocation_person_id = COALESCE(?, allocation_person_id),
          allocation_person_full_name = COALESCE(?, allocation_person_full_name),
          completed_time = COALESCE(?, completed_time),
          completed_date = COALESCE(?, completed_date),
          description = COALESCE(?, description),
          log_details = COALESCE(?, log_details),
          arrive_date = COALESCE(?, arrive_date),
          arrive_time = COALESCE(?, arrive_time),
          service_agreement_id = COALESCE(?, service_agreement_id),
          asset_id = COALESCE(?, asset_id),
          completion_details = COALESCE(?, completion_details),
          contact_details = COALESCE(?, contact_details),
          asset_code = COALESCE(?, asset_code),
          asset_description = COALESCE(?, asset_description),
          log_address = COALESCE(?, log_address),
          stored_files = COALESCE(?, stored_files),
          arrive_latitude = COALESCE(?, arrive_latitude),
          arrive_longitude = COALESCE(?, arrive_longitude),
          completion_latitude = COALESCE(?, completion_latitude),
          completion_longitude = COALESCE(?, completion_longitude),
          is_completed = COALESCE(?, is_completed),
          is_draft = COALESCE(?, is_draft),
          create_log_timestamp = COALESCE(?, create_log_timestamp),
          event_time_stamp = COALESCE(?, event_time_stamp),
          updated_date = CURRENT_TIMESTAMP,
          service_type = COALESCE(?, service_type),
          priority = COALESCE(?, priority),
          comments = COALESCE(?, comments),
          order_no = COALESCE(?, order_no),
          task_code = COALESCE(?, task_code)
        WHERE local_log_id = ?
      `,
        [
          log_header_id,
          status,
          schd_date,
          log_type,
          company_id,
          allocation_person_id,
          allocation_person_full_name,
          completed_time,
          completed_date,
          description,
          log_details,
          arrive_date,
          arrive_time,
          service_agreement_id,
          asset_id,
          completion_details,
          contact_details,
          asset_code,
          asset_description,
          log_address,
          stored_files,
          arrive_latitude,
          arrive_longitude,
          completion_latitude,
          completion_longitude,
          is_completed,
          is_draft,
          create_log_timestamp,
          event_time_stamp,
          service_type,
          priority,
          comments,
          order_no,
          task_code,
          localLogId,
        ],
      );
    } catch (error) {
      console.error('Error updating log:', error);
    }
  }

  static async updateByLogHeaderId(
    logHeaderId: number,
    log: Partial<LogList>,
  ): Promise<void> {
    try {
      const updateResult = await executeQuery(
        `
      UPDATE log_list
      SET
        status = COALESCE(?, status),
        schd_date = COALESCE(?, schd_date),
        log_type = COALESCE(?, log_type),
        company_id = COALESCE(?, company_id),
        allocation_person_id = COALESCE(?, allocation_person_id),
        allocation_person_full_name = COALESCE(?, allocation_person_full_name),
        completed_time = COALESCE(?, completed_time),
        completed_date = COALESCE(?, completed_date),
        description = COALESCE(?, description),
        log_details = COALESCE(?, log_details),
        arrive_date = COALESCE(?, arrive_date),
        arrive_time = COALESCE(?, arrive_time),
        service_agreement_id = COALESCE(?, service_agreement_id),
        asset_id = COALESCE(?, asset_id),
        completion_details = COALESCE(?, completion_details),
        contact_details = COALESCE(?, contact_details),
        asset_code = COALESCE(?, asset_code),
        asset_description = COALESCE(?, asset_description),
        log_address = COALESCE(?, log_address),
        stored_files = COALESCE(?, stored_files),
        arrive_latitude = COALESCE(?, arrive_latitude),
        arrive_longitude = COALESCE(?, arrive_longitude),
        completion_latitude = COALESCE(?, completion_latitude),
        completion_longitude = COALESCE(?, completion_longitude),
        is_completed = COALESCE(?, is_completed),
        is_draft = COALESCE(?, is_draft),
        create_log_timestamp = COALESCE(?, create_log_timestamp),
        event_time_stamp = COALESCE(?, event_time_stamp),
        updated_date = CURRENT_TIMESTAMP,
        service_type = COALESCE(?, service_type),
        priority = COALESCE(?, priority),
        comments = COALESCE(?, comments),
        order_no = COALESCE(?, order_no),
        task_code = COALESCE(?, task_code)
      WHERE log_header_id = ?
    `,
        [
          log.status,
          log.schd_date,
          log.log_type,
          log.company_id,
          log.allocation_person_id,
          log.allocation_person_full_name,
          log.completed_time,
          log.completed_date,
          log.description,
          log.log_details,
          log.arrive_date,
          log.arrive_time,
          log.service_agreement_id,
          log.asset_id,
          log.completion_details,
          log.contact_details,
          log.asset_code,
          log.asset_description,
          log.log_address,
          log.stored_files,
          log.arrive_latitude,
          log.arrive_longitude,
          log.completion_latitude,
          log.completion_longitude,
          log.is_completed,
          log.is_draft,
          log.create_log_timestamp,
          log.event_time_stamp,
          log.service_type,
          log.priority,
          log.comments,
          log.order_no,
          log.task_code,
          logHeaderId,
        ]
      );

    } catch (error) {
      console.error('Error updating log:', error);
      throw error;
    }
  }

  static async delete(logHeaderId: number): Promise<void> {
    try {
      await executeQuery('DELETE FROM log_list WHERE log_header_id = ?', [
        logHeaderId,
      ]);
      console.log('LogListScreen deleted successfully');
    } catch (error) {
      console.error('Error deleting log:', error);
    }
  }

  static async getByIds(logHeaderIds: number[]): Promise<LogList[]> {
    if (logHeaderIds.length === 0) {
      return [];
    }

    const placeholders = logHeaderIds.map(() => '?').join(',');
    const query = `SELECT * FROM log_list WHERE log_header_id IN (${placeholders})`;

    try {
      const result = await executeQuery(query, logHeaderIds);
      let logs: LogList[] = [];
      for (let i = 0; i < result.rows.length; i++) {
        logs.push(new LogList(result.rows.item(i)));
      }
      return logs;
    } catch (error) {
      console.error('Error fetching logs by IDs:', error);
      return [];
    }
  }

  static async createNewLog(newLogData: NewLogModel): Promise<number> {
    try {
      const sql = `
        INSERT INTO log_list (
          service_agreement_id, status, schd_date, description, 
          allocation_person_id, asset_id, order_no
        ) VALUES (?, ?, ?, ?, ?, ?, ?)
      `;

      const values = [
        newLogData.serviceAgreementID,
        'Open', // Default state
        new Date().toISOString().split('T')[0], // Current date as scheduled date
        newLogData.description,
        newLogData.allocatedPersonId,
        newLogData.assetID,
        newLogData.orderNo,
      ];

      const result = await executeQuery(sql, values);
      console.log('New log created successfully');
      return result.insertId;
    } catch (error) {
      console.error('Error creating new log:', error);
      throw error;
    }
  }

  static async updateLog(
    localLogId: number,
    updateData: UpdateLogModel,
  ): Promise<void> {
    // Implement the logic for updating the log
    try {
      const updateFields = [];
      const values = [];

      for (const [key, value] of Object.entries(updateData)) {
        if (value !== undefined) {
          updateFields.push(`${key} = ?`);
          values.push(value);
        }
      }

      if (updateFields.length === 0) {
        return; // There are no fields that need to be updated
      }

      const sql = `
        UPDATE log_list
        SET ${updateFields.join(', ')}
        WHERE local_log_id = ?
      `;

      values.push(localLogId);

      await executeQuery(sql, values);
      console.log('Log updated successfully');
    } catch (error) {
      console.error('Error updating log:', error);
      throw error;
    }
  }

  static async markAsSynced(
    localLogId: number,
    logHeaderId: number,
  ): Promise<void> {
    try {
      await executeQuery(
        `
        UPDATE log_list
        SET log_header_id = ?, status = 'Synced'
        WHERE local_log_id = ?
        `,
        [logHeaderId, localLogId],
      );
      console.log('Log marked as synced successfully');
    } catch (error) {
      console.error('Error marking log as synced:', error);
      throw error;
    }
  }

  static async getPendingLogs(): Promise<LogList[]> {
    try {
      const result = await executeQuery(
        "SELECT * FROM log_list WHERE status = 'Pending' OR status = 'New'",
      );
      let pendingLogs: LogList[] = [];
      for (let i = 0; i < result.rows.length; i++) {
        pendingLogs.push(new LogList(result.rows.item(i)));
      }
      return pendingLogs;
    } catch (error) {
      console.error('Error fetching pending logs:', error);
      throw error;
    }
  }

  static async updateStatusById(
    localLogId: number,
    status: string,
  ): Promise<void> {
    try {
      await executeQuery(
        `
        UPDATE log_list
        SET status = ?
        WHERE local_log_id = ?
        `,
        [status, localLogId],
      );
      console.log('Log status updated successfully');
    } catch (error) {
      console.error('Error updating log status:', error);
      throw error;
    }
  }

  static async getByLogHeaderId(logHeaderId: number): Promise<LogList | null> {
    try {
      const result = await executeQuery(
        'SELECT l.*, sa.description AS service_agreement_description FROM log_list l LEFT JOIN service_agreement sa ON l.service_agreement_id = sa.id WHERE l.log_header_id = ?',
        [logHeaderId],
      );
      if (result.rows.length > 0) {
        const log = new LogList(result.rows.item(0));
        log.service_agreement_description =
          result.rows.item(0).service_agreement_description;
        log.extend_columns = await this.getExtendColumns(
          log.service_agreement_id,
        );
        return log;
      }
      return null;
    } catch (error) {
      console.error('Error fetching log by log_header_id:', error);
      return null;
    }
  }

  static async getRecentLogs(limit: number = 20): Promise<LogList[]> {
    try {
      const result = await executeQuery(
        `SELECT * FROM log_list 
       WHERE arrive_latitude IS NOT NULL AND arrive_longitude IS NOT NULL 
       ORDER BY create_log_timestamp DESC LIMIT ?`,
        [limit],
      );
      return result.rows.raw().map(row => new LogList(row));
    } catch (error) {
      console.error('Error fetching recent logs:', error);
      throw error;
    }
  }

  static async getFilteredLogs(
    page: number = 1,
    pageSize: number = 20,
    filters: LogFilter = {}
  ): Promise<LogList[]> {
    try {
      let query = `SELECT l.*, ac.latitude as asset_latitude, ac.longitude as asset_longitude
                   FROM log_list l
                   LEFT JOIN assets a ON l.asset_id = a.asset_id
                   LEFT JOIN asset_coordinates ac ON a.asset_code = ac.asset_id
                   WHERE 1=1`;
      const params: any[] = [];

      console.log('🔍 Starting filtered logs query with asset coordinates join');

      console.log('Filters:', filters);
      if (filters.userId) {
        query += ' AND allocation_person_id = ?';
        params.push(filters.userId);
      }

      if (filters.searchKeyword) {
        query += ` AND (
        log_header_id LIKE ? 
        OR description LIKE ?
      )`;
        const keyword = `%${filters.searchKeyword}%`;
        params.push(keyword, keyword);
      }

      if (!filters.includeCompleted) {
        query += " AND status <> 'Completed' AND status <> 'submitting'";
      }

      if (filters.logStatus) {
        query += ' AND status = ?';
        params.push(filters.logStatus);
      }

      if (filters.priority) {
        query += ' AND priority = ?';
        params.push(filters.priority);
      }

      if (filters.logType) {
        query += ' AND log_type = ?';
        params.push(filters.logType);
      }

      if (filters.taskType) {
        query += ' AND service_agreement_id in ( ? )';
        params.push(filters.taskType);
      }

      if (filters.streetAddress) {
        query += ' AND log_address LIKE ?';
        params.push(`%${filters.streetAddress}%`);
      }

      if (filters.dateFrom) {
        query += ' AND schd_date >= ?';
        // Convert to format that matches database storage (without timezone info, same as LogGroup)
        params.push(filters.dateFrom.toISOString().replace('Z', '').replace('.000', ''));
      }

      if (filters.dateTo) {
        query += ' AND schd_date <= ?';
        // Convert to format that matches database storage (without timezone info, same as LogGroup)
        params.push(filters.dateTo.toISOString().replace('Z', '').replace('.000', ''));
      }

      if (filters.job) {
        query += ' AND job_code LIKE ?';
        params.push(`%${filters.job}%`);
      }

      if (filters.logNo) {
        query += ' AND log_header_id LIKE ?';
        params.push(`%${filters.logNo}%`);
      }

      if (filters.description) {
        query += ' AND description LIKE ?';
        params.push(`%${filters.description}%`);
      }

      if (filters.site) {
        query += ' AND log_address LIKE ?';
        params.push(`%${filters.site}%`);
      }

      if (filters.assetCode) {
        query += ' AND asset_code LIKE ?';
        params.push(`%${filters.assetCode}%`);
      }

      const sortBy = filters.sortBy || 'log_header_id';
      const sortOrder = filters.sortOrder || 'DESC';
      query += ` ORDER BY ${sortBy} ${sortOrder}`;

      query += ' LIMIT ? OFFSET ?';
      console.log(query)
      params.push(pageSize, (page - 1) * pageSize);

      // Debug logging for LogList date filtering
      if (filters.dateFrom || filters.dateTo) {
        console.log('🔍 LogList getFilteredLogs date filtering:', {
          query: query.replace(/\s+/g, ' '),
          dateParams: {
            dateFrom: filters.dateFrom?.toISOString(),
            dateTo: filters.dateTo?.toISOString()
          }
        });
      }

      const result = await executeQuery(query, params);

      let logs: LogList[] = [];
      for (let i = 0; i < result.rows.length; i++) {
        const row = result.rows.item(i);
        logs.push(new LogList(row));
      }

      console.log(`📊 LogList getFilteredLogs found ${logs.length} logs`);

      return logs;
    } catch (error) {
      console.error('Error fetching filtered logs:', error);
      throw error;
    }
  }

  static async getScheduledByServiceAgreementIdWithPaging(
    serviceAgreementId: number,
    page: number = 1,
    pageSize: number = 10,
    startDate?: Date,
    endDate?: Date
  ): Promise<LogList[]> {
    try {
      const offset = (page - 1) * pageSize;

      let query = `SELECT * FROM log_list 
                   WHERE service_agreement_id = ? 
                   AND status NOT IN ('Finalised', 'Completed', 'submitting')`;
      const params: any[] = [serviceAgreementId];

      // Add user filtering (same as LogList getFilteredLogs)
      const currentUser = await AuthService.getCurrentUser();
      
      if (currentUser) {
        query += ' AND allocation_person_id = ?';
        params.push(currentUser.person_id);
        
      }

      // Add date filtering if provided
      // Note: Dates are already processed for NZ timezone in the frontend
      let actualStartDateStr = null;
      let actualEndDateStr = null;

      if (startDate) {
        query += ' AND schd_date >= ?';
        // Convert to format that matches database storage (without timezone info)
        actualStartDateStr = startDate.toISOString().replace('Z', '').replace('.000', '');
        params.push(actualStartDateStr);
      }

      if (endDate) {
        query += ' AND schd_date <= ?';
        // Convert to format that matches database storage (without timezone info)
        actualEndDateStr = endDate.toISOString().replace('Z', '').replace('.000', '');
        params.push(actualEndDateStr);
      }

      // Add ordering and pagination
      query += ' ORDER BY log_header_id DESC LIMIT ? OFFSET ?';
      params.push(pageSize, offset);


      const result = await executeQuery(query, params);

      let logs: LogList[] = [];
      for (let i = 0; i < result.rows.length; i++) {
        logs.push(new LogList(result.rows.item(i)));
      }

      console.log(`🔍 LogGroup found ${logs.length} logs for user ${currentUser?.person_id} on ${startDate?.toDateString()}`);
      
      // Debug: Check status distribution for this date range
      if (startDate && endDate && currentUser) {
        const statusCheckQuery = `SELECT status, COUNT(*) as count 
                                 FROM log_list 
                                 WHERE service_agreement_id = ? 
                                 AND allocation_person_id = ?
                                 AND schd_date >= ? AND schd_date <= ?
                                 GROUP BY status`;
        const statusParams = [serviceAgreementId, currentUser.person_id, 
                             startDate.toISOString().replace('Z', '').replace('.000', ''),
                             endDate.toISOString().replace('Z', '').replace('.000', '')];
        const statusResult = await executeQuery(statusCheckQuery, statusParams);
        console.log('🔍 Status distribution:');
        for (let i = 0; i < statusResult.rows.length; i++) {
          const row = statusResult.rows.item(i);
          console.log(`  ${row.status}: ${row.count}`);
        }
      }

      return logs;
    } catch (error) {
      console.error('Error fetching scheduled logs by ServiceAgreementId with paging:', error);
      throw error;
    }
  }

}


