import {executeQuery} from './DatabaseInit';

export class StoredFiles {
  id: number;
  file_name: string;
  file_extension: string;
  content_type: string;
  related_record_id: string;
  related_record_type: string;
  stored_file_id: number;
  local_log_id: number;
  log_header_id: number;
  file_path: string;
  secondary_path: string;
  url: string;

  constructor(data: {
    id?: number;
    file_name: string;
    file_extension: string;
    content_type: string;
    related_record_id: string;
    related_record_type: string;
    stored_file_id?: number;
    local_log_id: number;
    log_header_id: number;
    file_path: string;
    secondary_path?: string;
    url?: string;
  }) {
    this.id = data.id;
    this.file_name = data.file_name;
    this.file_extension = data.file_extension;
    this.content_type = data.content_type;
    this.related_record_id = data.related_record_id;
    this.related_record_type = data.related_record_type;
    this.stored_file_id = data.stored_file_id;
    this.local_log_id = data.local_log_id;
    this.log_header_id = data.log_header_id;
    this.file_path = data.file_path;
    this.secondary_path = data.secondary_path;
    this.url = data.url;
  }

  static async upsert(file: StoredFiles): Promise<void> {
    try {
      const existingFile = await this.getById(file.id);
      if (existingFile) {
        await this.update(file.id, file);
      } else {
        await this.insert(file);
      }
      console.log('StoredFile upserted successfully');
    } catch (error) {
      console.error('Error upserting StoredFile:', error);
    }
  }

  static async insert(file: StoredFiles): Promise<number> {
    try {
      const result = await executeQuery(
        `
        INSERT INTO stored_files (
          file_name, file_extension, content_type, related_record_id, 
          related_record_type, stored_file_id, local_log_id, file_path, 
          secondary_path, url
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `,
        [
          file.file_name,
          file.file_extension,
          file.content_type,
          file.related_record_id,
          file.related_record_type,
          file.stored_file_id,
          file.local_log_id,
          file.file_path,
          file.secondary_path,
          file.url,
        ],
      );
      console.log('StoredFile inserted successfully');
      return result.insertId;
    } catch (error) {
      console.error('Error inserting StoredFile:', error);
      throw error;
    }
  }

  static async getAll(limit: number = 10): Promise<StoredFiles[]> {
    try {
      const result = await executeQuery(
        'SELECT * FROM stored_files ORDER BY id DESC LIMIT ?',
        [limit],
      );
      let files: StoredFiles[] = [];
      for (let i = 0; i < result.rows.length; i++) {
        files.push(new StoredFiles(result.rows.item(i)));
      }
      return files;
    } catch (error) {
      console.error('Error fetching StoredFiles:', error);
      return [];
    }
  }

  static async getById(id: number): Promise<StoredFiles | null> {
    try {
      const result = await executeQuery(
        'SELECT * FROM stored_files WHERE id = ?',
        [id],
      );
      if (result.rows.length > 0) {
        return new StoredFiles(result.rows.item(0));
      }
      return null;
    } catch (error) {
      console.error('Error fetching StoredFile by ID:', error);
      return null;
    }
  }

  static async update(id: number, file: Partial<StoredFiles>): Promise<void> {
    const {
      file_name,
      file_extension,
      content_type,
      related_record_id,
      related_record_type,
      stored_file_id,
      log_header_id,
      file_path,
      secondary_path,
      url,
    } = file;
    try {
      await executeQuery(
        `
        UPDATE stored_files
        SET
          file_name = COALESCE(?, file_name),
          file_extension = COALESCE(?, file_extension),
          content_type = COALESCE(?, content_type),
          related_record_id = COALESCE(?, related_record_id),
          related_record_type = COALESCE(?, related_record_type),
          stored_file_id = COALESCE(?, stored_file_id),
          log_header_id = COALESCE(?, log_header_id),
          file_path = COALESCE(?, file_path),
          secondary_path = COALESCE(?, secondary_path),
          url = COALESCE(?, url)
        WHERE id = ?
      `,
        [
          file_name,
          file_extension,
          content_type,
          related_record_id,
          related_record_type,
          stored_file_id,
          log_header_id,
          file_path,
          secondary_path,
          url,
          id,
        ],
      );
      console.log('StoredFile updated successfully');
    } catch (error) {
      console.error('Error updating StoredFile:', error);
    }
  }

  static async delete(id: number): Promise<void> {
    try {
      await executeQuery('DELETE FROM stored_files WHERE id = ?', [id]);
      console.log('StoredFile deleted successfully');
    } catch (error) {
      console.error('Error deleting StoredFile:', error);
    }
  }

  static async getByLogHeaderId(logHeaderId: number): Promise<StoredFiles[]> {
    try {
      const result = await executeQuery(
        'SELECT * FROM stored_files WHERE log_header_id = ?',
        [logHeaderId],
      );
      let files: StoredFiles[] = [];
      for (let i = 0; i < result.rows.length; i++) {
        files.push(new StoredFiles(result.rows.item(i)));
      }
      return files;
    } catch (error) {
      console.error('Error fetching StoredFiles by log_header_id:', error);
      return [];
    }
  }

  static async getByLocalLogId(localLogId: number): Promise<StoredFiles[]> {
    try {
      const result = await executeQuery(
        'SELECT * FROM stored_files WHERE local_log_id = ?',
        [localLogId],
      );
      let files: StoredFiles[] = [];
      for (let i = 0; i < result.rows.length; i++) {
        files.push(new StoredFiles(result.rows.item(i)));
      }
      return files;
    } catch (error) {
      console.error('Error fetching StoredFiles by local_log_id:', error);
      return [];
    }
  }

  static async updateStoredFileId(
    id: number,
    storedFileId: number,
  ): Promise<void> {
    try {
      await executeQuery(
        'UPDATE stored_files SET stored_file_id = ? WHERE id = ?',
        [storedFileId, id],
      );
      console.log('StoredFile stored_file_id updated successfully');
    } catch (error) {
      console.error('Error updating StoredFile stored_file_id:', error);
    }
  }

  static async updateStoredFileIdAndLogHeadId(
    id: number,
    storedFileId: number,
    logHeaderId: number,
  ) {
    try {
      await executeQuery(
        'UPDATE stored_files SET stored_file_id = ? and log_header_id =? WHERE id = ?',
        [storedFileId, logHeaderId, id],
      );
      console.log(
        'StoredFile stored_file_id and log_header_id updated successfully',
      );
    } catch (error) {
      console.error('Error updating StoredFile stored_file_id:', error);
    }
  }

  static async deleteByLocalLogId(localLogId: number) {
    try {
      await executeQuery('DELETE FROM stored_files WHERE local_log_id = ?', [
        localLogId,
      ]);
      console.log('StoredFile deleted successfully');
    } catch (error) {
      console.error('Error deleting StoredFile:', error);
    }
  }
}
