import SQLite from 'react-native-sqlite-storage';
import RNFS from 'react-native-fs';
import SyncService from '../services/SyncService.ts';

// Constant definition of database file name and location
const DB_NAME = 'corde_mobile.db';
const DB_LOCATION = 'default';

let db: SQLite.SQLiteDatabase;

// Asynchronous function to open the database
const openDatabase = (): Promise<SQLite.SQLiteDatabase> => {
  return new Promise((resolve, reject) => {
    SQLite.openDatabase(
      {
        name: DB_NAME,
        location: DB_LOCATION,
      },
      dbObject => {
        resolve(dbObject);
      },
      error => {
        console.log('Error opening database: ', error);
        reject(error);
      },
    );
  });
};

// Execute non-transactional queries (e.g. set PRAGMA command)
const executeNonTransactionalQuery = (
  sql: string,
  params: any[] = [],
): Promise<any> =>
  new Promise((resolve, reject) => {
    db.executeSql(
      sql,
      params,
      (tx, results) => {
        //console.log(`Executed: ${sql}`);
        resolve(results);
      },
      error => {
        console.log(`Error executing: ${sql}, Error: ${error.message}`);
        reject(error);
      },
    );
  });

const executeQuery = (sql: string, params: any[] = []): Promise<any> =>
  new Promise((resolve, reject) => {
    db.transaction(tx => {
      tx.executeSql(
        sql,
        params,
        (tx, results) => {
          //console.log(`Executed: ${sql}`);
          resolve(results);
        },
        error => {
          console.log(`Error executing: ${sql}, Error: ${error.message}`);
          reject(error);
        },
      );
    });
  });

// Asynchronous function that gets the current database version from the db_version table.
const getCurrentDBVersion = async (): Promise<number> => {
  try {
    const result = await executeQuery('SELECT version FROM db_version LIMIT 1');
    if (result.rows.length > 0) {
      return result.rows.item(0).version;
    }
  } catch (error) {
    console.error('Error getting current database version:', error);
  }
  return 0;
};

// Asynchronous function to update the database version number
const updateDBVersion = async (version: number): Promise<void> => {
  try {
    await executeQuery('UPDATE db_version SET version = ?', [version]);
  } catch (error) {
    console.error('Error updating database version:', error);
  }
};

// Asynchronous function that performs a series of PRAGMA statements to optimise database operations
// Set foreign key support, logging mode, timeout, synchronous mode, cache size and temporary storage
const initPRAGMAStatements = async (): Promise<void> => {
  try {
    await executeNonTransactionalQuery('PRAGMA foreign_keys = ON');
    await executeNonTransactionalQuery('PRAGMA journal_mode = WAL');
    await executeNonTransactionalQuery('PRAGMA busy_timeout = 5000');
    await executeNonTransactionalQuery('PRAGMA synchronous = NORMAL');
    await executeNonTransactionalQuery('PRAGMA cache_size = 100000');
    await executeNonTransactionalQuery('PRAGMA temp_store = MEMORY');
  } catch (error) {
    console.error('Error executing PRAGMA statements: ', error);
  }
};

// Create all tables
const createAllTables = async (currentVersion: number): Promise<void> => {
  const createTablesSQL = [
    `CREATE TABLE IF NOT EXISTS users (
      user_id INTEGER PRIMARY KEY AUTOINCREMENT,
      username VARCHAR(255) UNIQUE NOT NULL,
      full_name VARCHAR(255),
      email VARCHAR(255),
      phone_number VARCHAR(20),
      last_login TIMESTAMP,
      last_error_message TEXT,
      show_tutorial BOOLEAN DEFAULT TRUE,
      person_id INTEGER,
      fin_co_code VARCHAR(10),
      is_authenticated BOOLEAN DEFAULT FALSE
      )`,
    `CREATE TABLE IF NOT EXISTS staff (
      person_id INTEGER PRIMARY KEY,
      full_name VARCHAR(50) NOT NULL,
      email_address VARCHAR(50),
      mobile_phone VARCHAR(20),
      profit_centre_code VARCHAR(20),
      employee_no VARCHAR(20),
      password_hash VARCHAR(200),
      last_login TIMESTAMP,
      last_error_message VARCHAR(200),
      show_tutorial INTEGER DEFAULT 1
    )`,
    `CREATE TABLE IF NOT EXISTS assets (
      asset_id INTEGER PRIMARY KEY UNIQUE NOT NULL,--Col01
      asset_contract_id INT,--Key.KeyValue is AssetContractID
      contract_code VARCHAR(50),--Col02
      suburb VARCHAR(50),--Suburb
      asset_location VARCHAR(100),--AssetLocation
      asset_code VARCHAR(50),--AssetCode
      description VARCHAR(100),--Description
      company_id INT,--CompanyID
      company_name VARCHAR(100),--CompanyName
      in_active INT,--Inactive 0,1
      parent_asset_id INT,--ParentAssetID
      street_no VARCHAR(50),--StreetNo
      street VARCHAR(255),--Street
      city VARCHAR(50),--City
      country VARCHAR(50)--Country
    )`,
    `CREATE TABLE IF NOT EXISTS asset_attribute_values (
      attribute_id INTEGER PRIMARY KEY AUTOINCREMENT,
      asset_id INT,--The interface for assets returns the AssetID.
      data_label VARCHAR(50),--DataLabel (Class1, Class2, Class3)
      data_type INT,--DataType
      text_data TEXT,--TextData
      delete_flag BOOLEAN--Delete
    )`,
    `CREATE TABLE IF NOT EXISTS asset_class_activity (
      line_id INTEGER PRIMARY KEY UNIQUE NOT NULL,--Key.KeyValue is LineID
      class1 VARCHAR(50),--Col01
      class2 VARCHAR(50),--Col02
      class3 VARCHAR(50),--Col03
      activity VARCHAR(50),--Col04
      contract_code VARCHAR(50)--Col05
    )`,
    `CREATE TABLE IF NOT EXISTS extension_group (
      extension_group_id INTEGER PRIMARY KEY UNIQUE NOT NULL,--Key.KeyValue is ExtensionGroupID
      extension_table_id INT NOT NULL,--constant value 24
      group_control_value INT, -- equate to 'service_agreement_id',Col02
      description VARCHAR(100) -- Col03
    )`,
    `CREATE TABLE IF NOT EXISTS extension_line (
       extension_line_id INTEGER PRIMARY KEY UNIQUE NOT NULL,--Key.KeyValue is ExtensionLineID
       extension_group_id INT NOT NULL,--Col01
       extension_column_id INT NOT NULL,--Col02
       display_label VARCHAR(100) --Col03
     )`,
    `CREATE TABLE IF NOT EXISTS extension_column (
      extension_column_id INTEGER PRIMARY KEY UNIQUE NOT NULL,--Key.KeyValue is ExtensionLineID
      extension_table_id INT,--canstant value 24
      column_name VARCHAR(50),--Col02
      data_type VARCHAR(50),--Col03 (Text, Yes/No, Date, Combo, Quantity)
      combo_list TEXT,--Col04,"0 - Cannot Assess, 1 - Excellent, 2 - Good, 3 - Average, 4 - Poor, 5 - Very Poor"
        -- "NP Closed by CORDE, NP Closed by CORDE H&S, NP Outside Contract, NP Weather, NP Within Specification"
        --"GDC Corrugations, GDP Potholes, USR Poor Cross Sect, SR Scouring>70, SR2 Scouring >400, R3 Rutting"
        --"Under Contract, Dayworks, Approval Req"
        -- "exec wbGetAssetClassActivity @LogHeaderID = FormControl(LogHeaderID)"
      required BOOLEAN -- Col05 0,-1
    )`,
    `CREATE TABLE IF NOT EXISTS log_list (
      local_log_id INTEGER PRIMARY KEY AUTOINCREMENT,
      log_header_id INTEGER UNIQUE,
      status VARCHAR(50),
      schd_date DATE,
      log_type VARCHAR(50),
      company_id VARCHAR(50),
      allocation_person_id INT,
      allocation_person_full_name VARCHAR(255),
      completed_time TIMESTAMP,
      completed_date DATE,
      description VARCHAR(100),
      log_details TEXT,
      arrive_date DATE,
      arrive_time TIMESTAMP,
      service_agreement_id INT,
      asset_id INT,
      completion_details TEXT,
      contact_details TEXT,
      asset_code VARCHAR(50),
      asset_description VARCHAR(100),
      log_address VARCHAR(255),
      stored_files TEXT,
      arrive_latitude REAL,
      arrive_longitude REAL,
      completion_latitude REAL,
      completion_longitude REAL,
      is_completed BOOLEAN DEFAULT FALSE,
      is_draft BOOLEAN DEFAULT FALSE,
      create_log_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      event_time_stamp TIMESTAMP,
      updated_date TIMESTAMP,
      service_type VARCHAR(50),
      priority  VARCHAR(30),
      comments varchar(200),
      order_no VARCHAR(20),
      task_code VARCHAR(50)
      )`,
    `CREATE TABLE IF NOT EXISTS service_agreement (
      id INTEGER PRIMARY KEY UNIQUE NOT NULL,--Key.KeyValue
      code VARCHAR(50),--Col02
      description VARCHAR(100),--Col03
      details VARCHAR(100),--Col04
      client_source VARCHAR(50),--Col05
      company_id VARCHAR(50),--Col06
      default_job_code VARCHAR(50),--Col07 or Col08
      default_parent_agreement VARCHAR(50)--Col09
    )`,
    `CREATE TABLE IF NOT EXISTS stored_files (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      file_name VARCHAR(100) NOT NULL,
      file_extension VARCHAR(255),
      content_type VARCHAR(20),
      related_record_id VARCHAR(20),
      related_record_type VARCHAR(20),
      stored_file_id INTEGER,
      local_log_id INTEGER NOT NULL,
      log_header_id INTEGER,
      file_path VARCHAR(255),
      secondary_path VARCHAR(200),
      upload_date TIMESTAMP,
      url VARCHAR(200)
      )`,
    `CREATE TABLE IF NOT EXISTS mobile_sync_logs (
      sync_log_id INTEGER PRIMARY KEY AUTOINCREMENT,
      user_id INT NOT NULL,
      local_log_id INTEGER NOT NULL,
      log_header_id INTEGER,
      last_sync_time TIMESTAMP,
      last_update_time TIMESTAMP,
      sync_status VARCHAR(255) DEFAULT 'Open',
      sync_errors TEXT,
      offline_indicator BOOLEAN DEFAULT FALSE,
      sync_task_count INT DEFAULT 0,
      conflict_status VARCHAR(255) DEFAULT 'none',
      retry_count INT DEFAULT 0
      )`,
    `CREATE TABLE IF NOT EXISTS synchronization_errors (
      error_id INTEGER PRIMARY KEY AUTOINCREMENT,
      error_code VARCHAR(255),
      error_description TEXT,
      resolution_tip TEXT
    )`,
    `CREATE TABLE IF NOT EXISTS filters (
      filter_id INTEGER PRIMARY KEY AUTOINCREMENT,
      user_id INT,
      filter_name VARCHAR(255),
      filter_criteria TEXT,
      creation_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      last_used TIMESTAMP,
      is_private BOOLEAN DEFAULT TRUE
    )`,
    `CREATE TABLE IF NOT EXISTS log_extensions (
      log_extension_id INTEGER PRIMARY KEY AUTOINCREMENT,
      local_log_id INTEGER,
      name VARCHAR(50),
      value_str VARCHAR(50)
    )`,
    
  ];

  if (currentVersion === 1) {
    try {
      for (const sql of createTablesSQL) {
        console.log(`Creating table with SQL: ${sql}`);
        await executeQuery(sql);
        console.log(`Table created successfully with SQL: ${sql}`);
      }
      console.log('createTablesSQL initialized successfully');
    } catch (error) {
      console.error('Error creating tables: ', error);
    }
  }
};

// Database migration
const migrateDB = async (currentVersion: number): Promise<void> => {
  const migrations = [
    {
      version: 2,
      script: `
        select count(1) from staff;
      `,
    },
    {
      version: 5,
      script: `
        delete from log_list where log_header_id is null;
        delete from users;
      `,
    },
    // another migration
    {
      version: 7,
      script: `CREATE TABLE IF NOT EXISTS asset_coordinates (
        asset_id INTEGER PRIMARY KEY UNIQUE NOT NULL,
        latitude REAL NOT NULL,
        longitude REAL NOT NULL
      )`,
    },
  ];

  for (let migration of migrations) {

    if (currentVersion < migration.version) {
      console.log(`Migrating to version ${migration.version}`);
      await executeQuery(migration.script);
      await updateDBVersion(migration.version);
      currentVersion = migration.version;
    }
  }
};

const listAllTables = async (): Promise<string[]> => {
  const result = await executeQuery(
    "SELECT name FROM sqlite_master WHERE type='table'",
  );
  const tables = [];
  for (let i = 0; i < result.rows.length; i++) {
    tables.push(result.rows.item(i).name);
  }
  // console.log('All tables:', tables);
  return tables;
};

const tablesToDrop = [
  'db_version',
  'users',
  'assets_details',
  'asset_contract',
  'asset_attribute_values',
  'asset_class_activity',
  'extension_group',
  'extension_column',
  'extension_line',
  'log_list',
  'stored_files',
  'mobile_sync_logs',
  'synchronization_errors',
  'filters',
];

const generateDropTableStatements = (): string[] => {
  return tablesToDrop.map(table => `DROP TABLE IF EXISTS ${table}`);
};

const dropSpecifiedTables = async (): Promise<void> => {
  await SyncService.clearSyncFlags();
  const dropStatements = generateDropTableStatements();
  for (const sql of dropStatements) {
    await executeQuery(sql);
  }
  console.log('Specified tables dropped successfully');
};

// Initialise the database
const initDB = async (): Promise<void> => {
  try {
    const dbPath = `/data/data/com.awesomeproject/databases/${DB_NAME}`;
    const dbShmPath = `${dbPath}-shm`;
    const dbWalPath = `${dbPath}-wal`;
    const assetDbPath = `${DB_NAME}`;

    const exists = await RNFS.exists(dbPath);
    if (!exists) {
      console.log(
        `Database does not exist. Attempting to copy from assets: ${assetDbPath}`,
      );
      try {
        await RNFS.copyFileAssets(assetDbPath, dbPath);
        if (await RNFS.exists(dbShmPath)) {
          await RNFS.unlink(dbShmPath);
        }
        if (await RNFS.exists(dbWalPath)) {
          await RNFS.unlink(dbWalPath);
        }
        console.log('Prefilled database copied successfully');
      } catch (copyError) {
        console.log('Error copying database from assets:', copyError);
      }
    } else {
      // 如果数据库已存在，检查WAL和SHM文件是否同步
      const dbExists = await RNFS.exists(dbPath);
      const shmExists = await RNFS.exists(dbShmPath);
      const walExists = await RNFS.exists(dbWalPath);

      // 记录文件状态，帮助调试
      console.log(`DB files status: main=${dbExists}, shm=${shmExists}, wal=${walExists}`);

      // 如果WAL或SHM只有一个存在，可能表明不同步
      if ((shmExists && !walExists) || (!shmExists && walExists)) {
        console.warn('Inconsistent DB files state detected, cleaning up');
        // 删除可能不同步的文件
        if (shmExists) await RNFS.unlink(dbShmPath);
        if (walExists) await RNFS.unlink(dbWalPath);
      }
    }

    db = await openDatabase();

    // init PRAGMA statements
    await initPRAGMAStatements();

    //await dropSpecifiedTables();
    //console.log('Database drop success');

    // create db_version table
    await executeQuery(`
      CREATE TABLE IF NOT EXISTS db_version (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        version INTEGER
      )
    `);

    // get current database version
    let currentVersion = await getCurrentDBVersion();

    // if current version is 0, insert version 1
    if (currentVersion === 0) {
      await executeQuery('INSERT INTO db_version (version) VALUES (1)');
      currentVersion = 1;
    }

    await createAllTables(currentVersion);
    await migrateDB(currentVersion);
    await listAllTables();

  } catch (error) {
    console.log('Error initializing database: ', error);
  }
};

export const closeDatabase = async (): Promise<void> => {
  if (db) {
    try {
      // 执行检查点操作，确保所有更改都同步到主数据库
      await executeNonTransactionalQuery('PRAGMA wal_checkpoint(FULL)');
      // 关闭数据库连接
      await db.close();
      console.log('Database connection closed properly');
    } catch (error) {
      console.error('Error closing database:', error);
    }
  }
};

export {initDB, executeQuery};
