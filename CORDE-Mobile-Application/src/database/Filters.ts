import { executeQuery } from './DatabaseInit';
import { AuthService } from '../services/AuthService';

export interface SavedFilter {
  id: number;
  filter_name: string;
  filter_criteria: any;
}

export const saveFilter = async (filterName: string, filterCriteria: any): Promise<void> => {
  const currentUser = await AuthService.getCurrentUser();
  if (!currentUser) {
    throw new Error('No authenticated user found');
  }

  const query = `
    INSERT INTO filters (user_id, filter_name, filter_criteria, creation_date, last_used)
    VALUES (?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
  `;
  await executeQuery(query, [currentUser.user_id, filterName, JSON.stringify(filterCriteria)]);
};

export const getSavedFilters = async (): Promise<SavedFilter[]> => {
  const currentUser = await AuthService.getCurrentUser();
  if (!currentUser) {
    throw new Error('No authenticated user found');
  }

  const query = `
    SELECT filter_id as id, filter_name, filter_criteria
    FROM filters
    WHERE user_id = ?
    ORDER BY last_used DESC
  `;
  const result = await executeQuery(query, [currentUser.user_id]);
  return result.rows.raw().map(row => ({
    ...row,
    filter_criteria: JSON.parse(row.filter_criteria)
  }));
};

export const updateFilterLastUsed = async (filterId: number): Promise<void> => {
  const currentUser = await AuthService.getCurrentUser();
  if (!currentUser) {
    throw new Error('No authenticated user found');
  }

  const query = `
    UPDATE filters
    SET last_used = CURRENT_TIMESTAMP
    WHERE filter_id = ? AND user_id = ?
  `;
  await executeQuery(query, [filterId, currentUser.user_id]);
};

export const deleteFilter = async (filterId: number): Promise<void> => {
  const currentUser = await AuthService.getCurrentUser();
  if (!currentUser) {
    throw new Error('No authenticated user found');
  }

  const query = `
    DELETE FROM filters
    WHERE filter_id = ? AND user_id = ?
  `;
  await executeQuery(query, [filterId, currentUser.user_id]);
};