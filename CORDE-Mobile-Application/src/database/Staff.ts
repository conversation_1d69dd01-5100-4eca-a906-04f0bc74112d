import {executeQuery} from './DatabaseInit';

export class Staff {
  person_id: number;
  full_name: string;
  email_address: string;
  mobile_phone: string;
  profit_centre_code: string;
  employee_no: string;
  password_hash: string;
  last_login: string;
  last_error_message: string;
  show_tutorial: boolean;

  constructor(data: {
    person_id: number;
    full_name: string;
    email_address: string;
    mobile_phone: string;
    profit_centre_code: string;
    employee_no: string;
    password_hash?: string;
    last_login?: string;
    last_error_message?: string;
    show_tutorial?: boolean;
  }) {
    this.person_id = data.person_id;
    this.full_name = data.full_name;
    this.email_address = data.email_address;
    this.mobile_phone = data.mobile_phone;
    this.profit_centre_code = data.profit_centre_code || '';
    this.employee_no = data.employee_no;
    this.password_hash = data.password_hash || '';
    this.last_login = data.last_login || '';
    this.last_error_message = data.last_error_message || '';
    this.show_tutorial =
      data.show_tutorial !== undefined ? data.show_tutorial : true;
  }

  static async upsert(staff: Staff): Promise<void> {
    try {
      const existingStaff = await this.getById(staff.person_id);
      if (existingStaff) {
        await this.update(staff.person_id, staff);
      } else {
        await this.insert(staff);
      }
    } catch (error) {
      console.error(
        `Error upserting Staff ${staff.full_name} (ID: ${staff.person_id}):`,
        error,
      );
    }
  }

  static async insert(staff: Staff): Promise<void> {
    try {
      const result = await executeQuery(
        `
        INSERT INTO staff (
          person_id, full_name, email_address, mobile_phone, profit_centre_code, employee_no, 
          password_hash, last_login, last_error_message, show_tutorial
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `,
        [
          staff.person_id,
          staff.full_name,
          staff.email_address,
          staff.mobile_phone,
          staff.profit_centre_code,
          staff.employee_no,
          staff.password_hash,
          staff.last_login,
          staff.last_error_message,
          staff.show_tutorial ? 1 : 0,
        ],
      );
    } catch (error) {
      console.error(
        `Error inserting Staff ${staff.full_name} (ID: ${staff.person_id}):`,
        error,
      );
      throw error;
    }
  }

  static async getAll(limit: number = 10): Promise<Staff[]> {
    try {
      const result = await executeQuery('SELECT * FROM staff LIMIT ?', [limit]);
      let staffList: Staff[] = [];
      for (let i = 0; i < result.rows.length; i++) {
        staffList.push(new Staff(result.rows.item(i)));
      }
      return staffList;
    } catch (error) {
      console.error('Error fetching Staff:', error);
      return [];
    }
  }

  static async getById(personId: number): Promise<Staff | null> {
    try {
      const result = await executeQuery(
        'SELECT * FROM staff WHERE person_id = ?',
        [personId],
      );
      console.log(`getById query result for ID ${personId}:`, result);
      if (result.rows.length > 0) {
        return new Staff(result.rows.item(0));
      }
      return null;
    } catch (error) {
      console.error(`Error fetching Staff by ID ${personId}:`, error);
      return null;
    }
  }

  static async update(personId: number, staff: Partial<Staff>): Promise<void> {
    const {
      full_name,
      email_address,
      mobile_phone,
      profit_centre_code,
      employee_no,
      password_hash,
      last_login,
      last_error_message,
      show_tutorial,
    } = staff;
    try {
      await executeQuery(
        `
        UPDATE staff
        SET
          full_name = COALESCE(?, full_name),
          email_address = COALESCE(?, email_address),
          mobile_phone = COALESCE(?, mobile_phone),
          profit_centre_code = COALESCE(?, profit_centre_code),
          employee_no = COALESCE(?, employee_no),
          password_hash = COALESCE(?, password_hash),
          last_login = COALESCE(?, last_login),
          last_error_message = COALESCE(?, last_error_message),
          show_tutorial = COALESCE(?, show_tutorial)
        WHERE person_id = ?
      `,
        [
          full_name,
          email_address,
          mobile_phone,
          profit_centre_code,
          employee_no,
          password_hash,
          last_login,
          last_error_message,
          show_tutorial !== undefined ? (show_tutorial ? 1 : 0) : undefined,
          personId,
        ],
      );
    } catch (error) {
      console.error(
        `Error updating Staff ${full_name} (ID: ${personId}):`,
        error,
      );
    }
  }

  static async delete(personId: number): Promise<void> {
    try {
      await executeQuery('DELETE FROM staff WHERE person_id = ?', [personId]);
    } catch (error) {
      console.error(`Error deleting Staff with ID ${personId}:`, error);
    }
  }

  static async search(query: string, limit: number = 10): Promise<Staff[]> {
    try {
      const result = await executeQuery(
        `SELECT * FROM staff
         WHERE full_name LIKE ? OR email_address LIKE ?
           LIMIT ?`,
        [`%${query}%`, `%${query}%`, limit],
      );
      let staffList: Staff[] = [];
      for (let i = 0; i < result.rows.length; i++) {
        staffList.push(new Staff(result.rows.item(i)));
      }
      return staffList;
    } catch (error) {
      console.error('Error searching Staff:', error);
      return [];
    }
  }

  static async getCount(): Promise<number> {
    try {
      const result = await executeQuery('SELECT COUNT(*) as count FROM staff');
      return result.rows.item(0).count;
    } catch (error) {
      console.error('Error getting staff count:', error);
      return 0;
    }
  }
}
