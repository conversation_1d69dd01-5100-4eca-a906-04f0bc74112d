import {executeQuery} from './DatabaseInit';

export class MobileSyncLogs {
  sync_log_id?: number;
  user_id: number;
  local_log_id: number;
  log_header_id?: number;
  last_sync_time?: string;
  last_update_time?: string;
  sync_status: string;
  sync_errors?: string;
  offline_indicator: boolean;
  sync_task_count: number;
  conflict_status: string;
  retry_count: number;

  constructor(data: {
    sync_log_id?: number;
    user_id: number;
    local_log_id: number;
    log_header_id?: number;
    last_sync_time?: string;
    last_update_time?: string;
    sync_status?: string;
    sync_errors?: string;
    offline_indicator?: boolean;
    sync_task_count?: number;
    conflict_status?: string;
    retry_count?: number;
  }) {
    this.sync_log_id = data.sync_log_id;
    this.user_id = data.user_id;
    this.local_log_id = data.local_log_id;
    this.log_header_id = data.log_header_id;
    this.last_sync_time = data.last_sync_time;
    this.last_update_time = data.last_update_time;
    this.sync_status = data.sync_status || 'pending';
    this.sync_errors = data.sync_errors;
    this.offline_indicator =
      data.offline_indicator !== undefined ? data.offline_indicator : false;
    this.sync_task_count = data.sync_task_count || 0;
    this.conflict_status = data.conflict_status || 'none';
    this.retry_count = data.retry_count || 0;
  }

  static async insert(syncLog: MobileSyncLogs): Promise<number> {
    try {
      const result = await executeQuery(
        `
        INSERT INTO mobile_sync_logs (
          user_id, local_log_id, log_header_id, last_sync_time, last_update_time,
          sync_status, sync_errors, offline_indicator, sync_task_count,
          conflict_status, retry_count
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `,
        [
          syncLog.user_id,
          syncLog.local_log_id,
          syncLog.log_header_id,
          syncLog.last_sync_time,
          syncLog.last_update_time,
          syncLog.sync_status,
          syncLog.sync_errors,
          syncLog.offline_indicator,
          syncLog.sync_task_count,
          syncLog.conflict_status,
          syncLog.retry_count,
        ],
      );
      console.log('MobileSyncLog inserted successfully');
      return result.insertId;
    } catch (error) {
      console.error('Error inserting MobileSyncLog:', error);
      throw error;
    }
  }

  static async getById(syncLogId: number): Promise<MobileSyncLogs | null> {
    try {
      const result = await executeQuery(
        'SELECT * FROM mobile_sync_logs WHERE sync_log_id = ?',
        [syncLogId],
      );
      if (result.rows.length > 0) {
        return new MobileSyncLogs(result.rows.item(0));
      }
      return null;
    } catch (error) {
      console.error('Error fetching MobileSyncLog by ID:', error);
      return null;
    }
  }

  static async update(
    syncLogId: number,
    syncLog: Partial<MobileSyncLogs>,
  ): Promise<void> {
    const updateFields = [];
    const values = [];

    for (const [key, value] of Object.entries(syncLog)) {
      if (value !== undefined) {
        updateFields.push(`${key} = ?`);
        values.push(value);
      }
    }

    if (updateFields.length === 0) {
      return; // No fields to update
    }

    try {
      await executeQuery(
        `
        UPDATE mobile_sync_logs
        SET ${updateFields.join(', ')}
        WHERE sync_log_id = ?
      `,
        [...values, syncLogId],
      );
      console.log('MobileSyncLog updated successfully');
    } catch (error) {
      console.error('Error updating MobileSyncLog:', error);
      throw error;
    }
  }

  static async delete(syncLogId: number): Promise<void> {
    try {
      await executeQuery('DELETE FROM mobile_sync_logs WHERE sync_log_id = ?', [
        syncLogId,
      ]);
      console.log('MobileSyncLog deleted successfully');
    } catch (error) {
      console.error('Error deleting MobileSyncLog:', error);
      throw error;
    }
  }

  static async getByLocalLogId(
    localLogId: number,
  ): Promise<MobileSyncLogs | null> {
    try {
      const result = await executeQuery(
        'SELECT * FROM mobile_sync_logs WHERE local_log_id = ?',
        [localLogId],
      );
      if (result.rows.length > 0) {
        return new MobileSyncLogs(result.rows.item(0));
      }
      return null;
    } catch (error) {
      console.error('Error fetching MobileSyncLog by local_log_id:', error);
      return null;
    }
  }

  static async getOpenSyncLogs(): Promise<MobileSyncLogs[]> {
    try {
      // Include 'error' status logs that haven't reached max retry count (3)
      const result = await executeQuery(
        "SELECT * FROM mobile_sync_logs WHERE sync_status = 'Open' OR (sync_status = 'error' AND retry_count < 3)",
      );
      let pendingLogs: MobileSyncLogs[] = [];
      for (let i = 0; i < result.rows.length; i++) {
        pendingLogs.push(new MobileSyncLogs(result.rows.item(i)));
      }
      console.log(`Found ${pendingLogs.length} open/retryable sync logs`);
      return pendingLogs;
    } catch (error) {
      console.error('Error fetching Open MobileSyncLogs:', error);
      return [];
    }
  }
  static async getSubmittingSyncLogs(): Promise<MobileSyncLogs[]> {
    try {
      // Include 'error' status logs that haven't reached max retry count (3)
      const result = await executeQuery(
        "SELECT * FROM mobile_sync_logs WHERE sync_status = 'submitting' OR (sync_status = 'error' AND retry_count < 3)",
      );
      let submittingLogs: MobileSyncLogs[] = [];
      for (let i = 0; i < result.rows.length; i++) {
        submittingLogs.push(new MobileSyncLogs(result.rows.item(i)));
      }
      console.log(`Found ${submittingLogs.length} submitting/retryable sync logs`);
      return submittingLogs;
    } catch (error) {
      console.error('Error fetching submitting MobileSyncLogs:', error);
      return [];
    }
  }

  static async incrementRetryCount(syncLogId: number): Promise<void> {
    try {
      await executeQuery(
        'UPDATE mobile_sync_logs SET retry_count = retry_count + 1 WHERE sync_log_id = ?',
        [syncLogId],
      );
      console.log('MobileSyncLog retry count incremented successfully');
    } catch (error) {
      console.error('Error incrementing MobileSyncLog retry count:', error);
      throw error;
    }
  }

  static async getAll(limit: number = 10): Promise<MobileSyncLogs[]> {
    try {
      const result = await executeQuery(
        'SELECT * FROM mobile_sync_logs ORDER BY sync_log_id DESC LIMIT ?',
        [limit],
      );
      let mobileSyncLogs: MobileSyncLogs[] = [];
      for (let i = 0; i < result.rows.length; i++) {
        mobileSyncLogs.push(new MobileSyncLogs(result.rows.item(i)));
      }
      return mobileSyncLogs;
    } catch (error) {
      console.error('Error fetching MobileSyncLogs:', error);
      return [];
    }
  }

  static async getAllPaginated(page: number = 1, limit: number = 10): Promise<{data: MobileSyncLogs[], total: number, hasMore: boolean}> {
    try {
      // Get total count
      const countResult = await executeQuery('SELECT COUNT(*) as total FROM mobile_sync_logs');
      const total = countResult.rows.item(0).total;
      
      // Get paginated data ordered by last_sync_time DESC for newest first
      const offset = (page - 1) * limit;
      const result = await executeQuery(
        'SELECT * FROM mobile_sync_logs ORDER BY COALESCE(last_sync_time, last_update_time, datetime("now")) DESC LIMIT ? OFFSET ?',
        [limit, offset],
      );
      
      let mobileSyncLogs: MobileSyncLogs[] = [];
      for (let i = 0; i < result.rows.length; i++) {
        mobileSyncLogs.push(new MobileSyncLogs(result.rows.item(i)));
      }
      
      const hasMore = offset + mobileSyncLogs.length < total;
      
      return {
        data: mobileSyncLogs,
        total,
        hasMore
      };
    } catch (error) {
      console.error('Error fetching paginated MobileSyncLogs:', error);
      return { data: [], total: 0, hasMore: false };
    }
  }
}
