import {executeQuery} from './DatabaseInit';

export class Assets {
  asset_id: number;
  asset_contract_id: number;
  contract_code: string;
  suburb: string;
  asset_location: string;
  asset_code: string;
  description: string;
  company_id: number;
  company_name: string;
  in_active: number;
  parent_asset_id: number;
  street_no: string;
  street: string;
  city: string;
  country: string;
  // The constructor takes an object, data, and assigns its property values to the corresponding properties of the class instance.
  constructor(data: {
    asset_id: number;
    asset_contract_id: number;
    contract_code: string;
    suburb: string;
    asset_location: string;
    asset_code: string;
    description: string;
    company_id: number;
    company_name: string;
    in_active: number;
    parent_asset_id: number;
    street_no: string;
    street: string;
    city: string;
    country: string;
  }) {
    this.asset_id = data.asset_id;
    this.asset_contract_id = data.asset_contract_id;
    this.contract_code = data.contract_code;
    this.suburb = data.suburb;
    this.asset_location = data.asset_location;
    this.asset_code = data.asset_code;
    this.description = data.description;
    this.company_id = data.company_id;
    this.company_name = data.company_name;
    this.in_active = data.in_active;
    this.parent_asset_id = data.parent_asset_id;
    this.street_no = data.street_no;
    this.street = data.street;
    this.city = data.city;
    this.country = data.country;
  }

  static async upsert(asset: Assets): Promise<void> {
    try {
      const existingAsset = await this.getById(asset.asset_id);
      if (existingAsset) {
        await this.update(asset.asset_id, asset);
      } else {
        await this.insert(asset);
      }
      console.log('Assets upserted successfully');
    } catch (error) {
      console.error('Error upserting Assets:', error);
    }
  }

  // Use parameterised queries to prevent SQL injection.
  // executeQuery function takes a SQL query string and an array of parameters.
  static async insert(asset: Assets): Promise<void> {
    try {
      await executeQuery(
        `
        INSERT INTO assets (
          asset_id, asset_contract_id, contract_code, suburb, asset_location, asset_code, description, company_id, company_name, in_active, parent_asset_id, street_no, street, city, country
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `,
        [
          asset.asset_id,
          asset.asset_contract_id,
          asset.contract_code,
          asset.suburb,
          asset.asset_location,
          asset.asset_code,
          asset.description,
          asset.company_id,
          asset.company_name,
          asset.in_active,
          asset.parent_asset_id,
          asset.street_no,
          asset.street,
          asset.city,
          asset.country,
        ],
      );
      console.log('Assets inserted successfully');
    } catch (error) {
      console.error('Error inserting Assets:', error);
    }
  }

  static async getAll(limit: number = 10): Promise<Assets[]> {
    try {
      const result = await executeQuery('SELECT * FROM assets LIMIT ?', [
        limit,
      ]);
      let assets: Assets[] = [];
      for (let i = 0; i < result.rows.length; i++) {
        assets.push(new Assets(result.rows.item(i)));
      }
      return assets;
    } catch (error) {
      console.error('Error fetching Assets:', error);
      return [];
    }
  }

  static async getById(assetId: number): Promise<Assets | null> {
    try {
      const result = await executeQuery(
        'SELECT * FROM assets WHERE asset_id = ?',
        [assetId],
      );
      if (result.rows.length > 0) {
        return new Assets(result.rows.item(0));
      }
      return null;
    } catch (error) {
      console.error('Error fetching Assets by ID:', error);
      return null;
    }
  }

  // Allow partial updates, i.e. only update the supplied fields.
  // Use the COALESCE function to ensure that existing values are not overwritten (if the new value provided is null).
  static async update(assetId: number, asset: Partial<Assets>): Promise<void> {
    const {
      asset_contract_id,
      contract_code,
      suburb,
      asset_location,
      asset_code,
      description,
      company_id,
      company_name,
      in_active,
      parent_asset_id,
      street_no,
      street,
      city,
      country,
    } = asset;
    try {
      await executeQuery(
        `
        UPDATE assets
        SET
          asset_contract_id = COALESCE(?, asset_contract_id),
          contract_code = COALESCE(?, contract_code),
          suburb = COALESCE(?, suburb),
          asset_location = COALESCE(?, asset_location),
          asset_code = COALESCE(?, asset_code),
          description = COALESCE(?, description),
          company_id = COALESCE(?, company_id),
          company_name = COALESCE(?, company_name),
          in_active = COALESCE(?, in_active),
          parent_asset_id = COALESCE(?, parent_asset_id),
          street_no = COALESCE(?, street_no),
          street = COALESCE(?, street),
          city = COALESCE(?, city),
          country = COALESCE(?, country)
        WHERE asset_id = ?
      `,
        [
          asset_contract_id,
          contract_code,
          suburb,
          asset_location,
          asset_code,
          description,
          company_id,
          company_name,
          in_active,
          parent_asset_id,
          street_no,
          street,
          city,
          country,
          assetId,
        ],
      );
      console.log('Assets updated successfully');
    } catch (error) {
      console.error('Error updating Assets:', error);
    }
  }

  static async delete(assetId: number): Promise<void> {
    try {
      await executeQuery('DELETE FROM assets WHERE asset_id = ?', [assetId]);
      console.log('Assets deleted successfully');
    } catch (error) {
      console.error('Error deleting Assets:', error);
    }
  }

  static async search(query: string, limit: number = 10): Promise<Assets[]> {
    try {
      const result = await executeQuery(
        `SELECT * FROM assets 
       WHERE asset_code LIKE ? OR description LIKE ? 
       LIMIT ?`,
        [`%${query}%`, `%${query}%`, limit],
      );
      let assets: Assets[] = [];
      for (let i = 0; i < result.rows.length; i++) {
        assets.push(new Assets(result.rows.item(i)));
      }
      return assets;
    } catch (error) {
      console.error('Error searching Assets:', error);
      return [];
    }
  }
}
