import { executeQuery } from './DatabaseInit';

export class ExtensionLine {
  extension_line_id: number;
  extension_group_id: number;
  extension_column_id: number;
  display_label: string;

  constructor(data: {
    extension_line_id: number;
    extension_group_id: number;
    extension_column_id: number;
    display_label: string;
  }) {
    this.extension_line_id = data.extension_line_id;
    this.extension_group_id = data.extension_group_id;
    this.extension_column_id = data.extension_column_id;
    this.display_label = data.display_label;
  }

  static async upsert(line: ExtensionLine): Promise<void> {
    try {
      const existingLine = await this.getById(line.extension_line_id);
      if (existingLine) {
        await this.update(line.extension_line_id, line);
      } else {
        await this.insert(line);
      }
      console.log('ExtensionLine upserted successfully');
    } catch (error) {
      console.error('Error upserting ExtensionLine:', error);
    }
  }

  static async insert(line: ExtensionLine): Promise<void> {
    try {
      await executeQuery(
        `
        INSERT INTO extension_line (
          extension_line_id, extension_group_id, extension_column_id, display_label
        ) VALUES (?, ?, ?, ?)
      `,
        [
          line.extension_line_id,
          line.extension_group_id,
          line.extension_column_id,
          line.display_label,
        ]
      );
      console.log('ExtensionLine inserted successfully');
    } catch (error) {
      console.error('Error inserting ExtensionLine:', error);
    }
  }

  static async getAll(): Promise<ExtensionLine[]> {
    try {
      const result = await executeQuery('SELECT * FROM extension_line');
      let lines: ExtensionLine[] = [];
      for (let i = 0; i < result.rows.length; i++) {
        lines.push(new ExtensionLine(result.rows.item(i)));
      }
      return lines;
    } catch (error) {
      console.error('Error fetching ExtensionLines:', error);
      return [];
    }
  }

  static async getById(extensionLineId: number): Promise<ExtensionLine | null> {
    try {
      const result = await executeQuery(
        'SELECT * FROM extension_line WHERE extension_line_id = ?',
        [extensionLineId]
      );
      if (result.rows.length > 0) {
        return new ExtensionLine(result.rows.item(0));
      }
      return null;
    } catch (error) {
      console.error('Error fetching ExtensionLine by ID:', error);
      return null;
    }
  }

  static async update(
    extensionLineId: number,
    line: Partial<ExtensionLine>
  ): Promise<void> {
    const { extension_group_id, extension_column_id, display_label } = line;
    try {
      await executeQuery(
        `
        UPDATE extension_line
        SET
          extension_group_id = COALESCE(?, extension_group_id),
          extension_column_id = COALESCE(?, extension_column_id),
          display_label = COALESCE(?, display_label)
        WHERE extension_line_id = ?
      `,
        [extension_group_id, extension_column_id, display_label, extensionLineId]
      );
      console.log('ExtensionLine updated successfully');
    } catch (error) {
      console.error('Error updating ExtensionLine:', error);
    }
  }

  static async delete(extensionLineId: number): Promise<void> {
    try {
      await executeQuery('DELETE FROM extension_line WHERE extension_line_id = ?', [
        extensionLineId,
      ]);
      console.log('ExtensionLine deleted successfully');
    } catch (error) {
      console.error('Error deleting ExtensionLine:', error);
    }
  }
}
