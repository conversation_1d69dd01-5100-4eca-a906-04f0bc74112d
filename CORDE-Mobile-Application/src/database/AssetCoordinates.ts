import {executeQuery} from './DatabaseInit';

export class AssetCoordinates {
  asset_id: number;
  latitude: number;
  longitude: number;

  constructor(data: {
    asset_id: number;
    latitude: number;
    longitude: number;
  }) {
    this.asset_id = data.asset_id;
    this.latitude = data.latitude;
    this.longitude = data.longitude;
  }

  static async upsert(coordinate: AssetCoordinates): Promise<void> {
    try {
      await executeQuery(
        `INSERT OR REPLACE INTO asset_coordinates (asset_id, latitude, longitude) VALUES (?, ?, ?)`,
        [coordinate.asset_id, coordinate.latitude, coordinate.longitude],
      );
    } catch (error) {
      console.error('Error upserting asset coordinate:', error);
      throw error;
    }
  }

  static async upsertBatch(coordinates: AssetCoordinates[]): Promise<void> {
    try {
      const sql = `INSERT OR REPLACE INTO asset_coordinates (asset_id, latitude, longitude) VALUES (?, ?, ?)`;

      for (const coordinate of coordinates) {
        await executeQuery(sql, [
          coordinate.asset_id,
          coordinate.latitude,
          coordinate.longitude,
        ]);
      }
    } catch (error) {
      console.error('Error batch upserting asset coordinates:', error);
      throw error;
    }
  }

  static async getByAssetId(assetId: number): Promise<AssetCoordinates | null> {
    try {
      const result = await executeQuery(
        `SELECT * FROM asset_coordinates WHERE asset_id = ? LIMIT 1`,
        [assetId],
      );

      if (result.rows.length > 0) {
        const row = result.rows.item(0);
        return new AssetCoordinates({
          asset_id: row.asset_id,
          latitude: row.latitude,
          longitude: row.longitude,
        });
      }
      return null;
    } catch (error) {
      console.error('Error getting asset coordinate by asset_id:', error);
      throw error;
    }
  }

  static async getByAssetIds(assetIds: number[]): Promise<Map<number, AssetCoordinates>> {
    try {
      if (assetIds.length === 0) {
        return new Map();
      }

      const placeholders = assetIds.map(() => '?').join(',');
      const result = await executeQuery(
        `SELECT * FROM asset_coordinates WHERE asset_id IN (${placeholders})`,
        assetIds,
      );

      const coordinatesMap = new Map<number, AssetCoordinates>();
      for (let i = 0; i < result.rows.length; i++) {
        const row = result.rows.item(i);
        const coordinate = new AssetCoordinates({
          asset_id: row.asset_id,
          latitude: row.latitude,
          longitude: row.longitude,
        });
        coordinatesMap.set(row.asset_id, coordinate);
      }

      return coordinatesMap;
    } catch (error) {
      console.error('Error getting asset coordinates by asset_ids:', error);
      throw error;
    }
  }



  static async delete(assetId: number): Promise<void> {
    try {
      await executeQuery(
        `DELETE FROM asset_coordinates WHERE asset_id = ?`,
        [assetId],
      );
    } catch (error) {
      console.error('Error deleting asset coordinate:', error);
      throw error;
    }
  }

  static async getAll(limit: number = 10): Promise<AssetCoordinates[]> {
    try {
      const result = await executeQuery(
        `SELECT * FROM asset_coordinates ORDER BY asset_id LIMIT ?`,
        [limit],
      );

      const coordinates: AssetCoordinates[] = [];
      for (let i = 0; i < result.rows.length; i++) {
        const row = result.rows.item(i);
        coordinates.push(new AssetCoordinates({
          asset_id: row.asset_id,
          latitude: row.latitude,
          longitude: row.longitude,
        }));
      }

      return coordinates;
    } catch (error) {
      console.error('Error getting all asset coordinates:', error);
      throw error;
    }
  }

  static async getCount(): Promise<number> {
    try {
      const result = await executeQuery(
        `SELECT COUNT(*) as count FROM asset_coordinates`,
        [],
      );

      if (result.rows.length > 0) {
        return result.rows.item(0).count;
      }
      return 0;
    } catch (error) {
      console.error('Error getting asset coordinates count:', error);
      throw error;
    }
  }

  static async exists(assetId: number): Promise<boolean> {
    try {
      const result = await executeQuery(
        `SELECT 1 FROM asset_coordinates WHERE asset_id = ? LIMIT 1`,
        [assetId],
      );
      return result.rows.length > 0;
    } catch (error) {
      console.error('Error checking if asset coordinate exists:', error);
      throw error;
    }
  }

  static async clear(): Promise<void> {
    try {
      await executeQuery(`DELETE FROM asset_coordinates`, []);
    } catch (error) {
      console.error('Error clearing asset coordinates:', error);
      throw error;
    }
  }
}
