import { executeQuery } from './DatabaseInit';

export class ExtensionGroup {
  extension_group_id: number;
  extension_table_id: number;
  group_control_value: number;
  description: string;

  constructor(data: {
    extension_group_id: number;
    extension_table_id: number;
    group_control_value: number;
    description: string;
  }) {
    this.extension_group_id = data.extension_group_id;
    this.extension_table_id = data.extension_table_id;
    this.group_control_value = data.group_control_value;
    this.description = data.description;
  }

  static async upsert(group: ExtensionGroup): Promise<void> {
    try {
      const existingGroup = await this.getById(group.extension_group_id);
      if (existingGroup) {
        await this.update(group.extension_group_id, group);
      } else {
        await this.insert(group);
      }
      console.log('ExtensionGroup upserted successfully');
    } catch (error) {
      console.error('Error upserting ExtensionGroup:', error);
    }
  }

  static async insert(group: ExtensionGroup): Promise<void> {
    try {
      await executeQuery(
        `
        INSERT INTO extension_group (
          extension_group_id, extension_table_id, group_control_value, description
        ) VALUES (?, ?, ?, ?)
      `,
        [
          group.extension_group_id,
          group.extension_table_id,
          group.group_control_value,
          group.description,
        ]
      );
      console.log('ExtensionGroup inserted successfully');
    } catch (error) {
      console.error('Error inserting ExtensionGroup:', error);
    }
  }

  static async getAll(): Promise<ExtensionGroup[]> {
    try {
      const result = await executeQuery('SELECT * FROM extension_group');
      let groups: ExtensionGroup[] = [];
      for (let i = 0; i < result.rows.length; i++) {
        groups.push(new ExtensionGroup(result.rows.item(i)));
      }
      return groups;
    } catch (error) {
      console.error('Error fetching ExtensionGroups:', error);
      return [];
    }
  }

  static async getById(extensionGroupId: number): Promise<ExtensionGroup | null> {
    try {
      const result = await executeQuery(
        'SELECT * FROM extension_group WHERE extension_group_id = ?',
        [extensionGroupId]
      );
      if (result.rows.length > 0) {
        return new ExtensionGroup(result.rows.item(0));
      }
      return null;
    } catch (error) {
      console.error('Error fetching ExtensionGroup by ID:', error);
      return null;
    }
  }

  static async update(
    extensionGroupId: number,
    group: Partial<ExtensionGroup>
  ): Promise<void> {
    const { extension_table_id, group_control_value, description } = group;
    try {
      await executeQuery(
        `
        UPDATE extension_group
        SET
          extension_table_id = COALESCE(?, extension_table_id),
          group_control_value = COALESCE(?, group_control_value),
          description = COALESCE(?, description)
        WHERE extension_group_id = ?
      `,
        [extension_table_id, group_control_value, description, extensionGroupId]
      );
      console.log('ExtensionGroup updated successfully');
    } catch (error) {
      console.error('Error updating ExtensionGroup:', error);
    }
  }

  static async delete(extensionGroupId: number): Promise<void> {
    try {
      await executeQuery('DELETE FROM extension_group WHERE extension_group_id = ?', [
        extensionGroupId,
      ]);
      console.log('ExtensionGroup deleted successfully');
    } catch (error) {
      console.error('Error deleting ExtensionGroup:', error);
    }
  }
}
