import {executeQuery} from './DatabaseInit';

export class LogExtensions {
  log_extension_id?: number;
  local_log_id: number;
  name: string;
  value_str: string;

  constructor(data: {
    log_extension_id?: number;
    local_log_id: number;
    name: string;
    value_str: string;
  }) {
    this.log_extension_id = data.log_extension_id;
    this.local_log_id = data.local_log_id;
    this.name = data.name;
    this.value_str = data.value_str;
  }

  static async insert(logExtension: LogExtensions): Promise<number> {
    try {
      console.log('Inserting log extension:', JSON.stringify(logExtension));
      const result = await executeQuery(
        `
              INSERT INTO log_extensions (
                local_log_id, name, value_str
              ) VALUES (?, ?, ?)
            `,
        [logExtension.local_log_id, logExtension.name, logExtension.value_str],
      );
      console.log('LogExtension inserted successfully');
      return result.insertId;
    } catch (error) {
      console.error('Error inserting LogExtension:', error);
      throw error;
    }
  }

  static async update(
    logExtensionId: number,
    logExtension: Partial<LogExtensions>,
  ): Promise<void> {
    const updateFields = [];
    const values = [];

    for (const [key, value] of Object.entries(logExtension)) {
      if (value !== undefined) {
        updateFields.push(`${key} = ?`);
        values.push(value);
      }
    }

    if (updateFields.length === 0) {
      return; // No fields to update
    }

    try {
      await executeQuery(
        `
        UPDATE log_extensions
        SET ${updateFields.join(', ')}
        WHERE log_extension_id = ?
      `,
        [...values, logExtensionId],
      );
      console.log('LogExtension updated successfully');
    } catch (error) {
      console.error('Error updating LogExtension:', error);
      throw error;
    }
  }

  static async delete(logExtensionId: number): Promise<void> {
    try {
      await executeQuery(
        'DELETE FROM log_extensions WHERE log_extension_id = ?',
        [logExtensionId],
      );
      console.log('LogExtension deleted successfully');
    } catch (error) {
      console.error('Error deleting LogExtension:', error);
      throw error;
    }
  }

  static async deleteByLocalLogId(localLogId: number): Promise<void> {
    try {
      await executeQuery('DELETE FROM log_extensions WHERE local_log_id = ?', [
        localLogId,
      ]);
      console.log(
        'LogExtensions deleted successfully for local_log_id:',
        localLogId,
      );
    } catch (error) {
      console.error('Error deleting LogExtensions by local_log_id:', error);
      throw error;
    }
  }

  static async getAll(limit: number = 10): Promise<LogExtensions[]> {
    try {
      const result = await executeQuery(
        'SELECT * FROM log_extensions ORDER BY log_extension_id DESC LIMIT ?',
        [limit],
      );
      let extensions: LogExtensions[] = [];
      for (let i = 0; i < result.rows.length; i++) {
        extensions.push(new LogExtensions(result.rows.item(i)));
      }
      return extensions;
    } catch (error) {
      console.error('Error fetching LogExtensions:', error);
      return [];
    }
  }

  static async getByLocalLogId(localLogId: number): Promise<LogExtensions[]> {
    try {
      // console.log(`Fetching log extensions for local_log_id: ${localLogId}`);
      const result = await executeQuery(
        'SELECT * FROM log_extensions WHERE local_log_id = ?',
        [localLogId],
      );
      let extensions: LogExtensions[] = [];
      for (let i = 0; i < result.rows.length; i++) {
        extensions.push(new LogExtensions(result.rows.item(i)));
      }
      // console.log('Found extensions:', extensions);
      return extensions;
    } catch (error) {
      console.error('Error fetching log extensions:', error);
      return [];
    }
  }
}
