import {executeQuery} from './DatabaseInit';

export class ExtensionColumn {
  extension_column_id: number;
  extension_table_id: number;
  column_name: string;
  data_type: string;
  combo_list: string;
  required: boolean;

  constructor(data: {
    extension_column_id: number;
    extension_table_id: number;
    column_name: string;
    data_type: string;
    combo_list: string;
    required: boolean;
  }) {
    this.extension_column_id = data.extension_column_id;
    this.extension_table_id = data.extension_table_id;
    this.column_name = data.column_name;
    this.data_type = data.data_type;
    this.combo_list = data.combo_list;
    this.required = data.required;
  }

  static async upsert(column: ExtensionColumn): Promise<void> {
    try {
      const existingColumn = await this.getById(column.extension_column_id);
      if (existingColumn) {
        await this.update(column.extension_column_id, column);
      } else {
        await this.insert(column);
      }
      console.log('ExtensionColumn upserted successfully');
    } catch (error) {
      console.error('Error upserting ExtensionColumn:', error);
    }
  }

  static async insert(column: ExtensionColumn): Promise<void> {
    try {
      await executeQuery(
        `
        INSERT INTO extension_column (
          extension_column_id, extension_table_id, column_name, data_type, combo_list, required
        ) VALUES (?, ?, ?, ?, ?, ?)
      `,
        [
          column.extension_column_id,
          column.extension_table_id,
          column.column_name,
          column.data_type,
          column.combo_list,
          column.required,
        ],
      );
      console.log('ExtensionColumn inserted successfully');
    } catch (error) {
      console.error('Error inserting ExtensionColumn:', error);
    }
  }

  static async getAll(): Promise<ExtensionColumn[]> {
    try {
      const result = await executeQuery('SELECT * FROM extension_column');
      let columns: ExtensionColumn[] = [];
      for (let i = 0; i < result.rows.length; i++) {
        columns.push(new ExtensionColumn(result.rows.item(i)));
      }
      return columns;
    } catch (error) {
      console.error('Error fetching ExtensionColumns:', error);
      return [];
    }
  }

  static async getById(
    extensionColumnId: number,
  ): Promise<ExtensionColumn | null> {
    try {
      const result = await executeQuery(
        'SELECT * FROM extension_column WHERE extension_column_id = ?',
        [extensionColumnId],
      );
      if (result.rows.length > 0) {
        return new ExtensionColumn(result.rows.item(0));
      }
      return null;
    } catch (error) {
      console.error('Error fetching ExtensionColumn by ID:', error);
      return null;
    }
  }

  static async update(
    extensionColumnId: number,
    column: Partial<ExtensionColumn>,
  ): Promise<void> {
    const {extension_table_id, column_name, data_type, combo_list, required} =
      column;
    try {
      await executeQuery(
        `
        UPDATE extension_column
        SET
          extension_table_id = COALESCE(?, extension_table_id),
          column_name = COALESCE(?, column_name),
          data_type = COALESCE(?, data_type),
          combo_list = COALESCE(?, combo_list),
          required = COALESCE(?, required)
        WHERE extension_column_id = ?
      `,
        [
          extension_table_id,
          column_name,
          data_type,
          combo_list,
          required,
          extensionColumnId,
        ],
      );
      console.log('ExtensionColumn updated successfully');
    } catch (error) {
      console.error('Error updating ExtensionColumn:', error);
    }
  }

  static async delete(extensionColumnId: number): Promise<void> {
    try {
      await executeQuery(
        'DELETE FROM extension_column WHERE extension_column_id = ?',
        [extensionColumnId],
      );
      console.log('ExtensionColumn deleted successfully');
    } catch (error) {
      console.error('Error deleting ExtensionColumn:', error);
    }
  }
}
