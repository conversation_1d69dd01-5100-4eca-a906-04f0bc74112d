import { executeQuery } from './DatabaseInit';

export class AssetAttributeValue {
  attribute_id: number;
  asset_id: number;
  data_label: string;
  data_type: number;
  text_data: string;
  delete_flag: boolean;

  constructor(data: {
    attribute_id: number;
    asset_id: number;
    data_label: string;
    data_type: number;
    text_data: string;
    delete_flag: boolean;
  }) {
    this.attribute_id = data.attribute_id;
    this.asset_id = data.asset_id;
    this.data_label = data.data_label;
    this.data_type = data.data_type;
    this.text_data = data.text_data || ''
    this.delete_flag = data.delete_flag;
  }

  static async upsert(attribute: AssetAttributeValue): Promise<void> {
    try {
      const existingAttribute = await this.getById(attribute.attribute_id);
      if (existingAttribute) {
        await this.update(attribute.attribute_id, attribute);
      } else {
        await this.insert(attribute);
      }
      console.log('AssetAttributeValue upserted successfully');
    } catch (error) {
      console.error('Error upserting AssetAttributeValue:', error);
    }
  }

  static async insert(attribute: AssetAttributeValue): Promise<void> {
    try {
      await executeQuery(
        `
          INSERT INTO asset_attribute_values (
            asset_id, data_label, data_type, text_data, delete_flag
          ) VALUES (?, ?, ?, ?, ?)
        `,
        [
          attribute.asset_id,
          attribute.data_label,
          attribute.data_type,
          attribute.text_data,
          attribute.delete_flag,
        ]
      );
      console.log('AssetAttributeValue inserted successfully');
    } catch (error) {
      console.error('Error inserting AssetAttributeValue:', error);
    }
  }

  static async getAll(): Promise<AssetAttributeValue[]> {
    try {
      const result = await executeQuery('SELECT * FROM asset_attribute_values');
      let attributes: AssetAttributeValue[] = [];
      for (let i = 0; i < result.rows.length; i++) {
        attributes.push(new AssetAttributeValue(result.rows.item(i)));
      }
      return attributes;
    } catch (error) {
      console.error('Error fetching AssetAttributeValues:', error);
      return [];
    }
  }

  static async getById(attributeId: number): Promise<AssetAttributeValue | null> {
    try {
      const result = await executeQuery(
        'SELECT * FROM asset_attribute_values WHERE attribute_id = ?',
        [attributeId]
      );
      if (result.rows.length > 0) {
        return new AssetAttributeValue(result.rows.item(0));
      }
      return null;
    } catch (error) {
      console.error('Error fetching AssetAttributeValue by ID:', error);
      return null;
    }
  }

  static async update(
    attributeId: number,
    attribute: Partial<AssetAttributeValue>
  ): Promise<void> {
    const { asset_id, data_label, data_type, text_data, delete_flag } = attribute;
    try {
      await executeQuery(
        `
          UPDATE asset_attribute_values
          SET
            asset_id = COALESCE(?, asset_id),
            data_label = COALESCE(?, data_label),
            data_type = COALESCE(?, data_type),
            text_data = COALESCE(?, text_data),
            delete_flag = COALESCE(?, delete_flag)
          WHERE attribute_id = ?
        `,
        [asset_id, data_label, data_type, text_data, delete_flag, attributeId]
      );
      console.log('AssetAttributeValue updated successfully');
    } catch (error) {
      console.error('Error updating AssetAttributeValue:', error);
    }
  }

  static async delete(attributeId: number): Promise<void> {
    try {
      await executeQuery('DELETE FROM asset_attribute_values WHERE attribute_id = ?', [
        attributeId,
      ]);
      console.log('AssetAttributeValue deleted successfully');
    } catch (error) {
      console.error('Error deleting AssetAttributeValue:', error);
    }
  }

  static async deleteByAssetId(assetId: number): Promise<void> {
    try {
      await executeQuery('DELETE FROM asset_attribute_values WHERE asset_id = ?', [
        assetId,
      ]);
      console.log('AssetAttributeValue deleteByAssetId successfully');
    } catch (error) {
      console.error('Error deleteByAssetId AssetAttributeValue:', error);
    }
  }
  static async getByAssetId(assetId: number): Promise<AssetAttributeValue[]> {
    try {
      const result = await executeQuery(
        'SELECT * FROM asset_attribute_values WHERE asset_id = ?',
        [assetId]
      );
      let attributes: AssetAttributeValue[] = [];
      for (let i = 0; i < result.rows.length; i++) {
        attributes.push(new AssetAttributeValue(result.rows.item(i)));
      }
      return attributes;
    } catch (error) {
      console.error('Error fetching AssetAttributeValues by asset_id:', error);
      return [];
    }
  }

  static async bulkInsert(attributes: AssetAttributeValue[]): Promise<void> {
    if (attributes.length === 0) {
      return; // 如果没有属性需要插入，直接返回
    }

    const placeholders = attributes.map(() => '(?, ?, ?, ?, ?)').join(', ');
    const values = attributes.flatMap(attr => [
      attr.asset_id,
      attr.data_label,
      attr.data_type,
      attr.text_data,
      attr.delete_flag ? 1 : 0
    ]);

    const sql = `
      INSERT INTO asset_attribute_values (
        asset_id, data_label, data_type, text_data, delete_flag
      ) VALUES ${placeholders}
    `;

    try {
      await executeQuery('BEGIN TRANSACTION');

      await executeQuery(sql, values);

      await executeQuery('COMMIT');

      console.log(`Bulk inserted ${attributes.length} AssetAttributeValues successfully`);
    } catch (error) {
      await executeQuery('ROLLBACK');
      console.error('Error bulk inserting AssetAttributeValues:', error);
      throw error;
    }
  }

}
