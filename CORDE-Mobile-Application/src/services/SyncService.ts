import NetInfo from '@react-native-community/netinfo';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {LogListService} from './LogListService';
import {ExtensionGroupService} from './ExtensionGroupService';
import {ExtensionColumnService} from './ExtensionColumnService';
import {AssetClassActivityService} from './AssetClassActivityService';
import {AssetsService} from './AssetsService';
import {ServiceAgreementService} from './ServiceAgreementService';
import StaffService from './StaffService.ts';
import {AssetCoordinates} from '../database/AssetCoordinates';
import * as XLSX from 'xlsx';
import RNFS from 'react-native-fs';

// Define a date range interface for filtering the time range of synchronised data
export interface DateRange {
  startDate?: string;
  endDate?: string;
}

// Define the synchronisation service class
class SyncService {
  // Synchronise log list data, allowing paging and time range filtering
  public static async syncLogList(
    pages: number = 5, // 5 pages synchronised by default
    dateRange: DateRange = {}, // Optional date range filtering
  ): Promise<number> {
    // Check network connection status
    const state = await NetInfo.fetch();
    if (state.isConnected) {
      try {
        // Call the logging service to synchronise and return the number of tasks
        const taskCount = await LogListService.syncLogs(pages, dateRange,100);
        // Print the sync log on success
        console.log(
          `LogList synchronized successfully for ${pages} pages${
            dateRange.startDate && dateRange.endDate
              ? ` from ${dateRange.startDate} to ${dateRange.endDate}`
              : ''
          }`,
        );
        return taskCount; // Return the number of tasks synchronised
      } catch (error) {
        // Handling synchronisation failures
        console.error('Error syncing LogList:', error);
        // throw error;
      }
    } else {
      // Prints logs and throws errors when there is no network connection.
      console.log('No network connection. LogList sync skipped.');
      // throw new Error('No network connection available.');
    }
  }

  // Synchronise data according to a given date range
  public static async syncSelectedDate(dateRange: DateRange): Promise<{
    lastSyncTime: string; // Last synchronisation time
    status: string; // Synchronisation status
    taskCount: number; // Number of synchronised tasks
  }> {
    try {
      const taskCount = await this.syncLogList(999999, dateRange);

      // Get the current time and store it as the last synchronised time
      const now = new Date().toISOString();
      await AsyncStorage.setItem(
        `lastSyncTime_${dateRange.startDate}_${dateRange.endDate}`,
        now,
      );

      return {
        lastSyncTime: now,
        status: 'Success',
        taskCount,
      };
    } catch (error) {
      // Handling synchronisation failures
      console.error(
        `Sync failed for date range ${dateRange.startDate} to ${dateRange.endDate}:`,
        error,
      );
      throw new Error(
        `Failed to sync data for ${dateRange.startDate} to ${dateRange.endDate}. Please try again.`,
      );
    }
  }

  // Synchronise extension data, including extension groups and extension columns
  static async syncExtensionData() {
    const state = await NetInfo.fetch(); // Check network status
    if (state.isConnected) {
      try {
        // Call the service to synchronise extension groups and columns
        await ExtensionGroupService.syncExtensionGroups();
        await ExtensionColumnService.syncExtensionColumns();
        console.log('Extension data synchronized successfully');
      } catch (error) {
        console.error('Error syncing extension data:', error);
      }
    } else {
      console.log('No network connection. Extension data sync skipped.');
    }
  }

  // Synchronise asset classification activity data
  static async syncAssetClassActivities() {
    const state = await NetInfo.fetch();
    if (state.isConnected) {
      try {
        // Call service to synchronise asset classification activities
        await AssetClassActivityService.syncAssetClassActivities();
        console.log('Asset class activities synchronized successfully');
      } catch (error) {
        console.error('Error syncing asset class activities:', error);
      }
    } else {
      console.log(
        'No network connection. Asset class activities sync skipped.',
      );
    }
  }

  // Synchronise asset data, async
  static async syncAssets() {
    const state = await NetInfo.fetch();
    if (state.isConnected) {
      try {
        // Call the service to synchronise asset data
        await AssetsService.syncAssets();
        console.log('Assets synchronized successfully');
      } catch (error) {
        console.error('Error syncing assets:', error);
      }
    } else {
      console.log('No network connection. Assets sync skipped.');
    }
  }

  // Synchronisation of service agreement data
  static async syncServiceAgreements() {
    const state = await NetInfo.fetch();
    if (state.isConnected) {
      try {
        // Call Service Synchronisation Service Agreement
        await ServiceAgreementService.syncServiceAgreements();
        console.log('Service agreements synchronized successfully');
      } catch (error) {
        console.error('Error syncing service agreements:', error);
      }
    } else {
      console.log('No network connection. Service agreements sync skipped.');
    }
  }

  // Synchronise employee data
  static async syncStaff() {
    const state = await NetInfo.fetch();
    if (state.isConnected) {
      try {
        // Call service to synchronise employee data
        await StaffService.syncStaff();
        console.log('Staff synchronized successfully');
      } catch (error) {
        console.error('Error syncing staff:', error);
      }
    } else {
      console.log('No network connection. Staff sync skipped.');
    }
  }

  // Check if it is the first time the application is launched
  static async isFirstLaunch(): Promise<boolean> {
    try {
      const firstLaunchFlag = await AsyncStorage.getItem('firstLaunchFlag');
      console.log('firstLaunchFlag:', firstLaunchFlag);
      if (firstLaunchFlag === null) {
        await AsyncStorage.setItem('firstLaunchFlag', 'true');
        return true; // Returns true for the first time
      }
      return false; // Non-first-time startup
    } catch (error) {
      console.error('Error checking first launch flag:', error);
      return false;
    }
  }

  // Check if you should synchronise today
  static async shouldSyncToday(): Promise<boolean> {
    try {
      const lastSyncDate = await AsyncStorage.getItem('lastSyncDate');
      console.log('lastSyncDate:', lastSyncDate);
      const today = new Date().toISOString().split('T')[0]; // Get today's date

      if (lastSyncDate !== today) {
        await AsyncStorage.setItem('lastSyncDate', today);
        return true;
      }
      return false;
    } catch (error) {
      console.error('Error checking last sync date:', error);
      return false;
    }
  }

  // Check if weekly synchronisation is required
  static async shouldSyncWeekly(): Promise<boolean> {
    try {
      const lastSyncWeek = await AsyncStorage.getItem('lastSyncWeek');
      console.log('lastSyncWeek:', lastSyncWeek);
      const currentWeek = `${new Date().getFullYear()}-${new Date().getWeek()}`; // Get the current week

      if (lastSyncWeek !== currentWeek) {
        await AsyncStorage.setItem('lastSyncWeek', currentWeek);
        return true;
      }
      return false;
    } catch (error) {
      console.error('Error checking last sync week:', error);
      return false;
    }
  }

  // Check if monthly synchronisation is required
  static async shouldSyncMonthly(): Promise<boolean> {
    try {
      const lastSyncMonth = await AsyncStorage.getItem('lastSyncMonth');
      console.log('lastSyncMonth:', lastSyncMonth);
      const currentMonth = `${new Date().getFullYear()}-${new Date().getMonth()}`; // Get current month

      if (lastSyncMonth !== currentMonth) {
        await AsyncStorage.setItem('lastSyncMonth', currentMonth);
        return true;
      }
      return false;
    } catch (error) {
      console.error('Error checking last sync month:', error);
      return false;
    }
  }

  // Clear all sync flags
  static async clearSyncFlags() {
    try {
      await AsyncStorage.removeItem('firstLaunchFlag');
      await AsyncStorage.removeItem('lastSyncDate');
      await AsyncStorage.removeItem('lastSyncWeek');
      await AsyncStorage.removeItem('lastSyncMonth');
      console.log('All sync flags cleared');
    } catch (error) {
      console.error('Error clearing sync flags:', error);
    }
  }

  // Start all synchronisation tasks
  static async startAllSyncTasks() {
    // Check for first time startup
    const isFirstLaunch = await SyncService.isFirstLaunch();
    console.log('isFirstLaunch:', isFirstLaunch);

    if (isFirstLaunch) {
      await Promise.all([
        SyncService.syncExtensionData(),
        SyncService.syncAssetClassActivities(),
        SyncService.syncServiceAgreements(),
        SyncService.syncStaff()
      ]);
      // huge data sync
      await SyncService.syncAssets();
      await SyncService.syncLogList(1000);
    }

    // Check if daily synchronisation is required
    const shouldSyncToday = await SyncService.shouldSyncToday();
    if (shouldSyncToday) {
      await SyncService.syncLogList(1); // Synchronise 1 page per day
    }

    // Check if weekly synchronisation is required
    const shouldSyncWeekly = await SyncService.shouldSyncWeekly();
    if (shouldSyncWeekly) {
      await SyncService.syncLogList(5); // 5 synchronised pages per week
    }

    // Check if monthly synchronisation is required
    const shouldSyncMonthly = await SyncService.shouldSyncMonthly();
    if (shouldSyncMonthly) {
      //await SyncService.syncExtensionData();
      //await SyncService.syncAssetClassActivities();
      //await SyncService.syncAssets();
      //await SyncService.syncServiceAgreements();
    }
  }

  // Get last base data synchronisation information
  static async getLastBasicSyncInfo() {
    try {
      const lastSyncTime = await AsyncStorage.getItem('lastBasicSyncTime');
      const lastSyncStatus = await AsyncStorage.getItem('lastBasicSyncStatus');
      const lastSyncTaskCount = await AsyncStorage.getItem(
        'lastBasicSyncTaskCount',
      );

      return {
        time: lastSyncTime || 'Never',
        status: lastSyncStatus || 'Not Started',
        taskCount: parseInt(lastSyncTaskCount || '0', 10),
      };
    } catch (error) {
      console.error('Error getting last basic sync info:', error);
      return null;
    }
  }

  // Update base data synchronisation information
  static async updateBasicSyncInfo(status: string, taskCount: number) {
    try {
      await AsyncStorage.setItem('lastBasicSyncTime', new Date().toISOString());
      await AsyncStorage.setItem('lastBasicSyncStatus', status);
      await AsyncStorage.setItem(
        'lastBasicSyncTaskCount',
        taskCount.toString(),
      );
    } catch (error) {
      console.error('Error updating basic sync info:', error);
    }
  }

  // Synchronisation of underlying data
  public static async syncBasicData(): Promise<{
    lastSyncTime: string;
    status: string;
    taskCount: number;
  }> {
    try {
      await this.syncExtensionData();
      await this.syncAssetClassActivities();
      await this.syncAssets();
      await this.syncServiceAgreements();
      await this.syncStaff();

      const now = new Date().toISOString();
      await AsyncStorage.setItem('lastBasicSyncTime', now);

      return {
        lastSyncTime: now,
        status: 'Success',
        taskCount: 6, // Number of sync tasks performed
      };
    } catch (error) {
      console.error('Basic data sync failed:', error);
      throw new Error('Failed to sync basic data. Please try again.');
    }
  }

  // Manually synchronise the log list
  static async manualSync(pages: number = 1) {
    await SyncService.syncLogList(pages);
  }

  // Manually synchronise extended data
  static async manualSyncExtensionData() {
    await SyncService.syncExtensionData();
  }

  // Manually synchronise asset data
  static async manualSyncAssets() {
    await SyncService.syncAssets();
  }

  // Manually synchronise asset classification activity data
  static async manualSyncAssetClassActivities() {
    await SyncService.syncAssetClassActivities();
  }

  // Manually synchronise service agreement data
  static async manualSyncServiceAgreements() {
    await SyncService.syncServiceAgreements();
  }

  // Manually synchronise employee data
  static async manualSyncStaff() {
    await SyncService.syncStaff();
  }

  // Import asset coordinates from Excel file
  static async importAssetCoordinates(
    filePath: string,
    progressCallback?: (progress: number, message: string) => void
  ): Promise<{
    success: boolean;
    totalRecords: number;
    importedRecords: number;
    errorMessage?: string;
  }> {
    try {
      progressCallback?.(0, 'Reading Excel file...');

      // Read Excel file
      const fileExists = await RNFS.exists(filePath);
      if (!fileExists) {
        throw new Error('Excel file not found');
      }

      const fileData = await RNFS.readFile(filePath, 'base64');
      const workbook = XLSX.read(fileData, { type: 'base64' });

      progressCallback?.(20, 'Parsing Excel data...');

      // Get first worksheet
      const sheetName = workbook.SheetNames[0];
      if (!sheetName) {
        throw new Error('No worksheet found in Excel file');
      }

      const worksheet = workbook.Sheets[sheetName];
      const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });

      if (jsonData.length < 2) {
        throw new Error('Excel file must contain at least header and one data row');
      }

      progressCallback?.(40, 'Validating data format...');

      // Validate header
      const headers = jsonData[0] as string[];
      const expectedHeaders = ['AMSKEY', 'latitude', 'longitude'];
      const headerMap: { [key: string]: number } = {};

      for (const expectedHeader of expectedHeaders) {
        const index = headers.findIndex(h =>
          h && h.toString().toLowerCase().trim() === expectedHeader.toLowerCase()
        );
        if (index === -1) {
          throw new Error(`Missing required column: ${expectedHeader}`);
        }
        headerMap[expectedHeader] = index;
      }

      progressCallback?.(60, 'Processing coordinate data...');

      // Process data rows
      const coordinates: AssetCoordinates[] = [];
      const dataRows = jsonData.slice(1) as any[][];
      let validRecords = 0;
      let invalidRecords = 0;

      for (let i = 0; i < dataRows.length; i++) {
        const row = dataRows[i];

        try {
          const assetId = row[headerMap['AMSKEY']];
          const latitude = row[headerMap['latitude']];
          const longitude = row[headerMap['longitude']];

          // Validate data
          if (!assetId || isNaN(Number(assetId))) {
            console.warn(`Row ${i + 2}: Invalid AMSKEY: ${assetId}`);
            invalidRecords++;
            continue;
          }

          if (!latitude || isNaN(Number(latitude))) {
            console.warn(`Row ${i + 2}: Invalid latitude: ${latitude}`);
            invalidRecords++;
            continue;
          }

          if (!longitude || isNaN(Number(longitude))) {
            console.warn(`Row ${i + 2}: Invalid longitude: ${longitude}`);
            invalidRecords++;
            continue;
          }

          coordinates.push(new AssetCoordinates({
            asset_id: Number(assetId),
            latitude: Number(latitude),
            longitude: Number(longitude),
          }));

          validRecords++;
        } catch (error) {
          console.warn(`Row ${i + 2}: Error processing row:`, error);
          invalidRecords++;
        }
      }

      if (coordinates.length === 0) {
        throw new Error('No valid coordinate data found in Excel file');
      }

      progressCallback?.(80, `Importing ${coordinates.length} coordinates to database...`);

      // Batch import to database
      await AssetCoordinates.upsertBatch(coordinates);

      progressCallback?.(100, 'Import completed successfully');

      return {
        success: true,
        totalRecords: dataRows.length,
        importedRecords: validRecords,
      };

    } catch (error) {
      console.error('Error importing asset coordinates:', error);
      return {
        success: false,
        totalRecords: 0,
        importedRecords: 0,
        errorMessage: error.message || 'Unknown error occurred',
      };
    }
  }
}

export default SyncService;

// Helper function to get the week number
Date.prototype.getWeek = function () {
  var onejan = new Date(this.getFullYear(), 0, 1);
  return Math.ceil(((this - onejan) / 86400000 + onejan.getDay() + 1) / 7);
};
