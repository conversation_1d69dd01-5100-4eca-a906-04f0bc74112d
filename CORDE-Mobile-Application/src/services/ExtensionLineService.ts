import { fetchExtensionLineData } from '../api/ExtensionLineApi';
import { ExtensionLine } from '../database/ExtensionLine';

export class ExtensionLineService {
  // Define an asynchronous static method that accepts an extension group ID as a parameter,
  // with no return value, for syncing extensions from the API to the local database
  static async syncExtensionLines(extensionGroupId: number): Promise<void> {
    try {
      // Get extension line data from the API based on the extension group ID provided
      const apiData = await fetchExtensionLineData(extensionGroupId);
      console.log('Fetched API Data for ExtensionLines:', apiData);

      // Process the data returned by the API, converting it to an array of ExtensionLine instances
      const extensionLines = apiData.rows.map(row => new ExtensionLine({
        extension_line_id: parseInt(row.Key.KeyValue),
        extension_group_id: parseInt(row.Col01),
        extension_column_id: parseInt(row.Col02),
        display_label: row.Col03 ?? '',
      }));

      for (const line of extensionLines) {
        await ExtensionLine.upsert(line);
      }
    } catch (error) {
      console.error('Error syncing extension lines:', error);
      throw error;
    }
  }
}
