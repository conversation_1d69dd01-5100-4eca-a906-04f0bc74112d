import {fetchLogListData} from '../api/LogListApi';
import {fetchLogHeaderData} from '../api/LogHeaderApi';
import {LogList} from '../database/LogList';
import {NewLogModel} from '../models/NewLogModel';
import {networkService} from '../utils/NetworkUtils';
import {LogSyncService} from './LogSyncService';
import {MobileSyncLogs} from '../database/MobileSyncLogs';
import {StoredFiles} from '../database/StoredFiles';
import {FileUtils} from '../utils/FileUtils.ts';
import {LogExtensions} from '../database/LogExtensions.ts';
import {LogFilter} from "../types/LogFilterTypes.ts";
import { AuthService } from '../services/AuthService';

export interface DateRange {
  startDate?: string;
  endDate?: string;
}

// 在 DateRange 接口中添加新参数
export interface SyncOptions extends DateRange {
  fetchDetails?: boolean;  // 是否获取详细信息
  useDefaultDateRange?: boolean;  // 是否使用默认日期范围(一个月)
}

export class LogListService {

  // add a new method to fetch logs from the server
  private static getDefaultDateRange(useDefaultDateRange: boolean = true): DateRange {
    const endDate = new Date();
    let startDate: Date;

    if (useDefaultDateRange) {
      // 如果使用默认日期范围，显示最近一个月的数据
      startDate = new Date();
      startDate.setMonth(startDate.getMonth() - 1);
    } else {
      // 否则从2016年开始
      startDate = new Date('2016-01-01');
    }

    return {
      startDate: startDate.toISOString().split('T')[0],
      endDate: endDate.toISOString().split('T')[0]
    };
  }

  static async syncLogs(pages: number, dateRange: DateRange = {}, pageSize: number, syncOptions?: SyncOptions): Promise<number> {
    const isConnected = await networkService.isNetworkConnected();

    console.log('syncLogs isConnected:', isConnected);

    if (!isConnected) {
      console.log('No network connection, skipping sync');
      return 0;
    }
    let totalTaskCount = 0;
    let totalPages = 0;


    try {
      // if the useDefaultDateRange option is set to true, use the default date range
      const effectiveDateRange = this.getDefaultDateRange(syncOptions?.useDefaultDateRange);

      console.log('Syncing logs with options:', syncOptions, 'and date range:', effectiveDateRange);

      const firstPageData = await this.fetchApiData(1, dateRange?dateRange:effectiveDateRange,pageSize);
      totalPages = firstPageData.total;

      const firstPageCount = await this.processLogData(firstPageData);
      totalTaskCount += firstPageCount;

      const pagesToProcess = Math.min(pages, totalPages);
      for (let page = 2; page <= pagesToProcess; page++) {
        const apiData = await this.fetchApiData(page, dateRange,pageSize);
        const processedCount = await this.processLogData(apiData);
        totalTaskCount += processedCount;
      }

      console.log(`Synced ${totalTaskCount} logs successfully from ${pagesToProcess} pages.`);
      return totalTaskCount;
    } catch (error) {
      console.warn('Error in syncLogs:', error);
      return 0;
    }
  }

  private static async fetchApiData(page: number, dateRange: DateRange, pageSize: number = 100): Promise<any> {
    try {
      return await fetchLogListData(page, pageSize, dateRange.startDate, dateRange.endDate);
    } catch (error) {
      console.warn(`Error fetching API data for page ${page}:`, error);
      return null;
    }
  }

  private static async processLogData(apiData: any): Promise<number> {

    const logHeaderIds = apiData.rows.map((row: { LogHeaderId: number }) => row.LogHeaderId);
    const existingLogs = await LogList.getByIds(logHeaderIds);
    const existingLogMap = new Map(existingLogs.map(log => [log.log_header_id, log]));

    const logDetailsPromises = logHeaderIds.map(id => this.processLogDetails(id, apiData, existingLogMap));
    const processedLogs = await Promise.all(logDetailsPromises);

    return processedLogs.filter(Boolean).length;
  }

  private static async processLogDetails(
    logHeaderId: number,
    apiData: any,
    existingLogMap: Map<number, LogList>
  ): Promise<boolean> {
    try {
      const basicData = apiData.rows.find((row: { LogHeaderId: number }) => row.LogHeaderId === logHeaderId);
      const logDetails = await fetchLogHeaderData(logHeaderId);

      const logData = this.createLogData(logDetails, basicData, logHeaderId);

      if (existingLogMap.has(logHeaderId)) {
        logData.updated_date = new Date().toISOString();
        await LogList.updateByLogHeaderId(logHeaderId, logData);
      } else {
        logData.create_log_timestamp = new Date().toISOString();
        logData.updated_date = new Date().toISOString();
        await LogList.insert(logData);
      }
      return true;
    } catch (error) {
      console.error(`Error processing log details for ID ${logHeaderId}:`, error);
      return false;
    }
  }


  // Format date from basic data format like "25-May-25 08.00" to ISO format "2025-05-25T08:00:00"
  private static formatBasicDate(dateTimeStr: string): string {
    if (!dateTimeStr) return '';
    try {
      // Parse date format like "25-May-25 08.00"
      const parts = dateTimeStr.split(' ');
      if (parts.length !== 2) return dateTimeStr;

      const dateParts = parts[0].split('-');
      const timePart = parts[1];

      const day = parseInt(dateParts[0]);

      // Month conversion mapping
      const months = {
        'Jan': '01', 'Feb': '02', 'Mar': '03', 'Apr': '04', 'May': '05', 'Jun': '06',
        'Jul': '07', 'Aug': '08', 'Sep': '09', 'Oct': '10', 'Nov': '11', 'Dec': '12'
      };
      const month = months[dateParts[1]];

      // Year processing, assuming 21st century
      const year = '20' + dateParts[2];

      // Time processing, convert "08.00" to "08:00:00"
      const timeParts = timePart.split('.');
      const hours = timeParts[0].padStart(2, '0');
      const minutes = (timeParts[1] || '00').padStart(2, '0');

      return `${year}-${month}-${day.toString().padStart(2, '0')}T${hours}:${minutes}:00`;
    } catch (e) {
      console.error('Error parsing basic date:', e, dateTimeStr);
      return dateTimeStr; // Return original string if parsing fails
    }
  }

// Merge separate date and time fields from logDetails into a single ISO format
  private static mergeDetailsDateTime(dateStr: string, timeStr: string): string {
    if (!dateStr) return '';
    try {
      // Extract date part from dateStr (e.g., "2025-05-01T00:00:00")
      const datePart = dateStr.split('T')[0];

      // Extract time part from timeStr (e.g., "1900-01-01T08:00:00")
      let timePart = "00:00:00";
      if (timeStr) {
        const timeSegments = timeStr.split('T')[1]?.split('.')[0];
        if (timeSegments) {
          timePart = timeSegments;
        }
      }

      return `${datePart}T${timePart}`;
    } catch (e) {
      console.error('Error merging date and time:', e, dateStr, timeStr);
      return dateStr; // Return at least the date part if merging fails
    }
  }



  private static createLogData(logDetails: any, basicData: any, logHeaderId: number): Partial<LogList> {
    // Process scheduled date with consistent format
    let scheduledDate = '';
    if (basicData?.SchdDate) {
      // Format from basicData
      scheduledDate = this.formatBasicDate(basicData.SchdDate);
    } else if (logDetails?.SchdDate) {
      // Merge date and time from logDetails
      scheduledDate = this.mergeDetailsDateTime(logDetails.SchdDate, logDetails.SchdTime);
    }


    return {
      log_header_id: logHeaderId,
      status: basicData?.Status || logDetails.Status,
      order_no: logDetails?.OrderNo || '',
      schd_date: scheduledDate,
      log_type: basicData?.LogType || logDetails.LogType,
      company_id: logDetails?.CompanyID?.toString(),
      allocation_person_id: logDetails?.AllocationPerson?.PersonID || 0,
      allocation_person_full_name: logDetails?.AllocationPerson?.FullName || '',
      completed_time: basicData?.CompletedTime || logDetails?.CompletedTime,
      completed_date: basicData?.CompletedDate || logDetails?.CompletedDate,
      description: JSON.stringify(logDetails?.Description),
      log_details: JSON.stringify(logDetails?.Description),
      arrive_date: basicData?.ArriveDate || logDetails?.ArriveDate,
      arrive_time: basicData?.ArriveTime || logDetails?.ArriveTime,
      service_agreement_id: logDetails?.ServiceAgreement?.ID || 0,
      asset_id: logDetails?.Asset?.AssetID || 0,
      completion_details: basicData?.CompletionDetails || logDetails?.CompletionDetails,
      contact_details: basicData?.ContactDetails || logDetails?.ContactDetails,
      asset_code: logDetails?.Asset?.AssetCode || '',
      asset_description: logDetails?.Asset?.Description || '',
      log_address: this.generateSiteAddress(logDetails),
      stored_files: JSON.stringify(logDetails?.StoredFiles || []),
      is_completed: false,
      is_draft: false,
      create_log_timestamp: '',
      event_time_stamp: logDetails?.EventTimeStamp,
      updated_date: basicData?.UpdatedDate || '',
      service_type: basicData?.ServiceType || '',
      priority: logDetails?.Priority || '',
      comments: logDetails?.Comment || '',
      task_code: logDetails?.AssetTask?.Task?.TaskCode || '',
    };
  }

  private static generateSiteAddress(logDetails: any): string {
    if (logDetails.Asset) {
      const { Street, Suburb, AssetLocation } = logDetails.Asset;
      return [Street, Suburb, AssetLocation].filter(Boolean).join(', ');
    } else {
      const { StreetNo, Street, Suburb } = logDetails;
      return [StreetNo, Street, Suburb].filter(Boolean).join(' ');
    }
  }

  static async fetchLogs(
    page: number = 1,
    pageSize: number = 20,
  ): Promise<LogList[]> {
    try {
      // Get the list of logs from the database
      return await LogList.getAll(page, pageSize);
    } catch (error) {
      console.error('Error fetching logs:', error);
      throw error;
    }
  }

  static async createNewLog(
    newLogData: NewLogModel,
    files: any[],
  ): Promise<number> {
    try {

      const currentUser = await AuthService.getCurrentUser();

      const localLogId = await LogList.createNewLog(newLogData);

      await this.saveFilesLocally(localLogId, files);

      const syncLog = await MobileSyncLogs.insert({
        conflict_status: '',
        offline_indicator: false,
        retry_count: 0,
        sync_task_count: 0,
        user_id: currentUser?.person_id ?? 1,
        local_log_id: localLogId,
        sync_status: 'Open',
      });

      // If there is a network connection, call the synchronisation service
      const isNetworkConnected = await networkService.isNetworkConnected();
      if (isNetworkConnected) {
        await LogSyncService.syncNewLogToServer(localLogId);
      }else {
        console.log('Network not connected:', isNetworkConnected);
      }

      return localLogId;
    } catch (error) {
      console.error('Error creating new log:', error);
      if (error instanceof Error) {
        console.error('Error details:', error.message, error.stack);
      }
      throw error;
    }
  }

  // Define asynchronous methods to save drafts, accepting partial log data, file arrays, and extension column data as arguments
  static async saveDraft(
    logData: Partial<LogList>,
    files: any[],
    extendColumns: any[],
  ): Promise<void> {
    try {
      // console.log('Starting saveDraft with data:', JSON.stringify(logData));
      // console.log('Files:', files);
      // console.log('ExtendColumns:', JSON.stringify(extendColumns));

      // Data validation
      this.validateDraftData(logData);
      logData.is_draft = true;

      // Update the draft information in the LogList table.
      await LogList.update(logData.local_log_id, {
        ...logData,
      });


      // console.log('Saving log extensions...');
      await this.saveLogExtensions(logData.local_log_id, extendColumns);
      // console.log('Log extensions saved successfully');

      // Document processing
      // console.log('Handling files...');
      await this.handleFiles(logData.local_log_id, files);
      // console.log('Files handled successfully');

      // console.log('Updating sync status...');
      await this.updateMobileSyncLogsStatus(logData.local_log_id, 'draft');
      // console.log('Sync status updated successfully');

      console.log('Draft saved successfully');
    } catch (error) {
      console.error('Error saving draft:', error);
      if (error instanceof Error) {
        console.error('Error details:', error.message, error.stack);
      }
      throw error;
    }
  }

  static async submitLog(
    logData: Partial<LogList>,
    extendColumns: any[],
  ): Promise<void> {
    try {
      console.log('Starting submitLog with data:', JSON.stringify(logData));
      console.log('extendColumns-----------------------:', JSON.stringify(extendColumns));     
      this.validateSubmitData(logData);
      logData.is_draft = false;

      const isAllFieldsFilled = logData.completed_time &&
        logData.completed_date &&
        logData.arrive_date &&
        logData.arrive_time;

      if (isAllFieldsFilled) {
        logData.is_completed = true;
        logData.status = 'Completed';
      }

      logData.updated_date = new Date().toISOString(); 
      await LogList.update(logData.local_log_id, {
        ...logData,
      });
      await this.saveLogExtensions(logData.local_log_id, extendColumns);
      await this.updateMobileSyncLogsStatus(logData.local_log_id, 'submitting');
      
      const isNetworkConnected = await networkService.isNetworkConnected();
      if (isNetworkConnected) {
        console.log('Network connected, attempting immediate sync');
        await LogSyncService.syncUpdatedLogToServer(logData.local_log_id);
      } else {
        console.log('No network connection, log saved locally and marked for background sync');
      }
    } catch (error) {
      console.error('Error submitting log:', error);
      throw error;
    }
  }

  static async submitLogWithFiles(
    logData: Partial<LogList>,
    extendColumns: any[],
    files: any[] = []  // 添加files参数
  ): Promise<void> {
    try {
      // 检查所有必填字段
      const isAllFieldsFilled = logData.completed_time && logData.completed_date

      // 更新状态
      logData.is_draft = false;
      logData.updated_date = new Date().toISOString();

      if (isAllFieldsFilled) {
        logData.is_completed = true;
        // Use status that's valid on server
        logData.status = 'Completed';
      }

      await this.saveDraft(logData, files, extendColumns);

      await this.updateMobileSyncLogsStatus(logData.local_log_id, 'submitting');

      // 2. 检查网络连接并执行提交逻辑
      const isNetworkConnected = await networkService.isNetworkConnected();
      if (isNetworkConnected) {
        console.log('Network available, proceeding with submission...');

        // 同步到服务器
        await LogSyncService.syncUpdatedLogToServer(logData.local_log_id);
        console.log('Server sync completed');
      }
    } catch (error) {
      console.error('Error in submitLog:', error);
      throw error;
    }
  }

  private static async saveLogExtensions(
    localLogId: number,
    extendColumns: any[],
  ): Promise<void> {
    console.log('Saving log extensions for localLogId:', localLogId);
    console.log('ExtendColumns:', JSON.stringify(extendColumns));

    await LogExtensions.deleteByLocalLogId(localLogId);

    for (const column of extendColumns) {
      console.log('Inserting extension:', JSON.stringify(column));
      await LogExtensions.insert({
        local_log_id: localLogId,
        name: column.column_name,
        value_str: column.value || '',
      });
    }
  }

  private static validateDraftData(logData: Partial<LogList>): void {
    if (!logData.local_log_id) {
      throw new Error('Invalid log ID');
    }
  }

  private static validateSubmitData(logData: Partial<LogList>): void {
    this.validateDraftData(logData);
  }

  private static async handleFiles(
    localLogId: number,
    files: any[],
  ): Promise<void> {
    // Get existing stored files
    const existingFiles = await StoredFiles.getByLocalLogId(localLogId);
    
    // Only delete files that haven't been uploaded yet (no stored_file_id)
    for (const existingFile of existingFiles) {
      if (!existingFile.stored_file_id || existingFile.stored_file_id === 0) {
        console.log(`Deleting unuploaded file: ${existingFile.file_name}`);
        await StoredFiles.delete(existingFile.id);
      } else {
        console.log(`Keeping uploaded file: ${existingFile.file_name} (stored_file_id: ${existingFile.stored_file_id})`);
      }
    }

    // Only save new files that aren't already stored
    const newFiles = [];
    
    for (const file of files) {
      const existingFile = existingFiles.find(existing => 
        existing.file_name === file.name && 
        existing.stored_file_id && 
        existing.stored_file_id > 0
      );
      
      if (!existingFile) {
        // New file, add it
        newFiles.push(file);
        console.log(`New file detected: ${file.name}`);
      } else {
        // File with same name exists, check if it's actually the same file
        try {
          // Check if the new file has different URI (different actual file)
          const existingFilePath = existingFile.file_path;
          const newFileUri = file.uri;
          
          // If URIs are different, it's likely a different file with same name
          if (newFileUri !== existingFilePath && !newFileUri.includes(existingFilePath)) {
            console.log(`File ${file.name} appears to be replaced (different URI)`);
            console.log(`Existing: ${existingFilePath}, New: ${newFileUri}`);
            
            // Mark the old file record for deletion and add the new file
            await StoredFiles.delete(existingFile.id);
            newFiles.push(file);
          } else {
            console.log(`File ${file.name} appears to be the same, skipping`);
          }
        } catch (error) {
          console.error(`Error comparing files for ${file.name}:`, error);
          // If in doubt, treat as new file
          newFiles.push(file);
        }
      }
    }

    if (newFiles.length > 0) {
      console.log(`Saving ${newFiles.length} new files`);
      await this.saveFilesLocally(localLogId, newFiles);
    } else {
      console.log('No new files to save');
    }
  }

  private static async updateMobileSyncLogsStatus(
    localLogId: number,
    status: string,
  ): Promise<void> {
    const currentUser = await AuthService.getCurrentUser();
    const syncLog = await MobileSyncLogs.getByLocalLogId(localLogId);
    if (syncLog) {
      await MobileSyncLogs.update(syncLog.sync_log_id, {
        sync_status: status,
        last_update_time: new Date().toISOString(),
      });
    } else {
      await MobileSyncLogs.insert({
        user_id: currentUser?.person_id ?? 1,
        local_log_id: localLogId,
        sync_status: status,
        offline_indicator: false,
        sync_task_count: 0,
        conflict_status: '',
        retry_count: 0,
      });
    }
  }

  public static async saveFilesLocally(
    localLogId: number,
    files: any[],
  ): Promise<void> {
    console.log(`Saving ${files.length} files for log ${localLogId}`);
    for (const file of files) {
      try {
        const savedFilePath = await FileUtils.saveFile(file.uri, file.name);
        const storedFile = await StoredFiles.insert(<StoredFiles>{
          file_name: file.name,
          file_extension: FileUtils.getFileExtension(file.name),
          content_type: file.type,
          related_record_id: localLogId.toString(),
          related_record_type: 'LogList',
          local_log_id: localLogId,
          file_path: savedFilePath,
        });
      } catch (error) {
        console.error('Error saving file:', file.name, error);
        if (error instanceof Error) {
          console.error('Error details:', error.message, error.stack);
        }
      }
    }
  }


  static async fetchFilteredLogs(
    page: number = 1,
    pageSize: number = 20,
    filters: LogFilter = {}
  ): Promise<LogList[]> {
    try {
      // get the filtered logs from the database
      const logs = await LogList.getFilteredLogs(page, pageSize, filters);
      // if there is a network connection, sync the logs
      const isConnected =await networkService.isNetworkConnected()
      console.log('fetchFilteredLogs isConnected:', isConnected);


      // 暂时禁用自动同步功能以减少API请求
      // if (await isConnected) {
      //   const syncPromises = logs
      //     .filter(log => log.log_header_id)
      //     .map(log =>
      //       this.syncLogById(log.log_header_id)
      //         .catch(err => {
      //           console.error(`Error syncing log ${log.log_header_id}:`, err);
      //           return Promise.resolve();
      //         })
      //     );

      //   await Promise.all(syncPromises);
      // }

      // return the filtered logs
      return await LogList.getFilteredLogs(page, pageSize, filters);

    } catch (error) {
      console.error('Error fetching filtered logs:', error);
      return [];
    }
  }

  static async syncLogById(logHeaderId: number | undefined): Promise<void> {

    if (!logHeaderId) return;
    try {
      if (!(await networkService.isNetworkConnected())) {
        console.log('No network connection, skipping sync');
        return;
      }

      // get the log header data from the server
      const serverData = await fetchLogHeaderData(logHeaderId);

      // use the server data to create the log data
      const logData = this.createLogData(serverData, serverData, logHeaderId);

      // update the log in the database
      await LogList.updateByLogHeaderId(logHeaderId, logData);

    } catch (error) {
      console.error(`Error syncing log ${logHeaderId}:`, error);
      // throw error;
    }
  }

}
