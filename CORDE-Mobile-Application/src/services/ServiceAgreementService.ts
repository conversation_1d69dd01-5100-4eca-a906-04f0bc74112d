import { fetchServiceAgreementData } from '../api/ServiceAgreementApi';
import { ServiceAgreement } from '../database/ServiceAgreement';

// Define the service agreement service class
export class ServiceAgreementService {
  // Define static methods for the Synchronisation Service Protocol
  static async syncServiceAgreements(): Promise<void> {
    try {
      // Get service agreement data from the API
      const apiData = await fetchServiceAgreementData();
      // Print the data retrieved from the API
      console.log('Fetched API Data for ServiceAgreements:', apiData);

      // Convert API data into an array of Service Agreement objects
      const serviceAgreements = apiData.rows.map(row => new ServiceAgreement({
        id: parseInt(row.Key.KeyValue),
        code: row.Col02 ?? '',
        description: row.Col03 ?? '',
        details: row.Col04 ?? '',
        client_source: row.Col05 ?? '',
        company_id: row.Col06 ?? '',
        default_job_code: row.Col09 ?? '',
        default_parent_agreement: row.Col07 ?? row.Col08 ?? ''
      }));

      // Iterate through the array of service agreement objects and update or insert each object in the database
      for (const agreement of serviceAgreements) {
        await ServiceAgreement.upsert(agreement);
      }
    } catch (error) {
      console.error('Error syncing service agreements:', error);
      throw error;
    }
  }
}
