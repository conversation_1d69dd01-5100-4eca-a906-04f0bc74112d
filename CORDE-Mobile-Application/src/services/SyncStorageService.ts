// Import AsyncStorage from React Native AsyncStorage package
import AsyncStorage from '@react-native-async-storage/async-storage';

// Define constants for storage keys
const BASIC_SYNC_KEY = '@BasicSyncInfo';
const SELECTED_DATE_SYNC_KEY = '@SelectedDateSyncInfo';

// Define the structure of the SyncInfo object
interface SyncInfo {
  lastSyncTime: string;
  status: string;
  taskCount: number;
}

// Export the SyncStorageService object with methods to save and retrieve sync information
export const SyncStorageService = {
  async saveBasicSyncInfo(info: SyncInfo): Promise<void> {
    try {
      //set items
      await AsyncStorage.setItem(BASIC_SYNC_KEY, JSON.stringify(info));
    } catch (error) {
      console.error('Error saving basic sync info:', error);
    }
  },

  // Retrieve basic sync information from AsyncStorage
  async getBasicSyncInfo(): Promise<SyncInfo | null> {
    try {
      const info = await AsyncStorage.getItem(BASIC_SYNC_KEY);
      return info ? JSON.parse(info) : null;
    } catch (error) {
      console.error('Error getting basic sync info:', error);
      return null;
    }
  },

  // Save selected date sync information to AsyncStorage
  async saveSelectedDateSyncInfo(info: SyncInfo): Promise<void> {
    try {
      await AsyncStorage.setItem(SELECTED_DATE_SYNC_KEY, JSON.stringify(info));
    } catch (error) {
      console.error('Error saving selected date sync info:', error);
    }
  },

  // Retrieve selected date sync information from AsyncStorage
  async getSelectedDateSyncInfo(): Promise<SyncInfo | null> {
    try {
      const info = await AsyncStorage.getItem(SELECTED_DATE_SYNC_KEY);
      return info ? JSON.parse(info) : null;
    } catch (error) {
      console.error('Error getting selected date sync info:', error);
      return null;
    }
  },
};
