// services/LocationService.ts
import Geolocation from '@react-native-community/geolocation';
import { PermissionsAndroid, Platform } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';

const LOCATION_PERMISSION_KEY = 'locationPermissionGranted';
const LAST_LOCATION_KEY = 'lastKnownLocation';

interface Location {
  latitude: number;
  longitude: number;
  timestamp: number;
}

export class LocationService {
  private static instance: LocationService;
  private isInitialized: boolean = false;
  private permissionGranted: boolean = false;
  private watchId: number | null = null;
  private lastLocation: Location | null = null;
  private locationListeners: Set<(location: Location) => void> = new Set();

  private constructor() {}

  public static getInstance(): LocationService {
    if (!LocationService.instance) {
      LocationService.instance = new LocationService();
    }
    return LocationService.instance;
  }

  public async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      // 配置定位选项
      Geolocation.setRNConfiguration({
        skipPermissionRequests: false,
        authorizationLevel: 'whenInUse',
        locationProvider: 'auto'
      });

      // 检查权限
      const granted = await AsyncStorage.getItem(LOCATION_PERMISSION_KEY);
      this.permissionGranted = granted === 'true';

      if (!this.permissionGranted) {
        await this.requestLocationPermission();
      }

      // 尝试加载上次保存的位置
      const lastLocationStr = await AsyncStorage.getItem(LAST_LOCATION_KEY);
      if (lastLocationStr) {
        const lastLocation = JSON.parse(lastLocationStr) as Location;
        // 只有当缓存的位置不超过5分钟才使用
        if (Date.now() - lastLocation.timestamp < 5 * 60 * 1000) {
          this.lastLocation = lastLocation;
        }
      }

      // 开始监听位置变化
      if (this.permissionGranted) {
        this.startWatchingLocation();
      }

      this.isInitialized = true;
    } catch (error) {
      console.error('Error initializing location service:', error);
      throw error;
    }
  }

  private async requestLocationPermission(): Promise<boolean> {
    try {
      if (Platform.OS === 'android') {
        const granted = await PermissionsAndroid.request(
          PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
          {
            title: 'Location Permission',
            message: 'App needs access to your location',
            buttonNeutral: 'Ask Me Later',
            buttonNegative: 'Cancel',
            buttonPositive: 'OK',
          }
        );

        this.permissionGranted = granted === PermissionsAndroid.RESULTS.GRANTED;
        await AsyncStorage.setItem(LOCATION_PERMISSION_KEY, String(this.permissionGranted));

        if (this.permissionGranted) {
          this.startWatchingLocation();
        }

        return this.permissionGranted;
      }

      this.permissionGranted = true;
      await AsyncStorage.setItem(LOCATION_PERMISSION_KEY, 'true');
      this.startWatchingLocation();
      return true;
    } catch (error) {
      console.error('Error requesting location permission:', error);
      return false;
    }
  }

  private startWatchingLocation(): void {
    if (this.watchId !== null) return;

    this.watchId = Geolocation.watchPosition(
      (position) => {
        const location: Location = {
          latitude: position.coords.latitude,
          longitude: position.coords.longitude,
          timestamp: Date.now()
        };
        this.lastLocation = location;
        this.saveLocation(location);
        this.notifyListeners(location);
      },
      (error) => {
        // 只有在真正需要处理的错误时才输出
        if (error.code !== 3 || // 不是超时错误
          (error.code === 3 && !error.message.includes("temporarily"))) { // 不是临时不可用
          console.error('Watch position error:', error);
        }
      },
      {
        enableHighAccuracy: true,
        distanceFilter: 10,
        interval: 10000,
        fastestInterval: 5000
      }
    );
  }

  private async saveLocation(location: Location): Promise<void> {
    try {
      await AsyncStorage.setItem(LAST_LOCATION_KEY, JSON.stringify(location));
    } catch (error) {
      console.error('Error saving location:', error);
    }
  }

  private notifyListeners(location: Location): void {
    this.locationListeners.forEach(listener => {
      try {
        listener(location);
      } catch (error) {
        console.error('Error notifying location listener:', error);
      }
    });
  }

  public async getCurrentLocation(
    options: { enableHighAccuracy?: boolean; timeout?: number; maximumAge?: number } = {}
  ): Promise<Location> {
    if (!this.isInitialized) {
      await this.initialize();
    }

    if (!this.permissionGranted) {
      throw new Error('Location permission not granted');
    }

    // 如果有最近的位置缓存且不超过30秒，直接返回
    if (this.lastLocation && (Date.now() - this.lastLocation.timestamp < 30000)) {
      return this.lastLocation;
    }

    return new Promise((resolve, reject) => {
      Geolocation.getCurrentPosition(
        position => {
          const location: Location = {
            latitude: position.coords.latitude,
            longitude: position.coords.longitude,
            timestamp: Date.now()
          };
          this.lastLocation = location;
          this.saveLocation(location);
          resolve(location);
        },
        error => {
          if (error.code === 3 && options.enableHighAccuracy) {
            // 如果高精度定位超时，尝试低精度定位
            this.getCurrentLocation({ ...options, enableHighAccuracy: false })
              .then(resolve)
              .catch(reject);
          } else {
            reject(error);
          }
        },
        {
          enableHighAccuracy: options.enableHighAccuracy ?? true,
          timeout: options.timeout ?? 15000,
          maximumAge: options.maximumAge ?? 10000,
        }
      );
    });
  }

  public addLocationListener(listener: (location: Location) => void): void {
    this.locationListeners.add(listener);
  }

  public removeLocationListener(listener: (location: Location) => void): void {
    this.locationListeners.delete(listener);
  }

  public cleanup(): void {
    if (this.watchId !== null) {
      Geolocation.clearWatch(this.watchId);
      this.watchId = null;
    }
    this.locationListeners.clear();
  }
}

export const locationService = LocationService.getInstance();
