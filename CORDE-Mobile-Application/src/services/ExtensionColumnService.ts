import {fetchExtensionColumnData} from '../api/ExtensionColumnApi';
import {ExtensionColumn} from '../database/ExtensionColumn';

export class ExtensionColumnService {
  // Define an asynchronous static method with no return value to sync the extension columns from the API to the local database
  static async syncExtensionColumns(): Promise<void> {
    try {
      // Get extension column data from the API
      const apiData = await fetchExtensionColumnData();
      console.log('Fetched API Data for ExtensionColumns:', apiData);

      // Process the data returned by the API, converting it into an array of ExtensionColumn instances
      const extensionColumns = apiData.rows.map(
        row =>
          new ExtensionColumn({
            extension_column_id: parseInt(row.Key.KeyValue), // Convert Key.KeyValue to an integer as a column ID
            extension_table_id: parseInt(row.Col01), // Convert Col01 to an integer as a table ID
            column_name: row.Col02 ?? '', // Use Col02 as the column name, or the empty string if it is not available
            data_type: row.Col03 ?? '',
            combo_list: row.Col04 ?? '',
            required: row.Col05 === '-1', // Judge the value of Col05, if it is '-1' then it is required
          }),
      );

      // Iterate through the array of processed expansion columns,
      // inserting or updating them one by one into the local database
      for (const column of extensionColumns) {
        await ExtensionColumn.upsert(column);
      }
    } catch (error) {
      console.error('Error syncing extension columns:', error);
      throw error;
    }
  }
}
