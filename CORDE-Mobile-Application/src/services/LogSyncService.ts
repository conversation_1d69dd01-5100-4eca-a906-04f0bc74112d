import {LogList} from '../database/LogList';
import {
  createNewLog,
  updateLog,
  NewLogData,
  updateLogHeader,
  updateLogExtension,
  UpdateLogHeaderData, createLogExtension,
} from '../api/LogApi';
import {MobileSyncLogs} from '../database/MobileSyncLogs';
import {networkService} from '../utils/NetworkUtils';
import {FileUtils} from '../utils/FileUtils.ts';
import {LogExtensions} from '../database/LogExtensions.ts';

export class LogSyncService {
  // Converting localLog to NewLogData format
  /**
   * 2. RelatedRecord and RelatedRecordID:
   *    I've observed that when creating a new log without uploading any files, the system still submits a fixed value for RelatedRecord (LogHeaderFile_3417). However, the RelatedRecordID changes with each submission (e.g., 240814214759).
   *
   *    Could you please explain:
   *    a) What does the RelatedRecord value (LogHeaderFile_3417) represent? The RelatedRecordID is a required field when using the UploadFileAPI, if a related record is not specified it looks to be defaulting to the current date/time (the same logic as you have configured for the OrderNo when its not specified
   *    b) What is the significance of the RelatedRecordID? ReleatedRecordID enables the file uploaded through the post /api/UploadFileApi to be linked to appropriate record.
   *    c) How is the RelatedRecordID generated, and what determines its value? RelatedRecordID should be the LogHeaderID of the Log that we wish to attach the file to. In the case of the Creating a new Log we:
   *
   * Use the ServiceAgreementApi to create the log, this provides the LogHeaderID as an output.
   * We take the LogHeaderID that has been output from the ServiceAgreementApi and use it within the RelatedRecordID field of the UploadFileApi to link the file we are uploading to the log we have just created.
   */

  // Methods for synchronising new logs to the server
  static async syncNewLogToServer(localLogId: number): Promise<void> {
    // Start synchronising logs, print log IDs
    console.log(`Starting syncNewLogToServer for localLogId: ${localLogId}`);
    try {
      const syncLog = await MobileSyncLogs.getByLocalLogId(localLogId);
      if (!syncLog) {
        console.error('Sync log not found for local log ID:', localLogId);
        return;
      }

      const localLog = await LogList.getByLocalLogId(localLogId);
      if (!localLog) {
        console.error('Local log not found for ID:', localLogId);
        return;
      }
      console.log('Local log found:', localLog);

      const newLogData: NewLogData = {
        serviceAgreementID: localLog.service_agreement_id,
        orderNo: localLog.order_no,
        description: localLog.description,
        allocatedPersonId: localLog.allocation_person_id,
        assetID: localLog.asset_id,
        relatedRecord: 'LogHeaderFile_'+localLog.allocation_person_id,
        relatedRecordID: localLog.order_no,
      };
      console.log('Prepared newLogData:', newLogData);

      const serverResponse = await createNewLog(newLogData);
      console.log('Server response:', serverResponse);
      await LogList.update(localLogId, {
        log_header_id: serverResponse.LogHeaderID,
        status: 'Allocated',
      });

      let logHeaderId = serverResponse.LogHeaderID;
      // Create LogExtension
      await createLogExtension({logHeaderId});

      await FileUtils.uploadFiles(localLogId, logHeaderId);

      if (syncLog.sync_log_id != null) {
        await MobileSyncLogs.update(syncLog.sync_log_id, {
          log_header_id: serverResponse.LogHeaderID,
          sync_status: 'Allocated',
          last_sync_time: new Date().toISOString(),
        });
      }

    } catch (error) {
      console.error('Error syncing new log to server:', error);
      await this.handleSyncError(localLogId, error);
    }
  }

  // Methods for updating existing logs to the server
  public static async syncUpdatedLogToServer(localLogId: number): Promise<void> {
    try {
      // Get the corresponding local log from the local database.
      const localLog = await LogList.getByLocalLogId(localLogId);
      // Throw an error if the local log is not found or if the local log is not synchronised.
      if (!localLog || !localLog.log_header_id) {
        throw new Error('Local log not found or not synced');
      }

      // Prepare to update UpdateLogHeaderData
      const updateLogHeaderData: UpdateLogHeaderData = {
        logHeaderId: localLog.log_header_id,
        comment: localLog.comments,
        completionDetails: localLog.completion_details,
        assetId: localLog.asset_id?.toString(),
        arriveDate: localLog.arrive_date,
        arriveTime: localLog.arrive_time,
        startLat: localLog.arrive_latitude?.toString(),
        startLong: localLog.arrive_longitude?.toString(),
        completedDate: localLog.completed_date,
        completedTime: localLog.completed_time,
        completedLat: localLog.completion_latitude?.toString(),
        completedLong: localLog.completion_longitude?.toString(),
        restoreDate: localLog.schd_date, // Assuming this is the restore date
        restoreTime: '', // You may need to add this field to LogList if it's required
        status: localLog.status,
      };

      // Call the API to update the server-side log header
      await updateLogHeader(updateLogHeaderData);

      // Get and synchronise LogExtension data
      const logExtensions = await LogExtensions.getByLocalLogId(localLogId);
      if (logExtensions.length > 0) {
        const columnNames = logExtensions.map(ext => ext.name).join(',');
        const extensionData = {
          Key: {
            TableName: 'LogExtension',
            KeyName: 'LogHeaderID',
            KeyValue: localLog.log_header_id.toString(),
            ColumnNames: columnNames,
          },
        };

        // Iterate over the extension data and add it to the request body
        logExtensions.forEach((ext, index) => {
          extensionData[`Col0${index + 1}`] = ext.value_str;
        });

        // Call the API update log extension
        await updateLogExtension(extensionData);
      }

      // Uploading files
      await FileUtils.uploadFiles(localLogId, localLog.log_header_id);

      if (localLog.completed_time && localLog.completed_date) {
        console.log('Updating log status to Completed');
        await LogList.update(localLogId, {status: 'Completed', 'is_completed': true});
        await this.updateMobileSyncLogsStatus(localLogId, 'Completed');
      }

      // Print the log synchronisation success message
      console.log('Log summitted synced successfully');
    } catch (error) {
      console.error('Error summitted syncing log to server:', error);
      await this.handleSyncError(localLogId, error);
    }
  }


  private static async updateMobileSyncLogsStatus(
    localLogId: number,
    status: string,
  ): Promise<void> {
    const syncLog = await MobileSyncLogs.getByLocalLogId(localLogId);
    if (syncLog) {
      await MobileSyncLogs.update(syncLog.sync_log_id, {
        sync_status: status,
        last_sync_time: new Date().toISOString(),
      });
    }
  }

  // 错误处理的方法
  private static async handleSyncError(
    localLogId: number,
    error: any,
  ): Promise<void> {
    await LogList.update(localLogId, {status: 'submitting'});
    const syncLog = await MobileSyncLogs.getByLocalLogId(localLogId);
    if (syncLog) {
      await MobileSyncLogs.update(syncLog.sync_log_id, {
        sync_status: 'error',
        sync_errors: JSON.stringify(error),
        retry_count: syncLog.retry_count + 1,
        last_update_time: new Date().toISOString(),
      });
    }
  }

  static async syncOpenLogs(): Promise<void> {
    console.log('Starting syncOpenLogs');
    if (!networkService.isNetworkConnected()) {
      console.log('No network connection. Skipping syncPendingLogs.');
      return;
    }

    const pendingLogs = await MobileSyncLogs.getOpenSyncLogs();

    for (const syncLog of pendingLogs) {
      await this.syncNewLogToServer(syncLog.local_log_id);
    }
  }

  static async syncSubmittingLog(): Promise<void> {
    console.log('Starting syncSubmittingLog');
    if (!networkService.isNetworkConnected()) {
      console.log('No network connection. Skipping syncSubmittingLog.');
      return;
    }

    const pendingLogs = await MobileSyncLogs.getSubmittingSyncLogs();
    console.log('Pending logs:', pendingLogs);

    for (const syncLog of pendingLogs) {
      await this.syncUpdatedLogToServer(syncLog.local_log_id);
    }
    console.log('Exiting syncSubmittingLog');
  }

  private static async handleSyncError(
    localLogId: number,
    error: any,
  ): Promise<void> {
    console.log('Entering handleSyncError');
    const syncLog = await MobileSyncLogs.getByLocalLogId(localLogId);
    if (!syncLog) {
      console.log('No sync log found for localLogId:', localLogId);
      return;
    }

    console.log('Updating sync log with error status');
    await MobileSyncLogs.update(syncLog.sync_log_id, {
      sync_status: 'error',
      sync_errors: JSON.stringify(error),
      retry_count: syncLog.retry_count + 1,
      last_update_time: new Date().toISOString(),
    });
    console.log('Sync log updated with error status');

    if (syncLog.retry_count >= 3) {
      console.log(`Max retry count reached for log ID: ${localLogId}`);
    }
  }
}
