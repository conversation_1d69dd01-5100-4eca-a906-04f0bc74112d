import {fetchStaffData} from '../api/StaffApi';
import {Staff} from '../database/Staff';

// Define the Employee Services class
export class StaffService {
  // Static method that processes employee data rows and inserts or updates the database
  static async processStaffRows(rows: any[]): Promise<void> {
    // Iterate through each row of incoming employee data
    for (const row of rows) {
      // Create a new employee object and populate the appropriate fields
      const staff = new Staff({
        person_id: parseInt(row.Col01),
        full_name: row.Col02 ?? '',
        email_address: row.Col03 ?? '',
        mobile_phone: row.Col04 ?? '',
        profit_centre_code: row.Col05 ?? '',
        employee_no: row.Col06 ?? '',
      });

      // Try to insert or update employee data into the database
      try {
        await Staff.upsert(staff);
      } catch (error) {
        // Print an error message if the insert or update operation fails
        console.error(
          `Error upserting staff ${staff.full_name} (ID: ${staff.person_id}):`,
          error,
        );
      }
    }
  }

  // Static method to synchronise employee data from the API to the local database
  static async syncStaff(): Promise<void> {
    try {
      // Get the first page of employee data
      const initialData = await fetchStaffData(1);
      // Calculate the total number of pages to be paged based on the total number of records
      const totalPages = Math.ceil(initialData.records / 100);

      // Process the first page of employee data
      await StaffService.processStaffRows(initialData.rows);

      // Starting on the second page, iterate through each page and process the employee data
      for (let page = 2; page <= totalPages; page++) {
        const staffData = await fetchStaffData(page);
        // Process employee data rows for each page
        await StaffService.processStaffRows(staffData.rows);
      }

      // Once the synchronisation is complete, fetch the number of employees in the local database and print the logs
      const syncedStaffCount = await Staff.getCount();
      console.log(
        `Total staff in local database after sync: ${syncedStaffCount}`,
      );
    } catch (error) {
      // If something goes wrong during synchronisation, print the error message and throw an exception.
      console.error('Error syncing staff:', error);
      throw error;
    }
  }
}

export default StaffService;
