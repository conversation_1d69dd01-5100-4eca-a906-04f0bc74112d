// BackgroundSyncService.ts
import BackgroundTimer from 'react-native-background-timer';
import { LogSyncService } from './LogSyncService';

interface SyncTask {
  name: string;
  syncFunction: () => Promise<void>;
  interval: number;
  lastRun?: number;
}

const syncTasks: SyncTask[] = [
  {
    name: 'SyncOpenLogs',
    // 使用箭头函数包装方法调用
    syncFunction: async () => await LogSyncService.syncOpenLogs(),
    interval: 3 * 60 * 1000, // 3 minutes
  },
  {
    name: 'SyncSubmittingLog',
    // 使用箭头函数包装方法调用
    syncFunction: async () => await LogSyncService.syncSubmittingLog(),
    interval: 5 * 60 * 1000, // 5 minutes
  },
];

export class BackgroundSyncService {
  static isInitialized = false;
  static backgroundTimer: number | null = null;
  static lastExecutionTime: number | null = null;

  static initialize() {
    if (this.isInitialized) {
      console.log('🔄 BackgroundSyncService already initialized');
      return;
    }
    this.isInitialized = true;
    console.log('✅ BackgroundSyncService initialized');
  }

  static startBackgroundSync() {
    console.log('🚀 Starting BackgroundSyncService...');
    this.initialize();

    if (this.backgroundTimer !== null) {
      console.log('⚠️ Clearing existing background timer');
      BackgroundTimer.clearInterval(this.backgroundTimer);
    }

    try {
      this.backgroundTimer = BackgroundTimer.setInterval(() => {
        console.log('⏰ Background timer tick - checking sync tasks');
        this.executeSyncTasks();
      }, 30000); // Check every 30 seconds for better precision

      console.log(`✅ Background timer started successfully (ID: ${this.backgroundTimer})`);
      
      // Initial execution
      console.log('🎯 Executing initial sync tasks...');
      this.executeSyncTasks();
    } catch (error) {
      console.error('❌ Error starting background sync:', error);
    }
  }

  static stopBackgroundSync() {
    console.log('🛑 Stopping BackgroundSyncService...');
    if (this.backgroundTimer !== null) {
      BackgroundTimer.clearInterval(this.backgroundTimer);
      console.log(`✅ Background timer stopped (ID: ${this.backgroundTimer})`);
      this.backgroundTimer = null;
    } else {
      console.log('⚠️ No background timer to stop');
    }
  }

  static getStatus() {
    return {
      isInitialized: this.isInitialized,
      hasActiveTimer: this.backgroundTimer !== null,
      timerId: this.backgroundTimer,
      lastExecutionTime: this.lastExecutionTime,
      syncTasks: syncTasks.map(task => ({
        name: task.name,
        interval: task.interval,
        lastRun: task.lastRun,
        nextRun: task.lastRun ? new Date(task.lastRun + task.interval) : 'Never run'
      }))
    };
  }

  private static async executeSyncTasks() {
    const now = Date.now();
    this.lastExecutionTime = now;
    
    console.log('🔍 BackgroundSync: executeSyncTasks started');
    console.log(`📊 Current time: ${new Date(now).toISOString()}`);
    
    let taskExecutedCount = 0;
    
    for (const task of syncTasks) {
      const timeSinceLastRun = task.lastRun ? now - task.lastRun : Infinity;
      // Add 35 second tolerance to account for 30s check interval + 5s buffer
      const toleranceMs = 35000;
      const shouldRun = !task.lastRun || timeSinceLastRun >= (task.interval - toleranceMs);
      
      console.log(`📋 Task ${task.name}:`);
      console.log(`   Last run: ${task.lastRun ? new Date(task.lastRun).toISOString() : 'Never'}`);
      console.log(`   Interval: ${task.interval}ms (${task.interval/1000/60} minutes)`);
      console.log(`   Time since last run: ${timeSinceLastRun}ms`);
      console.log(`   Required time: ${task.interval - toleranceMs}ms (with ${toleranceMs/1000}s tolerance)`);
      console.log(`   Should run: ${shouldRun}`);
      
      if (shouldRun) {
        console.log(`🎯 Executing task: ${task.name}`);
        try {
          await task.syncFunction();
          task.lastRun = now;
          taskExecutedCount++;
          console.log(`✅ Task ${task.name} executed successfully`);
        } catch (error) {
          console.error(`❌ Error executing task ${task.name}:`, error);
        }
      } else {
        console.log(`⏭️ Skipping task ${task.name} (not due yet)`);
      }
    }
    
    console.log(`🏁 BackgroundSync: executeSyncTasks completed (${taskExecutedCount} tasks executed)`);
  }

  // Debug method to manually trigger sync
  static async manualSync() {
    console.log('🔧 Manual sync triggered');
    await this.executeSyncTasks();
  }

  // Method to reset all task timers (force next sync)
  static resetTaskTimers() {
    console.log('🔄 Resetting all task timers');
    syncTasks.forEach(task => {
      task.lastRun = undefined;
    });
  }
}

