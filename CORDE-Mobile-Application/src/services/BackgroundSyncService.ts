// BackgroundSyncService.ts
import BackgroundTimer from 'react-native-background-timer';
import { LogSyncService } from './LogSyncService';

interface SyncTask {
  name: string;
  syncFunction: () => Promise<void>;
  interval: number;
  lastRun?: number;
}

const syncTasks: SyncTask[] = [
  {
    name: 'SyncOpenLogs',
    // 使用箭头函数包装方法调用
    syncFunction: async () => await LogSyncService.syncOpenLogs(),
    interval: 3 * 60 * 1000, // 3 minutes
  },
  {
    name: 'SyncSubmittingLog',
    // 使用箭头函数包装方法调用
    syncFunction: async () => await LogSyncService.syncSubmittingLog(),
    interval: 5 * 60 * 1000, // 5 minutes
  },
];

export class BackgroundSyncService {
  static isInitialized = false;
  static backgroundTimer: number | null = null;

  static initialize() {
    if (this.isInitialized) return;
    this.isInitialized = true;
  }

  static startBackgroundSync() {
    this.initialize();

    if (this.backgroundTimer !== null) {
      BackgroundTimer.clearInterval(this.backgroundTimer);
    }

    this.backgroundTimer = BackgroundTimer.setInterval(() => {
      this.executeSyncTasks();
    }, 60000); // Check every minute

    // Initial execution
    this.executeSyncTasks();
  }

  static stopBackgroundSync() {
    if (this.backgroundTimer !== null) {
      BackgroundTimer.clearInterval(this.backgroundTimer);
      this.backgroundTimer = null;
    }
  }

  private static async executeSyncTasks() {
    const now = Date.now();
    for (const task of syncTasks) {
      if (!task.lastRun || now - task.lastRun >= task.interval) {
        console.log(`Executing task: ${task.name}`);
        try {
          await task.syncFunction();
          task.lastRun = now;
          console.log(`Task ${task.name} executed successfully`);
        } catch (error) {
          console.error(`Error executing task ${task.name}:`, error);
        }
      }
    }
  }
}
