import {fetchAssetsData} from '../api/AssetsApi';
import {fetchAssetDetailData} from '../api/AssetDetailApi';
import {Assets} from '../database/Assets';
import {AssetAttributeValue} from '../database/AssetAttributeValue';

export class AssetsService {
  static async processAssetRows(rows: any[]): Promise<void> {
    for (const row of rows) {
      const assetId = parseInt(row.Col01); // Resolve the asset ID
      const assetDetail = await fetchAssetDetailData(assetId); // Get asset details

      // Construct asset objects and store or update to database
      const asset = new Assets({
        asset_id: assetDetail.AssetID,
        asset_contract_id: parseInt(row.Key.KeyValue),
        contract_code: row.Col02 ?? '',
        suburb: assetDetail.Suburb ?? '',
        asset_location: assetDetail.AssetLocation ?? '',
        asset_code: assetDetail.AssetCode ?? '',
        description: assetDetail.Description ?? '',
        company_id: assetDetail.CompanyID,
        company_name: assetDetail.CompanyName ?? '',
        in_active: assetDetail.Inactive,
        parent_asset_id: assetDetail.ParentAssetID ?? 0,
        street_no: assetDetail.StreetNo ?? '',
        street: assetDetail.Street ?? '',
        city: assetDetail.City ?? '',
        country: assetDetail.Country ?? '',
      });

      await Assets.upsert(asset); // Insert or update asset information

      // Remove old AssetAttributeValues first to ensure data is up-to-date.
      await AssetAttributeValue.deleteByAssetId(assetDetail.AssetID);

      // Add new AssetAttributeValues
      const attributesToInsert = assetDetail.AssetAttributeValues.map(attribute => new AssetAttributeValue({
        attribute_id: 0, // Auto-generated ID
        asset_id: assetDetail.AssetID,
        data_label: attribute.DataLabel,
        data_type: attribute.DataType,
        text_data: attribute.TextData || '',
        delete_flag: attribute.Delete,
      }));

      // 批量插入新属性
      await AssetAttributeValue.bulkInsert(attributesToInsert);
    }
  }

  static async syncAssets(): Promise<void> {
    try {
      console.log('Starting assets sync');
      const startTime = Date.now();

      const initialData = await fetchAssetsData(1);
      const totalPages = initialData.total;
      console.log(`Total ${totalPages} pages to process`);

      // Process initial page
      await AssetsService.processAssetRows(initialData.rows);

      // Process remaining pages with controlled concurrency
      const CONCURRENT_REQUESTS = 3;
      const pages = Array.from({length: totalPages - 1}, (_, i) => i + 2);

      for (let i = 0; i < pages.length; i += CONCURRENT_REQUESTS) {
        const batch = pages.slice(i, i + CONCURRENT_REQUESTS);
        await Promise.all(
          batch.map(async page => {
            const assetsData = await fetchAssetsData(page);
            await AssetsService.processAssetRows(assetsData.rows);
            console.log(`Processed page ${page}/${totalPages}`);
          })
        );
      }

      console.log(`Assets sync completed in ${(Date.now() - startTime)/1000} seconds`);
    } catch (error) {
      console.error('Error syncing assets:', error);
      throw error;
    }
  }
}

export default AssetsService;
