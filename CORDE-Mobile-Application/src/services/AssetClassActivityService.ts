import {fetchAssetClassActivityData} from '../api/AssetClassActivityApi';
import {AssetClassActivity} from '../database/AssetClassActivity';

export class AssetClassActivityService {
  // This method processes each row of asset class activity data retrieved from the API.
  static async processAssetClassActivityRows(rows: any[]): Promise<void> {
    // Use the map function to iterate through each row of data,
    // creating an instance of AssetClassActivity for each row that will be used to insert or update to the database later.
    const assetClassActivities = rows.map(
      row =>
        new AssetClassActivity({
          line_id: parseInt(row.Key.KeyValue), // Get line_id
          class1: row.Col01 ?? '', // Get class1, defaults to empty string if not provided
          class2: row.Col02 ?? '',
          class3: row.Col03 ?? '',
          activity: row.Col04 ?? '',
          contract_code: row.Col05 ?? '',
        }),
    );

    for (const activity of assetClassActivities) {
      // Use the upsert method to save or update data entities.
      // This ensures that the data is up-to-date and consistent, especially in cases where the data may already exist but needs to be updated.
      await AssetClassActivity.upsert(activity);
    }
  }

  static async syncAssetClassActivities(): Promise<void> {
    try {
      // fetch the first page of data to get the total number of pages
      const initialData = await fetchAssetClassActivityData(1);
      const totalPages = initialData.total;
      console.log(`Total pages of AssetClassActivities: ${totalPages}`);

      await AssetClassActivityService.processAssetClassActivityRows(
        initialData.rows,
      );

      // get the rest of the pages
      for (let page = 2; page <= totalPages; page++) {
        const apiData = await fetchAssetClassActivityData(page);
        console.log(
          `Fetched API Data for AssetClassActivities - Page ${page}:`,
          apiData,
        );

        await AssetClassActivityService.processAssetClassActivityRows(
          apiData.rows,
        );
      }
      console.log('All pages of AssetClassActivities synced successfully');
    } catch (error) {
      console.error('Error syncing asset class activities:', error);
      throw error;
    }
  }
}
