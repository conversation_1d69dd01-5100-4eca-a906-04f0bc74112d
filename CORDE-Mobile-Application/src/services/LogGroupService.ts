import { LogListService } from './LogListService';

export class LogGroupService {
  static async completeGroupLogs(
    logIds: number[],
    formValues: any,
    extendColumns: any[]
  ): Promise<void> {
    try {
      console.log(`Starting group completion for ${logIds.length} logs`);
      console.log('extendColumns:', extendColumns);

      await Promise.all(logIds.map(async (logId) => {
        try {
          const logData = {
            local_log_id: logId,
            ...formValues,
            status: 'Completed'
          };

          await LogListService.submitLog(logData, extendColumns);
          console.log(`Log ${logId} submitted successfully`);
        } catch (error) {
          console.error(`<PERSON>rror submitting log ${logId}:`, error);
          throw error; // Re-throw to be caught by the outer try-catch
        }
      }));

      console.log('All logs in the group completed successfully');
    } catch (error) {
      console.error('Error completing group logs:', error);
      throw new Error('Failed to complete one or more logs in the group');
    }
  }
}
