import {fetchExtensionGroupData} from '../api/ExtensionGroupApi';
import {ExtensionGroup} from '../database/ExtensionGroup';
import {ExtensionLineService} from './ExtensionLineService.ts';

// Define the ExtensionGroupService class, which contains static methods for synchronising extension groups.
export class ExtensionGroupService {
  // Define an asynchronous static method with no return value to sync the extension group from the API to the local database
  static async syncExtensionGroups(): Promise<void> {
    try {
      // Get extension group data from the API
      const apiData = await fetchExtensionGroupData();
      console.log('Fetched API Data for ExtensionGroups:', apiData);

      // Process the data returned by the API, converting it into an array of ExtensionGroup instances
      const extensionGroups = apiData.rows.map(
        row =>
          new ExtensionGroup({
            extension_group_id: parseInt(row.Key.KeyValue),
            extension_table_id: parseInt(row.Col01),
            group_control_value: parseInt(row.Col02 ?? '0'),
            description: row.Col03 ?? '',
          }),
      );

      // Iterate through the array of processed extension groups, inserting or updating them one by one into the local database
      for (const group of extensionGroups) {
        await ExtensionGroup.upsert(group);
        // Call ExtensionLineService to sync lines for the current group
        await ExtensionLineService.syncExtensionLines(group.extension_group_id);
      }
    } catch (error) {
      console.error('Error syncing extension groups:', error);
      throw error;
    }
  }
}
