import { login } from '../api/AccountApi';
import { User } from '../database/Users';
import * as Keychain from 'react-native-keychain';
import AsyncStorage from "@react-native-async-storage/async-storage";

const REMEMBER_ME_KEY = 'rememberMe';
const USER_INFO_KEY = 'userInfo';
const USER_CREDENTIALS_KEY = 'userCredentials';

export class AuthService {

  static async login(username: string, password: string, rememberMe: boolean): Promise<User> {
    try {
      // 尝试本地认证
      const localUser = await this.localAuthenticate(username, password);
      if (localUser) {
        // 如果用户选择记住我，清除之前的记住我设置
        if (rememberMe) {
          // 清除其他用户的记住我设置
          await this.clearOtherRememberMe(localUser.person_id);
        }

        // 记录最后登录的用户ID
        await AsyncStorage.setItem('lastLoginPersonId', localUser.person_id.toString());
        await this.updateRememberMeStatus(rememberMe);
        return localUser;
      }

      // API认证
      const apiUser = await this.apiAuthenticate(username, password, rememberMe);

      return apiUser;
    } catch (error) {
      console.error('Login error:', error);
      throw error;
    }
  }

  // 新增方法，清除其他用户的记住我设置
  static async clearOtherRememberMe(currentPersonId: number): Promise<void> {
    try {
      // 清除所有用户的rememberMe标志
      const users = await User.getAll();
      for (const user of users) {
        if (user.person_id !== currentPersonId) {
          await Keychain.resetGenericPassword({
            service: `${REMEMBER_ME_KEY}_${user.username}`
          });
        }
      }
    } catch (error) {
      console.error('Error clearing other remember me settings:', error);
    }
  }

  static async getStoredCredentials(): Promise<{ username: string; password: string } | null> {
    try {
      const credentials = await Keychain.getGenericPassword({ service: USER_CREDENTIALS_KEY });
      if (credentials) {
        const parsedCredentials = JSON.parse(credentials.password);
        return parsedCredentials;
      }
      return null;
    } catch (error) {
      console.error('Error retrieving stored credentials:', error);
      return null;
    }
  }

  static async localAuthenticate(username: string, password: string): Promise<User | null> {
    try {
      const storedCredentials = await this.getStoredCredentials();
      if (storedCredentials && storedCredentials.username === username && storedCredentials.password === password) {
        const user = await User.getByUsername(username);
        if (user) {
          user.is_authenticated = true;
          await User.update(user.user_id!, user);
          return user;
        }
      }
      return null;
    } catch (error) {
      console.error('Error during local authentication:', error);
      throw error;
    }
  }

  static async apiAuthenticate(username: string, password: string, rememberMe: boolean): Promise<User> {
    // console.log('API authentication started for user:', username);
    try {
      const response = await login(username, password);
      // console.log('API login response received');
      const user = new User({
        username,
        full_name: response.FullName,
        person_id: response.PersonID,
        fin_co_code: response.FinCoCode,
        is_authenticated: true,
      });

      await User.upsert(user);
      // console.log('User upserted to local database');

      // 无论是否选择"记住我"，都存储凭证
      await this.storeCredentials(user, password, rememberMe);

      // 只更新"记住我"状态
      await this.updateRememberMeStatus(rememberMe);
      return user;
    } catch (error) {
      console.error('API authentication error:', error);
      throw error;
    }
  }

  static async apiAuthenticate(username: string, password: string, rememberMe: boolean): Promise<User> {
    // console.log('API authentication started for user:', username);
    try {
      const response = await login(username, password);
      // console.log('API login response received');
      const user = new User({
        username,
        full_name: response.FullName,
        person_id: response.PersonID,
        fin_co_code: response.FinCoCode,
        is_authenticated: true,
      });

      // 确保upsert后返回完整的user对象
      const completeUser = await User.upsert(user);
      // console.log('User upserted to local database');

      // 无论是否选择"记住我"，都存储凭证
      await this.storeCredentials(completeUser, password, rememberMe);

      // 只更新"记住我"状态
      await this.updateRememberMeStatus(rememberMe);

      // 保存最后登录的person_id
      await AsyncStorage.setItem('lastLoginPersonId', completeUser.person_id.toString());

      return completeUser;
    } catch (error) {
      console.error('API authentication error:', error);
      throw error;
    }
  }

  static async getStoredUserInfo(): Promise<Partial<User> | null> {
    try {
      const result = await Keychain.getGenericPassword({ service: USER_INFO_KEY });
      if (result) {
        return JSON.parse(result.password);
      }
      return null;
    } catch (error) {
      console.error('Error retrieving stored user info:', error);
      return null;
    }
  }

  static async logout(username: string): Promise<void> {
    console.log('Logging out user:', username);
    const user = await User.getByUsername(username);
    if (user) {
      user.is_authenticated = false;
      await User.update(user.user_id!, user);
    }
    await Keychain.resetGenericPassword({ service: USER_CREDENTIALS_KEY });
    await Keychain.resetGenericPassword({ service: USER_INFO_KEY });
    await Keychain.resetGenericPassword({ service: `${REMEMBER_ME_KEY}_${username}` });

    // 使用新的键名
    await AsyncStorage.removeItem('lastLoginPersonId');

    await this.updateRememberMeStatus(false);
    console.log('Logout completed');
  }

  static async storeCredentials(user: User, password: string, rememberMe: boolean): Promise<void> {
    console.log('Storing credentials for user:', user.username);

    const userInfo = JSON.stringify({
      username: user.username,
      person_id: user.person_id,
      full_name: user.full_name,
      fin_co_code: user.fin_co_code,
    });

    try {
      // 存储用户凭证
      await Keychain.setGenericPassword(
        USER_CREDENTIALS_KEY,
        JSON.stringify({ username: user.username, password }),
        { service: USER_CREDENTIALS_KEY }
      );
      console.log('User credentials stored successfully');

      if (rememberMe) {
        // 为特定用户存储"记住我"标记
        await Keychain.setGenericPassword(
          `${REMEMBER_ME_KEY}_${user.username}`,
          'true',
          { service: `${REMEMBER_ME_KEY}_${user.username}` }
        );
      }

      // 存储用户信息
      await Keychain.setGenericPassword(USER_INFO_KEY, userInfo, { service: USER_INFO_KEY });
      console.log('User info stored successfully');
    } catch (error) {
      console.error('Error storing credentials:', error);
      throw error;
    }
  }

  static async updateRememberMeStatus(rememberMe: boolean): Promise<void> {
    try {
      await Keychain.setGenericPassword(REMEMBER_ME_KEY, rememberMe ? 'true' : 'false', { service: REMEMBER_ME_KEY });
      // console.log('Remember Me status updated:', rememberMe);
    } catch (error) {
      console.error('Error updating Remember Me status:', error);
      throw error;
    }
  }

  static async isRememberMeEnabled(): Promise<boolean> {
    try {
      const result = await Keychain.getGenericPassword({ service: REMEMBER_ME_KEY });
      console.log('Remember Me status:', result);
      return result && result.password === 'true';
    } catch (error) {
      console.error('Error checking Remember Me status:', error);
      return false;
    }
  }

  static async isAuthenticated(): Promise<boolean> {
    const user = await this.getCurrentUser();
    return user ? user.is_authenticated : false;
  }

  static async autoLogin(): Promise<User | null> {
    try {
      const isRememberMe = await this.isRememberMeEnabled();
      console.log('Auto login - Remember Me enabled:', isRememberMe);
      if (isRememberMe) {
        const storedCredentials = await this.getStoredCredentials();
        const userInfo = await this.getStoredUserInfo();
        console.log('Auto login - Stored credentials:', storedCredentials);
        console.log('Auto login - User info:', userInfo);
        if (storedCredentials && userInfo && storedCredentials.username === userInfo.username) {
          const user = await this.login(storedCredentials.username, storedCredentials.password, true);
          if (user) {
            user.person_id = userInfo.person_id;
            user.full_name = userInfo.full_name;
            user.fin_co_code = userInfo.fin_co_code;
            await User.upsert(user);
            console.log('Auto login successful for user:', user.username);
          }
          return user;
        }
      }
    } catch (error) {
      console.error('Auto login error:', error);
    }
    console.log('Auto login failed');
    return null;
  }

  // 在AuthService.ts中
  static async getCurrentUser(): Promise<User | null> {
    try {
      // 检查是否是首次安装
      const isFirstLaunch = await AsyncStorage.getItem('firstLaunch') === null;
      if (isFirstLaunch) {
        // 首次启动，清除所有用户状态
        await User.updateAll({ is_authenticated: false });
        await AsyncStorage.setItem('firstLaunch', 'done');
        return null;
      }

      // 获取最后登录的person_id
      const lastLoginPersonId = await AsyncStorage.getItem('lastLoginPersonId');
      //console.log('Last login person ID:', lastLoginPersonId);
      if (!lastLoginPersonId) return null;

      // 使用person_id获取用户
      const user = await User.getByPersonId(parseInt(lastLoginPersonId));
      //console.log('Found user by person_id:', user);
      return user && user.is_authenticated ? user : null;
    } catch (error) {
      console.error('Error in getCurrentUser:', error);
      return null;
    }
  }

  static async debugAllUsers(): Promise<void> {
    try {
      const users = await User.getAll();
      console.log('所有用户信息:');
      users.forEach(user => {
        console.log(`Username: ${user.username}, user_id: ${user.user_id}, person_id: ${user.person_id}, is_authenticated: ${user.is_authenticated}`);
      });
    } catch (error) {
      console.error('Error getting all users:', error);
    }
  }


}
