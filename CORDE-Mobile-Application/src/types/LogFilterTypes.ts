// src/types/LogFilterTypes.ts

export interface LogFilter {
  logStatus?: string;
  priority?: string;
  logType?: string;
  streetAddress?: string;
  taskType?: string;
  dateFrom?: Date;
  dateTo?: Date;
  job?: string;
  complTargetFrom?: Date;
  complTargetTo?: Date;
  logNo?: string;
  description?: string;
  site?: string;
  assetCode?: string;
  userId?: number;
  includeCompleted?: boolean;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  searchKeyword?: string;
  syncOptions?: {
    useDefaultDateRange: boolean;
  };
}
