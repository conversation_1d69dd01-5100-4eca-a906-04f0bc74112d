import {useState} from 'react';
import {fromZonedTimeToUTC} from '../utils/dateUtils';

export const useDateTimePicker = (initialDate: Date) => {
  const [date, setDate] = useState(initialDate);
  const [showPicker, setShowPicker] = useState(false);

  const onChange = (event: any, selectedDate?: Date) => {
    const currentDate = selectedDate ? fromZonedTimeToUTC(selectedDate) : date;
    setShowPicker(false);
    setDate(currentDate);
  };

  return {
    date,
    showPicker,
    setShowPicker,
    onChange,
  };
};
