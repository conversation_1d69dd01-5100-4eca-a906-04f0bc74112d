import { useState, useEffect, useCallback } from 'react';
import { LogListService } from '../services/LogListService';
import { LogList } from '../database/LogList';
import { useAuth } from '../context/AuthContext';
import { LogFilter } from '../types/LogFilterTypes';

interface UseLogListHook {
  data: LogList[];
  loading: boolean;
  error: Error | null;
  fetchMoreData: () => Promise<LogList[]>;
  hasMoreData: boolean;
  refreshData: () => Promise<void>;
  applyFilters: (filters: LogFilter) => Promise<void>;
  sortBy: string;
  sortOrder: 'asc' | 'desc';
  setSortBy: (sortBy: string) => void;
  setSortOrder: (sortOrder: 'asc' | 'desc') => void;
  applySorting: (newSortBy: string, newSortOrder: 'asc' | 'desc') => Promise<void>;
}

export const useLogList = (): UseLogListHook => {
  const [data, setData] = useState<LogList[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<Error | null>(null);
  const [page, setPage] = useState<number>(1);
  const [hasMoreData, setHasMoreData] = useState<boolean>(true);
  const [filters, setFilters] = useState<LogFilter>({});
  const { user } = useAuth();
  const [sortBy, setSortBy] = useState<string>('log_header_id');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [isInitialLoad, setIsInitialLoad] = useState<boolean>(true);

  const handleFetchData = useCallback(
    async (
      pageNum: number,
      currentFilters: LogFilter = filters,
      currentSortBy: string = sortBy,
      currentSortOrder: 'asc' | 'desc' = sortOrder
    ): Promise<LogList[]> => {
      try {
        console.log(`🚀 handleFetchData called with pageNum: ${pageNum}`);
        console.log(`🚀 Call stack: ${new Error().stack?.split('\n')[2]?.trim()}`);
        setLoading(true);
        const syncOptions = currentFilters.syncOptions || {
          useDefaultDateRange: true
        };

        await LogListService.syncLogs(pageNum, {}, 15, syncOptions);
        const logs = await LogListService.fetchFilteredLogs(pageNum, 15, {
          ...currentFilters,
          userId: user?.person_id,
          includeCompleted: false,
          sortBy: currentSortBy,
          sortOrder: currentSortOrder
        });

        setHasMoreData(logs.length === 15);
        return logs;
      } catch (error: any) {
        console.error('Error fetching logs:', error);
        setError(new Error(error.message || 'An error occurred while fetching logs'));
        return [];
      } finally {
        setLoading(false);
      }
    },
    [user?.person_id]
  );

  const fetchMoreData = useCallback(async (): Promise<LogList[]> => {
    if (!loading && hasMoreData && !isInitialLoad) {
      console.log('📄 fetchMoreData: Loading page', page + 1);
      const newLogs = await handleFetchData(page + 1, filters, sortBy, sortOrder);
      if (newLogs.length > 0) {
        setData(prevData => [...prevData, ...newLogs]);
        setPage(prev => prev + 1);
      }
      return newLogs;
    } else {
      console.log('📄 fetchMoreData: Skipped - loading:', loading, 'hasMoreData:', hasMoreData, 'isInitialLoad:', isInitialLoad);
    }
    return [];
  }, [handleFetchData, loading, page, hasMoreData, isInitialLoad]);

  const applyFilters = useCallback(async (newFilters: LogFilter): Promise<void> => {
    console.log('🔧 applyFilters called');
    console.log(`🔧 Call stack: ${new Error().stack?.split('\n')[2]?.trim()}`);
    setFilters(newFilters);
    setPage(1);
    setHasMoreData(true);
    setIsInitialLoad(true); // 设置为初始加载状态，防止fetchMoreData触发
    const logs = await handleFetchData(1, newFilters, sortBy, sortOrder);
    setData(logs);
    // 延迟解除初始加载状态
    setTimeout(() => {
      console.log('🏁 applyFilters: Setting isInitialLoad to false');
      setIsInitialLoad(false);
    }, 1000);
  }, [handleFetchData]);

  const applySorting = useCallback(async (newSortBy: string, newSortOrder: 'asc' | 'desc'): Promise<void> => {
    setSortBy(newSortBy);
    setSortOrder(newSortOrder);
    setPage(1);
    setHasMoreData(true);
    setIsInitialLoad(true); // 设置为初始加载状态，防止fetchMoreData触发
    const logs = await handleFetchData(1, filters, newSortBy, newSortOrder);
    setData(logs);
    // 延迟解除初始加载状态
    setTimeout(() => {
      console.log('🏁 applySorting: Setting isInitialLoad to false');
      setIsInitialLoad(false);
    }, 1000);
  }, [handleFetchData]);

  const refreshData = useCallback(async () => {
    console.log('🔄 refreshData called');
    console.log(`🔄 Call stack: ${new Error().stack?.split('\n')[2]?.trim()}`);
    const logs = await handleFetchData(1, filters, sortBy, sortOrder);
    setData(logs);
    // 延迟设置初始加载完成，等待FlatList渲染完成
    setTimeout(() => {
      console.log('🏁 Setting isInitialLoad to false');
      setIsInitialLoad(false);
    }, 1000);
  }, [handleFetchData]);

  useEffect(() => {
    console.log('⚡ useEffect triggered - calling refreshData');
    refreshData();
  }, []);

  return {
    data,
    loading,
    error,
    fetchMoreData,
    refreshData,
    hasMoreData,
    applyFilters,
    sortBy,
    sortOrder,
    setSortBy,
    setSortOrder,
    applySorting
  };
};
