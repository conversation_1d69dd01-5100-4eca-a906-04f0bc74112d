import {IButtonProps} from 'native-base';

export const orangeOutlineButtonStyle: IButtonProps & {
  gradientColors: string[];
} = {
  variant: 'outline',
  borderColor: '#FF620A',
  borderWidth: 1,
  bg: 'transparent',
  _text: {
    color: 'white',
  },
  _icon: {
    color: 'white',
  },
  _pressed: {
    borderColor: 'white',
    bg: '#FF620A',
    _text: {
      color: 'white',
    },
    _icon: {
      color: 'white',
    },
    transform: [{scale: 0.9}],
  },
};

export const orangeSubtleButtonStyle = (
  mode: 'light' | 'dark',
): IButtonProps & {
  gradientColors: string[];
} => ({
  variant: 'subtle',
  borderColor: 'white',
  borderWidth: 1,
  bg: '#FF620A',
  _text: {
    color: 'white',
  },
  _icon: {
    color: 'white',
  },
  _pressed: {
    borderColor: '#FF620A',
    bg: 'transparent',
    _text: {
      color: mode === 'dark' ? 'white' : 'black',
    },
    _icon: {
      color: mode === 'dark' ? 'white' : 'black',
    },
    transform: [{scale: 0.9}],
  },
});

export const blackSubtleButtonStyle: IButtonProps & {
  gradientColors: string[];
} = {
  variant: 'subtle',
  borderColor: 'black',
  borderWidth: 1,
  bg: '#282828',
  _text: {
    color: 'white',
  },
  _icon: {
    color: 'white',
  },
  _pressed: {
    borderColor: 'white',
    bg: 'trueGray.600',
    _text: {
      color: 'white',
    },
    _icon: {
      color: 'white',
    },
    transform: [{scale: 0.9}],
  },
};

export const graySubtleButtonStyle = (
  mode: 'light' | 'dark',
): IButtonProps & {
  gradientColors: string[];
} => ({
  variant: 'subtle',
  borderColor: 'white',
  borderWidth: 1,
  bg: 'trueGray.500',
  _text: {
    color: 'white',
  },
  _icon: {
    color: 'white',
  },
  _pressed: {
    borderColor: 'trueGray.500',
    bg: 'transparent',
    _text: {
      color: mode === 'dark' ? 'white' : 'black',
    },
    _icon: {
      color: mode === 'dark' ? 'white' : 'black',
    },
    transform: [{scale: 0.9}],
  },
});

export const gradientButtonStyle: IButtonProps & {gradientColors: string[]} = {
  _text: {
    color: 'white',
  },
  _pressed: {
    borderColor: 'white',
    _text: {
      color: 'white',
    },
    transform: [{scale: 0.9}],
  },
  gradientColors: ['#FF9800', '#F44336'],
  borderRadius: 4,
};

export const blueSubtleButtonStyle = (
  mode: 'light' | 'dark',
): IButtonProps & {
  gradientColors: string[];
} => ({
  variant: 'subtle',
  borderColor: 'white',
  borderWidth: 1,
  bg: 'primary.500',
  _text: {
    color: 'white',
  },
  _icon: {
    color: 'white',
  },
  _pressed: {
    borderColor: 'primary.500',
    bg: 'transparent',
    _text: {
      color: mode === 'dark' ? 'white' : 'black',
    },
    _icon: {
      color: mode === 'dark' ? 'white' : 'black',
    },
    transform: [{scale: 0.9}],
  },
});

export const orangeGhostButtonStyle = (
  mode: 'light' | 'dark',
): IButtonProps => ({
  variant: 'ghost',
  _text: {
    color: mode === 'dark' ? 'white' : 'black',
  },
  _icon: {
    color: mode === 'dark' ? 'white' : 'black',
  },
  _pressed: {
    bg: mode === 'dark' ? 'trueGray.700' : 'orange.50',
  },
});


export const takePhotoButtonStyle = (
  mode: 'light' | 'dark',
): IButtonProps & {
  gradientColors: string[];
} => ({
  variant: 'subtle',
  borderColor: 'white',
  borderWidth: 1,
  bg: '#4CAF50', // 使用绿色表示拍照按钮
  _text: {
    color: 'white',
  },
  _icon: {
    color: 'white',
  },
  _pressed: {
    borderColor: '#4CAF50',
    bg: 'transparent',
    _text: {
      color: mode === 'dark' ? 'white' : 'black',
    },
    _icon: {
      color: mode === 'dark' ? 'white' : 'black',
    },
    transform: [{scale: 0.9}],
  },
});

export const choosePhotoButtonStyle = (
  mode: 'light' | 'dark',
): IButtonProps & {
  gradientColors: string[];
} => ({
  variant: 'subtle',
  borderColor: 'white',
  borderWidth: 1,
  bg: '#2196F3', // 使用蓝色表示选择照片按钮
  _text: {
    color: 'white',
  },
  _icon: {
    color: 'white',
  },
  _pressed: {
    borderColor: '#2196F3',
    bg: 'transparent',
    _text: {
      color: mode === 'dark' ? 'white' : 'black',
    },
    _icon: {
      color: mode === 'dark' ? 'white' : 'black',
    },
    transform: [{scale: 0.9}],
  },
});
