import {IBoxProps, IIconProps, ITextProps} from 'native-base';

export const successSlideStyle: IBoxProps = {
  w: '100%',
  position: 'absolute',
  p: '2',
  borderRadius: 'xs',
  bg: 'emerald.100',
  alignItems: 'center',
  justifyContent: 'center',
  _dark: {
    bg: 'emerald.200',
  },
  safeArea: true,
};

export const successCheckIconStyle: IIconProps = {
  size: '4',
  color: 'emerald.600',
  mt: '1',
  _dark: {
    color: 'emerald.700',
  },
};

export const successTextStyle: ITextProps = {
  color: 'emerald.600',
  textAlign: 'center',
  _dark: {
    color: 'emerald.700',
  },
  fontWeight: 'medium',
};

export const errorSlideStyle: IBoxProps = {
  w: '100%',
  position: 'absolute',
  p: '2',
  borderRadius: 'xs',
  bg: 'rose.100',
  alignItems: 'center',
  justifyContent: 'center',
  _dark: {
    bg: 'rose.200',
  },
  safeArea: true,
};

export const errorIconStyle: IIconProps = {
  size: '4',
  color: 'rose.600',
  mt: '1',
  _dark: {
    color: 'rose.700',
  },
};

export const errorTextStyle: ITextProps = {
  color: 'rose.600',
  textAlign: 'center',
  _dark: {
    color: 'rose.700',
  },
  fontWeight: 'medium',
};
