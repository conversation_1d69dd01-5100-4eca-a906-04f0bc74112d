import {StyleSheet} from 'react-native';

export const containerBackgroundColorStyles = (mode: 'light' | 'dark') =>
  StyleSheet.create({
    containerBackgroundColor: {
      backgroundColor: mode === 'dark' ? '#000000' : '#FFFFFF',
      paddingHorizontal: 10,
    },
  });

export const logListContainerStyles = (mode: 'light' | 'dark') =>
  StyleSheet.create({
    containerBackgroundColor: {
      backgroundColor: mode === 'dark' ? '#000000' : '#FFFFFF',
      flex: 1,
    },
  });

export const screenGeneralStyles = StyleSheet.create({
  container: {
    paddingHorizontal: 10,
    flex: 1,
  },
});

export const syncLogContainerBackgroundColorStyles = (mode: 'light' | 'dark') =>
  StyleSheet.create({
    containerBackgroundColor: {
      backgroundColor: mode === 'dark' ? '#000000' : '#FFFFFF',
    },
  });

export const createGroupContainerBackgroundColorStyles = (
  mode: 'light' | 'dark',
) =>
  StyleSheet.create({
    containerBackgroundColor: {
      backgroundColor: mode === 'dark' ? '#000000' : '#FFFFFF',
      paddingHorizontal: 10,
      flex: 1,
    },
  });
