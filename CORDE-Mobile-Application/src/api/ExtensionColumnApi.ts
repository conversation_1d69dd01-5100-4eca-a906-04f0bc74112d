import api from './BaseApi';

interface ExtensionColumnKey {
  TableName: string;
  KeyName: string;
  KeyValue: string;
  ColumnNames: string;
}

interface ExtensionColumnRow {
  Key: ExtensionColumnKey;
  Col01: string;
  Tag01: string;
  Col02: string;
  Tag02: string;
  Col03: string;
  Tag03: string;
  Col04: string;
  Tag04: string;
  Col05: string;
}

interface ExtensionColumnResponse {
  total: number;
  page: number;
  records: number;
  rows: ExtensionColumnRow[];
}

// Define default options
const defaultOptions = {
  TableName: "ExtensionColumn",
  ColumnNames: "ExtensionTableID,ColumnName,DataType,ComboList,Required",
  PredicateRows: [{
    LeftOperand: "ExtensionTableID",
    Operator: "Eq",
    RightOperand: ["24"],
    Display: true
  }],
  Page: 1,
  Rows: 200
};

export const fetchExtensionColumnData = async (
  options = {}
): Promise<ExtensionColumnResponse> => {
  try {
    const mergedOptions = {
      ...defaultOptions,
      ...options,
    };

    console.log(JSON.stringify(mergedOptions, null, 2));  // Log the merged options to verify the structure

    const response = await api.post('/TableApi', mergedOptions);
    return response.data as ExtensionColumnResponse;
  } catch (error) {
    if (error.response) {
      console.error('Response Error Data:', error.response.data);
      console.error('Response Error Status:', error.response.status);
      console.error('Response Error Headers:', error.response.headers);
    } else if (error.request) {
      console.error('Request Error:', error.request);
    } else {
      console.error('Axios Error:', error.message);
    }
    throw error;
  }
};
