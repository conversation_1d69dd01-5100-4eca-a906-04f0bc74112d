import axios, {AxiosInstance} from 'axios';
import {encode} from 'base-64';
import * as Keychain from 'react-native-keychain';
import {AuthService} from "../services/AuthService.ts";

// Define the base URL for the API
// const BASE_URL = 'https://sicon-mnlweb.sicon.co.nz/WorkbenchLogTest/api';
const BASE_URL = 'https://sicon-mnlweb.sicon.co.nz/WorkbenchTest/api';
// const BASE_URL = 'https://sicon-mnlweb.sicon.co.nz/WorkbenchLogTest/swagger/ui/index';

export const GOOGLE_MAPS_API_KEY = 'AIzaSyA5SvS5evWbn2_ory2v1H_Q4dB9qsGQjWE';
export const GOOGLE_MAPS_GEOCODE_URL = 'https://maps.googleapis.com/maps/api/geocode/json';

interface CustomAxiosInstance extends AxiosInstance {
  handleApiError: (error: any, context: string) => never;
}

// Create an instance of axios
const api: CustomAxiosInstance = axios.create({
  baseURL: BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
  transformRequest: [
    (data, headers) => {
      if (headers['Content-Type'] === 'application/json') {
        return JSON.stringify(data);
      }
      return data;
    },
  ],
}) as CustomAxiosInstance;

// Add a request interceptor that sets the authentication header
api.interceptors.request.use(async (config) => {
  try {
    const credentials = await AuthService.getStoredCredentials();
    if (credentials) {
      const encodedCredentials = encode(`${credentials.username}:${credentials.password}`);
      config.headers.Authorization = `Basic ${encodedCredentials}`;
    } else {
      console.log('No stored credentials found, using default');
      const defaultUsername = 'Lindaj';
      const defaultPassword = 'CORDEwinter24!';
      const defaultEncodedCredentials = encode(`${defaultUsername}:${defaultPassword}`);
      config.headers.Authorization = `Basic ${defaultEncodedCredentials}`;
    }
  } catch (error) {
    console.error('Error setting auth header:', error);
    // 在出错时使用默认凭据
    const defaultUsername = 'Lindaj';
    const defaultPassword = 'CORDEwinter24!';
    const defaultEncodedCredentials = encode(`${defaultUsername}:${defaultPassword}`);
    config.headers.Authorization = `Basic ${defaultEncodedCredentials}`;
  }

  const fullUrl = `${config.baseURL}${config.url}`;
  console.log('Starting Request', {
    method: config.method,
    url: fullUrl,
    headers: config.headers,
    data: config.data,
  });
  return config;
});

const handleApiError = (error: any, context: string): never => {
  console.error(`Error in ${context}:`, error);
  if (error.response) {
    console.error('Response data:', error.response.data);
    console.error('Response status:', error.response.status);
    console.error('Response headers:', error.response.headers);
  } else if (error.request) {
    console.error('No response received:', error.request);
  } else {
    console.error('Error message:', error.message);
  }
  throw error;
};


// Add a response interceptor that prints the response message
api.interceptors.response.use(
  response => {
    return response;
  },
  error => {
    console.error('Response Error:', error);
    return Promise.reject(error);
  },
);

// Add handleApiError to the api object
api.handleApiError = handleApiError;

// Export the configured axios instance
export default api;
