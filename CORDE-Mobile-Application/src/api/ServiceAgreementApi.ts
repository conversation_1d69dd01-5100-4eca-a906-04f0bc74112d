import api from './BaseApi';

interface PredicateRow {
  LogicalOperator: string;
  Predicates: {
    LeftOperand: string;
    Operator: string;
    RightOperand: string[];
  }[];
}

interface ServiceAgreementKey {
  TableName: string;
  KeyName: string;
  KeyValue: string;
  ColumnNames: string;
}

interface ServiceAgreementRow {
  Key: ServiceAgreementKey;
  Col01: string;
  Col02: string;
  Tag02: string;
  Col03: string;
  Tag03: string;
  Col04: string;
  Tag04: string;
  Col05: string;
  Tag05: string;
  Col06: string;
  Tag06: string;
  Col07: string;
  Tag07: string;
  Col08: string;
  Tag08: string;
  Col09: string;
}

interface ServiceAgreementResponse {
  total: number;
  page: number;
  records: number;
  rows: ServiceAgreementRow[];
}

const defaultOptions = {
  TableName: 'ServiceAgreement',
  ColumnNames:
    'ID,Code,Description,Details,ClientSource,CompanyID,DefaultJobCode,DefaultParentJobCode,DefaultAgreement',
  PredicateRows: [
    {
      LogicalOperator: 'Or',
      Predicates: [
        {
          LeftOperand: 'DefaultParentJobCode',
          Operator: 'Eq',
          RightOperand: ['C05.'],
        },
        {
          LeftOperand: 'DefaultParentJobCode',
          Operator: 'Eq',
          RightOperand: ['N04.'],
        },
        {
          LeftOperand: 'DefaultParentJobCode',
          Operator: 'Eq',
          RightOperand: ['N05.'],
        },
      ],
    },
  ],
  Page: 1,
  Rows: 200,
};

export const fetchServiceAgreementData = async (
  options = {},
): Promise<ServiceAgreementResponse> => {
  try {
    const mergedOptions = {
      ...defaultOptions,
      ...options,
    };

    console.log(JSON.stringify(mergedOptions, null, 2)); // 打印合并后的选项以确认数据结构

    const response = await api.post('/TableApi', mergedOptions);
    return response.data as ServiceAgreementResponse;
  } catch (error) {
    if (error.response) {
      console.error('Response Error Data:', error.response.data);
      console.error('Response Error Status:', error.response.status);
      console.error('Response Error Headers:', error.response.headers);
    } else if (error.request) {
      console.error('Request Error:', error.request);
    } else {
      console.error('Axios Error:', error.message);
    }
    throw error;
  }
};
