import api from './BaseApi';

interface PredicateRow {
  LeftOperand: string;
  Operator: string;
  RightOperand: number[];
  Display: boolean;
}

interface Predicate {
  PredicateRows: PredicateRow[];
  IsOr: boolean;
}

interface ExtensionLineKey {
  TableName: string;
  KeyName: string;
  KeyValue: string;
  ColumnNames: string;
}

interface ExtensionLineRow {
  Key: ExtensionLineKey;
  Col01: string;
  Tag01: string;
  Col02: string;
  Tag02: string;
  Col03: string;
  Tag03: string;
}

interface ExtensionLineResponse {
  total: number;
  page: number;
  records: number;
  rows: ExtensionLineRow[];
}

// Define default options
const defaultOptions = {
  TableName: "ExtensionLine",
  ColumnNames: "ExtensionGroupID,ExtensionColumnID,DisplayLabel",
  PredicateRows: [], // Initially null, will be set in the function
  Page: 1,
  Rows: 98
};

export const fetchExtensionLineData = async (
  extensionGroupID: number,
  options = {}
): Promise<ExtensionLineResponse> => {
  try {
    const predicateRow: PredicateRow = {
      LeftOperand: 'ExtensionGroupID',
      Operator: 'Eq',
      RightOperand: [extensionGroupID],
      Display: true
    };

    const mergedOptions = {
      ...defaultOptions,
      ...options,
      PredicateRows: [predicateRow] // Setting query conditions
    };

    // Print the merged options to confirm the data structure
    console.log(JSON.stringify(mergedOptions, null, 2));

    const response = await api.post('/TableApi', mergedOptions);
    return response.data as ExtensionLineResponse;
  } catch (error) {
    if (error.response) {
      console.error('Response Error Data:', error.response.data);
      console.error('Response Error Status:', error.response.status);
      console.error('Response Error Headers:', error.response.headers);
    } else if (error.request) {
      console.error('Request Error:', error.request);
    } else {
      console.error('Axios Error:', error.message);
    }
    throw error;
  }
};
