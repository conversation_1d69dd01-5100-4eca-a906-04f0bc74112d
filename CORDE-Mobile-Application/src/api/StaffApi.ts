import api from './BaseApi';

interface PredicateRow {
  LeftOperand: string;
  Operator: string;
  RightOperand: string[] | number[];
}

interface StaffOptions {
  TableName: string;
  ColumnNames: string;
  PredicateRows: PredicateRow[];
  Page: number;
  Rows: number;
}

const defaultOptions: StaffOptions = {
  TableName: 'People',
  ColumnNames: 'PersonID, FullName, EmailAddress, MobilePhone, ProfitCentreCode, EmployeeNo',
  PredicateRows: [
    {
      LeftOperand: 'ProfitCentreCode',
      Operator: 'Like',
      RightOperand: ['C003', 'N003', 'N002']
    },
    {
      LeftOperand: 'IsEmployee',
      Operator: 'Eq',
      RightOperand: [-1]
    },
    {
      LeftOperand: 'HasLogin',
      Operator: 'Eq',
      RightOperand: [-1]
    }
  ],
  Page: 1,
  Rows: 100
};

interface StaffRow {
  Key: {
    TableName: string;
    KeyName: string;
    KeyValue: string;
    ColumnNames: string;
  };
  Col01: string;
  Col02: string;
  Tag02: string;
  Col03: string;
  Tag03: string;
  Col04: string;
  Tag04: string;
  Col05?: string;
  Tag05?: string;
  Col06: string;
  Tag06: string;
}

interface StaffApiResponse {
  total: number;
  page: number;
  records: number;
  rows: StaffRow[];
}

export const fetchStaffData = async (
  page: number = 1,
  rows: number = 100,
  options: Partial<StaffOptions> = {}
): Promise<StaffApiResponse> => {
  try {
    const mergedOptions: StaffOptions = {
      ...defaultOptions,
      Page: page,
      Rows: rows,
      ...options,
    };

    const response = await api.post('/TableApi', mergedOptions);
    return response.data;
  } catch (error) {
    if (error.response) {
      console.error('Response Error Data:', error.response.data);
      console.error('Response Error Status:', error.response.status);
      console.error('Response Error Headers:', error.response.headers);
    } else if (error.request) {
      console.error('Request Error:', error.request);
    } else {
      console.error('Axios Error:', error.message);
    }
    throw error;
  }
};
