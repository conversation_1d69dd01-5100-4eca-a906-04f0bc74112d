// FileApi.ts
import api from './BaseApi';
import RNFS from "react-native-fs";

interface UploadFileResponse {
  FileName: string;
  FileExtension: string;
  ContentType: string;
  RelatedRecordId: string;
  RelatedRecordType: string;
  StoredFileId: number;
}

// 生成安全的文件名
const generateSafeFileName = (originalName: string): {
  safeFileName: string,
  extension: string
} => {
  // 获取文件扩展名
  const extension = originalName.split('.').pop() || '';

  // 生成时间戳 + 随机数的文件名
  const timestamp = new Date().getTime();
  const random = Math.floor(Math.random() * 10000);
  const safeFileName = `file_${timestamp}_${random}.${extension}`;

  return {
    safeFileName,
    extension
  };
};

export const uploadFile = async (
  fileInfo: {
    uri: string;
    type: string;
    name: string;
  },
  logHeaderId: string
): Promise<UploadFileResponse> => {
  try {
    // 生成安全的文件名
    const { safeFileName, extension } = generateSafeFileName(fileInfo.name);

    const formData = new FormData();

    // 创建文件对象，使用安全文件名
    const fileObject = {
      uri: fileInfo.uri,
      type: fileInfo.type,
      name: safeFileName,     // 使用安全文件名
      originalName: fileInfo.name  // 保留原始文件名作为额外信息
    };

    // Check file size using the correct path
    let fileSize = 0;
    try {
      const filePath = fileInfo.uri.startsWith('file://') ? 
        fileInfo.uri.replace('file://', '') : fileInfo.uri;
      const stat = await RNFS.stat(filePath);
      fileSize = stat.size;
    } catch (error) {
      console.error('Error getting file size:', error);
    }

    console.log('Preparing file upload:', {
      fileObject,
      uri: fileInfo.uri,
      fileSize,
      logHeaderId,
      originalName: fileInfo.name,  // 记录原始文件名
      safeFileName              // 记录安全文件名
    });

    // Check if file size is 0, which indicates a problem
    if (fileSize === 0) {
      throw new Error(`File ${fileInfo.name} has size 0, cannot upload empty file`);
    }

    formData.append('file', fileObject);
    // 添加原始文件名作为额外参数
    formData.append('originalFileName', fileInfo.name);

    console.log('Upload request details:', {
      url: '/UploadFileApi',
      params: {
        fileName: safeFileName,
        originalFileName: fileInfo.name,
        relatedRecordId: logHeaderId,
        relatedRecordType: 'ServiceDesk'
      },
      formDataEntries: Array.from((formData as any)._parts).map(([key, value]) => ({
        key,
        value: value.name || value
      }))
    });

    const response = await api.post<UploadFileResponse>(
      '/UploadFileApi',
      formData,
      {
        params: {
          fileName: safeFileName,
          originalFileName: fileInfo.name, // 传递原始文件名
          relatedRecordId: logHeaderId,
          relatedRecordType: 'ServiceDesk'
        },
        headers: {
          'Content-Type': 'multipart/form-data',
          'Accept': 'application/json',
          'X-Original-Filename': encodeURIComponent(fileInfo.name) // 在header中也传递原始文件名
        },
        transformRequest: (data, headers) => {
          return data;
        },
      }
    );

    console.log('File upload response:', {
      status: response.status,
      data: response.data,
      headers: response.headers,
    });

    return response.data;
  } catch (error) {
    console.error('Error uploading file:', {
      message: error.message,
      response: error.response?.data,
      status: error.response?.status,
    });
    throw error;
  }
};
