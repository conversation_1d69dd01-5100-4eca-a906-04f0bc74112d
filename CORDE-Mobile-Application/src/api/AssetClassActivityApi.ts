import api from './BaseApi';

interface AssetClassActivityKey {
  TableName: string;
  KeyName: string;
  KeyValue: string;
  ColumnNames: string;
}

interface AssetClassActivityRow {
  Key: AssetClassActivityKey;
  Col01: string;
  Tag01: string;
  Col02: string;
  Tag02: string;
  Col03: string;
  Tag03: string;
  Col04: string;
  Tag04: string;
  Col05: string;
}

interface AssetClassActivityResponse {
  total: number;
  page: number;
  records: number;
  rows: AssetClassActivityRow[];
}

// Define default options
const defaultOptions = {
  TableName: "AssetClassActivity",
  ColumnNames: "Class1,Class2,Class3,Activity,ContractCode",
  PredicateRows: [{
    LeftOperand: "ContractCode",
    Operator: "Eq",
    RightOperand: ["C05."],
    Display: true
  }],
  Page: 1,
  Rows: 100
};

export const fetchAssetClassActivityData = async (
  page: number = 1,
  options = {}
): Promise<AssetClassActivityResponse> => {
  try {
    const mergedOptions = {
      ...defaultOptions,
      ...options,
      Page: page,  // Dynamically set the page parameter
    };

    console.log(JSON.stringify(mergedOptions, null, 2));  // Log the merged options to verify the structure

    const response = await api.post('/TableApi', mergedOptions);
    return response.data as AssetClassActivityResponse;
  } catch (error) {
    if (error.response) {
      console.error('Response Error Data:', error.response.data);
      console.error('Response Error Status:', error.response.status);
      console.error('Response Error Headers:', error.response.headers);
    } else if (error.request) {
      console.error('Request Error:', error.request);
    } else {
      console.error('Axios Error:', error.message);
    }
    throw error;
  }
};
