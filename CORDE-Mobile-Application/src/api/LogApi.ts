import api from './BaseApi';

export interface NewLogData {
  serviceAgreementID: number;
  // serviceAgreementCode: string;
  orderNo: string;
  description: string;
  allocatedPersonId?: number;
  assetID?: number;
  relatedRecord?: string;
  relatedRecordID?: string;
}

export interface UpdateLogHeaderData {
  logHeaderId: number;
  comment?: string;
  completionDetails?: string;
  assetId?: string;
  arriveDate?: string;
  arriveTime?: string;
  startLat?: string;
  startLong?: string;
  completedDate?: string;
  completedTime?: string;
  completedLat?: string;
  completedLong?: string;
  restoreDate?: string;
  restoreTime?: string;
  status?: string;
}

export interface UpdateLogExtensionData {
  logHeaderId: string;
  assetClassActivity?: string;
  quoteApp?: string;
  tskElyCloseSDC?: string;
}

interface NewLogResponse {
  ServiceAgreementID: number;
  ServiceAgreementCode: string;
  OrderNo: string;
  LogHeaderID: number;
  AssetID: number;
}

interface TableRowApiResponse {
  Key: {
    TableName: string;
    KeyName: string;
    KeyValue: string;
    ColumnNames: string;
  };
  [key: string]: string | {[key: string]: string};
}

export interface CreateLogExtensionData {
  logHeaderId: number;
}

export const createNewLog = async (
  newLogData: NewLogData,
): Promise<NewLogResponse> => {
  try {
    console.log('Enter createNewLog', newLogData);
    //     //  {"allocatedPersonId": 50, "assetID": 1, "description": "some desc", "orderNo": "20240817010052", "relatedRecord": "LogHeaderFile_3417", "relatedRecordID": "20240817010052", "serviceAgreementCode": "1", "serviceAgreementID": 1}
    const response = await api.put<NewLogResponse>(
      '/LogServiceAgreementApi',
      null,
      {
        params: newLogData,
      },
    );
    console.log('CcreateNewLog response', response);
    return response.data;
  } catch (error) {
    api.handleApiError(error, 'createNewLog');
  }
};

// Functions to update log headers
/**
 * Updating LogHeader             {"Col01": null, "Col02": null, "Col03": "5", "Col04": null, "Col05": null, "Col06": undefined, "Col07": undefined, "Col08": null, "Col09": null, "Col10": undefined, "Col11": undefined, "Col12": "2024-08-26", "Col13": "", "Col14": "submitting", "Key": {"ColumnNames": "Comment,CompletionDetails,AssetID,ArriveDate,ArriveTime,StartLat,StartLong,CompletedDate,CompletedTime,CompletedLat,CompletedLong,RestoreDate,RestoreTime,Status", "KeyName": "LogHeaderID", "KeyValue": 675613, "TableName": "LogHeader"}}
 *  LOG  UpdateLogHeader response {"Col03": "5", "Col06": "0.000000", "Col07": "0.000000", "Col10": "0.000000", "Col11": "0.000000", "Col12": "26-Aug-2024", "Col14": "submitting", "Key": {"ColumnNames": "Comment,CompletionDetails,AssetID,ArriveDate,ArriveTime,StartLat,StartLong,CompletedDate,CompletedTime,CompletedLat,CompletedLong,RestoreDate,RestoreTime,Status", "KeyName": "LogHeaderID", "KeyValue": "675613", "TableName": "LogHeader"}, "Tag12": "26-Aug-2024", "Tag14": "submitting"}
 * @param data
 */

export const updateLogHeader = async (
  data: UpdateLogHeaderData,
): Promise<TableRowApiResponse> => {
  try {
    const payload = {
      Key: {
        TableName: 'LogHeader',
        KeyName: 'LogHeaderID',
        KeyValue: data.logHeaderId,
        ColumnNames:
          'Comment,CompletionDetails,AssetID,ArriveDate,ArriveTime,StartLat,StartLong,CompletedDate,CompletedTime,CompletedLat,CompletedLong,RestoreDate,RestoreTime,Status',
      },
      Col01: data.comment,
      Col02: data.completionDetails,
      Col03: data.assetId,
      Col04: data.arriveDate,
      Col05: data.arriveTime,
      Col06: data.startLat,
      Col07: data.startLong,
      Col08: data.completedDate,
      Col09: data.completedTime,
      Col10: data.completedLat,
      Col11: data.completedLong,
      Col12: data.restoreDate,
      Col13: data.restoreTime,
      Col14: data.status,
    };

    console.log('Updating LogHeader', payload);
    const response = await api.post<TableRowApiResponse>(
      '/TableRowApi',
      payload,
    );
    console.log('UpdateLogHeader response', response.data);
    return response.data;
  } catch (error) {
    api.handleApiError(error, 'updateLogExtension');
  }
};

// Functions to update log extension information
// new Log 后根据 logHeaderId查询，发现extensions字段都是null，这样是不是导致了后续更新extensions时出现的问题
// 此处的bbug将会被修复，在new log的时候直接添加一个新的log extension, 因为当update不成功的并没有报错
/*
 * Updating LogExtension {"Col1": "NP Outside Contract", "Key": {"ColumnNames": "TskElyCloseSDC", "KeyName": "LogHeaderID", "KeyValue": "675613", "TableName": "LogExtension"}}
 * "ExceptionMessage": "Specified argument was out of the range of valid values.
 * Parameter name: Object is not found in Table: LogExtension where LogHeaderID = 675613"
 */
export const updateLogExtension = async (
  data: any,
): Promise<TableRowApiResponse> => {
  try {
    console.log('Updating LogExtension', data);
    const response = await api.post<TableRowApiResponse>('/TableRowApi', data);
    console.log('UpdateLogExtension response', response.data);
    return response.data;
  } catch (error) {
    console.error('Error updating LogExtension:', error);
    if (error.response && error.response.status === 500) {
      await createLogExtension(data);
    }
    if (error.response) {
      console.error('Error response status:', error.response.status);
      console.error('Error response data:', error.response.data);
      console.error('Error response headers:', error.response.headers);
    } else if (error.request) {
      console.error('No response received:', error.request);
    } else {
      console.error('Error message:', error.message);
    }
    console.error('Error config:', error.config);
    throw error;
  }
};


export const createLogExtension = async (
  data: CreateLogExtensionData,
): Promise<TableRowApiResponse> => {
  try {
    console.log('Creating LogExtension:', data);
    const payload = {
      Key: {
        TableName: 'LogExtension',
        KeyName: 'LogHeaderID',
        ColumnNames: 'LogHeaderID',
      },
      Col01: data.logHeaderId.toString(),
    };
    const response = await api.put<TableRowApiResponse>('/TableRowApi', payload);
    console.log('LogExtension created successfully:', response.data);
    return response.data;
  } catch (error) {
    api.handleApiError(error, 'createLogExtension');
  }
};
