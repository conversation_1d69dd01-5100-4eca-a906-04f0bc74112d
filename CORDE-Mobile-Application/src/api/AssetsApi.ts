import api from './BaseApi';

interface PredicateRow {
  LeftOperand: string;
  Operator: string;
  RightOperand: string[];
  Display?: boolean;
}

interface AssetsOptions {
  TableName: string;
  ColumnNames: string;
  PredicateRows: PredicateRow[];
  Page: number;
  Rows: number;
}

// Default request options
const defaultOptions: AssetsOptions = {
  TableName: 'AssetContracts',
  ColumnNames: 'AssetID,ContractCode',
  PredicateRows: [
    {
      LeftOperand: 'ContractCode',
      Operator: 'Eq',
      RightOperand: ['C05.', 'N05.', 'N04.'],
    },
  ],
  Page: 1,
  Rows: 100,
};

interface AssetRow {
  Key: {
    TableName: string;
    KeyName: string;
    KeyValue: string;
    ColumnNames: string;
  };
  Col01: string;
  Tag01: string;
  Col02: string;
  Tag02: string;
}

// Define the structure of the API response
interface AssetsApiResponse {
  total: number;
  page: number;
  records: number;
  rows: AssetRow[];
}

// Asynchronous function to get asset data
export const fetchAssetsData = async (
  page: number = 1,
  rows: number = 100,
  options: Partial<AssetsOptions> = {},
): Promise<AssetsApiResponse> => {
  try {
    // Merge default and incoming options
    const mergedOptions: AssetsOptions = {
      ...defaultOptions,
      Page: page,
      Rows: rows,
      ...options,
    };

    // Send a POST request to the server's /TableApi endpoint
    const response = await api.post('/TableApi', mergedOptions);
    // Return response data
    return response.data;
  } catch (error) {
    // Handling and logging errors
    if (error.response) {
      console.error('Response Error Data:', error.response.data);
      console.error('Response Error Status:', error.response.status);
      console.error('Response Error Headers:', error.response.headers);
    } else if (error.request) {
      console.error('Request Error:', error.request);
    } else {
      console.error('Axios Error:', error.message);
    }
    throw error;
  }
};
