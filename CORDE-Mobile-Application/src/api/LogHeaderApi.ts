import api from './BaseApi';

interface LogByPerson {
  PersonID: number;
  FullName: string;
}

interface AllocationPerson {
  PersonID: number;
  FullName: string;
}

interface AssetAttributeValue {
  DataLabel: string;
  DataType: number;
  DateData?: string;
  NumericData?: number;
  TextData?: string;
  Delete: boolean;
}

interface Asset {
  AssetID: number;
  AssetCode: string;
  ExternalCode: string;
  Description: string;
  Inactive: boolean;
  CompanyID: number;
  ParentAssetID: number;
  AssetLocation: string;
  StreetNo?: string;
  Street: string;
  Suburb: string;
  City?: string;
  Country?: string;
  AssetAttributeValues: AssetAttributeValue[];
}

interface ServiceAgreement {
  ID: number;
  Code: string;
  Description: string;
  Details: string;
  ClientSource: string;
  CompanyID: number;
  DefaultJobCode?: string;
  DefaultParentJobCode?: string;
  DefaultAgreement: number;
  Inactive: boolean;
}

interface LogDetail {
  LogDetailID: number;
  LogHeaderID: number;
  DetailDate: string;
  DetailTime: string;
  DetailType: string;
  PersonID: number;
  Comments: string;
  TSCreated: number;
  TimesheetDate: string;
  ActivityCode: string;
}

interface Extension {
  Name: string;
  Type: string;
  ValueInt: number;
}

/**
 "AssetTask": {
 "AssetTaskID": 8391,
 "AssetTaskDescription": "Grade 2 Bin Empties Summer WEDNESDAY",
 "AssetTaskDetails": "Liffey Domain North",
 "ContractCode": "C05.",
 "ScheduleCode": "C05.ParksScheduled",
 "AssetContractID": 2615,
 "TaskID": 56,
 "WorkGroupID": 4,
 "RunID": 69,
 "RunCode": "P&R LJ SDC",
 "FrequencyCode": "1Week",
 "Recurring": 3,
 "OutputCode": "C05.43",
 "OutputQuantity": 1,
 "SequenceNumber": 207,
 "RunDay": "Wed",
 "Inactive": 0,
 "ScheduledStartDate": "2024-10-30T00:00:00",
 "UseExemptionDates": 0,
 "AssetCode": "518841",
 "AssetID": 1432,
 "ServiceAgreementID": 25,
 "ExpectedAnnualQuantity": 178,
 "ServiceType": "C1419",
 "Task": {
 "TaskID": 56,
 "TaskCode": "G2SBINWED",
 "Description": "Grade 2 Bin Empties Summer WEDNESDAY",
 "Inactive": 0,
 "PercentCompleteOnSummary": 0,
 "BudgetHours": 0,
 "ActualHours": 0,
 "EstimateHours": 0,
 "Completed": 0,
 "DefRecurring": 0,
 "DefServiceAgreementID": 25
 }
 },
 */


interface Task{
  TaskID: number;
  TaskCode: string;
  Description: string;
  Inactive: number;
  PercentCompleteOnSummary: number;
  BudgetHours: number;
  ActualHours: number;
  EstimateHours: number;
  Completed: number;
  DefRecurring: number;
  DefServiceAgreementID: number;
}
interface AssetTask {
  AssetTaskID: number;
  AssetTaskDescription: string;
  AssetTaskDetails: string;
  Task: Task;
}

interface LogHeader {
  LogHeaderID: number;
  LogDate: string;
  LogTime: string;
  LogBy: number;
  LogByPerson: LogByPerson;
  Status: string;
  Priority: string;
  LogType: string;
  ProfitCentreCode: string;
  CompanyID: number;
  InvContact: number;
  Allocation: number;
  AllocationPerson: AllocationPerson;
  LocationCode: string;
  JobCode: string;
  SchdDate: string;
  SchdTime: string;
  Description: string;
  EscalationTime: string;
  SiteAddressID: number;
  Comment: string;
  EventTimeStamp: string;
  DispatcherID: number;
  ServiceAgreementID: number;
  InvoiceContactName: string;
  PercentComplete: number;
  ActualTimeUnitsQuantity: number;
  SchdDateOriginal: string;
  SchdTimeOriginal: string;
  SentFlag: number;
  SchdDateMaximumHours: number;
  RestoreDateTargetHours: number;
  CompleteDateTargetHours: number;
  SchdDateUnits: string;
  SchdDateMaximumUnits: string;
  RestoreDateTargetUnits: string;
  CompleteDateTargetUnits: string;
  SchdDateMaximum: string;
  SchdTimeMaximum: string;
  RestoreDateTarget: string;
  RestoreTimeTarget: string;
  CompleteDateTarget: string;
  CompleteTimeTarget: string;
  OrderNo: string;
  CompletedDate: string;
  CompletedTime: string;
  ArriveDate: string;
  ArriveTime: string;
  CompletionDetails: string;
  ContactDetails: string;
  AssetID: number;
  ServiceAgreement: ServiceAgreement;
  Asset: Asset;
  StoredFiles: any[];
  LogDetails: LogDetail[];
  AnalysisTransactions: any[];
  Extensions: Extension[];
  AssetTask: AssetTask;
}

export const fetchLogHeaderData = async (
  logHeaderId: number,
): Promise<LogHeader> => {
  try {
    const response = await api.get(`/LogHeaderApi?logHeaderId=${logHeaderId}`);
    //console.log('Response Data Header:', response.data); // Log the response data
    return response.data;
  } catch (error) {
    if (error.response) {
      // The request was made and the server responded with a status code
      // that falls out of the range of 2xx
      console.error('Response Error Data:', error.response.data);
      console.error('Response Error Status:', error.response.status);
      console.error('Response Error Headers:', error.response.headers);
    } else if (error.request) {
      // The request was made but no response was received
      console.error('Request Error:', error.request);
    } else {
      // Something happened in setting up the request that triggered an Error
      console.error('Axios Error:', error.message);
    }
    throw error;
  }
};
