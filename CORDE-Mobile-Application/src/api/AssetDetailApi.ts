import api from './BaseApi';

interface AssetAttributeValue {
  DataLabel: string;
  DataType: number;
  DateData?: string;
  NumericData?: number;
  TextData?: string;
  Delete: boolean;
}

interface AssetContract {
  AssetContractID: number;
  ContractCode: string;
  Delete: boolean;
}

// AssetDetail is a dictionary,
// and both AssetAttributeValues and AssetContracts are an array contained within the AssetDetail dictionary,
// each containing one or more dependent dictionaries inside.
interface AssetDetail {
  AssetID: number;
  AssetCode: string;
  ExternalCode: string;
  Description: string;
  JobCode: string;
  ScheduleCode: string;
  Quantity: number;
  Unit: string;
  Details: string;
  Inactive: number;
  CompanyID: number;
  CompanyName: string;
  ParentAssetID: number;
  AssetLocation: string;
  StreetNo: string;
  Street: string;
  Suburb: string;
  City: string;
  Country: string;
  AssetAttributeValues: AssetAttributeValue[];
  AssetContracts: AssetContract[];
}

// Asynchronous function to get the details of the specified asset
export const fetchAssetDetailData = async (assetId: number): Promise<AssetDetail> => {
  try {
    // Send a GET request to the server's /AssetDetailApi endpoint, using the asset ID as a query parameter
    const response = await api.get(`/AssetDetailApi?key=${assetId}`);
    // Return the data in the response
    return response.data;
  } catch (error) {
    // Handling and logging errors
    if (error.response) {
      console.error('Response Error Data:', error.response.data);
      console.error('Response Error Status:', error.response.status);
      console.error('Response Error Headers:', error.response.headers);
    } else if (error.request) {
      console.error('Request Error:', error.request);
    } else {
      console.error('Axios Error:', error.message);
    }
    throw error;
  }
};
