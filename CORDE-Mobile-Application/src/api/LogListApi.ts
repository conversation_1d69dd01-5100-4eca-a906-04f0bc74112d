import api from './BaseApi';

interface PredicateRow {
  LeftOperand: string;
  Operator: string;
  RightOperand: string[];
  Display: boolean;
}

interface Predicate {
  PredicateRows: PredicateRow[];
  IsOr: boolean;
}

interface LogListOptions {
  predicate?: Predicate;
  sidx?: string;
  sord?: string;
  page?: number;
  rows?: number;
  functionalCode?: string;
  IntValue?: number;
  BoolValue?: boolean;
}

const defaultOptions: LogListOptions = {
  predicate: {
    PredicateRows: [
      {
        LeftOperand: 'LogDate',
        Operator: 'between',
        RightOperand: ['2016-01-01', new Date(Date.now() + 86400000).toISOString().split('T')[0]],
        Display: true,
      }
      /*,{
        LeftOperand: "Status",
        Operator: "Ne",
        RightOperand: ["Completed"],
        Display: true
      }*/
    ],
    IsOr: false,
  },
  sidx: 'LogDate',
  sord: 'desc',
  functionalCode: 'General',
  IntValue: 0,
  BoolValue: false,
};

export const fetchLogListData = async (
  page: number = 1,
  rowsParam: number = 100,
  startDate?: string,
  endDate?: string,
  options: Partial<LogListOptions> = {},
) => {
  try {
    const mergedOptions: LogListOptions = {
      ...defaultOptions,
      page,
      rows: rowsParam,
      ...options,
    };

    // 处理日期范围
    const now = new Date(Date.now() + 86400000).toISOString().split('T')[0];
    if (startDate || endDate) {
      if ("PredicateRows" in mergedOptions.predicate) {
        // Process startDate - set to start of day if provided
        let processedStartDate = startDate || '2016-01-01';
        if (startDate) {
          const startDateObj = new Date(startDate);
          startDateObj.setHours(0, 0, 0, 0);
          processedStartDate = startDateObj.toISOString().split('T')[0] + ' 00:00:00';
        }

        // Process endDate - set to end of day if provided to include all data from the selected date
        let processedEndDate = endDate || now;
        if (endDate) {
          const endDateObj = new Date(endDate);
          endDateObj.setHours(23, 59, 59, 999);
          processedEndDate = endDateObj.toISOString().replace('T', ' ').replace('Z', '');
        }

        mergedOptions.predicate.PredicateRows[0] = {
          LeftOperand: 'LogDate',
          Operator: 'between',
          RightOperand: [processedStartDate, processedEndDate],
          Display: true,
        };
      }
    }

    // Ensure rows is a number
    mergedOptions.rows = Number(mergedOptions.rows);

    console.log('Request Options:', JSON.stringify(mergedOptions, null, 2));
    const response = await api.post('/LogListApi', mergedOptions);
    return response.data;
  } catch (error) {
    if (error.response) {
      console.error('Response Error Data:', error.response.data);
      console.error('Response Error Status:', error.response.status);
      console.error('Response Error Headers:', error.response.headers);
    } else if (error.request) {
      console.error('Request Error:', error.request);
    } else {
      console.error('Axios Error:', error.message);
    }
    throw error;
  }
};
