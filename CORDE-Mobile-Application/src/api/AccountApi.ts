// src/api/AccountApi.ts

import api from './BaseApi';
import * as Keychain from 'react-native-keychain';

interface LoginResponse {
  PersonID: number;
  FullName: string;
  FinCoCode: string;
}

export const login = async (username: string, password: string): Promise<LoginResponse> => {
  try {
    const response = await api.get<LoginResponse>('/AccountApi', {
      auth: {
        username,
        password,
      },
    });

    console.log('Login response:', response.data);
    // 如果登录成功，将凭证存储到 Keychain
    await Keychain.setGenericPassword(username, password);

    return response.data;
  } catch (error) {
    console.error('Login error:', error);
    throw error;
  }
};

export const fetchAccountData = async () => {
  try {
    const response = await api.get('/AccountApi');
    return response.data;
  } catch (error) {
    console.error('There was a problem with the axios operation:', error);
    throw error;
  }
};
