// src/api/GoogleMapsApi.ts

import { GOOGLE_MAPS_API_KEY, GOOGLE_MAPS_GEOCODE_URL } from './BaseApi';

export const getGeocodingData = async (address: string) => {
  const encodedAddress = encodeURIComponent(address);
  let url = `${GOOGLE_MAPS_GEOCODE_URL}?address=${encodedAddress}&key=${GOOGLE_MAPS_API_KEY}`;
  console.log('Fetching geocoding data from:', url);
  const response = await fetch(
    url
  );
  return response.json();
};
