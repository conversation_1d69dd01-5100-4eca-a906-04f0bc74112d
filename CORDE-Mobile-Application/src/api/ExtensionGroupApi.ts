import api from './BaseApi';

interface ExtensionGroupKey {
  TableName: string;
  KeyName: string;
  KeyValue: string;
  ColumnNames: string;
}

interface ExtensionGroupRow {
  Key: ExtensionGroupKey;
  Col01: string;
  Tag01: string;
  Col02?: string;
  Tag02?: string;
  Col03?: string;
  Tag03?: string;
}

interface ExtensionGroupResponse {
  total: number;
  page: number;
  records: number;
  rows: ExtensionGroupRow[];
}

// Define default options
const defaultOptions = {
  TableName: "ExtensionGroup",
  ColumnNames: "ExtensionTableID,GroupControlValue,Description",
  PredicateRows: [{
    LeftOperand: "ExtensionTableID",
    Operator: "Eq",
    RightOperand: ["24"],
    Display: true
  }],
  Page: 1,
  Rows: 99
};

export const fetchExtensionGroupData = async (
  options = {}
): Promise<ExtensionGroupResponse> => {
  try {
    const mergedOptions = {
      ...defaultOptions,
      ...options,
    };

    // Print the merged options to confirm the data structure
    console.log(JSON.stringify(mergedOptions, null, 2));

    const response = await api.post('/TableApi', mergedOptions);
    return response.data as ExtensionGroupResponse;
  } catch (error) {
    if (error.response) {
      console.error('Response Error Data:', error.response.data);
      console.error('Response Error Status:', error.response.status);
      console.error('Response Error Headers:', error.response.headers);
    } else if (error.request) {
      console.error('Request Error:', error.request);
    } else {
      console.error('Axios Error:', error.message);
    }
    throw error;
  }
};
