import React, { useEffect, useState } from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { NativeBaseProvider, Box } from 'native-base';
import {closeDatabase, initDB} from './database/DatabaseInit';
import AppNavigator from './navigation/AppNavigator';
import { ThemeProvider } from './styles/ThemeContext';
import { Text, View } from 'react-native';
import { BackgroundSyncService } from "./services/BackgroundSyncService.ts";
import { AuthProvider } from "./context/AuthContext.tsx";
import './utils/devWarningFilter';
import SyncService from "./services/SyncService.ts";
import {locationService} from "./services/LocationService.ts";
import {SyncProvider} from "./context/SyncContext.tsx";
import {AuthService} from "./services/AuthService.ts";
import GlobalEnvironmentIndicator from './components/common/EnvironmentIndicator';

const LoadingScreen = () => (
  <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
    <Text>Initializing...</Text>
  </View>
);

export default function App() {
  const [isDatabaseReady, setIsDatabaseReady] = useState(false);

  useEffect(() => {
    const initializeApp = async () => {
      try {
        await initDB();
        await locationService.initialize();
        setIsDatabaseReady(true);
        //await SyncService.startAllSyncTasks();//
        BackgroundSyncService.startBackgroundSync();
        // AuthService.debugAllUsers();
      } catch (error) {
        console.error('Error initializing app:', error);
      }
    };
    initializeApp();

    return () => {
      closeDatabase()
        .then(() => console.log('Database cleanup complete'))
        .catch(err => console.error('Error during database cleanup:', err));

      BackgroundSyncService.stopBackgroundSync();
      locationService.cleanup();
    };
  }, []);

  if (!isDatabaseReady) {
    return <LoadingScreen />;
  }

  return (
    <ThemeProvider>
      <AuthProvider>
        <SyncProvider>
          <NavigationContainer>
            <NativeBaseProvider>
              <Box flex={1} position="relative">
                <AppNavigator />
                <GlobalEnvironmentIndicator />
              </Box>
            </NativeBaseProvider>
          </NavigationContainer>
        </SyncProvider>
      </AuthProvider>
    </ThemeProvider>
  );
}
