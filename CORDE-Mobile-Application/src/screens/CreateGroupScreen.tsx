import * as React from 'react';
import {ScrollView, View} from 'native-base';
import {useTheme} from '../styles/ThemeContext';
import {createGroupContainerBackgroundColorStyles} from '../styles/screenGeneralStyles';
import CreateGroup from '../components/CreateGroupScreen/CreateGroup.tsx';

const CreateGroupScreen = () => {
  const {mode} = useTheme();

  const containerStyle = createGroupContainerBackgroundColorStyles(mode);

  return (
    <View style={containerStyle.containerBackgroundColor}>
      <CreateGroup />
    </View>
  );
};

export default CreateGroupScreen;
