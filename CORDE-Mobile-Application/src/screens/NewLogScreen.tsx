import * as React from 'react';
import {ScrollView, VStack} from 'native-base';
import NewLog from '../components/NewLogScreen/NewLog.tsx';
import {useTheme} from '../styles/ThemeContext';
import {containerBackgroundColorStyles} from '../styles/screenGeneralStyles';

const NewLogScreen = () => {
  const {mode} = useTheme(); // Get current theme mode

  const containerStyle = containerBackgroundColorStyles(mode); // Get container styles based on theme patterns

  return (
    <ScrollView contentContainerStyle={containerStyle.containerBackgroundColor}>
      <VStack space={2} p={2}>
        <NewLog />
      </VStack>
    </ScrollView>
  );
};

export default NewLogScreen;
