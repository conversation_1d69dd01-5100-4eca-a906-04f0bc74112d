import * as React from 'react';
import {ScrollView} from 'native-base';
import {useTheme} from '../styles/ThemeContext';
import {logListContainerStyles} from '../styles/screenGeneralStyles';
import Settings from '../components/SettingsScreen/Settings.tsx';

const SettingsScreen = () => {
  const {mode} = useTheme();

  const containerStyle = logListContainerStyles(mode);

  return (
    <ScrollView contentContainerStyle={containerStyle.containerBackgroundColor}>
      <Settings />
    </ScrollView>
  );
};

export default SettingsScreen;
