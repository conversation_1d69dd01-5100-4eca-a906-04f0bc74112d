import * as React from 'react';
import {ScrollView, VStack, Box} from 'native-base';
import {useTheme} from '../styles/ThemeContext';
import {createGroupContainerBackgroundColorStyles} from '../styles/screenGeneralStyles';
import CompleteGroup from '../components/CompleteGroupScreen/CompleteGroup';

const CompleteGroupScreen = () => {
  const {mode} = useTheme();

  const containerStyle = createGroupContainerBackgroundColorStyles(mode);

  return (
    // <Box flex={1} safeArea>
    <ScrollView
      contentContainerStyle={containerStyle.containerBackgroundColor}
      flex={1}>
      <VStack flex={1} justifyContent="space-between">
        <CompleteGroup />
      </VStack>
    </ScrollView>
    // </Box>
  );
};

export default CompleteGroupScreen;
