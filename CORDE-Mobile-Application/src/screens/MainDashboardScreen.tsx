import * as React from 'react';
import {Image, VStack, Box} from 'native-base';
import {useTheme} from '../styles/ThemeContext';
import {useWindowDimensions} from 'react-native';
import Overview from '../components/MainDashboardScreen/Overview.tsx';
import {screenGeneralStyles} from '../styles/screenGeneralStyles.ts';

const MainDashboardScreen = () => {
  const {mode} = useTheme();
  const {width, height} = useWindowDimensions();
  const imagePath =
    mode === 'dark'
      ? require('../assets/Logotype/CORDE_Logotype_White.png')
      : require('../assets/Logotype/CORDE_Logotype_Black.png');

  const containerPadding = width > 600 ? '10' : '6';
  const logoHeight = height * 0.15; // 15% of screen height

  return (
    <Box
      bg={mode === 'dark' ? '#000000' : '#ffffff'}
      style={[
        screenGeneralStyles.container,
        {paddingHorizontal: containerPadding},
      ]}
      height="100%"
      safeArea>
      <VStack space={2} alignItems="center" height="100%">
        <Box
          alignItems="center"
          width="100%"
          height={`${logoHeight}px`}
          justifyContent="center">
          <Image
            key={mode}
            source={imagePath}
            alt="CORDE Logotype"
            resizeMode="contain"
            height="100%"
            width="90%"
          />
        </Box>
        <Box flex={1} width="100%" px={4}>
          <Overview />
        </Box>
      </VStack>
    </Box>
  );
};

export default MainDashboardScreen;
