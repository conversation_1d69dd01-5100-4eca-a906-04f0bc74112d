import React, {useEffect, useState} from 'react';
import {View, Text, FlatList, StyleSheet} from 'react-native';
import {Assets} from '../database/Assets.ts';

const AssetsScreen = () => {
  const [assets, setAssets] = useState<Assets[]>([]);

  useEffect(() => {
    const fetchAssets = async () => {
      const allAssets = await Assets.getAll();
      setAssets(allAssets);
    };

    fetchAssets();
  }, []);

  const renderItem = ({item}: {item: Assets}) => (
    <View style={styles.item}>
      <Text>
        {item.street_no} {item.street}, {item.suburb}, {item.city},{' '}
        {item.country}
      </Text>
    </View>
  );

  return (
    <View style={styles.container}>
      <FlatList
        data={assets}
        renderItem={renderItem}
        keyExtractor={item => item.asset_id.toString()}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  item: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#ccc',
  },
});

export default AssetsScreen;
