import React from 'react';
import {View} from 'native-base';
import LogListView from '../components/LogListScreen/LogListView.tsx';
import {useTheme} from '../styles/ThemeContext';
import {logListContainerStyles} from '../styles/screenGeneralStyles';

const LogListScreen = () => {
  const {mode} = useTheme();

  const containerStyle = logListContainerStyles(mode);

  return (
    <View style={containerStyle.containerBackgroundColor}>
      <LogListView />
    </View>
  );
};

export default LogListScreen;
