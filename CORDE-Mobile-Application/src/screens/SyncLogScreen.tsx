import * as React from 'react';
import { SafeAreaView } from 'react-native';
import { useTheme } from '../styles/ThemeContext';
import { logListContainerStyles } from '../styles/screenGeneralStyles';
import SyncLogTabNavigator from '../navigation/SyncLogTabNavigator';

const SyncLogScreen = () => {
  const { mode } = useTheme();
  const containerStyle = logListContainerStyles(mode);

  return (
    <SafeAreaView style={containerStyle.containerBackgroundColor}>
      <SyncLogTabNavigator />
    </SafeAreaView>
  );
};

export default SyncLogScreen;
