import React, { useState, useEffect } from 'react';
import { ScrollView, VStack, Box } from 'native-base';
import LogHeaderReadOnly from '../components/LogHeaderScreen/LogHeaderReadOnly.tsx';
import LogHeaderInputable from '../components/LogHeaderScreen/LogHeaderInputable.tsx';
import { useTheme } from '../styles/ThemeContext';
import { containerBackgroundColorStyles } from '../styles/screenGeneralStyles';
import { useRoute } from '@react-navigation/native';
import { LogList } from '../database/LogList';

const LogHeaderScreen = () => {
  const route = useRoute();
  const initialItem = route.params.item;
  const [item, setItem] = useState(initialItem);

  const { mode } = useTheme();
  const containerStyle = containerBackgroundColorStyles(mode);

  useEffect(() => {
    const fetchLatestData = async () => {
      if (initialItem && initialItem.log_header_id) {
        const latestData = await LogList.getByLogHeaderId(initialItem.log_header_id);
        if (latestData) {
          setItem(latestData);
        }
      }
    };

    fetchLatestData();
  }, [initialItem]);

  return (
    <ScrollView contentContainerStyle={containerStyle.containerBackgroundColor}>
      <VStack space={0} px={0}>
        <Box bg={mode === 'dark' ? 'gray.800' : 'gray.100'} p={4} rounded="lg" shadow={2}>
          <LogHeaderReadOnly item={item} />
        </Box>
        <Box bg={mode === 'dark' ? 'gray.800' : 'gray.100'} p={4} rounded="lg" shadow={2}>
          <LogHeaderInputable item={item} setItem={setItem} />
        </Box>
      </VStack>
    </ScrollView>
  );
};

export default LogHeaderScreen;
