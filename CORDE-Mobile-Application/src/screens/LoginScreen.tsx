import * as React from 'react';
import {ImageBackground} from 'react-native';
import {VStack, Box} from 'native-base';
import {useTheme} from '../styles/ThemeContext';
import Header from '../components/LoginScreen/Header.tsx';
import Login from '../components/LoginScreen/Login.tsx';
import {useWindowDimensions} from 'react-native';

const LoginScreen = () => {
  const {mode} = useTheme();
  const {height, width} = useWindowDimensions();
  const backgroundImage =
    mode === 'dark'
      ? require('../assets/Backgrounds/CORDE_Background_V2.1.png')
      : require('../assets/Backgrounds/CORDE_Background_V2.2.png');

  return (
    <ImageBackground
      source={backgroundImage}
      style={{flex: 1, width: '100%', height: '100%'}}>
      <VStack
        flex={1}
        justifyContent="space-between"
        px={width > 600 ? '15%' : '5%'}
        py={2}>
        <Box alignItems="center" flex={1} justifyContent="center">
          <Header />
        </Box>
        <Box flex={2} justifyContent="center">
          <Login />
        </Box>
      </VStack>
    </ImageBackground>
  );
};

export default LoginScreen;
