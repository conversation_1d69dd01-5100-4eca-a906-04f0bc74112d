import React, { useState, useEffect } from 'react';
import { Box, VStack, Button, useToast } from 'native-base';
import { LogList } from '../database/LogList';
import { StoredFiles } from '../database/StoredFiles';
import { MobileSyncLogs } from '../database/MobileSyncLogs';
import { LogExtensions } from '../database/LogExtensions';
import { AssetCoordinates } from '../database/AssetCoordinates';
import DebugTabNavigator from '../navigation/DebugTabNavigator';

const DebugScreen = () => {
  const [data, setData] = useState<{
    logs: any[];
    files: any[];
    syncLogs: any[];
    logExtensions: any[];
    assetCoordinates: any[];
  }>({
    logs: [],
    files: [],
    syncLogs: [],
    logExtensions: [],
    assetCoordinates: [],
  });
  const toast = useToast();

  const fetchData = async () => {
    try {
      const logs = await LogList.getAll(1, 5);
      const files = await StoredFiles.getAll(5);
      const syncLogs = await MobileSyncLogs.getAll(5);
      const logExtensions = await LogExtensions.getAll(5);
      const assetCoordinates = await AssetCoordinates.getAll(10);

      setData({ logs, files, syncLogs, logExtensions, assetCoordinates });

      toast.show({
        title: "Data refreshed",
        placement: "top"
      });
    } catch (error) {
      console.error('Error fetching data:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      toast.show({
        title: `Error fetching data: ${errorMessage}`,
        placement: "top"
      });
    }
  };

  useEffect(() => {
    fetchData();
  }, []);

  return (
    <Box flex={1} safeArea>
      <VStack flex={1}>
        <Box flex={1}>
          <DebugTabNavigator data={data} />
        </Box>
      </VStack>
    </Box>
  );
};

export default DebugScreen;
