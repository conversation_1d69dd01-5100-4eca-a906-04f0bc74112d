import React, {useState, useEffect, useRef, useCallback} from 'react';
import {StyleSheet, TouchableOpacity, Pressable} from 'react-native';
import {Box, Spinner, Text, useToast, HStack, Input, Icon} from 'native-base';
import MapView, {Marker, Callout, Region} from 'react-native-maps';
import {useTheme} from '../styles/ThemeContext';
import {useRoute, useNavigation} from '@react-navigation/native';
import {LogList} from '../database/LogList';
import {getGeocodingData} from '../api/GoogleMapsApi';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import {locationService} from "../services/LocationService.ts";
import { LogListService } from '../services/LogListService';
import { VStack, IconButton } from 'native-base';
import {AuthService} from "../services/AuthService.ts";
import {debounce} from "lodash";
import DateTimePicker from '@react-native-community/datetimepicker';
import {processDateRangeForNZ} from '../utils/dateUtils';


interface LogMarker {
  id: number;
  log_header_id?: number;
  address: string;
  description: string;
  latitude: number;
  longitude: number;
  log_type?: string;
  allocated?: string;
  schd_date?: string;
  isHighlighted?: boolean;
}

interface MarkerGroup {
  id: string; // use concatenated lat and long as id
  markers: LogMarker[];
  currentIndex: number; // track the current marker index
}

interface DateFilter {
  startDate?: Date;
  endDate?: Date;
}

const DEFAULT_ADDRESS = 'CORDE - North Canterbury';

// First, let's create a persistent cache outside the component
const globalAddressCache = new Map<string, {lat: number; lng: number}>();

const MapMarkerScreen: React.FC = () => {
  const [markerGroups, setMarkerGroups] = useState<MarkerGroup[]>([]);
  const [flattedMarkers, setFlattedMarkers] = useState<LogMarker[]>([]);
  const [loading, setLoading] = useState(true);
  const [region, setRegion] = useState<Region | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const {mode} = useTheme();
  const toast = useToast();
  const mapRef = useRef<MapView>(null);
  const route = useRoute();
  const navigation = useNavigation();
  const [selectedMarker, setSelectedMarker] = useState<string | null>(null);
  const [dateFilter, setDateFilter] = useState<DateFilter>({
    startDate: undefined,
    endDate: undefined
  });
  const [showDatePicker, setShowDatePicker] = useState({
    startDate: false,
    endDate: false,
  });
  const [isDateFilterCollapsed, setIsDateFilterCollapsed] = useState(false);
  const [highlightedLog, setHighlightedLog] = useState<LogMarker | null>(null);

  const processedAddresses = useRef(new Set<string>());
  const globalAddressCache = useRef(new Map<string, {lat: number, lng: number}>());

  useEffect(() => {
    const {log_address, description} = route.params || {};
    console.log('Received params in MapMarkerScreen:', {
      log_address,
      description,
    });

    if (log_address) {
      geocodeAddress(log_address, description);
    } else {
      initializeMap();
    }
  }, [route.params]);

  // Removed automatic date filter initialization
  // Map will start empty, user must manually select date range

  const fetchTopLog = async () => {
    try {
      const currentUser = await AuthService.getCurrentUser();

      const filters = {
        userId: currentUser?.person_id,
      };

      const logs = await LogList.getFilteredLogs(1, 100, filters ); // Get one log
      if (logs.length > 0) {
        const log = logs[0];
        geocodeAddress(log.log_address, log.description);
      } else {
        geocodeAddress(DEFAULT_ADDRESS, DEFAULT_ADDRESS);
      }
    } catch (error) {
      console.error('Error fetching top log:', error);
      toast.show({
        title: 'Error',
        description: 'Failed to load log data',

      });
      geocodeAddress(DEFAULT_ADDRESS, DEFAULT_ADDRESS);
    }
  };

  const setDefaultMapLocation = async () => {
    try {
      const data = await getGeocodingData(DEFAULT_ADDRESS);
      if (data.results && data.results.length > 0) {
        const {lat, lng} = data.results[0].geometry.location;
        const newRegion = {
          latitude: lat,
          longitude: lng,
          latitudeDelta: 0.01,
          longitudeDelta: 0.01,
        };
        setRegion(newRegion);
        
        // Don't create any markers for default location
        setFlattedMarkers([]);
        setMarkerGroups([]);
        
        if (mapRef.current) {
          mapRef.current.animateToRegion(newRegion, 1000);
        }
      }
    } catch (error) {
      console.error('Error setting default map location:', error);
    }
  };

  const initializeMap = async () => {
    setLoading(true);

    // 首先加载默认地址但不创建标记
    await setDefaultMapLocation();

    try {
      // 异步获取用户位置
      const location = await locationService.getCurrentLocation({
        enableHighAccuracy: true,
        timeout: 15000,
        maximumAge: 10000
      });

      console.log('Current location:', location);
      if (location) {
        const newRegion = {
          latitude: location.latitude,
          longitude: location.longitude,
          latitudeDelta: 0.01,
          longitudeDelta: 0.01,
        };
        setRegion(newRegion);
        
        // Don't create a manual current location marker - MapView showsUserLocation will handle this
        // Initialize with empty markers - user must select date range to see logs
        setFlattedMarkers([]);
        setMarkerGroups([]);

        if (mapRef.current) {
          mapRef.current.animateToRegion(newRegion, 1000);
        }
      } else {
        // 如果获取位置失败，回退到默认地址但不创建标记
        await setDefaultMapLocation();
      }
    } catch (error) {
      console.error('Error getting current location:', error);
      // 如果出错，回退到默认地址但不创建标记
      await setDefaultMapLocation();
    } finally {
      setLoading(false);
    }
  };

  const applyDateFilter = useCallback(
    debounce(async (startDate: Date, endDate: Date) => {
      const currentUser = await AuthService.getCurrentUser();

      // Process date range for New Zealand timezone (same as Group Log)
      const processedDateRange = processDateRangeForNZ({
        startDate,
        endDate
      });

      const newFilters = {
        userId: currentUser?.person_id,
        dateFrom: processedDateRange.startDate,
        dateTo: processedDateRange.endDate,
      };

      console.log('🗺️ Map date filter applied:', {
        originalDates: { startDate: startDate.toISOString(), endDate: endDate.toISOString() },
        processedDates: { 
          dateFrom: processedDateRange.startDate?.toISOString(), 
          dateTo: processedDateRange.endDate?.toISOString() 
        }
      });

      // Use new filters to reload data
      fetchLogListMarkers(newFilters);
    }, 500), // 500ms debounce to prevent rapid successive calls
    []
  );

  const handleDateChange = (event: any, selectedDate: Date | undefined, field: 'startDate' | 'endDate') => {
    setShowDatePicker(prev => ({ ...prev, [field]: false }));
    
    if (selectedDate) {
      const newDateFilter = { ...dateFilter };
      
      if (field === 'startDate') {
        selectedDate.setHours(0, 0, 0, 0);
        newDateFilter.startDate = selectedDate;
      } else {
        selectedDate.setHours(23, 59, 59, 999);
        newDateFilter.endDate = selectedDate;
      }
      
      setDateFilter(newDateFilter);
      
      // Apply filter if both dates are set
      if (newDateFilter.startDate && newDateFilter.endDate) {
        applyDateFilter(newDateFilter.startDate, newDateFilter.endDate);
      }
    }
  };

  const fetchLogListMarkers = async (customFilters?: any) => {
    try {
      // Clear existing markers immediately to prevent accumulation
      // Don't set loading=true here as it will reset the map view
      setFlattedMarkers([]);
      setMarkerGroups([]);
      
      const currentUser = await AuthService.getCurrentUser();
      const filters = customFilters || {
        userId: currentUser?.person_id,
      };

      // 如果没有自定义过滤条件，则添加当前日期过滤
      if (!customFilters && dateFilter.startDate && dateFilter.endDate) {
        filters.dateFrom = dateFilter.startDate;
        filters.dateTo = dateFilter.endDate;
      }

      const logs = await LogListService.fetchFilteredLogs(1, 1000, filters);



      // Only geocode addresses for logs that don't have asset coordinates
      const logsNeedingGeocoding = logs.filter(log => !log.asset_latitude || !log.asset_longitude);
      const uniqueAddresses = [...new Set(logsNeedingGeocoding.map(log => log.log_address))]
        .filter(address => !processedAddresses.current.has(address));

      // Only geocode addresses we haven't processed before and don't have asset coordinates
      if (uniqueAddresses.length > 0) {
        await Promise.all(
          uniqueAddresses.map(async address => {
            try {
              // Skip if we already have this address in our global cache
              if (!globalAddressCache.current.has(address)) {
                const geocodeData = await getGeocodingData(address);
                if (geocodeData.results && geocodeData.results.length > 0) {
                  globalAddressCache.current.set(address, geocodeData.results[0].geometry.location);
                }
              }
              // Mark address as processed
              processedAddresses.current.add(address);
            } catch (error) {
              console.error(`Error geocoding address: ${address}`, error);
            }
          })
        );
      }

      // Create markers using asset coordinates first, then fallback to geocoding cache
      let resolvedMarkers = logs
        .map(log => {
          // Priority 1: Use asset coordinates from database
          if (log.asset_latitude && log.asset_longitude) {
            return {
              id: log.local_log_id,
              log_header_id: log.log_header_id,
              address: log.log_address,
              description: log.description,
              latitude: log.asset_latitude,
              longitude: log.asset_longitude,
              log_type: log.log_type,
              allocated: log.allocation_person_full_name,
              schd_date: log.schd_date
            };
          }

          // Priority 2: Use geocoding cache as fallback
          const coords = globalAddressCache.current.get(log.log_address);
          if (coords) {
            return {
              id: log.local_log_id,
              log_header_id: log.log_header_id,
              address: log.log_address,
              description: log.description,
              latitude: coords.lat,
              longitude: coords.lng,
              log_type: log.log_type,
              allocated: log.allocation_person_full_name,
              schd_date: log.schd_date
            };
          }
          return null;
        }).filter((marker): marker is LogMarker => marker !== null) as LogMarker[];

      // Only include highlighted log if it's from navigation (not from date filtering)
      // This prevents highlighted markers from interfering with date filter results
      if (highlightedLog && !customFilters) {
        // Only add highlighted log when not using date filters
        const highlightedExists = resolvedMarkers.some(marker =>
          Math.abs(marker.latitude - highlightedLog.latitude) < 0.0001 &&
          Math.abs(marker.longitude - highlightedLog.longitude) < 0.0001
        );

        if (!highlightedExists) {
          resolvedMarkers = [highlightedLog, ...resolvedMarkers];
        }
      }

      const groups = groupMarkersByLocation(resolvedMarkers);
      setMarkerGroups(groups);
      setFlattedMarkers(resolvedMarkers);
    } catch (error) {
      console.error('Error fetching log list:', error);
      toast.show({
        title: 'Error',
        description: 'Failed to load log markers',
      });
    }
    // Don't modify loading state here as it would reset map view
  };

  const geocodeAddress = async (address: string, description?: string) => {
    setLoading(true);
    try {
      const data = await getGeocodingData(address || DEFAULT_ADDRESS);

      if (data.results && data.results.length > 0) {
        const {lat, lng} = data.results[0].geometry.location;
        const newRegion = {
          latitude: lat,
          longitude: lng,
          latitudeDelta: 0.01,
          longitudeDelta: 0.01,
        };
        setRegion(newRegion);

        const highlightedMarker = {
          id: 1,
          address: address || DEFAULT_ADDRESS,
          description: description || DEFAULT_ADDRESS,
          latitude: lat,
          longitude: lng,
          isHighlighted: true
        };

        // 保存高亮日志，以便后续过滤处理
        setHighlightedLog(highlightedMarker);

        // Only show the highlighted marker initially - don't fetch log data
        setFlattedMarkers([highlightedMarker]);
        setMarkerGroups(groupMarkersByLocation([highlightedMarker]));

        if (mapRef.current) {
          mapRef.current.animateToRegion(newRegion, 1000);
        }
      } else {
        console.log('No results found for the address');
        if (address !== DEFAULT_ADDRESS) {
          geocodeAddress(DEFAULT_ADDRESS, DEFAULT_ADDRESS);
        }
      }
    } catch (error) {
      console.error('Error geocoding address:', error);
      if (address !== DEFAULT_ADDRESS) {
        geocodeAddress(DEFAULT_ADDRESS, DEFAULT_ADDRESS);
      }
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = () => {
    if (searchQuery) {
      geocodeAddress(searchQuery);
    }
  };

  const handleMarkerPress = (marker: LogMarker) => {
    // Navigate back to LogHeaderScreen with the marker data
    navigation.navigate('Log Header', {item: marker});
  };

  const navigateMarker = (groupId: string, direction: 'next' | 'prev') => {
    setMarkerGroups(prevGroups => {
      return prevGroups.map(group => {
        if (group.id === groupId) {
          const count = group.markers.length;
          let newIndex = group.currentIndex;

          if (direction === 'next') {
            newIndex = (group.currentIndex + 1) % count;
          } else {
            newIndex = (group.currentIndex - 1 + count) % count;
          }

          return { ...group, currentIndex: newIndex };
        }
        return group;
      });
    });
  };

  // Region change handler - disabled automatic data fetching
  const handleRegionChange = useCallback(
    debounce(async (newRegion: Region) => {
      // Don't automatically fetch log data on region change
      // Users must manually select date range to see logs
    }, 1000),
    []
  );

  const formatDate = (dateString: string | undefined) => {
    if (!dateString) return 'No date';
    const date = new Date(dateString);
    return date.toLocaleDateString();
  };

  const groupMarkersByLocation = (markers: LogMarker[]): MarkerGroup[] => {
    if (!markers) return [];

    const groups = new Map<string, LogMarker[]>();

    // 按位置分组标记
    markers.forEach(marker => {
      // 使用精度为5位小数的经纬度作为键
      const key = `${marker.latitude.toFixed(5)},${marker.longitude.toFixed(5)}`;
      if (!groups.has(key)) {
        groups.set(key, []);
      }
      groups.get(key)!.push(marker);
    });

    // 转换为数组格式
    return Array.from(groups.entries()).map(([id, markers]) => ({
      id,
      markers,
      currentIndex: 0,
    }));
  };

  return (
    <Box flex={1} bg={mode === 'dark' ? 'black' : 'white'}>
      {loading ? (
        <Spinner size="lg" />
      ) : (
        <MapView
          ref={mapRef}
          style={StyleSheet.absoluteFillObject}
          region={region || undefined}
          showsUserLocation={true}
          showsMyLocationButton={true}
          showsCompass={true}
          showsScale={true}
          onRegionChangeComplete={handleRegionChange}
          onPress={() => setSelectedMarker(null)}
        >
          {markerGroups.map(group => {
            const currentMarker = group.markers[group.currentIndex];
            return (
              <Marker
                key={group.id}
                coordinate={{
                  latitude: currentMarker.latitude,
                  longitude: currentMarker.longitude,
                }}
                pinColor={currentMarker.isHighlighted ? 'red' : undefined}
                onPress={() => {
                  // 点击marker时选中/取消选中
                  setSelectedMarker(prevSelected =>
                    prevSelected === group.id ? null : group.id
                  );
                }}
              />
            );
          })}
        </MapView>
      )}

      {selectedMarker && (
        <Box
          position="absolute"
          bottom={100} // 位置可以调整
          left={20}
          right={20}
          bg={mode === 'dark' ? 'gray.800' : 'white'}
          borderRadius="md"
          p={4}
          shadow={5}
        >
          <IconButton
            position="absolute"
            top={2}
            right={2}
            size="sm"
            onPress={() => setSelectedMarker(null)}
            _pressed={{
              bg: mode === 'dark' ? 'gray.700' : 'gray.200'
            }}
          />
          {(() => {
            // 找到当前选中的marker组
            const group = markerGroups.find(g => g.id === selectedMarker)!;
            const currentMarker = group.markers[group.currentIndex];

            return (
              <VStack space={2}>
                <HStack justifyContent="space-between" alignItems="center">
                  <Text fontSize="md" fontWeight="bold" color="orange.500">
                    Log #{currentMarker.log_header_id}
                  </Text>

                  {group.markers.length > 1 && (
                    <HStack alignItems="center" space={2}>
                      <IconButton
                        size="xs"
                        icon={<Icon
                          as={MaterialCommunityIcons}
                          name="chevron-left"
                          size="xs"
                          color="orange.500"
                        />}
                        onPress={() => navigateMarker(group.id, 'prev')}
                      />

                      <Text fontSize="xs" color="gray.500">
                        {group.currentIndex + 1}/{group.markers.length}
                      </Text>

                      <IconButton
                        size="xs"
                        icon={<Icon
                          as={MaterialCommunityIcons}
                          name="chevron-right"
                          size="xs"
                          color="orange.500"
                        />}
                        onPress={() => navigateMarker(group.id, 'next')}
                      />
                    </HStack>
                  )}

                </HStack>

                <VStack space={0}>
                  <HStack justifyContent="space-between" alignItems="center">
                    <Text fontSize="sm" fontWeight="bold">Description:</Text>
                    <IconButton
                      size="sm"
                      icon={<Icon
                        as={MaterialCommunityIcons}
                        name="arrow-right-circle"
                        size="xl"
                        color="orange.600"
                      />}
                      onPress={() => handleMarkerPress(currentMarker)}
                    />
                  </HStack>
                  <Text fontSize="sm">{currentMarker.description?.replace(/"/g, '') || ''}</Text>

                  <Text fontSize="sm" fontWeight="bold">Address:</Text>
                  <Text fontSize="sm">{currentMarker.address}</Text>

                  <Text fontSize="sm" fontWeight="bold">Scheduled Date:</Text>
                  <Text fontSize="sm">{formatDate(currentMarker.schd_date)}</Text>

                  <Text fontSize="sm" fontWeight="bold">Log Type:</Text>
                  <Text fontSize="sm">{currentMarker.log_type || 'N/A'}</Text>

                  <Text fontSize="sm" fontWeight="bold">Allocated:</Text>
                  <Text fontSize="sm">{currentMarker.allocated || 'N/A'}</Text>
                </VStack>
              </VStack>
            );
          })()}
        </Box>
      )}

      {/* Date Filter Box */}
      <Box
        position="absolute"
        top={2}
        left={2}
        bg={mode === 'dark' ? 'gray.800' : 'white'}
        borderRadius={8}
        shadow={3}
        borderWidth={1}
        borderColor={mode === 'dark' ? 'gray.600' : 'gray.200'}
        minWidth={isDateFilterCollapsed ? 50 : 180}
        width={isDateFilterCollapsed ? 50 : "auto"}
        overflow="hidden"
      >
        {isDateFilterCollapsed ? (
          // Collapsed State
          <Pressable 
            onPress={() => setIsDateFilterCollapsed(false)}
            p={2}
          >
            <VStack alignItems="center" space={1}>
              <Icon 
                as={MaterialCommunityIcons} 
                name="calendar" 
                size={5} 
                color="orange.500" 
              />
              <Icon 
                as={MaterialCommunityIcons} 
                name="chevron-right" 
                size={4} 
                color={mode === 'dark' ? 'gray.400' : 'gray.600'} 
              />
            </VStack>
          </Pressable>
        ) : (
          // Expanded State
          <Box p={3}>
            <HStack justifyContent="space-between" alignItems="center" mb={2}>
              <Text fontSize="sm" fontWeight="bold" color={mode === 'dark' ? 'white' : 'gray.800'}>
                📅 Date Range
              </Text>
              <Pressable 
                onPress={() => setIsDateFilterCollapsed(true)}
                p={1}
                borderRadius={4}
                _pressed={{
                  bg: mode === 'dark' ? 'gray.700' : 'gray.100'
                }}
              >
                <Icon 
                  as={MaterialCommunityIcons} 
                  name="chevron-left" 
                  size={4} 
                  color={mode === 'dark' ? 'gray.400' : 'gray.600'} 
                />
              </Pressable>
            </HStack>
            
            <VStack space={2}>
              <VStack space={1}>
                <Text fontSize="xs" fontWeight="medium" color={mode === 'dark' ? 'gray.300' : 'gray.600'}>
                  From:
                </Text>
                <Pressable 
                  onPress={() => setShowDatePicker({ ...showDatePicker, startDate: true })}
                >
                  <Input
                    value={dateFilter.startDate ? dateFilter.startDate.toLocaleDateString('en-NZ') : ''}
                    isReadOnly={true}
                    size="sm"
                    height="32px"
                    fontSize="xs"
                    bg={mode === 'dark' ? 'gray.700' : 'gray.50'}
                    color={mode === 'dark' ? 'white' : 'gray.800'}
                    borderColor={mode === 'dark' ? 'gray.500' : 'gray.300'}
                    borderWidth={1}
                    _focus={{
                      borderColor: 'orange.500',
                      bg: mode === 'dark' ? 'gray.600' : 'white'
                    }}
                    InputRightElement={
                      <Icon as={MaterialCommunityIcons} name="calendar" size={4} mr="2" color="orange.500" />
                    }
                  />
                </Pressable>
              </VStack>
              
              <VStack space={1}>
                <Text fontSize="xs" fontWeight="medium" color={mode === 'dark' ? 'gray.300' : 'gray.600'}>
                  To:
                </Text>
                <Pressable 
                  onPress={() => setShowDatePicker({ ...showDatePicker, endDate: true })}
                >
                  <Input
                    value={dateFilter.endDate ? dateFilter.endDate.toLocaleDateString('en-NZ') : ''}
                    isReadOnly={true}
                    size="sm"
                    height="32px"
                    fontSize="xs"
                    bg={mode === 'dark' ? 'gray.700' : 'gray.50'}
                    color={mode === 'dark' ? 'white' : 'gray.800'}
                    borderColor={mode === 'dark' ? 'gray.500' : 'gray.300'}
                    borderWidth={1}
                    _focus={{
                      borderColor: 'orange.500',
                      bg: mode === 'dark' ? 'gray.600' : 'white'
                    }}
                    InputRightElement={
                      <Icon as={MaterialCommunityIcons} name="calendar" size={4} mr="2" color="orange.500" />
                    }
                  />
                </Pressable>
              </VStack>
            </VStack>
          </Box>
        )}
        
        {/* Date Pickers - only show when expanded */}
        {!isDateFilterCollapsed && showDatePicker.startDate && (
          <DateTimePicker
            value={dateFilter.startDate || new Date()}
            mode="date"
            display="default"
            onChange={(event, selectedDate) => handleDateChange(event, selectedDate, 'startDate')}
          />
        )}
        {!isDateFilterCollapsed && showDatePicker.endDate && (
          <DateTimePicker
            value={dateFilter.endDate || new Date()}
            mode="date"
            display="default"
            onChange={(event, selectedDate) => handleDateChange(event, selectedDate, 'endDate')}
          />
        )}
      </Box>

      <Box
        position="absolute"
        bottom={4}
        left={10}
        right={10}
        bg={mode === 'dark' ? 'gray.800' : 'white'}
        borderRadius="full"
        p={2}
        shadow={3}>
        <HStack alignItems="center" space={2}>
          <Input
            placeholder="Search location"
            flex={1}
            value={searchQuery}
            onChangeText={setSearchQuery}
            onSubmitEditing={handleSearch}
            borderWidth={0}
            fontSize="md"
            height={8}
            color={mode === 'dark' ? 'white' : 'black'}
            _focus={{
              borderWidth: 0,
              backgroundColor: 'transparent',
            }}
          />
          <IconButton
            icon={
              <Icon
                as={MaterialCommunityIcons}
                name="magnify"
                size={5}
                color={mode === 'dark' ? 'white' : 'black'}
              />
            }
            onPress={handleSearch}
            variant="ghost"
            _pressed={{
              bg: mode === 'dark' ? 'gray.700' : 'gray.100'
            }}
          />
        </HStack>
      </Box>
    </Box>
  );
};

export default MapMarkerScreen;
