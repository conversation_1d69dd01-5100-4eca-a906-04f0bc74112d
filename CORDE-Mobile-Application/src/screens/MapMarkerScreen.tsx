import React, {useState, useEffect, useRef, useCallback} from 'react';
import {StyleSheet, TouchableOpacity} from 'react-native';
import {Box, Spinner, Text, useToast, HStack, Input, Icon, Select} from 'native-base';
import MapView, {Marker, Callout, Region} from 'react-native-maps';
import {useTheme} from '../styles/ThemeContext';
import {useRoute, useNavigation} from '@react-navigation/native';
import {LogList} from '../database/LogList';
import {getGeocodingData} from '../api/GoogleMapsApi';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import {locationService} from "../services/LocationService.ts";
import { LogListService } from '../services/LogListService';
import { VStack, IconButton } from 'native-base';
import {AuthService} from "../services/AuthService.ts";
import {debounce} from "lodash";


interface LogMarker {
  id: number;
  log_header_id?: number;
  address: string;
  description: string;
  latitude: number;
  longitude: number;
  log_type?: string;
  allocated?: string;
  schd_date?: string;
  isHighlighted?: boolean;
}

interface MarkerGroup {
  id: string; // use concatenated lat and long as id
  markers: LogMarker[];
  currentIndex: number; // track the current marker index
}

interface DateFilter {
  range: 'today' | '3days' | 'week' | 'month' | 'all';
  startDate?: Date;
  endDate?: Date;
}

const DEFAULT_ADDRESS = 'CORDE - North Canterbury';

// First, let's create a persistent cache outside the component
const globalAddressCache = new Map<string, {lat: number; lng: number}>();

const MapMarkerScreen: React.FC = () => {
  const [markerGroups, setMarkerGroups] = useState<MarkerGroup[]>([]);
  const [flattedMarkers, setFlattedMarkers] = useState<LogMarker[]>([]);
  const [loading, setLoading] = useState(true);
  const [region, setRegion] = useState<Region | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const {mode} = useTheme();
  const toast = useToast();
  const mapRef = useRef<MapView>(null);
  const route = useRoute();
  const navigation = useNavigation();
  const [selectedMarker, setSelectedMarker] = useState<string | null>(null);
  const [dateFilter, setDateFilter] = useState<DateFilter>({ range: 'today' });
  const [highlightedLog, setHighlightedLog] = useState<LogMarker | null>(null);

  const processedAddresses = useRef(new Set<string>());
  const globalAddressCache = useRef(new Map<string, {lat: number, lng: number}>());

  useEffect(() => {
    const {log_address, description} = route.params || {};
    console.log('Received params in MapMarkerScreen:', {
      log_address,
      description,
    });

    if (log_address) {
      geocodeAddress(log_address, description);
    } else {
      initializeMap();
    }
  }, [route.params]);

  useEffect(() => {
    updateDateFilterDates('today');
  }, []);

  const fetchTopLog = async () => {
    try {
      const currentUser = await AuthService.getCurrentUser();

      const filters = {
        userId: currentUser?.person_id,
      };

      const logs = await LogList.getFilteredLogs(1, 100, filters ); // Get one log
      if (logs.length > 0) {
        const log = logs[0];
        geocodeAddress(log.log_address, log.description);
      } else {
        geocodeAddress(DEFAULT_ADDRESS, DEFAULT_ADDRESS);
      }
    } catch (error) {
      console.error('Error fetching top log:', error);
      toast.show({
        title: 'Error',
        description: 'Failed to load log data',

      });
      geocodeAddress(DEFAULT_ADDRESS, DEFAULT_ADDRESS);
    }
  };

  const initializeMap = async () => {
    setLoading(true);

    // 首先加载默认地址
    await geocodeAddress(DEFAULT_ADDRESS, DEFAULT_ADDRESS);

    try {
      // 异步获取用户位置
      const location = await locationService.getCurrentLocation({
        enableHighAccuracy: true,
        timeout: 15000,
        maximumAge: 10000
      });

      console.log('Current location:', location);
      if (location) {
        const newRegion = {
          latitude: location.latitude,
          longitude: location.longitude,
          latitudeDelta: 0.01,
          longitudeDelta: 0.01,
        };
        setRegion(newRegion);
        const currentLocationMarker = {
          id: 1,
          address: 'Current Location',
          description: 'You are here',
          latitude: location.latitude,
          longitude: location.longitude,
        };
        setFlattedMarkers([currentLocationMarker]);
        setMarkerGroups(groupMarkersByLocation([currentLocationMarker]));

        await fetchLogListMarkers();

        if (mapRef.current) {
          mapRef.current.animateToRegion(newRegion, 1000);
        }
      } else {
        // 如果获取位置失败，回退到默认地址
        await fetchTopLog();
      }
    } catch (error) {
      console.error('Error getting current location:', error);
      // 如果出错，回退到默认地址
      await fetchTopLog();
    } finally {
      setLoading(false);
    }
  };

  const updateDateFilterDates = async (range: string) => {
    const now = new Date();
    const endDate = new Date(now);
    let startDate = new Date(now);

    const currentUser = await AuthService.getCurrentUser();

    switch (range) {
      case 'today':
        startDate.setHours(0, 0, 0, 0);
        break;
      case '3days':
        startDate.setDate(now.getDate() - 2);
        startDate.setHours(0, 0, 0, 0);
        break;
      case 'week':
        startDate.setDate(now.getDate() - 7);
        startDate.setHours(0, 0, 0, 0);
        break;
      case 'month':
        startDate.setMonth(now.getMonth() - 1);
        startDate.setHours(0, 0, 0, 0);
        break;
      case 'all':
        startDate = new Date(2000, 0, 1); // 远过去的日期以包含所有
        break;
    }

    // 先更新状态
    setDateFilter({
      range: range as any,
      startDate,
      endDate
    });

    // 立即使用新的值触发重新加载
    const newFilters = {
      userId: currentUser?.person_id,
    };

    // 如果不是"全部"范围，则添加日期过滤
    if (range !== 'all') {
      newFilters.dateFrom = startDate;
      newFilters.dateTo = endDate;
    }

    // 使用新的过滤条件重新加载数据
    fetchLogListMarkers(newFilters);
  };

  const fetchLogListMarkers = async (customFilters?: any) => {
    try {
      const currentUser = await AuthService.getCurrentUser();
      const filters = customFilters || {
        userId: currentUser?.person_id,
      };

      // 如果没有自定义过滤条件，且当前不是"全部"范围，则添加日期过滤
      if (!customFilters && dateFilter.range !== 'all' && dateFilter.startDate && dateFilter.endDate) {
        filters.dateFrom = dateFilter.startDate;
        filters.dateTo = dateFilter.endDate;
      }

      const logs = await LogListService.fetchFilteredLogs(1, 1000, filters);



      // Only geocode addresses for logs that don't have asset coordinates
      const logsNeedingGeocoding = logs.filter(log => !log.asset_latitude || !log.asset_longitude);
      const uniqueAddresses = [...new Set(logsNeedingGeocoding.map(log => log.log_address))]
        .filter(address => !processedAddresses.current.has(address));

      // Only geocode addresses we haven't processed before and don't have asset coordinates
      if (uniqueAddresses.length > 0) {
        await Promise.all(
          uniqueAddresses.map(async address => {
            try {
              // Skip if we already have this address in our global cache
              if (!globalAddressCache.current.has(address)) {
                const geocodeData = await getGeocodingData(address);
                if (geocodeData.results && geocodeData.results.length > 0) {
                  globalAddressCache.current.set(address, geocodeData.results[0].geometry.location);
                }
              }
              // Mark address as processed
              processedAddresses.current.add(address);
            } catch (error) {
              console.error(`Error geocoding address: ${address}`, error);
            }
          })
        );
      }

      // Create markers using asset coordinates first, then fallback to geocoding cache
      let resolvedMarkers = logs
        .map(log => {
          // Priority 1: Use asset coordinates from database
          if (log.asset_latitude && log.asset_longitude) {
            return {
              id: log.local_log_id,
              log_header_id: log.log_header_id,
              address: log.log_address,
              description: log.description,
              latitude: log.asset_latitude,
              longitude: log.asset_longitude,
              log_type: log.log_type,
              allocated: log.allocation_person_full_name,
              schd_date: log.schd_date
            };
          }

          // Priority 2: Use geocoding cache as fallback
          const coords = globalAddressCache.current.get(log.log_address);
          if (coords) {
            return {
              id: log.local_log_id,
              log_header_id: log.log_header_id,
              address: log.log_address,
              description: log.description,
              latitude: coords.lat,
              longitude: coords.lng,
              log_type: log.log_type,
              allocated: log.allocation_person_full_name,
              schd_date: log.schd_date
            };
          }
          return null;
        }).filter((marker): marker is LogMarker => marker !== null) as LogMarker[];

      // 如果有高亮日志，确保它包含在结果中
      if (highlightedLog) {
        // 检查是否已存在相同位置的标记
        const highlightedExists = resolvedMarkers.some(marker =>
          Math.abs(marker.latitude - highlightedLog.latitude) < 0.0001 &&
          Math.abs(marker.longitude - highlightedLog.longitude) < 0.0001
        );

        if (!highlightedExists) {
          resolvedMarkers = [highlightedLog, ...resolvedMarkers];
        }
      }

      const groups = groupMarkersByLocation(resolvedMarkers);
      setMarkerGroups(groups);
      setFlattedMarkers(resolvedMarkers);
    } catch (error) {
      console.error('Error fetching log list:', error);
      toast.show({
        title: 'Error',
        description: 'Failed to load log markers',
      });
    }
  };

  const geocodeAddress = async (address: string, description?: string) => {
    setLoading(true);
    try {
      const data = await getGeocodingData(address || DEFAULT_ADDRESS);

      if (data.results && data.results.length > 0) {
        const {lat, lng} = data.results[0].geometry.location;
        const newRegion = {
          latitude: lat,
          longitude: lng,
          latitudeDelta: 0.01,
          longitudeDelta: 0.01,
        };
        setRegion(newRegion);

        const highlightedMarker = {
          id: 1,
          address: address || DEFAULT_ADDRESS,
          description: description || DEFAULT_ADDRESS,
          latitude: lat,
          longitude: lng,
          isHighlighted: true
        };

        // 保存高亮日志，以便后续过滤处理
        setHighlightedLog(highlightedMarker);

        await fetchLogListMarkers();
        const currentMarkers = flattedMarkers; // 使用当前状态

        // 创建新的标记数组
        const updatedMarkers = [highlightedMarker, ...currentMarkers];

        // 更新两个状态
        setFlattedMarkers(updatedMarkers);
        setMarkerGroups(groupMarkersByLocation(updatedMarkers));

        if (mapRef.current) {
          mapRef.current.animateToRegion(newRegion, 1000);
        }
      } else {
        console.log('No results found for the address');
        if (address !== DEFAULT_ADDRESS) {
          geocodeAddress(DEFAULT_ADDRESS, DEFAULT_ADDRESS);
        }
      }
    } catch (error) {
      console.error('Error geocoding address:', error);
      if (address !== DEFAULT_ADDRESS) {
        geocodeAddress(DEFAULT_ADDRESS, DEFAULT_ADDRESS);
      }
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = () => {
    if (searchQuery) {
      geocodeAddress(searchQuery);
    }
  };

  const handleMarkerPress = (marker: LogMarker) => {
    // Navigate back to LogHeaderScreen with the marker data
    navigation.navigate('Log Header', {item: marker});
  };

  const navigateMarker = (groupId: string, direction: 'next' | 'prev') => {
    setMarkerGroups(prevGroups => {
      return prevGroups.map(group => {
        if (group.id === groupId) {
          const count = group.markers.length;
          let newIndex = group.currentIndex;

          if (direction === 'next') {
            newIndex = (group.currentIndex + 1) % count;
          } else {
            newIndex = (group.currentIndex - 1 + count) % count;
          }

          return { ...group, currentIndex: newIndex };
        }
        return group;
      });
    });
  };

  // 在现有的 state 声明下面添加
  const handleRegionChange = useCallback(
    debounce(async (newRegion: Region) => {
      try {
        // 使用已有的flattedMarkers数据，避免重复查询数据库
        if (flattedMarkers.length === 0) {
          // 只有在没有数据时才查询数据库
          const currentUser = await AuthService.getCurrentUser();
          const filters = {
            userId: currentUser?.person_id,
          };

          const logs = await LogListService.fetchFilteredLogs(1, 1000, filters);

          // 处理地理编码逻辑
          const logsNeedingGeocoding = logs.filter(log => !log.asset_latitude || !log.asset_longitude);
          const uniqueAddresses = [...new Set(logsNeedingGeocoding.map(log => log.log_address))]
            .filter(address => !processedAddresses.current.has(address));

          if (uniqueAddresses.length > 0) {
            await Promise.all(
              uniqueAddresses.map(async address => {
                try {
                  if (!globalAddressCache.current.has(address)) {
                    const geocodeData = await getGeocodingData(address);
                    if (geocodeData.results && geocodeData.results.length > 0) {
                      globalAddressCache.current.set(address, geocodeData.results[0].geometry.location);
                    }
                  }
                  processedAddresses.current.add(address);
                } catch (error) {
                  console.error(`Error geocoding address: ${address}`, error);
                }
              })
            );
          }

          // 创建标记
          const resolvedMarkers = logs
            .map(log => {
              if (log.asset_latitude && log.asset_longitude) {
                return {
                  id: log.local_log_id,
                  log_header_id: log.log_header_id,
                  address: log.log_address,
                  description: log.description,
                  latitude: log.asset_latitude,
                  longitude: log.asset_longitude,
                  log_type: log.log_type,
                  allocated: log.allocation_person_full_name,
                  schd_date: log.schd_date
                };
              }

              const coords = globalAddressCache.current.get(log.log_address);
              if (coords) {
                return {
                  id: log.local_log_id,
                  log_header_id: log.log_header_id,
                  address: log.log_address,
                  description: log.description,
                  latitude: coords.lat,
                  longitude: coords.lng,
                  log_type: log.log_type,
                  allocated: log.allocation_person_full_name,
                  schd_date: log.schd_date
                };
              }
              return null;
            })
            .filter((marker): marker is LogMarker => marker !== null) as LogMarker[];

          const groups = groupMarkersByLocation(resolvedMarkers);
          setMarkerGroups(groups);
          setFlattedMarkers(resolvedMarkers);
        }
      } catch (error) {
        console.error('Error handling region change:', error);
      }
    }, 1000),
    [flattedMarkers]
  );

  const formatDate = (dateString: string | undefined) => {
    if (!dateString) return 'No date';
    const date = new Date(dateString);
    return date.toLocaleDateString();
  };

  const groupMarkersByLocation = (markers: LogMarker[]): MarkerGroup[] => {
    if (!markers) return [];

    const groups = new Map<string, LogMarker[]>();

    // 按位置分组标记
    markers.forEach(marker => {
      // 使用精度为5位小数的经纬度作为键
      const key = `${marker.latitude.toFixed(5)},${marker.longitude.toFixed(5)}`;
      if (!groups.has(key)) {
        groups.set(key, []);
      }
      groups.get(key)!.push(marker);
    });

    // 转换为数组格式
    return Array.from(groups.entries()).map(([id, markers]) => ({
      id,
      markers,
      currentIndex: 0,
    }));
  };

  return (
    <Box flex={1} bg={mode === 'dark' ? 'black' : 'white'}>
      {loading ? (
        <Spinner size="lg" />
      ) : (
        <MapView
          ref={mapRef}
          style={StyleSheet.absoluteFillObject}
          region={region || undefined}
          showsUserLocation={true}
          showsMyLocationButton={true}
          showsCompass={true}
          showsScale={true}
          onRegionChangeComplete={handleRegionChange}
          onPress={() => setSelectedMarker(null)}
        >
          {markerGroups.map(group => {
            const currentMarker = group.markers[group.currentIndex];
            return (
              <Marker
                key={group.id}
                coordinate={{
                  latitude: currentMarker.latitude,
                  longitude: currentMarker.longitude,
                }}
                pinColor={currentMarker.isHighlighted ? 'red' : undefined}
                onPress={() => {
                  // 点击marker时选中/取消选中
                  setSelectedMarker(prevSelected =>
                    prevSelected === group.id ? null : group.id
                  );
                }}
              />
            );
          })}
        </MapView>
      )}

      {selectedMarker && (
        <Box
          position="absolute"
          bottom={100} // 位置可以调整
          left={20}
          right={20}
          bg={mode === 'dark' ? 'gray.800' : 'white'}
          borderRadius="md"
          p={4}
          shadow={5}
        >
          <IconButton
            position="absolute"
            top={2}
            right={2}
            size="sm"
            onPress={() => setSelectedMarker(null)}
            _pressed={{
              bg: mode === 'dark' ? 'gray.700' : 'gray.200'
            }}
          />
          {(() => {
            // 找到当前选中的marker组
            const group = markerGroups.find(g => g.id === selectedMarker)!;
            const currentMarker = group.markers[group.currentIndex];

            return (
              <VStack space={2}>
                <HStack justifyContent="space-between" alignItems="center">
                  <Text fontSize="md" fontWeight="bold" color="orange.500">
                    Log #{currentMarker.log_header_id}
                  </Text>

                  {group.markers.length > 1 && (
                    <HStack alignItems="center" space={2}>
                      <IconButton
                        size="xs"
                        icon={<Icon
                          as={MaterialCommunityIcons}
                          name="chevron-left"
                          size="xs"
                          color="orange.500"
                        />}
                        onPress={() => navigateMarker(group.id, 'prev')}
                      />

                      <Text fontSize="xs" color="gray.500">
                        {group.currentIndex + 1}/{group.markers.length}
                      </Text>

                      <IconButton
                        size="xs"
                        icon={<Icon
                          as={MaterialCommunityIcons}
                          name="chevron-right"
                          size="xs"
                          color="orange.500"
                        />}
                        onPress={() => navigateMarker(group.id, 'next')}
                      />
                    </HStack>
                  )}

                </HStack>

                <VStack space={0}>
                  <HStack justifyContent="space-between" alignItems="center">
                    <Text fontSize="sm" fontWeight="bold">Description:</Text>
                    <IconButton
                      size="sm"
                      icon={<Icon
                        as={MaterialCommunityIcons}
                        name="arrow-right-circle"
                        size="xl"
                        color="orange.600"
                      />}
                      onPress={() => handleMarkerPress(currentMarker)}
                    />
                  </HStack>
                  <Text fontSize="sm">{currentMarker.description?.replace(/"/g, '') || ''}</Text>

                  <Text fontSize="sm" fontWeight="bold">Address:</Text>
                  <Text fontSize="sm">{currentMarker.address}</Text>

                  <Text fontSize="sm" fontWeight="bold">Scheduled Date:</Text>
                  <Text fontSize="sm">{formatDate(currentMarker.schd_date)}</Text>

                  <Text fontSize="sm" fontWeight="bold">Log Type:</Text>
                  <Text fontSize="sm">{currentMarker.log_type || 'N/A'}</Text>

                  <Text fontSize="sm" fontWeight="bold">Allocated:</Text>
                  <Text fontSize="sm">{currentMarker.allocated || 'N/A'}</Text>
                </VStack>
              </VStack>
            );
          })()}
        </Box>
      )}

      {/* Date Filter Box */}
      <Box
        position="absolute"
        top={2}
        left={2}
        bg={mode === 'dark' ? 'gray.800:alpha.70' : 'white:alpha.70'}
        borderRadius="sm"
        p={0.5}
        shadow={1}
      >
        <Select
          size="xs"
          width="90px"
          height="34px"
          selectedValue={dateFilter.range}
          accessibilityLabel="Choose Date Range"
          fontSize="2xs"
          dropdownIcon={
            <Icon as={MaterialCommunityIcons} name="chevron-down" size={3} />
          }
          onValueChange={(itemValue) => updateDateFilterDates(itemValue)}
          _selectedItem={{
            bg: "orange.500"
          }}
        >
          <Select.Item label="Today" value="today" />
          <Select.Item label="Last 3 Days" value="3days" />
          <Select.Item label="Last Week" value="week" />
          <Select.Item label="Last Month" value="month" />
          <Select.Item label="All Logs" value="all" />
        </Select>
      </Box>

      <Box
        position="absolute"
        bottom={4}
        left={10}
        right={10}
        bg={mode === 'dark' ? 'gray.800' : 'white'}
        borderRadius="full"
        p={2}
        shadow={3}>
        <HStack alignItems="center" space={2}>
          <Input
            placeholder="Search location"
            flex={1}
            value={searchQuery}
            onChangeText={setSearchQuery}
            onSubmitEditing={handleSearch}
            borderWidth={0}
            fontSize="md"
            height={8}
            color={mode === 'dark' ? 'white' : 'black'}
            _focus={{
              borderWidth: 0,
              backgroundColor: 'transparent',
            }}
          />
          <IconButton
            icon={
              <Icon
                as={MaterialCommunityIcons}
                name="magnify"
                size={5}
                color={mode === 'dark' ? 'white' : 'black'}
              />
            }
            onPress={handleSearch}
            variant="ghost"
            _pressed={{
              bg: mode === 'dark' ? 'gray.700' : 'gray.100'
            }}
          />
        </HStack>
      </Box>
    </Box>
  );
};

export default MapMarkerScreen;
