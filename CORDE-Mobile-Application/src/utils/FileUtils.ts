import RNFS from 'react-native-fs';
import { Platform } from 'react-native';
import {StoredFiles} from "../database/StoredFiles.ts";
import {uploadFile} from "../api/FileApi.ts";

export class FileUtils {
  // The static method saveFile receives a file URI and filename to copy the file from its original location to the appropriate directory
  // iOS's documents directory or Android's external directory
  static async saveFile(fileUri: string, fileName: string): Promise<string> {
    const directory = Platform.OS === 'ios' ? RNFS.DocumentDirectoryPath : RNFS.ExternalDirectoryPath;
    const filePath = `${directory}/${fileName}`;

    try {
      // Use the copyFile method to copy a file from the fileUri to a new filePath.
      await RNFS.copyFile(fileUri, filePath);
      console.log('File saved successfully:', filePath);
      return filePath;
    } catch (error) {
      console.error('Error saving file:', error);
      throw error;
    }
  }

  static async deleteFile(filePath: string): Promise<void> {
    try {
      // Use the unlink method to delete the file at the specified path.
      await RNFS.unlink(filePath);
      console.log('File deleted successfully:', filePath);
    } catch (error) {
      console.error('Error deleting file:', error);
      throw error;
    }
  }

  static async readFile(filePath: string): Promise<string> {
    try {
      // Use the readFile method to read the contents of a file in Base64 encoding.
      const content = await RNFS.readFile(filePath, 'base64');
      console.log(`File read successfully: ${filePath}`);
      return content;
    } catch (error) {
      console.error('Error reading file:', error);
      throw error;
    }
  }

  // The static method getFileExtension takes a filename and returns the file extension.
  static getFileExtension(fileName: string): string {
    // Use string manipulation to find the last dot (.) The text after that is used as the extension.
    return fileName.slice((fileName.lastIndexOf(".") - 1 >>> 0) + 2);
  }

  static getFileName(filePath: string): string {
    // Use string manipulation to find the text after the last slash (/) in the path as the filename.
    return filePath.split('/').pop() || '';
  }

  static async getFileInfo(filePath: string): Promise<{
    uri: string;
    type: string;
    name: string;
  }> {
    try {
      // 在 Android 中，我们需要添加 file:// 前缀
      const uri = Platform.OS === 'android' ?
        `file://${filePath}` : filePath;

      const fileInfo = await RNFS.stat(filePath);
      const fileName = filePath.split('/').pop() || '';

      // 根据文件扩展名推断MIME类型
      const extension = fileName.split('.').pop()?.toLowerCase() || '';
      const mimeType = this.getMimeType(extension);

      return {
        uri: uri,
        type: mimeType,
        name: fileName
      };
    } catch (error) {
      console.error('Error getting file info:', error);
      throw error;
    }
  }

  private static getMimeType(extension: string): string {
    const mimeTypes = {
      'jpg': 'image/jpeg',
      'jpeg': 'image/jpeg',
      'png': 'image/png',
      'pdf': 'application/pdf',
      // 可以根据需要添加更多类型
    };
    return mimeTypes[extension] || 'application/octet-stream';
  }

  // File upload helper method
  public static async uploadFiles(
    localLogId: number,
    logHeaderId: number,
  ): Promise<void> {
    try {
      // 获取本地文件记录
      const files = await StoredFiles.getByLocalLogId(localLogId);
      console.log(`Found ${files.length} files to upload for log ${localLogId}`);

      for (const file of files) {
        try {
          console.log(`Processing file for upload:`, {
            fileName: file.file_name,
            filePath: file.file_path
          });

          const fileInfo = await FileUtils.getFileInfo(file.file_path);
          console.log('File info retrieved:', fileInfo);

          const uploadResponse = await uploadFile(fileInfo, logHeaderId.toString());
          console.log('Upload completed:', {
            fileName: file.file_name,
            storedFileId: uploadResponse.StoredFileId,
            logHeaderId
          });

          await StoredFiles.updateStoredFileIdAndLogHeadId(
            file.id,
            uploadResponse.StoredFileId,
            logHeaderId
          );
          console.log('Database updated for file:', {
            fileId: file.id,
            storedFileId: uploadResponse.StoredFileId,
            logHeaderId
          });
        } catch (error) {
          console.error(`Error uploading file ${file.file_name}:`, error);
          // 继续处理其他文件，而不是完全中断
        }
      }
    } catch (error) {
      console.error('Error in uploadFiles:', error);
      throw error;
    }
  }

}
