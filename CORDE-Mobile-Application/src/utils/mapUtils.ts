import {LatLng} from 'react-native-maps';

// The Marker interface inherits from LatLng, a common type of geographic coordinate that includes latitude and longitude.
// Marker also includes id and description, which identifies and describes each marker point.
interface Marker extends LatLng {
  id: number;
  description: string;
}

// The clusterMarkers function takes an array of Markers and a distance parameter (default 50 metres)
// to control which markers should be clustered.
export function clusterMarkers(
  markers: Marker[],
  distance: number = 50,
): Marker[] {
  const clustered: Marker[] = [];

  markers.forEach(marker => {
    let foundCluster = false;
    for (let i = 0; i < clustered.length; i++) {
      // If the distance between the current marker and one of the markers in the clustered is less than or equal to distance,
      // then they are aggregated into a new marker, with the position being the average of the two, and the description updated to the number of aggregations.
      if (calculateDistance(marker, clustered[i]) <= distance) {
        clustered[i] = {
          ...clustered[i],
          latitude: (clustered[i].latitude + marker.latitude) / 2,
          longitude: (clustered[i].longitude + marker.longitude) / 2,
          description: `Cluster of ${
            parseInt(clustered[i].description.split(' ')[2]) + 1
          } logs`,
        };
        foundCluster = true;
        break;
      }
    }
    // If the current marker is not aggregated, add it to the clustered array.
    if (!foundCluster) {
      clustered.push({...marker, description: 'Cluster of 1 log'});
    }
  });

  return clustered;
}

// Calculate the distance on the Earth's surface between two points.
function calculateDistance(a: LatLng, b: LatLng): number {
  const R = 6371e3; // Earth's radius in meters
  const φ1 = (a.latitude * Math.PI) / 180;
  const φ2 = (b.latitude * Math.PI) / 180;
  const Δφ = ((b.latitude - a.latitude) * Math.PI) / 180;
  const Δλ = ((b.longitude - a.longitude) * Math.PI) / 180;

  const x =
    Math.sin(Δφ / 2) * Math.sin(Δφ / 2) +
    Math.cos(φ1) * Math.cos(φ2) * Math.sin(Δλ / 2) * Math.sin(Δλ / 2);
  const c = 2 * Math.atan2(Math.sqrt(x), Math.sqrt(1 - x));

  return R * c;
}
