import NetInfo, {
  NetInfoState,
  NetInfoSubscription,
} from '@react-native-community/netinfo';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Key for storing manual offline mode setting
const MANUAL_OFFLINE_KEY = 'manualOfflineMode';

export class NetworkService {
  private static instance: NetworkService;
  private isConnected: boolean = false;
  private listeners: ((isConnected: boolean) => void)[] = [];
  private unsubscribe: NetInfoSubscription | null = null;
  private initializationPromise: Promise<void>;

  // Add a property to track manual offline mode
  private isManualOfflineMode: boolean = false;

  // Add a property to track current screen
  private currentScreen: string = '';

  private constructor() {
    this.initializationPromise = this.initNetInfo();
    this.loadManualOfflineMode();
  }

  public static getInstance(): NetworkService {
    if (!NetworkService.instance) {
      NetworkService.instance = new NetworkService();
    }
    return NetworkService.instance;
  }

  private async initNetInfo(): Promise<void> {
    return new Promise<void>(resolve => {
      this.unsubscribe = NetInfo.addEventListener(this.handleNetInfoChange);
      NetInfo.fetch().then(state => {
        this.handleNetInfoChange(state);
        resolve();
      });
    });
  }

  private handleNetInfoChange = (state: NetInfoState) => {
    const connected = state.isConnected && state.isInternetReachable;
    if (this.isConnected !== connected) {
      this.isConnected = connected;
      this.notifyListeners();
    }
  };

  public addListener(listener: (isConnected: boolean) => void) {
    this.listeners.push(listener);
  }

  public removeListener(listener: (isConnected: boolean) => void) {
    this.listeners = this.listeners.filter(l => l !== listener);
  }

  private notifyListeners() {
    // When notifying listeners, consider both actual connectivity and manual offline mode
    const effectiveConnectivity = this.isConnected && !this.isManualOfflineMode;
    this.listeners.forEach(listener => listener(effectiveConnectivity));
  }

  // Load manual offline mode setting from AsyncStorage
  private async loadManualOfflineMode() {
    try {
      const savedMode = await AsyncStorage.getItem(MANUAL_OFFLINE_KEY);
      this.isManualOfflineMode = savedMode === 'true';
    } catch (error) {
      console.error('Error loading offline mode setting:', error);
    }
  }

  // Method to set manual offline mode
  public async setManualOfflineMode(value: boolean) {
    if (this.isManualOfflineMode !== value) {
      this.isManualOfflineMode = value;
      try {
        await AsyncStorage.setItem(MANUAL_OFFLINE_KEY, String(value));
        // Notify listeners when manual offline mode changes
        this.notifyListeners();
      } catch (error) {
        console.error('Error saving offline mode setting:', error);
      }
    }
  }

  // Get current manual offline mode state
  public getManualOfflineMode(): boolean {
    return this.isManualOfflineMode;
  }

  // Set current screen name
  public setCurrentScreen(screenName: string) {
    this.currentScreen = screenName;
  }

  // The modified isNetworkConnected method considering manual offline mode
  public async isNetworkConnected(): Promise<boolean> {
    await this.initializationPromise;

    // Check if we're on the Map Marker screen
    const isMapScreen = this.currentScreen === 'Map Marker';

    // If we're on the map screen, ignore manual offline mode
    if (isMapScreen) {
      return this.isConnected;
    }

    // For other screens, consider both actual connectivity and manual offline mode
    return this.isConnected && !this.isManualOfflineMode;
  }

  public destroy() {
    if (this.unsubscribe) {
      this.unsubscribe();
    }
    this.listeners = [];
  }
}

export const networkService = NetworkService.getInstance();
