import {format, fromZonedTime, toZonedTime} from 'date-fns-tz';
import {enNZ} from "date-fns/locale";

const timeZone = 'Pacific/Auckland';

export const formatDate = (date: Date): string => {
  // Convert UTC to Auckland timezone
  const zonedDate = toZonedTime(date, timeZone);
  return format(zonedDate, 'yyyy/MM/dd', {timeZone});
};

export const formatTime = (date: Date): string => {
  // Convert UTC to Auckland timezone
  const zonedTime = toZonedTime(date, timeZone);
  return format(zonedTime, 'HH:mm', {timeZone});
};

export const fromZonedTimeToUTC = (date: Date): Date =>
  fromZonedTime(date, timeZone);


export const formatCurrentDateTime = (): string => {
  const now = new Date();
  return format(now, 'yyyyMMddHHmmss', { timeZone });
};

export const formatDateToNZ = (dateString: string): string => {
  const date = new Date(dateString);
  return format(date, 'dd/MM/yyyy HH:mm:ss', { locale: enNZ });
};

// ===== New Zealand Timezone Filter Utilities =====

export interface DateRange {
  startDate?: Date;
  endDate?: Date;
}

/**
 * Get current New Zealand timezone offset in hours
 * Returns +12 or +13 depending on daylight saving time
 */
export const getNZOffsetHours = (): number => {
  try {
    const now = new Date();
    // Use Intl.DateTimeFormat API to get New Zealand timezone info
    const nzTimeFormatter = new Intl.DateTimeFormat('en-US', {
      timeZone: 'Pacific/Auckland',
      timeZoneName: 'short'
    });

    // Get formatted date string with timezone info
    const formattedDate = nzTimeFormatter.format(now);
    console.log('NZ Formatted date:', formattedDate);

    // Extract GMT offset from formatted string, like GMT+12 or GMT+13
    const match = formattedDate.match(/GMT([+-]\d+)/);
    if (match && match[1]) {
      // Convert offset to number
      return parseInt(match[1], 10);
    }

    // Fallback if string matching fails
    console.warn('Could not extract timezone offset, using default +12');
    return 12;
  } catch (error) {
    console.error('Error calculating NZ offset:', error);
    // Default value on error
    return 12;
  }
};

/**
 * Adjust a date to New Zealand timezone for database queries
 * This prevents timezone conversion issues where user selects day 29 but query uses day 28
 *
 * @param date - The date to adjust
 * @param isEndOfDay - If true, sets time to 23:59:59.999, otherwise 00:00:00
 * @returns Adjusted date object
 */
export const adjustDateForNZTimezone = (date: Date, isEndOfDay: boolean = false): Date => {
  const adjustedDate = new Date(date);

  if (isEndOfDay) {
    // Set to end of day (23:59:59.999)
    adjustedDate.setHours(23, 59, 59, 999);
  } else {
    // Set to start of day (00:00:00)
    adjustedDate.setHours(0, 0, 0, 0);
  }

  // Get current NZ timezone offset
  const hourOffset = getNZOffsetHours();
  console.log(`Current hour offset for NZ: UTC+${hourOffset}`);

  // Directly add timezone difference to make UTC time value equal to NZ time
  adjustedDate.setHours(adjustedDate.getHours() + hourOffset);

  console.log(`Adjusted date (${isEndOfDay ? 'end' : 'start'} of day):`, adjustedDate.toISOString());

  return adjustedDate;
};

/**
 * Process date range for New Zealand timezone
 * Adjusts both start and end dates to prevent timezone offset issues
 *
 * @param dateRange - Object containing startDate and/or endDate
 * @returns Processed date range with timezone adjustments
 */
export const processDateRangeForNZ = (dateRange: DateRange): DateRange => {
  const processed: DateRange = {};

  if (dateRange.startDate instanceof Date) {
    processed.startDate = adjustDateForNZTimezone(dateRange.startDate, false);
  }

  if (dateRange.endDate instanceof Date) {
    processed.endDate = adjustDateForNZTimezone(dateRange.endDate, true);
  }

  return processed;
};

/**
 * Process filters object with date fields for New Zealand timezone
 * Generic function that can handle any object with date properties
 *
 * @param filters - Object containing date fields
 * @param dateFields - Array of field names that contain dates to process
 * @returns Processed filters with timezone-adjusted dates
 */
export const processFiltersForNZ = <T extends Record<string, any>>(
  filters: T,
  dateFields: { field: keyof T; isEndDate?: boolean }[]
): T => {
  const processed = { ...filters };

  dateFields.forEach(({ field, isEndDate = false }) => {
    const fieldValue = processed[field];
    if (fieldValue instanceof Date) {
      processed[field] = adjustDateForNZTimezone(fieldValue, isEndDate);
    } else if (typeof fieldValue === 'string' && fieldValue) {
      // Handle string dates by converting to Date first
      const dateObj = new Date(fieldValue);
      if (!isNaN(dateObj.getTime())) {
        processed[field] = adjustDateForNZTimezone(dateObj, isEndDate);
      }
    }
  });

  return processed;
};

/**
 * Format date for display in New Zealand format
 * @param date - Date to format
 * @returns Formatted date string (DD/MM/YYYY)
 */
export const formatScheduleDate = (date: Date | string | undefined): string => {
  if (!date) return 'No date';

  try {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    return dateObj.toLocaleDateString('en-NZ', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  } catch (error) {
    return 'Invalid date';
  }
};
