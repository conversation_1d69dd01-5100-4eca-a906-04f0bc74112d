import Clipboard from '@react-native-clipboard/clipboard';
import { useToast } from 'native-base';

export const useCopyToClipboard = () => {
  const toast = useToast();

  const copyToClipboard = (text: string) => {
    Clipboard.setString(text);
    toast.show({
      title: "Copied to clipboard",
      status: "success",
      placement: "top",
      duration: 2000
    });
  };

  return copyToClipboard;
};
