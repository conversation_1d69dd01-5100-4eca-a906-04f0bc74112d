# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
water-meter-ai-env/
water/
*.egg-info/
dist/
build/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS files
# macOS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
# Windows
Thumbs.db
Desktop.ini
$RECYCLE.BIN/
# Linux
*~
.fuse_hidden*
.directory
.Trash-*

# Training outputs
data/yolo_training_dataset/runs/
*.pt
*.onnx

# Large datasets (optional - you may want to include some sample data)
data/raw_images/
data/yolo_dataset/images/

# Logs
logs/
*.log

# API Keys and secrets
config/ai_config.json
.env
*.key

# Temporary files
*.tmp
*.cache

# Model weights (too large for git)
weights/
models/
*.weights

# YOLO cache files
*.cache 