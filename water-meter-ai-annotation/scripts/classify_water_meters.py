#!/usr/bin/env python3
"""
Script to classify water meter brands and models
"""
import os
import sys
import json
import argparse
from pathlib import Path
import shutil

# Add src to path
sys.path.append(str(Path(__file__).parent.parent / 'src'))

from utils.water_meter_classifier import WaterMeterClassifier


def print_classification_report(results):
    """Print formatted classification report"""
    print("\n" + "="*70)
    print("WATER METER CLASSIFICATION REPORT")
    print("="*70)
    
    print(f"Total images: {results['total_images']}")
    print(f"Valid images: {results['valid_images']}")
    print(f"Suitable for training: {results['suitable_for_training']}")
    print(f"Dominant brand: {results['dominant_brand']}")
    print(f"Dominant model type: {results['dominant_model_type']}")
    
    print("\nBRAND DISTRIBUTION:")
    print("-" * 30)
    for brand, count in results['brand_distribution'].items():
        percentage = (count / results['total_images']) * 100
        print(f"{brand.upper():<15} {count:>3} ({percentage:>5.1f}%)")
    
    print("\nMODEL TYPE DISTRIBUTION:")
    print("-" * 30)
    for model_type, count in results['model_type_distribution'].items():
        percentage = (count / results['total_images']) * 100
        print(f"{model_type.upper():<15} {count:>3} ({percentage:>5.1f}%)")
    
    print("\nDETAILED RESULTS:")
    print("-" * 85)
    print(f"{'Filename':<25} {'Brand':<10} {'Type':<8} {'Conf':<6} {'Recommendation'}")
    print("-" * 85)
    
    for result in results['results']:
        if not result['is_valid']:
            print(f"{Path(result['image_path']).name:<25} {'ERROR':<10} {'N/A':<8} {'0.00':<6} {result.get('error', 'Unknown error')}")
            continue
        
        filename = Path(result['image_path']).name
        brand = result['brand'].upper()
        model_type = result['model_type'].upper()
        confidence = f"{result['confidence']:.2f}"
        recommendation = result['recommendation']
        
        print(f"{filename:<25} {brand:<10} {model_type:<8} {confidence:<6} {recommendation}")


def create_brand_directories(results, input_dir, output_base_dir):
    """Create directories organized by brand"""
    brand_dirs = {}
    
    for brand in results['brand_distribution'].keys():
        brand_dir = Path(output_base_dir) / f"{brand}_meters"
        brand_dir.mkdir(parents=True, exist_ok=True)
        brand_dirs[brand] = brand_dir
    
    # Create special directories
    suitable_dir = Path(output_base_dir) / "suitable_for_training"
    unsuitable_dir = Path(output_base_dir) / "unsuitable_for_training"
    suitable_dir.mkdir(parents=True, exist_ok=True)
    unsuitable_dir.mkdir(parents=True, exist_ok=True)
    
    brand_counts = {}
    suitable_count = 0
    unsuitable_count = 0
    
    for result in results['results']:
        if not result['is_valid']:
            continue
        
        source_path = Path(result['image_path'])
        brand = result['brand']
        
        # Copy to brand directory
        brand_dest = brand_dirs[brand] / source_path.name
        shutil.copy2(source_path, brand_dest)
        brand_counts[brand] = brand_counts.get(brand, 0) + 1
        
        # Copy to suitable/unsuitable directory
        is_suitable = result.get('training_compatibility', {}).get('is_suitable', False)
        if is_suitable:
            suitable_dest = suitable_dir / source_path.name
            shutil.copy2(source_path, suitable_dest)
            suitable_count += 1
        else:
            unsuitable_dest = unsuitable_dir / source_path.name
            shutil.copy2(source_path, unsuitable_dest)
            unsuitable_count += 1
    
    print(f"\nORGANIZED RESULTS:")
    print("=" * 40)
    print(f"Suitable for training: {suitable_count} images")
    print(f"Unsuitable for training: {unsuitable_count} images")
    
    print(f"\nBRAND-SPECIFIC DIRECTORIES:")
    for brand, count in brand_counts.items():
        print(f"  {brand.upper()}: {count} images → {brand_dirs[brand]}")
    
    print(f"\nTRAINING-READY DIRECTORIES:")
    print(f"  SUITABLE: {suitable_count} images → {suitable_dir}")
    print(f"  UNSUITABLE: {unsuitable_count} images → {unsuitable_dir}")


def generate_training_recommendations(results):
    """Generate specific training recommendations"""
    print("\n" + "="*60)
    print("TRAINING STRATEGY RECOMMENDATIONS")
    print("="*60)
    
    total_images = results['total_images']
    suitable_images = results['suitable_for_training']
    brand_counts = results['brand_distribution']
    dominant_brand = results['dominant_brand']
    
    # Strategy based on data composition
    sensus_count = brand_counts.get('sensus', 0)
    other_brands_count = total_images - sensus_count
    
    print(f"Data Composition:")
    print(f"  Sensus meters: {sensus_count} ({sensus_count/total_images*100:.1f}%)")
    print(f"  Other brands: {other_brands_count} ({other_brands_count/total_images*100:.1f}%)")
    print(f"  Suitable for training: {suitable_images} ({suitable_images/total_images*100:.1f}%)")
    
    print(f"\nRECOMMENDED STRATEGY:")
    
    if sensus_count >= total_images * 0.8:
        print("📊 SINGLE-BRAND STRATEGY (Recommended)")
        print("   • High Sensus concentration detected")
        print("   • Train a specialized Sensus model")
        print("   • Expected accuracy: 95%+")
        print("   • Faster training, less data needed")
        print("   • Use only Sensus images for training")
        
    elif sensus_count >= total_images * 0.6:
        print("📊 HYBRID STRATEGY (Recommended)")
        print("   • Moderate Sensus concentration")
        print("   • Train primary Sensus model + secondary general model")
        print("   • Expected accuracy: 90%+ for Sensus, 85%+ for others")
        print("   • Separate training datasets by brand")
        
    elif sensus_count >= total_images * 0.4:
        print("📊 MULTI-BRAND STRATEGY (Recommended)")
        print("   • Mixed brand distribution")
        print("   • Train a general water meter model")
        print("   • Expected accuracy: 85%+")
        print("   • Higher generalization ability")
        print("   • Use all suitable images for training")
        
    else:
        print("📊 BRAND-SPECIFIC STRATEGY (Recommended)")
        print("   • Low Sensus concentration")
        print("   • Train separate models for each major brand")
        print("   • Expected accuracy: 90%+ per brand")
        print("   • Requires more training time")
        
    print(f"\nACTION ITEMS:")
    print("1. Review unsuitable images - can quality be improved?")
    print("2. Consider collecting more data for minority brands")
    print("3. Validate brand classifications manually")
    print("4. Decide on single-brand vs multi-brand approach")
    
    if suitable_images < 50:
        print("⚠️  WARNING: Less than 50 suitable images for training")
        print("   Consider collecting more data or lowering quality thresholds")


def main():
    parser = argparse.ArgumentParser(description='Classify water meter brands and models')
    parser.add_argument('--input', '-i', required=True, help='Input directory with images')
    parser.add_argument('--output', '-o', help='Output JSON file for results')
    parser.add_argument('--organize', action='store_true', help='Create organized directories by brand')
    parser.add_argument('--output-dir', default='data/classified_meters', help='Output directory for organized results')
    
    args = parser.parse_args()
    
    if not os.path.exists(args.input):
        print(f"Error: Input directory does not exist: {args.input}")
        sys.exit(1)
    
    print(f"Classifying water meters in: {args.input}")
    
    # Initialize classifier
    classifier = WaterMeterClassifier()
    
    # Classify all images
    print("Analyzing images...")
    results = classifier.classify_batch(args.input, args.output)
    
    # Print report
    print_classification_report(results)
    
    # Generate training recommendations
    generate_training_recommendations(results)
    
    # Save results if requested
    if args.output:
        print(f"\nDetailed results saved to: {args.output}")
    
    # Create organized directories if requested
    if args.organize:
        create_brand_directories(results, args.input, args.output_dir)
    
    print(f"\nCLASSIFICATION COMPLETE!")
    print("=" * 40)
    print("Next steps:")
    print("1. Review the recommendations above")
    print("2. Decide on training strategy (single-brand vs multi-brand)")
    print("3. Use suitable images for AI annotation")
    print("4. Consider manual verification of uncertain classifications")


if __name__ == '__main__':
    main() 