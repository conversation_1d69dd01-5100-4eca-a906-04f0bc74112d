#!/usr/bin/env python3
"""
JSON to YOLO Format Converter
Converts water meter AI annotations from JSON format to YOLO training format
"""

import json
import os
from pathlib import Path

def convert_bbox_to_yolo(bbox):
    """
    Convert bbox from JSON format to YOLO format
    JSON format: {"x": left, "y": top, "width": w, "height": h}
    YOLO format: center_x center_y width height (all normalized 0-1)
    """
    x = bbox["x"]
    y = bbox["y"]
    width = bbox["width"]
    height = bbox["height"]
    
    # Convert to center coordinates
    center_x = x + width / 2
    center_y = y + height / 2
    
    return center_x, center_y, width, height

def get_class_id(annotation_type):
    """
    Map annotation type to YOLO class ID
    """
    class_mapping = {
        "digital_reading": 0,    # Black digits (main reading)
        "decimal_reading": 1,    # Red digits (decimal part)
        "serial_number": 2       # Device serial number
    }
    return class_mapping.get(annotation_type, -1)

def convert_json_to_yolo(json_file_path, output_dir):
    """
    Convert JSON annotations to YOLO format
    """
    # Create output directory if it doesn't exist
    output_dir = Path(output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # Read JSON file
    with open(json_file_path, 'r', encoding='utf-8') as f:
        annotations_data = json.load(f)
    
    print(f"Processing {len(annotations_data)} images...")
    
    # Process each image annotation
    for i, image_data in enumerate(annotations_data):
        # Generate filename (assuming meter_01.txt, meter_02.txt, etc.)
        output_filename = f"meter_{i+1:02d}.txt"
        output_path = output_dir / output_filename
        
        yolo_lines = []
        
        # Process each annotation in the image
        for annotation in image_data["annotations"]:
            annotation_type = annotation["type"]
            bbox = annotation["bbox"]
            
            # Get class ID
            class_id = get_class_id(annotation_type)
            if class_id == -1:
                print(f"Warning: Unknown annotation type '{annotation_type}' in image {i+1}")
                continue
            
            # Convert bbox to YOLO format
            center_x, center_y, width, height = convert_bbox_to_yolo(bbox)
            
            # Create YOLO line: class_id center_x center_y width height
            yolo_line = f"{class_id} {center_x:.6f} {center_y:.6f} {width:.6f} {height:.6f}"
            yolo_lines.append(yolo_line)
            
            # Debug info
            print(f"  {output_filename}: {annotation_type} -> class {class_id}")
        
        # Write YOLO annotation file
        with open(output_path, 'w') as f:
            f.write('\n'.join(yolo_lines) + '\n')
        
        print(f"Created: {output_path} ({len(yolo_lines)} annotations)")
    
    # Create classes.txt file
    classes_file = output_dir / "classes.txt"
    with open(classes_file, 'w') as f:
        f.write("digital_reading\n")
        f.write("decimal_reading\n")  
        f.write("serial_number\n")
    
    print(f"\nConversion completed!")
    print(f"Output directory: {output_dir}")
    print(f"Created {len(annotations_data)} YOLO annotation files")
    print(f"Class mapping:")
    print("  0: digital_reading (black digits)")
    print("  1: decimal_reading (red digits)")
    print("  2: serial_number")
    print(f"Classes definition saved to: {classes_file}")

def main():
    # File paths
    json_file = "data/ai_annotations/water_meter_full_batch_annotations.json"
    output_dir = "data/yolo_dataset/labels"
    
    # Convert relative paths to absolute
    script_dir = Path(__file__).parent.parent  # Go up one level from scripts/
    json_path = script_dir / json_file
    output_path = script_dir / output_dir
    
    if not json_path.exists():
        print(f"Error: JSON file not found: {json_path}")
        print("Please make sure the file exists and run from the project root directory")
        return
    
    print("JSON to YOLO Converter")
    print("=" * 30)
    print(f"Input JSON: {json_path}")
    print(f"Output dir: {output_path}")
    print()
    
    convert_json_to_yolo(json_path, output_path)

if __name__ == "__main__":
    main() 