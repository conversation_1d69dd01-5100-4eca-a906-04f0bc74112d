#!/usr/bin/env python3
"""
YOLO Dataset Preparation Script
Prepare water meter dataset for YOLO training
"""

import os
import shutil
import random
from pathlib import Path

def create_yolo_structure(base_dir):
    """Create standard YOLO dataset directory structure"""
    base_dir = Path(base_dir)
    
    # Create directories
    dirs_to_create = [
        "train/images",
        "train/labels", 
        "val/images",
        "val/labels"
    ]
    
    for dir_path in dirs_to_create:
        (base_dir / dir_path).mkdir(parents=True, exist_ok=True)
        print(f"Created: {base_dir / dir_path}")

def split_dataset(images_dir, labels_dir, output_dir, train_ratio=0.78):
    """Split dataset into train/val sets"""
    images_dir = Path(images_dir)
    labels_dir = Path(labels_dir)
    output_dir = Path(output_dir)
    
    # Get all image files
    image_files = list(images_dir.glob("*.jpg")) + list(images_dir.glob("*.png"))
    image_files.sort()  # Ensure consistent ordering
    
    print(f"Found {len(image_files)} images")
    
    # Split files
    random.seed(42)  # For reproducible splits
    random.shuffle(image_files)
    
    train_count = int(len(image_files) * train_ratio)
    train_files = image_files[:train_count]
    val_files = image_files[train_count:]
    
    print(f"Train set: {len(train_files)} images")
    print(f"Val set: {len(val_files)} images")
    
    # Copy files to train/val directories
    for split, files in [("train", train_files), ("val", val_files)]:
        for image_file in files:
            # Image file
            dest_image = output_dir / split / "images" / image_file.name
            shutil.copy2(image_file, dest_image)
            
            # Corresponding label file
            label_name = image_file.stem + ".txt"
            label_file = labels_dir / label_name
            dest_label = output_dir / split / "labels" / label_name
            
            if label_file.exists():
                shutil.copy2(label_file, dest_label)
                print(f"  {split}: {image_file.name} + {label_name}")
            else:
                print(f"  Warning: No label file for {image_file.name}")

def create_dataset_yaml(output_dir, num_classes=3):
    """Create YOLO dataset configuration file"""
    output_dir = Path(output_dir)
    
    # Read class names from classes.txt
    classes_file = output_dir.parent / "labels" / "classes.txt"
    if classes_file.exists():
        with open(classes_file, 'r') as f:
            class_names = [line.strip() for line in f if line.strip()]
    else:
        class_names = ["digital_reading", "decimal_reading", "serial_number"]
    
    yaml_content = f"""# Water Meter Dataset Configuration
# Generated automatically for YOLO training

# Dataset paths (relative to this file)
path: .  # dataset root dir
train: train/images  # train images
val: val/images      # val images

# Classes
nc: {len(class_names)}  # number of classes
names: {class_names}  # class names

# Additional info
description: "Water meter reading detection dataset"
author: "AI Annotation System"
version: "1.0"
"""
    
    yaml_file = output_dir / "dataset.yaml"
    with open(yaml_file, 'w') as f:
        f.write(yaml_content)
    
    print(f"Created dataset configuration: {yaml_file}")
    return yaml_file

def main():
    # Paths
    base_dir = Path(__file__).parent.parent
    images_dir = base_dir / "data/yolo_dataset/images"
    labels_dir = base_dir / "data/yolo_dataset/labels"
    output_dir = base_dir / "data/yolo_training_dataset"
    
    print("YOLO Dataset Preparation")
    print("=" * 30)
    print(f"Images source: {images_dir}")
    print(f"Labels source: {labels_dir}")
    print(f"Output directory: {output_dir}")
    print()
    
    # Check source directories
    if not images_dir.exists():
        print(f"Error: Images directory not found: {images_dir}")
        return
    
    if not labels_dir.exists():
        print(f"Error: Labels directory not found: {labels_dir}")
        return
    
    # Create output directory structure
    print("1. Creating directory structure...")
    create_yolo_structure(output_dir)
    print()
    
    # Split dataset
    print("2. Splitting dataset...")
    split_dataset(images_dir, labels_dir, output_dir)
    print()
    
    # Create dataset.yaml
    print("3. Creating dataset configuration...")
    yaml_file = create_dataset_yaml(output_dir)
    print()
    
    print("Dataset preparation completed!")
    print(f"Ready for YOLO training with: {yaml_file}")
    print("\nNext steps:")
    print("1. Install YOLOv8: pip install ultralytics")
    print("2. Start training: yolo detect train data=dataset.yaml model=yolov8n.pt epochs=100")

if __name__ == "__main__":
    main() 