#!/usr/bin/env python3
"""
Script to check image quality for water meter annotation
"""
import os
import sys
import json
import argparse
from pathlib import Path

# Add src to path
sys.path.append(str(Path(__file__).parent.parent / 'src'))

from utils.image_quality_checker import <PERSON>Qual<PERSON><PERSON><PERSON><PERSON>


def print_quality_report(results):
    """Print formatted quality report"""
    print("\n" + "="*60)
    print("WATER METER IMAGE QUALITY REPORT")
    print("="*60)
    
    print(f"Total images: {results['total_images']}")
    print(f"Valid images: {results['valid_images']}")
    print(f"Should keep: {results['should_keep']}")
    print(f"Should discard: {results['should_discard']}")
    print(f"Average quality score: {results['avg_quality_score']:.2f}")
    
    print("\nDETAILED RESULTS:")
    print("-" * 80)
    print(f"{'Filename':<30} {'Score':<8} {'Keep':<6} {'Difficulty':<12} {'Issues'}")
    print("-" * 80)
    
    for result in results['results']:
        if not result['is_valid']:
            print(f"{Path(result['file_path']).name:<30} {'ERROR':<8} {'No':<6} {'N/A':<12} {result.get('error', 'Unknown error')}")
            continue
        
        filename = Path(result['file_path']).name
        score = f"{result['overall_score']:.2f}"
        keep = "Yes" if result['should_keep'] else "No"
        difficulty = result['annotation_difficulty']
        
        # Get main issues
        issues = []
        if result['metrics']['brightness_score'] < 0.5:
            issues.append("Brightness")
        if result['metrics']['contrast_score'] < 0.5:
            issues.append("Contrast")
        if result['metrics']['sharpness_score'] < 0.5:
            issues.append("Blur")
        if result['metrics']['noise_score'] < 0.5:
            issues.append("Noise")
        if result['metrics']['resolution_score'] < 0.5:
            issues.append("Resolution")
        
        issues_str = ", ".join(issues) if issues else "None"
        
        print(f"{filename:<30} {score:<8} {keep:<6} {difficulty:<12} {issues_str}")


def create_filtered_directories(results, input_dir, good_dir, bad_dir):
    """Create directories with good and bad images"""
    os.makedirs(good_dir, exist_ok=True)
    os.makedirs(bad_dir, exist_ok=True)
    
    good_count = 0
    bad_count = 0
    
    for result in results['results']:
        if not result['is_valid']:
            continue
            
        source_path = Path(result['file_path'])
        
        if result['should_keep']:
            # Copy to good directory
            dest_path = Path(good_dir) / source_path.name
            import shutil
            shutil.copy2(source_path, dest_path)
            good_count += 1
        else:
            # Copy to bad directory
            dest_path = Path(bad_dir) / source_path.name
            import shutil
            shutil.copy2(source_path, dest_path)
            bad_count += 1
    
    print(f"\nFILTERED RESULTS:")
    print(f"Good images copied to: {good_dir} ({good_count} files)")
    print(f"Bad images copied to: {bad_dir} ({bad_count} files)")


def main():
    parser = argparse.ArgumentParser(description='Check water meter image quality')
    parser.add_argument('--input', '-i', required=True, help='Input directory with images')
    parser.add_argument('--output', '-o', help='Output JSON file for results')
    parser.add_argument('--filter', action='store_true', help='Create filtered directories')
    parser.add_argument('--good-dir', default='data/good_images', help='Directory for good images')
    parser.add_argument('--bad-dir', default='data/bad_images', help='Directory for bad images')
    parser.add_argument('--threshold', type=float, default=0.6, help='Quality threshold (0-1)')
    
    args = parser.parse_args()
    
    if not os.path.exists(args.input):
        print(f"Error: Input directory does not exist: {args.input}")
        sys.exit(1)
    
    print(f"Checking image quality in: {args.input}")
    print(f"Quality threshold: {args.threshold}")
    
    # Initialize checker
    checker = ImageQualityChecker()
    
    # Update threshold if provided
    if args.threshold != 0.6:
        print(f"Using custom threshold: {args.threshold}")
    
    # Check all images
    results = checker.check_batch(args.input, args.output)
    
    # Print report
    print_quality_report(results)
    
    # Save results if requested
    if args.output:
        print(f"\nDetailed results saved to: {args.output}")
    
    # Create filtered directories if requested
    if args.filter:
        create_filtered_directories(results, args.input, args.good_dir, args.bad_dir)
    
    # Print recommendations
    print("\nRECOMMENDATIONS:")
    print("-" * 40)
    
    discard_count = results['should_discard']
    keep_count = results['should_keep']
    
    if discard_count > 0:
        print(f"• Consider discarding {discard_count} low-quality images")
        print(f"• Keep {keep_count} good-quality images for annotation")
        print(f"• This will improve annotation accuracy and reduce costs")
    else:
        print("• All images meet quality standards")
    
    if results['avg_quality_score'] < 0.7:
        print("• Overall image quality is low. Consider:")
        print("  - Better lighting conditions")
        print("  - Image stabilization")
        print("  - Higher resolution cameras")
        print("  - Closer shooting distance")
    
    print("\nNEXT STEPS:")
    print("1. Review the quality report above")
    print("2. Optionally use --filter to separate good/bad images")
    print("3. Use good images for AI annotation")
    print("4. Consider retaking bad images if possible")


if __name__ == '__main__':
    main() 