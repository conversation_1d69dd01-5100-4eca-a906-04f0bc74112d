#!/usr/bin/env python3
"""
Debug YOLO annotations to find training issues
"""

import json
from pathlib import Path
import cv2

def check_bbox_validity():
    """Check if YOLO bbox coordinates are valid"""
    
    base_dir = Path(__file__).parent.parent
    
    # Check all YOLO annotation files
    labels_dirs = [
        base_dir / "data/yolo_training_dataset/train/labels",
        base_dir / "data/yolo_training_dataset/val/labels"
    ]
    
    print("🔍 Checking YOLO annotation validity...")
    print("=" * 50)
    
    for labels_dir in labels_dirs:
        split = "TRAIN" if "train" in str(labels_dir) else "VAL"
        print(f"\n{split} SET:")
        
        txt_files = list(labels_dir.glob("*.txt"))
        for txt_file in txt_files:
            if txt_file.name == "classes.txt":
                continue
                
            print(f"\n📄 {txt_file.name}:")
            
            with open(txt_file, 'r') as f:
                lines = f.readlines()
            
            for i, line in enumerate(lines):
                line = line.strip()
                if not line:
                    continue
                    
                parts = line.split()
                if len(parts) != 5:
                    print(f"  ❌ Line {i+1}: Wrong format! Expected 5 values, got {len(parts)}")
                    continue
                
                try:
                    class_id = int(parts[0])
                    center_x = float(parts[1])
                    center_y = float(parts[2])
                    width = float(parts[3])
                    height = float(parts[4])
                    
                    # Check class ID
                    if class_id < 0 or class_id > 2:
                        print(f"  ❌ Line {i+1}: Invalid class_id {class_id} (should be 0-2)")
                    
                    # Check coordinate ranges (should be 0-1)
                    issues = []
                    if not (0 <= center_x <= 1):
                        issues.append(f"center_x={center_x}")
                    if not (0 <= center_y <= 1):
                        issues.append(f"center_y={center_y}")
                    if not (0 < width <= 1):
                        issues.append(f"width={width}")
                    if not (0 < height <= 1):
                        issues.append(f"height={height}")
                    
                    # Check if bbox is within image bounds
                    left = center_x - width/2
                    right = center_x + width/2
                    top = center_y - height/2
                    bottom = center_y + height/2
                    
                    if left < 0:
                        issues.append(f"left_edge={left}<0")
                    if right > 1:
                        issues.append(f"right_edge={right}>1")
                    if top < 0:
                        issues.append(f"top_edge={top}<0")  
                    if bottom > 1:
                        issues.append(f"bottom_edge={bottom}>1")
                    
                    class_names = ["digital_reading", "decimal_reading", "serial_number"]
                    class_name = class_names[class_id]
                    
                    if issues:
                        print(f"  ❌ Line {i+1} ({class_name}): {', '.join(issues)}")
                    else:
                        print(f"  ✅ Line {i+1} ({class_name}): Valid bbox")
                        
                except ValueError as e:
                    print(f"  ❌ Line {i+1}: Parse error - {e}")

def check_image_sizes():
    """Check actual image dimensions"""
    
    base_dir = Path(__file__).parent.parent
    images_dirs = [
        base_dir / "data/yolo_training_dataset/train/images",
        base_dir / "data/yolo_training_dataset/val/images"
    ]
    
    print("\n\n📷 Checking image dimensions...")
    print("=" * 50)
    
    for images_dir in images_dirs:
        split = "TRAIN" if "train" in str(images_dir) else "VAL"
        print(f"\n{split} SET:")
        
        image_files = list(images_dir.glob("*.jpg")) + list(images_dir.glob("*.png"))
        for img_file in image_files:
            img = cv2.imread(str(img_file))
            if img is not None:
                h, w, c = img.shape
                print(f"  📄 {img_file.name}: {w}x{h} pixels")
            else:
                print(f"  ❌ {img_file.name}: Cannot read image!")

def compare_with_original_json():
    """Compare YOLO annotations with original JSON"""
    
    base_dir = Path(__file__).parent.parent
    json_file = base_dir / "data/ai_annotations/water_meter_full_batch_annotations.json"
    
    print("\n\n🔍 Comparing with original JSON...")
    print("=" * 50)
    
    with open(json_file, 'r', encoding='utf-8') as f:
        json_data = json.load(f)
    
    # Check first few entries
    for i in range(min(3, len(json_data))):
        print(f"\n📄 Image {i+1} (meter_{i+1:02d}):")
        
        image_data = json_data[i]
        print(f"  JSON annotations: {len(image_data['annotations'])}")
        
        for j, annotation in enumerate(image_data['annotations']):
            bbox = annotation['bbox']
            ann_type = annotation['type']
            
            # Original coordinates
            x, y, w, h = bbox['x'], bbox['y'], bbox['width'], bbox['height']
            
            # Converted coordinates (what should be in YOLO file)
            center_x = x + w/2
            center_y = y + h/2
            
            print(f"    {j+1}. {ann_type}:")
            print(f"       JSON: x={x}, y={y}, w={w}, h={h}")
            print(f"       YOLO: cx={center_x:.6f}, cy={center_y:.6f}, w={w:.6f}, h={h:.6f}")

def main():
    try:
        check_bbox_validity()
        check_image_sizes()
        compare_with_original_json()
        
        print("\n\n🎯 SUMMARY:")
        print("=" * 50)
        print("If you see ❌ errors above, those are likely causing the training issues!")
        print("Common problems:")
        print("- Coordinates outside 0-1 range")
        print("- Bbox extending beyond image boundaries") 
        print("- Wrong coordinate system (absolute vs relative)")
        
    except Exception as e:
        print(f"Error during debugging: {e}")

if __name__ == "__main__":
    main() 