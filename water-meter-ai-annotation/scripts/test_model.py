#!/usr/bin/env python3
"""
Test trained YOLO model on water meter images
"""

from ultralytics import YOLO
import cv2
import os
from pathlib import Path

def test_model():
    """Test the trained model on validation images"""
    
    # Paths
    base_dir = Path(__file__).parent.parent
    model_path = base_dir / "data/yolo_training_dataset/runs/detect/train/weights/best.pt"
    test_images_dir = base_dir / "data/yolo_training_dataset/val/images"
    
    if not model_path.exists():
        print(f"Error: Model file not found: {model_path}")
        return
    
    # Load trained model
    print(f"Loading model: {model_path}")
    model = YOLO(model_path)
    
    # Test on validation images
    test_images = list(test_images_dir.glob("*.jpg"))
    print(f"Testing on {len(test_images)} images...")
    
    for img_path in test_images:
        print(f"\nTesting: {img_path.name}")
        
        # Run inference
        results = model(img_path, verbose=False)
        
        # Display results
        for result in results:
            boxes = result.boxes
            if boxes is not None and len(boxes) > 0:
                print(f"  Detected {len(boxes)} objects:")
                for i, box in enumerate(boxes):
                    class_id = int(box.cls[0])
                    confidence = float(box.conf[0])
                    coords = box.xyxy[0].tolist()
                    
                    class_names = ["digital_reading", "decimal_reading", "serial_number"]
                    class_name = class_names[class_id] if class_id < len(class_names) else f"class_{class_id}"
                    
                    print(f"    {i+1}. {class_name}: {confidence:.3f} - bbox: {coords}")
            else:
                print("  No objects detected")
    
    # Run inference on all validation images and save results
    print("\nGenerating prediction images...")
    results = model(test_images_dir, save=True)
    print("Prediction images saved to runs/detect/predict/")

if __name__ == "__main__":
    test_model() 