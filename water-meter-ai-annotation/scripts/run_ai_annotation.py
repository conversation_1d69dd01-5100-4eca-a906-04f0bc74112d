#!/usr/bin/env python3
"""
Main script to run AI annotation on water meter images
"""
import os
import sys
import json
import argparse
from pathlib import Path
from typing import List, Dict, Any

# Add src to path
sys.path.append(str(Path(__file__).parent.parent / 'src'))

from ai_annotation.claude_annotator import Claude<PERSON>nn<PERSON><PERSON>
from ai_annotation.base_annotator import AnnotationR<PERSON>ult


def load_config(config_path: str) -> Dict[str, Any]:
    """Load configuration from JSON file"""
    try:
        with open(config_path, 'r') as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"Config file not found: {config_path}")
        sys.exit(1)
    except json.JSONDecodeError as e:
        print(f"Invalid JSON in config file: {e}")
        sys.exit(1)


def get_image_files(input_dir: str) -> List[str]:
    """Get all image files from input directory"""
    image_extensions = {'.jpg', '.jpeg', '.png', '.bmp'}
    image_files = []
    
    input_path = Path(input_dir)
    if not input_path.exists():
        print(f"Input directory does not exist: {input_dir}")
        sys.exit(1)
    
    for file_path in input_path.iterdir():
        if file_path.is_file() and file_path.suffix.lower() in image_extensions:
            image_files.append(str(file_path))
    
    return sorted(image_files)


def save_results(results: List[AnnotationResult], output_dir: str):
    """Save annotation results to output directory"""
    output_path = Path(output_dir)
    output_path.mkdir(parents=True, exist_ok=True)
    
    # Save individual results
    for result in results:
        filename = Path(result.image_id).stem + '_annotation.json'
        output_file = output_path / filename
        
        with open(output_file, 'w') as f:
            json.dump(result.to_dict(), f, indent=2)
    
    # Save summary
    summary = {
        'total_images': len(results),
        'avg_quality_score': sum(r.quality_score for r in results) / len(results) if results else 0,
        'avg_processing_time': sum(r.processing_time for r in results) / len(results) if results else 0,
        'total_annotations': sum(len(r.annotations) for r in results),
        'results': [r.to_dict() for r in results]
    }
    
    summary_file = output_path / 'annotation_summary.json'
    with open(summary_file, 'w') as f:
        json.dump(summary, f, indent=2)
    
    print(f"Results saved to: {output_dir}")
    print(f"Summary: {len(results)} images, avg quality: {summary['avg_quality_score']:.2f}")


def main():
    parser = argparse.ArgumentParser(description='Run AI annotation on water meter images')
    parser.add_argument('--input', '-i', required=True, help='Input directory with images')
    parser.add_argument('--output', '-o', required=True, help='Output directory for results')
    parser.add_argument('--config', '-c', default='config/ai_config.json', help='Config file path')
    parser.add_argument('--provider', '-p', default='claude', choices=['claude', 'openai'], 
                       help='AI provider to use')
    parser.add_argument('--test-connection', action='store_true', help='Test API connection only')
    
    args = parser.parse_args()
    
    # Load configuration
    config = load_config(args.config)
    
    # Initialize annotator
    if args.provider == 'claude':
        annotator = ClaudeAnnotator(config)
    else:
        print(f"Provider {args.provider} not yet implemented")
        sys.exit(1)
    
    # Test connection if requested
    if args.test_connection:
        print("Testing API connection...")
        if annotator.test_connection():
            print("✓ API connection successful")
            sys.exit(0)
        else:
            print("✗ API connection failed")
            sys.exit(1)
    
    # Get image files
    print(f"Scanning for images in: {args.input}")
    image_files = get_image_files(args.input)
    
    if not image_files:
        print("No image files found")
        sys.exit(1)
    
    print(f"Found {len(image_files)} images")
    
    # Run annotation
    print("Starting annotation process...")
    try:
        results = annotator.annotate_batch(image_files)
        
        if results:
            save_results(results, args.output)
            
            # Print cost summary
            cost_summary = annotator.get_cost_summary()
            print("\nCost Summary:")
            print(f"Total requests: {cost_summary['request_count']}")
            print(f"Estimated cost: ${cost_summary['total_cost']:.4f}")
            print(f"Budget used: {cost_summary['budget_used_percentage']:.1f}%")
        else:
            print("No results generated")
            
    except Exception as e:
        print(f"Error during annotation: {str(e)}")
        sys.exit(1)


if __name__ == '__main__':
    main() 