# 快速开始指南
根据json生成yolo
python scripts/json_to_yolo_converter.py
创建标准YOLO训练结构
python scripts/prepare_yolo_dataset.py
# 3. 安装YOLOv8（如果还没安装）
pip install ultralytics
# 4. 进入数据集目录
cd data/yolo_training_dataset
# 5. 开始训练（核心命令）
yolo detect train data=dataset.yaml model=yolov8n.pt epochs=100 imgsz=640
测试脚本
python scripts/debug_annotations.py

## 步骤1: 环境设置

首先安装Python依赖：

```bash
python -m venv water
.\water\Scripts\activate

pip install -r requirements.txt
```

## 步骤2: 配置API密钥

编辑 `config/ai_config.json` 文件，添加您的API密钥：

```json
{
  "ai_providers": {
    "claude": {
      "api_key": "sk-ant-api03-YOUR_ACTUAL_CLAUDE_KEY_HERE"
    },
    "openai": {
      "api_key": "sk-YOUR_ACTUAL_OPENAI_KEY_HERE"
    }
  }
}
```

## 步骤3: 运行项目设置

```bash
python setup.py
```

这将创建所有必需的目录结构并验证配置。

## 步骤4: 测试API连接

```bash
python scripts/run_ai_annotation.py --test-connection
```

如果看到 "✓ API connection successful"，说明配置正确。

## 步骤5: 准备水表图片

将您的水表图片放入 `data/raw_images/` 目录：

```
data/raw_images/
├── meter_001.jpg
├── meter_002.jpg
└── meter_003.jpg
```

## 步骤6: 运行AI标注

```bash
python scripts/run_ai_annotation.py --input data/raw_images --output data/ai_annotations
```

## 步骤7: 查看结果

标注结果将保存在 `data/ai_annotations/` 目录中：

```
data/ai_annotations/
├── meter_001_annotation.json
├── meter_002_annotation.json
├── meter_003_annotation.json
└── annotation_summary.json
```

每个JSON文件包含：
- 数字读数的位置和值
- 序列号的位置和值
- 品牌/型号信息
- 置信度分数

## 示例结果

```json
{
  "image_id": "meter_001.jpg",
  "annotations": [
    {
      "type": "digital_reading",
      "bbox": {"x": 0.2, "y": 0.3, "width": 0.4, "height": 0.1},
      "value": "00.0566",
      "confidence": 0.95
    },
    {
      "type": "serial_number",
      "bbox": {"x": 0.1, "y": 0.8, "width": 0.6, "height": 0.05},
      "value": "M21010221MG282487",
      "confidence": 0.88
    }
  ],
  "quality_score": 0.92,
  "processing_time": 1.2,
  "provider": "claude"
}
```

## 常见问题

### Q: API密钥如何获取？
A: 
- Claude API: 访问 https://console.anthropic.com/
- OpenAI API: 访问 https://platform.openai.com/

### Q: 支持什么图片格式？
A: 支持 JPG, JPEG, PNG, BMP 格式

### Q: 处理速度如何？
A: 每张图片约1-2秒，取决于图片大小和API响应速度

### Q: 成本如何？
A: 每张图片约$0.02，150张图片总成本约$3

### Q: 如何提高准确率？
A: 
1. 使用高质量、清晰的图片
2. 确保水表数字显示完整
3. 避免反光或阴影

## 下一步

完成基础标注后，您可以：

1. 将结果转换为CVAT格式进行人工精修
2. 使用标注数据训练自定义模型
3. 部署到移动端应用

详见项目文档中的完整工作流程说明。 