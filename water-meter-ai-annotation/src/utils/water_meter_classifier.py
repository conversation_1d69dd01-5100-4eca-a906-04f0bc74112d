"""
Water meter brand and model classifier
"""
import cv2
import numpy as np
from PIL import Image
import re
import os
from pathlib import Path
from typing import Dict, List, Tuple, Any
import json
import logging

logger = logging.getLogger(__name__)


class WaterMeterClassifier:
    """Classify water meter brands and models"""
    
    def __init__(self):
        # Known water meter brands and their characteristics
        self.brand_patterns = {
            'sensus': {
                'text_patterns': [
                    r'sensus',
                    r'620',
                    r'M\d{8}MG\d{6}',  # Serial number pattern
                ],
                'visual_features': {
                    'digital_display': True,
                    'circular_face': True,
                    'blue_housing': True
                }
            },
            'neptune': {
                'text_patterns': [
                    r'neptune',
                    r'T-10',
                    r'R900',
                ],
                'visual_features': {
                    'digital_display': True,
                    'rectangular_face': True,
                    'gray_housing': True
                }
            },
            'badger': {
                'text_patterns': [
                    r'badger',
                    r'E-Series',
                    r'ORION',
                ],
                'visual_features': {
                    'digital_display': True,
                    'square_face': True,
                    'white_housing': True
                }
            },
            'elster': {
                'text_patterns': [
                    r'elster',
                    r'amco',
                    r'C700',
                ],
                'visual_features': {
                    'analog_display': True,
                    'circular_face': True,
                    'brass_housing': True
                }
            },
            'unknown': {
                'text_patterns': [],
                'visual_features': {}
            }
        }
        
        # Model type classifications
        self.model_types = {
            'digital': {
                'display_type': 'digital_lcd',
                'reading_method': 'ocr',
                'complexity': 'high'
            },
            'analog': {
                'display_type': 'mechanical_dials',
                'reading_method': 'dial_recognition',
                'complexity': 'medium'
            },
            'mixed': {
                'display_type': 'digital_plus_analog',
                'reading_method': 'combined',
                'complexity': 'very_high'
            }
        }
    
    def classify_meter(self, image_path: str) -> Dict[str, Any]:
        """
        Classify water meter brand and model type
        
        Args:
            image_path: Path to the water meter image
            
        Returns:
            Dictionary with classification results
        """
        try:
            # Load image
            image = cv2.imread(image_path)
            if image is None:
                return {
                    'image_path': image_path,
                    'is_valid': False,
                    'error': 'Could not load image'
                }
            
            # Analyze image
            text_analysis = self._analyze_text_content(image)
            visual_analysis = self._analyze_visual_features(image)
            
            # Classify brand
            brand_scores = self._calculate_brand_scores(text_analysis, visual_analysis)
            predicted_brand = max(brand_scores, key=brand_scores.get)
            
            # Classify model type
            model_type = self._classify_model_type(visual_analysis)
            
            # Calculate confidence
            confidence = brand_scores[predicted_brand] if predicted_brand in brand_scores else 0.0
            
            # Get training compatibility
            training_compatibility = self._assess_training_compatibility(
                predicted_brand, model_type, confidence
            )
            
            return {
                'image_path': image_path,
                'is_valid': True,
                'brand': predicted_brand,
                'model_type': model_type,
                'confidence': confidence,
                'brand_scores': brand_scores,
                'text_features': text_analysis,
                'visual_features': visual_analysis,
                'training_compatibility': training_compatibility,
                'recommendation': self._get_recommendation(predicted_brand, model_type, confidence)
            }
            
        except Exception as e:
            logger.error(f"Error classifying meter {image_path}: {str(e)}")
            return {
                'image_path': image_path,
                'is_valid': False,
                'error': str(e)
            }
    
    def _analyze_text_content(self, image: np.ndarray) -> Dict[str, Any]:
        """Analyze text content in the image"""
        try:
            # Convert to grayscale
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            
            # Apply OCR preprocessing
            # Increase contrast
            clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))
            enhanced = clahe.apply(gray)
            
            # Find text regions (simplified approach)
            # In a real implementation, you'd use OCR libraries like Tesseract
            text_regions = self._find_text_regions(enhanced)
            
            # For now, we'll use basic pattern matching on filename and basic image analysis
            # In production, you'd integrate with OCR
            
            return {
                'text_regions_count': len(text_regions),
                'has_digital_display': self._detect_digital_display(enhanced),
                'has_brand_text': self._detect_brand_text(enhanced),
                'serial_number_pattern': self._detect_serial_pattern(enhanced)
            }
            
        except Exception as e:
            logger.error(f"Error analyzing text content: {str(e)}")
            return {
                'text_regions_count': 0,
                'has_digital_display': False,
                'has_brand_text': False,
                'serial_number_pattern': False
            }
    
    def _analyze_visual_features(self, image: np.ndarray) -> Dict[str, Any]:
        """Analyze visual features of the water meter"""
        try:
            # Convert to different color spaces
            hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            
            # Analyze shape
            shape_analysis = self._analyze_shape(gray)
            
            # Analyze color
            color_analysis = self._analyze_color(hsv)
            
            # Analyze display type
            display_analysis = self._analyze_display_type(gray)
            
            return {
                'shape': shape_analysis,
                'color': color_analysis,
                'display': display_analysis,
                'size': image.shape[:2],
                'aspect_ratio': image.shape[1] / image.shape[0]
            }
            
        except Exception as e:
            logger.error(f"Error analyzing visual features: {str(e)}")
            return {
                'shape': 'unknown',
                'color': 'unknown',
                'display': 'unknown',
                'size': (0, 0),
                'aspect_ratio': 1.0
            }
    
    def _analyze_shape(self, gray: np.ndarray) -> str:
        """Analyze the shape of the water meter"""
        # Find contours
        edges = cv2.Canny(gray, 50, 150)
        contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        if not contours:
            return 'unknown'
        
        # Find the largest contour (likely the meter housing)
        largest_contour = max(contours, key=cv2.contourArea)
        
        # Approximate contour
        epsilon = 0.02 * cv2.arcLength(largest_contour, True)
        approx = cv2.approxPolyDP(largest_contour, epsilon, True)
        
        # Classify shape based on number of vertices
        vertices = len(approx)
        if vertices == 3:
            return 'triangular'
        elif vertices == 4:
            return 'rectangular'
        elif vertices > 8:
            return 'circular'
        else:
            return 'polygonal'
    
    def _analyze_color(self, hsv: np.ndarray) -> str:
        """Analyze dominant colors in the image"""
        # Calculate histogram
        hist = cv2.calcHist([hsv], [0], None, [180], [0, 180])
        
        # Find dominant hue
        dominant_hue = np.argmax(hist)
        
        # Classify color
        if 100 <= dominant_hue <= 120:
            return 'blue'
        elif 60 <= dominant_hue <= 80:
            return 'green'
        elif 0 <= dominant_hue <= 20 or 160 <= dominant_hue <= 180:
            return 'red'
        elif 20 <= dominant_hue <= 40:
            return 'yellow'
        else:
            return 'gray'
    
    def _analyze_display_type(self, gray: np.ndarray) -> str:
        """Analyze the type of display"""
        # Look for digital display characteristics
        # Digital displays typically have uniform rectangular regions
        
        # Apply threshold to find high-contrast regions
        _, thresh = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        
        # Find rectangular regions
        contours, _ = cv2.findContours(thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        rectangular_regions = 0
        for contour in contours:
            # Check if contour is rectangular
            epsilon = 0.02 * cv2.arcLength(contour, True)
            approx = cv2.approxPolyDP(contour, epsilon, True)
            
            if len(approx) == 4:
                # Check aspect ratio
                rect = cv2.boundingRect(contour)
                aspect_ratio = rect[2] / rect[3]
                
                # Digital displays often have specific aspect ratios
                if 2 <= aspect_ratio <= 8:
                    rectangular_regions += 1
        
        if rectangular_regions >= 2:
            return 'digital'
        else:
            return 'analog'
    
    def _find_text_regions(self, gray: np.ndarray) -> List[Tuple[int, int, int, int]]:
        """Find text regions in the image"""
        # This is a simplified implementation
        # In production, you'd use more sophisticated text detection
        
        # Apply morphological operations to find text-like regions
        kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (9, 1))
        dilated = cv2.dilate(gray, kernel, iterations=1)
        
        # Find contours
        contours, _ = cv2.findContours(dilated, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        text_regions = []
        for contour in contours:
            rect = cv2.boundingRect(contour)
            x, y, w, h = rect
            
            # Filter based on size and aspect ratio
            if w > 20 and h > 10 and w/h > 2:
                text_regions.append(rect)
        
        return text_regions
    
    def _detect_digital_display(self, gray: np.ndarray) -> bool:
        """Detect if image contains digital display"""
        # Look for high-contrast rectangular regions
        _, thresh = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        
        # Find connected components
        num_labels, labels, stats, centroids = cv2.connectedComponentsWithStats(thresh)
        
        # Check for digital display characteristics
        for i in range(1, num_labels):
            area = stats[i, cv2.CC_STAT_AREA]
            width = stats[i, cv2.CC_STAT_WIDTH]
            height = stats[i, cv2.CC_STAT_HEIGHT]
            
            # Digital displays have specific size and aspect ratio
            if area > 1000 and 2 <= width/height <= 8:
                return True
        
        return False
    
    def _detect_brand_text(self, gray: np.ndarray) -> bool:
        """Detect if image contains brand text"""
        # This would typically use OCR
        # For now, we'll use basic pattern detection
        return True  # Simplified for demo
    
    def _detect_serial_pattern(self, gray: np.ndarray) -> bool:
        """Detect serial number patterns"""
        # This would typically use OCR to find specific patterns
        # For now, we'll use basic detection
        return True  # Simplified for demo
    
    def _calculate_brand_scores(self, text_analysis: Dict, visual_analysis: Dict) -> Dict[str, float]:
        """Calculate brand prediction scores"""
        scores = {}
        
        for brand, features in self.brand_patterns.items():
            score = 0.0
            
            # Visual feature scoring
            if visual_analysis['shape'] == 'circular' and features['visual_features'].get('circular_face'):
                score += 0.3
            elif visual_analysis['shape'] == 'rectangular' and features['visual_features'].get('rectangular_face'):
                score += 0.3
            
            if visual_analysis['color'] == 'blue' and features['visual_features'].get('blue_housing'):
                score += 0.2
            elif visual_analysis['color'] == 'gray' and features['visual_features'].get('gray_housing'):
                score += 0.2
            
            if visual_analysis['display'] == 'digital' and features['visual_features'].get('digital_display'):
                score += 0.3
            elif visual_analysis['display'] == 'analog' and features['visual_features'].get('analog_display'):
                score += 0.3
            
            # Text feature scoring (simplified)
            if text_analysis['has_digital_display'] and features['visual_features'].get('digital_display'):
                score += 0.2
            
            scores[brand] = score
        
        return scores
    
    def _classify_model_type(self, visual_analysis: Dict) -> str:
        """Classify model type based on visual features"""
        if visual_analysis['display'] == 'digital':
            return 'digital'
        elif visual_analysis['display'] == 'analog':
            return 'analog'
        else:
            return 'mixed'
    
    def _assess_training_compatibility(self, brand: str, model_type: str, confidence: float) -> Dict[str, Any]:
        """Assess compatibility for training"""
        compatibility = {
            'is_suitable': False,
            'compatibility_score': 0.0,
            'issues': [],
            'recommendations': []
        }
        
        # Check confidence threshold
        if confidence < 0.5:
            compatibility['issues'].append('Low brand identification confidence')
            compatibility['recommendations'].append('Manual verification required')
        
        # Check if it's a target brand (assuming Sensus is primary target)
        if brand == 'sensus':
            compatibility['compatibility_score'] += 0.5
        elif brand == 'unknown':
            compatibility['issues'].append('Unknown brand')
            compatibility['recommendations'].append('Consider manual classification')
        else:
            compatibility['compatibility_score'] += 0.3
            compatibility['recommendations'].append('Different brand - consider separate model')
        
        # Check model type consistency
        if model_type == 'digital':
            compatibility['compatibility_score'] += 0.3
        elif model_type == 'analog':
            compatibility['issues'].append('Analog meter - different recognition approach needed')
        else:
            compatibility['issues'].append('Mixed display type - complex recognition required')
        
        # Final assessment
        compatibility['is_suitable'] = (
            compatibility['compatibility_score'] >= 0.6 and 
            len(compatibility['issues']) <= 1
        )
        
        return compatibility
    
    def _get_recommendation(self, brand: str, model_type: str, confidence: float) -> str:
        """Get recommendation for this image"""
        if confidence < 0.3:
            return "EXCLUDE - Cannot identify reliably"
        elif brand == 'sensus' and model_type == 'digital' and confidence >= 0.7:
            return "INCLUDE - Perfect match for training"
        elif brand == 'sensus' and confidence >= 0.5:
            return "INCLUDE - Good match for training"
        elif brand != 'sensus' and brand != 'unknown' and confidence >= 0.6:
            return "SEPARATE - Different brand, train separately"
        elif brand == 'unknown':
            return "REVIEW - Manual classification needed"
        else:
            return "EXCLUDE - Not suitable for current training"
    
    def classify_batch(self, image_dir: str, output_file: str = None) -> Dict[str, Any]:
        """
        Classify a batch of water meter images
        
        Args:
            image_dir: Directory containing images
            output_file: Optional file to save results
            
        Returns:
            Dictionary with classification results
        """
        image_extensions = {'.jpg', '.jpeg', '.png', '.bmp'}
        image_files = []
        
        for file_path in Path(image_dir).iterdir():
            if file_path.is_file() and file_path.suffix.lower() in image_extensions:
                image_files.append(str(file_path))
        
        results = []
        brand_counts = {}
        model_type_counts = {}
        
        for image_path in image_files:
            result = self.classify_meter(image_path)
            results.append(result)
            
            if result['is_valid']:
                brand = result['brand']
                model_type = result['model_type']
                
                brand_counts[brand] = brand_counts.get(brand, 0) + 1
                model_type_counts[model_type] = model_type_counts.get(model_type, 0) + 1
        
        # Generate summary
        total_images = len(results)
        valid_images = sum(1 for r in results if r['is_valid'])
        suitable_for_training = sum(1 for r in results if r.get('training_compatibility', {}).get('is_suitable', False))
        
        summary = {
            'total_images': total_images,
            'valid_images': valid_images,
            'suitable_for_training': suitable_for_training,
            'brand_distribution': brand_counts,
            'model_type_distribution': model_type_counts,
            'dominant_brand': max(brand_counts, key=brand_counts.get) if brand_counts else 'unknown',
            'dominant_model_type': max(model_type_counts, key=model_type_counts.get) if model_type_counts else 'unknown',
            'results': results
        }
        
        if output_file:
            with open(output_file, 'w') as f:
                json.dump(summary, f, indent=2)
        
        return summary 