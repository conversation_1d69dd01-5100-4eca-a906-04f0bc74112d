"""
Image quality checker for water meter images
"""
import cv2
import numpy as np
from PIL import Image, ImageStat
import os
from pathlib import Path
from typing import Dict, List, Tuple, Any
import json
import logging

logger = logging.getLogger(__name__)


class ImageQualityChecker:
    """Check image quality for water meter annotation"""
    
    def __init__(self):
        self.quality_thresholds = {
            'min_resolution': (800, 600),      # Minimum resolution
            'max_resolution': (4000, 3000),    # Maximum resolution
            'min_brightness': 50,              # Too dark
            'max_brightness': 200,             # Too bright
            'min_contrast': 30,                # Too low contrast
            'min_sharpness': 100,              # Too blurry
            'max_noise': 25,                   # Too noisy
            'min_file_size': 50 * 1024,        # 50KB minimum
            'max_file_size': 10 * 1024 * 1024  # 10MB maximum
        }
    
    def check_image_quality(self, image_path: str) -> Dict[str, Any]:
        """
        Check comprehensive image quality
        
        Args:
            image_path: Path to the image file
            
        Returns:
            Dictionary with quality metrics and recommendations
        """
        try:
            # Basic file checks
            file_size = os.path.getsize(image_path)
            
            # Load image
            pil_image = Image.open(image_path)
            cv_image = cv2.imread(image_path)
            
            if cv_image is None:
                return {
                    'file_path': image_path,
                    'is_valid': False,
                    'error': 'Could not load image',
                    'overall_score': 0
                }
            
            # Calculate metrics
            resolution = self._check_resolution(pil_image)
            brightness = self._check_brightness(pil_image)
            contrast = self._check_contrast(cv_image)
            sharpness = self._check_sharpness(cv_image)
            noise = self._check_noise(cv_image)
            color_balance = self._check_color_balance(cv_image)
            
            # File size check
            file_size_ok = (self.quality_thresholds['min_file_size'] <= 
                          file_size <= self.quality_thresholds['max_file_size'])
            
            # Overall quality score
            quality_score = self._calculate_overall_score({
                'resolution': resolution,
                'brightness': brightness,
                'contrast': contrast,
                'sharpness': sharpness,
                'noise': noise,
                'color_balance': color_balance,
                'file_size': file_size_ok
            })
            
            # Recommendations
            recommendations = self._generate_recommendations({
                'resolution': resolution,
                'brightness': brightness,
                'contrast': contrast,
                'sharpness': sharpness,
                'noise': noise,
                'file_size': file_size_ok
            })
            
            return {
                'file_path': image_path,
                'is_valid': True,
                'file_size': file_size,
                'resolution': pil_image.size,
                'metrics': {
                    'resolution_score': resolution['score'],
                    'brightness_score': brightness['score'],
                    'contrast_score': contrast['score'],
                    'sharpness_score': sharpness['score'],
                    'noise_score': noise['score'],
                    'color_balance_score': color_balance['score'],
                    'file_size_ok': file_size_ok
                },
                'details': {
                    'brightness_value': brightness['value'],
                    'contrast_value': contrast['value'],
                    'sharpness_value': sharpness['value'],
                    'noise_value': noise['value']
                },
                'overall_score': quality_score,
                'recommendations': recommendations,
                'should_keep': quality_score >= 0.6,  # Keep if score >= 60%
                'annotation_difficulty': self._get_annotation_difficulty(quality_score)
            }
            
        except Exception as e:
            logger.error(f"Error checking image quality for {image_path}: {str(e)}")
            return {
                'file_path': image_path,
                'is_valid': False,
                'error': str(e),
                'overall_score': 0
            }
    
    def _check_resolution(self, image: Image.Image) -> Dict[str, Any]:
        """Check image resolution"""
        width, height = image.size
        min_w, min_h = self.quality_thresholds['min_resolution']
        max_w, max_h = self.quality_thresholds['max_resolution']
        
        if width < min_w or height < min_h:
            score = 0.3  # Too small
        elif width > max_w or height > max_h:
            score = 0.8  # Too large but usable
        else:
            score = 1.0  # Good resolution
        
        return {
            'score': score,
            'width': width,
            'height': height,
            'total_pixels': width * height
        }
    
    def _check_brightness(self, image: Image.Image) -> Dict[str, Any]:
        """Check image brightness"""
        # Convert to grayscale for brightness calculation
        gray_image = image.convert('L')
        stat = ImageStat.Stat(gray_image)
        brightness = stat.mean[0]
        
        min_brightness = self.quality_thresholds['min_brightness']
        max_brightness = self.quality_thresholds['max_brightness']
        
        if brightness < min_brightness:
            score = 0.4  # Too dark
        elif brightness > max_brightness:
            score = 0.6  # Too bright
        else:
            # Optimal brightness range
            score = 1.0
        
        return {
            'score': score,
            'value': brightness
        }
    
    def _check_contrast(self, image: np.ndarray) -> Dict[str, Any]:
        """Check image contrast"""
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        contrast = gray.std()
        
        min_contrast = self.quality_thresholds['min_contrast']
        
        if contrast < min_contrast:
            score = 0.4  # Low contrast
        else:
            score = min(1.0, contrast / 60)  # Normalize to 0-1
        
        return {
            'score': score,
            'value': contrast
        }
    
    def _check_sharpness(self, image: np.ndarray) -> Dict[str, Any]:
        """Check image sharpness using Laplacian variance"""
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        laplacian = cv2.Laplacian(gray, cv2.CV_64F)
        sharpness = laplacian.var()
        
        min_sharpness = self.quality_thresholds['min_sharpness']
        
        if sharpness < min_sharpness:
            score = 0.3  # Blurry
        else:
            score = min(1.0, sharpness / 500)  # Normalize to 0-1
        
        return {
            'score': score,
            'value': sharpness
        }
    
    def _check_noise(self, image: np.ndarray) -> Dict[str, Any]:
        """Check image noise level"""
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        
        # Use median filter to estimate noise
        median_filtered = cv2.medianBlur(gray, 5)
        noise = np.mean(np.abs(gray.astype(np.float32) - median_filtered.astype(np.float32)))
        
        max_noise = self.quality_thresholds['max_noise']
        
        if noise > max_noise:
            score = 0.5  # Too noisy
        else:
            score = 1.0 - (noise / max_noise)
        
        return {
            'score': score,
            'value': noise
        }
    
    def _check_color_balance(self, image: np.ndarray) -> Dict[str, Any]:
        """Check color balance"""
        # Calculate mean values for each channel
        mean_b = np.mean(image[:, :, 0])
        mean_g = np.mean(image[:, :, 1])
        mean_r = np.mean(image[:, :, 2])
        
        # Calculate color balance score
        color_diff = max(abs(mean_r - mean_g), abs(mean_g - mean_b), abs(mean_b - mean_r))
        
        if color_diff > 50:
            score = 0.6  # Poor color balance
        else:
            score = 1.0 - (color_diff / 50) * 0.4
        
        return {
            'score': score,
            'color_diff': color_diff
        }
    
    def _calculate_overall_score(self, metrics: Dict[str, Any]) -> float:
        """Calculate overall quality score"""
        weights = {
            'resolution': 0.15,
            'brightness': 0.20,
            'contrast': 0.20,
            'sharpness': 0.25,
            'noise': 0.15,
            'color_balance': 0.05
        }
        
        total_score = 0
        for metric, weight in weights.items():
            if metric in metrics:
                total_score += metrics[metric]['score'] * weight
        
        # File size penalty
        if not metrics.get('file_size', True):
            total_score *= 0.8
        
        return min(1.0, total_score)
    
    def _generate_recommendations(self, metrics: Dict[str, Any]) -> List[str]:
        """Generate improvement recommendations"""
        recommendations = []
        
        if metrics['resolution']['score'] < 0.5:
            recommendations.append("Image resolution is too low. Use higher resolution camera.")
        
        if metrics['brightness']['score'] < 0.5:
            recommendations.append("Image is too dark or bright. Adjust lighting conditions.")
        
        if metrics['contrast']['score'] < 0.5:
            recommendations.append("Low contrast. Ensure good lighting and avoid overexposure.")
        
        if metrics['sharpness']['score'] < 0.5:
            recommendations.append("Image is blurry. Use image stabilization or faster shutter speed.")
        
        if metrics['noise']['score'] < 0.5:
            recommendations.append("Image is too noisy. Use lower ISO or better lighting.")
        
        if not metrics.get('file_size', True):
            recommendations.append("File size is inappropriate. Check compression settings.")
        
        return recommendations
    
    def _get_annotation_difficulty(self, score: float) -> str:
        """Get annotation difficulty level"""
        if score >= 0.8:
            return "Easy"
        elif score >= 0.6:
            return "Medium"
        elif score >= 0.4:
            return "Hard"
        else:
            return "Very Hard"
    
    def check_batch(self, image_dir: str, output_file: str = None) -> List[Dict[str, Any]]:
        """
        Check quality for a batch of images
        
        Args:
            image_dir: Directory containing images
            output_file: Optional file to save results
            
        Returns:
            List of quality check results
        """
        image_extensions = {'.jpg', '.jpeg', '.png', '.bmp'}
        image_files = []
        
        for file_path in Path(image_dir).iterdir():
            if file_path.is_file() and file_path.suffix.lower() in image_extensions:
                image_files.append(str(file_path))
        
        results = []
        for image_path in image_files:
            result = self.check_image_quality(image_path)
            results.append(result)
        
        # Generate summary
        total_images = len(results)
        valid_images = sum(1 for r in results if r['is_valid'])
        should_keep = sum(1 for r in results if r.get('should_keep', False))
        
        summary = {
            'total_images': total_images,
            'valid_images': valid_images,
            'should_keep': should_keep,
            'should_discard': valid_images - should_keep,
            'avg_quality_score': sum(r['overall_score'] for r in results) / max(total_images, 1),
            'results': results
        }
        
        if output_file:
            with open(output_file, 'w') as f:
                json.dump(summary, f, indent=2)
        
        return summary 