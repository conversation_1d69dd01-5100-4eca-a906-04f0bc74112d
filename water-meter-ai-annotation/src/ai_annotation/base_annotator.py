"""
Base annotator class for AI-powered water meter annotation
"""
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any
import json
import time
from dataclasses import dataclass
from PIL import Image
import logging

logger = logging.getLogger(__name__)


@dataclass
class BoundingBox:
    """Bounding box representation"""
    x: float
    y: float
    width: float
    height: float
    
    def to_dict(self) -> Dict[str, float]:
        return {
            'x': self.x,
            'y': self.y,
            'width': self.width,
            'height': self.height
        }


@dataclass
class Annotation:
    """Single annotation result"""
    type: str
    bbox: BoundingBox
    value: str
    confidence: float
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'type': self.type,
            'bbox': self.bbox.to_dict(),
            'value': self.value,
            'confidence': self.confidence
        }


@dataclass
class AnnotationResult:
    """Complete annotation result for one image"""
    image_id: str
    annotations: List[Annotation]
    quality_score: float
    processing_time: float
    provider: str
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'image_id': self.image_id,
            'annotations': [ann.to_dict() for ann in self.annotations],
            'quality_score': self.quality_score,
            'processing_time': self.processing_time,
            'provider': self.provider
        }


class BaseAnnotator(ABC):
    """Base class for all AI annotators"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.provider_config = config.get('ai_providers', {})
        self.annotation_settings = config.get('annotation_settings', {})
        self.water_meter_config = config.get('water_meter_detection', {})
        
        # Initialize cost tracking
        self.cost_tracking = config.get('cost_tracking', {})
        self.total_cost = 0.0
        self.request_count = 0
        
    @abstractmethod
    def annotate_image(self, image_path: str) -> AnnotationResult:
        """
        Annotate a single image
        
        Args:
            image_path: Path to the image file
            
        Returns:
            AnnotationResult object with annotations
        """
        pass
    
    def annotate_batch(self, image_paths: List[str]) -> List[AnnotationResult]:
        """
        Annotate a batch of images
        
        Args:
            image_paths: List of image file paths
            
        Returns:
            List of AnnotationResult objects
        """
        results = []
        batch_size = self.annotation_settings.get('batch_size', 5)
        
        for i in range(0, len(image_paths), batch_size):
            batch = image_paths[i:i + batch_size]
            logger.info(f"Processing batch {i//batch_size + 1}/{(len(image_paths) + batch_size - 1)//batch_size}")
            
            for image_path in batch:
                try:
                    result = self.annotate_image(image_path)
                    results.append(result)
                    self._update_cost_tracking(result)
                    
                    # Check budget limit
                    if self._check_budget_limit():
                        logger.warning("Budget limit reached, stopping annotation")
                        return results
                        
                except Exception as e:
                    logger.error(f"Error annotating {image_path}: {str(e)}")
                    continue
                    
        return results
    
    def _preprocess_image(self, image_path: str) -> Image.Image:
        """Preprocess image before annotation"""
        image = Image.open(image_path)
        
        # Resize if too large
        max_size = self.annotation_settings.get('max_image_size', 4096)
        if max(image.size) > max_size:
            ratio = max_size / max(image.size)
            new_size = (int(image.width * ratio), int(image.height * ratio))
            image = image.resize(new_size, Image.Resampling.LANCZOS)
            
        # Convert to RGB if needed
        if image.mode != 'RGB':
            image = image.convert('RGB')
            
        return image
    
    def _validate_annotation(self, annotation: Annotation) -> bool:
        """Validate annotation quality"""
        min_confidence = self.water_meter_config.get('min_confidence', 0.7)
        
        if annotation.confidence < min_confidence:
            return False
            
        # Check if bbox is valid
        bbox = annotation.bbox
        if (bbox.x < 0 or bbox.y < 0 or 
            bbox.width <= 0 or bbox.height <= 0 or
            bbox.x + bbox.width > 1 or bbox.y + bbox.height > 1):
            return False
            
        return True
    
    def _calculate_quality_score(self, annotations: List[Annotation]) -> float:
        """Calculate overall quality score for annotations"""
        if not annotations:
            return 0.0
            
        # Average confidence of all annotations
        avg_confidence = sum(ann.confidence for ann in annotations) / len(annotations)
        
        # Bonus for finding expected objects
        target_objects = self.water_meter_config.get('target_objects', [])
        found_objects = set(ann.type for ann in annotations)
        completeness = len(found_objects.intersection(target_objects)) / len(target_objects)
        
        # Combine confidence and completeness
        quality_score = (avg_confidence * 0.7) + (completeness * 0.3)
        
        return min(quality_score, 1.0)
    
    def _update_cost_tracking(self, result: AnnotationResult):
        """Update cost tracking information"""
        if not self.cost_tracking.get('enabled', False):
            return
            
        estimated_cost = self.cost_tracking.get('cost_per_image_estimate', 0.02)
        self.total_cost += estimated_cost
        self.request_count += 1
        
        logger.info(f"Total cost: ${self.total_cost:.4f}, Requests: {self.request_count}")
    
    def _check_budget_limit(self) -> bool:
        """Check if budget limit is exceeded"""
        if not self.cost_tracking.get('enabled', False):
            return False
            
        budget_limit = self.cost_tracking.get('budget_limit', 100.0)
        alert_threshold = self.cost_tracking.get('alert_threshold', 0.8)
        
        if self.total_cost >= budget_limit:
            return True
            
        if self.total_cost >= budget_limit * alert_threshold:
            logger.warning(f"Budget alert: ${self.total_cost:.4f} / ${budget_limit:.4f}")
            
        return False
    
    def get_cost_summary(self) -> Dict[str, Any]:
        """Get cost tracking summary"""
        return {
            'total_cost': self.total_cost,
            'request_count': self.request_count,
            'avg_cost_per_request': self.total_cost / max(self.request_count, 1),
            'budget_limit': self.cost_tracking.get('budget_limit', 100.0),
            'budget_used_percentage': (self.total_cost / self.cost_tracking.get('budget_limit', 100.0)) * 100
        } 