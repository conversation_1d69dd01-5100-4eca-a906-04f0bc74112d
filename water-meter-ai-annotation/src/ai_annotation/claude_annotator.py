"""
Claude AI annotator for water meter images
"""
import base64
import json
import time
from typing import Dict, List, Any
import requests
from PIL import Image
import io
import logging

from .base_annotator import BaseAnnotator, AnnotationResult, Annotation, BoundingBox

logger = logging.getLogger(__name__)


class ClaudeAnnotator(BaseAnnotator):
    """Claude AI annotator for water meter images"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.api_key = self.provider_config.get('claude', {}).get('api_key')
        self.model = self.provider_config.get('claude', {}).get('model', 'claude-3-5-sonnet-20241022')
        self.max_tokens = self.provider_config.get('claude', {}).get('max_tokens', 4000)
        self.temperature = self.provider_config.get('claude', {}).get('temperature', 0.1)
        self.timeout = self.provider_config.get('claude', {}).get('timeout', 30)
        self.retry_attempts = self.provider_config.get('claude', {}).get('retry_attempts', 3)
        self.retry_delay = self.provider_config.get('claude', {}).get('retry_delay', 2)
        
        if not self.api_key:
            raise ValueError("Claude API key is required")
            
        self.headers = {
            'Content-Type': 'application/json',
            'x-api-key': self.api_key,
            'anthropic-version': '2023-06-01'
        }
        
        self.base_url = 'https://api.anthropic.com/v1/messages'
    
    def annotate_image(self, image_path: str) -> AnnotationResult:
        """
        Annotate a single water meter image using Claude
        
        Args:
            image_path: Path to the image file
            
        Returns:
            AnnotationResult object with annotations
        """
        start_time = time.time()
        
        # Preprocess image
        image = self._preprocess_image(image_path)
        
        # Convert image to base64
        image_b64 = self._image_to_base64(image)
        
        # Create prompt
        prompt = self._create_annotation_prompt()
        
        # Make API request with retry logic
        response_data = self._make_api_request(image_b64, prompt)
        
        # Parse response
        annotations = self._parse_response(response_data)
        
        # Calculate quality score
        quality_score = self._calculate_quality_score(annotations)
        
        # Create result
        result = AnnotationResult(
            image_id=image_path,
            annotations=annotations,
            quality_score=quality_score,
            processing_time=time.time() - start_time,
            provider='claude'
        )
        
        return result
    
    def _image_to_base64(self, image: Image.Image) -> str:
        """Convert PIL Image to base64 string"""
        buffered = io.BytesIO()
        image.save(buffered, format="JPEG", quality=85)
        img_str = base64.b64encode(buffered.getvalue()).decode()
        return img_str
    
    def _create_annotation_prompt(self) -> str:
        """Create the annotation prompt for Claude"""
        prompt = """
You are an expert computer vision annotator specializing in water meter reading. 
Your task is to analyze water meter images and provide precise annotations.

Please analyze this water meter image and identify the following objects with their bounding boxes:

1. **Digital Reading**: The numerical display showing the current meter reading
2. **Serial Number**: The unique identifier printed on the meter
3. **Meter Brand**: The manufacturer name/logo
4. **Meter Type**: The model or type information

For each detected object, provide:
- Object type (digital_reading, serial_number, meter_brand, meter_type)
- Bounding box coordinates in normalized format (0-1 range): x, y, width, height
- Extracted text value (if applicable)
- Confidence score (0-1)

Return your response in this exact JSON format:
```json
{
  "annotations": [
    {
      "type": "digital_reading",
      "bbox": {"x": 0.2, "y": 0.3, "width": 0.4, "height": 0.1},
      "value": "00.0566",
      "confidence": 0.95
    },
    {
      "type": "serial_number", 
      "bbox": {"x": 0.1, "y": 0.8, "width": 0.6, "height": 0.05},
      "value": "M21010221MG282487",
      "confidence": 0.88
    }
  ]
}
```

Important guidelines:
- Focus on Sensus 620 series digital water meters
- Separate black digits from red digits in readings if present
- Use normalized coordinates (0-1 range)
- Only include objects you can clearly identify
- Provide realistic confidence scores based on image quality
- If text is unclear, indicate lower confidence
- Ensure bounding boxes are tight around the objects
"""
        return prompt
    
    def _make_api_request(self, image_b64: str, prompt: str) -> Dict[str, Any]:
        """Make API request to Claude with retry logic"""
        
        payload = {
            "model": self.model,
            "max_tokens": self.max_tokens,
            "temperature": self.temperature,
            "messages": [
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": prompt
                        },
                        {
                            "type": "image",
                            "source": {
                                "type": "base64",
                                "media_type": "image/jpeg",
                                "data": image_b64
                            }
                        }
                    ]
                }
            ]
        }
        
        for attempt in range(self.retry_attempts):
            try:
                logger.info(f"Making Claude API request (attempt {attempt + 1}/{self.retry_attempts})")
                
                response = requests.post(
                    self.base_url,
                    headers=self.headers,
                    json=payload,
                    timeout=self.timeout
                )
                
                if response.status_code == 200:
                    return response.json()
                elif response.status_code == 429:
                    # Rate limit, wait longer
                    wait_time = (2 ** attempt) * self.retry_delay
                    logger.warning(f"Rate limited, waiting {wait_time}s before retry")
                    time.sleep(wait_time)
                else:
                    logger.error(f"API request failed with status {response.status_code}: {response.text}")
                    if attempt == self.retry_attempts - 1:
                        raise Exception(f"API request failed after {self.retry_attempts} attempts")
                    
            except Exception as e:
                logger.error(f"Request attempt {attempt + 1} failed: {str(e)}")
                if attempt == self.retry_attempts - 1:
                    raise
                time.sleep(self.retry_delay)
        
        raise Exception("All retry attempts failed")
    
    def _parse_response(self, response_data: Dict[str, Any]) -> List[Annotation]:
        """Parse Claude API response and extract annotations"""
        try:
            content = response_data.get('content', [])
            if not content:
                logger.warning("Empty response content")
                return []
            
            # Get the text response
            text_content = content[0].get('text', '')
            
            # Extract JSON from response
            json_start = text_content.find('```json')
            json_end = text_content.find('```', json_start + 7)
            
            if json_start == -1 or json_end == -1:
                # Try to find JSON without code blocks
                json_start = text_content.find('{')
                json_end = text_content.rfind('}') + 1
                
                if json_start == -1 or json_end == 0:
                    logger.error("No JSON found in response")
                    return []
                    
                json_str = text_content[json_start:json_end]
            else:
                json_str = text_content[json_start + 7:json_end]
            
            # Parse JSON
            annotation_data = json.loads(json_str)
            
            # Convert to Annotation objects
            annotations = []
            for ann_data in annotation_data.get('annotations', []):
                bbox_data = ann_data.get('bbox', {})
                bbox = BoundingBox(
                    x=bbox_data.get('x', 0),
                    y=bbox_data.get('y', 0),
                    width=bbox_data.get('width', 0),
                    height=bbox_data.get('height', 0)
                )
                
                annotation = Annotation(
                    type=ann_data.get('type', ''),
                    bbox=bbox,
                    value=ann_data.get('value', ''),
                    confidence=ann_data.get('confidence', 0.0)
                )
                
                # Validate annotation
                if self._validate_annotation(annotation):
                    annotations.append(annotation)
                else:
                    logger.warning(f"Invalid annotation filtered out: {annotation.type}")
            
            return annotations
            
        except json.JSONDecodeError as e:
            logger.error(f"JSON parse error: {str(e)}")
            return []
        except Exception as e:
            logger.error(f"Response parsing error: {str(e)}")
            return []
    
    def test_connection(self) -> bool:
        """Test Claude API connection"""
        try:
            # Create a simple test image
            test_image = Image.new('RGB', (100, 100), color='white')
            image_b64 = self._image_to_base64(test_image)
            
            # Simple test prompt
            test_prompt = "Please describe what you see in this image in one sentence."
            
            # Make test request
            response_data = self._make_api_request(image_b64, test_prompt)
            
            if response_data and 'content' in response_data:
                logger.info("Claude API connection successful")
                return True
            else:
                logger.error("Claude API connection failed")
                return False
                
        except Exception as e:
            logger.error(f"Claude API connection test failed: {str(e)}")
            return False 