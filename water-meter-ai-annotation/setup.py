#!/usr/bin/env python3
"""
Setup script for Water Meter AI Annotation project
"""
import os
import sys
import json
from pathlib import Path


def create_directories():
    """Create required project directories"""
    directories = [
        'config',
        'src/ai_annotation',
        'src/data_processing', 
        'src/cvat_integration',
        'src/training',
        'src/utils',
        'data/raw_images',
        'data/ai_annotations',
        'data/cvat_exports',
        'data/final_dataset',
        'models/checkpoints',
        'models/trained_models',
        'models/tflite_models',
        'scripts',
        'tests',
        'docs',
        'deployment'
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        
        # Create __init__.py files for Python packages
        if directory.startswith('src/'):
            init_file = Path(directory) / '__init__.py'
            if not init_file.exists():
                init_file.write_text('')
    
    print("✓ Project directories created")


def create_sample_images():
    """Create sample test images"""
    sample_dir = Path('data/raw_images')
    sample_dir.mkdir(parents=True, exist_ok=True)
    
    # Create a simple README for the sample directory
    readme_content = """# Sample Images Directory

Place your water meter images here for processing.

Supported formats:
- JPG/JPEG
- PNG
- BMP

Example usage:
```bash
python scripts/run_ai_annotation.py --input data/raw_images --output data/ai_annotations
```
"""
    
    readme_file = sample_dir / 'README.md'
    readme_file.write_text(readme_content)
    
    print("✓ Sample directory structure created")


def setup_config():
    """Setup configuration files"""
    config_dir = Path('config')
    config_file = config_dir / 'ai_config.json'
    
    if not config_file.exists():
        print("⚠ Config file not found. Please edit config/ai_config.json with your API keys")
        return False
    
    try:
        with open(config_file, 'r') as f:
            config = json.load(f)
        
        # Check for API keys
        claude_key = config.get('ai_providers', {}).get('claude', {}).get('api_key', '')
        openai_key = config.get('ai_providers', {}).get('openai', {}).get('api_key', '')
        
        if 'your_anthropic_api_key_here' in claude_key:
            print("⚠ Please update your Anthropic API key in config/ai_config.json")
            return False
            
        if 'your_openai_api_key_here' in openai_key:
            print("⚠ Please update your OpenAI API key in config/ai_config.json")
            return False
            
        print("✓ Configuration validated")
        return True
        
    except json.JSONDecodeError:
        print("✗ Invalid JSON in config file")
        return False
    except Exception as e:
        print(f"✗ Config validation error: {str(e)}")
        return False


def test_dependencies():
    """Test that required dependencies are installed"""
    required_packages = [
        'requests',
        'pillow',
        'numpy',
        'anthropic',
        'openai'
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"✗ Missing packages: {', '.join(missing_packages)}")
        print("Please install with: pip install -r requirements.txt")
        return False
    
    print("✓ All dependencies installed")
    return True


def main():
    """Main setup function"""
    print("Water Meter AI Annotation System Setup")
    print("=" * 40)
    
    # Create directories
    create_directories()
    
    # Create sample structure
    create_sample_images()
    
    # Test dependencies
    deps_ok = test_dependencies()
    
    # Setup config
    config_ok = setup_config()
    
    print("\nSetup Summary:")
    print("=" * 40)
    
    if deps_ok and config_ok:
        print("✓ Setup completed successfully!")
        print("\nNext steps:")
        print("1. Add your water meter images to data/raw_images/")
        print("2. Test API connection: python scripts/run_ai_annotation.py --test-connection")
        print("3. Run annotation: python scripts/run_ai_annotation.py --input data/raw_images --output data/ai_annotations")
    else:
        print("⚠ Setup completed with warnings")
        print("Please resolve the issues above before running the annotation system")
    
    print("\nFor more information, see README.md")


if __name__ == '__main__':
    main() 