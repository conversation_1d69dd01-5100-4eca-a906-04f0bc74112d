# 水表AI标注系统完整设置指南

## 步骤1: 创建Python虚拟环境

### Windows用户：

```bash
# 创建虚拟环境
python -m venv water-meter-ai-env

# 激活虚拟环境
water-meter-ai-env\Scripts\activate

# 或者使用提供的批处理文件
activate_env.bat
```

### Linux/Mac用户：

```bash
# 创建虚拟环境
python3 -m venv water-meter-ai-env

# 激活虚拟环境
source water-meter-ai-env/bin/activate
```

## 步骤2: 安装依赖

```bash
# 确保虚拟环境已激活
pip install --upgrade pip
pip install -r requirements.txt
```

## 步骤3: 配置API密钥

编辑 `config/ai_config.json` 文件：

```json
{
  "ai_providers": {
    "claude": {
      "api_key": "sk-ant-api03-YOUR_ACTUAL_CLAUDE_KEY_HERE"
    },
    "openai": {
      "api_key": "sk-YOUR_ACTUAL_OPENAI_KEY_HERE"
    }
  }
}
```

## 步骤4: 运行项目设置

```bash
python setup.py
```

## 步骤5: 图片质量检查（重要！）

在运行AI标注之前，建议先检查图片质量：

```bash
# 检查图片质量
python scripts/check_image_quality.py --input data/raw_images

# 自动分离好坏图片
python scripts/check_image_quality.py --input data/raw_images --filter

# 保存详细报告
python scripts/check_image_quality.py --input data/raw_images --output quality_report.json
```

### 图片质量检查结果解读：

- **Overall Score**: 0.8+ (优秀) | 0.6-0.8 (良好) | 0.4-0.6 (一般) | <0.4 (差)
- **Should Keep**: 建议保留用于标注的图片
- **Difficulty**: 标注难度等级
- **Issues**: 主要问题（亮度、对比度、模糊、噪声等）

### 图片质量建议：

**应该保留的图片：**
- 分辨率 ≥ 800x600
- 清晰度好，数字可读
- 亮度适中，不过暗或过亮
- 对比度足够，数字与背景区分明显

**应该丢弃的图片：**
- 分辨率过低 < 800x600
- 严重模糊或噪声
- 过度曝光或过暗
- 数字完全不可读

## 步骤6: 测试API连接

```bash
python scripts/run_ai_annotation.py --test-connection
```

## 步骤7: 运行AI标注

```bash
# 使用筛选后的好图片
python scripts/run_ai_annotation.py --input data/good_images --output data/ai_annotations

# 或者使用原始图片目录
python scripts/run_ai_annotation.py --input data/raw_images --output data/ai_annotations
```

## 完整工作流程示例

```bash
# 1. 激活虚拟环境
activate_env.bat

# 2. 将水表图片放入原始目录
# 手动复制图片到 data/raw_images/

# 3. 检查图片质量并自动分离
python scripts/check_image_quality.py --input data/raw_images --filter

# 4. 查看质量报告
# 检查终端输出，确认有多少图片被保留

# 5. 测试API连接
python scripts/run_ai_annotation.py --test-connection

# 6. 运行AI标注（仅对好图片）
python scripts/run_ai_annotation.py --input data/good_images --output data/ai_annotations

# 7. 查看标注结果
# 查看 data/ai_annotations/annotation_summary.json
```

## 常见问题解决

### Q1: 虚拟环境激活失败？
```bash
# Windows PowerShell 可能需要修改执行策略
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser

# 然后重新激活
activate_env.bat
```

### Q2: 图片质量都很差怎么办？
```bash
# 降低质量阈值
python scripts/check_image_quality.py --input data/raw_images --threshold 0.4

# 或者手动筛选后再运行
python scripts/run_ai_annotation.py --input data/raw_images --output data/ai_annotations
```

### Q3: API连接失败？
1. 检查网络连接
2. 确认API密钥正确
3. 检查API配额是否充足
4. 尝试使用备用API（Claude/OpenAI）

### Q4: 依赖安装失败？
```bash
# 升级pip
pip install --upgrade pip

# 清理缓存重新安装
pip cache purge
pip install -r requirements.txt --no-cache-dir
```

## 效率提升提示

1. **批量处理**: 一次处理多张图片而不是单张
2. **质量预筛选**: 使用质量检查工具节省API调用成本
3. **监控成本**: 注意API使用量和成本
4. **保存中间结果**: 定期保存标注结果避免重复处理

## 成功标志

✅ 虚拟环境正常激活  
✅ 所有依赖包安装成功  
✅ API连接测试通过  
✅ 图片质量检查完成  
✅ AI标注成功运行  
✅ 生成标注结果文件  

完成以上步骤后，您就可以开始使用水表AI标注系统了！ 