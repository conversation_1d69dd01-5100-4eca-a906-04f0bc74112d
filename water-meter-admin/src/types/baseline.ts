// Baseline Record List DTO for table display
export interface BaselineRecordListDto {
  id: number;
  meterId: number;
  meterSerialNumber: string;
  meterLocation: string;
  baselineDate: string;
  baselineValue: number;
  baselineType: string;
  status: string;
  dataSource: string;
  isValidated: boolean;
  hasValidationErrors: boolean;
  isAnomalous: boolean;
  variancePercentage?: number;
}

// Full Baseline Record DTO
export interface BaselineRecordDto {
  id: number;
  meterId: number;
  meterSerialNumber?: string;
  meterLocation?: string;
  baselineDate: string;
  baselineValue: number;
  baselineType: string;
  status: string;
  importBatch?: string;
  sourceFile?: string;
  sourceRowNumber?: number;
  dataSource: string;
  validationNotes?: string;
  isValidated: boolean;
  validatedDate?: string;
  validatedBy?: string;
  hasValidationErrors: boolean;
  validationErrors?: string;
  isAnomalous: boolean;
  anomalyDescription?: string;
  previousBaselineId?: number;
  previousBaselineValue?: number;
  varianceFromPrevious?: number;
  variancePercentage?: number;
  isCorrected: boolean;
  correctedDate?: string;
  correctedBy?: string;
  correctionReason?: string;
  createdAt: string;
  updatedAt: string;
}

// Create Baseline Record DTO
export interface CreateBaselineRecordDto {
  meterId: number;
  baselineDate: Date;
  baselineValue: number;
  baselineType: string;
  status: string;
  dataSource: string;
  validationNotes?: string;
}

// Update Baseline Record DTO
export interface UpdateBaselineRecordDto {
  baselineDate: Date;
  baselineValue: number;
  baselineType: string;
  status: string;
  validationNotes?: string;
  isValidated: boolean;
}

// Baseline Search DTO
export interface BaselineSearchDto {
  page: number;
  pageSize: number;
  sortBy?: string;
  sortDirection?: string;
  serialNumber?: string;
  location?: string;
  baselineType?: string;
  status?: string;
  dataSource?: string;
  baselineDateFrom?: Date;
  baselineDateTo?: Date;
  isValidated?: boolean;
  hasValidationErrors?: boolean;
  isAnomalous?: boolean;
  importBatch?: string;
}

// Baseline Search Response (matches backend PaginatedResponse)
export interface BaselineSearchResponseDto {
  data: BaselineRecordListDto[];
  totalCount: number;
  page: number;
  pageSize: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

// Baseline Import DTO
export interface BaselineImportDto {
  rowNumber: number;
  meterId?: number;
  serialNumber: string;
  baselineDate: Date;
  baselineValue: number;
  baselineType: string;
  status: string;
  validationNotes?: string;
  validationErrors: string[];
}

// Baseline Import Result DTO
export interface BaselineImportResultDto {
  totalRows: number;
  successfulRows: number;
  failedRows: number;
  newRows?: number;
  updatedRows?: number;
  failedRecords: BaselineImportDto[];
  importBatch: string;
  fileName: string;
  importDate: Date;
}

// Baseline History DTO
export interface BaselineHistoryDto {
  meterId: number;
  meterSerialNumber: string;
  meterLocation: string;
  baselineRecords: BaselineRecordDto[];
  totalRecords: number;
  firstBaselineDate?: Date;
  lastBaselineDate?: Date;
  currentBaseline?: number;
  previousBaseline?: number;
  totalChange?: number;
  averageMonthlyChange?: number;
}

// Baseline Anomaly DTO
export interface BaselineAnomalyDto {
  id: number;
  meterId: number;
  meterSerialNumber: string;
  baselineDate: string;
  baselineValue: number;
  previousBaselineValue?: number;
  varianceFromPrevious?: number;
  variancePercentage?: number;
  anomalyDescription?: string;
  status: string;
  createdAt: string;
}

// Baseline Validation DTO
export interface BaselineValidationDto {
  baselineId: number;
  isValid: boolean;
  validatedBy?: string;
  validationNotes?: string;
  validationErrors: string[];
}

// Batch Validation DTO
export interface BatchValidationDto {
  baselineIds: number[];
  isValid: boolean;
  validationNotes?: string;
  validatedBy: string;
}

// Batch Validation Result DTO
export interface BatchValidationResultDto {
  totalRequested: number;
  successful: number;
  failed: number;
  errors: string[];
  processedAt: string;
  processedBy: string;
}

// Baseline Correction DTO
export interface BaselineCorrectionDto {
  baselineId: number;
  newBaselineValue: number;
  correctionReason: string;
  notes?: string;
}

// Baseline Statistics DTO
export interface BaselineStatisticsDto {
  totalBaselines: number;
  activeBaselines: number;
  todayBaselines: number;
  weeklyBaselines: number;
  monthlyBaselines: number;
  unvalidatedBaselines: number;
  anomalousBaselines: number;
  errorBaselines: number;
  importedBaselines: number;
  averageVariance?: number;
  lastImportDate?: string;
}

// Meter Baseline Summary DTO
export interface MeterBaselineSummaryDto {
  meterId: number;
  meterSerialNumber: string;
  totalBaselines: number;
  currentBaseline?: number;
  lastBaselineDate?: string;
  hasAnomalies: boolean;
  pendingValidation: number;
}

// Baseline Trend DTO
export interface BaselineTrendDto {
  date: string;
  value: number;
  variance?: number;
  isAnomalous: boolean;
}

// Baseline Export DTO
export interface BaselineExportDto {
  meterSerialNumber: string;
  meterLocation: string;
  baselineDate: string;
  baselineValue: number;
  baselineType: string;
  status: string;
  dataSource: string;
  isValidated: boolean;
  validationNotes?: string;
  previousValue?: number;
  variance?: number;
  variancePercentage?: number;
  isAnomalous: boolean;
  createdDate: string;
} 