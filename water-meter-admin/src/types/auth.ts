export interface LoginRequest {
  username: string;
  password: string;
}

export interface LoginResponse {
  success: boolean;
  message: string;
  token?: string;
  user?: {
    id: number;
    username: string;
    fullName: string;
    personId: number;
    finCoCode: string;
    isAuthenticated: boolean;
    lastLogin?: string;
  };
}

export interface User {
  id: number;
  username: string;
  fullName: string;
  personId: number;
  finCoCode: string;
  isAuthenticated: boolean;
  lastLogin?: string;
} 