export interface UserRoleDto {
  userId: number;
  roleId: number;
  userName: string;
  roleName: string;
  roleDescription: string;
  createdAt: string;
  createdBy: string;
}

export interface AssignRolesDto {
  roleIds: number[];
}

export interface UserRoleAssignmentResultDto {
  userId: number;
  userName: string;
  results: RoleAssignmentDetailDto[];
  successCount: number;
  errorCount: number;
}

export interface RoleAssignmentDetailDto {
  roleId: number;
  roleName: string;
  status: 'Created' | 'Restored' | 'Skipped' | 'Error';
  message: string;
}

export interface Role {
  id: number;
  name: string;
  description: string;
  createdAt: string;
  updatedAt: string;
  isDeleted: boolean;
}

export interface ApiResponse<T = any> {
  code: number;
  success: boolean;
  message: string;
  data?: T;
}

export interface UserRoleFormData {
  userId: number;
  selectedRoleIds: number[];
}

export interface UserRoleTableData extends UserRoleDto {
  key: string;
}
