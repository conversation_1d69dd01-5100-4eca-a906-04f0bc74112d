// AMIS Sync List DTO for table display
export interface AmisSyncListDto {
  id: number;
  syncType: string;
  status: string;
  startTime: string;
  endTime?: string;
  duration?: number;
  totalRecords?: number;
  successfulRecords?: number;
  failedRecords?: number;
  progressPercentage?: number;
  errorMessage?: string;
}

// Full AMIS Sync DTO
export interface AmisSyncDto {
  id: number;
  syncType: string;
  status: string;
  startTime: string;
  endTime?: string;
  duration?: number;
  triggerBy?: string;
  totalRecords?: number;
  processedRecords?: number;
  successfulRecords?: number;
  failedRecords?: number;
  skippedRecords?: number;
  progressPercentage?: number;
  currentOperation?: string;
  resultSummary?: string;
  errorMessage?: string;
  recordsPerSecond?: number;
  dataSizeMB?: number;
  retryCount?: number;
  maxRetries?: number;
  nextRetryTime?: string;
  amisEndpoint?: string;
  amisVersion?: string;
  lastSyncDate?: string;
  createdAt: string;
  updatedAt: string;
}

// AMIS Sync Search DTO
export interface AmisSyncSearchDto {
  page: number;
  pageSize: number;
  sortBy?: string;
  sortDirection?: string;
  syncType?: string;
  status?: string;
  triggerBy?: string;
  startTimeFrom?: Date;
  startTimeTo?: Date;
}

// AMIS Sync Search Response
export interface AmisSyncSearchResponseDto {
  syncs: AmisSyncListDto[];
  totalCount: number;
}

// Manual Sync Request DTO
export interface ManualSyncRequestDto {
  syncType: string;
  fromDate?: Date;
  toDate?: Date;
  forceFullSync?: boolean;
  notes?: string;
}

// Sync Progress DTO
export interface SyncProgressDto {
  syncId: number;
  status: string;
  progressPercentage?: number;
  currentOperation?: string;
  processedRecords?: number;
  totalRecords?: number;
  errorMessage?: string;
  lastUpdate: string;
}

// AMIS Sync Error DTO
export interface AmisSyncErrorDto {
  id: number;
  amisSyncId: number;
  errorType: string;
  severity: string;
  errorMessage: string;
  detailedError?: string;
  recordIdentifier?: string;
  recordLineNumber?: number;
  operation?: string;
  endpoint?: string;
  httpStatusCode?: number;
  resolutionStatus: string;
  resolvedDate?: string;
  resolvedBy?: string;
  resolutionNotes?: string;
  canRetry: boolean;
  retryCount?: number;
  lastRetryDate?: string;
  createdAt: string;
}

// AMIS Sync Configuration DTO
export interface AmisSyncConfigurationDto {
  amisEndpoint?: string;
  timeoutSeconds: number;
  batchSize: number;
  enableRetry: boolean;
  maxRetries: number;
  retryDelaySeconds: number;
  syncTypes: string[];
}

// Sync Statistics DTO
export interface SyncStatisticsDto {
  totalSyncs: number;
  activeSyncs: number;
  todaySyncs: number;
  weeklySyncs: number;
  monthlySyncs: number;
  successfulSyncs: number;
  failedSyncs: number;
  unresolvedErrors: number;
  averageDuration?: number;
  lastSyncDate?: string;
}

// Error Resolution DTO
export interface ErrorResolutionDto {
  errorId: number;
  resolutionNotes: string;
  resolvedBy: string;
}

// Retry Configuration DTO
export interface RetryConfigurationDto {
  syncId: number;
  maxRetries: number;
  retryDelaySeconds: number;
  retryBy: string;
} 