// Configuration Management Types

export interface ConfigurationItem {
  key: string
  displayName: string
  description?: string
  dataType: 'string' | 'number' | 'boolean' | 'array' | 'object'
  defaultValue?: any
  value?: any
  validationRules?: any
  uiComponent: 'input' | 'number' | 'switch' | 'select' | 'radio' | 'checkbox' | 'slider' | 'timepicker' | 'textarea'
  uiProperties?: any
  displayOrder: number
  requiresRestart?: boolean
  isSensitive?: boolean
  isReadOnly?: boolean
  isInherited?: boolean
  inheritedFrom?: string
}

export interface ConfigurationGroup {
  group: string
  groupDisplayName: string
  items: ConfigurationItem[]
}

export interface ConfigurationCategory {
  category: string
  categoryDisplayName: string
  categoryIcon?: string
  groups: ConfigurationGroup[]
}

export interface GetConfigurationRequest {
  scope: 'System' | 'User' | 'Role'
  category?: string
  userId?: number
  roleId?: number
  includeInherited?: boolean
}

export interface UpdateConfigurationRequest {
  scope: 'System' | 'User' | 'Role'
  category: string
  configurations: Array<{
    key: string
    value: any
  }>
}

export interface ConfigurationUpdateResponse {
  success: boolean
  message: string
  updatedCount: number
  failedCount: number
  errors: string[]
  requiresRestart: string[]
}

export interface ConfigurationService {
  getConfigurations(request: GetConfigurationRequest): Promise<ConfigurationCategory[]>
  updateConfigurations(request: UpdateConfigurationRequest): Promise<ConfigurationUpdateResponse>
  getConfigurationValue<T>(scope: string, category: string, key: string, userId?: number, roleId?: number): Promise<T>
  setConfigurationValue<T>(scope: string, category: string, key: string, value: T, userId?: number, roleId?: number): Promise<boolean>
  resetToDefaults(scope: string, category: string, userId?: number, roleId?: number): Promise<ConfigurationUpdateResponse>
  initializeDefaults(): Promise<boolean>
}

// UI Component Props Types
export interface ConfigurationFormProps {
  scope: 'System' | 'User' | 'Role'
  categories: ConfigurationCategory[]
  loading?: boolean
  onSave?: (category: string, values: Record<string, any>) => Promise<void>
  onReset?: (category: string) => Promise<void>
  userId?: number
  roleId?: number
}

export interface ConfigurationItemProps {
  item: ConfigurationItem
  value?: any
  onChange?: (key: string, value: any) => void
  disabled?: boolean
}

// System Settings specific types
export interface SystemSettings {
  appName: string
  appVersion: string
  apiTimeout: number
  workbenchApiUrl: string
  logLevel: string
  maxLogFiles: number
}

export interface SecuritySettings {
  jwtExpirationDays: number
  passwordMinLength: number
  passwordRequireNumbers: boolean
  passwordRequireSymbols: boolean
  sessionTimeoutMinutes: number
  maxConcurrentSessions: number
  enableAuditLog: boolean
}

export interface NotificationSettings {
  enableEmailNotifications: boolean
  smtpServer: string
  smtpPort: number
  smtpUsername: string
  smtpPassword: string
  emailFrom: string
  enableSmsNotifications: boolean
  smsProvider: string
  smsApiKey: string
}

// User Settings specific types
export interface PersonalPreferences {
  theme: 'light' | 'dark' | 'auto'
  language: string
  timezone: string
  dateFormat: string
  timeFormat: '12h' | '24h'
  pageSize: number
  autoSave: boolean
  compactMode: boolean
}

export interface NotificationPreferences {
  emailNotifications: boolean
  browserNotifications: boolean
  desktopNotifications: boolean
  soundNotifications: boolean
  weeklyReport: boolean
  systemAlerts: boolean
  maintenanceNotices: boolean
  notificationTime: {
    start: string
    end: string
  }
}

export interface PrivacySettings {
  showOnlineStatus: boolean
  showLastSeen: boolean
  allowProfileView: boolean
  showEmail: boolean
  showPhone: boolean
  sessionTimeout: number
  autoLogout: boolean
} 