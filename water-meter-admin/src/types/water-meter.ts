// Water Meter List DTO for table display
export interface WaterMeterListDto {
  id: number;
  serialNumber: string;
  location: string;
  meterType: string;
  status: string;
  lastReadingDate?: string;
  lastRead?: number;
  customerName?: string;
  batteryLevel?: number;
  installDate?: string;
  // GPS Coordinates
  latitude?: number;
  longitude?: number;
  // AMS Integration fields
  assetId?: string;
  meterNumber?: string;
  accountNumber?: string;
  township?: string;
  roadName?: string;
  syncStatus?: string;
  lastSyncDate?: string;
}

// Full Water Meter DTO
export interface WaterMeterDto {
  id: number;
  serialNumber: string;
  location: string;
  address?: string;
  meterType: string;
  status: string;
  installDate?: string;
  lastReadingDate?: string;
  lastReading?: number;
  lastRead?: number;
  customerCode?: string;
  customerName?: string;
  latitude?: number;
  longitude?: number;
  brand?: string;
  model?: string;
  batteryLevel?: number;
  communicationMethod?: string;
  lastMaintenanceDate?: string;
  nextMaintenanceDate?: string;
  notes?: string;
  createdAt: string;
  updatedAt: string;
  // AMS Integration fields
  assetId?: string;
  meterNumber?: string;
  accountNumber?: string;
  bookNumber?: string;
  unit?: number;
  roadNumber?: number;
  roadName?: string;
  township?: string;
  subArea?: string;
  recentChange?: number;
  subd?: string;
  dateOfRead?: string;
  read?: number;
  cantRead?: boolean;
  condition?: string;
  comments?: string;
  syncStatus?: string;
  lastSyncDate?: string;
  source?: string;
}

// Create Water Meter DTO
export interface CreateWaterMeterDto {
  serialNumber: string;
  location: string;
  address?: string;
  meterType: string;
  status: string;
  installDate?: Date;
  customerCode?: string;
  customerName?: string;
  latitude?: number;
  longitude?: number;
  brand?: string;
  model?: string;
  communicationMethod?: string;
  notes?: string;

  // AMS Integration fields
  accountNumber?: string;
  assetId?: string;

  // Structured address fields (from address parsing)
  roadNumber?: number;
  roadName?: string;
  township?: string;
  subArea?: string;
}

// Update Water Meter DTO
export interface UpdateWaterMeterDto {
  location?: string;
  address?: string;
  meterType?: string;
  status?: string;
  installDate?: Date;
  accountNumber?: string;
  customerCode?: string;
  customerName?: string;
  latitude?: number;
  longitude?: number;
  brand?: string;
  model?: string;
  communicationMethod?: string;
  lastMaintenanceDate?: Date;
  nextMaintenanceDate?: Date;
  notes?: string;
}

// Water Meter Search DTO
export interface WaterMeterSearchDto {
  page: number;
  pageSize: number;
  sortBy?: string;
  sortDirection?: string;
  workPackageAssignment?: string; // 'unassigned' | 'assigned' | 'all'
  serialNumber?: string;
  location?: string;
  meterType?: string;
  status?: string;
  customerName?: string;
  installDateFrom?: Date;
  installDateTo?: Date;
}

// Water Meter Search Response (matches backend PaginatedResponse)
export interface WaterMeterSearchResponseDto {
  data: WaterMeterListDto[];
  totalCount: number;
  page: number;
  pageSize: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

// Water Meter Import DTO
export interface WaterMeterImportDto {
  rowNumber: number;
  serialNumber: string;
  location: string;
  address?: string;
  meterType: string;
  status: string;
  installDate?: Date;
  customerCode?: string;
  customerName?: string;
  latitude?: number;
  longitude?: number;
  brand?: string;
  model?: string;
  communicationMethod?: string;
  notes?: string;
  validationErrors: string[];
}

// Water Meter Import Result DTO
export interface WaterMeterImportResultDto {
  totalRows: number;
  successfulRows: number;
  failedRows: number;
  failedRecords: WaterMeterImportDto[];
}

// Meter Reading DTO
export interface MeterReadingDto {
  id: number;
  meterId: number;
  readingDate: string;
  readingValue: number;
  previousReading?: number;
  consumption?: number;
  readingType: string;
  status: string;
  qualityScore?: number;
  dataSource: string;
  notes?: string;
  isValidated: boolean;
  validatedBy?: string;
  validatedDate?: string;
  createdAt: string;
  updatedAt: string;
}

// Create Meter Reading DTO
export interface CreateMeterReadingDto {
  meterId: number;
  readingDate: Date;
  readingValue: number;
  readingType: string;
  dataSource: string;
  notes?: string;
}

// Water Meter Statistics DTO
export interface WaterMeterStatisticsDto {
  totalMeters: number;
  activeMeters: number;
  inactiveMeters: number;
  maintenanceMeters: number;
  lowBatteryMeters: number;
  metersByType: { [key: string]: number };
  metersByLocation: { [key: string]: number };
  averageBatteryLevel: number;
  lastSyncDate?: string;
}

// AMS Excel Import DTO
export interface AmsExcelImportDto {
  file: File;
}

// AMS Import Result DTO
export interface AmsImportResultDto {
  success: boolean;
  metersProcessed: number;
  metersImported: number;
  metersSkipped: number;
  routesProcessed: number;
  routesImported: number;
  errors: string[];
  warnings: string[];
  summary: string;
}

// Work Package Import Result DTO
export interface WorkPackageImportResultDto {
  success: boolean;
  message: string;
  totalRows: number;
  successCount: number;
  failureCount: number;
  errors: string[];
  importedWorkPackages: WorkPackageListDto[];
}

// Work Package List DTO for display
export interface WorkPackageListDto {
  id: number;
  name: string;
  description?: string;
  packageType: string;
  status: string;
  plannedStartDate: string;
  plannedEndDate: string;
  totalMeters: number;
  completedMeters?: number;
  createdBy: string;
  createdAt: string;
}

// Work Package Item DTO
export interface WorkPackageItemDto {
  id: number;
  workPackageId: number;
  meterId: number;
  sequenceOrder: number;
  status: string;
  scheduledDate?: string;
  actualDate?: string;
  assignedTo?: string;
  priority?: string;
  estimatedMinutes?: number;
  actualMinutes?: number;
  specialInstructions?: string;
  notes?: string;
  lastReading?: string;
  requiresSpecialHandling: boolean;
  specialHandlingReason?: string;
  difficultyRating?: number;
  latitude?: number; // Coordinates for map display
  longitude?: number; // Coordinates for map display
  serviceAddress?: string;
  propertyDetails?: string;
  accessNotes?: string;
  hasPhoto: boolean;
  photoUrl?: string;
  qrCode?: string;
  meterSerialNumber?: string;
  meterType?: string;
  meterSize?: string;
  createdAt: string;
  updatedAt?: string;
  // Computed properties
  isCompleted: boolean;
  canRetry: boolean;
  isOverdue: boolean;
  efficiencyScore?: number;
  meterLocation?: string;
  meterDescription?: string;
}

// Work Package DTO
export interface WorkPackageDto {
  id: number;
  name: string;
  description?: string;
  packageType: string;
  status: string;
  plannedStartDate: string;
  plannedEndDate: string;
  actualStartDate?: string;
  actualEndDate?: string;
  frequency: string;
  serviceArea: string;
  subArea?: string;
  priority: string;
  totalMeters: number;
  completedMeters: number;
  progressPercentage: number;
  assignedTeam?: string;
  estimatedHours: number;
  actualHours?: number;
  estimatedCost?: number;
  actualCost?: number;
  isTemplate: boolean;
  templateCategory?: string;
  isRecurring: boolean;
  recurrencePattern?: string;
  recurrenceInterval?: number;
  lastExecuted?: string;
  nextExecution?: string;
  notes?: string;
  instructions?: string;
  amsImportBatch?: string;
  amsImportDate?: string;
  source: string;
  createdBy: string;
  updatedBy?: string;
  createdAt: string;
  updatedAt?: string;
  // Computed properties
  isStarted: boolean;
  isCompleted: boolean;
  isOverdue: boolean;
  remainingDays?: number;
  completionRate: number;
  // Related data
  items: WorkPackageItemDto[];
}

// Create Work Package DTO
export interface CreateWorkPackageDto {
  name: string;
  description?: string;
  packageType: string;
  plannedStartDate: string;
  plannedEndDate: string;
  frequency: string;
  serviceArea: string;
  subArea?: string;
  priority: string;
  assignedTeam?: string;
  estimatedHours: number;
  estimatedCost?: number;
  isTemplate: boolean;
  templateCategory?: string;
  isRecurring: boolean;
  recurrencePattern?: string;
  recurrenceInterval?: number;
  notes?: string;
  instructions?: string;
  meterIds: number[];
}

// Update Work Package DTO
export interface UpdateWorkPackageDto {
  name: string;
  description?: string;
  packageType: string;
  plannedStartDate: string;
  plannedEndDate: string;
  actualStartDate?: string;
  actualEndDate?: string;
  frequency: string;
  serviceArea: string;
  subArea?: string;
  priority: string;
  assignedTeam?: string;
  estimatedHours: number;
  actualHours?: number;
  estimatedCost?: number;
  actualCost?: number;
  isTemplate: boolean;
  templateCategory?: string;
  isRecurring: boolean;
  recurrencePattern?: string;
  recurrenceInterval?: number;
  nextExecution?: string;
  notes?: string;
  instructions?: string;
} 