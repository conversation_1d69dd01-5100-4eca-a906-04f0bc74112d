// Device Sync Status Types
export interface DeviceSyncStatusDto {
  id: number;
  deviceId: string;
  deviceName: string;
  lastSync: string;
  syncStatus: 'online' | 'syncing' | 'offline' | 'error';
  batteryLevel: number;
  dataIntegrity: number;
  location: string;
  firmwareVersion: string;
  connectionType: 'wifi' | 'cellular' | 'bluetooth';
  signalStrength: number;
  errorMessage?: string;
  pendingDataCount: number;
  lastHeartbeat: string;
  userId?: string;
  syncProgress?: number;
  estimatedTimeRemaining?: number;
}

export interface DeviceSyncSearchParams {
  searchTerm?: string;
  syncStatus?: string;
  connectionType?: string;
  batteryLevel?: {
    min: number;
    max: number;
  };
  location?: string;
  userId?: string;
  pageIndex?: number;
  pageSize?: number;
}

export interface DeviceSyncStats {
  totalDevices: number;
  onlineDevices: number;
  syncingDevices: number;
  offlineDevices: number;
  errorDevices: number;
  averageBatteryLevel: number;
  averageDataIntegrity: number;
  totalPendingData: number;
}

// Sync Queue Types
export interface SyncQueueDto {
  id: number;
  taskType: 'data_sync' | 'file_upload' | 'report_generation' | 'batch_update' | 'config_sync';
  description: string;
  deviceId?: string;
  userId?: string;
  priority: 'critical' | 'high' | 'medium' | 'low';
  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled' | 'paused';
  progress: number;
  estimatedDuration: number;
  actualDuration?: number;
  dataSize: number;
  createdAt: string;
  startedAt?: string;
  completedAt?: string;
  errorMessage?: string;
  retryCount: number;
  maxRetries: number;
  dependencies?: number[];
}

export interface SyncQueueSearchParams {
  searchTerm?: string;
  taskType?: string;
  status?: string;
  priority?: string;
  deviceId?: string;
  userId?: string;
  dateRange?: {
    start: string;
    end: string;
  };
  pageIndex?: number;
  pageSize?: number;
}

export interface SyncQueueStats {
  totalTasks: number;
  pendingTasks: number;
  runningTasks: number;
  completedTasks: number;
  failedTasks: number;
  averageWaitTime: number;
  averageExecutionTime: number;
  totalDataSize: number;
  throughputPerHour: number;
}

// Offline Data Types
export interface OfflineDataDto {
  id: number;
  dataType: 'meter_reading' | 'photo' | 'task_data' | 'user_settings' | 'location_data';
  description: string;
  deviceId: string;
  dataSize: number;
  status: 'stored' | 'uploading' | 'uploaded' | 'failed' | 'conflict';
  storageLocation: string;
  createdAt: string;
  lastModified: string;
  uploadAttempts: number;
  errorMessage?: string;
  checksum: string;
  compressionRatio?: number;
  metadata?: Record<string, any>;
}

export interface OfflineDataSearchParams {
  searchTerm?: string;
  dataType?: string;
  status?: string;
  deviceId?: string;
  startDate?: string;
  endDate?: string;
  pageIndex?: number;
  pageSize?: number;
}

export interface OfflineDataStats {
  totalItems: number;
  pendingUpload: number;
  failedItems: number;
  totalStorageSize: number;
  storageUsedPercentage: number;
  averageFileSize: number;
  oldestItem: string;
  compressionSavings: number;
}

// Sync Logs Types
export interface SyncLogDto {
  id: number;
  timestamp: string;
  level: 'info' | 'warning' | 'error' | 'success' | 'debug';
  operation: string;
  message: string;
  details?: string;
  stackTrace?: string;
  deviceId?: string;
  userId?: string;
  duration?: number;
  dataSize?: number;
  correlationId: string;
  sessionId?: string;
  ipAddress?: string;
  userAgent?: string;
}

export interface SyncLogSearchParams {
  searchTerm?: string;
  level?: string;
  operation?: string;
  deviceId?: string;
  userId?: string;
  startDate?: string;
  endDate?: string;
  correlationId?: string;
  pageIndex?: number;
  pageSize?: number;
}

export interface SyncLogStats {
  totalLogs: number;
  errorLogs: number;
  warningLogs: number;
  successfulSyncs: number;
  averageOperationTime: number;
  mostCommonOperations: Array<{
    operation: string;
    count: number;
  }>;
  errorRate: number;
  logsPerHour: number;
}

// Sync Center Configuration Types
export interface SyncCenterConfig {
  autoSyncEnabled: boolean;
  syncInterval: number;
  maxRetryAttempts: number;
  batchSize: number;
  compressionEnabled: boolean;
  offlineStorageLimit: number;
  logRetentionDays: number;
  priorityRules: Array<{
    condition: string;
    priority: 'critical' | 'high' | 'medium' | 'low';
  }>;
}

// API Response Types
export interface SyncCenterApiResponse<T> {
  data: T;
  success: boolean;
  message?: string;
  totalCount?: number;
  pageIndex?: number;
  pageSize?: number;
}

export interface SyncCenterPaginatedResponse<T> extends SyncCenterApiResponse<T[]> {
  totalPages: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

// Bulk Operations Types
export interface BulkSyncOperation {
  operation: 'sync' | 'pause' | 'cancel' | 'retry' | 'delete';
  taskIds: number[];
  reason?: string;
}

export interface BulkSyncResult {
  successCount: number;
  failureCount: number;
  results: Array<{
    taskId: number;
    success: boolean;
    message?: string;
  }>;
}

// Real-time Updates Types
export interface SyncStatusUpdate {
  type: 'device_status' | 'queue_update' | 'sync_complete' | 'error_occurred';
  deviceId?: string;
  taskId?: number;
  data: any;
  timestamp: string;
}

// Export/Import Types
export interface SyncDataExport {
  exportType: 'logs' | 'offline_data' | 'device_status' | 'queue_history';
  format: 'csv' | 'json' | 'excel';
  dateRange?: {
    start: string;
    end: string;
  };
  filters?: Record<string, any>;
}

export interface SyncDataImport {
  importType: 'configuration' | 'device_registry' | 'user_settings';
  file: File;
  overwriteExisting: boolean;
  validateData: boolean;
} 