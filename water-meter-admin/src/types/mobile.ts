// Mobile Management module types

// ======================== Device Registry Types ========================

export interface DeviceDto {
  id: number;
  deviceId: string;
  deviceName: string;
  deviceType: 'smartphone' | 'tablet' | 'handheld_scanner' | 'other';
  brand: string;
  model: string;
  osType: 'android' | 'ios' | 'windows' | 'other';
  osVersion: string;
  appVersion: string;
  registeredAt: string;
  lastActiveAt: string;
  status: 'active' | 'inactive' | 'suspended' | 'maintenance';
  userId?: number;
  userName?: string;
  imei?: string;
  serialNumber?: string;
  macAddress?: string;
  ipAddress?: string;
  location?: string;
  batteryLevel?: number;
  storageUsed?: number;
  storageTotal?: number;
  memoryUsed?: number;
  memoryTotal?: number;
  networkType?: 'wifi' | '4g' | '5g' | 'ethernet' | 'other';
  lastSyncAt?: string;
  notes?: string;
}

export interface DeviceSearchDto {
  page: number;
  pageSize: number;
  sortBy?: string;
  sortDirection?: 'asc' | 'desc';
  deviceId?: string;
  deviceName?: string;
  deviceType?: string;
  brand?: string;
  osType?: string;
  status?: string;
  userId?: number;
  registeredFrom?: Date;
  registeredTo?: Date;
  lastActiveFrom?: Date;
  lastActiveTo?: Date;
}

export interface DeviceSearchResultDto {
  devices: DeviceDto[];
  totalCount: number;
}

export interface CreateDeviceDto {
  deviceId: string;
  deviceName: string;
  deviceType: 'smartphone' | 'tablet' | 'handheld_scanner' | 'other';
  brand: string;
  model: string;
  osType: 'android' | 'ios' | 'windows' | 'other';
  osVersion: string;
  appVersion: string;
  userId?: number;
  imei?: string;
  serialNumber?: string;
  macAddress?: string;
  location?: string;
  notes?: string;
}

export interface UpdateDeviceDto extends CreateDeviceDto {
  status: 'active' | 'inactive' | 'suspended' | 'maintenance';
}

export interface DeviceStatsDto {
  totalDevices: number;
  activeDevices: number;
  inactiveDevices: number;
  suspendedDevices: number;
  androidDevices: number;
  iosDevices: number;
  otherDevices: number;
  averageBatteryLevel: number;
  devicesLowBattery: number;
  devicesNeedUpdate: number;
}

// ======================== App Versions Types ========================

export interface AppVersionDto {
  id: number;
  versionNumber: string;
  versionCode: number;
  platform: 'android' | 'ios' | 'windows' | 'web';
  releaseDate: string;
  status: 'development' | 'testing' | 'released' | 'deprecated';
  minOsVersion: string;
  targetOsVersion?: string;
  fileSize: number;
  downloadUrl?: string;
  releaseNotes: string;
  features: string[];
  bugFixes: string[];
  knownIssues: string[];
  isForced: boolean;
  isRecommended: boolean;
  installCount: number;
  activeUsers: number;
  crashReports: number;
  rating: number;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
}

export interface AppVersionSearchDto {
  page: number;
  pageSize: number;
  sortBy?: string;
  sortDirection?: 'asc' | 'desc';
  platform?: string;
  status?: string;
  versionNumber?: string;
  releasedFrom?: Date;
  releasedTo?: Date;
}

export interface AppVersionSearchResultDto {
  versions: AppVersionDto[];
  totalCount: number;
}

export interface CreateAppVersionDto {
  versionNumber: string;
  versionCode: number;
  platform: 'android' | 'ios' | 'windows' | 'web';
  minOsVersion: string;
  targetOsVersion?: string;
  releaseNotes: string;
  features: string[];
  bugFixes: string[];
  knownIssues: string[];
  isForced: boolean;
  isRecommended: boolean;
}

export interface UpdateAppVersionDto extends CreateAppVersionDto {
  status: 'development' | 'testing' | 'released' | 'deprecated';
  downloadUrl?: string;
  fileSize?: number;
}

export interface AppVersionStatsDto {
  totalVersions: number;
  activeVersions: number;
  latestVersion: string;
  totalDownloads: number;
  totalActiveUsers: number;
  averageRating: number;
  criticalIssues: number;
  platformDistribution: { platform: string; count: number; percentage: number }[];
}

// ======================== Compatibility Tests Types ========================

export interface CompatibilityTestDto {
  id: number;
  testName: string;
  testType: 'functional' | 'performance' | 'ui' | 'integration' | 'security';
  appVersion: string;
  platform: string;
  osVersion: string;
  deviceModel: string;
  testStatus: 'pending' | 'running' | 'passed' | 'failed' | 'skipped';
  startedAt?: string;
  completedAt?: string;
  duration?: number;
  testCases: TestCaseDto[];
  overallScore: number;
  passRate: number;
  failureReason?: string;
  testEnvironment: string;
  testerName: string;
  createdAt: string;
  updatedAt: string;
}

export interface TestCaseDto {
  id: number;
  name: string;
  description: string;
  category: string;
  priority: 'low' | 'medium' | 'high' | 'critical';
  status: 'pending' | 'running' | 'passed' | 'failed' | 'skipped';
  result?: string;
  errorMessage?: string;
  screenshot?: string;
  duration?: number;
  executedAt?: string;
}

export interface CompatibilityTestSearchDto {
  page: number;
  pageSize: number;
  sortBy?: string;
  sortDirection?: 'asc' | 'desc';
  testName?: string;
  testType?: string;
  appVersion?: string;
  platform?: string;
  testStatus?: string;
  testerName?: string;
  startedFrom?: Date;
  startedTo?: Date;
}

export interface CompatibilityTestSearchResultDto {
  tests: CompatibilityTestDto[];
  totalCount: number;
}

export interface CreateCompatibilityTestDto {
  testName: string;
  testType: 'functional' | 'performance' | 'ui' | 'integration' | 'security';
  appVersion: string;
  platform: string;
  osVersion: string;
  deviceModel: string;
  testEnvironment: string;
  testCases: Omit<TestCaseDto, 'id' | 'status' | 'executedAt'>[];
}

export interface UpdateCompatibilityTestDto extends CreateCompatibilityTestDto {
  testStatus: 'pending' | 'running' | 'passed' | 'failed' | 'skipped';
  overallScore?: number;
  passRate?: number;
  failureReason?: string;
}

export interface CompatibilityStatsDto {
  totalTests: number;
  passedTests: number;
  failedTests: number;
  pendingTests: number;
  overallPassRate: number;
  averageTestDuration: number;
  criticalFailures: number;
  platformCoverage: { platform: string; testCount: number; passRate: number }[];
  recentTestTrends: { date: string; passed: number; failed: number }[];
}

// ======================== Common Types ========================

export interface MobileDeviceInfo {
  deviceId: string;
  brand: string;
  model: string;
  osType: string;
  osVersion: string;
  appVersion: string;
  screenResolution: string;
  memoryTotal: number;
  storageTotal: number;
  batteryCapacity: number;
  networkCapabilities: string[];
  sensors: string[];
}

export interface TestConfiguration {
  targetPlatforms: string[];
  minOsVersions: { [platform: string]: string };
  testDevices: MobileDeviceInfo[];
  testCategories: string[];
  testPriorities: string[];
}

export interface MobileAnalyticsDto {
  deviceUsageStats: { date: string; activeDevices: number; newRegistrations: number }[];
  versionAdoptionRates: { version: string; adoptionRate: number; userCount: number }[];
  crashReports: { date: string; crashCount: number; affectedUsers: number }[];
  performanceMetrics: { metric: string; value: number; trend: 'up' | 'down' | 'stable' }[];
} 