// Quick Access module types

// ======================== Create Reactive Task Types ========================

export interface ReactiveTaskDto {
  id: number;
  taskType: 'emergency_reading' | 'maintenance_request' | 'customer_complaint' | 'equipment_failure' | 'leak_detection' | 'other';
  priority: 'low' | 'medium' | 'high' | 'critical' | 'emergency';
  title: string;
  description: string;
  location: string;
  waterMeterId?: string;
  customerId?: number;
  customerName?: string;
  reportedBy: string;
  reportedAt: string;
  assignedUserId?: number;
  assignedToName?: string;
  assignedAt?: string;
  expectedCompletionDate?: string;
  status: 'pending' | 'assigned' | 'in_progress' | 'completed' | 'cancelled' | 'on_hold';
  relatedTasks?: number[];
  attachments?: TaskAttachmentDto[];
  gpsCoordinates?: { latitude: number; longitude: number };
  estimatedDuration?: number;
  actualDuration?: number;
  completedAt?: string;
  completionNotes?: string;
  feedback?: string;
  rating?: number;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
}

export interface TaskAttachmentDto {
  id: number;
  fileName: string;
  fileType: string;
  fileSize: number;
  uploadedBy: string;
  uploadedAt: string;
  url: string;
}

export interface CreateReactiveTaskDto {
  taskType: 'emergency_reading' | 'maintenance_request' | 'customer_complaint' | 'equipment_failure' | 'leak_detection' | 'other';
  priority: 'low' | 'medium' | 'high' | 'critical' | 'emergency';
  title: string;
  description: string;
  location: string;
  waterMeterId?: string;
  customerId?: number;
  reportedBy: string;
  assignedTo?: number;
  expectedCompletionDate?: string;
  gpsCoordinates?: { latitude: number; longitude: number };
  estimatedDuration?: number;
  attachments?: File[];
}

export interface ReactiveTaskSearchDto {
  page: number;
  pageSize: number;
  sortBy?: string;
  sortDirection?: 'asc' | 'desc';
  taskType?: string;
  priority?: string;
  status?: string;
  assignedTo?: number;
  reportedBy?: string;
  location?: string;
  createdFrom?: Date;
  createdTo?: Date;
  keyword?: string;
}

export interface ReactiveTaskSearchResultDto {
  tasks: ReactiveTaskDto[];
  totalCount: number;
}

export interface ReactiveTaskStatsDto {
  totalTasks: number;
  pendingTasks: number;
  inProgressTasks: number;
  completedTasks: number;
  overdueTasks: number;
  criticalTasks: number;
  averageCompletionTime: number;
  tasksByType: { type: string; count: number }[];
  tasksByPriority: { priority: string; count: number }[];
}

// ======================== Today's Assignments Types ========================

export interface TodayAssignmentDto {
  id: number;
  taskId: number;
  taskType: 'reading' | 'maintenance' | 'inspection' | 'installation' | 'repair' | 'emergency';
  title: string;
  description: string;
  priority: 'low' | 'medium' | 'high' | 'critical';
  status: 'pending' | 'in_progress' | 'completed' | 'delayed' | 'cancelled';
  assignedTo: number;
  assignedToName: string;
  location: string;
  waterMeterId?: string;
  customerName?: string;
  scheduledTime: string;
  estimatedDuration: number;
  actualStartTime?: string;
  actualEndTime?: string;
  progress: number;
  gpsCoordinates?: { latitude: number; longitude: number };
  notes?: string;
  attachments?: TaskAttachmentDto[];
  completionPhotos?: string[];
  route?: RouteInfoDto;
  equipment?: EquipmentInfoDto[];
  weatherConditions?: WeatherInfoDto;
  createdAt: string;
  updatedAt: string;
}

export interface RouteInfoDto {
  routeId: number;
  routeName: string;
  sequence: number;
  totalTasks: number;
  completedTasks: number;
  estimatedTravelTime: number;
  actualTravelTime?: number;
}

export interface EquipmentInfoDto {
  equipmentId: string;
  equipmentType: string;
  description: string;
  isRequired: boolean;
  isAssigned: boolean;
}

export interface WeatherInfoDto {
  temperature: number;
  humidity: number;
  condition: string;
  windSpeed: number;
  visibility: number;
  precipitation: number;
}

export interface TodayAssignmentSearchDto {
  assignedTo?: number;
  status?: string;
  priority?: string;
  taskType?: string;
  location?: string;
  routeId?: number;
}

export interface TodayAssignmentStatsDto {
  totalAssignments: number;
  completedAssignments: number;
  inProgressAssignments: number;
  pendingAssignments: number;
  delayedAssignments: number;
  completionRate: number;
  averageTaskDuration: number;
  onTimeCompletionRate: number;
  assignmentsByType: { type: string; count: number; completed: number }[];
  assignmentsByPriority: { priority: string; count: number; completed: number }[];
}

// ======================== Pending Sync Types ========================

export interface PendingSyncDto {
  id: number;
  syncType: 'reading_data' | 'task_completion' | 'photos' | 'signatures' | 'forms' | 'gps_coordinates' | 'device_status';
  entityType: 'water_meter_reading' | 'task' | 'photo' | 'customer_signature' | 'inspection_form' | 'location_data' | 'device_info';
  entityId: string;
  deviceId: string;
  deviceName: string;
  userId: number;
  userName: string;
  dataSize: number;
  priority: 'low' | 'medium' | 'high' | 'critical';
  status: 'pending' | 'syncing' | 'failed' | 'partial' | 'conflicted';
  syncAttempts: number;
  maxAttempts: number;
  lastAttemptAt?: string;
  nextAttemptAt?: string;
  errorMessage?: string;
  conflictDetails?: ConflictDetailsDto;
  dataPreview: any;
  createdAt: string;
  updatedAt: string;
  expiresAt?: string;
}

export interface ConflictDetailsDto {
  conflictType: 'version_mismatch' | 'data_changed' | 'duplicate_entry' | 'validation_error';
  localVersion: string;
  serverVersion: string;
  conflictedFields: string[];
  localData: any;
  serverData: any;
  suggestedResolution: 'use_local' | 'use_server' | 'merge' | 'manual_review';
}

export interface PendingSyncSearchDto {
  page: number;
  pageSize: number;
  sortBy?: string;
  sortDirection?: 'asc' | 'desc';
  syncType?: string;
  entityType?: string;
  deviceId?: string;
  userId?: number;
  status?: string;
  priority?: string;
  createdFrom?: Date;
  createdTo?: Date;
}

export interface PendingSyncSearchResultDto {
  syncs: PendingSyncDto[];
  totalCount: number;
}

export interface SyncOperationDto {
  operationType: 'retry' | 'force_sync' | 'resolve_conflict' | 'cancel' | 'priority_boost';
  entityIds: string[];
  resolution?: 'use_local' | 'use_server' | 'merge';
  notes?: string;
}

export interface SyncStatsDto {
  totalPending: number;
  highPriority: number;
  failed: number;
  conflicted: number;
  avgSyncTime: number;
  successRate: number;
  syncsByType: { type: string; count: number; failureRate: number }[];
  syncsByDevice: { deviceId: string; deviceName: string; pendingCount: number }[];
  syncTrends: { date: string; successful: number; failed: number }[];
}

// ======================== Recent Alerts Types ========================

export interface AlertDto {
  id: number;
  alertType: 'system' | 'device' | 'data' | 'user' | 'security' | 'maintenance' | 'emergency';
  category: 'info' | 'warning' | 'error' | 'critical' | 'success';
  severity: 'low' | 'medium' | 'high' | 'critical';
  title: string;
  description: string;
  source: string;
  sourceId?: string;
  userId?: number;
  userName?: string;
  deviceId?: string;
  deviceName?: string;
  waterMeterId?: string;
  location?: string;
  status: 'active' | 'acknowledged' | 'resolved' | 'dismissed' | 'escalated';
  isRead: boolean;
  isArchived: boolean;
  acknowledgedBy?: string;
  acknowledgedAt?: string;
  resolvedBy?: string;
  resolvedAt?: string;
  resolutionNotes?: string;
  escalatedTo?: string;
  escalatedAt?: string;
  relatedAlerts?: number[];
  actionRequired: boolean;
  autoResolve: boolean;
  expiresAt?: string;
  metadata?: { [key: string]: any };
  createdAt: string;
  updatedAt: string;
}

export interface AlertSearchDto {
  page: number;
  pageSize: number;
  sortBy?: string;
  sortDirection?: 'asc' | 'desc';
  alertType?: string;
  category?: string;
  severity?: string;
  status?: string;
  source?: string;
  userId?: number;
  deviceId?: string;
  isRead?: boolean;
  isArchived?: boolean;
  actionRequired?: boolean;
  createdFrom?: Date;
  createdTo?: Date;
  keyword?: string;
}

export interface AlertSearchResultDto {
  alerts: AlertDto[];
  totalCount: number;
  unreadCount: number;
  criticalCount: number;
}

export interface AlertActionDto {
  alertIds: number[];
  action: 'acknowledge' | 'resolve' | 'dismiss' | 'escalate' | 'archive' | 'mark_read';
  notes?: string;
  escalateTo?: string;
}

export interface AlertStatsDto {
  totalAlerts: number;
  unreadAlerts: number;
  criticalAlerts: number;
  activeAlerts: number;
  resolvedToday: number;
  averageResolutionTime: number;
  alertsByType: { type: string; count: number; criticalCount: number }[];
  alertsBySeverity: { severity: string; count: number; resolvedCount: number }[];
  alertTrends: { date: string; created: number; resolved: number }[];
  topSources: { source: string; alertCount: number; criticalCount: number }[];
}

// ======================== Common Quick Access Types ========================

export interface QuickActionDto {
  id: string;
  title: string;
  description: string;
  icon: string;
  color: string;
  route: string;
  permissions: string[];
  isEnabled: boolean;
  sortOrder: number;
  category: 'task' | 'data' | 'alert' | 'report' | 'admin';
  lastUsed?: string;
  usageCount: number;
}

export interface DashboardWidgetDto {
  id: string;
  title: string;
  type: 'stat' | 'chart' | 'list' | 'map' | 'progress' | 'calendar';
  size: 'small' | 'medium' | 'large';
  position: { x: number; y: number; width: number; height: number };
  data: any;
  refreshInterval: number;
  lastRefreshed: string;
  isVisible: boolean;
  settings: { [key: string]: any };
}

export interface QuickAccessPreferencesDto {
  userId: number;
  favoriteActions: string[];
  widgetLayout: DashboardWidgetDto[];
  defaultFilters: { [module: string]: any };
  notificationSettings: {
    enableBrowserNotifications: boolean;
    enableEmailNotifications: boolean;
    alertThreshold: 'all' | 'medium' | 'high' | 'critical';
    quietHours: { start: string; end: string };
  };
  autoRefreshInterval: number;
}

// ======================== Type Aliases for Compatibility ========================

// Aliases for Recent Alerts - maintain compatibility with existing code
export type RecentAlertDto = AlertDto;
export type AlertSearchParams = AlertSearchDto;
export type AlertStats = AlertStatsDto;

// Aliases for Pending Sync - maintain compatibility with existing code
export type PendingSyncSearchParams = PendingSyncSearchDto;
export type PendingSyncStats = SyncStatsDto; 