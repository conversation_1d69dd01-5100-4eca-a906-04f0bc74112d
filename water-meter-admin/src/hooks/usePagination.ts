import { useState, useMemo } from 'react'
import type { TablePaginationConfig } from 'antd'

export interface UsePaginationOptions {
  defaultPageSize?: number
  pageSizeOptions?: string[]
  showSizeChanger?: boolean
  showQuickJumper?: boolean
  showTotal?: boolean
}

interface UsePaginationReturn {
  currentPage: number
  pageSize: number
  setCurrentPage: (page: number) => void
  setPageSize: (size: number) => void
  resetPagination: () => void
  getPaginationConfig: (total: number) => TablePaginationConfig
}

export const usePagination = (options: UsePaginationOptions = {}): UsePaginationReturn => {
  const {
    defaultPageSize = 10,
    pageSizeOptions = ['10', '20', '50', '100'],
    showSizeChanger = true,
    showQuickJumper = true,
    showTotal = true
  } = options

  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize, setPageSize] = useState(defaultPageSize)

  const resetPagination = () => {
    setCurrentPage(1)
    setPageSize(defaultPageSize)
  }

  const getPaginationConfig = useMemo(() => {
    return (total: number): TablePaginationConfig => ({
      current: currentPage,
      pageSize: pageSize,
      total: total,
      pageSizeOptions: pageSizeOptions,
      showSizeChanger: showSizeChanger,
      showQuickJumper: showQuickJumper,
      showTotal: showTotal ? (total, range) => 
        `${range[0]}-${range[1]} of ${total} items` : undefined,
      onChange: (page: number, size?: number) => {
        setCurrentPage(page)
        if (size && size !== pageSize) {
          setPageSize(size)
          setCurrentPage(1) // Reset to first page when page size changes
        }
      },
      onShowSizeChange: (current: number, size: number) => {
        setPageSize(size)
        setCurrentPage(1) // Reset to first page when page size changes
      }
    })
  }, [currentPage, pageSize, pageSizeOptions, showSizeChanger, showQuickJumper, showTotal])

  return {
    currentPage,
    pageSize,
    setCurrentPage,
    setPageSize,
    resetPagination,
    getPaginationConfig
  }
} 