import { api } from './api';

export interface RouteListDto {
  id: number;
  name: string;
  description?: string;
  status: string;
  zone?: string;
  area?: string;
  estimatedDuration?: number;
  estimatedDistance?: number;
  totalMeters: number;
  assignedTo?: string;
  isTemplate: boolean;
  templateCategory?: string;
  difficultyRating?: number;
  startLatitude?: number;
  startLongitude?: number;
  endLatitude?: number;
  endLongitude?: number;
  startAddress?: string;
  endAddress?: string;
  createdAt: string;
  createdBy: string;
}

export interface CreateRouteDto {
  name: string;
  description?: string;
  status: string;
  zone?: string;
  area?: string;
  estimatedDuration?: number;
  estimatedDistance?: number;
  assignedTo?: string;
  isTemplate: boolean;
  templateCategory?: string;
  difficultyRating?: number;
  notes?: string;
}

export interface UpdateRouteDto extends CreateRouteDto {
  id: number;
}

export interface RouteSearchDto {
  page: number;
  pageSize: number;
  sortBy?: string;
  sortDirection?: string;
  name?: string;
  status?: string;
  zone?: string;
  area?: string;
  assignedTo?: string;
  isTemplate?: boolean;
  templateCategory?: string;
  difficultyRatingMin?: number;
  difficultyRatingMax?: number;
}

export interface RouteSearchResultDto {
  routes: RouteListDto[];
  totalCount: number;
  pageNumber: number;
  pageSize: number;
  totalPages: number;
}

// Backend response format (what actually comes from the API)
export interface RouteApiResponse {
  data: RouteListDto[];
  totalCount: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

export interface RouteWaypointDto {
  id: number;
  routeId: number;
  waterMeterId: number;
  sequenceOrder: number;
  latitude?: number;
  longitude?: number;
  address?: string;
  estimatedDuration?: number;
  distanceFromPrevious?: number;
  travelTimeFromPrevious?: number;
  notes?: string;
  accessDifficulty?: number;
  requiresSpecialEquipment: boolean;
  specialInstructions?: string;
  waterMeterSerial?: string;
  customerName?: string;
}

export interface CreateRouteWaypointDto {
  waterMeterId: number;
  sequenceOrder: number;
  estimatedDuration?: number;
  notes?: string;
  accessDifficulty?: number;
  requiresSpecialEquipment?: boolean;
  specialInstructions?: string;
}

export interface UpdateWaypointCoordinatesDto {
  latitude: number;
  longitude: number;
  address?: string;
}

export interface WaypointCoordinateUpdate {
  waypointId: number;
  latitude: number;
  longitude: number;
  address?: string;
}

export interface BatchUpdateCoordinatesDto {
  updates: WaypointCoordinateUpdate[];
}

export interface BatchUpdateResult {
  successCount: number;
  failedCount: number;
  failures: string[];
}

export class RouteService {
  private readonly baseUrl = '/routes';

  async getRoutes(params: RouteSearchDto): Promise<RouteSearchResultDto> {
    try {
      const response = await api.get<RouteApiResponse>(this.baseUrl, { params });
      // Transform backend response format to frontend expected format
      return {
        routes: response.data.data,
        totalCount: response.data.totalCount,
        pageNumber: response.data.page,
        pageSize: response.data.pageSize,
        totalPages: response.data.totalPages
      };
    } catch (error) {
      console.error('Error fetching routes:', error);
      throw error;
    }
  }

  async getRouteById(id: number): Promise<RouteListDto> {
    try {
      const response = await api.get<RouteListDto>(`${this.baseUrl}/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching route ${id}:`, error);
      throw error;
    }
  }

  async createRoute(data: CreateRouteDto): Promise<RouteListDto> {
    try {
      const response = await api.post<RouteListDto>(this.baseUrl, data);
      return response.data;
    } catch (error) {
      console.error('Error creating route:', error);
      throw error;
    }
  }

  async updateRoute(id: number, data: UpdateRouteDto): Promise<RouteListDto> {
    try {
      const response = await api.put<RouteListDto>(`${this.baseUrl}/${id}`, data);
      return response.data;
    } catch (error) {
      console.error(`Error updating route ${id}:`, error);
      throw error;
    }
  }

  async deleteRoute(id: number): Promise<void> {
    try {
      await api.delete(`${this.baseUrl}/${id}`);
    } catch (error) {
      console.error(`Error deleting route ${id}:`, error);
      throw error;
    }
  }

  async getRouteWaypoints(routeId: number): Promise<RouteWaypointDto[]> {
    try {
      const response = await api.get<RouteWaypointDto[]>(`${this.baseUrl}/${routeId}/waypoints`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching waypoints for route ${routeId}:`, error);
      throw error;
    }
  }

  async addWaypoint(routeId: number, waypoint: CreateRouteWaypointDto): Promise<RouteWaypointDto> {
    try {
      const response = await api.post<RouteWaypointDto>(`${this.baseUrl}/${routeId}/waypoints`, waypoint);
      return response.data;
    } catch (error) {
      console.error(`Error adding waypoint to route ${routeId}:`, error);
      throw error;
    }
  }

  async updateWaypoint(waypointId: number, waypoint: CreateRouteWaypointDto): Promise<void> {
    try {
      await api.put(`${this.baseUrl}/waypoints/${waypointId}`, waypoint);
    } catch (error) {
      console.error(`Error updating waypoint ${waypointId}:`, error);
      throw error;
    }
  }

  async deleteWaypoint(waypointId: number): Promise<void> {
    try {
      await api.delete(`${this.baseUrl}/waypoints/${waypointId}`);
    } catch (error) {
      console.error(`Error deleting waypoint ${waypointId}:`, error);
      throw error;
    }
  }

  async reorderWaypoints(routeId: number, waypointIds: number[]): Promise<void> {
    try {
      await api.put(`${this.baseUrl}/${routeId}/waypoints/reorder`, waypointIds);
    } catch (error) {
      console.error(`Error reordering waypoints for route ${routeId}:`, error);
      throw error;
    }
  }

  async optimizeRoute(routeId: number): Promise<{ success: boolean; message: string; optimizedRoute?: RouteListDto }> {
    try {
      const optimizationData = {
        routeId: routeId,
        optimizationMethod: "DistanceOptimal",
        preserveStartEnd: true,
        considerTrafficConditions: false,
        considerTimeWindows: false
      };
      const response = await api.post(`${this.baseUrl}/${routeId}/optimize`, optimizationData);
      const result = response.data;
      
      // Convert RouteOptimizationResultDto to expected format
      if (result.optimizedWaypoints && result.optimizedWaypoints.length > 0) {
        return {
          success: true,
          message: `Route optimized successfully! Distance saved: ${result.distanceSaved.toFixed(2)} km, Time saved: ${result.timeSaved} minutes`
        };
      } else {
        return {
          success: true,
          message: `Route optimization completed. No waypoints found or no improvements possible.`
        };
      }
    } catch (error) {
      console.error(`Error optimizing route ${routeId}:`, error);
      throw error;
    }
  }

  async duplicateRoute(routeId: number, newName: string): Promise<RouteListDto> {
    try {
      const duplicateData = {
        name: newName,
        description: `Copy of route ${routeId}`,
        includeWaypoints: true,
        assignedTo: null
      };
      const response = await api.post<RouteListDto>(`${this.baseUrl}/${routeId}/duplicate`, duplicateData);
      return response.data;
    } catch (error) {
      console.error(`Error duplicating route ${routeId}:`, error);
      throw error;
    }
  }

  async getRouteTemplates(): Promise<RouteListDto[]> {
    try {
      const response = await api.get<RouteListDto[]>(`${this.baseUrl}/templates`);
      return response.data;
    } catch (error) {
      console.error('Error fetching route templates:', error);
      throw error;
    }
  }

  async exportRoutes(params?: RouteSearchDto): Promise<Blob> {
    try {
      const response = await api.get(`${this.baseUrl}/export`, { 
        params,
        responseType: 'blob'
      });
      return response.data;
    } catch (error) {
      console.error('Error exporting routes:', error);
      throw error;
    }
  }

  async getUnassignedMeters(page: number = 1, pageSize: number = 50): Promise<any[]> {
    try {
      const response = await api.get(`${this.baseUrl}/unassigned-meters`, {
        params: { page, pageSize }
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching unassigned meters:', error);
      throw error;
    }
  }

  async updateWaypointCoordinates(waypointId: number, coordinates: UpdateWaypointCoordinatesDto): Promise<void> {
    try {
      await api.put(`${this.baseUrl}/waypoints/${waypointId}/coordinates`, coordinates);
    } catch (error) {
      console.error(`Error updating waypoint ${waypointId} coordinates:`, error);
      throw error;
    }
  }

  async batchUpdateWaypointCoordinates(routeId: number, batchData: BatchUpdateCoordinatesDto): Promise<BatchUpdateResult> {
    try {
      const response = await api.put<BatchUpdateResult>(`${this.baseUrl}/${routeId}/waypoints/coordinates/batch`, batchData);
      return response.data;
    } catch (error) {
      console.error(`Error batch updating waypoint coordinates for route ${routeId}:`, error);
      throw error;
    }
  }
}

export const routeService = new RouteService(); 