import { api } from './api';

// Types
export interface TaskListDto {
  id: number;
  name: string;
  description?: string;
  status: string;
  priority: string;
  type: string;
  assignedUserId?: number;
  assignedUserName?: string;
  createdBy: string;
  routeId?: number;
  dueDate?: string;
  startDate?: string;
  completedDate?: string;
  estimatedHours?: number;
  actualHours?: number;
  progressPercentage: number;
  location?: string;
  instructions?: string;
  notes?: string;
  createdAt: string;
  updatedAt: string;
  routeName?: string;
  workPackageName?: string;
  meterSerialNumber?: string;
  // Water meter details
  meterAssetId?: string;
  meterAccountNumber?: string;
  meterType?: string;
  // GPS coordinates
  latitude?: number;
  longitude?: number;
  // Meter GPS coordinates (from associated water meter)
  meterLatitude?: number;
  meterLongitude?: number;
}

export interface CreateTaskDto {
  name: string;
  description?: string;
  status: string;
  priority: string;
  type: string;
  assignedTo?: string;
  scheduleId?: number;
  routeId?: number;
  dueDate?: string;
  startDate?: string;
  estimatedHours?: number;
  location?: string;
  instructions?: string;
  notes?: string;
}

export interface UpdateTaskDto extends CreateTaskDto {
  id: number;
  completedDate?: string;
  actualHours?: number;
  progressPercentage: number;
}

export interface TaskSearchDto {
  page?: number;
  pageSize?: number;
  sortBy?: string;
  sortDirection?: string;
  name?: string;
  status?: string;
  priority?: string;
  type?: string;
  assignedUserId?: number;
  routeId?: number;
  workPackageName?: string;
  dueDateFrom?: string;
  dueDateTo?: string;
  createdDateFrom?: string;
  createdDateTo?: string;
  isOverdue?: boolean;
}

export interface TaskSearchResultDto {
  tasks: TaskListDto[];
  totalCount: number;
  pageNumber: number;
  pageSize: number;
  totalPages: number;
}

export interface BulkAssignmentDto {
  taskIds: number[];
  userId: number;
  reason?: string;
}

export interface ReactiveAssignmentDto {
  criteria: string;
  priority: string;
  zone?: string;
  area?: string;
  maxTasksPerUser?: number;
}

export interface TaskStatisticsDto {
  totalTasks: number;
  pendingTasks: number;
  inProgressTasks: number;
  completedTasks: number;
  cancelledTasks: number;
  overdueTasks: number;
  averageCompletionTime: number;
  taskCompletionRate: number;
  statusCounts: TaskStatusCountDto[];
  priorityCounts: TaskPriorityCountDto[];
}

export interface TaskStatusCountDto {
  status: string;
  count: number;
  percentage: number;
}

export interface TaskPriorityCountDto {
  priority: string;
  count: number;
  percentage: number;
}

export interface OverdueTaskDto {
  id: number;
  name: string;
  priority: string;
  assignedTo?: string;
  dueDate: string;
  daysOverdue: number;
  location?: string;
  createdAt: string;
}

export interface TaskPerformanceMetricsDto {
  userId: string;
  userName: string;
  assignedTasks: number;
  completedTasks: number;
  overdueTasks: number;
  completionRate: number;
  averageCompletionTime: number;
  efficiencyScore: number;
}

export interface TaskHistory {
  id: number;
  taskId: number;
  action: string;
  oldValue?: string;
  newValue?: string;
  changedBy: string;
  changedAt: string;
  reason?: string;
}

export interface TaskAssignmentDto {
  id: number;
  taskId: number;
  userId: number;
  assignedDate: string;
  assignedBy: string;
  assignmentType: string;
  status: string;
  acceptedDate?: string;
  rejectedDate?: string;
  reason?: string;
  taskName?: string;
  userName?: string;
  userEmail?: string;
  taskStatus?: string;
  taskPriority?: string;
}

export interface CreateTaskAssignmentDto {
  taskId: number;
  userId: number;
  assignmentType?: string;
  reason?: string;
}

export interface UserWorkloadSummaryDto {
  userId: number;
  userName: string;
  email: string;
  department?: string;
  zone?: string;
  activeTaskCount: number;
  completedTaskCount: number;
  overdueTaskCount: number;
  workloadPercentage: number;
  efficiencyScore: number;
  availabilityStatus: string;
  lastActivity?: string;
  skills: string[];
  maxCapacity: number;
}

export interface TaskAssignmentValidationDto {
  canAssign: boolean;
  reason?: string;
  warnings: string[];
  userWorkload?: UserWorkloadSummaryDto;
  recommendedTaskCount: number;
  priorityScore: number;
}

export interface AssignmentResultDto {
  success: boolean;
  message: string;
  createdAssignments: TaskAssignmentDto[];
  warnings: string[];
  errors: string[];
  totalAssigned: number;
  totalFailed: number;
}

export interface BulkAssignmentEnhancedRequest {
  taskIds: number[];
  userId: number;
  assignmentType?: string;
  reason?: string;
}

export interface RejectAssignmentRequest {
  reason: string;
}

export interface TaskWorkPackageSearchDto {
  page?: number;
  pageSize?: number;
  assignedUserId?: number;
  workPackageFilter?: string;
  status?: string;
  priority?: string;
}

export interface WorkPackageGroupDto {
  workPackageName: string;
  taskCount: number;
  tasks: TaskListDto[];
}

export interface WorkPackageGroupedTasksDto {
  workPackages: WorkPackageGroupDto[];
  totalWorkPackages: number;
  totalTasks: number;
  pageNumber: number;
  pageSize: number;
  totalPages: number;
}

// API Service
class TaskService {
  async getTasks(searchDto?: TaskSearchDto): Promise<TaskSearchResultDto> {
    try {
      const params = new URLSearchParams();
      if (searchDto) {
        Object.entries(searchDto).forEach(([key, value]) => {
          if (value !== undefined && value !== null && value !== '') {
            params.append(key, value.toString());
          }
        });
      }
      const response = await api.get(`/task?${params.toString()}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching tasks:', error);
      throw error;
    }
  }

  async getTaskById(id: number): Promise<TaskListDto> {
    try {
      const response = await api.get(`/task/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching task ${id}:`, error);
      throw error;
    }
  }

  async createTask(createDto: CreateTaskDto): Promise<TaskListDto> {
    try {
      const response = await api.post(`/task`, createDto);
      return response.data;
    } catch (error) {
      console.error('Error creating task:', error);
      throw error;
    }
  }

  async updateTask(id: number, updateDto: UpdateTaskDto): Promise<TaskListDto> {
    try {
      const response = await api.put(`/task/${id}`, updateDto);
      return response.data;
    } catch (error) {
      console.error(`Error updating task ${id}:`, error);
      throw error;
    }
  }

  async deleteTask(id: number): Promise<void> {
    try {
      await api.delete(`/task/${id}`);
    } catch (error) {
      console.error(`Error deleting task ${id}:`, error);
      throw error;
    }
  }

  async assignTask(id: number, userId: string): Promise<void> {
    try {
      await api.post(`/task/${id}/assign`, userId);
    } catch (error) {
      console.error(`Error assigning task ${id}:`, error);
      throw error;
    }
  }

  async bulkAssignTasks(bulkAssignmentDto: BulkAssignmentDto): Promise<void> {
    try {
      await api.post(`/task/bulk-assign`, bulkAssignmentDto);
    } catch (error) {
      console.error('Error bulk assigning tasks:', error);
      throw error;
    }
  }

  async reactiveAssignTasks(reactiveDto: ReactiveAssignmentDto): Promise<TaskListDto[]> {
    try {
      const response = await api.post(`/task/reactive-assign`, reactiveDto);
      return response.data;
    } catch (error) {
      console.error('Error in reactive assignment:', error);
      throw error;
    }
  }

  async updateTaskStatus(id: number, status: string): Promise<void> {
    try {
      await api.patch(`/task/${id}/status`, status);
    } catch (error) {
      console.error(`Error updating task status ${id}:`, error);
      throw error;
    }
  }

  async updateTaskProgress(id: number, progressPercentage: number): Promise<void> {
    try {
      await api.patch(`/task/${id}/progress`, progressPercentage);
    } catch (error) {
      console.error(`Error updating task progress ${id}:`, error);
      throw error;
    }
  }

  async getTaskHistory(id: number): Promise<TaskHistory[]> {
    try {
      const response = await api.get(`/task/${id}/history`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching task history ${id}:`, error);
      throw error;
    }
  }

  async exportTasks(searchDto?: TaskSearchDto): Promise<Blob> {
    try {
      const params = new URLSearchParams();
      if (searchDto) {
        Object.entries(searchDto).forEach(([key, value]) => {
          if (value !== undefined && value !== null && value !== '') {
            params.append(key, value.toString());
          }
        });
      }
      const response = await api.get(`/task/export?${params.toString()}`, {
        responseType: 'blob',
      });
      return response.data;
    } catch (error) {
      console.error('Error exporting tasks:', error);
      throw error;
    }
  }

  async importTasks(file: File): Promise<{ message: string; importedCount: number }> {
    try {
      const formData = new FormData();
      formData.append('file', file);
      const response = await api.post(`/task/import`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      return response.data;
    } catch (error) {
      console.error('Error importing tasks:', error);
      throw error;
    }
  }

  async getTaskStatistics(): Promise<TaskStatisticsDto> {
    try {
      const response = await api.get(`/task/statistics`);
      return response.data;
    } catch (error) {
      console.error('Error fetching task statistics:', error);
      throw error;
    }
  }

  async getOverdueTasks(): Promise<OverdueTaskDto[]> {
    try {
      const response = await api.get(`/task/overdue`);
      return response.data;
    } catch (error) {
      console.error('Error fetching overdue tasks:', error);
      throw error;
    }
  }

  async getTaskPerformanceMetrics(): Promise<TaskPerformanceMetricsDto[]> {
    try {
      const response = await api.get(`/task/performance-metrics`);
      return response.data;
    } catch (error) {
      console.error('Error fetching task performance metrics:', error);
      throw error;
    }
  }

  async createTaskAssignment(assignmentDto: CreateTaskAssignmentDto): Promise<AssignmentResultDto> {
    try {
      const response = await api.post(`/task/assignments`, assignmentDto);
      return response.data;
    } catch (error) {
      console.error('Error creating task assignment:', error);
      throw error;
    }
  }

  async getTaskAssignments(taskId?: number, userId?: number): Promise<TaskAssignmentDto[]> {
    try {
      const params = new URLSearchParams();
      if (taskId) params.append('taskId', taskId.toString());
      if (userId) params.append('userId', userId.toString());
      const response = await api.get(`/task/assignments?${params.toString()}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching task assignments:', error);
      throw error;
    }
  }

  async validateTaskAssignment(taskId: number, userId: number): Promise<TaskAssignmentValidationDto> {
    try {
      const response = await api.post(`/task/${taskId}/validate-assignment`, userId);
      return response.data;
    } catch (error) {
      console.error('Error validating task assignment:', error);
      throw error;
    }
  }

  async getUserWorkloadSummary(): Promise<UserWorkloadSummaryDto[]> {
    try {
      const response = await api.get(`/task/users/workload`);
      return response.data;
    } catch (error) {
      console.error('Error fetching user workload summary:', error);
      throw error;
    }
  }

  async getUserWorkload(userId: number): Promise<UserWorkloadSummaryDto> {
    try {
      const response = await api.get(`/task/users/${userId}/workload`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching user workload for user ${userId}:`, error);
      throw error;
    }
  }

  async acceptTaskAssignment(assignmentId: number): Promise<void> {
    try {
      await api.post(`/task/assignments/${assignmentId}/accept`);
    } catch (error) {
      console.error(`Error accepting task assignment ${assignmentId}:`, error);
      throw error;
    }
  }

  async rejectTaskAssignment(assignmentId: number, reason: string): Promise<void> {
    try {
      const request: RejectAssignmentRequest = { reason };
      await api.post(`/task/assignments/${assignmentId}/reject`, request);
    } catch (error) {
      console.error(`Error rejecting task assignment ${assignmentId}:`, error);
      throw error;
    }
  }

  async bulkAssignTasksEnhanced(request: BulkAssignmentEnhancedRequest): Promise<AssignmentResultDto> {
    try {
      const response = await api.post(`/task/bulk-assign-enhanced`, request);
      return response.data;
    } catch (error) {
      console.error('Error in enhanced bulk assignment:', error);
      throw error;
    }
  }

  async getWorkPackageGroupedTasks(searchDto?: TaskWorkPackageSearchDto): Promise<WorkPackageGroupedTasksDto> {
    try {
      const params = new URLSearchParams();
      if (searchDto) {
        Object.entries(searchDto).forEach(([key, value]) => {
          if (value !== undefined && value !== null && value !== '') {
            params.append(key, value.toString());
          }
        });
      }
      const response = await api.get(`/task/work-package-grouped?${params.toString()}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching work package grouped tasks:', error);
      throw error;
    }
  }

  async getTasksGroupedByWorkPackage(searchDto: TaskWorkPackageSearchDto): Promise<WorkPackageGroupedTasksDto> {
    try {
      const response = await api.get(`/task/work-packages`, { params: searchDto });
      return response.data;
    } catch (error) {
      console.error('Error getting tasks grouped by work package:', error);
      throw error;
    }
  }
}

export const taskService = new TaskService();
