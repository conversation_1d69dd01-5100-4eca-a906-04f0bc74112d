import { api } from './api';
import dayjs from 'dayjs';
import type {
  ReactiveTaskDto, CreateReactiveTaskDto, ReactiveTaskSearchDto, ReactiveTaskSearchResultDto,
  TodayAssignmentDto,
  PendingSyncDto, PendingSyncSearchDto, PendingSyncSearchResultDto,
  AlertDto, AlertSearchDto, AlertSearchResultDto,
  AlertStatsDto
} from '@/types/quickaccess';

class QuickAccessService {

  // ======================== Reactive Tasks ========================

  async getReactiveTasks(searchParams: ReactiveTaskSearchDto): Promise<ReactiveTaskSearchResultDto> {
    return new Promise((resolve) => {
      setTimeout(() => {
        const tasks: ReactiveTaskDto[] = [];
        const taskTypes = ['emergency_reading', 'maintenance_request', 'customer_complaint', 'equipment_failure', 'leak_detection', 'other'];
        const priorities = ['low', 'medium', 'high', 'critical', 'emergency'];
        const statuses = ['pending', 'assigned', 'in_progress', 'completed', 'cancelled', 'on_hold'];
        
        for (let i = 0; i < 50; i++) {
          tasks.push({
            id: i + 1,
            taskType: taskTypes[Math.floor(Math.random() * taskTypes.length)] as any,
            priority: priorities[Math.floor(Math.random() * priorities.length)] as any,
            title: `Emergency Task ${i + 1}`,
            description: `Description for emergency task ${i + 1}`,
            location: `Location ${Math.floor(Math.random() * 100) + 1}`,
            waterMeterId: Math.random() > 0.5 ? `WM-${(i + 1).toString().padStart(4, '0')}` : undefined,
            customerId: Math.random() > 0.3 ? Math.floor(Math.random() * 1000) + 1 : undefined,
            customerName: Math.random() > 0.3 ? `Customer ${i + 1}` : undefined,
            reportedBy: `Reporter ${Math.floor(Math.random() * 10) + 1}`,
            reportedAt: dayjs().subtract(Math.floor(Math.random() * 72), 'hour').toISOString(),
            assignedTo: Math.random() > 0.4 ? Math.floor(Math.random() * 20) + 1 : undefined,
            assignedToName: Math.random() > 0.4 ? `Technician ${Math.floor(Math.random() * 20) + 1}` : undefined,
            assignedAt: Math.random() > 0.4 ? dayjs().subtract(Math.floor(Math.random() * 48), 'hour').toISOString() : undefined,
            expectedCompletionDate: dayjs().add(Math.floor(Math.random() * 7), 'day').toISOString(),
            status: statuses[Math.floor(Math.random() * statuses.length)] as any,
            attachments: Math.random() > 0.7 ? [
              {
                id: 1,
                fileName: 'photo.jpg',
                fileType: 'image/jpeg',
                fileSize: 1024 * 1024,
                uploadedBy: 'Reporter',
                uploadedAt: dayjs().toISOString(),
                url: '/api/files/photo.jpg'
              }
            ] : [],
            gpsCoordinates: Math.random() > 0.6 ? { 
              latitude: -36.8485 + (Math.random() - 0.5) * 0.1, 
              longitude: 174.7633 + (Math.random() - 0.5) * 0.1 
            } : undefined,
            estimatedDuration: Math.floor(Math.random() * 180) + 30,
            createdBy: 'System',
            createdAt: dayjs().subtract(Math.floor(Math.random() * 100), 'hour').toISOString(),
            updatedAt: dayjs().subtract(Math.floor(Math.random() * 24), 'hour').toISOString()
          });
        }

        const start = (searchParams.page - 1) * searchParams.pageSize;
        const end = start + searchParams.pageSize;

        resolve({
          tasks: tasks.slice(start, end),
          totalCount: tasks.length
        });
      }, 500);
    });
  }

  async createReactiveTask(task: CreateReactiveTaskDto): Promise<ReactiveTaskDto> {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          id: Math.floor(Math.random() * 10000) + 1000,
          ...task,
          reportedAt: dayjs().toISOString(),
          assignedToName: task.assignedTo ? `Technician ${task.assignedTo}` : undefined,
          assignedAt: task.assignedTo ? dayjs().toISOString() : undefined,
          status: 'pending',
          attachments: [],
          createdBy: 'Current User',
          createdAt: dayjs().toISOString(),
          updatedAt: dayjs().toISOString()
        });
      }, 500);
    });
  }

  // ======================== Today's Assignments ========================

  async getTodayAssignments(): Promise<TodayAssignmentDto[]> {
    return new Promise((resolve) => {
      setTimeout(() => {
        const assignments: TodayAssignmentDto[] = [];
        const taskTypes = ['reading', 'maintenance', 'inspection', 'installation', 'repair', 'emergency'];
        const priorities = ['low', 'medium', 'high', 'critical'];
        const statuses = ['pending', 'in_progress', 'completed', 'delayed', 'cancelled'];
        
        for (let i = 0; i < 15; i++) {
          const scheduledTime = dayjs().hour(8 + Math.floor(Math.random() * 10)).minute(Math.floor(Math.random() * 60));
          const estimatedDuration = Math.floor(Math.random() * 120) + 30;
          const status = statuses[Math.floor(Math.random() * statuses.length)] as any;
          
          assignments.push({
            id: i + 1,
            taskId: i + 100,
            taskType: taskTypes[Math.floor(Math.random() * taskTypes.length)] as any,
            title: `Assignment ${i + 1}`,
            description: `Description for assignment ${i + 1}`,
            priority: priorities[Math.floor(Math.random() * priorities.length)] as any,
            status,
            assignedTo: Math.floor(Math.random() * 20) + 1,
            assignedToName: `Technician ${Math.floor(Math.random() * 20) + 1}`,
            location: `Location ${Math.floor(Math.random() * 100) + 1}`,
            waterMeterId: `WM-${(i + 1).toString().padStart(4, '0')}`,
            customerName: `Customer ${i + 1}`,
            scheduledTime: scheduledTime.toISOString(),
            estimatedDuration,
            actualStartTime: status !== 'pending' ? scheduledTime.add(Math.floor(Math.random() * 30), 'minute').toISOString() : undefined,
            actualEndTime: status === 'completed' ? scheduledTime.add(estimatedDuration + Math.floor(Math.random() * 30), 'minute').toISOString() : undefined,
            progress: status === 'completed' ? 100 : status === 'in_progress' ? Math.floor(Math.random() * 80) + 20 : 0,
            gpsCoordinates: { 
              latitude: -36.8485 + (Math.random() - 0.5) * 0.1, 
              longitude: 174.7633 + (Math.random() - 0.5) * 0.1 
            },
            notes: Math.random() > 0.6 ? 'Some notes about this assignment' : undefined,
            createdAt: dayjs().subtract(1, 'day').toISOString(),
            updatedAt: dayjs().subtract(Math.floor(Math.random() * 60), 'minute').toISOString()
          });
        }

        resolve(assignments);
      }, 500);
    });
  }

  // ======================== Pending Sync ========================

  async getPendingSync(searchParams: PendingSyncSearchDto): Promise<PendingSyncSearchResultDto> {
    return new Promise((resolve) => {
      setTimeout(() => {
        const syncs: PendingSyncDto[] = [];
        const syncTypes = ['reading_data', 'task_completion', 'photos', 'signatures', 'forms', 'gps_coordinates', 'device_status'];
        const entityTypes = ['water_meter_reading', 'task', 'photo', 'customer_signature', 'inspection_form', 'location_data', 'device_info'];
        const priorities = ['low', 'medium', 'high', 'critical'];
        const statuses = ['pending', 'syncing', 'failed', 'partial', 'conflicted'];
        
        for (let i = 0; i < 30; i++) {
          const status = statuses[Math.floor(Math.random() * statuses.length)] as any;
          const attempts = status === 'failed' ? Math.floor(Math.random() * 5) + 1 : Math.floor(Math.random() * 3);
          
          syncs.push({
            id: i + 1,
            syncType: syncTypes[Math.floor(Math.random() * syncTypes.length)] as any,
            entityType: entityTypes[Math.floor(Math.random() * entityTypes.length)] as any,
            entityId: `ENTITY-${(i + 1).toString().padStart(6, '0')}`,
            deviceId: `DEV-${Math.floor(Math.random() * 100) + 1}`,
            deviceName: `Device ${Math.floor(Math.random() * 100) + 1}`,
            userId: Math.floor(Math.random() * 50) + 1,
            userName: `User ${Math.floor(Math.random() * 50) + 1}`,
            dataSize: Math.floor(Math.random() * 10) * 1024 + 512,
            priority: priorities[Math.floor(Math.random() * priorities.length)] as any,
            status,
            syncAttempts: attempts,
            maxAttempts: 5,
            lastAttemptAt: attempts > 0 ? dayjs().subtract(Math.floor(Math.random() * 60), 'minute').toISOString() : undefined,
            nextAttemptAt: status === 'failed' ? dayjs().add(Math.floor(Math.random() * 30) + 10, 'minute').toISOString() : undefined,
            errorMessage: status === 'failed' ? 'Network timeout error' : undefined,
            dataPreview: {
              type: 'preview',
              content: 'Sample data preview...'
            },
            createdAt: dayjs().subtract(Math.floor(Math.random() * 24), 'hour').toISOString(),
            updatedAt: dayjs().subtract(Math.floor(Math.random() * 60), 'minute').toISOString(),
            expiresAt: dayjs().add(Math.floor(Math.random() * 7) + 1, 'day').toISOString()
          });
        }

        const start = (searchParams.page - 1) * searchParams.pageSize;
        const end = start + searchParams.pageSize;

        resolve({
          syncs: syncs.slice(start, end),
          totalCount: syncs.length
        });
      }, 500);
    });
  }

  // ======================== Recent Alerts ========================

  async getRecentAlerts(searchParams: AlertSearchDto): Promise<AlertSearchResultDto> {
    return new Promise((resolve) => {
      setTimeout(() => {
        const alerts: AlertDto[] = [];
        const alertTypes = ['system', 'device', 'data', 'user', 'security', 'maintenance', 'emergency'];
        const categories = ['info', 'warning', 'error', 'critical', 'success'];
        const severities = ['low', 'medium', 'high', 'critical'];
        const statuses = ['active', 'acknowledged', 'resolved', 'dismissed', 'escalated'];
        const sources = ['System Monitor', 'Device Manager', 'Data Validator', 'Security Scanner', 'User Activity', 'Maintenance Scheduler'];
        
        for (let i = 0; i < 60; i++) {
          const alertType = alertTypes[Math.floor(Math.random() * alertTypes.length)] as any;
          const category = categories[Math.floor(Math.random() * categories.length)] as any;
          const severity = severities[Math.floor(Math.random() * severities.length)] as any;
          const status = statuses[Math.floor(Math.random() * statuses.length)] as any;
          const isRead = Math.random() > 0.3;
          
          alerts.push({
            id: i + 1,
            alertType,
            category,
            severity,
            title: `${alertType.charAt(0).toUpperCase() + alertType.slice(1)} Alert ${i + 1}`,
            description: `Description for ${alertType} alert ${i + 1}`,
            source: sources[Math.floor(Math.random() * sources.length)],
            sourceId: `SRC-${Math.floor(Math.random() * 1000) + 1}`,
            userId: Math.random() > 0.5 ? Math.floor(Math.random() * 100) + 1 : undefined,
            userName: Math.random() > 0.5 ? `User ${Math.floor(Math.random() * 100) + 1}` : undefined,
            deviceId: Math.random() > 0.4 ? `DEV-${Math.floor(Math.random() * 100) + 1}` : undefined,
            deviceName: Math.random() > 0.4 ? `Device ${Math.floor(Math.random() * 100) + 1}` : undefined,
            waterMeterId: Math.random() > 0.6 ? `WM-${Math.floor(Math.random() * 1000) + 1}` : undefined,
            location: Math.random() > 0.5 ? `Location ${Math.floor(Math.random() * 100) + 1}` : undefined,
            status,
            isRead,
            isArchived: Math.random() > 0.9,
            acknowledgedBy: status === 'acknowledged' || status === 'resolved' ? `User ${Math.floor(Math.random() * 20) + 1}` : undefined,
            acknowledgedAt: status === 'acknowledged' || status === 'resolved' ? dayjs().subtract(Math.floor(Math.random() * 60), 'minute').toISOString() : undefined,
            resolvedBy: status === 'resolved' ? `User ${Math.floor(Math.random() * 20) + 1}` : undefined,
            resolvedAt: status === 'resolved' ? dayjs().subtract(Math.floor(Math.random() * 30), 'minute').toISOString() : undefined,
            resolutionNotes: status === 'resolved' ? 'Issue has been resolved successfully' : undefined,
            actionRequired: Math.random() > 0.7,
            autoResolve: Math.random() > 0.8,
            expiresAt: Math.random() > 0.8 ? dayjs().add(Math.floor(Math.random() * 7) + 1, 'day').toISOString() : undefined,
            createdAt: dayjs().subtract(Math.floor(Math.random() * 48), 'hour').toISOString(),
            updatedAt: dayjs().subtract(Math.floor(Math.random() * 24), 'hour').toISOString()
          });
        }

        const start = (searchParams.page - 1) * searchParams.pageSize;
        const end = start + searchParams.pageSize;
        const unreadCount = alerts.filter(a => !a.isRead).length;
        const criticalCount = alerts.filter(a => a.severity === 'critical').length;

        resolve({
          alerts: alerts.slice(start, end),
          totalCount: alerts.length,
          unreadCount,
          criticalCount
        });
      }, 500);
    });
  }

  async getAlertStats(): Promise<AlertStatsDto> {
    return new Promise((resolve) => {
      setTimeout(() => {
        const mockStats: AlertStatsDto = {
          totalAlerts: 45,
          unreadAlerts: 12,
          criticalAlerts: 3,
          activeAlerts: 38,
          resolvedToday: 7,
          averageResolutionTime: 125,
          alertsByType: [
            { type: 'system', count: 15, criticalCount: 1 },
            { type: 'device', count: 12, criticalCount: 2 },
            { type: 'data', count: 8, criticalCount: 0 },
            { type: 'security', count: 6, criticalCount: 0 },
            { type: 'user', count: 4, criticalCount: 0 }
          ],
          alertsBySeverity: [
            { severity: 'critical', count: 3, resolvedCount: 0 },
            { severity: 'high', count: 8, resolvedCount: 2 },
            { severity: 'medium', count: 18, resolvedCount: 4 },
            { severity: 'low', count: 16, resolvedCount: 1 }
          ],
          alertTrends: [
            { date: '2024-01-15', created: 12, resolved: 8 },
            { date: '2024-01-14', created: 9, resolved: 11 },
            { date: '2024-01-13', created: 15, resolved: 7 }
          ],
          topSources: [
            { source: 'System Monitor', alertCount: 15, criticalCount: 1 },
            { source: 'Device Manager', alertCount: 12, criticalCount: 2 },
            { source: 'Data Validator', alertCount: 8, criticalCount: 0 }
          ]
        };
        resolve(mockStats);
      }, 300);
    });
  }

  async markAlertAsRead(alertId: number): Promise<void> {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve();
      }, 300);
    });
  }

  async dismissAlert(alertId: number): Promise<void> {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve();
      }, 300);
    });
  }

  async markAllAlertsAsRead(): Promise<void> {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve();
      }, 500);
    });
  }

  // ======================== Pending Sync Methods ========================

  async getPendingSyncData(searchParams: any): Promise<any[]> {
    // Mock implementation for pending sync data
    return new Promise((resolve) => {
      setTimeout(() => {
        const mockData = [
          {
            id: 1,
            dataType: 'meter_reading',
            description: 'Meter readings batch from Route 5',
            deviceId: 'DEV-001',
            dataSize: 1024000,
            syncStatus: 'pending',
            progressPercentage: 0,
            priority: 'high',
            createdAt: '2024-01-15T08:30:00Z'
          },
          {
            id: 2,
            dataType: 'photo',
            description: 'Inspection photos',
            deviceId: 'DEV-002',
            dataSize: 5242880,
            syncStatus: 'syncing',
            progressPercentage: 45,
            priority: 'medium',
            createdAt: '2024-01-15T09:15:00Z'
          }
        ];
        resolve(mockData);
      }, 500);
    });
  }

  async getPendingSyncStats(): Promise<any> {
    return new Promise((resolve) => {
      setTimeout(() => {
        const mockStats = {
          totalPending: 15,
          failedItems: 2,
          completedToday: 8,
          totalDataSize: 45.6
        };
        resolve(mockStats);
      }, 300);
    });
  }

  async syncAllData(): Promise<void> {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve();
      }, 2000);
    });
  }

  async syncSelectedData(itemIds: number[]): Promise<void> {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve();
      }, 1500);
    });
  }

  async retryFailedSync(): Promise<void> {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve();
      }, 1000);
    });
  }

  async retrySingleSync(itemId: number): Promise<void> {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve();
      }, 800);
    });
  }

  async syncSingleData(itemId: number): Promise<void> {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve();
      }, 1000);
    });
  }
}

export const quickAccessService = new QuickAccessService(); 