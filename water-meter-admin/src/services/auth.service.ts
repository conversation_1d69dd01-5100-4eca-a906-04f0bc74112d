import { api } from './api';
import { LoginRequest, LoginResponse, User } from '../types/auth';

export class AuthService {
  private tokenKey = 'token'; // Changed from 'auth_token' to 'token' to match API interceptors
  private userKey = 'currentUser';

  async login(credentials: LoginRequest): Promise<LoginResponse> {
    try {
      const response = await api.post('/Auth/login', credentials);
      const data = response.data;
      
      // Handle case-sensitive API response fields (backend uses Pascal case)
      return {
        success: data.Success ?? data.success ?? false,
        message: data.Message ?? data.message ?? '',
        token: data.Token ?? data.token,
        user: data.User ?? data.user
      };
    } catch (error: any) {
      console.error('Login error:', error);
      return {
        success: false,
        message: error?.response?.data?.message || 'Login failed'
      };
    }
  }

  saveToken(token: string): void {
    localStorage.setItem(this.tokenKey, token);
  }

  getToken(): string | null {
    return localStorage.getItem(this.tokenKey);
  }

  saveUser(user: User): void {
    localStorage.setItem(this.userKey, JSON.stringify(user));
  }

  getUser(): User | null {
    const userData = localStorage.getItem(this.userKey);
    if (userData) {
      try {
        return JSON.parse(userData);
      } catch (e) {
        console.error('Failed to parse user data:', e);
        return null;
      }
    }
    return null;
  }

  logout(): void {
    localStorage.removeItem(this.tokenKey);
    localStorage.removeItem(this.userKey);
  }

  isAuthenticated(): boolean {
    const token = this.getToken();
    return !!token;
  }
}

// Create and export authService instance
export const authService = new AuthService();

// Simple utility to get current user information
export function getCurrentUsername(): string {
  // Try to get from localStorage first
  const storedUser = localStorage.getItem('currentUser');
  if (storedUser) {
    try {
      const user = JSON.parse(storedUser);
      return user.username || 'admin';
    } catch (e) {
      console.error('Failed to parse stored user:', e);
    }
  }

  // Default to admin for now (since this is admin interface)
  return 'admin';
} 