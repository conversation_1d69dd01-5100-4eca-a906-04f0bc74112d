import { api } from './api';
import { DynamicSelectRequestDto, DynamicSelectResponseDto } from '@/components/DynamicSelect/types';

export class DynamicSelectService {
  private readonly baseUrl = '/dynamic-select';

  /**
   * Get dynamic select options via GET request
   */
  async getOptions(
    strategy: string,
    dataSource: string,
    params: Record<string, any> = {}
  ): Promise<DynamicSelectResponseDto> {
    try {
      const queryParams = new URLSearchParams({
        strategy,
        dataSource,
        params: JSON.stringify(params)
      });

      const response = await api.get<DynamicSelectResponseDto>(
        `${this.baseUrl}/options?${queryParams.toString()}`
      );

      return response.data;
    } catch (error) {
      console.error('Error getting dynamic select options:', error);
      throw error;
    }
  }

  /**
   * Get dynamic select options via POST request (for complex parameters)
   */
  async getOptionsPost(request: DynamicSelectRequestDto): Promise<DynamicSelectResponseDto> {
    try {
      const response = await api.post<DynamicSelectResponseDto>(
        `${this.baseUrl}/options`,
        request
      );

      return response.data;
    } catch (error) {
      console.error('Error getting dynamic select options via POST:', error);
      throw error;
    }
  }

  /**
   * Get available strategies
   */
  async getAvailableStrategies(): Promise<string[]> {
    try {
      const response = await api.get<string[]>(`${this.baseUrl}/strategies`);
      return response.data;
    } catch (error) {
      console.error('Error getting available strategies:', error);
      throw error;
    }
  }

  /**
   * Get available data sources for a strategy
   */
  async getAvailableDataSources(strategy: string): Promise<string[]> {
    try {
      const response = await api.get<string[]>(`${this.baseUrl}/strategies/${strategy}/datasources`);
      return response.data;
    } catch (error) {
      console.error('Error getting available data sources:', error);
      throw error;
    }
  }
}

export const dynamicSelectService = new DynamicSelectService();
