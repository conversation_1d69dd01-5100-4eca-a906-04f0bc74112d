import { message } from 'antd';
import User<PERSON><PERSON><PERSON><PERSON> from '../api/userRoleApi';
import type {
  UserRoleDto,
  UserRoleAssignmentResultDto,
  RoleAssignmentDetailDto,
  Role,
  UserRoleTableData,
  UserRoleFormData
} from '../types/userRole';

export class UserRoleService {

  static async getUserRolesForTable(userId: number): Promise<UserRoleTableData[]> {
    try {
      const response = await UserRoleApi.getUserRoles(userId);

      if (!response.success) {
        message.error(response.message || 'Failed to get user roles');
        return [];
      }

      return (response.data || []).map(role => ({
        ...role,
        key: `${role.userId}-${role.roleId}`,
        createdAt: this.formatDateTime(role.createdAt)
      }));
    } catch (error) {
      console.error('Failed to get user roles:', error);
      message.error('Failed to get user roles, please try again');
      return [];
    }
  }

  static async getRoleUsersForTable(roleId: number): Promise<UserRoleTableData[]> {
    try {
      const response = await UserRoleApi.getRoleUsers(roleId);

      if (!response.success) {
        message.error(response.message || 'Failed to get role users');
        return [];
      }

      return (response.data || []).map(userRole => ({
        ...userRole,
        key: `${userRole.userId}-${userRole.roleId}`,
        createdAt: this.formatDateTime(userRole.createdAt)
      }));
    } catch (error) {
      console.error('Failed to get role users:', error);
      message.error('Failed to get role users, please try again');
      return [];
    }
  }

  static async assignRolesToUser(formData: UserRoleFormData): Promise<boolean> {
    try {
      const response = await UserRoleApi.assignRolesToUser(formData.userId, {
        roleIds: formData.selectedRoleIds
      });

      if (!response.success) {
        message.error(response.message || 'Failed to assign roles');
        return false;
      }

      this.showAssignmentResult(response.data!);
      return true;
    } catch (error) {
      console.error('Failed to assign roles:', error);
      message.error('Failed to assign roles, please try again');
      return false;
    }
  }

  static async addRoleToUser(userId: number, roleId: number): Promise<boolean> {
    try {
      const response = await UserRoleApi.addRoleToUser(userId, roleId);

      if (!response.success) {
        message.error(response.message || 'Failed to add role');
        return false;
      }

      const result = response.data!;
      if (result.status === 'Error') {
        message.error(result.message);
        return false;
      } else {
        message.success(result.message);
        return true;
      }
    } catch (error) {
      console.error('Failed to add role:', error);
      message.error('Failed to add role, please try again');
      return false;
    }
  }

  static async removeRoleFromUser(userId: number, roleId: number): Promise<boolean> {
    try {
      const response = await UserRoleApi.removeRoleFromUser(userId, roleId);

      if (!response.success) {
        message.error(response.message || 'Failed to remove role');
        return false;
      }

      message.success(response.message || 'Role removed successfully');
      return true;
    } catch (error) {
      console.error('Failed to remove role:', error);
      message.error('Failed to remove role, please try again');
      return false;
    }
  }

  static async checkUserHasRole(userId: number, roleId: number): Promise<boolean> {
    try {
      const response = await UserRoleApi.userHasRole(userId, roleId);
      return response.success && response.data === true;
    } catch (error) {
      console.error('Failed to check user role:', error);
      return false;
    }
  }

  static async getAvailableRoles(): Promise<Role[]> {
    try {
      const response = await UserRoleApi.getAvailableRoles();

      if (!response.success) {
        message.error(response.message || 'Failed to get available roles');
        return [];
      }

      return response.data || [];
    } catch (error) {
      console.error('Failed to get available roles:', error);
      message.error('Failed to get available roles, please try again');
      return [];
    }
  }

  private static showAssignmentResult(result: UserRoleAssignmentResultDto): void {
    if (result.errorCount === 0) {
      message.success(`Successfully assigned ${result.successCount} roles to user ${result.userName}`);
    } else if (result.successCount > 0) {
      message.warning(
        `Partial success: ${result.successCount} roles assigned, ${result.errorCount} failed`
      );
    } else {
      message.error('All role assignments failed');
    }

    if (process.env.NODE_ENV === 'development') {
      console.log('Role assignment details:', result.results);
    }
  }

  private static formatDateTime(dateString: string): string {
    try {
      return new Date(dateString).toLocaleString('en-US', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      });
    } catch {
      return dateString;
    }
  }

  static validateFormData(formData: UserRoleFormData): string | null {
    if (!formData.userId || formData.userId <= 0) {
      return 'Please select a valid user';
    }

    if (!formData.selectedRoleIds || formData.selectedRoleIds.length === 0) {
      return 'Please select at least one role';
    }

    return null;
  }
}

export default UserRoleService;
