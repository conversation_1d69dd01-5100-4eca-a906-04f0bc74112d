import { api } from './api';
import {
  AmisSyncListDto,
  AmisSyncDto,
  AmisSyncSearchDto,
  AmisSyncSearchResponseDto,
  ManualSyncRequestDto,
  SyncProgressDto,
  AmisSyncErrorDto,
  AmisSyncConfigurationDto,
  SyncStatisticsDto,
  ErrorResolutionDto
} from '@/types/amis-sync';

class AmisSyncService {

  // Get sync history with search and pagination
  async getSyncHistory(searchParams: AmisSyncSearchDto): Promise<AmisSyncSearchResponseDto> {
    const response = await api.get('/amis-sync', {
      params: {
        ...searchParams,
        startTimeFrom: searchParams.startTimeFrom?.toISOString(),
        startTimeTo: searchParams.startTimeTo?.toISOString()
      }
    });
    return response.data;
  }

  // Get sync by ID
  async getSyncById(id: number): Promise<AmisSyncDto> {
    const response = await api.get(`/amis-sync/${id}`);
    return response.data;
  }

  // Start manual sync
  async startManualSync(request: ManualSyncRequestDto, triggerBy: string): Promise<AmisSyncDto> {
    const response = await api.post('/amis-sync/manual', {
      ...request,
      fromDate: request.fromDate?.toISOString(),
      toDate: request.toDate?.toISOString(),
      triggerBy
    });
    return response.data;
  }

  // Cancel sync
  async cancelSync(syncId: number, cancelledBy: string): Promise<boolean> {
    const response = await api.put(`/amis-sync/${syncId}/cancel`, {
      cancelledBy
    });
    return response.data.success;
  }

  // Get sync progress
  async getSyncProgress(syncId: number): Promise<SyncProgressDto | null> {
    try {
      const response = await api.get(`/amis-sync/${syncId}/progress`);
      return response.data;
    } catch (error) {
      return null;
    }
  }

  // Get sync errors
  async getSyncErrors(syncId: number): Promise<AmisSyncErrorDto[]> {
    const response = await api.get(`/amis-sync/${syncId}/errors`);
    return response.data;
  }

  // Retry sync
  async retrySync(syncId: number, retryBy: string): Promise<boolean> {
    const response = await api.put(`/amis-sync/${syncId}/retry`, {
      retryBy
    });
    return response.data.success;
  }

  // Resolve sync error
  async resolveSyncError(errorId: number, resolution: ErrorResolutionDto): Promise<boolean> {
    const response = await api.put(`/amis-sync/errors/${errorId}/resolve`, resolution);
    return response.data.success;
  }

  // Get sync configuration
  async getSyncConfiguration(): Promise<AmisSyncConfigurationDto> {
    const response = await api.get('/amis-sync/configuration');
    return response.data;
  }

  // Update sync configuration
  async updateSyncConfiguration(configuration: AmisSyncConfigurationDto): Promise<boolean> {
    const response = await api.put('/amis-sync/configuration', configuration);
    return response.data.success;
  }

  // Test AMIS connection
  async testAmisConnection(): Promise<boolean> {
    try {
      const response = await api.get('/amis-sync/test-connection');
      return response.data.success;
    } catch (error) {
      return false;
    }
  }

  // Get active syncs
  async getActiveSyncs(): Promise<AmisSyncDto[]> {
    const response = await api.get('/amis-sync/active');
    return response.data;
  }

  // Get unresolved errors
  async getUnresolvedErrors(): Promise<AmisSyncErrorDto[]> {
    const response = await api.get('/amis-sync/errors/unresolved');
    return response.data;
  }

  // Get sync statistics
  async getSyncStatistics(): Promise<SyncStatisticsDto> {
    const response = await api.get('/amis-sync/statistics');
    return response.data;
  }

  // Clean up old sync logs
  async cleanupOldSyncLogs(retentionDays: number = 30): Promise<void> {
    await api.delete('/amis-sync/cleanup', {
      params: { retentionDays }
    });
  }

  // Get sync logs for export
  async exportSyncLogs(
    format: 'csv' | 'excel' = 'csv',
    fromDate?: Date,
    toDate?: Date
  ): Promise<Blob> {
    const response = await api.get(`/amis-sync/export/${format}`, {
      params: {
        fromDate: fromDate?.toISOString(),
        toDate: toDate?.toISOString()
      },
      responseType: 'blob'
    });
    return response.data;
  }

  // Get sync errors for export
  async exportSyncErrors(
    format: 'csv' | 'excel' = 'csv',
    syncId?: number
  ): Promise<Blob> {
    const response = await api.get(`/amis-sync/errors/export/${format}`, {
      params: { syncId },
      responseType: 'blob'
    });
    return response.data;
  }

  // Force stop all active syncs
  async forceStopAllSyncs(stoppedBy: string): Promise<number> {
    const response = await api.put('/amis-sync/force-stop-all', {
      stoppedBy
    });
    return response.data.stoppedCount;
  }

  // Get sync performance metrics
  async getSyncPerformanceMetrics(days: number = 7): Promise<any> {
    const response = await api.get('/amis-sync/metrics/performance', {
      params: { days }
    });
    return response.data;
  }

  // Schedule sync
  async scheduleSync(
    syncType: string,
    scheduledTime: Date,
    triggerBy: string
  ): Promise<AmisSyncDto> {
    const response = await api.post('/amis-sync/schedule', {
      syncType,
      scheduledTime: scheduledTime.toISOString(),
      triggerBy
    });
    return response.data;
  }

  // Get scheduled syncs
  async getScheduledSyncs(): Promise<AmisSyncDto[]> {
    const response = await api.get('/amis-sync/scheduled');
    return response.data;
  }

  // Cancel scheduled sync
  async cancelScheduledSync(syncId: number, cancelledBy: string): Promise<boolean> {
    const response = await api.put(`/amis-sync/scheduled/${syncId}/cancel`, {
      cancelledBy
    });
    return response.data.success;
  }

  // Get sync dashboard data
  async getSyncDashboardData(): Promise<any> {
    const response = await api.get('/amis-sync/dashboard');
    return response.data;
  }

  // Validate sync configuration
  async validateSyncConfiguration(configuration: AmisSyncConfigurationDto): Promise<any> {
    const response = await api.post('/amis-sync/configuration/validate', configuration);
    return response.data;
  }

  // Get sync log details
  async getSyncLogDetails(syncId: number): Promise<any> {
    const response = await api.get(`/amis-sync/${syncId}/logs`);
    return response.data;
  }

  // Mark error as resolved
  async markErrorAsResolved(errorId: number, resolvedBy: string, notes?: string): Promise<boolean> {
    const response = await api.put(`/amis-sync/errors/${errorId}/mark-resolved`, {
      resolvedBy,
      notes
    });
    return response.data.success;
  }

  // Bulk resolve errors
  async bulkResolveErrors(errorIds: number[], resolvedBy: string, notes?: string): Promise<number> {
    const response = await api.put('/amis-sync/errors/bulk-resolve', {
      errorIds,
      resolvedBy,
      notes
    });
    return response.data.resolvedCount;
  }

  // Get recent sync activities
  async getRecentActivities(limit: number = 10): Promise<any[]> {
    const response = await api.get('/amis-sync/activities/recent', {
      params: { limit }
    });
    return response.data;
  }
}

export const amisSyncService = new AmisSyncService(); 