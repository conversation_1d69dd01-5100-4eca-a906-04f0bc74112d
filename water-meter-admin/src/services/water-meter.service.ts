import { api } from './api';
import {
  WaterMeterListDto,
  WaterMeterDto,
  CreateWaterMeterDto,
  UpdateWaterMeterDto,
  WaterMeterSearchDto,
  WaterMeterSearchResponseDto,
  WaterMeterImportResultDto,
  WaterMeterStatisticsDto,
  WorkPackageImportResultDto,
  AmsImportResultDto
} from '@/types/water-meter';

class WaterMeterService {

  // Get water meters with search and pagination
  async getWaterMeters(searchParams: WaterMeterSearchDto): Promise<WaterMeterSearchResponseDto> {
    const response = await api.get('/water-meters', {
      params: {
        ...searchParams,
        installDateFrom: searchParams.installDateFrom?.toISOString(),
        installDateTo: searchParams.installDateTo?.toISOString()
      }
    });
    return response.data;
  }

  // Get water meter by ID
  async getWaterMeterById(id: number): Promise<WaterMeterDto> {
    const response = await api.get(`/water-meters/${id}`);
    return response.data;
  }

  // Get water meter by serial number
  async getWaterMeterBySerialNumber(serialNumber: string): Promise<WaterMeterDto> {
    const response = await api.get(`/water-meters/serial/${serialNumber}`);
    return response.data;
  }

  // Create new water meter
  async createWaterMeter(waterMeter: CreateWaterMeterDto): Promise<WaterMeterDto> {
    const response = await api.post('/water-meters', {
      ...waterMeter,
      installDate: waterMeter.installDate?.toISOString()
    });
    return response.data;
  }

  // Update water meter
  async updateWaterMeter(id: number, waterMeter: UpdateWaterMeterDto): Promise<WaterMeterDto> {
    const response = await api.put(`/water-meters/${id}`, {
      ...waterMeter,
      lastMaintenanceDate: waterMeter.lastMaintenanceDate?.toISOString(),
      nextMaintenanceDate: waterMeter.nextMaintenanceDate?.toISOString()
    });
    return response.data;
  }

  // Delete water meter
  async deleteWaterMeter(id: number): Promise<void> {
    await api.delete(`/water-meters/${id}`);
  }

  // Import water meters from CSV
  async importFromCsv(file: File, fileName: string): Promise<WaterMeterImportResultDto> {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('fileName', fileName);

    const response = await api.post('/water-meters/import/csv', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  }

  // Import water meters from Excel
  async importFromExcel(file: File, fileName: string): Promise<WaterMeterImportResultDto> {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('fileName', fileName);

    const response = await api.post('/water-meters/import/excel', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  }

  // Get meters by location
  async getMetersByLocation(location: string): Promise<WaterMeterListDto[]> {
    const response = await api.get('/water-meters/location', {
      params: { location }
    });
    return response.data;
  }

  // Get meters by status
  async getMetersByStatus(status: string): Promise<WaterMeterListDto[]> {
    const response = await api.get('/water-meters/status', {
      params: { status }
    });
    return response.data;
  }

  // Get meters due for maintenance
  async getMetersDueForMaintenance(): Promise<WaterMeterListDto[]> {
    const response = await api.get('/water-meters/maintenance/due');
    return response.data;
  }

  // Get meters with low battery
  async getMetersWithLowBattery(threshold?: number): Promise<WaterMeterListDto[]> {
    const response = await api.get('/water-meters/battery/low', {
      params: { threshold }
    });
    return response.data;
  }

  // Validate serial number uniqueness
  async validateSerialNumber(serialNumber: string, excludeId?: number): Promise<boolean> {
    const response = await api.get('/water-meters/validate/serial', {
      params: { serialNumber, excludeId }
    });
    return response.data.isValid;
  }

  // Update meter reading
  async updateMeterReading(
    meterId: number,
    readingValue: number,
    readingDate: Date,
    dataSource: string = 'Manual'
  ): Promise<void> {
    await api.post(`/water-meters/${meterId}/reading`, {
      readingValue,
      readingDate: readingDate.toISOString(),
      dataSource
    });
  }

  // Get water meter statistics
  async getStatistics(): Promise<WaterMeterStatisticsDto> {
    const response = await api.get('/water-meters/statistics');
    return response.data;
  }

  // Export water meters
  async exportWaterMeters(format: 'csv' | 'excel' = 'csv'): Promise<Blob> {
    const response = await api.get(`/water-meters/export/${format}`, {
      responseType: 'blob'
    });
    return response.data;
  }

  // Download import template
  async downloadImportTemplate(format: 'csv' | 'excel' = 'csv'): Promise<Blob> {
    const response = await api.get(`/water-meters/template/${format}`, {
      responseType: 'blob'
    });
    return response.data;
  }

  // Generate work package template with filtered meters
  async generateWorkPackageTemplate(meterIds: string[]): Promise<Blob> {
    const response = await api.post('/water-meters/work-package-template', {
      meterIds: meterIds
    }, {
      responseType: 'blob'
    });
    return response.data;
  }

  // Import work packages from Excel (water meter template format)
  async importWorkPackagesFromExcel(file: File): Promise<WorkPackageImportResultDto> {
    const formData = new FormData();
    formData.append('file', file);
    
    const response = await api.post('/water-meters/import-work-packages', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  }

  // Bulk update water meters
  async bulkUpdateStatus(meterIds: number[], status: string): Promise<void> {
    await api.put('/water-meters/bulk/status', {
      meterIds,
      status
    });
  }

  // Bulk delete water meters
  async bulkDelete(meterIds: number[]): Promise<void> {
    await api.delete('/water-meters/bulk', {
      data: { meterIds }
    });
  }

  // Search meters by coordinates (for map view)
  async searchMetersByLocation(
    latitude: number,
    longitude: number,
    radius: number
  ): Promise<WaterMeterListDto[]> {
    const response = await api.get('/water-meters/search/location', {
      params: { latitude, longitude, radius }
    });
    return response.data;
  }

  // Get meter reading history
  async getMeterReadingHistory(
    meterId: number,
    fromDate?: Date,
    toDate?: Date
  ): Promise<any[]> {
    const response = await api.get(`/water-meters/${meterId}/readings`, {
      params: {
        fromDate: fromDate?.toISOString(),
        toDate: toDate?.toISOString()
      }
    });
    return response.data;
  }

  // Get meters summary for dashboard
  async getDashboardSummary(): Promise<any> {
    const response = await api.get('/water-meters/dashboard/summary');
    return response.data;
  }

  // Import water meters from AMS Excel
  async importFromAmsExcel(file: File): Promise<AmsImportResultDto> {
    const formData = new FormData();
    formData.append('file', file);

    const response = await api.post('/AmsImport/excel', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      timeout: 60000, // Increase timeout for large files
    });
    return response.data;
  }

  // Download AMS import template
  async downloadAmsImportTemplate(): Promise<Blob> {
    const response = await api.get('/AmsImport/templates/sample', {
      responseType: 'blob'
    });
    return response.data;
  }

  // Get AMS import history
  async getAmsImportHistory(page: number = 1, pageSize: number = 10): Promise<any> {
    const response = await api.get('/AmsImport/history', {
      params: { page, pageSize }
    });
    return response.data;
  }

  // Validate AMS Excel file
  async validateAmsExcelFile(file: File): Promise<any> {
    const formData = new FormData();
    formData.append('file', file);

    const response = await api.post('/AmsImport/validate', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  }
}

export const waterMeterService = new WaterMeterService(); 