import api from './api';

export interface GpsCoordinateResult {
  success: boolean;
  latitude?: number;
  longitude?: number;
  formattedAddress?: string;
  errorMessage?: string;
  placeId?: string;
  addressComponents: string[];
  locationType?: string;
  accuracy?: number;
}

export interface BatchGpsResult {
  totalRequests: number;
  successCount: number;
  failureCount: number;
  results: GpsCoordinateResult[];
  errors: string[];
  processingTime: string;
}

export interface GpsUpdateResult {
  success: boolean;
  waterMeterId: number;
  waterMeterSerial?: string;
  address?: string;
  oldLatitude?: number;
  oldLongitude?: number;
  newLatitude?: number;
  newLongitude?: number;
  errorMessage?: string;
  updatedAt: string;
}

export interface BatchGpsUpdateResult {
  totalMeters: number;
  successCount: number;
  failureCount: number;
  skippedCount: number;
  results: GpsUpdateResult[];
  errors: string[];
  processingTime: string;
  startedAt: string;
  completedAt: string;
}

export interface GpsServiceStats {
  totalWaterMeters: number;
  metersWithGps: number;
  metersWithoutGps: number;
  gpsCompletionPercentage: number;
  todayGpsRequests: number;
  todaySuccessfulRequests: number;
  todayFailedRequests: number;
  todaySuccessRate: number;
  lastGpsUpdate: string;
}

export class GpsService {
  private readonly baseUrl = '/gps';

  async geocodeAddress(address: string): Promise<GpsCoordinateResult> {
    try {
      const response = await api.post(`${this.baseUrl}/geocode`, address, {
        headers: {
          'Content-Type': 'application/json'
        }
      });
      return response.data;
    } catch (error) {
      console.error('Error geocoding address:', error);
      throw error;
    }
  }

  async geocodeAddressesBatch(addresses: string[]): Promise<BatchGpsResult> {
    try {
      const response = await api.post(`${this.baseUrl}/geocode/batch`, addresses);
      return response.data;
    } catch (error) {
      console.error('Error batch geocoding addresses:', error);
      throw error;
    }
  }

  async updateWaterMeterGps(waterMeterId: number): Promise<GpsUpdateResult> {
    try {
      const response = await api.post(`${this.baseUrl}/water-meter/${waterMeterId}/update-gps`);
      return response.data;
    } catch (error) {
      console.error(`Error updating GPS for water meter ${waterMeterId}:`, error);
      throw error;
    }
  }

  async updateWaterMetersGpsBatch(waterMeterIds: number[]): Promise<BatchGpsUpdateResult> {
    try {
      const response = await api.post(`${this.baseUrl}/water-meters/update-gps/batch`, waterMeterIds);
      return response.data;
    } catch (error) {
      console.error('Error batch updating GPS for water meters:', error);
      throw error;
    }
  }

  async updateAllMissingGps(): Promise<BatchGpsUpdateResult> {
    try {
      const response = await api.post(`${this.baseUrl}/water-meters/update-missing-gps`);
      return response.data;
    } catch (error) {
      console.error('Error updating all missing GPS coordinates:', error);
      throw error;
    }
  }

  async validateGpsCoordinates(latitude: number, longitude: number): Promise<{ isValid: boolean; latitude: number; longitude: number }> {
    try {
      const response = await api.get(`${this.baseUrl}/validate`, {
        params: { latitude, longitude }
      });
      return response.data;
    } catch (error) {
      console.error('Error validating GPS coordinates:', error);
      throw error;
    }
  }

  async getGpsServiceStats(): Promise<GpsServiceStats> {
    try {
      const response = await api.get(`${this.baseUrl}/stats`);
      return response.data;
    } catch (error) {
      console.error('Error getting GPS service stats:', error);
      throw error;
    }
  }
}

export const gpsService = new GpsService();
