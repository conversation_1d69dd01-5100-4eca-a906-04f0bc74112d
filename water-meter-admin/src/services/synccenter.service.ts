import { 
  DeviceSyncStatusDto, 
  DeviceSyncSearchParams, 
  DeviceSyncStats,
  SyncQueueDto,
  SyncQueueSearchParams,
  SyncQueueStats,
  OfflineDataDto,
  OfflineDataSearchParams,
  OfflineDataStats,
  SyncLogDto,
  SyncLogSearchParams,
  SyncLogStats,
  BulkSyncOperation,
  BulkSyncResult,
  SyncCenterConfig,
  SyncDataExport
} from '@/types/synccenter';

class SyncCenterService {
  private baseUrl = '/api/sync-center';

  // Device Sync Status Methods
  async getDeviceSyncStatus(params: DeviceSyncSearchParams = {}): Promise<DeviceSyncStatusDto[]> {
    // Mock data for development
    const mockData: DeviceSyncStatusDto[] = [
      {
        id: 1,
        deviceId: 'DEV-001',
        deviceName: 'Mobile Reader 001',
        lastSync: '2024-01-15T10:30:00Z',
        syncStatus: 'online',
        batteryLevel: 85,
        dataIntegrity: 98,
        location: 'Zone A',
        firmwareVersion: '2.1.0',
        connectionType: 'wifi',
        signalStrength: 85,
        pendingDataCount: 0,
        lastHeartbeat: '2024-01-15T10:29:30Z',
        userId: 'user001',
        syncProgress: 100
      },
      {
        id: 2,
        deviceId: 'DEV-002',
        deviceName: 'Mobile Reader 002',
        lastSync: '2024-01-15T09:45:00Z',
        syncStatus: 'syncing',
        batteryLevel: 92,
        dataIntegrity: 95,
        location: 'Zone B',
        firmwareVersion: '2.0.8',
        connectionType: 'cellular',
        signalStrength: 70,
        pendingDataCount: 5,
        lastHeartbeat: '2024-01-15T10:25:00Z',
        userId: 'user002',
        syncProgress: 65,
        estimatedTimeRemaining: 120000
      },
      {
        id: 3,
        deviceId: 'DEV-003',
        deviceName: 'Mobile Reader 003',
        lastSync: '2024-01-15T08:15:00Z',
        syncStatus: 'offline',
        batteryLevel: 15,
        dataIntegrity: 88,
        location: 'Zone C',
        firmwareVersion: '1.9.5',
        connectionType: 'wifi',
        signalStrength: 0,
        pendingDataCount: 12,
        lastHeartbeat: '2024-01-15T08:30:00Z',
        userId: 'user003'
      },
      {
        id: 4,
        deviceId: 'DEV-004',
        deviceName: 'Mobile Reader 004',
        lastSync: '2024-01-15T10:00:00Z',
        syncStatus: 'error',
        batteryLevel: 78,
        dataIntegrity: 75,
        location: 'Zone D',
        firmwareVersion: '2.1.0',
        connectionType: 'cellular',
        signalStrength: 45,
        errorMessage: 'Authentication failed - invalid credentials',
        pendingDataCount: 8,
        lastHeartbeat: '2024-01-15T10:20:00Z',
        userId: 'user004'
      }
    ];

    // Apply filters
    let filteredData = mockData;
    if (params.searchTerm) {
      const term = params.searchTerm.toLowerCase();
      filteredData = filteredData.filter(item => 
        item.deviceId.toLowerCase().includes(term) ||
        item.deviceName.toLowerCase().includes(term) ||
        item.location.toLowerCase().includes(term)
      );
    }
    if (params.syncStatus) {
      filteredData = filteredData.filter(item => item.syncStatus === params.syncStatus);
    }
    if (params.connectionType) {
      filteredData = filteredData.filter(item => item.connectionType === params.connectionType);
    }

    return new Promise(resolve => {
      setTimeout(() => resolve(filteredData), 500);
    });
  }

  async getDeviceSyncStats(): Promise<DeviceSyncStats> {
    const mockStats: DeviceSyncStats = {
      totalDevices: 24,
      onlineDevices: 18,
      syncingDevices: 3,
      offlineDevices: 2,
      errorDevices: 1,
      averageBatteryLevel: 78,
      averageDataIntegrity: 92,
      totalPendingData: 25
    };

    return new Promise(resolve => {
      setTimeout(() => resolve(mockStats), 300);
    });
  }

  async forceSyncDevice(deviceId: string): Promise<void> {
    return new Promise(resolve => {
      setTimeout(resolve, 1000);
    });
  }

  async restartDevice(deviceId: string): Promise<void> {
    return new Promise(resolve => {
      setTimeout(resolve, 1500);
    });
  }

  // Sync Queue Methods
  async getSyncQueue(params: SyncQueueSearchParams = {}): Promise<SyncQueueDto[]> {
    const mockData: SyncQueueDto[] = [
      {
        id: 1,
        taskType: 'data_sync',
        description: 'Sync meter readings from DEV-001',
        deviceId: 'DEV-001',
        userId: 'user001',
        priority: 'high',
        status: 'running',
        progress: 75,
        estimatedDuration: 300000,
        actualDuration: 225000,
        dataSize: 2048576,
        createdAt: '2024-01-15T10:00:00Z',
        startedAt: '2024-01-15T10:05:00Z',
        retryCount: 0,
        maxRetries: 3
      },
      {
        id: 2,
        taskType: 'file_upload',
        description: 'Upload photos from field inspection',
        deviceId: 'DEV-002',
        userId: 'user002',
        priority: 'medium',
        status: 'pending',
        progress: 0,
        estimatedDuration: 600000,
        dataSize: 15728640,
        createdAt: '2024-01-15T09:30:00Z',
        retryCount: 0,
        maxRetries: 3
      },
      {
        id: 3,
        taskType: 'report_generation',
        description: 'Generate daily completion report',
        priority: 'low',
        status: 'completed',
        progress: 100,
        estimatedDuration: 120000,
        actualDuration: 95000,
        dataSize: 524288,
        createdAt: '2024-01-15T08:00:00Z',
        startedAt: '2024-01-15T08:05:00Z',
        completedAt: '2024-01-15T08:06:35Z',
        retryCount: 0,
        maxRetries: 3
      },
      {
        id: 4,
        taskType: 'batch_update',
        description: 'Update device configurations',
        priority: 'critical',
        status: 'failed',
        progress: 45,
        estimatedDuration: 180000,
        actualDuration: 81000,
        dataSize: 1048576,
        createdAt: '2024-01-15T07:30:00Z',
        startedAt: '2024-01-15T07:35:00Z',
        errorMessage: 'Network timeout during configuration upload',
        retryCount: 2,
        maxRetries: 3
      }
    ];

    let filteredData = mockData;
    if (params.searchTerm) {
      const term = params.searchTerm.toLowerCase();
      filteredData = filteredData.filter(item => 
        item.description.toLowerCase().includes(term) ||
        (item.deviceId && item.deviceId.toLowerCase().includes(term))
      );
    }
    if (params.taskType) {
      filteredData = filteredData.filter(item => item.taskType === params.taskType);
    }
    if (params.status) {
      filteredData = filteredData.filter(item => item.status === params.status);
    }
    if (params.priority) {
      filteredData = filteredData.filter(item => item.priority === params.priority);
    }

    return new Promise(resolve => {
      setTimeout(() => resolve(filteredData), 500);
    });
  }

  async getSyncQueueStats(): Promise<SyncQueueStats> {
    const mockStats: SyncQueueStats = {
      totalTasks: 156,
      pendingTasks: 8,
      runningTasks: 3,
      completedTasks: 142,
      failedTasks: 3,
      averageWaitTime: 25000,
      averageExecutionTime: 180000,
      totalDataSize: 1073741824,
      throughputPerHour: 45
    };

    return new Promise(resolve => {
      setTimeout(() => resolve(mockStats), 300);
    });
  }

  async startTask(taskId: number): Promise<void> {
    return new Promise(resolve => {
      setTimeout(resolve, 500);
    });
  }

  async pauseTask(taskId: number): Promise<void> {
    return new Promise(resolve => {
      setTimeout(resolve, 500);
    });
  }

  async cancelTask(taskId: number): Promise<void> {
    return new Promise(resolve => {
      setTimeout(resolve, 500);
    });
  }

  async retryTask(taskId: number): Promise<void> {
    return new Promise(resolve => {
      setTimeout(resolve, 500);
    });
  }

  async bulkOperationTasks(operation: BulkSyncOperation): Promise<BulkSyncResult> {
    const mockResult: BulkSyncResult = {
      successCount: operation.taskIds.length - 1,
      failureCount: 1,
      results: operation.taskIds.map((id, index) => ({
        taskId: id,
        success: index !== 0,
        message: index === 0 ? 'Task is currently running and cannot be modified' : undefined
      }))
    };

    return new Promise(resolve => {
      setTimeout(() => resolve(mockResult), 1000);
    });
  }

  // Offline Data Methods
  async getOfflineData(params: OfflineDataSearchParams = {}): Promise<OfflineDataDto[]> {
    const mockData: OfflineDataDto[] = [
      {
        id: 1,
        dataType: 'meter_reading',
        description: 'Meter readings from Route 5',
        deviceId: 'DEV-001',
        dataSize: 1024576,
        status: 'stored',
        storageLocation: '/storage/offline/readings_001.dat',
        createdAt: '2024-01-15T08:30:00Z',
        lastModified: '2024-01-15T08:30:00Z',
        uploadAttempts: 0,
        checksum: 'sha256:abc123...',
        compressionRatio: 0.65
      },
      {
        id: 2,
        dataType: 'photo',
        description: 'Meter photos from inspection',
        deviceId: 'DEV-002',
        dataSize: 8388608,
        status: 'uploading',
        storageLocation: '/storage/offline/photos_002.zip',
        createdAt: '2024-01-15T09:15:00Z',
        lastModified: '2024-01-15T10:20:00Z',
        uploadAttempts: 1,
        checksum: 'sha256:def456...'
      },
      {
        id: 3,
        dataType: 'task_data',
        description: 'Task completion data',
        deviceId: 'DEV-003',
        dataSize: 512000,
        status: 'failed',
        storageLocation: '/storage/offline/tasks_003.json',
        createdAt: '2024-01-14T16:45:00Z',
        lastModified: '2024-01-15T08:00:00Z',
        uploadAttempts: 3,
        errorMessage: 'Server rejected data due to validation errors',
        checksum: 'sha256:ghi789...'
      },
      {
        id: 4,
        dataType: 'user_settings',
        description: 'User preference updates',
        deviceId: 'DEV-001',
        dataSize: 32768,
        status: 'uploaded',
        storageLocation: '/storage/offline/settings_001.cfg',
        createdAt: '2024-01-14T14:20:00Z',
        lastModified: '2024-01-14T14:20:00Z',
        uploadAttempts: 1,
        checksum: 'sha256:jkl012...'
      }
    ];

    let filteredData = mockData;
    if (params.searchTerm) {
      const term = params.searchTerm.toLowerCase();
      filteredData = filteredData.filter(item => 
        item.description.toLowerCase().includes(term) ||
        item.deviceId.toLowerCase().includes(term)
      );
    }
    if (params.dataType) {
      filteredData = filteredData.filter(item => item.dataType === params.dataType);
    }
    if (params.status) {
      filteredData = filteredData.filter(item => item.status === params.status);
    }

    return new Promise(resolve => {
      setTimeout(() => resolve(filteredData), 500);
    });
  }

  async getOfflineDataStats(): Promise<OfflineDataStats> {
    const mockStats: OfflineDataStats = {
      totalItems: 47,
      pendingUpload: 12,
      failedItems: 3,
      totalStorageSize: 256.7,
      storageUsedPercentage: 12.8,
      averageFileSize: 5.5,
      oldestItem: '2024-01-10T10:00:00Z',
      compressionSavings: 42.3
    };

    return new Promise(resolve => {
      setTimeout(() => resolve(mockStats), 300);
    });
  }

  async uploadOfflineData(itemIds: number[]): Promise<void> {
    return new Promise(resolve => {
      setTimeout(resolve, 2000);
    });
  }

  async uploadSingleOfflineData(itemId: number): Promise<void> {
    return new Promise(resolve => {
      setTimeout(resolve, 1000);
    });
  }

  async deleteOfflineData(itemIds: number[]): Promise<void> {
    return new Promise(resolve => {
      setTimeout(resolve, 500);
    });
  }

  async downloadOfflineData(itemId: number): Promise<void> {
    return new Promise(resolve => {
      setTimeout(resolve, 1000);
    });
  }

  async cleanupOldOfflineData(): Promise<void> {
    return new Promise(resolve => {
      setTimeout(resolve, 3000);
    });
  }

  // Sync Logs Methods
  async getSyncLogs(params: SyncLogSearchParams = {}): Promise<SyncLogDto[]> {
    const mockData: SyncLogDto[] = [
      {
        id: 1,
        timestamp: '2024-01-15T10:30:15Z',
        level: 'info',
        operation: 'data_sync',
        message: 'Successfully synchronized 125 meter readings',
        deviceId: 'DEV-001',
        userId: 'user001',
        duration: 15000,
        dataSize: 1024576,
        correlationId: 'sync-001-20240115'
      },
      {
        id: 2,
        timestamp: '2024-01-15T10:25:42Z',
        level: 'warning',
        operation: 'file_upload',
        message: 'Upload completed with warnings - 2 files skipped due to size limit',
        details: 'Files: large_photo_1.jpg (15MB), large_photo_2.jpg (18MB)',
        deviceId: 'DEV-002',
        userId: 'user002',
        duration: 45000,
        dataSize: 12582912,
        correlationId: 'upload-002-20240115'
      },
      {
        id: 3,
        timestamp: '2024-01-15T10:20:08Z',
        level: 'error',
        operation: 'config_sync',
        message: 'Failed to update device configuration',
        details: 'Connection timeout after 30 seconds',
        stackTrace: 'TimeoutException: The operation has timed out...',
        deviceId: 'DEV-003',
        duration: 30000,
        correlationId: 'config-003-20240115'
      },
      {
        id: 4,
        timestamp: '2024-01-15T10:15:22Z',
        level: 'success',
        operation: 'batch_operation',
        message: 'Batch update completed successfully',
        details: 'Updated 50 device configurations',
        duration: 120000,
        dataSize: 2097152,
        correlationId: 'batch-001-20240115'
      },
      {
        id: 5,
        timestamp: '2024-01-15T10:10:55Z',
        level: 'debug',
        operation: 'auth_sync',
        message: 'User authentication token refreshed',
        userId: 'user001',
        duration: 500,
        correlationId: 'auth-001-20240115'
      }
    ];

    let filteredData = mockData;
    if (params.searchTerm) {
      const term = params.searchTerm.toLowerCase();
      filteredData = filteredData.filter(item => 
        item.message.toLowerCase().includes(term) ||
        item.operation.toLowerCase().includes(term) ||
        (item.details && item.details.toLowerCase().includes(term))
      );
    }
    if (params.level) {
      filteredData = filteredData.filter(item => item.level === params.level);
    }
    if (params.operation) {
      filteredData = filteredData.filter(item => item.operation === params.operation);
    }

    return new Promise(resolve => {
      setTimeout(() => resolve(filteredData), 500);
    });
  }

  async getSyncLogStats(): Promise<SyncLogStats> {
    const mockStats: SyncLogStats = {
      totalLogs: 1247,
      errorLogs: 23,
      warningLogs: 89,
      successfulSyncs: 1086,
      averageOperationTime: 25000,
      mostCommonOperations: [
        { operation: 'data_sync', count: 523 },
        { operation: 'file_upload', count: 287 },
        { operation: 'config_sync', count: 156 },
        { operation: 'auth_sync', count: 145 },
        { operation: 'batch_operation', count: 136 }
      ],
      errorRate: 1.8,
      logsPerHour: 52
    };

    return new Promise(resolve => {
      setTimeout(() => resolve(mockStats), 300);
    });
  }

  async exportSyncLogs(params: SyncLogSearchParams): Promise<void> {
    return new Promise(resolve => {
      setTimeout(resolve, 2000);
    });
  }

  async deleteSyncLogs(logIds: number[]): Promise<void> {
    return new Promise(resolve => {
      setTimeout(resolve, 1000);
    });
  }

  async clearOldSyncLogs(): Promise<void> {
    return new Promise(resolve => {
      setTimeout(resolve, 5000);
    });
  }

  async downloadLogDetails(logId: number): Promise<void> {
    return new Promise(resolve => {
      setTimeout(resolve, 1000);
    });
  }

  // Configuration Methods
  async getSyncCenterConfig(): Promise<SyncCenterConfig> {
    const mockConfig: SyncCenterConfig = {
      autoSyncEnabled: true,
      syncInterval: 300000,
      maxRetryAttempts: 3,
      batchSize: 100,
      compressionEnabled: true,
      offlineStorageLimit: 1073741824,
      logRetentionDays: 90,
      priorityRules: [
        { condition: 'taskType === "data_sync" && dataSize > 10MB', priority: 'high' },
        { condition: 'batteryLevel < 20', priority: 'low' },
        { condition: 'errorCount > 3', priority: 'critical' }
      ]
    };

    return new Promise(resolve => {
      setTimeout(() => resolve(mockConfig), 300);
    });
  }

  async updateSyncCenterConfig(config: Partial<SyncCenterConfig>): Promise<void> {
    return new Promise(resolve => {
      setTimeout(resolve, 1000);
    });
  }
}

export const syncCenterService = new SyncCenterService(); 