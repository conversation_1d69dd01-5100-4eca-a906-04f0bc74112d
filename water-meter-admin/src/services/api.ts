import axios, { AxiosInstance, AxiosRequestConfig } from 'axios';
import { message } from 'antd';

// base url
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:5000';

/**
 * create a unified api client instance
 * all services should use this instance to avoid duplicate interceptor configurations
 */
export const createApiClient = (config?: AxiosRequestConfig): AxiosInstance => {
  const instance = axios.create({
    baseURL: `${API_BASE_URL}/api`,
    timeout: 30000,
    headers: {
      'Content-Type': 'application/json',
    },
    ...config
  });

  // request interceptor - add auth token
  instance.interceptors.request.use(
    (config) => {
      const token = localStorage.getItem('token');
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
      return config;
    },
    (error) => {
      console.error('Request interceptor error:', error);
      return Promise.reject(error);
    }
  );

  // response interceptor - unified error handling
  instance.interceptors.response.use(
    (response) => response,
    (error) => {
      console.error('API Error:', error);
      
      if (error.response?.status === 401) {
        message.error('Authentication failed, please login again');
        localStorage.removeItem('token');
        localStorage.removeItem('refreshToken');
        // avoid infinite redirect on login page
        if (window.location.pathname !== '/login') {
          window.location.href = '/login';
        }
      } else if (error.response?.status === 403) {
        message.error('Access denied. You do not have permission for this operation');
      } else if (error.response?.status >= 500) {
        message.error('Server error, please try again later');
      } else if (error.response?.status === 404) {
        message.error('Requested resource not found');
      } else if (error.code === 'ECONNABORTED') {
        message.error('Request timeout, please try again');
      } else if (!error.response) {
        message.error('Network error, please check your connection');
      }
      
      return Promise.reject(error);
    }
  );

  return instance;
};

// default api client
export const api = createApiClient();

// re-export api
export default api; 