// Mobile Management Service

import { api } from './api';
import dayjs from 'dayjs';
import type {
  DeviceDto, DeviceSearchDto, DeviceSearchResultDto, CreateDeviceDto, UpdateDeviceDto, DeviceStatsDto,
  AppVersionDto, AppVersionSearchDto, AppVersionSearchResultDto, CreateAppVersionDto, UpdateAppVersionDto, AppVersionStatsDto,
  CompatibilityTestDto, CompatibilityTestSearchDto, CompatibilityTestSearchResultDto, CreateCompatibilityTestDto, UpdateCompatibilityTestDto, CompatibilityStatsDto
} from '@/types/mobile';

class MobileService {

  // ======================== Device Registry ========================

  async getDevices(searchParams: DeviceSearchDto): Promise<DeviceSearchResultDto> {
    return new Promise((resolve) => {
      setTimeout(() => {
        const devices: DeviceDto[] = [];
        const brands = ['Samsung', 'Apple', 'Huawei', 'Xiaomi', 'Google', 'OnePlus'];
        const deviceTypes: ('smartphone' | 'tablet' | 'handheld_scanner' | 'other')[] = ['smartphone', 'tablet', 'handheld_scanner', 'other'];
        const osTypes: ('android' | 'ios' | 'windows' | 'other')[] = ['android', 'ios', 'windows', 'other'];
        const statuses: ('active' | 'inactive' | 'suspended' | 'maintenance')[] = ['active', 'inactive', 'suspended', 'maintenance'];
        
        for (let i = 0; i < 50; i++) {
          const osType = osTypes[Math.floor(Math.random() * osTypes.length)];
          const brand = brands[Math.floor(Math.random() * brands.length)];
          const deviceType = deviceTypes[Math.floor(Math.random() * deviceTypes.length)];
          
          devices.push({
            id: i + 1,
            deviceId: `DEV-${(i + 1).toString().padStart(4, '0')}`,
            deviceName: `${brand} Device`,
            deviceType,
            brand,
            model: `Model-${Math.floor(Math.random() * 100) + 1}`,
            osType,
            osVersion: osType === 'android' ? `${Math.floor(Math.random() * 4) + 10}.0` : osType === 'ios' ? `${Math.floor(Math.random() * 3) + 14}.0` : '10.0',
            appVersion: `${Math.floor(Math.random() * 3) + 1}.${Math.floor(Math.random() * 10)}.${Math.floor(Math.random() * 10)}`,
            registeredAt: dayjs().subtract(Math.floor(Math.random() * 365), 'day').toISOString(),
            lastActiveAt: dayjs().subtract(Math.floor(Math.random() * 24), 'hour').toISOString(),
            status: statuses[Math.floor(Math.random() * statuses.length)],
            userId: Math.floor(Math.random() * 100) + 1,
            userName: `User ${i + 1}`,
            batteryLevel: Math.floor(Math.random() * 100),
            storageUsed: Math.floor(Math.random() * 50) + 10,
            storageTotal: 64,
            memoryUsed: Math.floor(Math.random() * 4) + 2,
            memoryTotal: 8,
            networkType: ['wifi', '4g', '5g'][Math.floor(Math.random() * 3)] as 'wifi' | '4g' | '5g',
            lastSyncAt: dayjs().subtract(Math.floor(Math.random() * 120), 'minute').toISOString(),
          });
        }

        const start = (searchParams.page - 1) * searchParams.pageSize;
        const end = start + searchParams.pageSize;

        resolve({
          devices: devices.slice(start, end),
          totalCount: devices.length
        });
      }, 500);
    });
  }

  async getDeviceStats(): Promise<DeviceStatsDto> {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          totalDevices: 127,
          activeDevices: 95,
          inactiveDevices: 18,
          suspendedDevices: 14,
          androidDevices: 78,
          iosDevices: 35,
          otherDevices: 14,
          averageBatteryLevel: 67,
          devicesLowBattery: 12,
          devicesNeedUpdate: 23
        });
      }, 300);
    });
  }

  async createDevice(device: CreateDeviceDto): Promise<DeviceDto> {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          id: Math.floor(Math.random() * 1000) + 1000,
          ...device,
          registeredAt: dayjs().toISOString(),
          lastActiveAt: dayjs().toISOString(),
          status: 'active',
          userName: device.userId ? `User ${device.userId}` : undefined,
          batteryLevel: Math.floor(Math.random() * 100),
          storageUsed: Math.floor(Math.random() * 50) + 10,
          storageTotal: 64,
          memoryUsed: Math.floor(Math.random() * 4) + 2,
          memoryTotal: 8,
          networkType: 'wifi',
          lastSyncAt: dayjs().toISOString()
        });
      }, 500);
    });
  }

  async updateDevice(id: number, device: UpdateDeviceDto): Promise<DeviceDto> {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          id,
          ...device,
          registeredAt: dayjs().subtract(30, 'day').toISOString(),
          lastActiveAt: dayjs().toISOString(),
          userName: device.userId ? `User ${device.userId}` : undefined,
          batteryLevel: Math.floor(Math.random() * 100),
          storageUsed: Math.floor(Math.random() * 50) + 10,
          storageTotal: 64,
          memoryUsed: Math.floor(Math.random() * 4) + 2,
          memoryTotal: 8,
          networkType: 'wifi',
          lastSyncAt: dayjs().toISOString()
        });
      }, 500);
    });
  }

  async deleteDevice(id: number): Promise<void> {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve();
      }, 300);
    });
  }

  // ======================== App Versions ========================

  async getAppVersions(searchParams: AppVersionSearchDto): Promise<AppVersionSearchResultDto> {
    return new Promise((resolve) => {
      setTimeout(() => {
        const versions: AppVersionDto[] = [];
        const platforms: ('android' | 'ios' | 'windows' | 'web')[] = ['android', 'ios', 'windows', 'web'];
        const statuses: ('development' | 'testing' | 'released' | 'deprecated')[] = ['development', 'testing', 'released', 'deprecated'];
        
        for (let i = 0; i < 30; i++) {
          const platform = platforms[Math.floor(Math.random() * platforms.length)];
          const majorVersion = Math.floor(Math.random() * 3) + 1;
          const minorVersion = Math.floor(Math.random() * 10);
          const patchVersion = Math.floor(Math.random() * 10);
          
          versions.push({
            id: i + 1,
            versionNumber: `${majorVersion}.${minorVersion}.${patchVersion}`,
            versionCode: i + 100,
            platform,
            releaseDate: dayjs().subtract(Math.floor(Math.random() * 180), 'day').toISOString(),
            status: statuses[Math.floor(Math.random() * statuses.length)],
            minOsVersion: platform === 'android' ? '8.0' : platform === 'ios' ? '12.0' : '10.0',
            targetOsVersion: platform === 'android' ? '13.0' : platform === 'ios' ? '16.0' : '11.0',
            fileSize: Math.floor(Math.random() * 100) * 1024 * 1024 + 50 * 1024 * 1024,
            downloadUrl: `https://downloads.example.com/app-${majorVersion}.${minorVersion}.${patchVersion}-${platform}.apk`,
            releaseNotes: `Release notes for version ${majorVersion}.${minorVersion}.${patchVersion}`,
            features: ['New feature 1', 'Improved performance', 'Enhanced security'],
            bugFixes: ['Fixed login issue', 'Resolved crash on startup', 'Fixed sync problems'],
            knownIssues: Math.random() > 0.7 ? ['Minor UI glitch on some devices'] : [],
            isForced: Math.random() > 0.8,
            isRecommended: Math.random() > 0.6,
            installCount: Math.floor(Math.random() * 10000) + 1000,
            activeUsers: Math.floor(Math.random() * 8000) + 500,
            crashReports: Math.floor(Math.random() * 50),
            rating: Math.floor(Math.random() * 2) + 3 + Math.random(),
            createdBy: 'Development Team',
            createdAt: dayjs().subtract(Math.floor(Math.random() * 200), 'day').toISOString(),
            updatedAt: dayjs().subtract(Math.floor(Math.random() * 30), 'day').toISOString()
          });
        }

        const start = (searchParams.page - 1) * searchParams.pageSize;
        const end = start + searchParams.pageSize;

        resolve({
          versions: versions.slice(start, end),
          totalCount: versions.length
        });
      }, 500);
    });
  }

  async getAppVersionStats(): Promise<AppVersionStatsDto> {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          totalVersions: 28,
          activeVersions: 12,
          latestVersion: '2.1.3',
          totalDownloads: 125847,
          totalActiveUsers: 3456,
          averageRating: 4.2,
          criticalIssues: 3,
          platformDistribution: [
            { platform: 'Android', count: 15, percentage: 53.6 },
            { platform: 'iOS', count: 8, percentage: 28.6 },
            { platform: 'Windows', count: 3, percentage: 10.7 },
            { platform: 'Web', count: 2, percentage: 7.1 }
          ]
        });
      }, 300);
    });
  }

  async createAppVersion(version: CreateAppVersionDto): Promise<AppVersionDto> {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          id: Math.floor(Math.random() * 1000) + 1000,
          ...version,
          releaseDate: dayjs().toISOString(),
          status: 'development',
          fileSize: 75 * 1024 * 1024,
          downloadUrl: `https://downloads.example.com/app-${version.versionNumber}-${version.platform}.apk`,
          installCount: 0,
          activeUsers: 0,
          crashReports: 0,
          rating: 0,
          createdBy: 'Current User',
          createdAt: dayjs().toISOString(),
          updatedAt: dayjs().toISOString()
        });
      }, 500);
    });
  }

  async updateAppVersion(id: number, version: UpdateAppVersionDto): Promise<AppVersionDto> {
    // Mock data for now
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          id,
          ...version,
          versionCode: id + 100,
          releaseDate: dayjs().toISOString(),
          installCount: Math.floor(Math.random() * 10000) + 1000,
          activeUsers: Math.floor(Math.random() * 8000) + 500,
          crashReports: Math.floor(Math.random() * 50),
          rating: Math.floor(Math.random() * 2) + 3 + Math.random(),
          createdBy: 'Development Team',
          createdAt: dayjs().subtract(30, 'day').toISOString(),
          updatedAt: dayjs().toISOString()
        });
      }, 500);
    });
  }

  async deleteAppVersion(id: number): Promise<void> {
    // Mock implementation
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve();
      }, 300);
    });
  }

  // ======================== Compatibility Tests ========================

  async getCompatibilityTests(searchParams: CompatibilityTestSearchDto): Promise<CompatibilityTestSearchResultDto> {
    return new Promise((resolve) => {
      setTimeout(() => {
        const tests: CompatibilityTestDto[] = [];
        const testTypes: ('functional' | 'performance' | 'ui' | 'integration' | 'security')[] = ['functional', 'performance', 'ui', 'integration', 'security'];
        const platforms = ['Android', 'iOS', 'Windows'];
        const statuses: ('pending' | 'running' | 'passed' | 'failed' | 'skipped')[] = ['pending', 'running', 'passed', 'failed', 'skipped'];
        
        for (let i = 0; i < 40; i++) {
          const testType = testTypes[Math.floor(Math.random() * testTypes.length)];
          const platform = platforms[Math.floor(Math.random() * platforms.length)];
          const testStatus = statuses[Math.floor(Math.random() * statuses.length)];
          const passRate = testStatus === 'passed' ? Math.floor(Math.random() * 20) + 80 : testStatus === 'failed' ? Math.floor(Math.random() * 50) + 10 : Math.floor(Math.random() * 100);
          
          tests.push({
            id: i + 1,
            testName: `${testType.charAt(0).toUpperCase() + testType.slice(1)} Test ${i + 1}`,
            testType,
            appVersion: `2.${Math.floor(Math.random() * 3)}.${Math.floor(Math.random() * 5)}`,
            platform,
            osVersion: platform === 'Android' ? `${Math.floor(Math.random() * 4) + 10}.0` : platform === 'iOS' ? `${Math.floor(Math.random() * 3) + 14}.0` : '10.0',
            deviceModel: ['Samsung Galaxy S22', 'iPhone 14', 'iPad Pro', 'Huawei P40', 'Google Pixel 7'][Math.floor(Math.random() * 5)],
            testStatus,
            startedAt: testStatus !== 'pending' ? dayjs().subtract(Math.floor(Math.random() * 72), 'hour').toISOString() : undefined,
            completedAt: testStatus === 'passed' || testStatus === 'failed' ? dayjs().subtract(Math.floor(Math.random() * 24), 'hour').toISOString() : undefined,
            duration: testStatus === 'passed' || testStatus === 'failed' ? Math.floor(Math.random() * 120) + 30 : undefined,
            testCases: [
              {
                id: 1,
                name: 'Login Test',
                description: 'Test user login functionality',
                category: 'Authentication',
                priority: 'high',
                status: testStatus === 'running' ? 'running' : testStatus === 'passed' ? 'passed' : testStatus === 'failed' ? 'failed' : 'pending',
                result: testStatus === 'passed' ? 'Success' : testStatus === 'failed' ? 'Login failed' : undefined,
                duration: testStatus === 'passed' || testStatus === 'failed' ? Math.floor(Math.random() * 30) + 5 : undefined,
                executedAt: testStatus !== 'pending' ? dayjs().subtract(Math.floor(Math.random() * 60), 'minute').toISOString() : undefined
              }
            ],
            overallScore: testStatus === 'passed' || testStatus === 'failed' ? Math.floor(Math.random() * 30) + 70 : 0,
            passRate,
            failureReason: testStatus === 'failed' ? 'Network connectivity issues' : undefined,
            testEnvironment: 'QA Environment',
            testerName: `Tester ${Math.floor(Math.random() * 10) + 1}`,
            createdAt: dayjs().subtract(Math.floor(Math.random() * 100), 'hour').toISOString(),
            updatedAt: dayjs().subtract(Math.floor(Math.random() * 24), 'hour').toISOString()
          });
        }

        const start = (searchParams.page - 1) * searchParams.pageSize;
        const end = start + searchParams.pageSize;

        resolve({
          tests: tests.slice(start, end),
          totalCount: tests.length
        });
      }, 500);
    });
  }

  async getCompatibilityStats(): Promise<CompatibilityStatsDto> {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          totalTests: 142,
          passedTests: 98,
          failedTests: 21,
          pendingTests: 23,
          overallPassRate: 82.4,
          averageTestDuration: 67.5,
          criticalFailures: 8,
          platformCoverage: [
            { platform: 'Android', testCount: 65, passRate: 84.6 },
            { platform: 'iOS', testCount: 52, passRate: 86.5 },
            { platform: 'Windows', testCount: 25, passRate: 72.0 }
          ],
          recentTestTrends: [
            { date: dayjs().subtract(6, 'day').format('YYYY-MM-DD'), passed: 12, failed: 3 },
            { date: dayjs().subtract(5, 'day').format('YYYY-MM-DD'), passed: 15, failed: 2 },
            { date: dayjs().subtract(4, 'day').format('YYYY-MM-DD'), passed: 18, failed: 4 },
            { date: dayjs().subtract(3, 'day').format('YYYY-MM-DD'), passed: 14, failed: 1 },
            { date: dayjs().subtract(2, 'day').format('YYYY-MM-DD'), passed: 16, failed: 5 },
            { date: dayjs().subtract(1, 'day').format('YYYY-MM-DD'), passed: 13, failed: 2 },
            { date: dayjs().format('YYYY-MM-DD'), passed: 10, failed: 4 }
          ]
        });
      }, 300);
    });
  }

  async createCompatibilityTest(test: CreateCompatibilityTestDto): Promise<CompatibilityTestDto> {
    // Mock data for now
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          id: Math.floor(Math.random() * 1000) + 1000,
          ...test,
          testStatus: 'pending',
          testCases: test.testCases.map((tc, index) => ({
            ...tc,
            id: index + 1,
            status: 'pending',
            executedAt: undefined
          })),
          overallScore: 0,
          passRate: 0,
          testerName: 'Current User',
          createdAt: dayjs().toISOString(),
          updatedAt: dayjs().toISOString()
        });
      }, 500);
    });
  }

  async updateCompatibilityTest(id: number, test: UpdateCompatibilityTestDto): Promise<CompatibilityTestDto> {
    // Mock data for now
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          id,
          ...test,
          testCases: test.testCases.map((tc, index) => ({
            ...tc,
            id: index + 1,
            status: test.testStatus === 'passed' ? 'passed' : test.testStatus === 'failed' ? 'failed' : 'pending',
            executedAt: test.testStatus !== 'pending' ? dayjs().toISOString() : undefined
          })),
          startedAt: test.testStatus !== 'pending' ? dayjs().subtract(1, 'hour').toISOString() : undefined,
          completedAt: test.testStatus === 'passed' || test.testStatus === 'failed' ? dayjs().toISOString() : undefined,
          duration: test.testStatus === 'passed' || test.testStatus === 'failed' ? 67 : undefined,
          testerName: 'Current User',
          createdAt: dayjs().subtract(1, 'day').toISOString(),
          updatedAt: dayjs().toISOString()
        });
      }, 500);
    });
  }

  async deleteCompatibilityTest(id: number): Promise<void> {
    // Mock implementation
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve();
      }, 300);
    });
  }
}

export const mobileService = new MobileService(); 