import { api } from './api';
import { AddressParseResult, AddressSuggestion } from '@/components/AddressInput/types';

export class AddressService {
  private readonly baseUrl = '/address';

  /**
   * Parse address and return structured components
   */
  async parseAddress(address: string): Promise<AddressParseResult> {
    try {
      const response = await api.post(`${this.baseUrl}/parse`, {
        address,
        countryCode: 'NZ'
      });
      return response.data;
    } catch (error) {
      console.error('Error parsing address:', error);
      // Return fallback result
      return {
        success: false,
        errorMessage: 'Address parsing service unavailable',
        formattedAddress: address
      };
    }
  }

  /**
   * Get address suggestions for autocomplete
   */
  async getAddressSuggestions(input: string): Promise<AddressSuggestion[]> {
    try {
      if (!input || input.length < 3) {
        return [];
      }

      const response = await api.get(`${this.baseUrl}/suggestions`, {
        params: {
          input,
          countryCode: 'NZ'
        }
      });
      return response.data;
    } catch (error) {
      console.error('Error getting address suggestions:', error);
      return [];
    }
  }
}

export const addressService = new AddressService();
