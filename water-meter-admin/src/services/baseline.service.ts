import { api } from './api';
import {
  BaselineRecordListDto,
  BaselineRecordDto,
  CreateBaselineRecordDto,
  UpdateBaselineRecordDto,
  BaselineSearchDto,
  BaselineSearchResponseDto,
  BaselineImportResultDto,
  BaselineHistoryDto,
  BaselineAnomalyDto,
  BaselineValidationDto,
  BaselineCorrectionDto,
  BaselineStatisticsDto,
  MeterBaselineSummaryDto,
  BatchValidationDto,
  BatchValidationResultDto
} from '@/types/baseline';

class BaselineService {

  // Get baseline records with search and pagination
  async getBaselineRecords(searchParams: BaselineSearchDto): Promise<BaselineSearchResponseDto> {
    const response = await api.get('/baseline', {
      params: {
        ...searchParams,
        baselineDateFrom: searchParams.baselineDateFrom?.toISOString(),
        baselineDateTo: searchParams.baselineDateTo?.toISOString()
      }
    });
    return response.data;
  }

  // Get baseline record by ID
  async getBaselineRecordById(id: number): Promise<BaselineRecordDto> {
    const response = await api.get(`/baseline/${id}`);
    return response.data;
  }

  // Create new baseline record
  async createBaselineRecord(baseline: CreateBaselineRecordDto): Promise<BaselineRecordDto> {
    const response = await api.post('/baseline', {
      ...baseline,
      baselineDate: baseline.baselineDate.toISOString()
    });
    return response.data;
  }

  // Update baseline record
  async updateBaselineRecord(id: number, baseline: UpdateBaselineRecordDto): Promise<BaselineRecordDto> {
    const response = await api.put(`/baseline/${id}`, {
      ...baseline,
      baselineDate: baseline.baselineDate.toISOString()
    });
    return response.data;
  }

  // Delete baseline record
  async deleteBaselineRecord(id: number): Promise<void> {
    await api.delete(`/baseline/${id}`);
  }

  // Import baseline records from CSV
  async importFromCsv(file: File, fileName: string): Promise<BaselineImportResultDto> {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('fileName', fileName);

    const response = await api.post('/baseline/import/csv', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  }

  // Import baseline records from Excel
  async importFromExcel(file: File, fileName: string): Promise<BaselineImportResultDto> {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('fileName', fileName);

    const response = await api.post('/baseline/import/excel', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  }

  // Get meter baseline history
  async getMeterBaselineHistory(meterId: number): Promise<BaselineHistoryDto> {
    const response = await api.get(`/baseline/meter/${meterId}/history`);
    return response.data;
  }

  // Get anomalous baselines
  async getAnomalousBaselines(): Promise<BaselineAnomalyDto[]> {
    const response = await api.get('/baseline/anomalous');
    return response.data;
  }

  // Get unvalidated baselines
  async getUnvalidatedBaselines(): Promise<BaselineRecordListDto[]> {
    const response = await api.get('/baseline/unvalidated');
    return response.data;
  }

  // Validate baseline
  async validateBaseline(validation: BaselineValidationDto): Promise<boolean> {
    const response = await api.put('/baseline/validate', validation);
    return response.data.success;
  }

  // Correct baseline
  async correctBaseline(correction: BaselineCorrectionDto, correctedBy: string): Promise<boolean> {
    const response = await api.put('/baseline/correct', {
      ...correction,
      correctedBy
    });
    return response.data.success;
  }

  // Get baselines by import batch
  async getBaselinesByImportBatch(importBatch: string): Promise<BaselineRecordListDto[]> {
    const response = await api.get(`/baseline/import-batch/${importBatch}`);
    return response.data;
  }

  // Get baseline statistics
  async getBaselineStatistics(): Promise<BaselineStatisticsDto> {
    const response = await api.get('/baseline/statistics');
    return response.data;
  }

  // Get recent baselines
  async getRecentBaselines(days: number = 7): Promise<BaselineRecordListDto[]> {
    const response = await api.get('/baseline/recent', {
      params: { days }
    });
    return response.data;
  }

  // Mark baseline as anomalous
  async markBaselineAsAnomalous(baselineId: number, description: string): Promise<boolean> {
    const response = await api.post(`/baseline/${baselineId}/mark-anomalous`, {
      description
    });
    return response.status === 204;
  }

  // Calculate variances for a meter
  async calculateVariances(meterId: number): Promise<boolean> {
    const response = await api.post(`/baseline/meter/${meterId}/calculate-variances`);
    return response.status === 204;
  }

  // Get superseded baselines for a meter
  async getSupersededBaselines(meterId: number): Promise<BaselineRecordDto[]> {
    const response = await api.get(`/baseline/meter/${meterId}/superseded`);
    return response.data;
  }

  // Export baseline records
  async exportBaselineRecords(
    format: 'csv' | 'excel' = 'csv',
    searchParams?: BaselineSearchDto
  ): Promise<Blob> {
    const response = await api.get(`/baseline/export/${format}`, {
      params: {
        ...searchParams,
        baselineDateFrom: searchParams?.baselineDateFrom?.toISOString(),
        baselineDateTo: searchParams?.baselineDateTo?.toISOString()
      },
      responseType: 'blob'
    });
    return response.data;
  }

  // Download import template
  async downloadImportTemplate(format: 'csv' | 'excel' = 'csv'): Promise<Blob> {
    const response = await api.get(`/baseline/template/${format}`, {
      responseType: 'blob'
    });
    return response.data;
  }

  // Bulk validate baselines
  async bulkValidateBaselines(
    baselineIds: number[],
    isValid: boolean,
    validatedBy: string,
    notes?: string
  ): Promise<number> {
    const response = await api.put('/baseline/bulk-validate', {
      baselineIds,
      isValid,
      validatedBy,
      notes
    });
    return response.data.validatedCount;
  }

  // Bulk delete baselines
  async bulkDeleteBaselines(baselineIds: number[]): Promise<number> {
    const response = await api.delete('/baseline/bulk', {
      data: { baselineIds }
    });
    return response.data.deletedCount;
  }

  // Get baseline trends for meter
  async getBaselineTrends(
    meterId: number,
    fromDate?: Date,
    toDate?: Date
  ): Promise<any[]> {
    const response = await api.get(`/baseline/meter/${meterId}/trends`, {
      params: {
        fromDate: fromDate?.toISOString(),
        toDate: toDate?.toISOString()
      }
    });
    return response.data;
  }

  // Get meter baseline summary
  async getMeterBaselineSummary(meterId: number): Promise<MeterBaselineSummaryDto> {
    const response = await api.get(`/baseline/meter/${meterId}/summary`);
    return response.data;
  }

  // Search baselines by value range
  async searchBaselinesByValueRange(
    minValue: number,
    maxValue: number,
    meterType?: string
  ): Promise<BaselineRecordListDto[]> {
    const response = await api.get('/baseline/search/value-range', {
      params: { minValue, maxValue, meterType }
    });
    return response.data;
  }

  // Get baseline variance analysis
  async getBaselineVarianceAnalysis(
    meterId?: number,
    fromDate?: Date,
    toDate?: Date
  ): Promise<any> {
    const response = await api.get('/baseline/analysis/variance', {
      params: {
        meterId,
        fromDate: fromDate?.toISOString(),
        toDate: toDate?.toISOString()
      }
    });
    return response.data;
  }

  // Get baselines requiring attention
  async getBaselinesRequiringAttention(): Promise<BaselineRecordListDto[]> {
    const response = await api.get('/baseline/requiring-attention');
    return response.data;
  }

  // Get baseline quality score
  async getBaselineQualityScore(baselineId: number): Promise<any> {
    const response = await api.get(`/baseline/${baselineId}/quality-score`);
    return response.data;
  }

  // Update baseline quality scores
  async updateBaselineQualityScores(meterId?: number): Promise<number> {
    const response = await api.put('/baseline/update-quality-scores', {
      meterId
    });
    return response.data.updatedCount;
  }

  // Get baseline dashboard data
  async getBaselineDashboardData(): Promise<any> {
    const response = await api.get('/baseline/dashboard');
    return response.data;
  }

  // Generate baseline report
  async generateBaselineReport(
    reportType: 'summary' | 'detailed' | 'anomalies',
    format: 'pdf' | 'excel' = 'pdf',
    filters?: any
  ): Promise<Blob> {
    const response = await api.post(`/baseline/reports/${reportType}`, filters, {
      params: { format },
      responseType: 'blob'
    });
    return response.data;
  }

  // Get validation rules
  async getValidationRules(): Promise<any[]> {
    const response = await api.get('/baseline/validation-rules');
    return response.data;
  }

  // Update validation rules
  async updateValidationRules(rules: any[]): Promise<boolean> {
    const response = await api.put('/baseline/validation-rules', { rules });
    return response.data.success;
  }

  // Export baselines
  async exportBaselines(searchParams: BaselineSearchDto): Promise<Blob> {
    const response = await api.get('/baseline/export', {
      params: {
        ...searchParams,
        baselineDateFrom: searchParams.baselineDateFrom?.toISOString(),
        baselineDateTo: searchParams.baselineDateTo?.toISOString()
      },
      responseType: 'blob'
    });
    return response.data;
  }

  // Download Excel template
  async downloadExcelTemplate(): Promise<Blob> {
    const response = await api.get('/baseline/template/excel', {
      responseType: 'blob'
    });
    return response.data;
  }

  // Batch validate baselines
  async batchValidateBaselines(batchDto: BatchValidationDto): Promise<BatchValidationResultDto> {
    const response = await api.post('/baseline/batch-validate', batchDto);
    return response.data;
  }

  // Import from SDC file
  async importFromSdcFile(file: File): Promise<BaselineImportResultDto> {
    const formData = new FormData();
    formData.append('file', file);

    const response = await api.post('/baseline/import/sdc', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      timeout: 60000, // Increase timeout for large files
    });
    return response.data;
  }

  // Download SDC import template
  async downloadSdcImportTemplate(): Promise<Blob> {
    const response = await api.get('/baseline/templates/sdc-sample', {
      responseType: 'blob'
    });
    return response.data;
  }

  // Get SDC import history
  async getSdcImportHistory(page: number = 1, pageSize: number = 10): Promise<any> {
    const response = await api.get('/baseline/import/sdc/history', {
      params: { page, pageSize }
    });
    return response.data;
  }

  // Validate SDC file
  async validateSdcFile(file: File): Promise<any> {
    const formData = new FormData();
    formData.append('file', file);

    const response = await api.post('/baseline/import/sdc/validate', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  }
}

export const baselineService = new BaselineService(); 