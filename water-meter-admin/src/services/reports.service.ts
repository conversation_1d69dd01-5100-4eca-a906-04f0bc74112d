import { api } from './api';
import dayjs from 'dayjs';

// Import types from our types file
import {
  DashboardMetricsDto,
  CompletionTrendDto,
  AnomalyTrendDto,
  UserPerformanceOverviewDto,
  CompletionReportDto,
  CompletionReportSearchDto,
  CompletionReportSearchResultDto,
  ExceptionAnalysisDto,
  ExceptionSearchDto,
  ExceptionAnalysisSearchResultDto,
  ExceptionSummaryDto,
  UserPerformanceDto,
  UserPerformanceSearchDto,
  UserPerformanceSearchResultDto,
  ExportRequestDto,
  ExportJobDto,
  ExportHistorySearchDto,
  ExportHistorySearchResultDto,
  ReportTemplateDto,
  CreateReportTemplateDto,
  UpdateReportTemplateDto,
  TemplateSearchDto,
  TemplateSearchResultDto,
  GenerateReportRequestDto,
  GeneratedReportDto,
  ChartDataPoint
} from '@/types/reports';

class ReportsService {

  // ======================== Operational Dashboard ========================

  async getDashboardMetrics(): Promise<DashboardMetricsDto> {
    // Mock data for now
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          totalWaterMeters: 12450,
          activeMeters: 11890,
          totalReadingsToday: 2870,
          totalReadingsThisMonth: 87450,
          completionRateToday: 85.6,
          completionRateThisMonth: 92.3,
          pendingTasks: 340,
          overdueTasks: 23,
          anomaliesCount: 12,
          systemHealthScore: 94.5
        });
      }, 500);
    });
  }

  async getCompletionTrends(): Promise<CompletionTrendDto> {
    // Mock data for now
    return new Promise((resolve) => {
      setTimeout(() => {
        const dailyData: ChartDataPoint[] = [];
        const weeklyData: ChartDataPoint[] = [];
        const monthlyData: ChartDataPoint[] = [];

        // Generate daily data for last 30 days
        for (let i = 29; i >= 0; i--) {
          const date = dayjs().subtract(i, 'day').format('YYYY-MM-DD');
          dailyData.push({
            date,
            value: Math.floor(Math.random() * 30) + 70 // 70-100%
          });
        }

        // Generate weekly data for last 12 weeks
        for (let i = 11; i >= 0; i--) {
          const date = dayjs().subtract(i, 'week').format('YYYY-MM-DD');
          weeklyData.push({
            date,
            value: Math.floor(Math.random() * 20) + 75 // 75-95%
          });
        }

        // Generate monthly data for last 12 months
        for (let i = 11; i >= 0; i--) {
          const date = dayjs().subtract(i, 'month').format('YYYY-MM');
          monthlyData.push({
            date,
            value: Math.floor(Math.random() * 15) + 80 // 80-95%
          });
        }

        resolve({
          daily: dailyData,
          weekly: weeklyData,
          monthly: monthlyData
        });
      }, 500);
    });
  }

  async getAnomalyTrends(): Promise<AnomalyTrendDto[]> {
    // Mock data for now
    return new Promise((resolve) => {
      setTimeout(() => {
        const data: AnomalyTrendDto[] = [];
        const types = ['Reading Error', 'Communication Failure', 'Data Quality', 'Hardware Issue'];
        
        for (let i = 29; i >= 0; i--) {
          const date = dayjs().subtract(i, 'day').format('YYYY-MM-DD');
          types.forEach(type => {
            data.push({
              date,
              count: Math.floor(Math.random() * 5),
              type
            });
          });
        }
        
        resolve(data);
      }, 500);
    });
  }

  async getUserPerformanceOverview(): Promise<UserPerformanceOverviewDto> {
    // Mock data for now
    return new Promise((resolve) => {
      setTimeout(() => {
        const topPerformers: UserPerformanceDto[] = [
          {
            userId: 1,
            userName: 'john.smith',
            fullName: 'John Smith',
            department: 'Field Operations',
            tasksAssigned: 245,
            tasksCompleted: 238,
            tasksOverdue: 2,
            completionRate: 97.1,
            averageCompletionTime: 24.5,
            qualityScore: 95.8,
            efficiency: 98.2,
            performanceRating: 'Excellent',
            lastActivity: dayjs().subtract(2, 'hours').toISOString()
          },
          {
            userId: 2,
            userName: 'sarah.jones',
            fullName: 'Sarah Jones',
            department: 'Field Operations',
            tasksAssigned: 198,
            tasksCompleted: 192,
            tasksOverdue: 1,
            completionRate: 97.0,
            averageCompletionTime: 23.8,
            qualityScore: 94.2,
            efficiency: 96.8,
            performanceRating: 'Excellent',
            lastActivity: dayjs().subtract(1, 'hour').toISOString()
          },
          {
            userId: 3,
            userName: 'mike.wilson',
            fullName: 'Mike Wilson',
            department: 'Field Operations',
            tasksAssigned: 167,
            tasksCompleted: 159,
            tasksOverdue: 3,
            completionRate: 95.2,
            averageCompletionTime: 26.1,
            qualityScore: 92.1,
            efficiency: 94.3,
            performanceRating: 'Good',
            lastActivity: dayjs().subtract(30, 'minutes').toISOString()
          }
        ];

        resolve({
          topPerformers,
          averagePerformance: 89.4,
          totalUsers: 45
        });
      }, 500);
    });
  }

  // ======================== Completion Reports ========================

  async getCompletionReports(searchParams: CompletionReportSearchDto): Promise<CompletionReportSearchResultDto> {
    // Mock data for now
    return new Promise((resolve) => {
      setTimeout(() => {
        const reports: CompletionReportDto[] = [];
        
        for (let i = 0; i < 20; i++) {
          reports.push({
            id: i + 1,
            reportDate: dayjs().subtract(i, 'day').format('YYYY-MM-DD'),
            totalTasks: Math.floor(Math.random() * 200) + 300,
            completedTasks: Math.floor(Math.random() * 150) + 250,
            pendingTasks: Math.floor(Math.random() * 30) + 20,
            overdueTasks: Math.floor(Math.random() * 10) + 2,
            completionRate: Math.floor(Math.random() * 20) + 75,
            averageCompletionTime: Math.floor(Math.random() * 10) + 20,
            createdAt: dayjs().subtract(i, 'day').toISOString(),
            createdBy: 'System'
          });
        }

        const start = (searchParams.page - 1) * searchParams.pageSize;
        const end = start + searchParams.pageSize;

        resolve({
          reports: reports.slice(start, end),
          totalCount: reports.length
        });
      }, 500);
    });
  }

  // ======================== Exception Analysis ========================

  async getExceptionAnalysis(searchParams: ExceptionSearchParams): Promise<ExceptionAnalysisDto> {
    // Mock data for now
    return new Promise((resolve) => {
      setTimeout(() => {
        const exceptions: ExceptionItemDto[] = [];
        const types = ['Reading Error', 'Communication Failure', 'Data Quality', 'Hardware Issue', 'Battery Low'];
        const severities: ('critical' | 'high' | 'medium' | 'low')[] = ['low', 'medium', 'high', 'critical'];
        const statuses: ('pending' | 'in_progress' | 'resolved' | 'dismissed')[] = ['pending', 'in_progress', 'resolved', 'dismissed'];
        
        for (let i = 0; i < 50; i++) {
          exceptions.push({
            id: `EXC-${(i + 1).toString().padStart(3, '0')}`,
            type: types[Math.floor(Math.random() * types.length)],
            description: `Exception description for issue #${i + 1}`,
            severity: severities[Math.floor(Math.random() * severities.length)],
            status: statuses[Math.floor(Math.random() * statuses.length)],
            waterMeterId: `WM-${(Math.floor(Math.random() * 1000) + 1).toString().padStart(4, '0')}`,
            location: `Building ${Math.floor(Math.random() * 20) + 1}`,
            detectedAt: dayjs().subtract(Math.floor(Math.random() * 30), 'day').toISOString(),
            resolvedAt: Math.random() > 0.5 ? dayjs().subtract(Math.floor(Math.random() * 5), 'day').toISOString() : undefined
          });
        }

        const start = (searchParams.pageIndex - 1) * searchParams.pageSize;
        const end = start + searchParams.pageSize;

        // Calculate statistics
        const totalExceptions = exceptions.length;
        const criticalCount = exceptions.filter(e => e.severity === 'critical').length;
        const resolvedCount = exceptions.filter(e => e.status === 'resolved').length;
        const resolutionRate = (resolvedCount / totalExceptions) * 100;

        // Severity distribution
        const severityDistribution = severities.map(severity => ({
          severity,
          count: exceptions.filter(e => e.severity === severity).length
        }));

        // Type distribution
        const typeDistribution = types.map(type => ({
          type,
          count: exceptions.filter(e => e.type === type).length
        }));

        resolve({
          id: 1,
          exceptionType: 'Analysis',
          description: 'Aggregated exception analysis',
          severity: 'medium',
          frequency: totalExceptions,
          firstOccurrence: dayjs().subtract(30, 'day').toISOString(),
          lastOccurrence: dayjs().toISOString(),
          affectedMeters: Math.floor(Math.random() * 500) + 100,
          status: 'active',
          totalExceptions,
          criticalCount,
          resolvedCount,
          avgResolutionTime: 18.5,
          resolutionRate,
          severityDistribution,
          typeDistribution,
          exceptions: exceptions.slice(start, end),
          total: totalExceptions
        });
      }, 500);
    });
  }

  async getExceptionTrend(request: ExceptionTrendRequestDto): Promise<ExceptionTrendDto[]> {
    // Mock data for now
    return new Promise((resolve) => {
      setTimeout(() => {
        const trends: ExceptionTrendDto[] = [];
        const severities = ['critical', 'high', 'medium', 'low'];
        const startDate = dayjs(request.dateFrom);
        const endDate = dayjs(request.dateTo);
        
        let current = startDate;
        while (current.isBefore(endDate) || current.isSame(endDate)) {
          severities.forEach(severity => {
            trends.push({
              date: current.format('YYYY-MM-DD'),
              count: Math.floor(Math.random() * 20) + 1,
              severity
            });
          });
          current = current.add(1, 'day');
        }

        resolve(trends);
      }, 300);
    });
  }

  async getExceptionSummary(): Promise<ExceptionSummaryDto> {
    // Mock data for now
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          byType: [
            { type: 'Reading Error', count: 45, percentage: 35.2, trend: 'down' },
            { type: 'Communication Failure', count: 32, percentage: 25.0, trend: 'up' },
            { type: 'Data Quality', count: 28, percentage: 21.9, trend: 'stable' },
            { type: 'Hardware Issue', count: 15, percentage: 11.7, trend: 'down' },
            { type: 'Battery Low', count: 8, percentage: 6.2, trend: 'up' }
          ],
          bySeverity: [
            { severity: 'Low', count: 65, percentage: 50.8, averageResolutionTime: 24.5 },
            { severity: 'Medium', count: 38, percentage: 29.7, averageResolutionTime: 12.3 },
            { severity: 'High', count: 20, percentage: 15.6, averageResolutionTime: 6.8 },
            { severity: 'Critical', count: 5, percentage: 3.9, averageResolutionTime: 2.1 }
          ],
          byStatus: [
            { status: 'Active', count: 42, percentage: 32.8 },
            { status: 'Investigating', count: 28, percentage: 21.9 },
            { status: 'Resolved', count: 38, percentage: 29.7 },
            { status: 'Closed', count: 20, percentage: 15.6 }
          ],
          monthlyTrend: []
        });
      }, 500);
    });
  }

  // ======================== User Performance ========================

  async getUserPerformance(searchParams: UserPerformanceSearchDto): Promise<UserPerformanceSearchResultDto> {
    // Mock data for now  
    return new Promise((resolve) => {
      setTimeout(() => {
        const users: UserPerformanceDto[] = [];
        const departments = ['Field Operations', 'Data Management', 'Customer Service', 'Maintenance'];
        const ratings = ['Excellent', 'Good', 'Average', 'Needs Improvement'];
        
        for (let i = 0; i < 45; i++) {
          users.push({
            userId: i + 1,
            userName: `user${i + 1}`,
            fullName: `User ${i + 1}`,
            department: departments[Math.floor(Math.random() * departments.length)],
            tasksAssigned: Math.floor(Math.random() * 200) + 50,
            tasksCompleted: Math.floor(Math.random() * 180) + 40,
            tasksOverdue: Math.floor(Math.random() * 10),
            completionRate: Math.floor(Math.random() * 30) + 70,
            averageCompletionTime: Math.floor(Math.random() * 20) + 15,
            qualityScore: Math.floor(Math.random() * 30) + 70,
            efficiency: Math.floor(Math.random() * 25) + 75,
            performanceRating: ratings[Math.floor(Math.random() * ratings.length)],
            lastActivity: dayjs().subtract(Math.floor(Math.random() * 24), 'hour').toISOString()
          });
        }

        const start = (searchParams.page - 1) * searchParams.pageSize;
        const end = start + searchParams.pageSize;

        resolve({
          users: users.slice(start, end),
          totalCount: users.length
        });
      }, 500);
    });
  }

  // ======================== Export Center ========================

  async createExportJob(request: ExportRequestDto): Promise<ExportJobDto> {
    // Mock data for now
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          id: Math.floor(Math.random() * 1000) + 1,
          exportType: request.exportType,
          format: request.format,
          fileName: `${request.exportType}_${dayjs().format('YYYYMMDD_HHmmss')}.${request.format}`,
          status: 'pending',
          progress: 0,
          createdAt: dayjs().toISOString(),
          createdBy: 'Current User',
          expiresAt: dayjs().add(7, 'day').toISOString()
        });
      }, 300);
    });
  }

  async getExportHistory(searchParams: ExportHistorySearchDto): Promise<ExportHistorySearchResultDto> {
    // Mock data for now
    return new Promise((resolve) => {
      setTimeout(() => {
        const exports: ExportJobDto[] = [];
        const types = ['Completion Report', 'Exception Analysis', 'User Performance', 'Operational Dashboard'];
        const formats = ['pdf', 'excel', 'csv'];
        const statuses: ('completed' | 'pending' | 'processing' | 'failed')[] = ['completed', 'pending', 'processing', 'failed'];
        
        for (let i = 0; i < 30; i++) {
          const status = statuses[Math.floor(Math.random() * statuses.length)];
          exports.push({
            id: i + 1,
            exportType: types[Math.floor(Math.random() * types.length)],
            format: formats[Math.floor(Math.random() * formats.length)],
            fileName: `export_${i + 1}.${formats[Math.floor(Math.random() * formats.length)]}`,
            status,
            progress: status === 'completed' ? 100 : Math.floor(Math.random() * 100),
            fileSize: status === 'completed' ? Math.floor(Math.random() * 10000) + 1000 : undefined,
            downloadUrl: status === 'completed' ? `/downloads/export_${i + 1}` : undefined,
            createdAt: dayjs().subtract(i, 'day').toISOString(),
            createdBy: 'Current User',
            completedAt: status === 'completed' ? dayjs().subtract(i, 'day').add(2, 'hour').toISOString() : undefined,
            error: status === 'failed' ? 'Export failed due to system error' : undefined,
            expiresAt: dayjs().add(7, 'day').toISOString()
          });
        }

        const start = (searchParams.page - 1) * searchParams.pageSize;
        const end = start + searchParams.pageSize;

        resolve({
          exports: exports.slice(start, end),
          totalCount: exports.length
        });
      }, 500);
    });
  }

  // ======================== Template Management ========================

  async getReportTemplates(searchParams: TemplateSearchDto): Promise<TemplateSearchResultDto> {
    // Mock data for now
    return new Promise((resolve) => {
      setTimeout(() => {
        const templates: ReportTemplateDto[] = [
          {
            id: 1,
            name: 'Daily Completion Report',
            type: 'Completion',
            description: 'Daily task completion summary report',
            templateContent: '{}',
            parameters: [
              {
                name: 'date',
                type: 'date',
                label: 'Report Date',
                required: true,
                defaultValue: dayjs().format('YYYY-MM-DD')
              }
            ],
            isActive: true,
            isDefault: true,
            category: 'Operations',
            createdAt: dayjs().subtract(30, 'day').toISOString(),
            createdBy: 'Admin',
            updatedAt: dayjs().subtract(5, 'day').toISOString(),
            updatedBy: 'Admin',
            usageCount: 150
          },
          {
            id: 2,
            name: 'Exception Analysis Report',
            type: 'Exception',
            description: 'Detailed exception analysis and trends',
            templateContent: '{}',
            parameters: [
              {
                name: 'dateRange',
                type: 'date',
                label: 'Date Range',
                required: true
              },
              {
                name: 'severity',
                type: 'select',
                label: 'Severity Filter',
                required: false,
                options: ['All', 'Low', 'Medium', 'High', 'Critical']
              }
            ],
            isActive: true,
            isDefault: false,
            category: 'Analysis',
            createdAt: dayjs().subtract(25, 'day').toISOString(),
            createdBy: 'Admin',
            updatedAt: dayjs().subtract(10, 'day').toISOString(),
            updatedBy: 'Manager',
            usageCount: 87
          }
        ];

        const start = (searchParams.page - 1) * searchParams.pageSize;
        const end = start + searchParams.pageSize;

        resolve({
          templates: templates.slice(start, end),
          totalCount: templates.length
        });
      }, 500);
    });
  }

  async createReportTemplate(template: CreateReportTemplateDto): Promise<ReportTemplateDto> {
    // Mock data for now
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          id: Math.floor(Math.random() * 1000) + 100,
          ...template,
          isActive: true,
          isDefault: template.isDefault || false,
          createdAt: dayjs().toISOString(),
          createdBy: 'Current User',
          updatedAt: dayjs().toISOString(),
          updatedBy: 'Current User',
          usageCount: 0
        });
      }, 300);
    });
  }

  async updateReportTemplate(id: number, template: UpdateReportTemplateDto): Promise<ReportTemplateDto> {
    // Mock data for now
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          id,
          ...template,
          isDefault: template.isDefault || false,
          createdAt: dayjs().subtract(30, 'day').toISOString(),
          createdBy: 'Admin',
          updatedAt: dayjs().toISOString(),
          updatedBy: 'Current User',
          usageCount: Math.floor(Math.random() * 100) + 10
        });
      }, 300);
    });
  }

  async deleteReportTemplate(id: number): Promise<void> {
    // Mock implementation
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve();
      }, 300);
    });
  }

  // ======================== Generate Reports ========================

  async generateReport(request: GenerateReportRequestDto): Promise<GeneratedReportDto> {
    // Mock data for now
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          id: Math.floor(Math.random() * 1000) + 1,
          reportType: request.reportType,
          title: request.title || `${request.reportType} Report`,
          format: request.format,
          fileName: `${request.reportType}_${dayjs().format('YYYYMMDD_HHmmss')}.${request.format}`,
          fileSize: Math.floor(Math.random() * 5000) + 1000,
          downloadUrl: `/downloads/report_${dayjs().format('YYYYMMDD_HHmmss')}`,
          generatedAt: dayjs().toISOString(),
          generatedBy: 'Current User',
          expiresAt: dayjs().add(30, 'day').toISOString(),
          parameters: request.parameters
        });
      }, 2000); // Simulate longer generation time
    });
  }

  // Helper method to download a file
  async downloadFile(url: string, fileName: string): Promise<void> {
    // Mock implementation - in real app would download actual file
    return new Promise((resolve) => {
      setTimeout(() => {
        // Create a mock blob and trigger download
        const element = document.createElement('a');
        element.href = url;
        element.download = fileName;
        document.body.appendChild(element);
        element.click();
        document.body.removeChild(element);
        resolve();
      }, 500);
    });
  }
}

export const reportsService = new ReportsService();
export default reportsService;
