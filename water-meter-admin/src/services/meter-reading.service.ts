import { api } from './api';

// ==================== Interfaces ====================

// Base search parameters
export interface BaseSearchDto {
  page?: number;
  pageSize?: number;
  sortBy?: string;
  sortDirection?: 'asc' | 'desc';
}

// Meter Reading DTOs
export interface MeterReadingSearchDto extends BaseSearchDto {
  meterNumber?: string;
  startDate?: string;
  endDate?: string;
  status?: string;
  validationStatus?: string;
  anomalyType?: string;
  readBy?: string;
  processedBy?: string;
  hasPhoto?: boolean;
  hasOCR?: boolean;
  zone?: string;
}

export interface MeterReadingListDto {
  id: number;
  meterId: number;
  userId: number;
  taskId: number;
  meterNumber: string;
  readingValue: number;
  previousReading?: number;
  consumption?: number;
  readingDate: string;
  readBy?: string;
  status: string;
  validationStatus?: string;
  anomalyType?: string;
  notes?: string;
  location?: string;
  hasPhoto: boolean;
  hasOCR: boolean;
  ocrStatus?: string;
  ocrConfidence?: number;
  readingMethod?: string;
  readingType?: string;
  dataSource?: string;
  isAnomalous: boolean;
  cantRead: boolean;
  isValidated: boolean;
  latitude?: number;
  longitude?: number;
  gpsAccuracy?: number;
  validatedBy?: number;
  validationDate?: string;
  validationComments?: string;
  anomalyReason?: string;
  cantReadReason?: string;
  createdBy: string;
  updatedBy: string;
  isDeleted: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface MeterReadingSearchResultDto {
  readings: MeterReadingListDto[];
  totalCount: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

export interface CreateMeterReadingDto {
  meterId: number;
  meterNumber: string;
  readingValue: number;
  previousReading?: number;
  readingDate: string;
  readBy?: string;
  notes?: string;
  location?: string;
  hasPhoto?: boolean;
  hasOCR?: boolean;
  ocrStatus?: string;
  ocrConfidence?: number;
  readingType?: string;
  dataSource?: string;
}

export interface UpdateMeterReadingDto {
  readingValue: number;
  status: string;
  validationStatus?: string;
  anomalyType?: string;
  notes?: string;
  location?: string;
}

// Photo Management DTOs
export interface ReadingPhotoDto {
  id: number;
  meterReadingId: number;
  originalFileName: string;
  cloudflareUrl: string;
  thumbnailUrl?: string;
  fileSize: number;
  mimeType: string;
  uploadTime: string;
  qualityScore?: number;
  qualityStatus?: string;
  isProcessed: boolean;
  hasOCR: boolean;
  ocrResult?: string;
  ocrConfidence?: number;
  ocrStatus?: string;
  isOverridden?: boolean;
  overriddenBy?: string;
  overriddenDate?: string;
  overrideReason?: string;
  createdAt: string;
}

export interface PhotoSearchDto extends BaseSearchDto {
  meterNumber?: string;
  startDate?: string;
  endDate?: string;
  qualityStatus?: string;
  ocrStatus?: string;
  isOverridden?: boolean;
}

// Anomaly Management DTOs
export interface ReadingAnomalyDto {
  id: number;
  meterReadingId: number;
  meterNumber: string;
  anomalyType: string;
  severity: string;
  description?: string;
  expectedValue?: number;
  actualValue?: number;
  variance?: number;
  confidenceScore?: number;
  status: string;
  assignedUserId?: number;
  assignedDate?: string;
  resolvedBy?: string;
  resolvedDate?: string;
  resolution?: string;
  resolutionType?: string;
  requiresFieldVisit: boolean;
  isRecurring: boolean;
  createdAt: string;
}

export interface AnomalySearchDto extends BaseSearchDto {
  meterNumber?: string;
  anomalyType?: string;
  severity?: string;
  status?: string;
  assignedTo?: string;
  startDate?: string;
  endDate?: string;
  requiresFieldVisit?: boolean;
  isRecurring?: boolean;
}

// Validation Rules DTOs
export interface ValidationRuleDto {
  id: number;
  ruleName: string;
  ruleType: string;
  description?: string;
  meterType?: string;
  zone?: string;
  customerType?: string;
  isActive: boolean;
  priority: number;
  minToleranceValue?: number;
  maxToleranceValue?: number;
  tolerancePercentage?: number;
  timeWindowDays?: number;
  seasonalAdjustment: boolean;
  summerMultiplier?: number;
  winterMultiplier?: number;
  warningThreshold?: number;
  errorThreshold?: number;
  criticalThreshold?: number;
  actionOnViolation?: string;
  autoCorrect: boolean;
  notificationRecipients?: string;
  ruleConfiguration?: string;
  effectivenessScore?: number;
  createdAt: string;
}

export interface CreateValidationRuleDto {
  ruleName: string;
  ruleType: string;
  description?: string;
  meterType?: string;
  zone?: string;
  customerType?: string;
  isActive?: boolean;
  priority?: number;
  minToleranceValue?: number;
  maxToleranceValue?: number;
  tolerancePercentage?: number;
  timeWindowDays?: number;
  seasonalAdjustment?: boolean;
  summerMultiplier?: number;
  winterMultiplier?: number;
  warningThreshold?: number;
  errorThreshold?: number;
  criticalThreshold?: number;
  actionOnViolation?: string;
  autoCorrect?: boolean;
  notificationRecipients?: string;
  ruleConfiguration?: string;
}

// Statistics DTOs
export interface MeterReadingStatisticsDto {
  totalReadings: number;
  pendingReadings: number;
  confirmedReadings: number;
  anomalyReadings: number;
  correctedReadings: number;
  readingsWithPhoto: number;
  readingsWithOCR: number;
  ocrSuccessRate: number;
  anomalyDetectionRate: number;
  dataQualityScore: number;
  statusCounts: Array<{ status: string; count: number; percentage: number }>;
  anomalyTypeCounts: Array<{ anomalyType: string; count: number; percentage: number }>;
}

export interface OCRRecordDto {
  id: number;
  meterReadingId: number;
  readingPhotoId?: number;
  processingEngine: string;
  processingVersion: string;
  recognizedValue: string;
  confidenceScore: number;
  status: string;
  errorMessage?: string;
  processingTimeMs: number;
  boundingBox?: string;
  qualityScore?: number;
  isManuallyVerified: boolean;
  verifiedBy?: string;
  verifiedDate?: string;
  correctedValue?: string;
  correctionReason?: string;
  isTrainingData: boolean;
  createdAt: string;
}

// ==================== Service Class ====================

class MeterReadingService {

  // Reading Management
  async getReadings(searchDto: MeterReadingSearchDto): Promise<MeterReadingSearchResultDto> {
    const response = await api.get('/meter-reading', { params: searchDto });
    return response.data;
  }

  async getReadingById(id: number): Promise<MeterReadingListDto> {
    const response = await api.get(`/meter-reading/${id}`);
    return response.data;
  }

  async createReading(createDto: CreateMeterReadingDto): Promise<MeterReadingListDto> {
    const response = await api.post('/meter-reading', createDto);
    return response.data;
  }

  async updateReading(id: number, updateDto: UpdateMeterReadingDto): Promise<MeterReadingListDto> {
    const response = await api.put(`/meter-reading/${id}`, updateDto);
    return response.data;
  }

  async deleteReading(id: number): Promise<void> {
    await api.delete(`/meter-reading/${id}`);
  }

  async confirmReading(id: number): Promise<void> {
    await api.post(`/meter-reading/${id}/confirm`);
  }

  async correctReading(id: number, correctedValue: number, reason: string): Promise<void> {
    await api.post(`/meter-reading/${id}/correct`, { correctedValue, reason });
  }

  // Photo Management
  async getReadingPhotos(readingId: number): Promise<ReadingPhotoDto[]> {
    const response = await api.get(`/meter-reading/${readingId}/photos`);
    return response.data;
  }

  async searchPhotos(searchDto: PhotoSearchDto): Promise<ReadingPhotoDto[]> {
    const response = await api.get('/meter-reading/photos/search', { params: searchDto });
    return response.data;
  }

  async overrideOCRResult(photoId: number, correctedValue: string, reason: string): Promise<void> {
    await api.post(`/meter-reading/photos/${photoId}/override-ocr`, { correctedValue, reason });
  }

  async deletePhoto(photoId: number): Promise<void> {
    await api.delete(`/meter-reading/photos/${photoId}`);
  }

  async updatePhotoQuality(photoId: number, qualityScore: number, qualityStatus: string): Promise<void> {
    await api.put(`/meter-reading/photos/${photoId}/quality`, { qualityScore, qualityStatus });
  }

  // Anomaly Management
  async getAnomalies(searchDto: AnomalySearchDto): Promise<ReadingAnomalyDto[]> {
    const response = await api.get('/meter-reading/anomalies', { params: searchDto });
    return response.data;
  }

  async getUnresolvedAnomalies(): Promise<ReadingAnomalyDto[]> {
    const response = await api.get('/meter-reading/anomalies/unresolved');
    return response.data;
  }

  async assignAnomaly(anomalyId: number, assignedUserId: number): Promise<void> {
    await api.post(`/meter-reading/anomalies/${anomalyId}/assign`, { assignedUserId });
  }

  async resolveAnomaly(anomalyId: number, resolution: string, resolutionType: string): Promise<void> {
    await api.post(`/meter-reading/anomalies/${anomalyId}/resolve`, { resolution, resolutionType });
  }

  // Validation Rules
  async getValidationRules(): Promise<ValidationRuleDto[]> {
    const response = await api.get('/meter-reading/validation-rules');
    return response.data;
  }

  async getValidationRuleById(id: number): Promise<ValidationRuleDto> {
    const response = await api.get(`/meter-reading/validation-rules/${id}`);
    return response.data;
  }

  async createValidationRule(createDto: CreateValidationRuleDto): Promise<ValidationRuleDto> {
    const response = await api.post('/meter-reading/validation-rules', createDto);
    return response.data;
  }

  async updateValidationRule(id: number, updateDto: CreateValidationRuleDto): Promise<ValidationRuleDto> {
    const response = await api.put(`/meter-reading/validation-rules/${id}`, updateDto);
    return response.data;
  }

  async deleteValidationRule(id: number): Promise<void> {
    await api.delete(`/meter-reading/validation-rules/${id}`);
  }

  // Statistics
  async getReadingStatistics(): Promise<MeterReadingStatisticsDto> {
    const response = await api.get('/meter-reading/statistics');
    return response.data;
  }

  async getOCRAccuracyRate(): Promise<{ accuracy: number }> {
    const response = await api.get('/meter-reading/ocr-accuracy');
    return response.data;
  }

  // Utility Methods
  async getMeterNumbers(): Promise<string[]> {
    const response = await api.get('/meter-reading/meter-numbers');
    return response.data;
  }

  async getReadingHistory(meterNumber: string, months?: number): Promise<MeterReadingListDto[]> {
    const params = months ? { months } : {};
    const response = await api.get(`/meter-reading/meters/${meterNumber}/history`, { params });
    return response.data;
  }

  // Export/Import
  async exportReadings(searchDto?: MeterReadingSearchDto): Promise<Blob> {
    const response = await api.get('/meter-reading/export', {
      params: searchDto,
      responseType: 'blob'
    });
    return response.data;
  }

  async importReadings(file: File): Promise<{ success: boolean; message: string; importedCount: number }> {
    const formData = new FormData();
    formData.append('file', file);
    
    const response = await api.post('/meter-reading/import', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
    return response.data;
  }
}

// Create and export singleton instance
export const meterReadingService = new MeterReadingService();
export default meterReadingService; 