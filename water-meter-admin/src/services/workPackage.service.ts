import { api } from './api';

// Work Package DTOs - renamed from Schedule to Work Package
export interface WorkPackageListDto {
  id: number;
  name: string;
  description?: string;
  packageType: string;
  status: string;
  plannedStartDate: string;
  plannedEndDate: string;
  actualStartDate?: string;
  actualEndDate?: string;
  frequency: string;
  serviceArea: string;
  subArea?: string;
  priority: string;
  totalMeters: number;
  completedMeters: number;
  progressPercentage: number;
  assignedTeam?: string;
  estimatedHours: number;
  actualHours?: number;
  estimatedCost?: number;
  actualCost?: number;
  isTemplate: boolean;
  templateCategory?: string;
  isRecurring: boolean;
  recurrencePattern?: string;
  recurrenceInterval?: number;
  lastExecuted?: string;
  nextExecution?: string;
  notes?: string;
  instructions?: string;
  source: string;
  createdBy: string;
  updatedBy?: string;
  createdAt: string;
  updatedAt: string;
  
  // Computed properties
  isStarted: boolean;
  isCompleted: boolean;
  isOverdue: boolean;
  remainingDays?: number;
  completionRate: number;
}

// Work Package详情DTO（包含完整信息和关联数据）
export interface WorkPackageDto {
  id: number;
  name: string;
  description?: string;
  packageType: string;
  status: string;
  plannedStartDate: string;
  plannedEndDate: string;
  actualStartDate?: string;
  actualEndDate?: string;
  frequency: string;
  serviceArea: string;
  subArea?: string;
  priority: string;
  totalMeters: number;
  completedMeters: number;
  progressPercentage: number;
  assignedTeam?: string;
  estimatedHours: number;
  actualHours?: number;
  estimatedCost?: number;
  actualCost?: number;
  isTemplate: boolean;
  templateCategory?: string;
  isRecurring: boolean;
  recurrencePattern?: string;
  recurrenceInterval?: number;
  lastExecuted?: string;
  nextExecution?: string;
  notes?: string;
  instructions?: string;
  amsImportBatch?: string;
  amsImportDate?: string;
  source: string;
  createdBy: string;
  updatedBy?: string;
  createdAt: string;
  updatedAt?: string;
  
  // 计算属性
  isStarted: boolean;
  isCompleted: boolean;
  isOverdue: boolean;
  remainingDays?: number;
  completionRate: number;
  
  // 关联数据
  items: WorkPackageItemDto[];
  assignments: WorkPackageAssignmentDto[];
}

// Work Package任务项DTO
export interface WorkPackageItemDto {
  id: number;
  workPackageId: number;
  meterId: number;
  sequenceOrder: number;
  status: string;
  scheduledDate?: string;
  actualDate?: string;
  assignedTo?: string;
  priority?: string;
  estimatedMinutes?: number;
  actualMinutes?: number;
  specialInstructions?: string;
  notes?: string;
  lastReading?: string;
  requiresSpecialHandling: boolean;
  specialHandlingReason?: string;
  difficultyRating?: number;
  latitude?: string;
  longitude?: string;
  serviceAddress?: string;
  propertyDetails?: string;
  accessNotes?: string;
  hasPhoto: boolean;
  photoUrl?: string;
  qrCode?: string;
  meterSerialNumber?: string;
  meterType?: string;
  meterSize?: string;
  createdAt: string;
  updatedAt?: string;
  
  // 计算属性
  isCompleted: boolean;
  canRetry: boolean;
  isOverdue: boolean;
  efficiencyScore?: number;
  
  // 水表详情
  meterLocation?: string;
  meterDescription?: string;
}

// Work Package分配DTO
export interface WorkPackageAssignmentDto {
  id: number;
  workPackageId: number;
  userId: number;
  assignmentType: string;
  status: string;
  assignedDate: string;
  assignedBy: string;
  acceptedDate?: string;
  rejectedDate?: string;
  startedDate?: string;
  completedDate?: string;
  reason?: string;
  rejectionReason?: string;
  expectedStartDate?: string;
  expectedCompletionDate?: string;
  assignedMeterCount?: number;
  completedMeterCount: number;
  workloadWeight: number;
  priority?: string;
  requiresSupervision: boolean;
  supervisorId?: number;
  skillRequirements?: string;
  equipmentRequirements?: string;
  specialInstructions?: string;
  emailNotificationSent: boolean;
  emailSentDate?: string;
  pushNotificationSent: boolean;
  pushNotificationSentDate?: string;
  lastActivityDate?: string;
  rating?: number;
  feedback?: string;
  ratedBy?: string;
  ratedDate?: string;
  
  // 计算属性
  isAccepted: boolean;
  isCompleted: boolean;
  isRejected: boolean;
  isInProgress: boolean;
  completionRate: number;
  isOverdue: boolean;
  
  // 关联用户信息
  userName?: string;
  userEmail?: string;
  supervisorName?: string;
}

export interface CreateWorkPackageDto {
  name: string;
  description?: string;
  packageType: string;
  plannedStartDate: string;
  plannedEndDate: string;
  frequency: string;
  serviceArea: string;
  subArea?: string;
  priority: string;
  assignedTeam?: string;
  estimatedHours: number;
  estimatedCost?: number;
  isTemplate: boolean;
  templateCategory?: string;
  isRecurring: boolean;
  recurrencePattern?: string;
  recurrenceInterval?: number;
  notes?: string;
  instructions?: string;
  createdBy: string;
  meterIds: number[]; // 添加水表ID数组字段
}

export interface UpdateWorkPackageDto {
  id: number;
  name: string;
  description?: string;
  packageType: string;
  status: string;
  plannedStartDate: string;
  plannedEndDate: string;
  actualStartDate?: string;
  actualEndDate?: string;
  frequency: string;
  serviceArea: string;
  subArea?: string;
  priority: string;
  assignedTeam?: string;
  estimatedHours: number;
  actualHours?: number;
  estimatedCost?: number;
  actualCost?: number;
  isTemplate: boolean;
  templateCategory?: string;
  isRecurring: boolean;
  recurrencePattern?: string;
  recurrenceInterval?: number;
  nextExecution?: string;
  notes?: string;
  instructions?: string;
  
  // Audit fields are managed automatically by BaseEntity + DbContext
  // No need to include any audit fields in frontend DTOs
}

export interface WorkPackageSearchDto {
  page?: number;
  pageSize?: number;
  name?: string;
  packageType?: string;
  status?: string;
  priority?: string;
  serviceArea?: string;
  subArea?: string;
  createdBy?: string;
  assignedTo?: string;
  plannedStartDateFrom?: string;
  plannedStartDateTo?: string;
  plannedEndDateFrom?: string;
  plannedEndDateTo?: string;
  isTemplate?: boolean;
  isRecurring?: boolean;
  isOverdue?: boolean;
  source?: string;
  minProgress?: number;
  maxProgress?: number;
}

export interface WorkPackageSearchResultDto {
  workPackages: WorkPackageListDto[];
  totalCount: number;
}

export interface WorkPackageStatistics {
  totalWorkPackages: number;
  activeWorkPackages: number;
  completedWorkPackages: number;
  pendingWorkPackages: number;
  totalMeters: number;
  completedMeters: number;
  overdueWorkPackages: number;
  avgCompletionTime: number;
}

export interface ExportFilterData {
  serviceAreas: string[];
  subAreas: string[];
  meterTypes: string[];
  dateRange: [string, string] | null;
  includeCompleted: boolean;
}

export interface ImportResult {
  totalRows: number;
  successCount: number;
  failureCount: number;
  errors: string[];
}

export interface TaskGenerationResultDto {
  success: boolean;
  message: string;
  generatedTaskCount: number;
  taskIds: number[];
  warnings: string[];
}

export interface WorkPackageActivationResultDto {
  success: boolean;
  message: string;
  tasksGenerated: boolean;
  generatedTaskCount: number;
  validationResults: string[];
  warnings: string[];
  activatedAt: string;
}

export class WorkPackageService {
  private readonly baseUrl = '/WorkPackage';

  async getWorkPackages(params: WorkPackageSearchDto = {}): Promise<WorkPackageSearchResultDto> {
    try {
      const response = await api.get(this.baseUrl, { params });
      return {
        workPackages: response.data.items || [],
        totalCount: response.data.totalCount || 0
      };
    } catch (error) {
      console.error('Error fetching work packages:', error);
      throw error;
    }
  }

  async getWorkPackageById(id: number): Promise<WorkPackageDto> {
    try {
      const response = await api.get(`${this.baseUrl}/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching work package ${id}:`, error);
      throw error;
    }
  }

  async createWorkPackage(data: CreateWorkPackageDto): Promise<WorkPackageListDto> {
    try {
      const response = await api.post(this.baseUrl, data);
      return response.data;
    } catch (error) {
      console.error('Error creating work package:', error);
      throw error;
    }
  }

  async updateWorkPackage(id: number, data: UpdateWorkPackageDto): Promise<WorkPackageListDto> {
    try {
      const response = await api.put(`${this.baseUrl}/${id}`, data);
      return response.data;
    } catch (error) {
      console.error(`Error updating work package ${id}:`, error);
      throw error;
    }
  }

  async deleteWorkPackage(id: number): Promise<void> {
    try {
      await api.delete(`${this.baseUrl}/${id}`);
    } catch (error) {
      console.error(`Error deleting work package ${id}:`, error);
      throw error;
    }
  }

  async generateTasks(workPackageId: number): Promise<TaskGenerationResultDto> {
    try {
      const response = await api.post(`${this.baseUrl}/${workPackageId}/generate-tasks`);
      return response.data;
    } catch (error: any) {
      console.error('Generate tasks error:', error);
      throw new Error(error.response?.data?.message || 'Failed to generate tasks');
    }
  }

  async activateWorkPackage(workPackageId: number, generateTasks: boolean = true): Promise<WorkPackageActivationResultDto> {
    try {
      const response = await api.post(`${this.baseUrl}/${workPackageId}/activate?generateTasks=${generateTasks}`);
      return response.data;
    } catch (error: any) {
      console.error('Activate work package error:', error);
      throw new Error(error.response?.data?.message || 'Failed to activate work package');
    }
  }

  async exportWorkPackages(params?: WorkPackageSearchDto): Promise<Blob> {
    try {
      const response = await api.get(`${this.baseUrl}/export/csv`, { 
        params,
        responseType: 'blob'
      });
      return response.data;
    } catch (error) {
      console.error('Error exporting work packages:', error);
      throw error;
    }
  }

  async importWorkPackages(file: File): Promise<ImportResult> {
    try {
      const formData = new FormData();
      formData.append('file', file);
      
      const response = await api.post(`${this.baseUrl}/import/excel`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      return response.data;
    } catch (error) {
      console.error('Error importing work packages:', error);
      throw error;
    }
  }

  async getWorkPackageStatistics(): Promise<WorkPackageStatistics> {
    try {
      const response = await api.get(`${this.baseUrl}/statistics`);
      return response.data;
    } catch (error) {
      console.error('Error fetching work package statistics:', error);
      throw error;
    }
  }

  async downloadTemplate(): Promise<Blob> {
    try {
      const response = await api.get(`${this.baseUrl}/template/download`, {
        responseType: 'blob'
      });
      return response.data;
    } catch (error) {
      console.error('Error downloading template:', error);
      throw error;
    }
  }

  async exportFilteredTemplate(filterData: ExportFilterData): Promise<Blob> {
    try {
      const response = await api.post(`${this.baseUrl}/template/filtered-export`, filterData, {
        responseType: 'blob'
      });
      return response.data;
    } catch (error) {
      console.error('Error exporting filtered template:', error);
      throw error;
    }
  }

  async batchOperation(ids: number[], operation: string, reason?: string): Promise<{ success: boolean; message: string }> {
    try {
      const payload = operation === 'cancel' && reason 
        ? { ids: ids, reason: reason }
        : { ids: ids };
        
      const response = await api.post(`${this.baseUrl}/batch/${operation}`, payload);
      return {
        success: true,
        message: response.data.message || `Batch ${operation} completed successfully`
      };
    } catch (error) {
      console.error(`Error performing batch ${operation}:`, error);
      throw error;
    }
  }
}

export const workPackageService = new WorkPackageService(); 