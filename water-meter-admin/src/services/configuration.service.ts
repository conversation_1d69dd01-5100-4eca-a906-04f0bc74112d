import api from './api'
import type {
  ConfigurationCategory,
  GetConfigurationRequest,
  UpdateConfigurationRequest,
  ConfigurationUpdateResponse,
  ConfigurationService as IConfigurationService
} from '../types/configuration'

class ConfigurationService implements IConfigurationService {
  private baseUrl = '/configuration'

  /**
   * Get configurations by scope and category
   */
  async getConfigurations(request: GetConfigurationRequest): Promise<ConfigurationCategory[]> {
    try {
      const params = new URLSearchParams()
      params.append('scope', request.scope)
      if (request.category) params.append('category', request.category)
      if (request.userId !== undefined) params.append('userId', request.userId.toString())
      if (request.roleId !== undefined) params.append('roleId', request.roleId.toString())
      if (request.includeInherited !== undefined) params.append('includeInherited', request.includeInherited.toString())

      const response = await api.get(`${this.baseUrl}?${params.toString()}`)
      return response.data
    } catch (error) {
      console.error('Error fetching configurations:', error)
      throw error
    }
  }

  /**
   * Update multiple configurations in bulk
   */
  async updateConfigurations(request: UpdateConfigurationRequest): Promise<ConfigurationUpdateResponse> {
    try {
      const response = await api.post(`${this.baseUrl}/update`, request)
      return response.data
    } catch (error) {
      console.error('Error updating configurations:', error)
      throw error
    }
  }

  /**
   * Get a specific configuration value
   */
  async getConfigurationValue<T>(
    scope: string, 
    category: string, 
    key: string, 
    userId?: number, 
    roleId?: number
  ): Promise<T> {
    try {
      const params = new URLSearchParams()
      if (userId !== undefined) params.append('userId', userId.toString())
      if (roleId !== undefined) params.append('roleId', roleId.toString())

      const url = `${this.baseUrl}/${scope}/${category}/${key}?${params.toString()}`
      const response = await api.get(url)
      return response.data.value
    } catch (error) {
      console.error(`Error getting configuration value ${scope}.${category}.${key}:`, error)
      throw error
    }
  }

  /**
   * Set a specific configuration value
   */
  async setConfigurationValue<T>(
    scope: string, 
    category: string, 
    key: string, 
    value: T, 
    userId?: number, 
    roleId?: number
  ): Promise<boolean> {
    try {
      const params = new URLSearchParams()
      if (userId !== undefined) params.append('userId', userId.toString())
      if (roleId !== undefined) params.append('roleId', roleId.toString())

      const url = `${this.baseUrl}/${scope}/${category}/${key}?${params.toString()}`
      const response = await api.post(url, value)
      return response.data.success !== false
    } catch (error) {
      console.error(`Error setting configuration value ${scope}.${category}.${key}:`, error)
      return false
    }
  }

  /**
   * Reset configurations to defaults
   */
  async resetToDefaults(
    scope: string, 
    category: string, 
    userId?: number, 
    roleId?: number
  ): Promise<ConfigurationUpdateResponse> {
    try {
      const request = {
        scope,
        category,
        userId,
        roleId
      }
      const response = await api.post(`${this.baseUrl}/reset`, request)
      return response.data
    } catch (error) {
      console.error('Error resetting configurations to defaults:', error)
      throw error
    }
  }

  /**
   * Initialize default configuration templates
   */
  async initializeDefaults(): Promise<boolean> {
    try {
      const response = await api.post(`${this.baseUrl}/initialize`)
      return response.data.success !== false
    } catch (error) {
      console.error('Error initializing default configurations:', error)
      return false
    }
  }

  /**
   * Clear all configurations and reinitialize (for debugging/reset purposes)
   */
  async clearAndReinitialize(): Promise<boolean> {
    try {
      const response = await api.post(`${this.baseUrl}/clear-and-reinitialize`)
      return response.data.success !== false
    } catch (error) {
      console.error('Error clearing and reinitializing configurations:', error)
      return false
    }
  }

  /**
   * Force reinitialize all configuration templates
   */
  async forceReinitialize(): Promise<boolean> {
    try {
      const response = await api.post(`${this.baseUrl}/force-reinitialize`)
      return response.data.success !== false
    } catch (error) {
      console.error('Error force reinitializing configurations:', error)
      return false
    }
  }

  /**
   * Get configuration templates for a scope
   */
  async getConfigurationTemplates(scope: string): Promise<ConfigurationCategory[]> {
    try {
      const response = await api.get(`${this.baseUrl}/templates/${scope}`)
      return response.data
    } catch (error) {
      console.error(`Error getting configuration templates for scope ${scope}:`, error)
      throw error
    }
  }

  /**
   * Validate a configuration value
   */
  async validateConfiguration(templateKey: string, value: any): Promise<{ isValid: boolean; errorMessage: string }> {
    try {
      const request = { templateKey, value }
      const response = await api.post(`${this.baseUrl}/validate`, request)
      return {
        isValid: response.data.isValid,
        errorMessage: response.data.errorMessage || ''
      }
    } catch (error) {
      console.error('Error validating configuration:', error)
      return {
        isValid: false,
        errorMessage: 'Validation failed'
      }
    }
  }

  /**
   * Helper method to get system settings
   */
  async getSystemSettings(): Promise<ConfigurationCategory[]> {
    return this.getConfigurations({
      scope: 'System',
      includeInherited: false
    })
  }

  /**
   * Helper method to get user settings
   */
  async getUserSettings(userId: number): Promise<ConfigurationCategory[]> {
    return this.getConfigurations({
      scope: 'User',
      userId,
      includeInherited: true
    })
  }

  /**
   * Helper method to update system settings
   */
  async updateSystemSettings(category: string, settings: Record<string, any>): Promise<ConfigurationUpdateResponse> {
    const configurations = Object.entries(settings).map(([key, value]) => ({
      key,
      value
    }))

    return this.updateConfigurations({
      scope: 'System',
      category,
      configurations
    })
  }

  /**
   * Helper method to update user settings
   */
  async updateUserSettings(category: string, settings: Record<string, any>, userId: number): Promise<ConfigurationUpdateResponse> {
    const configurations = Object.entries(settings).map(([key, value]) => ({
      key,
      value
    }))

    return this.updateConfigurations({
      scope: 'User',
      category,
      configurations
    })
  }
}

// Export singleton instance
export const configurationService = new ConfigurationService()
export default configurationService 