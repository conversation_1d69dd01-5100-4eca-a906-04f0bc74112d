import { api } from './api';

export interface FrequencyTemplateListDto {
  id: number;
  name: string;
  description?: string;
  frequencyType: string;
  intervalValue: number;
  intervalUnit: string;
  isActive: boolean;
  estimatedDuration?: number;
  category: string;
  applicableAreas?: string;
  createdAt: string;
  createdBy: string;
  usageCount: number;
}

export interface CreateFrequencyTemplateDto {
  name: string;
  description?: string;
  frequencyType: string;
  intervalValue: number;
  intervalUnit: string;
  isActive: boolean;
  estimatedDuration?: number;
  category: string;
  applicableAreas?: string;
  notes?: string;
}

export interface UpdateFrequencyTemplateDto extends CreateFrequencyTemplateDto {
  id: number;
}

export interface FrequencyTemplateSearchDto {
  page: number;
  pageSize: number;
  sortBy?: string;
  sortDirection?: string;
  name?: string;
  frequencyType?: string;
  category?: string;
  isActive?: boolean;
  intervalUnit?: string;
}

export interface FrequencyTemplateSearchResultDto {
  templates: FrequencyTemplateListDto[];
  totalCount: number;
  pageNumber: number;
  pageSize: number;
  totalPages: number;
}

export interface FrequencyCalculationDto {
  scheduledDates: string[];
  totalOccurrences: number;
  firstOccurrence?: string;
  lastOccurrence?: string;
  frequencyDescription: string;
}

export class FrequencyTemplateService {
  private readonly baseUrl = '/frequency-templates';

  async getTemplates(params: FrequencyTemplateSearchDto): Promise<FrequencyTemplateSearchResultDto> {
    try {
      const response = await api.get<FrequencyTemplateSearchResultDto>(this.baseUrl, { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching frequency templates:', error);
      throw error;
    }
  }

  async getTemplateById(id: number): Promise<FrequencyTemplateListDto> {
    try {
      const response = await api.get<FrequencyTemplateListDto>(`${this.baseUrl}/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching frequency template ${id}:`, error);
      throw error;
    }
  }

  async createTemplate(data: CreateFrequencyTemplateDto): Promise<FrequencyTemplateListDto> {
    try {
      const response = await api.post<FrequencyTemplateListDto>(this.baseUrl, data);
      return response.data;
    } catch (error) {
      console.error('Error creating frequency template:', error);
      throw error;
    }
  }

  async updateTemplate(id: number, data: UpdateFrequencyTemplateDto): Promise<FrequencyTemplateListDto> {
    try {
      const response = await api.put<FrequencyTemplateListDto>(`${this.baseUrl}/${id}`, data);
      return response.data;
    } catch (error) {
      console.error(`Error updating frequency template ${id}:`, error);
      throw error;
    }
  }

  async deleteTemplate(id: number): Promise<void> {
    try {
      await api.delete(`${this.baseUrl}/${id}`);
    } catch (error) {
      console.error(`Error deleting frequency template ${id}:`, error);
      throw error;
    }
  }

  async calculateFrequency(
    templateId: number, 
    startDate: string, 
    endDate: string
  ): Promise<FrequencyCalculationDto> {
    try {
      const response = await api.post<FrequencyCalculationDto>(
        `${this.baseUrl}/${templateId}/calculate`,
        { startDate, endDate }
      );
      return response.data;
    } catch (error) {
      console.error(`Error calculating frequency for template ${templateId}:`, error);
      throw error;
    }
  }

  async duplicateTemplate(templateId: number, newName: string): Promise<FrequencyTemplateListDto> {
    try {
      const response = await api.post<FrequencyTemplateListDto>(
        `${this.baseUrl}/${templateId}/duplicate`,
        { name: newName }
      );
      return response.data;
    } catch (error) {
      console.error(`Error duplicating frequency template ${templateId}:`, error);
      throw error;
    }
  }

  async getTemplateUsage(templateId: number): Promise<{ schedules: any[]; totalUsage: number }> {
    try {
      const response = await api.get(`${this.baseUrl}/${templateId}/usage`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching usage for template ${templateId}:`, error);
      throw error;
    }
  }

  async bulkUpdateTemplates(templateIds: number[], updates: Partial<CreateFrequencyTemplateDto>): Promise<void> {
    try {
      await api.patch(`${this.baseUrl}/bulk-update`, { templateIds, updates });
    } catch (error) {
      console.error('Error bulk updating frequency templates:', error);
      throw error;
    }
  }

  async exportTemplates(params?: FrequencyTemplateSearchDto): Promise<Blob> {
    try {
      const response = await api.get(`${this.baseUrl}/export`, { 
        params,
        responseType: 'blob'
      });
      return response.data;
    } catch (error) {
      console.error('Error exporting frequency templates:', error);
      throw error;
    }
  }
}

export const frequencyTemplateService = new FrequencyTemplateService(); 