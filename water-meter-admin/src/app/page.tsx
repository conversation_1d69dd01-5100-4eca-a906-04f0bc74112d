'use client';

import { useEffect } from 'react';
import { authService } from '../services/auth.service';

export default function HomePage() {
  useEffect(() => {
    // Check if user is authenticated before redirecting
    if (authService.isAuthenticated()) {
      // If authenticated, redirect to dashboard
      window.location.href = '/dashboard';
    } else {
      // If not authenticated, redirect to login
      window.location.href = '/login';
    }
  }, []);

  // Show a simple loading message while redirecting
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="text-center">
        <h1 className="text-2xl font-bold text-gray-800 mb-4">
          Water Meter Management System
        </h1>
        <p className="text-gray-600">Checking authentication...</p>
      </div>
    </div>
  );
}
