'use client';

import { useState } from 'react';
import { Form, Input, Button, Card, message } from 'antd';
import { UserOutlined, LockOutlined } from '@ant-design/icons';
import { authService } from '../../services/auth.service';
import { LoginRequest } from '../../types/auth';

export default function LoginPage() {
  const [loading, setLoading] = useState(false);

  const onFinish = async (values: LoginRequest) => {
    setLoading(true);
    try {
      const response = await authService.login(values);
      
      console.log('Login response:', response); // Debug log
      
      if (response.success && response.token) {
        // Save token and user information
        authService.saveToken(response.token);
        if (response.user) {
          authService.saveUser(response.user);
        }
        
        message.success(response.message || 'Login successful');
        window.location.href = '/dashboard';
      } else {
        console.error('Login failed - response:', response);
        message.error(response.message || '<PERSON><PERSON> failed');
      }
    } catch (error: any) {
      console.error('Login error:', error);
      message.error(error?.response?.data?.message || 'Login failed');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <Card title="Water Meter Management System" className="w-full max-w-md">
        <Form
          name="login"
          onFinish={onFinish}
          autoComplete="off"
          size="large"
        >
          <Form.Item
            name="username"
            rules={[{ required: true, message: 'Please input your username!' }]}
          >
            <Input
              prefix={<UserOutlined />}
              placeholder="Username"
            />
          </Form.Item>

          <Form.Item
            name="password"
            rules={[{ required: true, message: 'Please input your password!' }]}
          >
            <Input.Password
              prefix={<LockOutlined />}
              placeholder="Password"
            />
          </Form.Item>

          <Form.Item>
            <Button
              type="primary"
              htmlType="submit"
              loading={loading}
              className="w-full"
            >
              Login
            </Button>
          </Form.Item>
        </Form>
      </Card>
    </div>
  );
} 