'use client';

import { useState } from 'react';
import MainLayout from '@/components/layout/MainLayout';
import MenuManagement from '@/components/pages/MenuManagement';
import UserManagement from '@/components/pages/UserManagement';
import RoleManagement from '@/components/pages/RoleManagement';
import PermissionManagement from '@/components/pages/PermissionManagement';
import Settings from '@/components/pages/Settings';
import MeterReading from '@/components/pages/MeterReading';
import Dashboard from '@/components/pages/Dashboard';
import WaterMetersPage from '../water-meters/page';
import AmisSyncPage from '../amis-sync/page';
import BaselinePage from '../baseline/page';
import WorkPackageManagementPage from '../work-packages/page';
import RouteManagementPage from '../route-management/page';
import FrequencyTemplatesPage from '../frequency-templates/page';
import TaskManagementPage from '../task-management/page';
import TaskAssignmentPage from '../task-assignment/page';
import TaskMonitoringPage from '../task-monitoring/page';
import OverdueManagementPage from '../overdue-management/page';
// Import Meter Reading module pages
import ReadingRecordsPage from '../reading-records/page';
import PhotoManagement from '../photo-management/page';
import AnomalyManagementPage from '../anomaly-management/page';
import ValidationRulesPage from '../validation-rules/page';

// Import Reports module pages (only those that exist and have content)
import OperationalDashboardPage from '../reports/operational-dashboard/page';
import CompletionReportsPage from '../reports/completion-reports/page';
import ExceptionAnalysisPage from '../reports/exception-analysis/page';
import UserPerformancePage from '../reports/user-performance/page';

// Import Mobile Management module pages
import DeviceRegistryPage from '../mobile-management/device-registry/page';
import AppVersionsPage from '../mobile-management/app-versions/page';
import CompatibilityTestsPage from '../mobile-management/compatibility-tests/page';

// Import Quick Access module pages
import TodayAssignmentsPage from '../quick-access/today-assignments/page';
import PendingSyncPage from '../quick-access/pending-sync/page';
import RecentAlertsPage from '../quick-access/recent-alerts/page';
import CreateReactiveTaskPage from '../quick-access/create-reactive-task/page';

// Import Reports module pages for Export Center
import GenerateReportsPage from '../reports/generate-reports/page';
import ExportHistoryPage from '../reports/export-history/page';
import TemplateManagementPage from '../reports/template-management/page';

// Import Sync Center module pages
import DeviceSyncStatusPage from '../sync-center/device-sync-status/page';
import SyncQueuePage from '../sync-center/sync-queue/page';
import OfflineDataPage from '../sync-center/offline-data/page';
import SyncLogsPage from '../sync-center/sync-logs/page';

// Temporary placeholder components for missing pages
const PlaceholderPage = ({ title }: { title: string }) => (
  <div className="p-6">
    <h1 className="text-2xl font-bold mb-4">{title}</h1>
    <p className="text-gray-600">This page is under development. Please check back later.</p>
  </div>
);

export default function DashboardPage() {
  const [selectedMenu, setSelectedMenu] = useState('dashboard');

  // Debug: log the selected menu
  // console.log('Current selectedMenu:', selectedMenu);

  let content = null;
  switch (selectedMenu) {
    // Water Meter Management Modules (matching database menu codes)
    case 'amis-sync':
      content = <AmisSyncPage />
      break;
    case 'meter-mgmt': // This matches the database menu code
    case 'water-meters':
      content = <WaterMetersPage />
      break;
    case 'baseline-mgmt': // This matches the database menu code  
    case 'baseline':
      content = <BaselinePage />
      break;
    
    // Meter Reading modules
    case 'reading-records':
    case 'reading-mgmt':
      content = <ReadingRecordsPage />
      break;
    case 'photo-mgmt':
    case 'photo-management':
    case 'photo-gallery':
      console.log('Photo management case triggered with key:', selectedMenu);
      content = <PhotoManagement />
      break;
    case 'anomaly-mgmt':
    case 'anomaly-management':
      content = <AnomalyManagementPage />
      break;
    case 'validation-rules':
    case 'validation-mgmt':
      content = <ValidationRulesPage />
      break;
    
    // Planning & Scheduling modules - now independent pages
    case 'work-packages':
    case 'work-package':
    case 'work-package-mgmt':
    case 'workpackage-mgmt': // Database menu code
    case 'schedule-mgmt':
    case 'schedule-management':
      content = <WorkPackageManagementPage />
      break;
    case 'route-mgmt':
    case 'route-management':
      content = <RouteManagementPage />
      break;
    case 'frequency-templates':
    case 'freq-templates':
      content = <FrequencyTemplatesPage />
      break;
    
    // Task Management modules - new independent pages
    case 'task-mgmt':
    case 'task-management':
      content = <TaskManagementPage />
      break;
    case 'task-assignment':
    case 'task-assign':
      content = <TaskAssignmentPage menuType="individual" />
      break;
    case 'bulk-assignment':
      content = <TaskAssignmentPage menuType="bulk" />
      break;
    case 'reactive-assignment':
      content = <TaskAssignmentPage menuType="reactive" />
      break;
    case 'task-monitoring':
    case 'task-monitor':
      content = <TaskMonitoringPage />
      break;
    case 'overdue-mgmt':
    case 'overdue-management':
      content = <OverdueManagementPage />
      break;
    
    // Reports modules - matching database menu codes
    case 'operational-dashboard':
      content = <OperationalDashboardPage />
      break;
    case 'completion-reports':
      content = <CompletionReportsPage />
      break;
    case 'exception-analysis':
      content = <ExceptionAnalysisPage />
      break;
    case 'user-performance':
      content = <UserPerformancePage />
      break;
    case 'generate-reports':
      content = <GenerateReportsPage />
      break;
    case 'export-history':
      content = <ExportHistoryPage />
      break;
    case 'template-mgmt':
      content = <TemplateManagementPage />
      break;
    
    // Mobile Management modules - matching database menu codes
    case 'device-registry':
      content = <DeviceRegistryPage />
      break;
    case 'app-versions':
      content = <AppVersionsPage />
      break;
    case 'compatibility-tests':
      content = <CompatibilityTestsPage />
      break;
    
    // Quick Access modules - matching database menu codes
    case 'create-reactive-task':
      content = <CreateReactiveTaskPage />
      break;
    case 'todays-assignments':
      content = <TodayAssignmentsPage />
      break;
    case 'pending-sync':
      content = <PendingSyncPage />
      break;
    case 'recent-alerts':
      content = <RecentAlertsPage />
      break;
    
    // Sync Center modules - matching database menu codes
    case 'device-sync-status':
      content = <DeviceSyncStatusPage />
      break;
    case 'sync-queue':
      content = <SyncQueuePage />
      break;
    case 'offline-data':
      content = <OfflineDataPage />
      break;
    case 'sync-logs':
      content = <SyncLogsPage />
      break;
    
    // Original modules (matching database menu codes)
    case 'menu-management':
    case 'menu-mgmt': // Database code
      content = <MenuManagement />
      break;
    case 'user-management':
    case 'user-mgmt': // Database code
      content = <UserManagement />
      break;
    case 'role-management':
    case 'role-mgmt': // Database code
      content = <RoleManagement />
      break;
    case 'permission-management':
    case 'perm-mgmt': // Database code
      content = <PermissionManagement />
      break;
    case 'settings':
      content = <Settings />
      break;
    case 'meter-reading':
      content = <MeterReading />
      break;
    case 'dashboard':
    default:
      content = <Dashboard />
  }

  return (
    <MainLayout selectedMenu={selectedMenu} onMenuSelect={setSelectedMenu}>
      {content}
    </MainLayout>
  );
} 