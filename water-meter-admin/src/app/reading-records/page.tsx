'use client';

import React, { useState, useEffect, useCallback } from 'react';
import {
  Table, Button, Modal, Form, Input, Select, Card, Space, DatePicker,
  Tag, Statistic, Row, Col, message, Tooltip, Popconfirm, Badge,
  InputNumber, Switch, Upload, Progress, Tabs
} from 'antd';
import {
  SearchOutlined, PlusOutlined, EditOutlined, DeleteOutlined,
  CheckOutlined, SettingOutlined, DownloadOutlined, UploadOutlined,
  EyeOutlined, HistoryOutlined, PictureOutlined, ReloadOutlined,
  ExclamationCircleOutlined, FileTextOutlined, BarChartOutlined
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import dayjs from 'dayjs';
import {
  meterReadingService, MeterReadingListDto, MeterReadingSearchDto,
  CreateMeterReadingDto, UpdateMeterReadingDto, MeterReadingStatisticsDto
} from '@/services/meter-reading.service';
import PhotoViewModal from '@/components/PhotoViewModal';

const { RangePicker } = DatePicker;
const { Option } = Select;
const { TextArea } = Input;
const { TabPane } = Tabs;

const ReadingRecords: React.FC = () => {
  const [readings, setReadings] = useState<MeterReadingListDto[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchForm] = Form.useForm();
  const [createForm] = Form.useForm();
  const [editForm] = Form.useForm();
  const [correctForm] = Form.useForm();

  // Modal states
  const [createModalVisible, setCreateModalVisible] = useState(false);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [correctModalVisible, setCorrectModalVisible] = useState(false);
  const [historyModalVisible, setHistoryModalVisible] = useState(false);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [photoModalVisible, setPhotoModalVisible] = useState(false);
  const [selectedReading, setSelectedReading] = useState<MeterReadingListDto | null>(null);

  // Search and pagination
  const [searchParams, setSearchParams] = useState<MeterReadingSearchDto>({
    page: 1,
    pageSize: 10,
    sortBy: 'createdAt',
    sortDirection: 'desc'
  });
  const [totalCount, setTotalCount] = useState(0);

  // Statistics
  const [statistics, setStatistics] = useState<MeterReadingStatisticsDto | null>(null);

  // Utility data
  const [meterNumbers, setMeterNumbers] = useState<string[]>([]);

  // Load readings
  const loadReadings = useCallback(async () => {
    setLoading(true);
    try {
      const result = await meterReadingService.getReadings(searchParams);
      setReadings(result.readings);
      setTotalCount(result.totalCount);
    } catch (error) {
      message.error('Failed to load readings');
      console.error('Load readings error:', error);
    } finally {
      setLoading(false);
    }
  }, [searchParams]);

  // Load statistics
  const loadStatistics = useCallback(async () => {
    try {
      const stats = await meterReadingService.getReadingStatistics();
      setStatistics(stats);
    } catch (error) {
      message.error('Failed to load statistics');
      console.error('Load statistics error:', error);
    }
  }, []);

  // Load utility data
  const loadUtilityData = useCallback(async () => {
    try {
      const numbers = await meterReadingService.getMeterNumbers();
      setMeterNumbers(numbers);
    } catch (error) {
      console.error('Load utility data error:', error);
    }
  }, []);

  useEffect(() => {
    loadReadings();
    loadStatistics();
    loadUtilityData();
  }, [loadReadings, loadStatistics, loadUtilityData]);

  // Handle search
  const handleSearch = (values: any) => {
    const newSearchParams = {
      ...searchParams,
      ...values,
      startDate: values.dateRange?.[0]?.format('YYYY-MM-DD'),
      endDate: values.dateRange?.[1]?.format('YYYY-MM-DD'),
      page: 1
    };
    delete newSearchParams.dateRange;
    setSearchParams(newSearchParams);
  };

  // Handle pagination
  const handleTableChange = (pagination: any, filters: any, sorter: any) => {
    setSearchParams({
      ...searchParams,
      page: pagination.current,
      pageSize: pagination.pageSize,
      sortBy: sorter.field,
      sortDirection: sorter.order === 'ascend' ? 'asc' : 'desc'
    });
  };

  // Handle create reading
  const handleCreateReading = async (values: any) => {
    try {
      const createDto: CreateMeterReadingDto = {
        ...values,
        readingDate: values.readingDate.format('YYYY-MM-DD HH:mm:ss')
      };

      await meterReadingService.createReading(createDto);
      message.success('Reading created successfully');
      setCreateModalVisible(false);
      createForm.resetFields();
      loadReadings();
      loadStatistics();
    } catch (error) {
      message.error('Failed to create reading');
      console.error('Create reading error:', error);
    }
  };

  // Handle edit reading
  const handleEditReading = async (values: any) => {
    if (!selectedReading) return;

    try {
      const updateDto: UpdateMeterReadingDto = values;
      await meterReadingService.updateReading(selectedReading.id, updateDto);
      message.success('Reading updated successfully');
      setEditModalVisible(false);
      editForm.resetFields();
      setSelectedReading(null);
      loadReadings();
    } catch (error) {
      message.error('Failed to update reading');
      console.error('Update reading error:', error);
    }
  };

  // Handle correct reading
  const handleCorrectReading = async (values: any) => {
    if (!selectedReading) return;

    try {
      await meterReadingService.correctReading(
        selectedReading.id,
        values.correctedValue,
        values.reason
      );
      message.success('Reading corrected successfully');
      setCorrectModalVisible(false);
      correctForm.resetFields();
      setSelectedReading(null);
      loadReadings();
      loadStatistics();
    } catch (error) {
      message.error('Failed to correct reading');
      console.error('Correct reading error:', error);
    }
  };

  // Handle confirm reading
  const handleConfirmReading = async (reading: MeterReadingListDto) => {
    try {
      await meterReadingService.confirmReading(reading.id);
      message.success('Reading confirmed successfully');
      loadReadings();
      loadStatistics();
    } catch (error) {
      message.error('Failed to confirm reading');
      console.error('Confirm reading error:', error);
    }
  };

  // Handle delete reading
  const handleDeleteReading = async (reading: MeterReadingListDto) => {
    try {
      await meterReadingService.deleteReading(reading.id);
      message.success('Reading deleted successfully');
      loadReadings();
      loadStatistics();
    } catch (error) {
      message.error('Failed to delete reading');
      console.error('Delete reading error:', error);
    }
  };

  // Handle export
  const handleExport = async () => {
    try {
      const blob = await meterReadingService.exportReadings(searchParams);
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `meter_readings_${dayjs().format('YYYY-MM-DD')}.xlsx`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      message.success('Export completed successfully');
    } catch (error) {
      message.error('Failed to export readings');
      console.error('Export error:', error);
    }
  };

  // Get status color
  const getStatusColor = (status: string) => {
    const colors = {
      'Pending': 'orange',
      'Confirmed': 'green',
      'Anomaly': 'red',
      'Corrected': 'blue'
    };
    return colors[status as keyof typeof colors] || 'default';
  };

  // Get validation status color
  const getValidationStatusColor = (status?: string) => {
    const colors = {
      'Valid': 'green',
      'Invalid': 'red',
      'RequiresReview': 'orange'
    };
    return colors[status as keyof typeof colors] || 'default';
  };

  // Table columns
  const columns: ColumnsType<MeterReadingListDto> = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
      render: (id: number) => <span className="font-mono">{id}</span>,
    },
    {
      title: 'Meter Info',
      key: 'meterInfo',
      fixed: 'left',
      width: 150,
      render: (_, record: MeterReadingListDto) => (
        <div>
          <div className="font-medium">{record.meterNumber || '-'}</div>
          <div className="text-xs text-gray-500">Meter ID: {record.meterId}</div>
          {record.location && (
            <div className="text-xs text-gray-500">{record.location}</div>
          )}
        </div>
      ),
    },
    {
      title: 'Task & User',
      key: 'taskUser',
      width: 120,
      render: (_, record: MeterReadingListDto) => (
        <div>
          <div className="text-sm">Task: {record.taskId}</div>
          <div className="text-sm">User: {record.userId}</div>
          <div className="text-xs text-gray-500">{record.readBy || '-'}</div>
        </div>
      ),
    },
    {
      title: 'Reading Value',
      dataIndex: 'readingValue',
      key: 'readingValue',
      sorter: true,
      width: 120,
      render: (value?: number) => (
        value !== null && value !== undefined ? (
          <span className="font-mono">{value.toLocaleString()}</span>
        ) : '-'
      ),
    },
    {
      title: 'Consumption',
      dataIndex: 'consumption',
      key: 'consumption',
      sorter: true,
      width: 100,
      render: (value?: number) => (
        value !== null && value !== undefined ? (
          <span className={`font-mono ${value < 0 ? 'text-red-500' : ''}`}>
            {value.toLocaleString()}
          </span>
        ) : '-'
      ),
    },
    {
      title: 'GPS Location',
      key: 'gpsLocation',
      width: 140,
      render: (_, record: MeterReadingListDto) => (
        record.latitude && record.longitude ? (
          <div className="text-xs">
            <div>Lat: {record.latitude.toFixed(6)}</div>
            <div>Lng: {record.longitude.toFixed(6)}</div>
            {record.gpsAccuracy && (
              <div className="text-gray-500">±{record.gpsAccuracy}m</div>
            )}
          </div>
        ) : '-'
      ),
    },
    {
      title: 'Reading Date',
      dataIndex: 'readingDate',
      key: 'readingDate',
      sorter: true,
      width: 120,
      render: (date?: string) => date ? dayjs(date).format('YYYY-MM-DD HH:mm') : '-',
    },
    {
      title: 'Read By',
      dataIndex: 'readBy',
      key: 'readBy',
      width: 100,
      render: (readBy?: string) => readBy || '-',
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status?: string) => status ? (
        <Tag color={getStatusColor(status)}>{status}</Tag>
      ) : '-',
    },
    {
      title: 'Validation',
      dataIndex: 'validationStatus',
      key: 'validationStatus',
      width: 120,
      render: (status?: string) => (
        status ? (
          <Tag color={getValidationStatusColor(status)}>{status}</Tag>
        ) : '-'
      ),
    },
    {
      title: 'Method & Source',
      key: 'methodSource',
      width: 120,
      render: (_, record: MeterReadingListDto) => (
        <div className="text-xs">
          <div>Method: {record.readingMethod || '-'}</div>
          <div>Type: {record.readingType || '-'}</div>
          <div>Source: {record.dataSource || '-'}</div>
        </div>
      ),
    },
    {
      title: 'Flags',
      key: 'flags',
      width: 100,
      render: (_, record: MeterReadingListDto) => (
        <div className="text-xs">
          <div>Anomalous: {record.isAnomalous ? 'Yes' : 'No'}</div>
          <div>Can't Read: {record.cantRead ? 'Yes' : 'No'}</div>
          <div>Validated: {record.isValidated ? 'Yes' : 'No'}</div>
          <div>Deleted: {record.isDeleted ? 'Yes' : 'No'}</div>
        </div>
      ),
    },
    {
      title: 'Validation Info',
      key: 'validationInfo',
      width: 140,
      render: (_, record: MeterReadingListDto) => (
        <div className="text-xs">
          {record.validationStatus && (
            <div>Status: <Tag color={getValidationStatusColor(record.validationStatus)}>{record.validationStatus}</Tag></div>
          )}
          {record.validatedBy && (
            <div>By: {record.validatedBy}</div>
          )}
          {record.validationDate && (
            <div>Date: {dayjs(record.validationDate).format('MM-DD HH:mm')}</div>
          )}
          {record.validationComments && (
            <div className="text-gray-500" title={record.validationComments}>
              {record.validationComments.substring(0, 20)}...
            </div>
          )}
        </div>
      ),
    },
    {
      title: 'Anomaly Info',
      key: 'anomalyInfo',
      width: 140,
      render: (_, record: MeterReadingListDto) => (
        record.isAnomalous ? (
          <div className="text-xs">
            {record.anomalyType && (
              <div>Type: <Tag color="red">{record.anomalyType}</Tag></div>
            )}
            {record.anomalyReason && (
              <div className="text-gray-500" title={record.anomalyReason}>
                {record.anomalyReason.substring(0, 20)}...
              </div>
            )}
            {record.cantReadReason && (
              <div className="text-red-500" title={record.cantReadReason}>
                Can't read: {record.cantReadReason.substring(0, 15)}...
              </div>
            )}
          </div>
        ) : '-'
      ),
    },
    {
      title: 'OCR & Photo',
      key: 'ocrPhoto',
      width: 120,
      render: (_, record: MeterReadingListDto) => (
        <Space direction="vertical" size="small">
          <div className="flex items-center space-x-2">
            {record.hasPhoto && (
              <Tooltip title="Has Photo">
                <PictureOutlined className="text-blue-500" />
              </Tooltip>
            )}
            {record.hasOCR && (
              <Tooltip title={`OCR: ${record.ocrStatus} (${record.ocrConfidence}%)`}>
                <FileTextOutlined 
                  className={record.ocrStatus === 'Success' ? 'text-green-500' : 'text-orange-500'} 
                />
              </Tooltip>
            )}
          </div>
          {record.ocrConfidence && (
            <div className="text-xs">Confidence: {record.ocrConfidence}%</div>
          )}
        </Space>
      ),
    },
    {
      title: 'Audit Info',
      key: 'auditInfo',
      width: 140,
      render: (_, record: MeterReadingListDto) => (
        <div className="text-xs">
          <div>Created: {record.createdBy}</div>
          <div>{dayjs(record.createdAt).format('MM-DD HH:mm')}</div>
          <div>Updated: {record.updatedBy}</div>
          <div>{dayjs(record.updatedAt).format('MM-DD HH:mm')}</div>
        </div>
      ),
    },
    {
      title: 'Actions',
      key: 'actions',
      fixed: 'right',
      width: 200,
      render: (_, record: MeterReadingListDto) => (
        <Space>
          <Tooltip title="View Details">
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() => {
                setSelectedReading(record);
                setDetailModalVisible(true);
              }}
            />
          </Tooltip>

          <Tooltip title="Edit">
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => {
                setSelectedReading(record);
                editForm.setFieldsValue({
                  readingValue: record.readingValue,
                  status: record.status,
                  validationStatus: record.validationStatus,
                  anomalyType: record.anomalyType,
                  notes: record.notes,
                  location: record.location
                });
                setEditModalVisible(true);
              }}
            />
          </Tooltip>

          <Tooltip title="View Photos">
            <Button
              type="text"
              icon={<PictureOutlined />}
              onClick={() => {
                setSelectedReading(record);
                setPhotoModalVisible(true);
              }}
            />
          </Tooltip>

          {record.status === 'Pending' && (
            <Tooltip title="Confirm">
              <Popconfirm
                title="Confirm this reading?"
                onConfirm={() => handleConfirmReading(record)}
                okText="Yes"
                cancelText="No"
              >
                <Button
                  type="text"
                  icon={<CheckOutlined />}
                  className="text-green-600"
                />
              </Popconfirm>
            </Tooltip>
          )}

          <Tooltip title="Correct Reading">
            <Button
              type="text"
              icon={<SettingOutlined />}
              onClick={() => {
                setSelectedReading(record);
                correctForm.setFieldsValue({
                  currentValue: record.readingValue
                });
                setCorrectModalVisible(true);
              }}
            />
          </Tooltip>

          <Tooltip title="Delete">
            <Popconfirm
              title="Are you sure to delete this reading?"
              onConfirm={() => handleDeleteReading(record)}
              okText="Yes"
              cancelText="No"
            >
              <Button
                type="text"
                icon={<DeleteOutlined />}
                danger
              />
            </Popconfirm>
          </Tooltip>
        </Space>
      ),
    },
  ];

  return (
    <div className="p-6">
      {/* Header */}
      <div className="mb-6">
        <div className="flex justify-between items-center mb-4">
          <Space>
            <Button icon={<ReloadOutlined />} onClick={loadReadings}>
              Refresh
            </Button>
            <Button icon={<DownloadOutlined />} onClick={handleExport}>
              Export
            </Button>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => setCreateModalVisible(true)}
            >
              Add Reading
            </Button>
          </Space>
        </div>

        {/* Statistics Cards */}
        {statistics && (
          <Row gutter={16} className="mb-6">
            <Col span={4}>
              <Card>
                <Statistic
                  title="Total Readings"
                  value={statistics.totalReadings}
                  valueStyle={{ color: '#1890ff' }}
                />
              </Card>
            </Col>
            <Col span={4}>
              <Card>
                <Statistic
                  title="Pending"
                  value={statistics.pendingReadings}
                  valueStyle={{ color: '#fa8c16' }}
                />
              </Card>
            </Col>
            <Col span={4}>
              <Card>
                <Statistic
                  title="Confirmed"
                  value={statistics.confirmedReadings}
                  valueStyle={{ color: '#52c41a' }}
                />
              </Card>
            </Col>
            <Col span={4}>
              <Card>
                <Statistic
                  title="Anomalies"
                  value={statistics.anomalyReadings}
                  valueStyle={{ color: '#f5222d' }}
                />
              </Card>
            </Col>
            <Col span={4}>
              <Card>
                <Statistic
                  title="OCR Success Rate"
                  value={statistics.ocrSuccessRate}
                  precision={1}
                  suffix="%"
                  valueStyle={{ color: '#722ed1' }}
                />
              </Card>
            </Col>
            <Col span={4}>
              <Card>
                <Statistic
                  title="Data Quality"
                  value={statistics.dataQualityScore}
                  precision={1}
                  suffix="/10"
                  valueStyle={{ color: '#13c2c2' }}
                />
              </Card>
            </Col>
          </Row>
        )}
      </div>

      {/* Search Form */}
      <Card className="mb-6">
        <Form
          form={searchForm}
          layout="inline"
          onFinish={handleSearch}
          initialValues={searchParams}
        >
          <Form.Item name="meterNumber">
            <Select
              placeholder="Select Meter Number"
              allowClear
              showSearch
              style={{ width: 200 }}
            >
              {meterNumbers.map(number => (
                <Option key={number} value={number}>{number}</Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item name="dateRange">
            <RangePicker placeholder={['Start Date', 'End Date']} />
          </Form.Item>

          <Form.Item name="status">
            <Select placeholder="Status" allowClear style={{ width: 120 }}>
              <Option value="Pending">Pending</Option>
              <Option value="Confirmed">Confirmed</Option>
              <Option value="Anomaly">Anomaly</Option>
              <Option value="Corrected">Corrected</Option>
            </Select>
          </Form.Item>

          <Form.Item name="validationStatus">
            <Select placeholder="Validation" allowClear style={{ width: 140 }}>
              <Option value="Valid">Valid</Option>
              <Option value="Invalid">Invalid</Option>
              <Option value="RequiresReview">Requires Review</Option>
            </Select>
          </Form.Item>

          <Form.Item name="hasPhoto">
            <Select placeholder="Has Photo" allowClear style={{ width: 120 }}>
              <Option value={true}>Yes</Option>
              <Option value={false}>No</Option>
            </Select>
          </Form.Item>

          <Form.Item>
            <Button type="primary" htmlType="submit" icon={<SearchOutlined />}>
              Search
            </Button>
          </Form.Item>

          <Form.Item>
            <Button
              onClick={() => {
                searchForm.resetFields();
                setSearchParams({
                  page: 1,
                  pageSize: 10,
                  sortBy: 'createdAt',
                  sortDirection: 'desc'
                });
              }}
            >
              Reset
            </Button>
          </Form.Item>
        </Form>
      </Card>

      {/* Readings Table */}
      <Card>
        <Table
          columns={columns}
          dataSource={readings}
          rowKey="id"
          loading={loading}
          scroll={{ x: 2000 }}
          pagination={{
            current: searchParams.page,
            pageSize: searchParams.pageSize,
            total: totalCount,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} readings`,
          }}
          onChange={handleTableChange}
        />
      </Card>

      {/* Create Reading Modal */}
      <Modal
        title="Add New Reading"
        visible={createModalVisible}
        onCancel={() => {
          setCreateModalVisible(false);
          createForm.resetFields();
        }}
        footer={null}
        width={600}
      >
        <Form
          form={createForm}
          layout="vertical"
          onFinish={handleCreateReading}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="meterNumber"
                label="Meter Number"
                rules={[{ required: true, message: 'Please enter meter number' }]}
              >
                <Select
                  placeholder="Select or enter meter number"
                  showSearch
                  allowClear
                >
                  {meterNumbers.map(number => (
                    <Option key={number} value={number}>{number}</Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="readingDate"
                label="Reading Date"
                rules={[{ required: true, message: 'Please select reading date' }]}
              >
                <DatePicker
                  showTime
                  style={{ width: '100%' }}
                  placeholder="Select reading date"
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="currentReading"
                label="Current Reading"
                rules={[{ required: true, message: 'Please enter current reading' }]}
              >
                <InputNumber
                  style={{ width: '100%' }}
                  placeholder="Enter current reading"
                  min={0}
                  precision={2}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="previousReading"
                label="Previous Reading"
              >
                <InputNumber
                  style={{ width: '100%' }}
                  placeholder="Enter previous reading"
                  min={0}
                  precision={2}
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item name="readBy" label="Read By">
                <Input placeholder="Enter reader name" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="location" label="Location">
                <Input placeholder="Enter location" />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item name="notes" label="Notes">
            <TextArea rows={3} placeholder="Enter any notes" />
          </Form.Item>

          <div className="flex justify-end space-x-2">
            <Button
              onClick={() => {
                setCreateModalVisible(false);
                createForm.resetFields();
              }}
            >
              Cancel
            </Button>
            <Button type="primary" htmlType="submit">
              Create Reading
            </Button>
          </div>
        </Form>
      </Modal>

      {/* Edit Reading Modal */}
      <Modal
        title="Edit Reading"
        visible={editModalVisible}
        onCancel={() => {
          setEditModalVisible(false);
          editForm.resetFields();
          setSelectedReading(null);
        }}
        footer={null}
        width={600}
      >
        <Form
          form={editForm}
          layout="vertical"
          onFinish={handleEditReading}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="readingValue"
                label="Reading Value"
                rules={[{ required: true, message: 'Please enter reading value' }]}
              >
                <InputNumber
                  style={{ width: '100%' }}
                  min={0}
                  precision={4}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="anomalyType" label="Anomaly Type">
                <Select allowClear placeholder="Select anomaly type">
                  <Option value="HighConsumption">High Consumption</Option>
                  <Option value="LowConsumption">Low Consumption</Option>
                  <Option value="NegativeConsumption">Negative Consumption</Option>
                  <Option value="MeterError">Meter Error</Option>
                  <Option value="ReadingError">Reading Error</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="status"
                label="Status"
                rules={[{ required: true, message: 'Please select status' }]}
              >
                <Select>
                  <Option value="Pending">Pending</Option>
                  <Option value="Confirmed">Confirmed</Option>
                  <Option value="Anomaly">Anomaly</Option>
                  <Option value="Corrected">Corrected</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="validationStatus" label="Validation Status">
                <Select allowClear>
                  <Option value="Valid">Valid</Option>
                  <Option value="Invalid">Invalid</Option>
                  <Option value="RequiresReview">Requires Review</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item name="location" label="Location">
            <Input placeholder="Enter location" />
          </Form.Item>

          <Form.Item name="notes" label="Notes">
            <TextArea rows={3} placeholder="Enter any notes" />
          </Form.Item>

          <div className="flex justify-end space-x-2">
            <Button
              onClick={() => {
                setEditModalVisible(false);
                editForm.resetFields();
                setSelectedReading(null);
              }}
            >
              Cancel
            </Button>
            <Button type="primary" htmlType="submit">
              Update Reading
            </Button>
          </div>
        </Form>
      </Modal>

      {/* Correct Reading Modal */}
      <Modal
        title="Correct Reading"
        visible={correctModalVisible}
        onCancel={() => {
          setCorrectModalVisible(false);
          correctForm.resetFields();
          setSelectedReading(null);
        }}
        footer={null}
        width={500}
      >
        <Form
          form={correctForm}
          layout="vertical"
          onFinish={handleCorrectReading}
        >
          <Form.Item name="currentValue" label="Current Value (Read Only)">
            <InputNumber
              style={{ width: '100%' }}
              disabled
              precision={2}
            />
          </Form.Item>

          <Form.Item
            name="correctedValue"
            label="Corrected Value"
            rules={[{ required: true, message: 'Please enter corrected value' }]}
          >
            <InputNumber
              style={{ width: '100%' }}
              min={0}
              precision={2}
              placeholder="Enter corrected value"
            />
          </Form.Item>

          <Form.Item
            name="reason"
            label="Correction Reason"
            rules={[{ required: true, message: 'Please enter correction reason' }]}
          >
            <TextArea
              rows={3}
              placeholder="Enter reason for correction"
            />
          </Form.Item>

          <div className="flex justify-end space-x-2">
            <Button
              onClick={() => {
                setCorrectModalVisible(false);
                correctForm.resetFields();
                setSelectedReading(null);
              }}
            >
              Cancel
            </Button>
            <Button type="primary" htmlType="submit">
              Correct Reading
            </Button>
          </div>
        </Form>
      </Modal>

      {/* Detail View Modal */}
      <Modal
        title="Reading Details"
        visible={detailModalVisible}
        onCancel={() => {
          setDetailModalVisible(false);
          setSelectedReading(null);
        }}
        footer={[
          <Button key="close" onClick={() => setDetailModalVisible(false)}>
            Close
          </Button>
        ]}
        width={800}
      >
        {selectedReading && (
          <div className="space-y-4">
            <Row gutter={16}>
              <Col span={8}>
                <div className="bg-gray-50 p-3 rounded">
                  <div className="text-sm text-gray-500">Reading ID</div>
                  <div className="font-mono text-lg">{selectedReading.id}</div>
                </div>
              </Col>
              <Col span={8}>
                <div className="bg-gray-50 p-3 rounded">
                  <div className="text-sm text-gray-500">Meter Number</div>
                  <div className="font-mono text-lg">{selectedReading.meterNumber}</div>
                </div>
              </Col>
              <Col span={8}>
                <div className="bg-gray-50 p-3 rounded">
                  <div className="text-sm text-gray-500">Reading Value</div>
                  <div className="font-mono text-lg">{selectedReading.readingValue.toLocaleString()}</div>
                </div>
              </Col>
            </Row>

            <Row gutter={16}>
              <Col span={8}>
                <div className="bg-gray-50 p-3 rounded">
                  <div className="text-sm text-gray-500">Previous Reading</div>
                  <div className="font-mono text-lg">{selectedReading.previousReading?.toLocaleString() || '-'}</div>
                </div>
              </Col>
              <Col span={8}>
                <div className="bg-gray-50 p-3 rounded">
                  <div className="text-sm text-gray-500">Consumption</div>
                  <div className="font-mono text-lg">{selectedReading.consumption?.toLocaleString() || '-'}</div>
                </div>
              </Col>
              <Col span={8}>
                <div className="bg-gray-50 p-3 rounded">
                  <div className="text-sm text-gray-500">Reading Date</div>
                  <div className="text-lg">{dayjs(selectedReading.readingDate).format('YYYY-MM-DD HH:mm:ss')}</div>
                </div>
              </Col>
            </Row>

            <Row gutter={16}>
              <Col span={8}>
                <div className="bg-gray-50 p-3 rounded">
                  <div className="text-sm text-gray-500">Read By</div>
                  <div className="text-lg">{selectedReading.readBy || '-'}</div>
                </div>
              </Col>
              <Col span={8}>
                <div className="bg-gray-50 p-3 rounded">
                  <div className="text-sm text-gray-500">Status</div>
                  <div className="text-lg">
                    <Tag color={getStatusColor(selectedReading.status)}>{selectedReading.status}</Tag>
                  </div>
                </div>
              </Col>
              <Col span={8}>
                <div className="bg-gray-50 p-3 rounded">
                  <div className="text-sm text-gray-500">Reading Method</div>
                  <div className="text-lg">{selectedReading.readingMethod || '-'}</div>
                </div>
              </Col>
            </Row>

            <Row gutter={16}>
              <Col span={12}>
                <div className="bg-gray-50 p-3 rounded">
                  <div className="text-sm text-gray-500">GPS Location</div>
                  <div className="text-lg">
                    {selectedReading.latitude && selectedReading.longitude ? (
                      <div>
                        <div>Lat: {selectedReading.latitude.toFixed(6)}</div>
                        <div>Lng: {selectedReading.longitude.toFixed(6)}</div>
                        {selectedReading.gpsAccuracy && (
                          <div className="text-sm text-gray-500">Accuracy: ±{selectedReading.gpsAccuracy}m</div>
                        )}
                      </div>
                    ) : '-'}
                  </div>
                </div>
              </Col>
              <Col span={12}>
                <div className="bg-gray-50 p-3 rounded">
                  <div className="text-sm text-gray-500">OCR Info</div>
                  <div className="text-lg">
                    {selectedReading.hasOCR ? (
                      <div>
                        <div>Status: {selectedReading.ocrStatus || 'N/A'}</div>
                        <div>Confidence: {selectedReading.ocrConfidence ? `${(selectedReading.ocrConfidence * 100).toFixed(1)}%` : 'N/A'}</div>
                      </div>
                    ) : 'No OCR'}
                  </div>
                </div>
              </Col>
            </Row>

            {selectedReading.notes && (
              <div className="bg-gray-50 p-3 rounded">
                <div className="text-sm text-gray-500">Notes</div>
                <div className="text-lg">{selectedReading.notes}</div>
              </div>
            )}

            {selectedReading.validationComments && (
              <div className="bg-gray-50 p-3 rounded">
                <div className="text-sm text-gray-500">Validation Comments</div>
                <div className="text-lg">{selectedReading.validationComments}</div>
              </div>
            )}

            <Row gutter={16}>
              <Col span={12}>
                <div className="bg-gray-50 p-3 rounded">
                  <div className="text-sm text-gray-500">Created At</div>
                  <div className="text-lg">{dayjs(selectedReading.createdAt).format('YYYY-MM-DD HH:mm:ss')}</div>
                </div>
              </Col>
              <Col span={12}>
                <div className="bg-gray-50 p-3 rounded">
                  <div className="text-sm text-gray-500">Updated At</div>
                  <div className="text-lg">{dayjs(selectedReading.updatedAt).format('YYYY-MM-DD HH:mm:ss')}</div>
                </div>
              </Col>
            </Row>
          </div>
        )}
      </Modal>

      {/* Photo View Modal */}
      <PhotoViewModal
        visible={photoModalVisible}
        onCancel={() => {
          setPhotoModalVisible(false);
          setSelectedReading(null);
        }}
        readingId={selectedReading?.id || 0}
        meterNumber={selectedReading?.meterNumber}
      />
    </div>
  );
};

export default ReadingRecords;