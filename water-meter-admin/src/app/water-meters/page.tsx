'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { 
  Table, 
  Button, 
  Input, 
  Select, 
  Modal, 
  Form, 
  message, 
  Space, 
  Card, 
  Row, 
  Col,
  Tag,
  Popconfirm,
  Tooltip,
  DatePicker,
  Upload,
  Progress,
  Badge,
  InputNumber
} from 'antd';
import { 
  PlusOutlined, 
  SearchOutlined, 
  EditOutlined, 
  DeleteOutlined, 
  ExportOutlined,
  ImportOutlined,
  EyeOutlined,
  EnvironmentOutlined,
  ThunderboltOutlined,
  ToolOutlined,
  AlertOutlined,
  AimOutlined,
  SaveOutlined,
  CloseOutlined,
  FormOutlined
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import { WaterMeterListDto, WaterMeterDto, WaterMeterSearchDto, CreateWaterMeterDto, UpdateWaterMeterDto, AmsImportResultDto } from '@/types/water-meter';
import { waterMeterService } from '@/services/water-meter.service';
import { gpsService, GpsUpdateResult, BatchGpsUpdateResult } from '@/services/gps.service';
import { format } from 'date-fns';
import dayjs from 'dayjs';
import { DataManagementPaginatedTable } from '@/components/PaginatedTable';
import { ExportDropdown, ExportConfigs } from '@/components/ExportButton';
import { AddressInput, AddressData } from '@/components/AddressInput';

const { Option } = Select;
const { RangePicker } = DatePicker;

export default function WaterMetersPage() {
  const [meters, setMeters] = useState<WaterMeterListDto[]>([]);
  const [loading, setLoading] = useState(false);
  const [total, setTotal] = useState(0);
  const [searchParams, setSearchParams] = useState<WaterMeterSearchDto>({
    page: 1,
    pageSize: 10,
    sortBy: 'SerialNumber',
    sortDirection: 'asc'
  });

  // Modal states
  const [isCreateModalVisible, setIsCreateModalVisible] = useState(false);
  const [isDetailModalVisible, setIsDetailModalVisible] = useState(false);
  const [isImportModalVisible, setIsImportModalVisible] = useState(false);
  const [isAmsImportModalVisible, setIsAmsImportModalVisible] = useState(false);
  const [selectedMeter, setSelectedMeter] = useState<WaterMeterDto | null>(null);
  const [form] = Form.useForm();
  
  // Import states
  const [importLoading, setImportLoading] = useState(false);
  const [importProgress, setImportProgress] = useState(0);

  // GPS states
  const [gpsLoading, setGpsLoading] = useState(false);
  const [gpsLoadingIds, setGpsLoadingIds] = useState<Set<number>>(new Set());

  // Inline editing states
  const [editingKey, setEditingKey] = useState<number | null>(null);
  const [editForm] = Form.useForm();

  const loadMeters = useCallback(async () => {
    setLoading(true);
    try {
      const response = await waterMeterService.getWaterMeters(searchParams);
      console.log('API Response:', response); // Debug log
      console.log('Meters data:', response.data); // Debug log
      setMeters(response.data);
      setTotal(response.totalCount);
    } catch (error) {
      message.error('Failed to load water meters');
      console.error('Error loading meters:', error);
    } finally {
      setLoading(false);
    }
  }, [searchParams]);

  useEffect(() => {
    loadMeters();
  }, [loadMeters]);

  const handleSearch = (values: any) => {
    setSearchParams({
      ...searchParams,
      page: 1,
      serialNumber: values.serialNumber,
      location: values.location,
      meterType: values.meterType,
      status: values.status,
      customerName: values.customerName,
      installDateFrom: values.installDateRange?.[0]?.toDate(),
      installDateTo: values.installDateRange?.[1]?.toDate()
    });
  };



  const handleSubmitWaterMeter = async (values: CreateWaterMeterDto) => {
    try {
      if (selectedMeter) {
        // Update existing water meter using PUT
        const updateData: UpdateWaterMeterDto = {
          location: values.location,
          address: values.address,
          meterType: values.meterType,
          status: values.status,
          installDate: values.installDate,
          accountNumber: values.accountNumber,
          customerCode: values.customerCode,
          customerName: values.customerName,
          latitude: values.latitude,
          longitude: values.longitude,
          brand: values.brand,
          model: values.model,
          communicationMethod: values.communicationMethod,
          notes: values.notes,
          // Note: assetId is not included in UpdateWaterMeterDto
        };
        await waterMeterService.updateWaterMeter(selectedMeter.id, updateData);
        message.success('Water meter updated successfully');
      } else {
        // Create new water meter using POST
        await waterMeterService.createWaterMeter(values);
        message.success('Water meter created successfully');
      }

      setIsCreateModalVisible(false);
      setSelectedMeter(null);
      form.resetFields();
      loadMeters();
    } catch (error) {
      const action = selectedMeter ? 'update' : 'create';
      message.error(`Failed to ${action} water meter`);
      console.error(`Error ${action}ing meter:`, error);
    }
  };

  const handleEdit = async (meter: WaterMeterListDto) => {
    try {
      const details = await waterMeterService.getWaterMeterById(meter.id);
      setSelectedMeter(details);
      form.setFieldsValue({
        ...details,
        installDate: details.installDate ? dayjs(details.installDate) : null
      });
      setIsCreateModalVisible(true);
    } catch (error) {
      message.error('Failed to load meter details for editing');
      console.error('Error loading details:', error);
    }
  };

  const handleDelete = async (id: number) => {
    try {
      await waterMeterService.deleteWaterMeter(id);
      message.success('Water meter deleted successfully');
      loadMeters();
    } catch (error) {
      message.error('Failed to delete water meter');
      console.error('Error deleting meter:', error);
    }
  };

  const handleViewDetails = async (meter: WaterMeterListDto) => {
    try {
      const details = await waterMeterService.getWaterMeterById(meter.id);
      setSelectedMeter(details);
      setIsDetailModalVisible(true);
    } catch (error) {
      message.error('Failed to load meter details');
      console.error('Error loading details:', error);
    }
  };

  const handleAmsImport = async (file: File) => {
    setImportLoading(true);
    setImportProgress(0);
    
    try {
      // Simulate progress
      const progressInterval = setInterval(() => {
        setImportProgress(prev => Math.min(prev + 10, 90));
      }, 500);

      const result: AmsImportResultDto = await waterMeterService.importFromAmsExcel(file);
      
      clearInterval(progressInterval);
      setImportProgress(100);
      
      if (result.success) {
        message.success({
          content: (
            <div>
              <div><strong>AMS Import Successful!</strong></div>
              <div>{result.summary}</div>
              {result.warnings.length > 0 && (
                <div style={{ marginTop: 8, fontSize: '12px' }}>
                  <strong>Warnings:</strong>
                  <ul style={{ margin: 0, paddingLeft: 16 }}>
                    {result.warnings.slice(0, 3).map((warning, index) => (
                      <li key={index}>{warning}</li>
                    ))}
                    {result.warnings.length > 3 && <li>... and {result.warnings.length - 3} more</li>}
                  </ul>
                </div>
              )}
            </div>
          ),
          duration: 10
        });
        setIsAmsImportModalVisible(false);
        loadMeters();
      } else {
        message.error({
          content: (
            <div>
              <div><strong>AMS Import Failed</strong></div>
              <div>Errors:</div>
              <ul style={{ margin: 0, paddingLeft: 16 }}>
                {result.errors.slice(0, 3).map((error, index) => (
                  <li key={index}>{error}</li>
                ))}
                {result.errors.length > 3 && <li>... and {result.errors.length - 3} more</li>}
              </ul>
            </div>
          ),
          duration: 15
        });
      }
    } catch (error) {
      message.error('Failed to import AMS Excel file');
      console.error('Error importing AMS Excel:', error);
    } finally {
      setImportLoading(false);
      setImportProgress(0);
    }
  };

  const handleUpdateGps = async (waterMeterId: number) => {
    setGpsLoadingIds(prev => new Set(prev).add(waterMeterId));

    try {
      const result: GpsUpdateResult = await gpsService.updateWaterMeterGps(waterMeterId);

      if (result.success) {
        message.success({
          content: (
            <div>
              <div><strong>GPS Updated Successfully!</strong></div>
              <div>Meter: {result.waterMeterSerial}</div>
              <div>Coordinates: {result.newLatitude?.toFixed(6)}, {result.newLongitude?.toFixed(6)}</div>
            </div>
          ),
          duration: 5
        });
        loadMeters();
      } else {
        message.error({
          content: (
            <div>
              <div><strong>GPS Update Failed</strong></div>
              <div>{result.errorMessage}</div>
            </div>
          ),
          duration: 8
        });
      }
    } catch (error) {
      message.error('Failed to update GPS coordinates');
      console.error('Error updating GPS:', error);
    } finally {
      setGpsLoadingIds(prev => {
        const newSet = new Set(prev);
        newSet.delete(waterMeterId);
        return newSet;
      });
    }
  };

  const handleBatchUpdateGps = async () => {
    Modal.confirm({
      title: 'Batch GPS Update',
      content: (
        <div>
          <p>This will start a background process to update GPS coordinates for all water meters with addresses.</p>
          <p><strong>Note:</strong> This process may take several minutes to complete and will continue running in the background.</p>
          <p>Are you sure you want to proceed?</p>
        </div>
      ),
      onOk: async () => {
        setGpsLoading(true);

        try {
          // Start the background process (don't wait for completion)
          gpsService.updateAllMissingGps().catch(error => {
            console.error('Background GPS update failed:', error);
          });

          message.success({
            content: (
              <div>
                <div><strong>GPS Update Started!</strong></div>
                <div>Background process is now updating GPS coordinates for all water meters.</div>
                <div>This may take several minutes to complete. Please check back later.</div>
              </div>
            ),
            duration: 8
          });

        } catch (error) {
          message.error('Failed to start GPS update process');
          console.error('Error starting GPS update:', error);
        } finally {
          setGpsLoading(false);
        }
      }
    });
  };

  // Handle export success
  const handleExportSuccess = (filename: string, format: string) => {
    console.log(`Exported ${filename} as ${format}`);
  };

  // Inline editing functions
  const isEditing = (record: WaterMeterListDto) => record.id === editingKey;

  const edit = (record: WaterMeterListDto) => {
    editForm.setFieldsValue({
      serialNumber: record.serialNumber,
      accountNumber: record.accountNumber,
      location: record.location,
      meterType: record.meterType,
      status: record.status,
      customerName: record.customerName,
      lastRead: record.lastRead,
      lastReadingDate: record.lastReadingDate ? dayjs(record.lastReadingDate) : null,
    });
    setEditingKey(record.id);
  };

  const cancel = () => {
    setEditingKey(null);
    editForm.resetFields();
  };

  const save = async (id: number) => {
    try {
      const values = await editForm.validateFields();

      // Convert date back to string if it exists
      if (values.lastReadingDate) {
        values.lastReadingDate = values.lastReadingDate.toISOString();
      }

      await waterMeterService.updateWaterMeter(id, values);
      message.success('Water meter updated successfully');
      setEditingKey(null);
      editForm.resetFields();
      loadMeters(); // Refresh the data
    } catch (error) {
      message.error('Failed to update water meter');
      console.error('Update error:', error);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Active': return 'green';
      case 'Inactive': return 'red';
      case 'Maintenance': return 'orange';
      case 'Replaced': return 'gray';
      default: return 'default';
    }
  };

  const getBatteryColor = (level?: number) => {
    if (!level) return 'gray';
    if (level > 60) return 'green';
    if (level > 30) return 'orange';
    return 'red';
  };



  const columns: ColumnsType<WaterMeterListDto> = [
    {
      title: 'Serial Number',
      dataIndex: 'serialNumber',
      key: 'serialNumber',
      sorter: true,
      render: (text: string, record) => {
        const editing = isEditing(record);
        return editing ? (
          <Form.Item
            name="serialNumber"
            style={{ margin: 0 }}
          >
            <Input disabled style={{ fontWeight: 'bold' }} />
          </Form.Item>
        ) : (
          <div>
            <strong>{text}</strong>
            {record.assetId && (
              <div style={{ fontSize: '12px', color: '#666' }}>
                ID: {record.assetId}
              </div>
            )}
          </div>
        );
      }
    },
    {
      title: 'Account',
      dataIndex: 'accountNumber',
      key: 'accountNumber',
      render: (accountNumber: string, record) => {
        const editing = isEditing(record);
        return editing ? (
          <Form.Item
            name="accountNumber"
            style={{ margin: 0 }}
          >
            <Input placeholder="Enter account number" />
          </Form.Item>
        ) : (
          <div>
            {accountNumber && <div><strong>{accountNumber}</strong></div>}
            {record.customerName && (
              <div style={{ fontSize: '12px', color: '#666' }}>
                {record.customerName}
              </div>
            )}
          </div>
        );
      }
    },
    {
      title: 'Location',
      dataIndex: 'location',
      key: 'location',
      sorter: true,
      render: (text: string, record) => {
        const editing = isEditing(record);
        return editing ? (
          <Form.Item
            name="location"
            style={{ margin: 0 }}
            rules={[{ required: true, message: 'Location is required' }]}
          >
            <Input placeholder="Enter location" />
          </Form.Item>
        ) : (
          <Space>
            <EnvironmentOutlined />
            {text}
          </Space>
        );
      }
    },
    {
      title: 'GPS Coordinates',
      key: 'gpsCoordinates',
      width: 150,
      render: (text: string, record) => (
        <div>
          {record.latitude && record.longitude ? (
            <div>
              <div style={{ fontSize: '12px', color: '#1890ff' }}>
                📍 {record.latitude.toFixed(6)}
              </div>
              <div style={{ fontSize: '12px', color: '#1890ff' }}>
                {record.longitude.toFixed(6)}
              </div>
            </div>
          ) : (
            <span style={{ color: '#999', fontSize: '12px' }}>No GPS</span>
          )}
        </div>
      )
    },
    {
      title: 'Type',
      dataIndex: 'meterType',
      key: 'meterType',
      render: (type: string, record) => {
        const editing = isEditing(record);
        return editing ? (
          <Form.Item
            name="meterType"
            style={{ margin: 0 }}
            rules={[{ required: true, message: 'Type is required' }]}
          >
            <Select placeholder="Select type" style={{ width: '100%' }}>
              <Option value="Residential">Residential</Option>
              <Option value="Commercial">Commercial</Option>
              <Option value="Industrial">Industrial</Option>
            </Select>
          </Form.Item>
        ) : (
          <Tag color="blue">{type}</Tag>
        );
      }
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status: string, record) => {
        const editing = isEditing(record);
        return editing ? (
          <Form.Item
            name="status"
            style={{ margin: 0 }}
            rules={[{ required: true, message: 'Status is required' }]}
          >
            <Select placeholder="Select status" style={{ width: '100%' }}>
              <Option value="Active">Active</Option>
              <Option value="Inactive">Inactive</Option>
              <Option value="Maintenance">Maintenance</Option>
              <Option value="Replaced">Replaced</Option>
            </Select>
          </Form.Item>
        ) : (
          <div>
            <Tag color={getStatusColor(status)}>{status}</Tag>
            {record.syncStatus && (
              <div style={{ marginTop: 4 }}>
                <Tag
                  color={record.syncStatus === 'Imported' ? 'green' :
                         record.syncStatus === 'Updated' ? 'blue' : 'orange'}
                  style={{ fontSize: '10px' }}
                >
                  {record.syncStatus}
                </Tag>
              </div>
            )}
          </div>
        );
      }
    },
    {
      title: 'Customer',
      dataIndex: 'customerName',
      key: 'customerName',
      render: (name: string, record) => {
        const editing = isEditing(record);
        return editing ? (
          <Form.Item
            name="customerName"
            style={{ margin: 0 }}
          >
            <Input placeholder="Enter customer name" />
          </Form.Item>
        ) : (
          name || 'N/A'
        );
      }
    },
    {
      title: 'Last Reading',
      dataIndex: 'lastRead',
      key: 'lastRead',
      align: 'right',
      render: (value: number, record) => {
        const editing = isEditing(record);
        return editing ? (
          <Form.Item
            name="lastRead"
            style={{ margin: 0 }}
          >
            <InputNumber
              placeholder="Enter reading"
              style={{ width: '100%' }}
              min={0}
              precision={2}
              addonAfter="m³"
            />
          </Form.Item>
        ) : (
          (value && typeof value === 'number') ? `${value.toFixed(2)} m³` : 'N/A'
        );
      }
    },
    {
      title: 'Last Reading Date',
      dataIndex: 'lastReadingDate',
      key: 'lastReadingDate',
      render: (date: string, record) => {
        const editing = isEditing(record);
        return editing ? (
          <Form.Item
            name="lastReadingDate"
            style={{ margin: 0 }}
          >
            <DatePicker
              placeholder="Select date"
              style={{ width: '100%' }}
              format="DD/MM/YYYY"
            />
          </Form.Item>
        ) : (
          (() => {
            try {
              return date ? format(new Date(date), 'dd/MM/yyyy') : 'Never';
            } catch (error) {
              console.error('Date parsing error:', error, 'for date:', date);
              return 'Invalid Date';
            }
          })()
        );
      }
    },
    {
      title: 'Battery Level',
      dataIndex: 'batteryLevel',
      key: 'batteryLevel',
      render: (level: number) => {
        const safeLevel = level && typeof level === 'number' ? level : 0;
        return (
          <Space>
            <Progress 
              type="circle" 
              size={30} 
              percent={safeLevel} 
              strokeColor={getBatteryColor(safeLevel)}
              format={() => <ThunderboltOutlined />}
            />
            {safeLevel > 0 ? `${safeLevel}%` : 'N/A'}
          </Space>
        );
      }
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record) => {
        const editing = isEditing(record);
        return editing ? (
          <Space>
            <Tooltip title="Save">
              <Button
                type="text"
                icon={<SaveOutlined />}
                onClick={() => save(record.id)}
                style={{ color: '#52c41a' }}
              />
            </Tooltip>
            <Tooltip title="Cancel">
              <Button
                type="text"
                icon={<CloseOutlined />}
                onClick={cancel}
                style={{ color: '#ff4d4f' }}
              />
            </Tooltip>
          </Space>
        ) : (
          <Space>
            <Tooltip title="View Details">
              <Button
                type="text"
                icon={<EyeOutlined />}
                onClick={() => handleViewDetails(record)}
              />
            </Tooltip>
            <Tooltip title="Quick Edit">
              <Button
                type="text"
                icon={<EditOutlined />}
                onClick={() => edit(record)}
                disabled={editingKey !== null}
              />
            </Tooltip>
            <Tooltip title="Form Edit">
              <Button
                type="text"
                icon={<FormOutlined />}
                onClick={() => handleEdit(record)}
                disabled={editingKey !== null}
              />
            </Tooltip>
            <Tooltip title="Update GPS Coordinates">
              <Button
                type="text"
                icon={<AimOutlined />}
                loading={gpsLoadingIds.has(record.id)}
                onClick={() => handleUpdateGps(record.id)}
                disabled={!record.location && !record.roadName && !record.township}
              />
            </Tooltip>
            <Tooltip title="Delete">
              <Popconfirm
                title="Are you sure you want to delete this water meter?"
                onConfirm={() => handleDelete(record.id)}
                okText="Yes"
                cancelText="No"
              >
                <Button type="text" danger icon={<DeleteOutlined />} />
              </Popconfirm>
            </Tooltip>
          </Space>
        );
      }
    }
  ];

  return (
    <div className="p-6">
      <Card>
        <div className="mb-6">
          {/* Search Form */}
          <Form
            layout="inline"
            onFinish={handleSearch}
            className="mb-4"
          >
            <Form.Item name="serialNumber">
              <Input placeholder="Serial Number" allowClear />
            </Form.Item>
            <Form.Item name="location">
              <Input placeholder="Location" allowClear />
            </Form.Item>
            <Form.Item name="meterType">
              <Select placeholder="Meter Type" allowClear style={{ width: 150 }}>
                <Option value="Residential">Residential</Option>
                <Option value="Commercial">Commercial</Option>
                <Option value="Industrial">Industrial</Option>
              </Select>
            </Form.Item>
            <Form.Item name="status">
              <Select placeholder="Status" allowClear style={{ width: 120 }}>
                <Option value="Active">Active</Option>
                <Option value="Inactive">Inactive</Option>
                <Option value="Maintenance">Maintenance</Option>
                <Option value="Replaced">Replaced</Option>
              </Select>
            </Form.Item>
            <Form.Item name="customerName">
              <Input placeholder="Customer Name" allowClear />
            </Form.Item>
            <Form.Item name="installDateRange">
              <RangePicker placeholder={['Install Date From', 'Install Date To']} />
            </Form.Item>
            <Form.Item>
              <Button type="primary" htmlType="submit" icon={<SearchOutlined />}>
                Search
              </Button>
            </Form.Item>
          </Form>

          {/* Action Buttons */}
          <Space className="mb-4">
            <Button 
              type="primary" 
              icon={<PlusOutlined />}
              onClick={() => {
                setSelectedMeter(null);
                form.resetFields();
                setIsCreateModalVisible(true);
              }}
            >
              Add Water Meter
            </Button>

            <Button 
              icon={<ImportOutlined />} 
              type="primary" 
              onClick={() => setIsAmsImportModalVisible(true)}
            >
              AMS Excel Import
            </Button>
            <ExportDropdown
              config={ExportConfigs.WaterMeters}
              searchParams={searchParams}
              onExportSuccess={handleExportSuccess}
              buttonText="Export"
            />
            <Button
              icon={<AimOutlined />}
              loading={gpsLoading}
              onClick={handleBatchUpdateGps}
            >
              Update Missing GPS
            </Button>
          </Space>
        </div>

        {/* Data Management Table with Pagination and Form Support */}
        <Form form={editForm} component={false}>
          <DataManagementPaginatedTable
            data={meters}
            total={total}
            currentPage={searchParams.page}
            currentPageSize={searchParams.pageSize}
            loading={loading}
            onPageChange={(page, pageSize) => {
              setSearchParams({
                ...searchParams,
                page,
                pageSize
              });
            }}
            onSortChange={(sortBy, sortDirection) => {
              setSearchParams({
                ...searchParams,
                sortBy,
                sortDirection
              });
            }}
            columns={columns}
            rowKey="id"
            size="middle"
            bordered
          />
        </Form>
      </Card>

      {/* Create/Edit Modal */}
      <Modal
        title={selectedMeter ? 'Edit Water Meter' : 'Create Water Meter'}
        open={isCreateModalVisible}
        onCancel={() => {
          setIsCreateModalVisible(false);
          setSelectedMeter(null);
          form.resetFields();
        }}
        footer={null}
        width={1000}
        className="compact-modal"
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmitWaterMeter}
          className="compact-form"
        >
          {/* Key Information */}
          <div className="form-section">
            <Row gutter={12}>
              <Col span={6}>
                <Form.Item
                  name="serialNumber"
                  label="Serial Number"
                  rules={[{ required: true, message: 'Please enter serial number' }]}
                >
                  <Input placeholder="Enter serial number" disabled={!!selectedMeter} />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item
                  name="accountNumber"
                  label="Account Number"
                >
                  <Input placeholder="Enter account number" />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item
                  name="assetId"
                  label="Asset ID"
                >
                  <Input placeholder="Enter asset ID" />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item
                  name="meterType"
                  label="Meter Type"
                  rules={[{ required: true, message: 'Please select meter type' }]}
                >
                  <Select placeholder="Select meter type">
                    <Option value="Residential">Residential</Option>
                    <Option value="Commercial">Commercial</Option>
                    <Option value="Industrial">Industrial</Option>
                  </Select>
                </Form.Item>
              </Col>
            </Row>
          </div>

          {/* Basic Information */}
          <div className="form-section">
            <Row gutter={12}>
              <Col span={6}>
                <Form.Item
                  name="status"
                  label="Status"
                  rules={[{ required: true, message: 'Please select status' }]}
                >
                  <Select placeholder="Select status">
                    <Option value="Active">Active</Option>
                    <Option value="Inactive">Inactive</Option>
                    <Option value="Maintenance">Maintenance</Option>
                    <Option value="Replaced">Replaced</Option>
                  </Select>
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item
                  name="brand"
                  label="Brand"
                >
                  <Input placeholder="Enter brand" />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item
                  name="model"
                  label="Model"
                >
                  <Input placeholder="Enter model" />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item
                  name="installDate"
                  label="Install Date"
                >
                  <DatePicker style={{ width: '100%' }} />
                </Form.Item>
              </Col>
            </Row>
          </div>

          {/* Location Information */}
          <div className="form-section">
            <Row gutter={12}>
              <Col span={24}>
                <Form.Item
                  name="location"
                  label="Location"
                  rules={[{ required: true, message: 'Please enter location' }]}
                >
                  <AddressInput
                    placeholder="Enter address..."
                    showDebugInfo={true}
                    onChange={(addressData: AddressData) => {
                      // Update form fields with parsed address data
                      form.setFieldsValue({
                        location: addressData.location,
                        latitude: addressData.latitude,
                        longitude: addressData.longitude,
                        roadNumber: addressData.components?.roadNumber,
                        roadName: addressData.components?.roadName,
                        township: addressData.components?.township,
                        subArea: addressData.components?.subArea,
                      });
                    }}
                  />
                </Form.Item>

                {/* Hidden fields to store structured address data */}
                <Form.Item name="roadNumber" hidden>
                  <Input />
                </Form.Item>
                <Form.Item name="roadName" hidden>
                  <Input />
                </Form.Item>
                <Form.Item name="township" hidden>
                  <Input />
                </Form.Item>
                <Form.Item name="subArea" hidden>
                  <Input />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={12}>
              <Col span={12}>
                <Form.Item
                  name="latitude"
                  label="Latitude"
                >
                  <Input type="number" placeholder="Enter latitude" step="any" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="longitude"
                  label="Longitude"
                >
                  <Input type="number" placeholder="Enter longitude" step="any" />
                </Form.Item>
              </Col>
            </Row>
          </div>

          {/* Customer Information */}
          <div className="form-section">
            <Row gutter={12}>
              <Col span={24}>
                <Form.Item
                  name="customerName"
                  label="Customer Name"
                >
                  <Input placeholder="Enter customer name" />
                </Form.Item>
              </Col>
            </Row>
          </div>

          <Form.Item
            name="communicationMethod"
            label="Communication Method"
          >
            <Select placeholder="Select communication method">
              <Option value="LoRaWAN">LoRaWAN</Option>
              <Option value="NB-IoT">NB-IoT</Option>
              <Option value="WiFi">WiFi</Option>
              <Option value="GSM">GSM</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="notes"
            label="Notes"
          >
            <Input.TextArea rows={3} placeholder="Enter notes" />
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => {
                setIsCreateModalVisible(false);
                setSelectedMeter(null);
                form.resetFields();
              }}>
                Cancel
              </Button>
              <Button type="primary" htmlType="submit">
                {selectedMeter ? 'Update' : 'Create'}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* Detail Modal */}
      <Modal
        title="Water Meter Details"
        open={isDetailModalVisible}
        onCancel={() => setIsDetailModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setIsDetailModalVisible(false)}>
            Close
          </Button>
        ]}
        width={700}
        className="compact-modal"
      >
        {selectedMeter && (
          <div className="space-y-4">
            <Row gutter={16}>
              <Col span={12}>
                <strong>Serial Number:</strong> {selectedMeter.serialNumber}
              </Col>
              <Col span={12}>
                <strong>Asset ID:</strong> {selectedMeter.assetId || 'N/A'}
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={12}>
                <strong>Account Number:</strong> {selectedMeter.accountNumber || 'N/A'}
              </Col>
              <Col span={12}>
                <strong>Type:</strong> <Tag color="blue">{selectedMeter.meterType}</Tag>
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={12}>
                <strong>Status:</strong> <Tag color={getStatusColor(selectedMeter.status)}>{selectedMeter.status}</Tag>
              </Col>
              <Col span={12}>
                <strong>Location:</strong> {selectedMeter.location}
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={24}>
                <strong>Customer:</strong> {selectedMeter.customerName || 'N/A'}
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={12}>
                <strong>GPS Coordinates:</strong>
                {selectedMeter.latitude && selectedMeter.longitude ? (
                  <div style={{ marginTop: 4 }}>
                    <div style={{ fontSize: '12px', color: '#1890ff' }}>
                      📍 Lat: {selectedMeter.latitude.toFixed(6)}
                    </div>
                    <div style={{ fontSize: '12px', color: '#1890ff' }}>
                      Lng: {selectedMeter.longitude.toFixed(6)}
                    </div>
                  </div>
                ) : (
                  <span style={{ color: '#999' }}>No GPS coordinates</span>
                )}
              </Col>
              <Col span={12}>
                <strong>Battery Level:</strong>
                <Badge
                  color={getBatteryColor(selectedMeter.batteryLevel)}
                  text={selectedMeter.batteryLevel ? `${selectedMeter.batteryLevel}%` : 'N/A'}
                />
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={12}>
                <strong>Last Reading:</strong> {selectedMeter.lastRead ? `${selectedMeter.lastRead} m³` : 'N/A'}
              </Col>
              <Col span={12}>
                <strong>Last Reading Date:</strong> {selectedMeter.lastReadingDate ? format(new Date(selectedMeter.lastReadingDate), 'dd/MM/yyyy HH:mm') : 'Never'}
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={12}>
                <strong>Brand:</strong> {selectedMeter.brand || 'N/A'}
              </Col>
              <Col span={12}>
                <strong>Model:</strong> {selectedMeter.model || 'N/A'}
              </Col>
            </Row>

            {selectedMeter.notes && (
              <Row gutter={16}>
                <Col span={24}>
                  <strong>Notes:</strong> {selectedMeter.notes}
                </Col>
              </Row>
            )}
          </div>
        )}
      </Modal>

      {/* AMS Excel Import Modal */}
      <Modal
        title="AMS Excel Import"
        open={isAmsImportModalVisible}
        onCancel={() => setIsAmsImportModalVisible(false)}
        footer={null}
        width={700}
        className="compact-modal"
      >
        <div style={{ padding: '16px 0' }}>
          <div style={{ marginBottom: 24 }}>
            <h4>Import Requirements:</h4>
            <ul style={{ paddingLeft: 20, margin: 0 }}>
              <li>Excel file (.xlsx) with three sheets: "Rolleston Area 1 MASTER", "Data Check", "Rolleston Area 1 Enter miss"</li>
              <li>Master sheet must contain: Asset ID, Meter Number, Account Number, Road Name, Township</li>
              <li>Data Check sheet contains route assignments</li>
              <li>File will update existing meters and create new ones</li>
            </ul>
          </div>

          {importLoading && (
            <div style={{ marginBottom: 24 }}>
              <div style={{ marginBottom: 8 }}>Processing AMS Excel file...</div>
              <Progress percent={importProgress} showInfo={false} />
            </div>
          )}

          <Upload.Dragger
            name="file"
            multiple={false}
            accept=".xlsx,.xls"
            showUploadList={false}
            disabled={importLoading}
            beforeUpload={(file) => {
              handleAmsImport(file);
              return false; // Prevent default upload
            }}
          >
            <p className="ant-upload-drag-icon">
              <ImportOutlined style={{ fontSize: 48, color: importLoading ? '#ccc' : '#1890ff' }} />
            </p>
            <p className="ant-upload-text">
              {importLoading ? 'Processing...' : 'Click or drag AMS Excel file to this area'}
            </p>
            <p className="ant-upload-hint">
              Only .xlsx and .xls files are supported. The file should contain AMS meter export data with all three required sheets.
            </p>
          </Upload.Dragger>
          
          <div style={{ marginTop: 16, textAlign: 'center' }}>
            <Space>
              <Button 
                type="link" 
                disabled
                onClick={async () => {
                  try {
                    const blob = await waterMeterService.downloadAmsImportTemplate();
                    const url = window.URL.createObjectURL(blob);
                    const link = document.createElement('a');
                    link.href = url;
                    link.download = 'AMS_Import_Template.xlsx';
                    link.click();
                    window.URL.revokeObjectURL(url);
                  } catch (error) {
                    message.error('Failed to download template');
                  }
                }}
              >
                Download AMS Template (Coming Soon)
              </Button>
              <Button 
                type="link"
                onClick={() => setIsAmsImportModalVisible(false)}
                disabled={importLoading}
              >
                Cancel
              </Button>
            </Space>
          </div>
        </div>
      </Modal>

      {/* Import Modal */}
      <Modal
        title="Import Water Meters"
        open={isImportModalVisible}
        onCancel={() => setIsImportModalVisible(false)}
        footer={null}
        width={600}
        className="compact-modal"
      >
        <div className="text-center py-8">
          <Upload.Dragger
            name="file"
            multiple={false}
            accept=".csv,.xlsx,.xls"
            showUploadList={false}
          >
            <p className="ant-upload-drag-icon">
              <ImportOutlined />
            </p>
            <p className="ant-upload-text">Click or drag file to this area to upload</p>
            <p className="ant-upload-hint">
              Support for CSV, Excel files. Ensure your file follows the required format.
            </p>
          </Upload.Dragger>
          
          <div className="mt-4">
            <Button type="link">Download Template</Button>
          </div>
        </div>
      </Modal>


    </div>
  );
}