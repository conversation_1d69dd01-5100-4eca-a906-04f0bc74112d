'use client';

import React, { useState, useEffect, useCallback } from 'react';
import {
  Table, Button, Modal, Form, Input, Select, Card, Space, 
  Tag, Badge, message, Row, Col, Statistic, Alert, DatePicker,
  Timeline, Tooltip, Popconfirm, Avatar, List, Tabs, Progress
} from 'antd';
import {
  ExclamationCircleOutlined, ClockCircleOutlined, UserOutlined,
  SendOutlined, PhoneOutlined, MailOutlined, ReloadOutlined,
  WarningOutlined, BellOutlined, CalendarOutlined, CheckCircleOutlined,
  SettingOutlined
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import dayjs from 'dayjs';
import { 
  taskService, OverdueTaskDto, TaskListDto, TaskSearchDto 
} from '@/services/task.service';

const { Option } = Select;
const { TabPane } = Tabs;
const { TextArea } = Input;

interface EscalationRule {
  id: string;
  name: string;
  triggerDays: number;
  actions: string[];
  recipients: string[];
  active: boolean;
}

interface NotificationLog {
  id: string;
  taskId: number;
  taskName: string;
  type: 'reminder' | 'escalation' | 'alert';
  recipient: string;
  method: 'email' | 'sms' | 'system';
  status: 'sent' | 'failed' | 'pending';
  sentAt: string;
  message: string;
}

const OverdueManagement: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [overdueTasks, setOverdueTasks] = useState<OverdueTaskDto[]>([]);
  const [allTasks, setAllTasks] = useState<TaskListDto[]>([]);
  const [selectedTasks, setSelectedTasks] = useState<number[]>([]);
  const [escalationModalVisible, setEscalationModalVisible] = useState(false);
  const [notificationModalVisible, setNotificationModalVisible] = useState(false);
  const [activeTab, setActiveTab] = useState('overdue');

  // Forms
  const [escalationForm] = Form.useForm();
  const [notificationForm] = Form.useForm();

  // Mock data for escalation rules
  const [escalationRules] = useState<EscalationRule[]>([
    {
      id: '1',
      name: 'Critical Tasks - 1 Day Overdue',
      triggerDays: 1,
      actions: ['Email Manager', 'SMS Alert'],
      recipients: ['<EMAIL>', '+1234567890'],
      active: true
    },
    {
      id: '2', 
      name: 'High Priority - 3 Days Overdue',
      triggerDays: 3,
      actions: ['Email Supervisor', 'System Notification'],
      recipients: ['<EMAIL>'],
      active: true
    },
    {
      id: '3',
      name: 'All Tasks - 7 Days Overdue', 
      triggerDays: 7,
      actions: ['Email Department Head'],
      recipients: ['<EMAIL>'],
      active: false
    }
  ]);

  // Mock notification logs
  const [notificationLogs] = useState<NotificationLog[]>([
    {
      id: '1',
      taskId: 1,
      taskName: 'Water Meter Reading - Zone A',
      type: 'reminder',
      recipient: '<EMAIL>',
      method: 'email',
      status: 'sent',
      sentAt: dayjs().subtract(2, 'hours').toISOString(),
      message: 'Task overdue reminder'
    },
    {
      id: '2', 
      taskId: 2,
      taskName: 'Meter Inspection - Building B',
      type: 'escalation',
      recipient: '<EMAIL>',
      method: 'email',
      status: 'sent',
      sentAt: dayjs().subtract(1, 'day').toISOString(),
      message: 'Task escalated to management'
    }
  ]);

  // Statistics
  const [overdueStats, setOverdueStats] = useState({
    totalOverdue: 0,
    criticalOverdue: 0,
    avgDaysOverdue: 0,
    longestOverdue: 0
  });

  // Load overdue tasks
  const loadOverdueTasks = useCallback(async () => {
    setLoading(true);
    try {
      const overdue = await taskService.getOverdueTasks();
      setOverdueTasks(overdue);

      // Load all tasks for context
      const allResult = await taskService.getTasks({ page: 1, pageSize: 1000 });
      setAllTasks(allResult.tasks);

      // Calculate statistics
      const critical = overdue.filter(task => task.priority === 'Critical');
      const avgDays = overdue.length > 0 
        ? overdue.reduce((sum, task) => sum + task.daysOverdue, 0) / overdue.length 
        : 0;
      const longestOverdue = overdue.length > 0 
        ? Math.max(...overdue.map(task => task.daysOverdue)) 
        : 0;

      setOverdueStats({
        totalOverdue: overdue.length,
        criticalOverdue: critical.length,
        avgDaysOverdue: Math.round(avgDays),
        longestOverdue
      });

    } catch (error) {
      message.error('Failed to load overdue tasks');
      console.error('Load overdue tasks error:', error);
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    loadOverdueTasks();
  }, [loadOverdueTasks]);

  // Send reminder
  const sendReminder = async (taskIds: number[]) => {
    try {
      // Mock API call - in real app, this would call a notification service
      message.success(`Reminders sent for ${taskIds.length} tasks`);
      loadOverdueTasks();
    } catch (error) {
      message.error('Failed to send reminders');
      console.error('Send reminder error:', error);
    }
  };

  // Escalate tasks
  const escalateTasks = async (values: any) => {
    try {
      // Mock escalation logic
      message.success(`Tasks escalated to ${values.escalateTo}`);
      setEscalationModalVisible(false);
      escalationForm.resetFields();
      setSelectedTasks([]);
      loadOverdueTasks();
    } catch (error) {
      message.error('Failed to escalate tasks');
      console.error('Escalation error:', error);
    }
  };

  // Send notification
  const sendNotification = async (values: any) => {
    try {
      // Mock notification sending
      message.success('Notifications sent successfully');
      setNotificationModalVisible(false);
      notificationForm.resetFields();
    } catch (error) {
      message.error('Failed to send notifications');
      console.error('Send notification error:', error);
    }
  };

  // Extend deadline
  const extendDeadline = async (taskId: number, newDate: string) => {
    try {
      // Mock deadline extension
      message.success('Deadline extended successfully');
      loadOverdueTasks();
    } catch (error) {
      message.error('Failed to extend deadline');
      console.error('Extend deadline error:', error);
    }
  };

  // Get priority color
  const getPriorityColor = (priority: string) => {
    const colors = {
      'Low': 'gray',
      'Medium': 'blue', 
      'High': 'orange',
      'Critical': 'red'
    };
    return colors[priority as keyof typeof colors] || 'default';
  };

  // Get overdue severity
  const getOverdueSeverity = (daysOverdue: number) => {
    if (daysOverdue >= 7) return { color: 'red', text: 'Severe' };
    if (daysOverdue >= 3) return { color: 'orange', text: 'High' };
    if (daysOverdue >= 1) return { color: 'yellow', text: 'Medium' };
    return { color: 'gray', text: 'Low' };
  };

  // Overdue tasks columns
  const overdueColumns: ColumnsType<OverdueTaskDto> = [
    {
      title: 'Select',
      key: 'select',
      width: 60,
      render: (_, record) => (
        <input
          type="checkbox"
          checked={selectedTasks.includes(record.id)}
          onChange={(e) => {
            if (e.target.checked) {
              setSelectedTasks([...selectedTasks, record.id]);
            } else {
              setSelectedTasks(selectedTasks.filter(id => id !== record.id));
            }
          }}
        />
      ),
    },
    {
      title: 'Task',
      key: 'task',
      render: (_, record) => (
        <div>
          <div className="font-medium">{record.name}</div>
          <div className="text-gray-500 text-sm">{record.location}</div>
        </div>
      ),
    },
    {
      title: 'Priority',
      dataIndex: 'priority',
      key: 'priority',
      render: (priority: string) => (
        <Tag color={getPriorityColor(priority)}>{priority}</Tag>
      ),
    },
    {
      title: 'Assigned To',
      dataIndex: 'assignedTo',
      key: 'assignedTo',
      render: (assignedTo: string) => (
        <div className="flex items-center">
          <Avatar size="small" icon={<UserOutlined />} className="mr-2" />
          {assignedTo || 'Unassigned'}
        </div>
      ),
    },
    {
      title: 'Due Date',
      dataIndex: 'dueDate',
      key: 'dueDate',
      render: (date: string) => (
        <div className="text-red-500">
          <ClockCircleOutlined className="mr-1" />
          {dayjs(date).format('YYYY-MM-DD')}
        </div>
      ),
    },
    {
      title: 'Days Overdue',
      dataIndex: 'daysOverdue',
      key: 'daysOverdue',
      render: (days: number) => {
        const severity = getOverdueSeverity(days);
        return <Badge color={severity.color} text={`${days} days`} />;
      },
      sorter: (a, b) => a.daysOverdue - b.daysOverdue,
      defaultSortOrder: 'descend',
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record) => (
        <Space>
          <Tooltip title="Send Reminder">
            <Button
              type="text"
              icon={<BellOutlined />}
              onClick={() => sendReminder([record.id])}
            />
          </Tooltip>
          <Tooltip title="Extend Deadline">
            <Button
              type="text"
              icon={<CalendarOutlined />}
              onClick={() => {
                // Open date picker modal
                const newDate = prompt('Enter new deadline (YYYY-MM-DD):');
                if (newDate) {
                  extendDeadline(record.id, newDate);
                }
              }}
            />
          </Tooltip>
          <Tooltip title="Contact Assignee">
            <Button type="text" icon={<PhoneOutlined />} />
          </Tooltip>
        </Space>
      ),
    },
  ];

  // Escalation rules columns
  const rulesColumns: ColumnsType<EscalationRule> = [
    {
      title: 'Rule Name',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: 'Trigger',
      dataIndex: 'triggerDays',
      key: 'triggerDays',
      render: (days: number) => `${days} days overdue`,
    },
    {
      title: 'Actions',
      dataIndex: 'actions',
      key: 'actions',
      render: (actions: string[]) => (
        <div>
          {actions.map(action => (
            <Tag key={action}>{action}</Tag>
          ))}
        </div>
      ),
    },
    {
      title: 'Recipients',
      dataIndex: 'recipients',
      key: 'recipients',
      render: (recipients: string[]) => (
        <div className="text-sm text-gray-500">
          {recipients.length} recipients
        </div>
      ),
    },
    {
      title: 'Status',
      dataIndex: 'active',
      key: 'active',
      render: (active: boolean) => (
        <Tag color={active ? 'green' : 'gray'}>
          {active ? 'Active' : 'Inactive'}
        </Tag>
      ),
    },
  ];

  // Notification logs columns
  const logsColumns: ColumnsType<NotificationLog> = [
    {
      title: 'Task',
      dataIndex: 'taskName',
      key: 'taskName',
    },
    {
      title: 'Type',
      dataIndex: 'type',
      key: 'type',
      render: (type: string) => {
        const colors = {
          'reminder': 'blue',
          'escalation': 'orange', 
          'alert': 'red'
        };
        return <Tag color={colors[type as keyof typeof colors]}>{type}</Tag>;
      },
    },
    {
      title: 'Recipient',
      dataIndex: 'recipient',
      key: 'recipient',
    },
    {
      title: 'Method',
      dataIndex: 'method',
      key: 'method',
      render: (method: string) => {
        const icons = {
          'email': <MailOutlined />,
          'sms': <PhoneOutlined />,
          'system': <BellOutlined />
        };
        return (
          <span>
            {icons[method as keyof typeof icons]} {method}
          </span>
        );
      },
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => {
        const colors = {
          'sent': 'green',
          'failed': 'red',
          'pending': 'orange'
        };
        return <Tag color={colors[status as keyof typeof colors]}>{status}</Tag>;
      },
    },
    {
      title: 'Sent At',
      dataIndex: 'sentAt',
      key: 'sentAt',
      render: (date: string) => dayjs(date).format('MM-DD HH:mm'),
    },
  ];

  return (
    <div className="p-4" style={{ height: '100vh', overflow: 'hidden' }}>
      <div className="mb-3">
        {/* Compressed Statistics Cards - 80px height */}
        <Row gutter={12} className="mb-3" style={{ height: '80px' }}>
          <Col span={6}>
            <Card size="small" style={{ height: '100%' }} styles={{ body: { padding: '8px 12px' } }}>
              <Statistic
                title="Total Overdue"
                value={overdueStats.totalOverdue}
                valueStyle={{ color: '#f5222d', fontSize: '18px' }}
                prefix={<ExclamationCircleOutlined />}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card size="small" style={{ height: '100%' }} styles={{ body: { padding: '8px 12px' } }}>
              <Statistic
                title="Critical Priority"
                value={overdueStats.criticalOverdue}
                valueStyle={{ color: '#ff4d4f', fontSize: '18px' }}
                prefix={<WarningOutlined />}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card size="small" style={{ height: '100%' }} styles={{ body: { padding: '8px 12px' } }}>
              <Statistic
                title="Average Days Overdue"
                value={overdueStats.avgDaysOverdue}
                valueStyle={{ color: '#fa8c16', fontSize: '18px' }}
                prefix={<ClockCircleOutlined />}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card size="small" style={{ height: '100%' }} styles={{ body: { padding: '8px 12px' } }}>
              <Statistic
                title="Longest Overdue"
                value={overdueStats.longestOverdue}
                suffix="days"
                valueStyle={{ color: '#ff7875', fontSize: '18px' }}
                prefix={<ExclamationCircleOutlined />}
              />
            </Card>
          </Col>
        </Row>
      </div>

      <Tabs 
        activeKey={activeTab} 
        onChange={setActiveTab} 
        size="small"
        style={{ height: 'calc(100vh - 140px)' }}
        tabBarStyle={{ marginBottom: '8px' }}
      >
        <TabPane tab={
          <span>
            <ExclamationCircleOutlined />
            Overdue Tasks ({overdueStats.totalOverdue})
          </span>
        } key="overdue">
          <Card 
            size="small" 
            style={{ height: '100%' }}
            styles={{ body: { padding: '12px', height: 'calc(100% - 40px)', overflow: 'hidden' } }}
          >
            <div className="flex justify-between items-center mb-3">
              <div>
                <h3 className="text-base font-medium mb-1">Overdue Tasks</h3>
                <p className="text-gray-500 text-sm">Tasks that have passed their due date</p>
              </div>
              <Space>
                <Button
                  size="small"
                  disabled={selectedTasks.length === 0}
                  onClick={() => {
                    if (overdueTasks.length > 0) {
                      const allIds = overdueTasks.map(task => task.id);
                      setSelectedTasks(selectedTasks.length === allIds.length ? [] : allIds);
                    }
                  }}
                >
                  {selectedTasks.length === overdueTasks.length ? 'Deselect All' : 'Select All'}
                </Button>
                <Button
                  type="primary"
                  icon={<BellOutlined />}
                  disabled={selectedTasks.length === 0}
                  onClick={() => sendReminder(selectedTasks)}
                  size="small"
                >
                  Send Reminders ({selectedTasks.length})
                </Button>
                <Button
                  danger
                  icon={<SendOutlined />}
                  disabled={selectedTasks.length === 0}
                  onClick={() => setEscalationModalVisible(true)}
                  size="small"
                >
                  Escalate ({selectedTasks.length})
                </Button>
                <Button
                  icon={<MailOutlined />}
                  onClick={() => setNotificationModalVisible(true)}
                  size="small"
                >
                  Send Notification
                </Button>
                <Button
                  icon={<ReloadOutlined />}
                  onClick={loadOverdueTasks}
                  size="small"
                >
                  Refresh
                </Button>
              </Space>
            </div>

            <Table
              columns={overdueColumns}
              dataSource={overdueTasks}
              rowKey="id"
              loading={loading}
              size="small"
              scroll={{ y: 'calc(100vh - 320px)' }}
              pagination={{
                size: 'small',
                pageSize: 15,
                showSizeChanger: true,
                showQuickJumper: true,
                pageSizeOptions: ['10', '15', '20', '30'],
                showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} overdue tasks`,
              }}
            />
          </Card>
        </TabPane>

        <TabPane tab={
          <span>
            <SettingOutlined />
            Escalation Rules
          </span>
        } key="rules">
          <Card 
            title="Escalation Rules Configuration" 
            size="small" 
            style={{ height: '100%' }}
            styles={{ body: { padding: '12px', height: 'calc(100% - 40px)', overflow: 'hidden' } }}
          >
            <Table
              columns={rulesColumns}
              dataSource={escalationRules}
              rowKey="id"
              size="small"
              scroll={{ y: 'calc(100vh - 260px)' }}
              pagination={{
                size: 'small',
                pageSize: 15,
                showSizeChanger: true,
              }}
            />
          </Card>
        </TabPane>

        <TabPane tab={
          <span>
            <BellOutlined />
            Notification Logs
          </span>
        } key="logs">
          <Card 
            title="Notification History" 
            size="small" 
            style={{ height: '100%' }}
            styles={{ body: { padding: '12px', height: 'calc(100% - 40px)', overflow: 'hidden' } }}
          >
            <Table
              columns={logsColumns}
              dataSource={notificationLogs}
              rowKey="id"
              size="small"
              scroll={{ y: 'calc(100vh - 260px)' }}
              pagination={{
                size: 'small',
                pageSize: 15,
                showSizeChanger: true,
                showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} notifications`,
              }}
            />
          </Card>
        </TabPane>
      </Tabs>

      {/* Escalation Modal */}
      <Modal
        title="Escalate Tasks"
        visible={escalationModalVisible}
        onCancel={() => {
          setEscalationModalVisible(false);
          escalationForm.resetFields();
        }}
        footer={null}
        width={600}
      >
        <div className="mb-4">
          <p>You are about to escalate <strong>{selectedTasks.length}</strong> overdue tasks.</p>
        </div>

        <Form form={escalationForm} onFinish={escalateTasks} layout="vertical">
          <Form.Item
            name="escalateTo"
            label="Escalate To"
            rules={[{ required: true, message: 'Please select escalation target' }]}
          >
            <Select placeholder="Select escalation target">
              <Option value="supervisor">Direct Supervisor</Option>
              <Option value="manager">Department Manager</Option>
              <Option value="director">Director</Option>
              <Option value="custom">Custom Recipients</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="priority"
            label="Escalation Priority"
            rules={[{ required: true, message: 'Please select priority' }]}
          >
            <Select placeholder="Select escalation priority">
              <Option value="normal">Normal</Option>
              <Option value="urgent">Urgent</Option>
              <Option value="critical">Critical</Option>
            </Select>
          </Form.Item>

          <Form.Item name="reason" label="Escalation Reason">
            <TextArea 
              rows={3} 
              placeholder="Enter reason for escalation"
            />
          </Form.Item>

          <div className="flex justify-end space-x-2">
            <Button
              onClick={() => {
                setEscalationModalVisible(false);
                escalationForm.resetFields();
              }}
            >
              Cancel
            </Button>
            <Button type="primary" danger htmlType="submit">
              Escalate Tasks
            </Button>
          </div>
        </Form>
      </Modal>

      {/* Notification Modal */}
      <Modal
        title="Send Notification"
        visible={notificationModalVisible}
        onCancel={() => {
          setNotificationModalVisible(false);
          notificationForm.resetFields();
        }}
        footer={null}
        width={600}
      >
        <Form form={notificationForm} onFinish={sendNotification} layout="vertical">
          <Form.Item
            name="recipients"
            label="Recipients"
            rules={[{ required: true, message: 'Please select recipients' }]}
          >
            <Select mode="multiple" placeholder="Select recipients">
              <Option value="assignees">Task Assignees</Option>
              <Option value="supervisors">Supervisors</Option>
              <Option value="managers">Managers</Option>
              <Option value="all">All Users</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="method"
            label="Notification Method"
            rules={[{ required: true, message: 'Please select method' }]}
          >
            <Select placeholder="Select notification method">
              <Option value="email">Email</Option>
              <Option value="sms">SMS</Option>
              <Option value="system">System Notification</Option>
              <Option value="all">All Methods</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="subject"
            label="Subject"
            rules={[{ required: true, message: 'Please enter subject' }]}
          >
            <Input placeholder="Enter notification subject" />
          </Form.Item>

          <Form.Item
            name="message"
            label="Message"
            rules={[{ required: true, message: 'Please enter message' }]}
          >
            <TextArea 
              rows={4} 
              placeholder="Enter notification message"
            />
          </Form.Item>

          <div className="flex justify-end space-x-2">
            <Button
              onClick={() => {
                setNotificationModalVisible(false);
                notificationForm.resetFields();
              }}
            >
              Cancel
            </Button>
            <Button type="primary" htmlType="submit">
              Send Notification
            </Button>
          </div>
        </Form>
      </Modal>
    </div>
  );
};

export default OverdueManagement; 