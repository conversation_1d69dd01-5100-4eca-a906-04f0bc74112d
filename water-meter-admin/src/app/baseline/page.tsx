'use client';

import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Table, 
  Button, 
  Input, 
  Select, 
  DatePicker, 
  Space, 
  Tag, 
  Dropdown, 
  Modal, 
  Form, 
  InputNumber, 
  message, 
  Upload,
  Progress,
  Statistic,
  Row,
  Col,
  Divider,
  Checkbox,
  Tooltip
} from 'antd';
import { 
  SearchOutlined, 
  DownloadOutlined, 
  UploadOutlined, 
  ReloadOutlined,
  ExportOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  WarningOutlined,
  EyeOutlined,
  EditOutlined,
  DeleteOutlined,
  MoreOutlined,
  FilterOutlined,
  FileExcelOutlined
} from '@ant-design/icons';
import dayjs from 'dayjs';

import { baselineService } from '@/services/baseline.service';
import { DataManagementPaginatedTable } from '@/components/PaginatedTable';
import ImportWizard from './components/ImportWizard';
import { 
  BaselineRecordListDto, 
  BaselineSearchDto, 
  CreateBaselineRecordDto,
  BaselineStatisticsDto,
  BaselineRecordDto,
  BaselineValidationDto,
  BaselineCorrectionDto,
  BaselineImportResultDto,
  BatchValidationDto,
  BatchValidationResultDto
} from '@/types/baseline';

const { RangePicker } = DatePicker;
const { Option } = Select;

export default function BaselinePage() {
  // State management
  const [baselines, setBaselines] = useState<BaselineRecordListDto[]>([]);
  const [statistics, setStatistics] = useState<BaselineStatisticsDto | null>(null);
  const [loading, setLoading] = useState(false);
  const [searchParams, setSearchParams] = useState<BaselineSearchDto>({
    page: 1,
    pageSize: 10,
    sortBy: 'baselineDate',
    sortDirection: 'desc'
  });
  const [totalCount, setTotalCount] = useState(0);
  const [selectedRowKeys, setSelectedRowKeys] = useState<number[]>([]);

  // Modal states
  const [isCreateModalVisible, setIsCreateModalVisible] = useState(false);
  const [isDetailModalVisible, setIsDetailModalVisible] = useState(false);
  const [isImportModalVisible, setIsImportModalVisible] = useState(false);
  const [isSdcImportModalVisible, setIsSdcImportModalVisible] = useState(false);
  const [isValidationModalVisible, setIsValidationModalVisible] = useState(false);
  const [isCorrectionModalVisible, setIsCorrectionModalVisible] = useState(false);
  
  // Selected data
  const [selectedBaseline, setSelectedBaseline] = useState<BaselineRecordDto | null>(null);
  const [importResult, setImportResult] = useState<BaselineImportResultDto | null>(null);
  
  // Forms
  const [form] = Form.useForm();
  const [validationForm] = Form.useForm();
  const [correctionForm] = Form.useForm();

  // Import states
  const [sdcImportLoading, setSdcImportLoading] = useState(false);
  const [sdcImportProgress, setSdcImportProgress] = useState(0);

  // Load data on component mount
  useEffect(() => {
    loadBaselines();
    loadStatistics();
  }, [searchParams]);

  // Load baselines
  const loadBaselines = async () => {
    try {
      setLoading(true);
      const response = await baselineService.getBaselineRecords(searchParams);
      setBaselines(response.data);
      setTotalCount(response.totalCount);
    } catch (error) {
      message.error('Failed to load baseline records');
      console.error('Error loading baselines:', error);
    } finally {
      setLoading(false);
    }
  };

  // Load statistics
  const loadStatistics = async () => {
    try {
      const stats = await baselineService.getBaselineStatistics();
      setStatistics(stats);
    } catch (error) {
      console.error('Error loading statistics:', error);
    }
  };

  // Handle search
  const handleSearch = (values: any) => {
    const newSearchParams = {
      ...searchParams,
      ...values,
      page: 1
    };
    
    if (values.dateRange && values.dateRange.length === 2) {
      newSearchParams.baselineDateFrom = values.dateRange[0].format('YYYY-MM-DD');
      newSearchParams.baselineDateTo = values.dateRange[1].format('YYYY-MM-DD');
    }
    
    setSearchParams(newSearchParams);
  };

  // Reset search
  const handleReset = () => {
    setSearchParams({
      page: 1,
      pageSize: 10,
      sortBy: 'baselineDate',
      sortDirection: 'desc'
    });
  };

  // Handle pagination change
  const handlePageChange = (page: number, pageSize: number) => {
    setSearchParams({
      ...searchParams,
      page,
      pageSize
    });
  };

  // Handle sort change
  const handleSortChange = (sortBy: string, sortDirection: 'asc' | 'desc') => {
    setSearchParams({
      ...searchParams,
      sortBy,
      sortDirection
    });
  };

  // Create new baseline
  const handleCreate = async (values: any) => {
    try {
      const createDto: CreateBaselineRecordDto = {
        ...values,
        baselineDate: values.baselineDate.toDate()
      };
      
      await baselineService.createBaselineRecord(createDto);
      message.success('Baseline record created successfully');
      setIsCreateModalVisible(false);
      form.resetFields();
      loadBaselines();
      loadStatistics();
    } catch (error) {
      message.error('Failed to create baseline record');
      console.error('Error creating baseline:', error);
    }
  };

  // View baseline details
  const handleViewDetails = async (baseline: BaselineRecordListDto) => {
    try {
      const details = await baselineService.getBaselineRecordById(baseline.id);
      setSelectedBaseline(details);
      setIsDetailModalVisible(true);
    } catch (error) {
      message.error('Failed to load baseline details');
      console.error('Error loading details:', error);
    }
  };

  // Delete baseline
  const handleDelete = async (id: number) => {
    try {
      await baselineService.deleteBaselineRecord(id);
      message.success('Baseline record deleted successfully');
      loadBaselines();
      loadStatistics();
    } catch (error) {
      message.error('Failed to delete baseline record');
      console.error('Error deleting baseline:', error);
    }
  };

  // Validate baseline
  const handleValidate = async (baseline: BaselineRecordListDto) => {
    setSelectedBaseline(baseline as any);
    validationForm.setFieldsValue({
      baselineId: baseline.id,
      isValid: true,
      validatedBy: 'Admin'
    });
    setIsValidationModalVisible(true);
  };

  const submitValidation = async (values: BaselineValidationDto) => {
    try {
      await baselineService.validateBaseline(values);
      message.success('Baseline validated successfully');
      setIsValidationModalVisible(false);
      validationForm.resetFields();
      loadBaselines();
      loadStatistics();
    } catch (error) {
      message.error('Failed to validate baseline');
      console.error('Error validating baseline:', error);
    }
  };

  // Correct baseline
  const handleCorrect = async (baseline: BaselineRecordListDto) => {
    setSelectedBaseline(baseline as any);
    correctionForm.setFieldsValue({
      baselineId: baseline.id,
      newBaselineValue: baseline.baselineValue
    });
    setIsCorrectionModalVisible(true);
  };

  const submitCorrection = async (values: BaselineCorrectionDto) => {
    try {
      await baselineService.correctBaseline(values, 'Admin');
      message.success('Baseline corrected successfully');
      setIsCorrectionModalVisible(false);
      correctionForm.resetFields();
      loadBaselines();
      loadStatistics();
    } catch (error) {
      message.error('Failed to correct baseline');
      console.error('Error correcting baseline:', error);
    }
  };

  // Export baselines
  const handleExport = async () => {
    try {
      const blob = await baselineService.exportBaselines(searchParams);
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `baseline_export_${dayjs().format('YYYYMMDD_HHmmss')}.xlsx`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      message.success('Baseline data exported successfully');
    } catch (error) {
      message.error('Failed to export baseline data');
      console.error('Error exporting data:', error);
    }
  };

  // Download template
  const handleDownloadTemplate = async () => {
    try {
      const blob = await baselineService.downloadExcelTemplate();
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `baseline_import_template_${dayjs().format('YYYYMMDD')}.xlsx`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      message.success('Template downloaded successfully');
    } catch (error) {
      message.error('Failed to download template');
      console.error('Error downloading template:', error);
    }
  };

  // Batch validate
  const handleBatchValidate = async (isValid: boolean) => {
    if (selectedRowKeys.length === 0) {
      message.warning('Please select baseline records to validate');
      return;
    }

    try {
      const batchDto: BatchValidationDto = {
        baselineIds: selectedRowKeys,
        isValid,
        validatedBy: 'Admin',
        validationNotes: isValid ? 'Batch validation - approved' : 'Batch validation - rejected'
      };

      const result = await baselineService.batchValidateBaselines(batchDto);
      
      if (result.successful > 0) {
        message.success(`Successfully validated ${result.successful} baseline records`);
      }
      
      if (result.failed > 0) {
        message.error(`Failed to validate ${result.failed} baseline records`);
      }

      setSelectedRowKeys([]);
      loadBaselines();
      loadStatistics();
    } catch (error) {
      message.error('Failed to batch validate baselines');
      console.error('Error batch validating:', error);
    }
  };

  // Handle SDC import (similar to AMS import in water management)
  const handleSdcImport = async (file: File) => {
    setSdcImportLoading(true);
    setSdcImportProgress(0);
    
    try {
      // Simulate progress
      const progressInterval = setInterval(() => {
        setSdcImportProgress(prev => Math.min(prev + 10, 90));
      }, 500);

      const result: BaselineImportResultDto = await baselineService.importFromSdcFile(file);
      
      clearInterval(progressInterval);
      setSdcImportProgress(100);
      
      if (result.successfulRows > 0) {
        message.success({
          content: (
            <div>
              <div><strong>SDC Import Successful!</strong></div>
              <div>Processed: {result.totalRows} rows, Success: {result.successfulRows}, Failed: {result.failedRows}</div>
              {result.failedRecords && result.failedRecords.length > 0 && (
                <div style={{ marginTop: 8, fontSize: '12px' }}>
                  <strong>Failed Records:</strong>
                  <ul style={{ margin: 0, paddingLeft: 16 }}>
                    {result.failedRecords.slice(0, 3).map((record: any, index: number) => (
                      <li key={index}>Row {record.rowNumber}: {record.validationErrors?.[0] || 'Unknown error'}</li>
                    ))}
                    {result.failedRecords.length > 3 && <li>... and {result.failedRecords.length - 3} more</li>}
                  </ul>
                </div>
              )}
            </div>
          ),
          duration: 10
        });
        setIsSdcImportModalVisible(false);
        loadBaselines();
        loadStatistics();
      } else {
        message.error({
          content: (
            <div>
              <div><strong>SDC Import Failed</strong></div>
              <div>Total rows: {result.totalRows}, Failed: {result.failedRows}</div>
              <div>Failed Records:</div>
              <ul style={{ margin: 0, paddingLeft: 16 }}>
                {result.failedRecords && result.failedRecords.slice(0, 3).map((record: any, index: number) => (
                  <li key={index}>Row {record.rowNumber}: {record.validationErrors?.[0] || 'Unknown error'}</li>
                ))}
                {result.failedRecords && result.failedRecords.length > 3 && <li>... and {result.failedRecords.length - 3} more</li>}
              </ul>
            </div>
          ),
          duration: 15
        });
      }
    } catch (error) {
      message.error('Failed to import SDC file');
      console.error('Error importing SDC file:', error);
    } finally {
      setSdcImportLoading(false);
      setSdcImportProgress(0);
    }
  };

  // Statistics Cards Component
  const StatisticsCards = () => (
    <Row gutter={16} style={{ marginBottom: 24 }}>
      <Col span={6}>
        <Card>
          <Statistic
            title="Total Baselines"
            value={statistics?.totalBaselines || 0}
            valueStyle={{ color: '#1890ff' }}
            prefix={<CheckCircleOutlined />}
          />
        </Card>
      </Col>
      <Col span={6}>
        <Card>
          <Statistic
            title="Unvalidated"
            value={statistics?.unvalidatedBaselines || 0}
            valueStyle={{ color: '#faad14' }}
            prefix={<ExclamationCircleOutlined />}
          />
        </Card>
      </Col>
      <Col span={6}>
        <Card>
          <Statistic
            title="Anomalous"
            value={statistics?.anomalousBaselines || 0}
            valueStyle={{ color: '#ff4d4f' }}
            prefix={<WarningOutlined />}
          />
        </Card>
      </Col>
      <Col span={6}>
        <Card>
          <Statistic
            title="Monthly New"
            value={statistics?.monthlyBaselines || 0}
            valueStyle={{ color: '#52c41a' }}
            prefix={<CheckCircleOutlined />}
          />
        </Card>
      </Col>
    </Row>
  );

  // Search Form Component
  const SearchForm = () => (
    <Card style={{ marginBottom: 16 }}>
      <Form
        layout="inline"
        onFinish={handleSearch}
        style={{ width: '100%' }}
      >
        <Form.Item name="serialNumber" style={{ marginBottom: 8 }}>
          <Input
            placeholder="Meter Serial Number"
            prefix={<SearchOutlined />}
            style={{ width: 200 }}
          />
        </Form.Item>
        
        <Form.Item name="location" style={{ marginBottom: 8 }}>
          <Input
            placeholder="Location"
            style={{ width: 150 }}
          />
        </Form.Item>
        
        <Form.Item name="baselineType" style={{ marginBottom: 8 }}>
          <Select placeholder="Baseline Type" style={{ width: 120 }} allowClear>
            <Option value="Initial">Initial</Option>
            <Option value="Periodic">Periodic</Option>
            <Option value="Correction">Correction</Option>
            <Option value="Migration">Migration</Option>
          </Select>
        </Form.Item>
        
        <Form.Item name="status" style={{ marginBottom: 8 }}>
          <Select placeholder="Status" style={{ width: 120 }} allowClear>
            <Option value="Active">Active</Option>
            <Option value="Superseded">Superseded</Option>
            <Option value="Invalid">Invalid</Option>
          </Select>
        </Form.Item>
        
        <Form.Item name="dataSource" style={{ marginBottom: 8 }}>
          <Select placeholder="Data Source" style={{ width: 120 }} allowClear>
            <Option value="Manual">Manual</Option>
            <Option value="CSV">CSV</Option>
            <Option value="Excel">Excel</Option>
          </Select>
        </Form.Item>
        
        <Form.Item name="dateRange" style={{ marginBottom: 8 }}>
          <RangePicker style={{ width: 240 }} />
        </Form.Item>
        
        <Form.Item name="isValidated" style={{ marginBottom: 8 }}>
          <Select placeholder="Validation Status" style={{ width: 120 }} allowClear>
            <Option value={true}>Validated</Option>
            <Option value={false}>Unvalidated</Option>
          </Select>
        </Form.Item>
        
        <Form.Item name="isAnomalous" style={{ marginBottom: 8 }}>
          <Select placeholder="Anomaly Status" style={{ width: 120 }} allowClear>
            <Option value={true}>Anomalous</Option>
            <Option value={false}>Normal</Option>
          </Select>
        </Form.Item>
        
        <Form.Item style={{ marginBottom: 8 }}>
          <Space>
            <Button type="primary" htmlType="submit" icon={<SearchOutlined />}>
              Search
            </Button>
            <Button onClick={handleReset} icon={<ReloadOutlined />}>
              Reset
            </Button>
          </Space>
        </Form.Item>
      </Form>
    </Card>
  );

  // Table columns
  const columns = [
    {
      title: 'Meter Serial',
      dataIndex: 'meterSerialNumber',
      key: 'meterSerialNumber',
      sorter: true,
      render: (text: string) => (
        <span style={{ fontWeight: 'bold', color: '#1890ff' }}>{text}</span>
      )
    },
    {
      title: 'Location',
      dataIndex: 'meterLocation',
      key: 'meterLocation',
      ellipsis: true
    },
    {
      title: 'Baseline Date',
      dataIndex: 'baselineDate',
      key: 'baselineDate',
      sorter: true,
      render: (date: string) => dayjs(date).format('YYYY-MM-DD')
    },
    {
      title: 'Baseline Value',
      dataIndex: 'baselineValue',
      key: 'baselineValue',
      sorter: true,
      render: (value: number) => value != null ? value.toFixed(2) : '-'
    },
    {
      title: 'Type',
      dataIndex: 'baselineType',
      key: 'baselineType',
      render: (type: string) => (
        <Tag color={
          type === 'Initial' ? 'blue' :
          type === 'Periodic' ? 'green' :
          type === 'Correction' ? 'orange' : 'purple'
        }>
          {type}
        </Tag>
      )
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Tag color={
          status === 'Active' ? 'green' :
          status === 'Superseded' ? 'orange' : 'red'
        }>
          {status}
        </Tag>
      )
    },
    {
      title: 'Data Source',
      dataIndex: 'dataSource',
      key: 'dataSource',
      render: (source: string) => (
        <Tag color={source === 'Manual' ? 'blue' : 'cyan'}>
          {source}
        </Tag>
      )
    },
    {
      title: 'Validated',
      dataIndex: 'isValidated',
      key: 'isValidated',
      render: (validated: boolean) => (
        <Tag color={validated ? 'green' : 'orange'}>
          {validated ? 'Yes' : 'No'}
        </Tag>
      )
    },
    {
      title: 'Anomalous',
      dataIndex: 'isAnomalous',
      key: 'isAnomalous',
      render: (anomalous: boolean) => (
        anomalous ? <Tag color="red">Yes</Tag> : <Tag color="green">No</Tag>
      )
    },
    {
      title: 'Variance %',
      dataIndex: 'variancePercentage',
      key: 'variancePercentage',
      render: (variance: number | null) => {
        if (variance === null) return 'N/A';
        const color = Math.abs(variance) > 20 ? 'red' : Math.abs(variance) > 10 ? 'orange' : 'green';
        return <span style={{ color }}>{variance?.toFixed(2)}%</span>;
      }
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_: any, record: BaselineRecordListDto) => (
        <Space>
          <Tooltip title="View Details">
            <Button 
              size="small" 
              icon={<EyeOutlined />} 
              onClick={() => handleViewDetails(record)}
            />
          </Tooltip>
          
          {!record.isValidated && (
            <Tooltip title="Validate">
              <Button 
                size="small" 
                icon={<CheckCircleOutlined />} 
                onClick={() => handleValidate(record)}
                type="primary"
              />
            </Tooltip>
          )}
          
          {record.isAnomalous && (
            <Tooltip title="Correct">
              <Button 
                size="small" 
                icon={<EditOutlined />} 
                onClick={() => handleCorrect(record)}
                type="default"
              />
            </Tooltip>
          )}
          
          <Dropdown
            menu={{
              items: [
                {
                  key: 'edit',
                  label: 'Edit',
                  icon: <EditOutlined />
                },
                {
                  key: 'delete',
                  label: 'Delete',
                  icon: <DeleteOutlined />,
                  danger: true,
                  onClick: () => {
                    Modal.confirm({
                      title: 'Delete Baseline Record',
                      content: 'Are you sure you want to delete this baseline record?',
                      onOk: () => handleDelete(record.id)
                    });
                  }
                }
              ]
            }}
          >
            <Button size="small" icon={<MoreOutlined />} />
          </Dropdown>
        </Space>
      )
    }
  ];

  // Row selection
  const rowSelection = {
    selectedRowKeys,
    onChange: (selectedRowKeys: React.Key[]) => {
      setSelectedRowKeys(selectedRowKeys as number[]);
    },
    getCheckboxProps: (record: BaselineRecordListDto) => ({
      disabled: record.isValidated, // Disable checkbox for already validated records
      name: record.meterSerialNumber,
    }),
  };

  return (
    <div style={{ padding: 24 }}>
      <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'flex-end', alignItems: 'center' }}>
        <Space>
          <Button 
            icon={<FileExcelOutlined />} 
            onClick={handleDownloadTemplate}
          >
            Download Template
            </Button>
          
          <Button
            icon={<UploadOutlined />}
            onClick={() => setIsImportModalVisible(true)}
            style={{ display: 'none' }} // Hidden - can be restored later
          >
            Import Data
              </Button>

            <Button
            icon={<UploadOutlined />}
              type="primary"
            onClick={() => setIsSdcImportModalVisible(true)}
            style={{ display: 'none' }} // Hidden - can be restored later
          >
            SDC Excel Import
            </Button>
          
          <Button 
            icon={<ExportOutlined />} 
            onClick={handleExport}
          >
            Export Data
            </Button>
          
          <Button 
            type="default" 
            icon={<CheckCircleOutlined />}
            onClick={() => setIsCreateModalVisible(true)}
          >
            New Baseline
            </Button>
          </Space>
        </div>

      <StatisticsCards />
      <SearchForm />

      {selectedRowKeys.length > 0 && (
        <Card style={{ marginBottom: 16 }}>
          <Space>
            <span>{selectedRowKeys.length} items selected</span>
            <Button 
              type="primary" 
              size="small"
              onClick={() => handleBatchValidate(true)}
            >
              Batch Approve
            </Button>
            <Button 
              danger 
              size="small"
              onClick={() => handleBatchValidate(false)}
            >
              Batch Reject
            </Button>
          </Space>
        </Card>
      )}

      <Card>
        <DataManagementPaginatedTable
          rowSelection={rowSelection}
          columns={columns}
          data={baselines}
          total={totalCount}
          currentPage={searchParams.page}
          currentPageSize={searchParams.pageSize}
          loading={loading}
          onPageChange={handlePageChange}
          onSortChange={handleSortChange}
          rowKey="id"
          scroll={{ x: 1200 }}
        />
      </Card>

      {/* Create Modal */}
      <Modal
        title="Create New Baseline Record"
        open={isCreateModalVisible}
        onCancel={() => setIsCreateModalVisible(false)}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleCreate}
        >
              <Form.Item
                name="meterId"
            label="Meter ID" 
            rules={[{ required: true, message: 'Please input meter ID!' }]}
              >
            <InputNumber style={{ width: '100%' }} />
              </Form.Item>
          
              <Form.Item
                name="baselineDate"
                label="Baseline Date"
            rules={[{ required: true, message: 'Please select baseline date!' }]}
              >
                <DatePicker style={{ width: '100%' }} />
              </Form.Item>

              <Form.Item
                name="baselineValue"
                label="Baseline Value"
            rules={[{ required: true, message: 'Please input baseline value!' }]}
          >
            <InputNumber style={{ width: '100%' }} step={0.01} />
              </Form.Item>
          
          <Form.Item name="baselineType" label="Baseline Type" initialValue="Periodic">
            <Select>
                  <Option value="Initial">Initial</Option>
                  <Option value="Periodic">Periodic</Option>
                  <Option value="Correction">Correction</Option>
                  <Option value="Migration">Migration</Option>
                </Select>
              </Form.Item>
          
          <Form.Item name="validationNotes" label="Validation Notes">
            <Input.TextArea rows={3} />
              </Form.Item>
          
          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                Create
              </Button>
              <Button onClick={() => setIsCreateModalVisible(false)}>
                Cancel
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* Detail Modal */}
      <Modal
        title="Baseline Record Details"
        open={isDetailModalVisible}
        onCancel={() => setIsDetailModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setIsDetailModalVisible(false)}>
            Close
          </Button>
        ]}
        width={800}
      >
        {selectedBaseline && (
          <div>
            <Row gutter={16}>
              <Col span={12}>
                <p><strong>Meter Serial:</strong> {selectedBaseline.meterSerialNumber}</p>
                <p><strong>Location:</strong> {selectedBaseline.meterLocation}</p>
                <p><strong>Baseline Date:</strong> {dayjs(selectedBaseline.baselineDate).format('YYYY-MM-DD')}</p>
                <p><strong>Baseline Value:</strong> {selectedBaseline.baselineValue != null ? selectedBaseline.baselineValue.toFixed(2) : 'N/A'}</p>
                <p><strong>Type:</strong> {selectedBaseline.baselineType}</p>
                <p><strong>Status:</strong> {selectedBaseline.status}</p>
              </Col>
              <Col span={12}>
                <p><strong>Data Source:</strong> {selectedBaseline.dataSource}</p>
                <p><strong>Validated:</strong> {selectedBaseline.isValidated ? 'Yes' : 'No'}</p>
                <p><strong>Anomalous:</strong> {selectedBaseline.isAnomalous ? 'Yes' : 'No'}</p>
                <p><strong>Variance:</strong> {selectedBaseline.variancePercentage?.toFixed(2) || 'N/A'}%</p>
                <p><strong>Created:</strong> {dayjs(selectedBaseline.createdAt).format('YYYY-MM-DD HH:mm')}</p>
                <p><strong>Updated:</strong> {dayjs(selectedBaseline.updatedAt).format('YYYY-MM-DD HH:mm')}</p>
              </Col>
            </Row>
            
            {selectedBaseline.validationNotes && (
              <>
                <Divider />
                <p><strong>Validation Notes:</strong></p>
                <p>{selectedBaseline.validationNotes}</p>
              </>
            )}
            
            {selectedBaseline.anomalyDescription && (
              <>
                <Divider />
                <p><strong>Anomaly Description:</strong></p>
                <p>{selectedBaseline.anomalyDescription}</p>
              </>
            )}
          </div>
        )}
      </Modal>

      {/* Validation Modal */}
      <Modal
        title="Validate Baseline Record"
        open={isValidationModalVisible}
        onCancel={() => setIsValidationModalVisible(false)}
        footer={null}
      >
        <Form
          form={validationForm}
          layout="vertical"
          onFinish={submitValidation}
        >
          <Form.Item name="baselineId" hidden>
            <InputNumber />
          </Form.Item>

          <Form.Item name="isValid" label="Validation Result" initialValue={true}>
            <Select>
              <Option value={true}>Valid</Option>
              <Option value={false}>Invalid</Option>
            </Select>
          </Form.Item>

          <Form.Item name="validationNotes" label="Validation Notes">
            <Input.TextArea rows={3} />
          </Form.Item>

          <Form.Item name="validatedBy" label="Validated By" initialValue="Admin">
            <Input />
          </Form.Item>

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                Validate
              </Button>
              <Button onClick={() => setIsValidationModalVisible(false)}>
                Cancel
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* Correction Modal */}
      <Modal
        title="Correct Baseline Record"
        open={isCorrectionModalVisible}
        onCancel={() => setIsCorrectionModalVisible(false)}
        footer={null}
      >
        <Form
          form={correctionForm}
          layout="vertical"
          onFinish={submitCorrection}
        >
          <Form.Item name="baselineId" hidden>
            <InputNumber />
          </Form.Item>

          <Form.Item
            name="newBaselineValue"
            label="New Baseline Value" 
            rules={[{ required: true, message: 'Please input new baseline value!' }]}
          >
            <InputNumber style={{ width: '100%' }} step={0.01} />
          </Form.Item>

          <Form.Item
            name="correctionReason"
            label="Correction Reason"
            rules={[{ required: true, message: 'Please input correction reason!' }]}
          >
            <Input.TextArea rows={3} />
          </Form.Item>

          <Form.Item name="notes" label="Additional Notes">
            <Input.TextArea rows={2} />
          </Form.Item>

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                Correct
              </Button>
              <Button onClick={() => setIsCorrectionModalVisible(false)}>
                Cancel
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* Import Modal - Replace with ImportWizard */}
      <ImportWizard
        visible={isImportModalVisible}
        onCancel={() => setIsImportModalVisible(false)}
        onSuccess={() => {
          loadBaselines();
          loadStatistics();
        }}
      />

      {/* SDC Excel Import Modal */}
      <Modal
        title="SDC Excel Import"
        open={isSdcImportModalVisible}
        onCancel={() => setIsSdcImportModalVisible(false)}
        footer={null}
        width={700}
      >
        <div style={{ padding: '16px 0' }}>
          <div style={{ marginBottom: 24 }}>
            <h4>Import Requirements:</h4>
            <ul style={{ paddingLeft: 20, margin: 0 }}>
              <li>Excel file (.xlsx) or CSV file (.csv) with baseline reading data</li>
              <li>Required columns: Meter Serial Number, Baseline Date, Baseline Value</li>
              <li>Optional columns: Location, Baseline Type, Validation Notes</li>
              <li>File will create new baseline records or update existing ones</li>
            </ul>
          </div>

          {sdcImportLoading && (
            <div style={{ marginBottom: 24 }}>
              <div style={{ marginBottom: 8 }}>Processing SDC baseline file...</div>
              <Progress percent={sdcImportProgress} showInfo={false} />
            </div>
          )}

          <Upload.Dragger
            name="file"
            multiple={false}
            accept=".xlsx,.xls,.csv"
            showUploadList={false}
            disabled={sdcImportLoading}
            beforeUpload={(file) => {
              handleSdcImport(file);
              return false; // Prevent default upload
            }}
          >
            <p className="ant-upload-drag-icon">
              <UploadOutlined style={{ fontSize: 48, color: sdcImportLoading ? '#ccc' : '#1890ff' }} />
            </p>
            <p className="ant-upload-text">
              {sdcImportLoading ? 'Processing...' : 'Click or drag SDC baseline file to this area'}
            </p>
            <p className="ant-upload-hint">
              Support for .xlsx, .xls, and .csv files. The file should contain baseline reading data from SDC.
            </p>
          </Upload.Dragger>
          
          <div style={{ marginTop: 16, textAlign: 'center' }}>
            <Space>
              <Button 
                type="link" 
                onClick={async () => {
                  try {
                    const blob = await baselineService.downloadSdcImportTemplate();
                    const url = window.URL.createObjectURL(blob);
                    const link = document.createElement('a');
                    link.href = url;
                    link.download = 'SDC_Baseline_Import_Template.xlsx';
                    link.click();
                    window.URL.revokeObjectURL(url);
                  } catch (error) {
                    message.error('Failed to download template');
                  }
                }}
              >
                Download SDC Template
              </Button>
              <Button 
                type="link"
                onClick={() => setIsSdcImportModalVisible(false)}
                disabled={sdcImportLoading}
              >
                Cancel
              </Button>
            </Space>
          </div>
        </div>
      </Modal>
    </div>
  );
} 