'use client';

import React, { useState } from 'react';
import {
  Modal,
  Steps,
  Upload,
  Button,
  Table,
  message,
  Progress,
  Alert,
  Space,
  Typography,
  Card,
  Divider,
  Tag,
  Result
} from 'antd';
import {
  UploadOutlined,
  InboxOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  FileExcelOutlined,
  DownloadOutlined
} from '@ant-design/icons';
import { baselineService } from '@/services/baseline.service';
import { BaselineImportResultDto } from '@/types/baseline';

const { Step } = Steps;
const { Dragger } = Upload;
const { Text, Title } = Typography;

interface ImportWizardProps {
  visible: boolean;
  onCancel: () => void;
  onSuccess: () => void;
}

export default function ImportWizard({ visible, onCancel, onSuccess }: ImportWizardProps) {
  const [currentStep, setCurrentStep] = useState(0);
  const [uploading, setUploading] = useState(false);
  const [importResult, setImportResult] = useState<BaselineImportResultDto | null>(null);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [previewData, setPreviewData] = useState<any[]>([]);

  // Reset wizard state
  const resetWizard = () => {
    setCurrentStep(0);
    setUploading(false);
    setImportResult(null);
    setSelectedFile(null);
    setPreviewData([]);
  };

  // Handle wizard close
  const handleCancel = () => {
    resetWizard();
    onCancel();
  };

  // Download template
  const handleDownloadTemplate = async () => {
    try {
      const blob = await baselineService.downloadExcelTemplate();
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `baseline_import_template.xlsx`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      message.success('Template downloaded successfully');
    } catch (error) {
      message.error('Failed to download template');
      console.error('Error downloading template:', error);
    }
  };

  // Handle file upload
  const handleFileUpload = (file: File) => {
    if (!file) return false;

    // Validate file type
    const allowedTypes = [
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
      'application/vnd.ms-excel', // .xls
      'text/csv' // .csv
    ];

    if (!allowedTypes.includes(file.type)) {
      message.error('Please upload Excel (.xlsx, .xls) or CSV (.csv) files only');
      return false;
    }

    // Validate file size (max 50MB)
    if (file.size > 50 * 1024 * 1024) {
      message.error('File size must be less than 50MB');
      return false;
    }

    setSelectedFile(file);
    setCurrentStep(1);
    
    // Simulate preview data generation (in real app, would parse file)
    generatePreviewData();
    
    return false; // Prevent auto upload
  };

  // Generate preview data (mock)
  const generatePreviewData = () => {
    const mockData = [
      {
        key: 1,
        serialNumber: '19M1567747',
        baselineDate: '2024-12-01',
        baselineValue: 125.45,
        baselineType: 'Initial',
        status: 'Active',
        validationNotes: 'Sample baseline reading',
        rowStatus: 'valid'
      },
      {
        key: 2,
        serialNumber: '17M245630',
        baselineDate: '2024-11-30',
        baselineValue: 89.12,
        baselineType: 'Periodic',
        status: 'Active',
        validationNotes: 'Monthly reading',
        rowStatus: 'valid'
      },
      {
        key: 3,
        serialNumber: 'INVALID123',
        baselineDate: '2024-12-01',
        baselineValue: -5.0,
        baselineType: 'Initial',
        status: 'Active',
        validationNotes: '',
        rowStatus: 'error',
        errorMessage: 'Serial number not found, negative baseline value'
      }
    ];
    setPreviewData(mockData);
  };

  // Process import
  const handleImport = async () => {
    if (!selectedFile) {
      message.error('No file selected');
      return;
    }

    try {
      setUploading(true);
      setCurrentStep(2);

      let result: BaselineImportResultDto;
      
      if (selectedFile.type === 'text/csv') {
        result = await baselineService.importFromCsv(selectedFile, selectedFile.name);
      } else {
        result = await baselineService.importFromExcel(selectedFile, selectedFile.name);
      }

      setImportResult(result);
      setCurrentStep(3);

      if (result.successfulRows > 0) {
        message.success(`Successfully imported ${result.successfulRows} baseline records`);
      }

      if (result.failedRows > 0) {
        message.warning(`${result.failedRows} records failed to import`);
      }

    } catch (error) {
      message.error('Import failed');
      console.error('Import error:', error);
      setCurrentStep(1); // Go back to preview step
    } finally {
      setUploading(false);
    }
  };

  // Handle success
  const handleSuccess = () => {
    onSuccess();
    resetWizard();
    onCancel();
  };

  // Preview table columns
  const previewColumns = [
    {
      title: 'Status',
      dataIndex: 'rowStatus',
      key: 'rowStatus',
      width: 80,
      render: (status: string) => (
        status === 'valid' ? 
          <CheckCircleOutlined style={{ color: '#52c41a' }} /> :
          <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />
      )
    },
    {
      title: 'Serial Number',
      dataIndex: 'serialNumber',
      key: 'serialNumber'
    },
    {
      title: 'Baseline Date',
      dataIndex: 'baselineDate',
      key: 'baselineDate'
    },
    {
      title: 'Baseline Value',
      dataIndex: 'baselineValue',
      key: 'baselineValue',
      render: (value: number) => value != null ? value.toFixed(2) : '-'
    },
    {
      title: 'Type',
      dataIndex: 'baselineType',
      key: 'baselineType',
      render: (type: string) => <Tag color="blue">{type}</Tag>
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => <Tag color="green">{status}</Tag>
    },
    {
      title: 'Error Message',
      dataIndex: 'errorMessage',
      key: 'errorMessage',
      render: (error: string) => error ? <Text type="danger">{error}</Text> : null
    }
  ];

  // Step content
  const stepContents = [
    // Step 0: Upload
    <div key="upload" style={{ textAlign: 'center', padding: '40px 20px' }}>
      <Title level={4}>Upload Baseline Data File</Title>
      <p style={{ marginBottom: 24, color: '#666' }}>
        Upload an Excel or CSV file containing baseline data. 
        Make sure your file follows the required format.
      </p>
      
      <Dragger
        name="file"
        multiple={false}
        accept=".csv,.xlsx,.xls"
        beforeUpload={handleFileUpload}
        showUploadList={false}
        style={{ marginBottom: 24 }}
      >
        <p className="ant-upload-drag-icon">
          <InboxOutlined style={{ fontSize: 48, color: '#1890ff' }} />
        </p>
        <p className="ant-upload-text">Click or drag file to this area to upload</p>
        <p className="ant-upload-hint">
          Support for CSV, Excel files (.csv, .xlsx, .xls). Maximum file size: 50MB
        </p>
      </Dragger>
      
      <Divider>Or</Divider>
      
      <Button 
        icon={<DownloadOutlined />} 
        onClick={handleDownloadTemplate}
        type="dashed"
      >
        Download Import Template
      </Button>
    </div>,

    // Step 1: Preview
    <div key="preview">
      <Alert
        message="Data Preview"
        description="Review the data before importing. Fix any errors in your source file if needed."
        type="info"
        showIcon
        style={{ marginBottom: 16 }}
      />
      
      <Card size="small" style={{ marginBottom: 16 }}>
        <Space>
          <Text strong>File:</Text>
          <Text>{selectedFile?.name}</Text>
          <Text strong>Size:</Text>
          <Text>{selectedFile ? (selectedFile.size / 1024).toFixed(1) + ' KB' : 'N/A'}</Text>
        </Space>
      </Card>

      <Table
        columns={previewColumns}
        dataSource={previewData}
        size="small"
        pagination={false}
        scroll={{ y: 300 }}
        rowClassName={(record) => record.rowStatus === 'error' ? 'error-row' : ''}
      />

      <div style={{ marginTop: 16 }}>
        <Space>
          <Text>Valid records: {previewData.filter(r => r.rowStatus === 'valid').length}</Text>
          <Text type="danger">Error records: {previewData.filter(r => r.rowStatus === 'error').length}</Text>
        </Space>
      </div>
    </div>,

    // Step 2: Processing
    <div key="processing" style={{ textAlign: 'center', padding: '60px 20px' }}>
      <Progress 
        type="circle" 
        percent={uploading ? 50 : 100} 
        status={uploading ? 'active' : 'success'}
        size={120}
      />
      <Title level={4} style={{ marginTop: 24 }}>
        {uploading ? 'Processing Import...' : 'Import Completed'}
      </Title>
      <p style={{ color: '#666' }}>
        {uploading ? 
          'Please wait while we process your baseline data.' :
          'Your baseline data has been processed successfully.'
        }
      </p>
    </div>,

    // Step 3: Results
    <div key="results">
      {importResult && (
        <Result
          status={importResult.failedRows === 0 ? 'success' : 'warning'}
          title={
            importResult.failedRows === 0 ? 
              'Import Completed Successfully' : 
              'Import Completed with Issues'
          }
          subTitle={
            <div>
              <p>Total records processed: {importResult.totalRows}</p>
              <p style={{ color: '#52c41a' }}>Successfully imported: {importResult.successfulRows}</p>
              {importResult.failedRows > 0 && (
                <p style={{ color: '#ff4d4f' }}>Failed to import: {importResult.failedRows}</p>
              )}
            </div>
          }
          extra={[
            <Button type="primary" key="done" onClick={handleSuccess}>
              Done
            </Button>,
            <Button key="import-batch" onClick={() => {
              // Navigate to import batch view
              message.info('Navigate to import batch view');
            }}>
              View Import Batch
            </Button>
          ]}
        >
          {importResult.failedRecords && importResult.failedRecords.length > 0 && (
            <div style={{ textAlign: 'left', marginTop: 16 }}>
              <Title level={5}>Import Errors:</Title>
              <ul style={{ color: '#ff4d4f' }}>
                {importResult.failedRecords.slice(0, 10).map((record, index) => (
                  <li key={index}>
                    Row {record.rowNumber}: {record.validationErrors.join(', ')}
                  </li>
                ))}
                {importResult.failedRecords.length > 10 && (
                  <li>... and {importResult.failedRecords.length - 10} more errors</li>
                )}
              </ul>
            </div>
          )}
        </Result>
      )}
    </div>
  ];

  return (
    <Modal
      title="Import Baseline Data"
      open={visible}
      onCancel={handleCancel}
      footer={null}
      width={800}
      destroyOnClose
    >
      <Steps current={currentStep} style={{ marginBottom: 24 }}>
        <Step title="Upload" icon={<UploadOutlined />} />
        <Step title="Preview" icon={<FileExcelOutlined />} />
        <Step title="Process" icon={<CheckCircleOutlined />} />
        <Step title="Complete" icon={<CheckCircleOutlined />} />
      </Steps>

      <div style={{ minHeight: 400 }}>
        {stepContents[currentStep]}
      </div>

      {/* Step navigation */}
      <div style={{ marginTop: 24, textAlign: 'right' }}>
        <Space>
          {currentStep > 0 && currentStep < 3 && (
            <Button onClick={() => setCurrentStep(currentStep - 1)}>
              Previous
            </Button>
          )}
          
          {currentStep === 1 && (
            <Button
              type="primary"
              onClick={handleImport}
              disabled={previewData.filter(r => r.rowStatus === 'valid').length === 0}
            >
              Import Data
            </Button>
          )}
          
          {currentStep === 0 && (
            <Button onClick={handleCancel}>
              Cancel
            </Button>
          )}
        </Space>
      </div>

      <style jsx>{`
        .error-row {
          background-color: #fff2f0 !important;
        }
      `}</style>
    </Modal>
  );
} 