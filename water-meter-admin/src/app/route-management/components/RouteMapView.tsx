'use client';

import React, { useState, useEffect } from 'react';
import { Card, Switch, Alert, Spin, Row, Col, List, Tag, Typography, Pagination } from 'antd';
import GoogleMapView, { MapMarker, MapPath } from '@/components/common/GoogleMapView';
import { RouteListDto, RouteWaypointDto, routeService } from '@/services/route.service';

const { Text } = Typography;

interface RouteMapViewProps {
  routes: RouteListDto[];
  selectedRoute?: RouteListDto;
  onRouteSelect: (route: RouteListDto | null) => void;
  loading?: boolean;
}



export default function RouteMapView({
  routes,
  selectedRoute,
  onRouteSelect,
  loading = false
}: RouteMapViewProps) {
  const [selectedRouteId, setSelectedRouteId] = useState<number | null>(selectedRoute?.id || null);
  const [routeWaypoints, setRouteWaypoints] = useState<RouteWaypointDto[]>([]);
  const [waypointsLoading, setWaypointsLoading] = useState(false);
  const [showInactiveRoutes, setShowInactiveRoutes] = useState(false);
  const [mapCenter, setMapCenter] = useState({ lat: -43.5321, lng: 172.6362 }); // Christchurch

  // Pagination state for route list
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(8); // Show 8 routes per page to fit in the sidebar

  // Load waypoints when route is selected
  useEffect(() => {
    const loadRouteWaypoints = async () => {
      if (!selectedRouteId) {
        setRouteWaypoints([]);
        return;
      }

      setWaypointsLoading(true);
      try {
        const waypoints = await routeService.getRouteWaypoints(selectedRouteId);
        setRouteWaypoints(waypoints);
      } catch (error) {
        console.error('Error loading route waypoints:', error);
        setRouteWaypoints([]);
      } finally {
        setWaypointsLoading(false);
      }
    };

    loadRouteWaypoints();
  }, [selectedRouteId]);

  // Handle route selection
  const handleRouteSelect = (route: RouteListDto) => {
    setSelectedRouteId(route.id);
    onRouteSelect(route);
  };

  // Generate map markers from waypoints
  const generateWaypointMarkers = (): MapMarker[] => {
    return routeWaypoints
      .filter(wp => wp.latitude && wp.longitude)
      .map((waypoint) => ({
        id: `waypoint-${waypoint.id}`,
        position: {
          lat: Number(waypoint.latitude),
          lng: Number(waypoint.longitude)
        },
        title: `${waypoint.sequenceOrder}. ${waypoint.waterMeterSerial || 'Unknown Meter'}`,
        info: {
          description: waypoint.customerName || 'Unknown Customer',
          status: waypoint.accessDifficulty ? `Difficulty: ${waypoint.accessDifficulty}` : 'Standard Access',
          type: 'waypoint',
          location: waypoint.address || 'No address',
          meterSerial: waypoint.waterMeterSerial
        },
        selected: false
      }));
  };

  // Generate map paths from waypoints
  const generateRoutePaths = (): MapPath[] => {
    if (routeWaypoints.length < 2) return [];

    const sortedWaypoints = routeWaypoints
      .filter(wp => wp.latitude && wp.longitude)
      .sort((a, b) => a.sequenceOrder - b.sequenceOrder);

    if (sortedWaypoints.length < 2) return [];

    const selectedRouteData = routes.find(r => r.id === selectedRouteId);
    const routeColor = getRouteColor(selectedRouteData?.status || 'Active');

    return [{
      id: `route-path-${selectedRouteId}`,
      coordinates: sortedWaypoints.map(wp => ({
        lat: Number(wp.latitude),
        lng: Number(wp.longitude)
      })),
      color: routeColor,
      strokeWeight: 4,
      strokeOpacity: 0.8
    }];
  };

  // Get route color based on status
  const getRouteColor = (status: string): string => {
    switch (status) {
      case 'Active': return '#52c41a'; // Green
      case 'Inactive': return '#d9d9d9'; // Gray
      case 'Planned': return '#1890ff'; // Blue
      default: return '#1890ff';
    }
  };

  // Update map center when route is selected
  useEffect(() => {
    if (routeWaypoints.length > 0) {
      // Calculate center from waypoints
      const latSum = routeWaypoints.reduce((sum, wp) => sum + (wp.latitude || 0), 0);
      const lngSum = routeWaypoints.reduce((sum, wp) => sum + (wp.longitude || 0), 0);
      const validWaypoints = routeWaypoints.filter(wp => wp.latitude && wp.longitude);

      if (validWaypoints.length > 0) {
        setMapCenter({
          lat: latSum / validWaypoints.length,
          lng: lngSum / validWaypoints.length
        });
      }
    }
  }, [routeWaypoints]);

  // Filter routes based on active/inactive toggle
  const filteredRoutes = routes.filter(route =>
    showInactiveRoutes || route.status === 'Active'
  );

  // Paginate filtered routes
  const startIndex = (currentPage - 1) * pageSize;
  const endIndex = startIndex + pageSize;
  const paginatedRoutes = filteredRoutes.slice(startIndex, endIndex);

  // Check if selected route is in current page, if not clear selection
  useEffect(() => {
    if (selectedRouteId) {
      const isSelectedRouteInCurrentPage = paginatedRoutes.some(route => route.id === selectedRouteId);
      if (!isSelectedRouteInCurrentPage) {
        setSelectedRouteId(null);
        onRouteSelect?.(null);
      }
    }
  }, [currentPage, paginatedRoutes, selectedRouteId, onRouteSelect]);

  if (loading) {
    return (
      <Card className="h-96">
        <div className="flex items-center justify-center h-full">
          <Spin size="large" />
          <span className="ml-4">Loading route map...</span>
        </div>
      </Card>
    );
  }

  return (
    <div style={{ height: 'calc(90vh - 100px)' }}>
      <Row gutter={16} className="h-full">
        {/* Left Panel - Route List */}
        <Col span={5} className="h-full">
          <Card
            title="Routes"
            extra={
              <Switch
                checked={showInactiveRoutes}
                onChange={setShowInactiveRoutes}
                checkedChildren="Show All"
                unCheckedChildren="Active Only"
                size="small"
              />
            }
            className="h-full"
            styles={{ body: { padding: 0, height: 'calc(100% - 57px)', display: 'flex', flexDirection: 'column' } }}
        >
          <div className="flex-1 overflow-auto">
            <List
              dataSource={paginatedRoutes}
              renderItem={(route) => (
                <List.Item
                  className={`cursor-pointer hover:bg-gray-50 ${
                    selectedRouteId === route.id ? 'bg-blue-50 border-l-4 border-blue-500' : ''
                  }`}
                  onClick={() => handleRouteSelect(route)}
                  style={{ padding: '8px 12px' }}
                >
                  <div className="w-full flex justify-between items-center">
                    <div className="flex-1 min-w-0">
                      <Text strong className="text-sm block truncate">{route.name}</Text>
                      <Text type="secondary" className="text-xs">
                        {route.zone} • {route.totalMeters} meters
                      </Text>
                    </div>
                    <Tag
                      color={route.status === 'Active' ? 'green' : 'default'}
                    >
                      {route.status}
                    </Tag>
                  </div>
                </List.Item>
              )}
            />
          </div>
          {filteredRoutes.length > pageSize && (
            <div className="p-4 border-t bg-white">
              <Pagination
                current={currentPage}
                pageSize={pageSize}
                total={filteredRoutes.length}
                onChange={setCurrentPage}
                size="small"
                showSizeChanger={false}
                showQuickJumper={false}
                showTotal={(total, range) =>
                  `${range[0]}-${range[1]} of ${total} routes`
                }
              />
            </div>
          )}
        </Card>
      </Col>

      {/* Right Panel - Map */}
      <Col span={19} className="h-full">
        <Card
          title={selectedRouteId ? `Route Path: ${routes.find(r => r.id === selectedRouteId)?.name}` : "Select a Route"}
          className="h-full"
          styles={{ body: { padding: 0, height: 'calc(100% - 57px)' } }}
        >
          {!selectedRouteId ? (
            <div className="flex items-center justify-center h-full bg-gray-50">
              <div className="text-center">
                <div className="text-4xl mb-4">🗺️</div>
                <Text type="secondary">Select a route from the left panel to view its path</Text>
              </div>
            </div>
          ) : waypointsLoading ? (
            <div className="flex items-center justify-center h-full">
              <Spin size="large" />
              <span className="ml-4">Loading route waypoints...</span>
            </div>
          ) : routeWaypoints.length === 0 ? (
            <div className="flex items-center justify-center h-full bg-gray-50">
              <Alert
                message="No waypoints available"
                description="This route doesn't have GPS coordinates for its waypoints yet."
                type="info"
                showIcon
              />
            </div>
          ) : (
            <GoogleMapView
              center={mapCenter}
              zoom={13}
              markers={generateWaypointMarkers()}
              paths={generateRoutePaths()}
              height="100%"
              width="100%"
            />
          )}
        </Card>
      </Col>
    </Row>
    </div>
  );
}