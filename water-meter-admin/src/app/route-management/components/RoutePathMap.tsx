'use client';

import React, { useMemo, useState, useEffect } from 'react';
import { Card, Space, Typography, Tag, Empty, Button, Table, message, Popconfirm } from 'antd';
import { RouteWaypointDto, routeService, UpdateWaypointCoordinatesDto, BatchUpdateCoordinatesDto } from '@/services/route.service';
import GoogleMapView, { MapMarker } from '@/components/common/GoogleMapView';
import {
  EnvironmentOutlined,
  AimOutlined,
  PlusOutlined,
  ReloadOutlined
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';

const { Text, Title } = Typography;

interface RoutePathMapProps {
  waypoints: RouteWaypointDto[];
  route: any;
  loading?: boolean;
  onWaypointsUpdate?: () => void;
}

// Global flag to track Google Maps API status
let googleMapsAPIReady = false;

// Function to ensure Google Maps API is loaded
const ensureGoogleMapsAPI = (): Promise<boolean> => {
  return new Promise((resolve) => {
    // Check if already loaded
    if (window.google?.maps?.Geocoder) {
      googleMapsAPIReady = true;
      resolve(true);
      return;
    }

    // Check multiple times with increasing delays
    let attempts = 0;
    const maxAttempts = 20; // Increased from 1 to 20 attempts
    
    const checkAPI = () => {
      attempts++;
      console.log(`Checking Google Maps API availability - Attempt ${attempts}/${maxAttempts}`);
      
      if (window.google?.maps?.Geocoder) {
        console.log('Google Maps API is now available!');
        googleMapsAPIReady = true;
        resolve(true);
        return;
      }
      
      if (attempts >= maxAttempts) {
        console.error('Failed to load Google Maps API after maximum attempts');
        resolve(false);
        return;
      }
      
      // Progressive delay: 500ms, 1s, 1.5s, 2s, then 2s for remaining attempts
      const delay = attempts <= 4 ? 500 * attempts : 2000;
      setTimeout(checkAPI, delay);
    };
    
    checkAPI();
  });
};

const RoutePathMap: React.FC<RoutePathMapProps> = ({ waypoints, route, loading = false, onWaypointsUpdate }) => {
  const [geocoding, setGeocoding] = useState(false);
  const [apiReady, setApiReady] = useState(googleMapsAPIReady);

  // Check API readiness on component mount
  useEffect(() => {
    if (!apiReady) {
      ensureGoogleMapsAPI().then((ready) => {
        setApiReady(ready);
      });
    }
  }, [apiReady]);

  // Define helper functions first
  const getWaypointStatus = (waypoint: RouteWaypointDto): string => {
    if (waypoint.requiresSpecialEquipment) return 'Special Equipment';
    return 'Standard';
  };

  const getWaypointPriority = (waypoint: RouteWaypointDto): string => {
    const difficulty = waypoint.accessDifficulty;
    if (!difficulty) return 'Medium';
    if (difficulty <= 2) return 'Low';
    if (difficulty <= 3) return 'Medium';
    return 'High';
  };

  // Convert waypoints to map markers
  const mapMarkers: MapMarker[] = useMemo(() => {
    return waypoints
      .filter(waypoint => waypoint.latitude && waypoint.longitude)
      .map((waypoint, index) => ({
        id: waypoint.id.toString(),
        position: {
          lat: Number(waypoint.latitude),
          lng: Number(waypoint.longitude)
        },
        title: `${waypoint.sequenceOrder}. ${waypoint.waterMeterSerial || 'Unknown Meter'}`,
        info: {
          description: `Customer: ${waypoint.customerName || 'Unknown'}\nAddress: ${waypoint.address || 'No address'}`,
          status: getWaypointStatus(waypoint),
          priority: getWaypointPriority(waypoint),
          type: 'waypoint'
        },
        selected: false
      }));
  }, [waypoints]);

  const waypointsWithoutCoordinates = useMemo(() => {
    return waypoints.filter(waypoint => !waypoint.latitude || !waypoint.longitude);
  }, [waypoints]);

  const handleMarkerClick = (marker: MapMarker) => {
    console.log('Clicked marker:', marker);
  };

  // Use Google Maps Geocoding API to resolve address coordinates
  const geocodeAddress = async (address: string): Promise<{lat: number, lng: number} | null> => {
    try {
      // Ensure API is ready before proceeding
      const ready = await ensureGoogleMapsAPI();
      if (!ready) {
        throw new Error('Google Maps API could not be loaded. Please check your internet connection and try again.');
      }

      const geocoder = new window.google.maps.Geocoder();
      
      return new Promise((resolve, reject) => {
        geocoder.geocode({ address }, (results, status) => {
          if (status === 'OK' && results && results[0]) {
            const location = results[0].geometry.location;
            const coordinates = {
              lat: location.lat(),
              lng: location.lng()
            };
            console.log(`Successfully geocoded "${address}" to:`, coordinates);
            resolve(coordinates);
          } else {
            const errorMsg = `Geocoding failed for "${address}". Status: ${status}`;
            console.error(errorMsg);
            reject(new Error(errorMsg));
          }
        });
      });
    } catch (error) {
      console.error('Geocoding error:', error);
      throw error;
    }
  };

  const handleGeocodeAddress = async (waypointId: number, address: string) => {
    setGeocoding(true);
    try {
      message.info('Resolving address coordinates...');
      const coordinates = await geocodeAddress(address);
      
      if (coordinates) {
        // Call API to update waypoint coordinates
        await routeService.updateWaypointCoordinates(waypointId, {
          latitude: coordinates.lat,
          longitude: coordinates.lng,
          address: address
        });
        
        message.success(`Address resolved successfully: ${coordinates.lat.toFixed(6)}, ${coordinates.lng.toFixed(6)}`);
        
        // Notify parent component to refresh data
        if (onWaypointsUpdate) {
          onWaypointsUpdate();
        }
      } else {
        message.error('Unable to resolve GPS coordinates for this address');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      message.error(`Address resolution failed: ${errorMessage}`);
      console.error('Geocoding error:', error);
    } finally {
      setGeocoding(false);
    }
  };

  const handleGeocodeAll = async () => {
    if (waypointsWithoutCoordinates.length === 0) return;

    setGeocoding(true);
    const updates: any[] = [];
    let successCount = 0;
    let failureCount = 0;
    
    try {
      message.info(`Starting to geocode ${waypointsWithoutCoordinates.length} addresses...`);
      
      // Ensure API is ready before batch processing
      const ready = await ensureGoogleMapsAPI();
      if (!ready) {
        throw new Error('Google Maps API could not be loaded');
      }
      
      for (const waypoint of waypointsWithoutCoordinates) {
        if (waypoint.address) {
          try {
            const coordinates = await geocodeAddress(waypoint.address);
            if (coordinates) {
              updates.push({
                waypointId: waypoint.id,
                latitude: coordinates.lat,
                longitude: coordinates.lng,
                address: waypoint.address
              });
              successCount++;
              console.log(`✓ Successfully geocoded: ${waypoint.address}`);
            }
            // Add delay to avoid API rate limits
            await new Promise(resolve => setTimeout(resolve, 300));
          } catch (error) {
            failureCount++;
            console.error(`✗ Failed to geocode waypoint ${waypoint.id} (${waypoint.address}):`, error);
          }
        } else {
          failureCount++;
          console.warn(`✗ Waypoint ${waypoint.id} has no address`);
        }
      }
      
      if (updates.length > 0) {
        message.info(`Saving ${updates.length} resolved coordinates...`);
        
        // Batch update coordinates
        const result = await routeService.batchUpdateWaypointCoordinates(route.id, { updates });
        
        if (result.successCount > 0) {
          message.success(`Successfully resolved and saved GPS coordinates for ${result.successCount} addresses`);
          
          // Notify parent component to refresh data
          if (onWaypointsUpdate) {
            onWaypointsUpdate();
          }
        }
        
        if (result.failedCount > 0) {
          message.warning(`${result.failedCount} coordinate updates failed to save`);
          console.log('Failed database updates:', result.failures);
        }
      } else {
        message.warning('No addresses were successfully resolved');
      }
      
      // Show final summary
      if (successCount > 0 || failureCount > 0) {
        console.log(`Batch geocoding completed: ${successCount} success, ${failureCount} failures`);
      }
      
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      message.error(`Batch address resolution failed: ${errorMessage}`);
      console.error('Batch geocoding error:', error);
    } finally {
      setGeocoding(false);
    }
  };

  const hasValidWaypoints = waypoints.some(w => w.latitude && w.longitude);

  const noCoordinatesColumns: ColumnsType<RouteWaypointDto> = [
    {
      title: 'Sequence',
      dataIndex: 'sequenceOrder',
      width: 80,
    },
    {
      title: 'Water Meter',
      dataIndex: 'waterMeterSerial',
      width: 120,
    },
    {
      title: 'Address',
      dataIndex: 'address',
      ellipsis: true,
    },
    {
      title: 'Action',
      width: 100,
      render: (_, record) => (
        <Button
          size="small"
          type="link"
          icon={<EnvironmentOutlined />}
          loading={geocoding}
          onClick={() => record.address && handleGeocodeAddress(record.id, record.address)}
          disabled={!record.address || geocoding}
          title={geocoding ? 'Processing...' : !record.address ? 'No address available' : 'Resolve Coordinates'}
        >
          Resolve
        </Button>
      ),
    },
  ];

  if (!hasValidWaypoints && waypointsWithoutCoordinates.length > 0) {
    return (
      <Card
        title={
          <Space>
            <EnvironmentOutlined />
            <Title level={5} style={{ margin: 0 }}>Route Path Map</Title>
          </Space>
        }
        loading={loading}
      >
        <div style={{ marginBottom: 16 }}>
          <Space wrap>
            <Text>
              <strong>Total Waypoints:</strong> {waypoints.length}
            </Text>
            <Text>
              <strong>With Coordinates:</strong> {waypoints.filter(w => w.latitude && w.longitude).length}
            </Text>
            <Text type="warning">
              <strong>Missing Coordinates:</strong> {waypointsWithoutCoordinates.length}
            </Text>
          </Space>
        </div>

        <div style={{ marginBottom: 16, padding: 16, backgroundColor: '#fff7e6', border: '1px solid #ffd591', borderRadius: 6 }}>
          <div style={{ marginBottom: 12 }}>
            <Text strong>No GPS coordinates found for waypoints</Text>
          </div>
          <Text>
            The waypoints don't have GPS coordinates yet. You can automatically resolve coordinates from addresses using Google Maps Geocoding service.
          </Text>
          {!apiReady && (
            <div style={{ marginTop: 8 }}>
              <Text type="secondary">⏳ Google Maps API is loading... (This may take a few seconds)</Text>
            </div>
          )}
        </div>

        <div style={{ marginBottom: 16 }}>
          <Space>
            <Button
              type="primary"
              size="large"
              icon={<AimOutlined />}
              loading={geocoding}
              onClick={handleGeocodeAll}
              disabled={waypointsWithoutCoordinates.length === 0 || geocoding}
            >
              🗺️ Geocode All Addresses ({waypointsWithoutCoordinates.length})
            </Button>
            <Button
              icon={<ReloadOutlined />}
              onClick={() => onWaypointsUpdate?.()}
            >
              Refresh Data
            </Button>
            {!apiReady && (
              <Button
                type="dashed"
                onClick={() => {
                  setApiReady(false);
                  ensureGoogleMapsAPI().then(setApiReady);
                }}
              >
                Retry API Load
              </Button>
            )}
          </Space>
        </div>

        <Table
          columns={noCoordinatesColumns}
          dataSource={waypointsWithoutCoordinates}
          rowKey="id"
          size="small"
          pagination={false}
          title={() => "Waypoints without GPS coordinates"}
        />
      </Card>
    );
  }

  if (!hasValidWaypoints) {
    return (
      <Card>
        <Empty
          image={Empty.PRESENTED_IMAGE_SIMPLE}
          description={
            <div>
              <div>No waypoints with location data</div>
              <div style={{ marginTop: 8, color: '#666' }}>
                Add waypoints with GPS coordinates to see the route on the map
              </div>
            </div>
          }
        />
      </Card>
    );
  }

  return (
    <Card
      title={
        <Space>
          <EnvironmentOutlined />
          <Title level={5} style={{ margin: 0 }}>Route Path Map</Title>
        </Space>
      }
      loading={loading}
    >
      <div style={{ marginBottom: 16 }}>
        <Space wrap>
          <Text>
            <strong>Total Waypoints:</strong> {waypoints.length}
          </Text>
          <Text>
            <strong>With Coordinates:</strong> {waypoints.filter(w => w.latitude && w.longitude).length}
          </Text>
          {waypointsWithoutCoordinates.length > 0 && (
            <Text type="warning">
              <strong>Missing Coordinates:</strong> {waypointsWithoutCoordinates.length}
            </Text>
          )}
          {route?.estimatedDistance && (
            <Text>
              <strong>Estimated Distance:</strong> {route.estimatedDistance} km
            </Text>
          )}
          {route?.estimatedDuration && (
            <Text>
              <strong>Estimated Duration:</strong> {route.estimatedDuration} min
            </Text>
          )}
        </Space>
      </div>

      {waypointsWithoutCoordinates.length > 0 && (
        <div style={{ marginBottom: 16, padding: 12, backgroundColor: '#f6ffed', border: '1px solid #b7eb8f', borderRadius: 6 }}>
          <Space>
            <Text strong>Missing Coordinates Action:</Text>
            <Button
              type="primary"
              size="small"
              icon={<AimOutlined />}
              loading={geocoding}
              onClick={handleGeocodeAll}
              disabled={geocoding}
            >
              🗺️ Geocode Missing Coordinates ({waypointsWithoutCoordinates.length})
            </Button>
            {geocoding && (
              <Text type="secondary">⏳ Processing addresses...</Text>
            )}
          </Space>
        </div>
      )}

      <div style={{ marginBottom: 12 }}>
        <Space>
          <Tag color="green">Low Difficulty</Tag>
          <Tag color="blue">Medium Difficulty</Tag>
          <Tag color="orange">High Difficulty</Tag>
          <Tag color="purple">Special Equipment</Tag>
        </Space>
      </div>

      <div style={{ height: '500px', border: '1px solid #d9d9d9', borderRadius: '6px' }}>
        <GoogleMapView
          markers={mapMarkers}
          onMarkerClick={handleMarkerClick}
          height="100%"
          width="100%"
          zoom={13}
        />
      </div>

      <div style={{ marginTop: 12, fontSize: '12px', color: '#666' }}>
        <Space split="•">
          <span>Click markers for details</span>
          <span>Map shows waypoints in sequence order</span>
          <span>Colors indicate access difficulty and requirements</span>
        </Space>
      </div>
    </Card>
  );
};

export default RoutePathMap; 