'use client';

import React, { useState, useEffect } from 'react';
import { routeService, RouteWaypointDto, CreateRouteWaypointDto } from '@/services/route.service';
import {
  Modal,
  Tabs,
  Table,
  Button,
  Form,
  Input,
  InputNumber,
  Select,
  message,
  Popconfirm,
  Space,
  Tag,
  Descriptions,
  Row,
  Col,
  Switch,
  Tooltip,
  Empty
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  DragOutlined,
  EnvironmentOutlined,
  ToolOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import { DndContext, closestCenter, KeyboardSensor, PointerSensor, useSensor, useSensors } from '@dnd-kit/core';
import { arrayMove, SortableContext, sortableKeyboardCoordinates, verticalListSortingStrategy } from '@dnd-kit/sortable';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import RoutePathMap from './RoutePathMap';

const { TabPane } = Tabs;
const { Option } = Select;
const { TextArea } = Input;

interface RouteDetailModalProps {
  visible: boolean;
  route: any | null;
  onCancel: () => void;
  onUpdate: () => void;
}

const RouteDetailModal: React.FC<RouteDetailModalProps> = ({
  visible,
  route,
  onCancel,
  onUpdate
}) => {
  const [waypoints, setWaypoints] = useState<RouteWaypointDto[]>([]);
  const [loading, setLoading] = useState(false);
  const [isAddWaypointModalVisible, setIsAddWaypointModalVisible] = useState(false);
  const [editingWaypoint, setEditingWaypoint] = useState<RouteWaypointDto | null>(null);
  const [availableMeters, setAvailableMeters] = useState<any[]>([]);
  const [loadingMeters, setLoadingMeters] = useState(false);
  const [form] = Form.useForm();

  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  useEffect(() => {
    if (visible && route) {
      loadWaypoints();
    }
  }, [visible, route]);

  const loadWaypoints = async () => {
    if (!route) return;
    
    setLoading(true);
    try {
      const waypoints = await routeService.getRouteWaypoints(route.id);
      setWaypoints(waypoints);
    } catch (error) {
      message.error('Failed to load waypoints');
      console.error('Error loading waypoints:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadAvailableMeters = async () => {
    setLoadingMeters(true);
    try {
      const meters = await routeService.getUnassignedMeters(1, 100); // Load first 100 unassigned meters
      setAvailableMeters(meters);
    } catch (error) {
      message.error('Failed to load available meters');
      console.error('Error loading available meters:', error);
    } finally {
      setLoadingMeters(false);
    }
  };

  const handleAddWaypoint = () => {
    setEditingWaypoint(null);
    form.resetFields();
    form.setFieldsValue({
      accessDifficulty: 1,
      requiresSpecialEquipment: false
    });
    setIsAddWaypointModalVisible(true);
    loadAvailableMeters(); // Load available meters when opening modal
  };

  const handleEditWaypoint = (waypoint: RouteWaypointDto) => {
    setEditingWaypoint(waypoint);
    form.setFieldsValue({
      waterMeterId: waypoint.waterMeterId,
      estimatedDuration: waypoint.estimatedDuration,
      notes: waypoint.notes,
      accessDifficulty: waypoint.accessDifficulty,
      requiresSpecialEquipment: waypoint.requiresSpecialEquipment,
      specialInstructions: waypoint.specialInstructions
    });
    setIsAddWaypointModalVisible(true);
  };

  const handleDeleteWaypoint = async (waypointId: number) => {
    try {
      await routeService.deleteWaypoint(waypointId);
      setWaypoints(waypoints.filter(w => w.id !== waypointId));
      message.success('Waypoint deleted successfully');
    } catch (error) {
      message.error('Failed to delete waypoint');
      console.error('Error deleting waypoint:', error);
    }
  };

  const handleSaveWaypoint = async (values: any) => {
    try {
      const waypointData: CreateRouteWaypointDto = {
        waterMeterId: values.waterMeterId,
        sequenceOrder: editingWaypoint ? editingWaypoint.sequenceOrder : waypoints.length + 1,
        estimatedDuration: values.estimatedDuration,
        notes: values.notes,
        accessDifficulty: values.accessDifficulty,
        requiresSpecialEquipment: values.requiresSpecialEquipment,
        specialInstructions: values.specialInstructions
      };

      if (editingWaypoint) {
        await routeService.updateWaypoint(editingWaypoint.id, waypointData);
        // Reload waypoints to get updated data from server
        await loadWaypoints();
        message.success('Waypoint updated successfully');
      } else {
        const newWaypoint = await routeService.addWaypoint(route.id, waypointData);
        setWaypoints([...waypoints, newWaypoint]);
        message.success('Waypoint added successfully');
      }
      
      setIsAddWaypointModalVisible(false);
      form.resetFields();
      onUpdate(); // Notify parent to update route data
    } catch (error) {
      message.error('Failed to save waypoint');
      console.error('Error saving waypoint:', error);
    }
  };

  const getDifficultyColor = (difficulty?: number) => {
    if (!difficulty) return 'default';
    if (difficulty <= 2) return 'green';
    if (difficulty <= 3) return 'orange';
    return 'red';
  };

  const getDifficultyText = (difficulty?: number) => {
    if (!difficulty) return 'Unknown';
    if (difficulty <= 2) return 'Easy';
    if (difficulty <= 3) return 'Medium';
    return 'Hard';
  };

  const waypointColumns: ColumnsType<RouteWaypointDto> = [
    {
      title: 'Sequence',
      dataIndex: 'sequenceOrder',
      width: 80,
      sorter: (a, b) => a.sequenceOrder - b.sequenceOrder,
      defaultSortOrder: 'ascend',
    },
    {
      title: 'Water Meter',
      dataIndex: 'waterMeterSerial',
      render: (text, record) => (
        <div>
          <div><strong>{text}</strong></div>
          <div style={{ fontSize: '12px', color: '#666' }}>ID: {record.waterMeterId}</div>
        </div>
      ),
    },
    {
      title: 'Customer',
      dataIndex: 'customerName',
      render: (text, record) => (
        <div>
          <div>{text || 'Unknown'}</div>
          <div style={{ fontSize: '12px', color: '#666' }}>{record.address}</div>
        </div>
      ),
    },
    {
      title: 'Duration',
      dataIndex: 'estimatedDuration',
      width: 80,
      render: (duration) => duration ? `${duration} min` : '-',
    },
    {
      title: 'Distance',
      dataIndex: 'distanceFromPrevious',
      width: 80,
      render: (distance) => distance ? `${distance} km` : '-',
    },
    {
      title: 'Difficulty',
      dataIndex: 'accessDifficulty',
      width: 100,
      render: (difficulty) => (
        <Tag color={getDifficultyColor(difficulty)}>
          {getDifficultyText(difficulty)}
        </Tag>
      ),
    },
    {
      title: 'Special Equipment',
      dataIndex: 'requiresSpecialEquipment',
      width: 120,
      render: (required) => (
        <Space>
          <ToolOutlined style={{ color: required ? '#f5222d' : '#d9d9d9' }} />
          <span>{required ? 'Yes' : 'None'}</span>
        </Space>
      ),
    },
    {
      title: 'Actions',
      width: 120,
      render: (_, record) => (
        <Space>
          <Tooltip title="Edit waypoint">
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => handleEditWaypoint(record)}
              size="small"
            />
          </Tooltip>
          <Tooltip title="View location">
            <Button
              type="text"
              icon={<EnvironmentOutlined />}
              onClick={() => {
                // You can add logic here to center map on this waypoint
                console.log('View location for waypoint:', record);
              }}
              size="small"
              disabled={!record.latitude || !record.longitude}
            />
          </Tooltip>
          <Popconfirm
            title="Are you sure you want to delete this waypoint?"
            onConfirm={() => handleDeleteWaypoint(record.id)}
            okText="Yes"
            cancelText="No"
          >
            <Tooltip title="Delete waypoint">
              <Button
                type="text"
                icon={<DeleteOutlined />}
                danger
                size="small"
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  const SortableRow = ({ children, ...props }: any) => {
    const {
      attributes,
      listeners,
      setNodeRef,
      transform,
      transition,
      isDragging,
    } = useSortable({
      id: props['data-row-key'],
    });

    const style = {
      transform: CSS.Transform.toString(transform),
      transition,
      ...(isDragging ? { position: 'relative' as const, zIndex: 9999 } : {}),
    };

    return (
      <tr {...props} ref={setNodeRef} style={style} {...attributes}>
        {React.Children.map(children, (child) => {
          if (child.key === 'drag') {
            return React.cloneElement(child, {
              children: (
                <div
                  {...listeners}
                  style={{
                    touchAction: 'none',
                    cursor: isDragging ? 'grabbing' : 'grab',
                  }}
                >
                  <DragOutlined />
                </div>
              ),
            });
          }
          return child;
        })}
      </tr>
    );
  };

  const handleDragEnd = async (event: any) => {
    const { active, over } = event;

    if (active.id !== over?.id) {
      const oldIndex = waypoints.findIndex(item => item.id === active.id);
      const newIndex = waypoints.findIndex(item => item.id === over.id);

      const updatedWaypoints = arrayMove(waypoints, oldIndex, newIndex).map((item, index) => ({
        ...item,
        sequenceOrder: index + 1
      }));
      
      setWaypoints(updatedWaypoints);
      
      try {
        await routeService.reorderWaypoints(route.id, updatedWaypoints.map(w => w.id));
        message.success('Waypoint order updated');
        onUpdate(); // Notify parent to update route data
      } catch (error) {
        message.error('Failed to update waypoint order');
        console.error('Error reordering waypoints:', error);
        // Revert changes
        setWaypoints(waypoints);
      }
    }
  };

  return (
    <>
      <Modal
        title={`Route Details - ${route?.name || 'Unknown'}`}
        open={visible}
        onCancel={onCancel}
        footer={null}
        width={1200}
        style={{ top: 20 }}
      >
        <Tabs defaultActiveKey="waypoints">
          <TabPane tab="Waypoints" key="waypoints">
            <div className="mb-4">
              <Row justify="space-between" align="middle">
                <Col>
                  <Space>
                    <InfoCircleOutlined />
                    <span>Drag rows to reorder waypoints</span>
                  </Space>
                </Col>
                <Col>
                  <Button
                    type="primary"
                    icon={<PlusOutlined />}
                    onClick={handleAddWaypoint}
                  >
                    Add Waypoint
                  </Button>
                </Col>
              </Row>
            </div>

            <DndContext
              sensors={sensors}
              collisionDetection={closestCenter}
              onDragEnd={handleDragEnd}
            >
              <SortableContext
                items={waypoints.map(w => w.id)}
                strategy={verticalListSortingStrategy}
              >
                <Table
                  components={{
                    body: {
                      row: SortableRow,
                    },
                  }}
                  rowKey="id"
                  columns={waypointColumns}
                  dataSource={waypoints}
                  loading={loading}
                  pagination={false}
                  size="small"
                  locale={{
                    emptyText: (
                      <Empty
                        image={Empty.PRESENTED_IMAGE_SIMPLE}
                        description={
                          <span>
                            No waypoints defined for this route<br />
                            Click "Add Waypoint" to get started
                          </span>
                        }
                      />
                    )
                  }}
                />
              </SortableContext>
            </DndContext>
          </TabPane>

          <TabPane tab="Route Info" key="info">
            <Descriptions column={2} bordered>
              <Descriptions.Item label="Route Name">{route?.name}</Descriptions.Item>
              <Descriptions.Item label="Status">
                <Tag color={route?.status === 'Active' ? 'green' : 'red'}>
                  {route?.status}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="Zone">{route?.zone || '-'}</Descriptions.Item>
              <Descriptions.Item label="Area">{route?.area || '-'}</Descriptions.Item>
              <Descriptions.Item label="Total Meters">{route?.totalMeters || 0}</Descriptions.Item>
              <Descriptions.Item label="Estimated Duration">
                {route?.estimatedDuration ? `${route.estimatedDuration} min` : '-'}
              </Descriptions.Item>
              <Descriptions.Item label="Estimated Distance">
                {route?.estimatedDistance ? `${route.estimatedDistance} km` : '-'}
              </Descriptions.Item>
              <Descriptions.Item label="Difficulty Rating">
                {route?.difficultyRating ? `${route.difficultyRating}/5` : '-'}
              </Descriptions.Item>
              <Descriptions.Item label="Assigned To">{route?.assignedTo || 'Unassigned'}</Descriptions.Item>
              <Descriptions.Item label="Created At">
                {route?.createdAt ? new Date(route.createdAt).toLocaleString() : '-'}
              </Descriptions.Item>
              <Descriptions.Item label="Description" span={2}>
                {route?.description || '-'}
              </Descriptions.Item>
            </Descriptions>
          </TabPane>

          <TabPane tab="Route Map" key="map">
            <RoutePathMap 
              waypoints={waypoints} 
              route={route} 
              loading={loading} 
              onWaypointsUpdate={loadWaypoints}
            />
          </TabPane>
        </Tabs>
      </Modal>

      {/* Add/Edit Waypoint Modal */}
      <Modal
        title={editingWaypoint ? 'Edit Waypoint' : 'Add Waypoint'}
        open={isAddWaypointModalVisible}
        onCancel={() => {
          setIsAddWaypointModalVisible(false);
          form.resetFields();
        }}
        onOk={() => form.submit()}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSaveWaypoint}
        >
          <Row gutter={16}>
            <Col span={24}>
              <Form.Item
                label="Water Meter"
                name="waterMeterId"
                rules={[{ required: true, message: 'Please select a water meter' }]}
              >
                <Select
                  placeholder="Select a water meter"
                  loading={loadingMeters}
                  showSearch
                  filterOption={(input, option) =>
                    option?.label?.toLowerCase().includes(input.toLowerCase()) ?? false
                  }
                  options={availableMeters.map(meter => ({
                    value: meter.id,
                    label: `${meter.serialNumber} - ${meter.customerName || 'Unknown'} (${meter.location || meter.fullAddress || 'No Address'})`,
                    key: meter.id
                  }))}
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="Estimated Duration (minutes)"
                name="estimatedDuration"
              >
                <InputNumber
                  min={1}
                  max={120}
                  placeholder="15"
                  style={{ width: '100%' }}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="Access Difficulty"
                name="accessDifficulty"
              >
                <Select placeholder="Select difficulty level">
                  <Option value={1}>Easy</Option>
                  <Option value={2}>Medium</Option>
                  <Option value={3}>Hard</Option>
                  <Option value={4}>Very Hard</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="requiresSpecialEquipment"
                valuePropName="checked"
              >
                <Space>
                  <Switch />
                  <span>Requires Special Equipment</span>
                </Space>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            label="Notes"
            name="notes"
          >
            <TextArea
              rows={3}
              placeholder="Enter any special notes or instructions..."
            />
          </Form.Item>

          <Form.Item
            label="Special Instructions"
            name="specialInstructions"
          >
            <TextArea
              rows={2}
              placeholder="Enter special instructions if any..."
            />
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
};

export default RouteDetailModal; 