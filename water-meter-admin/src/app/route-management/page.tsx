'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { 
  Table, 
  Button, 
  Input, 
  Select, 
  Modal, 
  Form, 
  message, 
  Space, 
  Card, 
  Row, 
  Col,
  Tag,
  Popconfirm,
  Tooltip,
  InputNumber,
  Switch,
  Tabs
} from 'antd';
import { 
  PlusOutlined, 
  SearchOutlined, 
  EditOutlined, 
  DeleteOutlined, 
  EyeOutlined,
  CopyOutlined,
  SettingOutlined,
  TableOutlined,
  EnvironmentOutlined
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import { routeService, RouteListDto, CreateRouteDto, RouteSearchDto } from '@/services/route.service';
import RouteMapView from './components/RouteMapView';
import RouteDetailModal from './components/RouteDetailModal';

const { Option } = Select;
const { TextArea } = Input;
const { TabPane } = Tabs;

export default function RouteManagementPage() {
  const [routes, setRoutes] = useState<RouteListDto[]>([]);
  const [loading, setLoading] = useState(false);
  const [total, setTotal] = useState(0);
  const [searchParams, setSearchParams] = useState<RouteSearchDto>({
    page: 1,
    pageSize: 10,
    sortBy: 'CreatedAt',
    sortDirection: 'desc'
  });

  const [isCreateModalVisible, setIsCreateModalVisible] = useState(false);
  const [isDetailModalVisible, setIsDetailModalVisible] = useState(false);
  const [selectedRoute, setSelectedRoute] = useState<RouteListDto | null>(null);
  const [form] = Form.useForm();
  const [searchForm] = Form.useForm();
  const [activeTab, setActiveTab] = useState('table');

  const loadRoutes = useCallback(async () => {
    setLoading(true);
    try {
      const response = await routeService.getRoutes(searchParams);
      setRoutes(response.routes);
      setTotal(response.totalCount);
    } catch (error) {
      message.error('Failed to load routes');
      console.error('Error loading routes:', error);
    } finally {
      setLoading(false);
    }
  }, [searchParams]);

  useEffect(() => {
    loadRoutes();
  }, [loadRoutes]);

  const handleCreate = async (values: CreateRouteDto) => {
    try {
      if (selectedRoute) {
        await routeService.updateRoute(selectedRoute.id, { ...values, id: selectedRoute.id });
        message.success('Route updated successfully');
      } else {
        await routeService.createRoute(values);
        message.success('Route created successfully');
      }
      
      setIsCreateModalVisible(false);
      setSelectedRoute(null);
      form.resetFields();
      loadRoutes();
    } catch (error) {
      message.error(selectedRoute ? 'Failed to update route' : 'Failed to create route');
      console.error('Error saving route:', error);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Active': return 'green';
      case 'Inactive': return 'red';
      default: return 'default';
    }
  };

  const getDifficultyColor = (rating?: number) => {
    if (!rating) return 'default';
    if (rating <= 2) return 'green';
    if (rating <= 3.5) return 'orange';
    return 'red';
  };

  const columns: ColumnsType<RouteListDto> = [
    {
      title: 'ID',
      dataIndex: 'id',
      width: 80,
    },
    {
      title: 'Route Name',
      dataIndex: 'name',
      render: (text, record) => (
        <div>
          {text}
          {record.isTemplate && <Tag color="blue">Template</Tag>}
        </div>
      ),
    },
    {
      title: 'Status',
      dataIndex: 'status',
      width: 120,
      render: (status) => (
        <Tag color={getStatusColor(status)}>{status}</Tag>
      ),
    },
    {
      title: 'Zone',
      dataIndex: 'zone',
      width: 100,
    },
    {
      title: 'Area',
      dataIndex: 'area',
      width: 150,
    },
    {
      title: 'Meters',
      dataIndex: 'totalMeters',
      width: 100,
    },
    {
      title: 'Distance (km)',
      dataIndex: 'estimatedDistance',
      width: 120,
      render: (distance) => distance ? `${distance} km` : '-',
    },
    {
      title: 'Difficulty',
      dataIndex: 'difficultyRating',
      width: 100,
      render: (rating) => rating ? (
        <Tag color={getDifficultyColor(rating)}>
          {rating.toFixed(1)}
        </Tag>
      ) : '-',
    },
    {
      title: 'Actions',
      width: 200,
      render: (_, record) => (
        <Space>
          <Tooltip title="View Details & Waypoints">
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() => {
                setSelectedRoute(record);
                setIsDetailModalVisible(true);
              }}
            />
          </Tooltip>
          <Tooltip title="Edit">
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => {
                setSelectedRoute(record);
                form.setFieldsValue(record);
                setIsCreateModalVisible(true);
              }}
            />
          </Tooltip>
          <Tooltip title="Optimize Route">
            <Button
              type="text"
              icon={<SettingOutlined />}
              onClick={async () => {
                try {
                  const result = await routeService.optimizeRoute(record.id);
                  if (result.success) {
                    message.success(result.message);
                    loadRoutes();
                  } else {
                    message.error(result.message);
                  }
                } catch (error) {
                  message.error('Failed to optimize route');
                }
              }}
            />
          </Tooltip>
          <Tooltip title="Duplicate">
            <Button
              type="text"
              icon={<CopyOutlined />}
              onClick={async () => {
                try {
                  const newName = `${record.name} - Copy`;
                  await routeService.duplicateRoute(record.id, newName);
                  message.success('Route duplicated successfully');
                  loadRoutes();
                } catch (error) {
                  message.error('Failed to duplicate route');
                }
              }}
            />
          </Tooltip>
          <Popconfirm
            title="Are you sure you want to delete this route?"
            onConfirm={async () => {
              try {
                await routeService.deleteRoute(record.id);
                message.success('Route deleted successfully');
                loadRoutes();
              } catch (error) {
                message.error('Failed to delete route');
              }
            }}
            okText="Yes"
            cancelText="No"
          >
            <Tooltip title="Delete">
              <Button
                type="text"
                danger
                icon={<DeleteOutlined />}
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div className="p-6">
      {/* Action Buttons */}
      <Card className="mb-4">
        <Space>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => setIsCreateModalVisible(true)}
          >
            Create Route
          </Button>
          <Button
            icon={<CopyOutlined />}
            onClick={async () => {
              try {
                const templates = await routeService.getRouteTemplates();
                message.success(`Found ${templates.length} route templates`);
              } catch (error) {
                message.error('Failed to load route templates');
              }
            }}
          >
            Route Templates
          </Button>
        </Space>
      </Card>

      {/* Routes Table and Map View */}
      <Card style={{ minHeight: '600px' }}>
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          items={[
            {
              key: 'table',
              label: (
                <span>
                  <TableOutlined />
                  Table View
                </span>
              ),
              children: (
                <Table
                  columns={columns}
                  dataSource={routes}
                  rowKey="id"
                  loading={loading}
                  pagination={{
                    current: searchParams.page,
                    pageSize: searchParams.pageSize,
                    total: total,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total) => `Total ${total} items`,
                  }}
                  onChange={(pagination) => {
                    setSearchParams({
                      ...searchParams,
                      page: pagination.current || 1,
                      pageSize: pagination.pageSize || 10,
                    });
                  }}
                  scroll={{ x: 1200 }}
                />
              ),
            },
            {
              key: 'map',
              label: (
                <span>
                  <EnvironmentOutlined />
                  Map View
                </span>
              ),
              children: (
                <RouteMapView
                  routes={routes}
                  selectedRoute={selectedRoute || undefined}
                  onRouteSelect={(route) => {
                    setSelectedRoute(route);
                    // Optionally switch to table view to show selected route details
                    // setActiveTab('table');
                  }}
                  loading={loading}
                />
              ),
            },
          ]}
        />
      </Card>

      {/* Create/Edit Route Modal */}
      <Modal
        title={selectedRoute ? 'Edit Route' : 'Create New Route'}
        open={isCreateModalVisible}
        onCancel={() => {
          setIsCreateModalVisible(false);
          setSelectedRoute(null);
          form.resetFields();
        }}
        footer={null}
        width={800}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleCreate}
          initialValues={{
            status: 'Active',
            isTemplate: false,
            difficultyRating: 1
          }}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="name"
                label="Route Name"
                rules={[{ required: true, message: 'Please enter route name' }]}
              >
                <Input placeholder="Enter route name" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="status" label="Status">
                <Select>
                  <Option value="Active">Active</Option>
                  <Option value="Inactive">Inactive</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item name="description" label="Description">
            <TextArea rows={3} placeholder="Enter route description" />
          </Form.Item>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item name="zone" label="Zone">
                <Input placeholder="Enter zone" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="area" label="Area">
                <Input placeholder="Enter area" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="assignedTo" label="Assigned To">
                <Input placeholder="Enter assignee" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item name="estimatedDistance" label="Estimated Distance (km)">
                <InputNumber
                  min={0}
                  step={0.1}
                  className="w-full"
                  placeholder="Enter distance"
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="estimatedDuration" label="Estimated Duration (min)">
                <InputNumber
                  min={0}
                  step={15}
                  className="w-full"
                  placeholder="Enter duration"
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="difficultyRating" label="Difficulty Rating (1-5)">
                <InputNumber
                  min={1}
                  max={5}
                  step={0.1}
                  className="w-full"
                  placeholder="Enter rating"
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item name="isTemplate" label="Is Template" valuePropName="checked">
                <Switch />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="templateCategory" label="Template Category">
                <Select placeholder="Select category" allowClear>
                  <Option value="Residential">Residential</Option>
                  <Option value="Commercial">Commercial</Option>
                  <Option value="Industrial">Industrial</Option>
                  <Option value="Mixed">Mixed</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item className="text-right">
            <Space>
              <Button onClick={() => {
                setIsCreateModalVisible(false);
                setSelectedRoute(null);
                form.resetFields();
              }}>
                Cancel
              </Button>
              <Button type="primary" htmlType="submit">
                {selectedRoute ? 'Update' : 'Create'}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* Route Detail Modal with Waypoint Management */}
      <RouteDetailModal
        visible={isDetailModalVisible}
        route={selectedRoute}
        onCancel={() => {
          setIsDetailModalVisible(false);
          setSelectedRoute(null);
        }}
        onUpdate={loadRoutes}
      />
    </div>
  );
} 