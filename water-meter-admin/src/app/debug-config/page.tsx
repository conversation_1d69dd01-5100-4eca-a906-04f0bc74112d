'use client'

import React, { useState } from 'react'
import { Card, Button, Space, message, Typography } from 'antd'
import configurationService from '../../services/configuration.service'

const { Title, Text, Paragraph } = Typography

export default function DebugConfigPage() {
  const [loading, setLoading] = useState(false)
  const [result, setResult] = useState<string>('')

  const testInitialize = async () => {
    try {
      setLoading(true)
      setResult('Testing initialize...')
      
      const success = await configurationService.initializeDefaults()
      
      if (success) {
        setResult('✅ Initialize successful')
        message.success('Initialize successful')
      } else {
        setResult('❌ Initialize failed')
        message.error('Initialize failed')
      }
    } catch (error) {
      setResult(`❌ Initialize error: ${error}`)
      message.error('Initialize error')
      console.error('Initialize error:', error)
    } finally {
      setLoading(false)
    }
  }

  const testClearAndReinitialize = async () => {
    try {
      setLoading(true)
      setResult('Testing clear and reinitialize...')
      
      const success = await configurationService.clearAndReinitialize()
      
      if (success) {
        setResult('✅ Clear and reinitialize successful')
        message.success('Clear and reinitialize successful')
      } else {
        setResult('❌ Clear and reinitialize failed')
        message.error('Clear and reinitialize failed')
      }
    } catch (error) {
      setResult(`❌ Clear and reinitialize error: ${error}`)
      message.error('Clear and reinitialize error')
      console.error('Clear and reinitialize error:', error)
    } finally {
      setLoading(false)
    }
  }

  const testGetSystemSettings = async () => {
    try {
      setLoading(true)
      setResult('Testing get system settings...')
      
      const settings = await configurationService.getSystemSettings()
      
      setResult(`✅ Got ${settings.length} categories:\n${JSON.stringify(settings, null, 2)}`)
      message.success(`Got ${settings.length} categories`)
    } catch (error) {
      setResult(`❌ Get settings error: ${error}`)
      message.error('Get settings error')
      console.error('Get settings error:', error)
    } finally {
      setLoading(false)
    }
  }

  const testApiConnection = async () => {
    try {
      setLoading(true)
      setResult('Testing API connection...')
      
      // Test basic API connection
      const response = await fetch('/api/configuration?scope=System')
      
      if (response.ok) {
        const data = await response.json()
        setResult(`✅ API connected. Response: ${JSON.stringify(data, null, 2)}`)
        message.success('API connected')
      } else {
        setResult(`❌ API failed with status: ${response.status}`)
        message.error(`API failed with status: ${response.status}`)
      }
    } catch (error) {
      setResult(`❌ API connection error: ${error}`)
      message.error('API connection error')
      console.error('API connection error:', error)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="p-6">
      <Title level={2}>Configuration Debug Page</Title>
      <Paragraph>Use this page to test configuration APIs directly.</Paragraph>
      
      <Card title="API Tests" className="mb-4">
        <Space direction="vertical" size="middle" style={{ width: '100%' }}>
          <Space>
            <Button onClick={testApiConnection} loading={loading}>
              Test API Connection
            </Button>
            <Button onClick={testGetSystemSettings} loading={loading}>
              Get System Settings
            </Button>
          </Space>
          
          <Space>
            <Button onClick={testInitialize} loading={loading} type="primary">
              Test Initialize
            </Button>
            <Button onClick={testClearAndReinitialize} loading={loading} danger>
              Test Clear & Reinitialize
            </Button>
          </Space>
        </Space>
      </Card>

      <Card title="Result" className="mb-4">
        <pre style={{ whiteSpace: 'pre-wrap', background: '#f5f5f5', padding: '12px', borderRadius: '4px' }}>
          {result || 'No test results yet...'}
        </pre>
      </Card>
    </div>
  )
} 