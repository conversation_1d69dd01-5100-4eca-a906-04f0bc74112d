'use client';

import React, { useState, useEffect, useCallback } from 'react';
import {
  Table, Button, Modal, Form, Input, Select, Card, Space, DatePicker,
  Tag, Row, Col, message, Tooltip, Popconfirm, Badge, Image,
  Progress, Upload, Tabs, List, Avatar, Divider, Typography,
  Rate, Alert, Checkbox, InputNumber
} from 'antd';
import {
  SearchOutlined, EyeOutlined, EditOutlined, DeleteOutlined,
  DownloadOutlined, UploadOutlined, PictureOutlined,
  FileTextOutlined, CheckOutlined, CloseOutlined,
  ExclamationCircleOutlined, ReloadOutlined, SettingOutlined,
  CameraOutlined, ScanOutlined, WarningOutlined
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import dayjs from 'dayjs';
import { 
  meterReadingService, 
  ReadingPhotoDto, 
  PhotoSearchDto, 
  OCRRecordDto
} from '@/services/meter-reading.service';

const { RangePicker } = DatePicker;
const { Option } = Select;
const { TextArea } = Input;
const { TabPane } = Tabs;
const { Text, Title } = Typography;

// Utility function to format file size
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

const PhotoManagement: React.FC = () => {
  const [photos, setPhotos] = useState<ReadingPhotoDto[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchForm] = Form.useForm();
  const [overrideForm] = Form.useForm();

  // Modal states
  const [photoDetailModalVisible, setPhotoDetailModalVisible] = useState(false);
  const [overrideModalVisible, setOverrideModalVisible] = useState(false);
  const [batchProcessModalVisible, setBatchProcessModalVisible] = useState(false);
  const [selectedPhoto, setSelectedPhoto] = useState<ReadingPhotoDto | null>(null);
  const [selectedPhotos, setSelectedPhotos] = useState<number[]>([]);

  // Search parameters
  const [searchParams, setSearchParams] = useState<PhotoSearchDto>({
    page: 1,
    pageSize: 12
  });

  // View mode
  const [viewMode, setViewMode] = useState<'grid' | 'table'>('grid');

  // Load photos
  const loadPhotos = useCallback(async () => {
    setLoading(true);
    try {
      // Use actual API call instead of mock
      const result = await meterReadingService.searchPhotos(searchParams);
      setPhotos(result);
    } catch (error) {
      message.error('Failed to load photos');
      console.error('Load photos error:', error);
      // Fallback to mock data with correct image URLs
      const mockPhotos: ReadingPhotoDto[] = [
        {
          id: 1,
          meterReadingId: 1,
          originalFileName: 'meter_001.jpg',
          cloudflareUrl: 'https://via.placeholder.com/300x200?text=Meter+001',
          thumbnailUrl: 'https://via.placeholder.com/150x150?text=Thumb+001',
          fileSize: 2621440,
          mimeType: 'image/jpeg',
          uploadTime: '2024-01-15T10:30:00Z',
          qualityScore: 0.95,
          qualityStatus: 'Excellent',
          isProcessed: true,
          hasOCR: true,
          ocrResult: '12345.67',
          ocrConfidence: 0.98,
          ocrStatus: 'Success',
          isOverridden: false,
          createdAt: '2024-01-15T10:30:00Z'
        },
        {
          id: 2,
          meterReadingId: 2,
          originalFileName: 'meter_002.jpg',
          cloudflareUrl: 'https://via.placeholder.com/300x200?text=Meter+002',
          thumbnailUrl: 'https://via.placeholder.com/150x150?text=Thumb+002',
          fileSize: 1887436,
          mimeType: 'image/jpeg',
          uploadTime: '2024-01-15T11:00:00Z',
          qualityScore: 0.78,
          qualityStatus: 'Good',
          isProcessed: true,
          hasOCR: true,
          ocrResult: '98765.43',
          ocrConfidence: 0.85,
          ocrStatus: 'Success',
          isOverridden: true,
          overriddenBy: 'john.doe',
          overriddenDate: '2024-01-15T11:00:00Z',
          overrideReason: 'Manual verification needed',
          createdAt: '2024-01-15T11:00:00Z'
        }
      ];
      setPhotos(mockPhotos);
    } finally {
      setLoading(false);
    }
  }, [searchParams]);

  useEffect(() => {
    loadPhotos();
  }, [loadPhotos]);

  // Handle search
  const handleSearch = (values: any) => {
    const newSearchParams = {
      ...searchParams,
      ...values,
      startDate: values.dateRange?.[0]?.format('YYYY-MM-DD'),
      endDate: values.dateRange?.[1]?.format('YYYY-MM-DD'),
      page: 1
    };
    delete newSearchParams.dateRange;
    setSearchParams(newSearchParams);
  };

  // Handle OCR override
  const handleOCROverride = async (values: any) => {
    if (!selectedPhoto) return;

    try {
      await meterReadingService.overrideOCRResult(selectedPhoto.id, values.correctedValue, values.reason);
      message.success('OCR result overridden successfully');
      setOverrideModalVisible(false);
      overrideForm.resetFields();
      setSelectedPhoto(null);
      loadPhotos();
    } catch (error) {
      message.error('Failed to override OCR result');
      console.error('Override OCR error:', error);
    }
  };

  // Handle delete photo
  const handleDeletePhoto = async (photoId: number) => {
    try {
      await meterReadingService.deletePhoto(photoId);
      message.success('Photo deleted successfully');
      loadPhotos();
    } catch (error) {
      message.error('Failed to delete photo');
      console.error('Delete photo error:', error);
    }
  };

  // Handle update photo quality
  const handleUpdateQuality = async (photoId: number, qualityScore: number, qualityStatus: string) => {
    try {
      await meterReadingService.updatePhotoQuality(photoId, qualityScore, qualityStatus);
      message.success('Photo quality updated successfully');
      loadPhotos();
    } catch (error) {
      message.error('Failed to update photo quality');
      console.error('Update quality error:', error);
    }
  };

  // Helper functions
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // Get quality status color
  const getQualityStatusColor = (status?: string) => {
    const colors = {
      'Excellent': 'green',
      'Good': 'blue',
      'Fair': 'orange',
      'Poor': 'red'
    };
    return colors[status as keyof typeof colors] || 'default';
  };

  // Get OCR status color
  const getOCRStatusColor = (status?: string) => {
    const colors = {
      'Success': 'green',
      'Failed': 'red',
      'Processing': 'orange',
      'Pending': 'gray'
    };
    return colors[status as keyof typeof colors] || 'default';
  };

  // Photo Grid Item Component
  const PhotoGridItem: React.FC<{ photo: ReadingPhotoDto }> = ({ photo }) => (
    <Card
      hoverable
      className="mb-4"
      cover={
        <div className="relative">
          <Image
            src={photo.thumbnailUrl || photo.cloudflareUrl}
            alt={photo.originalFileName}
            height={200}
            style={{ objectFit: 'cover' }}
            preview={false}
          />
          <div className="absolute top-2 right-2">
            <Tag color={getQualityStatusColor(photo.qualityStatus)}>
              {photo.qualityStatus}
            </Tag>
          </div>
        </div>
      }
      actions={[
        <EyeOutlined key="view" onClick={() => {
          setSelectedPhoto(photo);
          setPhotoDetailModalVisible(true);
        }} />,
        <EditOutlined key="edit" onClick={() => {
          setSelectedPhoto(photo);
          setOverrideModalVisible(true);
        }} />,
        <Popconfirm
          key="delete"
          title="Delete Photo"
          description="Are you sure you want to delete this photo?"
          onConfirm={() => handleDeletePhoto(photo.id)}
          okText="Yes"
          cancelText="No"
        >
          <DeleteOutlined />
        </Popconfirm>
      ]}
    >
      <Card.Meta
        title={
          <div className="flex justify-between items-center">
            <span className="text-sm">{photo.originalFileName}</span>
            {photo.hasOCR && (
              <Tag color={getOCRStatusColor(photo.ocrStatus)}>
                OCR
              </Tag>
            )}
          </div>
        }
        description={
          <div className="space-y-1">
            <div className="text-xs text-gray-600">
              Size: {formatFileSize(photo.fileSize)} | Quality: {photo.qualityScore ? (photo.qualityScore * 100).toFixed(0) : 'N/A'}%
            </div>
            {photo.ocrResult && (
              <div className="text-sm font-medium text-blue-600">
                Reading: {photo.ocrResult}
              </div>
            )}
            {photo.isOverridden && (
              <div className="text-xs text-orange-600">
                Override by {photo.overriddenBy}
              </div>
            )}
          </div>
        }
      />
    </Card>
  );

  return (
    <div className="p-6 space-y-6">
      <div className="flex justify-end items-center">
        <Space>
          <Button 
            type="primary" 
            icon={<UploadOutlined />}
            onClick={() => setBatchProcessModalVisible(true)}
          >
            Batch Upload
          </Button>
        </Space>
      </div>

      {/* Search Form */}
      <Card title="Search Filters" size="small">
        <Form 
          form={searchForm} 
          layout="inline" 
          onFinish={handleSearch}
          className="space-y-4"
        >
          <Form.Item name="meterNumber" label="Meter Number">
            <Input 
              placeholder="Enter meter number" 
              prefix={<SearchOutlined />}
            />
          </Form.Item>
          
          <Form.Item name="qualityStatus" label="Quality Status">
            <Select placeholder="Select quality" style={{ width: 150 }} allowClear>
              <Option value="Excellent">Excellent</Option>
              <Option value="Good">Good</Option>
              <Option value="Fair">Fair</Option>
              <Option value="Poor">Poor</Option>
            </Select>
          </Form.Item>
          
          <Form.Item name="ocrStatus" label="OCR Status">
            <Select placeholder="Select OCR status" style={{ width: 150 }} allowClear>
              <Option value="Success">Success</Option>
              <Option value="Failed">Failed</Option>
              <Option value="Processing">Processing</Option>
              <Option value="Pending">Pending</Option>
            </Select>
          </Form.Item>
          
          <Form.Item name="isOverridden" label="Override Status">
            <Select placeholder="Override status" style={{ width: 150 }} allowClear>
              <Option value={true}>Overridden</Option>
              <Option value={false}>Not Overridden</Option>
            </Select>
          </Form.Item>
          
          <Form.Item name="dateRange" label="Date Range">
            <RangePicker />
          </Form.Item>
          
          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit" icon={<SearchOutlined />}>
                Search
              </Button>
              <Button onClick={() => {
                searchForm.resetFields();
                setSearchParams({ page: 1, pageSize: 12 });
              }}>
                Reset
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Card>

      {/* View Controls */}
      <Card size="small">
        <div className="flex justify-between items-center">
          <div className="flex items-center space-x-4">
            <Text strong>View Mode:</Text>
            <Select
              value={viewMode}
              onChange={setViewMode}
              style={{ width: 120 }}
            >
              <Option value="grid">Grid View</Option>
              <Option value="table">Table View</Option>
            </Select>
          </div>
          
          <div className="flex items-center space-x-4">
            <Text type="secondary">
              Total: {photos.length} photos
            </Text>
            <Space>
              <Button 
                size="small" 
                icon={<ReloadOutlined />}
                onClick={loadPhotos}
                loading={loading}
              >
                Refresh
              </Button>
              <Button 
                size="small" 
                icon={<DownloadOutlined />}
              >
                Export
              </Button>
            </Space>
          </div>
        </div>
      </Card>

      {/* Photos Display */}
      {viewMode === 'grid' ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
          {photos.map(photo => (
            <PhotoGridItem key={photo.id} photo={photo} />
          ))}
        </div>
      ) : (
        <Table
          dataSource={photos}
          loading={loading}
          rowKey="id"
          columns={[
            {
              title: 'Photo',
              key: 'photo',
              render: (_, record) => (
                <div className="flex items-center space-x-2">
                  <Image
                    src={record.cloudflareUrl}
                    alt={record.originalFileName}
                    width={50}
                    height={50}
                    style={{ objectFit: 'cover' }}
                    preview={false}
                  />
                  <div>
                    <div className="font-medium">{record.originalFileName}</div>
                    <div className="text-xs text-gray-500">{formatFileSize(record.fileSize)}</div>
                  </div>
                </div>
              )
            },
            {
              title: 'Quality',
              dataIndex: 'qualityStatus',
              render: (status, record) => (
                <div>
                  <Tag color={getQualityStatusColor(status)}>{status || 'N/A'}</Tag>
                  <div className="text-xs text-gray-500">
                    {record.qualityScore ? (record.qualityScore * 100).toFixed(0) + '%' : 'N/A'}
                  </div>
                </div>
              )
            },
            {
              title: 'OCR Result',
              key: 'ocr',
              render: (_, record) => (
                <div>
                  {record.hasOCR ? (
                    <>
                      <div className="font-medium">{record.ocrResult}</div>
                      <div className="flex items-center space-x-1">
                        <Tag color={getOCRStatusColor(record.ocrStatus)}>
                          {record.ocrStatus}
                        </Tag>
                        <Text type="secondary" className="text-xs">
                          {Math.round((record.ocrConfidence || 0) * 100)}%
                        </Text>
                      </div>
                    </>
                  ) : (
                    <Text type="secondary">No OCR</Text>
                  )}
                </div>
              )
            },
            {
              title: 'Status',
              key: 'status',
              render: (_, record) => (
                <div>
                  {record.isOverridden && (
                    <Tag color="orange">Overridden</Tag>
                  )}
                  {record.isProcessed && (
                    <Tag color="green">Processed</Tag>
                  )}
                </div>
              )
            },
            {
              title: 'Actions',
              key: 'actions',
              render: (_, record) => (
                <Space>
                  <Button 
                    size="small" 
                    icon={<EyeOutlined />}
                    onClick={() => {
                      setSelectedPhoto(record);
                      setPhotoDetailModalVisible(true);
                    }}
                  />
                  <Button
                    size="small"
                    icon={<EditOutlined />}
                    onClick={() => {
                      setSelectedPhoto(record);
                      setOverrideModalVisible(true);
                    }}
                  />
                  <Popconfirm
                    title="Delete Photo"
                    description="Are you sure you want to delete this photo?"
                    onConfirm={() => handleDeletePhoto(record.id)}
                    okText="Yes"
                    cancelText="No"
                  >
                    <Button
                      size="small"
                      icon={<DeleteOutlined />}
                      danger
                    />
                  </Popconfirm>
                </Space>
              )
            }
          ]}
          pagination={{
            current: searchParams.page,
            pageSize: searchParams.pageSize,
            total: photos.length,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} photos`
          }}
        />
      )}

      {/* Photo Detail Modal */}
      <Modal
        title="Photo Details"
        open={photoDetailModalVisible}
        onCancel={() => {
          setPhotoDetailModalVisible(false);
          setSelectedPhoto(null);
        }}
        footer={null}
        width={800}
      >
        {selectedPhoto && (
          <div className="space-y-4">
            <div className="text-center">
              <Image
                src={selectedPhoto.cloudflareUrl}
                alt={selectedPhoto.originalFileName}
                style={{ maxHeight: 400 }}
              />
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Text strong>File Name:</Text> {selectedPhoto.originalFileName}
              </div>
              <div>
                <Text strong>File Size:</Text> {formatFileSize(selectedPhoto.fileSize)}
              </div>
              <div>
                <Text strong>Quality Score:</Text> {selectedPhoto.qualityScore ? (selectedPhoto.qualityScore * 100).toFixed(0) + '%' : 'N/A'}
              </div>
              <div>
                <Text strong>Quality Status:</Text>
                <Tag color={getQualityStatusColor(selectedPhoto.qualityStatus)} className="ml-2">
                  {selectedPhoto.qualityStatus || 'N/A'}
                </Tag>
              </div>
              {selectedPhoto.hasOCR && (
                <>
                  <div>
                    <Text strong>OCR Result:</Text> {selectedPhoto.ocrResult}
                  </div>
                  <div>
                    <Text strong>OCR Confidence:</Text> {Math.round((selectedPhoto.ocrConfidence || 0) * 100)}%
                  </div>
                </>
              )}
            </div>
          </div>
        )}
      </Modal>

      {/* OCR Override Modal */}
      <Modal
        title="Override OCR Result"
        open={overrideModalVisible}
        onCancel={() => {
          setOverrideModalVisible(false);
          setSelectedPhoto(null);
          overrideForm.resetFields();
        }}
        onOk={() => overrideForm.submit()}
      >
        <Form form={overrideForm} onFinish={handleOCROverride} layout="vertical">
          <Form.Item
            name="correctedValue"
            label="Corrected Value"
            rules={[{ required: true, message: 'Please enter the corrected value' }]}
          >
            <Input placeholder="Enter the correct reading value" />
          </Form.Item>
          <Form.Item
            name="reason"
            label="Reason for Override"
            rules={[{ required: true, message: 'Please provide a reason' }]}
          >
            <TextArea rows={3} placeholder="Explain why the OCR result needs to be overridden" />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default PhotoManagement;
 