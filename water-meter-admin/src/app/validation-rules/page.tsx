'use client';

import React, { useState, useEffect, useCallback } from 'react';
import {
  Table, Button, Modal, Form, Input, Select, Card, Space, Switch,
  Tag, Row, Col, message, Tooltip, Popconfirm, Badge, InputNumber,
  Collapse, Alert, Divider, Typography, Tabs, Progress, List, Avatar
} from 'antd';
import {
  SearchOutlined, PlusOutlined, EditOutlined, DeleteOutlined,
  SettingOutlined, ReloadOutlined, ExperimentOutlined,
  CheckCircleOutlined, CloseCircleOutlined, WarningOutlined,
  InfoCircleOutlined, LineChartOutlined, BulbOutlined, FireOutlined
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import { 
  meterReadingService, ValidationRuleDto, CreateValidationRuleDto
} from '@/services/meter-reading.service';

const { Option } = Select;
const { TextArea } = Input;
const { Panel } = Collapse;
const { Title, Text } = Typography;
const { TabPane } = Tabs;

const ValidationRules: React.FC = () => {
  const [rules, setRules] = useState<ValidationRuleDto[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchForm] = Form.useForm();
  const [createForm] = Form.useForm();
  const [editForm] = Form.useForm();

  // Modal states
  const [createModalVisible, setCreateModalVisible] = useState(false);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [testModalVisible, setTestModalVisible] = useState(false);
  const [selectedRule, setSelectedRule] = useState<ValidationRuleDto | null>(null);

  // Search and filter
  const [filteredRules, setFilteredRules] = useState<ValidationRuleDto[]>([]);
  const [activeTab, setActiveTab] = useState('all');

  // Load validation rules
  const loadRules = useCallback(async () => {
    setLoading(true);
    try {
      const rulesData = await meterReadingService.getValidationRules();
      setRules(rulesData);
      setFilteredRules(rulesData);
    } catch (error) {
      message.error('Failed to load validation rules');
      console.error('Load rules error:', error);
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    loadRules();
  }, [loadRules]);

  // Filter rules based on active tab
  useEffect(() => {
    let filtered = rules;
    switch (activeTab) {
      case 'active':
        filtered = rules.filter(rule => rule.isActive);
        break;
      case 'inactive':
        filtered = rules.filter(rule => !rule.isActive);
        break;
      case 'high-priority':
        filtered = rules.filter(rule => rule.priority >= 8);
        break;
      case 'low-effectiveness':
        filtered = rules.filter(rule => rule.effectivenessScore && rule.effectivenessScore < 70);
        break;
      default:
        filtered = rules;
    }
    setFilteredRules(filtered);
  }, [rules, activeTab]);

  // Handle create rule
  const handleCreateRule = async (values: any) => {
    try {
      const createDto: CreateValidationRuleDto = {
        ...values,
        isActive: values.isActive ?? true,
        priority: values.priority ?? 5,
        seasonalAdjustment: values.seasonalAdjustment ?? false,
        autoCorrect: values.autoCorrect ?? false
      };

      await meterReadingService.createValidationRule(createDto);
      message.success('Validation rule created successfully');
      setCreateModalVisible(false);
      createForm.resetFields();
      loadRules();
    } catch (error) {
      message.error('Failed to create validation rule');
      console.error('Create rule error:', error);
    }
  };

  // Handle edit rule
  const handleEditRule = async (values: any) => {
    if (!selectedRule) return;

    try {
      const updateDto: CreateValidationRuleDto = values;
      await meterReadingService.updateValidationRule(selectedRule.id, updateDto);
      message.success('Validation rule updated successfully');
      setEditModalVisible(false);
      editForm.resetFields();
      setSelectedRule(null);
      loadRules();
    } catch (error) {
      message.error('Failed to update validation rule');
      console.error('Update rule error:', error);
    }
  };

  // Handle delete rule
  const handleDeleteRule = async (rule: ValidationRuleDto) => {
    try {
      await meterReadingService.deleteValidationRule(rule.id);
      message.success('Validation rule deleted successfully');
      loadRules();
    } catch (error) {
      message.error('Failed to delete validation rule');
      console.error('Delete rule error:', error);
    }
  };

  // Get rule type color
  const getRuleTypeColor = (type: string) => {
    const colors = {
      'ToleranceCheck': 'blue',
      'AnomalyDetection': 'red',
      'ConsistencyValidation': 'green',
      'BusinessLogic': 'purple',
      'QualityAssurance': 'orange'
    };
    return colors[type as keyof typeof colors] || 'default';
  };

  // Get priority color
  const getPriorityColor = (priority: number) => {
    if (priority >= 9) return 'red';
    if (priority >= 7) return 'orange';
    if (priority >= 5) return 'blue';
    return 'gray';
  };

  // Get effectiveness color
  const getEffectivenessColor = (score?: number) => {
    if (!score) return 'gray';
    if (score >= 90) return 'green';
    if (score >= 70) return 'blue';
    if (score >= 50) return 'orange';
    return 'red';
  };

  // Table columns
  const columns: ColumnsType<ValidationRuleDto> = [
    {
      title: 'Rule Name',
      dataIndex: 'ruleName',
      key: 'ruleName',
      sorter: true,
      fixed: 'left',
      width: 200,
      render: (text: string, record: ValidationRuleDto) => (
        <div>
          <div className="font-medium">{text}</div>
          <div className="text-xs text-gray-500">{record.description}</div>
          <div className="mt-1">
            <Tag color={getRuleTypeColor(record.ruleType)} size="small">
              {record.ruleType}
            </Tag>
          </div>
        </div>
      ),
    },
    {
      title: 'Status',
      dataIndex: 'isActive',
      key: 'isActive',
      width: 100,
      render: (isActive: boolean) => (
        <Badge
          status={isActive ? 'success' : 'default'}
          text={isActive ? 'Active' : 'Inactive'}
        />
      ),
    },
    {
      title: 'Priority',
      dataIndex: 'priority',
      key: 'priority',
      sorter: true,
      width: 100,
      render: (priority: number) => (
        <Tag color={getPriorityColor(priority)}>
          {priority}/10
        </Tag>
      ),
    },
    {
      title: 'Scope',
      key: 'scope',
      width: 150,
      render: (_, record: ValidationRuleDto) => (
        <div className="text-xs">
          {record.meterType && (
            <div>Meter: {record.meterType}</div>
          )}
          {record.zone && (
            <div>Zone: {record.zone}</div>
          )}
          {record.customerType && (
            <div>Customer: {record.customerType}</div>
          )}
          {!record.meterType && !record.zone && !record.customerType && (
            <span className="text-gray-400">All</span>
          )}
        </div>
      ),
    },
    {
      title: 'Tolerance',
      key: 'tolerance',
      width: 150,
      render: (_, record: ValidationRuleDto) => (
        <div className="text-xs">
          {record.tolerancePercentage && (
            <div>±{record.tolerancePercentage}%</div>
          )}
          {record.minToleranceValue && record.maxToleranceValue && (
            <div>{record.minToleranceValue} - {record.maxToleranceValue}</div>
          )}
          {record.timeWindowDays && (
            <div>{record.timeWindowDays} days window</div>
          )}
        </div>
      ),
    },
    {
      title: 'Thresholds',
      key: 'thresholds',
      width: 120,
      render: (_, record: ValidationRuleDto) => (
        <div className="text-xs space-y-1">
          {record.warningThreshold && (
            <div className="text-orange-600">⚠ {record.warningThreshold}</div>
          )}
          {record.errorThreshold && (
            <div className="text-red-600">❌ {record.errorThreshold}</div>
          )}
          {record.criticalThreshold && (
            <div className="text-red-800">🚨 {record.criticalThreshold}</div>
          )}
        </div>
      ),
    },
    {
      title: 'Features',
      key: 'features',
      width: 120,
      render: (_, record: ValidationRuleDto) => (
        <Space direction="vertical" size="small">
          {record.seasonalAdjustment && (
            <Tag color="cyan" size="small" icon={<FireOutlined />}>
              Seasonal
            </Tag>
          )}
          {record.autoCorrect && (
            <Tag color="green" size="small" icon={<BulbOutlined />}>
              Auto Fix
            </Tag>
          )}
        </Space>
      ),
    },
    {
      title: 'Effectiveness',
      dataIndex: 'effectivenessScore',
      key: 'effectivenessScore',
      sorter: true,
      width: 120,
      render: (score?: number) => (
        <div>
          {score ? (
            <div>
              <Progress
                percent={score}
                size="small"
                strokeColor={getEffectivenessColor(score)}
                format={() => `${score.toFixed(1)}%`}
              />
            </div>
          ) : (
            <span className="text-gray-400">No data</span>
          )}
        </div>
      ),
    },
    {
      title: 'Actions',
      key: 'actions',
      fixed: 'right',
      width: 180,
      render: (_, record: ValidationRuleDto) => (
        <Space>
          <Tooltip title="Test Rule">
            <Button
              type="text"
              icon={<ExperimentOutlined />}
              onClick={() => {
                setSelectedRule(record);
                setTestModalVisible(true);
              }}
            />
          </Tooltip>

          <Tooltip title="Edit">
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => {
                setSelectedRule(record);
                editForm.setFieldsValue(record);
                setEditModalVisible(true);
              }}
            />
          </Tooltip>

          <Tooltip title="Toggle Status">
            <Button
              type="text"
              icon={record.isActive ? <CloseCircleOutlined /> : <CheckCircleOutlined />}
              onClick={async () => {
                try {
                  await meterReadingService.updateValidationRule(record.id, {
                    ...record,
                    isActive: !record.isActive
                  });
                  message.success(`Rule ${record.isActive ? 'deactivated' : 'activated'} successfully`);
                  loadRules();
                } catch (error) {
                  message.error('Failed to update rule status');
                }
              }}
            />
          </Tooltip>

          <Tooltip title="Delete">
            <Popconfirm
              title="Are you sure to delete this validation rule?"
              onConfirm={() => handleDeleteRule(record)}
              okText="Yes"
              cancelText="No"
            >
              <Button
                type="text"
                icon={<DeleteOutlined />}
                danger
              />
            </Popconfirm>
          </Tooltip>
        </Space>
      ),
    },
  ];

  // Rule configuration form component
  const RuleConfigForm: React.FC<{ form: any; isEdit?: boolean }> = ({ form, isEdit }) => (
    <Form form={form} layout="vertical" onFinish={isEdit ? handleEditRule : handleCreateRule}>
      <Tabs defaultActiveKey="basic">
        <TabPane tab="Basic Information" key="basic">
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="ruleName"
                label="Rule Name"
                rules={[{ required: true, message: 'Please enter rule name' }]}
              >
                <Input placeholder="Enter rule name" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="ruleType"
                label="Rule Type"
                rules={[{ required: true, message: 'Please select rule type' }]}
              >
                <Select placeholder="Select rule type">
                  <Option value="ToleranceCheck">Tolerance Check</Option>
                  <Option value="AnomalyDetection">Anomaly Detection</Option>
                  <Option value="ConsistencyValidation">Consistency Validation</Option>
                  <Option value="BusinessLogic">Business Logic</Option>
                  <Option value="QualityAssurance">Quality Assurance</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item name="description" label="Description">
            <TextArea rows={3} placeholder="Describe what this rule validates" />
          </Form.Item>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item name="priority" label="Priority (1-10)">
                <InputNumber min={1} max={10} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="isActive" label="Status" valuePropName="checked">
                <Switch checkedChildren="Active" unCheckedChildren="Inactive" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="autoCorrect" label="Auto Correct" valuePropName="checked">
                <Switch checkedChildren="Enabled" unCheckedChildren="Disabled" />
              </Form.Item>
            </Col>
          </Row>
        </TabPane>

        <TabPane tab="Scope & Filters" key="scope">
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item name="meterType" label="Meter Type">
                <Select placeholder="Select meter type" allowClear>
                  <Option value="Residential">Residential</Option>
                  <Option value="Commercial">Commercial</Option>
                  <Option value="Industrial">Industrial</Option>
                  <Option value="Municipal">Municipal</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="zone" label="Zone">
                <Input placeholder="Enter zone" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="customerType" label="Customer Type">
                <Select placeholder="Select customer type" allowClear>
                  <Option value="Domestic">Domestic</Option>
                  <Option value="Commercial">Commercial</Option>
                  <Option value="Industrial">Industrial</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>
        </TabPane>

        <TabPane tab="Tolerance Settings" key="tolerance">
          <Alert
            message="Tolerance Configuration"
            description="Set up tolerance ranges and validation thresholds for this rule."
            type="info"
            showIcon
            className="mb-4"
          />

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item name="tolerancePercentage" label="Tolerance Percentage">
                <InputNumber
                  min={0}
                  max={100}
                  precision={2}
                  suffix="%"
                  style={{ width: '100%' }}
                  placeholder="e.g., 10.5"
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="timeWindowDays" label="Time Window (Days)">
                <InputNumber
                  min={1}
                  max={365}
                  style={{ width: '100%' }}
                  placeholder="e.g., 30"
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item name="minToleranceValue" label="Min Tolerance Value">
                <InputNumber
                  min={0}
                  precision={2}
                  style={{ width: '100%' }}
                  placeholder="Minimum allowed value"
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="maxToleranceValue" label="Max Tolerance Value">
                <InputNumber
                  min={0}
                  precision={2}
                  style={{ width: '100%' }}
                  placeholder="Maximum allowed value"
                />
              </Form.Item>
            </Col>
          </Row>
        </TabPane>

        <TabPane tab="Thresholds" key="thresholds">
          <Alert
            message="Threshold Configuration"
            description="Define warning, error, and critical thresholds for different severity levels."
            type="warning"
            showIcon
            className="mb-4"
          />

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item name="warningThreshold" label="Warning Threshold">
                <InputNumber
                  min={0}
                  precision={2}
                  style={{ width: '100%' }}
                  placeholder="Warning level"
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="errorThreshold" label="Error Threshold">
                <InputNumber
                  min={0}
                  precision={2}
                  style={{ width: '100%' }}
                  placeholder="Error level"
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="criticalThreshold" label="Critical Threshold">
                <InputNumber
                  min={0}
                  precision={2}
                  style={{ width: '100%' }}
                  placeholder="Critical level"
                />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item name="actionOnViolation" label="Action on Violation">
            <Select placeholder="Select action" allowClear>
              <Option value="Warning">Show Warning</Option>
              <Option value="Block">Block Reading</Option>
              <Option value="Review">Require Review</Option>
              <Option value="AutoCorrect">Auto Correct</Option>
            </Select>
          </Form.Item>
        </TabPane>

        <TabPane tab="Seasonal Adjustment" key="seasonal">
          <Form.Item name="seasonalAdjustment" label="Enable Seasonal Adjustment" valuePropName="checked">
            <Switch checkedChildren="Enabled" unCheckedChildren="Disabled" />
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item name="summerMultiplier" label="Summer Multiplier">
                <InputNumber
                  min={0}
                  max={10}
                  precision={2}
                  step={0.1}
                  style={{ width: '100%' }}
                  placeholder="e.g., 1.2"
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="winterMultiplier" label="Winter Multiplier">
                <InputNumber
                  min={0}
                  max={10}
                  precision={2}
                  step={0.1}
                  style={{ width: '100%' }}
                  placeholder="e.g., 0.8"
                />
              </Form.Item>
            </Col>
          </Row>
        </TabPane>

        <TabPane tab="Advanced" key="advanced">
          <Form.Item name="notificationRecipients" label="Notification Recipients">
            <Input placeholder="Email addresses (comma-separated)" />
          </Form.Item>

          <Form.Item name="ruleConfiguration" label="Custom Configuration (JSON)">
            <TextArea
              rows={6}
              placeholder='{"custom": "configuration", "parameters": {}}'
            />
          </Form.Item>
        </TabPane>
      </Tabs>

      <Divider />
      
      <div className="flex justify-end space-x-2">
        <Button
          onClick={() => {
            if (isEdit) {
              setEditModalVisible(false);
              editForm.resetFields();
              setSelectedRule(null);
            } else {
              setCreateModalVisible(false);
              createForm.resetFields();
            }
          }}
        >
          Cancel
        </Button>
        <Button type="primary" htmlType="submit">
          {isEdit ? 'Update Rule' : 'Create Rule'}
        </Button>
      </div>
    </Form>
  );

  return (
    <div className="p-6">
      {/* Header */}
      <div className="mb-6">
        <div className="flex justify-between items-center mb-4">
          <Space>
            <Button icon={<ReloadOutlined />} onClick={loadRules}>
              Refresh
            </Button>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => setCreateModalVisible(true)}
            >
              Add Rule
            </Button>
          </Space>
        </div>

        {/* Quick Stats */}
        <Row gutter={16} className="mb-4">
          <Col span={6}>
            <Card size="small">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">{rules.length}</div>
                <div className="text-sm text-gray-500">Total Rules</div>
              </div>
            </Card>
          </Col>
          <Col span={6}>
            <Card size="small">
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">
                  {rules.filter(r => r.isActive).length}
                </div>
                <div className="text-sm text-gray-500">Active Rules</div>
              </div>
            </Card>
          </Col>
          <Col span={6}>
            <Card size="small">
              <div className="text-center">
                <div className="text-2xl font-bold text-orange-600">
                  {rules.filter(r => r.priority >= 8).length}
                </div>
                <div className="text-sm text-gray-500">High Priority</div>
              </div>
            </Card>
          </Col>
          <Col span={6}>
            <Card size="small">
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600">
                  {rules.filter(r => r.effectivenessScore && r.effectivenessScore >= 90).length}
                </div>
                <div className="text-sm text-gray-500">High Effectiveness</div>
              </div>
            </Card>
          </Col>
        </Row>
      </div>

      {/* Filter Tabs */}
      <Card className="mb-6">
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane tab={`All Rules (${rules.length})`} key="all" />
          <TabPane 
            tab={`Active (${rules.filter(r => r.isActive).length})`} 
            key="active" 
          />
          <TabPane 
            tab={`Inactive (${rules.filter(r => !r.isActive).length})`} 
            key="inactive" 
          />
          <TabPane 
            tab={`High Priority (${rules.filter(r => r.priority >= 8).length})`} 
            key="high-priority" 
          />
          <TabPane 
            tab={`Low Effectiveness (${rules.filter(r => r.effectivenessScore && r.effectivenessScore < 70).length})`} 
            key="low-effectiveness" 
          />
        </Tabs>
      </Card>

      {/* Rules Table */}
      <Card>
        <Table
          columns={columns}
          dataSource={filteredRules}
          rowKey="id"
          loading={loading}
          scroll={{ x: 1400 }}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} rules`,
          }}
        />
      </Card>

      {/* Create Rule Modal */}
      <Modal
        title="Create Validation Rule"
        visible={createModalVisible}
        onCancel={() => {
          setCreateModalVisible(false);
          createForm.resetFields();
        }}
        footer={null}
        width={800}
        destroyOnClose
      >
        <RuleConfigForm form={createForm} />
      </Modal>

      {/* Edit Rule Modal */}
      <Modal
        title="Edit Validation Rule"
        visible={editModalVisible}
        onCancel={() => {
          setEditModalVisible(false);
          editForm.resetFields();
          setSelectedRule(null);
        }}
        footer={null}
        width={800}
        destroyOnClose
      >
        <RuleConfigForm form={editForm} isEdit />
      </Modal>

      {/* Test Rule Modal */}
      <Modal
        title="Test Validation Rule"
        visible={testModalVisible}
        onCancel={() => {
          setTestModalVisible(false);
          setSelectedRule(null);
        }}
        footer={null}
        width={600}
      >
        {selectedRule && (
          <div className="space-y-4">
            <Alert
              message="Rule Testing"
              description="Enter test data to see how this rule would validate readings."
              type="info"
              showIcon
            />
            
            <div className="bg-gray-50 p-4 rounded">
              <Title level={5}>{selectedRule.ruleName}</Title>
              <Text type="secondary">{selectedRule.description}</Text>
            </div>

            {/* Test form would go here */}
            <div className="text-center text-gray-500 py-8">
              <ExperimentOutlined style={{ fontSize: 48 }} />
              <div className="mt-2">Rule testing functionality coming soon...</div>
            </div>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default ValidationRules; 