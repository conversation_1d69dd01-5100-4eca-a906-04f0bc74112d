'use client';

import React, { useState, useEffect } from 'react';
import { Card, Table, Button, Space, Tag, Progress, Statistic, Row, Col, Typography, Select, Input, Badge, Tooltip, Modal, message, Popconfirm } from 'antd';
import { PlayCircleOutlined, PauseCircleOutlined, StopOutlined, DeleteOutlined, ReloadOutlined, ClockCircleOutlined, CheckCircleOutlined, ExclamationCircleOutlined, PlusOutlined } from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import dayjs from 'dayjs';

const { Title, Text } = Typography;
const { Search } = Input;

interface SyncQueueItem {
  id: number;
  taskId: string;
  taskType: 'data_sync' | 'file_upload' | 'report_generation' | 'batch_update';
  deviceId?: string;
  deviceName?: string;
  description: string;
  priority: 'low' | 'medium' | 'high' | 'critical';
  status: 'pending' | 'running' | 'paused' | 'completed' | 'failed';
  progress: number;
  estimatedDuration: number;
  actualDuration?: number;
  createdAt: string;
  startedAt?: string;
  completedAt?: string;
  failureReason?: string;
  retryCount: number;
  maxRetries: number;
  dataSize?: number;
  processedItems: number;
  totalItems: number;
  createdBy: string;
  dependencies?: string[];
}

interface QueueStats {
  totalItems: number;
  pendingItems: number;
  runningItems: number;
  completedItems: number;
  failedItems: number;
  averageWaitTime: number;
  averageProcessingTime: number;
  queueThroughput: number;
}

export default function SyncQueuePage() {
  const [queueItems, setQueueItems] = useState<SyncQueueItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [stats, setStats] = useState<QueueStats | null>(null);
  const [selectedItems, setSelectedItems] = useState<number[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string | undefined>();
  const [priorityFilter, setPriorityFilter] = useState<string | undefined>();

  useEffect(() => {
    fetchQueueItems();
    fetchStats();
    
    // Auto-refresh every 10 seconds
    const interval = setInterval(() => {
      fetchQueueItems();
      fetchStats();
    }, 10000);
    
    return () => clearInterval(interval);
  }, []);

  const fetchQueueItems = async () => {
    setLoading(true);
    try {
      // Mock data - replace with actual API call
      const mockItems: SyncQueueItem[] = [
        {
          id: 1,
          taskId: 'SYNC001',
          taskType: 'data_sync',
          deviceId: 'DEV001',
          deviceName: 'Samsung Galaxy S21',
          description: 'Sync meter readings from North Area',
          priority: 'high',
          status: 'running',
          progress: 65,
          estimatedDuration: 300,
          actualDuration: 195,
          createdAt: dayjs().subtract(5, 'minutes').toISOString(),
          startedAt: dayjs().subtract(3, 'minutes').toISOString(),
          retryCount: 0,
          maxRetries: 3,
          dataSize: 1024 * 1024 * 15, // 15MB
          processedItems: 130,
          totalItems: 200,
          createdBy: 'John Smith'
        },
        {
          id: 2,
          taskId: 'UPLOAD002',
          taskType: 'file_upload',
          deviceId: 'DEV002',
          deviceName: 'iPad Pro',
          description: 'Upload photos and documents',
          priority: 'medium',
          status: 'pending',
          progress: 0,
          estimatedDuration: 180,
          createdAt: dayjs().subtract(2, 'minutes').toISOString(),
          retryCount: 0,
          maxRetries: 3,
          dataSize: 1024 * 1024 * 45, // 45MB
          processedItems: 0,
          totalItems: 25,
          createdBy: 'Sarah Johnson',
          dependencies: ['SYNC001']
        },
        {
          id: 3,
          taskId: 'REPORT003',
          taskType: 'report_generation',
          description: 'Generate monthly water consumption report',
          priority: 'low',
          status: 'completed',
          progress: 100,
          estimatedDuration: 120,
          actualDuration: 115,
          createdAt: dayjs().subtract(30, 'minutes').toISOString(),
          startedAt: dayjs().subtract(25, 'minutes').toISOString(),
          completedAt: dayjs().subtract(23, 'minutes').toISOString(),
          retryCount: 0,
          maxRetries: 2,
          processedItems: 1,
          totalItems: 1,
          createdBy: 'Admin'
        },
        {
          id: 4,
          taskId: 'BATCH004',
          taskType: 'batch_update',
          description: 'Update device configurations',
          priority: 'critical',
          status: 'failed',
          progress: 35,
          estimatedDuration: 600,
          actualDuration: 210,
          createdAt: dayjs().subtract(1, 'hour').toISOString(),
          startedAt: dayjs().subtract(55, 'minutes').toISOString(),
          retryCount: 2,
          maxRetries: 3,
          processedItems: 7,
          totalItems: 20,
          createdBy: 'Mike Wilson',
          failureReason: 'Network timeout during batch operation'
        }
      ];
      
      setQueueItems(mockItems);
    } catch (error) {
      console.error('Failed to fetch queue items:', error);
      message.error('Failed to load sync queue');
    } finally {
      setLoading(false);
    }
  };

  const fetchStats = async () => {
    try {
      // Mock stats - replace with actual API call
      const mockStats: QueueStats = {
        totalItems: 156,
        pendingItems: 23,
        runningItems: 5,
        completedItems: 118,
        failedItems: 10,
        averageWaitTime: 45,
        averageProcessingTime: 120,
        queueThroughput: 85
      };
      
      setStats(mockStats);
    } catch (error) {
      console.error('Failed to fetch queue stats:', error);
    }
  };

  const handleStart = async (item: SyncQueueItem) => {
    try {
      message.success(`Task ${item.taskId} started`);
      fetchQueueItems();
    } catch (error) {
      message.error('Failed to start task');
    }
  };

  const handlePause = async (item: SyncQueueItem) => {
    try {
      message.success(`Task ${item.taskId} paused`);
      fetchQueueItems();
    } catch (error) {
      message.error('Failed to pause task');
    }
  };

  const handleStop = async (item: SyncQueueItem) => {
    try {
      message.success(`Task ${item.taskId} stopped`);
      fetchQueueItems();
    } catch (error) {
      message.error('Failed to stop task');
    }
  };

  const handleRetry = async (item: SyncQueueItem) => {
    try {
      message.success(`Task ${item.taskId} queued for retry`);
      fetchQueueItems();
    } catch (error) {
      message.error('Failed to retry task');
    }
  };

  const handleDelete = async (item: SyncQueueItem) => {
    try {
      message.success(`Task ${item.taskId} removed from queue`);
      fetchQueueItems();
    } catch (error) {
      message.error('Failed to delete task');
    }
  };

  const handleBulkAction = async (action: 'start' | 'pause' | 'stop' | 'delete') => {
    if (selectedItems.length === 0) {
      message.warning('Please select items to perform bulk action');
      return;
    }

    try {
      message.success(`Bulk ${action} operation completed for ${selectedItems.length} items`);
      setSelectedItems([]);
      fetchQueueItems();
    } catch (error) {
      message.error(`Failed to perform bulk ${action}`);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'orange';
      case 'running':
        return 'blue';
      case 'paused':
        return 'purple';
      case 'completed':
        return 'green';
      case 'failed':
        return 'red';
      default:
        return 'default';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'critical':
        return 'red';
      case 'high':
        return 'orange';
      case 'medium':
        return 'blue';
      case 'low':
        return 'green';
      default:
        return 'default';
    }
  };

  const getTaskTypeIcon = (type: string) => {
    switch (type) {
      case 'data_sync':
        return '🔄';
      case 'file_upload':
        return '📤';
      case 'report_generation':
        return '📊';
      case 'batch_update':
        return '🔧';
      default:
        return '📋';
    }
  };

  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}m ${secs}s`;
  };

  const formatFileSize = (bytes: number) => {
    if (bytes < 1024 * 1024) {
      return `${(bytes / 1024).toFixed(1)} KB`;
    }
    return `${(bytes / 1024 / 1024).toFixed(1)} MB`;
  };

  const filteredItems = queueItems.filter(item => {
    const matchesSearch = !searchTerm || 
      item.taskId.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (item.deviceName && item.deviceName.toLowerCase().includes(searchTerm.toLowerCase()));
    
    const matchesStatus = !statusFilter || item.status === statusFilter;
    const matchesPriority = !priorityFilter || item.priority === priorityFilter;
    
    return matchesSearch && matchesStatus && matchesPriority;
  });

  const columns: ColumnsType<SyncQueueItem> = [
    {
      title: 'Task',
      key: 'task',
      render: (_, record) => (
        <Space>
          <span style={{ fontSize: '18px' }}>{getTaskTypeIcon(record.taskType)}</span>
          <div>
            <Text strong>{record.taskId}</Text>
            <br />
            <Text type="secondary" style={{ fontSize: '12px' }}>
              {record.taskType.replace('_', ' ')}
            </Text>
          </div>
        </Space>
      ),
    },
    {
      title: 'Description',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
      render: (description, record) => (
        <div>
          <Text>{description}</Text>
          {record.deviceName && (
            <>
              <br />
              <Text type="secondary" style={{ fontSize: '12px' }}>
                Device: {record.deviceName}
              </Text>
            </>
          )}
        </div>
      ),
    },
    {
      title: 'Priority',
      dataIndex: 'priority',
      key: 'priority',
      render: (priority) => (
        <Tag color={getPriorityColor(priority)}>
          {priority.toUpperCase()}
        </Tag>
      ),
      filters: [
        { text: 'Critical', value: 'critical' },
        { text: 'High', value: 'high' },
        { text: 'Medium', value: 'medium' },
        { text: 'Low', value: 'low' },
      ],
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status, record) => (
        <Space direction="vertical" size="small">
          <Tag color={getStatusColor(status)}>
            {status.toUpperCase()}
          </Tag>
          {status === 'running' && (
            <Progress percent={record.progress} size="small" />
          )}
          {record.failureReason && (
            <Tooltip title={record.failureReason}>
              <Text type="danger" style={{ fontSize: '12px' }}>
                Error
              </Text>
            </Tooltip>
          )}
        </Space>
      ),
      filters: [
        { text: 'Pending', value: 'pending' },
        { text: 'Running', value: 'running' },
        { text: 'Paused', value: 'paused' },
        { text: 'Completed', value: 'completed' },
        { text: 'Failed', value: 'failed' },
      ],
    },
    {
      title: 'Progress',
      key: 'progress',
      render: (_, record) => (
        <div>
          <Text style={{ fontSize: '12px' }}>
            {record.processedItems}/{record.totalItems} items
          </Text>
          <Progress 
            percent={(record.processedItems / record.totalItems) * 100} 
            size="small"
            showInfo={false}
          />
        </div>
      ),
    },
    {
      title: 'Duration',
      key: 'duration',
      render: (_, record) => (
        <div>
          {record.status === 'running' && record.actualDuration && (
            <Text>{formatDuration(record.actualDuration)} / {formatDuration(record.estimatedDuration)}</Text>
          )}
          {record.status === 'completed' && record.actualDuration && (
            <Text>{formatDuration(record.actualDuration)}</Text>
          )}
          {record.status === 'pending' && (
            <Text type="secondary">~{formatDuration(record.estimatedDuration)}</Text>
          )}
          {record.status === 'failed' && record.actualDuration && (
            <Text type="danger">{formatDuration(record.actualDuration)} (failed)</Text>
          )}
        </div>
      ),
    },
    {
      title: 'Data Size',
      dataIndex: 'dataSize',
      key: 'dataSize',
      render: (size) => size ? formatFileSize(size) : '-',
    },
    {
      title: 'Retries',
      key: 'retries',
      render: (_, record) => (
        <Text>
          {record.retryCount}/{record.maxRetries}
        </Text>
      ),
    },
    {
      title: 'Created',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (date) => (
        <Tooltip title={dayjs(date).format('YYYY-MM-DD HH:mm:ss')}>
          <Text>{dayjs(date).fromNow()}</Text>
        </Tooltip>
      ),
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record) => (
        <Space>
          {record.status === 'pending' && (
            <Tooltip title="Start">
              <Button
                type="link"
                icon={<PlayCircleOutlined />}
                onClick={() => handleStart(record)}
              />
            </Tooltip>
          )}
          {record.status === 'running' && (
            <>
              <Tooltip title="Pause">
                <Button
                  type="link"
                  icon={<PauseCircleOutlined />}
                  onClick={() => handlePause(record)}
                />
              </Tooltip>
              <Tooltip title="Stop">
                <Button
                  type="link"
                  icon={<StopOutlined />}
                  onClick={() => handleStop(record)}
                />
              </Tooltip>
            </>
          )}
          {record.status === 'paused' && (
            <Tooltip title="Resume">
              <Button
                type="link"
                icon={<PlayCircleOutlined />}
                onClick={() => handleStart(record)}
              />
            </Tooltip>
          )}
          {record.status === 'failed' && record.retryCount < record.maxRetries && (
            <Tooltip title="Retry">
              <Button
                type="link"
                icon={<ReloadOutlined />}
                onClick={() => handleRetry(record)}
              />
            </Tooltip>
          )}
          {(record.status === 'completed' || record.status === 'failed') && (
            <Popconfirm
              title="Are you sure to delete this task?"
              onConfirm={() => handleDelete(record)}
              okText="Yes"
              cancelText="No"
            >
              <Tooltip title="Delete">
                <Button
                  type="link"
                  danger
                  icon={<DeleteOutlined />}
                />
              </Tooltip>
            </Popconfirm>
          )}
        </Space>
      ),
    },
  ];

  const rowSelection = {
    selectedRowKeys: selectedItems,
    onChange: (selectedRowKeys: React.Key[]) => {
      setSelectedItems(selectedRowKeys as number[]);
    },
  };

  return (
    <div className="p-6">
      <div className="mb-6">
        <div className="flex justify-end items-center">
          <Button 
            type="primary" 
            icon={<ReloadOutlined />} 
            onClick={fetchQueueItems}
            loading={loading}
          >
            Refresh Queue
          </Button>
        </div>
      </div>

      {/* Statistics Cards */}
      {stats && (
        <Row gutter={16} className="mb-6">
          <Col span={6}>
            <Card>
              <Statistic
                title="Total Items"
                value={stats.totalItems}
                prefix={<ClockCircleOutlined />}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="Running"
                value={stats.runningItems}
                prefix={<PlayCircleOutlined />}
                valueStyle={{ color: '#1890ff' }}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="Completed"
                value={stats.completedItems}
                prefix={<CheckCircleOutlined />}
                valueStyle={{ color: '#3f8600' }}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="Throughput"
                value={stats.queueThroughput}
                suffix="%"
                prefix={<CheckCircleOutlined />}
                valueStyle={{ color: stats.queueThroughput > 80 ? '#3f8600' : '#faad14' }}
              />
            </Card>
          </Col>
        </Row>
      )}

      {/* Filters and Bulk Actions */}
      <Card className="mb-6">
        <Row gutter={16} align="middle">
          <Col span={8}>
            <Search
              placeholder="Search by task ID, description, or device"
              allowClear
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              style={{ width: '100%' }}
            />
          </Col>
          <Col span={4}>
            <Select
              placeholder="Filter by status"
              style={{ width: '100%' }}
              allowClear
              value={statusFilter}
              onChange={setStatusFilter}
            >
              <Select.Option value="pending">Pending</Select.Option>
              <Select.Option value="running">Running</Select.Option>
              <Select.Option value="paused">Paused</Select.Option>
              <Select.Option value="completed">Completed</Select.Option>
              <Select.Option value="failed">Failed</Select.Option>
            </Select>
          </Col>
          <Col span={4}>
            <Select
              placeholder="Filter by priority"
              style={{ width: '100%' }}
              allowClear
              value={priorityFilter}
              onChange={setPriorityFilter}
            >
              <Select.Option value="critical">Critical</Select.Option>
              <Select.Option value="high">High</Select.Option>
              <Select.Option value="medium">Medium</Select.Option>
              <Select.Option value="low">Low</Select.Option>
            </Select>
          </Col>
          <Col span={8}>
            <Space>
              <Text type="secondary">Bulk Actions:</Text>
              <Button
                size="small"
                icon={<PlayCircleOutlined />}
                onClick={() => handleBulkAction('start')}
                disabled={selectedItems.length === 0}
              >
                Start
              </Button>
              <Button
                size="small"
                icon={<PauseCircleOutlined />}
                onClick={() => handleBulkAction('pause')}
                disabled={selectedItems.length === 0}
              >
                Pause
              </Button>
              <Button
                size="small"
                danger
                icon={<DeleteOutlined />}
                onClick={() => handleBulkAction('delete')}
                disabled={selectedItems.length === 0}
              >
                Delete
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* Queue Table */}
      <Card>
        <Table
          columns={columns}
          dataSource={filteredItems}
          rowKey="id"
          loading={loading}
          rowSelection={rowSelection}
          pagination={{
            pageSize: 20,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} tasks`,
          }}
          scroll={{ x: 1400 }}
        />
      </Card>
    </div>
  );
} 