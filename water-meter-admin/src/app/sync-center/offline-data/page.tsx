'use client';

import React, { useState, useEffect } from 'react';
import { Card, Table, Button, Space, Tag, Progress, Statistic, Row, Col, Typography, Select, Input, Badge, Tooltip, Modal, message, DatePicker } from 'antd';
import { DatabaseOutlined, CloudUploadOutlined, DownloadOutlined, ExclamationCircleOutlined, CheckCircleOutlined, SyncOutlined, ReloadOutlined, DeleteOutlined } from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import { syncCenterService } from '@/services/synccenter.service';
import type { OfflineDataDto, OfflineDataSearchParams, OfflineDataStats } from '@/types/synccenter';

const { Title, Text } = Typography;
const { Search } = Input;
const { RangePicker } = DatePicker;

export default function OfflineDataPage() {
  const [offlineData, setOfflineData] = useState<OfflineDataDto[]>([]);
  const [loading, setLoading] = useState(false);
  const [uploading, setUploading] = useState(false);
  const [searchParams, setSearchParams] = useState<OfflineDataSearchParams>({});
  const [stats, setStats] = useState<OfflineDataStats | null>(null);
  const [selectedRows, setSelectedRows] = useState<number[]>([]);

  useEffect(() => {
    fetchOfflineData();
    fetchStats();
    // Auto refresh every 60 seconds
    const interval = setInterval(() => {
      fetchOfflineData();
      fetchStats();
    }, 60000);
    
    return () => clearInterval(interval);
  }, []);

  const fetchOfflineData = async () => {
    setLoading(true);
    try {
      const data = await syncCenterService.getOfflineData(searchParams);
      setOfflineData(data);
    } catch (error) {
      console.error('Failed to fetch offline data:', error);
      message.error('Failed to load offline data');
    } finally {
      setLoading(false);
    }
  };

  const fetchStats = async () => {
    try {
      const data = await syncCenterService.getOfflineDataStats();
      setStats(data);
    } catch (error) {
      console.error('Failed to fetch offline data stats:', error);
    }
  };

  const handleUploadSelected = async () => {
    if (selectedRows.length === 0) {
      message.warning('Please select items to upload');
      return;
    }
    
    setUploading(true);
    try {
      await syncCenterService.uploadOfflineData(selectedRows);
      message.success(`${selectedRows.length} items uploaded successfully`);
      setSelectedRows([]);
      fetchOfflineData();
      fetchStats();
    } catch (error) {
      console.error('Failed to upload offline data:', error);
      message.error('Failed to upload offline data');
    } finally {
      setUploading(false);
    }
  };

  const handleDeleteSelected = async () => {
    if (selectedRows.length === 0) {
      message.warning('Please select items to delete');
      return;
    }

    Modal.confirm({
      title: 'Delete Offline Data',
      content: `Are you sure you want to delete ${selectedRows.length} selected items? This action cannot be undone.`,
      onOk: async () => {
        try {
          await syncCenterService.deleteOfflineData(selectedRows);
          message.success(`${selectedRows.length} items deleted successfully`);
          setSelectedRows([]);
          fetchOfflineData();
          fetchStats();
        } catch (error) {
          console.error('Failed to delete offline data:', error);
          message.error('Failed to delete offline data');
        }
      },
    });
  };

  const handleCleanupOldData = async () => {
    Modal.confirm({
      title: 'Cleanup Old Data',
      content: 'This will delete all offline data older than 30 days. Are you sure?',
      onOk: async () => {
        try {
          await syncCenterService.cleanupOldOfflineData();
          message.success('Old offline data cleaned up successfully');
          fetchOfflineData();
          fetchStats();
        } catch (error) {
          console.error('Failed to cleanup old data:', error);
          message.error('Failed to cleanup old data');
        }
      },
    });
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'stored':
        return 'blue';
      case 'uploading':
        return 'orange';
      case 'uploaded':
        return 'green';
      case 'failed':
        return 'red';
      case 'conflict':
        return 'purple';
      default:
        return 'default';
    }
  };

  const getDataTypeIcon = (dataType: string) => {
    switch (dataType.toLowerCase()) {
      case 'meter_reading':
        return '📊';
      case 'photo':
        return '📷';
      case 'task_data':
        return '📝';
      case 'user_settings':
        return '⚙️';
      case 'location_data':
        return '📍';
      default:
        return '📋';
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes < 1024) return `${bytes} B`;
    if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(1)} KB`;
    return `${(bytes / 1024 / 1024).toFixed(1)} MB`;
  };

  const columns: ColumnsType<OfflineDataDto> = [
    {
      title: 'Type',
      dataIndex: 'dataType',
      key: 'dataType',
      render: (type) => (
        <Space>
          <span>{getDataTypeIcon(type)}</span>
          <Text>{type.replace('_', ' ').toUpperCase()}</Text>
        </Space>
      ),
    },
    {
      title: 'Description',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
    },
    {
      title: 'Device',
      dataIndex: 'deviceId',
      key: 'deviceId',
      render: (deviceId) => <Text code>{deviceId}</Text>,
    },
    {
      title: 'Size',
      dataIndex: 'dataSize',
      key: 'dataSize',
      render: (size) => formatFileSize(size),
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <Tag color={getStatusColor(status)}>
          {status === 'uploading' && <SyncOutlined spin />}
          {status.toUpperCase()}
        </Tag>
      ),
    },
    {
      title: 'Storage Location',
      dataIndex: 'storageLocation',
      key: 'storageLocation',
      render: (location) => <Text type="secondary">{location}</Text>,
    },
    {
      title: 'Created Date',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (date) => new Date(date).toLocaleString(),
    },
    {
      title: 'Last Modified',
      dataIndex: 'lastModified',
      key: 'lastModified',
      render: (date) => new Date(date).toLocaleString(),
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record) => (
        <Space>
          {record.status === 'stored' && (
            <Tooltip title="Upload Now">
              <Button
                type="link"
                icon={<CloudUploadOutlined />}
                onClick={() => syncCenterService.uploadSingleOfflineData(record.id)}
              />
            </Tooltip>
          )}
          <Tooltip title="Download">
            <Button
              type="link"
              icon={<DownloadOutlined />}
              onClick={() => syncCenterService.downloadOfflineData(record.id)}
            />
          </Tooltip>
          <Tooltip title="Delete">
            <Button
              type="link"
              danger
              icon={<DeleteOutlined />}
              onClick={() => syncCenterService.deleteOfflineData([record.id])}
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  const rowSelection = {
    selectedRowKeys: selectedRows,
    onChange: (selectedRowKeys: React.Key[]) => {
      setSelectedRows(selectedRowKeys as number[]);
    },
    getCheckboxProps: (record: OfflineDataDto) => ({
      disabled: record.status === 'uploading',
    }),
  };

  return (
    <div className="p-6">
      <div className="mb-6">
        <div className="flex justify-end items-center">
          <Space>
            <Button 
              icon={<ReloadOutlined />} 
              onClick={fetchOfflineData}
              loading={loading}
            >
              Refresh
            </Button>
            <Button 
              onClick={handleCleanupOldData}
            >
              Cleanup Old Data
            </Button>
            <Button 
              type="primary" 
              icon={<CloudUploadOutlined />} 
              onClick={handleUploadSelected}
              loading={uploading}
              disabled={selectedRows.length === 0}
            >
              Upload Selected ({selectedRows.length})
            </Button>
          </Space>
        </div>
      </div>

      {/* Statistics Cards */}
      {stats && (
        <Row gutter={16} className="mb-6">
          <Col span={6}>
            <Card>
              <Statistic
                title="Total Offline Items"
                value={stats.totalItems}
                prefix={<DatabaseOutlined />}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="Pending Upload"
                value={stats.pendingUpload}
                prefix={<CloudUploadOutlined />}
                valueStyle={{ color: '#1890ff' }}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="Failed Items"
                value={stats.failedItems}
                prefix={<ExclamationCircleOutlined />}
                valueStyle={{ color: '#cf1322' }}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="Storage Used"
                value={stats.totalStorageSize}
                precision={1}
                suffix="MB"
                prefix={<DatabaseOutlined />}
              />
            </Card>
          </Col>
        </Row>
      )}

      {/* Control Panel */}
      <Card className="mb-6">
        <Row gutter={16} align="middle">
          <Col span={6}>
            <Search
              placeholder="Search by description or device"
              allowClear
              onSearch={(value) => setSearchParams({ ...searchParams, searchTerm: value })}
            />
          </Col>
          <Col span={4}>
            <Select
              placeholder="Filter by status"
              style={{ width: '100%' }}
              allowClear
              onChange={(status) => setSearchParams({ ...searchParams, status })}
            >
              <Select.Option value="stored">Stored</Select.Option>
              <Select.Option value="uploading">Uploading</Select.Option>
              <Select.Option value="uploaded">Uploaded</Select.Option>
              <Select.Option value="failed">Failed</Select.Option>
              <Select.Option value="conflict">Conflict</Select.Option>
            </Select>
          </Col>
          <Col span={4}>
            <Select
              placeholder="Filter by type"
              style={{ width: '100%' }}
              allowClear
              onChange={(dataType) => setSearchParams({ ...searchParams, dataType })}
            >
              <Select.Option value="meter_reading">Meter Reading</Select.Option>
              <Select.Option value="photo">Photo</Select.Option>
              <Select.Option value="task_data">Task Data</Select.Option>
              <Select.Option value="user_settings">User Settings</Select.Option>
              <Select.Option value="location_data">Location Data</Select.Option>
            </Select>
          </Col>
          <Col span={6}>
            <RangePicker
              style={{ width: '100%' }}
              placeholder={['Start Date', 'End Date']}
              onChange={(dates) => setSearchParams({ 
                ...searchParams, 
                startDate: dates?.[0]?.format('YYYY-MM-DD'),
                endDate: dates?.[1]?.format('YYYY-MM-DD')
              })}
            />
          </Col>
          <Col span={4}>
            <Space>
              <Button 
                danger
                onClick={handleDeleteSelected}
                disabled={selectedRows.length === 0}
                icon={<DeleteOutlined />}
              >
                Delete Selected
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* Data Table */}
      <Card>
        <Table
          columns={columns}
          dataSource={offlineData}
          rowKey="id"
          loading={loading}
          rowSelection={rowSelection}
          pagination={{
            pageSize: 20,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`,
          }}
          scroll={{ x: 1400 }}
        />
      </Card>
    </div>
  );
} 