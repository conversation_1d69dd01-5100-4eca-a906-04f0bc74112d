'use client';

import React, { useState, useEffect } from 'react';
import { Card, Table, Button, Space, Tag, Alert, Row, Col, Typography, Select, Input, DatePicker, Modal, message } from 'antd';
import { FileTextOutlined, DownloadOutlined, DeleteOutlined, ExclamationCircleOutlined, CheckCircleOutlined, InfoCircleOutlined, WarningOutlined, ReloadOutlined } from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import { syncCenterService } from '@/services/synccenter.service';
import type { SyncLogDto, SyncLogSearchParams, SyncLogStats } from '@/types/synccenter';

const { Title, Text } = Typography;
const { Search } = Input;
const { RangePicker } = DatePicker;

export default function SyncLogsPage() {
  const [syncLogs, setSyncLogs] = useState<SyncLogDto[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchParams, setSearchParams] = useState<SyncLogSearchParams>({});
  const [stats, setStats] = useState<SyncLogStats | null>(null);
  const [selectedRows, setSelectedRows] = useState<number[]>([]);
  const [logDetailVisible, setLogDetailVisible] = useState(false);
  const [selectedLog, setSelectedLog] = useState<SyncLogDto | null>(null);

  useEffect(() => {
    fetchSyncLogs();
    fetchStats();
    // Auto refresh every 30 seconds
    const interval = setInterval(() => {
      fetchSyncLogs();
      fetchStats();
    }, 30000);
    
    return () => clearInterval(interval);
  }, []);

  const fetchSyncLogs = async () => {
    setLoading(true);
    try {
      const data = await syncCenterService.getSyncLogs(searchParams);
      setSyncLogs(data);
    } catch (error) {
      console.error('Failed to fetch sync logs:', error);
      message.error('Failed to load sync logs');
    } finally {
      setLoading(false);
    }
  };

  const fetchStats = async () => {
    try {
      const data = await syncCenterService.getSyncLogStats();
      setStats(data);
    } catch (error) {
      console.error('Failed to fetch sync log stats:', error);
    }
  };

  const handleExportLogs = async () => {
    try {
      await syncCenterService.exportSyncLogs(searchParams);
      message.success('Sync logs exported successfully');
    } catch (error) {
      console.error('Failed to export sync logs:', error);
      message.error('Failed to export sync logs');
    }
  };

  const handleClearOldLogs = async () => {
    Modal.confirm({
      title: 'Clear Old Logs',
      content: 'This will delete all sync logs older than 90 days. Are you sure?',
      onOk: async () => {
        try {
          await syncCenterService.clearOldSyncLogs();
          message.success('Old sync logs cleared successfully');
          fetchSyncLogs();
          fetchStats();
        } catch (error) {
          console.error('Failed to clear old logs:', error);
          message.error('Failed to clear old logs');
        }
      },
    });
  };

  const handleDeleteSelected = async () => {
    if (selectedRows.length === 0) {
      message.warning('Please select logs to delete');
      return;
    }

    Modal.confirm({
      title: 'Delete Selected Logs',
      content: `Are you sure you want to delete ${selectedRows.length} selected logs? This action cannot be undone.`,
      onOk: async () => {
        try {
          await syncCenterService.deleteSyncLogs(selectedRows);
          message.success(`${selectedRows.length} logs deleted successfully`);
          setSelectedRows([]);
          fetchSyncLogs();
          fetchStats();
        } catch (error) {
          console.error('Failed to delete logs:', error);
          message.error('Failed to delete logs');
        }
      },
    });
  };

  const getLevelColor = (level: string) => {
    switch (level.toLowerCase()) {
      case 'info':
        return 'blue';
      case 'warning':
        return 'orange';
      case 'error':
        return 'red';
      case 'success':
        return 'green';
      case 'debug':
        return 'purple';
      default:
        return 'default';
    }
  };

  const getLevelIcon = (level: string) => {
    switch (level.toLowerCase()) {
      case 'info':
        return <InfoCircleOutlined />;
      case 'warning':
        return <WarningOutlined />;
      case 'error':
        return <ExclamationCircleOutlined />;
      case 'success':
        return <CheckCircleOutlined />;
      default:
        return <FileTextOutlined />;
    }
  };

  const formatDuration = (duration: number) => {
    if (duration < 1000) return `${duration}ms`;
    if (duration < 60000) return `${(duration / 1000).toFixed(1)}s`;
    return `${(duration / 60000).toFixed(1)}m`;
  };

  const columns: ColumnsType<SyncLogDto> = [
    {
      title: 'Level',
      dataIndex: 'level',
      key: 'level',
      width: 100,
      render: (level) => (
        <Tag color={getLevelColor(level)} icon={getLevelIcon(level)}>
          {level.toUpperCase()}
        </Tag>
      ),
    },
    {
      title: 'Operation',
      dataIndex: 'operation',
      key: 'operation',
      width: 150,
      render: (operation) => <Text strong>{operation}</Text>,
    },
    {
      title: 'Message',
      dataIndex: 'message',
      key: 'message',
      ellipsis: true,
      render: (message, record) => (
        <div 
          style={{ cursor: 'pointer' }} 
          onClick={() => {
            setSelectedLog(record);
            setLogDetailVisible(true);
          }}
        >
          <Text>{message}</Text>
        </div>
      ),
    },
    {
      title: 'Device',
      dataIndex: 'deviceId',
      key: 'deviceId',
      width: 120,
      render: (deviceId) => deviceId ? <Text code>{deviceId}</Text> : '-',
    },
    {
      title: 'User',
      dataIndex: 'userId',
      key: 'userId',
      width: 120,
      render: (userId) => userId ? <Text>{userId}</Text> : 'System',
    },
    {
      title: 'Duration',
      dataIndex: 'duration',
      key: 'duration',
      width: 100,
      render: (duration) => duration ? formatDuration(duration) : '-',
    },
    {
      title: 'Data Size',
      dataIndex: 'dataSize',
      key: 'dataSize',
      width: 100,
      render: (size) => {
        if (!size) return '-';
        if (size < 1024) return `${size} B`;
        if (size < 1024 * 1024) return `${(size / 1024).toFixed(1)} KB`;
        return `${(size / 1024 / 1024).toFixed(1)} MB`;
      },
    },
    {
      title: 'Timestamp',
      dataIndex: 'timestamp',
      key: 'timestamp',
      width: 180,
      render: (timestamp) => new Date(timestamp).toLocaleString(),
    },
    {
      title: 'Actions',
      key: 'actions',
      width: 120,
      render: (_, record) => (
        <Space>
          <Button
            type="link"
            icon={<FileTextOutlined />}
            onClick={() => {
              setSelectedLog(record);
              setLogDetailVisible(true);
            }}
            title="View Details"
          />
          {record.stackTrace && (
            <Button
              type="link"
              icon={<DownloadOutlined />}
              onClick={() => syncCenterService.downloadLogDetails(record.id)}
              title="Download Details"
            />
          )}
        </Space>
      ),
    },
  ];

  const rowSelection = {
    selectedRowKeys: selectedRows,
    onChange: (selectedRowKeys: React.Key[]) => {
      setSelectedRows(selectedRowKeys as number[]);
    },
  };

  return (
    <div className="p-6">
      <div className="mb-6">
        <div className="flex justify-end items-center">
          <Space>
            <Button 
              icon={<ReloadOutlined />} 
              onClick={fetchSyncLogs}
              loading={loading}
            >
              Refresh
            </Button>
            <Button 
              icon={<DownloadOutlined />} 
              onClick={handleExportLogs}
            >
              Export Logs
            </Button>
            <Button 
              onClick={handleClearOldLogs}
            >
              Clear Old Logs
            </Button>
          </Space>
        </div>
      </div>

      {/* Statistics Cards */}
      {stats && (
        <Row gutter={16} className="mb-6">
          <Col span={6}>
            <Card>
              <div className="flex items-center">
                <InfoCircleOutlined className="text-2xl text-blue-500 mr-3" />
                <div>
                  <div className="text-2xl font-bold">{stats.totalLogs}</div>
                  <div className="text-gray-500">Total Logs</div>
                </div>
              </div>
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <div className="flex items-center">
                <ExclamationCircleOutlined className="text-2xl text-red-500 mr-3" />
                <div>
                  <div className="text-2xl font-bold">{stats.errorLogs}</div>
                  <div className="text-gray-500">Error Logs</div>
                </div>
              </div>
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <div className="flex items-center">
                <WarningOutlined className="text-2xl text-orange-500 mr-3" />
                <div>
                  <div className="text-2xl font-bold">{stats.warningLogs}</div>
                  <div className="text-gray-500">Warning Logs</div>
                </div>
              </div>
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <div className="flex items-center">
                <CheckCircleOutlined className="text-2xl text-green-500 mr-3" />
                <div>
                  <div className="text-2xl font-bold">{stats.successfulSyncs}</div>
                  <div className="text-gray-500">Successful Syncs</div>
                </div>
              </div>
            </Card>
          </Col>
        </Row>
      )}

      {/* Control Panel */}
      <Card className="mb-6">
        <Row gutter={16} align="middle">
          <Col span={6}>
            <Search
              placeholder="Search logs by message or operation"
              allowClear
              onSearch={(value) => setSearchParams({ ...searchParams, searchTerm: value })}
            />
          </Col>
          <Col span={3}>
            <Select
              placeholder="Log Level"
              style={{ width: '100%' }}
              allowClear
              onChange={(level) => setSearchParams({ ...searchParams, level })}
            >
              <Select.Option value="info">Info</Select.Option>
              <Select.Option value="warning">Warning</Select.Option>
              <Select.Option value="error">Error</Select.Option>
              <Select.Option value="success">Success</Select.Option>
              <Select.Option value="debug">Debug</Select.Option>
            </Select>
          </Col>
          <Col span={4}>
            <Select
              placeholder="Operation Type"
              style={{ width: '100%' }}
              allowClear
              onChange={(operation) => setSearchParams({ ...searchParams, operation })}
            >
              <Select.Option value="data_sync">Data Sync</Select.Option>
              <Select.Option value="file_upload">File Upload</Select.Option>
              <Select.Option value="config_sync">Config Sync</Select.Option>
              <Select.Option value="batch_operation">Batch Operation</Select.Option>
              <Select.Option value="auth_sync">Auth Sync</Select.Option>
            </Select>
          </Col>
          <Col span={6}>
            <RangePicker
              style={{ width: '100%' }}
              placeholder={['Start Date', 'End Date']}
              onChange={(dates) => setSearchParams({ 
                ...searchParams, 
                startDate: dates?.[0]?.format('YYYY-MM-DD HH:mm:ss'),
                endDate: dates?.[1]?.format('YYYY-MM-DD HH:mm:ss')
              })}
            />
          </Col>
          <Col span={5}>
            <Space>
              <Button 
                danger
                onClick={handleDeleteSelected}
                disabled={selectedRows.length === 0}
                icon={<DeleteOutlined />}
              >
                Delete Selected ({selectedRows.length})
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* Logs Table */}
      <Card>
        <Table
          columns={columns}
          dataSource={syncLogs}
          rowKey="id"
          loading={loading}
          rowSelection={rowSelection}
          pagination={{
            pageSize: 50,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} logs`,
          }}
          scroll={{ x: 1300 }}
          size="small"
        />
      </Card>

      {/* Log Detail Modal */}
      <Modal
        title={`Log Details - ${selectedLog?.operation}`}
        open={logDetailVisible}
        onCancel={() => {
          setLogDetailVisible(false);
          setSelectedLog(null);
        }}
        footer={[
          <Button key="close" onClick={() => setLogDetailVisible(false)}>
            Close
          </Button>,
          selectedLog?.stackTrace && (
            <Button 
              key="download" 
              type="primary" 
              icon={<DownloadOutlined />}
              onClick={() => syncCenterService.downloadLogDetails(selectedLog.id)}
            >
              Download Full Log
            </Button>
          ),
        ]}
        width={800}
      >
        {selectedLog && (
          <div className="space-y-4">
            <div>
              <Text strong>Level: </Text>
              <Tag color={getLevelColor(selectedLog.level)} icon={getLevelIcon(selectedLog.level)}>
                {selectedLog.level.toUpperCase()}
              </Tag>
            </div>
            <div>
              <Text strong>Timestamp: </Text>
              <Text>{new Date(selectedLog.timestamp).toLocaleString()}</Text>
            </div>
            <div>
              <Text strong>Operation: </Text>
              <Text>{selectedLog.operation}</Text>
            </div>
            <div>
              <Text strong>Message: </Text>
              <div className="mt-2 p-3 bg-gray-50 rounded">
                <Text>{selectedLog.message}</Text>
              </div>
            </div>
            {selectedLog.details && (
              <div>
                <Text strong>Details: </Text>
                <div className="mt-2 p-3 bg-gray-50 rounded">
                  <pre className="whitespace-pre-wrap text-sm">{selectedLog.details}</pre>
                </div>
              </div>
            )}
            {selectedLog.stackTrace && (
              <div>
                <Text strong>Stack Trace: </Text>
                <div className="mt-2 p-3 bg-red-50 rounded border border-red-200">
                  <pre className="whitespace-pre-wrap text-sm text-red-700">{selectedLog.stackTrace}</pre>
                </div>
              </div>
            )}
            {selectedLog.deviceId && (
              <div>
                <Text strong>Device ID: </Text>
                <Text code>{selectedLog.deviceId}</Text>
              </div>
            )}
            {selectedLog.userId && (
              <div>
                <Text strong>User ID: </Text>
                <Text>{selectedLog.userId}</Text>
              </div>
            )}
            {selectedLog.duration && (
              <div>
                <Text strong>Duration: </Text>
                <Text>{formatDuration(selectedLog.duration)}</Text>
              </div>
            )}
          </div>
        )}
      </Modal>
    </div>
  );
} 