'use client';

import React, { useState, useEffect } from 'react';
import { Card, Table, Button, Space, Tag, Progress, Statistic, Row, Col, Typography, Select, Input, Badge, Tooltip, Modal, message, Avatar } from 'antd';
import { SyncOutlined, MobileOutlined, CheckCircleOutlined, ExclamationCircleOutlined, CloseCircleOutlined, ReloadOutlined, SearchOutlined, FilterOutlined, InfoCircleOutlined } from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import dayjs from 'dayjs';

const { Title, Text } = Typography;
const { Search } = Input;

interface DeviceSyncStatus {
  id: number;
  deviceId: string;
  deviceName: string;
  deviceType: 'smartphone' | 'tablet' | 'handheld_scanner';
  userId: number;
  userName: string;
  userAvatar?: string;
  lastSyncAt: string;
  syncStatus: 'online' | 'syncing' | 'offline' | 'error';
  batteryLevel: number;
  dataIntegrity: number;
  pendingItems: number;
  totalItems: number;
  syncProgress: number;
  networkType: 'wifi' | '4g' | '5g' | 'ethernet';
  location?: string;
  lastError?: string;
  isActive: boolean;
}

interface SyncStats {
  totalDevices: number;
  onlineDevices: number;
  syncingDevices: number;
  offlineDevices: number;
  errorDevices: number;
  totalPendingItems: number;
  avgSyncTime: number;
  lastFullSyncTime: string;
}

export default function DeviceSyncStatusPage() {
  const [devices, setDevices] = useState<DeviceSyncStatus[]>([]);
  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [stats, setStats] = useState<SyncStats | null>(null);
  const [selectedDevice, setSelectedDevice] = useState<DeviceSyncStatus | null>(null);
  const [detailsVisible, setDetailsVisible] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string | undefined>();

  useEffect(() => {
    fetchDeviceStatus();
    fetchStats();
    
    // Auto-refresh every 30 seconds
    const interval = setInterval(() => {
      fetchDeviceStatus();
      fetchStats();
    }, 30000);
    
    return () => clearInterval(interval);
  }, []);

  const fetchDeviceStatus = async () => {
    setLoading(true);
    try {
      // Mock data - replace with actual API call
      const mockDevices: DeviceSyncStatus[] = [
        {
          id: 1,
          deviceId: 'DEV001',
          deviceName: 'Samsung Galaxy S21',
          deviceType: 'smartphone',
          userId: 101,
          userName: 'John Smith',
          userAvatar: 'https://i.pravatar.cc/40?img=1',
          lastSyncAt: dayjs().subtract(5, 'minutes').toISOString(),
          syncStatus: 'online',
          batteryLevel: 85,
          dataIntegrity: 98,
          pendingItems: 3,
          totalItems: 150,
          syncProgress: 95,
          networkType: '4g',
          location: 'Area North',
          isActive: true
        },
        {
          id: 2,
          deviceId: 'DEV002',
          deviceName: 'iPad Pro',
          deviceType: 'tablet',
          userId: 102,
          userName: 'Sarah Johnson',
          userAvatar: 'https://i.pravatar.cc/40?img=2',
          lastSyncAt: dayjs().subtract(2, 'hours').toISOString(),
          syncStatus: 'syncing',
          batteryLevel: 42,
          dataIntegrity: 92,
          pendingItems: 12,
          totalItems: 89,
          syncProgress: 67,
          networkType: 'wifi',
          location: 'Area South',
          isActive: true
        },
        {
          id: 3,
          deviceId: 'DEV003',
          deviceName: 'Handheld Scanner Pro',
          deviceType: 'handheld_scanner',
          userId: 103,
          userName: 'Mike Wilson',
          userAvatar: 'https://i.pravatar.cc/40?img=3',
          lastSyncAt: dayjs().subtract(1, 'day').toISOString(),
          syncStatus: 'offline',
          batteryLevel: 15,
          dataIntegrity: 87,
          pendingItems: 45,
          totalItems: 200,
          syncProgress: 0,
          networkType: '4g',
          location: 'Area East',
          isActive: false
        },
        {
          id: 4,
          deviceId: 'DEV004',
          deviceName: 'iPhone 13',
          deviceType: 'smartphone',
          userId: 104,
          userName: 'Lisa Brown',
          userAvatar: 'https://i.pravatar.cc/40?img=4',
          lastSyncAt: dayjs().subtract(30, 'minutes').toISOString(),
          syncStatus: 'error',
          batteryLevel: 72,
          dataIntegrity: 45,
          pendingItems: 8,
          totalItems: 125,
          syncProgress: 0,
          networkType: 'wifi',
          location: 'Area West',
          lastError: 'Authentication failed',
          isActive: true
        }
      ];
      
      setDevices(mockDevices);
    } catch (error) {
      console.error('Failed to fetch device status:', error);
      message.error('Failed to load device sync status');
    } finally {
      setLoading(false);
    }
  };

  const fetchStats = async () => {
    try {
      // Mock stats - replace with actual API call
      const mockStats: SyncStats = {
        totalDevices: 45,
        onlineDevices: 32,
        syncingDevices: 5,
        offlineDevices: 6,
        errorDevices: 2,
        totalPendingItems: 234,
        avgSyncTime: 45,
        lastFullSyncTime: dayjs().subtract(2, 'hours').toISOString()
      };
      
      setStats(mockStats);
    } catch (error) {
      console.error('Failed to fetch sync stats:', error);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await fetchDeviceStatus();
    await fetchStats();
    setRefreshing(false);
    message.success('Device status refreshed');
  };

  const handleForceSync = async (device: DeviceSyncStatus) => {
    try {
      // Mock force sync - replace with actual API call
      message.success(`Force sync initiated for ${device.deviceName}`);
      setTimeout(() => {
        fetchDeviceStatus();
      }, 1000);
    } catch (error) {
      message.error('Failed to initiate force sync');
    }
  };

  const handleViewDetails = (device: DeviceSyncStatus) => {
    setSelectedDevice(device);
    setDetailsVisible(true);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'online':
        return 'green';
      case 'syncing':
        return 'blue';
      case 'offline':
        return 'gray';
      case 'error':
        return 'red';
      default:
        return 'default';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'online':
        return <CheckCircleOutlined style={{ color: '#52c41a' }} />;
      case 'syncing':
        return <SyncOutlined spin style={{ color: '#1890ff' }} />;
      case 'offline':
        return <CloseCircleOutlined style={{ color: '#8c8c8c' }} />;
      case 'error':
        return <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />;
      default:
        return <InfoCircleOutlined />;
    }
  };

  const getDeviceTypeIcon = (type: string) => {
    switch (type) {
      case 'smartphone':
        return '📱';
      case 'tablet':
        return '📱';
      case 'handheld_scanner':
        return '🔍';
      default:
        return '📟';
    }
  };

  const filteredDevices = devices.filter(device => {
    const matchesSearch = !searchTerm || 
      device.deviceName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      device.userName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      device.deviceId.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = !statusFilter || device.syncStatus === statusFilter;
    
    return matchesSearch && matchesStatus;
  });

  const columns: ColumnsType<DeviceSyncStatus> = [
    {
      title: 'Device',
      key: 'device',
      render: (_, record) => (
        <Space>
          <span style={{ fontSize: '18px' }}>{getDeviceTypeIcon(record.deviceType)}</span>
          <div>
            <Text strong>{record.deviceName}</Text>
            <br />
            <Text type="secondary" style={{ fontSize: '12px' }}>{record.deviceId}</Text>
          </div>
        </Space>
      ),
    },
    {
      title: 'User',
      key: 'user',
      render: (_, record) => (
        <Space>
          <Avatar src={record.userAvatar} size="small">
            {record.userName.charAt(0)}
          </Avatar>
          <div>
            <Text>{record.userName}</Text>
            <br />
            <Text type="secondary" style={{ fontSize: '12px' }}>{record.location}</Text>
          </div>
        </Space>
      ),
    },
    {
      title: 'Status',
      dataIndex: 'syncStatus',
      key: 'syncStatus',
      render: (status, record) => (
        <Space direction="vertical" size="small">
          <Space>
            {getStatusIcon(status)}
            <Tag color={getStatusColor(status)}>
              {status.toUpperCase()}
            </Tag>
          </Space>
          {status === 'syncing' && (
            <Progress percent={record.syncProgress} size="small" />
          )}
          {record.lastError && (
            <Tooltip title={record.lastError}>
              <Text type="danger" style={{ fontSize: '12px' }}>
                Error: {record.lastError.substring(0, 20)}...
              </Text>
            </Tooltip>
          )}
        </Space>
      ),
    },
    {
      title: 'Sync Progress',
      key: 'progress',
      render: (_, record) => (
        <div>
          <Text>{record.pendingItems}/{record.totalItems} items</Text>
          <Progress 
            percent={(record.totalItems - record.pendingItems) / record.totalItems * 100} 
            size="small"
            showInfo={false}
          />
        </div>
      ),
    },
    {
      title: 'Battery',
      dataIndex: 'batteryLevel',
      key: 'batteryLevel',
      render: (level) => (
        <Progress
          type="circle"
          percent={level}
          size={40}
          status={level < 20 ? 'exception' : level < 50 ? 'normal' : 'success'}
        />
      ),
    },
    {
      title: 'Data Integrity',
      dataIndex: 'dataIntegrity',
      key: 'dataIntegrity',
      render: (integrity) => (
        <Progress
          percent={integrity}
          size="small"
          status={integrity < 80 ? 'exception' : integrity < 95 ? 'normal' : 'success'}
        />
      ),
    },
    {
      title: 'Network',
      dataIndex: 'networkType',
      key: 'networkType',
      render: (type) => (
        <Tag color={type === 'wifi' ? 'blue' : 'green'}>
          {type.toUpperCase()}
        </Tag>
      ),
    },
    {
      title: 'Last Sync',
      dataIndex: 'lastSyncAt',
      key: 'lastSyncAt',
      render: (date) => (
        <Tooltip title={dayjs(date).format('YYYY-MM-DD HH:mm:ss')}>
          <Text>{dayjs(date).fromNow()}</Text>
        </Tooltip>
      ),
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record) => (
        <Space>
          <Tooltip title="View Details">
            <Button
              type="link"
              icon={<InfoCircleOutlined />}
              onClick={() => handleViewDetails(record)}
            />
          </Tooltip>
          <Tooltip title="Force Sync">
            <Button
              type="link"
              icon={<SyncOutlined />}
              onClick={() => handleForceSync(record)}
              disabled={record.syncStatus === 'syncing'}
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  return (
    <div className="p-6">
      <div className="mb-6">
        <div className="flex justify-end items-center">
          <Button 
            type="primary" 
            icon={<ReloadOutlined />} 
            onClick={handleRefresh}
            loading={refreshing}
          >
            Refresh Status
          </Button>
        </div>
      </div>

      {/* Statistics Cards */}
      {stats && (
        <Row gutter={16} className="mb-6">
          <Col span={6}>
            <Card>
              <Statistic
                title="Total Devices"
                value={stats.totalDevices}
                prefix={<MobileOutlined />}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="Online"
                value={stats.onlineDevices}
                prefix={<CheckCircleOutlined />}
                valueStyle={{ color: '#3f8600' }}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="Pending Items"
                value={stats.totalPendingItems}
                prefix={<SyncOutlined />}
                valueStyle={{ color: '#1890ff' }}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="Avg Sync Time"
                value={stats.avgSyncTime}
                suffix="sec"
                prefix={<SyncOutlined />}
              />
            </Card>
          </Col>
        </Row>
      )}

      {/* Filters */}
      <Card className="mb-6">
        <Row gutter={16} align="middle">
          <Col span={8}>
            <Search
              placeholder="Search by device, user, or device ID"
              allowClear
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              style={{ width: '100%' }}
            />
          </Col>
          <Col span={6}>
            <Select
              placeholder="Filter by status"
              style={{ width: '100%' }}
              allowClear
              value={statusFilter}
              onChange={setStatusFilter}
            >
              <Select.Option value="online">Online</Select.Option>
              <Select.Option value="syncing">Syncing</Select.Option>
              <Select.Option value="offline">Offline</Select.Option>
              <Select.Option value="error">Error</Select.Option>
            </Select>
          </Col>
          <Col span={6}>
            <Text type="secondary">
              Showing {filteredDevices.length} of {devices.length} devices
            </Text>
          </Col>
        </Row>
      </Card>

      {/* Device Table */}
      <Card>
        <Table
          columns={columns}
          dataSource={filteredDevices}
          rowKey="id"
          loading={loading}
          pagination={{
            pageSize: 20,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} devices`,
          }}
        />
      </Card>

      {/* Device Details Modal */}
      <Modal
        title={`Device Details - ${selectedDevice?.deviceName}`}
        open={detailsVisible}
        onCancel={() => setDetailsVisible(false)}
        footer={[
          <Button key="close" onClick={() => setDetailsVisible(false)}>
            Close
          </Button>,
          <Button
            key="sync"
            type="primary"
            icon={<SyncOutlined />}
            onClick={() => selectedDevice && handleForceSync(selectedDevice)}
          >
            Force Sync
          </Button>
        ]}
        width={600}
      >
        {selectedDevice && (
          <div className="space-y-4">
            <Row gutter={16}>
              <Col span={12}>
                <Text strong>Device ID:</Text>
                <br />
                <Text code>{selectedDevice.deviceId}</Text>
              </Col>
              <Col span={12}>
                <Text strong>Device Type:</Text>
                <br />
                <Text>{selectedDevice.deviceType.replace('_', ' ')}</Text>
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={12}>
                <Text strong>Assigned User:</Text>
                <br />
                <Space>
                  <Avatar src={selectedDevice.userAvatar} size="small">
                    {selectedDevice.userName.charAt(0)}
                  </Avatar>
                  <Text>{selectedDevice.userName}</Text>
                </Space>
              </Col>
              <Col span={12}>
                <Text strong>Location:</Text>
                <br />
                <Text>{selectedDevice.location || 'Not set'}</Text>
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={12}>
                <Text strong>Network Type:</Text>
                <br />
                <Tag color={selectedDevice.networkType === 'wifi' ? 'blue' : 'green'}>
                  {selectedDevice.networkType.toUpperCase()}
                </Tag>
              </Col>
              <Col span={12}>
                <Text strong>Battery Level:</Text>
                <br />
                <Progress percent={selectedDevice.batteryLevel} size="small" />
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={12}>
                <Text strong>Data Integrity:</Text>
                <br />
                <Progress percent={selectedDevice.dataIntegrity} size="small" />
              </Col>
              <Col span={12}>
                <Text strong>Sync Progress:</Text>
                <br />
                <Text>{selectedDevice.totalItems - selectedDevice.pendingItems}/{selectedDevice.totalItems} items synced</Text>
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={24}>
                <Text strong>Last Sync:</Text>
                <br />
                <Text>{dayjs(selectedDevice.lastSyncAt).format('YYYY-MM-DD HH:mm:ss')} ({dayjs(selectedDevice.lastSyncAt).fromNow()})</Text>
              </Col>
            </Row>
            {selectedDevice.lastError && (
              <Row gutter={16}>
                <Col span={24}>
                  <Text strong>Last Error:</Text>
                  <br />
                  <Text type="danger">{selectedDevice.lastError}</Text>
                </Col>
              </Row>
            )}
          </div>
        )}
      </Modal>
    </div>
  );
} 