@tailwind base;
@tailwind components;
@tailwind utilities;

/* Import compact modal styles */
@import '../styles/modal-compact.css';

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
  margin: 0;
  padding: 0;
}

* {
  box-sizing: border-box;
}

/* Global page layout optimization - Ultra compact */
.page-container {
  padding: 8px 16px !important;
}

/* Optimize common page structures - More compact */
.p-6 {
  padding: 8px 16px !important;
}

.p-3 {
  padding: 6px 12px !important;
}

.p-2 {
  padding: 4px 8px !important;
}

/* Reduce excessive spacing on cards and containers */
.ant-card {
  margin-bottom: 12px;
}

.ant-card .ant-card-body {
  padding: 12px 16px;
}

/* Ultra compact form and search areas */
.mb-6 {
  margin-bottom: 8px !important;
}

.mb-4 {
  margin-bottom: 6px !important;
}

.mb-3 {
  margin-bottom: 4px !important;
}

.mb-2 {
  margin-bottom: 3px !important;
}

/* Compact toolbar and button groups */
.ant-space {
  gap: 8px !important;
}

.ant-space-item {
  margin-bottom: 0 !important;
}

/* Optimize form spacing */
.ant-form {
  margin-bottom: 8px !important;
}

.ant-form-item {
  margin-bottom: 8px !important;
}

.ant-form-item-label {
  padding-bottom: 2px !important;
}

/* Compact search and filter forms */
.search-form {
  margin-bottom: 6px !important;
  padding: 8px 12px !important;
}

.search-form .ant-form-item {
  margin-bottom: 0 !important;
}

/* Compact button groups and toolbars */
.toolbar-section {
  margin: 6px 0 !important;
  padding: 6px 0 !important;
}

/* Ensure consistent compact spacing for page headers */
.page-header {
  margin-bottom: 8px;
  padding-bottom: 4px;
}

/* Optimize table and content spacing */
.ant-table-wrapper {
  margin-top: 6px;
}

.ant-table-thead > tr > th {
  padding: 8px 12px !important;
}

.ant-table-tbody > tr > td {
  padding: 6px 12px !important;
}

/* Compact statistics cards */
.ant-statistic {
  margin-bottom: 0 !important;
}

.ant-statistic-content {
  margin-bottom: 0 !important;
}

/* Compact row spacing */
.ant-row {
  margin-bottom: 8px !important;
}

/* Compact input and select components */
.ant-input, .ant-select-selector {
  padding: 4px 8px !important;
}

/* Responsive spacing adjustments - Even more compact */
@media (max-width: 768px) {
  .page-container {
    padding: 4px 8px !important;
  }
  
  .p-6 {
    padding: 4px 8px !important;
  }
  
  .p-3 {
    padding: 3px 6px !important;
  }
  
  .ant-card .ant-card-body {
    padding: 8px 12px;
  }
}

/* Ultra compact layout override */
.compact-layout {
  padding: 4px 8px !important;
  margin: 2px 0 !important;
}
