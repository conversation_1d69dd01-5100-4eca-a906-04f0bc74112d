'use client';

import React, { useState, useEffect, useCallback } from 'react';
import {
  Table, Button, Modal, Form, Input, Select, Card, Space,
  message, Tabs, Row, Col, Statistic, Tag, Badge,
  InputNumber, Avatar, List
} from 'antd';
import {
  UserOutlined, TeamOutlined, ThunderboltOutlined,
  SendOutlined, InfoCircleOutlined, ExclamationCircleOutlined,
  EnvironmentOutlined
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import dayjs from 'dayjs';
import {
  taskService, TaskListDto, ReactiveAssignmentDto,
  TaskAssignmentDto, CreateTaskAssignmentDto, UserWorkloadSummaryDto,
  TaskAssignmentValidationDto, BulkAssignmentEnhancedRequest,
  TaskWorkPackageSearchDto, WorkPackageGroupedTasksDto
} from '@/services/task.service';
import GoogleMapView, { MapMarker } from '@/components/common/GoogleMapView';
import UserWorkloadCard from '@/components/task/UserWorkloadCard';
import UserTasksModal from '@/components/task/UserTasksModal';

const { Option } = Select;
const { TabPane } = Tabs;
const { TextArea } = Input;

interface TaskAssignmentProps {
  menuType?: 'individual' | 'bulk' | 'reactive';
}

// Define extended type for expandable table data
type ExpandableTaskData = TaskListDto & {
  isGroupHeader?: boolean;
  taskCount?: number;
  children?: TaskListDto[];
  key?: string;
};

const TaskAssignment: React.FC<TaskAssignmentProps> = ({ menuType = 'individual' }) => {
  const [unassignedTasks, setUnassignedTasks] = useState<TaskListDto[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedTasks, setSelectedTasks] = useState<number[]>([]);
  const [users, setUsers] = useState<UserWorkloadSummaryDto[]>([]);
  const [usersLoading, setUsersLoading] = useState(false);
  
  // Pagination states - changed to work package based
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(3); // Number of work packages per page
  
  // Filter states
  const [workPackageFilter, setWorkPackageFilter] = useState<string>('');
  const [availableWorkPackages, setAvailableWorkPackages] = useState<{value: string, label: string}[]>([]);
  const [workPackageData, setWorkPackageData] = useState<WorkPackageGroupedTasksDto | null>(null);
  // Remove groupByWorkPackage state since it's now always enabled
  
  // Map-related states
  const [mapMarkers, setMapMarkers] = useState<MapMarker[]>([]);
  const [selectedMarkerId, setSelectedMarkerId] = useState<string | undefined>();
  
  // User selection modal states
  const [userSelectionVisible, setUserSelectionVisible] = useState(false);
  const [selectedUser, setSelectedUser] = useState<UserWorkloadSummaryDto | null>(null);
  const [validationData, setValidationData] = useState<{[userId: number]: TaskAssignmentValidationDto}>({});
  const [pendingTaskId, setPendingTaskId] = useState<number | null>(null);
  
  // User Tasks Modal states
  const [userTasksModalVisible, setUserTasksModalVisible] = useState(false);
  const [selectedUserForTasks, setSelectedUserForTasks] = useState<UserWorkloadSummaryDto | null>(null);
  const [userTasks, setUserTasks] = useState<TaskAssignmentDto[]>([]);
  const [userTasksLoading, setUserTasksLoading] = useState(false);
  
  // Set initial tab based on menuType
  const getInitialTab = () => {
    switch (menuType) {
      case 'bulk': return 'bulk';
      case 'reactive': return 'reactive';
      default: return 'individual';
    }
  };
  
  const [activeTab, setActiveTab] = useState(getInitialTab());

  // Forms
  const [bulkForm] = Form.useForm();
  const [reactiveForm] = Form.useForm();

  // Modals
  const [bulkModalVisible, setBulkModalVisible] = useState(false);
  const [reactiveModalVisible, setReactiveModalVisible] = useState(false);

  // Statistics
  const [assignmentStats, setAssignmentStats] = useState({
    totalUnassigned: 0,
    totalAssigned: 0,
    criticalPriority: 0,
    overdueTasks: 0
  });

  // Work package data for table display
  // const [workPackageData, setWorkPackageData] = useState<any>(null);

  // Convert tasks to map markers
  const convertTasksToMarkers = useCallback((tasks: TaskListDto[]): MapMarker[] => {
    return tasks
      .filter(task => {
        // Only include tasks that have GPS coordinates (either task coordinates or meter coordinates)
        const hasTaskGps = task.latitude != null && task.longitude != null;
        const hasMeterGps = task.meterLatitude != null && task.meterLongitude != null;
        return hasTaskGps || hasMeterGps;
      })
      .map((task) => {
        // Use task GPS coordinates first, fallback to meter GPS coordinates
        const lat = task.latitude != null ? task.latitude : task.meterLatitude!;
        const lng = task.longitude != null ? task.longitude : task.meterLongitude!;

        return {
          id: task.id.toString(),
          position: {
            lat: lat,
            lng: lng
          },
          title: task.name || `Task #${task.id}`,
          info: {
            description: task.description || 'No description',
            status: task.status,
            priority: task.priority,
            type: task.type,
            location: task.location || 'No location specified',
            meterSerial: task.meterSerialNumber || 'No meter'
          },
          selected: selectedTasks.includes(task.id)
        };
      });
  }, [selectedTasks]);

  // Handle marker click on map with optimized state updates
  const handleMapMarkerClick = useCallback((marker: MapMarker) => {
    const taskId = parseInt(marker.id);
    const task = unassignedTasks.find(t => t.id === taskId);

    if (task) {
      // Immediately update selected marker for visual feedback
      setSelectedMarkerId(marker.id);

      // Delay task selection update to prevent info window from closing
      setTimeout(() => {
        // Toggle task selection
        if (selectedTasks.includes(taskId)) {
          setSelectedTasks(prev => prev.filter(id => id !== taskId));
        } else {
          setSelectedTasks(prev => [...prev, taskId]);
        }
      }, 100);
    }
  }, [selectedTasks, unassignedTasks]);

  // Load unassigned tasks and statistics
  const loadUnassignedTasks = useCallback(async () => {
    setLoading(true);
    try {
      // Get unassigned tasks grouped by work package
      const workPackageSearchDto: TaskWorkPackageSearchDto = {
        assignedUserId: 0, // Zero means unassigned
        page: currentPage,
        pageSize: pageSize,
        workPackageFilter: workPackageFilter || undefined
      };
      
      const workPackageResult = await taskService.getTasksGroupedByWorkPackage(workPackageSearchDto);
      
      // Convert to flat task list for compatibility with existing logic
      const flatTasks = workPackageResult.workPackages.flatMap(wp => wp.tasks);
      setUnassignedTasks(flatTasks);
      
      // Store work package result for table display
      setWorkPackageData(workPackageResult);
      
      // Extract unique work packages for filter dropdown
      const workPackages = workPackageResult.workPackages
        .map(wp => ({ value: wp.workPackageName, label: wp.workPackageName }));
      setAvailableWorkPackages(workPackages);
      
      // Convert tasks to markers (inline to avoid dependency issues)
      const markers = flatTasks
        .filter(task => {
          const hasTaskGps = task.latitude != null && task.longitude != null;
          const hasMeterGps = task.meterLatitude != null && task.meterLongitude != null;
          return hasTaskGps || hasMeterGps;
        })
        .map((task) => {
          const lat = task.latitude != null ? task.latitude : task.meterLatitude!;
          const lng = task.longitude != null ? task.longitude : task.meterLongitude!;

          return {
            id: task.id.toString(),
            position: { lat, lng },
            title: task.name || `Task #${task.id}`,
            info: {
              description: task.description || 'No description',
              status: task.status,
              priority: task.priority,
              type: task.type,
              location: task.location || 'No location specified',
              meterSerial: task.meterSerialNumber || 'No meter'
            },
            selected: false // Don't use selectedTasks here to avoid dependency
          };
        });
      setMapMarkers(markers);
      
      // Get statistics from dedicated API
      const stats = await taskService.getTaskStatistics();
      setAssignmentStats({
        totalUnassigned: stats.totalTasks - stats.completedTasks - stats.inProgressTasks,
        totalAssigned: stats.inProgressTasks + stats.completedTasks,
        criticalPriority: stats.priorityCounts.find(p => p.priority === 'Critical')?.count || 0,
        overdueTasks: stats.overdueTasks
      });
      
    } catch (error) {
      message.error('Failed to load tasks');
      console.error('Load tasks error:', error);
    } finally {
      setLoading(false);
    }
  }, [currentPage, pageSize, workPackageFilter]);

  // Load users workload data
  const loadUsers = useCallback(async () => {
    setUsersLoading(true);
    try {
      const usersData = await taskService.getUserWorkloadSummary();
      setUsers(usersData);
    } catch (error) {
      message.error('Failed to load users');
      console.error('Load users error:', error);
    } finally {
      setUsersLoading(false);
    }
  }, []);

  useEffect(() => {
    loadUnassignedTasks();
    loadUsers();
  }, [loadUnassignedTasks, loadUsers]);

  // Debug: Monitor users state changes
  useEffect(() => {
    
  }, [users]);

  // Update map markers selection when selected tasks change
  useEffect(() => {
    setMapMarkers(prevMarkers =>
      prevMarkers.map(marker => ({
        ...marker,
        selected: selectedTasks.includes(parseInt(marker.id))
      }))
    );
  }, [selectedTasks]);



  // Update active tab when menuType changes
  useEffect(() => {
    const initialTab = (() => {
      switch (menuType) {
        case 'bulk': return 'bulk';
        case 'reactive': return 'reactive';
        default: return 'individual';
      }
    })();
    setActiveTab(initialTab);
  }, [menuType]);

  // Bulk assignment with enhanced API
  const handleBulkAssignment = async (values: any) => {
    if (selectedTasks.length === 0) {
      message.warning('Please select tasks to assign');
      return;
    }

    try {
      const request: BulkAssignmentEnhancedRequest = {
        taskIds: selectedTasks,
        userId: parseInt(values.userId),
        assignmentType: 'Bulk',
        reason: values.reason
      };

      const result = await taskService.bulkAssignTasksEnhanced(request);
      
      if (result.success) {
        message.success(`Successfully assigned ${result.totalAssigned} tasks`);
        if (result.warnings.length > 0) {
          result.warnings.forEach(warning => message.warning(warning));
        }
      } else {
        message.error(result.message);
        if (result.errors.length > 0) {
          result.errors.forEach(error => message.error(error));
        }
      }
      
      setBulkModalVisible(false);
      bulkForm.resetFields();
      setSelectedTasks([]);
      // Refresh data
      loadUnassignedTasks();
      loadUsers();
    } catch (error) {
      message.error('Failed to assign tasks');
      console.error('Bulk assignment error:', error);
    }
  };

  // Reactive assignment
  const handleReactiveAssignment = async (values: any) => {
    try {
      const reactiveDto: ReactiveAssignmentDto = {
        criteria: values.criteria,
        priority: values.priority,
        zone: values.zone,
        area: values.area,
        maxTasksPerUser: values.maxTasksPerUser
      };

      const assignedTasks = await taskService.reactiveAssignTasks(reactiveDto);
      if (assignedTasks.length > 0) {
        message.success(`Successfully assigned ${assignedTasks.length} tasks using reactive assignment`);
        setReactiveModalVisible(false);
        reactiveForm.resetFields();
        // Refresh data
        loadUnassignedTasks();
        loadUsers();
      } else {
        message.warning('No tasks matched the criteria for assignment');
      }
    } catch (error) {
      message.error('Failed to perform reactive assignment');
      console.error('Reactive assignment error:', error);
    }
  };

  // Individual assignment
  const handleIndividualAssignment = async (taskId: number, userId: string) => {
    try {
      const assignmentDto: CreateTaskAssignmentDto = {
        taskId: taskId,
        userId: parseInt(userId),
        assignmentType: 'Individual',
        reason: 'Individual task assignment'
      };

      const result = await taskService.createTaskAssignment(assignmentDto);
      
      if (result.success) {
        message.success('Task assigned successfully');
        if (result.warnings.length > 0) {
          result.warnings.forEach(warning => message.warning(warning));
        }
      } else {
        message.error(result.message);
        if (result.errors.length > 0) {
          result.errors.forEach(error => message.error(error));
        }
      }
      
      // Refresh tasks and users
      loadUnassignedTasks();
      loadUsers();
    } catch (error) {
      message.error('Failed to assign task');
      console.error('Individual assignment error:', error);
    }
  };

  // Enhanced individual assignment with user selection modal
  const handleTaskAssignmentClick = async (taskId: number) => {
    setPendingTaskId(taskId);
    setUserSelectionVisible(true);
    
    // Load validation data for all users for this task
    const validations: {[userId: number]: TaskAssignmentValidationDto} = {};
    for (const user of users) {
      try {
        const validation = await taskService.validateTaskAssignment(taskId, user.userId);
        validations[user.userId] = validation;
      } catch (error) {
        console.error(`Failed to validate assignment for user ${user.userId}:`, error);
      }
    }
    setValidationData(validations);
  };

  // Handle user selection from modal
  const handleUserSelected = async (user: UserWorkloadSummaryDto) => {
    if (!pendingTaskId) return;
    
    setSelectedUser(user);
    
    try {
      const assignmentDto: CreateTaskAssignmentDto = {
        taskId: pendingTaskId,
        userId: user.userId,
        assignmentType: 'Individual',
        reason: 'Task assignment via enhanced user selection'
      };

      const result = await taskService.createTaskAssignment(assignmentDto);
      
      if (result.success) {
        message.success(`Task assigned to ${user.userName} successfully`);
        if (result.warnings.length > 0) {
          result.warnings.forEach(warning => message.warning(warning));
        }
      } else {
        message.error(result.message);
        if (result.errors.length > 0) {
          result.errors.forEach(error => message.error(error));
        }
      }
      
      setUserSelectionVisible(false);
      setSelectedUser(null);
      setPendingTaskId(null);
      setValidationData({});
      
      // Refresh data
      loadUnassignedTasks();
      loadUsers();
    } catch (error) {
      message.error('Failed to assign task');
      console.error('Enhanced assignment error:', error);
    }
  };

  // Handle view user tasks
  const handleViewUserTasks = async (user: UserWorkloadSummaryDto) => {
    setSelectedUserForTasks(user);
    setUserTasksLoading(true);
    setUserTasksModalVisible(true);
    try {
      const assignments = await taskService.getTaskAssignments(undefined, user.userId);
      setUserTasks(assignments);
    } catch (error) {
      message.error('Failed to load user tasks');
      console.error('Load user tasks error:', error);
    } finally {
      setUserTasksLoading(false);
    }
  };

  // Get priority color
  const getPriorityColor = (priority: string) => {
    const colors = {
      'Low': 'gray',
      'Medium': 'blue',
      'High': 'orange',
      'Critical': 'red'
    };
    return colors[priority as keyof typeof colors] || 'default';
  };

  // Get availability color
  const getAvailabilityColor = (availability: string) => {
    const colors = {
      'Available': 'green',
      'Busy': 'orange',
      'Offline': 'red'
    };
    return colors[availability as keyof typeof colors] || 'default';
  };

  // Check if task is overdue
  const isOverdue = (task: TaskListDto) => {
    return task.dueDate && dayjs(task.dueDate).isBefore(dayjs()) && task.status !== 'Completed';
  };

  // Group tasks by work package for expandable table with pagination
  const getExpandableTableData = () => {
    if (!workPackageData) {
      return [];
    }

    // Convert API data to expandable table format
    return workPackageData.workPackages.map((wp, index) => ({
      key: `group-${index}`,
      id: -(index + 1), // Negative ID for group rows  
      name: wp.workPackageName,
      isGroupHeader: true,
      taskCount: wp.taskCount,
      children: wp.tasks.map(task => ({
        ...task,
        key: `task-${task.id}`,
        parentWorkPackage: wp.workPackageName
      })), // Child tasks for this group
      // Required fields for type compatibility
      status: '',
      priority: '',
      type: '',
      createdBy: '',
      progressPercentage: 0,
      createdAt: '',
      updatedAt: '',
      // For the group header display
      workPackageName: wp.workPackageName,
      description: `Work package containing ${wp.taskCount} tasks`
    } as ExpandableTaskData));
  };

  // Calculate total work packages separately
  const getTotalWorkPackages = () => {
    return workPackageData?.totalWorkPackages || 0;
  };

  // Unassigned tasks table columns
  const unassignedColumns: ColumnsType<ExpandableTaskData> = [
    {
      title: '', // Empty title for Select column
      key: 'select',
      width: 40,
      // Remove custom checkbox rendering - let rowSelection handle it
    },
    {
      title: 'Task',
      key: 'task',
      width: 180,
      render: (_, record) => {
        if (record.isGroupHeader) {
          return (
            <div style={{ fontWeight: 'bold', fontSize: '14px', color: '#1890ff', display: 'flex', alignItems: 'center', gap: 8 }}>
              📁 {record.name} ({record.taskCount} tasks)
            </div>
          );
        }
        
        return (
          <div style={{ lineHeight: '1.2' }}>
            <div style={{ fontWeight: 'medium', fontSize: '13px' }}>{record.name}</div>
            {record.description && (
              <div style={{ color: '#666', fontSize: '11px', marginTop: '2px' }}>
                {record.description.length > 40 ? `${record.description.substring(0, 40)}...` : record.description}
              </div>
            )}
            {isOverdue(record) && <Badge status="error" text="Overdue" style={{ fontSize: '10px' }} />}
          </div>
        );
      },
    },
    {
      title: 'Asset ID',
      dataIndex: 'meterAssetId',
      key: 'meterAssetId',
      width: 80,
      render: (text: string, record) => record.isGroupHeader ? null : (text || '-'),
    },
    {
      title: 'Meter No.',
      dataIndex: 'meterSerialNumber',
      key: 'meterSerialNumber',
      width: 90,
      render: (text: string, record) => record.isGroupHeader ? null : (text || '-'),
    },
    {
      title: 'Account',
      dataIndex: 'meterAccountNumber',
      key: 'meterAccountNumber',
      width: 70,
      render: (text: string, record) => record.isGroupHeader ? null : (text || '-'),
    },
    {
      title: 'Work Package',
      dataIndex: 'workPackageName',
      key: 'workPackageName',
      width: 100,
      render: (text: string, record) => record.isGroupHeader ? null : (text || '-'),
    },
    {
      title: 'Priority',
      dataIndex: 'priority',
      key: 'priority',
      width: 80,
      render: (priority: string, record) => {
        if (record.isGroupHeader) return null;
        return <Tag color={getPriorityColor(priority)}>{priority}</Tag>;
      },
    },
    {
      title: 'Due Date',
      dataIndex: 'dueDate',
      key: 'dueDate',
      width: 80,
      render: (date: string, record) => {
        if (record.isGroupHeader || !date) return record.isGroupHeader ? null : '-';
        const isLate = isOverdue(record);
        return (
          <div style={{ color: isLate ? '#ff4d4f' : undefined, fontSize: '12px' }}>
            {dayjs(date).format('MM-DD')}
          </div>
        );
      },
    },
    {
      title: 'Location',
      dataIndex: 'location',
      key: 'location',
      width: 120,
      render: (location: string, record) => {
        if (record.isGroupHeader) return null;
        return (
          <div style={{ fontSize: '12px' }}>
            {location ? (location.length > 25 ? `${location.substring(0, 25)}...` : location) : '-'}
          </div>
        );
      },
    },
    {
      title: 'Assign',
      key: 'assign',
      width: 80,
      render: (_, record) => {
        if (record.isGroupHeader) {
          return (
            <Button
              size="small"
              onClick={() => {
                const groupTasks = record.children?.map(task => task.id) || [];
                setSelectedTasks(prev => [...new Set([...prev, ...groupTasks])]);
              }}
              style={{ fontSize: '11px', height: '24px', padding: '0 8px' }}
            >
              Select All
            </Button>
          );
        }
        
        return (
          <Button
            type="primary"
            size="small"
            icon={<UserOutlined />}
            onClick={() => handleTaskAssignmentClick(record.id)}
            loading={usersLoading}
            style={{ fontSize: '11px', height: '24px', padding: '0 8px' }}
          >
            Assign
          </Button>
        );
      },
    },
  ];

  // User workload columns
  const userColumns: ColumnsType<UserWorkloadSummaryDto> = [
    {
      title: 'User',
      key: 'user',
      sorter: false,
      render: (_, record) => (
        <div className="flex items-center">
          <Avatar icon={<UserOutlined />} className="mr-3" />
          <div>
            <div className="font-medium">{record.userName}</div>
            <div className="text-gray-500 text-sm">{record.zone}</div>
          </div>
        </div>
      ),
    },
    {
      title: 'Availability',
      dataIndex: 'availabilityStatus',
      key: 'availabilityStatus',
      sorter: false,
      render: (availabilityStatus: string) => (
        <Tag color={getAvailabilityColor(availabilityStatus)}>{availabilityStatus}</Tag>
      ),
    },
    {
      title: 'Current Load',
      key: 'workload',
      sorter: false,
      render: (_, record) => (
        <div>
          <div className="flex justify-between">
            <span>{record.activeTaskCount}/{record.maxCapacity}</span>
            <span>{Math.round((record.activeTaskCount / record.maxCapacity) * 100)}%</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2 mt-1">
            <div 
              className={`h-2 rounded-full ${
                record.activeTaskCount / record.maxCapacity > 0.8 ? 'bg-red-500' :
                record.activeTaskCount / record.maxCapacity > 0.6 ? 'bg-orange-500' : 'bg-green-500'
              }`}
              style={{ width: `${(record.activeTaskCount / record.maxCapacity) * 100}%` }}
            />
          </div>
        </div>
      ),
    },
    {
      title: 'Skills',
      dataIndex: 'skills',
      key: 'skills',
      sorter: false,
      render: (skills: string[]) => (
        <div>
          {skills.map(skill => (
            <Tag key={skill}>{skill}</Tag>
          ))}
        </div>
      ),
    },
    {
      title: 'Actions',
      key: 'actions',
      width: 120,
      sorter: false,
      render: (_, record) => (
        <Button
          type="link"
          size="small"
          icon={<InfoCircleOutlined />}
          onClick={() => handleViewUserTasks(record)}
          style={{ padding: '0 8px' }}
        >
          View Tasks
        </Button>
      ),
    },
  ];

  return (
    <div className="p-4" style={{ height: '100vh', overflow: 'hidden' }}>
      {/* Compressed Statistics Cards */}
      <div style={{ 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'stretch',
        marginBottom: '12px',
        height: '70px'
      }}>
        <Row gutter={16} style={{ flex: 1, height: '100%' }}>
          <Col span={6}>
            <Card size="small" style={{ height: '100%' }} styles={{ body: { padding: '8px 12px' } }}>
              <Statistic 
                title="Total Unassigned" 
                value={assignmentStats.totalUnassigned} 
                valueStyle={{ color: '#1890ff', fontSize: '16px' }}
                prefix={<TeamOutlined />}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card size="small" style={{ height: '100%' }} styles={{ body: { padding: '8px 12px' } }}>
              <Statistic 
                title="Total Assigned" 
                value={assignmentStats.totalAssigned} 
                valueStyle={{ color: '#52c41a', fontSize: '16px' }}
                prefix={<ThunderboltOutlined />}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card size="small" style={{ height: '100%' }} styles={{ body: { padding: '8px 12px' } }}>
              <Statistic 
                title="Critical Priority" 
                value={assignmentStats.criticalPriority} 
                valueStyle={{ color: '#f5222d', fontSize: '16px' }}
                prefix={<ExclamationCircleOutlined />}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card size="small" style={{ height: '100%' }} styles={{ body: { padding: '8px 12px' } }}>
              <Statistic 
                title="Overdue" 
                value={assignmentStats.overdueTasks} 
                valueStyle={{ color: '#f5222d', fontSize: '16px' }}
                prefix={<ExclamationCircleOutlined />}
              />
            </Card>
          </Col>
        </Row>
      </div>

      <Tabs 
        activeKey={activeTab} 
        onChange={setActiveTab} 
        size="small"
        style={{ height: 'calc(100vh - 120px)' }}
        tabBarStyle={{ marginBottom: '8px' }}
      >
        <TabPane tab={
          <span>
            <TeamOutlined />
            Bulk Assignment
          </span>
        } key="bulk">
          <div style={{ height: 'calc(100vh - 280px)' }}>
            <Row gutter={16} style={{ height: '100%' }}>
              {/* Left Panel - Task List */}
              <Col span={14} style={{ height: '100%' }}>
                <Card style={{ height: '100%' }} styles={{ body: { padding: '12px', height: 'calc(100% - 45px)' } }}>
                  {/* Compressed Task List Header */}
                  <div style={{ 
                    display: 'flex', 
                    justifyContent: 'space-between', 
                    alignItems: 'center', 
                    marginBottom: '8px',
                    paddingBottom: '6px',
                    borderBottom: '1px solid #f0f0f0'
                  }}>
                    <div>
                      <h3 style={{ margin: 0, fontSize: '16px', fontWeight: 500 }}>Unassigned Tasks (Select multiple tasks to assign in bulk)</h3>
                    </div>
                    <Space size="small">
                      <Button
                        size="small"
                        disabled={selectedTasks.length === 0}
                        onClick={() => {
                          if (unassignedTasks.length > 0) {
                            const allIds = unassignedTasks.map(task => task.id);
                            setSelectedTasks(selectedTasks.length === allIds.length ? [] : allIds);
                          }
                        }}
                      >
                        {selectedTasks.length === unassignedTasks.length ? 'Deselect All' : 'Select All'}
                      </Button>
                      <Button
                        type="primary"
                        size="small"
                        icon={<SendOutlined />}
                        disabled={selectedTasks.length === 0}
                        onClick={() => setBulkModalVisible(true)}
                      >
                        Assign Selected ({selectedTasks.length})
                      </Button>
                    </Space>
                  </div>

                  {/* Filter Controls */}
                  <div style={{ marginBottom: 16, padding: '8px 12px', background: '#f5f5f5', borderRadius: '6px' }}>
                    <Row gutter={16} align="middle">
                      <Col span={12}>
                        <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                          <span style={{ fontSize: '12px', color: '#666' }}>Filter by Work Package:</span>
                          <Select
                            placeholder="All Work Packages"
                            value={workPackageFilter}
                            onChange={setWorkPackageFilter}
                            allowClear
                            style={{ minWidth: 180 }}
                            size="small"
                          >
                            {availableWorkPackages.map(wp => (
                              <Option key={wp.value} value={wp.value}>{wp.label}</Option>
                            ))}
                          </Select>
                        </div>
                      </Col>
                      <Col span={12}>
                        {workPackageFilter && (
                          <Button
                            size="small"
                            onClick={() => {
                              const workPackageTasks = unassignedTasks
                                .filter(task => task.workPackageName === workPackageFilter)
                                .map(task => task.id);
                              setSelectedTasks(workPackageTasks);
                            }}
                          >
                            Select All in "{workPackageFilter}"
                          </Button>
                        )}
                      </Col>
                    </Row>
                  </div>

                  <Table<ExpandableTaskData>
                    columns={unassignedColumns}
                    dataSource={getExpandableTableData()}
                    rowKey={(record) => record.isGroupHeader ? `header-${record.name}` : record.id}
                    loading={loading}
                    scroll={{ y: 'calc(100vh - 380px)' }}
                    pagination={{
                      current: currentPage,
                      pageSize: pageSize,
                      total: getTotalWorkPackages(), // Use the new function
                      showSizeChanger: true,
                      showQuickJumper: true,
                      pageSizeOptions: ['2', '3', '5', '10'],
                      showTotal: (_, range) => {
                        const taskCount = getExpandableTableData()
                          .reduce((sum, group) => sum + (group.children?.length || 0), 0);
                        return `${range[0]}-${range[1]} of ${getTotalWorkPackages()} work packages (${taskCount} tasks)`;
                      },
                      size: 'small',
                      onChange: (page, size) => {
                        setCurrentPage(page);
                        if (size !== pageSize) {
                          setPageSize(size);
                        }
                      },
                      onShowSizeChange: (_, size) => {
                        setCurrentPage(1); // Reset to first page when changing page size
                        setPageSize(size);
                      }
                    }}
                    expandable={{
                      childrenColumnName: 'children',
                      defaultExpandAllRows: false,
                      rowExpandable: (record) => !!(record.isGroupHeader && record.children && record.children.length > 0),
                      expandIcon: ({ expanded, onExpand, record }) => {
                        if (!record.isGroupHeader) return null;
                        return (
                          <span
                            onClick={e => onExpand(record, e)}
                            style={{ cursor: 'pointer', fontSize: '14px', padding: '4px' }}
                          >
                            {expanded ? '🔼' : '🔽'}
                          </span>
                        );
                      }
                    }}
                    rowSelection={{
                      type: 'checkbox',
                      selectedRowKeys: selectedTasks,
                      getCheckboxProps: (_) => ({
                        disabled: false, // Allow selection for both groups and tasks
                      }),
                      onSelect: (record, selected, _) => {
                        if (record.isGroupHeader) {
                          // Handle group selection
                          const groupTaskIds = record.children?.map(task => task.id) || [];
                          if (selected) {
                            // Add all group tasks to selection
                            setSelectedTasks(prev => [...new Set([...prev, ...groupTaskIds])]);
                          } else {
                            // Remove all group tasks from selection
                            setSelectedTasks(prev => prev.filter(id => !groupTaskIds.includes(id)));
                          }
                        } else {
                          // Handle individual task selection
                          if (selected) {
                            setSelectedTasks(prev => [...prev, record.id]);
                          } else {
                            setSelectedTasks(prev => prev.filter(id => id !== record.id));
                          }
                          setSelectedMarkerId(record.id.toString());
                        }
                      },
                      onSelectAll: (selected, selectedRows, changeRows) => {
                        if (selected) {
                          // Select all visible tasks
                          const allTaskIds = getExpandableTableData()
                            .flatMap(group => group.children || [])
                            .map(task => task.id);
                          setSelectedTasks(prev => [...new Set([...prev, ...allTaskIds])]);
                        } else {
                          // Deselect all visible tasks
                          const allTaskIds = getExpandableTableData()
                            .flatMap(group => group.children || [])
                            .map(task => task.id);
                          setSelectedTasks(prev => prev.filter(id => !allTaskIds.includes(id)));
                        }
                      },
                      // Custom selection logic for group headers
                      checkStrictly: false,
                    }}
                    onRow={(record) => {
                      if (record.isGroupHeader) {
                        return {
                          style: { 
                            backgroundColor: '#f8f9fa', 
                            fontWeight: 'bold'
                          }
                        };
                      } else {
                        return {
                          onClick: () => {
                            setSelectedMarkerId(record.id.toString());
                          },
                          className: selectedTasks.includes(record.id) ? 'bg-blue-50' : ''
                        };
                      }
                    }}
                  />
                </Card>
              </Col>
              
              {/* Right Panel - Map */}
              <Col span={10} style={{ height: '100%' }}>
                <Card 
                  title={
                    <Space>
                      <EnvironmentOutlined />
                      <span>Task Locations</span>
                      <Badge count={mapMarkers.length} style={{ backgroundColor: '#52c41a' }} />
                    </Space>
                  }
                  style={{ height: '100%' }} 
                  styles={{ body: { padding: '16px', height: 'calc(100% - 57px)' } }}
                >
                  <GoogleMapView
                    markers={mapMarkers}
                    onMarkerClick={handleMapMarkerClick}
                    selectedMarkerId={selectedMarkerId}
                    height="100%"
                    showInfoWindowOnly={true}
                  />
                </Card>
              </Col>
            </Row>
          </div>
        </TabPane>

        <TabPane tab={
          <span>
            <ThunderboltOutlined />
            Reactive Assignment
          </span>
        } key="reactive">
          <Row gutter={16}>
            <Col span={16}>
              <Card title="Smart Assignment Configuration">
                <Form
                  form={reactiveForm}
                  layout="vertical"
                  onFinish={handleReactiveAssignment}
                >
                  <Row gutter={16}>
                    <Col span={12}>
                      <Form.Item
                        name="criteria"
                        label="Assignment Criteria"
                        rules={[{ required: true, message: 'Please select criteria' }]}
                      >
                        <Select placeholder="Select assignment criteria">
                          <Option value="Location">Location-based</Option>
                          <Option value="Skill">Skill-based</Option>
                          <Option value="Workload">Workload-based</Option>
                          <Option value="Mixed">Mixed criteria</Option>
                        </Select>
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item
                        name="priority"
                        label="Priority Focus"
                        rules={[{ required: true, message: 'Please select priority' }]}
                      >
                        <Select placeholder="Select priority focus">
                          <Option value="Critical">Critical First</Option>
                          <Option value="High">High Priority</Option>
                          <Option value="Due">Due Date Priority</Option>
                          <Option value="Balanced">Balanced</Option>
                        </Select>
                      </Form.Item>
                    </Col>
                  </Row>

                  <Row gutter={16}>
                    <Col span={8}>
                      <Form.Item name="zone" label="Zone Filter">
                        <Select placeholder="Select zone" allowClear>
                          <Option value="North">North</Option>
                          <Option value="South">South</Option>
                          <Option value="East">East</Option>
                          <Option value="West">West</Option>
                        </Select>
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <Form.Item name="area" label="Area Filter">
                        <Input placeholder="Enter area" />
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <Form.Item name="maxTasksPerUser" label="Max Tasks per User">
                        <InputNumber min={1} max={20} placeholder="5" style={{ width: '100%' }} />
                      </Form.Item>
                    </Col>
                  </Row>

                  <Form.Item>
                    <Button
                      type="primary"
                      htmlType="submit"
                      icon={<ThunderboltOutlined />}
                      size="large"
                    >
                      Run Smart Assignment
                    </Button>
                  </Form.Item>
                </Form>
              </Card>
            </Col>
            <Col span={8}>
              <Card title="Assignment Rules" size="small">
                <List
                  size="small"
                  dataSource={[
                    { icon: '🎯', text: 'Match skills to task types' },
                    { icon: '📍', text: 'Consider location proximity' },
                    { icon: '⚖️', text: 'Balance workload evenly' },
                    { icon: '⏰', text: 'Prioritize urgent tasks' },
                    { icon: '👥', text: 'Check user availability' },
                  ]}
                  renderItem={item => (
                    <List.Item>
                      <span className="mr-2">{item.icon}</span>
                      <span className="text-sm">{item.text}</span>
                    </List.Item>
                  )}
                />
              </Card>
            </Col>
          </Row>
        </TabPane>

        <TabPane tab={
          <span>
            <UserOutlined />
            User Workload
          </span>
        } key="workload">
          <Card title="User Workload Overview">
            <Table
              columns={userColumns}
              dataSource={[...users].sort((a, b) => b.activeTaskCount - a.activeTaskCount)}
              rowKey="userId"
              pagination={{
                pageSize: 10,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} users`,
              }}
              loading={usersLoading}
              showSorterTooltip={false}
              sortDirections={[]}
              onChange={(pagination, filters, sorter, extra) => {
                console.log('Table onChange:', { pagination, filters, sorter, extra });
                console.log('Sorted table data order:', [...users].sort((a, b) => b.activeTaskCount - a.activeTaskCount).map(u => ({ userId: u.userId, userName: u.userName, activeTaskCount: u.activeTaskCount })));
              }}
            />
          </Card>
        </TabPane>
      </Tabs>

      {/* Bulk Assignment Modal */}
      <Modal
        title="Bulk Task Assignment"
        visible={bulkModalVisible}
        onCancel={() => {
          setBulkModalVisible(false);
          bulkForm.resetFields();
        }}
        footer={null}
        width={600}
      >
        <div className="mb-4">
          <p>You are about to assign <strong>{selectedTasks.length}</strong> tasks.</p>
        </div>

        <Form form={bulkForm} onFinish={handleBulkAssignment} layout="vertical">
          <Form.Item
            name="userId"
            label="Assign To User"
            rules={[{ required: true, message: 'Please select a user' }]}
          >
            <Select placeholder="Select user" loading={usersLoading}>
              {users
                .filter(user => user.availabilityStatus === 'Available')
                .map(user => (
                  <Option key={user.userId} value={user.userId}>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <Avatar size="small" icon={<UserOutlined />} className="mr-2" />
                        <div>
                          <div className="text-sm font-medium">{user.userName}</div>
                          <div className="text-xs text-gray-500">{user.zone}</div>
                        </div>
                      </div>
                      <div className="text-xs">
                        {user.activeTaskCount}/{user.maxCapacity}
                      </div>
                    </div>
                  </Option>
                ))}
            </Select>
          </Form.Item>

          <Form.Item name="reason" label="Assignment Reason">
            <TextArea 
              rows={3} 
              placeholder="Enter reason for bulk assignment (optional)"
            />
          </Form.Item>

          <div className="flex justify-end space-x-2">
            <Button
              onClick={() => {
                setBulkModalVisible(false);
                bulkForm.resetFields();
              }}
            >
              Cancel
            </Button>
            <Button type="primary" htmlType="submit">
              Assign Tasks
            </Button>
          </div>
        </Form>
      </Modal>

      {/* User Selection Modal */}
      <Modal
        title={`Select User for Task Assignment`}
        visible={userSelectionVisible}
        onCancel={() => {
          setUserSelectionVisible(false);
          setSelectedUser(null);
          setPendingTaskId(null);
          setValidationData({});
        }}
        footer={[
          <Button key="cancel" onClick={() => {
            setUserSelectionVisible(false);
            setSelectedUser(null);
            setPendingTaskId(null);
            setValidationData({});
          }}>
            Cancel
          </Button>,
          <Button 
            key="assign" 
            type="primary" 
            disabled={!selectedUser}
            onClick={() => selectedUser && handleUserSelected(selectedUser)}
          >
            Assign Task
          </Button>
        ]}
        width={600}
        styles={{ body: { maxHeight: '70vh', overflowY: 'auto' } }}
      >
        <div className="mb-4">
          <p className="text-gray-600">
            Select a user to assign Task #{pendingTaskId}. Click on a user card to select them.
          </p>
        </div>

        <div className="space-y-2" style={{ maxHeight: '50vh', overflowY: 'auto', paddingRight: '8px' }}>
          {users
            .sort((a, b) => {
              // Sort by availability status first, then by workload
              if (a.availabilityStatus === 'Available' && b.availabilityStatus !== 'Available') return -1;
              if (a.availabilityStatus !== 'Available' && b.availabilityStatus === 'Available') return 1;
              return a.workloadPercentage - b.workloadPercentage;
            })
            .map(user => (
              <UserWorkloadCard
                key={user.userId}
                user={user}
                selected={selectedUser?.userId === user.userId}
                onClick={setSelectedUser}
                showValidation={true}
                validationWarnings={validationData[user.userId]?.warnings || []}
              />
            ))}
        </div>
      </Modal>

      {/* User Tasks Modal */}
      <UserTasksModal
        visible={userTasksModalVisible}
        user={selectedUserForTasks}
        tasks={userTasks}
        loading={userTasksLoading}
        onClose={() => setUserTasksModalVisible(false)}
      />
    </div>
  );
};

export default TaskAssignment; 