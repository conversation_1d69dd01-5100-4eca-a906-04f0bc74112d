'use client';

import React, { useState, useEffect, useCallback } from 'react';
import {
  Table, Button, Modal, Form, Input, Select, Card, Space, DatePicker,
  Tag, Statistic, Row, Col, message, Tooltip, Popconfirm, Badge,
  Progress, Tabs, List, Avatar, Timeline, Alert, Divider, Typography,
  Rate, Descriptions, Switch, InputNumber
} from 'antd';
import {
  SearchOutlined, PlusOutlined, EditOutlined, DeleteOutlined,
  UserOutlined, CheckOutlined, CloseOutlined, ExclamationCircleOutlined,
  WarningOutlined, ReloadOutlined, FieldTimeOutlined, HistoryOutlined,
  EyeOutlined, TeamOutlined, BugOutlined, ToolOutlined, LineChartOutlined,
  CalendarOutlined, FireOutlined, AlertOutlined
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import dayjs from 'dayjs';
import { 
  meterReadingService, ReadingAnomalyDto, AnomalySearchDto,
  CreateAnomalyDto, UpdateAnomalyDto
} from '@/services/meter-reading.service';

const { RangePicker } = DatePicker;
const { Option } = Select;
const { TextArea } = Input;
const { TabPane } = Tabs;
const { Title, Text } = Typography;

const AnomalyManagement: React.FC = () => {
  const [anomalies, setAnomalies] = useState<ReadingAnomalyDto[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchForm] = Form.useForm();
  const [assignForm] = Form.useForm();
  const [resolveForm] = Form.useForm();
  const [createForm] = Form.useForm();

  // Modal states
  const [assignModalVisible, setAssignModalVisible] = useState(false);
  const [resolveModalVisible, setResolveModalVisible] = useState(false);
  const [createModalVisible, setCreateModalVisible] = useState(false);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [selectedAnomaly, setSelectedAnomaly] = useState<ReadingAnomalyDto | null>(null);

  // Search and filter
  const [searchParams, setSearchParams] = useState<AnomalySearchDto>({
    page: 1,
    pageSize: 10
  });
  const [totalCount, setTotalCount] = useState(0);
  const [activeTab, setActiveTab] = useState('all');

  // Statistics
  const [stats, setStats] = useState({
    total: 0,
    unresolved: 0,
    assigned: 0,
    critical: 0,
    recurring: 0,
    fieldVisitRequired: 0
  });

  // Load anomalies
  const loadAnomalies = useCallback(async () => {
    setLoading(true);
    try {
      let result;
      if (activeTab === 'unresolved') {
        result = await meterReadingService.getUnresolvedAnomalies();
        setAnomalies(result);
        setTotalCount(result.length);
      } else {
        const searchParamsWithTab = {
          ...searchParams,
          status: activeTab === 'all' ? undefined : activeTab
        };
        result = await meterReadingService.getAnomalies(searchParamsWithTab);
        setAnomalies(result);
        // Note: API should return paginated result with total count
        setTotalCount(result.length);
      }
      
      // Calculate statistics
      const allAnomalies = result;
      setStats({
        total: allAnomalies.length,
        unresolved: allAnomalies.filter(a => a.status !== 'Resolved').length,
        assigned: allAnomalies.filter(a => a.assignedTo).length,
        critical: allAnomalies.filter(a => a.severity === 'Critical').length,
        recurring: allAnomalies.filter(a => a.isRecurring).length,
        fieldVisitRequired: allAnomalies.filter(a => a.requiresFieldVisit).length
      });
    } catch (error) {
      message.error('Failed to load anomalies');
      console.error('Load anomalies error:', error);
    } finally {
      setLoading(false);
    }
  }, [searchParams, activeTab]);

  useEffect(() => {
    loadAnomalies();
  }, [loadAnomalies]);

  // Handle search
  const handleSearch = (values: any) => {
    const newSearchParams = {
      ...searchParams,
      ...values,
      startDate: values.dateRange?.[0]?.format('YYYY-MM-DD'),
      endDate: values.dateRange?.[1]?.format('YYYY-MM-DD'),
      page: 1
    };
    delete newSearchParams.dateRange;
    setSearchParams(newSearchParams);
  };

  // Handle assign anomaly
  const handleAssignAnomaly = async (values: any) => {
    if (!selectedAnomaly) return;

    try {
      await meterReadingService.assignAnomaly(selectedAnomaly.id, values.assignedUserId);
      message.success('Anomaly assigned successfully');
      setAssignModalVisible(false);
      assignForm.resetFields();
      setSelectedAnomaly(null);
      loadAnomalies();
    } catch (error) {
      message.error('Failed to assign anomaly');
      console.error('Assign anomaly error:', error);
    }
  };

  // Handle resolve anomaly
  const handleResolveAnomaly = async (values: any) => {
    if (!selectedAnomaly) return;

    try {
      await meterReadingService.resolveAnomaly(
        selectedAnomaly.id,
        values.resolution,
        values.resolutionType
      );
      message.success('Anomaly resolved successfully');
      setResolveModalVisible(false);
      resolveForm.resetFields();
      setSelectedAnomaly(null);
      loadAnomalies();
    } catch (error) {
      message.error('Failed to resolve anomaly');
      console.error('Resolve anomaly error:', error);
    }
  };

  // Get severity color
  const getSeverityColor = (severity: string) => {
    const colors = {
      'Low': 'green',
      'Medium': 'orange',
      'High': 'red',
      'Critical': 'red'
    };
    return colors[severity as keyof typeof colors] || 'default';
  };

  // Get status color
  const getStatusColor = (status: string) => {
    const colors = {
      'Detected': 'orange',
      'Assigned': 'blue',
      'InProgress': 'purple',
      'Resolved': 'green',
      'Dismissed': 'gray'
    };
    return colors[status as keyof typeof colors] || 'default';
  };

  // Get anomaly type icon
  const getAnomalyTypeIcon = (type: string) => {
    const icons = {
      'HighUsage': <LineChartOutlined />,
      'LowUsage': <LineChartOutlined />,
      'NegativeUsage': <ExclamationCircleOutlined />,
      'ZeroUsage': <CloseOutlined />,
      'SuspiciousPattern': <BugOutlined />,
      'DataInconsistency': <WarningOutlined />,
      'QualityIssue': <AlertOutlined />
    };
    return icons[type as keyof typeof icons] || <ExclamationCircleOutlined />;
  };

  // Table columns
  const columns: ColumnsType<ReadingAnomalyDto> = [
    {
      title: 'Anomaly',
      key: 'anomaly',
      fixed: 'left',
      width: 200,
      render: (_, record: ReadingAnomalyDto) => (
        <div>
          <div className="flex items-center space-x-2 mb-1">
            {getAnomalyTypeIcon(record.anomalyType)}
            <span className="font-medium">{record.anomalyType}</span>
          </div>
          <div className="text-xs text-gray-500">
            Meter: {record.meterNumber}
          </div>
          {record.description && (
            <div className="text-xs text-gray-600 mt-1 truncate" style={{ maxWidth: 180 }}>
              {record.description}
            </div>
          )}
        </div>
      ),
    },
    {
      title: 'Severity',
      dataIndex: 'severity',
      key: 'severity',
      width: 100,
      render: (severity: string) => (
        <Tag color={getSeverityColor(severity)} icon={
          severity === 'Critical' ? <FireOutlined /> : 
          severity === 'High' ? <WarningOutlined /> : null
        }>
          {severity}
        </Tag>
      ),
    },
    {
      title: 'Values',
      key: 'values',
      width: 150,
      render: (_, record: ReadingAnomalyDto) => (
        <div className="text-xs">
          {record.expectedValue !== null && record.actualValue !== null && (
            <div>
              <div>Expected: {record.expectedValue?.toLocaleString()}</div>
              <div>Actual: {record.actualValue?.toLocaleString()}</div>
              {record.variance !== null && (
                <div className="text-red-600">
                  Variance: {record.variance > 0 ? '+' : ''}{record.variance?.toLocaleString()}
                </div>
              )}
            </div>
          )}
          {record.confidenceScore && (
            <div className="mt-1">
              <Progress
                percent={record.confidenceScore}
                size="small"
                format={() => `${record.confidenceScore?.toFixed(0)}%`}
              />
            </div>
          )}
        </div>
      ),
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      width: 120,
      render: (status: string, record: ReadingAnomalyDto) => (
        <div>
          <Tag color={getStatusColor(status)}>{status}</Tag>
          {record.requiresFieldVisit && (
            <div className="mt-1">
              <Tag color="purple" size="small" icon={<FieldTimeOutlined />}>
                Field Visit
              </Tag>
            </div>
          )}
          {record.isRecurring && (
            <div className="mt-1">
              <Tag color="orange" size="small" icon={<HistoryOutlined />}>
                Recurring
              </Tag>
            </div>
          )}
        </div>
      ),
    },
    {
      title: 'Assignment',
      key: 'assignment',
      width: 150,
      render: (_, record: ReadingAnomalyDto) => (
        <div>
          {record.assignedUserId ? (
            <div>
              <div className="flex items-center space-x-1">
                <UserOutlined />
                <span className="text-sm">User {record.assignedUserId}</span>
              </div>
              {record.assignedDate && (
                <div className="text-xs text-gray-500">
                  {dayjs(record.assignedDate).format('MM-DD HH:mm')}
                </div>
              )}
            </div>
          ) : (
            <span className="text-gray-400 text-sm">Unassigned</span>
          )}
        </div>
      ),
    },
    {
      title: 'Resolution',
      key: 'resolution',
      width: 150,
      render: (_, record: ReadingAnomalyDto) => (
        <div>
          {record.status === 'Resolved' ? (
            <div>
              <div className="text-sm">{record.resolutionType}</div>
              {record.resolvedBy && (
                <div className="text-xs text-gray-500">
                  By: {record.resolvedBy}
                </div>
              )}
              {record.resolvedDate && (
                <div className="text-xs text-gray-500">
                  {dayjs(record.resolvedDate).format('MM-DD HH:mm')}
                </div>
              )}
            </div>
          ) : (
            <span className="text-gray-400 text-sm">Pending</span>
          )}
        </div>
      ),
    },
    {
      title: 'Created',
      dataIndex: 'createdAt',
      key: 'createdAt',
      sorter: true,
      width: 120,
      render: (date: string) => dayjs(date).format('MM-DD HH:mm'),
    },
    {
      title: 'Actions',
      key: 'actions',
      fixed: 'right',
      width: 200,
      render: (_, record: ReadingAnomalyDto) => (
        <Space>
          <Tooltip title="View Details">
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() => {
                setSelectedAnomaly(record);
                setDetailModalVisible(true);
              }}
            />
          </Tooltip>

          {record.status !== 'Resolved' && (
            <>
              <Tooltip title="Assign">
                <Button
                  type="text"
                  icon={<TeamOutlined />}
                  onClick={() => {
                    setSelectedAnomaly(record);
                    assignForm.setFieldsValue({
                      currentAssignee: record.assignedTo
                    });
                    setAssignModalVisible(true);
                  }}
                />
              </Tooltip>

              <Tooltip title="Resolve">
                <Button
                  type="text"
                  icon={<CheckOutlined />}
                  className="text-green-600"
                  onClick={() => {
                    setSelectedAnomaly(record);
                    setResolveModalVisible(true);
                  }}
                />
              </Tooltip>
            </>
          )}
        </Space>
      ),
    },
  ];

  return (
    <div className="p-6">
      {/* Header */}
      <div className="mb-6">
        <Form
          form={searchForm}
          layout="inline"
          onFinish={handleSearch}
          initialValues={searchParams}
        >
          <Form.Item name="meterNumber">
            <Input placeholder="Meter Number" style={{ width: 150 }} />
          </Form.Item>

          <Form.Item name="dateRange">
            <RangePicker placeholder={['Start Date', 'End Date']} />
          </Form.Item>

          <Form.Item name="anomalyType">
            <Select placeholder="Anomaly Type" allowClear style={{ width: 160 }}>
              <Option value="HighUsage">High Usage</Option>
              <Option value="LowUsage">Low Usage</Option>
              <Option value="NegativeUsage">Negative Usage</Option>
              <Option value="ZeroUsage">Zero Usage</Option>
              <Option value="SuspiciousPattern">Suspicious Pattern</Option>
              <Option value="DataInconsistency">Data Inconsistency</Option>
              <Option value="QualityIssue">Quality Issue</Option>
            </Select>
          </Form.Item>

          <Form.Item name="severity">
            <Select placeholder="Severity" allowClear style={{ width: 120 }}>
              <Option value="Low">Low</Option>
              <Option value="Medium">Medium</Option>
              <Option value="High">High</Option>
              <Option value="Critical">Critical</Option>
            </Select>
          </Form.Item>

          <Form.Item name="assignedTo">
            <Input placeholder="Assigned To" style={{ width: 150 }} />
          </Form.Item>

          <Form.Item name="requiresFieldVisit">
            <Select placeholder="Field Visit" allowClear style={{ width: 130 }}>
              <Option value={true}>Required</Option>
              <Option value={false}>Not Required</Option>
            </Select>
          </Form.Item>

          <Form.Item>
            <Button type="primary" htmlType="submit" icon={<SearchOutlined />}>
              Search
            </Button>
          </Form.Item>

          <Form.Item>
            <Button
              onClick={() => {
                searchForm.resetFields();
                setSearchParams({ page: 1, pageSize: 10 });
              }}
            >
              Reset
            </Button>
          </Form.Item>
        </Form>
      </div>

      {/* Status Tabs */}
      <Card className="mb-6">
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane tab="All Anomalies" key="all" />
          <TabPane tab="Unresolved" key="unresolved" />
          <TabPane tab="Detected" key="Detected" />
          <TabPane tab="Assigned" key="Assigned" />
          <TabPane tab="In Progress" key="InProgress" />
          <TabPane tab="Resolved" key="Resolved" />
        </Tabs>
      </Card>

      {/* Statistics Cards */}
      <Row gutter={16} className="mb-6">
        <Col span={4}>
          <Card>
            <Statistic
              title="Total Anomalies"
              value={stats.total}
              valueStyle={{ color: '#1890ff' }}
              prefix={<BugOutlined />}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic
              title="Unresolved"
              value={stats.unresolved}
              valueStyle={{ color: '#fa8c16' }}
              prefix={<ExclamationCircleOutlined />}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic
              title="Assigned"
              value={stats.assigned}
              valueStyle={{ color: '#52c41a' }}
              prefix={<UserOutlined />}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic
              title="Critical"
              value={stats.critical}
              valueStyle={{ color: '#f5222d' }}
              prefix={<FireOutlined />}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic
              title="Recurring"
              value={stats.recurring}
              valueStyle={{ color: '#722ed1' }}
              prefix={<HistoryOutlined />}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic
              title="Field Visits"
              value={stats.fieldVisitRequired}
              valueStyle={{ color: '#13c2c2' }}
              prefix={<FieldTimeOutlined />}
            />
          </Card>
        </Col>
      </Row>

      {/* Anomalies Table */}
      <Card>
        <Table
          columns={columns}
          dataSource={anomalies}
          rowKey="id"
          loading={loading}
          scroll={{ x: 1400 }}
          pagination={{
            current: searchParams.page,
            pageSize: searchParams.pageSize,
            total: totalCount,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} anomalies`,
          }}
        />
      </Card>

      {/* Assign Anomaly Modal */}
      <Modal
        title="Assign Anomaly"
        visible={assignModalVisible}
        onCancel={() => {
          setAssignModalVisible(false);
          assignForm.resetFields();
          setSelectedAnomaly(null);
        }}
        footer={null}
        width={500}
      >
        <Form
          form={assignForm}
          layout="vertical"
          onFinish={handleAssignAnomaly}
        >
          <Form.Item name="currentAssignee" label="Current Assignee">
            <Input disabled placeholder="Unassigned" />
          </Form.Item>

          <Form.Item
            name="assignedUserId"
            label="Assign To"
            rules={[{ required: true, message: 'Please select assignee' }]}
          >
            <Select placeholder="Select user to assign">
              <Option value={1}>Technician 1</Option>
              <Option value={2}>Technician 2</Option>
              <Option value={3}>Supervisor</Option>
              <Option value={4}>Manager</Option>
            </Select>
          </Form.Item>

          <div className="flex justify-end space-x-2">
            <Button
              onClick={() => {
                setAssignModalVisible(false);
                assignForm.resetFields();
                setSelectedAnomaly(null);
              }}
            >
              Cancel
            </Button>
            <Button type="primary" htmlType="submit">
              Assign
            </Button>
          </div>
        </Form>
      </Modal>

      {/* Resolve Anomaly Modal */}
      <Modal
        title="Resolve Anomaly"
        visible={resolveModalVisible}
        onCancel={() => {
          setResolveModalVisible(false);
          resolveForm.resetFields();
          setSelectedAnomaly(null);
        }}
        footer={null}
        width={600}
      >
        <Form
          form={resolveForm}
          layout="vertical"
          onFinish={handleResolveAnomaly}
        >
          <Form.Item
            name="resolutionType"
            label="Resolution Type"
            rules={[{ required: true, message: 'Please select resolution type' }]}
          >
            <Select placeholder="Select resolution type">
              <Option value="Fixed">Fixed</Option>
              <Option value="False Positive">False Positive</Option>
              <Option value="Data Corrected">Data Corrected</Option>
              <Option value="Meter Replaced">Meter Replaced</Option>
              <Option value="Customer Notified">Customer Notified</Option>
              <Option value="Dismissed">Dismissed</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="resolution"
            label="Resolution Details"
            rules={[{ required: true, message: 'Please provide resolution details' }]}
          >
            <TextArea
              rows={4}
              placeholder="Describe how the anomaly was resolved"
            />
          </Form.Item>

          <div className="flex justify-end space-x-2">
            <Button
              onClick={() => {
                setResolveModalVisible(false);
                resolveForm.resetFields();
                setSelectedAnomaly(null);
              }}
            >
              Cancel
            </Button>
            <Button type="primary" htmlType="submit">
              Resolve Anomaly
            </Button>
          </div>
        </Form>
      </Modal>

      {/* Anomaly Detail Modal */}
      <Modal
        title="Anomaly Details"
        visible={detailModalVisible}
        onCancel={() => {
          setDetailModalVisible(false);
          setSelectedAnomaly(null);
        }}
        footer={null}
        width={800}
      >
        {selectedAnomaly && (
          <div>
            <Descriptions bordered size="small">
              <Descriptions.Item label="Anomaly Type" span={2}>
                <div className="flex items-center space-x-2">
                  {getAnomalyTypeIcon(selectedAnomaly.anomalyType)}
                  <span>{selectedAnomaly.anomalyType}</span>
                </div>
              </Descriptions.Item>
              <Descriptions.Item label="Severity">
                <Tag color={getSeverityColor(selectedAnomaly.severity)}>
                  {selectedAnomaly.severity}
                </Tag>
              </Descriptions.Item>
              
              <Descriptions.Item label="Meter Number">
                {selectedAnomaly.meterNumber}
              </Descriptions.Item>
              <Descriptions.Item label="Status">
                <Tag color={getStatusColor(selectedAnomaly.status)}>
                  {selectedAnomaly.status}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="Confidence">
                {selectedAnomaly.confidenceScore?.toFixed(1)}%
              </Descriptions.Item>

              {selectedAnomaly.expectedValue !== null && (
                <Descriptions.Item label="Expected Value">
                  {selectedAnomaly.expectedValue?.toLocaleString()}
                </Descriptions.Item>
              )}
              {selectedAnomaly.actualValue !== null && (
                <Descriptions.Item label="Actual Value">
                  {selectedAnomaly.actualValue?.toLocaleString()}
                </Descriptions.Item>
              )}
              {selectedAnomaly.variance !== null && (
                <Descriptions.Item label="Variance">
                  <span className={selectedAnomaly.variance > 0 ? 'text-red-600' : 'text-green-600'}>
                    {selectedAnomaly.variance > 0 ? '+' : ''}{selectedAnomaly.variance?.toLocaleString()}
                  </span>
                </Descriptions.Item>
              )}

              <Descriptions.Item label="Field Visit Required">
                {selectedAnomaly.requiresFieldVisit ? 'Yes' : 'No'}
              </Descriptions.Item>
              <Descriptions.Item label="Recurring">
                {selectedAnomaly.isRecurring ? 'Yes' : 'No'}
              </Descriptions.Item>
              <Descriptions.Item label="Created">
                {dayjs(selectedAnomaly.createdAt).format('YYYY-MM-DD HH:mm:ss')}
              </Descriptions.Item>

              {selectedAnomaly.description && (
                <Descriptions.Item label="Description" span={3}>
                  {selectedAnomaly.description}
                </Descriptions.Item>
              )}

              {selectedAnomaly.assignedUserId && (
                <>
                  <Descriptions.Item label="Assigned To">
                    User {selectedAnomaly.assignedUserId}
                  </Descriptions.Item>
                  <Descriptions.Item label="Assigned Date" span={2}>
                    {selectedAnomaly.assignedDate && 
                      dayjs(selectedAnomaly.assignedDate).format('YYYY-MM-DD HH:mm:ss')
                    }
                  </Descriptions.Item>
                </>
              )}

              {selectedAnomaly.status === 'Resolved' && (
                <>
                  <Descriptions.Item label="Resolution Type">
                    {selectedAnomaly.resolutionType}
                  </Descriptions.Item>
                  <Descriptions.Item label="Resolved By">
                    {selectedAnomaly.resolvedBy}
                  </Descriptions.Item>
                  <Descriptions.Item label="Resolved Date">
                    {selectedAnomaly.resolvedDate && 
                      dayjs(selectedAnomaly.resolvedDate).format('YYYY-MM-DD HH:mm:ss')
                    }
                  </Descriptions.Item>
                  {selectedAnomaly.resolution && (
                    <Descriptions.Item label="Resolution Details" span={3}>
                      {selectedAnomaly.resolution}
                    </Descriptions.Item>
                  )}
                </>
              )}
            </Descriptions>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default AnomalyManagement; 