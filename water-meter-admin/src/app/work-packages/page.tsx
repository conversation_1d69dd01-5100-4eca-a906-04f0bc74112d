'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { 
  Table, 
  Button, 
  Input, 
  Select, 
  Modal, 
  Form, 
  message, 
  Space, 
  Row, 
  Col,
  Tag,
  Popconfirm,
  Tooltip,
  DatePicker,
  Typography,
  Spin,
  InputNumber
} from 'antd';
import { 
  PlusOutlined, 
  SearchOutlined, 
  EditOutlined, 
  DeleteOutlined, 
  ExportOutlined,
  EyeOutlined,
  ReloadOutlined,
  DownloadOutlined,
  FilterOutlined,
  SendOutlined,
  PlayCircleOutlined
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import dayjs from 'dayjs';

// Import services and types
import { 
  workPackageService, 
  WorkPackageListDto, 
  WorkPackageDto, 
  WorkPackageSearchDto,
  WorkPackageStatistics,
  CreateWorkPackageDto,
  TaskGenerationResultDto,
  WorkPackageActivationResultDto
} from '@/services/workPackage.service';
import { waterMeterService } from '@/services/water-meter.service';
import { WaterMeterListDto } from '@/types/water-meter';

// Import custom components
import { 
  StatisticsCards, 
  WorkPackageDetailsModal, 
  ExcelImportWizard,
  CreateWorkPackageModal,
  WorkPackageCreationMethodModal,
  MeterFilterData 
} from './components';
import type { CreationMethod } from './components';

const { Option } = Select;
const { RangePicker } = DatePicker;
const { Title } = Typography;

// Interface for Excel Import Wizard data
interface ExcelImportWizardData {
  filteredMeters: WaterMeterListDto[];
  selectedMeterIds: string[];
  uploadedFile: File | null;
  parsedWorkPackages: any[];
  validationResults: any[];
  importResult?: any | null; // Make importResult optional
}

// Main component
export default function WorkPackageManagementRefactored() {
  // Main data states
  const [workPackages, setWorkPackages] = useState<WorkPackageListDto[]>([]);
  const [statistics, setStatistics] = useState<WorkPackageStatistics | null>(null);
  const [loading, setLoading] = useState(false);
  const [total, setTotal] = useState(0);
  const [searchParams, setSearchParams] = useState<WorkPackageSearchDto>({
    page: 1,
    pageSize: 10
  });

  // Selection states
  const [selectedRowKeys, setSelectedRowKeys] = useState<number[]>([]);

  // Modal states
  const [isCreationMethodModalVisible, setIsCreationMethodModalVisible] = useState(false);
  const [isExcelImportWizardVisible, setIsExcelImportWizardVisible] = useState(false);
  const [isCreateWorkPackageModalVisible, setIsCreateWorkPackageModalVisible] = useState(false);
  const [isViewDetailsModalVisible, setIsViewDetailsModalVisible] = useState(false);
  const [isWorkPackageCreationMethodModalVisible, setIsWorkPackageCreationMethodModalVisible] = useState(false);
  const [isEditWorkPackageModalVisible, setIsEditWorkPackageModalVisible] = useState(false);

  // Loading states
  const [createWorkPackageLoading, setCreateWorkPackageLoading] = useState(false);
  const [editWorkPackageLoading, setEditWorkPackageLoading] = useState(false);

  // Excel Import Wizard states
  const [excelImportStep, setExcelImportStep] = useState(0);
  const [excelImportLoading, setExcelImportLoading] = useState(false);
  // Excel Import Wizard Data State
  const [excelImportData, setExcelImportData] = useState<ExcelImportWizardData>({
    filteredMeters: [],
    selectedMeterIds: [],
    uploadedFile: null,
    parsedWorkPackages: [],
    validationResults: [],
    importResult: null
  });

  // View details states
  const [selectedWorkPackage, setSelectedWorkPackage] = useState<WorkPackageDto | null>(null);
  const [viewDetailsLoading, setViewDetailsLoading] = useState(false);

  // Edit states
  const [editingWorkPackage, setEditingWorkPackage] = useState<WorkPackageListDto | null>(null);

  // Forms
  const [searchForm] = Form.useForm();
  const [editForm] = Form.useForm();

  // Load data
  const loadData = useCallback(async () => {
    setLoading(true);
    try {
      const [workPackagesResponse, statisticsResponse] = await Promise.all([
        workPackageService.getWorkPackages(searchParams),
        workPackageService.getWorkPackageStatistics()
      ]);

      setWorkPackages(workPackagesResponse.workPackages);
      setTotal(workPackagesResponse.totalCount);
      setStatistics(statisticsResponse);
    } catch (error) {
      message.error('Failed to load work packages');
      console.error('Load data error:', error);
    } finally {
      setLoading(false);
    }
  }, [searchParams]);

  useEffect(() => {
    loadData();
  }, [loadData]);

  // Handle search
  const handleSearch = (values: any) => {
    const newSearchParams: WorkPackageSearchDto = {
      page: 1,
      pageSize: searchParams.pageSize,
      name: values.name,
      packageType: values.packageType,
      status: values.status,
      priority: values.priority,
      serviceArea: values.serviceArea,
      plannedStartDateFrom: values.dateRange?.[0] ? dayjs(values.dateRange[0]).toISOString() : undefined,
      plannedStartDateTo: values.dateRange?.[1] ? dayjs(values.dateRange[1]).toISOString() : undefined,
    };
    setSearchParams(newSearchParams);
  };

  // Handle reset
  const handleReset = () => {
    searchForm.resetFields();
    setSearchParams({ page: 1, pageSize: 10 });
  };

  // Handle view details
  const handleViewDetails = async (record: WorkPackageListDto) => {
    setViewDetailsLoading(true);
    setIsViewDetailsModalVisible(true);
    try {
      const workPackage = await workPackageService.getWorkPackageById(record.id);
      setSelectedWorkPackage(workPackage);
    } catch (error) {
      message.error('Failed to load work package details');
      console.error('Load details error:', error);
    } finally {
      setViewDetailsLoading(false);
    }
  };

  // Handle delete
  const handleDelete = async (id: number) => {
    try {
      await workPackageService.deleteWorkPackage(id);
      message.success('Work package deleted successfully');
      loadData();
    } catch (error) {
      message.error('Failed to delete work package');
      console.error('Delete error:', error);
    }
  };

  // Handle generate tasks
  const handleGenerateTasks = async (record: WorkPackageListDto) => {
    try {
      const result = await workPackageService.generateTasks(record.id);
      
      if (result.success) {
        message.success(`${result.message}. Generated ${result.generatedTaskCount} tasks.`);
        
        // Show warnings if any
        if (result.warnings && result.warnings.length > 0) {
          result.warnings.forEach(warning => {
            message.warning(warning);
          });
        }
        
        // Refresh data to show updated status
        loadData();
      } else {
        message.error(result.message);
      }
    } catch (error: any) {
      console.error('Generate tasks error:', error);
      
      // Handle specific error cases with friendly messages
      const errorMessage = error.response?.data?.message || error.message;
      
      if (errorMessage?.includes('Tasks already exist')) {
        Modal.info({
          title: 'Tasks Already Generated',
          content: (
            <div>
              <p>Tasks have already been generated for this work package.</p>
              <p><strong>Details:</strong> {errorMessage}</p>
              <p>You can view the existing tasks in the task management section or edit individual tasks if needed.</p>
            </div>
          ),
          okText: 'Understood'
        });
      } else if (errorMessage?.includes('No water meters')) {
        Modal.warning({
          title: 'Cannot Generate Tasks',
          content: (
            <div>
              <p>No water meters are assigned to this work package.</p>
              <p><strong>Details:</strong> {errorMessage}</p>
              <p>Please assign water meters to this work package before generating tasks.</p>
            </div>
          ),
          okText: 'Got it'
        });
      } else {
        Modal.error({
          title: 'Failed to Generate Tasks',
          content: (
            <div>
              <p><strong>Error:</strong> {errorMessage || 'An unexpected error occurred'}</p>
              <p>Please try again or contact support if the problem persists.</p>
            </div>
          ),
          okText: 'Close'
        });
      }
    }
  };

  // Handle activate work package
  const handleActivateWorkPackage = async (record: WorkPackageListDto) => {
    Modal.confirm({
      title: `Activate Work Package: ${record.name}`,
      content: (
        <div>
          <p>This will activate the work package and automatically generate tasks for all {record.totalMeters} water meters.</p>
          <p>Are you sure you want to proceed?</p>
        </div>
      ),
      okText: 'Activate',
      okType: 'primary',
      cancelText: 'Cancel',
      onOk: async () => {
        try {
          const result = await workPackageService.activateWorkPackage(record.id, true);
          
          if (result.success) {
            // Success message
            message.success(result.message);
            
            // Show task generation info if tasks were generated
            if (result.tasksGenerated && result.generatedTaskCount > 0) {
              message.info(`${result.generatedTaskCount} tasks generated successfully`);
            }
            
            // Show warnings if any
            if (result.warnings && result.warnings.length > 0) {
              result.warnings.forEach((warning, index) => {
                setTimeout(() => {
                  message.warning(warning);
                }, index * 1000); // Stagger warnings
              });
            }
            
            // Refresh data to show updated status
            loadData();
          } else {
            // Show detailed error message
            Modal.error({
              title: 'Activation Failed',
              content: (
                <div>
                  <p><strong>Message:</strong> {result.message}</p>
                  {result.validationResults && result.validationResults.length > 0 && (
                    <div>
                      <p><strong>Validation Issues:</strong></p>
                      <ul>
                        {result.validationResults.map((validation, index) => (
                          <li key={index}>{validation}</li>
                        ))}
                      </ul>
                    </div>
                  )}
                  {result.warnings && result.warnings.length > 0 && (
                    <div>
                      <p><strong>Warnings:</strong></p>
                      <ul>
                        {result.warnings.map((warning, index) => (
                          <li key={index}>{warning}</li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              ),
            });
          }
        } catch (error: any) {
          console.error('Activate work package error:', error);
          
          // Handle specific error responses with user-friendly messages
          const errorData = error.response?.data;
          const errorMessage = errorData?.message || error.message;
          
          if (errorMessage?.includes('No water meters assigned')) {
            Modal.warning({
              title: 'Cannot Activate Work Package',
              content: (
                <div>
                  <p><strong>Issue:</strong> No water meters are assigned to this work package.</p>
                  <p><strong>Solution:</strong> Please assign water meters to this work package before activation.</p>
                  <br />
                  <p><strong>Steps to fix:</strong></p>
                  <ol>
                    <li>Edit this work package</li>
                    <li>Add water meters to the assignment</li>
                    <li>Save changes</li>
                    <li>Try activating again</li>
                  </ol>
                  {errorData?.validationResults && errorData.validationResults.length > 0 && (
                    <div>
                      <p><strong>Additional details:</strong></p>
                      <ul>
                        {errorData.validationResults.map((validation: string, index: number) => (
                          <li key={index}>{validation}</li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              ),
              okText: 'Understood',
              width: 500
            });
          } else if (errorMessage?.includes('already active')) {
            Modal.info({
              title: 'Work Package Already Active',
              content: (
                <div>
                  <p>This work package is already in an active state.</p>
                  <p><strong>Details:</strong> {errorMessage}</p>
                </div>
              ),
              okText: 'Got it'
            });
          } else {
            Modal.error({
              title: 'Failed to Activate Work Package',
              content: (
                <div>
                  <p><strong>Error:</strong> {errorMessage || 'An unexpected error occurred'}</p>
                  {errorData?.validationResults && errorData.validationResults.length > 0 && (
                    <div>
                      <p><strong>Validation Issues:</strong></p>
                      <ul>
                        {errorData.validationResults.map((validation: string, index: number) => (
                          <li key={index}>{validation}</li>
                        ))}
                      </ul>
                    </div>
                  )}
                  <p>Please check the work package configuration and try again.</p>
                </div>
              ),
              okText: 'Close',
              width: 500
            });
          }
        }
      },
    });
  };

  // States for storing filter conditions and pagination info
  const [currentFilterData, setCurrentFilterData] = useState<MeterFilterData | null>(null);
  const [totalFilteredCount, setTotalFilteredCount] = useState(0);
  const [currentFilterPage, setCurrentFilterPage] = useState(1);
  const [currentFilterPageSize, setCurrentFilterPageSize] = useState(50);

  // Excel Import Wizard handlers
  const handleMeterFilter = async (filterData: MeterFilterData) => {
    setExcelImportLoading(true);
    try {
      // Save filter conditions for later use (export, pagination)
      setCurrentFilterData(filterData);
      setCurrentFilterPage(1);
      setCurrentFilterPageSize(50);

      // Call API to get first page and total count
      const searchParams = {
        page: 1,
        pageSize: 50,
        workPackageAssignment: filterData.workPackageAssignment,
        serviceArea: filterData.serviceArea,
        subArea: filterData.subArea,
        meterType: filterData.meterType,
        status: filterData.status,
        addressContains: filterData.addressContains,
        installDateFrom: filterData.installDateRange?.[0]?.toDate(),
        installDateTo: filterData.installDateRange?.[1]?.toDate(),
        latitudeMin: filterData.gpsRange?.latMin,
        latitudeMax: filterData.gpsRange?.latMax,
        longitudeMin: filterData.gpsRange?.lngMin,
        longitudeMax: filterData.gpsRange?.lngMax
      };

      console.log('🔍 Calling water meter API with params:', searchParams);
      const response = await waterMeterService.getWaterMeters(searchParams);
      console.log('✅ API Response:', response);
      
      // Check if response and data exist
      if (!response || (!response.data && !response.meters)) {
        throw new Error('Invalid API response structure');
      }

      // Use data or meters field (the type supports both)
      const waterMeters = response.data || response.meters || [];
      const totalCount = response.totalCount || waterMeters.length;
      
      // Store the first page data and total count
      setTotalFilteredCount(totalCount);
      setExcelImportData(prev => ({
        ...prev,
        filteredMeters: waterMeters,
        selectedMeterIds: waterMeters.map((m: WaterMeterListDto) => m.serialNumber || '')
      }));

      message.success(`Found ${totalCount} water meters matching your criteria`);
    } catch (error) {
      console.error('🚨 Filter error:', error);
      message.error(`Failed to filter meters: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setExcelImportLoading(false);
    }
  };

  // Handle pagination for excel import wizard
  const handleExcelWizardPageChange = async (page: number, pageSize: number) => {
    if (!currentFilterData) return;

    // Update pagination state
    setCurrentFilterPage(page);
    setCurrentFilterPageSize(pageSize);

    setExcelImportLoading(true);
    try {
      const searchParams = {
        page,
        pageSize,
        workPackageAssignment: currentFilterData.workPackageAssignment,
        serviceArea: currentFilterData.serviceArea,
        subArea: currentFilterData.subArea,
        meterType: currentFilterData.meterType,
        status: currentFilterData.status,
        addressContains: currentFilterData.addressContains,
        installDateFrom: currentFilterData.installDateRange?.[0]?.toDate(),
        installDateTo: currentFilterData.installDateRange?.[1]?.toDate(),
        latitudeMin: currentFilterData.gpsRange?.latMin,
        latitudeMax: currentFilterData.gpsRange?.latMax,
        longitudeMin: currentFilterData.gpsRange?.lngMin,
        longitudeMax: currentFilterData.gpsRange?.lngMax
      };

      const response = await waterMeterService.getWaterMeters(searchParams);
      const waterMeters = response.data || response.meters || [];
      
      setExcelImportData(prev => ({
        ...prev,
        filteredMeters: waterMeters,
        selectedMeterIds: waterMeters.map((m: WaterMeterListDto) => m.serialNumber || '')
      }));
    } catch (error) {
      console.error('🚨 Pagination error:', error);
      message.error('Failed to load page data');
    } finally {
      setExcelImportLoading(false);
    }
  };

  const handleExportTemplate = async () => {
    try {
      // Check if we have current filter data
      if (!currentFilterData) {
        message.warning('Please filter water meters first before exporting template');
        return;
      }

      if (totalFilteredCount === 0) {
        message.warning('No meters found matching the current filter criteria');
        return;
      }

      setExcelImportLoading(true);
      message.loading('Collecting all filtered meters for export...', 0);

      // Collect all meter IDs from all pages
      const allMeterIds: string[] = [];
      const totalPages = Math.ceil(totalFilteredCount / 50); // Use reasonable page size for export
      
      for (let page = 1; page <= totalPages; page++) {
        try {
          const searchParams = {
            page,
            pageSize: 50,
            workPackageAssignment: currentFilterData.workPackageAssignment,
            serviceArea: currentFilterData.serviceArea,
            subArea: currentFilterData.subArea,
            meterType: currentFilterData.meterType,
            status: currentFilterData.status,
            addressContains: currentFilterData.addressContains,
            installDateFrom: currentFilterData.installDateRange?.[0]?.toDate(),
            installDateTo: currentFilterData.installDateRange?.[1]?.toDate(),
            latitudeMin: currentFilterData.gpsRange?.latMin,
            latitudeMax: currentFilterData.gpsRange?.latMax,
            longitudeMin: currentFilterData.gpsRange?.lngMin,
            longitudeMax: currentFilterData.gpsRange?.lngMax
          };

          const response = await waterMeterService.getWaterMeters(searchParams);
          const waterMeters = response.data || response.meters || [];
          const pageMetersIds = waterMeters.map((m: WaterMeterListDto) => m.serialNumber).filter(id => id);
          allMeterIds.push(...pageMetersIds);
          
          console.log(`📄 Collected page ${page}/${totalPages}: ${pageMetersIds.length} meters`);
        } catch (error) {
          console.warn(`Failed to fetch export data for page ${page}:`, error);
          message.destroy();
          message.error(`Failed to collect data from page ${page}. Export may be incomplete.`);
        }
      }

      message.destroy();
      
      if (allMeterIds.length === 0) {
        message.warning('No meter IDs collected for export');
        return;
      }

      console.log('🔍 Exporting template with all filtered meter IDs:', allMeterIds.length);
      
      // Use water meter service to generate template with actual data
      const blob = await waterMeterService.generateWorkPackageTemplate(allMeterIds);
      
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `work_package_template_${dayjs().format('YYYY-MM-DD-HHmm')}_${allMeterIds.length}meters.xlsx`;
      link.click();
      window.URL.revokeObjectURL(url);
      
      message.success(`Template with ${allMeterIds.length} meters exported successfully`);
      setExcelImportStep(1);
    } catch (error) {
      console.error('🚨 Export error:', error);
      message.error(`Failed to export template: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setExcelImportLoading(false);
    }
  };

  const handleExcelFileUpload = async (file: File) => {
    setExcelImportLoading(true);
    try {
      console.log('📤 Uploading Excel file:', file.name);
      
      // Call correct API to import Excel file with water meter format
      const importResult = await waterMeterService.importWorkPackagesFromExcel(file);
      console.log('✅ Import result (full):', JSON.stringify(importResult, null, 2));

      // Store the upload result
      setExcelImportData(prev => ({
        ...prev,
        uploadedFile: file,
        parsedWorkPackages: [], // No longer used
        validationResults: importResult.errors || [],
        importResult: {
          totalRows: importResult.totalRows || 0,
          successCount: importResult.successCount || 0, // Use backend's successCount
          failureCount: importResult.failureCount || 0, // Use backend's failureCount
          errors: importResult.errors || []
        }
      }));

      // Move to step 3 (results) since import is already complete
      setExcelImportStep(2);
      
      const successCount = importResult.importedWorkPackages?.length || 0; // Work packages created
      const totalRows = importResult.totalRows || 0; // Rows processed
      const failureCount = importResult.failureCount || 0; // Failed rows (from backend)
      
      console.log('📊 Import summary:', {
        totalRows,
        workPackagesCreated: successCount,
        failedRows: failureCount,
        hasErrors: importResult.errors?.length > 0,
        errorCount: importResult.errors?.length || 0
      });
      
      if (successCount > 0) {
        message.success(
          `Excel import completed successfully! Created ${successCount} work package(s) from ${totalRows} water meters.` +
          (failureCount > 0 ? ` ${failureCount} rows failed.` : '')
        );
        
        // Refresh the work packages list to show newly created ones
        loadData();
      } else {
        // Show more specific error message
        if (totalRows === 0) {
          message.warning('No data rows found in Excel file. Please check if the file has data rows below the header.');
        } else if (importResult.errors && importResult.errors.length > 0) {
          message.warning(`No work packages were created. Found ${importResult.errors.length} errors. Check the console for details.`);
        } else {
          message.warning('No work packages were created. Please check the Excel file format and ensure Work Package Name column is filled.');
        }
      }

      if (importResult.errors && importResult.errors.length > 0) {
        console.warn('⚠️ Import errors:', importResult.errors);
        console.table(importResult.errors); // Better display of errors
        
        // Show first few errors as notifications
        importResult.errors.slice(0, 3).forEach((error: string) => {
          message.error(error, 8);
        });
        if (importResult.errors.length > 3) {
          message.warning(`And ${importResult.errors.length - 3} more errors. Check the console for details.`, 5);
        }
      }

    } catch (error: any) {
      console.error('🚨 Excel upload error:', error);
      const errorMessage = error.response?.data?.Message || error.response?.data?.message || error.message || 'Failed to process Excel file';
      message.error(`Excel import failed: ${errorMessage}`);
    } finally {
      setExcelImportLoading(false);
    }
  };

  const handleCreateWorkPackagesFromExcel = async () => {
    try {
      // This function is no longer needed since import already creates work packages
      // Just close the modal and refresh data
      message.success('Import process completed');
      handleCloseExcelImportWizard();
      loadData();
    } catch (error) {
      message.error('Failed to complete import process');
      console.error('Create error:', error);
    }
  };

  // Handle closing Excel Import Wizard with state reset
  const handleCloseExcelImportWizard = () => {
    setIsExcelImportWizardVisible(false);
    // Reset pagination state
    setCurrentFilterPage(1);
    setCurrentFilterPageSize(50);
    // Reset filter data
    setCurrentFilterData(null);
    setTotalFilteredCount(0);
  };

  // Direct Work Package creation handler
  const handleCreateWorkPackage = async (createDto: CreateWorkPackageDto) => {
    setCreateWorkPackageLoading(true);
    try {
      await workPackageService.createWorkPackage(createDto);
      message.success('Work package created successfully');
      setIsCreateWorkPackageModalVisible(false);
      loadData(); // Refresh the list
    } catch (error) {
      console.error('Create work package error:', error);
      message.error('Failed to create work package');
      throw error; // Re-throw to let modal handle it
    } finally {
      setCreateWorkPackageLoading(false);
    }
  };

  // Edit Work Package handler
  const handleEditWorkPackage = (workPackage: WorkPackageListDto) => {
    setEditingWorkPackage(workPackage);
    // Pre-fill the form with current data
    editForm.setFieldsValue({
      name: workPackage.name,
      description: workPackage.description,
      packageType: workPackage.packageType,
      status: workPackage.status,
      plannedStartDate: dayjs(workPackage.plannedStartDate),
      plannedEndDate: dayjs(workPackage.plannedEndDate),
      priority: workPackage.priority,
      serviceArea: workPackage.serviceArea,
      subArea: workPackage.subArea,
      assignedTeam: workPackage.assignedTeam,
      estimatedHours: workPackage.estimatedHours,
    });
    setIsEditWorkPackageModalVisible(true);
  };

  // Update Work Package handler
  const handleUpdateWorkPackage = async (values: any) => {
    if (!editingWorkPackage) return;
    
    setEditWorkPackageLoading(true);
    try {
      const updateDto = {
        id: editingWorkPackage.id,
        name: values.name,
        description: values.description,
        packageType: values.packageType,
        status: values.status,
        plannedStartDate: values.plannedStartDate.toISOString(),
        plannedEndDate: values.plannedEndDate.toISOString(),
        frequency: editingWorkPackage.frequency || 'Monthly',
        serviceArea: values.serviceArea,
        subArea: values.subArea,
        priority: values.priority,
        assignedTeam: values.assignedTeam,
        estimatedHours: values.estimatedHours || 0,
        isTemplate: editingWorkPackage.isTemplate || false,
        isRecurring: editingWorkPackage.isRecurring || false,
      };

      await workPackageService.updateWorkPackage(editingWorkPackage.id, updateDto);
      message.success('Work package updated successfully');
      setIsEditWorkPackageModalVisible(false);
      setEditingWorkPackage(null);
      editForm.resetFields();
      loadData(); // Refresh the list
    } catch (error) {
      console.error('Update work package error:', error);
      message.error('Failed to update work package');
    } finally {
      setEditWorkPackageLoading(false);
    }
  };

  // Cancel edit handler
  const handleCancelEdit = () => {
    setIsEditWorkPackageModalVisible(false);
    setEditingWorkPackage(null);
    editForm.resetFields();
  };

  // Handle creation method selection
  const handleCreationMethodSelected = (method: CreationMethod) => {
    if (method === 'excel') {
      setIsExcelImportWizardVisible(true);
    } else if (method === 'online') {
      setIsCreateWorkPackageModalVisible(true);
    }
  };

  // Utility functions
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Completed': return 'success';
      case 'InProgress': return 'processing';
      case 'Pending': return 'warning';
      case 'Cancelled': return 'error';
      default: return 'default';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'Critical': return 'red';
      case 'High': return 'orange';
      case 'Medium': return 'blue';
      case 'Low': return 'green';
      default: return 'default';
    }
  };

  // Table columns
  const columns: ColumnsType<WorkPackageListDto> = [
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
      sorter: true,
      render: (text, record) => (
        <div>
          <div style={{ fontWeight: 'bold' }}>{text}</div>
          <div style={{ fontSize: '12px', color: '#666' }}>
            {record.serviceArea} {record.subArea && `- ${record.subArea}`}
          </div>
        </div>
      ),
    },
    {
      title: 'Type',
      dataIndex: 'packageType',
      key: 'packageType',
      width: 120,
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      width: 120,
      render: (status) => (
        <Tag color={getStatusColor(status)}>{status}</Tag>
      ),
    },
    {
      title: 'Priority',
      dataIndex: 'priority',
      key: 'priority',
      width: 100,
      render: (priority) => (
        <Tag color={getPriorityColor(priority)}>{priority}</Tag>
      ),
    },
    {
      title: 'Progress',
      key: 'progress',
      width: 120,
      render: (_, record) => (
        <div>
          <div>{record.completedMeters || 0} / {record.totalMeters}</div>
          <div style={{ fontSize: '12px', color: '#666' }}>
            {record.progressPercentage}%
          </div>
        </div>
      ),
    },
    {
      title: 'Planned Dates',
      key: 'dates',
      width: 160,
      render: (_, record) => (
        <div>
          <div style={{ fontSize: '12px' }}>
            Start: {dayjs(record.plannedStartDate).format('MM-DD')}
          </div>
          <div style={{ fontSize: '12px' }}>
            End: {dayjs(record.plannedEndDate).format('MM-DD')}
          </div>
        </div>
      ),
    },
    {
      title: 'Actions',
      key: 'actions',
      width: 200,
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="View Details">
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() => handleViewDetails(record)}
            />
          </Tooltip>
          {record.status === 'Draft' && (
            <Tooltip title="Activate Work Package">
              <Button
                type="text"
                icon={<PlayCircleOutlined />}
                onClick={() => handleActivateWorkPackage(record)}
                style={{ color: '#52c41a' }}
              />
            </Tooltip>
          )}
          {(record.status === 'Draft' || record.status === 'Active') && (
            <Tooltip title="Generate Tasks">
              <Button
                type="text"
                icon={<SendOutlined />}
                onClick={() => handleGenerateTasks(record)}
                style={{ color: '#1890ff' }}
              />
            </Tooltip>
          )}
          <Tooltip title="Edit">
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => handleEditWorkPackage(record)}
            />
          </Tooltip>
          <Popconfirm
            title="Are you sure you want to delete this work package?"
            onConfirm={() => handleDelete(record.id)}
            okText="Yes"
            cancelText="No"
          >
            <Tooltip title="Delete">
              <Button
                type="text"
                danger
                icon={<DeleteOutlined />}
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div style={{ padding: '24px' }}>
      {/* Statistics Cards */}
      <StatisticsCards 
        statistics={statistics} 
        loading={loading} 
      />

      {/* Action Toolbar */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col>
          <Button 
            type="primary" 
            icon={<PlusOutlined />}
            onClick={() => setIsCreationMethodModalVisible(true)}
          >
            Create Work Package
          </Button>
        </Col>
        <Col>
          <Button icon={<ExportOutlined />}>
            Export All
          </Button>
        </Col>
        <Col>
          <Button icon={<ReloadOutlined />} onClick={loadData}>
            Refresh
          </Button>
        </Col>
      </Row>

      {/* Search and Filter */}
      <Form
        form={searchForm}
        layout="inline"
        onFinish={handleSearch}
        style={{ marginBottom: 24 }}
      >
        <Form.Item name="name">
          <Input placeholder="Search by name" />
        </Form.Item>
        <Form.Item name="status">
          <Select placeholder="Status" style={{ width: 120 }} allowClear>
            <Option value="Pending">Pending</Option>
            <Option value="InProgress">In Progress</Option>
            <Option value="Completed">Completed</Option>
            <Option value="Cancelled">Cancelled</Option>
          </Select>
        </Form.Item>
        <Form.Item name="priority">
          <Select placeholder="Priority" style={{ width: 120 }} allowClear>
            <Option value="Low">Low</Option>
            <Option value="Medium">Medium</Option>
            <Option value="High">High</Option>
            <Option value="Critical">Critical</Option>
          </Select>
        </Form.Item>
        <Form.Item name="dateRange">
          <RangePicker />
        </Form.Item>
        <Form.Item>
          <Button type="primary" htmlType="submit" icon={<SearchOutlined />}>
            Search
          </Button>
        </Form.Item>
        <Form.Item>
          <Button onClick={handleReset}>Reset</Button>
        </Form.Item>
      </Form>

      {/* Main Data Table */}
      <Table
        columns={columns}
        dataSource={workPackages}
        rowKey="id"
        loading={loading}
        pagination={{
          current: searchParams.page,
          pageSize: searchParams.pageSize,
          total: total,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) => 
            `${range[0]}-${range[1]} of ${total} items`,
          onChange: (page, pageSize) => {
            setSearchParams(prev => ({ ...prev, page, pageSize }));
          },
        }}
        rowSelection={{
          selectedRowKeys,
          onChange: (keys) => setSelectedRowKeys(keys as number[]),
        }}
        scroll={{ x: 1200 }}
      />

      {/* Work Package Creation Method Selection Modal */}
      <WorkPackageCreationMethodModal
        visible={isCreationMethodModalVisible}
        onClose={() => setIsCreationMethodModalVisible(false)}
        onMethodSelected={handleCreationMethodSelected}
      />

      {/* Excel Import Wizard */}
      <ExcelImportWizard
        visible={isExcelImportWizardVisible}
        onClose={handleCloseExcelImportWizard}
        currentStep={excelImportStep}
        onStepChange={setExcelImportStep}
        data={excelImportData}
        onDataChange={setExcelImportData}
        loading={excelImportLoading}
        onMeterFilter={handleMeterFilter}
        onExportTemplate={handleExportTemplate}
        onFileUpload={handleExcelFileUpload}
        onCreateWorkPackages={handleCreateWorkPackagesFromExcel}
        onPageChange={handleExcelWizardPageChange}
        totalFilteredCount={totalFilteredCount}
        currentPage={currentFilterPage}
        currentPageSize={currentFilterPageSize}
      />

      {/* Work Package Details Modal */}
      <WorkPackageDetailsModal
        visible={isViewDetailsModalVisible}
        onClose={() => {
          setIsViewDetailsModalVisible(false);
          setSelectedWorkPackage(null);
        }}
        workPackage={selectedWorkPackage}
        loading={viewDetailsLoading}
      />

      {/* Create Work Package Modal - Direct */}
      <CreateWorkPackageModal
        visible={isCreateWorkPackageModalVisible}
        onClose={() => setIsCreateWorkPackageModalVisible(false)}
        onCreateWorkPackage={handleCreateWorkPackage}
        loading={createWorkPackageLoading}
      />

      {/* Edit Work Package Modal */}
      <Modal
        title="Edit Work Package"
        visible={isEditWorkPackageModalVisible}
        onCancel={handleCancelEdit}
        onOk={editForm.submit}
        confirmLoading={editWorkPackageLoading}
        destroyOnClose
        width={1000}
        bodyStyle={{ 
          maxHeight: '70vh', 
          overflowY: 'auto', 
          overflowX: 'hidden',
          padding: '20px'
        }}
      >
        <Form
          form={editForm}
          layout="vertical"
          onFinish={handleUpdateWorkPackage}
        >
          <Row gutter={12}>
            <Col span={12}>
              <Form.Item
                label="Name"
                name="name"
                rules={[{ required: true, message: 'Please enter work package name' }]}
              >
                <Input />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="Package Type"
                name="packageType"
                rules={[{ required: true, message: 'Please select package type' }]}
              >
                <Select>
                  <Option value="Routine">Routine</Option>
                  <Option value="Emergency">Emergency</Option>
                  <Option value="Preventive">Preventive</Option>
                  <Option value="Scheduled">Scheduled</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={12}>
            <Col span={24}>
              <Form.Item
                label="Description"
                name="description"
              >
                <Input.TextArea rows={3} />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={12}>
            <Col span={8}>
              <Form.Item
                label="Status"
                name="status"
                rules={[{ required: true, message: 'Please select status' }]}
              >
                <Select>
                  <Option value="Draft">Draft</Option>
                  <Option value="Active">Active</Option>
                  <Option value="Completed">Completed</Option>
                  <Option value="Cancelled">Cancelled</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label="Priority"
                name="priority"
                rules={[{ required: true, message: 'Please select priority' }]}
              >
                <Select>
                  <Option value="Low">Low</Option>
                  <Option value="Medium">Medium</Option>
                  <Option value="High">High</Option>
                  <Option value="Critical">Critical</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label="Estimated Hours"
                name="estimatedHours"
              >
                <InputNumber min={0} step={0.5} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={12}>
            <Col span={12}>
              <Form.Item
                label="Planned Start Date"
                name="plannedStartDate"
                rules={[{ required: true, message: 'Please select planned start date' }]}
              >
                <DatePicker style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="Planned End Date"
                name="plannedEndDate"
                rules={[{ required: true, message: 'Please select planned end date' }]}
              >
                <DatePicker style={{ width: '100%' }} />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={12}>
            <Col span={8}>
              <Form.Item
                label="Service Area"
                name="serviceArea"
              >
                <Input />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label="Sub Area"
                name="subArea"
              >
                <Input />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label="Assigned Team"
                name="assignedTeam"
              >
                <Input />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>
    </div>
  );
} 