'use client';

import React, { useCallback, useMemo } from 'react';
import { Modal, Card, Typography, Tag, List, Tabs, Progress, Row, Col, Empty } from 'antd';
import {
  ClockCircleOutlined,
  TeamOutlined,
  EnvironmentOutlined,
  UserOutlined,
  CalendarOutlined,
  CheckCircleOutlined
} from '@ant-design/icons';
import dayjs from 'dayjs';
import GoogleMapView, { MapMarker } from '@/components/common/GoogleMapView';
import { WorkPackageDto, WorkPackageItemDto } from '@/services/workPackage.service';

const { Text, Title } = Typography;

interface WorkPackageDetailsModalProps {
  visible: boolean;
  onClose: () => void;
  workPackage: WorkPackageDto | null;
  loading: boolean;
}

export const WorkPackageDetailsModal: React.FC<WorkPackageDetailsModalProps> = ({
  visible,
  onClose,
  workPackage,
  loading
}) => {
  // Convert work package items to map markers
  const convertItemsToMarkers = useCallback((items: WorkPackageItemDto[]): MapMarker[] => {
    if (!items || items.length === 0) return [];

    return items.map((item, index) => {
      // Mock coordinates for demo (in real app, use actual meter coordinates)
      const mockCoordinates = [
        { lat: -43.5320, lng: 172.6306 }, // Christchurch center
        { lat: -43.5280, lng: 172.6250 },
        { lat: -43.5350, lng: 172.6400 },
        { lat: -43.5300, lng: 172.6350 },
        { lat: -43.5340, lng: 172.6280 },
      ];
      const coords = mockCoordinates[index % mockCoordinates.length];

      return {
        id: item.id?.toString() || index.toString(),
        position: {
          lat: coords.lat + (Math.random() - 0.5) * 0.01, // Add small random offset
          lng: coords.lng + (Math.random() - 0.5) * 0.01
        },
        title: item.meterSerialNumber || `Meter #${index + 1}`,
                  info: {
            description: item.serviceAddress || item.propertyDetails || 'No address available',
            status: item.status || 'Pending',
            priority: workPackage?.priority || 'Medium',
            type: 'Reading'
          }
      };
    });
  }, [workPackage?.priority]);

  const mapMarkers = useMemo(() => {
    return workPackage?.items ? convertItemsToMarkers(workPackage.items) : [];
  }, [workPackage?.items, convertItemsToMarkers]);

  const getStatusColor = (status?: string) => {
    switch (status) {
      case 'Completed': return 'green';
      case 'InProgress': return 'blue';
      case 'Pending': return 'orange';
      case 'Cancelled': return 'red';
      default: return 'default';
    }
  };

  const getPriorityColor = (priority?: string) => {
    switch (priority) {
      case 'Critical': return 'red';
      case 'High': return 'orange';
      case 'Medium': return 'blue';
      case 'Low': return 'green';
      default: return 'default';
    }
  };

  const calculateProgress = () => {
    if (!workPackage?.items || workPackage.items.length === 0) return 0;
    const completedItems = workPackage.items.filter(item => item.status === 'Completed').length;
    return Math.round((completedItems / workPackage.items.length) * 100);
  };

  return (
    <Modal
      title={
        <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
          <TeamOutlined />
          <span>Work Package Details</span>
          {workPackage && (
            <Tag color={getStatusColor(workPackage.status)}>
              {workPackage.status}
            </Tag>
          )}
        </div>
      }
      open={visible}
      onCancel={onClose}
      footer={null}
      width={1000}
      style={{ top: 20 }}
    >
      {loading ? (
        <div style={{ textAlign: 'center', padding: '40px 0' }}>
          Loading work package details...
        </div>
      ) : workPackage ? (
        <div>
          {/* Basic Information */}
          <Card title="Basic Information" style={{ marginBottom: 16 }}>
            <Row gutter={[16, 16]}>
              <Col span={12}>
                <div style={{ marginBottom: 8 }}>
                  <Text strong>Package Name:</Text>
                  <br />
                  <Text style={{ fontSize: '16px' }}>{workPackage.name}</Text>
                </div>
              </Col>
              <Col span={6}>
                <div style={{ marginBottom: 8 }}>
                  <Text strong>Priority:</Text>
                  <br />
                  <Tag color={getPriorityColor(workPackage.priority)}>
                    {workPackage.priority}
                  </Tag>
                </div>
              </Col>
              <Col span={6}>
                <div style={{ marginBottom: 8 }}>
                  <Text strong>Progress:</Text>
                  <br />
                  <Progress 
                    percent={calculateProgress()} 
                    size="small" 
                    status={workPackage.status === 'Completed' ? 'success' : 'active'}
                  />
                </div>
              </Col>
              <Col span={12}>
                <div style={{ marginBottom: 8 }}>
                  <Text strong>Service Area:</Text>
                  <br />
                  <Text>{workPackage.serviceArea || 'N/A'}</Text>
                </div>
              </Col>
              <Col span={6}>
                <div style={{ marginBottom: 8 }}>
                  <Text strong>Sub Area:</Text>
                  <br />
                  <Text>{workPackage.subArea || 'N/A'}</Text>
                </div>
              </Col>
              <Col span={6}>
                <div style={{ marginBottom: 8 }}>
                  <Text strong>Package Type:</Text>
                  <br />
                  <Text>{workPackage.packageType || 'N/A'}</Text>
                </div>
              </Col>
              <Col span={12}>
                <div style={{ marginBottom: 8 }}>
                  <Text strong><CalendarOutlined /> Planned Start:</Text>
                  <br />
                  <Text>{workPackage.plannedStartDate ? dayjs(workPackage.plannedStartDate).format('YYYY-MM-DD') : 'N/A'}</Text>
                </div>
              </Col>
              <Col span={12}>
                <div style={{ marginBottom: 8 }}>
                  <Text strong><CalendarOutlined /> Planned End:</Text>
                  <br />
                  <Text>{workPackage.plannedEndDate ? dayjs(workPackage.plannedEndDate).format('YYYY-MM-DD') : 'N/A'}</Text>
                </div>
              </Col>
              <Col span={8}>
                <div style={{ marginBottom: 8 }}>
                  <Text strong><ClockCircleOutlined /> Estimated Hours:</Text>
                  <br />
                  <Text>{workPackage.estimatedHours || 'N/A'}</Text>
                </div>
              </Col>
              <Col span={8}>
                <div style={{ marginBottom: 8 }}>
                  <Text strong><UserOutlined /> Assigned Team:</Text>
                  <br />
                  <Text>{workPackage.assignedTeam || 'Unassigned'}</Text>
                </div>
              </Col>
              <Col span={8}>
                <div style={{ marginBottom: 8 }}>
                  <Text strong>Frequency:</Text>
                  <br />
                  <Text>{workPackage.frequency || 'Once'}</Text>
                </div>
              </Col>
            </Row>
            {workPackage.description && (
              <div style={{ marginTop: 16 }}>
                <Text strong>Description:</Text>
                <br />
                <Text>{workPackage.description}</Text>
              </div>
            )}
          </Card>

          {/* Items and Map */}
          <Card title={`Work Package Items (${workPackage.items?.length || 0})`}>
            <Tabs
              defaultActiveKey="list"
              items={[
                {
                  key: 'list',
                  label: 'List View',
                  children: (
                    <div style={{ maxHeight: 300, overflowY: 'auto' }}>
                      {workPackage.items && workPackage.items.length > 0 ? (
                        <List
                          size="small"
                          dataSource={workPackage.items}
                          renderItem={(item: WorkPackageItemDto, index: number) => (
                            <List.Item 
                              key={item.id || index}
                              style={{ padding: '8px 12px' }}
                            >
                              <div style={{ 
                                display: 'flex', 
                                alignItems: 'center', 
                                width: '100%',
                                gap: 12
                              }}>
                                {/* Sequence number */}
                                <div style={{ 
                                  width: 24, 
                                  height: 24, 
                                  borderRadius: '50%', 
                                  backgroundColor: getStatusColor(item.status) === 'green' ? '#52c41a' : '#1890ff',
                                  display: 'flex',
                                  alignItems: 'center',
                                  justifyContent: 'center',
                                  color: 'white',
                                  fontSize: '12px',
                                  fontWeight: 'bold',
                                  flexShrink: 0
                                }}>
                                  {index + 1}
                                </div>
                                
                                {/* Meter Serial Number */}
                                <div style={{ minWidth: 120, flexShrink: 0 }}>
                                  <Text strong>{item.meterSerialNumber || `Item #${index + 1}`}</Text>
                                </div>
                                
                                {/* Address */}
                                <div style={{ flex: 1, minWidth: 0 }}>
                                  <Text 
                                    type="secondary" 
                                    style={{ 
                                      overflow: 'hidden', 
                                      textOverflow: 'ellipsis', 
                                      whiteSpace: 'nowrap' 
                                    }}
                                    title={item.serviceAddress || item.propertyDetails || 'No address available'}
                                  >
                                    <EnvironmentOutlined style={{ marginRight: 4 }} />
                                    {item.serviceAddress || item.propertyDetails || 'No address available'}
                                  </Text>
                                </div>
                                
                                {/* Status */}
                                <div style={{ flexShrink: 0 }}>
                                  <Tag color={getStatusColor(item.status)} size="small">
                                    {item.status || 'Pending'}
                                  </Tag>
                                </div>
                                
                                {/* Completion indicator */}
                                {item.status === 'Completed' && (
                                  <CheckCircleOutlined 
                                    style={{ color: '#52c41a', flexShrink: 0 }} 
                                  />
                                )}
                                
                                {/* Completion date */}
                                {item.actualDate && (
                                  <div style={{ flexShrink: 0, fontSize: '12px' }}>
                                    <Text type="secondary">
                                      {dayjs(item.actualDate).format('MM-DD HH:mm')}
                                    </Text>
                                  </div>
                                )}
                              </div>
                            </List.Item>
                          )}
                        />
                      ) : (
                        <Empty description="No items in this work package" />
                      )}
                    </div>
                  )
                },
                {
                  key: 'map',
                  label: 'Map View',
                  children: (
                    <div style={{ height: 400 }}>
                      <GoogleMapView
                        markers={mapMarkers}
                        height={400}
                        center={{ lat: -43.5320, lng: 172.6306 }}
                        zoom={13}
                      />
                    </div>
                  )
                }
              ]}
            />
          </Card>

          {/* Additional Information */}
          {(workPackage.instructions || workPackage.notes) && (
            <Card title="Additional Information" style={{ marginTop: 16 }}>
              {workPackage.instructions && (
                <div style={{ marginBottom: 16 }}>
                  <Title level={5}>Instructions</Title>
                  <Text>{workPackage.instructions}</Text>
                </div>
              )}
              {workPackage.notes && (
                <div>
                  <Title level={5}>Notes</Title>
                  <Text>{workPackage.notes}</Text>
                </div>
              )}
            </Card>
          )}
        </div>
      ) : (
        <Empty description="No work package selected" />
      )}
    </Modal>
  );
}; 