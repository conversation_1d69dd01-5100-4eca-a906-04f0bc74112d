'use client';

import React, { useState, useCallback, useMemo } from 'react';
import { 
  Modal, 
  Steps, 
  Card, 
  Form, 
  Input, 
  Select, 
  DatePicker, 
  InputNumber, 
  Button, 
  Alert, 
  List,
  Tabs,
  Typography,
  message,
  Row,
  Col,
  Checkbox
} from 'antd';
import {
  FilterOutlined,
  SettingOutlined,
  CheckCircleOutlined,
  UserOutlined,
  EnvironmentOutlined
} from '@ant-design/icons';
import dayjs from 'dayjs';
import GoogleMapView, { MapMarker } from '@/components/common/GoogleMapView';
import { WaterMeterListDto } from '@/types/water-meter';
import { CreateWorkPackageDto } from '@/services/workPackage.service';
import MeterFilterForm, { MeterFilterData } from './MeterFilterForm';
import { DataManagementPaginatedTable } from '@/components/PaginatedTable';

const { Step } = Steps;
const { Option } = Select;
const { TextArea } = Input;
const { RangePicker } = DatePicker;
const { Text } = Typography;

interface CreateWorkPackageModalProps {
  visible: boolean;
  onClose: () => void;
  onCreateWorkPackage: (data: CreateWorkPackageDto) => Promise<void>;
  loading: boolean;
}

interface CreateWorkPackageData {
  filteredMeters: WaterMeterListDto[];
  selectedMeterIds: string[];
  workPackageInfo: Partial<CreateWorkPackageDto>;
  currentFilterData?: MeterFilterData | null;
  totalFilteredCount?: number;
}

export const CreateWorkPackageModal: React.FC<CreateWorkPackageModalProps> = ({
  visible,
  onClose,
  onCreateWorkPackage,
  loading
}) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [filterLoading, setFilterLoading] = useState(false);
  
  // Pagination state for list view
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(50);
  
  const [data, setData] = useState<CreateWorkPackageData>({
    filteredMeters: [],
    selectedMeterIds: [],
    workPackageInfo: {},
    currentFilterData: null,
    totalFilteredCount: 0
  });

  const [form] = Form.useForm();

  // Define columns for the table
  const columns = [
    {
      title: '#',
      key: 'index',
      width: 60,
      render: (_: any, __: any, index: number) => {
        const globalIndex = (currentPage - 1) * pageSize + index + 1;
        return (
          <div style={{
            width: 24,
            height: 24,
            borderRadius: '50%',
            backgroundColor: '#1890ff',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            color: 'white',
            fontSize: '12px',
            fontWeight: 'bold'
          }}>
            {globalIndex}
          </div>
        );
      }
    },
    {
      title: 'Serial Number',
      dataIndex: 'serialNumber',
      key: 'serialNumber',
      width: 120,
      render: (text: string) => <strong>{text}</strong>
    },
    {
      title: 'Type',
      dataIndex: 'meterType',
      key: 'meterType',
      width: 100
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      width: 80
    },
    {
      title: 'Location',
      dataIndex: 'location',
      key: 'location',
      ellipsis: true
    }
  ];

  // Handle pagination change
  const handlePageChange = async (page: number, size?: number) => {
    if (!data.currentFilterData) return;
    
    const newPageSize = size || pageSize;
    setCurrentPage(page);
    setPageSize(newPageSize);
    
    setFilterLoading(true);
    try {
      const searchParams = {
        page,
        pageSize: newPageSize,
        workPackageAssignment: data.currentFilterData.workPackageAssignment,
        serviceArea: data.currentFilterData.serviceArea,
        subArea: data.currentFilterData.subArea,
        meterType: data.currentFilterData.meterType,
        status: data.currentFilterData.status,
        addressContains: data.currentFilterData.addressContains,
        installDateFrom: data.currentFilterData.installDateRange?.[0]?.toDate(),
        installDateTo: data.currentFilterData.installDateRange?.[1]?.toDate(),
        latitudeMin: data.currentFilterData.gpsRange?.latMin,
        latitudeMax: data.currentFilterData.gpsRange?.latMax,
        longitudeMin: data.currentFilterData.gpsRange?.lngMin,
        longitudeMax: data.currentFilterData.gpsRange?.lngMax
      };

      const { waterMeterService } = await import('@/services/water-meter.service');
      const response = await waterMeterService.getWaterMeters(searchParams);
      const waterMeters = response.data || response.meters || [];
      
      setData(prev => ({
        ...prev,
        filteredMeters: waterMeters
        // Don't auto-select meters on pagination
      }));
    } catch (error) {
      console.error('🚨 Pagination error:', error);
      message.error('Failed to load page data');
    } finally {
      setFilterLoading(false);
    }
  };

  // Handle meter filtering - only get first page data
  const handleMeterFilter = async (filterData: MeterFilterData) => {
    setFilterLoading(true);
    setCurrentPage(1); // Reset to first page
    
    try {
      // Call API to get first page and total count
      const searchParams = {
        page: 1,
        pageSize: 50,
        workPackageAssignment: filterData.workPackageAssignment,
        serviceArea: filterData.serviceArea,
        subArea: filterData.subArea,
        meterType: filterData.meterType,
        status: filterData.status,
        addressContains: filterData.addressContains,
        installDateFrom: filterData.installDateRange?.[0]?.toDate(),
        installDateTo: filterData.installDateRange?.[1]?.toDate(),
        latitudeMin: filterData.gpsRange?.latMin,
        latitudeMax: filterData.gpsRange?.latMax,
        longitudeMin: filterData.gpsRange?.lngMin,
        longitudeMax: filterData.gpsRange?.lngMax
      };

      console.log('🔍 [Direct Create] Calling water meter API with params:', searchParams);
      
      const { waterMeterService } = await import('@/services/water-meter.service');
      const response = await waterMeterService.getWaterMeters(searchParams);
      console.log('✅ [Direct Create] API Response:', response);
      
      if (!response || (!response.data && !response.meters)) {
        throw new Error('Invalid API response structure');
      }

      const waterMeters = response.data || response.meters || [];
      const totalCount = response.totalCount || waterMeters.length;
      
      // Store first page data, filter conditions and total count
      setData(prev => ({
        ...prev,
        filteredMeters: waterMeters,
        selectedMeterIds: [], // Reset selection when filtering
        currentFilterData: filterData,
        totalFilteredCount: totalCount
      }));

      message.success(`Found ${totalCount} water meters matching your criteria`);
    } catch (error) {
      console.error('🚨 [Direct Create] Filter error:', error);
      message.error(`Failed to filter meters: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setFilterLoading(false);
    }
  };



  // Convert water meters to map markers (using real coordinates like Task Assignment)
  const convertMetersToMarkers = useCallback((meters: WaterMeterListDto[]): MapMarker[] => {
    return meters
      .filter(meter => {
        // Only include meters that have GPS coordinates
        return meter.latitude != null && meter.longitude != null;
      })
      .map((meter) => {
        return {
          id: meter.serialNumber,
          position: {
            lat: meter.latitude!,
            lng: meter.longitude!
          },
          title: meter.serialNumber,
          info: {
            description: meter.location || 'No location specified',
            status: meter.status,
            type: meter.meterType || 'Unknown',
            location: meter.location || 'No location specified',
            meterSerial: meter.serialNumber
          },
          selected: data.selectedMeterIds.includes(meter.serialNumber)
        };
      });
  }, [data.selectedMeterIds]);

  const mapMarkers = useMemo(() => {
    return convertMetersToMarkers(data.filteredMeters);
  }, [data.filteredMeters, convertMetersToMarkers]);

  // Calculate map center based on markers (like Task Assignment)
  const mapCenter = useMemo(() => {
    if (mapMarkers.length === 0) {
      return { lat: -43.5320, lng: 172.6306 }; // Default to Christchurch
    }

    // Calculate center from all markers
    const totalLat = mapMarkers.reduce((sum, marker) => sum + marker.position.lat, 0);
    const totalLng = mapMarkers.reduce((sum, marker) => sum + marker.position.lng, 0);

    return {
      lat: totalLat / mapMarkers.length,
      lng: totalLng / mapMarkers.length
    };
  }, [mapMarkers]);

  const handleMapMarkerClick = useCallback((marker: MapMarker) => {
    const meterId = marker.id;
    const newSelectedIds = data.selectedMeterIds.includes(meterId)
      ? data.selectedMeterIds.filter(id => id !== meterId)
      : [...data.selectedMeterIds, meterId];

    setData(prev => ({
      ...prev,
      selectedMeterIds: newSelectedIds
    }));
  }, [data.selectedMeterIds]);

  // Handle select all/none for paginated data
  const handleSelectAll = async () => {
    if (!data.currentFilterData) {
      message.warning('Please filter meters first');
      return;
    }

    // For online creation mode, we'll select all filtered meters
    // This requires getting all meter IDs from all pages
    setFilterLoading(true);
    try {
      const allMeterIds: string[] = [];
      const totalPages = Math.ceil((data.totalFilteredCount || 0) / 50);
      
      for (let page = 1; page <= totalPages; page++) {
        const searchParams = {
          page,
          pageSize: 50,
          workPackageAssignment: data.currentFilterData.workPackageAssignment,
          serviceArea: data.currentFilterData.serviceArea,
          subArea: data.currentFilterData.subArea,
          meterType: data.currentFilterData.meterType,
          status: data.currentFilterData.status,
          addressContains: data.currentFilterData.addressContains,
          installDateFrom: data.currentFilterData.installDateRange?.[0]?.toDate(),
          installDateTo: data.currentFilterData.installDateRange?.[1]?.toDate(),
          latitudeMin: data.currentFilterData.gpsRange?.latMin,
          latitudeMax: data.currentFilterData.gpsRange?.latMax,
          longitudeMin: data.currentFilterData.gpsRange?.lngMin,
          longitudeMax: data.currentFilterData.gpsRange?.lngMax
        };

        const { waterMeterService } = await import('@/services/water-meter.service');
        const response = await waterMeterService.getWaterMeters(searchParams);
        const waterMeters = response.data || [];
        const pageMetersIds = waterMeters.map((m: WaterMeterListDto) => m.serialNumber).filter(id => id);
        allMeterIds.push(...pageMetersIds);
      }

      setData(prev => ({
        ...prev,
        selectedMeterIds: allMeterIds
      }));

      message.success(`Selected all ${allMeterIds.length} filtered meters`);
    } catch (error) {
      console.error('🚨 Select all error:', error);
      message.error('Failed to select all meters');
    } finally {
      setFilterLoading(false);
    }
  };

  const handleSelectNone = () => {
    setData(prev => ({
      ...prev,
      selectedMeterIds: []
    }));
  };

  const handleSelectCurrentPage = () => {
    setData(prev => ({
      ...prev,
      selectedMeterIds: [...new Set([...prev.selectedMeterIds, ...prev.filteredMeters.map(m => m.serialNumber)])]
    }));
  };

  const handleNext = () => {
    if (currentStep === 0 && data.filteredMeters.length === 0) {
      message.warning('Please filter and select water meters first');
      return;
    }
    if (currentStep === 0 && data.selectedMeterIds.length === 0) {
      message.warning('Please select at least one water meter');
      return;
    }
    if (currentStep === 0 && !data.workPackageInfo.name) {
      message.warning('Please enter work package name');
      return;
    }
    
    // For online creation, we can create directly after first step
    if (currentStep === 0) {
      handleCreateWorkPackage();
    } else {
      setCurrentStep(prev => prev + 1);
    }
  };

  // Handle work package creation
  const handleCreateWorkPackage = async () => {
    try {
      if (data.selectedMeterIds.length === 0) {
        message.error('Please select at least one water meter');
        return;
      }

      if (!data.workPackageInfo.name) {
        message.error('Please enter work package name');
        return;
      }

      // Get selected meter IDs
      const selectedMeters = data.filteredMeters.filter(m => 
        data.selectedMeterIds.includes(m.serialNumber)
      );

      const createDto: CreateWorkPackageDto = {
        name: data.workPackageInfo.name,
        description: data.workPackageInfo.description || `Work package created with ${selectedMeters.length} meters`,
        packageType: 'Scheduled',
        plannedStartDate: new Date().toISOString(),
        plannedEndDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days from now
        frequency: 'Once',
        serviceArea: selectedMeters[0]?.township || 'General',
        subArea: '',
        priority: data.workPackageInfo.priority || 'Medium',
        assignedTeam: '',
        estimatedHours: selectedMeters.length * 0.5,
        estimatedCost: 0,
        isTemplate: false,
        templateCategory: '',
        isRecurring: false,
        recurrencePattern: '',
        recurrenceInterval: 0,
        notes: '',
        instructions: '',
        createdBy: 'current-user', // TODO: Get from auth context
        meterIds: selectedMeters.map(m => m.id)
      };

      await onCreateWorkPackage(createDto);
      
      message.success('Work package created successfully!');
      handleClose();
    } catch (error) {
      console.error('Create work package error:', error);
      message.error('Failed to create work package');
    }
  };

  const handleClose = () => {
    setCurrentStep(0);
    setCurrentPage(1); // Reset pagination
    setData({
      filteredMeters: [],
      selectedMeterIds: [],
      workPackageInfo: {},
      currentFilterData: null,
      totalFilteredCount: 0
    });
    form.resetFields();
    onClose();
  };

  const handlePrev = () => {
    setCurrentStep(prev => prev - 1);
  };

  return (
    <Modal
      title={
        <div>
          <UserOutlined style={{ marginRight: 8 }} />
          Create Work Package - Direct
        </div>
      }
      open={visible}
      onCancel={handleClose}
      footer={null}
      width={1200}
      style={{ top: 20 }}
    >
      <Steps current={currentStep} style={{ marginBottom: 24 }}>
        <Step title="Filter & Configure" description="Filter meters and configure work package" />
        <Step title="Review & Create" description="Review and create work package" />
      </Steps>

      {/* Step 1: Filter and Select Meters */}
      {currentStep === 0 && (
        <div>
          <Alert
            message="Step 1: Filter and Select Water Meters"
            description="Filter water meters and select which ones to include in your work package."
            type="info"
            showIcon
            style={{ marginBottom: 24 }}
          />

          <MeterFilterForm
            onFilter={handleMeterFilter}
            loading={filterLoading}
          />

          {/* Work Package Name Input - For Online Creation */}
          <Card title="Work Package Information" style={{ marginTop: 24, marginBottom: 24 }}>
            <Form layout="vertical">
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item label="Work Package Name" required>
                    <Input 
                      placeholder="Enter work package name"
                      value={data.workPackageInfo.name}
                      onChange={(e) => setData(prev => ({
                        ...prev,
                        workPackageInfo: { ...prev.workPackageInfo, name: e.target.value }
                      }))}
                    />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item label="Priority">
                    <Select 
                      value={data.workPackageInfo.priority || 'Medium'}
                      onChange={(value) => setData(prev => ({
                        ...prev,
                        workPackageInfo: { ...prev.workPackageInfo, priority: value }
                      }))}
                    >
                      <Option value="Low">Low</Option>
                      <Option value="Medium">Medium</Option>
                      <Option value="High">High</Option>
                      <Option value="Critical">Critical</Option>
                    </Select>
                  </Form.Item>
                </Col>
              </Row>
              <Row gutter={16}>
                <Col span={24}>
                  <Form.Item label="Description">
                    <Input.TextArea 
                      placeholder="Enter work package description"
                      rows={2}
                      value={data.workPackageInfo.description}
                      onChange={(e) => setData(prev => ({
                        ...prev,
                        workPackageInfo: { ...prev.workPackageInfo, description: e.target.value }
                      }))}
                    />
                  </Form.Item>
                </Col>
              </Row>
            </Form>
          </Card>

          {/* Results Display */}
          {data.filteredMeters.length > 0 && (
            <Card 
              title={`Filtered Results (${data.totalFilteredCount || data.filteredMeters.length} meters total, page ${currentPage})`} 
              style={{ marginTop: 24 }}
              extra={
                <div>
                  <Button size="small" onClick={handleSelectCurrentPage} style={{ marginRight: 8 }}>
                    Select Current Page
                  </Button>
                  <Button size="small" onClick={handleSelectAll} style={{ marginRight: 8 }} loading={filterLoading}>
                    Select All Filtered
                  </Button>
                  <Button size="small" onClick={handleSelectNone}>
                    Select None
                  </Button>
                </div>
              }
            >
              <Tabs
                defaultActiveKey="list"
                items={[
                  {
                    key: 'list',
                    label: 'List View',
                    children: (
                      <div>
                        <DataManagementPaginatedTable
                          data={data.filteredMeters}
                          total={data.totalFilteredCount}
                          currentPage={currentPage}
                          currentPageSize={pageSize}
                          loading={filterLoading}
                          onPageChange={handlePageChange}
                          columns={columns}
                          rowKey="serialNumber"
                          size="small"
                          scroll={{ y: 300 }}
                          rowSelection={{
                            selectedRowKeys: data.selectedMeterIds,
                            onChange: (selectedRowKeys) => {
                              setData(prev => ({
                                ...prev,
                                selectedMeterIds: selectedRowKeys as string[]
                              }));
                            },
                            getCheckboxProps: (record: WaterMeterListDto) => ({
                              name: record.serialNumber,
                            }),
                          }}
                        />
                      </div>
                    )
                  },
                  {
                    key: 'map',
                    label: 'Map View',
                    children: (
                      <div style={{ height: 300 }}>
                        {mapMarkers.length === 0 ? (
                          <Alert
                            message="No meter locations available"
                            description="Water meters need GPS coordinates to be displayed on the map. The filtered meters may not have location data."
                            type="info"
                            showIcon
                            style={{ margin: '20px' }}
                          />
                        ) : (
                          <GoogleMapView
                            markers={mapMarkers}
                            onMarkerClick={handleMapMarkerClick}
                            height={300}
                            center={mapCenter}
                            zoom={mapMarkers.length > 0 ? 13 : 12}
                            showInfoWindowOnly={true}
                          />
                        )}
                      </div>
                    )
                  }
                ]}
              />

              <div style={{ marginTop: 16, textAlign: 'center' }}>
                <Text strong>
                  Selected: {data.selectedMeterIds.length} / {data.totalFilteredCount || data.filteredMeters.length} meters
                </Text>
              </div>
            </Card>
          )}

          <div style={{ textAlign: 'center', marginTop: 24 }}>
            <Button
              type="primary"
              size="large"
              onClick={handleNext}
              disabled={data.selectedMeterIds.length === 0 || !data.workPackageInfo.name}
              loading={loading}
              icon={<CheckCircleOutlined />}
            >
              Create Work Package
            </Button>
          </div>
        </div>
      )}

      {/* Step 2: Review (Optional - can be removed if direct creation is preferred) */}
      {currentStep === 1 && (
        <div>
          <Alert
            message="Step 2: Review and Create Work Package"
            description="Review your work package configuration and create it."
            type="info"
            showIcon
            style={{ marginBottom: 24 }}
          />

          <Row gutter={16}>
            <Col span={12}>
              <Card title="Work Package Information" size="small">
                <div style={{ space: '8px 0' }}>
                  <div><Text strong>Name:</Text> {data.workPackageInfo.name}</div>
                  <div><Text strong>Priority:</Text> {data.workPackageInfo.priority || 'Medium'}</div>
                  <div><Text strong>Description:</Text> {data.workPackageInfo.description || 'N/A'}</div>
                  <div><Text strong>Estimated Hours:</Text> {data.selectedMeterIds.length * 0.5}</div>
                </div>
              </Card>
            </Col>
            <Col span={12}>
              <Card title="Selected Meters" size="small">
                <div style={{ maxHeight: 200, overflowY: 'auto' }}>
                  <List
                    size="small"
                    dataSource={data.filteredMeters.filter(m => data.selectedMeterIds.includes(m.serialNumber))}
                    renderItem={(meter) => (
                      <List.Item style={{ padding: '4px 8px' }}>
                        <div style={{ display: 'flex', alignItems: 'center', width: '100%', gap: 12 }}>
                          <Text strong style={{ minWidth: 100 }}>{meter.serialNumber}</Text>
                          <Text style={{ flex: 1, overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' }}>
                            {meter.location}
                          </Text>
                        </div>
                      </List.Item>
                    )}
                  />
                </div>
                <div style={{ marginTop: 8, textAlign: 'center' }}>
                  <Text strong>Total: {data.selectedMeterIds.length} meters</Text>
                </div>
              </Card>
            </Col>
          </Row>

          <div style={{ textAlign: 'center', marginTop: 24 }}>
            <Button onClick={handlePrev} style={{ marginRight: 16 }}>
              Previous
            </Button>
            <Button 
              type="primary" 
              size="large" 
              icon={<CheckCircleOutlined />}
              onClick={handleCreateWorkPackage}
              loading={loading}
            >
              Create Work Package
            </Button>
          </div>
        </div>
      )}
    </Modal>
  );
}; 