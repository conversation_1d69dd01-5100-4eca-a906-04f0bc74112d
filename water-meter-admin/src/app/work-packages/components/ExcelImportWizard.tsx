'use client';

import React, { useCallback, useMemo, useState } from 'react';
import { Modal, Steps, Card, Form, Button, Alert, Upload, List, Empty, Tabs, Typography, message } from 'antd';
import {
  FilterOutlined,
  FileExcelOutlined,
  UploadOutlined,
  CheckCircleOutlined,
  EnvironmentOutlined
} from '@ant-design/icons';
import dayjs from 'dayjs';
import GoogleMapView, { MapMarker } from '@/components/common/GoogleMapView';
import { WaterMeterListDto } from '@/types/water-meter';
import MeterFilterForm, { MeterFilterData } from './MeterFilterForm';
import { DataManagementPaginatedTable } from '@/components/PaginatedTable';


const { Step } = Steps;
const { Text } = Typography;

interface ExcelImportWizardData {
  filteredMeters: WaterMeterListDto[];
  selectedMeterIds: string[];
  uploadedFile: File | null;
  parsedWorkPackages: any[];
  validationResults: any[];
  importResult?: {
    totalRows: number;
    successCount: number;
    failureCount: number;
    errors: string[];
  } | null;
}

interface ExcelImportWizardProps {
  visible: boolean;
  onClose: () => void;
  currentStep: number;
  onStepChange: (step: number) => void;
  data: ExcelImportWizardData;
  onDataChange: (data: ExcelImportWizardData) => void;
  loading: boolean;
  onMeterFilter: (filterData: MeterFilterData) => Promise<void>;
  onExportTemplate: () => Promise<void>;
  onFileUpload: (file: File) => Promise<void>;
  onCreateWorkPackages: () => Promise<void>;
  onPageChange: (page: number, pageSize: number) => Promise<void>;
  totalFilteredCount: number;
  currentPage: number;
  currentPageSize: number;
}

export const ExcelImportWizard: React.FC<ExcelImportWizardProps> = ({
  visible,
  onClose,
  currentStep,
  onStepChange,
  data,
  onDataChange,
  loading,
  onMeterFilter,
  onExportTemplate,
  onFileUpload,
  onCreateWorkPackages,
  onPageChange,
  totalFilteredCount,
  currentPage: propCurrentPage,
  currentPageSize: propCurrentPageSize
}) => {
  // Use props for pagination state
  const currentPage = propCurrentPage;
  const currentPageSize = propCurrentPageSize;

  // Define columns for the table
  const columns = [
    {
      title: '#',
      key: 'index',
      width: 60,
      render: (_: any, __: any, index: number) => {
        const globalIndex = (currentPage - 1) * currentPageSize + index + 1;
        return (
          <div style={{
            width: 24,
            height: 24,
            borderRadius: '50%',
            backgroundColor: '#1890ff',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            color: 'white',
            fontSize: '12px',
            fontWeight: 'bold'
          }}>
            {globalIndex}
          </div>
        );
      }
    },
    {
      title: 'Serial Number',
      dataIndex: 'serialNumber',
      key: 'serialNumber',
      width: 120,
      render: (text: string) => <strong>{text}</strong>
    },
    {
      title: 'Type',
      dataIndex: 'meterType',
      key: 'meterType',
      width: 100
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      width: 80
    },
    {
      title: 'Location',
      dataIndex: 'location',
      key: 'location',
      ellipsis: true
    }
  ];

  // Convert water meters to map markers (using real coordinates like Task Assignment)
  const convertMetersToMarkers = useCallback((meters: WaterMeterListDto[]): MapMarker[] => {
    return meters
      .filter(meter => {
        // Only include meters that have GPS coordinates
        return meter.latitude != null && meter.longitude != null;
      })
      .map((meter) => {
        return {
          id: meter.serialNumber,
          position: {
            lat: meter.latitude!,
            lng: meter.longitude!
          },
          title: meter.serialNumber,
          info: {
            description: meter.location || 'No location specified',
            status: meter.status,
            type: meter.meterType || 'Unknown',
            location: meter.location || 'No location specified',
            meterSerial: meter.serialNumber
          },
          selected: data.selectedMeterIds.includes(meter.serialNumber)
        };
      });
  }, [data.selectedMeterIds]);

  const mapMarkers = useMemo(() => {
    return convertMetersToMarkers(data.filteredMeters);
  }, [data.filteredMeters, convertMetersToMarkers]);

  // Calculate map center based on markers (like Task Assignment)
  const mapCenter = useMemo(() => {
    if (mapMarkers.length === 0) {
      return { lat: -43.5320, lng: 172.6306 }; // Default to Christchurch
    }

    // Calculate center from all markers
    const totalLat = mapMarkers.reduce((sum, marker) => sum + marker.position.lat, 0);
    const totalLng = mapMarkers.reduce((sum, marker) => sum + marker.position.lng, 0);

    return {
      lat: totalLat / mapMarkers.length,
      lng: totalLng / mapMarkers.length
    };
  }, [mapMarkers]);

  const handleMapMarkerClick = useCallback((marker: MapMarker) => {
    const meterId = marker.id;
    const newSelectedIds = data.selectedMeterIds.includes(meterId)
      ? data.selectedMeterIds.filter(id => id !== meterId)
      : [...data.selectedMeterIds, meterId];

    onDataChange({
      ...data,
      selectedMeterIds: newSelectedIds
    });
  }, [data, onDataChange]);

  // Handle pagination change - call backend API instead of frontend slicing
  const handlePageChange = async (page: number, size?: number) => {
    const newPageSize = size || currentPageSize;

    // Call parent's onPageChange to fetch data from backend
    await onPageChange(page, newPageSize);
  };

  // Remove getCurrentPageData since we now use backend pagination
  // The data.filteredMeters already contains the current page data

  const handleClose = () => {
    onStepChange(0);
    // Reset pagination is now handled by parent component
    onDataChange({
      filteredMeters: [],
      selectedMeterIds: [],
      uploadedFile: null,
      parsedWorkPackages: [],
      validationResults: [],
      importResult: null
    });
    onClose();
  };

  return (
    <Modal
      title={
        <div>
          <FileExcelOutlined style={{ marginRight: 8 }} />
          Excel Import Wizard
        </div>
      }
      open={visible}
      onCancel={handleClose}
      footer={null}
      width={1200}
      style={{ top: 20 }}
    >
      <Steps current={currentStep} style={{ marginBottom: 24 }}>
        <Step title="Filter & Export" description="Select meters and export template" />
        <Step title="Upload Excel" description="Upload completed Excel file" />
        <Step title="Review Results" description="Review created work packages" />
      </Steps>

      {/* Step 1: Filter and Export Template */}
      {currentStep === 0 && (
        <div>
          <Alert
            message="Step 1: Filter Meters and Export Template"
            description="First, filter the water meters you want to include in your work packages, then export the Excel template."
            type="info"
            showIcon
            style={{ marginBottom: 24 }}
          />

          <MeterFilterForm
            onFilter={onMeterFilter}
            loading={loading}
          />

          {/* Results Display */}
          {data.filteredMeters.length > 0 && (
            <Card title={`Filtered Results (${data.filteredMeters.length} meters)`} style={{ marginTop: 24 }}>
              <Tabs
                defaultActiveKey="list"
                items={[
                  {
                    key: 'list',
                    label: 'List View',
                    children: (
                      <div>
                        <DataManagementPaginatedTable
                          data={data.filteredMeters}
                          total={totalFilteredCount}
                          currentPage={currentPage}
                          currentPageSize={currentPageSize}
                          loading={loading}
                          onPageChange={handlePageChange}
                          columns={columns}
                          rowKey="serialNumber"
                          size="small"
                          scroll={{ y: 400 }}
                        />

                        
                        {/* Export info */}
                        <div style={{ textAlign: 'center', padding: 16, backgroundColor: '#f5f5f5', marginTop: 16, borderRadius: 6 }}>
                          <Text type="secondary">
                            <strong>Export will include all {totalFilteredCount} filtered meters</strong>
                            {totalFilteredCount > currentPageSize && ` (not just the ${currentPageSize} shown on current page)`}
                          </Text>
                        </div>
                      </div>
                    )
                  },
                  {
                    key: 'map',
                    label: 'Map View',
                    children: (
                      <div style={{ height: 300 }}>
                        {mapMarkers.length === 0 ? (
                          <Alert
                            message="No meter locations available"
                            description="Water meters need GPS coordinates to be displayed on the map. The filtered meters may not have location data."
                            type="info"
                            showIcon
                            style={{ margin: '20px' }}
                          />
                        ) : (
                          <GoogleMapView
                            markers={mapMarkers}
                            onMarkerClick={handleMapMarkerClick}
                            height={300}
                            center={mapCenter}
                            zoom={mapMarkers.length > 0 ? 13 : 12}
                            showInfoWindowOnly={true}
                          />
                        )}
                      </div>
                    )
                  }
                ]}
              />
            </Card>
          )}

          {/* Export Actions */}
          <div style={{ textAlign: 'center', marginTop: 24 }}>
            <Button
              type="primary"
              size="large"
              icon={<FileExcelOutlined />}
              onClick={onExportTemplate}
              disabled={data.filteredMeters.length === 0}
              loading={loading}
            >
              Export Excel Template ({totalFilteredCount} meters)
            </Button>
          </div>
        </div>
      )}

      {/* Step 2: Upload Excel File */}
      {currentStep === 1 && (
        <div>
          <Alert
            message="Step 2: Upload Edited Excel File"
            description="Upload the Excel file you edited with work package names. The system will parse it and create work packages automatically."
            type="info"
            showIcon
            style={{ marginBottom: 24 }}
          />

          <div style={{ textAlign: 'center', padding: '40px 0' }}>
            <Upload.Dragger
              name="file"
              multiple={false}
              accept=".xlsx,.xls"
              showUploadList={false}
              beforeUpload={(file) => {
                onFileUpload(file);
                return false;
              }}
            >
              <p className="ant-upload-drag-icon">
                <UploadOutlined style={{ fontSize: '48px' }} />
              </p>
              <p className="ant-upload-text">Click or drag Excel file to this area to upload</p>
              <p className="ant-upload-hint">
                Upload the Excel file with work package names filled in
              </p>
            </Upload.Dragger>
          </div>

          <div style={{ textAlign: 'center' }}>
            <Button
              onClick={() => onStepChange(0)}
              icon={<FileExcelOutlined />}
            >
              Back to Download Template
            </Button>
          </div>
        </div>
      )}

      {/* Step 3: Review and Create */}
      {currentStep === 2 && (
        <div>
          <Alert
            message="Excel Import Completed!"
            description={
              data.importResult 
                ? `Processed ${data.importResult.totalRows} rows. Successfully created ${data.importResult.successCount} work packages.${data.importResult.failureCount > 0 ? ` ${data.importResult.failureCount} failed.` : ''}`
                : `Import completed successfully.`
            }
            type={data.importResult && data.importResult.failureCount > 0 ? "warning" : "success"}
            showIcon
            style={{ marginBottom: 24 }}
          />

          <Card title="Import Summary">
            {data.importResult ? (
              <div>
                <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: 16, marginBottom: 16 }}>
                  <div style={{ textAlign: 'center', padding: 16, backgroundColor: '#f6ffed', borderRadius: 8 }}>
                    <div style={{ fontSize: 24, fontWeight: 'bold', color: '#52c41a' }}>
                      {data.importResult.successCount}
                    </div>
                    <div style={{ color: '#666' }}>Work Packages Created</div>
                  </div>
                  
                  <div style={{ textAlign: 'center', padding: 16, backgroundColor: '#f0f0f0', borderRadius: 8 }}>
                    <div style={{ fontSize: 24, fontWeight: 'bold', color: '#666' }}>
                      {data.importResult.totalRows}
                    </div>
                    <div style={{ color: '#666' }}>Total Rows Processed</div>
                  </div>
                  
                  {data.importResult.failureCount > 0 && (
                    <div style={{ textAlign: 'center', padding: 16, backgroundColor: '#fff2e8', borderRadius: 8 }}>
                      <div style={{ fontSize: 24, fontWeight: 'bold', color: '#fa8c16' }}>
                        {data.importResult.failureCount}
                      </div>
                      <div style={{ color: '#666' }}>Failed Rows</div>
                    </div>
                  )}
                </div>

                {/* Show detailed errors if any */}
                {data.importResult.errors && data.importResult.errors.length > 0 && (
                  <div style={{ marginTop: 16 }}>
                    <Alert
                      message="Import Errors"
                      description={
                        <div>
                          <p>The following issues occurred during import:</p>
                          <ul style={{ marginTop: 8, paddingLeft: 20 }}>
                            {data.importResult.errors.slice(0, 10).map((error: string, index: number) => (
                              <li key={index} style={{ marginBottom: 4 }}>{error}</li>
                            ))}
                            {data.importResult.errors.length > 10 && (
                              <li style={{ fontStyle: 'italic', color: '#666' }}>
                                ... and {data.importResult.errors.length - 10} more issues
                              </li>
                            )}
                          </ul>
                        </div>
                      }
                      type="warning"
                      showIcon
                    />
                  </div>
                )}
              </div>
            ) : (
              <Empty description="No import result available" />
            )}
          </Card>

          <div style={{ textAlign: 'center', marginTop: 24 }}>
            <Button type="primary" onClick={handleClose} size="large">
              Complete Import
            </Button>
          </div>
        </div>
      )}
    </Modal>
  );
}; 