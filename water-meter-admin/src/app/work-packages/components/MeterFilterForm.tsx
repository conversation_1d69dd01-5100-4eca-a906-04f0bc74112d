'use client';

import React, { useState } from 'react';
import { Form, Input, DatePicker, Row, Col, Button, Card, Select } from 'antd';
import { SearchOutlined, ReloadOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';
import { DynamicSelect } from '@/components/DynamicSelect';

const { RangePicker } = DatePicker;
const { Option } = Select;

// Stable empty object to prevent unnecessary re-renders
const EMPTY_PARAMS = {};

export interface MeterFilterData {
  workPackageAssignment?: string; // 'unassigned' | 'assigned' | 'all'
  serviceArea?: string;
  subArea?: string;
  meterType?: string;
  status?: string;
  addressContains?: string;
  installDateRange?: [dayjs.Dayjs, dayjs.Dayjs] | null;
  gpsRange?: {
    latMin?: number;
    latMax?: number;
    lngMin?: number;
    lngMax?: number;
  };
}

interface MeterFilterFormProps {
  onFilter: (filterData: MeterFilterData) => Promise<void>;
  loading: boolean;
}

const MeterFilterForm: React.FC<MeterFilterFormProps> = ({ onFilter, loading }) => {
  const [form] = Form.useForm();
  const [selectedServiceArea, setSelectedServiceArea] = useState<string>('');

  const handleSubmit = async (values: any) => {
    const filterData: MeterFilterData = {
      workPackageAssignment: values.workPackageAssignment,
      serviceArea: values.serviceArea,
      subArea: values.subArea,
      meterType: values.meterType,
      status: values.status,
      addressContains: values.addressContains,
      installDateRange: values.installDateRange ? [
        dayjs(values.installDateRange[0]),
        dayjs(values.installDateRange[1])
      ] : null,
      gpsRange: values.gpsLatMin || values.gpsLatMax || values.gpsLngMin || values.gpsLngMax ? {
        latMin: values.gpsLatMin,
        latMax: values.gpsLatMax,
        lngMin: values.gpsLngMin,
        lngMax: values.gpsLngMax
      } : undefined
    };

    await onFilter(filterData);
  };

  const handleReset = () => {
    form.resetFields();
    onFilter({});
  };

  return (
    <Card title="Filter Water Meters" style={{ marginBottom: 24 }}>
      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
      >
        <Row gutter={16}>
          <Col span={6}>
            <Form.Item
              label="Work Package Assignment"
              name="workPackageAssignment"
              initialValue="unassigned"
            >
              <Select placeholder="Select assignment status" allowClear>
                <Option value="unassigned">🔍 Unassigned to Work Package</Option>
                <Option value="assigned">📦 Assigned to Work Package</Option>
                <Option value="all">📋 All Meters</Option>
              </Select>
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item label="Service Area" name="serviceArea">
              <DynamicSelect
                strategy="database"
                dataSource="workpackage.servicarea"
                params={EMPTY_PARAMS}
                placeholder="Select service area"
                allowClear
                onChange={(value) => {
                  setSelectedServiceArea(value || '');
                  // Clear sub area when service area changes
                  form.setFieldValue('subArea', undefined);
                }}
              />
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item label="Sub Area" name="subArea">
              <DynamicSelect
                strategy="database"
                dataSource="watermeter.subarea"
                params={EMPTY_PARAMS}
                placeholder="Select sub area"
                allowClear
              />
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item label="Meter Type" name="meterType">
              <DynamicSelect
                strategy="enum"
                dataSource="metertype"
                params={EMPTY_PARAMS}
                placeholder="Select meter type"
                allowClear
              />
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item label="Status" name="status">
              <DynamicSelect
                strategy="enum"
                dataSource="meterstatus"
                params={EMPTY_PARAMS}
                placeholder="Select status"
                allowClear
              />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item label="Address Contains" name="addressContains">
              <Input placeholder="Enter address keywords" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="Install Date Range" name="installDateRange">
              <RangePicker style={{ width: '100%' }} />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={6}>
            <Form.Item label="Min Latitude" name="gpsLatMin">
              <Input type="number" placeholder="-43.60" step="0.01" />
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item label="Max Latitude" name="gpsLatMax">
              <Input type="number" placeholder="-43.40" step="0.01" />
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item label="Min Longitude" name="gpsLngMin">
              <Input type="number" placeholder="172.50" step="0.01" />
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item label="Max Longitude" name="gpsLngMax">
              <Input type="number" placeholder="172.80" step="0.01" />
            </Form.Item>
          </Col>
        </Row>

        <div style={{ textAlign: 'right' }}>
          <Button onClick={handleReset} style={{ marginRight: 8 }}>
            <ReloadOutlined /> Reset
          </Button>
          <Button type="primary" htmlType="submit" loading={loading}>
            <SearchOutlined /> Filter Meters
          </Button>
        </div>
      </Form>
    </Card>
  );
};

export default MeterFilterForm; 