'use client';

import React from 'react';
import { Card, Row, Col, Statistic } from 'antd';
import {
  BarChartOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  ExclamationCircleOutlined,
  TeamOutlined,
  WarningOutlined
} from '@ant-design/icons';

interface WorkPackageStatistics {
  totalWorkPackages: number;
  activeWorkPackages: number;
  completedWorkPackages: number;
  pendingWorkPackages: number;
  totalMeters: number;
  completedMeters: number;
  overdueWorkPackages: number;
  avgCompletionTime: number;
}

interface StatisticsCardsProps {
  statistics: WorkPackageStatistics | null;
  loading: boolean;
}

export const StatisticsCards: React.FC<StatisticsCardsProps> = ({ statistics, loading }) => {
  return (
    <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
      <Col xs={24} sm={12} md={6}>
        <Card>
          <Statistic
            title="Total Work Packages"
            value={statistics?.totalWorkPackages || 0}
            loading={loading}
            prefix={<BarChartOutlined style={{ color: '#1890ff' }} />}
          />
        </Card>
      </Col>
      <Col xs={24} sm={12} md={6}>
        <Card>
          <Statistic
            title="Active Packages"
            value={statistics?.activeWorkPackages || 0}
            loading={loading}
            prefix={<ClockCircleOutlined style={{ color: '#52c41a' }} />}
          />
        </Card>
      </Col>
      <Col xs={24} sm={12} md={6}>
        <Card>
          <Statistic
            title="Completed"
            value={statistics?.completedWorkPackages || 0}
            loading={loading}
            prefix={<CheckCircleOutlined style={{ color: '#52c41a' }} />}
          />
        </Card>
      </Col>
      <Col xs={24} sm={12} md={6}>
        <Card>
          <Statistic
            title="Overdue"
            value={statistics?.overdueWorkPackages || 0}
            loading={loading}
            prefix={<WarningOutlined style={{ color: '#ff4d4f' }} />}
          />
        </Card>
      </Col>
      <Col xs={24} sm={12} md={6}>
        <Card>
          <Statistic
            title="Total Meters"
            value={statistics?.totalMeters || 0}
            loading={loading}
            prefix={<TeamOutlined style={{ color: '#722ed1' }} />}
          />
        </Card>
      </Col>
      <Col xs={24} sm={12} md={6}>
        <Card>
          <Statistic
            title="Completed Meters"
            value={statistics?.completedMeters || 0}
            loading={loading}
            prefix={<CheckCircleOutlined style={{ color: '#52c41a' }} />}
          />
        </Card>
      </Col>
      <Col xs={24} sm={12} md={6}>
        <Card>
          <Statistic
            title="Pending"
            value={statistics?.pendingWorkPackages || 0}
            loading={loading}
            prefix={<ExclamationCircleOutlined style={{ color: '#fa8c16' }} />}
          />
        </Card>
      </Col>
      <Col xs={24} sm={12} md={6}>
        <Card>
          <Statistic
            title="Avg Completion (hrs)"
            value={statistics?.avgCompletionTime || 0}
            precision={1}
            loading={loading}
            prefix={<ClockCircleOutlined style={{ color: '#1890ff' }} />}
          />
        </Card>
      </Col>
    </Row>
  );
}; 