'use client';

import React from 'react';
import { Modal, Radio, Card, Button, Typography, Space } from 'antd';
import { 
  FileExcelOutlined, 
  ThunderboltOutlined,
  ExportOutlined,
  EditOutlined,
  UploadOutlined,
  FilterOutlined,
  SaveOutlined
} from '@ant-design/icons';

const { Title, Text, Paragraph } = Typography;

export type CreationMethod = 'excel' | 'online';

interface WorkPackageCreationMethodModalProps {
  visible: boolean;
  onClose: () => void;
  onMethodSelected: (method: CreationMethod) => void;
}

export const WorkPackageCreationMethodModal: React.FC<WorkPackageCreationMethodModalProps> = ({
  visible,
  onClose,
  onMethodSelected
}) => {
  const [selectedMethod, setSelectedMethod] = React.useState<CreationMethod>('excel');

  const handleContinue = () => {
    onMethodSelected(selectedMethod);
    onClose();
  };

  const handleClose = () => {
    setSelectedMethod('excel');
    onClose();
  };

  return (
    <Modal
      title={
        <div>
          <Title level={4} style={{ margin: 0 }}>
            Create Work Package - Select Method
          </Title>
        </div>
      }
      open={visible}
      onCancel={handleClose}
      footer={
        <div style={{ textAlign: 'center' }}>
          <Button onClick={handleClose} style={{ marginRight: 16 }}>
            Cancel
          </Button>
          <Button type="primary" onClick={handleContinue}>
            Continue
          </Button>
        </div>
      }
      width={680}
      centered
    >
      <div style={{ padding: '20px 0' }}>
        <Paragraph style={{ fontSize: '16px', marginBottom: 32, textAlign: 'center' }}>
          Please select your preferred Work Package creation method:
        </Paragraph>

        <Radio.Group 
          value={selectedMethod} 
          onChange={(e) => setSelectedMethod(e.target.value)}
          style={{ width: '100%' }}
        >
          <Space direction="vertical" style={{ width: '100%' }} size={16}>
            {/* Excel Import Creation */}
            <Radio value="excel" style={{ width: '100%' }}>
              <Card 
                hoverable
                style={{ 
                  border: selectedMethod === 'excel' ? '2px solid #1890ff' : '1px solid #d9d9d9',
                  backgroundColor: selectedMethod === 'excel' ? '#f6ffed' : '#fff'
                }}
              >
                <div style={{ display: 'flex', alignItems: 'flex-start' }}>
                  <div style={{ marginRight: 16 }}>
                    <FileExcelOutlined style={{ fontSize: 32, color: '#52c41a' }} />
                  </div>
                  <div style={{ flex: 1 }}>
                    <Title level={5} style={{ margin: '0 0 8px 0' }}>
                      Excel Import Creation (Recommended for Batch)
                    </Title>
                    <Paragraph style={{ margin: '0 0 12px 0', color: '#666' }}>
                      <Text strong>Best for:</Text> Large datasets, multiple Work Packages, offline editing
                    </Paragraph>
                    <div style={{ display: 'flex', alignItems: 'center', gap: 8, color: '#1890ff' }}>
                      <Text strong>Process:</Text>
                      <FilterOutlined /> <Text>Filter</Text>
                      <Text>→</Text>
                      <ExportOutlined /> <Text>Export</Text>
                      <Text>→</Text>
                      <EditOutlined /> <Text>Edit Excel</Text>
                      <Text>→</Text>
                      <UploadOutlined /> <Text>Upload</Text>
                    </div>
                  </div>
                </div>
              </Card>
            </Radio>

            {/* Online Creation */}
            <Radio value="online" style={{ width: '100%' }}>
              <Card 
                hoverable
                style={{ 
                  border: selectedMethod === 'online' ? '2px solid #1890ff' : '1px solid #d9d9d9',
                  backgroundColor: selectedMethod === 'online' ? '#f6ffed' : '#fff'
                }}
              >
                <div style={{ display: 'flex', alignItems: 'flex-start' }}>
                  <div style={{ marginRight: 16 }}>
                    <ThunderboltOutlined style={{ fontSize: 32, color: '#faad14' }} />
                  </div>
                  <div style={{ flex: 1 }}>
                    <Title level={5} style={{ margin: '0 0 8px 0' }}>
                      Online Creation (Quick Single Package)
                    </Title>
                    <Paragraph style={{ margin: '0 0 12px 0', color: '#666' }}>
                      <Text strong>Best for:</Text> Small datasets, single Work Package, instant creation
                    </Paragraph>
                    <div style={{ display: 'flex', alignItems: 'center', gap: 8, color: '#faad14' }}>
                      <Text strong>Process:</Text>
                      <FilterOutlined /> <Text>Filter</Text>
                      <Text>→</Text>
                      <EditOutlined /> <Text>Configure</Text>
                      <Text>→</Text>
                      <SaveOutlined /> <Text>Save</Text>
                    </div>
                  </div>
                </div>
              </Card>
            </Radio>
          </Space>
        </Radio.Group>
      </div>
    </Modal>
  );
}; 