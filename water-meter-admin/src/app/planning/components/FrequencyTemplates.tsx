'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { 
  Table, 
  Button, 
  Input, 
  Select, 
  Modal, 
  Form, 
  message, 
  Space, 
  Card, 
  Row, 
  Col,
  Tag,
  Popconfirm,
  Tooltip,
  InputNumber,
  Descriptions,
  Typography,
  Switch,
  Slider
} from 'antd';
import { 
  PlusOutlined, 
  SearchOutlined, 
  EditOutlined, 
  DeleteOutlined, 
  EyeOutlined,
  CopyOutlined,
  CalculatorOutlined,
  ClockCircleOutlined
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';

const { Option } = Select;
const { TextArea } = Input;

interface FrequencyTemplateDto {
  id: number;
  name: string;
  description?: string;
  frequencyType: string;
  intervalValue: number;
  intervalUnit: string;
  isActive: boolean;
  estimatedDuration?: number;
  category: string;
  applicableAreas?: string;
  createdAt: string;
  createdBy: string;
  usageCount: number;
}

interface CreateFrequencyTemplateDto {
  name: string;
  description?: string;
  frequencyType: string;
  intervalValue: number;
  intervalUnit: string;
  isActive: boolean;
  estimatedDuration?: number;
  category: string;
  applicableAreas?: string;
}

interface SearchParams {
  page: number;
  pageSize: number;
  sortBy?: string;
  sortDirection?: string;
  name?: string;
  frequencyType?: string;
  category?: string;
  isActive?: boolean;
}

const FrequencyTemplates: React.FC = () => {
  const [templates, setTemplates] = useState<FrequencyTemplateDto[]>([]);
  const [loading, setLoading] = useState(false);
  const [total, setTotal] = useState(0);
  const [searchParams, setSearchParams] = useState<SearchParams>({
    page: 1,
    pageSize: 10,
    sortBy: 'CreatedAt',
    sortDirection: 'desc'
  });

  // Modal states
  const [isCreateModalVisible, setIsCreateModalVisible] = useState(false);
  const [isDetailModalVisible, setIsDetailModalVisible] = useState(false);
  const [isCalculatorModalVisible, setIsCalculatorModalVisible] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<FrequencyTemplateDto | null>(null);
  const [form] = Form.useForm();
  const [searchForm] = Form.useForm();
  const [calculatorForm] = Form.useForm();

  // Mock data for development
  const mockTemplates: FrequencyTemplateDto[] = [
    {
      id: 1,
      name: 'Weekly Residential Reading',
      description: 'Standard weekly reading template for residential areas',
      frequencyType: 'Regular',
      intervalValue: 1,
      intervalUnit: 'Week',
      isActive: true,
      estimatedDuration: 240, // 4 hours in minutes
      category: 'Residential',
      applicableAreas: 'Residential zones, apartments',
      createdAt: '2024-01-01T10:00:00Z',
      createdBy: 'System',
      usageCount: 25
    },
    {
      id: 2,
      name: 'Monthly Commercial Inspection',
      description: 'Monthly inspection schedule for commercial properties',
      frequencyType: 'Scheduled',
      intervalValue: 1,
      intervalUnit: 'Month',
      isActive: true,
      estimatedDuration: 480, // 8 hours in minutes
      category: 'Commercial',
      applicableAreas: 'Office buildings, shopping centers',
      createdAt: '2024-01-05T14:30:00Z',
      createdBy: 'Admin',
      usageCount: 12
    },
    {
      id: 3,
      name: 'Quarterly Industrial Audit',
      description: 'Quarterly comprehensive audit for industrial facilities',
      frequencyType: 'Audit',
      intervalValue: 3,
      intervalUnit: 'Month',
      isActive: true,
      estimatedDuration: 720, // 12 hours in minutes
      category: 'Industrial',
      applicableAreas: 'Manufacturing plants, warehouses',
      createdAt: '2024-01-10T09:00:00Z',
      createdBy: 'Manager',
      usageCount: 6
    }
  ];

  const loadTemplates = useCallback(async () => {
    setLoading(true);
    try {
      // TODO: Replace with actual API call
      // const response = await frequencyTemplateService.getTemplates(searchParams);
      // setTemplates(response.templates);
      // setTotal(response.totalCount);
      
      // Mock implementation
      setTimeout(() => {
        setTemplates(mockTemplates);
        setTotal(mockTemplates.length);
        setLoading(false);
      }, 500);
    } catch (error) {
      message.error('Failed to load frequency templates');
      console.error('Error loading templates:', error);
      setLoading(false);
    }
  }, [searchParams]);

  useEffect(() => {
    loadTemplates();
  }, [loadTemplates]);

  const handleSearch = (values: any) => {
    setSearchParams({
      ...searchParams,
      page: 1,
      name: values.name,
      frequencyType: values.frequencyType,
      category: values.category,
      isActive: values.isActive,
    });
  };

  const handleTableChange = (pagination: any, filters: any, sorter: any) => {
    setSearchParams({
      ...searchParams,
      page: pagination.current,
      pageSize: pagination.pageSize,
      sortBy: sorter.field,
      sortDirection: sorter.order === 'ascend' ? 'asc' : 'desc'
    });
  };

  const handleCreate = async (values: CreateFrequencyTemplateDto) => {
    try {
      // TODO: Replace with actual API call
      // await frequencyTemplateService.createTemplate(values);
      message.success('Frequency template created successfully');
      setIsCreateModalVisible(false);
      form.resetFields();
      loadTemplates();
    } catch (error) {
      message.error('Failed to create frequency template');
      console.error('Error creating template:', error);
    }
  };

  const handleEdit = (template: FrequencyTemplateDto) => {
    setSelectedTemplate(template);
    form.setFieldsValue(template);
    setIsCreateModalVisible(true);
  };

  const handleDelete = async (id: number) => {
    try {
      // TODO: Replace with actual API call
      // await frequencyTemplateService.deleteTemplate(id);
      message.success('Frequency template deleted successfully');
      loadTemplates();
    } catch (error) {
      message.error('Failed to delete frequency template');
      console.error('Error deleting template:', error);
    }
  };

  const handleViewDetails = (template: FrequencyTemplateDto) => {
    setSelectedTemplate(template);
    setIsDetailModalVisible(true);
  };

  const handleClone = async (template: FrequencyTemplateDto) => {
    try {
      // TODO: Replace with actual API call
      // await frequencyTemplateService.cloneTemplate(template.id, `${template.name} (Copy)`);
      message.success('Template cloned successfully');
      loadTemplates();
    } catch (error) {
      message.error('Failed to clone template');
      console.error('Error cloning template:', error);
    }
  };

  const handleCalculator = (template: FrequencyTemplateDto) => {
    setSelectedTemplate(template);
    calculatorForm.setFieldsValue({
      totalMeters: 100,
      workingDaysPerWeek: 5,
      workingHoursPerDay: 8
    });
    setIsCalculatorModalVisible(true);
  };

  const calculateSchedule = () => {
    const values = calculatorForm.getFieldsValue();
    const { totalMeters, workingDaysPerWeek, workingHoursPerDay } = values;
    
    if (!selectedTemplate) return;

    const estimatedDurationHours = (selectedTemplate.estimatedDuration || 0) / 60;
    const totalWorkingHoursPerWeek = workingDaysPerWeek * workingHoursPerDay;
    const metersPerHour = totalMeters / estimatedDurationHours;
    const metersPerWeek = metersPerHour * totalWorkingHoursPerWeek;
    
    message.success(`Calculation complete: ${metersPerWeek.toFixed(0)} meters can be processed per week`);
  };

  const getFrequencyColor = (frequencyType: string) => {
    switch (frequencyType) {
      case 'Regular':
        return 'green';
      case 'Scheduled':
        return 'blue';
      case 'Audit':
        return 'orange';
      case 'Emergency':
        return 'red';
      default:
        return 'default';
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'Residential':
        return 'cyan';
      case 'Commercial':
        return 'purple';
      case 'Industrial':
        return 'orange';
      default:
        return 'default';
    }
  };

  const formatFrequency = (intervalValue: number, intervalUnit: string) => {
    return `Every ${intervalValue} ${intervalUnit}${intervalValue > 1 ? 's' : ''}`;
  };

  const columns: ColumnsType<FrequencyTemplateDto> = [
    {
      title: 'ID',
      dataIndex: 'id',
      width: 80,
      sorter: true,
    },
    {
      title: 'Template Name',
      dataIndex: 'name',
      sorter: true,
      render: (text, record) => (
        <Button type="link" onClick={() => handleViewDetails(record)}>
          {text}
        </Button>
      ),
    },
    {
      title: 'Frequency Type',
      dataIndex: 'frequencyType',
      width: 130,
      render: (type) => (
        <Tag color={getFrequencyColor(type)}>{type}</Tag>
      ),
    },
    {
      title: 'Frequency',
      width: 150,
      render: (_, record) => formatFrequency(record.intervalValue, record.intervalUnit),
    },
    {
      title: 'Category',
      dataIndex: 'category',
      width: 120,
      render: (category) => (
        <Tag color={getCategoryColor(category)}>{category}</Tag>
      ),
    },
    {
      title: 'Est. Duration',
      dataIndex: 'estimatedDuration',
      width: 120,
      render: (duration) => duration ? `${(duration / 60).toFixed(1)} hrs` : '-',
    },
    {
      title: 'Usage Count',
      dataIndex: 'usageCount',
      width: 100,
      sorter: true,
      render: (count) => (
        <Tag color={count > 10 ? 'green' : count > 5 ? 'orange' : 'default'}>
          {count}
        </Tag>
      ),
    },
    {
      title: 'Status',
      dataIndex: 'isActive',
      width: 100,
      render: (isActive) => (
        <Tag color={isActive ? 'green' : 'red'}>
          {isActive ? 'Active' : 'Inactive'}
        </Tag>
      ),
    },
    {
      title: 'Actions',
      width: 250,
      render: (_, record) => (
        <Space>
          <Tooltip title="View Details">
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() => handleViewDetails(record)}
            />
          </Tooltip>
          <Tooltip title="Edit">
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => handleEdit(record)}
            />
          </Tooltip>
          <Tooltip title="Clone Template">
            <Button
              type="text"
              icon={<CopyOutlined />}
              onClick={() => handleClone(record)}
            />
          </Tooltip>
          <Tooltip title="Frequency Calculator">
            <Button
              type="text"
              icon={<CalculatorOutlined />}
              onClick={() => handleCalculator(record)}
            />
          </Tooltip>
          <Popconfirm
            title="Are you sure you want to delete this template?"
            onConfirm={() => handleDelete(record.id)}
            okText="Yes"
            cancelText="No"
          >
            <Tooltip title="Delete">
              <Button
                type="text"
                danger
                icon={<DeleteOutlined />}
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div className="frequency-templates">
      {/* Search Form */}
      <Card className="mb-4">
        <Form
          form={searchForm}
          layout="inline"
          onFinish={handleSearch}
          className="search-form"
        >
          <Row gutter={16} className="w-full">
            <Col span={6}>
              <Form.Item name="name">
                <Input placeholder="Template Name" />
              </Form.Item>
            </Col>
            <Col span={4}>
              <Form.Item name="frequencyType">
                <Select placeholder="Frequency Type" allowClear>
                  <Option value="Regular">Regular</Option>
                  <Option value="Scheduled">Scheduled</Option>
                  <Option value="Audit">Audit</Option>
                  <Option value="Emergency">Emergency</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={4}>
              <Form.Item name="category">
                <Select placeholder="Category" allowClear>
                  <Option value="Residential">Residential</Option>
                  <Option value="Commercial">Commercial</Option>
                  <Option value="Industrial">Industrial</Option>
                  <Option value="Mixed">Mixed</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={4}>
              <Form.Item name="isActive">
                <Select placeholder="Status" allowClear>
                  <Option value={true}>Active</Option>
                  <Option value={false}>Inactive</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={6}>
              <Space>
                <Button type="primary" htmlType="submit" icon={<SearchOutlined />}>
                  Search
                </Button>
                <Button
                  onClick={() => {
                    searchForm.resetFields();
                    setSearchParams({
                      page: 1,
                      pageSize: 10,
                      sortBy: 'CreatedAt',
                      sortDirection: 'desc'
                    });
                  }}
                >
                  Reset
                </Button>
              </Space>
            </Col>
          </Row>
        </Form>
      </Card>

      {/* Action Buttons */}
      <Card className="mb-4">
        <Space>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => setIsCreateModalVisible(true)}
          >
            Create Template
          </Button>
          <Button
            icon={<CalculatorOutlined />}
            onClick={() => message.info('Batch calculator feature coming soon')}
          >
            Batch Calculator
          </Button>
        </Space>
      </Card>

      {/* Templates Table */}
      <Card>
        <Table
          columns={columns}
          dataSource={templates}
          rowKey="id"
          loading={loading}
          pagination={{
            current: searchParams.page,
            pageSize: searchParams.pageSize,
            total: total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `Total ${total} items`,
          }}
          onChange={handleTableChange}
          scroll={{ x: 1200 }}
        />
      </Card>

      {/* Create/Edit Template Modal */}
      <Modal
        title={selectedTemplate ? 'Edit Frequency Template' : 'Create New Template'}
        open={isCreateModalVisible}
        onCancel={() => {
          setIsCreateModalVisible(false);
          setSelectedTemplate(null);
          form.resetFields();
        }}
        footer={null}
        width={800}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleCreate}
          initialValues={{
            frequencyType: 'Regular',
            intervalValue: 1,
            intervalUnit: 'Week',
            isActive: true,
            category: 'Residential'
          }}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="name"
                label="Template Name"
                rules={[{ required: true, message: 'Please enter template name' }]}
              >
                <Input placeholder="Enter template name" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="category" label="Category">
                <Select>
                  <Option value="Residential">Residential</Option>
                  <Option value="Commercial">Commercial</Option>
                  <Option value="Industrial">Industrial</Option>
                  <Option value="Mixed">Mixed</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item name="description" label="Description">
            <TextArea rows={3} placeholder="Enter template description" />
          </Form.Item>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item name="frequencyType" label="Frequency Type">
                <Select>
                  <Option value="Regular">Regular</Option>
                  <Option value="Scheduled">Scheduled</Option>
                  <Option value="Audit">Audit</Option>
                  <Option value="Emergency">Emergency</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="intervalValue"
                label="Interval Value"
                rules={[{ required: true, message: 'Please enter interval value' }]}
              >
                <InputNumber
                  min={1}
                  className="w-full"
                  placeholder="Enter interval value"
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="intervalUnit" label="Interval Unit">
                <Select>
                  <Option value="Day">Day</Option>
                  <Option value="Week">Week</Option>
                  <Option value="Month">Month</Option>
                  <Option value="Quarter">Quarter</Option>
                  <Option value="Year">Year</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item name="estimatedDuration" label="Estimated Duration (minutes)">
                <InputNumber
                  min={0}
                  className="w-full"
                  placeholder="Enter duration in minutes"
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="isActive" label="Active" valuePropName="checked">
                <Switch />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item name="applicableAreas" label="Applicable Areas">
            <TextArea rows={2} placeholder="Enter applicable areas" />
          </Form.Item>

          <Form.Item className="text-right">
            <Space>
              <Button onClick={() => {
                setIsCreateModalVisible(false);
                setSelectedTemplate(null);
                form.resetFields();
              }}>
                Cancel
              </Button>
              <Button type="primary" htmlType="submit">
                {selectedTemplate ? 'Update' : 'Create'}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* Template Details Modal */}
      <Modal
        title="Frequency Template Details"
        open={isDetailModalVisible}
        onCancel={() => setIsDetailModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setIsDetailModalVisible(false)}>
            Close
          </Button>
        ]}
        width={900}
      >
        {selectedTemplate && (
          <div>
            <Descriptions bordered column={2}>
              <Descriptions.Item label="Template Name">{selectedTemplate.name}</Descriptions.Item>
              <Descriptions.Item label="Category">
                <Tag color={getCategoryColor(selectedTemplate.category)}>{selectedTemplate.category}</Tag>
              </Descriptions.Item>
              <Descriptions.Item label="Frequency Type">
                <Tag color={getFrequencyColor(selectedTemplate.frequencyType)}>{selectedTemplate.frequencyType}</Tag>
              </Descriptions.Item>
              <Descriptions.Item label="Frequency">
                {formatFrequency(selectedTemplate.intervalValue, selectedTemplate.intervalUnit)}
              </Descriptions.Item>
              <Descriptions.Item label="Estimated Duration">
                {selectedTemplate.estimatedDuration ? `${(selectedTemplate.estimatedDuration / 60).toFixed(1)} hours` : '-'}
              </Descriptions.Item>
              <Descriptions.Item label="Usage Count">
                <Tag color={selectedTemplate.usageCount > 10 ? 'green' : selectedTemplate.usageCount > 5 ? 'orange' : 'default'}>
                  {selectedTemplate.usageCount}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="Status">
                <Tag color={selectedTemplate.isActive ? 'green' : 'red'}>
                  {selectedTemplate.isActive ? 'Active' : 'Inactive'}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="Created At">{selectedTemplate.createdAt}</Descriptions.Item>
              <Descriptions.Item label="Created By">{selectedTemplate.createdBy}</Descriptions.Item>
            </Descriptions>

            {selectedTemplate.description && (
              <div className="mt-4">
                <Typography.Title level={5}>Description</Typography.Title>
                <Typography.Paragraph>{selectedTemplate.description}</Typography.Paragraph>
              </div>
            )}

            {selectedTemplate.applicableAreas && (
              <div className="mt-4">
                <Typography.Title level={5}>Applicable Areas</Typography.Title>
                <Typography.Paragraph>{selectedTemplate.applicableAreas}</Typography.Paragraph>
              </div>
            )}
          </div>
        )}
      </Modal>

      {/* Frequency Calculator Modal */}
      <Modal
        title="Frequency Calculator"
        open={isCalculatorModalVisible}
        onCancel={() => setIsCalculatorModalVisible(false)}
        footer={[
          <Button key="cancel" onClick={() => setIsCalculatorModalVisible(false)}>
            Cancel
          </Button>,
          <Button key="calculate" type="primary" onClick={calculateSchedule}>
            Calculate
          </Button>
        ]}
        width={600}
      >
        {selectedTemplate && (
          <div>
            <Typography.Title level={5}>
              Calculate Schedule for: {selectedTemplate.name}
            </Typography.Title>
            <Form form={calculatorForm} layout="vertical">
              <Form.Item
                name="totalMeters"
                label="Total Water Meters"
                rules={[{ required: true, message: 'Please enter total meters' }]}
              >
                <InputNumber
                  min={1}
                  className="w-full"
                  placeholder="Enter total number of meters"
                />
              </Form.Item>
              
              <Form.Item
                name="workingDaysPerWeek"
                label="Working Days per Week"
                rules={[{ required: true, message: 'Please enter working days' }]}
              >
                <Slider
                  min={1}
                  max={7}
                  marks={{
                    1: '1',
                    2: '2',
                    3: '3',
                    4: '4',
                    5: '5',
                    6: '6',
                    7: '7'
                  }}
                  className="w-full"
                />
              </Form.Item>
              
              <Form.Item
                name="workingHoursPerDay"
                label="Working Hours per Day"
                rules={[{ required: true, message: 'Please enter working hours' }]}
              >
                <Slider
                  min={4}
                  max={12}
                  marks={{
                    4: '4h',
                    6: '6h',
                    8: '8h',
                    10: '10h',
                    12: '12h'
                  }}
                  className="w-full"
                />
              </Form.Item>
              
              <Typography.Paragraph>
                This calculator will estimate how many meters can be processed based on the template's frequency and your working schedule.
              </Typography.Paragraph>
            </Form>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default FrequencyTemplates; 