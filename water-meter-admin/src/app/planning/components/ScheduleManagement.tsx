'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { 
  Table, 
  Button, 
  Input, 
  Select, 
  Modal, 
  Form, 
  message, 
  Space, 
  Card, 
  Row, 
  Col,
  Tag,
  Popconfirm,
  Tooltip,
  DatePicker,
  Upload,
  Progress,
  Descriptions,
  Statistic,
  Badge,
  Typography,
  InputNumber
} from 'antd';
import { 
  PlusOutlined, 
  SearchOutlined, 
  EditOutlined, 
  DeleteOutlined, 
  ExportOutlined,
  ImportOutlined,
  EyeOutlined,
  EnvironmentOutlined,
  CalendarOutlined
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';

const { Option } = Select;
const { RangePicker } = DatePicker;
const { TextArea } = Input;

interface ScheduleDto {
  id: number;
  name: string;
  description?: string;
  status: string;
  scheduledDate: string;
  zone?: string;
  area?: string;
  assignedTo?: string;
  priority: string;
  recurrenceType: string;
  totalItems: number;
  completedItems: number;
  progressPercentage: number;
  totalEstimatedHours?: number;
  totalActualHours?: number;
  createdAt: string;
  createdBy: string;
}

interface CreateScheduleDto {
  name: string;
  description?: string;
  status: string;
  scheduledDate: string;
  zone?: string;
  area?: string;
  assignedTo?: string;
  priority: string;
  recurrenceType: string;
  totalEstimatedHours?: number;
  notes?: string;
}

interface SearchParams {
  page: number;
  pageSize: number;
  sortBy?: string;
  sortDirection?: string;
  name?: string;
  status?: string;
  zone?: string;
  area?: string;
  assignedTo?: string;
  priority?: string;
  scheduledDateFrom?: Date;
  scheduledDateTo?: Date;
}

const ScheduleManagement: React.FC = () => {
  const [schedules, setSchedules] = useState<ScheduleDto[]>([]);
  const [loading, setLoading] = useState(false);
  const [total, setTotal] = useState(0);
  const [searchParams, setSearchParams] = useState<SearchParams>({
    page: 1,
    pageSize: 10,
    sortBy: 'CreatedAt',
    sortDirection: 'desc'
  });

  // Modal states
  const [isCreateModalVisible, setIsCreateModalVisible] = useState(false);
  const [isDetailModalVisible, setIsDetailModalVisible] = useState(false);
  const [isImportModalVisible, setIsImportModalVisible] = useState(false);
  const [selectedSchedule, setSelectedSchedule] = useState<ScheduleDto | null>(null);
  const [form] = Form.useForm();
  const [searchForm] = Form.useForm();

  // Mock data for development
  const mockSchedules: ScheduleDto[] = [
    {
      id: 1,
      name: 'Weekly Zone A Reading',
      description: 'Weekly water meter readings for Zone A',
      status: 'Active',
      scheduledDate: '2024-01-15',
      zone: 'Zone A',
      area: 'Residential North',
      assignedTo: 'John Smith',
      priority: 'High',
      recurrenceType: 'Weekly',
      totalItems: 150,
      completedItems: 120,
      progressPercentage: 80,
      totalEstimatedHours: 8,
      totalActualHours: 6.5,
      createdAt: '2024-01-01T10:00:00Z',
      createdBy: 'System'
    },
    {
      id: 2,
      name: 'Monthly Zone B Inspection',
      description: 'Monthly inspection of Zone B meters',
      status: 'Draft',
      scheduledDate: '2024-01-20',
      zone: 'Zone B',
      area: 'Commercial District',
      assignedTo: 'Jane Doe',
      priority: 'Medium',
      recurrenceType: 'Monthly',
      totalItems: 80,
      completedItems: 0,
      progressPercentage: 0,
      totalEstimatedHours: 12,
      totalActualHours: 0,
      createdAt: '2024-01-05T14:30:00Z',
      createdBy: 'Admin'
    }
  ];

  const loadSchedules = useCallback(async () => {
    setLoading(true);
    try {
      // TODO: Replace with actual API call
      // const response = await scheduleService.getSchedules(searchParams);
      // setSchedules(response.schedules);
      // setTotal(response.totalCount);
      
      // Mock implementation
      setTimeout(() => {
        setSchedules(mockSchedules);
        setTotal(mockSchedules.length);
        setLoading(false);
      }, 500);
    } catch (error) {
      message.error('Failed to load schedules');
      console.error('Error loading schedules:', error);
      setLoading(false);
    }
  }, [searchParams]);

  useEffect(() => {
    loadSchedules();
  }, [loadSchedules]);

  const handleSearch = (values: any) => {
    setSearchParams({
      ...searchParams,
      page: 1,
      name: values.name,
      status: values.status,
      zone: values.zone,
      area: values.area,
      assignedTo: values.assignedTo,
      priority: values.priority,
      scheduledDateFrom: values.dateRange?.[0]?.toDate(),
      scheduledDateTo: values.dateRange?.[1]?.toDate(),
    });
  };

  const handleTableChange = (pagination: any, filters: any, sorter: any) => {
    setSearchParams({
      ...searchParams,
      page: pagination.current,
      pageSize: pagination.pageSize,
      sortBy: sorter.field,
      sortDirection: sorter.order === 'ascend' ? 'asc' : 'desc'
    });
  };

  const handleCreate = async (values: CreateScheduleDto) => {
    try {
      // TODO: Replace with actual API call
      // await scheduleService.createSchedule(values);
      message.success('Schedule created successfully');
      setIsCreateModalVisible(false);
      form.resetFields();
      loadSchedules();
    } catch (error) {
      message.error('Failed to create schedule');
      console.error('Error creating schedule:', error);
    }
  };

  const handleEdit = (schedule: ScheduleDto) => {
    setSelectedSchedule(schedule);
    form.setFieldsValue({
      ...schedule,
      scheduledDate: schedule.scheduledDate ? new Date(schedule.scheduledDate) : null
    });
    setIsCreateModalVisible(true);
  };

  const handleDelete = async (id: number) => {
    try {
      // TODO: Replace with actual API call
      // await scheduleService.deleteSchedule(id);
      message.success('Schedule deleted successfully');
      loadSchedules();
    } catch (error) {
      message.error('Failed to delete schedule');
      console.error('Error deleting schedule:', error);
    }
  };

  const handleViewDetails = (schedule: ScheduleDto) => {
    setSelectedSchedule(schedule);
    setIsDetailModalVisible(true);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Active':
        return 'green';
      case 'Completed':
        return 'blue';
      case 'Cancelled':
        return 'red';
      default:
        return 'default';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'Critical':
        return 'red';
      case 'High':
        return 'orange';
      case 'Medium':
        return 'yellow';
      case 'Low':
        return 'green';
      default:
        return 'default';
    }
  };

  const columns: ColumnsType<ScheduleDto> = [
    {
      title: 'ID',
      dataIndex: 'id',
      width: 80,
      sorter: true,
    },
    {
      title: 'Schedule Name',
      dataIndex: 'name',
      sorter: true,
      render: (text, record) => (
        <Button type="link" onClick={() => handleViewDetails(record)}>
          {text}
        </Button>
      ),
    },
    {
      title: 'Status',
      dataIndex: 'status',
      width: 120,
      render: (status) => (
        <Tag color={getStatusColor(status)}>{status}</Tag>
      ),
    },
    {
      title: 'Scheduled Date',
      dataIndex: 'scheduledDate',
      width: 140,
      sorter: true,
      render: (date) => new Date(date).toLocaleDateString(),
    },
    {
      title: 'Zone',
      dataIndex: 'zone',
      width: 100,
    },
    {
      title: 'Area',
      dataIndex: 'area',
      width: 150,
    },
    {
      title: 'Assigned To',
      dataIndex: 'assignedTo',
      width: 120,
    },
    {
      title: 'Priority',
      dataIndex: 'priority',
      width: 100,
      render: (priority) => (
        <Tag color={getPriorityColor(priority)}>{priority}</Tag>
      ),
    },
    {
      title: 'Progress',
      dataIndex: 'progressPercentage',
      width: 120,
      render: (percentage, record) => (
        <div>
          <Progress percent={percentage} size="small" />
          <span style={{ fontSize: '12px' }}>
            {record.completedItems}/{record.totalItems}
          </span>
        </div>
      ),
    },
    {
      title: 'Est. Hours',
      dataIndex: 'totalEstimatedHours',
      width: 100,
    },
    {
      title: 'Actual Hours',
      dataIndex: 'totalActualHours',
      width: 100,
    },
    {
      title: 'Actions',
      width: 200,
      render: (_, record) => (
        <Space>
          <Tooltip title="View Details">
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() => handleViewDetails(record)}
            />
          </Tooltip>
          <Tooltip title="Edit">
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => handleEdit(record)}
            />
          </Tooltip>
                     <Tooltip title="View Map">
             <Button
               type="text"
               icon={<EnvironmentOutlined />}
               onClick={() => message.info('Map view feature coming soon')}
             />
           </Tooltip>
          <Popconfirm
            title="Are you sure you want to delete this schedule?"
            onConfirm={() => handleDelete(record.id)}
            okText="Yes"
            cancelText="No"
          >
            <Tooltip title="Delete">
              <Button
                type="text"
                danger
                icon={<DeleteOutlined />}
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div className="schedule-management">
      {/* Search Form */}
      <Card className="mb-4">
        <Form
          form={searchForm}
          layout="inline"
          onFinish={handleSearch}
          className="search-form"
        >
          <Row gutter={16} className="w-full">
            <Col span={6}>
              <Form.Item name="name">
                <Input placeholder="Schedule Name" />
              </Form.Item>
            </Col>
            <Col span={4}>
              <Form.Item name="status">
                <Select placeholder="Status" allowClear>
                  <Option value="Draft">Draft</Option>
                  <Option value="Active">Active</Option>
                  <Option value="Completed">Completed</Option>
                  <Option value="Cancelled">Cancelled</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={4}>
              <Form.Item name="zone">
                <Input placeholder="Zone" />
              </Form.Item>
            </Col>
            <Col span={4}>
              <Form.Item name="priority">
                <Select placeholder="Priority" allowClear>
                  <Option value="Low">Low</Option>
                  <Option value="Medium">Medium</Option>
                  <Option value="High">High</Option>
                  <Option value="Critical">Critical</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item name="dateRange">
                <RangePicker placeholder={['Start Date', 'End Date']} />
              </Form.Item>
            </Col>
          </Row>
          <Row className="mt-4">
            <Col>
              <Space>
                <Button type="primary" htmlType="submit" icon={<SearchOutlined />}>
                  Search
                </Button>
                <Button
                  onClick={() => {
                    searchForm.resetFields();
                    setSearchParams({
                      page: 1,
                      pageSize: 10,
                      sortBy: 'CreatedAt',
                      sortDirection: 'desc'
                    });
                  }}
                >
                  Reset
                </Button>
              </Space>
            </Col>
          </Row>
        </Form>
      </Card>

      {/* Action Buttons */}
      <Card className="mb-4">
        <Space>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => setIsCreateModalVisible(true)}
          >
            Create Schedule
          </Button>
          <Button
            icon={<ImportOutlined />}
            onClick={() => setIsImportModalVisible(true)}
          >
            Import CSV
          </Button>
          <Button
            icon={<ExportOutlined />}
            onClick={() => message.info('Export feature coming soon')}
          >
            Export Excel
          </Button>
        </Space>
      </Card>

      {/* Schedules Table */}
      <Card>
        <Table
          columns={columns}
          dataSource={schedules}
          rowKey="id"
          loading={loading}
          pagination={{
            current: searchParams.page,
            pageSize: searchParams.pageSize,
            total: total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `Total ${total} items`,
          }}
          onChange={handleTableChange}
          scroll={{ x: 1200 }}
        />
      </Card>

      {/* Create/Edit Schedule Modal */}
      <Modal
        title={selectedSchedule ? 'Edit Schedule' : 'Create New Schedule'}
        open={isCreateModalVisible}
        onCancel={() => {
          setIsCreateModalVisible(false);
          setSelectedSchedule(null);
          form.resetFields();
        }}
        footer={null}
        width={800}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleCreate}
          initialValues={{
            status: 'Draft',
            priority: 'Medium',
            recurrenceType: 'None'
          }}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="name"
                label="Schedule Name"
                rules={[{ required: true, message: 'Please enter schedule name' }]}
              >
                <Input placeholder="Enter schedule name" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="status" label="Status">
                <Select>
                  <Option value="Draft">Draft</Option>
                  <Option value="Active">Active</Option>
                  <Option value="Completed">Completed</Option>
                  <Option value="Cancelled">Cancelled</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item name="description" label="Description">
            <TextArea rows={3} placeholder="Enter schedule description" />
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="scheduledDate"
                label="Scheduled Date"
                rules={[{ required: true, message: 'Please select scheduled date' }]}
              >
                <DatePicker className="w-full" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="priority" label="Priority">
                <Select>
                  <Option value="Low">Low</Option>
                  <Option value="Medium">Medium</Option>
                  <Option value="High">High</Option>
                  <Option value="Critical">Critical</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item name="zone" label="Zone">
                <Input placeholder="Enter zone" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="area" label="Area">
                <Input placeholder="Enter area" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="assignedTo" label="Assigned To">
                <Input placeholder="Enter assignee" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item name="recurrenceType" label="Recurrence Type">
                <Select>
                  <Option value="None">None</Option>
                  <Option value="Daily">Daily</Option>
                  <Option value="Weekly">Weekly</Option>
                  <Option value="Monthly">Monthly</Option>
                  <Option value="Quarterly">Quarterly</Option>
                  <Option value="Annual">Annual</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="totalEstimatedHours" label="Estimated Hours">
                <InputNumber
                  min={0}
                  step={0.5}
                  className="w-full"
                  placeholder="Enter estimated hours"
                />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item name="notes" label="Notes">
            <TextArea rows={3} placeholder="Enter additional notes" />
          </Form.Item>

          <Form.Item className="text-right">
            <Space>
              <Button onClick={() => {
                setIsCreateModalVisible(false);
                setSelectedSchedule(null);
                form.resetFields();
              }}>
                Cancel
              </Button>
              <Button type="primary" htmlType="submit">
                {selectedSchedule ? 'Update' : 'Create'}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* Schedule Details Modal */}
      <Modal
        title="Schedule Details"
        open={isDetailModalVisible}
        onCancel={() => setIsDetailModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setIsDetailModalVisible(false)}>
            Close
          </Button>
        ]}
        width={900}
      >
        {selectedSchedule && (
          <div>
            <Descriptions bordered column={2}>
              <Descriptions.Item label="Schedule Name">{selectedSchedule.name}</Descriptions.Item>
              <Descriptions.Item label="Status">
                <Tag color={getStatusColor(selectedSchedule.status)}>{selectedSchedule.status}</Tag>
              </Descriptions.Item>
              <Descriptions.Item label="Scheduled Date">{selectedSchedule.scheduledDate}</Descriptions.Item>
              <Descriptions.Item label="Priority">
                <Tag color={getPriorityColor(selectedSchedule.priority)}>{selectedSchedule.priority}</Tag>
              </Descriptions.Item>
              <Descriptions.Item label="Zone">{selectedSchedule.zone}</Descriptions.Item>
              <Descriptions.Item label="Area">{selectedSchedule.area}</Descriptions.Item>
              <Descriptions.Item label="Assigned To">{selectedSchedule.assignedTo}</Descriptions.Item>
              <Descriptions.Item label="Recurrence">{selectedSchedule.recurrenceType}</Descriptions.Item>
              <Descriptions.Item label="Total Items">{selectedSchedule.totalItems}</Descriptions.Item>
              <Descriptions.Item label="Completed Items">{selectedSchedule.completedItems}</Descriptions.Item>
              <Descriptions.Item label="Progress">
                <Progress percent={selectedSchedule.progressPercentage} />
              </Descriptions.Item>
              <Descriptions.Item label="Estimated Hours">{selectedSchedule.totalEstimatedHours}</Descriptions.Item>
              <Descriptions.Item label="Actual Hours">{selectedSchedule.totalActualHours}</Descriptions.Item>
              <Descriptions.Item label="Created At">{selectedSchedule.createdAt}</Descriptions.Item>
              <Descriptions.Item label="Created By">{selectedSchedule.createdBy}</Descriptions.Item>
            </Descriptions>

            {selectedSchedule.description && (
              <div className="mt-4">
                <Typography.Title level={5}>Description</Typography.Title>
                <Typography.Paragraph>{selectedSchedule.description}</Typography.Paragraph>
              </div>
            )}
          </div>
        )}
      </Modal>

      {/* Import CSV Modal */}
      <Modal
        title="Import Schedules from CSV"
        open={isImportModalVisible}
        onCancel={() => setIsImportModalVisible(false)}
        footer={null}
      >
        <Upload.Dragger
          accept=".csv"
          beforeUpload={() => false}
          onChange={(info) => {
            // Handle file upload
            message.info('Import feature coming soon');
          }}
        >
          <p className="ant-upload-drag-icon">
            <ImportOutlined />
          </p>
          <p className="ant-upload-text">Click or drag CSV file to this area to upload</p>
          <p className="ant-upload-hint">
            Upload CSV file with schedule data. Support for a single upload.
          </p>
        </Upload.Dragger>
      </Modal>
    </div>
  );
};

export default ScheduleManagement; 