'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { 
  Table, 
  Button, 
  Input, 
  Select, 
  Modal, 
  Form, 
  message, 
  Space, 
  Card, 
  Row, 
  Col,
  Tag,
  Popconfirm,
  Tooltip,
  InputNumber,
  Descriptions,
  Typography,
  Switch
} from 'antd';
import { 
  PlusOutlined, 
  SearchOutlined, 
  EditOutlined, 
  DeleteOutlined, 
  EyeOutlined,
  EnvironmentOutlined,
  CopyOutlined,
  SettingOutlined
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';

const { Option } = Select;
const { TextArea } = Input;

interface RouteDto {
  id: number;
  name: string;
  description?: string;
  status: string;
  zone?: string;
  area?: string;
  estimatedDuration?: number;
  estimatedDistance?: number;
  totalMeters: number;
  assignedTo?: string;
  isTemplate: boolean;
  templateCategory?: string;
  difficultyRating?: number;
  createdAt: string;
  createdBy: string;
}

interface CreateRouteDto {
  name: string;
  description?: string;
  zone?: string;
  area?: string;
  estimatedDuration?: number;
  estimatedDistance?: number;
  assignedTo?: string;
  isTemplate: boolean;
  templateCategory?: string;
  difficultyRating?: number;
  notes?: string;
}

interface SearchParams {
  page: number;
  pageSize: number;
  sortBy?: string;
  sortDirection?: string;
  name?: string;
  status?: string;
  zone?: string;
  area?: string;
  assignedTo?: string;
  isTemplate?: boolean;
  templateCategory?: string;
}

const RouteManagement: React.FC = () => {
  const [routes, setRoutes] = useState<RouteDto[]>([]);
  const [loading, setLoading] = useState(false);
  const [total, setTotal] = useState(0);
  const [searchParams, setSearchParams] = useState<SearchParams>({
    page: 1,
    pageSize: 10,
    sortBy: 'CreatedAt',
    sortDirection: 'desc'
  });

  // Modal states
  const [isCreateModalVisible, setIsCreateModalVisible] = useState(false);
  const [isDetailModalVisible, setIsDetailModalVisible] = useState(false);
  const [isOptimizeModalVisible, setIsOptimizeModalVisible] = useState(false);
  const [selectedRoute, setSelectedRoute] = useState<RouteDto | null>(null);
  const [form] = Form.useForm();
  const [searchForm] = Form.useForm();

  // Mock data for development
  const mockRoutes: RouteDto[] = [
    {
      id: 1,
      name: 'Downtown Commercial Route',
      description: 'Main commercial district route covering office buildings',
      status: 'Active',
      zone: 'Zone A',
      area: 'Downtown',
      estimatedDuration: 480, // 8 hours in minutes
      estimatedDistance: 15.5,
      totalMeters: 120,
      assignedTo: 'John Smith',
      isTemplate: false,
      difficultyRating: 3.5,
      createdAt: '2024-01-01T10:00:00Z',
      createdBy: 'System'
    },
    {
      id: 2,
      name: 'Residential Zone B Template',
      description: 'Standard residential area route template',
      status: 'Active',
      zone: 'Zone B',
      area: 'Residential North',
      estimatedDuration: 360, // 6 hours in minutes
      estimatedDistance: 12.0,
      totalMeters: 150,
      assignedTo: null,
      isTemplate: true,
      templateCategory: 'Residential',
      difficultyRating: 2.0,
      createdAt: '2024-01-05T14:30:00Z',
      createdBy: 'Admin'
    }
  ];

  const loadRoutes = useCallback(async () => {
    setLoading(true);
    try {
      // TODO: Replace with actual API call
      // const response = await routeService.getRoutes(searchParams);
      // setRoutes(response.routes);
      // setTotal(response.totalCount);
      
      // Mock implementation
      setTimeout(() => {
        setRoutes(mockRoutes);
        setTotal(mockRoutes.length);
        setLoading(false);
      }, 500);
    } catch (error) {
      message.error('Failed to load routes');
      console.error('Error loading routes:', error);
      setLoading(false);
    }
  }, [searchParams]);

  useEffect(() => {
    loadRoutes();
  }, [loadRoutes]);

  const handleSearch = (values: any) => {
    setSearchParams({
      ...searchParams,
      page: 1,
      name: values.name,
      status: values.status,
      zone: values.zone,
      area: values.area,
      assignedTo: values.assignedTo,
      isTemplate: values.isTemplate,
      templateCategory: values.templateCategory,
    });
  };

  const handleTableChange = (pagination: any, filters: any, sorter: any) => {
    setSearchParams({
      ...searchParams,
      page: pagination.current,
      pageSize: pagination.pageSize,
      sortBy: sorter.field,
      sortDirection: sorter.order === 'ascend' ? 'asc' : 'desc'
    });
  };

  const handleCreate = async (values: CreateRouteDto) => {
    try {
      // TODO: Replace with actual API call
      // await routeService.createRoute(values);
      message.success('Route created successfully');
      setIsCreateModalVisible(false);
      form.resetFields();
      loadRoutes();
    } catch (error) {
      message.error('Failed to create route');
      console.error('Error creating route:', error);
    }
  };

  const handleEdit = (route: RouteDto) => {
    setSelectedRoute(route);
    form.setFieldsValue(route);
    setIsCreateModalVisible(true);
  };

  const handleDelete = async (id: number) => {
    try {
      // TODO: Replace with actual API call
      // await routeService.deleteRoute(id);
      message.success('Route deleted successfully');
      loadRoutes();
    } catch (error) {
      message.error('Failed to delete route');
      console.error('Error deleting route:', error);
    }
  };

  const handleViewDetails = (route: RouteDto) => {
    setSelectedRoute(route);
    setIsDetailModalVisible(true);
  };

  const handleOptimize = (route: RouteDto) => {
    setSelectedRoute(route);
    setIsOptimizeModalVisible(true);
  };

  const handleCopyAsTemplate = async (route: RouteDto) => {
    try {
      // TODO: Replace with actual API call
      // await routeService.saveAsTemplate(route.id, `${route.name} Template`);
      message.success('Route saved as template successfully');
      loadRoutes();
    } catch (error) {
      message.error('Failed to save route as template');
      console.error('Error saving template:', error);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Active':
        return 'green';
      case 'Inactive':
        return 'red';
      default:
        return 'default';
    }
  };

  const getDifficultyColor = (rating?: number) => {
    if (!rating) return 'default';
    if (rating <= 2) return 'green';
    if (rating <= 3.5) return 'orange';
    return 'red';
  };

  const columns: ColumnsType<RouteDto> = [
    {
      title: 'ID',
      dataIndex: 'id',
      width: 80,
      sorter: true,
    },
    {
      title: 'Route Name',
      dataIndex: 'name',
      sorter: true,
      render: (text, record) => (
        <div>
          <Button type="link" onClick={() => handleViewDetails(record)}>
            {text}
          </Button>
          {record.isTemplate && <Tag color="blue" size="small">Template</Tag>}
        </div>
      ),
    },
    {
      title: 'Status',
      dataIndex: 'status',
      width: 120,
      render: (status) => (
        <Tag color={getStatusColor(status)}>{status}</Tag>
      ),
    },
    {
      title: 'Zone',
      dataIndex: 'zone',
      width: 100,
    },
    {
      title: 'Area',
      dataIndex: 'area',
      width: 150,
    },
    {
      title: 'Meters',
      dataIndex: 'totalMeters',
      width: 100,
      sorter: true,
    },
    {
      title: 'Distance (km)',
      dataIndex: 'estimatedDistance',
      width: 120,
      render: (distance) => distance ? `${distance} km` : '-',
    },
    {
      title: 'Duration (hrs)',
      dataIndex: 'estimatedDuration',
      width: 120,
      render: (duration) => duration ? `${(duration / 60).toFixed(1)} hrs` : '-',
    },
    {
      title: 'Difficulty',
      dataIndex: 'difficultyRating',
      width: 100,
      render: (rating) => rating ? (
        <Tag color={getDifficultyColor(rating)}>
          {rating.toFixed(1)}
        </Tag>
      ) : '-',
    },
    {
      title: 'Assigned To',
      dataIndex: 'assignedTo',
      width: 120,
      render: (assignedTo) => assignedTo || '-',
    },
    {
      title: 'Actions',
      width: 250,
      render: (_, record) => (
        <Space>
          <Tooltip title="View Details">
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() => handleViewDetails(record)}
            />
          </Tooltip>
          <Tooltip title="Edit">
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => handleEdit(record)}
            />
          </Tooltip>
          <Tooltip title="View Map">
            <Button
              type="text"
              icon={<EnvironmentOutlined />}
              onClick={() => message.info('Map view feature coming soon')}
            />
          </Tooltip>
          <Tooltip title="Optimize Route">
            <Button
              type="text"
              icon={<SettingOutlined />}
              onClick={() => handleOptimize(record)}
            />
          </Tooltip>
          {!record.isTemplate && (
            <Tooltip title="Save as Template">
              <Button
                type="text"
                icon={<CopyOutlined />}
                onClick={() => handleCopyAsTemplate(record)}
              />
            </Tooltip>
          )}
          <Popconfirm
            title="Are you sure you want to delete this route?"
            onConfirm={() => handleDelete(record.id)}
            okText="Yes"
            cancelText="No"
          >
            <Tooltip title="Delete">
              <Button
                type="text"
                danger
                icon={<DeleteOutlined />}
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div className="route-management">
      {/* Search Form */}
      <Card className="mb-4">
        <Form
          form={searchForm}
          layout="inline"
          onFinish={handleSearch}
          className="search-form"
        >
          <Row gutter={16} className="w-full">
            <Col span={6}>
              <Form.Item name="name">
                <Input placeholder="Route Name" />
              </Form.Item>
            </Col>
            <Col span={4}>
              <Form.Item name="status">
                <Select placeholder="Status" allowClear>
                  <Option value="Active">Active</Option>
                  <Option value="Inactive">Inactive</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={4}>
              <Form.Item name="zone">
                <Input placeholder="Zone" />
              </Form.Item>
            </Col>
            <Col span={4}>
              <Form.Item name="isTemplate">
                <Select placeholder="Type" allowClear>
                  <Option value={true}>Template</Option>
                  <Option value={false}>Regular Route</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item name="templateCategory">
                <Select placeholder="Template Category" allowClear>
                  <Option value="Residential">Residential</Option>
                  <Option value="Commercial">Commercial</Option>
                  <Option value="Industrial">Industrial</Option>
                  <Option value="Mixed">Mixed</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>
          <Row className="mt-4">
            <Col>
              <Space>
                <Button type="primary" htmlType="submit" icon={<SearchOutlined />}>
                  Search
                </Button>
                <Button
                  onClick={() => {
                    searchForm.resetFields();
                    setSearchParams({
                      page: 1,
                      pageSize: 10,
                      sortBy: 'CreatedAt',
                      sortDirection: 'desc'
                    });
                  }}
                >
                  Reset
                </Button>
              </Space>
            </Col>
          </Row>
        </Form>
      </Card>

      {/* Action Buttons */}
      <Card className="mb-4">
        <Space>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => setIsCreateModalVisible(true)}
          >
            Create Route
          </Button>
          <Button
            icon={<CopyOutlined />}
            onClick={() => message.info('Template management feature coming soon')}
          >
            Route Templates
          </Button>
        </Space>
      </Card>

      {/* Routes Table */}
      <Card>
        <Table
          columns={columns}
          dataSource={routes}
          rowKey="id"
          loading={loading}
          pagination={{
            current: searchParams.page,
            pageSize: searchParams.pageSize,
            total: total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `Total ${total} items`,
          }}
          onChange={handleTableChange}
          scroll={{ x: 1200 }}
        />
      </Card>

      {/* Create/Edit Route Modal */}
      <Modal
        title={selectedRoute ? 'Edit Route' : 'Create New Route'}
        open={isCreateModalVisible}
        onCancel={() => {
          setIsCreateModalVisible(false);
          setSelectedRoute(null);
          form.resetFields();
        }}
        footer={null}
        width={800}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleCreate}
          initialValues={{
            status: 'Active',
            isTemplate: false,
            difficultyRating: 2.5
          }}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="name"
                label="Route Name"
                rules={[{ required: true, message: 'Please enter route name' }]}
              >
                <Input placeholder="Enter route name" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="status" label="Status">
                <Select>
                  <Option value="Active">Active</Option>
                  <Option value="Inactive">Inactive</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item name="description" label="Description">
            <TextArea rows={3} placeholder="Enter route description" />
          </Form.Item>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item name="zone" label="Zone">
                <Input placeholder="Enter zone" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="area" label="Area">
                <Input placeholder="Enter area" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="assignedTo" label="Assigned To">
                <Input placeholder="Enter assignee" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item name="estimatedDuration" label="Estimated Duration (minutes)">
                <InputNumber
                  min={0}
                  className="w-full"
                  placeholder="Enter duration in minutes"
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="estimatedDistance" label="Estimated Distance (km)">
                <InputNumber
                  min={0}
                  step={0.1}
                  className="w-full"
                  placeholder="Enter distance in km"
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="difficultyRating" label="Difficulty Rating (1-5)">
                <InputNumber
                  min={1}
                  max={5}
                  step={0.1}
                  className="w-full"
                  placeholder="Enter difficulty rating"
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item name="isTemplate" label="Is Template" valuePropName="checked">
                <Switch />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="templateCategory" label="Template Category">
                <Select placeholder="Select category" allowClear>
                  <Option value="Residential">Residential</Option>
                  <Option value="Commercial">Commercial</Option>
                  <Option value="Industrial">Industrial</Option>
                  <Option value="Mixed">Mixed</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item name="notes" label="Notes">
            <TextArea rows={3} placeholder="Enter additional notes" />
          </Form.Item>

          <Form.Item className="text-right">
            <Space>
              <Button onClick={() => {
                setIsCreateModalVisible(false);
                setSelectedRoute(null);
                form.resetFields();
              }}>
                Cancel
              </Button>
              <Button type="primary" htmlType="submit">
                {selectedRoute ? 'Update' : 'Create'}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* Route Details Modal */}
      <Modal
        title="Route Details"
        open={isDetailModalVisible}
        onCancel={() => setIsDetailModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setIsDetailModalVisible(false)}>
            Close
          </Button>
        ]}
        width={900}
      >
        {selectedRoute && (
          <div>
            <Descriptions bordered column={2}>
              <Descriptions.Item label="Route Name">{selectedRoute.name}</Descriptions.Item>
              <Descriptions.Item label="Status">
                <Tag color={getStatusColor(selectedRoute.status)}>{selectedRoute.status}</Tag>
              </Descriptions.Item>
              <Descriptions.Item label="Type">
                {selectedRoute.isTemplate ? (
                  <Tag color="blue">Template</Tag>
                ) : (
                  <Tag>Regular Route</Tag>
                )}
              </Descriptions.Item>
              <Descriptions.Item label="Zone">{selectedRoute.zone}</Descriptions.Item>
              <Descriptions.Item label="Area">{selectedRoute.area}</Descriptions.Item>
              <Descriptions.Item label="Assigned To">{selectedRoute.assignedTo || '-'}</Descriptions.Item>
              <Descriptions.Item label="Total Meters">{selectedRoute.totalMeters}</Descriptions.Item>
              <Descriptions.Item label="Estimated Distance">{selectedRoute.estimatedDistance ? `${selectedRoute.estimatedDistance} km` : '-'}</Descriptions.Item>
              <Descriptions.Item label="Estimated Duration">{selectedRoute.estimatedDuration ? `${(selectedRoute.estimatedDuration / 60).toFixed(1)} hours` : '-'}</Descriptions.Item>
              <Descriptions.Item label="Difficulty Rating">
                {selectedRoute.difficultyRating ? (
                  <Tag color={getDifficultyColor(selectedRoute.difficultyRating)}>
                    {selectedRoute.difficultyRating.toFixed(1)}
                  </Tag>
                ) : '-'}
              </Descriptions.Item>
              <Descriptions.Item label="Template Category">{selectedRoute.templateCategory || '-'}</Descriptions.Item>
              <Descriptions.Item label="Created At">{selectedRoute.createdAt}</Descriptions.Item>
              <Descriptions.Item label="Created By">{selectedRoute.createdBy}</Descriptions.Item>
            </Descriptions>

            {selectedRoute.description && (
              <div className="mt-4">
                <Typography.Title level={5}>Description</Typography.Title>
                <Typography.Paragraph>{selectedRoute.description}</Typography.Paragraph>
              </div>
            )}
          </div>
        )}
      </Modal>

      {/* Route Optimization Modal */}
      <Modal
        title="Route Optimization"
        open={isOptimizeModalVisible}
        onCancel={() => setIsOptimizeModalVisible(false)}
        footer={[
          <Button key="cancel" onClick={() => setIsOptimizeModalVisible(false)}>
            Cancel
          </Button>,
          <Button key="optimize" type="primary" onClick={() => {
            message.success('Route optimization completed');
            setIsOptimizeModalVisible(false);
          }}>
            Optimize Route
          </Button>
        ]}
        width={600}
      >
        {selectedRoute && (
          <div>
            <Typography.Title level={5}>Optimize Route: {selectedRoute.name}</Typography.Title>
            <Form layout="vertical">
              <Form.Item label="Optimization Method">
                <Select defaultValue="distance" className="w-full">
                  <Option value="distance">Distance Optimal</Option>
                  <Option value="time">Time Optimal</Option>
                  <Option value="balanced">Balanced</Option>
                </Select>
              </Form.Item>
              
              <Form.Item label="Options" valuePropName="checked">
                <Space direction="vertical">
                  <Switch defaultChecked /> Preserve start and end points
                  <Switch /> Consider traffic conditions
                  <Switch /> Consider time windows
                </Space>
              </Form.Item>
              
              <Typography.Paragraph>
                This will optimize the order of waypoints in the route to minimize travel time and distance.
              </Typography.Paragraph>
            </Form>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default RouteManagement; 