'use client';

import React, { useState, useEffect } from 'react';
import { Card, Form, Select, DatePicker, Switch, Button, message, Table, Progress, Tag, Space, Row, Col, Divider, Statistic } from 'antd';
import { ExportOutlined, FileTextOutlined, FilePdfOutlined, FileExcelOutlined, DownloadOutlined, ClockCircleOutlined } from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import dayjs from 'dayjs';
import { reportsService } from '@/services/reports.service';
import type { ExportRequestDto, ExportJobDto, ExportHistorySearchDto } from '@/types/reports';

const { RangePicker } = DatePicker;
const { Option } = Select;

export default function ExportCenterPage() {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [exportJobs, setExportJobs] = useState<ExportJobDto[]>([]);
  const [exportHistory, setExportHistory] = useState<ExportJobDto[]>([]);

  const fetchExportHistory = async () => {
    try {
      const searchParams: ExportHistorySearchDto = {
        page: 1,
        pageSize: 10,
        sortBy: 'createdAt',
        sortDirection: 'desc'
      };
      const result = await reportsService.getExportHistory(searchParams);
      setExportHistory(result.exports);
    } catch (error) {
      message.error('Failed to load export history');
    }
  };

  useEffect(() => {
    fetchExportHistory();
  }, []);

  const handleExport = async (values: any) => {
    setLoading(true);
    try {
      const exportRequest: ExportRequestDto = {
        exportType: values.exportType,
        format: values.format,
        dateFrom: values.dateRange ? values.dateRange[0].toDate() : undefined,
        dateTo: values.dateRange ? values.dateRange[1].toDate() : undefined,
        filters: values.filters || {},
        includeCharts: values.includeCharts || false,
        includeDetails: values.includeDetails || false,
        recipientEmails: values.recipientEmails ? values.recipientEmails.split(',').map((email: string) => email.trim()) : undefined,
        scheduledTime: values.scheduledTime ? values.scheduledTime.toDate() : undefined
      };

      const job = await reportsService.createExportJob(exportRequest);
      message.success('Export job created successfully');
      
      setExportJobs(prev => [job, ...prev]);
      fetchExportHistory();
      form.resetFields();
    } catch (error) {
      message.error('Failed to create export job');
    } finally {
      setLoading(false);
    }
  };

  const handleQuickExport = async (exportType: string, format: 'pdf' | 'excel' | 'csv') => {
    setLoading(true);
    try {
      const exportRequest: ExportRequestDto = {
        exportType,
        format,
        dateFrom: dayjs().subtract(30, 'day').toDate(),
        dateTo: dayjs().toDate(),
        includeCharts: true,
        includeDetails: true
      };

      const job = await reportsService.createExportJob(exportRequest);
      message.success(`${exportType} export started`);
      setExportJobs(prev => [job, ...prev]);
    } catch (error) {
      message.error('Failed to start export');
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'success';
      case 'processing': return 'processing';
      case 'pending': return 'warning';
      case 'failed': return 'error';
      default: return 'default';
    }
  };

  const getFormatIcon = (format: string) => {
    switch (format) {
      case 'pdf': return <FilePdfOutlined style={{ color: '#ff4d4f' }} />;
      case 'excel': return <FileExcelOutlined style={{ color: '#52c41a' }} />;
      case 'csv': return <FileTextOutlined style={{ color: '#1890ff' }} />;
      default: return <FileTextOutlined />;
    }
  };

  const columns: ColumnsType<ExportJobDto> = [
    {
      title: 'Job ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: 'Export Type',
      dataIndex: 'exportType',
      key: 'exportType',
      width: 150,
    },
    {
      title: 'Format',
      dataIndex: 'format',
      key: 'format',
      width: 80,
      render: (format: string) => (
        <span>
          {getFormatIcon(format)} {format.toUpperCase()}
        </span>
      ),
    },
    {
      title: 'File Name',
      dataIndex: 'fileName',
      key: 'fileName',
      ellipsis: true,
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      width: 120,
      render: (status: string) => (
        <Tag color={getStatusColor(status)}>
          {status.toUpperCase()}
        </Tag>
      ),
    },
    {
      title: 'Progress',
      dataIndex: 'progress',
      key: 'progress',
      width: 120,
      render: (progress: number, record: ExportJobDto) => (
        <Progress 
          percent={progress} 
          size="small" 
          status={record.status === 'failed' ? 'exception' : undefined}
        />
      ),
    },
    {
      title: 'File Size',
      dataIndex: 'fileSize',
      key: 'fileSize',
      width: 100,
      render: (size: number) => size ? `${(size / 1024 / 1024).toFixed(2)} MB` : '-',
    },
    {
      title: 'Created At',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 150,
      render: (date: string) => dayjs(date).format('YYYY-MM-DD HH:mm'),
    },
    {
      title: 'Actions',
      key: 'actions',
      width: 100,
      render: (_, record: ExportJobDto) => (
        <Space>
          {record.status === 'completed' && record.downloadUrl && (
            <Button 
              type="link" 
              icon={<DownloadOutlined />}
              onClick={() => {
                if (record.downloadUrl) {
                  reportsService.downloadFile(record.downloadUrl, record.fileName);
                }
              }}
            >
              Download
            </Button>
          )}
        </Space>
      ),
    },
  ];

  const quickExportOptions = [
    { type: 'Operational Dashboard', label: 'Dashboard Report', icon: <FileTextOutlined /> },
    { type: 'Completion Report', label: 'Completion Report', icon: <FileTextOutlined /> },
    { type: 'Exception Analysis', label: 'Exception Report', icon: <FileTextOutlined /> },
    { type: 'User Performance', label: 'Performance Report', icon: <FileTextOutlined /> },
  ];

  return (
    <div className="p-6 space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <p className="text-gray-600">Create and manage data exports with flexible options</p>
        </div>
      </div>

      <Row gutter={[16, 16]}>
        <Col xs={24} sm={8}>
          <Card>
            <Statistic
              title="Total Exports Today"
              value={exportHistory.filter(job => dayjs(job.createdAt).isToday()).length}
              prefix={<ExportOutlined style={{ color: '#1890ff' }} />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={8}>
          <Card>
            <Statistic
              title="Active Jobs"
              value={exportHistory.filter(job => ['pending', 'processing'].includes(job.status)).length}
              prefix={<ClockCircleOutlined style={{ color: '#fa8c16' }} />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={8}>
          <Card>
            <Statistic
              title="Success Rate"
              value={85.6}
              suffix="%"
              precision={1}
              valueStyle={{ color: '#3f8600' }}
            />
          </Card>
        </Col>
      </Row>

      <Row gutter={[16, 16]}>
        <Col xs={24} lg={8}>
          <Card title="Quick Export" className="h-full">
            <div className="space-y-4">
              <p className="text-gray-600 text-sm">Export common reports with default settings</p>
              {quickExportOptions.map((option) => (
                <div key={option.type} className="border rounded-lg p-3">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center space-x-2">
                      {option.icon}
                      <span className="font-medium">{option.label}</span>
                    </div>
                  </div>
                  <div className="flex space-x-2">
                    <Button
                      size="small"
                      onClick={() => handleQuickExport(option.type, 'pdf')}
                      icon={<FilePdfOutlined />}
                    >
                      PDF
                    </Button>
                    <Button
                      size="small"
                      onClick={() => handleQuickExport(option.type, 'excel')}
                      icon={<FileExcelOutlined />}
                    >
                      Excel
                    </Button>
                    <Button
                      size="small"
                      onClick={() => handleQuickExport(option.type, 'csv')}
                      icon={<FileTextOutlined />}
                    >
                      CSV
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </Card>
        </Col>

        <Col xs={24} lg={16}>
          <Card title="Custom Export" className="h-full">
            <Form
              form={form}
              layout="vertical"
              onFinish={handleExport}
              initialValues={{
                includeCharts: true,
                includeDetails: true
              }}
            >
              <Row gutter={[16, 16]}>
                <Col xs={24} sm={12}>
                  <Form.Item
                    name="exportType"
                    label="Export Type"
                    rules={[{ required: true, message: 'Please select export type' }]}
                  >
                    <Select placeholder="Select report type">
                      <Option value="Operational Dashboard">Operational Dashboard</Option>
                      <Option value="Completion Report">Completion Report</Option>
                      <Option value="Exception Analysis">Exception Analysis</Option>
                      <Option value="User Performance">User Performance</Option>
                      <Option value="Custom Report">Custom Report</Option>
                    </Select>
                  </Form.Item>
                </Col>
                <Col xs={24} sm={12}>
                  <Form.Item
                    name="format"
                    label="Format"
                    rules={[{ required: true, message: 'Please select format' }]}
                  >
                    <Select placeholder="Select format">
                      <Option value="pdf">PDF</Option>
                      <Option value="excel">Excel</Option>
                      <Option value="csv">CSV</Option>
                    </Select>
                  </Form.Item>
                </Col>
                <Col xs={24} sm={12}>
                  <Form.Item name="dateRange" label="Date Range">
                    <RangePicker className="w-full" />
                  </Form.Item>
                </Col>
                <Col xs={24} sm={12}>
                  <Form.Item name="scheduledTime" label="Schedule Export (Optional)">
                    <DatePicker
                      showTime
                      placeholder="Select schedule time"
                      className="w-full"
                      disabledDate={(current) => current && current < dayjs().endOf('day')}
                    />
                  </Form.Item>
                </Col>
                <Col xs={24}>
                  <Form.Item name="recipientEmails" label="Email Recipients (Optional)">
                    <Select
                      mode="tags"
                      placeholder="Enter email addresses"
                      tokenSeparators={[',']}
                    />
                  </Form.Item>
                </Col>
                <Col xs={24} sm={12}>
                  <Form.Item name="includeCharts" valuePropName="checked" label=" ">
                    <Switch /> Include Charts
                  </Form.Item>
                </Col>
                <Col xs={24} sm={12}>
                  <Form.Item name="includeDetails" valuePropName="checked" label=" ">
                    <Switch /> Include Detailed Data
                  </Form.Item>
                </Col>
                <Col xs={24}>
                  <Form.Item>
                    <Button type="primary" htmlType="submit" loading={loading} icon={<ExportOutlined />}>
                      Create Export Job
                    </Button>
                  </Form.Item>
                </Col>
              </Row>
            </Form>
          </Card>
        </Col>
      </Row>

      {exportJobs.length > 0 && (
        <Card title="Current Export Jobs">
          <Table
            columns={columns}
            dataSource={exportJobs}
            rowKey="id"
            pagination={false}
            size="small"
          />
        </Card>
      )}

      <Divider />

      <Card title="Recent Export History">
        <Table
          columns={columns}
          dataSource={exportHistory}
          rowKey="id"
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} exports`,
          }}
        />
      </Card>
    </div>
  );
} 