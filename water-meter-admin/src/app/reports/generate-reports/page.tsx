'use client';

import React, { useState, useEffect } from 'react';
import { Card, Form, Select, Input, DatePicker, Button, message, Steps, Row, Col, Switch, Radio, InputNumber, Modal } from 'antd';
import { FileTextOutlined, SettingOutlined, EyeOutlined, DownloadOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';
import { reportsService } from '@/services/reports.service';
import type { ReportTemplateDto, GenerateReportRequestDto, GeneratedReportDto, TemplateSearchDto } from '@/types/reports';

const { RangePicker } = DatePicker;
const { Option } = Select;
const { TextArea } = Input;

export default function GenerateReportsPage() {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [currentStep, setCurrentStep] = useState(0);
  const [templates, setTemplates] = useState<ReportTemplateDto[]>([]);
  const [selectedTemplate, setSelectedTemplate] = useState<ReportTemplateDto | null>(null);
  const [generatedReport, setGeneratedReport] = useState<GeneratedReportDto | null>(null);
  const [previewVisible, setPreviewVisible] = useState(false);

  const fetchTemplates = async () => {
    try {
      const searchParams: TemplateSearchDto = {
        page: 1,
        pageSize: 50,
        isActive: true,
        sortBy: 'name'
      };
      const result = await reportsService.getReportTemplates(searchParams);
      setTemplates(result.templates);
    } catch (error) {
      message.error('Failed to load report templates');
    }
  };

  useEffect(() => {
    fetchTemplates();
  }, []);

  const handleTemplateSelect = (templateId: number) => {
    const template = templates.find(t => t.id === templateId);
    setSelectedTemplate(template || null);
    
    if (template) {
      const defaultValues: any = {
        templateId,
        reportType: template.type,
        format: 'pdf',
        includeCharts: true,
        includeDetails: true,
        title: `${template.name} - ${dayjs().format('YYYY-MM-DD')}`
      };

      template.parameters.forEach(param => {
        if (param.defaultValue !== undefined) {
          defaultValues[param.name] = param.defaultValue;
        }
      });

      form.setFieldsValue(defaultValues);
      setCurrentStep(1);
    }
  };

  const handleGenerate = async (values: any) => {
    if (!selectedTemplate) {
      message.error('Please select a template first');
      return;
    }

    setLoading(true);
    try {
      const parameters: { [key: string]: any } = {};
      selectedTemplate.parameters.forEach(param => {
        if (values[param.name] !== undefined) {
          parameters[param.name] = values[param.name];
        }
      });

      const generateRequest: GenerateReportRequestDto = {
        templateId: selectedTemplate.id,
        reportType: values.reportType,
        format: values.format,
        parameters,
        dateFrom: values.dateRange ? values.dateRange[0].toDate() : undefined,
        dateTo: values.dateRange ? values.dateRange[1].toDate() : undefined,
        filters: values.filters || {},
        includeCharts: values.includeCharts || false,
        includeDetails: values.includeDetails || false,
        title: values.title,
        description: values.description
      };

      const report = await reportsService.generateReport(generateRequest);
      setGeneratedReport(report);
      setCurrentStep(2);
      message.success('Report generated successfully');
    } catch (error) {
      message.error('Failed to generate report');
    } finally {
      setLoading(false);
    }
  };

  const handleDownload = () => {
    if (generatedReport?.downloadUrl) {
      reportsService.downloadFile(generatedReport.downloadUrl, generatedReport.fileName);
    }
  };

  const handleReset = () => {
    form.resetFields();
    setSelectedTemplate(null);
    setGeneratedReport(null);
    setCurrentStep(0);
  };

  const renderTemplateCard = (template: ReportTemplateDto) => (
    <Card
      key={template.id}
      hoverable
      className="mb-4"
      onClick={() => handleTemplateSelect(template.id)}
      style={{ 
        border: selectedTemplate?.id === template.id ? '2px solid #1890ff' : '1px solid #d9d9d9' 
      }}
    >
      <div className="flex items-start justify-between">
        <div className="flex-1">
          <h3 className="font-semibold text-lg mb-2">{template.name}</h3>
          <p className="text-gray-600 mb-2">{template.description}</p>
          <div className="flex items-center space-x-4 text-sm text-gray-500">
            <span>Type: {template.type}</span>
            <span>Category: {template.category}</span>
            <span>Used: {template.usageCount} times</span>
          </div>
        </div>
        <div className="text-right">
          <div className="text-sm text-gray-500">
            Created: {dayjs(template.createdAt).format('YYYY-MM-DD')}
          </div>
        </div>
      </div>
    </Card>
  );

  const renderParameterField = (param: any) => {
    const commonProps = {
      name: param.name,
      label: param.label,
      rules: param.required ? [{ required: true, message: `${param.label} is required` }] : []
    };

    switch (param.type) {
      case 'string':
        return (
          <Form.Item {...commonProps}>
            <Input placeholder={`Enter ${param.label}`} />
          </Form.Item>
        );
      case 'number':
        return (
          <Form.Item {...commonProps}>
            <InputNumber className="w-full" placeholder={`Enter ${param.label}`} />
          </Form.Item>
        );
      case 'date':
        return (
          <Form.Item {...commonProps}>
            <DatePicker className="w-full" placeholder={`Select ${param.label}`} />
          </Form.Item>
        );
      case 'boolean':
        return (
          <Form.Item {...commonProps} valuePropName="checked">
            <Switch />
          </Form.Item>
        );
      case 'select':
        return (
          <Form.Item {...commonProps}>
            <Select placeholder={`Select ${param.label}`}>
              {param.options?.map((option: string) => (
                <Option key={option} value={option}>{option}</Option>
              ))}
            </Select>
          </Form.Item>
        );
      default:
        return (
          <Form.Item {...commonProps}>
            <Input placeholder={`Enter ${param.label}`} />
          </Form.Item>
        );
    }
  };

  const steps = [
    { title: 'Select Template', icon: <FileTextOutlined /> },
    { title: 'Configure & Generate', icon: <SettingOutlined /> },
    { title: 'Download', icon: <DownloadOutlined /> }
  ];

  return (
    <div className="p-6 space-y-6">
      <div className="flex justify-end items-center">
        <Button onClick={handleReset} disabled={currentStep === 0}>
          Start Over
        </Button>
      </div>

      <Card>
        <Steps current={currentStep} items={steps} />
      </Card>

      <Form form={form} layout="vertical" onFinish={handleGenerate}>
        {currentStep === 0 && (
          <Card title="Select Report Template">
            <div className="grid gap-4">
              {templates.map(renderTemplateCard)}
              {templates.length === 0 && (
                <div className="text-center py-8 text-gray-500">
                  No templates available. Please create templates first.
                </div>
              )}
            </div>
          </Card>
        )}

        {currentStep === 1 && selectedTemplate && (
          <Card 
            title={`Configure Parameters - ${selectedTemplate.name}`}
            extra={
              <Button type="primary" htmlType="submit" loading={loading}>
                Generate Report
              </Button>
            }
          >
            <Row gutter={[16, 16]}>
              <Col xs={24} lg={12}>
                <h3 className="font-semibold mb-4">Template Parameters</h3>
                {selectedTemplate.parameters.map((param, index) => (
                  <div key={index}>
                    {renderParameterField(param)}
                  </div>
                ))}
                
                <Form.Item name="title" label="Report Title" rules={[{ required: true }]}>
                  <Input placeholder="Enter report title" />
                </Form.Item>
                <Form.Item name="description" label="Description">
                  <TextArea rows={3} placeholder="Enter report description" />
                </Form.Item>
              </Col>
              <Col xs={24} lg={12}>
                <h3 className="font-semibold mb-4">General Settings</h3>
                <Form.Item name="dateRange" label="Date Range">
                  <RangePicker className="w-full" />
                </Form.Item>
                <Form.Item name="format" label="Output Format" initialValue="pdf">
                  <Radio.Group>
                    <Radio value="pdf">PDF</Radio>
                    <Radio value="excel">Excel</Radio>
                    <Radio value="csv">CSV</Radio>
                  </Radio.Group>
                </Form.Item>
                <Form.Item name="includeCharts" valuePropName="checked" initialValue={true}>
                  <Switch /> Include Charts
                </Form.Item>
                <Form.Item name="includeDetails" valuePropName="checked" initialValue={true}>
                  <Switch /> Include Detailed Data
                </Form.Item>
              </Col>
            </Row>
          </Card>
        )}

        {currentStep === 2 && generatedReport && (
          <Card title="Report Generated Successfully">
            <div className="text-center py-8">
              <div className="mb-6">
                <FileTextOutlined style={{ fontSize: 64, color: '#52c41a' }} />
              </div>
              <h3 className="text-xl font-semibold mb-2">{generatedReport.title}</h3>
              <p className="text-gray-600 mb-4">
                Generated on {dayjs(generatedReport.generatedAt).format('YYYY-MM-DD HH:mm')}
              </p>
              <div className="space-y-2 mb-6">
                <div><strong>File Name:</strong> {generatedReport.fileName}</div>
                <div><strong>Format:</strong> {generatedReport.format.toUpperCase()}</div>
                <div><strong>File Size:</strong> {(generatedReport.fileSize / 1024 / 1024).toFixed(2)} MB</div>
              </div>
              <div className="space-x-4">
                <Button type="primary" icon={<DownloadOutlined />} onClick={handleDownload}>
                  Download Report
                </Button>
                <Button icon={<EyeOutlined />} onClick={() => setPreviewVisible(true)}>
                  Preview
                </Button>
                <Button onClick={handleReset}>
                  Generate Another
                </Button>
              </div>
            </div>
          </Card>
        )}
      </Form>

      <Modal
        title="Report Preview"
        open={previewVisible}
        onCancel={() => setPreviewVisible(false)}
        footer={[
          <Button key="close" onClick={() => setPreviewVisible(false)}>
            Close
          </Button>,
          <Button key="download" type="primary" icon={<DownloadOutlined />} onClick={handleDownload}>
            Download
          </Button>
        ]}
        width={800}
      >
        <div className="text-center py-8">
          <p className="text-gray-600">Preview functionality will be implemented with actual report generation.</p>
          <div className="mt-4 p-4 bg-gray-50 rounded">
            <p><strong>Report:</strong> {generatedReport?.title}</p>
            <p><strong>Format:</strong> {generatedReport?.format.toUpperCase()}</p>
          </div>
        </div>
      </Modal>
    </div>
  );
} 