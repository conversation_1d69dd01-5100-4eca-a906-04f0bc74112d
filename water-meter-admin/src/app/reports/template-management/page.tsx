'use client';

import React, { useState, useEffect } from 'react';
import { Card, Table, Button, message, Tag, Space, Row, Col, Input, Select, Modal, Form, Switch, Tooltip, Popconfirm, Divider } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined, EyeOutlined, CopyOutlined, SearchOutlined, ReloadOutlined, FileTextOutlined, SettingOutlined } from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import dayjs from 'dayjs';
import { reportsService } from '@/services/reports.service';
import type { ReportTemplateDto, CreateReportTemplateDto, UpdateReportTemplateDto, TemplateSearchDto, TemplateParameterDto } from '@/types/reports';

const { Search } = Input;
const { Option } = Select;
const { TextArea } = Input;

export default function TemplateManagementPage() {
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<ReportTemplateDto[]>([]);
  const [total, setTotal] = useState(0);
  const [searchParams, setSearchParams] = useState<TemplateSearchDto>({
    page: 1,
    pageSize: 10,
    sortBy: 'updatedAt',
    sortDirection: 'desc'
  });

  // Modal states
  const [modalVisible, setModalVisible] = useState(false);
  const [previewVisible, setPreviewVisible] = useState(false);
  const [editingTemplate, setEditingTemplate] = useState<ReportTemplateDto | null>(null);
  const [previewTemplate, setPreviewTemplate] = useState<ReportTemplateDto | null>(null);

  const [form] = Form.useForm();
  const [parameterForm] = Form.useForm();

  const fetchData = async () => {
    setLoading(true);
    try {
      const result = await reportsService.getReportTemplates(searchParams);
      setData(result.templates);
      setTotal(result.totalCount);
    } catch (error) {
      message.error('Failed to load templates');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, [searchParams]);

  const handleSearch = (value: string) => {
    setSearchParams(prev => ({ ...prev, name: value, page: 1 }));
  };

  const handleReset = () => {
    setSearchParams({
      page: 1,
      pageSize: 10,
      sortBy: 'updatedAt',
      sortDirection: 'desc'
    });
  };

  const handleCreate = () => {
    setEditingTemplate(null);
    form.resetFields();
    parameterForm.resetFields();
    setModalVisible(true);
  };

  const handleEdit = (template: ReportTemplateDto) => {
    setEditingTemplate(template);
    
    // Set form values
    form.setFieldsValue({
      name: template.name,
      type: template.type,
      description: template.description,
      category: template.category,
      isDefault: template.isDefault,
      templateContent: template.templateContent
    });

    // Set parameter form values
    if (template.parameters && template.parameters.length > 0) {
      parameterForm.setFieldsValue({
        parameters: template.parameters
      });
    }

    setModalVisible(true);
  };

  const handleDelete = async (id: number) => {
    try {
      await reportsService.deleteReportTemplate(id);
      message.success('Template deleted successfully');
      fetchData();
    } catch (error) {
      message.error('Failed to delete template');
    }
  };

  const handlePreview = (template: ReportTemplateDto) => {
    setPreviewTemplate(template);
    setPreviewVisible(true);
  };

  const handleCopy = async (template: ReportTemplateDto) => {
    const newTemplate: CreateReportTemplateDto = {
      name: `${template.name} (Copy)`,
      type: template.type,
      description: `Copy of ${template.description}`,
      templateContent: template.templateContent,
      parameters: template.parameters,
      category: template.category,
      isDefault: false
    };

    try {
      await reportsService.createReportTemplate(newTemplate);
      message.success('Template copied successfully');
      fetchData();
    } catch (error) {
      message.error('Failed to copy template');
    }
  };

  const handleSubmit = async () => {
    try {
      const formValues = await form.validateFields();
      let parameters: TemplateParameterDto[] = [];

      try {
        const paramValues = await parameterForm.validateFields();
        parameters = paramValues.parameters || [];
      } catch (error) {
        // Parameters form validation failed, but we can still proceed with empty parameters
      }

      const templateData = {
        ...formValues,
        parameters
      };

      if (editingTemplate) {
        // Update existing template
        const updateData: UpdateReportTemplateDto = {
          ...templateData,
          isActive: editingTemplate.isActive
        };
        await reportsService.updateReportTemplate(editingTemplate.id, updateData);
        message.success('Template updated successfully');
      } else {
        // Create new template
        const createData: CreateReportTemplateDto = templateData;
        await reportsService.createReportTemplate(createData);
        message.success('Template created successfully');
      }

      setModalVisible(false);
      fetchData();
    } catch (error) {
      message.error('Failed to save template');
    }
  };

  const columns: ColumnsType<ReportTemplateDto> = [
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
      width: 200,
      ellipsis: true,
      render: (name: string, record: ReportTemplateDto) => (
        <div>
          <div className="font-medium">{name}</div>
          {record.isDefault && <Tag size="small" color="blue">Default</Tag>}
        </div>
      ),
    },
    {
      title: 'Type',
      dataIndex: 'type',
      key: 'type',
      width: 150,
    },
    {
      title: 'Category',
      dataIndex: 'category',
      key: 'category',
      width: 120,
    },
    {
      title: 'Description',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
      render: (description: string) => (
        <Tooltip title={description}>
          {description}
        </Tooltip>
      ),
    },
    {
      title: 'Status',
      dataIndex: 'isActive',
      key: 'isActive',
      width: 100,
      render: (isActive: boolean) => (
        <Tag color={isActive ? 'success' : 'default'}>
          {isActive ? 'Active' : 'Inactive'}
        </Tag>
      ),
      filters: [
        { text: 'Active', value: true },
        { text: 'Inactive', value: false },
      ],
    },
    {
      title: 'Parameters',
      dataIndex: 'parameters',
      key: 'parameters',
      width: 100,
      render: (parameters: TemplateParameterDto[]) => (
        <span>{parameters?.length || 0}</span>
      ),
    },
    {
      title: 'Usage Count',
      dataIndex: 'usageCount',
      key: 'usageCount',
      width: 100,
      sorter: true,
    },
    {
      title: 'Created By',
      dataIndex: 'createdBy',
      key: 'createdBy',
      width: 120,
    },
    {
      title: 'Updated At',
      dataIndex: 'updatedAt',
      key: 'updatedAt',
      width: 150,
      render: (date: string) => dayjs(date).format('YYYY-MM-DD HH:mm'),
      sorter: true,
    },
    {
      title: 'Actions',
      key: 'actions',
      width: 200,
      fixed: 'right',
      render: (_, record: ReportTemplateDto) => (
        <Space size="small">
          <Tooltip title="Preview">
            <Button 
              type="text" 
              icon={<EyeOutlined />} 
              onClick={() => handlePreview(record)}
              size="small"
            />
          </Tooltip>
          <Tooltip title="Edit">
            <Button 
              type="text" 
              icon={<EditOutlined />} 
              onClick={() => handleEdit(record)}
              size="small"
            />
          </Tooltip>
          <Tooltip title="Copy">
            <Button 
              type="text" 
              icon={<CopyOutlined />} 
              onClick={() => handleCopy(record)}
              size="small"
            />
          </Tooltip>
          <Popconfirm
            title="Are you sure you want to delete this template?"
            onConfirm={() => handleDelete(record.id)}
            okText="Yes"
            cancelText="No"
          >
            <Tooltip title="Delete">
              <Button 
                type="text" 
                danger
                icon={<DeleteOutlined />}
                size="small"
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div className="p-6 space-y-6">
      {/* Page Header */}
      <div className="flex justify-end items-center">
        <Space>
          <Button icon={<ReloadOutlined />} onClick={fetchData}>
            Refresh
          </Button>
          <Button type="primary" icon={<PlusOutlined />} onClick={handleCreate}>
            Create Template
          </Button>
        </Space>
      </div>

      {/* Search and Filter */}
      <Card>
        <Row gutter={[16, 16]} align="middle">
          <Col xs={24} sm={8} md={6}>
            <Search
              placeholder="Search by name"
              allowClear
              onSearch={handleSearch}
            />
          </Col>
          <Col xs={24} sm={8} md={4}>
            <Select
              placeholder="Filter by type"
              allowClear
              className="w-full"
              onChange={(value) => {
                setSearchParams(prev => ({ ...prev, type: value, page: 1 }));
              }}
            >
              <Option value="Operational Dashboard">Dashboard</Option>
              <Option value="Completion Report">Completion</Option>
              <Option value="Exception Analysis">Exception</Option>
              <Option value="User Performance">Performance</Option>
            </Select>
          </Col>
          <Col xs={24} sm={8} md={4}>
            <Select
              placeholder="Filter by category"
              allowClear
              className="w-full"
              onChange={(value) => {
                setSearchParams(prev => ({ ...prev, category: value, page: 1 }));
              }}
            >
              <Option value="Operational">Operational</Option>
              <Option value="Performance">Performance</Option>
              <Option value="Compliance">Compliance</Option>
              <Option value="Analytics">Analytics</Option>
            </Select>
          </Col>
          <Col xs={24} sm={12} md={4}>
            <Select
              placeholder="Filter by status"
              allowClear
              className="w-full"
              onChange={(value) => {
                setSearchParams(prev => ({ ...prev, isActive: value, page: 1 }));
              }}
            >
              <Option value={true}>Active</Option>
              <Option value={false}>Inactive</Option>
            </Select>
          </Col>
          <Col xs={24} sm={12} md={6}>
            <Space>
              <Button icon={<SearchOutlined />} onClick={() => fetchData()}>
                Search
              </Button>
              <Button onClick={handleReset}>
                Reset
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* Templates Table */}
      <Card>
        <Table
          columns={columns}
          dataSource={data}
          rowKey="id"
          loading={loading}
          pagination={{
            current: searchParams.page,
            pageSize: searchParams.pageSize,
            total: total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} templates`,
            onChange: (page, size) => {
              setSearchParams(prev => ({
                ...prev,
                page,
                pageSize: size || 10
              }));
            },
          }}
          scroll={{ x: 1200 }}
        />
      </Card>

      {/* Create/Edit Modal */}
      <Modal
        title={editingTemplate ? 'Edit Template' : 'Create Template'}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        onOk={handleSubmit}
        width={800}
        destroyOnClose
      >
        <Form form={form} layout="vertical">
          <Row gutter={[16, 16]}>
            <Col xs={24} sm={12}>
              <Form.Item
                name="name"
                label="Template Name"
                rules={[{ required: true, message: 'Please enter template name' }]}
              >
                <Input placeholder="Enter template name" />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12}>
              <Form.Item
                name="type"
                label="Template Type"
                rules={[{ required: true, message: 'Please select template type' }]}
              >
                <Select placeholder="Select template type">
                  <Option value="Operational Dashboard">Operational Dashboard</Option>
                  <Option value="Completion Report">Completion Report</Option>
                  <Option value="Exception Analysis">Exception Analysis</Option>
                  <Option value="User Performance">User Performance</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col xs={24} sm={12}>
              <Form.Item
                name="category"
                label="Category"
                rules={[{ required: true, message: 'Please enter category' }]}
              >
                <Select placeholder="Select category">
                  <Option value="Operational">Operational</Option>
                  <Option value="Performance">Performance</Option>
                  <Option value="Compliance">Compliance</Option>
                  <Option value="Analytics">Analytics</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col xs={24} sm={12}>
              <Form.Item name="isDefault" valuePropName="checked">
                <Switch /> Set as Default Template
              </Form.Item>
            </Col>
            <Col xs={24}>
              <Form.Item
                name="description"
                label="Description"
                rules={[{ required: true, message: 'Please enter description' }]}
              >
                <TextArea rows={3} placeholder="Enter template description" />
              </Form.Item>
            </Col>
            <Col xs={24}>
              <Form.Item
                name="templateContent"
                label="Template Content"
                rules={[{ required: true, message: 'Please enter template content' }]}
              >
                <TextArea rows={6} placeholder="Enter template content (JSON format)" />
              </Form.Item>
            </Col>
          </Row>
        </Form>

        <Divider>Template Parameters</Divider>
        
        <Form form={parameterForm} layout="vertical">
          <Form.List name="parameters">
            {(fields, { add, remove }) => (
              <>
                {fields.map(({ key, name, ...restField }) => (
                  <Row key={key} gutter={[8, 8]} align="middle">
                    <Col xs={24} sm={6}>
                      <Form.Item
                        {...restField}
                        name={[name, 'name']}
                        rules={[{ required: true, message: 'Missing parameter name' }]}
                      >
                        <Input placeholder="Parameter name" />
                      </Form.Item>
                    </Col>
                    <Col xs={24} sm={4}>
                      <Form.Item
                        {...restField}
                        name={[name, 'type']}
                        rules={[{ required: true, message: 'Missing type' }]}
                      >
                        <Select placeholder="Type">
                          <Option value="string">String</Option>
                          <Option value="number">Number</Option>
                          <Option value="date">Date</Option>
                          <Option value="boolean">Boolean</Option>
                          <Option value="select">Select</Option>
                        </Select>
                      </Form.Item>
                    </Col>
                    <Col xs={24} sm={6}>
                      <Form.Item
                        {...restField}
                        name={[name, 'label']}
                        rules={[{ required: true, message: 'Missing label' }]}
                      >
                        <Input placeholder="Display label" />
                      </Form.Item>
                    </Col>
                    <Col xs={24} sm={3}>
                      <Form.Item {...restField} name={[name, 'required']} valuePropName="checked">
                        <Switch size="small" /> Required
                      </Form.Item>
                    </Col>
                    <Col xs={24} sm={4}>
                      <Form.Item {...restField} name={[name, 'defaultValue']}>
                        <Input placeholder="Default value" />
                      </Form.Item>
                    </Col>
                    <Col xs={24} sm={1}>
                      <Button type="text" danger onClick={() => remove(name)} icon={<DeleteOutlined />} />
                    </Col>
                  </Row>
                ))}
                <Form.Item>
                  <Button type="dashed" onClick={() => add()} block icon={<PlusOutlined />}>
                    Add Parameter
                  </Button>
                </Form.Item>
              </>
            )}
          </Form.List>
        </Form>
      </Modal>

      {/* Preview Modal */}
      <Modal
        title="Template Preview"
        open={previewVisible}
        onCancel={() => setPreviewVisible(false)}
        footer={[
          <Button key="close" onClick={() => setPreviewVisible(false)}>
            Close
          </Button>
        ]}
        width={800}
      >
        {previewTemplate && (
          <div className="space-y-4">
            <Row gutter={[16, 16]}>
              <Col xs={24} sm={12}>
                <div><strong>Name:</strong> {previewTemplate.name}</div>
              </Col>
              <Col xs={24} sm={12}>
                <div><strong>Type:</strong> {previewTemplate.type}</div>
              </Col>
              <Col xs={24} sm={12}>
                <div><strong>Category:</strong> {previewTemplate.category}</div>
              </Col>
              <Col xs={24} sm={12}>
                <div><strong>Status:</strong> {previewTemplate.isActive ? 'Active' : 'Inactive'}</div>
              </Col>
              <Col xs={24}>
                <div><strong>Description:</strong> {previewTemplate.description}</div>
              </Col>
            </Row>
            
            <Divider>Parameters</Divider>
            {previewTemplate.parameters && previewTemplate.parameters.length > 0 ? (
              <div className="space-y-2">
                {previewTemplate.parameters.map((param, index) => (
                  <div key={index} className="p-3 bg-gray-50 rounded">
                    <div className="flex justify-between items-start">
                      <div>
                        <span className="font-medium">{param.label}</span>
                        <span className="ml-2 text-gray-500">({param.name})</span>
                      </div>
                      <div className="text-right">
                        <Tag>{param.type}</Tag>
                        {param.required && <Tag color="red">Required</Tag>}
                      </div>
                    </div>
                    {param.defaultValue && (
                      <div className="text-sm text-gray-600 mt-1">
                        Default: {param.defaultValue}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-gray-500 text-center py-4">No parameters defined</div>
            )}
            
            <Divider>Template Content</Divider>
            <pre className="bg-gray-50 p-4 rounded text-sm overflow-auto max-h-64">
              {previewTemplate.templateContent}
            </pre>
          </div>
        )}
      </Modal>
    </div>
  );
} 