'use client';

import React, { useState, useEffect, useCallback } from 'react';
import {
  Table, Button, Form, Input, Select, Space, Card, Row, Col, 
  Statistic, Progress, message, Tag, Avatar, Rate
} from 'antd';
import {
  SearchOutlined, ExportOutlined, ReloadOutlined, FilterOutlined,
  TrophyOutlined, UserOutlined, CheckCircleOutlined, ClockCircleOutlined,
  LineChartOutlined, StarOutlined
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';
import { reportsService } from '@/services/reports.service';
import {
  UserPerformanceDto,
  UserPerformanceSearchDto,
  UserPerformanceSearchResultDto
} from '@/types/reports';

dayjs.extend(relativeTime);

const { Option } = Select;

const UserPerformance: React.FC = () => {
  const [users, setUsers] = useState<UserPerformanceDto[]>([]);
  const [loading, setLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [totalCount, setTotalCount] = useState(0);
  const [searchParams, setSearchParams] = useState<UserPerformanceSearchDto>({
    page: 1,
    pageSize: 10,
    sortBy: 'completionRate',
    sortDirection: 'desc'
  });

  const [form] = Form.useForm();

  // Load user performance data
  const loadUserPerformance = useCallback(async () => {
    setLoading(true);
    try {
      const searchDto: UserPerformanceSearchDto = {
        ...searchParams,
        page: currentPage,
        pageSize
      };
      
      const result: UserPerformanceSearchResultDto = await reportsService.getUserPerformance(searchDto);
      setUsers(result.users);
      setTotalCount(result.totalCount);
    } catch (error) {
      message.error('Failed to load user performance data');
      console.error('Load user performance error:', error);
    } finally {
      setLoading(false);
    }
  }, [currentPage, pageSize, searchParams]);

  useEffect(() => {
    loadUserPerformance();
  }, [loadUserPerformance]);

  // Handle search
  const handleSearch = (values: any) => {
    const searchDto: UserPerformanceSearchDto = {
      ...searchParams,
      page: 1,
      ...values,
      dateFrom: values.dateRange?.[0] ? dayjs(values.dateRange[0]).toDate() : undefined,
      dateTo: values.dateRange?.[1] ? dayjs(values.dateRange[1]).toDate() : undefined,
    };
    delete (searchDto as any).dateRange;
    
    setSearchParams(searchDto);
    setCurrentPage(1);
  };

  // Reset search
  const handleResetSearch = () => {
    form.resetFields();
    setSearchParams({
      page: 1,
      pageSize,
      sortBy: 'completionRate',
      sortDirection: 'desc'
    });
    setCurrentPage(1);
  };

  // Get performance rating config
  const getPerformanceRating = (rating: string) => {
    switch (rating) {
      case 'Excellent':
        return { color: 'green', stars: 5, icon: '🏆' };
      case 'Good':
        return { color: 'blue', stars: 4, icon: '⭐' };
      case 'Average':
        return { color: 'orange', stars: 3, icon: '👍' };
      case 'Needs Improvement':
        return { color: 'red', stars: 2, icon: '📈' };
      default:
        return { color: 'gray', stars: 1, icon: '❓' };
    }
  };

  // Get completion rate color
  const getCompletionRateColor = (rate: number) => {
    if (rate >= 95) return '#52c41a';
    if (rate >= 85) return '#1890ff';
    if (rate >= 75) return '#faad14';
    return '#ff4d4f';
  };

  // Table columns
  const columns: ColumnsType<UserPerformanceDto> = [
    {
      title: 'Rank',
      key: 'rank',
      width: 70,
      render: (_, __, index) => {
        const rank = (currentPage - 1) * pageSize + index + 1;
        let className = 'w-8 h-8 rounded-full flex items-center justify-center text-white text-sm font-bold';
        
        if (rank === 1) className += ' bg-yellow-500';
        else if (rank === 2) className += ' bg-gray-400';
        else if (rank === 3) className += ' bg-yellow-600';
        else className += ' bg-blue-500';

        return (
          <div className={className}>
            {rank}
          </div>
        );
      },
    },
    {
      title: 'User',
      key: 'user',
      fixed: 'left',
      width: 200,
      render: (_, record) => (
        <div className="flex items-center space-x-3">
          <Avatar size="large" icon={<UserOutlined />} />
          <div>
            <div className="font-medium">{record.fullName}</div>
            <div className="text-sm text-gray-500">@{record.userName}</div>
            <div className="text-sm text-gray-500">{record.department}</div>
          </div>
        </div>
      ),
    },
    {
      title: 'Performance Rating',
      dataIndex: 'performanceRating',
      key: 'performanceRating',
      width: 150,
      render: (rating: string) => {
        const ratingConfig = getPerformanceRating(rating);
        return (
          <div className="text-center">
            <Tag color={ratingConfig.color} className="mb-1">
              {ratingConfig.icon} {rating}
            </Tag>
            <div>
              <Rate disabled value={ratingConfig.stars} className="text-sm" />
            </div>
          </div>
        );
      },
    },
    {
      title: 'Tasks',
      children: [
        {
          title: 'Assigned',
          dataIndex: 'tasksAssigned',
          key: 'tasksAssigned',
          width: 80,
          sorter: true,
          render: (value: number) => value.toLocaleString(),
        },
        {
          title: 'Completed',
          dataIndex: 'tasksCompleted',
          key: 'tasksCompleted',
          width: 80,
          sorter: true,
          render: (value: number) => (
            <span className="text-green-600 font-medium">
              {value.toLocaleString()}
            </span>
          ),
        },
        {
          title: 'Overdue',
          dataIndex: 'tasksOverdue',
          key: 'tasksOverdue',
          width: 80,
          sorter: true,
          render: (value: number) => (
            <span className="text-red-600 font-medium">
              {value.toLocaleString()}
            </span>
          ),
        },
      ],
    },
    {
      title: 'Completion Rate',
      dataIndex: 'completionRate',
      key: 'completionRate',
      sorter: true,
      width: 140,
      render: (rate: number) => (
        <div>
          <Progress
            percent={rate}
            size="small"
            strokeColor={getCompletionRateColor(rate)}
            showInfo={false}
          />
          <div className="text-center mt-1">
            <span className="font-bold" style={{ color: getCompletionRateColor(rate) }}>
              {rate != null ? rate.toFixed(1) : '0.0'}%
            </span>
          </div>
        </div>
      ),
    },
    {
      title: 'Quality Score',
      dataIndex: 'qualityScore',
      key: 'qualityScore',
      sorter: true,
      width: 120,
      render: (score: number) => (
        <div className="text-center">
          <div className="text-lg font-bold text-blue-600">{score != null ? score.toFixed(1) : '0.0'}</div>
          <Rate disabled value={score / 20} allowHalf className="text-xs" />
        </div>
      ),
    },
    {
      title: 'Efficiency',
      dataIndex: 'efficiency',
      key: 'efficiency',
      sorter: true,
      width: 120,
      render: (efficiency: number) => (
        <div>
          <Progress
            type="circle"
            percent={efficiency}
            size={60}
            strokeColor={efficiency >= 90 ? '#52c41a' : efficiency >= 80 ? '#1890ff' : '#faad14'}
          />
        </div>
      ),
    },
    {
      title: 'Avg Time (hrs)',
      dataIndex: 'averageCompletionTime',
      key: 'averageCompletionTime',
      sorter: true,
      width: 120,
      render: (time: number) => time != null ? `${time.toFixed(1)}h` : '-',
    },
    {
      title: 'Last Activity',
      dataIndex: 'lastActivity',
      key: 'lastActivity',
      sorter: true,
      width: 140,
      render: (date: string) => dayjs(date).fromNow(),
    },
  ];

  // Calculate summary statistics
  const calculateSummary = () => {
    if (users.length === 0) {
      return {
        avgCompletionRate: 0,
        avgQualityScore: 0,
        avgEfficiency: 0,
        topPerformers: 0,
        totalTasks: 0
      };
    }

    const avgCompletionRate = users.reduce((sum, user) => sum + user.completionRate, 0) / users.length;
    const avgQualityScore = users.reduce((sum, user) => sum + user.qualityScore, 0) / users.length;
    const avgEfficiency = users.reduce((sum, user) => sum + user.efficiency, 0) / users.length;
    const topPerformers = users.filter(user => user.performanceRating === 'Excellent').length;
    const totalTasks = users.reduce((sum, user) => sum + user.tasksAssigned, 0);

    return {
      avgCompletionRate,
      avgQualityScore,
      avgEfficiency,
      topPerformers,
      totalTasks
    };
  };

  const summary = calculateSummary();

  return (
    <div className="p-6">
      <div className="flex justify-end items-center mb-6">
        <Space>
          <Button
            type="primary"
            icon={<ExportOutlined />}
            onClick={() => message.success('Performance report export initiated')}
          >
            Export Report
          </Button>
          <Button
            icon={<ReloadOutlined />}
            onClick={loadUserPerformance}
            loading={loading}
          >
            Refresh
          </Button>
        </Space>
      </div>

      {/* Summary Statistics */}
      <Row gutter={[16, 16]} className="mb-6">
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="Average Completion Rate"
              value={summary.avgCompletionRate}
              precision={1}
              suffix="%"
              prefix={<CheckCircleOutlined style={{ color: '#52c41a' }} />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="Average Quality Score"
              value={summary.avgQualityScore}
              precision={1}
              prefix={<StarOutlined style={{ color: '#1890ff' }} />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="Top Performers"
              value={summary.topPerformers}
              prefix={<TrophyOutlined style={{ color: '#faad14' }} />}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="Average Efficiency"
              value={summary.avgEfficiency}
              precision={1}
              suffix="%"
              prefix={<LineChartOutlined style={{ color: '#722ed1' }} />}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
      </Row>

      {/* Search Form */}
      <Card className="mb-6">
        <Form
          form={form}
          layout="inline"
          onFinish={handleSearch}
          className="w-full"
        >
          <Form.Item name="department" className="mb-2">
            <Select
              placeholder="Department"
              allowClear
              style={{ width: 160 }}
            >
              <Option value="Field Operations">Field Operations</Option>
              <Option value="Data Management">Data Management</Option>
              <Option value="Customer Service">Customer Service</Option>
              <Option value="Maintenance">Maintenance</Option>
            </Select>
          </Form.Item>

          <Form.Item name="performanceRating" className="mb-2">
            <Select
              placeholder="Performance Rating"
              allowClear
              style={{ width: 160 }}
            >
              <Option value="Excellent">Excellent</Option>
              <Option value="Good">Good</Option>
              <Option value="Average">Average</Option>
              <Option value="Needs Improvement">Needs Improvement</Option>
            </Select>
          </Form.Item>

          <Form.Item name="minCompletionRate" className="mb-2">
            <Input
              placeholder="Min Completion %"
              type="number"
              style={{ width: 140 }}
            />
          </Form.Item>

          <Form.Item name="maxCompletionRate" className="mb-2">
            <Input
              placeholder="Max Completion %"
              type="number"
              style={{ width: 140 }}
            />
          </Form.Item>

          <Form.Item className="mb-2">
            <Space>
              <Button type="primary" htmlType="submit" icon={<SearchOutlined />}>
                Search
              </Button>
              <Button onClick={handleResetSearch} icon={<FilterOutlined />}>
                Reset
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Card>

      {/* Performance Table */}
      <Card>
        <Table
          columns={columns}
          dataSource={users}
          rowKey="userId"
          loading={loading}
          scroll={{ x: 1400 }}
          pagination={{
            current: currentPage,
            pageSize: pageSize,
            total: totalCount,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => 
              `${range[0]}-${range[1]} of ${total} users`,
            onChange: (page, size) => {
              setCurrentPage(page);
              setPageSize(size || 10);
            },
          }}
          onChange={(pagination, filters, sorter) => {
            if (!Array.isArray(sorter) && sorter.field) {
              setSearchParams(prev => ({
                ...prev,
                sortBy: sorter.field as string,
                sortDirection: sorter.order === 'ascend' ? 'asc' : 'desc',
              }));
            }
          }}
        />
      </Card>
    </div>
  );
};

export default UserPerformance;
