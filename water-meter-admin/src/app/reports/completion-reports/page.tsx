'use client';

import React, { useState, useEffect, useCallback } from 'react';
import {
  Table, Button, Form, Input, DatePicker, Select, Space, Card, Row, Col, 
  Statistic, Progress, message, Tag, Tooltip
} from 'antd';
import {
  SearchOutlined, ExportOutlined, ReloadOutlined, FilterOutlined,
  CheckCircleOutlined, ClockCircleOutlined, ExclamationCircleOutlined,
  Bar<PERSON>hartOutlined, EyeOutlined
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import dayjs from 'dayjs';
import { reportsService } from '@/services/reports.service';
import {
  CompletionReportDto,
  CompletionReportSearchDto,
  CompletionReportSearchResultDto
} from '@/types/reports';

const { RangePicker } = DatePicker;
const { Option } = Select;

const CompletionReports: React.FC = () => {
  const [reports, setReports] = useState<CompletionReportDto[]>([]);
  const [loading, setLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [totalCount, setTotalCount] = useState(0);
  const [searchParams, setSearchParams] = useState<CompletionReportSearchDto>({
    page: 1,
    pageSize: 10,
    sortBy: 'reportDate',
    sortDirection: 'desc'
  });

  const [form] = Form.useForm();

  // Load completion reports
  const loadReports = useCallback(async () => {
    setLoading(true);
    try {
      const searchDto: CompletionReportSearchDto = {
        ...searchParams,
        page: currentPage,
        pageSize
      };
      
      const result: CompletionReportSearchResultDto = await reportsService.getCompletionReports(searchDto);
      setReports(result.reports);
      setTotalCount(result.totalCount);
    } catch (error) {
      message.error('Failed to load completion reports');
      console.error('Load reports error:', error);
    } finally {
      setLoading(false);
    }
  }, [currentPage, pageSize, searchParams]);

  useEffect(() => {
    loadReports();
  }, [loadReports]);

  // Handle search
  const handleSearch = (values: any) => {
    const searchDto: CompletionReportSearchDto = {
      page: 1,
      pageSize,
      sortBy: 'reportDate',
      sortDirection: 'desc',
      ...values,
      dateFrom: values.dateRange?.[0] ? dayjs(values.dateRange[0]).toDate() : undefined,
      dateTo: values.dateRange?.[1] ? dayjs(values.dateRange[1]).toDate() : undefined,
    };
    delete (searchDto as any).dateRange;
    
    setSearchParams(searchDto);
    setCurrentPage(1);
  };

  // Reset search
  const handleResetSearch = () => {
    form.resetFields();
    setSearchParams({
      page: 1,
      pageSize,
      sortBy: 'reportDate',
      sortDirection: 'desc'
    });
    setCurrentPage(1);
  };

  // Export reports
  const handleExport = async () => {
    try {
      message.success('Export initiated. You will be notified when ready for download.');
    } catch (error) {
      message.error('Failed to export reports');
      console.error('Export error:', error);
    }
  };

  // Get completion rate status
  const getCompletionRateStatus = (rate: number) => {
    if (rate >= 90) return { color: 'green', text: 'Excellent' };
    if (rate >= 80) return { color: 'blue', text: 'Good' };
    if (rate >= 70) return { color: 'orange', text: 'Average' };
    return { color: 'red', text: 'Poor' };
  };

  // Table columns
  const columns: ColumnsType<CompletionReportDto> = [
    {
      title: 'Report Date',
      dataIndex: 'reportDate',
      key: 'reportDate',
      sorter: true,
      render: (date: string) => dayjs(date).format('YYYY-MM-DD'),
      fixed: 'left',
      width: 120,
    },
    {
      title: 'Total Tasks',
      dataIndex: 'totalTasks',
      key: 'totalTasks',
      sorter: true,
      width: 100,
      render: (value: number) => value.toLocaleString(),
    },
    {
      title: 'Completed',
      dataIndex: 'completedTasks',
      key: 'completedTasks',
      sorter: true,
      width: 100,
      render: (value: number) => (
        <span className="text-green-600 font-medium">
          {value.toLocaleString()}
        </span>
      ),
    },
    {
      title: 'Pending',
      dataIndex: 'pendingTasks',
      key: 'pendingTasks',
      sorter: true,
      width: 100,
      render: (value: number) => (
        <span className="text-orange-600 font-medium">
          {value.toLocaleString()}
        </span>
      ),
    },
    {
      title: 'Overdue',
      dataIndex: 'overdueTasks',
      key: 'overdueTasks',
      sorter: true,
      width: 100,
      render: (value: number) => (
        <span className="text-red-600 font-medium">
          {value.toLocaleString()}
        </span>
      ),
    },
    {
      title: 'Completion Rate',
      dataIndex: 'completionRate',
      key: 'completionRate',
      sorter: true,
      width: 140,
      render: (rate: number) => {
        const status = getCompletionRateStatus(rate);
        return (
          <div>
            <Tag color={status.color}>{status.text}</Tag>
            <div className="mt-1">
              <Progress
                percent={rate}
                size="small"
                showInfo={false}
                strokeColor={status.color === 'green' ? '#52c41a' : status.color === 'blue' ? '#1890ff' : status.color === 'orange' ? '#faad14' : '#ff4d4f'}
              />
              <span className="text-sm font-medium">{rate.toFixed(1)}%</span>
            </div>
          </div>
        );
      },
    },
    {
      title: 'Avg Completion Time',
      dataIndex: 'averageCompletionTime',
      key: 'averageCompletionTime',
      sorter: true,
      width: 140,
      render: (time: number) => `${time.toFixed(1)} hrs`,
    },
    {
      title: 'Created By',
      dataIndex: 'createdBy',
      key: 'createdBy',
      width: 120,
    },
    {
      title: 'Created At',
      dataIndex: 'createdAt',
      key: 'createdAt',
      sorter: true,
      width: 140,
      render: (date: string) => dayjs(date).format('MM-DD HH:mm'),
    },
    {
      title: 'Actions',
      key: 'actions',
      fixed: 'right',
      width: 100,
      render: (_, record) => (
        <Space>
          <Tooltip title="View Details">
            <Button
              type="link"
              icon={<EyeOutlined />}
              size="small"
              onClick={() => {
                message.info('Detailed view coming soon');
              }}
            />
          </Tooltip>
          <Tooltip title="Export Report">
            <Button
              type="link"
              icon={<ExportOutlined />}
              size="small"
              onClick={() => {
                message.success('Report export initiated');
              }}
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  // Calculate summary statistics
  const calculateSummary = () => {
    if (reports.length === 0) {
      return {
        avgCompletionRate: 0,
        totalTasks: 0,
        totalCompleted: 0,
        totalPending: 0,
        totalOverdue: 0
      };
    }

    const totalTasks = reports.reduce((sum, report) => sum + report.totalTasks, 0);
    const totalCompleted = reports.reduce((sum, report) => sum + report.completedTasks, 0);
    const totalPending = reports.reduce((sum, report) => sum + report.pendingTasks, 0);
    const totalOverdue = reports.reduce((sum, report) => sum + report.overdueTasks, 0);
    const avgCompletionRate = reports.reduce((sum, report) => sum + report.completionRate, 0) / reports.length;

    return {
      avgCompletionRate,
      totalTasks,
      totalCompleted,
      totalPending,
      totalOverdue
    };
  };

  const summary = calculateSummary();

  return (
    <div className="p-6">
      <div className="flex justify-end items-center mb-6">
        <Space>
          <Button
            type="primary"
            icon={<ExportOutlined />}
            onClick={handleExport}
          >
            Export All
          </Button>
          <Button
            icon={<ReloadOutlined />}
            onClick={loadReports}
            loading={loading}
          >
            Refresh
          </Button>
        </Space>
      </div>

      {/* Summary Statistics */}
      <Row gutter={[16, 16]} className="mb-6">
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="Average Completion Rate"
              value={summary.avgCompletionRate}
              precision={1}
              suffix="%"
              prefix={<CheckCircleOutlined style={{ color: '#52c41a' }} />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="Total Tasks"
              value={summary.totalTasks}
              prefix={<BarChartOutlined style={{ color: '#1890ff' }} />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="Pending Tasks"
              value={summary.totalPending}
              prefix={<ClockCircleOutlined style={{ color: '#faad14' }} />}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="Overdue Tasks"
              value={summary.totalOverdue}
              prefix={<ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />}
              valueStyle={{ color: '#ff4d4f' }}
            />
          </Card>
        </Col>
      </Row>

      {/* Search Form */}
      <Card className="mb-6">
        <Form
          form={form}
          layout="inline"
          onFinish={handleSearch}
          className="w-full"
        >
          <Form.Item name="dateRange" className="mb-2">
            <RangePicker
              placeholder={['Start Date', 'End Date']}
              allowClear
            />
          </Form.Item>
          
          <Form.Item name="createdBy" className="mb-2">
            <Input
              placeholder="Created By"
              allowClear
              style={{ width: 150 }}
            />
          </Form.Item>

          <Form.Item name="minCompletionRate" className="mb-2">
            <Input
              placeholder="Min Rate %"
              type="number"
              style={{ width: 120 }}
            />
          </Form.Item>

          <Form.Item name="maxCompletionRate" className="mb-2">
            <Input
              placeholder="Max Rate %"
              type="number"
              style={{ width: 120 }}
            />
          </Form.Item>

          <Form.Item className="mb-2">
            <Space>
              <Button type="primary" htmlType="submit" icon={<SearchOutlined />}>
                Search
              </Button>
              <Button onClick={handleResetSearch} icon={<FilterOutlined />}>
                Reset
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Card>

      {/* Reports Table */}
      <Card>
        <Table
          columns={columns}
          dataSource={reports}
          rowKey="id"
          loading={loading}
          scroll={{ x: 1200 }}
          pagination={{
            current: currentPage,
            pageSize: pageSize,
            total: totalCount,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => 
              `${range[0]}-${range[1]} of ${total} reports`,
            onChange: (page, size) => {
              setCurrentPage(page);
              setPageSize(size || 10);
            },
          }}
          onChange={(pagination, filters, sorter) => {
            if (!Array.isArray(sorter) && sorter.field) {
              setSearchParams(prev => ({
                ...prev,
                sortBy: sorter.field as string,
                sortDirection: sorter.order === 'ascend' ? 'asc' : 'desc',
              }));
            }
          }}
        />
      </Card>
    </div>
  );
};

export default CompletionReports;
