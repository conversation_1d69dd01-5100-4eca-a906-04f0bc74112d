'use client';

import React, { useState, useEffect } from 'react';
import { Card, Table, Select, DatePicker, Button, message, Tag, Progress, Space, Statistic, Row, Col, Empty, Spin } from 'antd';
import { SearchOutlined, ReloadOutlined, ExportOutlined, AlertOutlined, CheckCircleOutlined, ClockCircleOutlined } from '@ant-design/icons';
import { Line, Pie, Column } from '@ant-design/plots';
import type { ColumnsType } from 'antd/es/table';
import dayjs from 'dayjs';
import { reportsService } from '@/services/reports.service';
import type { ExceptionAnalysisDto, ExceptionTrendDto, ExceptionItemDto, ExceptionSearchParams } from '@/types/reports';

const { RangePicker } = DatePicker;
const { Option } = Select;

export default function ExceptionAnalysisPage() {
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<ExceptionAnalysisDto | null>(null);
  const [exceptionTrend, setExceptionTrend] = useState<ExceptionTrendDto[]>([]);
  const [searchParams, setSearchParams] = useState<ExceptionSearchParams>({
    dateFrom: dayjs().subtract(30, 'day').format('YYYY-MM-DD'),
    dateTo: dayjs().format('YYYY-MM-DD'),
    severity: undefined,
    status: undefined,
    pageIndex: 1,
    pageSize: 10
  });

  const fetchData = async () => {
    setLoading(true);
    try {
      const [analysisData, trendData] = await Promise.all([
        reportsService.getExceptionAnalysis(searchParams),
        reportsService.getExceptionTrend({
          dateFrom: searchParams.dateFrom,
          dateTo: searchParams.dateTo,
          timeRange: 'daily'
        })
      ]);
      setData(analysisData);
      setExceptionTrend(trendData);
    } catch (error) {
      message.error('Failed to load exception analysis data');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, []);

  const handleSearch = () => {
    fetchData();
  };

  const handleReset = () => {
    setSearchParams({
      dateFrom: dayjs().subtract(30, 'day').format('YYYY-MM-DD'),
      dateTo: dayjs().format('YYYY-MM-DD'),
      severity: undefined,
      status: undefined,
      pageIndex: 1,
      pageSize: 10
    });
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return '#ff4d4f';
      case 'high': return '#fa8c16';
      case 'medium': return '#fadb14';
      case 'low': return '#52c41a';
      default: return '#d9d9d9';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'resolved': return 'success';
      case 'in_progress': return 'processing';
      case 'pending': return 'warning';
      case 'dismissed': return 'default';
      default: return 'default';
    }
  };

  const columns: ColumnsType<ExceptionItemDto> = [
    {
      title: 'Exception ID',
      dataIndex: 'id',
      key: 'id',
      width: 120,
    },
    {
      title: 'Type',
      dataIndex: 'type',
      key: 'type',
      width: 150,
    },
    {
      title: 'Description',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
    },
    {
      title: 'Severity',
      dataIndex: 'severity',
      key: 'severity',
      width: 100,
      render: (severity: string) => (
        <Tag color={getSeverityColor(severity)}>
          {severity.toUpperCase()}
        </Tag>
      ),
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      width: 120,
      render: (status: string) => (
        <Tag color={getStatusColor(status)}>
          {status.replace('_', ' ').toUpperCase()}
        </Tag>
      ),
    },
    {
      title: 'Water Meter',
      dataIndex: 'waterMeterId',
      key: 'waterMeterId',
      width: 120,
    },
    {
      title: 'Location',
      dataIndex: 'location',
      key: 'location',
      width: 150,
      ellipsis: true,
    },
    {
      title: 'Detected At',
      dataIndex: 'detectedAt',
      key: 'detectedAt',
      width: 150,
      render: (date: string) => dayjs(date).format('YYYY-MM-DD HH:mm'),
    },
    {
      title: 'Resolved At',
      dataIndex: 'resolvedAt',
      key: 'resolvedAt',
      width: 150,
      render: (date: string) => date ? dayjs(date).format('YYYY-MM-DD HH:mm') : '-',
    },
  ];

  // Trend chart configuration
  const trendConfig = {
    data: exceptionTrend,
    xField: 'date',
    yField: 'count',
    seriesField: 'severity',
    color: ['#ff4d4f', '#fa8c16', '#fadb14', '#52c41a'],
    point: {
      size: 5,
      shape: 'diamond',
    },
    label: {
      style: {
        fill: '#aaa',
      },
    },
  };

  // Severity distribution chart
  const severityDistribution = data?.severityDistribution || [];
  const pieConfig = {
    data: severityDistribution,
    angleField: 'count',
    colorField: 'severity',
    radius: 0.8,
    color: ['#ff4d4f', '#fa8c16', '#fadb14', '#52c41a'],
    label: {
      type: 'outer',
      content: '{name} {percentage}',
    },
    interactions: [{ type: 'element-selected' }, { type: 'element-active' }],
  };

  // Type distribution chart
  const typeDistribution = data?.typeDistribution || [];
  const columnConfig = {
    data: typeDistribution,
    xField: 'type',
    yField: 'count',
    color: '#1890ff',
    label: {
      position: 'middle' as const,
      style: {
        fill: '#FFFFFF',
        opacity: 0.6,
      },
    },
    xAxis: {
      label: {
        autoHide: true,
        autoRotate: false,
      },
    },
    meta: {
      type: {
        alias: 'Exception Type',
      },
      count: {
        alias: 'Count',
      },
    },
  };

  if (loading && !data) {
    return (
      <div className="flex justify-center items-center min-h-[400px]">
        <Spin size="large" />
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      {/* Page Header */}
      <div className="flex justify-between items-center">
        <div>
        </div>
        <Space>
          <Button icon={<ReloadOutlined />} onClick={fetchData}>
            Refresh
          </Button>
          <Button type="primary" icon={<ExportOutlined />}>
            Export Report
          </Button>
        </Space>
      </div>

      {/* Statistics Cards */}
      <Row gutter={[16, 16]}>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="Total Exceptions"
              value={data?.totalExceptions || 0}
              prefix={<AlertOutlined style={{ color: '#fa8c16' }} />}
              valueStyle={{ color: '#fa8c16' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="Critical Issues"
              value={data?.criticalCount || 0}
              prefix={<AlertOutlined style={{ color: '#ff4d4f' }} />}
              valueStyle={{ color: '#ff4d4f' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="Resolved"
              value={data?.resolvedCount || 0}
              prefix={<CheckCircleOutlined style={{ color: '#52c41a' }} />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="Avg Resolution Time"
              value={data?.avgResolutionTime || 0}
              suffix="hours"
              prefix={<ClockCircleOutlined style={{ color: '#1890ff' }} />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
      </Row>

      {/* Resolution Rate Progress */}
      <Card title="Resolution Rate">
        <Progress
          percent={data?.resolutionRate || 0}
          status={data && data.resolutionRate >= 90 ? 'success' : data && data.resolutionRate >= 70 ? 'normal' : 'exception'}
          strokeColor={{
            '0%': '#108ee9',
            '100%': '#87d068',
          }}
        />
      </Card>

      {/* Charts Row */}
      <Row gutter={[16, 16]}>
        <Col xs={24} lg={12}>
          <Card title="Exception Trend" className="h-96">
            {exceptionTrend.length > 0 ? (
              <Line {...trendConfig} height={300} />
            ) : (
              <Empty description="No trend data available" />
            )}
          </Card>
        </Col>
        <Col xs={24} lg={12}>
          <Card title="Severity Distribution" className="h-96">
            {severityDistribution.length > 0 ? (
              <Pie {...pieConfig} height={300} />
            ) : (
              <Empty description="No severity data available" />
            )}
          </Card>
        </Col>
      </Row>

      {/* Type Distribution Chart */}
      <Card title="Exception Type Distribution">
        {typeDistribution.length > 0 ? (
          <Column {...columnConfig} height={300} />
        ) : (
          <Empty description="No type distribution data available" />
        )}
      </Card>

      {/* Search Filters */}
      <Card title="Exception List" className="mt-6">
        <div className="mb-4 p-4 bg-gray-50 rounded-lg">
          <Row gutter={[16, 16]}>
            <Col xs={24} sm={12} lg={6}>
              <RangePicker
                value={[dayjs(searchParams.dateFrom), dayjs(searchParams.dateTo)]}
                onChange={(dates) => {
                  if (dates) {
                    setSearchParams(prev => ({
                      ...prev,
                      dateFrom: dates[0]?.format('YYYY-MM-DD') || '',
                      dateTo: dates[1]?.format('YYYY-MM-DD') || ''
                    }));
                  }
                }}
                placeholder={['Start Date', 'End Date']}
                className="w-full"
              />
            </Col>
            <Col xs={24} sm={12} lg={4}>
              <Select
                placeholder="Severity"
                value={searchParams.severity}
                onChange={(value) => setSearchParams(prev => ({ ...prev, severity: value }))}
                className="w-full"
                allowClear
              >
                <Option value="critical">Critical</Option>
                <Option value="high">High</Option>
                <Option value="medium">Medium</Option>
                <Option value="low">Low</Option>
              </Select>
            </Col>
            <Col xs={24} sm={12} lg={4}>
              <Select
                placeholder="Status"
                value={searchParams.status}
                onChange={(value) => setSearchParams(prev => ({ ...prev, status: value }))}
                className="w-full"
                allowClear
              >
                <Option value="pending">Pending</Option>
                <Option value="in_progress">In Progress</Option>
                <Option value="resolved">Resolved</Option>
                <Option value="dismissed">Dismissed</Option>
              </Select>
            </Col>
            <Col xs={24} sm={12} lg={6}>
              <Space>
                <Button type="primary" icon={<SearchOutlined />} onClick={handleSearch}>
                  Search
                </Button>
                <Button onClick={handleReset}>
                  Reset
                </Button>
              </Space>
            </Col>
          </Row>
        </div>

        {/* Exception Table */}
        <Table
          columns={columns}
          dataSource={data?.exceptions || []}
          rowKey="id"
          loading={loading}
          pagination={{
            current: searchParams.pageIndex,
            pageSize: searchParams.pageSize,
            total: data?.total || 0,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} exceptions`,
            onChange: (page, size) => {
              setSearchParams(prev => ({
                ...prev,
                pageIndex: page,
                pageSize: size || 10
              }));
            },
          }}
          scroll={{ x: 1200 }}
        />
      </Card>
    </div>
  );
} 