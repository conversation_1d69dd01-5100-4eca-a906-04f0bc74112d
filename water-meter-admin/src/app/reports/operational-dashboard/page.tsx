'use client';

import React, { useState, useEffect } from 'react';
import {
  Card, Row, Col, Statistic, Progress, Spin, Button, DatePicker, Select, Space, Alert
} from 'antd';
import {
  DashboardOutlined, TrophyOutlined, ClockCircleOutlined, WarningOutlined,
  UserOutlined, CheckCircleOutlined, ExclamationCircleOutlined, ReloadOutlined
} from '@ant-design/icons';
import { Line, Bar, Pie } from '@ant-design/plots';
import dayjs from 'dayjs';
import { reportsService } from '@/services/reports.service';
import {
  DashboardMetricsDto,
  CompletionTrendDto,
  AnomalyTrendDto,
  UserPerformanceOverviewDto
} from '@/types/reports';

const { RangePicker } = DatePicker;
const { Option } = Select;

const OperationalDashboard: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [metrics, setMetrics] = useState<DashboardMetricsDto | null>(null);
  const [completionTrends, setCompletionTrends] = useState<CompletionTrendDto | null>(null);
  const [anomalyTrends, setAnomalyTrends] = useState<AnomalyTrendDto[]>([]);
  const [userPerformance, setUserPerformance] = useState<UserPerformanceOverviewDto | null>(null);
  const [timeRange, setTimeRange] = useState<'daily' | 'weekly' | 'monthly'>('daily');
  const [dateRange, setDateRange] = useState<[dayjs.Dayjs, dayjs.Dayjs]>([
    dayjs().subtract(30, 'day'),
    dayjs()
  ]);

  // Load dashboard data
  const loadDashboardData = async () => {
    setLoading(true);
    try {
      const [metricsData, trendsData, anomaliesData, performanceData] = await Promise.all([
        reportsService.getDashboardMetrics(),
        reportsService.getCompletionTrends(),
        reportsService.getAnomalyTrends(),
        reportsService.getUserPerformanceOverview()
      ]);

      setMetrics(metricsData);
      setCompletionTrends(trendsData);
      setAnomalyTrends(anomaliesData);
      setUserPerformance(performanceData);
    } catch (error) {
      console.error('Error loading dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadDashboardData();
  }, []);

  const handleRefresh = () => {
    loadDashboardData();
  };

  const getHealthScoreStatus = (score: number) => {
    if (score >= 90) return { status: 'success' as const, color: '#52c41a' };
    if (score >= 70) return { status: 'normal' as const, color: '#faad14' };
    return { status: 'exception' as const, color: '#ff4d4f' };
  };

  // Completion Trend Chart Configuration
  const completionChartConfig = {
    data: completionTrends?.[timeRange] || [],
    xField: 'date',
    yField: 'value',
    smooth: true,
    color: '#1890ff',
    point: {
      size: 4,
      shape: 'circle',
    },
    label: {
      style: {
        fill: 'rgba(0,0,0,0.6)',
        fontSize: 12,
      },
    },
    xAxis: {
      type: 'time',
      tickCount: 8,
    },
    yAxis: {
      label: {
        formatter: (v: string) => `${v}%`,
      },
    },
    tooltip: {
      formatter: (datum: any) => ({
        name: 'Completion Rate',
        value: `${datum.value}%`,
      }),
    },
  };

  // Anomaly Trend Chart Configuration
  const anomalyChartConfig = {
    data: anomalyTrends,
    xField: 'date',
    yField: 'count',
    seriesField: 'type',
    isStack: true,
    color: ['#ff4d4f', '#faad14', '#52c41a', '#1890ff'],
    legend: {
      position: 'top' as const,
    },
    xAxis: {
      type: 'time',
      tickCount: 10,
    },
    tooltip: {
      shared: true,
    },
  };

  // Performance Distribution Chart Configuration
  const performanceChartConfig = {
    appendPadding: 10,
    data: userPerformance?.topPerformers.map(user => ({
      type: user.performanceRating,
      value: 1,
    })) || [],
    angleField: 'value',
    colorField: 'type',
    radius: 0.8,
    label: {
      type: 'outer',
      content: '{name} {percentage}',
    },
    color: ['#52c41a', '#1890ff', '#faad14', '#ff4d4f'],
    interactions: [
      {
        type: 'element-selected',
      },
      {
        type: 'element-active',
      },
    ],
  };

  return (
    <div className="p-6">
      <div className="flex justify-end items-center mb-6">
        <Space>
          <Select
            value={timeRange}
            onChange={setTimeRange}
            style={{ width: 120 }}
          >
            <Option value="daily">Daily</Option>
            <Option value="weekly">Weekly</Option>
            <Option value="monthly">Monthly</Option>
          </Select>
          <RangePicker
            value={dateRange}
            onChange={(dates) => dates && setDateRange(dates as [dayjs.Dayjs, dayjs.Dayjs])}
            allowClear={false}
          />
          <Button
            type="primary"
            icon={<ReloadOutlined />}
            onClick={handleRefresh}
            loading={loading}
          >
            Refresh
          </Button>
        </Space>
      </div>

      <Spin spinning={loading}>
        {/* Key Metrics */}
        <Row gutter={[16, 16]} className="mb-6">
          <Col xs={24} sm={12} md={6}>
            <Card>
              <Statistic
                title="Total Water Meters"
                value={metrics?.totalWaterMeters || 0}
                prefix={<DashboardOutlined style={{ color: '#1890ff' }} />}
                valueStyle={{ color: '#1890ff' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} md={6}>
            <Card>
              <Statistic
                title="Active Meters"
                value={metrics?.activeMeters || 0}
                prefix={<CheckCircleOutlined style={{ color: '#52c41a' }} />}
                valueStyle={{ color: '#52c41a' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} md={6}>
            <Card>
              <Statistic
                title="Readings Today"
                value={metrics?.totalReadingsToday || 0}
                prefix={<ClockCircleOutlined style={{ color: '#faad14' }} />}
                valueStyle={{ color: '#faad14' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} md={6}>
            <Card>
              <Statistic
                title="System Health"
                value={metrics?.systemHealthScore || 0}
                suffix="%"
                prefix={<TrophyOutlined style={{ color: getHealthScoreStatus(metrics?.systemHealthScore || 0).color }} />}
                valueStyle={{ color: getHealthScoreStatus(metrics?.systemHealthScore || 0).color }}
              />
              <Progress
                percent={metrics?.systemHealthScore || 0}
                status={getHealthScoreStatus(metrics?.systemHealthScore || 0).status}
                showInfo={false}
                className="mt-2"
              />
            </Card>
          </Col>
        </Row>

        {/* Completion Rate Overview */}
        <Row gutter={[16, 16]} className="mb-6">
          <Col xs={24} sm={12} md={8}>
            <Card>
              <Statistic
                title="Completion Rate Today"
                value={metrics?.completionRateToday || 0}
                suffix="%"
                prefix={<CheckCircleOutlined style={{ color: '#52c41a' }} />}
                valueStyle={{ color: '#52c41a' }}
              />
              <Progress
                percent={metrics?.completionRateToday || 0}
                status="active"
                showInfo={false}
                className="mt-2"
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} md={8}>
            <Card>
              <Statistic
                title="Pending Tasks"
                value={metrics?.pendingTasks || 0}
                prefix={<ClockCircleOutlined style={{ color: '#faad14' }} />}
                valueStyle={{ color: '#faad14' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} md={8}>
            <Card>
              <Statistic
                title="Overdue Tasks"
                value={metrics?.overdueTasks || 0}
                prefix={<ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />}
                valueStyle={{ color: '#ff4d4f' }}
              />
              {(metrics?.overdueTasks || 0) > 20 && (
                <Alert
                  message="High number of overdue tasks"
                  type="warning"
                  showIcon
                  className="mt-2"
                />
              )}
            </Card>
          </Col>
        </Row>

        {/* Charts Section */}
        <Row gutter={[16, 16]} className="mb-6">
          <Col xs={24} lg={16}>
            <Card 
              title={`Completion Rate Trend (${timeRange.charAt(0).toUpperCase() + timeRange.slice(1)})`}
              extra={<span className="text-gray-500">Last 30 days</span>}
            >
              <Line {...completionChartConfig} height={300} />
            </Card>
          </Col>
          <Col xs={24} lg={8}>
            <Card title="Performance Distribution">
              <Pie {...performanceChartConfig} height={300} />
            </Card>
          </Col>
        </Row>

        <Row gutter={[16, 16]} className="mb-6">
          <Col xs={24} lg={16}>
            <Card title="Anomaly Trends" extra={<span className="text-gray-500">By exception type</span>}>
              <Bar {...anomalyChartConfig} height={300} />
            </Card>
          </Col>
          <Col xs={24} lg={8}>
            <Card title="Top Performers">
              <div className="space-y-4">
                {userPerformance?.topPerformers.slice(0, 5).map((user, index) => (
                  <div key={user.userId} className="flex items-center justify-between">
                    <div className="flex items-center">
                      <div className={`w-8 h-8 rounded-full flex items-center justify-center text-white text-sm font-bold mr-3 ${
                        index === 0 ? 'bg-yellow-500' : 
                        index === 1 ? 'bg-gray-400' : 
                        index === 2 ? 'bg-yellow-600' : 'bg-blue-500'
                      }`}>
                        {index + 1}
                      </div>
                      <div>
                        <div className="font-medium">{user.fullName}</div>
                        <div className="text-sm text-gray-500">{user.department}</div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="font-bold text-green-600">{user.completionRate}%</div>
                      <div className="text-sm text-gray-500">{user.tasksCompleted} tasks</div>
                    </div>
                  </div>
                ))}
              </div>
            </Card>
          </Col>
        </Row>

        {/* Summary Cards */}
        <Row gutter={[16, 16]}>
          <Col xs={24} md={8}>
            <Card>
              <div className="flex items-center justify-between">
                <div>
                  <div className="text-2xl font-bold text-blue-600">{userPerformance?.totalUsers || 0}</div>
                  <div className="text-gray-600">Active Users</div>
                </div>
                <UserOutlined className="text-3xl text-blue-600" />
              </div>
            </Card>
          </Col>
          <Col xs={24} md={8}>
            <Card>
              <div className="flex items-center justify-between">
                <div>
                  <div className="text-2xl font-bold text-green-600">{userPerformance?.averagePerformance || 0}%</div>
                  <div className="text-gray-600">Average Performance</div>
                </div>
                <TrophyOutlined className="text-3xl text-green-600" />
              </div>
            </Card>
          </Col>
          <Col xs={24} md={8}>
            <Card>
              <div className="flex items-center justify-between">
                <div>
                  <div className="text-2xl font-bold text-orange-600">{metrics?.anomaliesCount || 0}</div>
                  <div className="text-gray-600">Active Anomalies</div>
                </div>
                <WarningOutlined className="text-3xl text-orange-600" />
              </div>
            </Card>
          </Col>
        </Row>
      </Spin>
    </div>
  );
};

export default OperationalDashboard; 