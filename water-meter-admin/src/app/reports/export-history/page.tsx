'use client';

import React, { useState, useEffect } from 'react';
import { Card, Table, Button, message, Tag, Progress, Space, Row, Col, DatePicker, Select, Input, Statistic, Tooltip } from 'antd';
import { SearchOutlined, ReloadOutlined, DownloadOutlined, DeleteOutlined, EyeOutlined, FilterOutlined, FileTextOutlined, FilePdfOutlined, FileExcelOutlined } from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import dayjs from 'dayjs';
import { reportsService } from '@/services/reports.service';
import type { ExportJobDto, ExportHistorySearchDto } from '@/types/reports';

const { RangePicker } = DatePicker;
const { Option } = Select;
const { Search } = Input;

export default function ExportHistoryPage() {
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<ExportJobDto[]>([]);
  const [total, setTotal] = useState(0);
  const [searchParams, setSearchParams] = useState<ExportHistorySearchDto>({
    page: 1,
    pageSize: 10,
    sortBy: 'createdAt',
    sortDirection: 'desc'
  });

  const fetchData = async () => {
    setLoading(true);
    try {
      const result = await reportsService.getExportHistory(searchParams);
      setData(result.exports);
      setTotal(result.totalCount);
    } catch (error) {
      message.error('Failed to load export history');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, [searchParams]);

  const handleSearch = () => {
    setSearchParams(prev => ({ ...prev, page: 1 }));
  };

  const handleReset = () => {
    setSearchParams({
      page: 1,
      pageSize: 10,
      sortBy: 'createdAt',
      sortDirection: 'desc'
    });
  };

  const handleDownload = async (record: ExportJobDto) => {
    if (record.downloadUrl) {
      try {
        await reportsService.downloadFile(record.downloadUrl, record.fileName);
        message.success('Download started');
      } catch (error) {
        message.error('Failed to download file');
      }
    } else {
      message.warning('Download URL not available');
    }
  };

  const handleDelete = async (id: number) => {
    try {
      // This would be implemented in the service
      message.success('Export record deleted');
      fetchData();
    } catch (error) {
      message.error('Failed to delete export record');
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'success';
      case 'processing': return 'processing';
      case 'pending': return 'warning';
      case 'failed': return 'error';
      default: return 'default';
    }
  };

  const getFormatIcon = (format: string) => {
    switch (format) {
      case 'pdf': return <FilePdfOutlined style={{ color: '#ff4d4f' }} />;
      case 'excel': return <FileExcelOutlined style={{ color: '#52c41a' }} />;
      case 'csv': return <FileTextOutlined style={{ color: '#1890ff' }} />;
      default: return <FileTextOutlined />;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'completed': return 'Completed';
      case 'processing': return 'Processing';
      case 'pending': return 'Pending';
      case 'failed': return 'Failed';
      default: return status;
    }
  };

  const columns: ColumnsType<ExportJobDto> = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
      sorter: true,
    },
    {
      title: 'Export Type',
      dataIndex: 'exportType',
      key: 'exportType',
      width: 150,
      sorter: true,
    },
    {
      title: 'Format',
      dataIndex: 'format',
      key: 'format',
      width: 100,
      render: (format: string) => (
        <span className="flex items-center space-x-2">
          {getFormatIcon(format)}
          <span>{format.toUpperCase()}</span>
        </span>
      ),
    },
    {
      title: 'File Name',
      dataIndex: 'fileName',
      key: 'fileName',
      ellipsis: true,
      render: (fileName: string) => (
        <Tooltip title={fileName}>
          {fileName}
        </Tooltip>
      ),
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      width: 120,
      render: (status: string) => (
        <Tag color={getStatusColor(status)}>
          {getStatusText(status)}
        </Tag>
      ),
      filters: [
        { text: 'Completed', value: 'completed' },
        { text: 'Processing', value: 'processing' },
        { text: 'Pending', value: 'pending' },
        { text: 'Failed', value: 'failed' },
      ],
    },
    {
      title: 'Progress',
      dataIndex: 'progress',
      key: 'progress',
      width: 120,
      render: (progress: number, record: ExportJobDto) => (
        <Progress 
          percent={progress} 
          size="small" 
          status={record.status === 'failed' ? 'exception' : record.status === 'completed' ? 'success' : 'active'}
        />
      ),
    },
    {
      title: 'File Size',
      dataIndex: 'fileSize',
      key: 'fileSize',
      width: 100,
      render: (size: number) => {
        if (!size) return '-';
        if (size < 1024 * 1024) {
          return `${(size / 1024).toFixed(1)} KB`;
        }
        return `${(size / 1024 / 1024).toFixed(2)} MB`;
      },
      sorter: true,
    },
    {
      title: 'Created By',
      dataIndex: 'createdBy',
      key: 'createdBy',
      width: 120,
    },
    {
      title: 'Created At',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 150,
      render: (date: string) => (
        <Tooltip title={dayjs(date).format('YYYY-MM-DD HH:mm:ss')}>
          {dayjs(date).format('MM-DD HH:mm')}
        </Tooltip>
      ),
      sorter: true,
    },
    {
      title: 'Completed At',
      dataIndex: 'completedAt',
      key: 'completedAt',
      width: 150,
      render: (date: string) => date ? (
        <Tooltip title={dayjs(date).format('YYYY-MM-DD HH:mm:ss')}>
          {dayjs(date).format('MM-DD HH:mm')}
        </Tooltip>
      ) : '-',
    },
    {
      title: 'Expires At',
      dataIndex: 'expiresAt',
      key: 'expiresAt',
      width: 150,
      render: (date: string) => {
        if (!date) return '-';
        const isExpired = dayjs(date).isBefore(dayjs());
        return (
          <Tooltip title={dayjs(date).format('YYYY-MM-DD HH:mm:ss')}>
            <span style={{ color: isExpired ? '#ff4d4f' : 'inherit' }}>
              {dayjs(date).format('MM-DD HH:mm')}
              {isExpired && ' (Expired)'}
            </span>
          </Tooltip>
        );
      },
    },
    {
      title: 'Actions',
      key: 'actions',
      width: 120,
      fixed: 'right',
      render: (_, record: ExportJobDto) => (
        <Space size="small">
          {record.status === 'completed' && record.downloadUrl && (
            <Tooltip title="Download">
              <Button 
                type="text" 
                icon={<DownloadOutlined />}
                onClick={() => handleDownload(record)}
                size="small"
              />
            </Tooltip>
          )}
          <Tooltip title="View Details">
            <Button 
              type="text" 
              icon={<EyeOutlined />}
              size="small"
              onClick={() => message.info('View details functionality to be implemented')}
            />
          </Tooltip>
          <Tooltip title="Delete">
            <Button 
              type="text" 
              danger
              icon={<DeleteOutlined />}
              size="small"
              onClick={() => handleDelete(record.id)}
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  // Calculate statistics
  const totalExports = data.length;
  const completedExports = data.filter(item => item.status === 'completed').length;
  const failedExports = data.filter(item => item.status === 'failed').length;
  const successRate = totalExports > 0 ? ((completedExports / totalExports) * 100).toFixed(1) : '0';

  return (
    <div className="p-6 space-y-6">
      {/* Page Header */}
      <div className="flex justify-end items-center">
        <Space>
          <Button icon={<ReloadOutlined />} onClick={fetchData}>
            Refresh
          </Button>
        </Space>
      </div>

      {/* Statistics Cards */}
      <Row gutter={[16, 16]}>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="Total Exports"
              value={totalExports}
              prefix={<FileTextOutlined style={{ color: '#1890ff' }} />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="Completed"
              value={completedExports}
              valueStyle={{ color: '#3f8600' }}
              prefix={<DownloadOutlined style={{ color: '#3f8600' }} />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="Failed"
              value={failedExports}
              valueStyle={{ color: '#cf1322' }}
              prefix={<DeleteOutlined style={{ color: '#cf1322' }} />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="Success Rate"
              value={successRate}
              suffix="%"
              valueStyle={{ color: successRate === '100.0' ? '#3f8600' : '#fa8c16' }}
            />
          </Card>
        </Col>
      </Row>

      {/* Search and Filter */}
      <Card>
        <div className="mb-4">
          <Row gutter={[16, 16]} align="middle">
            <Col xs={24} sm={8} md={6}>
              <Search
                placeholder="Search by file name or type"
                allowClear
                onSearch={(value) => {
                  setSearchParams(prev => ({ ...prev, exportType: value, page: 1 }));
                }}
              />
            </Col>
            <Col xs={24} sm={8} md={6}>
              <Select
                placeholder="Filter by format"
                allowClear
                className="w-full"
                onChange={(value) => {
                  setSearchParams(prev => ({ ...prev, format: value, page: 1 }));
                }}
              >
                <Option value="pdf">PDF</Option>
                <Option value="excel">Excel</Option>
                <Option value="csv">CSV</Option>
              </Select>
            </Col>
            <Col xs={24} sm={8} md={6}>
              <Select
                placeholder="Filter by status"
                allowClear
                className="w-full"
                onChange={(value) => {
                  setSearchParams(prev => ({ ...prev, status: value, page: 1 }));
                }}
              >
                <Option value="completed">Completed</Option>
                <Option value="processing">Processing</Option>
                <Option value="pending">Pending</Option>
                <Option value="failed">Failed</Option>
              </Select>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <RangePicker
                className="w-full"
                onChange={(dates) => {
                  if (dates) {
                    setSearchParams(prev => ({
                      ...prev,
                      dateFrom: dates[0]?.toDate(),
                      dateTo: dates[1]?.toDate(),
                      page: 1
                    }));
                  }
                }}
              />
            </Col>
            <Col xs={24} sm={12} md={24} lg={24}>
              <Space>
                <Button type="primary" icon={<SearchOutlined />} onClick={handleSearch}>
                  Search
                </Button>
                <Button onClick={handleReset}>
                  Reset
                </Button>
              </Space>
            </Col>
          </Row>
        </div>

        {/* Export History Table */}
        <Table
          columns={columns}
          dataSource={data}
          rowKey="id"
          loading={loading}
          pagination={{
            current: searchParams.page,
            pageSize: searchParams.pageSize,
            total: total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} exports`,
            onChange: (page, size) => {
              setSearchParams(prev => ({
                ...prev,
                page,
                pageSize: size || 10
              }));
            },
          }}
          scroll={{ x: 1200 }}
          onChange={(pagination, filters, sorter) => {
            if (Array.isArray(sorter)) return;
            if (sorter.field && sorter.order) {
              setSearchParams(prev => ({
                ...prev,
                sortBy: sorter.field as string,
                sortDirection: sorter.order === 'ascend' ? 'asc' : 'desc',
                page: 1
              }));
            }
          }}
        />
      </Card>
    </div>
  );
} 