﻿'use client';

import React, { useState, useEffect } from 'react';
import { <PERSON>, List, Button, Space, Tag, Row, Col, Typography, Select, Badge, Tooltip, message } from 'antd';
import { ExclamationCircleOutlined, CheckCircleOutlined, WarningOutlined, InfoCircleOutlined, ReloadOutlined, BellOutlined, CloseOutlined } from '@ant-design/icons';
import { quickAccessService } from '@/services/quickaccess.service';
import type { RecentAlertDto, AlertSearchParams, AlertStats } from '@/types/quickaccess';

const { Title, Text } = Typography;

export default function RecentAlertsPage() {
  const [alerts, setAlerts] = useState<RecentAlertDto[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchParams, setSearchParams] = useState<AlertSearchParams>({
    page: 1,
    pageSize: 10
  });
  const [stats, setStats] = useState<AlertStats | null>(null);

  useEffect(() => {
    fetchAlerts();
    fetchStats();
    // Auto refresh every 30 seconds
    const interval = setInterval(() => {
      fetchAlerts();
      fetchStats();
    }, 30000);
    
    return () => clearInterval(interval);
  }, []);

  const fetchAlerts = async () => {
    setLoading(true);
    try {
      const data = await quickAccessService.getRecentAlerts(searchParams);
      // Extract alerts array from the response
      setAlerts(Array.isArray(data) ? data : (data.alerts || []));
    } catch (error) {
      console.error('Failed to fetch recent alerts:', error);
      message.error('Failed to load recent alerts');
      setAlerts([]); // Set empty array on error
    } finally {
      setLoading(false);
    }
  };

  const fetchStats = async () => {
    try {
      const data = await quickAccessService.getAlertStats();
      setStats(data);
    } catch (error) {
      console.error('Failed to fetch alert stats:', error);
    }
  };

  const handleDismissAlert = async (alertId: number) => {
    try {
      await quickAccessService.dismissAlert(alertId);
      message.success('Alert dismissed');
      fetchAlerts();
      fetchStats();
    } catch (error) {
      console.error('Failed to dismiss alert:', error);
      message.error('Failed to dismiss alert');
    }
  };

  const handleMarkAllAsRead = async () => {
    try {
      await quickAccessService.markAllAlertsAsRead();
      message.success('All alerts marked as read');
      fetchAlerts();
      fetchStats();
    } catch (error) {
      console.error('Failed to mark alerts as read:', error);
      message.error('Failed to mark alerts as read');
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity.toLowerCase()) {
      case 'critical':
        return 'red';
      case 'high':
        return 'orange';
      case 'medium':
        return 'yellow';
      case 'low':
        return 'blue';
      case 'info':
        return 'green';
      default:
        return 'default';
    }
  };

  const getSeverityIcon = (severity: string) => {
    switch (severity.toLowerCase()) {
      case 'critical':
        return <ExclamationCircleOutlined />;
      case 'high':
        return <WarningOutlined />;
      case 'medium':
        return <InfoCircleOutlined />;
      case 'low':
        return <CheckCircleOutlined />;
      case 'info':
        return <InfoCircleOutlined />;
      default:
        return <BellOutlined />;
    }
  };

  return (
    <div className="p-6">
      <div className="mb-6">
      </div>

      {/* Statistics Cards */}
      {stats && (
        <Row gutter={16} className="mb-6">
          <Col span={6}>
            <Card>
              <div className="flex items-center">
                <ExclamationCircleOutlined className="text-2xl text-red-500 mr-3" />
                <div>
                  <div className="text-2xl font-bold">{stats.criticalAlerts || 0}</div>
                  <div className="text-gray-500">Critical</div>
                </div>
              </div>
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <div className="flex items-center">
                <WarningOutlined className="text-2xl text-orange-500 mr-3" />
                <div>
                  <div className="text-2xl font-bold">{stats.alertsByType?.filter(a => a.type === 'high').length || 0}</div>
                  <div className="text-gray-500">High Priority</div>
                </div>
              </div>
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <div className="flex items-center">
                <InfoCircleOutlined className="text-2xl text-yellow-500 mr-3" />
                <div>
                  <div className="text-2xl font-bold">{stats.alertsByType?.filter(a => a.type === 'medium').length || 0}</div>
                  <div className="text-gray-500">Medium Priority</div>
                </div>
              </div>
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <div className="flex items-center">
                <BellOutlined className="text-2xl text-blue-500 mr-3" />
                <div>
                  <div className="text-2xl font-bold">{stats.totalAlerts || 0}</div>
                  <div className="text-gray-500">Total Alerts</div>
                </div>
              </div>
            </Card>
          </Col>
        </Row>
      )}

      {/* Filter Controls */}
      <Card className="mb-6">
        <Row gutter={16} align="middle">
          <Col span={6}>
            <Select
              placeholder="Filter by severity"
              style={{ width: '100%' }}
              allowClear
              onChange={(severity) => setSearchParams({ ...searchParams, severity })}
            >
              <Select.Option value="critical">Critical</Select.Option>
              <Select.Option value="high">High</Select.Option>
              <Select.Option value="medium">Medium</Select.Option>
              <Select.Option value="low">Low</Select.Option>
              <Select.Option value="info">Info</Select.Option>
            </Select>
          </Col>
          <Col span={6}>
            <Select
              placeholder="Filter by type"
              style={{ width: '100%' }}
              allowClear
              onChange={(alertType) => setSearchParams({ ...searchParams, alertType })}
            >
              <Select.Option value="system">System</Select.Option>
              <Select.Option value="device">Device</Select.Option>
              <Select.Option value="task">Task</Select.Option>
              <Select.Option value="sync">Sync</Select.Option>
              <Select.Option value="security">Security</Select.Option>
            </Select>
          </Col>
          <Col span={6}>
            <Select
              placeholder="Filter by status"
              style={{ width: '100%' }}
              allowClear
              onChange={(status) => setSearchParams({ ...searchParams, status })}
            >
              <Select.Option value="unread">Unread</Select.Option>
              <Select.Option value="read">Read</Select.Option>
              <Select.Option value="acknowledged">Acknowledged</Select.Option>
              <Select.Option value="resolved">Resolved</Select.Option>
            </Select>
          </Col>
        </Row>
      </Card>

      {/* Alerts List */}
      <Card>
        <List
          loading={loading}
          dataSource={alerts || []} // Ensure it's always an array
          renderItem={(alert) => (
            <List.Item
              key={alert.id}
              className={`${!alert.isRead ? 'bg-blue-50' : ''} hover:bg-gray-50`}
              actions={[
                <Tooltip title="Mark as Read" key="read">
                  <Button
                    type="link"
                    icon={<CheckCircleOutlined />}
                    onClick={() => quickAccessService.markAlertAsRead(alert.id)}
                    disabled={alert.isRead}
                  />
                </Tooltip>,
                <Tooltip title="Dismiss" key="dismiss">
                  <Button
                    type="link"
                    danger
                    icon={<CloseOutlined />}
                    onClick={() => handleDismissAlert(alert.id)}
                  />
                </Tooltip>,
              ]}
            >
              <List.Item.Meta
                avatar={
                  <div className="flex items-center">
                    {getSeverityIcon(alert.severity)}
                    {!alert.isRead && <Badge dot />}
                  </div>
                }
                title={
                  <div className="flex items-center space-x-2">
                    <Text strong={!alert.isRead}>{alert.title}</Text>
                    <Tag color={getSeverityColor(alert.severity)}>
                      {alert.severity.toUpperCase()}
                    </Tag>
                    <Tag>{alert.alertType?.toUpperCase()}</Tag>
                  </div>
                }
                description={
                  <div>
                    <Text>{alert.description}</Text>
                    <div className="mt-2 text-sm text-gray-500">
                      <Text type="secondary">
                        {new Date(alert.createdAt).toLocaleString()}
                        {alert.deviceId && ` • Device: ${alert.deviceId}`}
                        {alert.userName && ` • User: ${alert.userName}`}
                      </Text>
                    </div>
                  </div>
                }
              />
            </List.Item>
          )}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} alerts`,
          }}
        />
      </Card>
    </div>
  );
}
