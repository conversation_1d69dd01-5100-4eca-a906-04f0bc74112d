﻿'use client';

import React, { useState, useEffect } from 'react';
import { Card, List, Avatar, Badge, Progress, Space, Tag, Row, Col, Statistic, Typography, Select, Input, Button, Tooltip } from 'antd';
import { UserOutlined, ClockCircleOutlined, CheckCircleOutlined, EnvironmentOutlined, SearchOutlined, FilterOutlined, ReloadOutlined } from '@ant-design/icons';
import { quickAccessService } from '@/services/quickaccess.service';
import type { TodayAssignmentDto, TodayAssignmentSearchParams, TodayAssignmentStats } from '@/types/quickaccess';

const { Title, Text } = Typography;
const { Search } = Input;

export default function TodayAssignmentsPage() {
  const [assignments, setAssignments] = useState<TodayAssignmentDto[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchParams, setSearchParams] = useState<TodayAssignmentSearchParams>({});
  const [stats, setStats] = useState<TodayAssignmentStats | null>(null);

  useEffect(() => {
    fetchAssignments();
    fetchStats();
    // Set up auto-refresh every 5 minutes
    const interval = setInterval(() => {
      fetchAssignments();
      fetchStats();
    }, 5 * 60 * 1000);
    
    return () => clearInterval(interval);
  }, []);

  const fetchAssignments = async () => {
    setLoading(true);
    try {
      const data = await quickAccessService.getTodayAssignments(searchParams);
      setAssignments(data);
    } catch (error) {
      console.error('Failed to fetch today assignments:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchStats = async () => {
    try {
      const data = await quickAccessService.getTodayAssignmentStats();
      setStats(data);
    } catch (error) {
      console.error('Failed to fetch stats:', error);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'completed':
        return 'green';
      case 'in_progress':
        return 'blue';
      case 'pending':
        return 'orange';
      case 'overdue':
        return 'red';
      default:
        return 'default';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority.toLowerCase()) {
      case 'high':
        return 'red';
      case 'medium':
        return 'orange';
      case 'low':
        return 'green';
      default:
        return 'default';
    }
  };

  const handleRefresh = () => {
    fetchAssignments();
    fetchStats();
  };

  return (
    <div className="p-6">
      <div className="mb-6">
      </div>

      {/* Statistics Cards */}
      {stats && (
        <Row gutter={16} className="mb-6">
          <Col span={6}>
            <Card>
              <Statistic
                title="Total Assignments"
                value={stats.totalAssignments}
                prefix={<UserOutlined />}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="Completed"
                value={stats.completedAssignments}
                prefix={<CheckCircleOutlined />}
                valueStyle={{ color: '#3f8600' }}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="In Progress"
                value={stats.inProgressAssignments}
                prefix={<ClockCircleOutlined />}
                valueStyle={{ color: '#1890ff' }}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="Overdue"
                value={stats.overdueAssignments}
                prefix={<ClockCircleOutlined />}
                valueStyle={{ color: '#cf1322' }}
              />
            </Card>
          </Col>
        </Row>
      )}

      {/* Filters */}
      <Card className="mb-6">
        <Row gutter={16}>
          <Col span={8}>
            <Search
              placeholder="Search by technician or task"
              allowClear
              onSearch={(value) => setSearchParams({ ...searchParams, searchTerm: value })}
              style={{ width: '100%' }}
            />
          </Col>
          <Col span={4}>
            <Select
              placeholder="Filter by status"
              style={{ width: '100%' }}
              allowClear
              onChange={(status) => setSearchParams({ ...searchParams, status })}
            >
              <Select.Option value="pending">Pending</Select.Option>
              <Select.Option value="in_progress">In Progress</Select.Option>
              <Select.Option value="completed">Completed</Select.Option>
              <Select.Option value="overdue">Overdue</Select.Option>
            </Select>
          </Col>
          <Col span={4}>
            <Select
              placeholder="Filter by priority"
              style={{ width: '100%' }}
              allowClear
              onChange={(priority) => setSearchParams({ ...searchParams, priority })}
            >
              <Select.Option value="high">High</Select.Option>
              <Select.Option value="medium">Medium</Select.Option>
              <Select.Option value="low">Low</Select.Option>
            </Select>
          </Col>
          <Col span={4}>
            <Select
              placeholder="Filter by area"
              style={{ width: '100%' }}
              allowClear
              onChange={(area) => setSearchParams({ ...searchParams, area })}
            >
              <Select.Option value="north">North</Select.Option>
              <Select.Option value="south">South</Select.Option>
              <Select.Option value="east">East</Select.Option>
              <Select.Option value="west">West</Select.Option>
              <Select.Option value="central">Central</Select.Option>
            </Select>
          </Col>
          <Col span={4}>
            <Button 
              type="primary" 
              icon={<FilterOutlined />} 
              onClick={fetchAssignments}
              loading={loading}
              style={{ width: '100%' }}
            >
              Apply Filters
            </Button>
          </Col>
        </Row>
      </Card>

      {/* Assignment List */}
      <Card>
        <List
          loading={loading}
          dataSource={assignments}
          renderItem={(assignment) => (
            <List.Item
              key={assignment.id}
              actions={[
                <Tooltip title="View Details">
                  <Button type="link">View</Button>
                </Tooltip>,
                <Tooltip title="Update Status">
                  <Button type="link">Update</Button>
                </Tooltip>
              ]}
            >
              <List.Item.Meta
                avatar={
                  <Badge 
                    dot 
                    color={getStatusColor(assignment.status)}
                  >
                    <Avatar 
                      src={assignment.technicianAvatar} 
                      icon={<UserOutlined />}
                    />
                  </Badge>
                }
                title={
                  <Space>
                    <Text strong>{assignment.taskTitle}</Text>
                    <Tag color={getPriorityColor(assignment.priority)}>
                      {assignment.priority.toUpperCase()}
                    </Tag>
                    <Tag color={getStatusColor(assignment.status)}>
                      {assignment.status.replace('_', ' ').toUpperCase()}
                    </Tag>
                  </Space>
                }
                description={
                  <div>
                    <div className="mb-2">
                      <Space>
                        <UserOutlined />
                        <Text>{assignment.technicianName}</Text>
                        <EnvironmentOutlined />
                        <Text>{assignment.location}</Text>
                        <ClockCircleOutlined />
                        <Text>{assignment.estimatedDuration}h</Text>
                      </Space>
                    </div>
                    {assignment.description && (
                      <Text type="secondary">{assignment.description}</Text>
                    )}
                    <div className="mt-2">
                      <Progress 
                        percent={assignment.progressPercentage} 
                        size="small"
                        status={assignment.status === 'overdue' ? 'exception' : 'normal'}
                      />
                    </div>
                    <div className="mt-2">
                      <Space size="middle">
                        <Text type="secondary">
                          Scheduled: {new Date(assignment.scheduledTime).toLocaleTimeString()}
                        </Text>
                        {assignment.startedAt && (
                          <Text type="secondary">
                            Started: {new Date(assignment.startedAt).toLocaleTimeString()}
                          </Text>
                        )}
                        {assignment.expectedCompletionTime && (
                          <Text type="secondary">
                            Expected: {new Date(assignment.expectedCompletionTime).toLocaleTimeString()}
                          </Text>
                        )}
                      </Space>
                    </div>
                  </div>
                }
              />
            </List.Item>
          )}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `Total ${total} assignments`,
          }}
        />
      </Card>
    </div>
  );
}
