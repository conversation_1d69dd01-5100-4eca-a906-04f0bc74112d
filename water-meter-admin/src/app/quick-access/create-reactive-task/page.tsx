﻿'use client';

import React, { useState, useEffect } from 'react';
import { Card, Form, Button, Input, Select, DatePicker, Row, Col, Typography, message, Spin, Upload, Space, InputNumber, Switch } from 'antd';
import { PlusOutlined, UploadOutlined, SendOutlined, SaveOutlined, EnvironmentOutlined, UserOutlined } from '@ant-design/icons';
import { quickAccessService } from '@/services/quickaccess.service';
import type { CreateReactiveTaskDto } from '@/types/quickaccess';
import dayjs from 'dayjs';

const { Title, Text } = Typography;
const { TextArea } = Input;
const { Option } = Select;

export default function CreateReactiveTaskPage() {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [urgentMode, setUrgentMode] = useState(false);
  const [availableUsers, setAvailableUsers] = useState<any[]>([]);
  const [attachments, setAttachments] = useState<any[]>([]);

  useEffect(() => {
    fetchAvailableUsers();
  }, []);

  const fetchAvailableUsers = async () => {
    setLoading(true);
    try {
      // Mock data for available users
      const mockUsers = [
        { id: 1, name: 'John Smith', role: 'Field Technician', status: 'available' },
        { id: 2, name: 'Sarah Johnson', role: 'Senior Technician', status: 'available' },
        { id: 3, name: 'Mike Davis', role: 'Maintenance Specialist', status: 'busy' },
        { id: 4, name: 'Lisa Wilson', role: 'Field Supervisor', status: 'available' },
        { id: 5, name: 'Tom Brown', role: 'Emergency Response', status: 'available' }
      ];
      setAvailableUsers(mockUsers);
    } catch (error) {
      console.error('Failed to fetch available users:', error);
      message.error('Failed to load available users');
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (values: any) => {
    setSubmitting(true);
    try {
      const taskData: CreateReactiveTaskDto = {
        ...values,
        reportedBy: 'Current User', // In real app, get from auth context
        expectedCompletionDate: values.expectedCompletionDate?.format('YYYY-MM-DD HH:mm:ss'),
        attachments: attachments.map(file => file.originFileObj),
      };

      await quickAccessService.createReactiveTask(taskData);
      message.success('Reactive task created successfully');
      
      // Reset form after successful submission
      form.resetFields();
      setAttachments([]);
      setUrgentMode(false);
    } catch (error) {
      console.error('Failed to create reactive task:', error);
      message.error('Failed to create reactive task');
    } finally {
      setSubmitting(false);
    }
  };

  const handleSaveDraft = async () => {
    try {
      const values = await form.validateFields();
      // Save as draft logic here
      message.success('Task saved as draft');
    } catch (error) {
      message.warning('Please fill in required fields to save draft');
    }
  };

  const handleUrgentModeChange = (checked: boolean) => {
    setUrgentMode(checked);
    if (checked) {
      form.setFieldsValue({
        priority: 'emergency',
        expectedCompletionDate: dayjs().add(2, 'hours')
      });
    }
  };

  const uploadProps = {
    beforeUpload: (file: any) => {
      const isValidType = file.type.startsWith('image/') || file.type === 'application/pdf';
      if (!isValidType) {
        message.error('You can only upload image files or PDF documents!');
        return false;
      }
      const isLt10M = file.size / 1024 / 1024 < 10;
      if (!isLt10M) {
        message.error('File must be smaller than 10MB!');
        return false;
      }
      return false; // Prevent auto upload, handle manually
    },
    onChange: (info: any) => {
      setAttachments(info.fileList);
    },
    fileList: attachments,
  };

  const taskTypeOptions = [
    { value: 'emergency_reading', label: 'Emergency Reading', urgent: true },
    { value: 'maintenance_request', label: 'Maintenance Request', urgent: false },
    { value: 'customer_complaint', label: 'Customer Complaint', urgent: false },
    { value: 'equipment_failure', label: 'Equipment Failure', urgent: true },
    { value: 'leak_detection', label: 'Leak Detection', urgent: true },
    { value: 'other', label: 'Other', urgent: false }
  ];

  const priorityOptions = [
    { value: 'low', label: 'Low Priority', color: 'green' },
    { value: 'medium', label: 'Medium Priority', color: 'orange' },
    { value: 'high', label: 'High Priority', color: 'red' },
    { value: 'critical', label: 'Critical', color: 'red' },
    { value: 'emergency', label: 'Emergency', color: 'red' }
  ];

  return (
    <div className="p-6">
      <div className="mb-6">
      </div>

      {urgentMode && (
        <Card className="mb-6 border-red-300 bg-red-50">
          <div className="flex items-center text-red-600">
            <SendOutlined className="mr-2" />
            <Text strong>Urgent Mode Active - Task will be prioritized and assigned immediately</Text>
          </div>
        </Card>
      )}

      <Spin spinning={loading}>
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          initialValues={{
            priority: 'medium',
            taskType: 'maintenance_request',
            estimatedDuration: 60
          }}
        >
          <Row gutter={24}>
            <Col span={12}>
              <Card title="Task Details" className="mb-6">
                <Form.Item
                  name="taskType"
                  label="Task Type"
                  rules={[{ required: true, message: 'Please select task type' }]}
                >
                  <Select placeholder="Select task type">
                    {taskTypeOptions.map(option => (
                      <Option key={option.value} value={option.value}>
                        <div className="flex justify-between items-center">
                          <span>{option.label}</span>
                          {option.urgent && <Text type="danger" className="text-xs">URGENT</Text>}
                        </div>
                      </Option>
                    ))}
                  </Select>
                </Form.Item>

                <Form.Item
                  name="priority"
                  label="Priority Level"
                  rules={[{ required: true, message: 'Please select priority' }]}
                >
                  <Select placeholder="Select priority">
                    {priorityOptions.map(option => (
                      <Option key={option.value} value={option.value}>
                        <Text style={{ color: option.color }}>{option.label}</Text>
                      </Option>
                    ))}
                  </Select>
                </Form.Item>

                <Form.Item
                  name="title"
                  label="Task Title"
                  rules={[{ required: true, message: 'Please enter task title' }]}
                >
                  <Input placeholder="Brief description of the task" maxLength={100} />
                </Form.Item>

                <Form.Item
                  name="description"
                  label="Detailed Description"
                  rules={[{ required: true, message: 'Please enter task description' }]}
                >
                  <TextArea
                    rows={4}
                    placeholder="Provide detailed information about the task, including any specific requirements or instructions"
                    maxLength={1000}
                    showCount
                  />
                </Form.Item>

                <Form.Item
                  name="estimatedDuration"
                  label="Estimated Duration (minutes)"
                >
                  <InputNumber
                    min={15}
                    max={480}
                    step={15}
                    placeholder="60"
                    style={{ width: '100%' }}
                  />
                </Form.Item>
              </Card>
            </Col>

            <Col span={12}>
              <Card title="Location & Assignment" className="mb-6">
                <Form.Item
                  name="location"
                  label="Location"
                  rules={[{ required: true, message: 'Please enter location' }]}
                >
                  <Input
                    placeholder="Street address, building, or area description"
                    prefix={<EnvironmentOutlined />}
                  />
                </Form.Item>

                <Form.Item
                  name="waterMeterId"
                  label="Water Meter ID (if applicable)"
                >
                  <Input placeholder="Enter water meter ID if task is meter-related" />
                </Form.Item>

                <Form.Item
                  name="customerId"
                  label="Customer ID (if applicable)"
                >
                  <InputNumber
                    placeholder="Enter customer ID if task is customer-related"
                    style={{ width: '100%' }}
                  />
                </Form.Item>

                <Form.Item
                  name="assignedUserId"
                  label="Assign To"
                  rules={urgentMode ? [{ required: true, message: 'Please assign to someone for urgent tasks' }] : []}
                >
                  <Select
                    placeholder="Select a team member"
                    allowClear
                    showSearch
                    filterOption={(input, option) =>
                      option?.children?.toLowerCase().indexOf(input.toLowerCase()) >= 0
                    }
                  >
                    {availableUsers.map(user => (
                      <Option key={user.id} value={user.id} disabled={user.status !== 'available'}>
                        <div className="flex justify-between items-center">
                          <span>
                            <UserOutlined className="mr-2" />
                            {user.name} - {user.role}
                          </span>
                          <Text type={user.status === 'available' ? 'success' : 'secondary'}>
                            {user.status}
                          </Text>
                        </div>
                      </Option>
                    ))}
                  </Select>
                </Form.Item>

                <Form.Item
                  name="expectedCompletionDate"
                  label="Expected Completion Date"
                  rules={urgentMode ? [{ required: true, message: 'Please set completion date for urgent tasks' }] : []}
                >
                  <DatePicker
                    showTime
                    style={{ width: '100%' }}
                    placeholder="Select date and time"
                    disabledDate={(current) => current && current < dayjs().startOf('day')}
                  />
                </Form.Item>
              </Card>

              <Card title="Additional Information">
                <Form.Item
                  name="gpsCoordinates"
                  label="GPS Coordinates (optional)"
                >
                  <Input placeholder="Latitude, Longitude (e.g., -36.8485, 174.7633)" />
                </Form.Item>

                <Form.Item
                  name="attachments"
                  label="Attachments"
                >
                  <Upload
                    {...uploadProps}
                    multiple
                    listType="text"
                  >
                    <Button icon={<UploadOutlined />}>
                      Upload Photos or Documents
                    </Button>
                  </Upload>
                  <Text type="secondary" className="text-xs">
                    Support: Images, PDF files (max 10MB each)
                  </Text>
                </Form.Item>
              </Card>
            </Col>
          </Row>

          <Card className="mt-6">
            <div className="flex justify-end space-x-4">
              <Button
                onClick={handleSaveDraft}
                icon={<SaveOutlined />}
              >
                Save as Draft
              </Button>
              <Button
                type="primary"
                htmlType="submit"
                loading={submitting}
                icon={<SendOutlined />}
                size="large"
              >
                {urgentMode ? 'Create Urgent Task' : 'Create Task'}
              </Button>
            </div>
          </Card>
        </Form>
      </Spin>
    </div>
  );
}
