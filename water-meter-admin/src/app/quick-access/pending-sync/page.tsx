'use client';

import React, { useState, useEffect } from 'react';
import { Card, Table, Button, Space, Tag, Progress, Statistic, Row, Col, Typography, Select, Input, Badge, Tooltip, message } from 'antd';
import { SyncOutlined, CloudUploadOutlined, ExclamationCircleOutlined, CheckCircleOutlined, ReloadOutlined, PlayCircleOutlined } from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import { quickAccessService } from '@/services/quickaccess.service';
import type { PendingSyncDto, PendingSyncSearchParams, PendingSyncStats } from '@/types/quickaccess';

const { Title, Text } = Typography;
const { Search } = Input;

export default function PendingSyncPage() {
  const [syncData, setSyncData] = useState<PendingSyncDto[]>([]);
  const [loading, setLoading] = useState(false);
  const [syncing, setSyncing] = useState(false);
  const [searchParams, setSearchParams] = useState<PendingSyncSearchParams>({});
  const [stats, setStats] = useState<PendingSyncStats | null>(null);
  const [selectedRows, setSelectedRows] = useState<number[]>([]);

  useEffect(() => {
    fetchSyncData();
    fetchStats();
    // Auto refresh every 30 seconds
    const interval = setInterval(() => {
      fetchSyncData();
      fetchStats();
    }, 30000);
    
    return () => clearInterval(interval);
  }, []);

  const fetchSyncData = async () => {
    setLoading(true);
    try {
      const data = await quickAccessService.getPendingSyncData(searchParams);
      setSyncData(data);
    } catch (error) {
      console.error('Failed to fetch pending sync data:', error);
      message.error('Failed to load pending sync data');
    } finally {
      setLoading(false);
    }
  };

  const fetchStats = async () => {
    try {
      const data = await quickAccessService.getPendingSyncStats();
      setStats(data);
    } catch (error) {
      console.error('Failed to fetch sync stats:', error);
    }
  };

  const handleSyncAll = async () => {
    setSyncing(true);
    try {
      await quickAccessService.syncAllData();
      message.success('All data synchronized successfully');
      fetchSyncData();
      fetchStats();
    } catch (error) {
      console.error('Failed to sync all data:', error);
      message.error('Failed to synchronize data');
    } finally {
      setSyncing(false);
    }
  };

  const handleSyncSelected = async () => {
    if (selectedRows.length === 0) {
      message.warning('Please select items to sync');
      return;
    }
    
    setSyncing(true);
    try {
      await quickAccessService.syncSelectedData(selectedRows);
      message.success(`${selectedRows.length} items synchronized successfully`);
      setSelectedRows([]);
      fetchSyncData();
      fetchStats();
    } catch (error) {
      console.error('Failed to sync selected data:', error);
      message.error('Failed to synchronize selected data');
    } finally {
      setSyncing(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'pending':
        return 'orange';
      case 'syncing':
        return 'blue';
      case 'completed':
        return 'green';
      case 'failed':
        return 'red';
      case 'conflict':
        return 'purple';
      default:
        return 'default';
    }
  };

  const getDataTypeIcon = (dataType: string) => {
    switch (dataType.toLowerCase()) {
      case 'meter_reading':
        return '📊';
      case 'photo':
        return '📷';
      case 'task_update':
        return '📝';
      case 'user_data':
        return '👤';
      case 'device_info':
        return '📱';
      default:
        return '📋';
    }
  };

  const columns: ColumnsType<PendingSyncDto> = [
    {
      title: 'Type',
      dataIndex: 'dataType',
      key: 'dataType',
      render: (type) => (
        <Space>
          <span>{getDataTypeIcon(type)}</span>
          <Text>{type.replace('_', ' ').toUpperCase()}</Text>
        </Space>
      ),
    },
    {
      title: 'Description',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
    },
    {
      title: 'Device',
      dataIndex: 'deviceId',
      key: 'deviceId',
      render: (deviceId) => <Text code>{deviceId}</Text>,
    },
    {
      title: 'Size',
      dataIndex: 'dataSize',
      key: 'dataSize',
      render: (size) => {
        if (size < 1024) return `${size} B`;
        if (size < 1024 * 1024) return `${(size / 1024).toFixed(1)} KB`;
        return `${(size / 1024 / 1024).toFixed(1)} MB`;
      },
    },
    {
      title: 'Status',
      dataIndex: 'syncStatus',
      key: 'syncStatus',
      render: (status) => (
        <Tag color={getStatusColor(status)}>
          {status === 'syncing' && <SyncOutlined spin />}
          {status.toUpperCase()}
        </Tag>
      ),
    },
    {
      title: 'Progress',
      dataIndex: 'progressPercentage',
      key: 'progressPercentage',
      render: (progress, record) => (
        <Progress
          percent={progress}
          size="small"
          status={record.syncStatus === 'failed' ? 'exception' : 'normal'}
          showInfo={false}
        />
      ),
    },
    {
      title: 'Priority',
      dataIndex: 'priority',
      key: 'priority',
      render: (priority) => (
        <Badge
          color={priority === 'high' ? 'red' : priority === 'medium' ? 'orange' : 'green'}
          text={priority.toUpperCase()}
        />
      ),
    },
    {
      title: 'Created',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (date) => new Date(date).toLocaleString(),
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record) => (
        <Space>
          {record.syncStatus === 'failed' && (
            <Tooltip title="Retry Sync">
              <Button
                type="link"
                icon={<ReloadOutlined />}
                onClick={() => quickAccessService.retrySingleSync(record.id)}
              />
            </Tooltip>
          )}
          {record.syncStatus === 'pending' && (
            <Tooltip title="Sync Now">
              <Button
                type="link"
                icon={<PlayCircleOutlined />}
                onClick={() => quickAccessService.syncSingleData(record.id)}
              />
            </Tooltip>
          )}
        </Space>
      ),
    },
  ];

  const rowSelection = {
    selectedRowKeys: selectedRows,
    onChange: (selectedRowKeys: React.Key[]) => {
      setSelectedRows(selectedRowKeys as number[]);
    },
    getCheckboxProps: (record: PendingSyncDto) => ({
      disabled: record.syncStatus === 'syncing' || record.syncStatus === 'completed',
    }),
  };

  return (
    <div className="p-6">
      <div className="mb-6">
      </div>

      {/* Statistics Cards */}
      {stats && (
        <Row gutter={16} className="mb-6">
          <Col span={6}>
            <Card>
              <Statistic
                title="Total Pending"
                value={stats.totalPending}
                prefix={<SyncOutlined />}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="Failed Items"
                value={stats.failedItems}
                prefix={<ExclamationCircleOutlined />}
                valueStyle={{ color: '#cf1322' }}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="Completed Today"
                value={stats.completedToday}
                prefix={<CheckCircleOutlined />}
                valueStyle={{ color: '#3f8600' }}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="Total Size"
                value={stats.totalDataSize}
                precision={1}
                suffix="MB"
                prefix={<CloudUploadOutlined />}
              />
            </Card>
          </Col>
        </Row>
      )}

      {/* Control Panel */}
      <Card className="mb-6">
        <Row gutter={16} align="middle">
          <Col span={6}>
            <Search
              placeholder="Search by description or device"
              allowClear
              onSearch={(value) => setSearchParams({ ...searchParams, searchTerm: value })}
            />
          </Col>
          <Col span={4}>
            <Select
              placeholder="Filter by status"
              style={{ width: '100%' }}
              allowClear
              onChange={(syncStatus) => setSearchParams({ ...searchParams, syncStatus })}
            >
              <Select.Option value="pending">Pending</Select.Option>
              <Select.Option value="syncing">Syncing</Select.Option>
              <Select.Option value="completed">Completed</Select.Option>
              <Select.Option value="failed">Failed</Select.Option>
              <Select.Option value="conflict">Conflict</Select.Option>
            </Select>
          </Col>
          <Col span={4}>
            <Select
              placeholder="Filter by type"
              style={{ width: '100%' }}
              allowClear
              onChange={(dataType) => setSearchParams({ ...searchParams, dataType })}
            >
              <Select.Option value="meter_reading">Meter Reading</Select.Option>
              <Select.Option value="photo">Photo</Select.Option>
              <Select.Option value="task_update">Task Update</Select.Option>
              <Select.Option value="user_data">User Data</Select.Option>
              <Select.Option value="device_info">Device Info</Select.Option>
            </Select>
          </Col>
          <Col span={4}>
            <Select
              placeholder="Filter by priority"
              style={{ width: '100%' }}
              allowClear
              onChange={(priority) => setSearchParams({ ...searchParams, priority })}
            >
              <Select.Option value="high">High</Select.Option>
              <Select.Option value="medium">Medium</Select.Option>
              <Select.Option value="low">Low</Select.Option>
            </Select>
          </Col>
          <Col span={6}>
            <Space>
              <Button 
                type="primary" 
                onClick={handleSyncSelected}
                disabled={selectedRows.length === 0}
                loading={syncing}
              >
                Sync Selected ({selectedRows.length})
              </Button>
              <Button 
                onClick={() => quickAccessService.retryFailedSync()}
                loading={syncing}
              >
                Retry Failed
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* Data Table */}
      <Card>
        <Table
          columns={columns}
          dataSource={syncData}
          rowKey="id"
          loading={loading}
          rowSelection={rowSelection}
          pagination={{
            pageSize: 20,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`,
          }}
          scroll={{ x: 1200 }}
        />
      </Card>
    </div>
  );
} 