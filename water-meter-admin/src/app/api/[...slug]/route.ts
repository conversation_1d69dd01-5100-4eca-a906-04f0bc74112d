import { NextRequest, NextResponse } from 'next/server'

const BACKEND_URL = process.env.BACKEND_URL || 'http://localhost:5000'

async function proxyRequest(
  request: NextRequest,
  method: string,
  endpoint: string
) {
  try {
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
    }

    // Forward authorization header if present
    const authHeader = request.headers.get('authorization')
    if (authHeader) {
      headers['Authorization'] = authHeader
    }

    const options: RequestInit = {
      method,
      headers,
    }

    // Add body for POST, PUT, PATCH requests
    if (['POST', 'PUT', 'PATCH'].includes(method)) {
      const body = await request.text()
      if (body) {
        options.body = body
      }
    }

    const response = await fetch(`${BACKEND_URL}${endpoint}`, options)

    // Handle different response types
    let responseData
    const contentType = response.headers.get('content-type')
    
    if (contentType && contentType.includes('application/json')) {
      responseData = await response.json()
    } else {
      responseData = await response.text()
    }

    return NextResponse.json(responseData, {
      status: response.status,
      statusText: response.statusText,
    })
  } catch (error) {
    console.error(`Error proxying ${method} ${endpoint}:`, error)
    return NextResponse.json(
      { error: 'Proxy server error' },
      { status: 500 }
    )
  }
}

export async function GET(
  request: NextRequest,
  { params }: { params: { slug: string[] } }
) {
  const endpoint = `/api/${params.slug.join('/')}`
  const url = new URL(request.url)
  const searchParams = url.searchParams.toString()
  const fullEndpoint = searchParams ? `${endpoint}?${searchParams}` : endpoint
  
  return proxyRequest(request, 'GET', fullEndpoint)
}

export async function POST(
  request: NextRequest,
  { params }: { params: { slug: string[] } }
) {
  const endpoint = `/api/${params.slug.join('/')}`
  return proxyRequest(request, 'POST', endpoint)
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { slug: string[] } }
) {
  const endpoint = `/api/${params.slug.join('/')}`
  return proxyRequest(request, 'PUT', endpoint)
}

export async function PATCH(
  request: NextRequest,
  { params }: { params: { slug: string[] } }
) {
  const endpoint = `/api/${params.slug.join('/')}`
  return proxyRequest(request, 'PATCH', endpoint)
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { slug: string[] } }
) {
  const endpoint = `/api/${params.slug.join('/')}`
  return proxyRequest(request, 'DELETE', endpoint)
} 