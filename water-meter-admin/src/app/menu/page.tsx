'use client';

import { useEffect, useState } from 'react';
import { Table, Button, Modal, Form, Input, message, Popconfirm } from 'antd';
import { EditOutlined, DeleteOutlined, PlusOutlined } from '@ant-design/icons';
import axios from 'axios';

interface Menu {
  id: number;
  name: string;
  code: string;
  parentId?: number;
  permissionId?: number;
}

export default function MenuPage() {
  const [menus, setMenus] = useState<Menu[]>([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingMenu, setEditingMenu] = useState<Menu | null>(null);
  const [form] = Form.useForm();

  useEffect(() => {
    fetchMenus();
  }, []);

  const fetchMenus = async () => {
    setLoading(true);
    try {
      const res = await axios.get('/api/menu');
      setMenus(res.data);
    } catch (error) {
      message.error('Failed to fetch menus');
    } finally {
      setLoading(false);
    }
  };

  const handleAdd = () => {
    setEditingMenu(null);
    form.resetFields();
    setModalVisible(true);
  };

  const handleEdit = (menu: Menu) => {
    setEditingMenu(menu);
    form.setFieldsValue(menu);
    setModalVisible(true);
  };

  const handleDelete = async (id: number) => {
    try {
      await axios.delete(`/api/menu/${id}`);
      message.success('Menu deleted');
      fetchMenus();
    } catch (error) {
      message.error('Failed to delete menu');
    }
  };

  const handleOk = async () => {
    try {
      const values = await form.validateFields();
      if (editingMenu) {
        await axios.put(`/api/menu/${editingMenu.id}`, values);
        message.success('Menu updated');
      } else {
        await axios.post('/api/menu', values);
        message.success('Menu created');
      }
      setModalVisible(false);
      fetchMenus();
    } catch (error) {
      message.error('Failed to save menu');
    }
  };

  const columns = [
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: 'Code',
      dataIndex: 'code',
      key: 'code',
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_: any, record: Menu) => (
        <>
          <Button icon={<EditOutlined />} onClick={() => handleEdit(record)} style={{ marginRight: 8 }} />
          <Popconfirm title="Are you sure to delete this menu?" onConfirm={() => handleDelete(record.id)}>
            <Button icon={<DeleteOutlined />} danger />
          </Popconfirm>
        </>
      ),
    },
  ];

  return (
    <div className="p-8">
      <div className="flex justify-end items-center mb-4">
        <Button type="primary" icon={<PlusOutlined />} onClick={handleAdd}>
          Add Menu
        </Button>
      </div>
      <Table rowKey="id" columns={columns} dataSource={menus} loading={loading} />
      <Modal
        title={editingMenu ? 'Edit Menu' : 'Add Menu'}
        open={modalVisible}
        onOk={handleOk}
        onCancel={() => setModalVisible(false)}
        okText="Save"
      >
        <Form form={form} layout="vertical">
          <Form.Item name="name" label="Name" rules={[{ required: true, message: 'Please input the menu name!' }]}> 
            <Input />
          </Form.Item>
          <Form.Item name="code" label="Code" rules={[{ required: true, message: 'Please input the menu code!' }]}> 
            <Input />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
} 