﻿'use client';

import React, { useState, useEffect } from 'react';
import { Card, Table, Button, Space, Modal, Form, Input, Select, Tag, Progress, Statistic, Row, Col, Typography, Tabs, Upload, message, Popconfirm, Tooltip } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined, DownloadOutlined, UploadOutlined, AndroidOutlined, AppleOutlined, WindowsOutlined, StarOutlined, CheckCircleOutlined, ClockCircleOutlined, ExclamationCircleOutlined } from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import { mobileService } from '@/services/mobile.service';
import type { AppVersionDto, AppVersionSearchDto, AppVersionStatsDto } from '@/types/mobile';

const { Title, Text } = Typography;
const { TabPane } = Tabs;

export default function AppVersionsPage() {
  const [versions, setVersions] = useState<AppVersionDto[]>([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingVersion, setEditingVersion] = useState<AppVersionDto | null>(null);
  const [searchParams, setSearchParams] = useState<AppVersionSearchDto>({
    page: 1,
    pageSize: 10
  });
  const [stats, setStats] = useState<AppVersionStatsDto | null>(null);
  const [form] = Form.useForm();

  useEffect(() => {
    fetchVersions();
    fetchStats();
  }, []);

  const fetchVersions = async () => {
    setLoading(true);
    try {
      const data = await mobileService.getAppVersions(searchParams);
      setVersions(data.versions);
    } catch (error) {
      console.error('Failed to fetch app versions:', error);
      message.error('Failed to load app versions');
    } finally {
      setLoading(false);
    }
  };

  const fetchStats = async () => {
    try {
      const data = await mobileService.getAppVersionStats();
      setStats(data);
    } catch (error) {
      console.error('Failed to fetch stats:', error);
    }
  };

  const handleCreate = () => {
    setEditingVersion(null);
    form.resetFields();
    setModalVisible(true);
  };

  const handleEdit = (version: AppVersionDto) => {
    setEditingVersion(version);
    form.setFieldsValue(version);
    setModalVisible(true);
  };

  const handleDelete = async (id: number) => {
    try {
      await mobileService.deleteAppVersion(id);
      message.success('App version deleted successfully');
      fetchVersions();
      fetchStats();
    } catch (error) {
      console.error('Failed to delete app version:', error);
      message.error('Failed to delete app version');
    }
  };

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      
      if (editingVersion) {
        await mobileService.updateAppVersion(editingVersion.id, values);
        message.success('App version updated successfully');
      } else {
        await mobileService.createAppVersion(values);
        message.success('App version created successfully');
      }
      
      setModalVisible(false);
      fetchVersions();
      fetchStats();
    } catch (error) {
      console.error('Failed to save app version:', error);
      message.error('Failed to save app version');
    }
  };

  const getPlatformIcon = (platform: string) => {
    switch (platform.toLowerCase()) {
      case 'android':
        return <AndroidOutlined style={{ color: '#a4c639' }} />;
      case 'ios':
        return <AppleOutlined style={{ color: '#000' }} />;
      case 'windows':
        return <WindowsOutlined style={{ color: '#0078d4' }} />;
      default:
        return null;
    }
  };

  const getReleaseStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'released':
        return 'green';
      case 'testing':
        return 'orange';
      case 'development':
        return 'red';
      case 'deprecated':
        return 'gray';
      default:
        return 'default';
    }
  };

  const columns: ColumnsType<AppVersionDto> = [
    {
      title: 'Version',
      dataIndex: 'versionNumber',
      key: 'versionNumber',
      render: (text, record) => (
        <Space>
          {getPlatformIcon(record.platform)}
          <Text strong>{text}</Text>
          <Text type="secondary">({record.versionCode})</Text>
        </Space>
      ),
    },
    {
      title: 'Platform',
      dataIndex: 'platform',
      key: 'platform',
      render: (platform) => (
        <Tag color={platform === 'android' ? 'green' : platform === 'ios' ? 'blue' : 'purple'}>
          {platform.toUpperCase()}
        </Tag>
      ),
    },
    {
      title: 'Release Status',
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <Tag color={getReleaseStatusColor(status)}>{status}</Tag>
      ),
    },
    {
      title: 'File Size',
      dataIndex: 'fileSize',
      key: 'fileSize',
      render: (size) => size ? `${(size / 1024 / 1024).toFixed(1)} MB` : 'N/A',
    },
    {
      title: 'Installs',
      dataIndex: 'installCount',
      key: 'installCount',
      render: (count) => count.toLocaleString(),
    },
    {
      title: 'Rating',
      dataIndex: 'rating',
      key: 'rating',
      render: (rating) => (
        <Space>
          <StarOutlined style={{ color: '#fadb14' }} />
          <Text>{rating ? rating.toFixed(1) : 'N/A'}</Text>
        </Space>
      ),
    },
    {
      title: 'Release Date',
      dataIndex: 'releaseDate',
      key: 'releaseDate',
      render: (date) => new Date(date).toLocaleDateString(),
    },
    {
      title: 'Forced',
      dataIndex: 'isForced',
      key: 'isForced',
      render: (isForced) => isForced ? <CheckCircleOutlined style={{ color: '#52c41a' }} /> : null,
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record) => (
        <Space>
          <Tooltip title="Edit">
            <Button 
              type="text" 
              icon={<EditOutlined />} 
              onClick={() => handleEdit(record)}
            />
          </Tooltip>
          <Tooltip title="Download">
            <Button 
              type="text" 
              icon={<DownloadOutlined />} 
              onClick={() => window.open(record.downloadUrl)}
              disabled={!record.downloadUrl}
            />
          </Tooltip>
          <Popconfirm
            title="Are you sure to delete this version?"
            onConfirm={() => handleDelete(record.id)}
            okText="Yes"
            cancelText="No"
          >
            <Tooltip title="Delete">
              <Button 
                type="text" 
                danger 
                icon={<DeleteOutlined />}
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div className="p-6">
      <div className="mb-6">
      </div>

      {/* Statistics Cards */}
      {stats && (
        <Row gutter={16} className="mb-6">
          <Col span={6}>
            <Card>
              <Statistic
                title="Total Versions"
                value={stats.totalVersions}
                prefix={<AppleOutlined />}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="Active Versions"
                value={stats.activeVersions}
                prefix={<CheckCircleOutlined />}
                valueStyle={{ color: '#3f8600' }}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="Total Downloads"
                value={stats.totalDownloads}
                prefix={<DownloadOutlined />}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="Avg Rating"
                value={stats.averageRating}
                precision={1}
                prefix={<StarOutlined />}
                suffix="/ 5"
                valueStyle={{ color: '#fadb14' }}
              />
            </Card>
          </Col>
        </Row>
      )}

      {/* Main Content */}
      <Card>
        <div className="mb-4 flex justify-between items-center">
          <Space>
            <Select
              placeholder="Filter by platform"
              style={{ width: 150 }}
              allowClear
              onChange={(platform) => setSearchParams({ ...searchParams, platform })}
            >
              <Select.Option value="android">Android</Select.Option>
              <Select.Option value="ios">iOS</Select.Option>
              <Select.Option value="windows">Windows</Select.Option>
            </Select>
            <Select
              placeholder="Filter by status"
              style={{ width: 150 }}
              allowClear
              onChange={(status) => setSearchParams({ ...searchParams, status })}
            >
              <Select.Option value="released">Released</Select.Option>
              <Select.Option value="testing">Testing</Select.Option>
              <Select.Option value="development">Development</Select.Option>
              <Select.Option value="deprecated">Deprecated</Select.Option>
            </Select>
          </Space>
          <Button type="primary" icon={<PlusOutlined />} onClick={handleCreate}>
            Add New Version
          </Button>
        </div>

        <Table
          columns={columns}
          dataSource={versions}
          rowKey="id"
          loading={loading}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `Total ${total} versions`,
          }}
        />
      </Card>

      {/* Create/Edit Modal */}
      <Modal
        title={editingVersion ? 'Edit App Version' : 'Create New App Version'}
        open={modalVisible}
        onOk={handleSubmit}
        onCancel={() => setModalVisible(false)}
        width={600}
        okText={editingVersion ? 'Update' : 'Create'}
      >
        <Form form={form} layout="vertical">
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="versionNumber"
                label="Version Number"
                rules={[{ required: true, message: 'Please enter version number!' }]}
              >
                <Input placeholder="e.g., 1.2.3" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="versionCode"
                label="Version Code"
                rules={[{ required: true, message: 'Please enter version code!' }]}
              >
                <Input type="number" placeholder="e.g., 123" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="platform"
                label="Platform"
                rules={[{ required: true, message: 'Please select platform!' }]}
              >
                <Select placeholder="Select platform">
                  <Select.Option value="android">Android</Select.Option>
                  <Select.Option value="ios">iOS</Select.Option>
                  <Select.Option value="windows">Windows</Select.Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="status"
                label="Release Status"
                rules={[{ required: true, message: 'Please select release status!' }]}
              >
                <Select placeholder="Select status">
                  <Select.Option value="development">Development</Select.Option>
                  <Select.Option value="testing">Testing</Select.Option>
                  <Select.Option value="released">Released</Select.Option>
                  <Select.Option value="deprecated">Deprecated</Select.Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="releaseNotes"
            label="Release Notes"
            rules={[{ required: true, message: 'Please enter release notes!' }]}
          >
            <Input.TextArea rows={4} placeholder="What's new in this version..." />
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="minOsVersion"
                label="Minimum OS Version"
                rules={[{ required: true, message: 'Please enter minimum OS version!' }]}
              >
                <Input placeholder="e.g., Android 6.0, iOS 12.0" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="fileSize"
                label="File Size (bytes)"
              >
                <Input type="number" placeholder="File size in bytes" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="downloadUrl"
                label="Download URL"
              >
                <Input placeholder="https://..." />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Space>
                <Form.Item name="isForced" valuePropName="checked" style={{ marginBottom: 0 }}>
                  <input type="checkbox" />
                </Form.Item>
                <Text>Forced Update</Text>
              </Space>
            </Col>
          </Row>
        </Form>
      </Modal>
    </div>
  );
}
