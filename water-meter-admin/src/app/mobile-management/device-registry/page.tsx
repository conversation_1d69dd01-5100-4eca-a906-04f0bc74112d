﻿'use client';

import React, { useState, useEffect } from 'react';
import { Card, Table, Button, Space, Input, Select, message, Row, Col, Statistic, Tag } from 'antd';
import { PlusOutlined, SearchOutlined, MobileOutlined, AndroidOutlined, AppleOutlined } from '@ant-design/icons';
import { mobileService } from '@/services/mobile.service';
import type { DeviceDto, DeviceSearchDto } from '@/types/mobile';

const DeviceRegistry = () => {
  const [devices, setDevices] = useState<DeviceDto[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchParams, setSearchParams] = useState<DeviceSearchDto>({
    page: 1,
    pageSize: 10
  });

  const loadDevices = async () => {
    setLoading(true);
    try {
      const result = await mobileService.getDevices(searchParams);
      setDevices(result.devices);
    } catch (error) {
      message.error('Failed to load devices');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadDevices();
  }, []);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'success';
      case 'inactive': return 'default';
      case 'suspended': return 'warning';
      case 'maintenance': return 'processing';
      default: return 'default';
    }
  };

  const columns = [
    {
      title: 'Device ID',
      dataIndex: 'deviceId',
      key: 'deviceId',
    },
    {
      title: 'Name',
      dataIndex: 'deviceName',
      key: 'deviceName',
    },
    {
      title: 'Type',
      dataIndex: 'deviceType',
      key: 'deviceType',
    },
    {
      title: 'Brand',
      dataIndex: 'brand',
      key: 'brand',
    },
    {
      title: 'OS',
      key: 'os',
      render: (_, record: DeviceDto) => `${record.osType} ${record.osVersion}`,
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Tag color={getStatusColor(status)}>
          {status.toUpperCase()}
        </Tag>
      )
    },
    {
      title: 'User',
      key: 'user',
      render: (_, record: DeviceDto) => record.userName || 'Unassigned',
    }
  ];

  return (
    <div className="p-6">
      
      <Row gutter={16} className="mb-6">
        <Col span={6}>
          <Card>
            <Statistic title="Total Devices" value={127} prefix={<MobileOutlined />} />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic title="Active Devices" value={95} valueStyle={{ color: '#3f8600' }} />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic title="Android" value={78} prefix={<AndroidOutlined />} />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic title="iOS" value={35} prefix={<AppleOutlined />} />
          </Card>
        </Col>
      </Row>
      
      <Card className="mb-6">
        <Space>
          <Input placeholder="Search devices..." />
          <Select placeholder="Device Type" style={{ width: 150 }}>
            <Select.Option value="smartphone">Smartphone</Select.Option>
            <Select.Option value="tablet">Tablet</Select.Option>
            <Select.Option value="handheld_scanner">Scanner</Select.Option>
          </Select>
          <Select placeholder="Status" style={{ width: 120 }}>
            <Select.Option value="active">Active</Select.Option>
            <Select.Option value="inactive">Inactive</Select.Option>
            <Select.Option value="suspended">Suspended</Select.Option>
            <Select.Option value="maintenance">Maintenance</Select.Option>
          </Select>
          <Button type="primary" icon={<SearchOutlined />}>Search</Button>
        </Space>
      </Card>

      <Card
        title="Devices"
        extra={<Button type="primary" icon={<PlusOutlined />}>Add Device</Button>}
      >
        <Table
          columns={columns}
          dataSource={devices}
          loading={loading}
          rowKey="id"
          pagination={{
            current: searchParams.page,
            pageSize: searchParams.pageSize,
            showSizeChanger: true,
            showQuickJumper: true,
          }}
        />
      </Card>
    </div>
  );
};

export default DeviceRegistry;
