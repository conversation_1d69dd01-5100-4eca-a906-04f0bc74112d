'use client';

import React, { useState, useEffect } from 'react';
import { Card, Table, Button, Space, Modal, Form, Input, Select, Tag, Progress, Statistic, Row, Col, Typography, Badge, message, Popconfirm, Tooltip } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined, PlayCircleOutlined, CheckCircleOutlined, CloseCircleOutlined, ClockCircleOutlined } from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import { mobileService } from '@/services/mobile.service';
import type { CompatibilityTestDto, CompatibilityTestSearchDto, CompatibilityStatsDto } from '@/types/mobile';

const { Title, Text } = Typography;

export default function CompatibilityTestsPage() {
  const [tests, setTests] = useState<CompatibilityTestDto[]>([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingTest, setEditingTest] = useState<CompatibilityTestDto | null>(null);
  const [searchParams, setSearchParams] = useState<CompatibilityTestSearchDto>({
    page: 1,
    pageSize: 10
  });
  const [stats, setStats] = useState<CompatibilityStatsDto | null>(null);
  const [form] = Form.useForm();

  useEffect(() => {
    fetchTests();
    fetchStats();
  }, []);

  const fetchTests = async () => {
    setLoading(true);
    try {
      const data = await mobileService.getCompatibilityTests(searchParams);
      setTests(data.tests);
    } catch (error) {
      console.error('Failed to fetch compatibility tests:', error);
      message.error('Failed to load compatibility tests');
    } finally {
      setLoading(false);
    }
  };

  const fetchStats = async () => {
    try {
      const data = await mobileService.getCompatibilityStats();
      setStats(data);
    } catch (error) {
      console.error('Failed to fetch stats:', error);
    }
  };

  const handleCreate = () => {
    setEditingTest(null);
    form.resetFields();
    setModalVisible(true);
  };

  const handleEdit = (test: CompatibilityTestDto) => {
    setEditingTest(test);
    form.setFieldsValue(test);
    setModalVisible(true);
  };

  const handleDelete = async (id: number) => {
    try {
      await mobileService.deleteCompatibilityTest(id);
      message.success('Compatibility test deleted successfully');
      fetchTests();
      fetchStats();
    } catch (error) {
      console.error('Failed to delete compatibility test:', error);
      message.error('Failed to delete compatibility test');
    }
  };

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      
      if (editingTest) {
        await mobileService.updateCompatibilityTest(editingTest.id, values);
        message.success('Compatibility test updated successfully');
      } else {
        await mobileService.createCompatibilityTest(values);
        message.success('Compatibility test created successfully');
      }
      
      setModalVisible(false);
      fetchTests();
      fetchStats();
    } catch (error) {
      console.error('Failed to save compatibility test:', error);
      message.error('Failed to save compatibility test');
    }
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'passed':
        return 'green';
      case 'failed':
        return 'red';
      case 'running':
        return 'blue';
      case 'pending':
        return 'orange';
      case 'skipped':
        return 'gray';
      default:
        return 'default';
    }
  };

  const columns: ColumnsType<CompatibilityTestDto> = [
    {
      title: 'Test Name',
      dataIndex: 'testName',
      key: 'testName',
      render: (text) => <Text strong>{text}</Text>,
    },
    {
      title: 'Type',
      dataIndex: 'testType',
      key: 'testType',
      render: (type) => (
        <Tag color="blue">{type}</Tag>
      ),
    },
    {
      title: 'App Version',
      dataIndex: 'appVersion',
      key: 'appVersion',
      render: (version) => <Text code>{version}</Text>,
    },
    {
      title: 'Platform',
      dataIndex: 'platform',
      key: 'platform',
      render: (platform) => (
        <Tag color={platform === 'android' ? 'green' : platform === 'ios' ? 'blue' : 'purple'}>
          {platform.toUpperCase()}
        </Tag>
      ),
    },
    {
      title: 'Status',
      dataIndex: 'testStatus',
      key: 'testStatus',
      render: (status) => (
        <Tag color={getStatusColor(status)}>{status}</Tag>
      ),
    },
    {
      title: 'Pass Rate',
      dataIndex: 'passRate',
      key: 'passRate',
      render: (rate) => (
        <Progress
          percent={rate}
          size="small"
          status={rate >= 90 ? 'success' : rate >= 70 ? 'normal' : 'exception'}
        />
      ),
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record) => (
        <Space>
          <Tooltip title="Edit">
            <Button 
              type="text" 
              icon={<EditOutlined />} 
              onClick={() => handleEdit(record)}
            />
          </Tooltip>
          <Popconfirm
            title="Are you sure to delete this test?"
            onConfirm={() => handleDelete(record.id)}
            okText="Yes"
            cancelText="No"
          >
            <Tooltip title="Delete">
              <Button 
                type="text" 
                danger 
                icon={<DeleteOutlined />}
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div className="p-6">
      <div className="mb-6">
      </div>

      {/* Statistics Cards */}
      {stats && (
        <Row gutter={16} className="mb-6">
          <Col span={6}>
            <Card>
              <Statistic
                title="Total Tests"
                value={stats.totalTests}
                prefix={<CheckCircleOutlined />}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="Pass Rate"
                value={stats.overallPassRate}
                precision={1}
                suffix="%"
                valueStyle={{ color: stats.overallPassRate >= 90 ? '#3f8600' : '#faad14' }}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="Failed Tests"
                value={stats.failedTests}
                prefix={<CloseCircleOutlined />}
                valueStyle={{ color: '#cf1322' }}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="Avg Duration"
                value={stats.averageTestDuration}
                precision={0}
                suffix="min"
                prefix={<ClockCircleOutlined />}
              />
            </Card>
          </Col>
        </Row>
      )}

      {/* Main Content */}
      <Card>
        <div className="mb-4 flex justify-between items-center">
          <Space>
            <Select
              placeholder="Filter by type"
              style={{ width: 150 }}
              allowClear
              onChange={(testType) => setSearchParams({ ...searchParams, testType })}
            >
              <Select.Option value="functional">Functional</Select.Option>
              <Select.Option value="performance">Performance</Select.Option>
              <Select.Option value="ui">UI</Select.Option>
              <Select.Option value="integration">Integration</Select.Option>
              <Select.Option value="security">Security</Select.Option>
            </Select>
            <Select
              placeholder="Filter by platform"
              style={{ width: 150 }}
              allowClear
              onChange={(platform) => setSearchParams({ ...searchParams, platform })}
            >
              <Select.Option value="android">Android</Select.Option>
              <Select.Option value="ios">iOS</Select.Option>
              <Select.Option value="windows">Windows</Select.Option>
            </Select>
          </Space>
          <Button type="primary" icon={<PlusOutlined />} onClick={handleCreate}>
            Create New Test
          </Button>
        </div>

        <Table
          columns={columns}
          dataSource={tests}
          rowKey="id"
          loading={loading}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `Total ${total} tests`,
          }}
        />
      </Card>

      {/* Create/Edit Modal */}
      <Modal
        title={editingTest ? 'Edit Compatibility Test' : 'Create New Compatibility Test'}
        open={modalVisible}
        onOk={handleSubmit}
        onCancel={() => setModalVisible(false)}
        width={800}
        okText={editingTest ? 'Update' : 'Create'}
      >
        <Form form={form} layout="vertical">
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="testName"
                label="Test Name"
                rules={[{ required: true, message: 'Please enter test name!' }]}
              >
                <Input placeholder="e.g., Login Flow Test" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="testType"
                label="Test Type"
                rules={[{ required: true, message: 'Please select test type!' }]}
              >
                <Select placeholder="Select test type">
                  <Select.Option value="functional">Functional</Select.Option>
                  <Select.Option value="performance">Performance</Select.Option>
                  <Select.Option value="ui">UI</Select.Option>
                  <Select.Option value="integration">Integration</Select.Option>
                  <Select.Option value="security">Security</Select.Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name="appVersion"
                label="App Version"
                rules={[{ required: true, message: 'Please enter app version!' }]}
              >
                <Input placeholder="e.g., 1.2.3" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="platform"
                label="Platform"
                rules={[{ required: true, message: 'Please select platform!' }]}
              >
                <Select placeholder="Select platform">
                  <Select.Option value="android">Android</Select.Option>
                  <Select.Option value="ios">iOS</Select.Option>
                  <Select.Option value="windows">Windows</Select.Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="osVersion"
                label="OS Version"
                rules={[{ required: true, message: 'Please enter OS version!' }]}
              >
                <Input placeholder="e.g., Android 12, iOS 15" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="deviceModel"
                label="Device Model"
                rules={[{ required: true, message: 'Please enter device model!' }]}
              >
                <Input placeholder="e.g., Samsung Galaxy S21, iPhone 13" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="testEnvironment"
                label="Test Environment"
                rules={[{ required: true, message: 'Please enter test environment!' }]}
              >
                <Input placeholder="e.g., Staging, Production" />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>
    </div>
  );
} 