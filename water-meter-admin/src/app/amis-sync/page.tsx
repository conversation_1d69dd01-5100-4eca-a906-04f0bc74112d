'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { 
  Table, 
  Button, 
  Input, 
  Select, 
  Modal, 
  Form, 
  message, 
  Space, 
  Card, 
  Row, 
  Col,
  Tag,
  Progress,
  Tooltip,
  DatePicker,
  Alert,
  Descriptions,
  Statistic,
  Badge,
  Tabs,
  List,
  Typography
} from 'antd';
import { 
  PlayCircleOutlined, 
  SearchOutlined, 
  ReloadOutlined, 
  PauseCircleOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  SyncOutlined,
  SettingOutlined,
  BarChartOutlined,
  InfoCircleOutlined,
  CloseCircleOutlined
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import { 
  AmisSyncListDto, 
  AmisSyncSearchDto, 
  ManualSyncRequestDto,
  AmisSyncErrorDto,
  SyncProgressDto 
} from '@/types/amis-sync';
import { amisSyncService } from '@/services/amis-sync.service';
import { format } from 'date-fns';

const { Option } = Select;
const { RangePicker } = DatePicker;
const { TabPane } = Tabs;
const { Text } = Typography;

export default function AmisSyncPage() {
  const [syncs, setSyncs] = useState<AmisSyncListDto[]>([]);
  const [loading, setLoading] = useState(false);
  const [total, setTotal] = useState(0);
  const [searchParams, setSearchParams] = useState<AmisSyncSearchDto>({
    page: 1,
    pageSize: 10,
    sortBy: 'StartTime',
    sortDirection: 'desc'
  });

  // Modal states
  const [isManualSyncModalVisible, setIsManualSyncModalVisible] = useState(false);
  const [isSyncDetailModalVisible, setIsSyncDetailModalVisible] = useState(false);
  const [isConfigModalVisible, setIsConfigModalVisible] = useState(false);
  const [selectedSync, setSelectedSync] = useState<AmisSyncListDto | null>(null);
  const [syncErrors, setSyncErrors] = useState<AmisSyncErrorDto[]>([]);
  const [syncProgress, setSyncProgress] = useState<SyncProgressDto | null>(null);
  const [statistics, setStatistics] = useState<any>({});
  const [form] = Form.useForm();

  // Auto-refresh for active syncs
  const [refreshInterval, setRefreshInterval] = useState<NodeJS.Timeout | null>(null);

  const loadSyncs = useCallback(async () => {
    setLoading(true);
    try {
      const response = await amisSyncService.getSyncHistory(searchParams);
      setSyncs(response?.syncs || []);
      setTotal(response?.totalCount || 0);
    } catch (error) {
      message.error('Failed to load sync history');
      console.error('Error loading syncs:', error);
      setSyncs([]); // Reset to empty array on error
      setTotal(0);
    } finally {
      setLoading(false);
    }
  }, [searchParams]);

  const loadStatistics = useCallback(async () => {
    try {
      const stats = await amisSyncService.getSyncStatistics();
      setStatistics(stats);
    } catch (error) {
      console.error('Error loading statistics:', error);
    }
  }, []);

  useEffect(() => {
    loadSyncs();
    loadStatistics();
  }, [loadSyncs, loadStatistics]);

  // Auto-refresh active syncs every 5 seconds
  useEffect(() => {
    const hasActiveSyncs = syncs && syncs.length > 0 && syncs.some(sync => 
      sync.status === 'Running' || sync.status === 'Pending'
    );

    if (hasActiveSyncs) {
      const interval = setInterval(() => {
        loadSyncs();
        loadStatistics();
      }, 5000);
      setRefreshInterval(interval);
    } else if (refreshInterval) {
      clearInterval(refreshInterval);
      setRefreshInterval(null);
    }

    return () => {
      if (refreshInterval) {
        clearInterval(refreshInterval);
      }
    };
  }, [syncs, loadSyncs, loadStatistics, refreshInterval]);

  const handleSearch = (values: any) => {
    setSearchParams({
      ...searchParams,
      page: 1,
      syncType: values.syncType,
      status: values.status,
      triggerBy: values.triggerBy,
      startTimeFrom: values.dateRange?.[0]?.toDate(),
      startTimeTo: values.dateRange?.[1]?.toDate()
    });
  };

  const handleTableChange = (pagination: any, filters: any, sorter: any) => {
    setSearchParams({
      ...searchParams,
      page: pagination.current,
      pageSize: pagination.pageSize,
      sortBy: sorter.field,
      sortDirection: sorter.order === 'ascend' ? 'asc' : 'desc'
    });
  };

  const handleManualSync = async (values: ManualSyncRequestDto) => {
    try {
      await amisSyncService.startManualSync(values, 'System');
      message.success('Manual sync started successfully');
      setIsManualSyncModalVisible(false);
      form.resetFields();
      loadSyncs();
      loadStatistics();
    } catch (error) {
      message.error('Failed to start manual sync');
      console.error('Error starting sync:', error);
    }
  };

  const handleCancelSync = async (syncId: number) => {
    try {
      await amisSyncService.cancelSync(syncId, 'System');
      message.success('Sync cancelled successfully');
      loadSyncs();
    } catch (error) {
      message.error('Failed to cancel sync');
      console.error('Error cancelling sync:', error);
    }
  };

  const handleRetrySync = async (syncId: number) => {
    try {
      await amisSyncService.retrySync(syncId, 'System');
      message.success('Sync retry initiated');
      loadSyncs();
    } catch (error) {
      message.error('Failed to retry sync');
      console.error('Error retrying sync:', error);
    }
  };

  const handleViewDetails = async (sync: AmisSyncListDto) => {
    try {
      const details = await amisSyncService.getSyncById(sync.id);
      const errors = await amisSyncService.getSyncErrors(sync.id);
      
      setSelectedSync(details);
      setSyncErrors(errors);
      setIsSyncDetailModalVisible(true);

      // Get progress if sync is active
      if (sync.status === 'Running' || sync.status === 'Pending') {
        const progress = await amisSyncService.getSyncProgress(sync.id);
        setSyncProgress(progress);
      }
    } catch (error) {
      message.error('Failed to load sync details');
      console.error('Error loading details:', error);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Completed': return 'green';
      case 'Running': return 'blue';
      case 'Pending': return 'orange';
      case 'Failed': return 'red';
      case 'Cancelled': return 'gray';
      default: return 'default';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'Completed': return <CheckCircleOutlined />;
      case 'Running': return <SyncOutlined spin />;
      case 'Pending': return <ClockCircleOutlined />;
      case 'Failed': return <ExclamationCircleOutlined />;
      case 'Cancelled': return <CloseCircleOutlined />;
      default: return <InfoCircleOutlined />;
    }
  };

  const formatDuration = (durationMs?: number) => {
    if (!durationMs) return 'N/A';
    const seconds = Math.floor(durationMs / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    
    if (hours > 0) {
      return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${seconds % 60}s`;
    } else {
      return `${seconds}s`;
    }
  };

  const columns: ColumnsType<AmisSyncListDto> = [
    {
      title: 'Sync Type',
      dataIndex: 'syncType',
      key: 'syncType',
      render: (type: string) => <Tag color="blue">{type}</Tag>
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Space>
          {getStatusIcon(status)}
          <Tag color={getStatusColor(status)}>{status}</Tag>
        </Space>
      )
    },
    {
      title: 'Start Time',
      dataIndex: 'startTime',
      key: 'startTime',
      sorter: true,
      render: (date: string) => format(new Date(date), 'dd/MM/yyyy HH:mm:ss')
    },
    {
      title: 'Duration',
      dataIndex: 'duration',
      key: 'duration',
      render: (duration: number) => formatDuration(duration)
    },
    {
      title: 'Progress',
      dataIndex: 'progressPercentage',
      key: 'progressPercentage',
      render: (progress: number, record: AmisSyncListDto) => (
        <div style={{ minWidth: 120 }}>
          <Progress 
            percent={Math.round(progress || 0)} 
            size="small"
            status={record.status === 'Failed' ? 'exception' : 'active'}
          />
          <Text type="secondary" style={{ fontSize: '12px' }}>
            {record.successfulRecords}/{record.totalRecords} records
          </Text>
        </div>
      )
    },
    {
      title: 'Records',
      key: 'records',
      render: (_, record: AmisSyncListDto) => (
        <Space direction="vertical" size="small">
          <Space>
            <Badge color="green" />
            <Text>Success: {record.successfulRecords || 0}</Text>
          </Space>
          {record.failedRecords ? (
            <Space>
              <Badge color="red" />
              <Text>Failed: {record.failedRecords}</Text>
            </Space>
          ) : null}
        </Space>
      )
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record) => (
        <Space>
          <Tooltip title="View Details">
            <Button 
              type="text" 
              icon={<InfoCircleOutlined />} 
              onClick={() => handleViewDetails(record)}
            />
          </Tooltip>
          {(record.status === 'Running' || record.status === 'Pending') && (
            <Tooltip title="Cancel">
              <Button 
                type="text" 
                danger
                icon={<PauseCircleOutlined />} 
                onClick={() => handleCancelSync(record.id)}
              />
            </Tooltip>
          )}
          {(record.status === 'Failed' || record.status === 'Cancelled') && (
            <Tooltip title="Retry">
              <Button 
                type="text" 
                icon={<ReloadOutlined />} 
                onClick={() => handleRetrySync(record.id)}
              />
            </Tooltip>
          )}
        </Space>
      )
    }
  ];

  return (
    <div className="p-6">
      {/* Statistics Cards */}
      <Row gutter={16} className="mb-6">
        <Col span={6}>
          <Card>
            <Statistic
              title="Total Syncs"
              value={statistics.TotalSyncs || 0}
              prefix={<BarChartOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Active Syncs"
              value={statistics.ActiveSyncs || 0}
              prefix={<SyncOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Successful Today"
              value={statistics.TodaySyncs || 0}
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Unresolved Errors"
              value={statistics.UnresolvedErrors || 0}
              prefix={<ExclamationCircleOutlined />}
              valueStyle={{ color: '#ff4d4f' }}
            />
          </Card>
        </Col>
      </Row>

      <Card>
        <div className="mb-6">
          {/* Search Form */}
          <Form
            layout="inline"
            onFinish={handleSearch}
            className="mb-4"
          >
            <Form.Item name="syncType">
              <Select placeholder="Sync Type" allowClear style={{ width: 150 }}>
                <Option value="FullSync">Full Sync</Option>
                <Option value="IncrementalSync">Incremental</Option>
                <Option value="MeterData">Meter Data</Option>
                <Option value="BaselineData">Baseline Data</Option>
              </Select>
            </Form.Item>
            <Form.Item name="status">
              <Select placeholder="Status" allowClear style={{ width: 120 }}>
                <Option value="Completed">Completed</Option>
                <Option value="Running">Running</Option>
                <Option value="Pending">Pending</Option>
                <Option value="Failed">Failed</Option>
                <Option value="Cancelled">Cancelled</Option>
              </Select>
            </Form.Item>
            <Form.Item name="triggerBy">
              <Input placeholder="Triggered By" allowClear />
            </Form.Item>
            <Form.Item name="dateRange">
              <RangePicker placeholder={['Start Date From', 'Start Date To']} />
            </Form.Item>
            <Form.Item>
              <Button type="primary" htmlType="submit" icon={<SearchOutlined />}>
                Search
              </Button>
            </Form.Item>
          </Form>

          {/* Action Buttons */}
          <Space className="mb-4">
            <Button 
              type="primary" 
              icon={<PlayCircleOutlined />}
              onClick={() => {
                form.resetFields();
                setIsManualSyncModalVisible(true);
              }}
            >
              Start Manual Sync
            </Button>
            <Button icon={<SettingOutlined />} onClick={() => setIsConfigModalVisible(true)}>
              Configuration
            </Button>
            <Button icon={<ReloadOutlined />} onClick={loadSyncs}>
              Refresh
            </Button>
          </Space>

          {/* Active Syncs Alert */}
          {syncs && syncs.length > 0 && syncs.some(sync => sync.status === 'Running' || sync.status === 'Pending') && (
            <Alert
              message="Active syncs detected"
              description="The page will auto-refresh every 5 seconds to show progress updates."
              type="info"
              showIcon
              className="mb-4"
            />
          )}
        </div>

        {/* Table */}
        <Table
          columns={columns}
          dataSource={syncs}
          rowKey="id"
          loading={loading}
          pagination={{
            current: searchParams.page,
            pageSize: searchParams.pageSize,
            total: total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`
          }}
          onChange={handleTableChange}
          scroll={{ x: 1000 }}
        />
      </Card>

      {/* Manual Sync Modal */}
      <Modal
        title="Start Manual Sync"
        open={isManualSyncModalVisible}
        onCancel={() => {
          setIsManualSyncModalVisible(false);
          form.resetFields();
        }}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleManualSync}
        >
          <Form.Item
            name="syncType"
            label="Sync Type"
            rules={[{ required: true, message: 'Please select sync type' }]}
          >
            <Select placeholder="Select sync type">
              <Option value="FullSync">Full Sync</Option>
              <Option value="IncrementalSync">Incremental Sync</Option>
              <Option value="MeterData">Meter Data Only</Option>
              <Option value="BaselineData">Baseline Data Only</Option>
            </Select>
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="fromDate"
                label="From Date"
              >
                <DatePicker style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="toDate"
                label="To Date"
              >
                <DatePicker style={{ width: '100%' }} />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="forceFullSync"
            label="Force Full Sync"
          >
            <Select defaultValue={false}>
              <Option value={false}>No</Option>
              <Option value={true}>Yes</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="notes"
            label="Notes"
          >
            <Input.TextArea rows={3} placeholder="Enter sync notes or reason" />
          </Form.Item>

          <Form.Item className="text-right">
            <Space>
              <Button onClick={() => {
                setIsManualSyncModalVisible(false);
                form.resetFields();
              }}>
                Cancel
              </Button>
              <Button type="primary" htmlType="submit">
                Start Sync
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* Sync Detail Modal */}
      <Modal
        title="Sync Details"
        open={isSyncDetailModalVisible}
        onCancel={() => setIsSyncDetailModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setIsSyncDetailModalVisible(false)}>
            Close
          </Button>
        ]}
        width={800}
      >
        {selectedSync && (
          <Tabs defaultActiveKey="details">
            <TabPane tab="Details" key="details">
              <Descriptions bordered column={2}>
                <Descriptions.Item label="Sync Type" span={1}>
                  <Tag color="blue">{selectedSync.syncType}</Tag>
                </Descriptions.Item>
                <Descriptions.Item label="Status" span={1}>
                  <Space>
                    {getStatusIcon(selectedSync.status)}
                    <Tag color={getStatusColor(selectedSync.status)}>{selectedSync.status}</Tag>
                  </Space>
                </Descriptions.Item>
                <Descriptions.Item label="Start Time" span={1}>
                  {format(new Date(selectedSync.startTime), 'dd/MM/yyyy HH:mm:ss')}
                </Descriptions.Item>
                <Descriptions.Item label="End Time" span={1}>
                  {selectedSync.endTime ? format(new Date(selectedSync.endTime), 'dd/MM/yyyy HH:mm:ss') : 'N/A'}
                </Descriptions.Item>
                <Descriptions.Item label="Duration" span={1}>
                  {formatDuration(selectedSync.duration)}
                </Descriptions.Item>
                <Descriptions.Item label="Progress" span={1}>
                  <Progress percent={Math.round(selectedSync.progressPercentage || 0)} />
                </Descriptions.Item>
                <Descriptions.Item label="Total Records" span={1}>
                  {selectedSync.totalRecords || 0}
                </Descriptions.Item>
                <Descriptions.Item label="Processed Records" span={1}>
                  {selectedSync.processedRecords || 0}
                </Descriptions.Item>
                <Descriptions.Item label="Successful Records" span={1}>
                  <Badge color="green" text={`${selectedSync.successfulRecords || 0}`} />
                </Descriptions.Item>
                <Descriptions.Item label="Failed Records" span={1}>
                  <Badge color="red" text={`${selectedSync.failedRecords || 0}`} />
                </Descriptions.Item>
                {selectedSync.errorMessage && (
                  <Descriptions.Item label="Error Message" span={2}>
                    <Text type="danger">{selectedSync.errorMessage}</Text>
                  </Descriptions.Item>
                )}
                {selectedSync.resultSummary && (
                  <Descriptions.Item label="Result Summary" span={2}>
                    {selectedSync.resultSummary}
                  </Descriptions.Item>
                )}
              </Descriptions>
            </TabPane>
            
            <TabPane tab={`Errors (${syncErrors.length})`} key="errors">
              <List
                dataSource={syncErrors}
                renderItem={error => (
                  <List.Item>
                    <List.Item.Meta
                      avatar={<ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />}
                      title={<Space>
                        <Tag color="red">{error.errorType}</Tag>
                        <Tag color="orange">{error.severity}</Tag>
                      </Space>}
                      description={
                        <div>
                          <Text>{error.errorMessage}</Text>
                          {error.recordIdentifier && (
                            <div><Text type="secondary">Record: {error.recordIdentifier}</Text></div>
                          )}
                          <div><Text type="secondary">
                            {format(new Date(error.createdAt), 'dd/MM/yyyy HH:mm:ss')}
                          </Text></div>
                        </div>
                      }
                    />
                  </List.Item>
                )}
              />
            </TabPane>
          </Tabs>
        )}
      </Modal>

      {/* Configuration Modal */}
      <Modal
        title="AMIS Sync Configuration"
        open={isConfigModalVisible}
        onCancel={() => setIsConfigModalVisible(false)}
        footer={null}
        width={600}
      >
        <div className="text-center py-8">
          <SettingOutlined style={{ fontSize: 48, color: '#1890ff' }} />
          <h3>Configuration Panel</h3>
          <p>AMIS sync configuration settings will be available here.</p>
        </div>
      </Modal>
    </div>
  );
} 