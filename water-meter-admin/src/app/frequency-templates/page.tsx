'use client';

import React, { useState, useEffect, useCallback } from 'react';
import {
  Table,
  Button,
  Input,
  Select,
  Modal,
  Form,
  Space,
  Card,
  Row,
  Col,
  Tag,
  Popconfirm,
  Tooltip,
  InputNumber,
  Descriptions,
  Typography,
  Switch,
  DatePicker
} from 'antd';
import { 
  PlusOutlined, 
  SearchOutlined, 
  EditOutlined, 
  DeleteOutlined, 
  EyeOutlined,
  CopyOutlined,
  CalculatorOutlined,
  ExportOutlined
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import {
  frequencyTemplateService,
  FrequencyTemplateListDto,
  CreateFrequencyTemplateDto,
  FrequencyTemplateSearchDto
} from '@/services/frequency-template.service';
import { useNotificationManager } from '@/components/common/NotificationManager';
import dayjs from 'dayjs';

const { Option } = Select;
const { TextArea } = Input;
const { RangePicker } = DatePicker;

export default function FrequencyTemplatesPage() {
  const [templates, setTemplates] = useState<FrequencyTemplateListDto[]>([]);
  const [loading, setLoading] = useState(false);
  const [total, setTotal] = useState(0);
  const [searchParams, setSearchParams] = useState<FrequencyTemplateSearchDto>({
    page: 1,
    pageSize: 10,
    sortBy: 'CreatedAt',
    sortDirection: 'desc'
  });

  // Modal states
  const [isCreateModalVisible, setIsCreateModalVisible] = useState(false);
  const [isDetailModalVisible, setIsDetailModalVisible] = useState(false);
  const [isCalculatorModalVisible, setIsCalculatorModalVisible] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<FrequencyTemplateListDto | null>(null);
  const [calculationResult, setCalculationResult] = useState<any>(null);
  const [form] = Form.useForm();
  const [searchForm] = Form.useForm();
  const [calculatorForm] = Form.useForm();
  const notification = useNotificationManager();

  const loadTemplates = useCallback(async () => {
    setLoading(true);
    try {
      const response = await frequencyTemplateService.getTemplates(searchParams);
      setTemplates(response.templates);
      setTotal(response.totalCount);
    } catch (error: any) {
      notification.handleError(error, 'Load templates');
    } finally {
      setLoading(false);
    }
  }, [searchParams]);

  useEffect(() => {
    loadTemplates();
  }, [loadTemplates]);

  const handleSearch = (values: any) => {
    setSearchParams({
      ...searchParams,
      page: 1,
      name: values.name,
      frequencyType: values.frequencyType,
      category: values.category,
      isActive: values.isActive,
      intervalUnit: values.intervalUnit,
    });
  };

  const handleTableChange = (pagination: any, filters: any, sorter: any) => {
    setSearchParams({
      ...searchParams,
      page: pagination.current,
      pageSize: pagination.pageSize,
      sortBy: sorter.field,
      sortDirection: sorter.order === 'ascend' ? 'asc' : 'desc'
    });
  };

  const handleCreate = async (values: CreateFrequencyTemplateDto) => {
    try {
      if (selectedTemplate) {
        await frequencyTemplateService.updateTemplate(selectedTemplate.id, { ...values, id: selectedTemplate.id });
        notification.showUpdateSuccess('Template');
      } else {
        await frequencyTemplateService.createTemplate(values);
        notification.showCreateSuccess('Template');
      }

      setIsCreateModalVisible(false);
      setSelectedTemplate(null);
      form.resetFields();
      loadTemplates();
    } catch (error: any) {
      const errorMessage = notification.extractErrorMessage(error);

      if (errorMessage.includes('already exists') || errorMessage.includes('duplicate')) {
        notification.showDuplicateWarning('template', values.name);
      } else {
        const context = selectedTemplate ? 'Update template' : 'Create template';
        notification.handleError(error, context);
      }
    }
  };

  const handleEdit = (template: FrequencyTemplateListDto) => {
    setSelectedTemplate(template);
    form.setFieldsValue(template);
    setIsCreateModalVisible(true);
  };

  const handleDelete = async (id: number) => {
    try {
      await frequencyTemplateService.deleteTemplate(id);
      notification.showDeleteSuccess('Template');
      loadTemplates();
    } catch (error: any) {
      notification.handleError(error, 'Delete template');
    }
  };

  const handleViewDetails = (template: FrequencyTemplateListDto) => {
    setSelectedTemplate(template);
    setIsDetailModalVisible(true);
  };

  const handleDuplicate = async (templateId: number, templateName: string) => {
    try {
      const newName = `${templateName} - Copy`;
      await frequencyTemplateService.duplicateTemplate(templateId, newName);
      notification.showMessage('success', 'Template duplicated successfully');
      loadTemplates();
    } catch (error: any) {
      notification.handleError(error, 'Duplicate template');
    }
  };

  const handleCalculateFrequency = async (values: any) => {
    if (!selectedTemplate) return;
    
    try {
      const startDate = dayjs(values.dateRange[0]).format('YYYY-MM-DD');
      const endDate = dayjs(values.dateRange[1]).format('YYYY-MM-DD');
      
      const result = await frequencyTemplateService.calculateFrequency(
        selectedTemplate.id,
        startDate,
        endDate
      );
      
      setCalculationResult(result);
      notification.showMessage('success', 'Frequency calculation completed');
    } catch (error: any) {
      notification.handleError(error, 'Calculate frequency');
    }
  };

  const handleExport = async () => {
    try {
      const blob = await frequencyTemplateService.exportTemplates(searchParams);
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `frequency_templates_${new Date().toISOString().split('T')[0]}.xlsx`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      message.success('Export completed successfully');
    } catch (error) {
      message.error('Failed to export templates');
      console.error('Error exporting templates:', error);
    }
  };

  const getFrequencyColor = (frequencyType: string) => {
    switch (frequencyType) {
      case 'Regular':
        return 'green';
      case 'Scheduled':
        return 'blue';
      case 'Audit':
        return 'orange';
      case 'Emergency':
        return 'red';
      default:
        return 'default';
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'Residential':
        return 'cyan';
      case 'Commercial':
        return 'purple';
      case 'Industrial':
        return 'orange';
      default:
        return 'default';
    }
  };

  const formatFrequency = (intervalValue: number, intervalUnit: string) => {
    return `Every ${intervalValue} ${intervalUnit}${intervalValue > 1 ? 's' : ''}`;
  };

  const columns: ColumnsType<FrequencyTemplateListDto> = [
    {
      title: 'ID',
      dataIndex: 'id',
      width: 80,
      sorter: true,
    },
    {
      title: 'Template Name',
      dataIndex: 'name',
      sorter: true,
      render: (text, record) => (
        <Button type="link" onClick={() => handleViewDetails(record)}>
          {text}
        </Button>
      ),
    },
    {
      title: 'Frequency Type',
      dataIndex: 'frequencyType',
      width: 130,
      render: (type) => (
        <Tag color={getFrequencyColor(type)}>{type}</Tag>
      ),
    },
    {
      title: 'Frequency',
      width: 150,
      render: (_, record) => formatFrequency(record.intervalValue, record.intervalUnit),
    },
    {
      title: 'Category',
      dataIndex: 'category',
      width: 120,
      render: (category) => (
        <Tag color={getCategoryColor(category)}>{category}</Tag>
      ),
    },
    {
      title: 'Est. Duration',
      dataIndex: 'estimatedDuration',
      width: 120,
      sorter: true,
      render: (duration) => duration ? `${(duration / 60).toFixed(1)} hrs` : '-',
    },
    {
      title: 'Usage Count',
      dataIndex: 'usageCount',
      width: 100,
      sorter: true,
      render: (count) => (
        <Tag color={count > 10 ? 'green' : count > 5 ? 'orange' : 'default'}>
          {count}
        </Tag>
      ),
    },
    {
      title: 'Status',
      dataIndex: 'isActive',
      width: 100,
      render: (isActive) => (
        <Tag color={isActive ? 'green' : 'red'}>
          {isActive ? 'Active' : 'Inactive'}
        </Tag>
      ),
    },
    {
      title: 'Actions',
      width: 250,
      render: (_, record) => (
        <Space>
          <Tooltip title="View Details">
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() => handleViewDetails(record)}
            />
          </Tooltip>
          <Tooltip title="Edit">
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => handleEdit(record)}
            />
          </Tooltip>
          <Tooltip title="Calculate Frequency">
            <Button
              type="text"
              icon={<CalculatorOutlined />}
              onClick={() => {
                setSelectedTemplate(record);
                setIsCalculatorModalVisible(true);
              }}
            />
          </Tooltip>
          <Tooltip title="Duplicate">
            <Button
              type="text"
              icon={<CopyOutlined />}
              onClick={() => handleDuplicate(record.id, record.name)}
            />
          </Tooltip>
          <Popconfirm
            title="Are you sure you want to delete this template?"
            onConfirm={() => handleDelete(record.id)}
            okText="Yes"
            cancelText="No"
          >
            <Tooltip title="Delete">
              <Button
                type="text"
                danger
                icon={<DeleteOutlined />}
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div className="p-6">
      {/* Search Form */}
      <Card className="mb-4">
        <Form
          form={searchForm}
          layout="inline"
          onFinish={handleSearch}
          className="search-form"
        >
          <Row gutter={16} className="w-full">
            <Col span={6}>
              <Form.Item name="name">
                <Input placeholder="Template Name" />
              </Form.Item>
            </Col>
            <Col span={4}>
              <Form.Item name="frequencyType">
                <Select placeholder="Frequency Type" allowClear>
                  <Option value="Regular">Regular</Option>
                  <Option value="Scheduled">Scheduled</Option>
                  <Option value="Audit">Audit</Option>
                  <Option value="Emergency">Emergency</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={4}>
              <Form.Item name="category">
                <Select placeholder="Category" allowClear>
                  <Option value="Residential">Residential</Option>
                  <Option value="Commercial">Commercial</Option>
                  <Option value="Industrial">Industrial</Option>
                  <Option value="Mixed">Mixed</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={4}>
              <Form.Item name="intervalUnit">
                <Select placeholder="Interval Unit" allowClear>
                  <Option value="Day">Day</Option>
                  <Option value="Week">Week</Option>
                  <Option value="Month">Month</Option>
                  <Option value="Quarter">Quarter</Option>
                  <Option value="Year">Year</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={4}>
              <Form.Item name="isActive">
                <Select placeholder="Status" allowClear>
                  <Option value={true}>Active</Option>
                  <Option value={false}>Inactive</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>
          <Row className="mt-4">
            <Col>
              <Space>
                <Button type="primary" htmlType="submit" icon={<SearchOutlined />}>
                  Search
                </Button>
                <Button
                  onClick={() => {
                    searchForm.resetFields();
                    setSearchParams({
                      page: 1,
                      pageSize: 10,
                      sortBy: 'CreatedAt',
                      sortDirection: 'desc'
                    });
                  }}
                >
                  Reset
                </Button>
              </Space>
            </Col>
          </Row>
        </Form>
      </Card>

      {/* Action Buttons */}
      <Card className="mb-4">
        <Space>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => setIsCreateModalVisible(true)}
          >
            Create Template
          </Button>
          <Button
            icon={<CalculatorOutlined />}
            onClick={() => message.info('Select a template to calculate frequency')}
          >
            Batch Calculator
          </Button>
          <Button
            icon={<ExportOutlined />}
            onClick={handleExport}
          >
            Export Excel
          </Button>
        </Space>
      </Card>

      {/* Templates Table */}
      <Card>
        <Table
          columns={columns}
          dataSource={templates}
          rowKey="id"
          loading={loading}
          pagination={{
            current: searchParams.page,
            pageSize: searchParams.pageSize,
            total: total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `Total ${total} items`,
          }}
          onChange={handleTableChange}
          scroll={{ x: 1400 }}
        />
      </Card>

      {/* Create/Edit Template Modal */}
      <Modal
        title={selectedTemplate ? 'Edit Template' : 'Create New Template'}
        open={isCreateModalVisible}
        onCancel={() => {
          setIsCreateModalVisible(false);
          setSelectedTemplate(null);
          form.resetFields();
        }}
        footer={null}
        width={800}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleCreate}
          initialValues={{
            frequencyType: 'Regular',
            intervalValue: 1,
            intervalUnit: 'Week',
            isActive: true,
            category: 'Residential'
          }}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="name"
                label="Template Name"
                rules={[{ required: true, message: 'Please enter template name' }]}
              >
                <Input placeholder="Enter template name" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="frequencyType" label="Frequency Type">
                <Select>
                  <Option value="Regular">Regular</Option>
                  <Option value="Scheduled">Scheduled</Option>
                  <Option value="Audit">Audit</Option>
                  <Option value="Emergency">Emergency</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item name="description" label="Description">
            <TextArea rows={3} placeholder="Enter template description" />
          </Form.Item>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name="intervalValue"
                label="Interval Value"
                rules={[{ required: true, message: 'Please enter interval value' }]}
              >
                <InputNumber min={1} className="w-full" placeholder="Enter value" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="intervalUnit" label="Interval Unit">
                <Select>
                  <Option value="Day">Day</Option>
                  <Option value="Week">Week</Option>
                  <Option value="Month">Month</Option>
                  <Option value="Quarter">Quarter</Option>
                  <Option value="Year">Year</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="estimatedDuration" label="Est. Duration (min)">
                <InputNumber
                  min={0}
                  step={15}
                  className="w-full"
                  placeholder="Enter duration"
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item name="category" label="Category">
                <Select>
                  <Option value="Residential">Residential</Option>
                  <Option value="Commercial">Commercial</Option>
                  <Option value="Industrial">Industrial</Option>
                  <Option value="Mixed">Mixed</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="isActive" label="Status" valuePropName="checked">
                <Switch checkedChildren="Active" unCheckedChildren="Inactive" />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item name="applicableAreas" label="Applicable Areas">
            <TextArea rows={2} placeholder="Enter applicable areas" />
          </Form.Item>

          <Form.Item name="notes" label="Notes">
            <TextArea rows={3} placeholder="Enter additional notes" />
          </Form.Item>

          <Form.Item className="text-right">
            <Space>
              <Button onClick={() => {
                setIsCreateModalVisible(false);
                setSelectedTemplate(null);
                form.resetFields();
              }}>
                Cancel
              </Button>
              <Button type="primary" htmlType="submit">
                {selectedTemplate ? 'Update' : 'Create'}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* Template Details Modal */}
      <Modal
        title="Template Details"
        open={isDetailModalVisible}
        onCancel={() => setIsDetailModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setIsDetailModalVisible(false)}>
            Close
          </Button>
        ]}
        width={900}
      >
        {selectedTemplate && (
          <div>
            <Descriptions bordered column={2}>
              <Descriptions.Item label="Template Name">{selectedTemplate.name}</Descriptions.Item>
              <Descriptions.Item label="Frequency Type">
                <Tag color={getFrequencyColor(selectedTemplate.frequencyType)}>{selectedTemplate.frequencyType}</Tag>
              </Descriptions.Item>
              <Descriptions.Item label="Frequency">
                {formatFrequency(selectedTemplate.intervalValue, selectedTemplate.intervalUnit)}
              </Descriptions.Item>
              <Descriptions.Item label="Category">
                <Tag color={getCategoryColor(selectedTemplate.category)}>{selectedTemplate.category}</Tag>
              </Descriptions.Item>
              <Descriptions.Item label="Estimated Duration">
                {selectedTemplate.estimatedDuration ? `${(selectedTemplate.estimatedDuration / 60).toFixed(1)} hrs` : '-'}
              </Descriptions.Item>
              <Descriptions.Item label="Usage Count">
                <Tag color={selectedTemplate.usageCount > 10 ? 'green' : selectedTemplate.usageCount > 5 ? 'orange' : 'default'}>
                  {selectedTemplate.usageCount}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="Status">
                <Tag color={selectedTemplate.isActive ? 'green' : 'red'}>
                  {selectedTemplate.isActive ? 'Active' : 'Inactive'}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="Created At">
                {new Date(selectedTemplate.createdAt).toLocaleString()}
              </Descriptions.Item>
              <Descriptions.Item label="Created By">{selectedTemplate.createdBy}</Descriptions.Item>
            </Descriptions>

            {selectedTemplate.description && (
              <div className="mt-4">
                <Typography.Title level={5}>Description</Typography.Title>
                <Typography.Paragraph>{selectedTemplate.description}</Typography.Paragraph>
              </div>
            )}

            {selectedTemplate.applicableAreas && (
              <div className="mt-4">
                <Typography.Title level={5}>Applicable Areas</Typography.Title>
                <Typography.Paragraph>{selectedTemplate.applicableAreas}</Typography.Paragraph>
              </div>
            )}
          </div>
        )}
      </Modal>

      {/* Frequency Calculator Modal */}
      <Modal
        title="Frequency Calculator"
        open={isCalculatorModalVisible}
        onCancel={() => {
          setIsCalculatorModalVisible(false);
          calculatorForm.resetFields();
        }}
        footer={null}
        width={700}
      >
        {selectedTemplate && (
          <div>
            <div className="mb-4">
              <Typography.Title level={5}>Template: {selectedTemplate.name}</Typography.Title>
              <Typography.Text type="secondary">
                Frequency: {formatFrequency(selectedTemplate.intervalValue, selectedTemplate.intervalUnit)}
              </Typography.Text>
            </div>

            <Form
              form={calculatorForm}
              layout="vertical"
              onFinish={handleCalculateFrequency}
            >
              <Form.Item
                name="dateRange"
                label="Date Range"
                rules={[{ required: true, message: 'Please select date range' }]}
              >
                <RangePicker className="w-full" />
              </Form.Item>

              <Form.Item>
                <Button type="primary" htmlType="submit" icon={<CalculatorOutlined />}>
                  Calculate Frequency
                </Button>
              </Form.Item>
            </Form>

            {calculationResult && (
              <div className="mt-4">
                <Typography.Title level={5}>Calculation Result</Typography.Title>
                <Descriptions bordered column={1}>
                  <Descriptions.Item label="Frequency Description">
                    {calculationResult.frequencyDescription}
                  </Descriptions.Item>
                  <Descriptions.Item label="Total Occurrences">
                    {calculationResult.totalOccurrences}
                  </Descriptions.Item>
                  {calculationResult.firstOccurrence && (
                    <Descriptions.Item label="First Occurrence">
                      {new Date(calculationResult.firstOccurrence).toLocaleDateString()}
                    </Descriptions.Item>
                  )}
                  {calculationResult.lastOccurrence && (
                    <Descriptions.Item label="Last Occurrence">
                      {new Date(calculationResult.lastOccurrence).toLocaleDateString()}
                    </Descriptions.Item>
                  )}
                  <Descriptions.Item label="Scheduled Dates">
                    <div style={{ maxHeight: '200px', overflowY: 'auto' }}>
                      {calculationResult.scheduledDates?.map((date: string, index: number) => (
                        <Tag key={index} className="mb-1 mr-1">
                          {new Date(date).toLocaleDateString()}
                        </Tag>
                      )) || 'No dates calculated'}
                    </div>
                  </Descriptions.Item>
                </Descriptions>
              </div>
            )}
          </div>
        )}
      </Modal>
    </div>
  );
} 