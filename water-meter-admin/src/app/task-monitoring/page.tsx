'use client';

import React, { useState, useEffect, useCallback } from 'react';
import {
  Card, Row, Col, Statistic, Table, Progress, Tag, Button, Modal, 
  Timeline, Space, Select, DatePicker, Radio, Tabs, List, Avatar,
  Badge, Tooltip, message, Input, Form, Descriptions, Collapse
} from 'antd';
import {
  DashboardOutlined, EyeOutlined, ClockCircleOutlined, UserOutlined,
  CheckCircleOutlined, ExclamationCircleOutlined, SyncOutlined,
  FilterOutlined, ReloadOutlined, HistoryOutlined, BellOutlined,
  InfoCircleOutlined, EnvironmentOutlined, TeamOutlined
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import { Line, Column, Pie } from '@ant-design/plots';
import dayjs from 'dayjs';
import { 
  taskService, TaskListDto, TaskStatisticsDto, TaskPerformanceMetricsDto,
  TaskHistory, TaskSearchDto, TaskWorkPackageSearchDto, WorkPackageGroupedTasksDto,
  TaskAssignmentDto
} from '@/services/task.service';

const { Option } = Select;
const { RangePicker } = DatePicker;
const { TabPane } = Tabs;
const { Panel } = Collapse;

interface RealTimeUpdate {
  id: string;
  taskId: number;
  taskName: string;
  action: string;
  user: string;
  timestamp: string;
  type: 'info' | 'success' | 'warning' | 'error';
}

// Extended task data for expandable table
interface ExpandableTaskData extends TaskListDto {
  isGroupHeader?: boolean;
  children?: ExpandableTaskData[];
}

const TaskMonitoring: React.FC = () => {
  // Suppress TabPane deprecation warning for this page only
  useEffect(() => {
    const originalWarn = console.warn;
    console.warn = (...args: any[]) => {
      const message = args.join(' ');
      if (message.includes('[antd: Tabs] `Tabs.TabPane` is deprecated')) {
        return; // Suppress this specific warning
      }
      originalWarn(...args);
    };
    
    return () => {
      console.warn = originalWarn; // Restore original console.warn when component unmounts
    };
  }, []);

  const [loading, setLoading] = useState(false);
  const [tasks, setTasks] = useState<TaskListDto[]>([]);
  const [workPackageGroupedTasks, setWorkPackageGroupedTasks] = useState<WorkPackageGroupedTasksDto | null>(null);
  const [statistics, setStatistics] = useState<TaskStatisticsDto | null>(null);
  const [performanceMetrics, setPerformanceMetrics] = useState<TaskPerformanceMetricsDto[]>([]);
  const [taskHistory, setTaskHistory] = useState<TaskHistory[]>([]);
  const [selectedTask, setSelectedTask] = useState<TaskListDto | null>(null);
  const [historyModalVisible, setHistoryModalVisible] = useState(false);
  const [taskDetailModalVisible, setTaskDetailModalVisible] = useState(false);
  const [activeTab, setActiveTab] = useState('overview');

  // Real-time updates simulation
  const [realTimeUpdates, setRealTimeUpdates] = useState<RealTimeUpdate[]>([]);

  // Filters
  const [dateRange, setDateRange] = useState<[dayjs.Dayjs, dayjs.Dayjs] | null>(null);
  const [statusFilter, setStatusFilter] = useState<string>('');
  const [priorityFilter, setPriorityFilter] = useState<string>('');
  const [workPackageFilter, setWorkPackageFilter] = useState<string>('');
  const [availableWorkPackages, setAvailableWorkPackages] = useState<{ value: string; label: string }[]>([]);

  // Load data
  const loadData = useCallback(async () => {
    setLoading(true);
    try {
      // Load work package grouped tasks for monitoring
      const workPackageSearchDto: TaskWorkPackageSearchDto = {
        status: statusFilter || undefined,
        priority: priorityFilter || undefined,
        assignedTo: workPackageFilter || undefined, // Use assignedTo instead of workPackageName
      };
      
      const workPackageResult = await taskService.getTasksGroupedByWorkPackage(workPackageSearchDto);
      setWorkPackageGroupedTasks(workPackageResult);

      // Extract all tasks from work packages for statistics
      const allTasks = workPackageResult.workPackages.flatMap(wp => wp.tasks);
      setTasks(allTasks);

      // Extract available work packages for filter
      const workPackages = workPackageResult.workPackages.map(wp => ({
        value: wp.workPackageName,
        label: `${wp.workPackageName} (${wp.tasks.length} tasks)`
      }));
      setAvailableWorkPackages(workPackages);

      // Load statistics
      const stats = await taskService.getTaskStatistics();
      setStatistics(stats);

      // Load performance metrics
      const metrics = await taskService.getTaskPerformanceMetrics();
      setPerformanceMetrics(metrics);

    } catch (error) {
      message.error('Failed to load monitoring data');
      console.error('Load data error:', error);
    } finally {
      setLoading(false);
    }
  }, [dateRange, statusFilter, priorityFilter, workPackageFilter]);

  useEffect(() => {
    loadData();
  }, [loadData]);

  // Simulate real-time updates
  useEffect(() => {
    const interval = setInterval(() => {
      if (tasks.length > 0) {
        const randomTask = tasks[Math.floor(Math.random() * tasks.length)];
        const actions = ['Updated', 'Assigned', 'Completed', 'Started'];
        const users = ['John Smith', 'Jane Doe', 'Mike Johnson', 'Sarah Wilson'];
        const types: ('info' | 'success' | 'warning' | 'error')[] = ['info', 'success', 'warning'];
        
        const update: RealTimeUpdate = {
          id: Date.now().toString(),
          taskId: randomTask.id,
          taskName: randomTask.name,
          action: actions[Math.floor(Math.random() * actions.length)],
          user: users[Math.floor(Math.random() * users.length)],
          timestamp: dayjs().format('HH:mm:ss'),
          type: types[Math.floor(Math.random() * types.length)]
        };

        setRealTimeUpdates(prev => [update, ...prev.slice(0, 9)]);
      }
    }, 10000); // Update every 10 seconds

    return () => clearInterval(interval);
  }, [tasks]);

  // Load task history
  const loadTaskHistory = async (taskId: number) => {
    try {
      const history = await taskService.getTaskHistory(taskId);
      setTaskHistory(history);
    } catch (error) {
      message.error('Failed to load task history');
      console.error('Load history error:', error);
    }
  };

  // Show task history
  const showTaskHistory = async (task: TaskListDto) => {
    setSelectedTask(task);
    await loadTaskHistory(task.id);
    setHistoryModalVisible(true);
  };

  // Show task details
  const showTaskDetails = (task: TaskListDto) => {
    setSelectedTask(task);
    setTaskDetailModalVisible(true);
  };

  // Get expandable table data for work package grouping
  const getExpandableTableData = (): ExpandableTaskData[] => {
    if (!workPackageGroupedTasks) return [];

    return workPackageGroupedTasks.workPackages.map(workPackage => ({
      id: 0, // Group header doesn't need real ID
      name: workPackage.workPackageName,
      description: `${workPackage.tasks.length} tasks`,
      status: 'Group',
      priority: 'Medium',
      type: 'WorkPackage',
      assignedTo: '',
      createdBy: '', // Add required field
      progressPercentage: Math.round(
        (workPackage.tasks.filter(t => t.status === 'Completed').length / workPackage.tasks.length) * 100
      ),
      dueDate: workPackage.tasks[0]?.dueDate || '',
      createdAt: '',
      updatedAt: '',
      isGroupHeader: true,
      children: workPackage.tasks.map(task => ({
        ...task,
        isGroupHeader: false
      }))
    }));
  };

  // Get status color
  const getStatusColor = (status: string) => {
    const colors = {
      'Pending': 'orange',
      'InProgress': 'blue',
      'Completed': 'green',
      'Cancelled': 'red',
      'Group': 'purple'
    };
    return colors[status as keyof typeof colors] || 'default';
  };

  // Get priority color
  const getPriorityColor = (priority: string) => {
    const colors = {
      'Low': 'gray',
      'Medium': 'blue',
      'High': 'orange',
      'Critical': 'red'
    };
    return colors[priority as keyof typeof colors] || 'default';
  };

  // Check if task is overdue
  const isOverdue = (task: TaskListDto) => {
    return task.dueDate && dayjs(task.dueDate).isBefore(dayjs()) && task.status !== 'Completed';
  };

  // Task monitoring table columns with work package grouping
  const monitoringColumns: ColumnsType<ExpandableTaskData> = [
    {
      title: 'Task / Work Package',
      key: 'task',
      render: (_, record) => {
        if (record.isGroupHeader) {
          return (
            <div className="flex items-center">
              <TeamOutlined className="mr-2 text-purple-500" />
              <div>
                <div className="font-bold text-purple-700">{record.name}</div>
                <div className="text-gray-500 text-sm">{record.description}</div>
              </div>
            </div>
          );
        } else {
          return (
            <div>
              <div className="font-medium">{record.name}</div>
              <div className="text-gray-500 text-sm">{record.type}</div>
              {isOverdue(record) && <Badge status="error" text="Overdue" />}
            </div>
          );
        }
      },
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status: string, record) => {
        if (record.isGroupHeader) {
          const completedTasks = record.children?.filter(t => t.status === 'Completed').length || 0;
          const totalTasks = record.children?.length || 0;
          return <Tag color="purple">{completedTasks}/{totalTasks} Completed</Tag>;
        }
        return <Tag color={getStatusColor(status)}>{status}</Tag>;
      },
    },
    {
      title: 'Priority',
      dataIndex: 'priority',
      key: 'priority',
      render: (priority: string, record) => {
        if (record.isGroupHeader) {
          const highPriorityCount = record.children?.filter(t => t.priority === 'High' || t.priority === 'Critical').length || 0;
          if (highPriorityCount > 0) {
            return <Tag color="red">{highPriorityCount} High Priority</Tag>;
          }
          return <Tag color="default">Normal</Tag>;
        }
        return <Tag color={getPriorityColor(priority)}>{priority}</Tag>;
      },
    },
    {
      title: 'Assigned To',
      dataIndex: 'assignedUserId',
      key: 'assignedUserId',
      render: (assignedUserId: number, record) => {
        if (record.isGroupHeader) {
          const assignedUsers = [...new Set(record.children?.map(t => t.assignedUserId).filter(Boolean))];
          return (
            <div className="flex items-center">
              <UserOutlined className="mr-1" />
              <span>{assignedUsers.length} Users</span>
            </div>
          );
        }
        return (
          <div className="flex items-center">
            <Avatar size="small" icon={<UserOutlined />} className="mr-2" />
            {assignedUserId ? `User ${assignedUserId}` : 'Unassigned'}
          </div>
        );
      },
    },
    {
      title: 'Progress',
      dataIndex: 'progressPercentage',
      key: 'progressPercentage',
      render: (progress: number, record) => (
        <div style={{ width: 100 }}>
          <Progress 
            percent={progress} 
            size="small" 
            status={progress === 100 ? 'success' : 'active'}
          />
        </div>
      ),
    },
    {
      title: 'Due Date',
      dataIndex: 'dueDate',
      key: 'dueDate',
      render: (date: string, record: ExpandableTaskData) => {
        if (record.isGroupHeader) {
          const overdueTasks = record.children?.filter(t => isOverdue(t)).length || 0;
          if (overdueTasks > 0) {
            return <Tag color="red">{overdueTasks} Overdue</Tag>;
          }
          return <Tag color="green">On Track</Tag>;
        }
        if (!date) return '-';
        const isLate = isOverdue(record);
        return (
          <div className={isLate ? 'text-red-500' : ''}>
            {dayjs(date).format('MM-DD')}
            {isLate && <ClockCircleOutlined className="ml-1" />}
          </div>
        );
      },
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record) => {
        if (record.isGroupHeader) {
          return <span className="text-gray-400">Group Actions</span>;
        }
        return (
          <Space>
            <Tooltip title="View History">
              <Button
                type="text"
                icon={<HistoryOutlined />}
                onClick={() => showTaskHistory(record)}
              />
            </Tooltip>
            <Tooltip title="Task Details">
              <Button 
                type="text" 
                icon={<EyeOutlined />} 
                onClick={() => showTaskDetails(record)}
              />
            </Tooltip>
          </Space>
        );
      },
    },
  ];

  // Performance metrics columns
  const performanceColumns: ColumnsType<TaskPerformanceMetricsDto> = [
    {
      title: 'User',
      key: 'user',
      render: (_, record) => (
        <div className="flex items-center">
          <Avatar icon={<UserOutlined />} className="mr-2" />
          <div>
            <div className="font-medium">{record.userName}</div>
            <div className="text-gray-500 text-sm">{record.userId}</div>
          </div>
        </div>
      ),
    },
    {
      title: 'Assigned',
      dataIndex: 'assignedTasks',
      key: 'assignedTasks',
    },
    {
      title: 'Completed',
      dataIndex: 'completedTasks',
      key: 'completedTasks',
    },
    {
      title: 'Overdue',
      dataIndex: 'overdueTasks',
      key: 'overdueTasks',
      render: (count: number) => count > 0 ? <Badge count={count} /> : 0,
    },
    {
      title: 'Completion Rate',
      dataIndex: 'completionRate',
      key: 'completionRate',
      render: (rate: number) => (
        <div style={{ width: 100 }}>
          <Progress percent={Math.round(rate)} size="small" />
        </div>
      ),
    },
    {
      title: 'Avg Time',
      dataIndex: 'averageCompletionTime',
      key: 'averageCompletionTime',
      render: (time: number) => `${Math.round(time)}h`,
    },
  ];

  // Prepare chart data
  const statusChartData = statistics?.statusCounts.map(item => ({
    type: item.status,
    value: item.count,
  })) || [];

  const priorityChartData = statistics?.priorityCounts.map(item => ({
    type: item.priority,
    value: item.count,
  })) || [];

  // Performance trend data (mock)
  const trendData = Array.from({ length: 7 }, (_, i) => ({
    date: dayjs().subtract(6 - i, 'day').format('MM-DD'),
    completed: Math.floor(Math.random() * 20) + 10,
    created: Math.floor(Math.random() * 25) + 15,
  }));

  return (
    <div className="p-4" style={{ height: '100vh', overflow: 'hidden' }}>
      <div className="mb-4">
        {/* Compressed Key Metrics - Max 100px height */}
        {statistics && (
          <Row gutter={12} className="mb-3" style={{ height: '90px' }}>
            <Col span={6}>
              <Card size="small" style={{ height: '100%' }} styles={{ body: { padding: '8px 12px' } }}>
                <Statistic
                  title="Total Tasks"
                  value={statistics.totalTasks}
                  prefix={<DashboardOutlined />}
                  valueStyle={{ fontSize: '18px' }}
                />
              </Card>
            </Col>
            <Col span={6}>
              <Card size="small" style={{ height: '100%' }} styles={{ body: { padding: '8px 12px' } }}>
                <Statistic
                  title="In Progress"
                  value={statistics.inProgressTasks}
                  valueStyle={{ color: '#1890ff', fontSize: '18px' }}
                  prefix={<SyncOutlined spin />}
                />
              </Card>
            </Col>
            <Col span={6}>
              <Card size="small" style={{ height: '100%' }} styles={{ body: { padding: '8px 12px' } }}>
                <Statistic
                  title="Completion Rate"
                  value={statistics.taskCompletionRate}
                  precision={1}
                  suffix="%"
                  valueStyle={{ color: '#52c41a', fontSize: '18px' }}
                  prefix={<CheckCircleOutlined />}
                />
              </Card>
            </Col>
            <Col span={6}>
              <Card size="small" style={{ height: '100%' }} styles={{ body: { padding: '8px 12px' } }}>
                <Statistic
                  title="Overdue"
                  value={statistics.overdueTasks}
                  valueStyle={{ color: '#f5222d', fontSize: '18px' }}
                  prefix={<ExclamationCircleOutlined />}
                />
              </Card>
            </Col>
          </Row>
        )}

        {/* Compact Filter Controls */}
        <Card size="small" className="mb-3" styles={{ body: { padding: '8px 12px' } }}>
          <Row gutter={12} align="middle">
            <Col span={5}>
              <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                <span style={{ fontSize: '12px', color: '#666', minWidth: '80px' }}>Work Package:</span>
                <Select
                  placeholder="All Work Packages"
                  value={workPackageFilter}
                  onChange={setWorkPackageFilter}
                  allowClear
                  style={{ width: '100%' }}
                  size="small"
                >
                  {availableWorkPackages.map(wp => (
                    <Option key={wp.value} value={wp.value}>{wp.label}</Option>
                  ))}
                </Select>
              </div>
            </Col>
            <Col span={3}>
              <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                <span style={{ fontSize: '12px', color: '#666', minWidth: '40px' }}>Status:</span>
                <Select
                  placeholder="All"
                  value={statusFilter}
                  onChange={setStatusFilter}
                  allowClear
                  style={{ width: '100%' }}
                  size="small"
                >
                  <Option value="Pending">Pending</Option>
                  <Option value="InProgress">In Progress</Option>
                  <Option value="Completed">Completed</Option>
                  <Option value="Cancelled">Cancelled</Option>
                </Select>
              </div>
            </Col>
            <Col span={3}>
              <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                <span style={{ fontSize: '12px', color: '#666', minWidth: '40px' }}>Priority:</span>
                <Select
                  placeholder="All"
                  value={priorityFilter}
                  onChange={setPriorityFilter}
                  allowClear
                  style={{ width: '100%' }}
                  size="small"
                >
                  <Option value="Low">Low</Option>
                  <Option value="Medium">Medium</Option>
                  <Option value="High">High</Option>
                  <Option value="Critical">Critical</Option>
                </Select>
              </div>
            </Col>
            <Col span={5}>
              <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                <span style={{ fontSize: '12px', color: '#666', minWidth: '60px' }}>Date Range:</span>
                <RangePicker
                  value={dateRange}
                  onChange={(dates) => setDateRange(dates as [dayjs.Dayjs, dayjs.Dayjs] | null)}
                  size="small"
                  style={{ width: '100%' }}
                />
              </div>
            </Col>
            <Col span={8}>
              <div className="flex justify-end gap-2">
                <Button
                  icon={<ReloadOutlined />}
                  onClick={loadData}
                  size="small"
                >
                  Refresh
                </Button>
                <Button
                  icon={<FilterOutlined />}
                  size="small"
                  type={statusFilter || priorityFilter || workPackageFilter || dateRange ? 'primary' : 'default'}
                >
                  {statusFilter || priorityFilter || workPackageFilter || dateRange ? 'Filtered' : 'Filter'}
                </Button>
              </div>
            </Col>
          </Row>
        </Card>
      </div>

      <Tabs 
        activeKey={activeTab} 
        onChange={setActiveTab} 
        size="small"
        style={{ height: 'calc(100vh - 160px)' }}
        tabBarStyle={{ marginBottom: '8px' }}
      >
        <TabPane tab={
          <span>
            <DashboardOutlined />
            Overview
          </span>
        } key="overview">
          <div style={{ height: 'calc(100vh - 200px)', display: 'flex', flexDirection: 'column' }}>
            {/* Compact Charts Row */}
            <Row gutter={12} style={{ marginBottom: '12px', height: '160px' }}>
              <Col span={8}>
                <Card title="Task Status Distribution" size="small" style={{ height: '100%' }}>
                  <Pie
                    data={statusChartData}
                    angleField="value"
                    colorField="type"
                    radius={0.8}
                    height={100}
                    legend={{
                      position: 'bottom',
                    }}
                  />
                </Card>
              </Col>
              <Col span={8}>
                <Card title="Priority Distribution" size="small" style={{ height: '100%' }}>
                  <Column
                    data={priorityChartData}
                    xField="type"
                    yField="value"
                    height={100}
                    meta={{
                      type: { alias: 'Priority' },
                      value: { alias: 'Count' },
                    }}
                  />
                </Card>
              </Col>
              <Col span={8}>
                <Card title="Task Trends (Last 7 Days)" size="small" style={{ height: '100%' }}>
                  <Line
                    data={[
                      ...trendData.map(d => ({ ...d, type: 'Completed', value: d.completed })),
                      ...trendData.map(d => ({ ...d, type: 'Created', value: d.created })),
                    ]}
                    xField="date"
                    yField="value"
                    seriesField="type"
                    height={100}
                    point={{
                      size: 3,
                    }}
                    meta={{
                      date: { alias: 'Date' },
                      value: { alias: 'Count' },
                    }}
                  />
                </Card>
              </Col>
            </Row>

            {/* Task Monitoring Table - Uses remaining space */}
            <Card 
              title="Tasks by Work Package" 
              size="small" 
              style={{ flex: 1, overflow: 'hidden' }}
              styles={{ body: { padding: '12px', height: 'calc(100% - 40px)', overflow: 'hidden' } }}
            >
              <Table<ExpandableTaskData>
                columns={monitoringColumns}
                dataSource={getExpandableTableData()}
                rowKey={(record) => record.isGroupHeader ? `header-${record.name}` : record.id}
                loading={loading}
                size="small"
                scroll={{ y: 'calc(100vh - 440px)' }}
                expandable={{
                  childrenColumnName: 'children',
                  defaultExpandAllRows: false,
                  rowExpandable: (record) => !!(record.isGroupHeader && record.children && record.children.length > 0),
                  expandIcon: ({ expanded, onExpand, record }) => {
                    if (!record.isGroupHeader) return null;
                    return (
                      <span
                        onClick={e => onExpand(record, e)}
                        style={{ cursor: 'pointer', fontSize: '12px', padding: '2px' }}
                      >
                        {expanded ? '🔼' : '🔽'}
                      </span>
                    );
                  }
                }}
                onRow={(record) => {
                  if (record.isGroupHeader) {
                    return {
                      style: { 
                        backgroundColor: '#f8f9fa', 
                        fontWeight: 'bold'
                      }
                    };
                  } else {
                    return {};
                  }
                }}
                pagination={{
                  size: 'small',
                  pageSize: 15,
                  showSizeChanger: true,
                  showQuickJumper: true,
                  pageSizeOptions: ['10', '15', '20', '30'],
                  showTotal: (total, range) => {
                    const taskCount = getExpandableTableData()
                      .reduce((sum, group) => sum + (group.children?.length || 0), 0);
                    return `${range[0]}-${range[1]} of ${total} work packages (${taskCount} tasks)`;
                  },
                }}
              />
            </Card>
          </div>
        </TabPane>

        <TabPane tab={
          <span>
            <UserOutlined />
            Performance
          </span>
        } key="performance">
          <Card title="User Performance Metrics" size="small" style={{ height: '100%' }}>
            <Table
              columns={performanceColumns}
              dataSource={performanceMetrics}
              rowKey="userId"
              size="small"
              scroll={{ y: 'calc(100vh - 240px)' }}
              pagination={{
                size: 'small',
                pageSize: 20,
                showSizeChanger: true,
              }}
            />
          </Card>
        </TabPane>

        <TabPane tab={
          <span>
            <BellOutlined />
            Real-time Updates
          </span>
        } key="realtime">
          <Row gutter={12} style={{ height: '100%' }}>
            <Col span={16}>
              <Card title="All Tasks" size="small" style={{ height: '100%' }}>
                <Table
                  columns={monitoringColumns}
                  dataSource={tasks}
                  rowKey="id"
                  loading={loading}
                  size="small"
                  scroll={{ y: 'calc(100vh - 280px)' }}
                  pagination={{
                    size: 'small',
                    pageSize: 12,
                    showSizeChanger: false,
                  }}
                />
              </Card>
            </Col>
            <Col span={8}>
              <Card title="Live Updates" size="small" style={{ height: '100%' }}>
                <div style={{ height: 'calc(100vh - 240px)', overflow: 'auto' }}>
                  <List
                    dataSource={realTimeUpdates}
                    renderItem={item => (
                      <List.Item className="py-1" style={{ padding: '4px 0' }}>
                        <div className="w-full">
                          <div className="flex justify-between items-start">
                            <div className="flex-1">
                              <div className="text-xs font-medium">{item.taskName}</div>
                              <div className="text-xs text-gray-500">
                                {item.action} by {item.user}
                              </div>
                            </div>
                            <div className="text-xs text-gray-400">{item.timestamp}</div>
                          </div>
                        </div>
                      </List.Item>
                    )}
                    size="small"
                  />
                </div>
              </Card>
            </Col>
          </Row>
        </TabPane>
      </Tabs>

      {/* Task History Modal */}
      <Modal
        title={`Task History - ${selectedTask?.name}`}
        open={historyModalVisible}
        onCancel={() => setHistoryModalVisible(false)}
        footer={null}
        width={800}
      >
        <Timeline mode="left">
          {taskHistory.map(item => (
            <Timeline.Item
              key={item.id}
              label={dayjs(item.changedAt).format('YYYY-MM-DD HH:mm')}
              color={
                item.action.includes('Completed') ? 'green' :
                item.action.includes('Assigned') ? 'blue' :
                item.action.includes('Updated') ? 'orange' : 'gray'
              }
            >
              <div>
                <div className="font-medium">{item.action}</div>
                <div className="text-gray-500 text-sm">by {item.changedBy}</div>
                {(item.oldValue || item.newValue) && (
                  <div className="text-xs text-gray-400 mt-1">
                    {item.oldValue && <span>From: {item.oldValue}</span>}
                    {item.oldValue && item.newValue && <span> → </span>}
                    {item.newValue && <span>To: {item.newValue}</span>}
                  </div>
                )}
                {item.reason && (
                  <div className="text-xs text-blue-600 mt-1">
                    Reason: {item.reason}
                  </div>
                )}
              </div>
            </Timeline.Item>
          ))}
        </Timeline>
      </Modal>

      {/* Task Details Modal */}
      <Modal
        title={`Task Details - ${selectedTask?.name}`}
        open={taskDetailModalVisible}
        onCancel={() => setTaskDetailModalVisible(false)}
        footer={null}
        width={800}
      >
        {selectedTask && (
          <div>
            <Descriptions bordered column={1} style={{ marginBottom: 16 }}>
              <Descriptions.Item label="Name">{selectedTask.name}</Descriptions.Item>
              <Descriptions.Item label="Type">{selectedTask.type}</Descriptions.Item>
              <Descriptions.Item label="Status">
                <Tag color={getStatusColor(selectedTask.status)}>{selectedTask.status}</Tag>
              </Descriptions.Item>
              <Descriptions.Item label="Priority">
                <Tag color={getPriorityColor(selectedTask.priority)}>{selectedTask.priority}</Tag>
              </Descriptions.Item>
              <Descriptions.Item label="Progress">
                <Progress percent={selectedTask.progressPercentage} size="small" />
              </Descriptions.Item>
              <Descriptions.Item label="Assigned To">
                {selectedTask.assignedTo ? (
                  <div className="flex items-center">
                    <Avatar size="small" icon={<UserOutlined />} className="mr-2" />
                    {selectedTask.assignedTo}
                  </div>
                ) : 'Unassigned'}
              </Descriptions.Item>
              <Descriptions.Item label="Due Date">
                {selectedTask.dueDate ? (
                  <div className={isOverdue(selectedTask) ? 'text-red-500' : ''}>
                    {dayjs(selectedTask.dueDate).format('YYYY-MM-DD HH:mm')}
                    {isOverdue(selectedTask) && <Badge status="error" text="Overdue" style={{ marginLeft: 8 }} />}
                  </div>
                ) : '-'}
              </Descriptions.Item>
              <Descriptions.Item label="Work Package">
                {selectedTask.workPackageName || 'N/A'}
              </Descriptions.Item>
              <Descriptions.Item label="Location">
                {selectedTask.location ? (
                  <div className="flex items-center">
                    <EnvironmentOutlined className="mr-1" />
                    {selectedTask.location}
                  </div>
                ) : 'N/A'}
              </Descriptions.Item>
              <Descriptions.Item label="Created At">
                {selectedTask.createdAt ? dayjs(selectedTask.createdAt).format('YYYY-MM-DD HH:mm') : '-'}
              </Descriptions.Item>
              <Descriptions.Item label="Updated At">
                {selectedTask.updatedAt ? dayjs(selectedTask.updatedAt).format('YYYY-MM-DD HH:mm') : '-'}
              </Descriptions.Item>
            </Descriptions>
            
            {selectedTask.description && (
              <div style={{ marginBottom: 16 }}>
                <h4>Description</h4>
                <div style={{ 
                  padding: '8px 12px', 
                  background: '#f5f5f5', 
                  border: '1px solid #d9d9d9', 
                  borderRadius: '6px' 
                }}>
                  {selectedTask.description}
                </div>
              </div>
            )}

            {selectedTask.instructions && (
              <div style={{ marginBottom: 16 }}>
                <h4>Instructions</h4>
                <div style={{ 
                  padding: '8px 12px', 
                  background: '#f0f9ff', 
                  border: '1px solid #bae6fd', 
                  borderRadius: '6px' 
                }}>
                  {selectedTask.instructions}
                </div>
              </div>
            )}

            {selectedTask.notes && (
              <div>
                <h4>Notes</h4>
                <div style={{ 
                  padding: '8px 12px', 
                  background: '#fffbf0', 
                  border: '1px solid #fde68a', 
                  borderRadius: '6px' 
                }}>
                  {selectedTask.notes}
                </div>
              </div>
            )}
          </div>
        )}
      </Modal>
    </div>
  );
};

export default TaskMonitoring; 