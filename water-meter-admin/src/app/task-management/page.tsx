'use client';

import React, { useState, useEffect, useCallback } from 'react';
import {
  Table, Button, Modal, Form, Input, Select, DatePicker, Space, 
  Card, Row, Col, Statistic, Tag, Progress, message, Popconfirm,
  Upload, Tooltip, InputNumber, Radio, Badge
} from 'antd';
import {
  PlusOutlined, SearchOutlined, ExportOutlined, ImportOutlined,
  EditOutlined, DeleteOutlined, UserOutlined, ClockCircleOutlined,
  DownloadOutlined, FilterOutlined, ReloadOutlined, EyeOutlined
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import dayjs from 'dayjs';
import { taskService, TaskListDto, CreateTaskDto, UpdateTaskDto, TaskSearchDto } from '@/services/task.service';

const { Option } = Select;
const { RangePicker } = DatePicker;
const { TextArea } = Input;

const TaskManagement: React.FC = () => {
  const [tasks, setTasks] = useState<TaskListDto[]>([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingTask, setEditingTask] = useState<TaskListDto | null>(null);
  const [form] = Form.useForm();
  const [searchForm] = Form.useForm();

  // Pagination and filtering
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [totalCount, setTotalCount] = useState(0);
  const [searchParams, setSearchParams] = useState<TaskSearchDto>({});

  // Statistics
  const [statistics, setStatistics] = useState({
    totalTasks: 0,
    pendingTasks: 0,
    inProgressTasks: 0,
    completedTasks: 0,
    overdueTasks: 0
  });

  // Constants
  const statusOptions = [
    { value: 'Pending', label: 'Pending', color: 'orange' },
    { value: 'InProgress', label: 'In Progress', color: 'blue' },
    { value: 'Completed', label: 'Completed', color: 'green' },
    { value: 'Cancelled', label: 'Cancelled', color: 'red' }
  ];

  const priorityOptions = [
    { value: 'Low', label: 'Low', color: 'gray' },
    { value: 'Medium', label: 'Medium', color: 'blue' },
    { value: 'High', label: 'High', color: 'orange' },
    { value: 'Critical', label: 'Critical', color: 'red' }
  ];

  const typeOptions = [
    { value: 'Reading', label: 'Reading' },
    { value: 'Inspection', label: 'Inspection' },
    { value: 'Maintenance', label: 'Maintenance' },
    { value: 'Installation', label: 'Installation' }
  ];

  // Load tasks
  const loadTasks = useCallback(async () => {
    setLoading(true);
    try {
      const searchDto: TaskSearchDto = {
        page: currentPage,
        pageSize,
        ...searchParams
      };
      
      const result = await taskService.getTasks(searchDto);
      setTasks(result.tasks);
      setTotalCount(result.totalCount);
    } catch (error) {
      message.error('Failed to load tasks');
      console.error('Load tasks error:', error);
    } finally {
      setLoading(false);
    }
  }, [currentPage, pageSize, searchParams]);

  // Load statistics
  const loadStatistics = useCallback(async () => {
    try {
      const stats = await taskService.getTaskStatistics();
      setStatistics({
        totalTasks: stats.totalTasks,
        pendingTasks: stats.pendingTasks,
        inProgressTasks: stats.inProgressTasks,
        completedTasks: stats.completedTasks,
        overdueTasks: stats.overdueTasks
      });
    } catch (error) {
      console.error('Load statistics error:', error);
    }
  }, []);

  useEffect(() => {
    loadTasks();
    loadStatistics();
  }, [loadTasks, loadStatistics]);

  // Create or update task
  const handleSubmit = async (values: any) => {
    try {
      const taskData = {
        ...values,
        dueDate: values.dueDate ? dayjs(values.dueDate).toISOString() : undefined,
        startDate: values.startDate ? dayjs(values.startDate).toISOString() : undefined,
      };

      if (editingTask) {
        await taskService.updateTask(editingTask.id, { ...taskData, id: editingTask.id });
        message.success('Task updated successfully');
      } else {
        await taskService.createTask(taskData);
        message.success('Task created successfully');
      }

      setModalVisible(false);
      setEditingTask(null);
      form.resetFields();
      loadTasks();
      loadStatistics();
    } catch (error) {
      message.error('Failed to save task');
      console.error('Save task error:', error);
    }
  };

  // Delete task
  const handleDelete = async (id: number) => {
    try {
      await taskService.deleteTask(id);
      message.success('Task deleted successfully');
      loadTasks();
      loadStatistics();
    } catch (error) {
      message.error('Failed to delete task');
      console.error('Delete task error:', error);
    }
  };

  // Search tasks
  const handleSearch = (values: any) => {
    const searchDto: TaskSearchDto = {
      ...values,
      dueDateFrom: values.dueDateRange?.[0] ? dayjs(values.dueDateRange[0]).toISOString() : undefined,
      dueDateTo: values.dueDateRange?.[1] ? dayjs(values.dueDateRange[1]).toISOString() : undefined,
    };
    delete searchDto.dueDateRange;
    
    setSearchParams(searchDto);
    setCurrentPage(1);
  };

  // Reset search
  const handleResetSearch = () => {
    searchForm.resetFields();
    setSearchParams({});
    setCurrentPage(1);
  };

  // Export tasks
  const handleExport = async () => {
    try {
      const blob = await taskService.exportTasks(searchParams);
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `tasks_${dayjs().format('YYYY-MM-DD')}.xlsx`;
      link.click();
      window.URL.revokeObjectURL(url);
      message.success('Tasks exported successfully');
    } catch (error) {
      message.error('Failed to export tasks');
      console.error('Export error:', error);
    }
  };

  // Import tasks
  const handleImport = async (file: File) => {
    try {
      const result = await taskService.importTasks(file);
      message.success(`${result.message}. Imported ${result.importedCount} tasks.`);
      loadTasks();
      loadStatistics();
    } catch (error) {
      message.error('Failed to import tasks');
      console.error('Import error:', error);
    }
  };

  // Update task status
  const handleStatusChange = async (taskId: number, status: string) => {
    try {
      await taskService.updateTaskStatus(taskId, status);
      message.success('Task status updated successfully');
      loadTasks();
      loadStatistics();
    } catch (error) {
      message.error('Failed to update task status');
      console.error('Status update error:', error);
    }
  };

  // Update task progress
  const handleProgressChange = async (taskId: number, progress: number) => {
    try {
      await taskService.updateTaskProgress(taskId, progress);
      message.success('Task progress updated successfully');
      loadTasks();
      loadStatistics();
    } catch (error) {
      message.error('Failed to update task progress');
      console.error('Progress update error:', error);
    }
  };

  // Get status color
  const getStatusColor = (status: string) => {
    return statusOptions.find(opt => opt.value === status)?.color || 'default';
  };

  // Get priority color
  const getPriorityColor = (priority: string) => {
    return priorityOptions.find(opt => opt.value === priority)?.color || 'default';
  };

  // Check if task is overdue
  const isOverdue = (task: TaskListDto) => {
    return task.dueDate && dayjs(task.dueDate).isBefore(dayjs()) && task.status !== 'Completed';
  };

  // Table columns
  const columns: ColumnsType<TaskListDto> = [
    {
      title: 'Task Name',
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: TaskListDto) => (
        <div>
          <div className="font-medium">{text}</div>
          {record.description && (
            <div className="text-gray-500 text-sm truncate max-w-xs">{record.description}</div>
          )}
        </div>
      ),
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status: string, record: TaskListDto) => (
        <div>
          <Select
            value={status}
            size="small"
            style={{ width: 120 }}
            onChange={(value) => handleStatusChange(record.id, value)}
          >
            {statusOptions.map(option => (
              <Option key={option.value} value={option.value}>
                <Tag color={option.color}>{option.label}</Tag>
              </Option>
            ))}
          </Select>
          {isOverdue(record) && <Badge status="error" text="Overdue" />}
        </div>
      ),
    },
    {
      title: 'Priority',
      dataIndex: 'priority',
      key: 'priority',
      render: (priority: string) => (
        <Tag color={getPriorityColor(priority)}>{priority}</Tag>
      ),
    },
    {
      title: 'Type',
      dataIndex: 'type',
      key: 'type',
    },
    {
      title: 'Assigned To',
      dataIndex: 'assignedUserId',
      key: 'assignedUserId',
      render: (assignedUserId: number, record: TaskListDto) => {
        if (!assignedUserId) return 'Unassigned';
        return record.assignedUserName || `User ${assignedUserId}`;
      },
    },
    {
      title: 'Progress',
      dataIndex: 'progressPercentage',
      key: 'progressPercentage',
      render: (progress: number, record: TaskListDto) => (
        <div style={{ width: 120 }}>
          <Progress 
            percent={progress} 
            size="small" 
            status={progress === 100 ? 'success' : 'active'}
            showInfo={false}
          />
          <div className="text-xs text-center mt-1">{progress}%</div>
        </div>
      ),
    },
    {
      title: 'Due Date',
      dataIndex: 'dueDate',
      key: 'dueDate',
      render: (date: string, record: TaskListDto) => {
        if (!date) return '-';
        const isLate = isOverdue(record);
        return (
          <div className={isLate ? 'text-red-500' : ''}>
            {dayjs(date).format('YYYY-MM-DD')}
            {isLate && <ClockCircleOutlined className="ml-1" />}
          </div>
        );
      },
    },
    {
      title: 'Created',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (date: string) => dayjs(date).format('YYYY-MM-DD'),
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record: TaskListDto) => (
        <Space>
          <Tooltip title="Edit Task">
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => {
                setEditingTask(record);
                form.setFieldsValue({
                  ...record,
                  dueDate: record.dueDate ? dayjs(record.dueDate) : undefined,
                  startDate: record.startDate ? dayjs(record.startDate) : undefined,
                });
                setModalVisible(true);
              }}
            />
          </Tooltip>
          <Popconfirm
            title="Are you sure to delete this task?"
            onConfirm={() => handleDelete(record.id)}
            okText="Yes"
            cancelText="No"
          >
            <Tooltip title="Delete Task">
              <Button type="text" danger icon={<DeleteOutlined />} />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div className="p-6">
      <div className="mb-6">
        <div className="flex justify-end items-center mb-4">
          <Space>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => {
                setEditingTask(null);
                form.resetFields();
                setModalVisible(true);
              }}
            >
              Create Task
            </Button>
          </Space>
        </div>

        {/* Statistics Cards */}
        <Row gutter={16} className="mb-6">
          <Col span={6}>
            <Card>
              <Statistic title="Total Tasks" value={statistics.totalTasks} />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic 
                title="Pending" 
                value={statistics.pendingTasks} 
                valueStyle={{ color: '#fa8c16' }}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic 
                title="In Progress" 
                value={statistics.inProgressTasks} 
                valueStyle={{ color: '#1890ff' }}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic 
                title="Completed" 
                value={statistics.completedTasks} 
                valueStyle={{ color: '#52c41a' }}
              />
            </Card>
          </Col>
        </Row>

        {/* Search Filters */}
        <Card className="mb-4">
          <Form form={searchForm} onFinish={handleSearch} layout="inline">
            <Form.Item name="name" label="Task Name">
              <Input placeholder="Search by name" style={{ width: 150 }} />
            </Form.Item>
            <Form.Item name="status" label="Status">
              <Select placeholder="Select status" style={{ width: 120 }}>
                {statusOptions.map(option => (
                  <Option key={option.value} value={option.value}>{option.label}</Option>
                ))}
              </Select>
            </Form.Item>
            <Form.Item name="priority" label="Priority">
              <Select placeholder="Select priority" style={{ width: 120 }}>
                {priorityOptions.map(option => (
                  <Option key={option.value} value={option.value}>{option.label}</Option>
                ))}
              </Select>
            </Form.Item>
            <Form.Item name="type" label="Type">
              <Select placeholder="Select type" style={{ width: 120 }}>
                {typeOptions.map(option => (
                  <Option key={option.value} value={option.value}>{option.label}</Option>
                ))}
              </Select>
            </Form.Item>
            <Form.Item name="assignedTo" label="Assigned To">
              <Input placeholder="Assigned user" style={{ width: 120 }} />
            </Form.Item>
            <Form.Item name="dueDateRange" label="Due Date">
              <RangePicker style={{ width: 250 }} />
            </Form.Item>
            <Form.Item name="isOverdue" label="Overdue">
              <Radio.Group>
                <Radio.Button value={true}>Overdue Only</Radio.Button>
                <Radio.Button value={false}>All</Radio.Button>
              </Radio.Group>
            </Form.Item>
            <Form.Item>
              <Space>
                <Button type="primary" htmlType="submit" icon={<SearchOutlined />}>
                  Search
                </Button>
                <Button onClick={handleResetSearch} icon={<ReloadOutlined />}>
                  Reset
                </Button>
              </Space>
            </Form.Item>
          </Form>
        </Card>

        {/* Action Bar */}
        <div className="flex justify-between items-center mb-4">
          <div>
            <Space>
              <Button icon={<ExportOutlined />} onClick={handleExport}>
                Export
              </Button>
              <Upload
                accept=".xlsx,.xls"
                showUploadList={false}
                beforeUpload={(file) => {
                  handleImport(file);
                  return false;
                }}
              >
                <Button icon={<ImportOutlined />}>Import</Button>
              </Upload>
            </Space>
          </div>
          <div className="text-gray-500">
            Total: {totalCount} tasks
          </div>
        </div>
      </div>

      {/* Tasks Table */}
      <Card>
        <Table
          columns={columns}
          dataSource={tasks}
          rowKey="id"
          loading={loading}
          pagination={{
            current: currentPage,
            pageSize: pageSize,
            total: totalCount,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} tasks`,
            onChange: (page, size) => {
              setCurrentPage(page);
              setPageSize(size || 10);
            },
          }}
          scroll={{ x: 1200 }}
        />
      </Card>

      {/* Create/Edit Modal */}
      <Modal
        title={editingTask ? 'Edit Task' : 'Create Task'}
        visible={modalVisible}
        onCancel={() => {
          setModalVisible(false);
          setEditingTask(null);
          form.resetFields();
        }}
        footer={null}
        width={800}
      >
        <Form form={form} onFinish={handleSubmit} layout="vertical">
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="name"
                label="Task Name"
                rules={[{ required: true, message: 'Please enter task name' }]}
              >
                <Input placeholder="Enter task name" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="type"
                label="Type"
                rules={[{ required: true, message: 'Please select task type' }]}
              >
                <Select placeholder="Select task type">
                  {typeOptions.map(option => (
                    <Option key={option.value} value={option.value}>{option.label}</Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item name="description" label="Description">
            <TextArea rows={3} placeholder="Enter task description" />
          </Form.Item>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name="status"
                label="Status"
                rules={[{ required: true, message: 'Please select status' }]}
              >
                <Select placeholder="Select status">
                  {statusOptions.map(option => (
                    <Option key={option.value} value={option.value}>{option.label}</Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="priority"
                label="Priority"
                rules={[{ required: true, message: 'Please select priority' }]}
              >
                <Select placeholder="Select priority">
                  {priorityOptions.map(option => (
                    <Option key={option.value} value={option.value}>{option.label}</Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="assignedUserId" label="Assigned To">
                <InputNumber placeholder="Enter user ID" style={{ width: '100%' }} />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item name="dueDate" label="Due Date">
                <DatePicker style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="startDate" label="Start Date">
                <DatePicker style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="estimatedHours" label="Estimated Hours">
                <InputNumber min={0} step={0.5} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
          </Row>

          {editingTask && (
            <Row gutter={16}>
              <Col span={8}>
                <Form.Item name="actualHours" label="Actual Hours">
                  <InputNumber min={0} step={0.5} style={{ width: '100%' }} />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item name="progressPercentage" label="Progress (%)">
                  <InputNumber min={0} max={100} style={{ width: '100%' }} />
                </Form.Item>
              </Col>
            </Row>
          )}

          <Form.Item name="location" label="Location">
            <Input placeholder="Enter task location" />
          </Form.Item>

          <Form.Item name="instructions" label="Instructions">
            <TextArea rows={3} placeholder="Enter task instructions" />
          </Form.Item>

          <Form.Item name="notes" label="Notes">
            <TextArea rows={2} placeholder="Enter additional notes" />
          </Form.Item>

          <div className="flex justify-end space-x-2">
            <Button
              onClick={() => {
                setModalVisible(false);
                setEditingTask(null);
                form.resetFields();
              }}
            >
              Cancel
            </Button>
            <Button type="primary" htmlType="submit">
              {editingTask ? 'Update' : 'Create'}
            </Button>
          </div>
        </Form>
      </Modal>
    </div>
  );
};

export default TaskManagement; 