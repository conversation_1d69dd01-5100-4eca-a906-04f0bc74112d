// Menu code to display name mapping
// This mapping should match the menu definitions in the database
export const menuCodeToNameMapping: Record<string, string> = {
  // Top level menus
  'dashboard': 'Dashboard',
  'master-data': 'Master Data',
  'planning': 'Planning',
  'task-management': 'Task Management',
  'meter-reading': 'Meter Reading',
  'sync-center': 'Sync Center',
  'reports': 'Reports',
  'mobile-management': 'Mobile Management',
  'quick-access': 'Quick Access',
  'system': 'System Management',

  // Master Data submenus
  'amis-sync': 'AMIS Sync',
  'meter-mgmt': 'Meter Management',
  'baseline-mgmt': 'Baseline Management',

  // Planning submenus
  'workpackage-mgmt': 'Work Package Management',
  'work-packages': 'Work Package Management',
  'route-mgmt': 'Route Management',
  'frequency-templates': 'Frequency Templates',

  // Task Management submenus
  'task-mgmt': 'Task Management',
  'task-assignment': 'Task Assignment',
  'bulk-assignment': 'Bulk Assignment',
  'reactive-assignment': 'Reactive Assignment',
  'task-monitor': 'Task Monitoring',
  'task-monitoring': 'Task Monitoring',
  'overdue-mgmt': 'Overdue Management',
  'overdue-management': 'Overdue Management',

  // Meter Reading submenus
  'reading-records': 'Reading Records',
  'photo-management': 'Photo Management',
  'photo-mgmt': 'Photo Management',
  'validation-rules': 'Validation Rules',
  'anomaly-mgmt': 'Anomaly Management',
  'anomaly-management': 'Anomaly Management',

  // Sync Center submenus
  'device-sync-status': 'Device Sync Status',
  'sync-queue': 'Sync Queue',
  'offline-data': 'Offline Data',
  'sync-logs': 'Sync Logs',

  // Reports submenus
  'operational-dashboard': 'Operational Dashboard',
  'completion-reports': 'Completion Reports',
  'exception-analysis': 'Exception Analysis',
  'user-performance': 'User Performance',
  'generate-reports': 'Generate Reports',
  'export-history': 'Export History',
  'template-mgmt': 'Template Management',

  // Mobile Management submenus
  'device-registry': 'Device Registry',
  'app-versions': 'App Versions',
  'compatibility-tests': 'Compatibility Tests',

  // Quick Access submenus
  'create-reactive-task': 'Create Reactive Task',
  'todays-assignments': "Today's Assignments",
  'pending-sync': 'Pending Sync',
  'recent-alerts': 'Recent Alerts',

  // System Management submenus
  'menu-mgmt': 'Menu Management',
  'role-mgmt': 'Role Management',
  'perm-mgmt': 'Permission Management',
  'user-mgmt': 'User Management',
  'settings': 'Settings',

  // Legacy menu codes (for backward compatibility)
  'menu-management': 'Menu Management',
  'user-management': 'User Management',
  'role-management': 'Role Management',
  'permission-management': 'Permission Management',
  'water-meters': 'Meter Management',
  'baseline': 'Baseline Management',
  'reading-mgmt': 'Reading Records',
  'photo-gallery': 'Photo Management',
  'validation-mgmt': 'Validation Rules',
  'meter-reading': 'Meter Reading'
};

/**
 * Get display name for a menu code
 * @param menuCode - The menu code to look up
 * @returns The display name or the original code if not found
 */
export function getMenuDisplayName(menuCode: string): string {
  return menuCodeToNameMapping[menuCode] || menuCode;
}

/**
 * Check if a menu code exists in the mapping
 * @param menuCode - The menu code to check
 * @returns True if the menu code exists in the mapping
 */
export function isValidMenuCode(menuCode: string): boolean {
  return menuCode in menuCodeToNameMapping;
} 