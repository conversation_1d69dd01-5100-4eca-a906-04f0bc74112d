import configurationService from '../services/configuration.service'

export const initializeAllConfigurations = async () => {
  try {
    console.log('Initializing configuration templates...')
    
    // Initialize default templates
    const success = await configurationService.initializeDefaults()
    
    if (success) {
      console.log('✅ Configuration templates initialized successfully')
      return true
    } else {
      console.error('❌ Failed to initialize configuration templates')
      return false
    }
  } catch (error) {
    console.error('❌ Error initializing configurations:', error)
    return false
  }
}

export const checkConfigurationHealth = async () => {
  try {
    // Check if system configurations exist
    const systemConfigs = await configurationService.getSystemSettings()
    console.log('System configurations found:', systemConfigs.length)
    
    // Check if user configurations exist  
    const userConfigs = await configurationService.getUserSettings(1)
    console.log('User configurations found:', userConfigs.length)
    
    return {
      systemConfigsCount: systemConfigs.length,
      userConfigsCount: userConfigs.length,
      hasConfigurations: systemConfigs.length > 0 || userConfigs.length > 0
    }
  } catch (error) {
    console.error('Error checking configuration health:', error)
    return {
      systemConfigsCount: 0,
      userConfigsCount: 0,
      hasConfigurations: false
    }
  }
} 