'use client'

import { Layout, Menu, Dropdown, Avatar } from 'antd'
import { LogoutOutlined, UserOutlined, SettingOutlined } from '@ant-design/icons'
import { useRouter } from 'next/navigation'
import type { MenuProps } from 'antd'
import { authService } from '../../services/auth.service'

const { Header } = Layout

interface NavbarProps {
  currentPageTitle: string
}

export default function Navbar({ currentPageTitle }: NavbarProps) {
  const router = useRouter()

  const handleLogout = () => {
    // Use authService to ensure proper cleanup
    authService.logout()
    // Redirect to login page
    router.push('/login')
  }

  const handleMenuClick = ({ key }: { key: string }) => {
    switch (key) {
      case 'profile':
        router.push('/profile')
        break
      case 'settings':
        router.push('/user-settings')
        break
      case 'logout':
        handleLogout()
        break
    }
  }

  const userMenuItems: MenuProps['items'] = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: 'Profile',
    },
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: 'Settings',
    },
    {
      type: 'divider',
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: 'Logout',
    },
  ]

  return (
    <Header className="bg-white px-6 flex items-center justify-between border-b">
      <div className="text-lg font-semibold">
        {currentPageTitle}
      </div>
      <Dropdown
        menu={{ items: userMenuItems, onClick: handleMenuClick }}
        placement="bottomRight"
        arrow
      >
        <Avatar
          className="cursor-pointer"
          icon={<UserOutlined />}
        />
      </Dropdown>
    </Header>
  )
} 