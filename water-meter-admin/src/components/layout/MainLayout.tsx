'use client'

import { Layout } from 'antd'
import Sidebar from './Sidebar'
import Navbar from './Navbar'
import React from 'react'
import { getMenuDisplayName } from '@/utils/menuMapping'

const { Content } = Layout

interface MainLayoutProps {
  selectedMenu: string
  onMenuSelect: (key: string) => void
  children: React.ReactNode
}

export default function MainLayout({ selectedMenu, onMenuSelect, children }: MainLayoutProps) {
  const currentPageTitle = getMenuDisplayName(selectedMenu)

  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Sidebar selectedMenu={selectedMenu} onMenuSelect={onMenuSelect} />
      <Layout>
        <Navbar currentPageTitle={currentPageTitle} />
        <Content className="p-2 bg-gray-50 min-h-screen">
          {children}
        </Content>
      </Layout>
    </Layout>
  )
} 