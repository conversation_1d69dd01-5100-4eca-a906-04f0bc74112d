'use client'

import { Layout, Menu, Spin } from 'antd'
import {
  DashboardOutlined,
  MenuOutlined,
  UserOutlined,
  TeamOutlined,
  SafetyOutlined,
  SettingOutlined,
  FileTextOutlined,
  DatabaseOutlined,
  CalendarOutlined,
  CheckSquareOutlined,
  ReadOutlined,
  SyncOutlined,
  <PERSON><PERSON><PERSON><PERSON>utlined,
  MobileOutlined,
  <PERSON><PERSON>Outlined,
  <PERSON><PERSON>hartOutlined,
  <PERSON>mOutlined,
  Clock<PERSON>ircleOutlined,
  UserSwitchOutlined,
  MonitorOutlined,
  ExclamationCircleOutlined,
  PictureOutlined,
  AlertOutlined,
  CheckCircleOutlined,
  TabletOutlined,
  UnorderedListOutlined,
  DisconnectOutlined,
  PieChartOutlined,
  WarningOutlined,
  TrophyOutlined,
  ExportOutlined,
  AppstoreOutlined,
  ExperimentOutlined,
  Plus<PERSON>ircleOutlined,
  <PERSON>glassOutlined,
  BellOutlined,
  UsergroupAddOutlined,
  FileExcelOutlined,
  HistoryOutlined,
  LayoutOutlined
} from '@ant-design/icons'
import { useEffect, useState } from 'react'
import { api } from '../../services/api'

const { Sider } = Layout

interface MenuItem {
  id: number
  name: string
  code: string
  path: string
  icon: string
  order: number
  isVisible: boolean
  isDeleted: boolean
  parentId?: number
  children?: MenuItem[]
}

interface SidebarProps {
  selectedMenu: string
  onMenuSelect: (key: string) => void
}

export default function Sidebar({ selectedMenu, onMenuSelect }: SidebarProps) {
  const [menuItems, setMenuItems] = useState<MenuItem[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchMenus = async () => {
      try {
        const response = await api.get('/menu')
        setMenuItems(response.data || [])
      } catch (error) {
        console.error('Failed to fetch menus:', error)
        // Fallback to default menus if API fails
        setMenuItems([])
      } finally {
        setLoading(false)
      }
    }

    fetchMenus()
  }, [])

  const getIcon = (iconName: string) => {
    switch (iconName) {
      // Dashboard
      case 'DashboardOutlined':
        return <DashboardOutlined />
      
      // Master Data
      case 'DatabaseOutlined':
        return <DatabaseOutlined />
      case 'LineChartOutlined':
        return <LineChartOutlined />
      
      // Planning
      case 'CalendarOutlined':
        return <CalendarOutlined />
      case 'AimOutlined':
        return <AimOutlined />
      case 'ClockCircleOutlined':
        return <ClockCircleOutlined />
      
      // Task Management
      case 'CheckSquareOutlined':
        return <CheckSquareOutlined />
      case 'UserSwitchOutlined':
        return <UserSwitchOutlined />
      case 'MonitorOutlined':
        return <MonitorOutlined />
      case 'ExclamationCircleOutlined':
        return <ExclamationCircleOutlined />
      case 'UsergroupAddOutlined':
        return <UsergroupAddOutlined />
      case 'ThunderboltOutlined':
        return <ThunderboltOutlined />
      
      // Meter Reading
      case 'ReadOutlined':
        return <ReadOutlined />
      case 'PictureOutlined':
        return <PictureOutlined />
      case 'AlertOutlined':
        return <AlertOutlined />
      case 'CheckCircleOutlined':
        return <CheckCircleOutlined />
      
      // Sync Center
      case 'SyncOutlined':
        return <SyncOutlined />
      case 'TabletOutlined':
        return <TabletOutlined />
      case 'UnorderedListOutlined':
        return <UnorderedListOutlined />
      case 'DisconnectOutlined':
        return <DisconnectOutlined />
      case 'FileTextOutlined':
        return <FileTextOutlined />
      
      // Reports
      case 'BarChartOutlined':
        return <BarChartOutlined />
      case 'PieChartOutlined':
        return <PieChartOutlined />
      case 'WarningOutlined':
        return <WarningOutlined />
      case 'TrophyOutlined':
        return <TrophyOutlined />
      case 'ExportOutlined':
        return <ExportOutlined />
      case 'FileExcelOutlined':
        return <FileExcelOutlined />
      case 'HistoryOutlined':
        return <HistoryOutlined />
      case 'LayoutOutlined':
        return <LayoutOutlined />
      
      // Mobile Management
      case 'MobileOutlined':
        return <MobileOutlined />
      case 'AppstoreOutlined':
        return <AppstoreOutlined />
      case 'ExperimentOutlined':
        return <ExperimentOutlined />
      
      // Quick Access
      case 'PlusCircleOutlined':
        return <PlusCircleOutlined />
      case 'HourglassOutlined':
        return <HourglassOutlined />
      case 'BellOutlined':
        return <BellOutlined />
      
      // System Management
      case 'SettingOutlined':
        return <SettingOutlined />
      case 'MenuOutlined':
        return <MenuOutlined />
      case 'TeamOutlined':
        return <TeamOutlined />
      case 'SafetyOutlined':
        return <SafetyOutlined />
      case 'UserOutlined':
        return <UserOutlined />
      
      // Legacy support for old naming
      case 'dashboard':
        return <DashboardOutlined />
      case 'menu':
        return <MenuOutlined />
      case 'user':
        return <UserOutlined />
      case 'team':
        return <TeamOutlined />
      case 'safety':
        return <SafetyOutlined />
      case 'setting':
        return <SettingOutlined />
      case 'file':
        return <FileTextOutlined />
      
      default:
        return <DashboardOutlined />
    }
  }

  // Build hierarchical menu structure
  const buildMenuTree = (items: MenuItem[]): MenuItem[] => {
    const itemMap = new Map<number, MenuItem>()
    const rootItems: MenuItem[] = []

    // Create a map of all items
    items.forEach(item => {
      itemMap.set(item.id, { ...item, children: [] })
    })

    // Build the tree structure
    items.forEach(item => {
      const menuItem = itemMap.get(item.id)!
      
      if (item.parentId && itemMap.has(item.parentId)) {
        // Add to parent's children
        const parent = itemMap.get(item.parentId)!
        if (!parent.children) parent.children = []
        parent.children.push(menuItem)
      } else {
        // Root level item
        rootItems.push(menuItem)
      }
    })

    return rootItems
  }

  const convertToAntdMenuItems = (items: MenuItem[]): any[] => {
    return items
      .filter(item => item.isVisible && !item.isDeleted)
      .sort((a, b) => a.order - b.order)
      .map((item) => {
        const hasChildren = item.children && item.children.length > 0
        
        return {
          key: item.code, // Use code as key for better identification
          icon: getIcon(item.icon),
          label: item.name,
          onClick: !hasChildren ? () => {
            onMenuSelect(item.code);
          } : undefined, // Only leaf nodes are clickable
          children: hasChildren ? convertToAntdMenuItems(item.children!) : undefined,
        }
      })
  }

  return (
    <Sider width={250} className="min-h-screen bg-white border-r" theme="light">
      <div className="h-16 flex items-center justify-center border-b">
        <h1 className="text-xl font-bold">Water Meter</h1>
      </div>
      {loading ? (
        <div className="flex justify-center items-center h-32">
          <Spin size="large" />
        </div>
      ) : (
        <Menu
          mode="inline"
          selectedKeys={[selectedMenu]}
          onSelect={({ key }) => {
            // console.log('Menu selected:', key);
            onMenuSelect(key as string);
          }}
          items={convertToAntdMenuItems(buildMenuTree(menuItems))}
          className="border-r-0"
        />
      )}
    </Sider>
  )
} 