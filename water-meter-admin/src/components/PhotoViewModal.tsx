import React, { useState, useEffect } from 'react';
import {
  Mo<PERSON>,
  Card,
  Row,
  Col,
  Image,
  Empty,
  Spin,
  message,
  Button,
  Tooltip,
  Typography,
  Space,
  Tag,
  Popconfirm
} from 'antd';
import {
  EyeOutlined,
  DownloadOutlined,
  DeleteOutlined,
  ZoomInOutlined
} from '@ant-design/icons';
import { meterReadingService, ReadingPhotoDto } from '../services/meter-reading.service';
import dayjs from 'dayjs';

const { Text } = Typography;

interface PhotoViewModalProps {
  visible: boolean;
  onCancel: () => void;
  readingId: number;
  meterNumber?: string;
}

const PhotoViewModal: React.FC<PhotoViewModalProps> = ({
  visible,
  onCancel,
  readingId,
  meterNumber
}) => {
  const [photos, setPhotos] = useState<ReadingPhotoDto[]>([]);
  const [loading, setLoading] = useState(false);
  const [previewVisible, setPreviewVisible] = useState(false);
  const [previewImage, setPreviewImage] = useState('');

  useEffect(() => {
    if (visible && readingId) {
      loadPhotos();
    }
  }, [visible, readingId]);

  const loadPhotos = async () => {
    setLoading(true);
    try {
      const result = await meterReadingService.getReadingPhotos(readingId);
      setPhotos(result);
    } catch (error) {
      message.error('Failed to load photos');
      console.error('Load photos error:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleDeletePhoto = async (photoId: number) => {
    try {
      await meterReadingService.deletePhoto(photoId);
      message.success('Photo deleted successfully');
      setPhotos(photos.filter(p => p.id !== photoId));
    } catch (error) {
      message.error('Failed to delete photo');
      console.error('Delete photo error:', error);
    }
  };

  const handleDownloadPhoto = (photo: ReadingPhotoDto) => {
    const link = document.createElement('a');
    link.href = photo.cloudflareUrl;
    link.download = photo.originalFileName;
    link.target = '_blank';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getQualityColor = (score?: number): string => {
    if (!score) return 'default';
    if (score >= 0.8) return 'green';
    if (score >= 0.6) return 'orange';
    return 'red';
  };

  return (
    <>
      <Modal
        title={`Photos for Reading #${readingId}${meterNumber ? ` - Meter ${meterNumber}` : ''}`}
        open={visible}
        onCancel={onCancel}
        footer={null}
        width={1000}
        style={{ top: 20 }}
      >
        <Spin spinning={loading}>
          {photos.length === 0 ? (
            <Empty 
              description="No photos found for this reading"
              style={{ margin: '40px 0' }}
            />
          ) : (
            <Row gutter={[16, 16]}>
              {photos.map((photo) => (
                <Col span={8} key={photo.id}>
                  <Card
                    hoverable
                    cover={
                      <div style={{ position: 'relative', height: 200, overflow: 'hidden' }}>
                        <Image
                          alt={photo.originalFileName}
                          src={photo.thumbnailUrl || photo.cloudflareUrl}
                          style={{ 
                            width: '100%', 
                            height: '100%', 
                            objectFit: 'cover' 
                          }}
                          preview={false}
                          onClick={() => {
                            setPreviewImage(photo.cloudflareUrl);
                            setPreviewVisible(true);
                          }}
                        />
                        <Button
                          type="primary"
                          icon={<ZoomInOutlined />}
                          size="small"
                          style={{
                            position: 'absolute',
                            top: 8,
                            right: 8,
                            opacity: 0.8
                          }}
                          onClick={() => {
                            setPreviewImage(photo.cloudflareUrl);
                            setPreviewVisible(true);
                          }}
                        />
                      </div>
                    }
                    size="small"
                    actions={[
                      <Tooltip title="View Full Size" key="view">
                        <Button
                          type="text"
                          icon={<EyeOutlined />}
                          onClick={() => {
                            setPreviewImage(photo.cloudflareUrl);
                            setPreviewVisible(true);
                          }}
                        />
                      </Tooltip>,
                      <Tooltip title="Download" key="download">
                        <Button
                          type="text"
                          icon={<DownloadOutlined />}
                          onClick={() => handleDownloadPhoto(photo)}
                        />
                      </Tooltip>,
                      <Popconfirm
                        title="Are you sure to delete this photo?"
                        onConfirm={() => handleDeletePhoto(photo.id)}
                        key="delete"
                      >
                        <Button
                          type="text"
                          icon={<DeleteOutlined />}
                          danger
                        />
                      </Popconfirm>
                    ]}
                  >
                    <Card.Meta
                      title={
                        <Tooltip title={photo.originalFileName}>
                          <Text ellipsis style={{ fontSize: 12 }}>
                            {photo.originalFileName}
                          </Text>
                        </Tooltip>
                      }
                      description={
                        <Space direction="vertical" size="small" style={{ width: '100%' }}>
                          <div>
                            <Text type="secondary" style={{ fontSize: 11 }}>
                              Size: {formatFileSize(photo.fileSize)}
                            </Text>
                          </div>
                          <div>
                            <Text type="secondary" style={{ fontSize: 11 }}>
                              Upload: {dayjs(photo.uploadTime).format('MM-DD HH:mm')}
                            </Text>
                          </div>
                          {photo.qualityScore && (
                            <div>
                              <Tag color={getQualityColor(photo.qualityScore)} size="small">
                                Quality: {(photo.qualityScore * 100).toFixed(0)}%
                              </Tag>
                            </div>
                          )}
                          {photo.hasOCR && photo.ocrResult && (
                            <div>
                              <Tag color="blue" size="small">
                                OCR: {photo.ocrResult}
                              </Tag>
                            </div>
                          )}
                        </Space>
                      }
                    />
                  </Card>
                </Col>
              ))}
            </Row>
          )}
        </Spin>
      </Modal>

      <Image
        width={200}
        style={{ display: 'none' }}
        src={previewImage}
        preview={{
          visible: previewVisible,
          src: previewImage,
          onVisibleChange: (value) => {
            setPreviewVisible(value);
          },
        }}
      />
    </>
  );
};

export default PhotoViewModal;
