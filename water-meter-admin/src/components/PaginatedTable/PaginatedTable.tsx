import React, { useMemo, useCallback } from 'react';
import { Table } from 'antd';
import { PaginatedTableProps, PaginationDefaults } from './types';

/**
 * Reusable paginated table component with standardized pagination
 * 可复用的分页表格组件，具有标准化分页功能
 */
export function PaginatedTable<T extends Record<string, any>>({
  data,
  total,
  currentPage = 1,
  currentPageSize,
  paginationConfig,
  useDataManagementPagination = true,
  loading = false,
  onPageChange,
  onSortChange,
  ...tableProps
}: PaginatedTableProps<T>) {
  
  // Determine default page size based on context
  const defaultPageSize = useMemo(() => {
    if (currentPageSize) return currentPageSize;
    return useDataManagementPagination 
      ? PaginationDefaults.dataManagementPageSize 
      : PaginationDefaults.defaultPageSize;
  }, [currentPageSize, useDataManagementPagination]);

  // Determine page size options based on context
  const pageSizeOptions = useMemo(() => {
    if (paginationConfig?.pageSizeOptions) return paginationConfig.pageSizeOptions;
    return useDataManagementPagination 
      ? PaginationDefaults.dataManagementPageSizeOptions 
      : PaginationDefaults.standardPageSizeOptions;
  }, [paginationConfig?.pageSizeOptions, useDataManagementPagination]);

  // Handle pagination change
  const handleTableChange = useCallback((pagination: any, filters: any, sorter: any) => {
    // Handle pagination
    if (onPageChange && pagination) {
      onPageChange(pagination.current || 1, pagination.pageSize || defaultPageSize);
    }

    // Handle sorting
    if (onSortChange && sorter && sorter.field) {
      const sortDirection = sorter.order === 'ascend' ? 'asc' : 'desc';
      onSortChange(sorter.field, sortDirection);
    }

    // Call original onChange if provided
    if (tableProps.onChange) {
      tableProps.onChange(pagination, filters, sorter);
    }
  }, [onPageChange, onSortChange, defaultPageSize, tableProps.onChange]);

  // Build pagination configuration
  const paginationProps = useMemo(() => {
    const defaultTotalFormatter = (total: number, range: [number, number]) => 
      `${range[0]}-${range[1]} of ${total} items`;

    return {
      current: currentPage,
      pageSize: defaultPageSize,
      total,
      showSizeChanger: paginationConfig?.showSizeChanger ?? true,
      showQuickJumper: paginationConfig?.showQuickJumper ?? true,
      pageSizeOptions,
      showTotal: paginationConfig?.showTotal !== false 
        ? (paginationConfig?.totalFormatter || defaultTotalFormatter)
        : undefined,
      ...paginationConfig,
    };
  }, [
    currentPage,
    defaultPageSize,
    total,
    pageSizeOptions,
    paginationConfig,
  ]);

  return (
    <Table
      {...tableProps}
      dataSource={data}
      loading={loading}
      pagination={paginationProps}
      onChange={handleTableChange}
    />
  );
}

/**
 * Paginated table with standard pagination (lower limits)
 * 标准分页表格（较低限制）
 */
export function StandardPaginatedTable<T extends Record<string, any>>(
  props: Omit<PaginatedTableProps<T>, 'useDataManagementPagination'>
) {
  return <PaginatedTable {...props} useDataManagementPagination={false} />;
}

/**
 * Paginated table with data management pagination (higher limits)
 * 数据管理分页表格（较高限制）
 */
export function DataManagementPaginatedTable<T extends Record<string, any>>(
  props: Omit<PaginatedTableProps<T>, 'useDataManagementPagination'>
) {
  return <PaginatedTable {...props} useDataManagementPagination={true} />;
}
