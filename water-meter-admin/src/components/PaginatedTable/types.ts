import { TableProps, PaginationProps } from 'antd';

/**
 * Pagination configuration for different contexts
 * 不同上下文的分页配置
 */
export const PaginationDefaults = {
  // Default page size for general listing
  defaultPageSize: 10,
  
  // Page size for data management interfaces (higher limit for admin users)
  dataManagementPageSize: 50,
  
  // Maximum page size for data management interfaces
  maxDataManagementPageSize: 1000,
  
  // Standard page size options for UI dropdowns
  standardPageSizeOptions: ['10', '20', '50', '100'],
  
  // Extended page size options for data management interfaces
  dataManagementPageSizeOptions: ['10', '20', '50', '100', '500', '1000'],
} as const;

/**
 * Base pagination parameters for API requests
 * API请求的基础分页参数
 */
export interface BasePaginationParams {
  page?: number;
  pageSize?: number;
  sortBy?: string;
  sortDirection?: 'asc' | 'desc';
}

/**
 * Generic paginated response from API
 * API返回的通用分页响应
 */
export interface PaginatedResponse<T> {
  data: T[];
  totalCount: number;
  page: number;
  pageSize: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

/**
 * Pagination configuration options
 * 分页配置选项
 */
export interface PaginationConfig {
  // Current page number
  current?: number;
  
  // Items per page
  pageSize?: number;
  
  // Total number of items
  total?: number;
  
  // Whether to show size changer
  showSizeChanger?: boolean;
  
  // Whether to show quick jumper
  showQuickJumper?: boolean;
  
  // Whether to show total count
  showTotal?: boolean;
  
  // Page size options
  pageSizeOptions?: string[];
  
  // Custom total display function
  totalFormatter?: (total: number, range: [number, number]) => string;
  
  // Pagination change handler
  onChange?: (page: number, pageSize: number) => void;
}

/**
 * Props for PaginatedTable component
 * PaginatedTable组件的属性
 */
export interface PaginatedTableProps<T = any> extends Omit<TableProps<T>, 'pagination'> {
  // Data source
  data: T[];
  
  // Total count for pagination
  total: number;
  
  // Current page
  currentPage?: number;
  
  // Current page size
  currentPageSize?: number;
  
  // Pagination configuration
  paginationConfig?: PaginationConfig;
  
  // Whether to use data management pagination (higher limits)
  useDataManagementPagination?: boolean;
  
  // Loading state
  loading?: boolean;
  
  // Page change handler
  onPageChange?: (page: number, pageSize: number) => void;
  
  // Sort change handler
  onSortChange?: (sortBy: string, sortDirection: 'asc' | 'desc') => void;
}

/**
 * Hook return type for pagination management
 * 分页管理Hook的返回类型
 */
export interface UsePaginationReturn {
  // Current pagination state
  pagination: {
    current: number;
    pageSize: number;
    total: number;
    showSizeChanger: boolean;
    showQuickJumper: boolean;
    showTotal: boolean;
    pageSizeOptions: string[];
  };
  
  // Update pagination state
  setPagination: (updates: Partial<PaginationConfig>) => void;
  
  // Get Ant Design pagination config
  getPaginationConfig: (total: number) => PaginationProps;
  
  // Reset to first page
  resetToFirstPage: () => void;
  
  // Handle page change
  handlePageChange: (page: number, pageSize?: number) => void;
}
