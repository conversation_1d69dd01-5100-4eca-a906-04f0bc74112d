# PaginatedTable Component

A reusable pagination component system that provides standardized pagination functionality across the application.

## Features

- **Standardized Pagination**: Consistent pagination behavior across all tables
- **Flexible Configuration**: Support for different pagination contexts (standard vs data management)
- **Higher Limits for Admin**: Data management interfaces support up to 1000 items per page
- **TypeScript Support**: Full type safety with comprehensive interfaces
- **Hook-based**: Provides both component and hook-based approaches

## Components

### PaginatedTable
Main component that wraps Ant Design Table with standardized pagination.

### StandardPaginatedTable
Pre-configured for standard pagination (10, 20, 50, 100 items per page).

### DataManagementPaginatedTable
Pre-configured for data management with higher limits (10, 20, 50, 100, 500, 1000 items per page).

## Hooks

### usePagination
Core hook for pagination state management.

### useStandardPagination
Hook pre-configured for standard pagination limits.

### useDataManagementPagination
Hook pre-configured for data management with higher limits.

## Usage Examples

### Basic Usage with Component

```tsx
import { DataManagementPaginatedTable } from '@/components/PaginatedTable';

function MyDataTable() {
  const [data, setData] = useState([]);
  const [total, setTotal] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(50);

  const handlePageChange = (page: number, size: number) => {
    setCurrentPage(page);
    setPageSize(size);
    // Fetch new data
  };

  return (
    <DataManagementPaginatedTable
      data={data}
      total={total}
      currentPage={currentPage}
      currentPageSize={pageSize}
      onPageChange={handlePageChange}
      columns={columns}
      rowKey="id"
    />
  );
}
```

### Usage with Hook

```tsx
import { useDataManagementPagination } from '@/components/PaginatedTable';
import { Table } from 'antd';

function MyTable() {
  const [data, setData] = useState([]);
  const [total, setTotal] = useState(0);
  
  const pagination = useDataManagementPagination({
    onChange: (page, pageSize) => {
      // Fetch new data
      fetchData(page, pageSize);
    }
  });

  return (
    <Table
      dataSource={data}
      columns={columns}
      pagination={pagination.getPaginationConfig(total)}
      rowKey="id"
    />
  );
}
```

### Advanced Configuration

```tsx
import { PaginatedTable } from '@/components/PaginatedTable';

function AdvancedTable() {
  return (
    <PaginatedTable
      data={data}
      total={total}
      currentPage={currentPage}
      currentPageSize={pageSize}
      useDataManagementPagination={true}
      paginationConfig={{
        showSizeChanger: true,
        showQuickJumper: true,
        showTotal: true,
        pageSizeOptions: ['10', '50', '100', '500', '1000'],
        totalFormatter: (total, range) => 
          `Showing ${range[0]}-${range[1]} of ${total} records`
      }}
      onPageChange={handlePageChange}
      onSortChange={handleSortChange}
      columns={columns}
      rowKey="id"
    />
  );
}
```

## Configuration Options

### PaginationDefaults

```typescript
export const PaginationDefaults = {
  defaultPageSize: 10,
  dataManagementPageSize: 50,
  maxDataManagementPageSize: 1000,
  standardPageSizeOptions: ['10', '20', '50', '100'],
  dataManagementPageSizeOptions: ['10', '20', '50', '100', '500', '1000'],
};
```

### PaginationConfig Interface

```typescript
interface PaginationConfig {
  current?: number;
  pageSize?: number;
  total?: number;
  showSizeChanger?: boolean;
  showQuickJumper?: boolean;
  showTotal?: boolean;
  pageSizeOptions?: string[];
  totalFormatter?: (total: number, range: [number, number]) => string;
  onChange?: (page: number, pageSize: number) => void;
}
```

## Backend Integration

The component works seamlessly with the backend pagination system:

```typescript
// Backend response format
interface PaginatedResponse<T> {
  data: T[];
  totalCount: number;
  page: number;
  pageSize: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

// Frontend usage
const response = await api.get('/water-meters', { params: searchParams });
setData(response.data);
setTotal(response.totalCount);
```

## Migration Guide

### From Existing Tables

1. Replace manual pagination configuration with PaginatedTable component
2. Update API calls to use standardized pagination parameters
3. Remove custom pagination logic and use provided hooks

### Before (Manual)
```tsx
const [pagination, setPagination] = useState({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  // ... manual configuration
});
```

### After (With Component)
```tsx
import { DataManagementPaginatedTable } from '@/components/PaginatedTable';

<DataManagementPaginatedTable
  data={data}
  total={total}
  currentPage={currentPage}
  currentPageSize={pageSize}
  onPageChange={handlePageChange}
  // ... other props
/>
```
