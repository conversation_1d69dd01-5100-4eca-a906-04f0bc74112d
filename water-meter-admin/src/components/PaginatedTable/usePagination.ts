import { useState, useCallback, useMemo } from 'react';
import { PaginationProps } from 'antd';
import { PaginationConfig, PaginationDefaults, UsePaginationReturn } from './types';

/**
 * Custom hook for pagination management
 * 分页管理的自定义Hook
 */
export function usePagination(initialConfig?: Partial<PaginationConfig>): UsePaginationReturn {
  const [paginationState, setPaginationState] = useState<PaginationConfig>({
    current: 1,
    pageSize: PaginationDefaults.dataManagementPageSize,
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: true,
    pageSizeOptions: PaginationDefaults.dataManagementPageSizeOptions,
    ...initialConfig,
  });

  const setPagination = useCallback((updates: Partial<PaginationConfig>) => {
    setPaginationState(prev => ({ ...prev, ...updates }));
  }, []);

  const resetToFirstPage = useCallback(() => {
    setPaginationState(prev => ({ ...prev, current: 1 }));
  }, []);

  const handlePageChange = useCallback((page: number, pageSize?: number) => {
    setPaginationState(prev => ({
      ...prev,
      current: page,
      pageSize: pageSize || prev.pageSize || PaginationDefaults.dataManagementPageSize,
    }));
    
    // Call the onChange handler if provided
    if (paginationState.onChange) {
      paginationState.onChange(page, pageSize || paginationState.pageSize || PaginationDefaults.dataManagementPageSize);
    }
  }, [paginationState.onChange, paginationState.pageSize]);

  const getPaginationConfig = useCallback((total: number): PaginationProps => {
    const defaultTotalFormatter = (total: number, range: [number, number]) => 
      `${range[0]}-${range[1]} of ${total} items`;

    return {
      current: paginationState.current || 1,
      pageSize: paginationState.pageSize || PaginationDefaults.dataManagementPageSize,
      total,
      showSizeChanger: paginationState.showSizeChanger ?? true,
      showQuickJumper: paginationState.showQuickJumper ?? true,
      pageSizeOptions: paginationState.pageSizeOptions || PaginationDefaults.dataManagementPageSizeOptions,
      showTotal: paginationState.showTotal 
        ? (paginationState.totalFormatter || defaultTotalFormatter)
        : undefined,
      onChange: handlePageChange,
      onShowSizeChange: handlePageChange,
    };
  }, [paginationState, handlePageChange]);

  const pagination = useMemo(() => ({
    current: paginationState.current || 1,
    pageSize: paginationState.pageSize || PaginationDefaults.dataManagementPageSize,
    total: 0, // This will be set by the component using this hook
    showSizeChanger: paginationState.showSizeChanger ?? true,
    showQuickJumper: paginationState.showQuickJumper ?? true,
    showTotal: paginationState.showTotal ?? true,
    pageSizeOptions: paginationState.pageSizeOptions || PaginationDefaults.dataManagementPageSizeOptions,
  }), [paginationState]);

  return {
    pagination,
    setPagination,
    getPaginationConfig,
    resetToFirstPage,
    handlePageChange,
  };
}

/**
 * Hook for standard pagination (lower limits)
 * 标准分页Hook（较低限制）
 */
export function useStandardPagination(initialConfig?: Partial<PaginationConfig>): UsePaginationReturn {
  return usePagination({
    pageSize: PaginationDefaults.defaultPageSize,
    pageSizeOptions: PaginationDefaults.standardPageSizeOptions,
    ...initialConfig,
  });
}

/**
 * Hook for data management pagination (higher limits)
 * 数据管理分页Hook（较高限制）
 */
export function useDataManagementPagination(initialConfig?: Partial<PaginationConfig>): UsePaginationReturn {
  return usePagination({
    pageSize: PaginationDefaults.dataManagementPageSize,
    pageSizeOptions: PaginationDefaults.dataManagementPageSizeOptions,
    ...initialConfig,
  });
}
