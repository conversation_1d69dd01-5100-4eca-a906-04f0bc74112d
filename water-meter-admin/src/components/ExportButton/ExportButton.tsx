import React from 'react';
import { Button } from 'antd';
import { ExportOutlined } from '@ant-design/icons';
import { ExportButtonProps } from './types';
import { useExport } from './useExport';

/**
 * Simple export button component
 * 简单的导出按钮组件
 */
export function ExportButton({
  config,
  searchParams,
  onBeforeExport,
  onExportSuccess,
  onExportError,
  buttonText = 'Export',
  icon = <ExportOutlined />,
  ...buttonProps
}: ExportButtonProps) {
  const { loading, exportWithValidation } = useExport();

  const handleExport = async () => {
    try {
      const result = await exportWithValidation(
        config,
        config.defaultFormat,
        searchParams,
        undefined,
        onBeforeExport
      );

      if (result.success && onExportSuccess) {
        onExportSuccess(result.filename!, result.format!);
      }
    } catch (error) {
      if (onExportError) {
        onExportError(error);
      }
    }
  };

  return (
    <Button
      icon={icon}
      loading={loading}
      onClick={handleExport}
      {...buttonProps}
    >
      {buttonText}
    </Button>
  );
}
