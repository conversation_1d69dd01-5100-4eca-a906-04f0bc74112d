import { useState, useCallback } from 'react';
import { message } from 'antd';
import dayjs from 'dayjs';
import { api } from '@/services/api';
import { ExportConfig, ExportFormat, FileExtensions } from './types';

/**
 * Custom hook for handling export functionality
 */
export function useExport() {
  const [loading, setLoading] = useState(false);

  /**
   * Download file from blob
   */
  const downloadFile = useCallback((blob: Blob, filename: string, format: ExportFormat) => {
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `${filename}.${FileExtensions[format]}`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  }, []);

  /**
   * Generate filename with timestamp
   */
  const generateFilename = useCallback((baseFilename: string) => {
    return `${baseFilename}_${dayjs().format('YYYY-MM-DD_HHmm')}`;
  }, []);

  /**
   * Build export URL with parameters
   */
  const buildExportUrl = useCallback((config: ExportConfig, format: ExportFormat, searchParams?: Record<string, any>) => {
    const { endpoint } = config;
    
    // Handle different endpoint patterns
    let url = endpoint;
    if (endpoint.includes('/export') && !endpoint.includes(`/${format}`)) {
      // Pattern: /api/resource/export -> /api/resource/export/excel
      url = `${endpoint}/${format}`;
    } else if (!endpoint.includes('/export')) {
      // Pattern: /api/resource -> /api/resource/export/excel
      url = `${endpoint}/export/${format}`;
    }
    
    // Add query parameters
    const params = new URLSearchParams();
    
    // Add search parameters
    if (searchParams) {
      Object.entries(searchParams).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          if (Array.isArray(value)) {
            value.forEach(v => params.append(key, String(v)));
          } else {
            params.append(key, String(value));
          }
        }
      });
    }
    
    // Add config query parameters
    if (config.queryParams) {
      Object.entries(config.queryParams).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          params.append(key, String(value));
        }
      });
    }
    
    const queryString = params.toString();
    return queryString ? `${url}?${queryString}` : url;
  }, []);

  /**
   * Execute export
   */
  const exportData = useCallback(async (
    config: ExportConfig,
    format: ExportFormat = config.defaultFormat || 'excel',
    searchParams?: Record<string, any>,
    customFilename?: string
  ) => {
    try {
      setLoading(true);
      
      const url = buildExportUrl(config, format, searchParams);
      
      const response = await api.get(url, {
        responseType: 'blob',
        headers: config.headers,
      });
      
      const filename = customFilename || generateFilename(config.defaultFilename || 'export');
      downloadFile(response.data, filename, format);
      
      message.success(config.successMessage || 'Export completed successfully');
      
      return { success: true, filename, format };
    } catch (error) {
      console.error('Export error:', error);
      message.error(config.errorMessage || 'Export failed');
      throw error;
    } finally {
      setLoading(false);
    }
  }, [buildExportUrl, downloadFile, generateFilename]);

  /**
   * Export with validation
   */
  const exportWithValidation = useCallback(async (
    config: ExportConfig,
    format: ExportFormat = config.defaultFormat || 'excel',
    searchParams?: Record<string, any>,
    customFilename?: string,
    onBeforeExport?: () => Promise<boolean> | boolean
  ) => {
    try {
      // Run pre-export validation
      if (onBeforeExport) {
        const canProceed = await onBeforeExport();
        if (!canProceed) {
          return { success: false, cancelled: true };
        }
      }
      
      return await exportData(config, format, searchParams, customFilename);
    } catch (error) {
      return { success: false, error };
    }
  }, [exportData]);

  return {
    loading,
    exportData,
    exportWithValidation,
    downloadFile,
    generateFilename,
    buildExportUrl,
  };
}
