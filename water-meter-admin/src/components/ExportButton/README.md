# ExportButton Component

A reusable export component system that provides standardized export functionality across the application.

## Features

- **Multiple Export Formats**: Support for Excel, CSV, and PDF exports
- **Flexible Configuration**: Easy to configure for different data types
- **Search Parameter Integration**: Automatically includes current filters and search parameters
- **Custom Filename Support**: Allow users to specify custom filenames
- **Loading States**: Built-in loading indicators
- **Error Handling**: Comprehensive error handling with user feedback
- **TypeScript Support**: Full type safety

## Components

### ExportButton
Simple export button for quick exports with default settings.

### ExportDropdown
Advanced export dropdown with format selection and filename customization.

## Usage Examples

### Basic Export Button

```tsx
import { ExportButton, ExportConfigs } from '@/components/ExportButton';

function WaterMetersPage() {
  const [searchParams, setSearchParams] = useState({
    page: 1,
    pageSize: 50,
    status: 'Active'
  });

  return (
    <ExportButton
      config={ExportConfigs.WaterMeters}
      searchParams={searchParams}
      buttonText="Export Water Meters"
    />
  );
}
```

### Advanced Export Dropdown

```tsx
import { ExportDropdown, ExportConfigs } from '@/components/ExportButton';

function BaselinePage() {
  const [searchParams, setSearchParams] = useState({
    dateFrom: '2024-01-01',
    dateTo: '2024-12-31'
  });

  const handleExportSuccess = (filename: string, format: string) => {
    console.log(`Exported ${filename} as ${format}`);
  };

  return (
    <ExportDropdown
      config={ExportConfigs.Baselines}
      searchParams={searchParams}
      onExportSuccess={handleExportSuccess}
      buttonText="Export Baselines"
    />
  );
}
```

### Custom Export Configuration

```tsx
import { ExportButton } from '@/components/ExportButton';

function CustomExportPage() {
  const customConfig = {
    endpoint: '/api/custom-data/export',
    defaultFilename: 'custom_data',
    supportedFormats: ['excel', 'csv'],
    defaultFormat: 'excel',
    successMessage: 'Custom data exported successfully',
    errorMessage: 'Failed to export custom data',
  };

  return (
    <ExportButton
      config={customConfig}
      searchParams={{ customParam: 'value' }}
    />
  );
}
```

### With Validation

```tsx
import { ExportDropdown } from '@/components/ExportButton';

function ValidatedExportPage() {
  const handleBeforeExport = async () => {
    // Perform validation before export
    const hasData = await checkIfDataExists();
    if (!hasData) {
      message.warning('No data available for export');
      return false;
    }
    return true;
  };

  return (
    <ExportDropdown
      config={ExportConfigs.WaterMeters}
      onBeforeExport={handleBeforeExport}
    />
  );
}
```

## Configuration Options

### ExportConfig Interface

```typescript
interface ExportConfig {
  endpoint: string;                    // API endpoint for export
  defaultFilename?: string;            // Default filename (without extension)
  supportedFormats?: ExportFormat[];   // Supported export formats
  defaultFormat?: ExportFormat;        // Default export format
  queryParams?: Record<string, any>;   // Additional query parameters
  headers?: Record<string, string>;    // Custom headers
  successMessage?: string;             // Success message
  errorMessage?: string;               // Error message
}
```

### Predefined Configurations

The component comes with predefined configurations for common data types:

- `ExportConfigs.WaterMeters`
- `ExportConfigs.Baselines`
- `ExportConfigs.WorkPackages`
- `ExportConfigs.Tasks`

## Backend Integration

The component expects the backend to provide export endpoints in the following format:

```
GET /api/{resource}/export/{format}?param1=value1&param2=value2
```

For example:
- `GET /api/water-meters/export/excel?page=1&pageSize=50&status=Active`
- `GET /api/baseline/export/csv?dateFrom=2024-01-01&dateTo=2024-12-31`

The backend should return the file as a blob with appropriate headers:

```csharp
[HttpGet("export/{format}")]
public async Task<ActionResult> ExportData(string format, [FromQuery] SearchDto searchDto)
{
    var data = await GetExportData(searchDto);
    var fileBytes = await GenerateExportFile(data, format);
    var fileName = $"export_{DateTime.Now:yyyyMMdd_HHmmss}.{GetFileExtension(format)}";
    
    return File(fileBytes, GetMimeType(format), fileName);
}
```

## Customization

### Custom Export Hook

```tsx
import { useExport } from '@/components/ExportButton';

function CustomExportComponent() {
  const { loading, exportData } = useExport();

  const handleCustomExport = async () => {
    const config = {
      endpoint: '/api/custom/export',
      defaultFilename: 'custom_export'
    };
    
    await exportData(config, 'excel', { customParam: 'value' });
  };

  return (
    <Button loading={loading} onClick={handleCustomExport}>
      Custom Export
    </Button>
  );
}
```

### Custom Button Styling

```tsx
<ExportButton
  config={ExportConfigs.WaterMeters}
  type="primary"
  size="large"
  style={{ backgroundColor: '#52c41a' }}
  buttonText="Download Excel"
/>
```

## Error Handling

The component provides comprehensive error handling:

1. **Network Errors**: Displays user-friendly error messages
2. **Validation Errors**: Prevents export if validation fails
3. **File Download Errors**: Handles browser download issues
4. **Custom Error Handlers**: Allows custom error handling logic

## Migration Guide

### From Manual Export Implementation

Before:
```tsx
const handleExport = async () => {
  try {
    const blob = await waterMeterService.exportWaterMeters('excel');
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `water_meters_${dayjs().format('YYYY-MM-DD')}.xlsx`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
    message.success('Export successful');
  } catch (error) {
    message.error('Export failed');
  }
};
```

After:
```tsx
<ExportButton
  config={ExportConfigs.WaterMeters}
  searchParams={searchParams}
/>
```
