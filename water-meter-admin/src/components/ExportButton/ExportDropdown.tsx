import React, { useState } from 'react';
import { Button, Dropdown, Modal, Input, Select, Space, Form } from 'antd';
import { ExportOutlined, DownOutlined } from '@ant-design/icons';
import type { MenuProps } from 'antd';
import { ExportDropdownProps, ExportFormat, FileExtensions } from './types';
import { useExport } from './useExport';

/**
 * Advanced export dropdown with format selection and filename customization
 * 高级导出下拉菜单，支持格式选择和文件名自定义
 */
export function ExportDropdown({
  config,
  searchParams,
  onBeforeExport,
  onExportSuccess,
  onExportError,
  buttonProps,
  buttonText = 'Export',
}: ExportDropdownProps) {
  const { loading, exportWithValidation, generateFilename } = useExport();
  const [modalVisible, setModalVisible] = useState(false);
  const [selectedFormat, setSelectedFormat] = useState<ExportFormat>(
    config.defaultFormat || 'excel'
  );
  const [customFilename, setCustomFilename] = useState('');
  const [form] = Form.useForm();

  const supportedFormats = config.supportedFormats || ['excel', 'csv'];

  // Quick export without modal
  const handleQuickExport = async (format: ExportFormat) => {
    try {
      const result = await exportWithValidation(
        config,
        format,
        searchParams,
        undefined,
        onBeforeExport
      );

      if (result.success && onExportSuccess) {
        onExportSuccess(result.filename!, result.format!);
      }
    } catch (error) {
      if (onExportError) {
        onExportError(error);
      }
    }
  };

  // Custom export with modal
  const handleCustomExport = () => {
    const defaultFilename = generateFilename(config.defaultFilename || 'export');
    setCustomFilename(defaultFilename);
    setSelectedFormat(config.defaultFormat || 'excel');
    form.setFieldsValue({
      filename: defaultFilename,
      format: config.defaultFormat || 'excel',
    });
    setModalVisible(true);
  };

  // Execute custom export
  const handleModalExport = async () => {
    try {
      const values = await form.validateFields();
      
      const result = await exportWithValidation(
        config,
        values.format,
        searchParams,
        values.filename,
        onBeforeExport
      );

      if (result.success) {
        setModalVisible(false);
        if (onExportSuccess) {
          onExportSuccess(result.filename!, result.format!);
        }
      }
    } catch (error) {
      if (onExportError) {
        onExportError(error);
      }
    }
  };

  // Build dropdown menu items
  const menuItems: MenuProps['items'] = [
    // Quick export options
    ...supportedFormats.map((format) => ({
      key: `quick-${format}`,
      label: `Export as ${format.toUpperCase()}`,
      onClick: () => handleQuickExport(format),
    })),
    
    // Separator
    { type: 'divider' as const },
    
    // Custom export option
    {
      key: 'custom',
      label: 'Custom Export...',
      onClick: handleCustomExport,
    },
  ];

  return (
    <>
      <Dropdown
        menu={{ items: menuItems }}
        trigger={['click']}
        disabled={loading}
      >
        <Button
          icon={<ExportOutlined />}
          loading={loading}
          {...buttonProps}
        >
          {buttonText} <DownOutlined />
        </Button>
      </Dropdown>

      <Modal
        title="Custom Export"
        open={modalVisible}
        onOk={handleModalExport}
        onCancel={() => setModalVisible(false)}
        confirmLoading={loading}
        okText="Export"
        cancelText="Cancel"
      >
        <Form
          form={form}
          layout="vertical"
          initialValues={{
            filename: customFilename,
            format: selectedFormat,
          }}
        >
          <Form.Item
            label="Filename"
            name="filename"
            rules={[
              { required: true, message: 'Please enter a filename' },
              { 
                pattern: /^[^<>:"/\\|?*]+$/, 
                message: 'Filename contains invalid characters' 
              },
            ]}
          >
            <Input 
              placeholder="Enter filename (without extension)"
              suffix={`.${FileExtensions[selectedFormat]}`}
            />
          </Form.Item>

          <Form.Item
            label="Format"
            name="format"
            rules={[{ required: true, message: 'Please select a format' }]}
          >
            <Select
              value={selectedFormat}
              onChange={(value) => {
                setSelectedFormat(value);
                // Update filename suffix in real-time
                const currentFilename = form.getFieldValue('filename');
                form.setFieldsValue({ filename: currentFilename });
              }}
            >
              {supportedFormats.map((format) => (
                <Select.Option key={format} value={format}>
                  {format.toUpperCase()} (.{FileExtensions[format]})
                </Select.Option>
              ))}
            </Select>
          </Form.Item>

          {searchParams && Object.keys(searchParams).length > 0 && (
            <Form.Item label="Export will include current filters and search parameters" />
          )}
        </Form>
      </Modal>
    </>
  );
}
