import { ButtonProps } from 'antd';

/**
 * Export format options
 */
export type ExportFormat = 'excel' | 'csv' | 'pdf';

/**
 * Export configuration for different data types
 */
export interface ExportConfig {
  // API endpoint for export
  endpoint: string;
  
  // Default filename (without extension)
  defaultFilename?: string;
  
  // Supported formats
  supportedFormats?: ExportFormat[];
  
  // Default format
  defaultFormat?: ExportFormat;
  
  // Additional query parameters
  queryParams?: Record<string, any>;
  
  // Custom headers
  headers?: Record<string, string>;
  
  // Success message
  successMessage?: string;
  
  // Error message
  errorMessage?: string;
}

/**
 * Props for ExportButton component
 */
export interface ExportButtonProps extends Omit<ButtonProps, 'onClick'> {
  // Export configuration
  config: ExportConfig;
  
  // Current search/filter parameters to include in export
  searchParams?: Record<string, any>;
  
  // Custom onClick handler (called before export)
  onBeforeExport?: () => Promise<boolean> | boolean;
  
  // Custom success handler
  onExportSuccess?: (filename: string, format: ExportFormat) => void;
  
  // Custom error handler
  onExportError?: (error: any) => void;
  
  // Whether to show format selection dropdown
  showFormatSelector?: boolean;
  
  // Whether to show filename input
  showFilenameInput?: boolean;
  
  // Custom button text
  buttonText?: string;
}

/**
 * Props for ExportDropdown component
 */
export interface ExportDropdownProps {
  // Export configuration
  config: ExportConfig;
  
  // Current search/filter parameters
  searchParams?: Record<string, any>;
  
  // Custom handlers
  onBeforeExport?: () => Promise<boolean> | boolean;
  onExportSuccess?: (filename: string, format: ExportFormat) => void;
  onExportError?: (error: any) => void;
  
  // Button props
  buttonProps?: ButtonProps;
  
  // Custom button text
  buttonText?: string;
}

/**
 * Predefined export configurations for common data types
 */
export const ExportConfigs = {
  WaterMeters: {
    endpoint: '/water-meters/export',
    defaultFilename: 'water_meters',
    supportedFormats: ['excel', 'csv'] as ExportFormat[],
    defaultFormat: 'excel' as ExportFormat,
    successMessage: 'Water meters exported successfully',
    errorMessage: 'Failed to export water meters',
  },
  
  Baselines: {
    endpoint: '/baseline/export',
    defaultFilename: 'baseline_records',
    supportedFormats: ['excel', 'csv'] as ExportFormat[],
    defaultFormat: 'excel' as ExportFormat,
    successMessage: 'Baseline records exported successfully',
    errorMessage: 'Failed to export baseline records',
  },
  
  WorkPackages: {
    endpoint: '/work-packages/export/excel',
    defaultFilename: 'work_packages',
    supportedFormats: ['excel'] as ExportFormat[],
    defaultFormat: 'excel' as ExportFormat,
    successMessage: 'Work packages exported successfully',
    errorMessage: 'Failed to export work packages',
  },
  
  Tasks: {
    endpoint: '/tasks/export',
    defaultFilename: 'tasks',
    supportedFormats: ['excel'] as ExportFormat[],
    defaultFormat: 'excel' as ExportFormat,
    successMessage: 'Tasks exported successfully',
    errorMessage: 'Failed to export tasks',
  },
} as const;

/**
 * File extension mapping
 */
export const FileExtensions: Record<ExportFormat, string> = {
  excel: 'xlsx',
  csv: 'csv',
  pdf: 'pdf',
};

/**
 * MIME type mapping
 */
export const MimeTypes: Record<ExportFormat, string> = {
  excel: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  csv: 'text/csv',
  pdf: 'application/pdf',
};
