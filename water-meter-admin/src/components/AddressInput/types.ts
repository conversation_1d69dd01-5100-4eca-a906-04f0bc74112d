export interface AddressComponents {
  roadNumber?: number;
  roadName?: string;
  township?: string;
  subArea?: string;
  postalCode?: string;
  country?: string;
}

export interface AddressData {
  // 用户看到的完整地址
  location: string;
  
  // 解析出的结构化字段（如果成功解析）
  components?: AddressComponents;
  
  // GPS坐标（如果获取到）
  latitude?: number;
  longitude?: number;
  
  // 标识是否成功解析
  isParsed: boolean;
  
  // Google Place ID for reference
  placeId?: string;
}

export interface AddressSuggestion {
  description: string;
  placeId: string;
  mainText: string;
  secondaryText: string;
}

export interface AddressParseResult {
  success: boolean;
  errorMessage?: string;
  formattedAddress: string;
  components?: AddressComponents;
  latitude?: number;
  longitude?: number;
  placeId?: string;
}

export interface AddressInputProps {
  value?: string;
  onChange?: (addressData: AddressData) => void;
  placeholder?: string;
  disabled?: boolean;
  style?: React.CSSProperties;
  className?: string;
  
  // 是否显示调试信息（显示解析出的结构化字段）
  showDebugInfo?: boolean;
  
  // 自定义样式
  size?: 'small' | 'middle' | 'large';
  
  // 是否必填
  required?: boolean;
  
  // 错误状态
  status?: 'error' | 'warning';
}
