import React, { useState, useEffect, useCallback } from 'react';
import { AutoComplete, Input, Spin, Tag, Space, Collapse } from 'antd';
import { EnvironmentOutlined, LoadingOutlined, CheckCircleOutlined, ExclamationCircleOutlined } from '@ant-design/icons';
import { debounce } from 'lodash';
import { addressService } from '@/services/address.service';
import { AddressInputProps, AddressSuggestion, AddressData } from './types';

const { Panel } = Collapse;

export function AddressInput({
  value = '',
  onChange,
  placeholder = 'Enter address...',
  disabled = false,
  style,
  className,
  showDebugInfo = false,
  size = 'middle',
  required = false,
  status,
}: AddressInputProps) {
  const [inputValue, setInputValue] = useState(value);
  const [suggestions, setSuggestions] = useState<AddressSuggestion[]>([]);
  const [loading, setLoading] = useState(false);
  const [parsing, setParsing] = useState(false);
  const [lastParsedAddress, setLastParsedAddress] = useState<AddressData | null>(null);

  // Update input value when prop changes
  useEffect(() => {
    setInputValue(value);
  }, [value]);

  // Debounced function to get suggestions
  const debouncedGetSuggestions = useCallback(
    debounce(async (searchText: string) => {
      if (!searchText || searchText.length < 3) {
        setSuggestions([]);
        return;
      }

      setLoading(true);
      try {
        const results = await addressService.getAddressSuggestions(searchText);
        setSuggestions(results);
      } catch (error) {
        console.error('Error getting suggestions:', error);
        setSuggestions([]);
      } finally {
        setLoading(false);
      }
    }, 300),
    []
  );

  // Parse address and extract components
  const parseAddress = useCallback(async (address: string) => {
    if (!address.trim()) {
      return;
    }

    setParsing(true);
    try {
      const result = await addressService.parseAddress(address);
      
      const addressData: AddressData = {
        location: result.formattedAddress,
        isParsed: result.success,
        components: result.components,
        latitude: result.latitude,
        longitude: result.longitude,
        placeId: result.placeId,
      };

      setLastParsedAddress(addressData);
      
      if (onChange) {
        onChange(addressData);
      }
    } catch (error) {
      console.error('Error parsing address:', error);
      
      // Fallback: return original input without parsing
      const fallbackData: AddressData = {
        location: address,
        isParsed: false,
      };
      
      setLastParsedAddress(fallbackData);
      
      if (onChange) {
        onChange(fallbackData);
      }
    } finally {
      setParsing(false);
    }
  }, [onChange]);

  // Handle input change
  const handleInputChange = (searchText: string) => {
    setInputValue(searchText);
    debouncedGetSuggestions(searchText);
  };

  // Handle suggestion selection
  const handleSelect = (selectedValue: string, option: any) => {
    setInputValue(selectedValue);
    setSuggestions([]);
    
    // Parse the selected address
    parseAddress(selectedValue);
  };

  // Handle manual input (when user types and presses enter or loses focus)
  const handleManualInput = () => {
    if (inputValue.trim() && inputValue !== lastParsedAddress?.location) {
      parseAddress(inputValue.trim());
    }
  };

  // Prepare options for AutoComplete
  const options = suggestions.map((suggestion) => ({
    value: suggestion.description,
    label: (
      <div style={{ display: 'flex', alignItems: 'center' }}>
        <EnvironmentOutlined style={{ marginRight: 8, color: '#1890ff' }} />
        <div>
          <div style={{ fontWeight: 500 }}>{suggestion.mainText}</div>
          {suggestion.secondaryText && (
            <div style={{ fontSize: '12px', color: '#666' }}>
              {suggestion.secondaryText}
            </div>
          )}
        </div>
      </div>
    ),
    key: suggestion.placeId,
  }));

  // Render status icon
  const renderStatusIcon = () => {
    if (parsing) {
      return <LoadingOutlined style={{ color: '#1890ff' }} />;
    }
    
    if (lastParsedAddress) {
      if (lastParsedAddress.isParsed) {
        return <CheckCircleOutlined style={{ color: '#52c41a' }} />;
      } else {
        return <ExclamationCircleOutlined style={{ color: '#faad14' }} />;
      }
    }
    
    return <EnvironmentOutlined style={{ color: '#d9d9d9' }} />;
  };

  return (
    <div style={style} className={className}>
      <AutoComplete
        value={inputValue}
        options={options}
        onSearch={handleInputChange}
        onSelect={handleSelect}
        onBlur={handleManualInput}
        placeholder={placeholder}
        disabled={disabled}
        size={size}
        status={status}
        style={{ width: '100%' }}
        notFoundContent={loading ? <Spin size="small" /> : null}
        suffixIcon={renderStatusIcon()}
      >
        <Input
          onPressEnter={handleManualInput}
          required={required}
        />
      </AutoComplete>

      {/* Debug Information */}
      {showDebugInfo && lastParsedAddress && (
        <div style={{ marginTop: 8 }}>
          <Collapse size="small">
            <Panel 
              header={
                <Space>
                  <span>Address Details</span>
                  <Tag color={lastParsedAddress.isParsed ? 'green' : 'orange'}>
                    {lastParsedAddress.isParsed ? 'Parsed' : 'Manual'}
                  </Tag>
                </Space>
              } 
              key="1"
            >
              <div style={{ fontSize: '12px' }}>
                <div><strong>Location:</strong> {lastParsedAddress.location}</div>
                
                {lastParsedAddress.components && (
                  <>
                    <div><strong>Road Number:</strong> {lastParsedAddress.components.roadNumber || 'N/A'}</div>
                    <div><strong>Road Name:</strong> {lastParsedAddress.components.roadName || 'N/A'}</div>
                    <div><strong>Township:</strong> {lastParsedAddress.components.township || 'N/A'}</div>
                    <div><strong>Sub Area:</strong> {lastParsedAddress.components.subArea || 'N/A'}</div>
                  </>
                )}
                
                {lastParsedAddress.latitude && lastParsedAddress.longitude && (
                  <div><strong>GPS:</strong> {lastParsedAddress.latitude.toFixed(6)}, {lastParsedAddress.longitude.toFixed(6)}</div>
                )}
                
                {lastParsedAddress.placeId && (
                  <div><strong>Place ID:</strong> {lastParsedAddress.placeId}</div>
                )}
              </div>
            </Panel>
          </Collapse>
        </div>
      )}
    </div>
  );
}
