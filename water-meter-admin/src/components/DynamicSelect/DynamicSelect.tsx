import React, { useState, useEffect, useCallback, useMemo, useRef } from 'react';
import { Select, Spin, message, Tooltip } from 'antd';
import { debounce } from 'lodash';
import { dynamicSelectService } from '@/services/dynamic-select.service';
import { DynamicSelectProps, DynamicOptionDto } from './types';

const { Option } = Select;

export const DynamicSelect: React.FC<DynamicSelectProps> = ({
  strategy,
  dataSource,
  params = {},
  valueField = 'value',
  labelField = 'label',
  mode = 'single',
  value,
  defaultValue,
  placeholder,
  disabled = false,
  allowClear = true,
  showSearch = true,
  onChange,
  onSelect,
  onDeselect,
  onSearch,
  style,
  className,
  size = 'middle',
  loading: externalLoading = false,
  notFoundContent,
  dropdownRender,
  debug = false,
}) => {
  const [options, setOptions] = useState<DynamicOptionDto[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Memoize params to prevent unnecessary re-renders
  const memoizedParams = useMemo(() => {
    // Return empty object if params is undefined/null
    const finalParams = params || {};
    // Create a stable reference by stringifying and parsing
    return JSON.parse(JSON.stringify(finalParams));
  }, [JSON.stringify(params)]);

  // Ref to prevent duplicate API calls
  const loadingRef = useRef(false);



  // Debug logging
  const debugLog = useCallback((message: string, data?: any) => {
    if (debug) {
      console.log(`[DynamicSelect] ${message}`, data);
    }
  }, [debug]);

  // Load options from backend
  const loadOptions = useCallback(async (searchValue?: string) => {
    // Prevent duplicate calls
    if (loadingRef.current) {
      debugLog('Skipping duplicate API call');
      return;
    }

    try {
      loadingRef.current = true;
      setLoading(true);
      setError(null);
      
      debugLog('Loading options', { strategy, dataSource, params: memoizedParams, searchValue });

      // Add search value to params if provided
      const requestParams = searchValue
        ? { ...memoizedParams, search: searchValue }
        : memoizedParams;

      const response = await dynamicSelectService.getOptions(strategy, dataSource, requestParams);
      
      if (response.success) {
        setOptions(response.options);
        debugLog('Options loaded successfully', response.options);
      } else {
        setError(response.errorMessage || 'Failed to load options');
        message.error(response.errorMessage || 'Failed to load options');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      setError(errorMessage);
      message.error(`Failed to load options: ${errorMessage}`);
      debugLog('Error loading options', error);
    } finally {
      loadingRef.current = false;
      setLoading(false);
    }
  }, [strategy, dataSource, memoizedParams]);

  // Debounced search function
  const debouncedSearch = useCallback(
    debounce((searchValue: string) => {
      if (searchValue) {
        loadOptions(searchValue);
      }
    }, 300),
    [loadOptions]
  );

  // Load initial options
  useEffect(() => {
    loadOptions();
  }, [loadOptions]);

  // Handle search
  const handleSearch = useCallback((searchValue: string) => {
    debugLog('Search triggered', searchValue);
    
    if (onSearch) {
      onSearch(searchValue);
    }
    
    if (searchValue) {
      debouncedSearch(searchValue);
    } else {
      // Reset to original options when search is cleared
      loadOptions();
    }
  }, [onSearch, debouncedSearch, loadOptions, debugLog]);

  // Smart value comparison - handles both string and non-string values
  const normalizeValue = useCallback((val: any): any => {
    // If value is null or undefined, return as is
    if (val == null) return val;

    // For arrays (multiple mode), normalize each item
    if (Array.isArray(val)) {
      return val.map(normalizeValue);
    }

    return val;
  }, []);

  // Check if two values are equal, handling type differences
  const valuesEqual = useCallback((val1: any, val2: any): boolean => {
    // Handle null/undefined
    if (val1 == null && val2 == null) return true;
    if (val1 == null || val2 == null) return false;

    // Handle arrays
    if (Array.isArray(val1) && Array.isArray(val2)) {
      if (val1.length !== val2.length) return false;
      return val1.every((item, index) => valuesEqual(item, val2[index]));
    }

    // Handle primitive values - convert both to string for comparison
    return String(val1) === String(val2);
  }, []);

  // Handle change
  const handleChange = useCallback((selectedValue: any, option: any) => {
    debugLog('Value changed', { selectedValue, option });

    if (onChange) {
      // Find the original value from options to maintain type consistency
      if (selectedValue != null && !Array.isArray(selectedValue)) {
        const matchedOption = options.find(opt => valuesEqual(opt.value, selectedValue));
        if (matchedOption) {
          onChange(matchedOption.value, option);
          return;
        }
      } else if (Array.isArray(selectedValue)) {
        const originalValues = selectedValue.map(val => {
          const matchedOption = options.find(opt => valuesEqual(opt.value, val));
          return matchedOption ? matchedOption.value : val;
        });
        onChange(originalValues, option);
        return;
      }

      onChange(selectedValue, option);
    }
  }, [onChange, debugLog, options, valuesEqual]);

  // Handle select
  const handleSelect = useCallback((selectedValue: any, option: any) => {
    debugLog('Option selected', { selectedValue, option });
    
    if (onSelect) {
      onSelect(selectedValue, option);
    }
  }, [onSelect, debugLog]);

  // Handle deselect
  const handleDeselect = useCallback((deselectedValue: any, option: any) => {
    debugLog('Option deselected', { deselectedValue, option });
    
    if (onDeselect) {
      onDeselect(deselectedValue, option);
    }
  }, [onDeselect, debugLog]);

  // Prepare select mode
  const selectMode = mode === 'multiple' ? 'multiple' : undefined;

  // Prepare not found content
  const finalNotFoundContent = notFoundContent || (
    error ? (
      <div style={{ padding: '8px', color: '#ff4d4f' }}>
        {error}
      </div>
    ) : (
      'No data'
    )
  );

  return (
    <Select
      mode={selectMode}
      value={value}
      defaultValue={defaultValue}
      placeholder={placeholder}
      disabled={disabled || externalLoading}
      allowClear={allowClear}
      showSearch={showSearch}
      loading={loading || externalLoading}
      notFoundContent={loading ? <Spin size="small" /> : finalNotFoundContent}
      onChange={handleChange}
      onSelect={handleSelect}
      onDeselect={handleDeselect}
      onSearch={showSearch ? handleSearch : undefined}
      style={style}
      className={className}
      size={size}
      dropdownRender={dropdownRender}
      filterOption={false} // Disable client-side filtering since we handle search on server
    >
      {options.map((option) => {
        // Create display text: if description exists, show "label - description", otherwise just label
        const displayText = option.description
          ? `${option.label} - ${option.description}`
          : option.label;

        // Tooltip content for full information
        const tooltipContent = option.description
          ? (
              <div>
                <div><strong>{option.label}</strong></div>
                <div>{option.description}</div>
              </div>
            )
          : option.label;

        return (
          <Option
            key={option.value}
            value={option.value}
            disabled={option.disabled}
            title={option.description || option.label}
          >
            <Tooltip title={tooltipContent} placement="right">
              <div style={{
                whiteSpace: 'nowrap',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                lineHeight: '22px' // Standard line height for Select options
              }}>
                {displayText}
              </div>
            </Tooltip>
          </Option>
        );
      })}
    </Select>
  );
};
