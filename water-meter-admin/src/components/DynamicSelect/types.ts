export interface DynamicOptionDto {
  value: any; // Support any type: string, number, boolean, etc.
  label: string;
  description?: string;
  disabled?: boolean;
  metadata?: Record<string, any>;
}

export interface DynamicSelectRequestDto {
  strategy: string;
  dataSource: string;
  params?: Record<string, any>;
  valueField?: string;
  labelField?: string;
}

export interface DynamicSelectResponseDto {
  options: DynamicOptionDto[];
  totalCount: number;
  success: boolean;
  errorMessage?: string;
  metadata?: Record<string, any>;
}

export interface DynamicSelectProps {
  // 核心配置
  strategy: 'database' | 'enum' | 'api' | 'custom';
  dataSource: string;
  params?: Record<string, any>;
  
  // 字段映射配置
  valueField?: string;
  labelField?: string;
  
  // 选择模式
  mode?: 'single' | 'multiple';
  
  // 基础属性
  value?: any;
  defaultValue?: any;
  placeholder?: string;
  disabled?: boolean;
  allowClear?: boolean;
  showSearch?: boolean;
  
  // 事件回调
  onChange?: (value: any, option?: any) => void;
  onSelect?: (value: any, option: any) => void;
  onDeselect?: (value: any, option: any) => void;
  onSearch?: (value: string) => void;
  
  // 样式
  style?: React.CSSProperties;
  className?: string;
  size?: 'small' | 'middle' | 'large';
  
  // 高级配置
  loading?: boolean;
  notFoundContent?: React.ReactNode;
  dropdownRender?: (menu: React.ReactElement) => React.ReactElement;
  
  // 调试
  debug?: boolean;
}
