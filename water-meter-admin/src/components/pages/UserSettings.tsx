'use client'

import React, { useState, useEffect } from 'react'
import { Card, Space, Button, message, Alert, Spin } from 'antd'
import { SettingOutlined, ReloadOutlined } from '@ant-design/icons'
import type { ConfigurationCategory } from '../../types/configuration'
import configurationService from '../../services/configuration.service'
import ConfigurationForm from '../common/ConfigurationForm'

export default function UserSettings() {
  const [loading, setLoading] = useState(true)
  const [categories, setCategories] = useState<ConfigurationCategory[]>([])

  // Mock current user ID - should come from auth context in real app
  const currentUserId = 1

  useEffect(() => {
    loadUserSettings()
  }, [])

  const loadUserSettings = async () => {
    try {
      setLoading(true)
      const userCategories = await configurationService.getUserSettings(currentUserId)
      setCategories(userCategories)
    } catch (error) {
      console.error('Error loading user settings:', error)
      message.error('Failed to load user settings')
    } finally {
      setLoading(false)
    }
  }

  const handleSave = async (category: string, values: Record<string, any>) => {
    try {
      const result = await configurationService.updateUserSettings(category, values, currentUserId)
      
      if (result.success) {
        message.success(`${category} settings saved successfully`)
        
        if (result.requiresRestart && result.requiresRestart.length > 0) {
          message.warning('Some changes may require refreshing the page to take effect')
        }
        
        // Reload settings to get updated values
        await loadUserSettings()
      } else {
        message.error(result.message || 'Failed to save settings')
      }
    } catch (error) {
      console.error('Error saving settings:', error)
      message.error('Failed to save settings')
    }
  }

  const handleReset = async (category: string) => {
    try {
      const result = await configurationService.resetToDefaults('User', category, currentUserId)
      
      if (result.success) {
        message.success(`${category} settings reset to defaults`)
        // Reload settings to reflect changes
        await loadUserSettings()
      } else {
        message.error(result.message || 'Failed to reset settings')
      }
    } catch (error) {
      console.error('Error resetting settings:', error)
      message.error('Failed to reset settings')
    }
  }

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Spin size="large" />
      </div>
    )
  }

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold flex items-center gap-2">
            <SettingOutlined />
            User Settings
          </h1>
          <p className="text-gray-600 mt-1">
            Configure your personal preferences and account settings
          </p>
        </div>
        <Space>
          <Button
            icon={<ReloadOutlined />}
            onClick={loadUserSettings}
            loading={loading}
          >
            Refresh
          </Button>
        </Space>
      </div>

      <div className="space-y-6">
        {/* User Configuration Settings */}
        {categories.length > 0 ? (
          <Card title="Personal Settings">
            <ConfigurationForm
              scope="User"
              categories={categories}
              loading={loading}
              onSave={handleSave}
              onReset={handleReset}
              userId={currentUserId}
            />
          </Card>
        ) : (
          <Alert
            message="No User Settings Found"
            description="No user configuration found. Please contact your administrator to initialize user settings."
            type="info"
            showIcon
          />
        )}
      </div>
    </div>
  )
} 