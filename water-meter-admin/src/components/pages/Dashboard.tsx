'use client'

import { useState, useEffect } from 'react'
import { 
  Card, 
  Row, 
  Col, 
  Statistic, 
  Progress, 
  Timeline, 
  Table, 
  Tag, 
  Avatar, 
  Space,
  Typography,
  Alert,
  But<PERSON>,
  List,
  Badge
} from 'antd'
import { 
  UserOutlined,
  TeamOutlined,
  SafetyOutlined,
  DashboardOutlined,
  <PERSON>Outlined,
  CheckCircleOutlined,
  Exclamation<PERSON><PERSON>cleOutlined,
  Clock<PERSON>ircleOutlined,
  EyeOutlined,
  SettingOutlined,
  TrophyOutlined,
  SecurityScanOutlined,
  DatabaseOutlined
} from '@ant-design/icons'
import { api } from '@/services/api'

const { Title, Text } = Typography

// 系统统计接口
interface SystemStats {
  totalUsers: number
  activeUsers: number
  totalRoles: number
  totalPermissions: number
  totalMeters: number
  recentReadings: number
  systemHealth: number
  dataSync: boolean
}

// 用户活动接口
interface UserActivity {
  id: number
  username: string
  action: string
  timestamp: string
  ip: string
  status: 'success' | 'warning' | 'error'
}

// 系统通知接口
interface SystemNotification {
  id: number
  type: 'info' | 'warning' | 'error' | 'success'
  title: string
  message: string
  timestamp: string
  read: boolean
}

// 最近登录用户接口
interface RecentUser {
  id: number
  username: string
  fullName: string
  lastLogin: string
  status: 'online' | 'offline'
  avatar?: string
}

export default function Dashboard() {
  const [loading, setLoading] = useState(false)
  const [lastUpdated, setLastUpdated] = useState<string>('Loading...')
  const [systemStats, setSystemStats] = useState<SystemStats>({
    totalUsers: 0,
    activeUsers: 0,
    totalRoles: 0,
    totalPermissions: 0,
    totalMeters: 0,
    recentReadings: 0,
    systemHealth: 0,
    dataSync: false
  })
  
  const [userActivities, setUserActivities] = useState<UserActivity[]>([])
  const [notifications, setNotifications] = useState<SystemNotification[]>([])
  const [recentUsers, setRecentUsers] = useState<RecentUser[]>([])

  // 只在客户端更新时间，避免hydration错误
  useEffect(() => {
    const updateTime = () => {
      setLastUpdated(new Date().toLocaleString('en-US', { timeZone: 'Asia/Shanghai' }))
    }
    
    updateTime() // 立即更新一次
    const interval = setInterval(updateTime, 1000) // 每秒更新
    
    return () => clearInterval(interval)
  }, [])

  useEffect(() => {
    loadDashboardData()
  }, [])

  const loadDashboardData = async () => {
    try {
      setLoading(true)
      
      // 并行加载所有数据
      await Promise.all([
        loadSystemStats(),
        loadUserActivities(),
        loadNotifications(),
        loadRecentUsers()
      ])
    } catch (error) {
      console.error('Error loading dashboard data:', error)
    } finally {
      setLoading(false)
    }
  }

  const loadSystemStats = async () => {
    try {
      // 获取用户统计
      const userResponse = await api.get('/user/statistics')
      const roleResponse = await api.get('/role')
      
      setSystemStats({
        totalUsers: userResponse.data?.totalUsers || 12,
        activeUsers: userResponse.data?.authenticatedUsers || 8,
        totalRoles: roleResponse.data?.length || 3,
        totalPermissions: 24, // 模拟数据
        totalMeters: 156, // 模拟数据
        recentReadings: 89, // 模拟数据
        systemHealth: 98.5,
        dataSync: true
      })
    } catch (error) {
      // 使用模拟数据
      setSystemStats({
        totalUsers: 12,
        activeUsers: 8,
        totalRoles: 3,
        totalPermissions: 24,
        totalMeters: 156,
        recentReadings: 89,
        systemHealth: 98.5,
        dataSync: true
      })
    }
  }

  const loadUserActivities = async () => {
    // 模拟用户活动数据
    const mockActivities: UserActivity[] = [
      {
        id: 1,
        username: 'admin',
        action: 'User role assignment',
        timestamp: new Date(Date.now() - 5 * 60 * 1000).toISOString(),
        ip: '*************',
        status: 'success'
      },
      {
        id: 2,
        username: 'Lindaj',
        action: 'System login',
        timestamp: new Date(Date.now() - 15 * 60 * 1000).toISOString(),
        ip: '*************',
        status: 'success'
      },
      {
        id: 3,
        username: 'manager01',
        action: 'Failed login attempt',
        timestamp: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
        ip: '*************',
        status: 'error'
      },
      {
        id: 4,
        username: 'admin',
        action: 'Permission updated',
        timestamp: new Date(Date.now() - 45 * 60 * 1000).toISOString(),
        ip: '*************',
        status: 'success'
      },
      {
        id: 5,
        username: 'operator02',
        action: 'Meter reading submission',
        timestamp: new Date(Date.now() - 60 * 60 * 1000).toISOString(),
        ip: '*************',
        status: 'warning'
      }
    ]
    setUserActivities(mockActivities)
  }

  const loadNotifications = async () => {
    // 模拟系统通知数据
    const mockNotifications: SystemNotification[] = [
      {
        id: 1,
        type: 'success',
        title: 'System Backup Completed',
        message: 'Daily system backup completed successfully at 02:00 AM',
        timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
        read: false
      },
      {
        id: 2,
        type: 'warning',
        title: 'High Memory Usage',
        message: 'System memory usage is at 85%. Consider optimizing resources.',
        timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(),
        read: false
      },
      {
        id: 3,
        type: 'info',
        title: 'New User Registration',
        message: '3 new users registered in the last 24 hours',
        timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(),
        read: true
      },
      {
        id: 4,
        type: 'error',
        title: 'Workbench API Error',
        message: 'Connection to Workbench API failed. Retrying...',
        timestamp: new Date(Date.now() - 8 * 60 * 60 * 1000).toISOString(),
        read: true
      }
    ]
    setNotifications(mockNotifications)
  }

  const loadRecentUsers = async () => {
    // 模拟最近用户数据
    const mockUsers: RecentUser[] = [
      {
        id: 1,
        username: 'admin',
        fullName: 'System Administrator',
        lastLogin: new Date(Date.now() - 5 * 60 * 1000).toISOString(),
        status: 'online'
      },
      {
        id: 2,
        username: 'Lindaj',
        fullName: 'Linda Jones',
        lastLogin: new Date(Date.now() - 15 * 60 * 1000).toISOString(),
        status: 'online'
      },
      {
        id: 3,
        username: 'manager01',
        fullName: 'John Manager',
        lastLogin: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
        status: 'offline'
      },
      {
        id: 4,
        username: 'operator01',
        fullName: 'Sarah Operator',
        lastLogin: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(),
        status: 'offline'
      }
    ]
    setRecentUsers(mockUsers)
  }

  const getActivityStatusColor = (status: string) => {
    switch (status) {
      case 'success': return 'green'
      case 'warning': return 'orange'
      case 'error': return 'red'
      default: return 'blue'
    }
  }

  const getActivityIcon = (status: string) => {
    switch (status) {
      case 'success': return <CheckCircleOutlined />
      case 'warning': return <ExclamationCircleOutlined />
      case 'error': return <ExclamationCircleOutlined />
      default: return <ClockCircleOutlined />
    }
  }

  const formatTimeAgo = (timestamp: string) => {
    const now = new Date()
    const time = new Date(timestamp)
    const diffInMinutes = Math.floor((now.getTime() - time.getTime()) / (1000 * 60))
    
    if (diffInMinutes < 60) {
      return `${diffInMinutes}m ago`
    } else if (diffInMinutes < 1440) {
      return `${Math.floor(diffInMinutes / 60)}h ago`
    } else {
      return `${Math.floor(diffInMinutes / 1440)}d ago`
    }
  }

  const activityColumns = [
    {
      title: 'User',
      dataIndex: 'username',
      key: 'username',
      render: (username: string) => (
        <Space>
          <Avatar size="small" icon={<UserOutlined />} />
          <Text strong>{username}</Text>
        </Space>
      )
    },
    {
      title: 'Action',
      dataIndex: 'action',
      key: 'action'
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Tag color={getActivityStatusColor(status)} icon={getActivityIcon(status)}>
          {status.toUpperCase()}
        </Tag>
      )
    },
    {
      title: 'Time',
      dataIndex: 'timestamp',
      key: 'timestamp',
      render: (timestamp: string) => (
        <Text type="secondary">{formatTimeAgo(timestamp)}</Text>
      )
    },
    {
      title: 'IP Address',
      dataIndex: 'ip',
      key: 'ip',
      render: (ip: string) => <Text code>{ip}</Text>
    }
  ]

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <Title level={2} className="!mb-0">
          <DashboardOutlined className="mr-2" />
          Water Meter Management Dashboard
        </Title>
        <Button 
          type="primary" 
          icon={<EyeOutlined />}
          onClick={loadDashboardData}
          loading={loading}
        >
          Refresh
        </Button>
      </div>

      {/* 系统状态警报 */}
      <Row gutter={16} className="mb-6">
        <Col span={24}>
          <Alert
            message="System Status: All Services Running"
            description={`Last updated: ${lastUpdated} | Data sync: ${systemStats.dataSync ? 'Active' : 'Inactive'} | Health: ${systemStats.systemHealth}%`}
            type="success"
            showIcon
            action={
              <Space>
                <Button size="small" icon={<SettingOutlined />}>
                  Settings
                </Button>
              </Space>
            }
          />
        </Col>
      </Row>

      {/* 主要统计卡片 */}
      <Row gutter={16} className="mb-6">
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="Total Users"
              value={systemStats.totalUsers}
              prefix={<UserOutlined />}
              valueStyle={{ color: '#1890ff' }}
              suffix={
                <div className="text-sm text-gray-500">
                  <div>{systemStats.activeUsers} active</div>
                </div>
              }
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="Roles & Permissions"
              value={systemStats.totalRoles}
              prefix={<SafetyOutlined />}
              valueStyle={{ color: '#52c41a' }}
              suffix={
                <div className="text-sm text-gray-500">
                  <div>{systemStats.totalPermissions} permissions</div>
                </div>
              }
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="Water Meters"
              value={systemStats.totalMeters}
              prefix={<DatabaseOutlined />}
              valueStyle={{ color: '#faad14' }}
              suffix={
                <div className="text-sm text-gray-500">
                  <div>{systemStats.recentReadings} recent readings</div>
                </div>
              }
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="System Health"
              value={systemStats.systemHealth}
              precision={1}
              prefix={<TrophyOutlined />}
              suffix="%"
              valueStyle={{ color: systemStats.systemHealth > 95 ? '#52c41a' : '#faad14' }}
            />
            <Progress 
              percent={systemStats.systemHealth} 
              strokeColor={systemStats.systemHealth > 95 ? '#52c41a' : '#faad14'}
              showInfo={false} 
              size="small"
              className="mt-2"
            />
          </Card>
        </Col>
      </Row>

      {/* 主要内容区域 */}
      <Row gutter={16} className="mb-6">
        {/* 用户活动 */}
        <Col xs={24} lg={16}>
          <Card 
            title={
              <Space>
                <SecurityScanOutlined />
                <span>Recent Activities</span>
                <Badge count={userActivities.length} />
              </Space>
            }
            extra={<Button type="link" size="small">View All</Button>}
          >
            <Table
              columns={activityColumns}
              dataSource={userActivities}
              rowKey="id"
              pagination={false}
              size="small"
              scroll={{ y: 300 }}
            />
          </Card>
        </Col>

        {/* 系统通知 */}
        <Col xs={24} lg={8}>
          <Card 
            title={
              <Space>
                <BellOutlined />
                <span>System Notifications</span>
                <Badge count={notifications.filter(n => !n.read).length} />
              </Space>
            }
            extra={<Button type="link" size="small">Mark All Read</Button>}
          >
            <List
              dataSource={notifications}
              size="small"
              renderItem={(item) => (
                <List.Item>
                  <List.Item.Meta
                    avatar={
                      <Avatar 
                        size="small" 
                        style={{ 
                          backgroundColor: 
                            item.type === 'success' ? '#52c41a' :
                            item.type === 'warning' ? '#faad14' :
                            item.type === 'error' ? '#ff4d4f' : '#1890ff'
                        }}
                        icon={getActivityIcon(item.type)}
                      />
                    }
                    title={
                      <div className="flex justify-between">
                        <Text strong={!item.read}>{item.title}</Text>
                        {!item.read && <Badge status="processing" />}
                      </div>
                    }
                    description={
                      <div>
                        <div className="text-xs text-gray-500 mb-1">
                          {formatTimeAgo(item.timestamp)}
                        </div>
                        <Text type="secondary" className="text-xs">
                          {item.message}
                        </Text>
                      </div>
                    }
                  />
                </List.Item>
              )}
            />
          </Card>
        </Col>
      </Row>

      {/* 最近用户和系统状态 */}
      <Row gutter={16}>
        {/* 最近登录用户 */}
        <Col xs={24} lg={12}>
          <Card 
            title={
              <Space>
                <TeamOutlined />
                <span>Recent Users</span>
              </Space>
            }
            extra={<Button type="link" size="small">View All Users</Button>}
          >
            <List
              dataSource={recentUsers}
              renderItem={(user) => (
                <List.Item>
                  <List.Item.Meta
                    avatar={
                      <Badge 
                        status={user.status === 'online' ? 'success' : 'default'} 
                        dot
                      >
                        <Avatar icon={<UserOutlined />} />
                      </Badge>
                    }
                    title={
                      <div className="flex justify-between">
                        <Space>
                          <Text strong>{user.fullName}</Text>
                          <Text type="secondary">@{user.username}</Text>
                        </Space>
                        <Tag color={user.status === 'online' ? 'green' : 'default'}>
                          {user.status}
                        </Tag>
                      </div>
                    }
                    description={
                      <Text type="secondary" className="text-xs">
                        Last login: {formatTimeAgo(user.lastLogin)}
                      </Text>
                    }
                  />
                </List.Item>
              )}
            />
          </Card>
        </Col>

        {/* 系统性能指标 */}
        <Col xs={24} lg={12}>
          <Card 
            title={
              <Space>
                <DatabaseOutlined />
                <span>System Performance</span>
              </Space>
            }
          >
            <Row gutter={16}>
              <Col span={12}>
                <div className="text-center">
                  <Progress 
                    type="circle" 
                    percent={75} 
                    format={(percent) => `${percent}%`}
                    size={80}
                  />
                  <div className="mt-2 text-sm">CPU Usage</div>
                </div>
              </Col>
              <Col span={12}>
                <div className="text-center">
                  <Progress 
                    type="circle" 
                    percent={85} 
                    strokeColor="#faad14"
                    format={(percent) => `${percent}%`}
                    size={80}
                  />
                  <div className="mt-2 text-sm">Memory Usage</div>
                </div>
              </Col>
            </Row>
            <div className="mt-4">
              <div className="flex justify-between mb-2">
                <Text>Disk Usage</Text>
                <Text>45%</Text>
              </div>
              <Progress percent={45} showInfo={false} />
              
              <div className="flex justify-between mb-2 mt-3">
                <Text>Network I/O</Text>
                <Text>22%</Text>
              </div>
              <Progress percent={22} showInfo={false} strokeColor="#52c41a" />
            </div>
          </Card>
        </Col>
      </Row>
    </div>
  )
} 