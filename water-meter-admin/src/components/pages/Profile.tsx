'use client'

import { useState, useEffect } from 'react'
import { 
  Card, 
  Form, 
  Input, 
  Button, 
  Avatar, 
  Upload, 
  message, 
  Row, 
  Col, 
  Divider,
  Typography,
  Space,
  Tag,
  Descriptions,
  Modal,
  InputNumber
} from 'antd'
import { 
  UserOutlined, 
  EditOutlined, 
  CameraOutlined,
  MailOutlined,
  PhoneOutlined,
  IdcardOutlined,
  CalendarOutlined,
  CheckCircleOutlined,
  SaveOutlined,
  CloseOutlined
} from '@ant-design/icons'
import type { UploadProps } from 'antd'

const { Title, Text } = Typography

// 用户信息接口
interface UserProfile {
  id: number
  username: string
  email: string
  fullName: string
  phoneNumber?: string
  avatar?: string
  roles: string[]
  department?: string
  position?: string
  employeeId?: string
  joinDate?: string
  lastLoginTime?: string
  status: 'active' | 'inactive'
}

export default function Profile() {
  const [loading, setLoading] = useState(false)
  const [editing, setEditing] = useState(false)
  const [form] = Form.useForm()
  const [avatarUrl, setAvatarUrl] = useState<string>()

  // 用户信息 - 这里用模拟数据，实际应该从API获取
  const [userProfile, setUserProfile] = useState<UserProfile>({
    id: 1,
    username: 'admin',
    email: '<EMAIL>',
    fullName: 'System Administrator',
    phoneNumber: '+64 21 123 4567',
    avatar: '',
    roles: ['Administrator', 'System Admin'],
    department: 'IT Department',
    position: 'System Administrator',
    employeeId: 'EMP001',
    joinDate: '2023-01-15',
    lastLoginTime: '2024-01-15 10:30:00',
    status: 'active'
  })

  useEffect(() => {
    loadUserProfile()
  }, [])

  const loadUserProfile = async () => {
    try {
      setLoading(true)
      // 这里应该调用API获取用户信息
      // const response = await api.get('/user/profile')
      // setUserProfile(response.data)
      
      // 设置表单初始值
      form.setFieldsValue({
        fullName: userProfile.fullName,
        email: userProfile.email,
        phoneNumber: userProfile.phoneNumber,
        department: userProfile.department,
        position: userProfile.position
      })
    } catch (error) {
      message.error('Failed to load user profile')
    } finally {
      setLoading(false)
    }
  }

  const handleEdit = () => {
    setEditing(true)
    form.setFieldsValue({
      fullName: userProfile.fullName,
      email: userProfile.email,
      phoneNumber: userProfile.phoneNumber,
      department: userProfile.department,
      position: userProfile.position
    })
  }

  const handleCancel = () => {
    setEditing(false)
    form.resetFields()
  }

  const handleSave = async (values: any) => {
    try {
      setLoading(true)
      // 这里应该调用API更新用户信息
      // await api.put('/user/profile', values)
      
      setUserProfile(prev => ({
        ...prev,
        ...values
      }))
      setEditing(false)
      message.success('Profile updated successfully')
    } catch (error) {
      message.error('Failed to update profile')
    } finally {
      setLoading(false)
    }
  }

  // 头像上传处理
  const uploadProps: UploadProps = {
    name: 'avatar',
    listType: 'picture-card',
    className: 'avatar-uploader',
    showUploadList: false,
    action: '/api/upload/avatar', // 头像上传API
    beforeUpload: (file) => {
      const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png'
      if (!isJpgOrPng) {
        message.error('You can only upload JPG/PNG files!')
        return false
      }
      const isLt2M = file.size / 1024 / 1024 < 2
      if (!isLt2M) {
        message.error('Image must smaller than 2MB!')
        return false
      }
      return true
    },
    onChange: (info) => {
      if (info.file.status === 'uploading') {
        setLoading(true)
        return
      }
      if (info.file.status === 'done') {
        setAvatarUrl(info.file.response?.url)
        setUserProfile(prev => ({
          ...prev,
          avatar: info.file.response?.url
        }))
        message.success('Avatar uploaded successfully')
        setLoading(false)
      }
      if (info.file.status === 'error') {
        message.error('Avatar upload failed')
        setLoading(false)
      }
    }
  }

  const formatDate = (dateStr?: string) => {
    if (!dateStr) return 'N/A'
    return new Date(dateStr).toLocaleDateString('en-NZ', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  const formatDateTime = (dateStr?: string) => {
    if (!dateStr) return 'N/A'
    return new Date(dateStr).toLocaleString('en-NZ', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  return (
    <div className="p-6">
      <Title level={2}>User Profile</Title>
      
      <Row gutter={24}>
        <Col xs={24} lg={8}>
          {/* Avatar and basic info */}
          <Card>
            <div className="text-center">
              <Upload {...uploadProps}>
                <Avatar
                  size={120}
                  src={avatarUrl || userProfile.avatar}
                  icon={<UserOutlined />}
                  className="mb-4"
                />
                {editing && (
                  <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50 rounded-full opacity-0 hover:opacity-100 transition-opacity cursor-pointer">
                    <CameraOutlined className="text-white text-lg" />
                  </div>
                )}
              </Upload>
              
              <div className="mt-4">
                <Title level={4} className="mb-2">{userProfile.fullName}</Title>
                <Text type="secondary" className="block mb-2">@{userProfile.username}</Text>
                <Space>
                  <Tag color={userProfile.status === 'active' ? 'green' : 'red'}>
                    <CheckCircleOutlined /> {userProfile.status.toUpperCase()}
                  </Tag>
                </Space>
              </div>

              <Divider />

              <div className="text-left">
                <Space direction="vertical" className="w-full">
                  <div className="flex items-center">
                    <MailOutlined className="mr-2 text-gray-500" />
                    <Text>{userProfile.email}</Text>
                  </div>
                  <div className="flex items-center">
                    <PhoneOutlined className="mr-2 text-gray-500" />
                    <Text>{userProfile.phoneNumber || 'Not provided'}</Text>
                  </div>
                  <div className="flex items-center">
                    <IdcardOutlined className="mr-2 text-gray-500" />
                    <Text>{userProfile.employeeId || 'Not provided'}</Text>
                  </div>
                  <div className="flex items-center">
                    <CalendarOutlined className="mr-2 text-gray-500" />
                    <Text>Joined {formatDate(userProfile.joinDate)}</Text>
                  </div>
                </Space>
              </div>
            </div>
          </Card>

          {/* Role information */}
          <Card title="Roles & Permissions" className="mt-4">
            <Space direction="vertical" className="w-full">
              {userProfile.roles.map((role, index) => (
                <Tag key={index} color="blue" className="mb-2">
                  {role}
                </Tag>
              ))}
            </Space>
          </Card>
        </Col>

        <Col xs={24} lg={16}>
          {/* Detailed information */}
          <Card 
            title="Detailed Information"
            extra={
              !editing ? (
                <Button 
                  type="primary" 
                  icon={<EditOutlined />}
                  onClick={handleEdit}
                >
                  Edit Profile
                </Button>
              ) : (
                <Space>
                  <Button 
                    icon={<CloseOutlined />}
                    onClick={handleCancel}
                  >
                    Cancel
                  </Button>
                  <Button 
                    type="primary" 
                    icon={<SaveOutlined />}
                    onClick={() => form.submit()}
                    loading={loading}
                  >
                    Save
                  </Button>
                </Space>
              )
            }
          >
            {!editing ? (
              <Descriptions column={2} bordered>
                <Descriptions.Item label="Full Name">{userProfile.fullName}</Descriptions.Item>
                <Descriptions.Item label="Username">{userProfile.username}</Descriptions.Item>
                <Descriptions.Item label="Email">{userProfile.email}</Descriptions.Item>
                <Descriptions.Item label="Phone">{userProfile.phoneNumber || 'Not provided'}</Descriptions.Item>
                <Descriptions.Item label="Department">{userProfile.department || 'Not provided'}</Descriptions.Item>
                <Descriptions.Item label="Position">{userProfile.position || 'Not provided'}</Descriptions.Item>
                <Descriptions.Item label="Employee ID">{userProfile.employeeId || 'Not provided'}</Descriptions.Item>
                <Descriptions.Item label="Join Date">{formatDate(userProfile.joinDate)}</Descriptions.Item>
                <Descriptions.Item label="Last Login" span={2}>
                  {formatDateTime(userProfile.lastLoginTime)}
                </Descriptions.Item>
              </Descriptions>
            ) : (
              <Form
                form={form}
                layout="vertical"
                onFinish={handleSave}
                className="mt-4"
              >
                <Row gutter={16}>
                  <Col span={12}>
                    <Form.Item
                      name="fullName"
                      label="Full Name"
                      rules={[
                        { required: true, message: 'Please input your full name!' },
                        { min: 2, message: 'Name must be at least 2 characters!' }
                      ]}
                    >
                      <Input />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item
                      name="email"
                      label="Email"
                      rules={[
                        { required: true, message: 'Please input your email!' },
                        { type: 'email', message: 'Please enter a valid email!' }
                      ]}
                    >
                      <Input />
                    </Form.Item>
                  </Col>
                </Row>

                <Row gutter={16}>
                  <Col span={12}>
                    <Form.Item
                      name="phoneNumber"
                      label="Phone Number"
                      rules={[
                        { pattern: /^[+]?[\d\s\-()]+$/, message: 'Please enter a valid phone number!' }
                      ]}
                    >
                      <Input />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item
                      name="department"
                      label="Department"
                    >
                      <Input />
                    </Form.Item>
                  </Col>
                </Row>

                <Row gutter={16}>
                  <Col span={12}>
                    <Form.Item
                      name="position"
                      label="Position"
                    >
                      <Input />
                    </Form.Item>
                  </Col>
                </Row>
              </Form>
            )}
          </Card>

          {/* Account Security */}
          <Card title="Account Security" className="mt-4">
            <Space direction="vertical" className="w-full">
              <div className="flex justify-between items-center p-4 bg-gray-50 rounded">
                <div>
                  <Text strong>Password</Text>
                  <br />
                  <Text type="secondary">Last changed: 30 days ago</Text>
                </div>
                <Button type="primary" ghost>
                  Change Password
                </Button>
              </div>
              
              <div className="flex justify-between items-center p-4 bg-gray-50 rounded">
                <div>
                  <Text strong>Two-Factor Authentication</Text>
                  <br />
                  <Text type="secondary">Enhance account security</Text>
                </div>
                <Button type="primary" ghost>
                  Enable 2FA
                </Button>
              </div>
            </Space>
          </Card>
        </Col>
      </Row>
    </div>
  )
} 