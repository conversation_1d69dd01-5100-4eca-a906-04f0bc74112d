'use client'

import { useState, useEffect } from 'react'
import { 
  Table, 
  Button, 
  Modal, 
  Form, 
  Input, 
  message, 
  Space, 
  Popconfirm,
  Transfer,
  Card,
  Divider
} from 'antd'
import { PlusOutlined, EditOutlined, DeleteOutlined, SettingOutlined } from '@ant-design/icons'
import { api } from '../../services/api'
import PaginatedTable from '@/components/common/PaginatedTable'

interface Role {
  id: number
  name: string
  description: string
  createdAt: string
  updatedAt: string
}

interface Permission {
  id: number
  name: string
  code: string
  description: string
  module: string
}

interface TransferItem {
  key: string
  title: string
  description: string
  module: string
}

export default function RoleManagement() {
  const [roles, setRoles] = useState<Role[]>([])
  const [permissions, setPermissions] = useState<Permission[]>([])
  const [loading, setLoading] = useState(true)
  const [modalVisible, setModalVisible] = useState(false)
  const [permissionModalVisible, setPermissionModalVisible] = useState(false)
  const [editingRole, setEditingRole] = useState<Role | null>(null)
  const [selectedRole, setSelectedRole] = useState<Role | null>(null)
  const [rolePermissions, setRolePermissions] = useState<Permission[]>([])
  const [targetKeys, setTargetKeys] = useState<string[]>([])
  const [form] = Form.useForm()

  useEffect(() => {
    fetchRoles()
    fetchPermissions()
  }, [])

  const fetchRoles = async () => {
    try {
      setLoading(true)
      const response = await api.get('/role')
      setRoles(response.data || [])
    } catch (error) {
      console.error('Failed to fetch roles:', error)
      message.error('Failed to load roles')
    } finally {
      setLoading(false)
    }
  }

  const fetchPermissions = async () => {
    try {
      const response = await api.get('/role/permissions')
      setPermissions(response.data || [])
    } catch (error) {
      console.error('Failed to fetch permissions:', error)
      message.error('Failed to load permissions')
    }
  }

  const fetchRolePermissions = async (roleId: number) => {
    try {
      const response = await api.get(`/role/${roleId}/permissions`)
      const rolePerms = response.data || []
      setRolePermissions(rolePerms)
      setTargetKeys(rolePerms.map((p: Permission) => p.id.toString()))
    } catch (error) {
      console.error('Failed to fetch role permissions:', error)
      message.error('Failed to load role permissions')
    }
  }

  const handleCreate = () => {
    setEditingRole(null)
    setModalVisible(true)
    form.resetFields()
  }

  const handleEdit = (role: Role) => {
    setEditingRole(role)
    setModalVisible(true)
    form.setFieldsValue({
      name: role.name,
      description: role.description
    })
  }

  const handleDelete = async (id: number) => {
    try {
      await api.delete(`/role/${id}`)
      message.success('Role deleted successfully')
      fetchRoles()
    } catch (error) {
      console.error('Failed to delete role:', error)
      message.error('Failed to delete role')
    }
  }

  const handleManagePermissions = async (role: Role) => {
    setSelectedRole(role)
    setPermissionModalVisible(true)
    await fetchRolePermissions(role.id)
  }

  const handleSubmit = async (values: any) => {
    try {
      if (editingRole) {
        await api.put(`/role/${editingRole.id}`, {
          id: editingRole.id,
          name: values.name,
          description: values.description
        })
        message.success('Role updated successfully')
      } else {
        await api.post('/role', values)
        message.success('Role created successfully')
      }
      setModalVisible(false)
      fetchRoles()
    } catch (error: any) {
      console.error('Failed to save role:', error)
      if (error.response?.status === 409) {
        message.error('Role name already exists')
      } else {
        message.error('Failed to save role')
      }
    }
  }

  const handlePermissionSubmit = async () => {
    if (!selectedRole) return

    try {
      const permissionIds = targetKeys.map(key => parseInt(key))
      await api.post(`/role/${selectedRole.id}/permissions`, permissionIds)
      message.success('Permissions updated successfully')
      setPermissionModalVisible(false)
    } catch (error) {
      console.error('Failed to update permissions:', error)
      message.error('Failed to update permissions')
    }
  }

  const transferDataSource: TransferItem[] = permissions.map(permission => ({
    key: permission.id.toString(),
    title: permission.name,
    description: permission.description,
    module: permission.module
  }))

  const columns = [
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
      sorter: (a: Role, b: Role) => a.name.localeCompare(b.name),
    },
    {
      title: 'Description',
      dataIndex: 'description',
      key: 'description',
    },
    {
      title: 'Created At',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (date: string) => new Date(date).toLocaleDateString(),
    },
    {
      title: 'Updated At',
      dataIndex: 'updatedAt',
      key: 'updatedAt',
      render: (date: string) => new Date(date).toLocaleDateString(),
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_: any, record: Role) => (
        <Space size="middle">
          <Button
            type="link"
            icon={<SettingOutlined />}
            onClick={() => handleManagePermissions(record)}
          >
            Permissions
          </Button>
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            Edit
          </Button>
          <Popconfirm
            title="Are you sure you want to delete this role?"
            onConfirm={() => handleDelete(record.id)}
            okText="Yes"
            cancelText="No"
          >
            <Button
              type="link"
              danger
              icon={<DeleteOutlined />}
            >
              Delete
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ]

  return (
    <div>
      <div className="flex justify-end items-center mb-6">
        <Button 
          type="primary" 
          icon={<PlusOutlined />}
          onClick={handleCreate}
        >
          Add Role
        </Button>
      </div>

      <Card>
        <PaginatedTable
          columns={columns}
          dataSource={roles}
          rowKey="id"
          loading={loading}
          paginationOptions={{
            defaultPageSize: 10,
            showTotal: true
          }}
        />
      </Card>

      {/* Role Create/Edit Modal */}
      <Modal
        title={editingRole ? 'Edit Role' : 'Create Role'}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        onOk={() => form.submit()}
        destroyOnClose
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Form.Item
            name="name"
            label="Role Name"
            rules={[
              { required: true, message: 'Please enter role name' },
              { min: 2, message: 'Role name must be at least 2 characters' }
            ]}
          >
            <Input placeholder="Enter role name" />
          </Form.Item>
          <Form.Item
            name="description"
            label="Description"
            rules={[
              { required: true, message: 'Please enter description' }
            ]}
          >
            <Input.TextArea rows={3} placeholder="Enter role description" />
          </Form.Item>
        </Form>
      </Modal>

      {/* Permission Management Modal */}
      <Modal
        title={`Manage Permissions - ${selectedRole?.name}`}
        open={permissionModalVisible}
        onCancel={() => setPermissionModalVisible(false)}
        onOk={handlePermissionSubmit}
        width={800}
        destroyOnClose
      >
        <div className="mb-4">
          <p className="text-gray-600">
            Select permissions for the role. Permissions are grouped by module.
          </p>
        </div>
        
        <Transfer
          dataSource={transferDataSource}
          targetKeys={targetKeys}
          onChange={(targetKeys) => setTargetKeys(targetKeys.map(String))}
          render={item => (
            <div>
              <div className="font-medium">{item.title}</div>
              <div className="text-sm text-gray-500">{item.description}</div>
              <div className="text-xs text-blue-500">{item.module}</div>
            </div>
          )}
          listStyle={{
            width: 350,
            height: 400,
          }}
          titles={['Available Permissions', 'Assigned Permissions']}
          showSearch
          filterOption={(search, item) =>
            item.title.toLowerCase().includes(search.toLowerCase()) ||
            item.description.toLowerCase().includes(search.toLowerCase()) ||
            item.module.toLowerCase().includes(search.toLowerCase())
          }
        />
      </Modal>
    </div>
  )
} 