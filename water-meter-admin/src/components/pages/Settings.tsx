'use client'

import React, { useState, useEffect } from 'react'
import { Card, Space, But<PERSON>, message, Alert, Spin, Typography, Divider, Statistic, Row, Col } from 'antd'
import { SettingOutlined, ReloadOutlined, CheckCircleOutlined, WarningOutlined, DatabaseOutlined } from '@ant-design/icons'
import type { ConfigurationCategory } from '../../types/configuration'
import configurationService from '../../services/configuration.service'
import ConfigurationForm from '../common/ConfigurationForm'
import { initializeAllConfigurations, checkConfigurationHealth } from '../../utils/initializeConfiguration'

const { Text, Paragraph } = Typography

export default function Settings() {
  const [loading, setLoading] = useState(true)
  const [categories, setCategories] = useState<ConfigurationCategory[]>([])
  const [initializing, setInitializing] = useState(false)
  const [healthStatus, setHealthStatus] = useState<{
    systemConfigsCount: number
    userConfigsCount: number
    hasConfigurations: boolean
  } | null>(null)

  useEffect(() => {
    loadSystemSettings()
    checkHealth()
  }, [])

  const checkHealth = async () => {
    const health = await checkConfigurationHealth()
    setHealthStatus(health)
  }

  const loadSystemSettings = async () => {
    try {
      setLoading(true)
      const systemCategories = await configurationService.getSystemSettings()
      setCategories(systemCategories)
    } catch (error) {
      console.error('Error loading system settings:', error)
      message.error('Failed to load system settings')
    } finally {
      setLoading(false)
    }
  }

  const handleSave = async (category: string, values: Record<string, any>) => {
    try {
      const result = await configurationService.updateSystemSettings(category, values)
      
      if (result.success) {
        message.success(`${category} settings saved successfully`)
        
        if (result.requiresRestart && result.requiresRestart.length > 0) {
          message.warning('Some changes require a system restart to take effect')
        }
        
        // Reload settings to get updated values
        await loadSystemSettings()
      } else {
        message.error(result.message || 'Failed to save settings')
      }
    } catch (error) {
      console.error('Error saving settings:', error)
      message.error('Failed to save settings')
    }
  }

  const handleReset = async (category: string) => {
    try {
      const result = await configurationService.resetToDefaults('System', category)
      
      if (result.success) {
        message.success(`${category} settings reset to defaults`)
        await loadSystemSettings()
      } else {
        message.error(result.message || 'Failed to reset settings')
      }
    } catch (error) {
      console.error('Error resetting settings:', error)
      message.error('Failed to reset settings')
    }
  }

  const handleInitializeDefaults = async () => {
    try {
      setInitializing(true)
      message.loading('Initializing configuration templates...', 0)
      
      const success = await initializeAllConfigurations()
      message.destroy()
      
      if (success) {
        message.success('Configuration templates initialized successfully!')
        await Promise.all([loadSystemSettings(), checkHealth()])
      } else {
        message.error('Failed to initialize configuration templates')
      }
    } catch (error) {
      message.destroy()
      console.error('Error initializing defaults:', error)
      message.error('Failed to initialize configuration templates')
    } finally {
      setInitializing(false)
    }
  }

  const handleClearAndReinitialize = async () => {
    try {
      setInitializing(true)
      message.loading('Clearing existing configurations and reinitializing...', 0)
      
      const success = await configurationService.clearAndReinitialize()
      message.destroy()
      
      if (success) {
        message.success('Configurations cleared and reinitialized successfully!')
        await Promise.all([loadSystemSettings(), checkHealth()])
      } else {
        message.error('Failed to clear and reinitialize configurations')
      }
    } catch (error) {
      message.destroy()
      console.error('Error clearing and reinitializing:', error)
      message.error('Failed to clear and reinitialize configurations')
    } finally {
      setInitializing(false)
    }
  }

  const handleForceReinitialize = async () => {
    try {
      setInitializing(true)
      message.loading('Force reinitializing configuration templates...', 0)
      
      const success = await configurationService.forceReinitialize()
      message.destroy()
      
      if (success) {
        message.success('Configuration templates force reinitialized successfully!')
        await Promise.all([loadSystemSettings(), checkHealth()])
      } else {
        message.error('Failed to force reinitialize configuration templates')
      }
    } catch (error) {
      message.destroy()
      console.error('Error force reinitializing:', error)
      message.error('Failed to force reinitialize configuration templates')
    } finally {
      setInitializing(false)
    }
  }

  if (loading && !healthStatus) {
    return (
      <div className="flex justify-center items-center h-64">
        <Spin size="large" />
      </div>
    )
  }

  const shouldShowInitialization = !healthStatus?.hasConfigurations || categories.length === 0

  return (
    <div className="p-6">
      <div className="flex justify-end items-center mb-6">
        <Space>
          <Button
            icon={<ReloadOutlined />}
            onClick={() => Promise.all([loadSystemSettings(), checkHealth()])}
            loading={loading}
          >
            Refresh
          </Button>
          <Button
            type="primary"
            icon={<DatabaseOutlined />}
            onClick={handleClearAndReinitialize}
            loading={initializing}
            danger
          >
            Clear & Reinitialize
          </Button>
          <Button
            icon={<DatabaseOutlined />}
            onClick={handleForceReinitialize}
            loading={initializing}
          >
            Force Reinitialize
          </Button>
        </Space>
      </div>

      {/* Configuration Health Status */}
      {healthStatus && (
        <Card className="mb-6" size="small">
          <Row gutter={16}>
            <Col span={8}>
              <Statistic 
                title="System Configurations" 
                value={healthStatus.systemConfigsCount} 
                prefix={<SettingOutlined />}
              />
            </Col>
            <Col span={8}>
              <Statistic 
                title="User Configuration Templates" 
                value={healthStatus.userConfigsCount} 
                prefix={<SettingOutlined />}
              />
            </Col>
            <Col span={8}>
              <div className="flex items-center gap-2">
                {healthStatus.hasConfigurations ? (
                  <>
                    <CheckCircleOutlined className="text-green-500" />
                    <Text type="success">Configuration System Active</Text>
                  </>
                ) : (
                  <>
                    <WarningOutlined className="text-orange-500" />
                    <Text type="warning">Needs Initialization</Text>
                  </>
                )}
              </div>
            </Col>
          </Row>
        </Card>
      )}

      {shouldShowInitialization ? (
        <Card>
          <Alert
            message="Configuration System Not Initialized"
            description={
              <div>
                <Paragraph>
                  The configuration management system needs to be initialized with default templates. 
                  This will create all the standard system settings including:
                </Paragraph>
                <ul className="ml-4 mb-4">
                  <li><strong>General Settings:</strong> Application name, version, API configuration</li>
                  <li><strong>Security Settings:</strong> JWT, password policies, session management</li>
                  <li><strong>Notification Settings:</strong> Email and SMS configuration</li>
                  <li><strong>API Settings:</strong> External service integrations</li>
                  <li><strong>Alert Settings:</strong> System monitoring thresholds</li>
                </ul>
                <Text type="secondary">
                  Click "Initialize Templates" to set up the default configuration structure.
                </Text>
              </div>
            }
            type="info"
            showIcon
            action={
              <Button
                type="primary"
                icon={<DatabaseOutlined />}
                onClick={handleInitializeDefaults}
                loading={initializing}
                size="large"
              >
                Initialize Templates
              </Button>
            }
          />
        </Card>
      ) : (
        <Card>
          <ConfigurationForm
            scope="System"
            categories={categories}
            loading={loading}
            onSave={handleSave}
            onReset={handleReset}
          />
        </Card>
      )}
    </div>
  )
} 