'use client'

import { useState, useEffect } from 'react'
import { 
  Table, 
  Button, 
  Modal, 
  Form, 
  Input, 
  message, 
  Space, 
  Popconfirm,
  Card,
  Select,
  Tag,
  Divider
} from 'antd'
import { PlusOutlined, EditOutlined, DeleteOutlined, AppstoreOutlined } from '@ant-design/icons'
import { api } from '../../services/api'
import PaginatedTable from '@/components/common/PaginatedTable'

interface Permission {
  id: number
  name: string
  code: string
  description: string
  module: string
  createdAt: string
  updatedAt: string
}

export default function PermissionManagement() {
  const [permissions, setPermissions] = useState<Permission[]>([])
  const [modules, setModules] = useState<string[]>([])
  const [loading, setLoading] = useState(true)
  const [modalVisible, setModalVisible] = useState(false)
  const [editingPermission, setEditingPermission] = useState<Permission | null>(null)
  const [selectedModule, setSelectedModule] = useState<string>('all')
  const [form] = Form.useForm()

  useEffect(() => {
    fetchPermissions()
    fetchModules()
  }, [])

  const fetchPermissions = async () => {
    try {
      setLoading(true)
      const response = await api.get('/permission')
      setPermissions(response.data || [])
    } catch (error) {
      console.error('Failed to fetch permissions:', error)
      message.error('Failed to load permissions')
    } finally {
      setLoading(false)
    }
  }

  const fetchModules = async () => {
    try {
      const response = await api.get('/permission/modules')
      setModules(response.data || [])
    } catch (error) {
      console.error('Failed to fetch modules:', error)
      message.error('Failed to load modules')
    }
  }

  const handleCreate = () => {
    setEditingPermission(null)
    setModalVisible(true)
    form.resetFields()
  }

  const handleEdit = (permission: Permission) => {
    setEditingPermission(permission)
    setModalVisible(true)
    form.setFieldsValue({
      name: permission.name,
      code: permission.code,
      description: permission.description,
      module: permission.module
    })
  }

  const handleDelete = async (id: number) => {
    try {
      await api.delete(`/permission/${id}`)
      message.success('Permission deleted successfully')
      fetchPermissions()
    } catch (error) {
      console.error('Failed to delete permission:', error)
      message.error('Failed to delete permission')
    }
  }

  const handleSubmit = async (values: any) => {
    try {
      if (editingPermission) {
        await api.put(`/permission/${editingPermission.id}`, {
          id: editingPermission.id,
          name: values.name,
          code: values.code,
          description: values.description,
          module: values.module
        })
        message.success('Permission updated successfully')
      } else {
        await api.post('/permission', values)
        message.success('Permission created successfully')
      }
      setModalVisible(false)
      fetchPermissions()
      fetchModules() // Refresh modules in case a new one was added
    } catch (error: any) {
      console.error('Failed to save permission:', error)
      if (error.response?.status === 409) {
        message.error('Permission code already exists')
      } else {
        message.error('Failed to save permission')
      }
    }
  }

  const getModuleColor = (module: string) => {
    const colors: { [key: string]: string } = {
      'Dashboard': 'blue',
      'System': 'green',
      'Business': 'orange',
      'Security': 'red',
      'Report': 'purple'
    }
    return colors[module] || 'default'
  }

  const filteredPermissions = selectedModule === 'all' 
    ? permissions 
    : permissions.filter(p => p.module === selectedModule)

  const columns = [
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
      sorter: (a: Permission, b: Permission) => a.name.localeCompare(b.name),
    },
    {
      title: 'Code',
      dataIndex: 'code',
      key: 'code',
      render: (code: string) => (
        <code className="bg-gray-100 px-2 py-1 rounded text-sm">{code}</code>
      ),
    },
    {
      title: 'Description',
      dataIndex: 'description',
      key: 'description',
    },
    {
      title: 'Module',
      dataIndex: 'module',
      key: 'module',
      render: (module: string) => (
        <Tag color={getModuleColor(module)}>{module}</Tag>
      ),
      filters: modules.map(module => ({ text: module, value: module })),
      onFilter: (value: any, record: Permission) => 
        record.module === value,
    },
    {
      title: 'Created At',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (date: string) => new Date(date).toLocaleDateString(),
      sorter: (a: Permission, b: Permission) => 
        new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime(),
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_: any, record: Permission) => (
        <Space size="middle">
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            Edit
          </Button>
          <Popconfirm
            title="Are you sure you want to delete this permission?"
            description="This action cannot be undone."
            onConfirm={() => handleDelete(record.id)}
            okText="Yes"
            cancelText="No"
          >
            <Button
              type="link"
              danger
              icon={<DeleteOutlined />}
            >
              Delete
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ]

  return (
    <div>
      <div className="flex justify-end items-center mb-6">
        <Button 
          type="primary" 
          icon={<PlusOutlined />}
          onClick={handleCreate}
        >
          Add Permission
        </Button>
      </div>

      <Card>
        <div className="mb-4 flex items-center gap-4">
          <div className="flex items-center gap-2">
            <AppstoreOutlined />
            <span>Filter by Module:</span>
          </div>
          <Select
            value={selectedModule}
            onChange={setSelectedModule}
            style={{ width: 150 }}
            placeholder="Select module"
          >
            <Select.Option value="all">All Modules</Select.Option>
            {modules.map(module => (
              <Select.Option key={module} value={module}>
                {module}
              </Select.Option>
            ))}
          </Select>
          <Divider type="vertical" />
          <span className="text-gray-500">
            Total: {filteredPermissions.length} permissions
          </span>
        </div>

        <PaginatedTable
          columns={columns}
          dataSource={filteredPermissions}
          rowKey="id"
          loading={loading}
          paginationOptions={{
            defaultPageSize: 10,
            showTotal: true
          }}
        />
      </Card>

      {/* Permission Create/Edit Modal */}
      <Modal
        title={editingPermission ? 'Edit Permission' : 'Create Permission'}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        onOk={() => form.submit()}
        destroyOnClose
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Form.Item
            name="name"
            label="Permission Name"
            rules={[
              { required: true, message: 'Please enter permission name' },
              { min: 3, message: 'Permission name must be at least 3 characters' }
            ]}
          >
            <Input placeholder="Enter permission name (e.g., View Dashboard)" />
          </Form.Item>
          
          <Form.Item
            name="code"
            label="Permission Code"
            rules={[
              { required: true, message: 'Please enter permission code' },
              { 
                pattern: /^[a-z0-9._-]+$/,
                message: 'Permission code should contain only lowercase letters, numbers, dots, underscores and hyphens'
              }
            ]}
          >
            <Input 
              placeholder="Enter permission code (e.g., dashboard.view)" 
              style={{ fontFamily: 'monospace' }}
            />
          </Form.Item>
          
          <Form.Item
            name="module"
            label="Module"
            rules={[
              { required: true, message: 'Please select or enter module' }
            ]}
          >
            <Select
              placeholder="Select or type module name"
              showSearch
              allowClear
              mode="tags"
              maxTagCount={1}
              options={modules.map(module => ({ label: module, value: module }))}
            />
          </Form.Item>
          
          <Form.Item
            name="description"
            label="Description"
            rules={[
              { required: true, message: 'Please enter description' }
            ]}
          >
            <Input.TextArea 
              rows={3} 
              placeholder="Describe what this permission allows users to do"
            />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
} 