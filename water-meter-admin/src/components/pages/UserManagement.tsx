'use client'

import React, { useState, useEffect } from 'react'
import { 
  Table, 
  Card, 
  Button, 
  Space, 
  Tag, 
  Modal, 
  Form, 
  Input, 
  message, 
  Popconfirm,
  Typography,
  Row,
  Col,
  Statistic,
  Transfer,
  Tooltip,
  Progress,
  Alert,
  Spin
} from 'antd'
import { 
  UserOutlined, 
  EditOutlined, 
  DeleteOutlined, 
  SafetyOutlined,
  TeamOutlined,
  UserAddOutlined,
  UsergroupAddOutlined,
  SyncOutlined,
  CloudDownloadOutlined,
  ReloadOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons'
import { api } from '@/services/api'
import UserRoleApi from '@/api/userRoleApi'
import PaginatedTable from '@/components/common/PaginatedTable'
import type { Key } from 'react'

const { Title, Text } = Typography

interface User {
  id: number
  username: string
  fullName: string
  personId: number
  finCoCode: string
  isAuthenticated: boolean
  lastLogin: string | null
  createdDate: string
  updatedDate: string
  roles: Array<{ id: number; name: string }>
  // New staff sync fields
  mobilePhone?: string
  profitCentreCode?: string
  employeeNo?: string
  email?: string
}

interface Role {
  id: number
  name: string
  description: string
}

interface UserStatistics {
  totalUsers: number
  authenticatedUsers: number
  recentLogins: number
  inactiveUsers: number
}

interface StaffSyncResult {
  success: boolean
  message: string
  totalProcessed: number
  successCount: number
  failureCount: number
  errors: string[]
  syncTime: string
}

export default function UserManagement() {
  const [users, setUsers] = useState<User[]>([])
  const [roles, setRoles] = useState<Role[]>([])
  const [statistics, setStatistics] = useState<UserStatistics | null>(null)
  const [loading, setLoading] = useState(false)
  const [syncLoading, setSyncLoading] = useState(false)
  const [editModalVisible, setEditModalVisible] = useState(false)
  const [roleModalVisible, setRoleModalVisible] = useState(false)
  const [syncModalVisible, setSyncModalVisible] = useState(false)
  const [syncResult, setSyncResult] = useState<StaffSyncResult | null>(null)
  const [currentUser, setCurrentUser] = useState<User | null>(null)
  const [editForm] = Form.useForm()
  const [targetKeys, setTargetKeys] = useState<Key[]>([])
  
  // Load initial data
  useEffect(() => {
    fetchUsers()
    fetchRoles()
    fetchStatistics()
  }, [])

  const fetchUsers = async () => {
    setLoading(true)
    try {
      const response = await api.get('/user')
      setUsers(response.data)
    } catch (error) {
      message.error('Failed to load users')
      console.error('Error fetching users:', error)
    } finally {
      setLoading(false)
    }
  }

  const fetchRoles = async () => {
    try {
      const response = await UserRoleApi.getAvailableRoles()
      if (response.success) {
        setRoles(response.data || [])
      } else {
        message.error('Failed to load roles')
      }
    } catch (error) {
      message.error('Failed to load roles')
      console.error('Error fetching roles:', error)
    }
  }

  const fetchStatistics = async () => {
    try {
      const response = await api.get('/user/statistics')
      setStatistics(response.data)
    } catch (error) {
      console.error('Error fetching statistics:', error)
    }
  }

  const handleSyncStaff = () => {
    Modal.confirm({
      title: 'Sync Staff Data',
      content: (
        <div>
          <p>This will synchronize staff data from the Workbench system to update user information including:</p>
          <ul style={{ marginTop: 8, marginBottom: 8 }}>
            <li>Employee numbers</li>
            <li>Mobile phone numbers</li>
            <li>Profit centre codes</li>
            <li>Email addresses</li>
          </ul>
          <p>This process may take a few minutes. Do you want to continue?</p>
        </div>
      ),
      icon: <SyncOutlined />,
      okText: 'Start Sync',
      cancelText: 'Cancel',
      onOk: performStaffSync,
      okButtonProps: {
        icon: <CloudDownloadOutlined />
      }
    })
  }

  const performStaffSync = async () => {
    setSyncLoading(true)
    setSyncModalVisible(true)
    setSyncResult(null)
    
    try {
      const response = await api.post('/staffsync/sync')
      const result: StaffSyncResult = response.data
      
      setSyncResult(result)
      
      if (result.success) {
        message.success(`Staff sync completed successfully! ${result.successCount} records processed.`)
      } else {
        message.warning(`Staff sync completed with issues. ${result.successCount} successful, ${result.failureCount} failed.`)
      }
      
      // Always refresh user data after sync, regardless of success/failure
      // as some records might have been processed successfully
      await fetchUsers()
      await fetchStatistics()
      
    } catch (error: any) {
      const errorResult: StaffSyncResult = {
        success: false,
        message: error.response?.data?.message || 'Staff synchronization failed',
        totalProcessed: 0,
        successCount: 0,
        failureCount: 1,
        errors: [error.response?.data?.message || error.message || 'Unknown error'],
        syncTime: new Date().toISOString()
      }
      setSyncResult(errorResult)
      message.error('Staff synchronization failed')
      console.error('Error syncing staff:', error)
      
      // Even on error, try to refresh data in case some records were processed
      try {
        await fetchUsers()
        await fetchStatistics()
      } catch (refreshError) {
        console.error('Error refreshing data after sync failure:', refreshError)
      }
    } finally {
      setSyncLoading(false)
    }
  }

  const handleEditUser = (user: User) => {
    setCurrentUser(user)
    editForm.setFieldsValue({
      fullName: user.fullName,
      finCoCode: user.finCoCode,
      email: user.email,
      mobilePhone: user.mobilePhone,
      employeeNo: user.employeeNo
    })
    setEditModalVisible(true)
  }

  const handleEditSubmit = async () => {
    try {
      const values = await editForm.validateFields()
      if (!currentUser) return

      await api.put(`/user/${currentUser.id}`, values)
      message.success('User updated successfully')
      setEditModalVisible(false)
      fetchUsers()
    } catch (error) {
      message.error('Failed to update user')
      console.error('Error updating user:', error)
    }
  }

  const handleManageRoles = async (user: User) => {
    setCurrentUser(user)
    try {
      const response = await UserRoleApi.getUserRoles(user.id)
      if (response.success) {
        const userRoleIds = (response.data || []).map((userRole) => userRole.roleId.toString())
        setTargetKeys(userRoleIds)
        setRoleModalVisible(true)
      } else {
        message.error('Failed to load user roles')
      }
    } catch (error) {
      message.error('Failed to load user roles')
      console.error('Error fetching user roles:', error)
    }
  }

  const handleRoleAssignment = async () => {
    try {
      if (!currentUser) return

      const roleIds = targetKeys.map(key => parseInt(key.toString()))
      const response = await UserRoleApi.assignRolesToUser(currentUser.id, { roleIds })

      if (response.success) {
        message.success('Roles assigned successfully')
        setRoleModalVisible(false)
        fetchUsers()
      } else {
        message.error(response.message || 'Failed to assign roles')
      }
    } catch (error) {
      message.error('Failed to assign roles')
      console.error('Error assigning roles:', error)
    }
  }

  const handleDeleteUser = async (userId: number) => {
    try {
      await api.delete(`/user/${userId}`)
      message.success('User deleted successfully')
      fetchUsers()
      fetchStatistics()
    } catch (error: any) {
      message.error(error.response?.data?.message || 'Failed to delete user')
      console.error('Error deleting user:', error)
    }
  }

  const getRoleColor = (roleName: string) => {
    switch (roleName) {
      case 'Administrator': return 'red'
      case 'Manager': return 'orange'
      case 'Operator': return 'blue'
      default: return 'default'
    }
  }

  const columns = [
    {
      title: 'User ID',
      dataIndex: 'id',
      key: 'id',
      width: 80
    },
    {
      title: 'Username',
      dataIndex: 'username',
      key: 'username',
      render: (text: string, record: User) => (
        <Space>
          <UserOutlined />
          <span>{text}</span>
          {record.username === 'admin' && (
            <Tag color="gold">ADMIN</Tag>
          )}
        </Space>
      )
    },
    {
      title: 'Full Name',
      dataIndex: 'fullName',
      key: 'fullName'
    },
    {
      title: 'Person ID',
      dataIndex: 'personId',
      key: 'personId'
    },
    {
      title: 'Email',
      dataIndex: 'email',
      key: 'email',
      render: (email: string) => email || '-'
    },
    {
      title: 'Mobile Phone',
      dataIndex: 'mobilePhone',
      key: 'mobilePhone',
      render: (phone: string) => phone || '-'
    },
    {
      title: 'Employee No',
      dataIndex: 'employeeNo',
      key: 'employeeNo',
      render: (empNo: string) => empNo || '-'
    },
    {
      title: 'Company Code',
      dataIndex: 'finCoCode',
      key: 'finCoCode'
    },
    {
      title: 'Roles',
      dataIndex: 'roles',
      key: 'roles',
      render: (roles: Array<{ id: number; name: string }>) => (
        <Space wrap>
          {roles.map(role => (
            <Tag key={role.id} color={getRoleColor(role.name)}>
              {role.name}
            </Tag>
          ))}
        </Space>
      )
    },
    {
      title: 'Status',
      dataIndex: 'isAuthenticated',
      key: 'isAuthenticated',
      render: (isAuthenticated: boolean) => (
        <Tag color={isAuthenticated ? 'green' : 'red'}>
          {isAuthenticated ? 'Active' : 'Inactive'}
        </Tag>
      )
    },
    {
      title: 'Last Login',
      dataIndex: 'lastLogin',
      key: 'lastLogin',
      render: (lastLogin: string | null) => 
        lastLogin ? new Date(lastLogin).toLocaleString() : 'Never'
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_: any, record: User) => (
        <Space>
          <Tooltip title="Edit User">
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => handleEditUser(record)}
            />
          </Tooltip>
          <Tooltip title="Manage Roles">
            <Button
              type="text"
              icon={<SafetyOutlined />}
              onClick={() => handleManageRoles(record)}
            />
          </Tooltip>
          {record.username !== 'admin' && (
            <Tooltip title="Delete User">
              <Popconfirm
                title="Are you sure you want to delete this user?"
                description="This action cannot be undone."
                onConfirm={() => handleDeleteUser(record.id)}
                okText="Yes"
                cancelText="No"
              >
                <Button
                  type="text"
                  danger
                  icon={<DeleteOutlined />}
                />
              </Popconfirm>
            </Tooltip>
          )}
        </Space>
      )
    }
  ]

  const transferData = roles.map(role => ({
    key: role.id.toString(),
    title: role.name,
    description: role.description
  }))

  return (
    <div>
      <div className="flex justify-end items-center mb-6">
      </div>

      {/* Statistics Cards */}
      {statistics && (
        <Row gutter={16} className="mb-6">
          <Col span={6}>
            <Card>
              <Statistic
                title="Total Users"
                value={statistics.totalUsers}
                prefix={<UserOutlined />}
                valueStyle={{ color: '#1890ff' }}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="Active Users"
                value={statistics.authenticatedUsers}
                prefix={<UserAddOutlined />}
                valueStyle={{ color: '#52c41a' }}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="Recent Logins"
                value={statistics.recentLogins}
                prefix={<UsergroupAddOutlined />}
                valueStyle={{ color: '#faad14' }}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="Inactive Users"
                value={statistics.inactiveUsers}
                prefix={<UserOutlined />}
                valueStyle={{ color: '#ff4d4f' }}
              />
            </Card>
          </Col>
        </Row>
      )}

      <Card>
        <div className="flex justify-between items-center mb-4">
          <Text strong>User List</Text>
          <Space>
            <Button 
              icon={<SyncOutlined />}
              onClick={handleSyncStaff}
              loading={syncLoading}
            >
              Sync Staff
            </Button>
            <Button 
              type="primary" 
              icon={<ReloadOutlined />}
              onClick={fetchUsers}
            >
              Refresh
            </Button>
          </Space>
        </div>

        <PaginatedTable
          columns={columns}
          dataSource={users}
          loading={loading}
          rowKey="id"
          scroll={{ x: 1200 }}
          paginationOptions={{
            defaultPageSize: 10,
            showTotal: true
          }}
        />
      </Card>

      {/* Edit User Modal */}
      <Modal
        title="Edit User"
        open={editModalVisible}
        onOk={handleEditSubmit}
        onCancel={() => setEditModalVisible(false)}
        okText="Save"
        cancelText="Cancel"
      >
        <Form
          form={editForm}
          layout="vertical"
        >
          <Form.Item
            label="Full Name"
            name="fullName"
            rules={[{ required: true, message: 'Please input full name!' }]}
          >
            <Input placeholder="Enter full name" />
          </Form.Item>
          <Form.Item
            label="Email"
            name="email"
            rules={[{ type: 'email', message: 'Please enter a valid email!' }]}
          >
            <Input placeholder="Enter email address" />
          </Form.Item>
          <Form.Item
            label="Mobile Phone"
            name="mobilePhone"
          >
            <Input placeholder="Enter mobile phone number" />
          </Form.Item>
          <Form.Item
            label="Employee No"
            name="employeeNo"
          >
            <Input placeholder="Enter employee number" />
          </Form.Item>
          <Form.Item
            label="Company Code"
            name="finCoCode"
            rules={[{ required: true, message: 'Please input company code!' }]}
          >
            <Input placeholder="Enter company code" />
          </Form.Item>
        </Form>
      </Modal>

      {/* Role Assignment Modal */}
      <Modal
        title={`Manage Roles - ${currentUser?.fullName}`}
        open={roleModalVisible}
        onOk={handleRoleAssignment}
        onCancel={() => setRoleModalVisible(false)}
        okText="Save Roles"
        cancelText="Cancel"
        width={600}
      >
        <div className="mb-4">
          <Text type="secondary">
            Select roles to assign to this user. Move roles between the lists to assign or remove them.
          </Text>
        </div>
        <Transfer
          dataSource={transferData}
          titles={['Available Roles', 'Assigned Roles']}
          targetKeys={targetKeys}
          onChange={setTargetKeys}
          render={item => `${item.title} - ${item.description}`}
          listStyle={{
            width: 250,
            height: 300,
          }}
        />
      </Modal>

      {/* Staff Sync Result Modal */}
      <Modal
        title="Staff Synchronization Result"
        open={syncModalVisible}
        onCancel={() => setSyncModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setSyncModalVisible(false)}>
            Close
          </Button>
        ]}
        width={600}
      >
        {syncLoading ? (
          <div className="text-center py-8">
            <Spin size="large" />
            <div className="mt-4">
              <Text>Synchronizing staff data from Workbench...</Text>
            </div>
            <div className="mt-2">
              <Text type="secondary">This may take a few minutes, please wait...</Text>
            </div>
          </div>
        ) : syncResult ? (
          <div>
            <Alert
              message={syncResult.success ? "Synchronization Completed" : "Synchronization Completed with Issues"}
              description={syncResult.message}
              type={syncResult.success ? "success" : "warning"}
              icon={syncResult.success ? <CheckCircleOutlined /> : <ExclamationCircleOutlined />}
              showIcon
              className="mb-4"
            />
            
            <Row gutter={16} className="mb-4">
              <Col span={6}>
                <Statistic
                  title="Total Processed"
                  value={syncResult.totalProcessed}
                  valueStyle={{ color: '#1890ff' }}
                />
              </Col>
              <Col span={6}>
                <Statistic
                  title="Successful"
                  value={syncResult.successCount}
                  valueStyle={{ color: '#52c41a' }}
                />
              </Col>
              <Col span={6}>
                <Statistic
                  title="Failed"
                  value={syncResult.failureCount}
                  valueStyle={{ color: '#ff4d4f' }}
                />
              </Col>
              <Col span={6}>
                <Statistic
                  title="Success Rate"
                  value={syncResult.totalProcessed > 0 ? Math.round((syncResult.successCount / syncResult.totalProcessed) * 100) : 0}
                  suffix="%"
                  valueStyle={{ color: syncResult.successCount === syncResult.totalProcessed ? '#52c41a' : '#faad14' }}
                />
              </Col>
            </Row>

            {syncResult.totalProcessed > 0 && (
              <div className="mb-4">
                <Text strong>Progress:</Text>
                <Progress
                  percent={Math.round((syncResult.successCount / syncResult.totalProcessed) * 100)}
                  success={{ percent: Math.round((syncResult.successCount / syncResult.totalProcessed) * 100) }}
                  strokeColor={{
                    '0%': '#52c41a',
                    '100%': '#52c41a'
                  }}
                />
              </div>
            )}

            {syncResult.errors.length > 0 && (
              <div>
                <Text strong className="text-red-500">Errors:</Text>
                <div className="mt-2 max-h-32 overflow-y-auto bg-red-50 p-3 rounded">
                  {syncResult.errors.map((error, index) => (
                    <div key={index} className="text-sm text-red-600 mb-1">
                      • {error}
                    </div>
                  ))}
                </div>
              </div>
            )}
            
            <div className="mt-4 text-right">
              <Text type="secondary">
                Sync completed at: {new Date(syncResult.syncTime).toLocaleString()}
              </Text>
            </div>
          </div>
        ) : null}
      </Modal>
    </div>
  )
} 