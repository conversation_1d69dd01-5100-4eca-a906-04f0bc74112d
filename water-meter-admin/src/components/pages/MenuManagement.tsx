'use client'

import { Table, Button, Space, Modal, Form, Input, message, Select, Checkbox } from 'antd'
import { PlusOutlined, EditOutlined, DeleteOutlined } from '@ant-design/icons'
import { useState, useEffect } from 'react'
import { api } from '../../services/api'

interface MenuItem {
  id: number
  name: string
  path: string
  code: string
  component: string
  icon?: string
  parentId?: number
  order: number
  isVisible: boolean
  isDeleted: boolean
  children?: MenuItem[]
}

export default function MenuManagement() {
  const [menus, setMenus] = useState<MenuItem[]>([])
  const [loading, setLoading] = useState(false)
  const [modalVisible, setModalVisible] = useState(false)
  const [form] = Form.useForm()
  const [editingId, setEditingId] = useState<number | null>(null)

  const fetchMenus = async () => {
    setLoading(true)
    try {
      const response = await api.get('/menu')
      setMenus(response.data)
    } catch (error) {
      message.error('Failed to fetch menus')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchMenus()
  }, [])

  const handleAdd = () => {
    setEditingId(null)
    form.resetFields()
    setModalVisible(true)
  }

  const handleEdit = (record: MenuItem) => {
    setEditingId(record.id)
    form.setFieldsValue({
      ...record,
      isVisible: record.isVisible
    })
    setModalVisible(true)
  }

  const handleDelete = async (id: number) => {
    try {
      await api.delete(`/menu/${id}`)
      message.success('Menu deleted successfully')
      fetchMenus()
    } catch (error) {
      message.error('Failed to delete menu')
    }
  }

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields()
      
      // Convert parentId to number or null
      const submitData = {
        ...values,
        parentId: values.parentId ? parseInt(values.parentId) : null
      }
      
      if (editingId) {
        await api.put(`/menu/${editingId}`, submitData)
        message.success('Menu updated successfully')
      } else {
        await api.post('/menu', submitData)
        message.success('Menu created successfully')
      }
      setModalVisible(false)
      fetchMenus()
    } catch (error) {
      message.error('Failed to save menu')
    }
  }

  // Get parent menu options (only top-level menus)
  const getParentOptions = () => {
    return menus
      .filter(menu => !menu.parentId && menu.id !== editingId)
      .map(menu => ({
        label: menu.name,
        value: menu.id
      }))
  }

  const columns = [
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: MenuItem) => (
        <span style={{ paddingLeft: record.parentId ? '20px' : '0px' }}>
          {record.parentId && '└─ '}{text}
        </span>
      )
    },
    {
      title: 'Code',
      dataIndex: 'code',
      key: 'code',
    },
    {
      title: 'Path',
      dataIndex: 'path',
      key: 'path',
    },
    {
      title: 'Component',
      dataIndex: 'component',
      key: 'component',
    },
    {
      title: 'Icon',
      dataIndex: 'icon',
      key: 'icon',
    },
    {
      title: 'Parent',
      dataIndex: 'parentId',
      key: 'parentId',
      render: (parentId: number) => {
        if (!parentId) return '-'
        const parent = menus.find(m => m.id === parentId)
        return parent ? parent.name : '-'
      }
    },
    {
      title: 'Order',
      dataIndex: 'order',
      key: 'order',
    },
    {
      title: 'Status',
      dataIndex: 'isVisible',
      key: 'isVisible',
      render: (isVisible: boolean) => (
        <span className={isVisible ? 'text-green-500' : 'text-red-500'}>
          {isVisible ? 'Visible' : 'Hidden'}
        </span>
      ),
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_: any, record: MenuItem) => (
        <Space>
          <Button
            type="primary"
            size="small"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            Edit
          </Button>
          <Button
            danger
            size="small"
            icon={<DeleteOutlined />}
            onClick={() => handleDelete(record.id)}
          >
            Delete
          </Button>
        </Space>
      ),
    },
  ]

  return (
    <div>
      <div className="mb-4">
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={handleAdd}
        >
          Add Menu
        </Button>
      </div>
      <Table
        columns={columns}
        dataSource={menus}
        loading={loading}
        rowKey="id"
        pagination={false}
        size="small"
      />
      <Modal
        title={editingId ? 'Edit Menu' : 'Add Menu'}
        open={modalVisible}
        onOk={handleSubmit}
        onCancel={() => setModalVisible(false)}
        width={600}
      >
        <Form form={form} layout="vertical">
          <Form.Item
            name="name"
            label="Menu Name"
            rules={[{ required: true, message: 'Please input menu name!' }]}
          >
            <Input placeholder="e.g. User Management" />
          </Form.Item>
          
          <Form.Item
            name="code"
            label="Menu Code"
            rules={[{ required: true, message: 'Please input menu code!' }]}
          >
            <Input placeholder="e.g. user-management" />
          </Form.Item>

          <Form.Item
            name="parentId"
            label="Parent Menu"
          >
            <Select
              placeholder="Select parent menu (leave empty for top-level)"
              allowClear
              options={getParentOptions()}
            />
          </Form.Item>
          
          <Form.Item
            name="path"
            label="Path"
            rules={[{ required: true, message: 'Please input menu path!' }]}
          >
            <Input placeholder="e.g. user-management" />
          </Form.Item>
          
          <Form.Item
            name="component"
            label="Component"
            rules={[{ required: true, message: 'Please input component name!' }]}
          >
            <Input placeholder="e.g. UserManagement" />
          </Form.Item>
          
          <Form.Item
            name="icon"
            label="Icon"
          >
            <Input placeholder="e.g. user" />
          </Form.Item>
          
          <Form.Item
            name="order"
            label="Display Order"
            rules={[{ required: true, message: 'Please input order!' }]}
          >
            <Input type="number" placeholder="e.g. 1" />
          </Form.Item>
          
          <Form.Item
            name="isVisible"
            valuePropName="checked"
          >
            <Checkbox>Visible in menu</Checkbox>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
} 