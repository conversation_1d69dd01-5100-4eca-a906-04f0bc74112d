import React, { useState, useEffect } from 'react';
import { Select, Spin } from 'antd';
import { UserOutlined } from '@ant-design/icons';
import api from '@/services/api';

const { Option } = Select;

interface User {
  id: number;
  username: string;
  fullName: string;
  email?: string;
}

interface UserSelectProps {
  value?: number;
  onChange?: (value: number) => void;
  placeholder?: string;
  allowClear?: boolean;
  style?: React.CSSProperties;
}

const UserSelect: React.FC<UserSelectProps> = ({
  value,
  onChange,
  placeholder = "Select user",
  allowClear = true,
  style
}) => {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    fetchUsers();
  }, []);

  const fetchUsers = async () => {
    setLoading(true);
    try {
      const response = await api.get('/user');
      setUsers(response.data || []);
    } catch (error) {
      console.error('Error fetching users:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Select
      value={value}
      onChange={onChange}
      placeholder={placeholder}
      allowClear={allowClear}
      style={style}
      loading={loading}
      showSearch
      filterOption={(input, option) =>
        (option?.children as string)?.toLowerCase().includes(input.toLowerCase())
      }
      suffixIcon={<UserOutlined />}
    >
      {users.map(user => (
        <Option key={user.id} value={user.id}>
          {user.fullName || user.username}
        </Option>
      ))}
    </Select>
  );
};

export default UserSelect;
