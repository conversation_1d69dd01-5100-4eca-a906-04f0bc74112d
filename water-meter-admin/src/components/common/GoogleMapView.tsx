'use client';

import React, { useEffect, useRef, useState, useCallback } from 'react';
import { Wrapper, Status } from '@googlemaps/react-wrapper';
import { Spin, Alert } from 'antd';
import { GOOGLE_MAPS_API_KEY } from '@/config/maps';

export interface MapMarker {
  id: string;
  position: {
    lat: number;
    lng: number;
  };
  title: string;
  info?: {
    description?: string;
    status?: string;
    priority?: string;
    type?: string;
    location?: string;
    meterSerial?: string;
  };
  selected?: boolean;
}

export interface MapPath {
  id: string;
  coordinates: { lat: number; lng: number }[];
  color?: string;
  strokeWeight?: number;
  strokeOpacity?: number;
  strokeDashArray?: string;
}

interface GoogleMapViewProps {
  markers: MapMarker[];
  paths?: MapPath[];
  onMarkerClick?: (marker: MapMarker) => void;
  selectedMarkerId?: string;
  zoom?: number;
  center?: { lat: number; lng: number };
  height?: string | number;
  width?: string | number;
  showInfoWindowOnly?: boolean; // If true, only show info window, don't call onMarkerClick
}

// Default center coordinates (you can adjust this to your area)
const DEFAULT_CENTER = { lat: -43.5320, lng: 172.6306 }; // Christchurch, NZ

const MapComponent: React.FC<GoogleMapViewProps> = ({
  markers,
  paths = [],
  onMarkerClick,
  selectedMarkerId,
  zoom = 12,
  center = DEFAULT_CENTER,
  height = '100%',
  width = '100%',
  showInfoWindowOnly = false
}) => {
  const mapRef = useRef<HTMLDivElement>(null);
  const mapInstanceRef = useRef<google.maps.Map | null>(null);
  const markersRef = useRef<Map<string, google.maps.Marker>>(new Map());
  const pathsRef = useRef<Map<string, google.maps.Polyline>>(new Map());
  const infoWindowRef = useRef<google.maps.InfoWindow | null>(null);
  const infoWindowProtectionRef = useRef<boolean>(false);
  const lastCenterRef = useRef<{ lat: number; lng: number } | null>(null);

  // Initialize map
  useEffect(() => {
    if (!mapRef.current || mapInstanceRef.current) return;

    mapInstanceRef.current = new google.maps.Map(mapRef.current, {
      center,
      zoom,
      mapTypeControl: true,
      streetViewControl: true,
      fullscreenControl: true,
      zoomControl: true,
    });

    // Create info window
    infoWindowRef.current = new google.maps.InfoWindow();

    // Add map click listener to close info window
    mapInstanceRef.current.addListener('click', () => {
      if (infoWindowRef.current) {
        infoWindowRef.current.close();
      }
    });
  }, [center, zoom]);

  // Update map center and zoom when props change (but only if significantly different)
  useEffect(() => {
    if (mapInstanceRef.current) {
      // Check if center actually changed from our last known value
      const centerChanged = !lastCenterRef.current ||
        Math.abs(lastCenterRef.current.lat - center.lat) > 0.001 ||
        Math.abs(lastCenterRef.current.lng - center.lng) > 0.001;

      if (centerChanged) {
        mapInstanceRef.current.setCenter(center);
        lastCenterRef.current = { ...center };
      }

      const currentZoom = mapInstanceRef.current.getZoom();
      if (currentZoom !== zoom) {
        mapInstanceRef.current.setZoom(zoom);
      }
    }
  }, [center, zoom]);

  // Update markers
  useEffect(() => {
    if (!mapInstanceRef.current) return;

    const map = mapInstanceRef.current;
    const existingMarkers = markersRef.current;

    // Clear existing markers
    existingMarkers.forEach(marker => marker.setMap(null));
    existingMarkers.clear();

    // Add new markers
    markers.forEach(markerData => {
      const marker = new google.maps.Marker({
        position: markerData.position,
        map,
        title: markerData.title,
        icon: {
          url: getMarkerIcon(markerData),
          scaledSize: new google.maps.Size(32, 32),
          anchor: new google.maps.Point(16, 32)
        }
      });

      // Add click listener
      marker.addListener('click', (event: google.maps.MapMouseEvent) => {
        // Prevent event bubbling to map
        if (event) {
          event.stop?.();
        }

        // Always show info window first for immediate feedback
        if (infoWindowRef.current) {
          // Set protection flag to prevent accidental closure
          infoWindowProtectionRef.current = true;

          const content = createInfoWindowContent(markerData);
          infoWindowRef.current.setContent(content);
          infoWindowRef.current.open(map, marker);

          // Remove protection after a short delay
          setTimeout(() => {
            infoWindowProtectionRef.current = false;
          }, 300);
        }

        // Only call the callback if not in info-window-only mode
        if (!showInfoWindowOnly && onMarkerClick) {
          // Use setTimeout to prevent immediate re-render that could close the info window
          setTimeout(() => {
            onMarkerClick(markerData);
          }, 100); // Slightly longer delay for stability
        }
      });

      existingMarkers.set(markerData.id, marker);
    });

    // Fit bounds if there are markers
    if (markers.length > 0) {
      const bounds = new google.maps.LatLngBounds();
      markers.forEach(marker => bounds.extend(marker.position));
      map.fitBounds(bounds);

      // Ensure minimum zoom level
      google.maps.event.addListenerOnce(map, 'bounds_changed', () => {
        const currentZoom = map.getZoom();
        if (currentZoom && currentZoom > 15) {
          map.setZoom(15);
        }
      });
    }
  }, [markers, onMarkerClick, showInfoWindowOnly]);

  // Update selected marker style
  useEffect(() => {
    const existingMarkers = markersRef.current;
    
    existingMarkers.forEach((marker, markerId) => {
      const markerData = markers.find(m => m.id === markerId);
      if (markerData) {
        const isSelected = markerId === selectedMarkerId;
        marker.setIcon({
          url: getMarkerIcon(markerData, isSelected),
          scaledSize: new google.maps.Size(isSelected ? 40 : 32, isSelected ? 40 : 32),
          anchor: new google.maps.Point(isSelected ? 20 : 16, isSelected ? 40 : 32)
        });
      }
    });
  }, [selectedMarkerId, markers]);

  // Handle paths rendering
  useEffect(() => {
    const map = mapInstanceRef.current;
    if (!map) return;

    // Clear existing paths
    pathsRef.current.forEach(path => path.setMap(null));
    pathsRef.current.clear();

    // Add new paths
    paths.forEach(pathData => {
      const polyline = new google.maps.Polyline({
        path: pathData.coordinates,
        geodesic: true,
        strokeColor: pathData.color || '#1890ff',
        strokeOpacity: pathData.strokeOpacity || 0.8,
        strokeWeight: pathData.strokeWeight || 3,
      });

      polyline.setMap(map);
      pathsRef.current.set(pathData.id, polyline);
    });

    // Fit bounds to include both markers and paths
    if (markers.length > 0 || paths.length > 0) {
      const bounds = new google.maps.LatLngBounds();

      // Add marker positions to bounds
      markers.forEach(marker => bounds.extend(marker.position));

      // Add path coordinates to bounds
      paths.forEach(path => {
        path.coordinates.forEach(coord => bounds.extend(coord));
      });

      if (!bounds.isEmpty()) {
        map.fitBounds(bounds);

        // Ensure minimum zoom level
        google.maps.event.addListenerOnce(map, 'bounds_changed', () => {
          const currentZoom = map.getZoom();
          if (currentZoom && currentZoom > 15) {
            map.setZoom(15);
          }
        });
      }
    }
  }, [paths, markers]);

  const getMarkerIcon = (marker: MapMarker, selected = false) => {
    const priority = marker.info?.priority || 'Medium';
    const status = marker.info?.status || 'Active';
    const type = marker.info?.type || '';

    let color = '#1890ff'; // Default blue

    // Color by status for water meters
    if (status === 'Active') color = '#52c41a'; // Green for active
    else if (status === 'Inactive') color = '#ff4d4f'; // Red for inactive
    else if (status === 'Maintenance') color = '#fa8c16'; // Orange for maintenance
    else if (status === 'Completed') color = '#52c41a'; // Green for completed tasks
    else if (status === 'InProgress' || status === 'In Progress') color = '#722ed1'; // Purple for in progress
    else if (status === 'Pending') color = '#1890ff'; // Blue for pending

    // Override with priority colors if it's a task
    if (priority && priority !== 'Medium') {
      if (priority === 'Critical') color = '#ff4d4f';
      else if (priority === 'High') color = '#fa8c16';
      else if (priority === 'Low') color = '#52c41a';
    }

    const size = selected ? 36 : 28;
    const iconSize = selected ? 18 : 14;

    // Create SVG marker with water meter icon
    const svg = `
      <svg width="${size}" height="${size}" viewBox="0 0 ${size} ${size}" xmlns="http://www.w3.org/2000/svg">
        <!-- Outer circle -->
        <circle cx="${size/2}" cy="${size/2}" r="${size/2 - 2}"
                fill="${color}"
                stroke="${selected ? '#ffffff' : '#ffffff'}"
                stroke-width="${selected ? '3' : '2'}"
                opacity="0.9"/>

        <!-- Water meter icon -->
        <g transform="translate(${(size-iconSize)/2}, ${(size-iconSize)/2})">
          <!-- Meter body -->
          <rect x="2" y="3" width="${iconSize-4}" height="${iconSize-6}"
                fill="white"
                rx="1"
                stroke="${color}"
                stroke-width="0.5"/>

          <!-- Display screen -->
          <rect x="3" y="4" width="${iconSize-6}" height="3"
                fill="${color}"
                opacity="0.3"/>

          <!-- Water drop -->
          <path d="M${iconSize/2} ${iconSize-4}
                   C${iconSize/2-1} ${iconSize-3}, ${iconSize/2-1} ${iconSize-2}, ${iconSize/2} ${iconSize-1.5}
                   C${iconSize/2+1} ${iconSize-2}, ${iconSize/2+1} ${iconSize-3}, ${iconSize/2} ${iconSize-4} Z"
                fill="white"/>
        </g>

        ${selected ? `<circle cx="${size/2}" cy="${size/2}" r="${size/2 - 1}" fill="none" stroke="#ffffff" stroke-width="2" opacity="0.8"/>` : ''}
      </svg>
    `;

    return `data:image/svg+xml;charset=UTF-8,${encodeURIComponent(svg)}`;
  };

  const createInfoWindowContent = (marker: MapMarker) => {
    const { title, info } = marker;

    return `
      <div style="padding: 14px; max-width: 320px; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.4;">
        <!-- Header with water meter icon -->
        <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 10px;">
          <div style="width: 24px; height: 24px; background: #1890ff; border-radius: 50%; display: flex; align-items: center; justify-content: center;">
            <span style="color: white; font-size: 12px;">💧</span>
          </div>
          <h4 style="margin: 0; font-size: 16px; font-weight: 600; color: #1f2937;">${title}</h4>
        </div>

        ${info?.location ? `
          <div style="margin: 8px 0; display: flex; align-items: flex-start; gap: 8px;">
            <span style="color: #6b7280; font-size: 14px; margin-top: 1px;">📍</span>
            <span style="color: #374151; font-size: 14px; flex: 1;">${info.location}</span>
          </div>
        ` : ''}

        ${info?.description && info.description !== info.location ? `
          <div style="margin: 8px 0; color: #6b7280; font-size: 13px; font-style: italic;">
            ${info.description}
          </div>
        ` : ''}

        <!-- Meter details -->
        <div style="background: #f8fafc; padding: 10px; border-radius: 6px; margin: 10px 0;">
          ${info?.meterSerial ? `
            <div style="margin: 4px 0; display: flex; justify-content: space-between;">
              <span style="color: #6b7280; font-size: 12px; font-weight: 500;">Serial Number:</span>
              <span style="color: #1f2937; font-size: 12px; font-weight: 600;">${info.meterSerial}</span>
            </div>
          ` : ''}

          ${info?.type ? `
            <div style="margin: 4px 0; display: flex; justify-content: space-between;">
              <span style="color: #6b7280; font-size: 12px; font-weight: 500;">Type:</span>
              <span style="color: #1f2937; font-size: 12px;">${info.type}</span>
            </div>
          ` : ''}
        </div>

        <!-- Status badges -->
        <div style="display: flex; gap: 6px; margin-top: 12px; flex-wrap: wrap;">
          ${info?.status ? `<span style="background: ${getStatusColor(info.status)}; color: white; padding: 4px 10px; border-radius: 14px; font-size: 11px; font-weight: 600; text-transform: uppercase; letter-spacing: 0.5px;">${info.status}</span>` : ''}
          ${info?.priority && info.priority !== 'Medium' ? `<span style="background: ${getPriorityColor(info.priority)}; color: white; padding: 4px 10px; border-radius: 14px; font-size: 11px; font-weight: 600;">${info.priority} Priority</span>` : ''}
        </div>
      </div>
    `;
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      // Water meter statuses
      case 'active': return '#10b981'; // Green
      case 'inactive': return '#ef4444'; // Red
      case 'maintenance': return '#f59e0b'; // Orange
      case 'replaced': return '#6b7280'; // Gray

      // Task statuses
      case 'completed': return '#10b981'; // Green
      case 'inprogress': case 'in progress': return '#8b5cf6'; // Purple
      case 'pending': return '#3b82f6'; // Blue
      case 'cancelled': return '#ef4444'; // Red
      case 'failed': return '#dc2626'; // Dark red

      default: return '#6b7280'; // Gray
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority.toLowerCase()) {
      case 'critical': return '#dc2626';
      case 'high': return '#ea580c';
      case 'medium': return '#d97706';
      case 'low': return '#65a30d';
      default: return '#6b7280';
    }
  };

  return (
    <div 
      ref={mapRef} 
      style={{ height, width }}
      className="rounded-lg border border-gray-300"
    />
  );
};

const GoogleMapView: React.FC<GoogleMapViewProps> = (props) => {
  const apiKey = GOOGLE_MAPS_API_KEY;
  // Debug: Check if API key is loaded
  React.useEffect(() => {
    // console.log('Google Maps API Key:', apiKey ? `${apiKey.substring(0, 10)}...` : 'NOT FOUND');
  }, [apiKey]);

  const render = (status: Status) => {
    switch (status) {
      case Status.LOADING:
        return <div className="flex items-center justify-center h-full"><Spin size="large" /></div>;
      case Status.FAILURE:
        return (
          <Alert 
            message="Failed to load Google Maps" 
            description={`API Key status: ${apiKey ? 'Present' : 'Missing'}. Check console for details.`}
            type="error" 
          />
        );
      case Status.SUCCESS:
        return <MapComponent {...props} />;
    }
  };

  if (!apiKey || apiKey === "YOUR_API_KEY") {
    return (
      <Alert
        message="Google Maps API Key Required"
        description="Please add NEXT_PUBLIC_GOOGLE_MAPS_API_KEY to your .env.local file"
        type="warning"
        showIcon
      />
    );
  }

  return (
    <Wrapper 
      apiKey={apiKey} 
      render={render}
      libraries={['places', 'geocoding']}
    />
  );
};

export default GoogleMapView; 