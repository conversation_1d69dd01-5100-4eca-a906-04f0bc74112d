'use client'

import React, { useState, useEffect } from 'react'
import {
  Form,
  Tabs,
  Card,
  Space,
  Button,
  Divider,
  Typo<PERSON>,
  Alert,
  message,
  Modal,
  Spin
} from 'antd'
import {
  SaveOutlined,
  ReloadOutlined,
  SettingOutlined,
  WarningOutlined,
  InfoCircleOutlined
} from '@ant-design/icons'
import type { ConfigurationFormProps } from '../../types/configuration'
import ConfigurationItem from './ConfigurationItem'

const { Title, Text } = Typography
const { TabPane } = Tabs

const ConfigurationForm: React.FC<ConfigurationFormProps> = ({
  scope,
  categories,
  loading = false,
  onSave,
  onReset,
  userId,
  roleId
}) => {
  const [form] = Form.useForm()
  const [activeCategory, setActiveCategory] = useState<string>('')
  const [formData, setFormData] = useState<Record<string, any>>({})
  const [hasChanges, setHasChanges] = useState(false)
  const [saving, setSaving] = useState(false)
  const [resetting, setResetting] = useState(false)

  // Initialize form data when categories change
  useEffect(() => {
    if (categories.length > 0) {
      const initialData: Record<string, any> = {}
      
      categories.forEach(category => {
        category.groups.forEach(group => {
          group.items.forEach(item => {
            initialData[item.key] = item.value !== undefined ? item.value : item.defaultValue
          })
        })
      })
      
      setFormData(initialData)
      form.setFieldsValue(initialData)
      
      if (!activeCategory && categories.length > 0) {
        setActiveCategory(categories[0].category)
      }
    }
  }, [categories, form, activeCategory])

  const handleFieldChange = (key: string, value: any) => {
    const newFormData = { ...formData, [key]: value }
    setFormData(newFormData)
    setHasChanges(true)
    form.setFieldValue(key, value)
  }

  const handleSave = async () => {
    if (!onSave || !activeCategory) return

    try {
      await form.validateFields()
      setSaving(true)

      // Get only changed values for the active category
      const activeConfig = categories.find(cat => cat.category === activeCategory)
      if (!activeConfig) return

      const categoryValues: Record<string, any> = {}
      activeConfig.groups.forEach(group => {
        group.items.forEach(item => {
          if (formData[item.key] !== undefined) {
            categoryValues[item.key] = formData[item.key]
          }
        })
      })

      await onSave(activeCategory, categoryValues)
      setHasChanges(false)
      message.success('Configuration saved successfully')
    } catch (error) {
      console.error('Save error:', error)
      message.error('Failed to save configuration')
    } finally {
      setSaving(false)
    }
  }

  const handleReset = async () => {
    if (!onReset || !activeCategory) return

    Modal.confirm({
      title: 'Reset Configuration',
      content: `Are you sure you want to reset all settings in the ${activeCategory} category to their default values?`,
      icon: <WarningOutlined />,
      okText: 'Reset',
      okType: 'danger',
      cancelText: 'Cancel',
      onOk: async () => {
        try {
          setResetting(true)
          await onReset(activeCategory)
          setHasChanges(false)
          message.success('Configuration reset to defaults')
        } catch (error) {
          console.error('Reset error:', error)
          message.error('Failed to reset configuration')
        } finally {
          setResetting(false)
        }
      }
    })
  }

  const renderCategoryContent = (category: any) => {
    if (!category) return null

    return (
      <div className="space-y-6">
        {category.groups.map((group: any) => (
          <Card
            key={group.group}
            title={
              <div className="flex items-center gap-2">
                <SettingOutlined />
                <span>{group.groupDisplayName}</span>
              </div>
            }
            size="small"
            className="mb-4"
          >
            <div className="space-y-4">
              {group.items.map((item: any) => (
                <ConfigurationItem
                  key={item.key}
                  item={item}
                  value={formData[item.key]}
                  onChange={handleFieldChange}
                  disabled={loading || saving}
                />
              ))}
            </div>
          </Card>
        ))}
      </div>
    )
  }

  const getRestartRequiredItems = () => {
    const activeConfig = categories.find(cat => cat.category === activeCategory)
    if (!activeConfig) return []

    const restartItems: string[] = []
    activeConfig.groups.forEach(group => {
      group.items.forEach(item => {
        if (item.requiresRestart && formData[item.key] !== item.value) {
          restartItems.push(item.displayName)
        }
      })
    })
    return restartItems
  }

  const renderTabBar = () => {
    return categories.map(category => (
      <TabPane
        key={category.category}
        tab={
          <div className="flex items-center gap-2">
            {category.categoryIcon && <span>{category.categoryIcon}</span>}
            <span>{category.categoryDisplayName}</span>
          </div>
        }
      />
    ))
  }

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Spin size="large" />
      </div>
    )
  }

  if (categories.length === 0) {
    return (
      <Alert
        message="No Configuration Found"
        description="No configuration categories are available for this scope."
        type="info"
        showIcon
        icon={<InfoCircleOutlined />}
      />
    )
  }

  const restartItems = getRestartRequiredItems()

  return (
    <div className="configuration-form">
      <div className="mb-4">
        <Title level={4}>{scope} Settings</Title>
        <Text type="secondary">
          Configure {scope.toLowerCase()} level settings and preferences.
        </Text>
      </div>

      {restartItems.length > 0 && (
        <Alert
          message="Restart Required"
          description={
            <div>
              The following settings require a system restart to take effect:
              <ul className="mt-2 ml-4">
                {restartItems.map((item, index) => (
                  <li key={index}>{item}</li>
                ))}
              </ul>
            </div>
          }
          type="warning"
          showIcon
          className="mb-4"
        />
      )}

      <Form form={form} layout="vertical">
        <Tabs
          activeKey={activeCategory}
          onChange={setActiveCategory}
          type="card"
          className="configuration-tabs"
        >
          {categories.map(category => (
            <TabPane
              key={category.category}
              tab={
                <div className="flex items-center gap-2">
                  <span>{category.categoryDisplayName}</span>
                </div>
              }
            >
              {renderCategoryContent(category)}
            </TabPane>
          ))}
        </Tabs>
      </Form>

      <Divider />

      <div className="flex justify-between items-center">
        <Space>
          <Button
            type="default"
            icon={<ReloadOutlined />}
            onClick={handleReset}
            loading={resetting}
            disabled={loading || saving}
          >
            Reset to Defaults
          </Button>
        </Space>

        <Space>
          <Button
            type="primary"
            icon={<SaveOutlined />}
            onClick={handleSave}
            loading={saving}
            disabled={loading || !hasChanges}
          >
            Save Changes
          </Button>
        </Space>
      </div>
    </div>
  )
}

export default ConfigurationForm 