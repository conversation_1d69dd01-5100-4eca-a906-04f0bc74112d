import React from 'react'
import { Table, TableProps } from 'antd'
import { usePagination, UsePaginationOptions } from '@/hooks/usePagination'

interface PaginatedTableProps<RecordType> extends Omit<TableProps<RecordType>, 'pagination'> {
  paginationOptions?: UsePaginationOptions
  showPagination?: boolean
}

function PaginatedTable<RecordType extends Record<string, any>>({
  dataSource = [],
  paginationOptions,
  showPagination = true,
  ...tableProps
}: PaginatedTableProps<RecordType>) {
  const pagination = usePagination(paginationOptions)

  return (
    <Table<RecordType>
      {...tableProps}
      dataSource={dataSource}
      pagination={showPagination ? pagination.getPaginationConfig(dataSource.length) : false}
    />
  )
}

export default PaginatedTable 