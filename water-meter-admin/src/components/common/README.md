# 通用分页组件使用指南

## 📋 概述

本项目提供了两种方式来实现表格分页功能：

1. **usePagination Hook** - 适合需要自定义分页逻辑的场景
2. **PaginatedTable 组件** - 适合标准分页需求的场景

## 🔧 方法1: 使用 usePagination Hook

适合需要自定义分页配置或有特殊业务逻辑的场景。

### 基本用法

```tsx
import { Table } from 'antd'
import { usePagination } from '@/hooks/usePagination'

export default function MyComponent() {
  const [data, setData] = useState([])
  
  // 使用分页Hook
  const pagination = usePagination({
    defaultPageSize: 10,
    showTotal: true
  })

  return (
    <Table
      columns={columns}
      dataSource={data}
      rowKey="id"
      pagination={pagination.getPaginationConfig(data.length)}
    />
  )
}
```

### 高级配置

```tsx
const pagination = usePagination({
  defaultPageSize: 20,
  pageSizeOptions: ['10', '20', '50', '100'],
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: true
})
```

### Hook返回值

- `currentPage` - 当前页码
- `pageSize` - 每页大小
- `setCurrentPage(page)` - 设置当前页
- `setPageSize(size)` - 设置每页大小
- `resetPagination()` - 重置分页
- `getPaginationConfig(total)` - 获取Antd Table分页配置

## 🚀 方法2: 使用 PaginatedTable 组件

适合标准分页需求，使用更简单。

### 基本用法

```tsx
import PaginatedTable from '@/components/common/PaginatedTable'

export default function MyComponent() {
  const [data, setData] = useState([])

  return (
    <PaginatedTable
      columns={columns}
      dataSource={data}
      rowKey="id"
      loading={loading}
    />
  )
}
```

### 自定义分页配置

```tsx
<PaginatedTable
  columns={columns}
  dataSource={data}
  rowKey="id"
  paginationOptions={{
    defaultPageSize: 20,
    pageSizeOptions: ['10', '20', '50', '100']
  }}
/>
```

### 禁用分页

```tsx
<PaginatedTable
  columns={columns}
  dataSource={data}
  rowKey="id"
  showPagination={false}
/>
```

## 📝 配置选项

### UsePaginationOptions

```tsx
interface UsePaginationOptions {
  defaultPageSize?: number        // 默认每页大小 (默认: 10)
  pageSizeOptions?: string[]      // 每页大小选项 (默认: ['10', '20', '50', '100'])
  showSizeChanger?: boolean       // 显示页面大小选择器 (默认: true)
  showQuickJumper?: boolean       // 显示快速跳转 (默认: true)
  showTotal?: boolean            // 显示总数信息 (默认: true)
}
```

## 🎯 最佳实践

1. **新建页面**：优先使用 `PaginatedTable` 组件，简单快捷
2. **现有页面**：使用 `usePagination` Hook 进行渐进式升级
3. **特殊需求**：使用 `usePagination` Hook 进行定制化开发

## 📄 迁移指南

### 从旧的分页实现迁移

**旧代码:**
```tsx
const [currentPage, setCurrentPage] = useState(1)
const [pageSize, setPageSize] = useState(10)

<Table
  pagination={{
    current: currentPage,
    pageSize: pageSize,
    total: data.length,
    // ... 其他配置
  }}
/>
```

**新代码:**
```tsx
const pagination = usePagination({ defaultPageSize: 10 })

<Table
  pagination={pagination.getPaginationConfig(data.length)}
/>
```

或者直接使用：
```tsx
<PaginatedTable
  dataSource={data}
  columns={columns}
  rowKey="id"
/>
``` 