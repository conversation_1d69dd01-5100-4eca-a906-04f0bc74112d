import React from 'react';
import { message, notification, Modal } from 'antd';
import { ExclamationCircleOutlined, InfoCircleOutlined, CheckCircleOutlined, CloseCircleOutlined } from '@ant-design/icons';

export interface ApiError {
  response?: {
    data?: {
      message?: string;
      Message?: string;
      errors?: string[];
      Errors?: string[];
    };
    status?: number;
  };
  message?: string;
}

export type NotificationType = 'success' | 'info' | 'warning' | 'error';

export interface NotificationConfig {
  title: string;
  description?: string;
  duration?: number;
  placement?: 'topLeft' | 'topRight' | 'bottomLeft' | 'bottomRight';
}

export class NotificationManager {
  static extractErrorMessage(error: ApiError, defaultMessage: string = 'Operation failed'): string {
    if (error?.response?.data) {
      const data = error.response.data;
      if (data.message) return data.message;
      if (data.Message) return data.Message;
      if (data.errors && Array.isArray(data.errors) && data.errors.length > 0) {
        return data.errors[0];
      }
      if (data.Errors && Array.isArray(data.Errors) && data.Errors.length > 0) {
        return data.Errors[0];
      }
    }
    
    if (error?.message) {
      return error.message;
    }
    
    if (error?.response?.status) {
      switch (error.response.status) {
        case 400: return 'Invalid request data';
        case 401: return 'Authentication required';
        case 403: return 'Access denied';
        case 404: return 'Resource not found';
        case 409: return 'Resource conflict - item already exists';
        case 422: return 'Validation failed';
        case 500: return 'Server error occurred';
        default: return defaultMessage;
      }
    }
    
    return defaultMessage;
  }

  static showMessage(type: NotificationType, content: string, duration: number = 3) {
    switch (type) {
      case 'success': message.success(content, duration); break;
      case 'error': message.error(content, duration); break;
      case 'warning': message.warning(content, duration); break;
      case 'info': message.info(content, duration); break;
    }
  }

  static showNotification(type: NotificationType, config: NotificationConfig) {
    const iconMap = {
      success: <CheckCircleOutlined style={{ color: '#52c41a' }} />,
      error: <CloseCircleOutlined style={{ color: '#ff4d4f' }} />,
      warning: <ExclamationCircleOutlined style={{ color: '#faad14' }} />,
      info: <InfoCircleOutlined style={{ color: '#1890ff' }} />
    };

    notification[type]({
      message: config.title,
      description: config.description,
      icon: iconMap[type],
      duration: config.duration || 4.5,
      placement: config.placement || 'topRight',
    });
  }

  static handleApiError(error: ApiError, context: string = 'Operation', showNotification: boolean = false) {
    const errorMessage = this.extractErrorMessage(error, `${context} failed`);
    
    if (showNotification) {
      this.showNotification('error', {
        title: `${context} Failed`,
        description: errorMessage,
        duration: 5
      });
    } else {
      this.showMessage('error', errorMessage);
    }
    
    console.error(`${context} error:`, error);
  }

  static handleSuccess(message: string, showNotification: boolean = false, title?: string) {
    if (showNotification && title) {
      this.showNotification('success', {
        title,
        description: message,
        duration: 3
      });
    } else {
      this.showMessage('success', message);
    }
  }

  static handleValidationError(errors: string[] | string, context: string = 'Validation') {
    const errorMessage = Array.isArray(errors) ? errors.join(', ') : errors;
    
    this.showNotification('warning', {
      title: `${context} Error`,
      description: errorMessage,
      duration: 5
    });
  }

  static showDuplicateWarning(itemType: string, itemName: string) {
    return Modal.warning({
      title: 'Duplicate Item Detected',
      content: `A ${itemType} with the name "${itemName}" already exists. Please choose a different name.`,
      okText: 'OK',
      centered: true,
    });
  }

  static showNetworkError(onRetry?: () => void) {
    if (onRetry) {
      return Modal.error({
        title: 'Network Error',
        content: 'Unable to connect to the server. Please check your internet connection and try again.',
        okText: 'Retry',
        onOk: onRetry,
        centered: true,
      });
    } else {
      this.showMessage('error', 'Network connection failed. Please check your internet connection.');
    }
  }

  static showPermissionError() {
    return Modal.error({
      title: 'Permission Denied',
      content: 'You do not have permission to perform this action. Please contact your administrator.',
      centered: true,
    });
  }

  static showDeleteConfirm(itemName: string, onConfirm: () => void | Promise<void>) {
    return Modal.confirm({
      title: 'Confirm Delete',
      content: `Are you sure you want to delete "${itemName}"? This action cannot be undone.`,
      icon: <ExclamationCircleOutlined style={{ color: '#faad14' }} />,
      okText: 'Delete',
      okButtonProps: { danger: true },
      cancelText: 'Cancel',
      onOk: onConfirm,
      centered: true,
    });
  }

  static showBatchDeleteConfirm(count: number, onConfirm: () => void | Promise<void>) {
    return Modal.confirm({
      title: 'Confirm Batch Delete',
      content: `Are you sure you want to delete ${count} selected items? This action cannot be undone.`,
      icon: <ExclamationCircleOutlined style={{ color: '#faad14' }} />,
      okText: 'Delete All',
      okButtonProps: { danger: true },
      cancelText: 'Cancel',
      onOk: onConfirm,
      centered: true,
    });
  }

  static showCreateSuccess(itemType: string = 'Item') {
    this.showMessage('success', `${itemType} created successfully`);
  }

  static showUpdateSuccess(itemType: string = 'Item') {
    this.showMessage('success', `${itemType} updated successfully`);
  }

  static showDeleteSuccess(itemType: string = 'Item') {
    this.showMessage('success', `${itemType} deleted successfully`);
  }

  static showCreateError(itemType: string = 'Item') {
    this.showMessage('error', `Failed to create ${itemType.toLowerCase()}`);
  }

  static showUpdateError(itemType: string = 'Item') {
    this.showMessage('error', `Failed to update ${itemType.toLowerCase()}`);
  }

  static showDeleteError(itemType: string = 'Item') {
    this.showMessage('error', `Failed to delete ${itemType.toLowerCase()}`);
  }

  static showLoadError(itemType: string = 'data') {
    this.showMessage('error', `Failed to load ${itemType.toLowerCase()}`);
  }

  static showSaveSuccess() {
    this.showMessage('success', 'Changes saved successfully');
  }

  static showValidationError(field?: string) {
    const message = field ? `Please enter a valid ${field}` : 'Please check your input and try again';
    this.showMessage('warning', message);
  }
}

export const useNotificationManager = () => {
  const handleError = (error: ApiError, context: string = 'Operation', showNotification: boolean = false) => {
    NotificationManager.handleApiError(error, context, showNotification);
  };

  const handleSuccess = (message: string, showNotification: boolean = false, title?: string) => {
    NotificationManager.handleSuccess(message, showNotification, title);
  };

  const handleValidationError = (errors: string[] | string, context: string = 'Validation') => {
    NotificationManager.handleValidationError(errors, context);
  };

  return {
    handleError,
    handleSuccess,
    handleValidationError,
    extractErrorMessage: NotificationManager.extractErrorMessage,
    showMessage: NotificationManager.showMessage,
    showNotification: NotificationManager.showNotification,
    showDuplicateWarning: NotificationManager.showDuplicateWarning,
    showDeleteConfirm: NotificationManager.showDeleteConfirm,
    showBatchDeleteConfirm: NotificationManager.showBatchDeleteConfirm,
    showCreateSuccess: NotificationManager.showCreateSuccess,
    showUpdateSuccess: NotificationManager.showUpdateSuccess,
    showDeleteSuccess: NotificationManager.showDeleteSuccess,
    showCreateError: NotificationManager.showCreateError,
    showUpdateError: NotificationManager.showUpdateError,
    showDeleteError: NotificationManager.showDeleteError,
    showLoadError: NotificationManager.showLoadError,
    showSaveSuccess: NotificationManager.showSaveSuccess,
    showValidationError: NotificationManager.showValidationError,
    showNetworkError: NotificationManager.showNetworkError,
    showPermissionError: NotificationManager.showPermissionError,
  };
};

export default NotificationManager;
