'use client'

import React from 'react'
import {
  Input,
  InputNumber,
  Switch,
  Select,
  Radio,
  Checkbox,
  Slider,
  TimePicker,
  Form,
  Typography,
  Tag,
  Tooltip
} from 'antd'
import { InfoCircleOutlined, InsertRowBelowOutlined } from '@ant-design/icons'
import type { ConfigurationItemProps } from '../../types/configuration'
import dayjs from 'dayjs'

const { TextArea } = Input
const { Text } = Typography
const { Option } = Select

const ConfigurationItem: React.FC<ConfigurationItemProps> = ({
  item,
  value,
  onChange,
  disabled = false
}) => {
  const handleChange = (newValue: any) => {
    if (onChange) {
      onChange(item.key, newValue)
    }
  }

  const renderFormItem = () => {
    const commonProps = {
      disabled: disabled || item.isReadOnly,
      placeholder: item.uiProperties?.placeholder || `Enter ${item.displayName.toLowerCase()}`
    }

    switch (item.uiComponent) {
      case 'input':
        return item.isSensitive ? (
          <Input.Password {...commonProps} value={value} onChange={(e) => handleChange(e.target.value)} />
        ) : (
          <Input {...commonProps} value={value} onChange={(e) => handleChange(e.target.value)} />
        )

      case 'textarea':
        return (
          <TextArea 
            {...commonProps} 
            value={value} 
            onChange={(e) => handleChange(e.target.value)}
            rows={item.uiProperties?.rows || 3}
          />
        )

      case 'number':
        return (
          <InputNumber
            {...commonProps}
            value={value}
            onChange={handleChange}
            min={item.uiProperties?.min}
            max={item.uiProperties?.max}
            step={item.uiProperties?.step || 1}
            style={{ width: '100%' }}
            formatter={item.uiProperties?.formatter ? (val) => `${val}${item.uiProperties.formatter}` : undefined}
            parser={item.uiProperties?.formatter ? (val) => val!.replace(item.uiProperties.formatter, '') : undefined}
          />
        )

      case 'switch':
        return (
          <Switch
            disabled={disabled || item.isReadOnly}
            checked={value}
            onChange={handleChange}
            checkedChildren={item.uiProperties?.checkedChildren || 'ON'}
            unCheckedChildren={item.uiProperties?.unCheckedChildren || 'OFF'}
          />
        )

      case 'select':
        return (
          <Select
            {...commonProps}
            value={value}
            onChange={handleChange}
            style={{ width: '100%' }}
          >
            {item.uiProperties?.options?.map((option: any) => (
              <Option key={option.value} value={option.value}>
                {option.label}
              </Option>
            ))}
          </Select>
        )

      case 'radio':
        return (
          <Radio.Group
            disabled={disabled || item.isReadOnly}
            value={value}
            onChange={(e) => handleChange(e.target.value)}
          >
            {item.uiProperties?.options?.map((option: any) => (
              <Radio key={option.value} value={option.value}>
                {option.label}
              </Radio>
            ))}
          </Radio.Group>
        )

      case 'checkbox':
        return item.uiProperties?.options ? (
          <Checkbox.Group
            disabled={disabled || item.isReadOnly}
            value={value}
            onChange={handleChange}
          >
            {item.uiProperties.options.map((option: any) => (
              <Checkbox key={option.value} value={option.value}>
                {option.label}
              </Checkbox>
            ))}
          </Checkbox.Group>
        ) : (
          <Checkbox
            disabled={disabled || item.isReadOnly}
            checked={value}
            onChange={(e) => handleChange(e.target.checked)}
          >
            {item.displayName}
          </Checkbox>
        )

      case 'slider':
        return (
          <Slider
            disabled={disabled || item.isReadOnly}
            value={value}
            onChange={handleChange}
            min={item.uiProperties?.min || 0}
            max={item.uiProperties?.max || 100}
            step={item.uiProperties?.step || 1}
            marks={item.uiProperties?.marks}
          />
        )

      case 'timepicker':
        return (
          <TimePicker
            disabled={disabled || item.isReadOnly}
            value={value ? dayjs(value, 'HH:mm') : undefined}
            onChange={(time) => handleChange(time ? time.format('HH:mm') : null)}
            format="HH:mm"
            style={{ width: '100%' }}
          />
        )

      default:
        return (
          <Input {...commonProps} value={value} onChange={(e) => handleChange(e.target.value)} />
        )
    }
  }

  const renderLabel = () => {
    return (
      <div className="flex items-center gap-2">
        <span>{item.displayName}</span>
        {item.description && (
          <Tooltip title={item.description}>
            <InfoCircleOutlined className="text-gray-400" />
          </Tooltip>
        )}
        {item.isInherited && (
          <Tooltip title={`Inherited from ${item.inheritedFrom || 'parent'}`}>
            <Tag icon={<InsertRowBelowOutlined />} color="blue">
              Inherited
            </Tag>
          </Tooltip>
        )}
        {item.requiresRestart && (
          <Tag color="orange">
            Requires Restart
          </Tag>
        )}
        {item.isSensitive && (
          <Tag color="red">
            Sensitive
          </Tag>
        )}
      </div>
    )
  }

  const renderHelpText = () => {
    if (!item.description && !item.isInherited) return null

    return (
      <div className="mt-1">
        {item.description && (
          <Text type="secondary" className="text-sm">
            {item.description}
          </Text>
        )}
        {item.isInherited && (
          <Text type="secondary" className="text-sm block">
            Current value is inherited from {item.inheritedFrom || 'parent'} settings.
          </Text>
        )}
      </div>
    )
  }

  return (
    <Form.Item
      label={renderLabel()}
      help={renderHelpText()}
      validateStatus={item.validationRules?.required && !value ? 'error' : ''}
    >
      {renderFormItem()}
    </Form.Item>
  )
}

export default ConfigurationItem 