import React, { useState } from 'react';
import { Table, Form, Button, Space, message, Popconfirm } from 'antd';
import { EditOutlined, SaveOutlined, CloseOutlined } from '@ant-design/icons';
import { EditableCell } from './EditableCell';
import { EditableTableProps, EditableColumnConfig } from './types';
import dayjs from 'dayjs';

export function EditableTable<T extends Record<string, any>>({
  data,
  columns,
  onSave,
  rowKey,
  loading = false,
  pagination,
  size = 'middle',
  bordered = true,
  customActions,
  showDefaultActions = true,
}: EditableTableProps<T>) {
  const [form] = Form.useForm();
  const [editingKey, setEditingKey] = useState<string | number>('');

  const isEditing = (record: T) => record[rowKey] === editingKey;

  const edit = (record: T) => {
    // 处理日期字段，转换为dayjs对象
    const formValues = { ...record };
    columns.forEach(col => {
      if (col.type === 'date' && formValues[col.key]) {
        formValues[col.key] = dayjs(formValues[col.key]);
      }
    });
    
    form.setFieldsValue(formValues);
    setEditingKey(record[rowKey]);
  };

  const cancel = () => {
    setEditingKey('');
    form.resetFields();
  };

  const save = async (record: T) => {
    try {
      const row = await form.validateFields();
      
      // 处理日期字段，转换回Date对象
      const processedRow = { ...row };
      columns.forEach(col => {
        if (col.type === 'date' && processedRow[col.key]) {
          processedRow[col.key] = processedRow[col.key].toDate();
        }
      });

      // 只发送实际改变的字段，并且只检查可编辑的字段
      const changedValues: Partial<T> = {};

      // 获取所有可编辑字段的key
      const editableFields = columns
        .filter(col => col.editable)
        .map(col => col.key as string);

      // 只检查可编辑字段的变更
      editableFields.forEach(key => {
        if (key in processedRow && processedRow[key] !== record[key as keyof T]) {
          changedValues[key as keyof T] = processedRow[key];
        }
      });

      if (Object.keys(changedValues).length === 0) {
        message.info('No changes detected');
        cancel();
        return;
      }

      await onSave(record, changedValues);
      setEditingKey('');
      message.success('Record updated successfully');
    } catch (errInfo) {
      console.error('Validate Failed:', errInfo);
      message.error('Failed to save changes');
    }
  };

  // 生成表格列配置
  const tableColumns = [
    ...columns.map((col: EditableColumnConfig<T>) => ({
      title: col.title,
      dataIndex: col.key as string,
      width: col.width,
      onCell: (record: T) => ({
        record,
        dataIndex: col.key,
        title: col.title,
        type: col.type || 'text',
        options: col.options,
        rules: col.rules,
        placeholder: col.placeholder,
        editing: isEditing(record) && col.editable,
        editComponent: col.editComponent,
      }),
      render: col.render || ((text: any, record: T, index: number) => {
        // 默认渲染逻辑
        if (col.type === 'date' && text) {
          return dayjs(text).format('YYYY-MM-DD');
        }
        if (col.type === 'select' && col.options) {
          const option = col.options.find(opt => opt.value === text);
          return option ? option.label : text;
        }
        return text;
      }),
    })),
    // 操作列
    {
      title: 'Actions',
      dataIndex: 'operation',
      width: customActions ? 200 : 120,
      render: (_: any, record: T) => {
        const editable = isEditing(record);

        // 如果有自定义Actions，使用自定义的
        if (customActions) {
          const editActions = {
            edit,
            save,
            cancel,
            isEditing
          };
          return customActions(record, editable, editActions);
        }

        // 否则使用默认的编辑Actions
        if (!showDefaultActions) {
          return null;
        }

        return editable ? (
          <Space size="small">
            <Button
              type="link"
              size="small"
              icon={<SaveOutlined />}
              onClick={() => save(record)}
            >
              Save
            </Button>
            <Popconfirm
              title="Cancel editing?"
              onConfirm={cancel}
              okText="Yes"
              cancelText="No"
            >
              <Button
                type="link"
                size="small"
                icon={<CloseOutlined />}
                danger
              >
                Cancel
              </Button>
            </Popconfirm>
          </Space>
        ) : (
          <Button
            type="link"
            size="small"
            icon={<EditOutlined />}
            disabled={editingKey !== ''}
            onClick={() => edit(record)}
          >
            Edit
          </Button>
        );
      },
    },
  ];

  return (
    <Form form={form} component={false}>
      <Table<T>
        components={{
          body: {
            cell: EditableCell,
          },
        }}
        bordered={bordered}
        dataSource={data}
        columns={tableColumns}
        rowClassName="editable-row"
        pagination={pagination}
        loading={loading}
        size={size}
        rowKey={rowKey as string}
      />
    </Form>
  );
}
