import { Rule } from 'antd/es/form';
import { ReactNode } from 'react';

export type FieldType = 'text' | 'number' | 'select' | 'date' | 'textarea';

export interface SelectOption {
  label: string;
  value: any;
}

export interface EditableColumnConfig<T = any> {
  key: keyof T;
  title: string;
  type?: FieldType;
  editable?: boolean;
  width?: number;
  options?: SelectOption[]; // for select type
  rules?: Rule[];
  placeholder?: string;
  render?: (value: any, record: T, index: number) => ReactNode;
  // 自定义编辑组件
  editComponent?: (value: any, onChange: (value: any) => void) => ReactNode;
}

export interface EditableTableProps<T = any> {
  data: T[];
  columns: EditableColumnConfig<T>[];
  onSave: (record: T, changedValues: Partial<T>) => Promise<void>;
  rowKey: keyof T;
  loading?: boolean;
  pagination?: any;
  size?: 'small' | 'middle' | 'large';
  bordered?: boolean;
  // 自定义Actions列
  customActions?: (record: T, isEditing: boolean, editActions: EditActions<T>) => ReactNode;
  // 是否显示默认的编辑Actions
  showDefaultActions?: boolean;
}

export interface EditActions<T = any> {
  edit: (record: T) => void;
  save: (record: T) => Promise<void>;
  cancel: () => void;
  isEditing: (record: T) => boolean;
}

export interface EditableCellProps {
  editing: boolean;
  dataIndex: string;
  title: string;
  type: FieldType;
  options?: SelectOption[];
  rules?: Rule[];
  placeholder?: string;
  children: ReactNode;
  editComponent?: (value: any, onChange: (value: any) => void) => ReactNode;
}
