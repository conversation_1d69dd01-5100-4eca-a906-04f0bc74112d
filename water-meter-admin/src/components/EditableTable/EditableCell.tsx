import React from 'react';
import { Form, Input, InputNumber, Select, DatePicker } from 'antd';
import { EditableCellProps } from './types';

const { TextArea } = Input;

export const EditableCell: React.FC<React.PropsWithChildren<EditableCellProps>> = ({
  editing,
  dataIndex,
  title,
  type = 'text',
  options = [],
  rules = [],
  placeholder,
  children,
  editComponent,
  ...restProps
}) => {
  const renderEditComponent = () => {
    // 如果提供了自定义编辑组件，使用它
    if (editComponent) {
      return (
        <Form.Item
          name={dataIndex}
          style={{ margin: 0 }}
          rules={rules}
        >
          {editComponent}
        </Form.Item>
      );
    }

    // 根据类型渲染不同的输入组件
    switch (type) {
      case 'number':
        return (
          <Form.Item
            name={dataIndex}
            style={{ margin: 0 }}
            rules={rules}
          >
            <InputNumber
              placeholder={placeholder}
              style={{ width: '100%' }}
            />
          </Form.Item>
        );

      case 'select':
        return (
          <Form.Item
            name={dataIndex}
            style={{ margin: 0 }}
            rules={rules}
          >
            <Select
              placeholder={placeholder || `Select ${title}`}
              style={{ width: '100%' }}
            >
              {options.map(option => (
                <Select.Option key={option.value} value={option.value}>
                  {option.label}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>
        );

      case 'date':
        return (
          <Form.Item
            name={dataIndex}
            style={{ margin: 0 }}
            rules={rules}
          >
            <DatePicker
              placeholder={placeholder}
              style={{ width: '100%' }}
              format="YYYY-MM-DD"
            />
          </Form.Item>
        );

      case 'textarea':
        return (
          <Form.Item
            name={dataIndex}
            style={{ margin: 0 }}
            rules={rules}
          >
            <TextArea
              placeholder={placeholder}
              autoSize={{ minRows: 2, maxRows: 4 }}
            />
          </Form.Item>
        );

      case 'text':
      default:
        return (
          <Form.Item
            name={dataIndex}
            style={{ margin: 0 }}
            rules={rules}
          >
            <Input
              placeholder={placeholder}
            />
          </Form.Item>
        );
    }
  };

  return (
    <td {...restProps}>
      {editing ? renderEditComponent() : children}
    </td>
  );
};
