import React from 'react';
import { Modal, Table, Tag } from 'antd';
import dayjs from 'dayjs';
import { TaskAssignmentDto, UserWorkloadSummaryDto } from '@/services/task.service';
import type { ColumnsType } from 'antd/es/table';

interface UserTasksModalProps {
  visible: boolean;
  user: UserWorkloadSummaryDto | null;
  tasks: TaskAssignmentDto[];
  loading: boolean;
  onClose: () => void;
}

const UserTasksModal: React.FC<UserTasksModalProps> = ({
  visible,
  user,
  tasks,
  loading,
  onClose
}) => {
  const getPriorityColor = (priority: string) => {
    const colors = {
      'Low': 'gray',
      'Medium': 'blue',
      'High': 'orange',
      'Critical': 'red'
    };
    return colors[priority as keyof typeof colors] || 'default';
  };

  const columns: ColumnsType<TaskAssignmentDto> = [
    {
      title: 'Task Name',
      dataIndex: 'taskName',
      key: 'taskName',
      width: 200,
      render: (name: string) => name || 'N/A',
    },
    {
      title: 'Priority',
      dataIndex: 'taskPriority',
      key: 'taskPriority',
      width: 100,
      render: (priority: string) => priority ? <Tag color={getPriorityColor(priority)}>{priority}</Tag> : '-',
    },
    {
      title: 'Task Status',
      dataIndex: 'taskStatus',
      key: 'taskStatus',
      width: 100,
      render: (status: string) => status ? <Tag color={status === 'Completed' ? 'success' : 'processing'}>{status}</Tag> : '-',
    },
    {
      title: 'Assignment Status',
      dataIndex: 'status',
      key: 'status',
      width: 120,
      render: (status: string) => (
        <Tag color={
          status === 'Accepted' ? 'success' : 
          status === 'Rejected' ? 'error' : 
          'default'
        }>
          {status}
        </Tag>
      ),
    },
    {
      title: 'Assigned By',
      dataIndex: 'assignedBy',
      key: 'assignedBy',
      width: 120,
    },
    {
      title: 'Assigned Date',
      dataIndex: 'assignedDate',
      key: 'assignedDate',
      width: 130,
      render: (date: string) => dayjs(date).format('MM-DD HH:mm'),
    },
    {
      title: 'Assignment Type',
      dataIndex: 'assignmentType',
      key: 'assignmentType',
      width: 120,
      render: (type: string) => <Tag>{type}</Tag>,
    },
  ];

  return (
    <Modal
      title={`Tasks Assigned to ${user?.userName || ''}`}
      open={visible}
      onCancel={onClose}
      footer={null}
      width={900}
      styles={{ body: { maxHeight: '70vh', overflowY: 'auto' } }}
    >
      {user && (
        <div className="mb-4 p-3 bg-blue-50 rounded">
          <div className="flex justify-between">
            <div>
              <strong>{user.userName}</strong>
              <div className="text-sm text-gray-600">{user.email}</div>
            </div>
            <div className="text-right">
              <div className="text-sm">
                Active: {user.activeTaskCount} / {user.maxCapacity}
              </div>
              <div className="text-sm text-gray-600">
                Completed: {user.completedTaskCount}
              </div>
            </div>
          </div>
        </div>
      )}
      
      <Table
        columns={columns}
        dataSource={tasks}
        loading={loading}
        rowKey="id"
        pagination={{
          pageSize: 10,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} assignments`,
        }}
        scroll={{ x: 800 }}
      />
    </Modal>
  );
};

export default UserTasksModal; 