import React from 'react';
import { Card, Progress, Tag, Avatar, Space, Tooltip } from 'antd';
import { UserOutlined, CheckCircleOutlined, ClockCircleOutlined, ExclamationCircleOutlined } from '@ant-design/icons';
import { UserWorkloadSummaryDto } from '@/services/task.service';

interface UserWorkloadCardProps {
  user: UserWorkloadSummaryDto;
  onClick?: (user: UserWorkloadSummaryDto) => void;
  selected?: boolean;
  showValidation?: boolean;
  validationWarnings?: string[];
}

const UserWorkloadCard: React.FC<UserWorkloadCardProps> = ({
  user,
  onClick,
  selected = false,
  showValidation = false,
  validationWarnings = []
}) => {
  const getAvailabilityColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'available': return 'green';
      case 'busy': return 'orange';
      case 'offline': return 'red';
      default: return 'gray';
    }
  };

  const getWorkloadColor = () => {
    if (user.workloadPercentage >= 90) return '#ff4d4f';
    if (user.workloadPercentage >= 75) return '#faad14';
    if (user.workloadPercentage >= 50) return '#52c41a';
    return '#1890ff';
  };

  const getWorkloadStatus = () => {
    if (user.workloadPercentage >= 90) return 'At Capacity';
    if (user.workloadPercentage >= 75) return 'High Load';
    if (user.workloadPercentage >= 50) return 'Moderate Load';
    return 'Available';
  };

  return (
    <Card
      hoverable
      onClick={() => onClick?.(user)}
      className={`${selected ? 'border-blue-500 shadow-md' : ''} cursor-pointer`}
      size="small"
      style={{ marginBottom: 16 }}
    >
      <div className="flex items-start justify-between">
        <div className="flex items-center space-x-3">
          <Avatar size={48} icon={<UserOutlined />} />
          
          <div>
            <div className="font-medium text-base">{user.userName}</div>
            <div className="text-gray-500 text-sm">{user.email}</div>
            {user.zone && (
              <Tag size="small" color="blue">{user.zone}</Tag>
            )}
            {user.department && (
              <Tag size="small" color="purple">{user.department}</Tag>
            )}
          </div>
        </div>

        <div className="text-right">
          <Tag color={getAvailabilityColor(user.availabilityStatus)}>
            {user.availabilityStatus}
          </Tag>
        </div>
      </div>

      <div className="mt-4">
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm font-medium">Workload</span>
          <span className="text-sm text-gray-600">
            {user.activeTaskCount}/{user.maxCapacity} tasks
          </span>
        </div>
        
        <Progress
          percent={user.workloadPercentage}
          strokeColor={getWorkloadColor()}
          size="small"
          format={() => `${user.workloadPercentage}%`}
        />
        
        <div className="flex items-center justify-between mt-2 text-xs text-gray-500">
          <span>{getWorkloadStatus()}</span>
          <span>Efficiency: {user.efficiencyScore}/10</span>
        </div>
      </div>

      <div className="mt-3">
        <Space size="small" wrap>
          <Tooltip title="Active Tasks">
            <div className="flex items-center text-xs">
              <ClockCircleOutlined className="text-blue-500 mr-1" />
              {user.activeTaskCount}
            </div>
          </Tooltip>
          
          <Tooltip title="Completed Tasks">
            <div className="flex items-center text-xs">
              <CheckCircleOutlined className="text-green-500 mr-1" />
              {user.completedTaskCount}
            </div>
          </Tooltip>
          
          <Tooltip title="Overdue Tasks">
            <div className="flex items-center text-xs">
              <ExclamationCircleOutlined className="text-red-500 mr-1" />
              {user.overdueTaskCount}
            </div>
          </Tooltip>
        </Space>
      </div>

      {user.skills && user.skills.length > 0 && (
        <div className="mt-3">
          <div className="text-xs text-gray-500 mb-1">Skills:</div>
          <Space size="small" wrap>
            {user.skills.map(skill => (
              <Tag key={skill} size="small">{skill}</Tag>
            ))}
          </Space>
        </div>
      )}

      {showValidation && validationWarnings.length > 0 && (
        <div className="mt-3 p-2 bg-yellow-50 rounded border-l-4 border-yellow-400">
          <div className="text-xs text-yellow-800">
            <ExclamationCircleOutlined className="mr-1" />
            Warnings:
          </div>
          {validationWarnings.map((warning, index) => (
            <div key={index} className="text-xs text-yellow-700">• {warning}</div>
          ))}
        </div>
      )}

      {user.lastActivity && (
        <div className="mt-2 text-xs text-gray-400">
          Last activity: {new Date(user.lastActivity).toLocaleDateString()}
        </div>
      )}
    </Card>
  );
};

export default UserWorkloadCard; 