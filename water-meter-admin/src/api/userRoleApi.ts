import { api } from '../services/api';
import type {
  UserRoleDto,
  AssignRolesDto,
  UserRoleAssignmentResultDto,
  RoleAssignmentDetailDto,
  Role,
  ApiResponse
} from '../types/userRole';

export class UserRoleApi {
  private static readonly BASE_URL = '/userrole';

  static async getUserRoles(userId: number): Promise<ApiResponse<UserRoleDto[]>> {
    const response = await api.get(`${this.BASE_URL}/user/${userId}/roles`);
    return response.data;
  }

  static async getRoleUsers(roleId: number): Promise<ApiResponse<UserRoleDto[]>> {
    const response = await api.get(`${this.BASE_URL}/role/${roleId}/users`);
    return response.data;
  }

  static async assignRolesToUser(
    userId: number,
    assignRolesDto: AssignRolesDto
  ): Promise<ApiResponse<UserRoleAssignmentResultDto>> {
    const response = await api.post(`${this.BASE_URL}/user/${userId}/roles`, assignRolesDto);
    return response.data;
  }

  static async addRoleToUser(
    userId: number,
    roleId: number
  ): Promise<ApiResponse<RoleAssignmentDetailDto>> {
    const response = await api.post(`${this.BASE_URL}/user/${userId}/role/${roleId}`);
    return response.data;
  }

  static async removeRoleFromUser(userId: number, roleId: number): Promise<ApiResponse> {
    const response = await api.delete(`${this.BASE_URL}/user/${userId}/role/${roleId}`);
    return response.data;
  }

  static async userHasRole(userId: number, roleId: number): Promise<ApiResponse<boolean>> {
    const response = await api.get(`${this.BASE_URL}/user/${userId}/role/${roleId}/exists`);
    return response.data;
  }

  static async getAvailableRoles(): Promise<ApiResponse<Role[]>> {
    const response = await api.get(`${this.BASE_URL}/available-roles`);
    return response.data;
  }
}

export default UserRoleApi;
