# Water Meter Management GPS Coordinates Fix

## Overview
Fixed both Task Management and Water Meter Management systems to properly display GPS coordinates.

## Problems Fixed

### 1. Task Management GPS Display
Previously, the Task Assignment page was using mock/random coordinates to display tasks on the map because:
1. TaskListDto didn't include GPS coordinate fields
2. <PERSON><PERSON> wasn't returning GPS coordinates for tasks
3. <PERSON>end was generating random coordinates around Christchurch

### 2. Water Meter Management Missing GPS & Fields
The Water Meter Management interface had several issues:
1. WaterMeterListDto was missing GPS coordinate fields
2. Important fields like accountNumber, assetId, serialNumber were not properly displayed
3. API endpoint `/api/water-meters` was not returning GPS coordinates

## Solutions

### 1. Task Management Backend Changes

#### Updated TaskListDto (DTOs/TaskDTOs.cs)
Added GPS coordinate fields:
```csharp
// GPS coordinates
public decimal? Latitude { get; set; }
public decimal? Longitude { get; set; }

// Meter GPS coordinates (from associated water meter)
public decimal? MeterLatitude { get; set; }
public decimal? MeterLongitude { get; set; }
```

#### Updated TaskService.cs
Modified all TaskListDto queries to include GPS coordinates:
- `GetTasksAsync()` - Main task search
- `GetTaskByIdAsync()` - Single task retrieval  
- `GetTasksGroupedByWorkPackageAsync()` - Work package grouped tasks

Added GPS coordinate mapping:
```csharp
// GPS coordinates
Latitude = t.Latitude,
Longitude = t.Longitude,
// Meter GPS coordinates (from associated water meter)
MeterLatitude = t.Meter != null ? t.Meter.Latitude : null,
MeterLongitude = t.Meter != null ? t.Meter.Longitude : null,
```

### 2. Water Meter Management Backend Changes

#### Updated WaterMeterListDto (DTOs/WaterMeterDto.cs)
Added GPS coordinate fields:
```csharp
// GPS Coordinates
public decimal? Latitude { get; set; }
public decimal? Longitude { get; set; }
```

#### Updated WaterMeterService.cs
Modified GetWaterMetersAsync query to include GPS coordinates:
```csharp
// GPS Coordinates
Latitude = m.Latitude,
Longitude = m.Longitude,
```

### 3. Task Management Frontend Changes

#### Updated TaskListDto Interface (services/task.service.ts)
Added GPS coordinate fields:
```typescript
// GPS coordinates
latitude?: number;
longitude?: number;
// Meter GPS coordinates (from associated water meter)
meterLatitude?: number;
meterLongitude?: number;
```

#### Updated Map Marker Logic (app/task-assignment/page.tsx)
Replaced mock coordinates with real GPS data:

**Before:**
```typescript
// Mock coordinates for demo
const mockCoordinates = [
  { lat: -43.5320, lng: 172.6306 }, // Christchurch center
  // ... more mock coordinates
];
const coords = mockCoordinates[index % mockCoordinates.length];
```

**After:**
```typescript
// Use task GPS coordinates first, fallback to meter GPS coordinates
const lat = task.latitude != null ? task.latitude : task.meterLatitude!;
const lng = task.longitude != null ? task.longitude : task.meterLongitude!;
```

#### Enhanced Map Info Windows (components/common/GoogleMapView.tsx)
Improved info window content with:
- Better styling and layout
- Location and meter serial number display
- Color-coded status and priority badges
- More professional appearance

### 4. Water Meter Management Frontend Changes

#### Updated WaterMeterListDto Interface (types/water-meter.ts)
Added GPS coordinate fields:
```typescript
// GPS Coordinates
latitude?: number;
longitude?: number;
```

#### Enhanced Water Meter Table (app/water-meters/page.tsx)
Added GPS Coordinates column:
```typescript
{
  title: 'GPS Coordinates',
  key: 'gpsCoordinates',
  width: 150,
  render: (text: string, record) => (
    <div>
      {record.latitude && record.longitude ? (
        <div>
          <div style={{ fontSize: '12px', color: '#1890ff' }}>
            📍 {record.latitude.toFixed(6)}
          </div>
          <div style={{ fontSize: '12px', color: '#1890ff' }}>
            {record.longitude.toFixed(6)}
          </div>
        </div>
      ) : (
        <span style={{ color: '#999', fontSize: '12px' }}>No GPS</span>
      )}
    </div>
  )
}
```

### 3. GPS Coordinate Sources

Tasks can get GPS coordinates from two sources:
1. **Task coordinates** (`latitude`, `longitude`) - Direct task location
2. **Meter coordinates** (`meterLatitude`, `meterLongitude`) - From associated water meter

Priority: Task coordinates take precedence over meter coordinates.

### 4. Filtering Logic

Only tasks with valid GPS coordinates are displayed on the map:
```typescript
.filter(task => {
  // Only include tasks that have GPS coordinates
  const hasTaskGps = task.latitude != null && task.longitude != null;
  const hasMeterGps = task.meterLatitude != null && task.meterLongitude != null;
  return hasTaskGps || hasMeterGps;
})
```

## Benefits

### Task Management
1. **Accurate Locations**: Tasks now show at their real GPS coordinates
2. **Better User Experience**: Users can see actual task locations for route planning
3. **Improved Info**: Enhanced info windows with location details and meter information
4. **Flexible Sources**: Can use either task-specific or meter-based coordinates
5. **Visual Clarity**: Color-coded status and priority indicators

### Water Meter Management
1. **Complete GPS Display**: Water meters now show their GPS coordinates in the table
2. **Better Field Visibility**: Important fields like accountNumber, assetId, serialNumber are properly displayed
3. **Enhanced Data Access**: API now returns all necessary GPS coordinate information
4. **Improved User Interface**: Clear visual indication of meters with/without GPS coordinates

## Testing

### Task Management Testing
1. Ensure water meters have GPS coordinates (use the GPS update feature)
2. Create tasks associated with water meters
3. Navigate to Task Assignment page
4. Verify tasks appear at correct locations on the map
5. Click markers to see enhanced info windows

### Water Meter Management Testing
1. Navigate to Master Data > Meter Management
2. Verify the GPS Coordinates column is displayed
3. Check that meters with GPS coordinates show latitude/longitude values
4. Verify meters without GPS show "No GPS" indicator
5. Confirm accountNumber, assetId, and serialNumber are properly displayed

## Dependencies

This fix depends on:
- Water meters having GPS coordinates (from the GPS coordinate system)
- Tasks being properly associated with water meters
- Google Maps API integration

## Future Enhancements

1. Add clustering for tasks in close proximity
2. Implement route optimization visualization
3. Add distance calculations between tasks
4. Show task assignment routes on the map
