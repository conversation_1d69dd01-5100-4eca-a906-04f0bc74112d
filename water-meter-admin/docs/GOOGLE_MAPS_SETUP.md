# Google Maps Setup Guide

## Overview
The Bulk Assignment page now includes an integrated Google Maps component that displays task locations and allows interactive selection.

## Configuration

### 1. Google Maps API Key Setup

1. Go to the [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select an existing one
3. Enable the Maps JavaScript API
4. Create credentials (API Key)
5. Restrict the API key to your domain for security

### 2. Environment Variable Configuration

Create a `.env.local` file in the root directory with:

```bash
NEXT_PUBLIC_GOOGLE_MAPS_API_KEY=your_actual_api_key_here
```

**Important**: Never commit your actual API key to version control!

### 3. Features Implemented

#### Bulk Assignment Page Enhancements:
- **Left Panel (60% width)**: Task list with checkbox selection
- **Right Panel (40% width)**: Interactive Google Maps
- **Bidirectional sync**: Select tasks in table ↔ highlight markers on map
- **Visual indicators**: Different marker colors for priority levels
- **Info windows**: Click markers to see task details

#### Map Features:
- **Marker colors** based on task priority:
  - 🔴 Critical: Red
  - 🟠 High: Orange
  - 🔵 Medium: Blue
  - 🟢 Low: Green
- **Selected markers** are highlighted with white border and larger size
- **Info windows** show task title, description, status, and priority
- **Auto-fit bounds** to show all task locations

### 4. Mock Data Notice

Currently using mock coordinates centered around Christchurch, NZ for demonstration.
In production, you should:

1. Update the `TaskListDto` interface to include proper location fields:
```typescript
export interface TaskListDto {
  // ... existing fields
  latitude?: number;
  longitude?: number;
  // OR
  location?: {
    latitude: number;
    longitude: number;
    address?: string;
  };
}
```

2. Update the `convertTasksToMarkers` function to use real coordinates
3. Ensure your backend provides actual GPS coordinates for tasks

### 5. Usage

1. Navigate to Task Assignment → Bulk Assignment tab
2. View tasks in the left panel and their locations on the right
3. Select tasks using checkboxes or by clicking map markers
4. Use "Assign Selected" button to bulk assign tasks
5. Map will update markers based on your selections

### 6. Performance Notes

- Map loads up to 100 unassigned tasks
- Markers are optimized with SVG icons
- Table pagination is set to 10 items per page
- Scroll area is limited to 400px height for better UX

## Troubleshooting

If the map doesn't load:
1. Check console for API key errors
2. Verify the API key has Maps JavaScript API enabled
3. Check network requests for CORS issues
4. Ensure the environment variable is properly set

If markers don't appear:
1. Check that tasks have valid latitude/longitude data
2. Verify the `convertTasksToMarkers` function is working
3. Look for console errors in the map component 