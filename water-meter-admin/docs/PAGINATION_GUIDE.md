# 🚀 通用分页解决方案

## 📋 概述

本项目提供了完整的通用分页解决方案，一劳永逸解决所有表格分页需求。

## 💡 解决方案

### 1️⃣ **usePagination Hook**
- 📍 位置: `src/hooks/usePagination.ts`
- 🎯 用途: 提供分页状态管理和配置生成
- ✨ 特点: 灵活、可定制、支持所有分页功能

### 2️⃣ **PaginatedTable 组件** 
- 📍 位置: `src/components/common/PaginatedTable.tsx`
- 🎯 用途: 开箱即用的分页表格组件
- ✨ 特点: 简单、快速、零配置

## 🔧 使用方法

### 方法1: usePagination Hook（推荐用于现有页面升级）

```tsx
import { usePagination } from '@/hooks/usePagination'

export default function MyPage() {
  const pagination = usePagination({
    defaultPageSize: 10,
    showTotal: true
  })

  return (
    <Table
      columns={columns}
      dataSource={data}
      rowKey="id"
      pagination={pagination.getPaginationConfig(data.length)}
    />
  )
}
```

### 方法2: PaginatedTable 组件（推荐用于新页面）

```tsx
import PaginatedTable from '@/components/common/PaginatedTable'

export default function MyPage() {
  return (
    <PaginatedTable
      columns={columns}
      dataSource={data}
      rowKey="id"
      loading={loading}
    />
  )
}
```

## ⚙️ 配置选项

```tsx
// 分页配置接口
interface UsePaginationOptions {
  defaultPageSize?: number        // 默认每页条数 (默认: 10)
  pageSizeOptions?: string[]      // 每页条数选项 (默认: ['10', '20', '50', '100'])
  showSizeChanger?: boolean       // 显示每页条数选择器 (默认: true)
  showQuickJumper?: boolean       // 显示快速跳转 (默认: true)
  showTotal?: boolean            // 显示总条数信息 (默认: true)
}
```

## 📈 已升级的页面

- ✅ **UserManagement** - 用户管理页面
- ✅ **RoleManagement** - 角色管理页面  
- ✅ **PermissionManagement** - 权限管理页面

## 🎯 新页面快速开发

对于新页面，只需要：

```tsx
// 1. 导入组件
import PaginatedTable from '@/components/common/PaginatedTable'

// 2. 直接使用，零配置
<PaginatedTable
  columns={columns}
  dataSource={data}
  rowKey="id"
/>
```

就这么简单！✨

## 🔥 核心优势

1. **一劳永逸** - 解决所有分页问题，不需要重复编写
2. **简洁高效** - 几行代码搞定复杂分页功能
3. **功能完整** - 支持页面大小选择、快速跳转、总数显示
4. **类型安全** - 完整的TypeScript支持
5. **易于维护** - 统一的分页逻辑，便于维护和升级

现在您可以专注于业务逻辑，分页功能完全不用担心！🎉 