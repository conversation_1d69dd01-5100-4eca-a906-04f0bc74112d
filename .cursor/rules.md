# 🎯 Global Development Rules & Coding Standards

## 📝 Language Usage Rules

### Code and Comments
- **ALL code must be written in English**
- **ALL comments must be written in English**
- **ALL variable names, function names, class names must be in English**
- **ALL configuration files, API documentation must be in English**
- **ALL database table names and field names must be in English**

### Communication and Documentation
- **User communication and Q&A responses must be in Chinese (中文)**
- **Documentation in deployment-config/docs folder should be in Chinese**
- **User stories and business requirement documents should be in Chinese**

## 💻 Code Quality Standards

### Naming Conventions
- Use **camelCase** for variables and functions
- Use **PascalCase** for classes and components
- Use **UPPER_SNAKE_CASE** for constants
- Use **kebab-case** for file names
- Use descriptive and meaningful names

### Comment Standards
```typescript
// Good: Calculate water meter reading consumption
const calculateConsumption = (currentReading: number, previousReading: number) => {
  // Validate input parameters to ensure positive values
  if (currentReading < 0 || previousReading < 0) {
    throw new Error('Reading values must be positive');
  }
  
  // Calculate consumption difference
  return currentReading - previousReading;
};
```

### Database Naming Standards
```sql
-- Table names: snake_case with descriptive names
CREATE TABLE meter_readings (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  meter_number TEXT NOT NULL,           -- English field names
  reading_value REAL NOT NULL,          -- Clear and descriptive
  reading_timestamp TEXT NOT NULL,      -- Consistent naming pattern
  gps_latitude REAL,                    -- GPS coordinates
  gps_longitude REAL,
  gps_accuracy REAL,
  created_at TEXT DEFAULT CURRENT_TIMESTAMP
);
```

## 🏗️ Architecture Standards

### Single Responsibility Principle
- Each function should have one clear purpose
- Each class should handle one specific domain
- Each file should contain related functionality only

### Error Handling Standards
```typescript
try {
  // Main operation with clear English comments
  const result = await performOperation();
  return result;
} catch (error) {
  // Log error with descriptive English message
  console.error('Failed to perform operation:', error.message);
  throw new Error('Operation failed due to invalid input');
}
```

### API Response Standards
```typescript
// English API responses and error messages
interface ApiResponse<T> {
  success: boolean;
  data?: T;
  message: string;          // Always in English
  errorCode?: string;       // English error codes
  timestamp: string;
}
```

## 🔧 Technical Implementation Rules

### Import Organization
```typescript
// 1. React/React Native imports
import React, { useState, useEffect } from 'react';

// 2. Third-party libraries
import { Box, VStack, Text } from 'native-base';

// 3. Internal utilities and services
import { DatabaseManager } from '../database/DatabaseManager';
import { LocationService } from '../services/LocationService';

// 4. Type definitions
import type { MeterReading, LocationData } from '../types';
```

### TypeScript Standards
- Use **strict mode** TypeScript
- Define interfaces for all data structures
- Use proper type annotations
- Avoid `any` type unless absolutely necessary

### Testing Standards
```typescript
// Test descriptions and assertions in English
describe('MeterReadingService', () => {
  it('should calculate consumption correctly with valid readings', () => {
    // Test implementation with English comments
    const consumption = calculateConsumption(1000, 800);
    expect(consumption).toBe(200);
  });
  
  it('should throw error when reading values are negative', () => {
    // Error case testing with clear English descriptions
    expect(() => calculateConsumption(-100, 800))
      .toThrow('Reading values must be positive');
  });
});
```

## 🚫 What NOT to Do

### ❌ Avoid Chinese in Code
```typescript
// ❌ BAD - Chinese names and comments
const 抄表数据 = {
  读数: 1000,        // 当前读数
  时间: new Date()   // 抄表时间
};

// ✅ GOOD - English names and comments
const meterReading = {
  readingValue: 1000,     // Current meter reading
  timestamp: new Date()   // Reading timestamp
};
```

### ❌ Avoid Mixed Language
```typescript
// ❌ BAD - Mixed language
const getUserInfo = () => {
  // 获取用户信息
  return userService.fetchUser();
};

// ✅ GOOD - Consistent English
const getUserInfo = () => {
  // Fetch user information from service
  return userService.fetchUser();
};
```

## 📁 File Management Rules

### Avoid File Duplication
- **ALWAYS check existing files before creating new ones**
- **DO NOT create files with similar names** (e.g., if `MobileReadingController` exists, don't create `MeterReadingController`)
- **ADD logic to existing files** instead of creating new ones when possible
- **Use existing service/repository patterns** rather than creating parallel implementations

### File Creation Guidelines
```typescript
// ❌ BAD - Creating duplicate files
// If MobileReadingController.cs already exists, don't create:
// - MeterReadingController.cs
// - ReadingController.cs
// - MobileController.cs

// ✅ GOOD - Extend existing files
// Add new methods to existing MobileReadingController.cs
```

### Before Creating Any File
1. **Search the codebase** for similar functionality
2. **Check existing controllers/services/repositories** for related features
3. **Extend existing files** when the functionality is related
4. **Only create new files** when the functionality is completely different

### File Naming Consistency
- Use **consistent naming patterns** across the project
- Follow **existing conventions** in the codebase
- Avoid **redundant or overlapping names**

## 🎯 Enforcement

- **Pre-commit hooks** should check for Chinese characters in code files
- **Code reviews** must verify English-only code and comments
- **File creation reviews** must check for duplication and naming conflicts
- **CI/CD pipeline** should validate naming conventions
- **Documentation** should clearly separate technical content (English) from business content (Chinese)

---

**Remember: Code is international, communication is local! 代码国际化，交流本地化！**  
**Remember: Extend before create! 扩展优于新建！** 