<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ChangeListManager">
    <list default="true" id="b858e36b-7d0f-4579-b56e-8e77fcf0a228" name="Changes" comment="">
      <change beforePath="$PROJECT_DIR$/CORDE-Mobile-Application/android/app/build.gradle" beforeDir="false" afterPath="$PROJECT_DIR$/CORDE-Mobile-Application/android/app/build.gradle" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/CORDE-Mobile-Application/src/api/BaseApi.ts" beforeDir="false" afterPath="$PROJECT_DIR$/CORDE-Mobile-Application/src/api/BaseApi.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/deployment-config/docs/CORDE 2025每日工作记录.md" beforeDir="false" afterPath="$PROJECT_DIR$/deployment-config/docs/CORDE 2025每日工作记录.md" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/WaterMeterManagement" />
    <option name="ROOT_SYNC" value="DONT_SYNC" />
  </component>
  <component name="HighlightingSettingsPerFile">
    <setting file="file://$PROJECT_DIR$/WaterMeterManagement/Data/ApplicationDbContext.cs" root0="SKIP_HIGHLIGHTING" />
  </component>
  <component name="ProjectColorInfo"><![CDATA[{
  "associatedIndex": 7
}]]></component>
  <component name="ProjectId" id="31ol308oZPkxpBBVbOvdVLbL5yZ" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "git-widget-placeholder": "main",
    "nodejs_package_manager_path": "yarn",
    "settings.editor.selected.configurable": "preferences.keymap",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="b858e36b-7d0f-4579-b56e-8e77fcf0a228" name="Changes" comment="" />
      <created>1756198103536</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1756198103536</updated>
      <workItem from="1756198104868" duration="260000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="UnityProjectConfiguration" hasMinimizedUI="false" />
  <component name="VcsManagerConfiguration">
    <option name="CLEAR_INITIAL_COMMIT_MESSAGE" value="true" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <breakpoint enabled="true" type="DotNet_Exception_Breakpoints">
          <properties exception="System.OperationCanceledException" breakIfHandledByOtherCode="false" displayValue="System.OperationCanceledException" />
          <option name="timeStamp" value="1" />
        </breakpoint>
        <breakpoint enabled="true" type="DotNet_Exception_Breakpoints">
          <properties exception="System.Threading.Tasks.TaskCanceledException" breakIfHandledByOtherCode="false" displayValue="System.Threading.Tasks.TaskCanceledException" />
          <option name="timeStamp" value="2" />
        </breakpoint>
        <breakpoint enabled="true" type="DotNet_Exception_Breakpoints">
          <properties exception="System.Threading.ThreadAbortException" breakIfHandledByOtherCode="false" displayValue="System.Threading.ThreadAbortException" />
          <option name="timeStamp" value="3" />
        </breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
</project>