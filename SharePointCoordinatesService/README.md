# SharePoint Coordinates Service

ASP.NET Core 8.0 service for SharePoint Excel file synchronization with mobile applications.

## Features

- **SharePoint Integration**: Microsoft Graph API integration for SharePoint file access
- **PostgreSQL Database**: Entity Framework Core with database persistence
- **Paginated API**: Endpoints optimized for mobile application consumption
- **Basic Authentication**: Secure API access with Basic Auth middleware
- **Docker Support**: Full containerization with Docker Compose
- **Structured Logging**: Comprehensive logging with Serilog
- **Health Checks**: Service monitoring and health check endpoints
- **Caching**: In-memory caching with database persistence
- **Excel Processing**: Excel file parsing using EPPlus
- **UPSERT Operations**: Coordinate data synchronization with conflict resolution

## Architecture

- **Framework**: ASP.NET Core 8.0
- **Database**: PostgreSQL with `sharepoint_coords` schema
- **Authentication**: Basic Authentication (luke.shi:gentoo666)
- **Port**: 5088 (standalone, no nginx integration)
- **Caching**: Memory + Database persistence
- **File Processing**: EPPlus for Excel file parsing

## 🚀 SharePoint Coordinates Service 部署命令

### **1. 本地构建和推送镜像**
```bash
# 在项目根目录执行
docker build --platform linux/amd64 --no-cache -t *********:5001/sharepoint-coordinates-service:latest .
docker push *********:5001/sharepoint-coordinates-service:latest
```

### **2. 服务器部署**
```bash
# SSH 连接到服务器
ssh adminluke@*********

# 创建项目目录 (和 watermeter 同级)
sudo mkdir -p /opt/sharepoint-coordinates
sudo chown adminluke:adminluke /opt/sharepoint-coordinates

# 进入项目目录
cd /opt/sharepoint-coordinates

**# 停止容器**
docker stop sharepoint-coordinates-service

**# 删除容器**
docker rm sharepoint-coordinates-service

# 清理旧镜像 (如果存在)
docker rmi -f *********:5001/sharepoint-coordinates-service:latest

# 拉取最新镜像并启动服务
docker pull *********:5001/sharepoint-coordinates-service:latest
docker-compose up -d sharepoint-coordinates-service
```

### **3. 复制配置文件到服务器**

```bash
# 复制 docker-compose.yml 到服务器
scp docker-compose.yml adminluke@*********:/opt/sharepoint-coordinates/
```

### **4. 验证部署**
```bash
# 检查服务状态
docker ps | grep sharepoint-coordinates
docker logs sharepoint-coordinates-service

# 健康检查
curl http://localhost:5088/health
```

## 📋 总结

✅ **配置已更新：**
- 镜像标签改为 `*********:5001/sharepoint-coordinates-service:latest`
- 移除了 .env 文件，直接在 docker-compose.yml 中配置环境变量
- 使用和 WaterMeterManagement 相同的配置模式
- 数据库密码和其他配置直接写在 compose 文件中

✅ **部署方式：**
- 在 `/opt/sharepoint-coordinates` 创建独立项目目录
- 使用现有的 PostgreSQL 数据库，但创建独立的 schema
- 运行在端口 5088，不与 nginx 集成
- 使用相同的网络配置 (`network_mode: host`)
- **自动数据库初始化**：程序启动时自动创建数据库和表结构，无需手动执行 SQL 脚本

✅ **API访问地址总结：**
通过nginx代理的地址：
API接口：http://**************/sharepoint-api/coordinates/paged
Swagger文档：http://**************/sharepoint-swagger
健康检查：http://**************/sharepoint-health
直接访问地址（5088端口）：
API接口：http://**************:5088/api/coordinates/paged
Swagger文档：http://**************:5088/swagger
健康检查：http://**************:5088/health

## API Endpoints

### Core Endpoints
- `GET /api/coordinates` - Get all coordinates
- `GET /api/coordinates/paged?page=1&pageSize=1000` - Get paginated coordinates
- `POST /api/coordinates/refresh` - Manual cache refresh

### Health & Monitoring
- `GET /health` - Service health check
- `GET /api/coordinates/cache/status` - Cache status
- `DELETE /api/coordinates/cache` - Clear cache

## Configuration

The service is configured via environment variables in `docker-compose.yml`:

- **Database**: Uses existing PostgreSQL instance with `SharePointCoordinatesDb` database
- **SharePoint**: Connects to `https://siconnz.sharepoint.com/sites/DataProcessing`
- **Authentication**: Basic Auth with `luke.shi:gentoo666`
- **Caching**: 60-minute cache expiry with database persistence

## Mobile Integration

This service provides coordinate data to the CORDE Mobile Application via paginated APIs. The mobile app synchronizes coordinates using the `/api/coordinates/paged` endpoint with 1000 items per page.

## Development

```bash
# Local development
dotnet run --urls="http://localhost:5088"

# Build
dotnet build

# Run tests
dotnet test
```
