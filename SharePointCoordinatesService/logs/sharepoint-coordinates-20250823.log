[2025-08-23 20:22:42 INF] Starting SharePoint Coordinates Service {}
[2025-08-23 20:23:09 WRN] Failed to determine the https port for redirect. {"EventId":{"Id":3,"Name":"FailedToDeterminePort"},"SourceContext":"Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware","RequestId":"0HNF23P25USOD:********","RequestPath":"/api/Coordinates/refresh","ConnectionId":"0HNF23P25USOD"}
[2025-08-23 20:23:09 INF] Basic was not authenticated. Failure message: Missing Authorization Header {"EventId":{"Id":7,"Name":"AuthenticationSchemeNotAuthenticatedWithFailure"},"SourceContext":"SharePointCoordinatesService.Middlewares.BasicAuthenticationHandler","RequestId":"0HNF23P25USOD:********","RequestPath":"/api/Coordinates/refresh","ConnectionId":"0HNF23P25USOD"}
[2025-08-23 20:23:09 INF] AuthenticationScheme: Basic was challenged. {"EventId":{"Id":12,"Name":"AuthenticationSchemeChallenged"},"SourceContext":"SharePointCoordinatesService.Middlewares.BasicAuthenticationHandler","RequestId":"0HNF23P25USOD:********","RequestPath":"/api/Coordinates/refresh","ConnectionId":"0HNF23P25USOD"}
[2025-08-23 20:23:22 INF] Successfully authenticated user: luke.shi {"SourceContext":"SharePointCoordinatesService.Middlewares.BasicAuthenticationHandler","RequestId":"0HNF23P25USOD:00000004","RequestPath":"/api/Coordinates/refresh","ConnectionId":"0HNF23P25USOD"}
[2025-08-23 20:23:22 INF] API request: Refresh coordinates cache {"SourceContext":"SharePointCoordinatesService.Controllers.CoordinatesController","ActionId":"9c1927fa-a749-4d40-b949-ac65a914ae17","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.RefreshCoordinates (SharePointCoordinatesService)","RequestId":"0HNF23P25USOD:00000004","RequestPath":"/api/Coordinates/refresh","ConnectionId":"0HNF23P25USOD"}
[2025-08-23 20:23:22 INF] Manually refreshing coordinates cache {"SourceContext":"SharePointCoordinatesService.Services.CoordinatesService","ActionId":"9c1927fa-a749-4d40-b949-ac65a914ae17","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.RefreshCoordinates (SharePointCoordinatesService)","RequestId":"0HNF23P25USOD:00000004","RequestPath":"/api/Coordinates/refresh","ConnectionId":"0HNF23P25USOD"}
[2025-08-23 20:23:22 INF] Starting SharePoint sync process {"SourceContext":"SharePointCoordinatesService.Services.CoordinatesService","ActionId":"9c1927fa-a749-4d40-b949-ac65a914ae17","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.RefreshCoordinates (SharePointCoordinatesService)","RequestId":"0HNF23P25USOD:00000004","RequestPath":"/api/Coordinates/refresh","ConnectionId":"0HNF23P25USOD"}
[2025-08-23 20:23:22 INF] Starting to download file 'assets_coordinates.xlsx' from SharePoint {"SourceContext":"SharePointCoordinatesService.Services.SharePointFileService","ActionId":"9c1927fa-a749-4d40-b949-ac65a914ae17","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.RefreshCoordinates (SharePointCoordinatesService)","RequestId":"0HNF23P25USOD:00000004","RequestPath":"/api/Coordinates/refresh","ConnectionId":"0HNF23P25USOD"}
[2025-08-23 20:23:22 INF] Attempting to acquire access token for SharePoint {"SourceContext":"SharePointCoordinatesService.Services.SharePointAuthService","ActionId":"9c1927fa-a749-4d40-b949-ac65a914ae17","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.RefreshCoordinates (SharePointCoordinatesService)","RequestId":"0HNF23P25USOD:00000004","RequestPath":"/api/Coordinates/refresh","ConnectionId":"0HNF23P25USOD"}
[2025-08-23 20:23:23 INF] Successfully acquired access token {"SourceContext":"SharePointCoordinatesService.Services.SharePointAuthService","ActionId":"9c1927fa-a749-4d40-b949-ac65a914ae17","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.RefreshCoordinates (SharePointCoordinatesService)","RequestId":"0HNF23P25USOD:00000004","RequestPath":"/api/Coordinates/refresh","ConnectionId":"0HNF23P25USOD"}
[2025-08-23 20:23:24 INF] Successfully downloaded file using path '/Support/assets_coordinates.xlsx', size: 422488 bytes {"SourceContext":"SharePointCoordinatesService.Services.SharePointFileService","ActionId":"9c1927fa-a749-4d40-b949-ac65a914ae17","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.RefreshCoordinates (SharePointCoordinatesService)","RequestId":"0HNF23P25USOD:00000004","RequestPath":"/api/Coordinates/refresh","ConnectionId":"0HNF23P25USOD"}
[2025-08-23 20:23:24 INF] Starting to parse Excel file to coordinates {"SourceContext":"SharePointCoordinatesService.Services.SharePointFileService","ActionId":"9c1927fa-a749-4d40-b949-ac65a914ae17","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.RefreshCoordinates (SharePointCoordinatesService)","RequestId":"0HNF23P25USOD:00000004","RequestPath":"/api/Coordinates/refresh","ConnectionId":"0HNF23P25USOD"}
[2025-08-23 20:23:24 ERR] Failed to parse Excel file to coordinates {"SourceContext":"SharePointCoordinatesService.Services.SharePointFileService","ActionId":"9c1927fa-a749-4d40-b949-ac65a914ae17","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.RefreshCoordinates (SharePointCoordinatesService)","RequestId":"0HNF23P25USOD:00000004","RequestPath":"/api/Coordinates/refresh","ConnectionId":"0HNF23P25USOD"}
OfficeOpenXml.LicenseContextPropertyObsoleteException: Please use the static 'ExcelPackage.License' property to set the required license information from EPPlus 8 and later versions. For more info see http://epplussoftware.com/developers/licensenotsetexception.
   at OfficeOpenXml.ExcelPackage.set_LicenseContext(Nullable`1 value)
   at SharePointCoordinatesService.Services.SharePointFileService.ParseExcelToCoordinates(Byte[] excelBytes) in /Users/<USER>/CORDE/SharePointCoordinatesService/Services/SharePointFileService.cs:line 169
[2025-08-23 20:23:24 ERR] Failed to fetch and save coordinates from SharePoint {"SourceContext":"SharePointCoordinatesService.Services.CoordinatesService","ActionId":"9c1927fa-a749-4d40-b949-ac65a914ae17","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.RefreshCoordinates (SharePointCoordinatesService)","RequestId":"0HNF23P25USOD:00000004","RequestPath":"/api/Coordinates/refresh","ConnectionId":"0HNF23P25USOD"}
System.Exception: Failed to parse Excel file: Please use the static 'ExcelPackage.License' property to set the required license information from EPPlus 8 and later versions. For more info see http://epplussoftware.com/developers/licensenotsetexception.
 ---> OfficeOpenXml.LicenseContextPropertyObsoleteException: Please use the static 'ExcelPackage.License' property to set the required license information from EPPlus 8 and later versions. For more info see http://epplussoftware.com/developers/licensenotsetexception.
   at OfficeOpenXml.ExcelPackage.set_LicenseContext(Nullable`1 value)
   at SharePointCoordinatesService.Services.SharePointFileService.ParseExcelToCoordinates(Byte[] excelBytes) in /Users/<USER>/CORDE/SharePointCoordinatesService/Services/SharePointFileService.cs:line 169
   --- End of inner exception stack trace ---
   at SharePointCoordinatesService.Services.SharePointFileService.ParseExcelToCoordinates(Byte[] excelBytes) in /Users/<USER>/CORDE/SharePointCoordinatesService/Services/SharePointFileService.cs:line 217
   at SharePointCoordinatesService.Services.CoordinatesService.FetchAndSaveCoordinatesFromSharePointAsync() in /Users/<USER>/CORDE/SharePointCoordinatesService/Services/CoordinatesService.cs:line 126
[2025-08-23 20:23:24 ERR] Error refreshing coordinates cache {"SourceContext":"SharePointCoordinatesService.Services.CoordinatesService","ActionId":"9c1927fa-a749-4d40-b949-ac65a914ae17","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.RefreshCoordinates (SharePointCoordinatesService)","RequestId":"0HNF23P25USOD:00000004","RequestPath":"/api/Coordinates/refresh","ConnectionId":"0HNF23P25USOD"}
System.Exception: Failed to parse Excel file: Please use the static 'ExcelPackage.License' property to set the required license information from EPPlus 8 and later versions. For more info see http://epplussoftware.com/developers/licensenotsetexception.
 ---> OfficeOpenXml.LicenseContextPropertyObsoleteException: Please use the static 'ExcelPackage.License' property to set the required license information from EPPlus 8 and later versions. For more info see http://epplussoftware.com/developers/licensenotsetexception.
   at OfficeOpenXml.ExcelPackage.set_LicenseContext(Nullable`1 value)
   at SharePointCoordinatesService.Services.SharePointFileService.ParseExcelToCoordinates(Byte[] excelBytes) in /Users/<USER>/CORDE/SharePointCoordinatesService/Services/SharePointFileService.cs:line 169
   --- End of inner exception stack trace ---
   at SharePointCoordinatesService.Services.SharePointFileService.ParseExcelToCoordinates(Byte[] excelBytes) in /Users/<USER>/CORDE/SharePointCoordinatesService/Services/SharePointFileService.cs:line 217
   at SharePointCoordinatesService.Services.CoordinatesService.FetchAndSaveCoordinatesFromSharePointAsync() in /Users/<USER>/CORDE/SharePointCoordinatesService/Services/CoordinatesService.cs:line 126
   at SharePointCoordinatesService.Services.CoordinatesService.FetchAndSaveCoordinatesFromSharePointAsync() in /Users/<USER>/CORDE/SharePointCoordinatesService/Services/CoordinatesService.cs:line 164
   at SharePointCoordinatesService.Services.CoordinatesService.RefreshCacheAsync() in /Users/<USER>/CORDE/SharePointCoordinatesService/Services/CoordinatesService.cs:line 212
[2025-08-23 20:23:24 ERR] Error in RefreshCoordinates API {"SourceContext":"SharePointCoordinatesService.Controllers.CoordinatesController","ActionId":"9c1927fa-a749-4d40-b949-ac65a914ae17","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.RefreshCoordinates (SharePointCoordinatesService)","RequestId":"0HNF23P25USOD:00000004","RequestPath":"/api/Coordinates/refresh","ConnectionId":"0HNF23P25USOD"}
System.Exception: Failed to parse Excel file: Please use the static 'ExcelPackage.License' property to set the required license information from EPPlus 8 and later versions. For more info see http://epplussoftware.com/developers/licensenotsetexception.
 ---> OfficeOpenXml.LicenseContextPropertyObsoleteException: Please use the static 'ExcelPackage.License' property to set the required license information from EPPlus 8 and later versions. For more info see http://epplussoftware.com/developers/licensenotsetexception.
   at OfficeOpenXml.ExcelPackage.set_LicenseContext(Nullable`1 value)
   at SharePointCoordinatesService.Services.SharePointFileService.ParseExcelToCoordinates(Byte[] excelBytes) in /Users/<USER>/CORDE/SharePointCoordinatesService/Services/SharePointFileService.cs:line 169
   --- End of inner exception stack trace ---
   at SharePointCoordinatesService.Services.SharePointFileService.ParseExcelToCoordinates(Byte[] excelBytes) in /Users/<USER>/CORDE/SharePointCoordinatesService/Services/SharePointFileService.cs:line 217
   at SharePointCoordinatesService.Services.CoordinatesService.FetchAndSaveCoordinatesFromSharePointAsync() in /Users/<USER>/CORDE/SharePointCoordinatesService/Services/CoordinatesService.cs:line 126
   at SharePointCoordinatesService.Services.CoordinatesService.FetchAndSaveCoordinatesFromSharePointAsync() in /Users/<USER>/CORDE/SharePointCoordinatesService/Services/CoordinatesService.cs:line 164
   at SharePointCoordinatesService.Services.CoordinatesService.RefreshCacheAsync() in /Users/<USER>/CORDE/SharePointCoordinatesService/Services/CoordinatesService.cs:line 212
   at SharePointCoordinatesService.Controllers.CoordinatesController.RefreshCoordinates() in /Users/<USER>/CORDE/SharePointCoordinatesService/Controllers/CoordinatesController.cs:line 65
[2025-08-23 20:27:17 INF] Starting SharePoint Coordinates Service {}
[2025-08-23 20:27:50 INF] Starting SharePoint Coordinates Service {}
[2025-08-23 20:28:04 WRN] Failed to determine the https port for redirect. {"EventId":{"Id":3,"Name":"FailedToDeterminePort"},"SourceContext":"Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware","RequestId":"0HNF23RS469L3:********","RequestPath":"/api/Coordinates/refresh","ConnectionId":"0HNF23RS469L3"}
[2025-08-23 20:28:04 INF] Successfully authenticated user: luke.shi {"SourceContext":"SharePointCoordinatesService.Middlewares.BasicAuthenticationHandler","RequestId":"0HNF23RS469L3:********","RequestPath":"/api/Coordinates/refresh","ConnectionId":"0HNF23RS469L3"}
[2025-08-23 20:28:04 INF] API request: Refresh coordinates cache {"SourceContext":"SharePointCoordinatesService.Controllers.CoordinatesController","ActionId":"9f46783d-3dd8-4d91-af58-3c866a9a1f2a","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.RefreshCoordinates (SharePointCoordinatesService)","RequestId":"0HNF23RS469L3:********","RequestPath":"/api/Coordinates/refresh","ConnectionId":"0HNF23RS469L3"}
[2025-08-23 20:28:04 INF] Manually refreshing coordinates cache {"SourceContext":"SharePointCoordinatesService.Services.CoordinatesService","ActionId":"9f46783d-3dd8-4d91-af58-3c866a9a1f2a","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.RefreshCoordinates (SharePointCoordinatesService)","RequestId":"0HNF23RS469L3:********","RequestPath":"/api/Coordinates/refresh","ConnectionId":"0HNF23RS469L3"}
[2025-08-23 20:28:04 INF] Starting SharePoint sync process {"SourceContext":"SharePointCoordinatesService.Services.CoordinatesService","ActionId":"9f46783d-3dd8-4d91-af58-3c866a9a1f2a","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.RefreshCoordinates (SharePointCoordinatesService)","RequestId":"0HNF23RS469L3:********","RequestPath":"/api/Coordinates/refresh","ConnectionId":"0HNF23RS469L3"}
[2025-08-23 20:28:04 INF] Starting to download file 'assets_coordinates.xlsx' from SharePoint {"SourceContext":"SharePointCoordinatesService.Services.SharePointFileService","ActionId":"9f46783d-3dd8-4d91-af58-3c866a9a1f2a","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.RefreshCoordinates (SharePointCoordinatesService)","RequestId":"0HNF23RS469L3:********","RequestPath":"/api/Coordinates/refresh","ConnectionId":"0HNF23RS469L3"}
[2025-08-23 20:28:04 INF] Attempting to acquire access token for SharePoint {"SourceContext":"SharePointCoordinatesService.Services.SharePointAuthService","ActionId":"9f46783d-3dd8-4d91-af58-3c866a9a1f2a","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.RefreshCoordinates (SharePointCoordinatesService)","RequestId":"0HNF23RS469L3:********","RequestPath":"/api/Coordinates/refresh","ConnectionId":"0HNF23RS469L3"}
[2025-08-23 20:28:04 INF] Successfully acquired access token {"SourceContext":"SharePointCoordinatesService.Services.SharePointAuthService","ActionId":"9f46783d-3dd8-4d91-af58-3c866a9a1f2a","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.RefreshCoordinates (SharePointCoordinatesService)","RequestId":"0HNF23RS469L3:********","RequestPath":"/api/Coordinates/refresh","ConnectionId":"0HNF23RS469L3"}
[2025-08-23 20:28:05 INF] Successfully downloaded file using path '/Support/assets_coordinates.xlsx', size: 422488 bytes {"SourceContext":"SharePointCoordinatesService.Services.SharePointFileService","ActionId":"9f46783d-3dd8-4d91-af58-3c866a9a1f2a","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.RefreshCoordinates (SharePointCoordinatesService)","RequestId":"0HNF23RS469L3:********","RequestPath":"/api/Coordinates/refresh","ConnectionId":"0HNF23RS469L3"}
[2025-08-23 20:28:05 INF] Starting to parse Excel file to coordinates {"SourceContext":"SharePointCoordinatesService.Services.SharePointFileService","ActionId":"9f46783d-3dd8-4d91-af58-3c866a9a1f2a","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.RefreshCoordinates (SharePointCoordinatesService)","RequestId":"0HNF23RS469L3:********","RequestPath":"/api/Coordinates/refresh","ConnectionId":"0HNF23RS469L3"}
[2025-08-23 20:28:05 ERR] Failed to parse Excel file to coordinates {"SourceContext":"SharePointCoordinatesService.Services.SharePointFileService","ActionId":"9f46783d-3dd8-4d91-af58-3c866a9a1f2a","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.RefreshCoordinates (SharePointCoordinatesService)","RequestId":"0HNF23RS469L3:********","RequestPath":"/api/Coordinates/refresh","ConnectionId":"0HNF23RS469L3"}
OfficeOpenXml.InvalidLicenseKeyException: The license key is not in a valid format. Please use the license key as stated on your license document or as displayed on your account at https://epplussoftware.com
   at OfficeOpenXml.EPPlusLicense.SetCommercial(String licenseKey)
   at OfficeOpenXml.EPPlusLicense.SetLicenseFromConfig(List`1 initErrors)
   at OfficeOpenXml.EPPlusLicense.IsLicenseSet(List`1 initErrors)
   at OfficeOpenXml.ExcelPackage.get_Workbook()
   at SharePointCoordinatesService.Services.SharePointFileService.ParseExcelToCoordinates(Byte[] excelBytes) in /Users/<USER>/CORDE/SharePointCoordinatesService/Services/SharePointFileService.cs:line 174
[2025-08-23 20:28:05 ERR] Failed to fetch and save coordinates from SharePoint {"SourceContext":"SharePointCoordinatesService.Services.CoordinatesService","ActionId":"9f46783d-3dd8-4d91-af58-3c866a9a1f2a","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.RefreshCoordinates (SharePointCoordinatesService)","RequestId":"0HNF23RS469L3:********","RequestPath":"/api/Coordinates/refresh","ConnectionId":"0HNF23RS469L3"}
System.Exception: Failed to parse Excel file: The license key is not in a valid format. Please use the license key as stated on your license document or as displayed on your account at https://epplussoftware.com
 ---> OfficeOpenXml.InvalidLicenseKeyException: The license key is not in a valid format. Please use the license key as stated on your license document or as displayed on your account at https://epplussoftware.com
   at OfficeOpenXml.EPPlusLicense.SetCommercial(String licenseKey)
   at OfficeOpenXml.EPPlusLicense.SetLicenseFromConfig(List`1 initErrors)
   at OfficeOpenXml.EPPlusLicense.IsLicenseSet(List`1 initErrors)
   at OfficeOpenXml.ExcelPackage.get_Workbook()
   at SharePointCoordinatesService.Services.SharePointFileService.ParseExcelToCoordinates(Byte[] excelBytes) in /Users/<USER>/CORDE/SharePointCoordinatesService/Services/SharePointFileService.cs:line 174
   --- End of inner exception stack trace ---
   at SharePointCoordinatesService.Services.SharePointFileService.ParseExcelToCoordinates(Byte[] excelBytes) in /Users/<USER>/CORDE/SharePointCoordinatesService/Services/SharePointFileService.cs:line 217
   at SharePointCoordinatesService.Services.CoordinatesService.FetchAndSaveCoordinatesFromSharePointAsync() in /Users/<USER>/CORDE/SharePointCoordinatesService/Services/CoordinatesService.cs:line 126
[2025-08-23 20:28:05 ERR] Error refreshing coordinates cache {"SourceContext":"SharePointCoordinatesService.Services.CoordinatesService","ActionId":"9f46783d-3dd8-4d91-af58-3c866a9a1f2a","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.RefreshCoordinates (SharePointCoordinatesService)","RequestId":"0HNF23RS469L3:********","RequestPath":"/api/Coordinates/refresh","ConnectionId":"0HNF23RS469L3"}
System.Exception: Failed to parse Excel file: The license key is not in a valid format. Please use the license key as stated on your license document or as displayed on your account at https://epplussoftware.com
 ---> OfficeOpenXml.InvalidLicenseKeyException: The license key is not in a valid format. Please use the license key as stated on your license document or as displayed on your account at https://epplussoftware.com
   at OfficeOpenXml.EPPlusLicense.SetCommercial(String licenseKey)
   at OfficeOpenXml.EPPlusLicense.SetLicenseFromConfig(List`1 initErrors)
   at OfficeOpenXml.EPPlusLicense.IsLicenseSet(List`1 initErrors)
   at OfficeOpenXml.ExcelPackage.get_Workbook()
   at SharePointCoordinatesService.Services.SharePointFileService.ParseExcelToCoordinates(Byte[] excelBytes) in /Users/<USER>/CORDE/SharePointCoordinatesService/Services/SharePointFileService.cs:line 174
   --- End of inner exception stack trace ---
   at SharePointCoordinatesService.Services.SharePointFileService.ParseExcelToCoordinates(Byte[] excelBytes) in /Users/<USER>/CORDE/SharePointCoordinatesService/Services/SharePointFileService.cs:line 217
   at SharePointCoordinatesService.Services.CoordinatesService.FetchAndSaveCoordinatesFromSharePointAsync() in /Users/<USER>/CORDE/SharePointCoordinatesService/Services/CoordinatesService.cs:line 126
   at SharePointCoordinatesService.Services.CoordinatesService.FetchAndSaveCoordinatesFromSharePointAsync() in /Users/<USER>/CORDE/SharePointCoordinatesService/Services/CoordinatesService.cs:line 164
   at SharePointCoordinatesService.Services.CoordinatesService.RefreshCacheAsync() in /Users/<USER>/CORDE/SharePointCoordinatesService/Services/CoordinatesService.cs:line 212
[2025-08-23 20:28:05 ERR] Error in RefreshCoordinates API {"SourceContext":"SharePointCoordinatesService.Controllers.CoordinatesController","ActionId":"9f46783d-3dd8-4d91-af58-3c866a9a1f2a","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.RefreshCoordinates (SharePointCoordinatesService)","RequestId":"0HNF23RS469L3:********","RequestPath":"/api/Coordinates/refresh","ConnectionId":"0HNF23RS469L3"}
System.Exception: Failed to parse Excel file: The license key is not in a valid format. Please use the license key as stated on your license document or as displayed on your account at https://epplussoftware.com
 ---> OfficeOpenXml.InvalidLicenseKeyException: The license key is not in a valid format. Please use the license key as stated on your license document or as displayed on your account at https://epplussoftware.com
   at OfficeOpenXml.EPPlusLicense.SetCommercial(String licenseKey)
   at OfficeOpenXml.EPPlusLicense.SetLicenseFromConfig(List`1 initErrors)
   at OfficeOpenXml.EPPlusLicense.IsLicenseSet(List`1 initErrors)
   at OfficeOpenXml.ExcelPackage.get_Workbook()
   at SharePointCoordinatesService.Services.SharePointFileService.ParseExcelToCoordinates(Byte[] excelBytes) in /Users/<USER>/CORDE/SharePointCoordinatesService/Services/SharePointFileService.cs:line 174
   --- End of inner exception stack trace ---
   at SharePointCoordinatesService.Services.SharePointFileService.ParseExcelToCoordinates(Byte[] excelBytes) in /Users/<USER>/CORDE/SharePointCoordinatesService/Services/SharePointFileService.cs:line 217
   at SharePointCoordinatesService.Services.CoordinatesService.FetchAndSaveCoordinatesFromSharePointAsync() in /Users/<USER>/CORDE/SharePointCoordinatesService/Services/CoordinatesService.cs:line 126
   at SharePointCoordinatesService.Services.CoordinatesService.FetchAndSaveCoordinatesFromSharePointAsync() in /Users/<USER>/CORDE/SharePointCoordinatesService/Services/CoordinatesService.cs:line 164
   at SharePointCoordinatesService.Services.CoordinatesService.RefreshCacheAsync() in /Users/<USER>/CORDE/SharePointCoordinatesService/Services/CoordinatesService.cs:line 212
   at SharePointCoordinatesService.Controllers.CoordinatesController.RefreshCoordinates() in /Users/<USER>/CORDE/SharePointCoordinatesService/Controllers/CoordinatesController.cs:line 65
[2025-08-23 20:30:58 INF] Successfully authenticated user: luke.shi {"SourceContext":"SharePointCoordinatesService.Middlewares.BasicAuthenticationHandler","RequestId":"0HNF23RS469L5:********","RequestPath":"/api/Coordinates/refresh","ConnectionId":"0HNF23RS469L5"}
[2025-08-23 20:30:58 INF] API request: Refresh coordinates cache {"SourceContext":"SharePointCoordinatesService.Controllers.CoordinatesController","ActionId":"9f46783d-3dd8-4d91-af58-3c866a9a1f2a","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.RefreshCoordinates (SharePointCoordinatesService)","RequestId":"0HNF23RS469L5:********","RequestPath":"/api/Coordinates/refresh","ConnectionId":"0HNF23RS469L5"}
[2025-08-23 20:30:58 INF] Manually refreshing coordinates cache {"SourceContext":"SharePointCoordinatesService.Services.CoordinatesService","ActionId":"9f46783d-3dd8-4d91-af58-3c866a9a1f2a","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.RefreshCoordinates (SharePointCoordinatesService)","RequestId":"0HNF23RS469L5:********","RequestPath":"/api/Coordinates/refresh","ConnectionId":"0HNF23RS469L5"}
[2025-08-23 20:30:58 INF] Starting SharePoint sync process {"SourceContext":"SharePointCoordinatesService.Services.CoordinatesService","ActionId":"9f46783d-3dd8-4d91-af58-3c866a9a1f2a","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.RefreshCoordinates (SharePointCoordinatesService)","RequestId":"0HNF23RS469L5:********","RequestPath":"/api/Coordinates/refresh","ConnectionId":"0HNF23RS469L5"}
[2025-08-23 20:30:58 INF] Starting to download file 'assets_coordinates.xlsx' from SharePoint {"SourceContext":"SharePointCoordinatesService.Services.SharePointFileService","ActionId":"9f46783d-3dd8-4d91-af58-3c866a9a1f2a","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.RefreshCoordinates (SharePointCoordinatesService)","RequestId":"0HNF23RS469L5:********","RequestPath":"/api/Coordinates/refresh","ConnectionId":"0HNF23RS469L5"}
[2025-08-23 20:30:58 INF] Attempting to acquire access token for SharePoint {"SourceContext":"SharePointCoordinatesService.Services.SharePointAuthService","ActionId":"9f46783d-3dd8-4d91-af58-3c866a9a1f2a","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.RefreshCoordinates (SharePointCoordinatesService)","RequestId":"0HNF23RS469L5:********","RequestPath":"/api/Coordinates/refresh","ConnectionId":"0HNF23RS469L5"}
[2025-08-23 20:30:58 INF] Successfully acquired access token {"SourceContext":"SharePointCoordinatesService.Services.SharePointAuthService","ActionId":"9f46783d-3dd8-4d91-af58-3c866a9a1f2a","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.RefreshCoordinates (SharePointCoordinatesService)","RequestId":"0HNF23RS469L5:********","RequestPath":"/api/Coordinates/refresh","ConnectionId":"0HNF23RS469L5"}
[2025-08-23 20:30:59 INF] Successfully downloaded file using path '/Support/assets_coordinates.xlsx', size: 422488 bytes {"SourceContext":"SharePointCoordinatesService.Services.SharePointFileService","ActionId":"9f46783d-3dd8-4d91-af58-3c866a9a1f2a","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.RefreshCoordinates (SharePointCoordinatesService)","RequestId":"0HNF23RS469L5:********","RequestPath":"/api/Coordinates/refresh","ConnectionId":"0HNF23RS469L5"}
[2025-08-23 20:30:59 INF] Starting to parse Excel file to coordinates {"SourceContext":"SharePointCoordinatesService.Services.SharePointFileService","ActionId":"9f46783d-3dd8-4d91-af58-3c866a9a1f2a","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.RefreshCoordinates (SharePointCoordinatesService)","RequestId":"0HNF23RS469L5:********","RequestPath":"/api/Coordinates/refresh","ConnectionId":"0HNF23RS469L5"}
[2025-08-23 20:30:59 ERR] Failed to parse Excel file to coordinates {"SourceContext":"SharePointCoordinatesService.Services.SharePointFileService","ActionId":"9f46783d-3dd8-4d91-af58-3c866a9a1f2a","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.RefreshCoordinates (SharePointCoordinatesService)","RequestId":"0HNF23RS469L5:********","RequestPath":"/api/Coordinates/refresh","ConnectionId":"0HNF23RS469L5"}
OfficeOpenXml.InvalidLicenseKeyException: The license key is not in a valid format. Please use the license key as stated on your license document or as displayed on your account at https://epplussoftware.com
   at OfficeOpenXml.EPPlusLicense.SetCommercial(String licenseKey)
   at OfficeOpenXml.EPPlusLicense.SetLicenseFromConfig(List`1 initErrors)
   at OfficeOpenXml.EPPlusLicense.IsLicenseSet(List`1 initErrors)
   at OfficeOpenXml.ExcelPackage.get_Workbook()
   at SharePointCoordinatesService.Services.SharePointFileService.ParseExcelToCoordinates(Byte[] excelBytes) in /Users/<USER>/CORDE/SharePointCoordinatesService/Services/SharePointFileService.cs:line 174
[2025-08-23 20:30:59 ERR] Failed to fetch and save coordinates from SharePoint {"SourceContext":"SharePointCoordinatesService.Services.CoordinatesService","ActionId":"9f46783d-3dd8-4d91-af58-3c866a9a1f2a","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.RefreshCoordinates (SharePointCoordinatesService)","RequestId":"0HNF23RS469L5:********","RequestPath":"/api/Coordinates/refresh","ConnectionId":"0HNF23RS469L5"}
System.Exception: Failed to parse Excel file: The license key is not in a valid format. Please use the license key as stated on your license document or as displayed on your account at https://epplussoftware.com
 ---> OfficeOpenXml.InvalidLicenseKeyException: The license key is not in a valid format. Please use the license key as stated on your license document or as displayed on your account at https://epplussoftware.com
   at OfficeOpenXml.EPPlusLicense.SetCommercial(String licenseKey)
   at OfficeOpenXml.EPPlusLicense.SetLicenseFromConfig(List`1 initErrors)
   at OfficeOpenXml.EPPlusLicense.IsLicenseSet(List`1 initErrors)
   at OfficeOpenXml.ExcelPackage.get_Workbook()
   at SharePointCoordinatesService.Services.SharePointFileService.ParseExcelToCoordinates(Byte[] excelBytes) in /Users/<USER>/CORDE/SharePointCoordinatesService/Services/SharePointFileService.cs:line 174
   --- End of inner exception stack trace ---
   at SharePointCoordinatesService.Services.SharePointFileService.ParseExcelToCoordinates(Byte[] excelBytes) in /Users/<USER>/CORDE/SharePointCoordinatesService/Services/SharePointFileService.cs:line 217
   at SharePointCoordinatesService.Services.CoordinatesService.FetchAndSaveCoordinatesFromSharePointAsync() in /Users/<USER>/CORDE/SharePointCoordinatesService/Services/CoordinatesService.cs:line 126
[2025-08-23 20:30:59 ERR] Error refreshing coordinates cache {"SourceContext":"SharePointCoordinatesService.Services.CoordinatesService","ActionId":"9f46783d-3dd8-4d91-af58-3c866a9a1f2a","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.RefreshCoordinates (SharePointCoordinatesService)","RequestId":"0HNF23RS469L5:********","RequestPath":"/api/Coordinates/refresh","ConnectionId":"0HNF23RS469L5"}
System.Exception: Failed to parse Excel file: The license key is not in a valid format. Please use the license key as stated on your license document or as displayed on your account at https://epplussoftware.com
 ---> OfficeOpenXml.InvalidLicenseKeyException: The license key is not in a valid format. Please use the license key as stated on your license document or as displayed on your account at https://epplussoftware.com
   at OfficeOpenXml.EPPlusLicense.SetCommercial(String licenseKey)
   at OfficeOpenXml.EPPlusLicense.SetLicenseFromConfig(List`1 initErrors)
   at OfficeOpenXml.EPPlusLicense.IsLicenseSet(List`1 initErrors)
   at OfficeOpenXml.ExcelPackage.get_Workbook()
   at SharePointCoordinatesService.Services.SharePointFileService.ParseExcelToCoordinates(Byte[] excelBytes) in /Users/<USER>/CORDE/SharePointCoordinatesService/Services/SharePointFileService.cs:line 174
   --- End of inner exception stack trace ---
   at SharePointCoordinatesService.Services.SharePointFileService.ParseExcelToCoordinates(Byte[] excelBytes) in /Users/<USER>/CORDE/SharePointCoordinatesService/Services/SharePointFileService.cs:line 217
   at SharePointCoordinatesService.Services.CoordinatesService.FetchAndSaveCoordinatesFromSharePointAsync() in /Users/<USER>/CORDE/SharePointCoordinatesService/Services/CoordinatesService.cs:line 126
   at SharePointCoordinatesService.Services.CoordinatesService.FetchAndSaveCoordinatesFromSharePointAsync() in /Users/<USER>/CORDE/SharePointCoordinatesService/Services/CoordinatesService.cs:line 164
   at SharePointCoordinatesService.Services.CoordinatesService.RefreshCacheAsync() in /Users/<USER>/CORDE/SharePointCoordinatesService/Services/CoordinatesService.cs:line 212
[2025-08-23 20:30:59 ERR] Error in RefreshCoordinates API {"SourceContext":"SharePointCoordinatesService.Controllers.CoordinatesController","ActionId":"9f46783d-3dd8-4d91-af58-3c866a9a1f2a","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.RefreshCoordinates (SharePointCoordinatesService)","RequestId":"0HNF23RS469L5:********","RequestPath":"/api/Coordinates/refresh","ConnectionId":"0HNF23RS469L5"}
System.Exception: Failed to parse Excel file: The license key is not in a valid format. Please use the license key as stated on your license document or as displayed on your account at https://epplussoftware.com
 ---> OfficeOpenXml.InvalidLicenseKeyException: The license key is not in a valid format. Please use the license key as stated on your license document or as displayed on your account at https://epplussoftware.com
   at OfficeOpenXml.EPPlusLicense.SetCommercial(String licenseKey)
   at OfficeOpenXml.EPPlusLicense.SetLicenseFromConfig(List`1 initErrors)
   at OfficeOpenXml.EPPlusLicense.IsLicenseSet(List`1 initErrors)
   at OfficeOpenXml.ExcelPackage.get_Workbook()
   at SharePointCoordinatesService.Services.SharePointFileService.ParseExcelToCoordinates(Byte[] excelBytes) in /Users/<USER>/CORDE/SharePointCoordinatesService/Services/SharePointFileService.cs:line 174
   --- End of inner exception stack trace ---
   at SharePointCoordinatesService.Services.SharePointFileService.ParseExcelToCoordinates(Byte[] excelBytes) in /Users/<USER>/CORDE/SharePointCoordinatesService/Services/SharePointFileService.cs:line 217
   at SharePointCoordinatesService.Services.CoordinatesService.FetchAndSaveCoordinatesFromSharePointAsync() in /Users/<USER>/CORDE/SharePointCoordinatesService/Services/CoordinatesService.cs:line 126
   at SharePointCoordinatesService.Services.CoordinatesService.FetchAndSaveCoordinatesFromSharePointAsync() in /Users/<USER>/CORDE/SharePointCoordinatesService/Services/CoordinatesService.cs:line 164
   at SharePointCoordinatesService.Services.CoordinatesService.RefreshCacheAsync() in /Users/<USER>/CORDE/SharePointCoordinatesService/Services/CoordinatesService.cs:line 212
   at SharePointCoordinatesService.Controllers.CoordinatesController.RefreshCoordinates() in /Users/<USER>/CORDE/SharePointCoordinatesService/Controllers/CoordinatesController.cs:line 65
ck_unique
   --- End of inner exception stack trace ---
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.Consume(RelationalDataReader reader, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
[2025-08-23 20:29:46 ERR] Failed to save coordinates to database {"SourceContext":"SharePointCoordinatesService.Services.CoordinatesService","ActionId":"aa8a2479-dfd6-472c-b541-fe72e6185f6d","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.RefreshCoordinates (SharePointCoordinatesService)","RequestId":"0HNF23SRD8DNL:00000001","RequestPath":"/api/coordinates/refresh","ConnectionId":"0HNF23SRD8DNL"}
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Npgsql.PostgresException (0x80004005): 23505: duplicate key value violates unique constraint "IX_Coordinates_AmsKey"

DETAIL: Detail redacted as it may contain sensitive data. Specify 'Include Error Detail' in the connection string to include this information.
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.Consume(RelationalDataReader reader, Boolean async, CancellationToken cancellationToken)
  Exception data:
    Severity: ERROR
    SqlState: 23505
    MessageText: duplicate key value violates unique constraint "IX_Coordinates_AmsKey"
    Detail: Detail redacted as it may contain sensitive data. Specify 'Include Error Detail' in the connection string to include this information.
    SchemaName: sharepoint_coords
    TableName: Coordinates
    ConstraintName: IX_Coordinates_AmsKey
    File: nbtinsert.c
    Line: 673
    Routine: _bt_check_unique
   --- End of inner exception stack trace ---
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.Consume(RelationalDataReader reader, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at SharePointCoordinatesService.Services.CoordinatesService.SaveCoordinatesToDatabaseAsync(List`1 coordinates) in /Users/<USER>/CORDE/SharePointCoordinatesService/Services/CoordinatesService.cs:line 192
[2025-08-23 20:29:46 ERR] Failed to fetch and save coordinates from SharePoint {"SourceContext":"SharePointCoordinatesService.Services.CoordinatesService","ActionId":"aa8a2479-dfd6-472c-b541-fe72e6185f6d","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.RefreshCoordinates (SharePointCoordinatesService)","RequestId":"0HNF23SRD8DNL:00000001","RequestPath":"/api/coordinates/refresh","ConnectionId":"0HNF23SRD8DNL"}
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Npgsql.PostgresException (0x80004005): 23505: duplicate key value violates unique constraint "IX_Coordinates_AmsKey"

DETAIL: Detail redacted as it may contain sensitive data. Specify 'Include Error Detail' in the connection string to include this information.
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.Consume(RelationalDataReader reader, Boolean async, CancellationToken cancellationToken)
  Exception data:
    Severity: ERROR
    SqlState: 23505
    MessageText: duplicate key value violates unique constraint "IX_Coordinates_AmsKey"
    Detail: Detail redacted as it may contain sensitive data. Specify 'Include Error Detail' in the connection string to include this information.
    SchemaName: sharepoint_coords
    TableName: Coordinates
    ConstraintName: IX_Coordinates_AmsKey
    File: nbtinsert.c
    Line: 673
    Routine: _bt_check_unique
   --- End of inner exception stack trace ---
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.Consume(RelationalDataReader reader, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at SharePointCoordinatesService.Services.CoordinatesService.SaveCoordinatesToDatabaseAsync(List`1 coordinates) in /Users/<USER>/CORDE/SharePointCoordinatesService/Services/CoordinatesService.cs:line 192
   at SharePointCoordinatesService.Services.CoordinatesService.FetchAndSaveCoordinatesFromSharePointAsync() in /Users/<USER>/CORDE/SharePointCoordinatesService/Services/CoordinatesService.cs:line 143
[2025-08-23 20:29:47 ERR] An exception occurred in the database while saving changes for context type 'SharePointCoordinatesService.Data.SharePointCoordinatesDbContext'.
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Npgsql.PostgresException (0x80004005): 23505: duplicate key value violates unique constraint "IX_Coordinates_AmsKey"

DETAIL: Detail redacted as it may contain sensitive data. Specify 'Include Error Detail' in the connection string to include this information.
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.Consume(RelationalDataReader reader, Boolean async, CancellationToken cancellationToken)
  Exception data:
    Severity: ERROR
    SqlState: 23505
    MessageText: duplicate key value violates unique constraint "IX_Coordinates_AmsKey"
    Detail: Detail redacted as it may contain sensitive data. Specify 'Include Error Detail' in the connection string to include this information.
    SchemaName: sharepoint_coords
    TableName: Coordinates
    ConstraintName: IX_Coordinates_AmsKey
    File: nbtinsert.c
    Line: 673
    Routine: _bt_check_unique
   --- End of inner exception stack trace ---
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.Consume(RelationalDataReader reader, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken) {"EventId":{"Id":10000,"Name":"Microsoft.EntityFrameworkCore.Update.SaveChangesFailed"},"SourceContext":"Microsoft.EntityFrameworkCore.Update","ActionId":"aa8a2479-dfd6-472c-b541-fe72e6185f6d","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.RefreshCoordinates (SharePointCoordinatesService)","RequestId":"0HNF23SRD8DNL:00000001","RequestPath":"/api/coordinates/refresh","ConnectionId":"0HNF23SRD8DNL"}
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Npgsql.PostgresException (0x80004005): 23505: duplicate key value violates unique constraint "IX_Coordinates_AmsKey"

DETAIL: Detail redacted as it may contain sensitive data. Specify 'Include Error Detail' in the connection string to include this information.
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.Consume(RelationalDataReader reader, Boolean async, CancellationToken cancellationToken)
  Exception data:
    Severity: ERROR
    SqlState: 23505
    MessageText: duplicate key value violates unique constraint "IX_Coordinates_AmsKey"
    Detail: Detail redacted as it may contain sensitive data. Specify 'Include Error Detail' in the connection string to include this information.
    SchemaName: sharepoint_coords
    TableName: Coordinates
    ConstraintName: IX_Coordinates_AmsKey
    File: nbtinsert.c
    Line: 673
    Routine: _bt_check_unique
   --- End of inner exception stack trace ---
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.Consume(RelationalDataReader reader, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
[2025-08-23 20:29:47 ERR] Error refreshing coordinates cache {"SourceContext":"SharePointCoordinatesService.Services.CoordinatesService","ActionId":"aa8a2479-dfd6-472c-b541-fe72e6185f6d","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.RefreshCoordinates (SharePointCoordinatesService)","RequestId":"0HNF23SRD8DNL:00000001","RequestPath":"/api/coordinates/refresh","ConnectionId":"0HNF23SRD8DNL"}
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Npgsql.PostgresException (0x80004005): 23505: duplicate key value violates unique constraint "IX_Coordinates_AmsKey"

DETAIL: Detail redacted as it may contain sensitive data. Specify 'Include Error Detail' in the connection string to include this information.
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.Consume(RelationalDataReader reader, Boolean async, CancellationToken cancellationToken)
  Exception data:
    Severity: ERROR
    SqlState: 23505
    MessageText: duplicate key value violates unique constraint "IX_Coordinates_AmsKey"
    Detail: Detail redacted as it may contain sensitive data. Specify 'Include Error Detail' in the connection string to include this information.
    SchemaName: sharepoint_coords
    TableName: Coordinates
    ConstraintName: IX_Coordinates_AmsKey
    File: nbtinsert.c
    Line: 673
    Routine: _bt_check_unique
   --- End of inner exception stack trace ---
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.Consume(RelationalDataReader reader, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at SharePointCoordinatesService.Services.CoordinatesService.FetchAndSaveCoordinatesFromSharePointAsync() in /Users/<USER>/CORDE/SharePointCoordinatesService/Services/CoordinatesService.cs:line 162
   at SharePointCoordinatesService.Services.CoordinatesService.RefreshCacheAsync() in /Users/<USER>/CORDE/SharePointCoordinatesService/Services/CoordinatesService.cs:line 212
[2025-08-23 20:29:47 ERR] Error in RefreshCoordinates API {"SourceContext":"SharePointCoordinatesService.Controllers.CoordinatesController","ActionId":"aa8a2479-dfd6-472c-b541-fe72e6185f6d","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.RefreshCoordinates (SharePointCoordinatesService)","RequestId":"0HNF23SRD8DNL:00000001","RequestPath":"/api/coordinates/refresh","ConnectionId":"0HNF23SRD8DNL"}
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Npgsql.PostgresException (0x80004005): 23505: duplicate key value violates unique constraint "IX_Coordinates_AmsKey"

DETAIL: Detail redacted as it may contain sensitive data. Specify 'Include Error Detail' in the connection string to include this information.
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.Consume(RelationalDataReader reader, Boolean async, CancellationToken cancellationToken)
  Exception data:
    Severity: ERROR
    SqlState: 23505
    MessageText: duplicate key value violates unique constraint "IX_Coordinates_AmsKey"
    Detail: Detail redacted as it may contain sensitive data. Specify 'Include Error Detail' in the connection string to include this information.
    SchemaName: sharepoint_coords
    TableName: Coordinates
    ConstraintName: IX_Coordinates_AmsKey
    File: nbtinsert.c
    Line: 673
    Routine: _bt_check_unique
   --- End of inner exception stack trace ---
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.Consume(RelationalDataReader reader, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at SharePointCoordinatesService.Services.CoordinatesService.FetchAndSaveCoordinatesFromSharePointAsync() in /Users/<USER>/CORDE/SharePointCoordinatesService/Services/CoordinatesService.cs:line 162
   at SharePointCoordinatesService.Services.CoordinatesService.RefreshCacheAsync() in /Users/<USER>/CORDE/SharePointCoordinatesService/Services/CoordinatesService.cs:line 212
   at SharePointCoordinatesService.Controllers.CoordinatesController.RefreshCoordinates() in /Users/<USER>/CORDE/SharePointCoordinatesService/Controllers/CoordinatesController.cs:line 65
[2025-08-23 20:57:36 INF] Successfully authenticated user: luke.shi {"SourceContext":"SharePointCoordinatesService.Middlewares.BasicAuthenticationHandler","RequestId":"0HNF23SRD8DNM:00000001","RequestPath":"/api/coordinates/paged","ConnectionId":"0HNF23SRD8DNM"}
[2025-08-23 21:00:21 INF] Successfully authenticated user: luke.shi {"SourceContext":"SharePointCoordinatesService.Middlewares.BasicAuthenticationHandler","RequestId":"0HNF23SRD8DNN:00000001","RequestPath":"/api/coordinates/paged","ConnectionId":"0HNF23SRD8DNN"}
[2025-08-23 21:00:45 INF] Successfully authenticated user: luke.shi {"SourceContext":"SharePointCoordinatesService.Middlewares.BasicAuthenticationHandler","RequestId":"0HNF23SRD8DNO:00000001","RequestPath":"/api/coordinates/paged","ConnectionId":"0HNF23SRD8DNO"}
[2025-08-23 21:00:50 INF] Basic was not authenticated. Failure message: Missing Authorization Header {"EventId":{"Id":7,"Name":"AuthenticationSchemeNotAuthenticatedWithFailure"},"SourceContext":"SharePointCoordinatesService.Middlewares.BasicAuthenticationHandler","RequestId":"0HNF23SRD8DNP:00000001","RequestPath":"/health","ConnectionId":"0HNF23SRD8DNP"}
[2025-08-23 21:01:03 INF] Successfully authenticated user: luke.shi {"SourceContext":"SharePointCoordinatesService.Middlewares.BasicAuthenticationHandler","RequestId":"0HNF23SRD8DNQ:00000001","RequestPath":"/api/coordinates/paged","ConnectionId":"0HNF23SRD8DNQ"}
[2025-08-23 21:01:08 INF] Basic was not authenticated. Failure message: Missing Authorization Header {"EventId":{"Id":7,"Name":"AuthenticationSchemeNotAuthenticatedWithFailure"},"SourceContext":"SharePointCoordinatesService.Middlewares.BasicAuthenticationHandler","RequestId":"0HNF23SRD8DNR:00000001","RequestPath":"/swagger","ConnectionId":"0HNF23SRD8DNR"}
oller.RefreshCoordinates (SharePointCoordinatesService)","RequestId":"0HNF23TO00PRT:00000001","RequestPath":"/api/Coordinates/refresh","ConnectionId":"0HNF23TO00PRT"}
[2025-08-23 20:31:21 INF] Starting to download file 'assets_coordinates.xlsx' from SharePoint {"SourceContext":"SharePointCoordinatesService.Services.SharePointFileService","ActionId":"0f52c312-35b8-4763-b13f-0c3e545fe3c9","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.RefreshCoordinates (SharePointCoordinatesService)","RequestId":"0HNF23TO00PRT:00000001","RequestPath":"/api/Coordinates/refresh","ConnectionId":"0HNF23TO00PRT"}
[2025-08-23 20:31:21 INF] Attempting to acquire access token for SharePoint {"SourceContext":"SharePointCoordinatesService.Services.SharePointAuthService","ActionId":"0f52c312-35b8-4763-b13f-0c3e545fe3c9","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.RefreshCoordinates (SharePointCoordinatesService)","RequestId":"0HNF23TO00PRT:00000001","RequestPath":"/api/Coordinates/refresh","ConnectionId":"0HNF23TO00PRT"}
[2025-08-23 20:31:21 INF] Successfully acquired access token {"SourceContext":"SharePointCoordinatesService.Services.SharePointAuthService","ActionId":"0f52c312-35b8-4763-b13f-0c3e545fe3c9","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.RefreshCoordinates (SharePointCoordinatesService)","RequestId":"0HNF23TO00PRT:00000001","RequestPath":"/api/Coordinates/refresh","ConnectionId":"0HNF23TO00PRT"}
[2025-08-23 20:31:22 INF] Successfully downloaded file using path '/Support/assets_coordinates.xlsx', size: 422488 bytes {"SourceContext":"SharePointCoordinatesService.Services.SharePointFileService","ActionId":"0f52c312-35b8-4763-b13f-0c3e545fe3c9","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.RefreshCoordinates (SharePointCoordinatesService)","RequestId":"0HNF23TO00PRT:00000001","RequestPath":"/api/Coordinates/refresh","ConnectionId":"0HNF23TO00PRT"}
[2025-08-23 20:31:22 INF] Starting to parse Excel file to coordinates {"SourceContext":"SharePointCoordinatesService.Services.SharePointFileService","ActionId":"0f52c312-35b8-4763-b13f-0c3e545fe3c9","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.RefreshCoordinates (SharePointCoordinatesService)","RequestId":"0HNF23TO00PRT:00000001","RequestPath":"/api/Coordinates/refresh","ConnectionId":"0HNF23TO00PRT"}
[2025-08-23 20:31:22 INF] Successfully parsed 12014 coordinates from Excel file {"SourceContext":"SharePointCoordinatesService.Services.SharePointFileService","ActionId":"0f52c312-35b8-4763-b13f-0c3e545fe3c9","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.RefreshCoordinates (SharePointCoordinatesService)","RequestId":"0HNF23TO00PRT:00000001","RequestPath":"/api/Coordinates/refresh","ConnectionId":"0HNF23TO00PRT"}
[2025-08-23 20:31:22 INF] Successfully parsed 12014 coordinates from SharePoint {"SourceContext":"SharePointCoordinatesService.Services.CoordinatesService","ActionId":"0f52c312-35b8-4763-b13f-0c3e545fe3c9","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.RefreshCoordinates (SharePointCoordinatesService)","RequestId":"0HNF23TO00PRT:00000001","RequestPath":"/api/Coordinates/refresh","ConnectionId":"0HNF23TO00PRT"}
[2025-08-23 20:31:22 INF] Saving 12014 coordinates to database {"SourceContext":"SharePointCoordinatesService.Services.CoordinatesService","ActionId":"0f52c312-35b8-4763-b13f-0c3e545fe3c9","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.RefreshCoordinates (SharePointCoordinatesService)","RequestId":"0HNF23TO00PRT:00000001","RequestPath":"/api/Coordinates/refresh","ConnectionId":"0HNF23TO00PRT"}
[2025-08-23 20:31:22 ERR] An exception occurred in the database while saving changes for context type 'SharePointCoordinatesService.Data.SharePointCoordinatesDbContext'.
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Npgsql.PostgresException (0x80004005): 23505: duplicate key value violates unique constraint "IX_Coordinates_AmsKey"

DETAIL: Detail redacted as it may contain sensitive data. Specify 'Include Error Detail' in the connection string to include this information.
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.Consume(RelationalDataReader reader, Boolean async, CancellationToken cancellationToken)
  Exception data:
    Severity: ERROR
    SqlState: 23505
    MessageText: duplicate key value violates unique constraint "IX_Coordinates_AmsKey"
    Detail: Detail redacted as it may contain sensitive data. Specify 'Include Error Detail' in the connection string to include this information.
    SchemaName: sharepoint_coords
    TableName: Coordinates
    ConstraintName: IX_Coordinates_AmsKey
    File: nbtinsert.c
    Line: 673
    Routine: _bt_check_unique
   --- End of inner exception stack trace ---
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.Consume(RelationalDataReader reader, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken) {"EventId":{"Id":10000,"Name":"Microsoft.EntityFrameworkCore.Update.SaveChangesFailed"},"SourceContext":"Microsoft.EntityFrameworkCore.Update","ActionId":"0f52c312-35b8-4763-b13f-0c3e545fe3c9","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.RefreshCoordinates (SharePointCoordinatesService)","RequestId":"0HNF23TO00PRT:00000001","RequestPath":"/api/Coordinates/refresh","ConnectionId":"0HNF23TO00PRT"}
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Npgsql.PostgresException (0x80004005): 23505: duplicate key value violates unique constraint "IX_Coordinates_AmsKey"

DETAIL: Detail redacted as it may contain sensitive data. Specify 'Include Error Detail' in the connection string to include this information.
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.Consume(RelationalDataReader reader, Boolean async, CancellationToken cancellationToken)
  Exception data:
    Severity: ERROR
    SqlState: 23505
    MessageText: duplicate key value violates unique constraint "IX_Coordinates_AmsKey"
    Detail: Detail redacted as it may contain sensitive data. Specify 'Include Error Detail' in the connection string to include this information.
    SchemaName: sharepoint_coords
    TableName: Coordinates
    ConstraintName: IX_Coordinates_AmsKey
    File: nbtinsert.c
    Line: 673
    Routine: _bt_check_unique
   --- End of inner exception stack trace ---
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.Consume(RelationalDataReader reader, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
[2025-08-23 20:31:22 ERR] Failed to save coordinates to database {"SourceContext":"SharePointCoordinatesService.Services.CoordinatesService","ActionId":"0f52c312-35b8-4763-b13f-0c3e545fe3c9","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.RefreshCoordinates (SharePointCoordinatesService)","RequestId":"0HNF23TO00PRT:00000001","RequestPath":"/api/Coordinates/refresh","ConnectionId":"0HNF23TO00PRT"}
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Npgsql.PostgresException (0x80004005): 23505: duplicate key value violates unique constraint "IX_Coordinates_AmsKey"

DETAIL: Detail redacted as it may contain sensitive data. Specify 'Include Error Detail' in the connection string to include this information.
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.Consume(RelationalDataReader reader, Boolean async, CancellationToken cancellationToken)
  Exception data:
    Severity: ERROR
    SqlState: 23505
    MessageText: duplicate key value violates unique constraint "IX_Coordinates_AmsKey"
    Detail: Detail redacted as it may contain sensitive data. Specify 'Include Error Detail' in the connection string to include this information.
    SchemaName: sharepoint_coords
    TableName: Coordinates
    ConstraintName: IX_Coordinates_AmsKey
    File: nbtinsert.c
    Line: 673
    Routine: _bt_check_unique
   --- End of inner exception stack trace ---
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.Consume(RelationalDataReader reader, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at SharePointCoordinatesService.Services.CoordinatesService.SaveCoordinatesToDatabaseAsync(List`1 coordinates) in /Users/<USER>/CORDE/SharePointCoordinatesService/Services/CoordinatesService.cs:line 196
   at SharePointCoordinatesService.Services.CoordinatesService.SaveCoordinatesToDatabaseAsync(List`1 coordinates) in /Users/<USER>/CORDE/SharePointCoordinatesService/Services/CoordinatesService.cs:line 206
[2025-08-23 20:31:22 ERR] Failed to fetch and save coordinates from SharePoint {"SourceContext":"SharePointCoordinatesService.Services.CoordinatesService","ActionId":"0f52c312-35b8-4763-b13f-0c3e545fe3c9","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.RefreshCoordinates (SharePointCoordinatesService)","RequestId":"0HNF23TO00PRT:00000001","RequestPath":"/api/Coordinates/refresh","ConnectionId":"0HNF23TO00PRT"}
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Npgsql.PostgresException (0x80004005): 23505: duplicate key value violates unique constraint "IX_Coordinates_AmsKey"

DETAIL: Detail redacted as it may contain sensitive data. Specify 'Include Error Detail' in the connection string to include this information.
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.Consume(RelationalDataReader reader, Boolean async, CancellationToken cancellationToken)
  Exception data:
    Severity: ERROR
    SqlState: 23505
    MessageText: duplicate key value violates unique constraint "IX_Coordinates_AmsKey"
    Detail: Detail redacted as it may contain sensitive data. Specify 'Include Error Detail' in the connection string to include this information.
    SchemaName: sharepoint_coords
    TableName: Coordinates
    ConstraintName: IX_Coordinates_AmsKey
    File: nbtinsert.c
    Line: 673
    Routine: _bt_check_unique
   --- End of inner exception stack trace ---
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.Consume(RelationalDataReader reader, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at SharePointCoordinatesService.Services.CoordinatesService.SaveCoordinatesToDatabaseAsync(List`1 coordinates) in /Users/<USER>/CORDE/SharePointCoordinatesService/Services/CoordinatesService.cs:line 196
   at SharePointCoordinatesService.Services.CoordinatesService.SaveCoordinatesToDatabaseAsync(List`1 coordinates) in /Users/<USER>/CORDE/SharePointCoordinatesService/Services/CoordinatesService.cs:line 206
   at SharePointCoordinatesService.Services.CoordinatesService.FetchAndSaveCoordinatesFromSharePointAsync() in /Users/<USER>/CORDE/SharePointCoordinatesService/Services/CoordinatesService.cs:line 143
[2025-08-23 20:31:22 ERR] An exception occurred in the database while saving changes for context type 'SharePointCoordinatesService.Data.SharePointCoordinatesDbContext'.
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Npgsql.PostgresException (0x80004005): 23505: duplicate key value violates unique constraint "IX_Coordinates_AmsKey"

DETAIL: Detail redacted as it may contain sensitive data. Specify 'Include Error Detail' in the connection string to include this information.
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.Consume(RelationalDataReader reader, Boolean async, CancellationToken cancellationToken)
  Exception data:
    Severity: ERROR
    SqlState: 23505
    MessageText: duplicate key value violates unique constraint "IX_Coordinates_AmsKey"
    Detail: Detail redacted as it may contain sensitive data. Specify 'Include Error Detail' in the connection string to include this information.
    SchemaName: sharepoint_coords
    TableName: Coordinates
    ConstraintName: IX_Coordinates_AmsKey
    File: nbtinsert.c
    Line: 673
    Routine: _bt_check_unique
   --- End of inner exception stack trace ---
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.Consume(RelationalDataReader reader, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken) {"EventId":{"Id":10000,"Name":"Microsoft.EntityFrameworkCore.Update.SaveChangesFailed"},"SourceContext":"Microsoft.EntityFrameworkCore.Update","ActionId":"0f52c312-35b8-4763-b13f-0c3e545fe3c9","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.RefreshCoordinates (SharePointCoordinatesService)","RequestId":"0HNF23TO00PRT:00000001","RequestPath":"/api/Coordinates/refresh","ConnectionId":"0HNF23TO00PRT"}
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Npgsql.PostgresException (0x80004005): 23505: duplicate key value violates unique constraint "IX_Coordinates_AmsKey"

DETAIL: Detail redacted as it may contain sensitive data. Specify 'Include Error Detail' in the connection string to include this information.
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.Consume(RelationalDataReader reader, Boolean async, CancellationToken cancellationToken)
  Exception data:
    Severity: ERROR
    SqlState: 23505
    MessageText: duplicate key value violates unique constraint "IX_Coordinates_AmsKey"
    Detail: Detail redacted as it may contain sensitive data. Specify 'Include Error Detail' in the connection string to include this information.
    SchemaName: sharepoint_coords
    TableName: Coordinates
    ConstraintName: IX_Coordinates_AmsKey
    File: nbtinsert.c
    Line: 673
    Routine: _bt_check_unique
   --- End of inner exception stack trace ---
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.Consume(RelationalDataReader reader, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
[2025-08-23 20:31:22 ERR] Error refreshing coordinates cache {"SourceContext":"SharePointCoordinatesService.Services.CoordinatesService","ActionId":"0f52c312-35b8-4763-b13f-0c3e545fe3c9","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.RefreshCoordinates (SharePointCoordinatesService)","RequestId":"0HNF23TO00PRT:00000001","RequestPath":"/api/Coordinates/refresh","ConnectionId":"0HNF23TO00PRT"}
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Npgsql.PostgresException (0x80004005): 23505: duplicate key value violates unique constraint "IX_Coordinates_AmsKey"

DETAIL: Detail redacted as it may contain sensitive data. Specify 'Include Error Detail' in the connection string to include this information.
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.Consume(RelationalDataReader reader, Boolean async, CancellationToken cancellationToken)
  Exception data:
    Severity: ERROR
    SqlState: 23505
    MessageText: duplicate key value violates unique constraint "IX_Coordinates_AmsKey"
    Detail: Detail redacted as it may contain sensitive data. Specify 'Include Error Detail' in the connection string to include this information.
    SchemaName: sharepoint_coords
    TableName: Coordinates
    ConstraintName: IX_Coordinates_AmsKey
    File: nbtinsert.c
    Line: 673
    Routine: _bt_check_unique
   --- End of inner exception stack trace ---
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.Consume(RelationalDataReader reader, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at SharePointCoordinatesService.Services.CoordinatesService.FetchAndSaveCoordinatesFromSharePointAsync() in /Users/<USER>/CORDE/SharePointCoordinatesService/Services/CoordinatesService.cs:line 162
   at SharePointCoordinatesService.Services.CoordinatesService.RefreshCacheAsync() in /Users/<USER>/CORDE/SharePointCoordinatesService/Services/CoordinatesService.cs:line 226
[2025-08-23 20:31:22 ERR] Error in RefreshCoordinates API {"SourceContext":"SharePointCoordinatesService.Controllers.CoordinatesController","ActionId":"0f52c312-35b8-4763-b13f-0c3e545fe3c9","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.RefreshCoordinates (SharePointCoordinatesService)","RequestId":"0HNF23TO00PRT:00000001","RequestPath":"/api/Coordinates/refresh","ConnectionId":"0HNF23TO00PRT"}
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Npgsql.PostgresException (0x80004005): 23505: duplicate key value violates unique constraint "IX_Coordinates_AmsKey"

DETAIL: Detail redacted as it may contain sensitive data. Specify 'Include Error Detail' in the connection string to include this information.
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.Consume(RelationalDataReader reader, Boolean async, CancellationToken cancellationToken)
  Exception data:
    Severity: ERROR
    SqlState: 23505
    MessageText: duplicate key value violates unique constraint "IX_Coordinates_AmsKey"
    Detail: Detail redacted as it may contain sensitive data. Specify 'Include Error Detail' in the connection string to include this information.
    SchemaName: sharepoint_coords
    TableName: Coordinates
    ConstraintName: IX_Coordinates_AmsKey
    File: nbtinsert.c
    Line: 673
    Routine: _bt_check_unique
   --- End of inner exception stack trace ---
   at Npgsql.EntityFrameworkCore.PostgreSQL.Update.Internal.NpgsqlModificationCommandBatch.Consume(RelationalDataReader reader, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at SharePointCoordinatesService.Services.CoordinatesService.FetchAndSaveCoordinatesFromSharePointAsync() in /Users/<USER>/CORDE/SharePointCoordinatesService/Services/CoordinatesService.cs:line 162
   at SharePointCoordinatesService.Services.CoordinatesService.RefreshCacheAsync() in /Users/<USER>/CORDE/SharePointCoordinatesService/Services/CoordinatesService.cs:line 226
   at SharePointCoordinatesService.Controllers.CoordinatesController.RefreshCoordinates() in /Users/<USER>/CORDE/SharePointCoordinatesService/Controllers/CoordinatesController.cs:line 65
[2025-08-23 20:32:55 INF] Starting SharePoint Coordinates Service {}
[2025-08-23 20:33:00 WRN] Failed to determine the https port for redirect. {"EventId":{"Id":3,"Name":"FailedToDeterminePort"},"SourceContext":"Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware","RequestId":"0HNF23ULIV6DJ:00000001","RequestPath":"/api/Coordinates/refresh","ConnectionId":"0HNF23ULIV6DJ"}
[2025-08-23 20:33:00 INF] Successfully authenticated user: luke.shi {"SourceContext":"SharePointCoordinatesService.Middlewares.BasicAuthenticationHandler","RequestId":"0HNF23ULIV6DJ:00000001","RequestPath":"/api/Coordinates/refresh","ConnectionId":"0HNF23ULIV6DJ"}
[2025-08-23 20:33:00 INF] API request: Refresh coordinates cache {"SourceContext":"SharePointCoordinatesService.Controllers.CoordinatesController","ActionId":"2491b3f5-0618-4c63-856c-90a4ae06dd6b","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.RefreshCoordinates (SharePointCoordinatesService)","RequestId":"0HNF23ULIV6DJ:00000001","RequestPath":"/api/Coordinates/refresh","ConnectionId":"0HNF23ULIV6DJ"}
[2025-08-23 20:33:00 INF] Manually refreshing coordinates cache {"SourceContext":"SharePointCoordinatesService.Services.CoordinatesService","ActionId":"2491b3f5-0618-4c63-856c-90a4ae06dd6b","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.RefreshCoordinates (SharePointCoordinatesService)","RequestId":"0HNF23ULIV6DJ:00000001","RequestPath":"/api/Coordinates/refresh","ConnectionId":"0HNF23ULIV6DJ"}
[2025-08-23 20:33:00 INF] Starting SharePoint sync process {"SourceContext":"SharePointCoordinatesService.Services.CoordinatesService","ActionId":"2491b3f5-0618-4c63-856c-90a4ae06dd6b","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.RefreshCoordinates (SharePointCoordinatesService)","RequestId":"0HNF23ULIV6DJ:00000001","RequestPath":"/api/Coordinates/refresh","ConnectionId":"0HNF23ULIV6DJ"}
[2025-08-23 20:33:00 INF] Starting to download file 'assets_coordinates.xlsx' from SharePoint {"SourceContext":"SharePointCoordinatesService.Services.SharePointFileService","ActionId":"2491b3f5-0618-4c63-856c-90a4ae06dd6b","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.RefreshCoordinates (SharePointCoordinatesService)","RequestId":"0HNF23ULIV6DJ:00000001","RequestPath":"/api/Coordinates/refresh","ConnectionId":"0HNF23ULIV6DJ"}
[2025-08-23 20:33:00 INF] Attempting to acquire access token for SharePoint {"SourceContext":"SharePointCoordinatesService.Services.SharePointAuthService","ActionId":"2491b3f5-0618-4c63-856c-90a4ae06dd6b","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.RefreshCoordinates (SharePointCoordinatesService)","RequestId":"0HNF23ULIV6DJ:00000001","RequestPath":"/api/Coordinates/refresh","ConnectionId":"0HNF23ULIV6DJ"}
[2025-08-23 20:33:00 INF] Successfully acquired access token {"SourceContext":"SharePointCoordinatesService.Services.SharePointAuthService","ActionId":"2491b3f5-0618-4c63-856c-90a4ae06dd6b","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.RefreshCoordinates (SharePointCoordinatesService)","RequestId":"0HNF23ULIV6DJ:00000001","RequestPath":"/api/Coordinates/refresh","ConnectionId":"0HNF23ULIV6DJ"}
[2025-08-23 20:33:01 INF] Successfully downloaded file using path '/Support/assets_coordinates.xlsx', size: 422488 bytes {"SourceContext":"SharePointCoordinatesService.Services.SharePointFileService","ActionId":"2491b3f5-0618-4c63-856c-90a4ae06dd6b","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.RefreshCoordinates (SharePointCoordinatesService)","RequestId":"0HNF23ULIV6DJ:00000001","RequestPath":"/api/Coordinates/refresh","ConnectionId":"0HNF23ULIV6DJ"}
[2025-08-23 20:33:01 INF] Starting to parse Excel file to coordinates {"SourceContext":"SharePointCoordinatesService.Services.SharePointFileService","ActionId":"2491b3f5-0618-4c63-856c-90a4ae06dd6b","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.RefreshCoordinates (SharePointCoordinatesService)","RequestId":"0HNF23ULIV6DJ:00000001","RequestPath":"/api/Coordinates/refresh","ConnectionId":"0HNF23ULIV6DJ"}
[2025-08-23 20:33:01 INF] Successfully parsed 12014 coordinates from Excel file {"SourceContext":"SharePointCoordinatesService.Services.SharePointFileService","ActionId":"2491b3f5-0618-4c63-856c-90a4ae06dd6b","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.RefreshCoordinates (SharePointCoordinatesService)","RequestId":"0HNF23ULIV6DJ:00000001","RequestPath":"/api/Coordinates/refresh","ConnectionId":"0HNF23ULIV6DJ"}
[2025-08-23 20:33:01 INF] Successfully parsed 12014 coordinates from SharePoint {"SourceContext":"SharePointCoordinatesService.Services.CoordinatesService","ActionId":"2491b3f5-0618-4c63-856c-90a4ae06dd6b","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.RefreshCoordinates (SharePointCoordinatesService)","RequestId":"0HNF23ULIV6DJ:00000001","RequestPath":"/api/Coordinates/refresh","ConnectionId":"0HNF23ULIV6DJ"}
[2025-08-23 20:33:01 INF] Saving 12014 coordinates to database using UPSERT {"SourceContext":"SharePointCoordinatesService.Services.CoordinatesService","ActionId":"2491b3f5-0618-4c63-856c-90a4ae06dd6b","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.RefreshCoordinates (SharePointCoordinatesService)","RequestId":"0HNF23ULIV6DJ:00000001","RequestPath":"/api/Coordinates/refresh","ConnectionId":"0HNF23ULIV6DJ"}
[2025-08-23 20:33:03 INF] Successfully upserted 12014 coordinates to database {"SourceContext":"SharePointCoordinatesService.Services.CoordinatesService","ActionId":"2491b3f5-0618-4c63-856c-90a4ae06dd6b","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.RefreshCoordinates (SharePointCoordinatesService)","RequestId":"0HNF23ULIV6DJ:00000001","RequestPath":"/api/Coordinates/refresh","ConnectionId":"0HNF23ULIV6DJ"}
[2025-08-23 20:33:03 INF] Successfully synced 12014 coordinates to database {"SourceContext":"SharePointCoordinatesService.Services.CoordinatesService","ActionId":"2491b3f5-0618-4c63-856c-90a4ae06dd6b","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.RefreshCoordinates (SharePointCoordinatesService)","RequestId":"0HNF23ULIV6DJ:00000001","RequestPath":"/api/Coordinates/refresh","ConnectionId":"0HNF23ULIV6DJ"}
[2025-08-23 20:33:03 INF] Successfully refreshed cache with 12014 coordinates {"SourceContext":"SharePointCoordinatesService.Controllers.CoordinatesController","ActionId":"2491b3f5-0618-4c63-856c-90a4ae06dd6b","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.RefreshCoordinates (SharePointCoordinatesService)","RequestId":"0HNF23ULIV6DJ:00000001","RequestPath":"/api/Coordinates/refresh","ConnectionId":"0HNF23ULIV6DJ"}
[2025-08-23 20:34:13 INF] Basic was not authenticated. Failure message: Missing Authorization Header {"EventId":{"Id":7,"Name":"AuthenticationSchemeNotAuthenticatedWithFailure"},"SourceContext":"SharePointCoordinatesService.Middlewares.BasicAuthenticationHandler","RequestId":"0HNF23ULIV6DJ:00000005","RequestPath":"/api/Coordinates","ConnectionId":"0HNF23ULIV6DJ"}
[2025-08-23 20:34:13 INF] AuthenticationScheme: Basic was challenged. {"EventId":{"Id":12,"Name":"AuthenticationSchemeChallenged"},"SourceContext":"SharePointCoordinatesService.Middlewares.BasicAuthenticationHandler","RequestId":"0HNF23ULIV6DJ:00000005","RequestPath":"/api/Coordinates","ConnectionId":"0HNF23ULIV6DJ"}
[2025-08-23 20:34:13 INF] Successfully authenticated user: luke.shi {"SourceContext":"SharePointCoordinatesService.Middlewares.BasicAuthenticationHandler","RequestId":"0HNF23ULIV6DJ:00000006","RequestPath":"/api/Coordinates","ConnectionId":"0HNF23ULIV6DJ"}
[2025-08-23 20:34:13 INF] API request: Get coordinates {"SourceContext":"SharePointCoordinatesService.Controllers.CoordinatesController","ActionId":"aa2c0aa9-04e8-4cb9-a147-a028dfd35757","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.GetCoordinates (SharePointCoordinatesService)","RequestId":"0HNF23ULIV6DJ:00000006","RequestPath":"/api/Coordinates","ConnectionId":"0HNF23ULIV6DJ"}
[2025-08-23 20:34:13 INF] Starting to get coordinates data {"SourceContext":"SharePointCoordinatesService.Services.CoordinatesService","ActionId":"aa2c0aa9-04e8-4cb9-a147-a028dfd35757","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.GetCoordinates (SharePointCoordinatesService)","RequestId":"0HNF23ULIV6DJ:00000006","RequestPath":"/api/Coordinates","ConnectionId":"0HNF23ULIV6DJ"}
[2025-08-23 20:34:13 INF] Returning memory cached coordinates data with 12014 items {"SourceContext":"SharePointCoordinatesService.Services.CoordinatesService","ActionId":"aa2c0aa9-04e8-4cb9-a147-a028dfd35757","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.GetCoordinates (SharePointCoordinatesService)","RequestId":"0HNF23ULIV6DJ:00000006","RequestPath":"/api/Coordinates","ConnectionId":"0HNF23ULIV6DJ"}
[2025-08-23 20:34:13 INF] Successfully returned 12014 coordinates {"SourceContext":"SharePointCoordinatesService.Controllers.CoordinatesController","ActionId":"aa2c0aa9-04e8-4cb9-a147-a028dfd35757","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.GetCoordinates (SharePointCoordinatesService)","RequestId":"0HNF23ULIV6DJ:00000006","RequestPath":"/api/Coordinates","ConnectionId":"0HNF23ULIV6DJ"}
[2025-08-23 20:34:39 INF] Successfully authenticated user: luke.shi {"SourceContext":"SharePointCoordinatesService.Middlewares.BasicAuthenticationHandler","RequestId":"0HNF23ULIV6DJ:00000007","RequestPath":"/api/Coordinates/refresh","ConnectionId":"0HNF23ULIV6DJ"}
[2025-08-23 20:34:39 INF] API request: Refresh coordinates cache {"SourceContext":"SharePointCoordinatesService.Controllers.CoordinatesController","ActionId":"2491b3f5-0618-4c63-856c-90a4ae06dd6b","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.RefreshCoordinates (SharePointCoordinatesService)","RequestId":"0HNF23ULIV6DJ:00000007","RequestPath":"/api/Coordinates/refresh","ConnectionId":"0HNF23ULIV6DJ"}
[2025-08-23 20:34:39 INF] Manually refreshing coordinates cache {"SourceContext":"SharePointCoordinatesService.Services.CoordinatesService","ActionId":"2491b3f5-0618-4c63-856c-90a4ae06dd6b","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.RefreshCoordinates (SharePointCoordinatesService)","RequestId":"0HNF23ULIV6DJ:00000007","RequestPath":"/api/Coordinates/refresh","ConnectionId":"0HNF23ULIV6DJ"}
[2025-08-23 20:34:39 INF] Starting SharePoint sync process {"SourceContext":"SharePointCoordinatesService.Services.CoordinatesService","ActionId":"2491b3f5-0618-4c63-856c-90a4ae06dd6b","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.RefreshCoordinates (SharePointCoordinatesService)","RequestId":"0HNF23ULIV6DJ:00000007","RequestPath":"/api/Coordinates/refresh","ConnectionId":"0HNF23ULIV6DJ"}
[2025-08-23 20:34:39 INF] Starting to download file 'assets_coordinates.xlsx' from SharePoint {"SourceContext":"SharePointCoordinatesService.Services.SharePointFileService","ActionId":"2491b3f5-0618-4c63-856c-90a4ae06dd6b","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.RefreshCoordinates (SharePointCoordinatesService)","RequestId":"0HNF23ULIV6DJ:00000007","RequestPath":"/api/Coordinates/refresh","ConnectionId":"0HNF23ULIV6DJ"}
[2025-08-23 20:34:39 INF] Attempting to acquire access token for SharePoint {"SourceContext":"SharePointCoordinatesService.Services.SharePointAuthService","ActionId":"2491b3f5-0618-4c63-856c-90a4ae06dd6b","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.RefreshCoordinates (SharePointCoordinatesService)","RequestId":"0HNF23ULIV6DJ:00000007","RequestPath":"/api/Coordinates/refresh","ConnectionId":"0HNF23ULIV6DJ"}
[2025-08-23 20:34:40 INF] Successfully acquired access token {"SourceContext":"SharePointCoordinatesService.Services.SharePointAuthService","ActionId":"2491b3f5-0618-4c63-856c-90a4ae06dd6b","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.RefreshCoordinates (SharePointCoordinatesService)","RequestId":"0HNF23ULIV6DJ:00000007","RequestPath":"/api/Coordinates/refresh","ConnectionId":"0HNF23ULIV6DJ"}
[2025-08-23 20:34:40 INF] Successfully downloaded file using path '/Support/assets_coordinates.xlsx', size: 422488 bytes {"SourceContext":"SharePointCoordinatesService.Services.SharePointFileService","ActionId":"2491b3f5-0618-4c63-856c-90a4ae06dd6b","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.RefreshCoordinates (SharePointCoordinatesService)","RequestId":"0HNF23ULIV6DJ:00000007","RequestPath":"/api/Coordinates/refresh","ConnectionId":"0HNF23ULIV6DJ"}
[2025-08-23 20:34:40 INF] Starting to parse Excel file to coordinates {"SourceContext":"SharePointCoordinatesService.Services.SharePointFileService","ActionId":"2491b3f5-0618-4c63-856c-90a4ae06dd6b","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.RefreshCoordinates (SharePointCoordinatesService)","RequestId":"0HNF23ULIV6DJ:00000007","RequestPath":"/api/Coordinates/refresh","ConnectionId":"0HNF23ULIV6DJ"}
[2025-08-23 20:34:41 INF] Successfully parsed 12014 coordinates from Excel file {"SourceContext":"SharePointCoordinatesService.Services.SharePointFileService","ActionId":"2491b3f5-0618-4c63-856c-90a4ae06dd6b","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.RefreshCoordinates (SharePointCoordinatesService)","RequestId":"0HNF23ULIV6DJ:00000007","RequestPath":"/api/Coordinates/refresh","ConnectionId":"0HNF23ULIV6DJ"}
[2025-08-23 20:34:41 INF] Successfully parsed 12014 coordinates from SharePoint {"SourceContext":"SharePointCoordinatesService.Services.CoordinatesService","ActionId":"2491b3f5-0618-4c63-856c-90a4ae06dd6b","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.RefreshCoordinates (SharePointCoordinatesService)","RequestId":"0HNF23ULIV6DJ:00000007","RequestPath":"/api/Coordinates/refresh","ConnectionId":"0HNF23ULIV6DJ"}
[2025-08-23 20:34:41 INF] Saving 12014 coordinates to database using UPSERT {"SourceContext":"SharePointCoordinatesService.Services.CoordinatesService","ActionId":"2491b3f5-0618-4c63-856c-90a4ae06dd6b","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.RefreshCoordinates (SharePointCoordinatesService)","RequestId":"0HNF23ULIV6DJ:00000007","RequestPath":"/api/Coordinates/refresh","ConnectionId":"0HNF23ULIV6DJ"}
[2025-08-23 20:34:42 INF] Successfully upserted 12014 coordinates to database {"SourceContext":"SharePointCoordinatesService.Services.CoordinatesService","ActionId":"2491b3f5-0618-4c63-856c-90a4ae06dd6b","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.RefreshCoordinates (SharePointCoordinatesService)","RequestId":"0HNF23ULIV6DJ:00000007","RequestPath":"/api/Coordinates/refresh","ConnectionId":"0HNF23ULIV6DJ"}
[2025-08-23 20:34:42 INF] Successfully synced 12014 coordinates to database {"SourceContext":"SharePointCoordinatesService.Services.CoordinatesService","ActionId":"2491b3f5-0618-4c63-856c-90a4ae06dd6b","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.RefreshCoordinates (SharePointCoordinatesService)","RequestId":"0HNF23ULIV6DJ:00000007","RequestPath":"/api/Coordinates/refresh","ConnectionId":"0HNF23ULIV6DJ"}
[2025-08-23 20:34:42 INF] Successfully refreshed cache with 12014 coordinates {"SourceContext":"SharePointCoordinatesService.Controllers.CoordinatesController","ActionId":"2491b3f5-0618-4c63-856c-90a4ae06dd6b","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.RefreshCoordinates (SharePointCoordinatesService)","RequestId":"0HNF23ULIV6DJ:00000007","RequestPath":"/api/Coordinates/refresh","ConnectionId":"0HNF23ULIV6DJ"}
[2025-08-23 20:38:25 INF] Starting SharePoint Coordinates Service {}
[2025-08-23 21:01:50 INF] Starting SharePoint Coordinates Service {}
[2025-08-23 21:02:39 INF] Starting SharePoint Coordinates Service {}
[2025-08-23 21:03:18 WRN] Failed to determine the https port for redirect. {"EventId":{"Id":3,"Name":"FailedToDeterminePort"},"SourceContext":"Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware","RequestId":"0HNF24FJFJ5JB:00000001","RequestPath":"/api/coordinates/paged","ConnectionId":"0HNF24FJFJ5JB"}
[2025-08-23 21:03:18 INF] Successfully authenticated user: luke.shi {"SourceContext":"SharePointCoordinatesService.Middlewares.BasicAuthenticationHandler","RequestId":"0HNF24FJFJ5JB:00000001","RequestPath":"/api/coordinates/paged","ConnectionId":"0HNF24FJFJ5JB"}
[2025-08-23 21:03:18 INF] API request: Get paged coordinates - Page: 1, PageSize: 1000 {"SourceContext":"SharePointCoordinatesService.Controllers.CoordinatesController","ActionId":"003bd5ee-f419-4aeb-b5b0-d631a449f079","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.GetCoordinatesPaged (SharePointCoordinatesService)","RequestId":"0HNF24FJFJ5JB:00000001","RequestPath":"/api/coordinates/paged","ConnectionId":"0HNF24FJFJ5JB"}
[2025-08-23 21:03:18 INF] Getting paged coordinates - Page: 1, PageSize: 1000 {"SourceContext":"SharePointCoordinatesService.Services.CoordinatesService","ActionId":"003bd5ee-f419-4aeb-b5b0-d631a449f079","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.GetCoordinatesPaged (SharePointCoordinatesService)","RequestId":"0HNF24FJFJ5JB:00000001","RequestPath":"/api/coordinates/paged","ConnectionId":"0HNF24FJFJ5JB"}
[2025-08-23 21:03:18 INF] Starting to get coordinates data {"SourceContext":"SharePointCoordinatesService.Services.CoordinatesService","ActionId":"003bd5ee-f419-4aeb-b5b0-d631a449f079","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.GetCoordinatesPaged (SharePointCoordinatesService)","RequestId":"0HNF24FJFJ5JB:00000001","RequestPath":"/api/coordinates/paged","ConnectionId":"0HNF24FJFJ5JB"}
[2025-08-23 21:03:18 INF] Memory cache miss, checking database {"SourceContext":"SharePointCoordinatesService.Services.CoordinatesService","ActionId":"003bd5ee-f419-4aeb-b5b0-d631a449f079","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.GetCoordinatesPaged (SharePointCoordinatesService)","RequestId":"0HNF24FJFJ5JB:00000001","RequestPath":"/api/coordinates/paged","ConnectionId":"0HNF24FJFJ5JB"}
[2025-08-23 21:03:18 INF] Found 11703 valid coordinates in database {"SourceContext":"SharePointCoordinatesService.Services.CoordinatesService","ActionId":"003bd5ee-f419-4aeb-b5b0-d631a449f079","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.GetCoordinatesPaged (SharePointCoordinatesService)","RequestId":"0HNF24FJFJ5JB:00000001","RequestPath":"/api/coordinates/paged","ConnectionId":"0HNF24FJFJ5JB"}
[2025-08-23 21:03:18 INF] Successfully returned page 1 with 1000 coordinates {"SourceContext":"SharePointCoordinatesService.Controllers.CoordinatesController","ActionId":"003bd5ee-f419-4aeb-b5b0-d631a449f079","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.GetCoordinatesPaged (SharePointCoordinatesService)","RequestId":"0HNF24FJFJ5JB:00000001","RequestPath":"/api/coordinates/paged","ConnectionId":"0HNF24FJFJ5JB"}
[2025-08-23 21:03:19 INF] Successfully authenticated user: luke.shi {"SourceContext":"SharePointCoordinatesService.Middlewares.BasicAuthenticationHandler","RequestId":"0HNF24FJFJ5JB:00000002","RequestPath":"/api/coordinates/paged","ConnectionId":"0HNF24FJFJ5JB"}
[2025-08-23 21:03:19 INF] API request: Get paged coordinates - Page: 2, PageSize: 1000 {"SourceContext":"SharePointCoordinatesService.Controllers.CoordinatesController","ActionId":"003bd5ee-f419-4aeb-b5b0-d631a449f079","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.GetCoordinatesPaged (SharePointCoordinatesService)","RequestId":"0HNF24FJFJ5JB:00000002","RequestPath":"/api/coordinates/paged","ConnectionId":"0HNF24FJFJ5JB"}
[2025-08-23 21:03:19 INF] Getting paged coordinates - Page: 2, PageSize: 1000 {"SourceContext":"SharePointCoordinatesService.Services.CoordinatesService","ActionId":"003bd5ee-f419-4aeb-b5b0-d631a449f079","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.GetCoordinatesPaged (SharePointCoordinatesService)","RequestId":"0HNF24FJFJ5JB:00000002","RequestPath":"/api/coordinates/paged","ConnectionId":"0HNF24FJFJ5JB"}
[2025-08-23 21:03:19 INF] Starting to get coordinates data {"SourceContext":"SharePointCoordinatesService.Services.CoordinatesService","ActionId":"003bd5ee-f419-4aeb-b5b0-d631a449f079","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.GetCoordinatesPaged (SharePointCoordinatesService)","RequestId":"0HNF24FJFJ5JB:00000002","RequestPath":"/api/coordinates/paged","ConnectionId":"0HNF24FJFJ5JB"}
[2025-08-23 21:03:19 INF] Returning memory cached coordinates data with 11703 items {"SourceContext":"SharePointCoordinatesService.Services.CoordinatesService","ActionId":"003bd5ee-f419-4aeb-b5b0-d631a449f079","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.GetCoordinatesPaged (SharePointCoordinatesService)","RequestId":"0HNF24FJFJ5JB:00000002","RequestPath":"/api/coordinates/paged","ConnectionId":"0HNF24FJFJ5JB"}
[2025-08-23 21:03:19 INF] Successfully returned page 2 with 1000 coordinates {"SourceContext":"SharePointCoordinatesService.Controllers.CoordinatesController","ActionId":"003bd5ee-f419-4aeb-b5b0-d631a449f079","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.GetCoordinatesPaged (SharePointCoordinatesService)","RequestId":"0HNF24FJFJ5JB:00000002","RequestPath":"/api/coordinates/paged","ConnectionId":"0HNF24FJFJ5JB"}
[2025-08-23 21:03:19 INF] Successfully authenticated user: luke.shi {"SourceContext":"SharePointCoordinatesService.Middlewares.BasicAuthenticationHandler","RequestId":"0HNF24FJFJ5JB:********","RequestPath":"/api/coordinates/paged","ConnectionId":"0HNF24FJFJ5JB"}
[2025-08-23 21:03:19 INF] API request: Get paged coordinates - Page: 3, PageSize: 1000 {"SourceContext":"SharePointCoordinatesService.Controllers.CoordinatesController","ActionId":"003bd5ee-f419-4aeb-b5b0-d631a449f079","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.GetCoordinatesPaged (SharePointCoordinatesService)","RequestId":"0HNF24FJFJ5JB:********","RequestPath":"/api/coordinates/paged","ConnectionId":"0HNF24FJFJ5JB"}
[2025-08-23 21:03:19 INF] Getting paged coordinates - Page: 3, PageSize: 1000 {"SourceContext":"SharePointCoordinatesService.Services.CoordinatesService","ActionId":"003bd5ee-f419-4aeb-b5b0-d631a449f079","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.GetCoordinatesPaged (SharePointCoordinatesService)","RequestId":"0HNF24FJFJ5JB:********","RequestPath":"/api/coordinates/paged","ConnectionId":"0HNF24FJFJ5JB"}
[2025-08-23 21:03:19 INF] Starting to get coordinates data {"SourceContext":"SharePointCoordinatesService.Services.CoordinatesService","ActionId":"003bd5ee-f419-4aeb-b5b0-d631a449f079","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.GetCoordinatesPaged (SharePointCoordinatesService)","RequestId":"0HNF24FJFJ5JB:********","RequestPath":"/api/coordinates/paged","ConnectionId":"0HNF24FJFJ5JB"}
[2025-08-23 21:03:19 INF] Returning memory cached coordinates data with 11703 items {"SourceContext":"SharePointCoordinatesService.Services.CoordinatesService","ActionId":"003bd5ee-f419-4aeb-b5b0-d631a449f079","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.GetCoordinatesPaged (SharePointCoordinatesService)","RequestId":"0HNF24FJFJ5JB:********","RequestPath":"/api/coordinates/paged","ConnectionId":"0HNF24FJFJ5JB"}
[2025-08-23 21:03:19 INF] Successfully returned page 3 with 1000 coordinates {"SourceContext":"SharePointCoordinatesService.Controllers.CoordinatesController","ActionId":"003bd5ee-f419-4aeb-b5b0-d631a449f079","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.GetCoordinatesPaged (SharePointCoordinatesService)","RequestId":"0HNF24FJFJ5JB:********","RequestPath":"/api/coordinates/paged","ConnectionId":"0HNF24FJFJ5JB"}
[2025-08-23 21:03:19 INF] Successfully authenticated user: luke.shi {"SourceContext":"SharePointCoordinatesService.Middlewares.BasicAuthenticationHandler","RequestId":"0HNF24FJFJ5JB:00000004","RequestPath":"/api/coordinates/paged","ConnectionId":"0HNF24FJFJ5JB"}
[2025-08-23 21:03:19 INF] API request: Get paged coordinates - Page: 4, PageSize: 1000 {"SourceContext":"SharePointCoordinatesService.Controllers.CoordinatesController","ActionId":"003bd5ee-f419-4aeb-b5b0-d631a449f079","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.GetCoordinatesPaged (SharePointCoordinatesService)","RequestId":"0HNF24FJFJ5JB:00000004","RequestPath":"/api/coordinates/paged","ConnectionId":"0HNF24FJFJ5JB"}
[2025-08-23 21:03:19 INF] Getting paged coordinates - Page: 4, PageSize: 1000 {"SourceContext":"SharePointCoordinatesService.Services.CoordinatesService","ActionId":"003bd5ee-f419-4aeb-b5b0-d631a449f079","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.GetCoordinatesPaged (SharePointCoordinatesService)","RequestId":"0HNF24FJFJ5JB:00000004","RequestPath":"/api/coordinates/paged","ConnectionId":"0HNF24FJFJ5JB"}
[2025-08-23 21:03:19 INF] Starting to get coordinates data {"SourceContext":"SharePointCoordinatesService.Services.CoordinatesService","ActionId":"003bd5ee-f419-4aeb-b5b0-d631a449f079","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.GetCoordinatesPaged (SharePointCoordinatesService)","RequestId":"0HNF24FJFJ5JB:00000004","RequestPath":"/api/coordinates/paged","ConnectionId":"0HNF24FJFJ5JB"}
[2025-08-23 21:03:19 INF] Returning memory cached coordinates data with 11703 items {"SourceContext":"SharePointCoordinatesService.Services.CoordinatesService","ActionId":"003bd5ee-f419-4aeb-b5b0-d631a449f079","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.GetCoordinatesPaged (SharePointCoordinatesService)","RequestId":"0HNF24FJFJ5JB:00000004","RequestPath":"/api/coordinates/paged","ConnectionId":"0HNF24FJFJ5JB"}
[2025-08-23 21:03:19 INF] Successfully returned page 4 with 1000 coordinates {"SourceContext":"SharePointCoordinatesService.Controllers.CoordinatesController","ActionId":"003bd5ee-f419-4aeb-b5b0-d631a449f079","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.GetCoordinatesPaged (SharePointCoordinatesService)","RequestId":"0HNF24FJFJ5JB:00000004","RequestPath":"/api/coordinates/paged","ConnectionId":"0HNF24FJFJ5JB"}
[2025-08-23 21:03:19 INF] Successfully authenticated user: luke.shi {"SourceContext":"SharePointCoordinatesService.Middlewares.BasicAuthenticationHandler","RequestId":"0HNF24FJFJ5JB:00000005","RequestPath":"/api/coordinates/paged","ConnectionId":"0HNF24FJFJ5JB"}
[2025-08-23 21:03:19 INF] API request: Get paged coordinates - Page: 5, PageSize: 1000 {"SourceContext":"SharePointCoordinatesService.Controllers.CoordinatesController","ActionId":"003bd5ee-f419-4aeb-b5b0-d631a449f079","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.GetCoordinatesPaged (SharePointCoordinatesService)","RequestId":"0HNF24FJFJ5JB:00000005","RequestPath":"/api/coordinates/paged","ConnectionId":"0HNF24FJFJ5JB"}
[2025-08-23 21:03:19 INF] Getting paged coordinates - Page: 5, PageSize: 1000 {"SourceContext":"SharePointCoordinatesService.Services.CoordinatesService","ActionId":"003bd5ee-f419-4aeb-b5b0-d631a449f079","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.GetCoordinatesPaged (SharePointCoordinatesService)","RequestId":"0HNF24FJFJ5JB:00000005","RequestPath":"/api/coordinates/paged","ConnectionId":"0HNF24FJFJ5JB"}
[2025-08-23 21:03:19 INF] Starting to get coordinates data {"SourceContext":"SharePointCoordinatesService.Services.CoordinatesService","ActionId":"003bd5ee-f419-4aeb-b5b0-d631a449f079","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.GetCoordinatesPaged (SharePointCoordinatesService)","RequestId":"0HNF24FJFJ5JB:00000005","RequestPath":"/api/coordinates/paged","ConnectionId":"0HNF24FJFJ5JB"}
[2025-08-23 21:03:19 INF] Returning memory cached coordinates data with 11703 items {"SourceContext":"SharePointCoordinatesService.Services.CoordinatesService","ActionId":"003bd5ee-f419-4aeb-b5b0-d631a449f079","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.GetCoordinatesPaged (SharePointCoordinatesService)","RequestId":"0HNF24FJFJ5JB:00000005","RequestPath":"/api/coordinates/paged","ConnectionId":"0HNF24FJFJ5JB"}
[2025-08-23 21:03:19 INF] Successfully returned page 5 with 1000 coordinates {"SourceContext":"SharePointCoordinatesService.Controllers.CoordinatesController","ActionId":"003bd5ee-f419-4aeb-b5b0-d631a449f079","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.GetCoordinatesPaged (SharePointCoordinatesService)","RequestId":"0HNF24FJFJ5JB:00000005","RequestPath":"/api/coordinates/paged","ConnectionId":"0HNF24FJFJ5JB"}
[2025-08-23 21:03:19 INF] Successfully authenticated user: luke.shi {"SourceContext":"SharePointCoordinatesService.Middlewares.BasicAuthenticationHandler","RequestId":"0HNF24FJFJ5JB:00000006","RequestPath":"/api/coordinates/paged","ConnectionId":"0HNF24FJFJ5JB"}
[2025-08-23 21:03:19 INF] API request: Get paged coordinates - Page: 6, PageSize: 1000 {"SourceContext":"SharePointCoordinatesService.Controllers.CoordinatesController","ActionId":"003bd5ee-f419-4aeb-b5b0-d631a449f079","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.GetCoordinatesPaged (SharePointCoordinatesService)","RequestId":"0HNF24FJFJ5JB:00000006","RequestPath":"/api/coordinates/paged","ConnectionId":"0HNF24FJFJ5JB"}
[2025-08-23 21:03:19 INF] Getting paged coordinates - Page: 6, PageSize: 1000 {"SourceContext":"SharePointCoordinatesService.Services.CoordinatesService","ActionId":"003bd5ee-f419-4aeb-b5b0-d631a449f079","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.GetCoordinatesPaged (SharePointCoordinatesService)","RequestId":"0HNF24FJFJ5JB:00000006","RequestPath":"/api/coordinates/paged","ConnectionId":"0HNF24FJFJ5JB"}
[2025-08-23 21:03:19 INF] Starting to get coordinates data {"SourceContext":"SharePointCoordinatesService.Services.CoordinatesService","ActionId":"003bd5ee-f419-4aeb-b5b0-d631a449f079","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.GetCoordinatesPaged (SharePointCoordinatesService)","RequestId":"0HNF24FJFJ5JB:00000006","RequestPath":"/api/coordinates/paged","ConnectionId":"0HNF24FJFJ5JB"}
[2025-08-23 21:03:19 INF] Returning memory cached coordinates data with 11703 items {"SourceContext":"SharePointCoordinatesService.Services.CoordinatesService","ActionId":"003bd5ee-f419-4aeb-b5b0-d631a449f079","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.GetCoordinatesPaged (SharePointCoordinatesService)","RequestId":"0HNF24FJFJ5JB:00000006","RequestPath":"/api/coordinates/paged","ConnectionId":"0HNF24FJFJ5JB"}
[2025-08-23 21:03:19 INF] Successfully returned page 6 with 1000 coordinates {"SourceContext":"SharePointCoordinatesService.Controllers.CoordinatesController","ActionId":"003bd5ee-f419-4aeb-b5b0-d631a449f079","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.GetCoordinatesPaged (SharePointCoordinatesService)","RequestId":"0HNF24FJFJ5JB:00000006","RequestPath":"/api/coordinates/paged","ConnectionId":"0HNF24FJFJ5JB"}
[2025-08-23 21:03:19 INF] Successfully authenticated user: luke.shi {"SourceContext":"SharePointCoordinatesService.Middlewares.BasicAuthenticationHandler","RequestId":"0HNF24FJFJ5JB:00000007","RequestPath":"/api/coordinates/paged","ConnectionId":"0HNF24FJFJ5JB"}
[2025-08-23 21:03:19 INF] API request: Get paged coordinates - Page: 7, PageSize: 1000 {"SourceContext":"SharePointCoordinatesService.Controllers.CoordinatesController","ActionId":"003bd5ee-f419-4aeb-b5b0-d631a449f079","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.GetCoordinatesPaged (SharePointCoordinatesService)","RequestId":"0HNF24FJFJ5JB:00000007","RequestPath":"/api/coordinates/paged","ConnectionId":"0HNF24FJFJ5JB"}
[2025-08-23 21:03:19 INF] Getting paged coordinates - Page: 7, PageSize: 1000 {"SourceContext":"SharePointCoordinatesService.Services.CoordinatesService","ActionId":"003bd5ee-f419-4aeb-b5b0-d631a449f079","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.GetCoordinatesPaged (SharePointCoordinatesService)","RequestId":"0HNF24FJFJ5JB:00000007","RequestPath":"/api/coordinates/paged","ConnectionId":"0HNF24FJFJ5JB"}
[2025-08-23 21:03:19 INF] Starting to get coordinates data {"SourceContext":"SharePointCoordinatesService.Services.CoordinatesService","ActionId":"003bd5ee-f419-4aeb-b5b0-d631a449f079","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.GetCoordinatesPaged (SharePointCoordinatesService)","RequestId":"0HNF24FJFJ5JB:00000007","RequestPath":"/api/coordinates/paged","ConnectionId":"0HNF24FJFJ5JB"}
[2025-08-23 21:03:19 INF] Returning memory cached coordinates data with 11703 items {"SourceContext":"SharePointCoordinatesService.Services.CoordinatesService","ActionId":"003bd5ee-f419-4aeb-b5b0-d631a449f079","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.GetCoordinatesPaged (SharePointCoordinatesService)","RequestId":"0HNF24FJFJ5JB:00000007","RequestPath":"/api/coordinates/paged","ConnectionId":"0HNF24FJFJ5JB"}
[2025-08-23 21:03:19 INF] Successfully returned page 7 with 1000 coordinates {"SourceContext":"SharePointCoordinatesService.Controllers.CoordinatesController","ActionId":"003bd5ee-f419-4aeb-b5b0-d631a449f079","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.GetCoordinatesPaged (SharePointCoordinatesService)","RequestId":"0HNF24FJFJ5JB:00000007","RequestPath":"/api/coordinates/paged","ConnectionId":"0HNF24FJFJ5JB"}
[2025-08-23 21:03:19 INF] Successfully authenticated user: luke.shi {"SourceContext":"SharePointCoordinatesService.Middlewares.BasicAuthenticationHandler","RequestId":"0HNF24FJFJ5JB:00000008","RequestPath":"/api/coordinates/paged","ConnectionId":"0HNF24FJFJ5JB"}
[2025-08-23 21:03:19 INF] API request: Get paged coordinates - Page: 8, PageSize: 1000 {"SourceContext":"SharePointCoordinatesService.Controllers.CoordinatesController","ActionId":"003bd5ee-f419-4aeb-b5b0-d631a449f079","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.GetCoordinatesPaged (SharePointCoordinatesService)","RequestId":"0HNF24FJFJ5JB:00000008","RequestPath":"/api/coordinates/paged","ConnectionId":"0HNF24FJFJ5JB"}
[2025-08-23 21:03:19 INF] Getting paged coordinates - Page: 8, PageSize: 1000 {"SourceContext":"SharePointCoordinatesService.Services.CoordinatesService","ActionId":"003bd5ee-f419-4aeb-b5b0-d631a449f079","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.GetCoordinatesPaged (SharePointCoordinatesService)","RequestId":"0HNF24FJFJ5JB:00000008","RequestPath":"/api/coordinates/paged","ConnectionId":"0HNF24FJFJ5JB"}
[2025-08-23 21:03:19 INF] Starting to get coordinates data {"SourceContext":"SharePointCoordinatesService.Services.CoordinatesService","ActionId":"003bd5ee-f419-4aeb-b5b0-d631a449f079","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.GetCoordinatesPaged (SharePointCoordinatesService)","RequestId":"0HNF24FJFJ5JB:00000008","RequestPath":"/api/coordinates/paged","ConnectionId":"0HNF24FJFJ5JB"}
[2025-08-23 21:03:19 INF] Returning memory cached coordinates data with 11703 items {"SourceContext":"SharePointCoordinatesService.Services.CoordinatesService","ActionId":"003bd5ee-f419-4aeb-b5b0-d631a449f079","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.GetCoordinatesPaged (SharePointCoordinatesService)","RequestId":"0HNF24FJFJ5JB:00000008","RequestPath":"/api/coordinates/paged","ConnectionId":"0HNF24FJFJ5JB"}
[2025-08-23 21:03:19 INF] Successfully returned page 8 with 1000 coordinates {"SourceContext":"SharePointCoordinatesService.Controllers.CoordinatesController","ActionId":"003bd5ee-f419-4aeb-b5b0-d631a449f079","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.GetCoordinatesPaged (SharePointCoordinatesService)","RequestId":"0HNF24FJFJ5JB:00000008","RequestPath":"/api/coordinates/paged","ConnectionId":"0HNF24FJFJ5JB"}
[2025-08-23 21:03:19 INF] Successfully authenticated user: luke.shi {"SourceContext":"SharePointCoordinatesService.Middlewares.BasicAuthenticationHandler","RequestId":"0HNF24FJFJ5JB:00000009","RequestPath":"/api/coordinates/paged","ConnectionId":"0HNF24FJFJ5JB"}
[2025-08-23 21:03:19 INF] API request: Get paged coordinates - Page: 9, PageSize: 1000 {"SourceContext":"SharePointCoordinatesService.Controllers.CoordinatesController","ActionId":"003bd5ee-f419-4aeb-b5b0-d631a449f079","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.GetCoordinatesPaged (SharePointCoordinatesService)","RequestId":"0HNF24FJFJ5JB:00000009","RequestPath":"/api/coordinates/paged","ConnectionId":"0HNF24FJFJ5JB"}
[2025-08-23 21:03:19 INF] Getting paged coordinates - Page: 9, PageSize: 1000 {"SourceContext":"SharePointCoordinatesService.Services.CoordinatesService","ActionId":"003bd5ee-f419-4aeb-b5b0-d631a449f079","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.GetCoordinatesPaged (SharePointCoordinatesService)","RequestId":"0HNF24FJFJ5JB:00000009","RequestPath":"/api/coordinates/paged","ConnectionId":"0HNF24FJFJ5JB"}
[2025-08-23 21:03:19 INF] Starting to get coordinates data {"SourceContext":"SharePointCoordinatesService.Services.CoordinatesService","ActionId":"003bd5ee-f419-4aeb-b5b0-d631a449f079","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.GetCoordinatesPaged (SharePointCoordinatesService)","RequestId":"0HNF24FJFJ5JB:00000009","RequestPath":"/api/coordinates/paged","ConnectionId":"0HNF24FJFJ5JB"}
[2025-08-23 21:03:19 INF] Returning memory cached coordinates data with 11703 items {"SourceContext":"SharePointCoordinatesService.Services.CoordinatesService","ActionId":"003bd5ee-f419-4aeb-b5b0-d631a449f079","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.GetCoordinatesPaged (SharePointCoordinatesService)","RequestId":"0HNF24FJFJ5JB:00000009","RequestPath":"/api/coordinates/paged","ConnectionId":"0HNF24FJFJ5JB"}
[2025-08-23 21:03:19 INF] Successfully returned page 9 with 1000 coordinates {"SourceContext":"SharePointCoordinatesService.Controllers.CoordinatesController","ActionId":"003bd5ee-f419-4aeb-b5b0-d631a449f079","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.GetCoordinatesPaged (SharePointCoordinatesService)","RequestId":"0HNF24FJFJ5JB:00000009","RequestPath":"/api/coordinates/paged","ConnectionId":"0HNF24FJFJ5JB"}
[2025-08-23 21:03:19 INF] Successfully authenticated user: luke.shi {"SourceContext":"SharePointCoordinatesService.Middlewares.BasicAuthenticationHandler","RequestId":"0HNF24FJFJ5JB:0000000A","RequestPath":"/api/coordinates/paged","ConnectionId":"0HNF24FJFJ5JB"}
[2025-08-23 21:03:19 INF] API request: Get paged coordinates - Page: 10, PageSize: 1000 {"SourceContext":"SharePointCoordinatesService.Controllers.CoordinatesController","ActionId":"003bd5ee-f419-4aeb-b5b0-d631a449f079","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.GetCoordinatesPaged (SharePointCoordinatesService)","RequestId":"0HNF24FJFJ5JB:0000000A","RequestPath":"/api/coordinates/paged","ConnectionId":"0HNF24FJFJ5JB"}
[2025-08-23 21:03:19 INF] Getting paged coordinates - Page: 10, PageSize: 1000 {"SourceContext":"SharePointCoordinatesService.Services.CoordinatesService","ActionId":"003bd5ee-f419-4aeb-b5b0-d631a449f079","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.GetCoordinatesPaged (SharePointCoordinatesService)","RequestId":"0HNF24FJFJ5JB:0000000A","RequestPath":"/api/coordinates/paged","ConnectionId":"0HNF24FJFJ5JB"}
[2025-08-23 21:03:19 INF] Starting to get coordinates data {"SourceContext":"SharePointCoordinatesService.Services.CoordinatesService","ActionId":"003bd5ee-f419-4aeb-b5b0-d631a449f079","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.GetCoordinatesPaged (SharePointCoordinatesService)","RequestId":"0HNF24FJFJ5JB:0000000A","RequestPath":"/api/coordinates/paged","ConnectionId":"0HNF24FJFJ5JB"}
[2025-08-23 21:03:19 INF] Returning memory cached coordinates data with 11703 items {"SourceContext":"SharePointCoordinatesService.Services.CoordinatesService","ActionId":"003bd5ee-f419-4aeb-b5b0-d631a449f079","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.GetCoordinatesPaged (SharePointCoordinatesService)","RequestId":"0HNF24FJFJ5JB:0000000A","RequestPath":"/api/coordinates/paged","ConnectionId":"0HNF24FJFJ5JB"}
[2025-08-23 21:03:19 INF] Successfully returned page 10 with 1000 coordinates {"SourceContext":"SharePointCoordinatesService.Controllers.CoordinatesController","ActionId":"003bd5ee-f419-4aeb-b5b0-d631a449f079","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.GetCoordinatesPaged (SharePointCoordinatesService)","RequestId":"0HNF24FJFJ5JB:0000000A","RequestPath":"/api/coordinates/paged","ConnectionId":"0HNF24FJFJ5JB"}
[2025-08-23 21:03:19 INF] Successfully authenticated user: luke.shi {"SourceContext":"SharePointCoordinatesService.Middlewares.BasicAuthenticationHandler","RequestId":"0HNF24FJFJ5JB:0000000B","RequestPath":"/api/coordinates/paged","ConnectionId":"0HNF24FJFJ5JB"}
[2025-08-23 21:03:19 INF] API request: Get paged coordinates - Page: 11, PageSize: 1000 {"SourceContext":"SharePointCoordinatesService.Controllers.CoordinatesController","ActionId":"003bd5ee-f419-4aeb-b5b0-d631a449f079","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.GetCoordinatesPaged (SharePointCoordinatesService)","RequestId":"0HNF24FJFJ5JB:0000000B","RequestPath":"/api/coordinates/paged","ConnectionId":"0HNF24FJFJ5JB"}
[2025-08-23 21:03:19 INF] Getting paged coordinates - Page: 11, PageSize: 1000 {"SourceContext":"SharePointCoordinatesService.Services.CoordinatesService","ActionId":"003bd5ee-f419-4aeb-b5b0-d631a449f079","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.GetCoordinatesPaged (SharePointCoordinatesService)","RequestId":"0HNF24FJFJ5JB:0000000B","RequestPath":"/api/coordinates/paged","ConnectionId":"0HNF24FJFJ5JB"}
[2025-08-23 21:03:19 INF] Starting to get coordinates data {"SourceContext":"SharePointCoordinatesService.Services.CoordinatesService","ActionId":"003bd5ee-f419-4aeb-b5b0-d631a449f079","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.GetCoordinatesPaged (SharePointCoordinatesService)","RequestId":"0HNF24FJFJ5JB:0000000B","RequestPath":"/api/coordinates/paged","ConnectionId":"0HNF24FJFJ5JB"}
[2025-08-23 21:03:19 INF] Returning memory cached coordinates data with 11703 items {"SourceContext":"SharePointCoordinatesService.Services.CoordinatesService","ActionId":"003bd5ee-f419-4aeb-b5b0-d631a449f079","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.GetCoordinatesPaged (SharePointCoordinatesService)","RequestId":"0HNF24FJFJ5JB:0000000B","RequestPath":"/api/coordinates/paged","ConnectionId":"0HNF24FJFJ5JB"}
[2025-08-23 21:03:19 INF] Successfully returned page 11 with 1000 coordinates {"SourceContext":"SharePointCoordinatesService.Controllers.CoordinatesController","ActionId":"003bd5ee-f419-4aeb-b5b0-d631a449f079","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.GetCoordinatesPaged (SharePointCoordinatesService)","RequestId":"0HNF24FJFJ5JB:0000000B","RequestPath":"/api/coordinates/paged","ConnectionId":"0HNF24FJFJ5JB"}
[2025-08-23 21:03:20 INF] Successfully authenticated user: luke.shi {"SourceContext":"SharePointCoordinatesService.Middlewares.BasicAuthenticationHandler","RequestId":"0HNF24FJFJ5JB:0000000C","RequestPath":"/api/coordinates/paged","ConnectionId":"0HNF24FJFJ5JB"}
[2025-08-23 21:03:20 INF] API request: Get paged coordinates - Page: 12, PageSize: 1000 {"SourceContext":"SharePointCoordinatesService.Controllers.CoordinatesController","ActionId":"003bd5ee-f419-4aeb-b5b0-d631a449f079","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.GetCoordinatesPaged (SharePointCoordinatesService)","RequestId":"0HNF24FJFJ5JB:0000000C","RequestPath":"/api/coordinates/paged","ConnectionId":"0HNF24FJFJ5JB"}
[2025-08-23 21:03:20 INF] Getting paged coordinates - Page: 12, PageSize: 1000 {"SourceContext":"SharePointCoordinatesService.Services.CoordinatesService","ActionId":"003bd5ee-f419-4aeb-b5b0-d631a449f079","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.GetCoordinatesPaged (SharePointCoordinatesService)","RequestId":"0HNF24FJFJ5JB:0000000C","RequestPath":"/api/coordinates/paged","ConnectionId":"0HNF24FJFJ5JB"}
[2025-08-23 21:03:20 INF] Starting to get coordinates data {"SourceContext":"SharePointCoordinatesService.Services.CoordinatesService","ActionId":"003bd5ee-f419-4aeb-b5b0-d631a449f079","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.GetCoordinatesPaged (SharePointCoordinatesService)","RequestId":"0HNF24FJFJ5JB:0000000C","RequestPath":"/api/coordinates/paged","ConnectionId":"0HNF24FJFJ5JB"}
[2025-08-23 21:03:20 INF] Returning memory cached coordinates data with 11703 items {"SourceContext":"SharePointCoordinatesService.Services.CoordinatesService","ActionId":"003bd5ee-f419-4aeb-b5b0-d631a449f079","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.GetCoordinatesPaged (SharePointCoordinatesService)","RequestId":"0HNF24FJFJ5JB:0000000C","RequestPath":"/api/coordinates/paged","ConnectionId":"0HNF24FJFJ5JB"}
[2025-08-23 21:03:20 INF] Successfully returned page 12 with 703 coordinates {"SourceContext":"SharePointCoordinatesService.Controllers.CoordinatesController","ActionId":"003bd5ee-f419-4aeb-b5b0-d631a449f079","ActionName":"SharePointCoordinatesService.Controllers.CoordinatesController.GetCoordinatesPaged (SharePointCoordinatesService)","RequestId":"0HNF24FJFJ5JB:0000000C","RequestPath":"/api/coordinates/paged","ConnectionId":"0HNF24FJFJ5JB"}
[2025-08-23 23:08:34 INF] Database migrations applied successfully {}
[2025-08-23 23:08:34 INF] SharePoint Coordinates database and schema are ready {}
[2025-08-23 23:08:34 INF] Database initialization completed successfully {}
[2025-08-23 23:08:34 INF] 🚀 SharePoint Coordinates Service started successfully {}
[2025-08-23 23:08:34 INF] 📖 API documentation available at: http://localhost:5088 {}
[2025-08-23 23:08:34 INF] 🔐 Health check available at: http://localhost:5088/health {}
