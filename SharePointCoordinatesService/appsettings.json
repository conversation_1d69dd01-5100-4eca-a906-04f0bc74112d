{"ConnectionStrings": {"DefaultConnection": "Host=localhost;Port=5432;Database=SharePointCoordinatesDb;Username=postgres;Password=******"}, "SharePointConfig": {"TenantId": "637a79a6-985f-4dcd-aa06-354f067419bf", "ClientId": "d50296cf-228e-4139-a530-71a68b516c58", "ClientSecret": "****************************************", "SiteUrl": "https://siconnz.sharepoint.com/sites/DataProcessing", "FileName": "assets_coordinates.xlsx", "CacheExpiryMinutes": 60, "RetryAttempts": 3, "TimeoutSeconds": 30}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.Graph": "Information"}}, "AllowedHosts": "*", "Authentication": {"EnableBasicAuth": true, "DefaultUsername": "luke.shi", "DefaultPassword": "gentoo666"}, "Serilog": {"Using": ["Serilog.Sinks.Console", "Serilog.Sinks.File"], "MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "System": "Warning"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss} {Level:u3}] {Message:lj} {Properties:j}{NewLine}{Exception}"}}, {"Name": "File", "Args": {"path": "logs/sharepoint-coordinates-.log", "rollingInterval": "Day", "outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss} {Level:u3}] {Message:lj} {Properties:j}{NewLine}{Exception}"}}]}}