using Microsoft.EntityFrameworkCore;
using SharePointCoordinatesService.Models;

namespace SharePointCoordinatesService.Data
{
    public class SharePointCoordinatesDbContext : DbContext
    {
        public SharePointCoordinatesDbContext(DbContextOptions<SharePointCoordinatesDbContext> options)
            : base(options)
        {
        }

        public DbSet<CoordinateModel> Coordinates { get; set; }
        public DbSet<SyncLogModel> SyncLogs { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // Use schema to avoid conflicts with existing databases
            modelBuilder.HasDefaultSchema("sharepoint_coords");

            // Configure Coordinates table
            modelBuilder.Entity<CoordinateModel>(entity =>
            {
                entity.ToTable("Coordinates");
                entity.HasKey(e => e.Id);
                entity.HasIndex(e => e.<PERSON>s<PERSON><PERSON>).IsUnique();
                entity.HasIndex(e => new { e.Latitude, e.Longitude });
                
                entity.Property(e => e.Ams<PERSON>ey)
                    .IsRequired()
                    .HasMaxLength(100);
                    
                entity.Property(e => e.Latitude)
                    .IsRequired()
                    .HasPrecision(10, 8);
                    
                entity.Property(e => e.Longitude)
                    .IsRequired()
                    .HasPrecision(11, 8);
            });

            // Configure SyncLogs table
            modelBuilder.Entity<SyncLogModel>(entity =>
            {
                entity.ToTable("SyncLogs");
                entity.HasKey(e => e.Id);
                entity.HasIndex(e => e.Status);
                entity.HasIndex(e => e.SyncStartTime);
                
                entity.Property(e => e.Status)
                    .IsRequired()
                    .HasMaxLength(20);
            });
        }
    }
}
