using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SharePointCoordinatesService.Services;

namespace SharePointCoordinatesService.Controllers
{
    [ApiController]
    [Route("[controller]")]
    [AllowAnonymous] // Health checks don't need authentication
    public class HealthController : ControllerBase
    {
        private readonly CoordinatesService _coordinatesService;
        private readonly SharePointAuthService _authService;
        private readonly ILogger<HealthController> _logger;

        public HealthController(
            CoordinatesService coordinatesService,
            SharePointAuthService authService,
            ILogger<HealthController> logger)
        {
            _coordinatesService = coordinatesService;
            _authService = authService;
            _logger = logger;
        }

        /// <summary>
        /// Basic health check
        /// </summary>
        [HttpGet]
        public IActionResult Get()
        {
            return Ok(new
            {
                status = "Healthy",
                timestamp = DateTime.UtcNow,
                service = "SharePoint Coordinates Service",
                version = "1.0.0"
            });
        }

        /// <summary>
        /// SharePoint connection health check
        /// </summary>
        [HttpGet("sharepoint")]
        public async Task<IActionResult> CheckSharePoint()
        {
            try
            {
                _logger.LogInformation("Performing SharePoint health check");
                
                // Try to get access token
                var token = await _authService.GetAccessTokenAsync();
                var hasToken = !string.IsNullOrEmpty(token);
                
                return Ok(new
                {
                    status = hasToken ? "Healthy" : "Unhealthy",
                    timestamp = DateTime.UtcNow,
                    component = "SharePoint Authentication",
                    hasAccessToken = hasToken
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "SharePoint health check failed");
                return StatusCode(503, new
                {
                    status = "Unhealthy",
                    timestamp = DateTime.UtcNow,
                    component = "SharePoint Authentication",
                    error = ex.Message
                });
            }
        }

        /// <summary>
        /// Cache health check with detailed status
        /// </summary>
        [HttpGet("cache")]
        public async Task<IActionResult> CheckCache()
        {
            try
            {
                var cacheStatus = await _coordinatesService.GetCacheStatus();
                
                return Ok(new
                {
                    status = "Healthy",
                    timestamp = DateTime.UtcNow,
                    component = "Cache System",
                    details = cacheStatus
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Cache health check failed");
                return StatusCode(503, new
                {
                    status = "Unhealthy",
                    timestamp = DateTime.UtcNow,
                    component = "Cache System",
                    error = ex.Message
                });
            }
        }
    }
}
