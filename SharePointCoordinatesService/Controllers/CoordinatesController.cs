using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SharePointCoordinatesService.Services;

namespace SharePointCoordinatesService.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize] // Will use Basic Auth middleware
    public class CoordinatesController : ControllerBase
    {
        private readonly CoordinatesService _coordinatesService;
        private readonly ILogger<CoordinatesController> _logger;

        public CoordinatesController(
            CoordinatesService coordinatesService,
            ILogger<CoordinatesController> logger)
        {
            _coordinatesService = coordinatesService;
            _logger = logger;
        }

        /// <summary>
        /// Get coordinates from SharePoint Excel file
        /// </summary>
        /// <returns>List of coordinates with asset_id, latitude, longitude</returns>
        [HttpGet]
        [ProducesResponseType(200)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> GetCoordinates()
        {
            try
            {
                _logger.LogInformation("API request: Get coordinates");
                
                var coordinates = await _coordinatesService.GetCoordinatesAsync();
                
                _logger.LogInformation("Successfully returned {Count} coordinates", coordinates.count);
                return Ok(coordinates);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GetCoordinates API");
                return StatusCode(500, new { 
                    error = "Failed to fetch coordinates",
                    message = ex.Message,
                    timestamp = DateTime.UtcNow
                });
            }
        }

        /// <summary>
        /// Manually refresh the coordinates cache
        /// </summary>
        /// <returns>Refreshed coordinates data</returns>
        [HttpPost("refresh")]
        [ProducesResponseType(200)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> RefreshCoordinates()
        {
            try
            {
                _logger.LogInformation("API request: Refresh coordinates cache");
                
                var coordinates = await _coordinatesService.RefreshCacheAsync();
                
                _logger.LogInformation("Successfully refreshed cache with {Count} coordinates", coordinates.count);
                return Ok(new { 
                    message = "Cache refreshed successfully",
                    data = coordinates,
                    timestamp = DateTime.UtcNow
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in RefreshCoordinates API");
                return StatusCode(500, new { 
                    error = "Failed to refresh coordinates cache",
                    message = ex.Message,
                    timestamp = DateTime.UtcNow
                });
            }
        }

        /// <summary>
        /// Get coordinates with pagination (recommended for mobile)
        /// </summary>
        /// <param name="page">Page number (starting from 1)</param>
        /// <param name="pageSize">Number of items per page (max 5000, default 1000)</param>
        /// <returns>Paged coordinates data</returns>
        [HttpGet("paged")]
        [ProducesResponseType(200)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> GetCoordinatesPaged([FromQuery] int page = 1, [FromQuery] int pageSize = 1000)
        {
            try
            {
                _logger.LogInformation("API request: Get paged coordinates - Page: {Page}, PageSize: {PageSize}", page, pageSize);
                
                // Validate parameters
                if (page < 1) page = 1;
                if (pageSize < 1 || pageSize > 5000) pageSize = 1000; // Max 5000 per page

                var pagedResult = await _coordinatesService.GetCoordinatesPagedAsync(page, pageSize);
                
                _logger.LogInformation("Successfully returned page {Page} with {Count} coordinates", page, pagedResult.data.Count);
                return Ok(pagedResult);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GetCoordinatesPaged API");
                return StatusCode(500, new
                {
                    error = "Failed to get paged coordinates",
                    message = ex.Message,
                    timestamp = DateTime.UtcNow
                });
            }
        }

        /// <summary>
        /// Get cache status
        /// </summary>
        /// <returns>Cache status information</returns>
        [HttpGet("cache/status")]
        [ProducesResponseType(200)]
        public IActionResult GetCacheStatus()
        {
            try
            {
                var isValid = _coordinatesService.IsCacheValid();
                
                return Ok(new {
                    cacheValid = isValid,
                    timestamp = DateTime.UtcNow,
                    message = isValid ? "Cache is valid" : "Cache is empty or expired"
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GetCacheStatus API");
                return StatusCode(500, new { 
                    error = "Failed to get cache status",
                    message = ex.Message,
                    timestamp = DateTime.UtcNow
                });
            }
        }

        /// <summary>
        /// Clear the coordinates cache
        /// </summary>
        /// <returns>Success message</returns>
        [HttpDelete("cache")]
        [ProducesResponseType(200)]
        public IActionResult ClearCache()
        {
            try
            {
                _logger.LogInformation("API request: Clear coordinates cache");
                
                _coordinatesService.ClearCache();
                
                return Ok(new {
                    message = "Cache cleared successfully",
                    timestamp = DateTime.UtcNow
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in ClearCache API");
                return StatusCode(500, new { 
                    error = "Failed to clear cache",
                    message = ex.Message,
                    timestamp = DateTime.UtcNow
                });
            }
        }
    }
}
