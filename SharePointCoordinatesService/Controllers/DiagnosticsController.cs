using Microsoft.AspNetCore.Mvc;
using SharePointCoordinatesService.Services;
using System.Text.Json;

namespace SharePointCoordinatesService.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class DiagnosticsController : ControllerBase
    {
        private readonly SharePointAuthService _authService;
        private readonly HttpClient _httpClient;
        private readonly ILogger<DiagnosticsController> _logger;

        public DiagnosticsController(
            SharePointAuthService authService,
            HttpClient httpClient,
            ILogger<DiagnosticsController> logger)
        {
            _authService = authService;
            _httpClient = httpClient;
            _logger = logger;
        }

        /// <summary>
        /// Test SharePoint permissions and access
        /// </summary>
        [HttpGet("sharepoint-access")]
        public async Task<IActionResult> TestSharePointAccess()
        {
            var results = new List<object>();

            try
            {
                // Step 1: Get access token
                var accessToken = await _authService.GetAccessTokenAsync();
                results.Add(new { test = "Get Access Token", status = "✅ Success", hasToken = !string.IsNullOrEmpty(accessToken) });

                _httpClient.DefaultRequestHeaders.Authorization = 
                    new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                // Step 2: Test root sites access
                try
                {
                    var sitesResponse = await _httpClient.GetAsync("https://graph.microsoft.com/v1.0/sites");
                    results.Add(new { 
                        test = "List Sites", 
                        status = sitesResponse.IsSuccessStatusCode ? "✅ Success" : "❌ Failed", 
                        statusCode = (int)sitesResponse.StatusCode 
                    });
                }
                catch (Exception ex)
                {
                    results.Add(new { test = "List Sites", status = "❌ Exception", error = ex.Message });
                }

                // Step 3: Test specific site access
                try
                {
                    var siteResponse = await _httpClient.GetAsync(
                        "https://graph.microsoft.com/v1.0/sites/siconnz.sharepoint.com:/sites/DataProcessing");
                    
                    if (siteResponse.IsSuccessStatusCode)
                    {
                        var siteData = await siteResponse.Content.ReadAsStringAsync();
                        var siteInfo = JsonSerializer.Deserialize<JsonElement>(siteData);
                        var siteId = siteInfo.GetProperty("id").GetString();
                        
                        results.Add(new { 
                            test = "Access DataProcessing Site", 
                            status = "✅ Success", 
                            siteId = siteId 
                        });

                        // Step 4: Test drive access
                        try
                        {
                            var driveResponse = await _httpClient.GetAsync(
                                $"https://graph.microsoft.com/v1.0/sites/{siteId}/drive");
                            
                            if (driveResponse.IsSuccessStatusCode)
                            {
                                var driveData = await driveResponse.Content.ReadAsStringAsync();
                                var driveInfo = JsonSerializer.Deserialize<JsonElement>(driveData);
                                var driveId = driveInfo.GetProperty("id").GetString();
                                
                                results.Add(new { 
                                    test = "Access Site Drive", 
                                    status = "✅ Success", 
                                    driveId = driveId 
                                });

                                // Step 5: Test file search
                                try
                                {
                                    var searchResponse = await _httpClient.GetAsync(
                                        $"https://graph.microsoft.com/v1.0/sites/{siteId}/drive/root/search(q='assets_coordinates.xlsx')");
                                    
                                    if (searchResponse.IsSuccessStatusCode)
                                    {
                                        var searchData = await searchResponse.Content.ReadAsStringAsync();
                                        var searchResult = JsonSerializer.Deserialize<JsonElement>(searchData);
                                        var files = searchResult.GetProperty("value").EnumerateArray().ToList();
                                        
                                        results.Add(new { 
                                            test = "Search for assets_coordinates.xlsx", 
                                            status = files.Count > 0 ? "✅ Found" : "⚠️ Not Found", 
                                            filesFound = files.Count,
                                            files = files.Take(3).Select(f => new {
                                                name = f.GetProperty("name").GetString(),
                                                id = f.TryGetProperty("id", out var id) ? id.GetString() : null,
                                                webUrl = f.TryGetProperty("webUrl", out var url) ? url.GetString() : null
                                            })
                                        });
                                    }
                                    else
                                    {
                                        results.Add(new { 
                                            test = "Search for File", 
                                            status = "❌ Failed", 
                                            statusCode = (int)searchResponse.StatusCode 
                                        });
                                    }
                                }
                                catch (Exception ex)
                                {
                                    results.Add(new { test = "Search for File", status = "❌ Exception", error = ex.Message });
                                }
                            }
                            else
                            {
                                results.Add(new { 
                                    test = "Access Site Drive", 
                                    status = "❌ Failed", 
                                    statusCode = (int)driveResponse.StatusCode 
                                });
                            }
                        }
                        catch (Exception ex)
                        {
                            results.Add(new { test = "Access Site Drive", status = "❌ Exception", error = ex.Message });
                        }
                    }
                    else
                    {
                        results.Add(new { 
                            test = "Access DataProcessing Site", 
                            status = "❌ Failed", 
                            statusCode = (int)siteResponse.StatusCode,
                            content = await siteResponse.Content.ReadAsStringAsync()
                        });
                    }
                }
                catch (Exception ex)
                {
                    results.Add(new { test = "Access DataProcessing Site", status = "❌ Exception", error = ex.Message });
                }

                return Ok(new {
                    timestamp = DateTime.UtcNow,
                    summary = "SharePoint Access Diagnostics",
                    results = results
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new {
                    error = "Diagnostics failed",
                    message = ex.Message,
                    results = results
                });
            }
        }
    }
}
