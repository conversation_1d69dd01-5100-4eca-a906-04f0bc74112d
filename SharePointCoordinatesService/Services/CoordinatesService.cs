using Microsoft.Extensions.Caching.Memory;
using Microsoft.EntityFrameworkCore;
using SharePointCoordinatesService.DTOs;
using SharePointCoordinatesService.Models;
using SharePointCoordinatesService.Data;

namespace SharePointCoordinatesService.Services
{
    public class CoordinatesService
    {
        private readonly SharePointFileService _fileService;
        private readonly IMemoryCache _cache;
        private readonly SharePointConfig _config;
        private readonly ILogger<CoordinatesService> _logger;
        private readonly SharePointCoordinatesDbContext _dbContext;
        private const string CACHE_KEY = "sharepoint_coordinates";

        public CoordinatesService(
            SharePointFileService fileService,
            IMemoryCache cache,
            SharePointConfig config,
            ILogger<CoordinatesService> logger,
            SharePointCoordinatesDbContext dbContext)
        {
            _fileService = fileService;
            _cache = cache;
            _config = config;
            _logger = logger;
            _dbContext = dbContext;
        }

        public async Task<CoordinatesResponse> GetCoordinatesAsync()
        {
            try
            {
                _logger.LogInformation("Starting to get coordinates data");

                // Check memory cache first
                if (_cache.TryGetValue(CACHE_KEY, out List<CoordinateDto>? cachedData) && cachedData != null)
                {
                    _logger.LogInformation("Returning memory cached coordinates data with {Count} items", cachedData.Count);
                    return new CoordinatesResponse
                    {
                        data = cachedData,
                        timestamp = DateTime.UtcNow,
                        count = cachedData.Count
                    };
                }

                _logger.LogInformation("Memory cache miss, checking database");

                // Check database cache
                var dbCoordinates = await _dbContext.Coordinates
                    .Where(c => c.UpdatedAt > DateTime.UtcNow.AddMinutes(-_config.CacheExpiryMinutes))
                    .OrderBy(c => c.AmsKey)
                    .ToListAsync();

                if (dbCoordinates.Any())
                {
                    _logger.LogInformation("Found {Count} valid coordinates in database", dbCoordinates.Count);
                    
                    var dbData = dbCoordinates.Select(coord => new CoordinateDto
                    {
                        asset_id = int.Parse(coord.AmsKey),
                        latitude = (double)coord.Latitude,
                        longitude = (double)coord.Longitude
                    }).ToList();

                    // Cache in memory for faster subsequent access
                    var cacheExpiry = TimeSpan.FromMinutes(_config.CacheExpiryMinutes);
                    _cache.Set(CACHE_KEY, dbData, cacheExpiry);

                    return new CoordinatesResponse
                    {
                        data = dbData,
                        timestamp = DateTime.UtcNow,
                        count = dbData.Count()
                    };
                }

                _logger.LogInformation("Database cache miss or expired, fetching from SharePoint");

                // Fetch from SharePoint and save to database
                var coordinates = await FetchAndSaveCoordinatesFromSharePointAsync();
                
                // Cache in memory
                var memoryCacheExpiry = TimeSpan.FromMinutes(_config.CacheExpiryMinutes);
                _cache.Set(CACHE_KEY, coordinates, memoryCacheExpiry);

                _logger.LogInformation("Fetched and cached {Count} coordinates", coordinates.Count);
                
                return new CoordinatesResponse
                {
                    data = coordinates,
                    timestamp = DateTime.UtcNow,
                    count = coordinates.Count
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error fetching coordinates");
                throw;
            }
        }

        private async Task<List<CoordinateDto>> FetchAndSaveCoordinatesFromSharePointAsync()
        {
            var syncLog = new SyncLogModel
            {
                SyncStartTime = DateTime.UtcNow,
                Status = "InProgress"
            };

            try
            {
                _logger.LogInformation("Starting SharePoint sync process");
                
                // Save sync log start
                _dbContext.SyncLogs.Add(syncLog);
                await _dbContext.SaveChangesAsync();

                // Get file content from SharePoint
                var fileBytes = await _fileService.GetFileByNameAsync();

                // Parse Excel file
                var rawCoordinates = _fileService.ParseExcelToCoordinates(fileBytes);

                // Convert to DTOs
                var coordinates = rawCoordinates.Select(coord =>
                {
                    var coordObj = coord as dynamic;
                    return new CoordinateDto
                    {
                        asset_id = (int)coordObj.asset_id,
                        latitude = (double)coordObj.latitude,
                        longitude = (double)coordObj.longitude
                    };
                }).ToList();

                _logger.LogInformation("Successfully parsed {Count} coordinates from SharePoint", coordinates.Count);

                // Save coordinates to database
                await SaveCoordinatesToDatabaseAsync(coordinates);

                // Update sync log as successful
                syncLog.SyncEndTime = DateTime.UtcNow;
                syncLog.Status = "Success";
                syncLog.RecordsProcessed = coordinates.Count;
                await _dbContext.SaveChangesAsync();

                _logger.LogInformation("Successfully synced {Count} coordinates to database", coordinates.Count);
                return coordinates;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to fetch and save coordinates from SharePoint");
                
                // Update sync log as failed
                syncLog.SyncEndTime = DateTime.UtcNow;
                syncLog.Status = "Failed";
                syncLog.ErrorMessage = ex.Message;
                await _dbContext.SaveChangesAsync();
                
                throw;
            }
        }

        private async Task SaveCoordinatesToDatabaseAsync(List<CoordinateDto> coordinates)
        {
            try
            {
                _logger.LogInformation("Saving {Count} coordinates to database using UPSERT", coordinates.Count);

                var now = DateTime.UtcNow;
                
                // Use PostgreSQL UPSERT (INSERT ... ON CONFLICT) for each coordinate
                foreach (var coord in coordinates)
                {
                    var amsKey = coord.asset_id.ToString();
                    var latitude = (decimal)coord.latitude;
                    var longitude = (decimal)coord.longitude;

                    await _dbContext.Database.ExecuteSqlRawAsync(@"
                        INSERT INTO sharepoint_coords.""Coordinates"" (""AmsKey"", ""Latitude"", ""Longitude"", ""LastModified"", ""CreatedAt"", ""UpdatedAt"")
                        VALUES ({0}, {1}, {2}, {3}, {4}, {5})
                        ON CONFLICT (""AmsKey"") 
                        DO UPDATE SET 
                            ""Latitude"" = EXCLUDED.""Latitude"",
                            ""Longitude"" = EXCLUDED.""Longitude"",
                            ""LastModified"" = EXCLUDED.""LastModified"",
                            ""UpdatedAt"" = EXCLUDED.""UpdatedAt""",
                        amsKey, latitude, longitude, now, now, now);
                }
                
                _logger.LogInformation("Successfully upserted {Count} coordinates to database", coordinates.Count);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to upsert coordinates to database");
                throw;
            }
        }

        public async Task<CoordinatesResponse> RefreshCacheAsync()
        {
            try
            {
                _logger.LogInformation("Manually refreshing coordinates cache");
                
                // Clear memory cache
                _cache.Remove(CACHE_KEY);
                
                // Force fetch from SharePoint (bypass database cache)
                var coordinates = await FetchAndSaveCoordinatesFromSharePointAsync();
                
                // Cache in memory
                var memoryCacheExpiry = TimeSpan.FromMinutes(_config.CacheExpiryMinutes);
                _cache.Set(CACHE_KEY, coordinates, memoryCacheExpiry);

                return new CoordinatesResponse
                {
                    data = coordinates,
                    timestamp = DateTime.UtcNow,
                    count = coordinates.Count
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error refreshing coordinates cache");
                throw;
            }
        }

        public bool IsCacheValid()
        {
            return _cache.TryGetValue(CACHE_KEY, out _);
        }

        public void ClearCache()
        {
            _logger.LogInformation("Clearing coordinates cache");
            _cache.Remove(CACHE_KEY);
        }

        public async Task<PagedCoordinatesResponse> GetCoordinatesPagedAsync(int page, int pageSize)
        {
            try
            {
                _logger.LogInformation("Getting paged coordinates - Page: {Page}, PageSize: {PageSize}", page, pageSize);

                // First, ensure we have data (try memory cache, then database, then SharePoint)
                var allCoordinatesResponse = await GetCoordinatesAsync();
                var allCoordinates = allCoordinatesResponse.data;

                // Calculate pagination
                var totalCount = allCoordinates.Count;
                var totalPages = (int)Math.Ceiling((double)totalCount / pageSize);
                var skip = (page - 1) * pageSize;

                // Get paged data
                var pagedData = allCoordinates
                    .Skip(skip)
                    .Take(pageSize)
                    .ToList();

                return new PagedCoordinatesResponse
                {
                    data = pagedData,
                    page = page,
                    pageSize = pageSize,
                    totalCount = totalCount,
                    totalPages = totalPages,
                    hasNextPage = page < totalPages,
                    hasPreviousPage = page > 1,
                    timestamp = DateTime.UtcNow
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting paged coordinates");
                throw;
            }
        }

        public async Task<object> GetCacheStatus()
        {
            var memoryCacheValid = _cache.TryGetValue(CACHE_KEY, out var cachedData);
            var cachedCount = memoryCacheValid && cachedData is List<CoordinateDto> data ? data.Count : 0;

            var dbCount = await _dbContext.Coordinates.CountAsync();
            var lastDbUpdate = await _dbContext.Coordinates
                .OrderByDescending(c => c.UpdatedAt)
                .Select(c => c.UpdatedAt)
                .FirstOrDefaultAsync();

            var lastSyncLog = await _dbContext.SyncLogs
                .OrderByDescending(s => s.SyncStartTime)
                .FirstOrDefaultAsync();

            return new
            {
                memoryCache = new
                {
                    isValid = memoryCacheValid,
                    count = cachedCount
                },
                database = new
                {
                    count = dbCount,
                    lastUpdate = lastDbUpdate,
                    isValid = lastDbUpdate > DateTime.UtcNow.AddMinutes(-_config.CacheExpiryMinutes)
                },
                lastSync = lastSyncLog != null ? new
                {
                    startTime = lastSyncLog.SyncStartTime,
                    endTime = lastSyncLog.SyncEndTime,
                    status = lastSyncLog.Status,
                    recordsProcessed = lastSyncLog.RecordsProcessed,
                    errorMessage = lastSyncLog.ErrorMessage
                } : null
            };
        }
    }
}
