using System.Text.Json;
using SharePointCoordinatesService.Models;
using OfficeOpenXml;

namespace SharePointCoordinatesService.Services
{
    public class SharePointFileService
    {
        private readonly HttpClient _httpClient;
        private readonly SharePointAuthService _authService;
        private readonly SharePointConfig _config;
        private readonly ILogger<SharePointFileService> _logger;

        public SharePointFileService(
            HttpClient httpClient, 
            SharePointAuthService authService,
            SharePointConfig config,
            ILogger<SharePointFileService> logger)
        {
            _httpClient = httpClient;
            _authService = authService;
            _config = config;
            _logger = logger;
        }

        public async Task<byte[]> GetFileByNameAsync()
        {
            try
            {
                _logger.LogInformation("Starting to download file '{FileName}' from SharePoint", _config.FileName);

                var accessToken = await _authService.GetAccessTokenAsync();
                _httpClient.DefaultRequestHeaders.Authorization = 
                    new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                // Method 1: Try direct path access using known site info
                var siteId = await GetSiteIdAsync();
                if (!string.IsNullOrEmpty(siteId))
                {
                    var fileBytes = await TryDirectPathAccessAsync(siteId);
                    if (fileBytes != null)
                    {
                        return fileBytes;
                    }
                }

                // Method 2: Fallback to search method
                return await TrySearchMethodAsync(siteId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to download file '{FileName}' from SharePoint", _config.FileName);
                throw new Exception($"Failed to download file from SharePoint: {ex.Message}", ex);
            }
        }

        private async Task<string> GetSiteIdAsync()
        {
            try
            {
                var siteResponse = await _httpClient.GetAsync(
                    "https://graph.microsoft.com/v1.0/sites/siconnz.sharepoint.com:/sites/DataProcessing");
                siteResponse.EnsureSuccessStatusCode();
                
                var siteData = await siteResponse.Content.ReadAsStringAsync();
                var siteInfo = JsonSerializer.Deserialize<JsonElement>(siteData);
                return siteInfo.GetProperty("id").GetString() ?? string.Empty;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get site ID");
                return string.Empty;
            }
        }

        private async Task<byte[]?> TryDirectPathAccessAsync(string siteId)
        {
            var possiblePaths = new[]
            {
                "/Support/assets_coordinates.xlsx",
                "/Shared Documents/assets_coordinates.xlsx", 
                "/assets_coordinates.xlsx"
            };

            foreach (var path in possiblePaths)
            {
                try
                {
                    _logger.LogDebug("Attempting direct file access using path: {Path}", path);
                    
                    var directUrl = $"https://graph.microsoft.com/v1.0/sites/{siteId}/drive/root:{path}:/content";
                    var directResponse = await _httpClient.GetAsync(directUrl);
                    
                    if (directResponse.IsSuccessStatusCode)
                    {
                        var fileBytes = await directResponse.Content.ReadAsByteArrayAsync();
                        _logger.LogInformation("Successfully downloaded file using path '{Path}', size: {Size} bytes", path, fileBytes.Length);
                        return fileBytes;
                    }
                    _logger.LogDebug("Path {Path} failed with status: {StatusCode}", path, directResponse.StatusCode);
                }
                catch (Exception ex)
                {
                    _logger.LogDebug(ex, "Path {Path} failed with exception", path);
                }
            }
            
            _logger.LogWarning("All direct path attempts failed");
            return null;
        }

        private async Task<byte[]> TrySearchMethodAsync(string siteId)
        {
            _logger.LogDebug("Using search method as fallback");

            // Get default document library
            _logger.LogDebug("Getting default document library");
            var driveResponse = await _httpClient.GetAsync(
                $"https://graph.microsoft.com/v1.0/sites/{siteId}/drive");
            driveResponse.EnsureSuccessStatusCode();
            
            var driveData = await driveResponse.Content.ReadAsStringAsync();
            var driveInfo = JsonSerializer.Deserialize<JsonElement>(driveData);
            var driveId = driveInfo.GetProperty("id").GetString();
            
            _logger.LogDebug("Found drive ID: {DriveId}", driveId);

            // Search for the specified file
            _logger.LogDebug("Searching for file: {FileName}", _config.FileName);
            var searchUrl = $"https://graph.microsoft.com/v1.0/sites/{siteId}/drive/root/search(q='{_config.FileName}')";
            var searchResponse = await _httpClient.GetAsync(searchUrl);
            searchResponse.EnsureSuccessStatusCode();
            
            var searchData = await searchResponse.Content.ReadAsStringAsync();
            var searchResult = JsonSerializer.Deserialize<JsonElement>(searchData);
            
            var files = searchResult.GetProperty("value").EnumerateArray();
            var targetFile = files.FirstOrDefault(f => 
                f.GetProperty("name").GetString()?.Equals(_config.FileName, StringComparison.OrdinalIgnoreCase) == true);
                
            if (targetFile.ValueKind == JsonValueKind.Undefined)
            {
                _logger.LogError("File '{FileName}' not found in SharePoint site", _config.FileName);
                throw new FileNotFoundException($"File '{_config.FileName}' not found in SharePoint site");
            }

            // Download file content
            _logger.LogDebug("Downloading file content using search result");
            var downloadUrl = targetFile.GetProperty("@microsoft.graph.downloadUrl").GetString();
            var fileResponse = await _httpClient.GetAsync(downloadUrl);
            fileResponse.EnsureSuccessStatusCode();
            
            var fileBytes = await fileResponse.Content.ReadAsByteArrayAsync();
            _logger.LogInformation("Successfully downloaded file '{FileName}', size: {Size} bytes", 
                _config.FileName, fileBytes.Length);
            
            return fileBytes;
        }

        public List<object> ParseExcelToCoordinates(byte[] excelBytes)
        {
            try
            {
                _logger.LogInformation("Starting to parse Excel file to coordinates");
                
                var coordinates = new List<object>();
                
                // Set EPPlus license for version 8.0+ (note: this is for non-commercial use)
                // This needs to be set in Program.cs during app startup

                using var stream = new MemoryStream(excelBytes);
                using var package = new ExcelPackage(stream);
                
                var worksheet = package.Workbook.Worksheets[0]; // First worksheet

                // Excel format: AMSKEY, latitude, longitude (aligned with mobile asset_coordinates table)
                // Example data: 516925, -43.61380511, 172.5086473
                var lastRow = worksheet.Dimension?.End.Row ?? 0;
                
                _logger.LogDebug("Found {RowCount} rows in Excel file", lastRow);
                
                for (int row = 2; row <= lastRow; row++) // Skip header row
                {
                    try
                    {
                        var amsKeyValue = worksheet.Cells[row, 1].Value?.ToString();
                        var latitudeValue = worksheet.Cells[row, 2].Value;
                        var longitudeValue = worksheet.Cells[row, 3].Value;

                        if (string.IsNullOrWhiteSpace(amsKeyValue) || latitudeValue == null || longitudeValue == null)
                        {
                            _logger.LogWarning("Skipping row {Row} due to missing data", row);
                            continue;
                        }

                        var coordinate = new
                        {
                            asset_id = int.Parse(amsKeyValue),                      // AMSKEY → asset_id
                            latitude = Convert.ToDouble(latitudeValue),             // latitude → latitude  
                            longitude = Convert.ToDouble(longitudeValue)           // longitude → longitude
                        };

                        coordinates.Add(coordinate);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "Failed to parse row {Row}, skipping", row);
                    }
                }

                _logger.LogInformation("Successfully parsed {Count} coordinates from Excel file", coordinates.Count);
                return coordinates;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to parse Excel file to coordinates");
                throw new Exception($"Failed to parse Excel file: {ex.Message}", ex);
            }
        }
    }
}
