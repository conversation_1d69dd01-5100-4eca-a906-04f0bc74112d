using Microsoft.Identity.Client;
using SharePointCoordinatesService.Models;

namespace SharePointCoordinatesService.Services
{
    public class SharePointAuthService
    {
        private readonly SharePointConfig _config;
        private readonly ILogger<SharePointAuthService> _logger;

        public SharePointAuthService(SharePointConfig config, ILogger<SharePointAuthService> logger)
        {
            _config = config;
            _logger = logger;
        }

        public async Task<string> GetAccessTokenAsync()
        {
            try
            {
                _logger.LogInformation("Attempting to acquire access token for SharePoint");

                var app = ConfidentialClientApplicationBuilder
                    .Create(_config.ClientId)
                    .WithClientSecret(_config.ClientSecret)
                    .WithAuthority(new Uri($"https://login.microsoftonline.com/{_config.TenantId}"))
                    .Build();

                // Use application permissions (no user login required)
                string[] scopes = { "https://graph.microsoft.com/.default" };
                
                var result = await app.AcquireTokenForClient(scopes).ExecuteAsync();
                
                _logger.LogInformation("Successfully acquired access token");
                return result.AccessToken;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to acquire access token");
                throw new InvalidOperationException("Failed to authenticate with Microsoft Graph", ex);
            }
        }
    }
}
