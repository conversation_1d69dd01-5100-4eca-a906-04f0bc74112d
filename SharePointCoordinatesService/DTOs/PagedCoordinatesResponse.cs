using SharePointCoordinatesService.DTOs;

namespace SharePointCoordinatesService.DTOs
{
    public class PagedCoordinatesResponse
    {
        public List<CoordinateDto> data { get; set; } = new List<CoordinateDto>();
        public int page { get; set; }
        public int pageSize { get; set; }
        public int totalCount { get; set; }
        public int totalPages { get; set; }
        public bool hasNextPage { get; set; }
        public bool hasPreviousPage { get; set; }
        public DateTime timestamp { get; set; }
    }
}
