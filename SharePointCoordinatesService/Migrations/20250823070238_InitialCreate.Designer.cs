﻿// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;
using SharePointCoordinatesService.Data;

#nullable disable

namespace SharePointCoordinatesService.Migrations
{
    [DbContext(typeof(SharePointCoordinatesDbContext))]
    [Migration("20250823070238_InitialCreate")]
    partial class InitialCreate
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasDefaultSchema("sharepoint_coords")
                .HasAnnotation("ProductVersion", "8.0.11")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("SharePointCoordinatesService.Models.CoordinateModel", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("AmsKey")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime>("LastModified")
                        .HasColumnType("timestamp with time zone");

                    b.Property<decimal>("Latitude")
                        .HasPrecision(10, 8)
                        .HasColumnType("numeric(10,8)");

                    b.Property<decimal>("Longitude")
                        .HasPrecision(11, 8)
                        .HasColumnType("numeric(11,8)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.HasIndex("AmsKey")
                        .IsUnique();

                    b.HasIndex("Latitude", "Longitude");

                    b.ToTable("Coordinates", "sharepoint_coords");
                });

            modelBuilder.Entity("SharePointCoordinatesService.Models.SyncLogModel", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ErrorMessage")
                        .HasColumnType("text");

                    b.Property<int>("RecordsProcessed")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("SharePointFileModified")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<DateTime?>("SyncEndTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime>("SyncStartTime")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.HasIndex("Status");

                    b.HasIndex("SyncStartTime");

                    b.ToTable("SyncLogs", "sharepoint_coords");
                });
#pragma warning restore 612, 618
        }
    }
}
