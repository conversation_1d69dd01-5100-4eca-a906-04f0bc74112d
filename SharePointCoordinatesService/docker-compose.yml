version: '3.8'

services:
  # SharePoint Coordinates Service (Standalone)
  sharepoint-coordinates-service:
    image: *********:5001/sharepoint-coordinates-service:latest
    container_name: sharepoint-coordinates-service
    network_mode: host
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ASPNETCORE_URLS=http://0.0.0.0:5088
      # Use existing PostgreSQL database from WaterMeterManagement
      - ConnectionStrings__DefaultConnection=Host=localhost;Port=5432;Database=SharePointCoordinatesDb;Username=postgres;Password=123456
      # SharePoint configuration
      - SharePointConfig__TenantId=637a79a6-985f-4dcd-aa06-354f067419bf
      - SharePointConfig__ClientId=d50296cf-228e-4139-a530-71a68b516c58
      - SharePointConfig__ClientSecret=****************************************
      - SharePointConfig__SiteUrl=https://siconnz.sharepoint.com/sites/DataProcessing
      - SharePointConfig__FileName=assets_coordinates.xlsx
      - SharePointConfig__CacheExpiryMinutes=60
      - SharePointConfig__RetryAttempts=3
      - SharePointConfig__TimeoutSeconds=30
      # Authentication
      - Authentication__EnableBasicAuth=true
      - Authentication__DefaultUsername=luke.shi
      - Authentication__DefaultPassword=gentoo666
      # Timezone
      - TZ=Pacific/Auckland
    volumes:
      - ./logs:/app/logs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5088/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s