FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS base
WORKDIR /app
EXPOSE 80

FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /src
COPY ["SharePointCoordinatesService.csproj", "."]
RUN dotnet restore "SharePointCoordinatesService.csproj"
COPY . .
WORKDIR "/src"
RUN dotnet build "SharePointCoordinatesService.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "SharePointCoordinatesService.csproj" -c Release -o /app/publish /p:UseAppHost=false

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .

# Install timezone data and set to NZ timezone
RUN apt-get update && apt-get install -y tzdata && \
    ln -sf /usr/share/zoneinfo/Pacific/Auckland /etc/localtime && \
    echo "Pacific/Auckland" > /etc/timezone

ENTRYPOINT ["dotnet", "SharePointCoordinatesService.dll"]
