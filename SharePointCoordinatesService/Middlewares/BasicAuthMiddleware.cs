using System.Net.Http.Headers;
using System.Security.Claims;
using System.Text;
using System.Text.Encodings.Web;
using Microsoft.AspNetCore.Authentication;
using Microsoft.Extensions.Options;

namespace SharePointCoordinatesService.Middlewares
{
    public class BasicAuthenticationSchemeOptions : AuthenticationSchemeOptions { }

    public class BasicAuthenticationHandler : AuthenticationHandler<BasicAuthenticationSchemeOptions>
    {
        private readonly IConfiguration _configuration;

        public BasicAuthenticationHandler(
            IOptionsMonitor<BasicAuthenticationSchemeOptions> options,
            ILoggerFactory logger,
            UrlEncoder encoder,
            ISystemClock clock,
            IConfiguration configuration)
            : base(options, logger, encoder, clock)
        {
            _configuration = configuration;
        }

        protected override Task<AuthenticateResult> HandleAuthenticateAsync()
        {
            if (!Request.Headers.ContainsKey("Authorization"))
            {
                return Task.FromResult(AuthenticateResult.Fail("Missing Authorization Header"));
            }

            try
            {
                var authHeader = AuthenticationHeaderValue.Parse(Request.Headers["Authorization"]);
                if (authHeader.Scheme != "Basic")
                {
                    return Task.FromResult(AuthenticateResult.Fail("Invalid Authorization Scheme"));
                }

                var credentialBytes = Convert.FromBase64String(authHeader.Parameter ?? string.Empty);
                var credentials = Encoding.UTF8.GetString(credentialBytes).Split(':', 2);
                
                if (credentials.Length != 2)
                {
                    return Task.FromResult(AuthenticateResult.Fail("Invalid Authorization Header"));
                }

                var username = credentials[0];
                var password = credentials[1];

                // Validate credentials against configuration
                var validUsername = _configuration["Authentication:DefaultUsername"];
                var validPassword = _configuration["Authentication:DefaultPassword"];

                if (username == validUsername && password == validPassword)
                {
                    var claims = new[]
                    {
                        new Claim(ClaimTypes.NameIdentifier, username),
                        new Claim(ClaimTypes.Name, username),
                    };

                    var identity = new ClaimsIdentity(claims, Scheme.Name);
                    var principal = new ClaimsPrincipal(identity);
                    var ticket = new AuthenticationTicket(principal, Scheme.Name);

                    Logger.LogInformation("Successfully authenticated user: {Username}", username);
                    return Task.FromResult(AuthenticateResult.Success(ticket));
                }

                Logger.LogWarning("Authentication failed for user: {Username}", username);
                return Task.FromResult(AuthenticateResult.Fail("Invalid Username or Password"));
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error in basic authentication");
                return Task.FromResult(AuthenticateResult.Fail("Authentication Error"));
            }
        }

        protected override Task HandleChallengeAsync(AuthenticationProperties properties)
        {
            Response.Headers["WWW-Authenticate"] = "Basic realm=\"SharePoint Coordinates Service\"";
            return base.HandleChallengeAsync(properties);
        }
    }

    public static class BasicAuthenticationExtensions
    {
        public static AuthenticationBuilder AddBasicAuthentication(this AuthenticationBuilder builder)
        {
            return builder.AddScheme<BasicAuthenticationSchemeOptions, BasicAuthenticationHandler>(
                "Basic", options => { });
        }
    }
}
