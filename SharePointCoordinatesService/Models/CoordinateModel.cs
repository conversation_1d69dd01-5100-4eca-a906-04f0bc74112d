using System.ComponentModel.DataAnnotations;

namespace SharePointCoordinatesService.Models
{
    public class CoordinateModel
    {
        [Key]
        public int Id { get; set; }
        
        [Required]
        public string AmsKey { get; set; } = string.Empty;
        
        [Required]
        public decimal Latitude { get; set; }
        
        [Required]
        public decimal Longitude { get; set; }
        
        public DateTime LastModified { get; set; }
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
    }
}
