using System.ComponentModel.DataAnnotations;

namespace SharePointCoordinatesService.Models
{
    public class SyncLogModel
    {
        [Key]
        public int Id { get; set; }
        
        [Required]
        public DateTime SyncStartTime { get; set; }
        
        public DateTime? SyncEndTime { get; set; }
        
        public int RecordsProcessed { get; set; }
        
        [Required]
        [MaxLength(20)]
        public string Status { get; set; } = string.Empty; // 'Success', 'Failed', 'InProgress'
        
        public string? ErrorMessage { get; set; }
        
        public DateTime? SharePointFileModified { get; set; }
        
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    }
}
