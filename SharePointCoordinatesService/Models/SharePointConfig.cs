namespace SharePointCoordinatesService.Models
{
    public class SharePointConfig
    {
        public string TenantId { get; set; } = string.Empty;
        public string ClientId { get; set; } = string.Empty;
        public string ClientSecret { get; set; } = string.Empty;
        public string SiteUrl { get; set; } = string.Empty;
        public string FileName { get; set; } = string.Empty;
        public int CacheExpiryMinutes { get; set; } = 60;
        public int RetryAttempts { get; set; } = 3;
        public int TimeoutSeconds { get; set; } = 30;
    }
}
