<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SharePoint Coordinates Service - Login</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 0;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .login-container {
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
            width: 100%;
            max-width: 400px;
        }
        
        .logo {
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .logo h1 {
            color: #333;
            margin: 0;
            font-size: 1.5rem;
        }
        
        .logo p {
            color: #666;
            margin: 0.5rem 0 0 0;
            font-size: 0.9rem;
        }
        
        .form-group {
            margin-bottom: 1rem;
        }
        
        label {
            display: block;
            margin-bottom: 0.5rem;
            color: #333;
            font-weight: 500;
        }
        
        input[type="text"], input[type="password"] {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid #e1e5e9;
            border-radius: 5px;
            font-size: 1rem;
            transition: border-color 0.3s;
            box-sizing: border-box;
        }
        
        input[type="text"]:focus, input[type="password"]:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .btn-group {
            display: flex;
            gap: 1rem;
            margin-top: 1.5rem;
        }
        
        .btn {
            flex: 1;
            padding: 0.75rem;
            border: none;
            border-radius: 5px;
            font-size: 1rem;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        
        .btn-primary {
            background: #667eea;
            color: white;
        }
        
        .btn-primary:hover {
            background: #5a6fd8;
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #5a6268;
        }
        
        .links {
            text-align: center;
            margin-top: 1.5rem;
            padding-top: 1.5rem;
            border-top: 1px solid #e1e5e9;
        }
        
        .links a {
            color: #667eea;
            text-decoration: none;
            margin: 0 1rem;
            font-size: 0.9rem;
        }
        
        .links a:hover {
            text-decoration: underline;
        }
        
        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 0.75rem;
            border-radius: 5px;
            margin-bottom: 1rem;
            display: none;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="logo">
            <h1>🔗 SharePoint Coordinates</h1>
            <p>Service Authentication</p>
        </div>
        
        <div id="error" class="error"></div>
        
        <form id="loginForm">
            <div class="form-group">
                <label for="username">Username:</label>
                <input type="text" id="username" name="username" required>
            </div>
            
            <div class="form-group">
                <label for="password">Password:</label>
                <input type="password" id="password" name="password" required>
            </div>
            
            <div class="btn-group">
                <button type="submit" class="btn btn-primary">Access Swagger</button>
                <button type="button" class="btn btn-secondary" onclick="testConnection()">Test API</button>
            </div>
        </form>
        
        <div class="links">
            <a href="/health" target="_blank">Health Check</a>
            <a href="javascript:void(0)" onclick="showCredentials()">Default Credentials</a>
        </div>
    </div>

    <script>
        // Default credentials for development
        const DEFAULT_USERNAME = 'luke.shi';
        const DEFAULT_PASSWORD = 'gentoo666';
        
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            // Create basic auth header
            const credentials = btoa(username + ':' + password);
            
            // Test authentication
            fetch('/api/coordinates/cache/status', {
                method: 'GET',
                headers: {
                    'Authorization': 'Basic ' + credentials,
                    'Content-Type': 'application/json'
                }
            })
            .then(response => {
                if (response.ok) {
                    // Authentication successful, redirect to Swagger with auth
                    const swaggerUrl = '/swagger/index.html';
                    window.open(swaggerUrl, '_blank');
                    showSuccess('Authentication successful! Swagger opened in new tab.');
                } else {
                    showError('Authentication failed. Please check your credentials.');
                }
            })
            .catch(error => {
                showError('Connection error: ' + error.message);
            });
        });
        
        function testConnection() {
            const username = document.getElementById('username').value || DEFAULT_USERNAME;
            const password = document.getElementById('password').value || DEFAULT_PASSWORD;
            const credentials = btoa(username + ':' + password);
            
            fetch('/api/coordinates/cache/status', {
                method: 'GET',
                headers: {
                    'Authorization': 'Basic ' + credentials,
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                showSuccess('API Connection successful! Cache status: ' + (data.cacheValid ? 'Valid' : 'Invalid'));
            })
            .catch(error => {
                showError('API Connection failed: ' + error.message);
            });
        }
        
        function showCredentials() {
            document.getElementById('username').value = DEFAULT_USERNAME;
            document.getElementById('password').value = DEFAULT_PASSWORD;
            showSuccess('Default credentials filled in.');
        }
        
        function showError(message) {
            const errorDiv = document.getElementById('error');
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
            errorDiv.style.background = '#f8d7da';
            errorDiv.style.color = '#721c24';
        }
        
        function showSuccess(message) {
            const errorDiv = document.getElementById('error');
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
            errorDiv.style.background = '#d4edda';
            errorDiv.style.color = '#155724';
        }
    </script>
</body>
</html>
