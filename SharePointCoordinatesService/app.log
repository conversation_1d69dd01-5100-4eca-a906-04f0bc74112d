Building...
/Users/<USER>/CORDE/SharePointCoordinatesService/SharePointCoordinatesService/Middlewares/BasicAuthMiddleware.cs(20,13): warning CS0618: 'ISystemClock' is obsolete: 'Use TimeProvider instead.' [/Users/<USER>/CORDE/SharePointCoordinatesService/SharePointCoordinatesService/SharePointCoordinatesService.csproj]
/Users/<USER>/CORDE/SharePointCoordinatesService/SharePointCoordinatesService/Middlewares/BasicAuthMiddleware.cs(22,13): warning CS0618: 'AuthenticationHandler<BasicAuthenticationSchemeOptions>.AuthenticationHandler(IOptionsMonitor<BasicAuthenticationSchemeOptions>, ILoggerFactory, UrlEncoder, ISystemClock)' is obsolete: 'ISystemClock is obsolete, use TimeProvider on AuthenticationSchemeOptions instead.' [/Users/<USER>/CORDE/SharePointCoordinatesService/SharePointCoordinatesService/SharePointCoordinatesService.csproj]
/Users/<USER>/CORDE/SharePointCoordinatesService/SharePointCoordinatesService/Middlewares/BasicAuthMiddleware.cs(36,66): warning CS8604: Possible null reference argument for parameter 'input' in 'AuthenticationHeaderValue AuthenticationHeaderValue.Parse(string input)'. [/Users/<USER>/CORDE/SharePointCoordinatesService/SharePointCoordinatesService/SharePointCoordinatesService.csproj]
/Users/<USER>/CORDE/SharePointCoordinatesService/SharePointCoordinatesService/Services/SharePointFileService.cs(169,17): warning CS0618: 'ExcelPackage.LicenseContext' is obsolete: 'Please use the static 'ExcelPackage.License' property to set the required license information from EPPlus 8 and later versions. For more info see http://epplussoftware.com/developers/licensenotsetexception.' [/Users/<USER>/CORDE/SharePointCoordinatesService/SharePointCoordinatesService/SharePointCoordinatesService.csproj]
/Users/<USER>/CORDE/SharePointCoordinatesService/SharePointCoordinatesService/Services/SharePointFileService.cs(169,47): warning CS0618: 'LicenseContext' is obsolete: 'Used in versions prior to EPPlus 8. Will be removed in coming versions.' [/Users/<USER>/CORDE/SharePointCoordinatesService/SharePointCoordinatesService/SharePointCoordinatesService.csproj]
[01:46:26 INF] Starting SharePoint Coordinates Service
[01:46:26 INF] User profile is available. Using '/Users/<USER>/.aspnet/DataProtection-Keys' as key repository; keys will not be encrypted at rest.
[01:46:26 INF] Now listening on: http://localhost:5088
[01:46:26 INF] Application started. Press Ctrl+C to shut down.
[01:46:26 INF] Hosting environment: Development
[01:46:26 INF] Content root path: /Users/<USER>/CORDE/SharePointCoordinatesService/SharePointCoordinatesService
[01:46:33 INF] Request starting HTTP/1.1 GET http://localhost:5088/api/coordinates - null null
[01:46:33 WRN] Failed to determine the https port for redirect.
[01:46:33 INF] Successfully authenticated user: luke.shi
[01:46:33 INF] Executing endpoint 'SharePointCoordinatesService.Controllers.CoordinatesController.GetCoordinates (SharePointCoordinatesService)'
[01:46:33 INF] Route matched with {action = "GetCoordinates", controller = "Coordinates"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetCoordinates() on controller SharePointCoordinatesService.Controllers.CoordinatesController (SharePointCoordinatesService).
[01:46:33 INF] API request: Get coordinates
[01:46:33 INF] Starting to get coordinates data
[01:46:33 INF] Cache miss, fetching from SharePoint
[01:46:33 INF] Starting to download file 'assets_coordinates.xlsx' from SharePoint
[01:46:33 INF] Attempting to acquire access token for SharePoint
[01:46:34 INF] Successfully acquired access token
[01:46:34 INF] Start processing HTTP request GET https://graph.microsoft.com/v1.0/sites/siconnz.sharepoint.com:/sites/DataProcessing
[01:46:34 INF] Sending HTTP request GET https://graph.microsoft.com/v1.0/sites/siconnz.sharepoint.com:/sites/DataProcessing
[01:46:34 INF] Received HTTP response headers after 281.3477ms - 200
[01:46:34 INF] End processing HTTP request after 288.7126ms - 200
[01:46:34 INF] Start processing HTTP request GET https://graph.microsoft.com/v1.0/sites/siconnz.sharepoint.com,ca3aa3eb-53dc-4f5a-b46e-c38b54a3519d,3d424223-5f56-4a0d-ba84-1f0ae3c323d8/drive/root:/Support/assets_coordinates.xlsx:/content
[01:46:34 INF] Sending HTTP request GET https://graph.microsoft.com/v1.0/sites/siconnz.sharepoint.com,ca3aa3eb-53dc-4f5a-b46e-c38b54a3519d,3d424223-5f56-4a0d-ba84-1f0ae3c323d8/drive/root:/Support/assets_coordinates.xlsx:/content
[01:46:35 INF] Received HTTP response headers after 487.5289ms - 200
[01:46:35 INF] End processing HTTP request after 488.1694ms - 200
[01:46:35 INF] Successfully downloaded file using path '/Support/assets_coordinates.xlsx', size: 422488 bytes
[01:46:35 INF] Starting to parse Excel file to coordinates
[01:46:35 ERR] Failed to parse Excel file to coordinates
OfficeOpenXml.LicenseContextPropertyObsoleteException: Please use the static 'ExcelPackage.License' property to set the required license information from EPPlus 8 and later versions. For more info see http://epplussoftware.com/developers/licensenotsetexception.
   at OfficeOpenXml.ExcelPackage.set_LicenseContext(Nullable`1 value)
   at SharePointCoordinatesService.Services.SharePointFileService.ParseExcelToCoordinates(Byte[] excelBytes) in /Users/<USER>/CORDE/SharePointCoordinatesService/SharePointCoordinatesService/Services/SharePointFileService.cs:line 169
[01:46:35 ERR] Failed to fetch coordinates from SharePoint
System.Exception: Failed to parse Excel file: Please use the static 'ExcelPackage.License' property to set the required license information from EPPlus 8 and later versions. For more info see http://epplussoftware.com/developers/licensenotsetexception.
 ---> OfficeOpenXml.LicenseContextPropertyObsoleteException: Please use the static 'ExcelPackage.License' property to set the required license information from EPPlus 8 and later versions. For more info see http://epplussoftware.com/developers/licensenotsetexception.
   at OfficeOpenXml.ExcelPackage.set_LicenseContext(Nullable`1 value)
   at SharePointCoordinatesService.Services.SharePointFileService.ParseExcelToCoordinates(Byte[] excelBytes) in /Users/<USER>/CORDE/SharePointCoordinatesService/SharePointCoordinatesService/Services/SharePointFileService.cs:line 169
   --- End of inner exception stack trace ---
   at SharePointCoordinatesService.Services.SharePointFileService.ParseExcelToCoordinates(Byte[] excelBytes) in /Users/<USER>/CORDE/SharePointCoordinatesService/SharePointCoordinatesService/Services/SharePointFileService.cs:line 217
   at SharePointCoordinatesService.Services.CoordinatesService.FetchCoordinatesFromSharePointAsync() in /Users/<USER>/CORDE/SharePointCoordinatesService/SharePointCoordinatesService/Services/CoordinatesService.cs:line 80
[01:46:35 ERR] Error fetching coordinates
System.Exception: Failed to parse Excel file: Please use the static 'ExcelPackage.License' property to set the required license information from EPPlus 8 and later versions. For more info see http://epplussoftware.com/developers/licensenotsetexception.
 ---> OfficeOpenXml.LicenseContextPropertyObsoleteException: Please use the static 'ExcelPackage.License' property to set the required license information from EPPlus 8 and later versions. For more info see http://epplussoftware.com/developers/licensenotsetexception.
   at OfficeOpenXml.ExcelPackage.set_LicenseContext(Nullable`1 value)
   at SharePointCoordinatesService.Services.SharePointFileService.ParseExcelToCoordinates(Byte[] excelBytes) in /Users/<USER>/CORDE/SharePointCoordinatesService/SharePointCoordinatesService/Services/SharePointFileService.cs:line 169
   --- End of inner exception stack trace ---
   at SharePointCoordinatesService.Services.SharePointFileService.ParseExcelToCoordinates(Byte[] excelBytes) in /Users/<USER>/CORDE/SharePointCoordinatesService/SharePointCoordinatesService/Services/SharePointFileService.cs:line 217
   at SharePointCoordinatesService.Services.CoordinatesService.FetchCoordinatesFromSharePointAsync() in /Users/<USER>/CORDE/SharePointCoordinatesService/SharePointCoordinatesService/Services/CoordinatesService.cs:line 80
   at SharePointCoordinatesService.Services.CoordinatesService.GetCoordinatesAsync() in /Users/<USER>/CORDE/SharePointCoordinatesService/SharePointCoordinatesService/Services/CoordinatesService.cs:line 48
[01:46:35 ERR] Error in GetCoordinates API
System.Exception: Failed to parse Excel file: Please use the static 'ExcelPackage.License' property to set the required license information from EPPlus 8 and later versions. For more info see http://epplussoftware.com/developers/licensenotsetexception.
 ---> OfficeOpenXml.LicenseContextPropertyObsoleteException: Please use the static 'ExcelPackage.License' property to set the required license information from EPPlus 8 and later versions. For more info see http://epplussoftware.com/developers/licensenotsetexception.
   at OfficeOpenXml.ExcelPackage.set_LicenseContext(Nullable`1 value)
   at SharePointCoordinatesService.Services.SharePointFileService.ParseExcelToCoordinates(Byte[] excelBytes) in /Users/<USER>/CORDE/SharePointCoordinatesService/SharePointCoordinatesService/Services/SharePointFileService.cs:line 169
   --- End of inner exception stack trace ---
   at SharePointCoordinatesService.Services.SharePointFileService.ParseExcelToCoordinates(Byte[] excelBytes) in /Users/<USER>/CORDE/SharePointCoordinatesService/SharePointCoordinatesService/Services/SharePointFileService.cs:line 217
   at SharePointCoordinatesService.Services.CoordinatesService.FetchCoordinatesFromSharePointAsync() in /Users/<USER>/CORDE/SharePointCoordinatesService/SharePointCoordinatesService/Services/CoordinatesService.cs:line 80
   at SharePointCoordinatesService.Services.CoordinatesService.GetCoordinatesAsync() in /Users/<USER>/CORDE/SharePointCoordinatesService/SharePointCoordinatesService/Services/CoordinatesService.cs:line 48
   at SharePointCoordinatesService.Controllers.CoordinatesController.GetCoordinates() in /Users/<USER>/CORDE/SharePointCoordinatesService/SharePointCoordinatesService/Controllers/CoordinatesController.cs:line 36
[01:46:35 INF] Executing ObjectResult, writing value of type '<>f__AnonymousType0`3[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
[01:46:35 INF] Executed action SharePointCoordinatesService.Controllers.CoordinatesController.GetCoordinates (SharePointCoordinatesService) in 1419.0322ms
[01:46:35 INF] Executed endpoint 'SharePointCoordinatesService.Controllers.CoordinatesController.GetCoordinates (SharePointCoordinatesService)'
[01:46:35 INF] Request finished HTTP/1.1 GET http://localhost:5088/api/coordinates - 500 null application/json; charset=utf-8 1448.2384ms
[01:47:14 INF] Application is shutting down...
