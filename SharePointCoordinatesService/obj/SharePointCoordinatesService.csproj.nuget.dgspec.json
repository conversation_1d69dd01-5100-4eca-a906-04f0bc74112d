{"format": 1, "restore": {"/Users/<USER>/CORDE/SharePointCoordinatesService/SharePointCoordinatesService.csproj": {}}, "projects": {"/Users/<USER>/CORDE/SharePointCoordinatesService/SharePointCoordinatesService.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/Users/<USER>/CORDE/SharePointCoordinatesService/SharePointCoordinatesService.csproj", "projectName": "SharePointCoordinatesService", "projectPath": "/Users/<USER>/CORDE/SharePointCoordinatesService/SharePointCoordinatesService.csproj", "packagesPath": "/Users/<USER>/.nuget/packages/", "outputPath": "/Users/<USER>/CORDE/SharePointCoordinatesService/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/Users/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"AspNetCore.HealthChecks.NpgSql": {"target": "Package", "version": "[9.0.0, )"}, "AutoMapper": {"target": "Package", "version": "[12.0.1, )"}, "AutoMapper.Extensions.Microsoft.DependencyInjection": {"target": "Package", "version": "[12.0.1, )"}, "EPPlus": {"target": "Package", "version": "[8.0.5, )"}, "Microsoft.AspNetCore.Authentication.JwtBearer": {"target": "Package", "version": "[8.0.11, )"}, "Microsoft.AspNetCore.OpenApi": {"target": "Package", "version": "[8.0.18, )"}, "Microsoft.EntityFrameworkCore.Tools": {"target": "Package", "version": "[8.0.11, )"}, "Microsoft.Graph": {"target": "Package", "version": "[5.56.0, )"}, "Microsoft.Identity.Client": {"target": "Package", "version": "[4.61.3, )"}, "Npgsql.EntityFrameworkCore.PostgreSQL": {"target": "Package", "version": "[8.0.11, )"}, "Serilog.AspNetCore": {"target": "Package", "version": "[9.0.0, )"}, "Serilog.Sinks.File": {"target": "Package", "version": "[6.0.0, )"}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[6.6.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/local/share/dotnet/sdk/8.0.412/PortableRuntimeIdentifierGraph.json"}}}}}