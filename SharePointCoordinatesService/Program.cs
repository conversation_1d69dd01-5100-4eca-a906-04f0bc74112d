using Serilog;
using Microsoft.EntityFrameworkCore;
using SharePointCoordinatesService.Models;
using SharePointCoordinatesService.Services;
using SharePointCoordinatesService.Middlewares;
using SharePointCoordinatesService.Data;
using OfficeOpenXml;

var builder = WebApplication.CreateBuilder(args);

// Set EPPlus license for non-commercial use (required for EPPlus 8.0+)
// According to official EPPlus documentation
ExcelPackage.License.SetNonCommercialPersonal("Luke Shi");

// Configure Serilog
Log.Logger = new LoggerConfiguration()
    .ReadFrom.Configuration(builder.Configuration)
    .Enrich.FromLogContext()
    .WriteTo.Console()
    .CreateLogger();

builder.Host.UseSerilog();

// Add services to the container
builder.Services.AddControllers();
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("v1", new() {
        Title = "SharePoint Coordinates Service",
        Version = "v1",
        Description = "API for syncing coordinates from SharePoint Excel files"
    });

    // Add Basic Authentication to Swagger
    c.AddSecurityDefinition("Basic", new Microsoft.OpenApi.Models.OpenApiSecurityScheme
    {
        Name = "Authorization",
        Type = Microsoft.OpenApi.Models.SecuritySchemeType.Http,
        Scheme = "basic",
        In = Microsoft.OpenApi.Models.ParameterLocation.Header,
        Description = "Basic Authorization header using the Bearer scheme."
    });

    c.AddSecurityRequirement(new Microsoft.OpenApi.Models.OpenApiSecurityRequirement
    {
        {
            new Microsoft.OpenApi.Models.OpenApiSecurityScheme
            {
                Reference = new Microsoft.OpenApi.Models.OpenApiReference
                {
                    Type = Microsoft.OpenApi.Models.ReferenceType.SecurityScheme,
                    Id = "Basic"
                }
            },
            new string[] {}
        }
    });
});

// Configure SharePoint settings
var sharePointConfig = new SharePointConfig();
builder.Configuration.GetSection("SharePointConfig").Bind(sharePointConfig);
builder.Services.AddSingleton(sharePointConfig);

// Add database context
builder.Services.AddDbContext<SharePointCoordinatesDbContext>(options =>
    options.UseNpgsql(builder.Configuration.GetConnectionString("DefaultConnection")));

// Add memory cache
builder.Services.AddMemoryCache();

// Add HTTP client
builder.Services.AddHttpClient();

// Add authentication
builder.Services.AddAuthentication("Basic")
    .AddBasicAuthentication();
builder.Services.AddAuthorization();

// Add application services
builder.Services.AddScoped<SharePointAuthService>();
builder.Services.AddScoped<SharePointFileService>();
builder.Services.AddScoped<CoordinatesService>();

// Add health checks
builder.Services.AddHealthChecks();

// Add CORS
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowAll", policy =>
    {
        policy.AllowAnyOrigin()
              .AllowAnyMethod()
              .AllowAnyHeader();
    });
});

var app = builder.Build();

// Configure the HTTP request pipeline
app.UseSwagger();
app.UseSwaggerUI(c =>
{
    c.SwaggerEndpoint("/swagger/v1/swagger.json", "SharePoint Coordinates Service v1");
    c.RoutePrefix = "swagger"; // Set Swagger UI at /swagger path
    c.DocumentTitle = "SharePoint Coordinates Service - API Documentation";
    c.DisplayRequestDuration();
    c.EnableDeepLinking();
    c.EnableFilter();
    c.ShowExtensions();
    c.EnableValidator();
});

// Enable static files
app.UseStaticFiles();

app.UseHttpsRedirection();

// Use CORS
app.UseCors("AllowAll");

// Use authentication and authorization
app.UseAuthentication();
app.UseAuthorization();

// Map controllers
app.MapControllers();

// Map health checks
app.MapHealthChecks("/health");

// Redirect root to login page
app.MapGet("/", () => Results.Redirect("/login.html"));

// Ensure database is created and initialized
using (var scope = app.Services.CreateScope())
{
    var context = scope.ServiceProvider.GetRequiredService<SharePointCoordinatesDbContext>();
    try
    {
        // Apply any pending migrations and ensure database is created
        context.Database.Migrate();
        Log.Information("Database migrations applied successfully");
        
        // Ensure database and schema are properly set up
        await EnsureDatabaseInitializedAsync(context);
        Log.Information("Database initialization completed successfully");
    }
    catch (Exception ex)
    {
        Log.Error(ex, "Database migration or initialization failed");
    }
}

try
{
    Log.Information("🚀 SharePoint Coordinates Service started successfully");
    Log.Information("📖 API documentation available at: http://localhost:5088");
    Log.Information("🔐 Health check available at: http://localhost:5088/health");
    app.Run();
}
catch (Exception ex)
{
    Log.Fatal(ex, "Application terminated unexpectedly");
}
finally
{
    Log.CloseAndFlush();
}

static async Task EnsureDatabaseInitializedAsync(SharePointCoordinatesDbContext context)
{
    // Ensure the database exists
    await context.Database.EnsureCreatedAsync();
    
    // The database should already have the schema created by migrations,
    // but we can add any additional initialization logic here if needed
    Log.Information("SharePoint Coordinates database and schema are ready");
}
